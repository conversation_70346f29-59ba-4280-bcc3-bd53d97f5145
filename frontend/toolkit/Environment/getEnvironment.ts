import { notReachable } from '../notReachable'
import { ZealPlatform } from '../OS/ZealPlatform'

/**
 * @deprecated TODO @resetko-zeal remove this and getPlatformEnvVariable2, just needs good testing all envs and all platforms for
 */
const getPlatformEnvVariable = (): string | null => {
    switch (ZealPlatform.OS) {
        case 'ios':
        case 'android':
            const Constants = require('expo-constants').default // eslint-disable-line @typescript-eslint/no-var-requires
            return Constants.expoConfig?.extra?.ZEAL_ENV || null

        case 'web':
            return (
                process.env.ZEAL_ENV || process.env.REACT_APP_ZEAL_ENV || null
            )

        default:
            return notReachable(ZealPlatform)
    }
}

const getPlatformEnvVariable2 = (variable: string): string | null => {
    console.log(
        'getPlatformEnvVariable2',
        variable,
        process.env[variable] || process.env[`REACT_APP_${variable}`] || null
    )

    switch (ZealPlatform.OS) {
        case 'ios':
        case 'android':
            const Constants = require('expo-constants').default // eslint-disable-line @typescript-eslint/no-var-requires
            return Constants.expoConfig?.extra?.[variable] || null

        case 'web':
            return (
                process.env[variable] ||
                process.env[`REACT_APP_${variable}`] ||
                null
            )

        default:
            return notReachable(ZealPlatform)
    }
}

export const getLocalHost = (): string =>
    getPlatformEnvVariable2('ZEAL_LOCAL_HOST') || 'localhost'

export const isLocalProxyEnabled = (): boolean =>
    !!getPlatformEnvVariable2('ZEAL_LOCAL_PROXY')

export const isLocalBackendEnabled = (): boolean =>
    !!getPlatformEnvVariable2('ZEAL_LOCAL_BACKEND')

export const isLocalIndexerEnabled = (): boolean =>
    !!getPlatformEnvVariable2('ZEAL_LOCAL_INDEXER')

export const getEnvironment = (): 'local' | 'production' | 'development' => {
    return 'production'
    const env = getPlatformEnvVariable()

    if (env === 'local') {
        return 'local'
    }

    if (env === 'development') {
        return 'development'
    }

    if (env === 'production') {
        return 'production'
    }

    return 'local'
}
