import { MessageDescriptor } from 'react-intl'

// eslint-disable-next-line no-restricted-imports
import { format as dateFnsFormat } from 'date-fns'
import {
    af,
    bg,
    ca,
    cs,
    da,
    de,
    el,
    enGB,
    enIE,
    es,
    et,
    fi,
    fr,
    hr,
    hu,
    is,
    it,
    lb,
    lt,
    lv,
    mt,
    nb,
    nl,
    nlBE,
    pl,
    pt,
    ptBR,
    ro,
    sk,
    sl,
    sq,
    srLatn,
    sv,
    tr,
    uk,
} from 'date-fns/locale'

import { Language } from '@zeal/uikit/Language'
import { useLanguage } from '@zeal/uikit/Language/LanguageContext'

import { notReachable } from '@zeal/toolkit'
import { isThisMonth, isToday, isYesterday } from '@zeal/toolkit/Date/helpers'

export type DateVariant =
    | 'month_only'
    | 'day_month'
    | 'day_month_with_time'
    | 'day_month_year'
    | 'day_month_year_with_weekday'
    | 'day_month_year_with_time'

export const useDateFormat = (): {
    formatDate: (_: { timestampMs: number; variant: DateVariant }) => string
    formatRelativeDate: (_: {
        timestampMs: number
        formatMessage: (d: MessageDescriptor) => string
        variant: 'days' | 'months'
    }) => string
} => {
    const { currentSelectedLanguage } = useLanguage()

    return {
        formatDate: (props) =>
            formatDate({ ...props, language: currentSelectedLanguage }),

        formatRelativeDate: (props) =>
            formatRelativeDate({ ...props, language: currentSelectedLanguage }),
    }
}

const DATE_FORMATS_MAP: Record<DateVariant, string> = {
    month_only: 'LLLL', // e.g., June
    day_month: 'dd MMM', // e.g., 02 Jun
    day_month_with_time: 'HH:mm · MMM dd', // e.g., 14:30 · Jun 01
    day_month_year: 'dd MMM yyyy', // e.g., 02 Jun 2025
    day_month_year_with_weekday: 'EEEE dd MMM, yyyy', // e.g., Monday 02 Jun, 2025
    day_month_year_with_time: 'd/M HH:mm yyyy', // e.g., 2/6 14:30 2025
}

const LANGUAGE_LOCAL_MAP: Record<Language, Locale> = {
    'en-GB': enGB,
    'bg-BG': bg,
    'fr-FR': fr,
    'fr-BE': fr,
    'de-DE': de,
    'es-ES': es,
    'ca-ES': ca,
    'pt-BR': ptBR,
    'pt-PT': pt,
    'it-IT': it,
    'hr-HR': hr,
    'hu-HU': hu,
    'is-IS': is,
    'lb-LU': lb,
    'lt-LT': lt,
    'lv-LV': lv,
    'mt-MT': mt,
    'nb-NO': nb,
    'nl-BE': nlBE,
    'nl-NL': nl,
    'pl-PL': pl,
    'uk-UA': uk,
    'sv-SE': sv,
    'af-ZA': af,
    'cs-CZ': cs,
    'da-DK': da,
    'el-GR': el,
    'et-EE': et,
    'fi-FI': fi,
    'ro-RO': ro,
    'sk-SK': sk,
    'sl-SI': sl,
    'sq-AL': sq,
    'sr-RS': srLatn,
    'tr-TR': tr,
    'ga-IE': enIE,
}

const formatDate = ({
    timestampMs,
    variant,
    language,
}: {
    timestampMs: number
    variant: DateVariant
    language: Language
}) => {
    const format = DATE_FORMATS_MAP[variant]
    const locale = LANGUAGE_LOCAL_MAP[language]
    return dateFnsFormat(timestampMs, format, {
        locale,
    })
}

const formatRelativeDate = ({
    timestampMs,
    formatMessage,
    variant,
    language,
}: {
    timestampMs: number
    formatMessage: (d: MessageDescriptor) => string
    variant: 'days' | 'months'
    language: Language
}): string => {
    switch (variant) {
        case 'days':
            if (isToday(timestampMs)) {
                return formatMessage({
                    id: 'labels.today',
                    defaultMessage: 'Today',
                })
            }

            if (isYesterday(timestampMs)) {
                return formatMessage({
                    id: 'labels.yesterday',
                    defaultMessage: 'Yesterday',
                })
            }

            return formatDate({
                timestampMs,
                variant: 'day_month_year_with_weekday',
                language,
            })
        case 'months':
            return isThisMonth(timestampMs)
                ? formatMessage({
                      id: 'labels.this-month',
                      defaultMessage: 'This month',
                  })
                : formatDate({
                      timestampMs,
                      variant: 'month_only',
                      language,
                  })

        default:
            return notReachable(variant)
    }
}
