import { getRandomIntArray } from '@zeal/toolkit/Crypto'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'

export const generateRandomNumber = (): number => {
    const array = new Uint32Array(1)
    getRandomIntArray(array)
    return array[0]
}

export const generateRandomNumberBetween = (
    min: number,
    max: number
): number => {
    if (min >= max) {
        throw new ImperativeError('min must be less than max')
    }

    const array = new Uint32Array(1)
    getRandomIntArray(array)

    // Uint32Array max value is 2^32 - 1, so we divide by 2^32 to get [0, 1)
    const randomFloat = array[0] / 2 ** 32

    return Math.floor(randomFloat * (max - min)) + min
}

export const generateRandomBigint = (nBytes: number): bigint => {
    const array = new Uint8Array(nBytes)
    getRandomIntArray(array)
    return BigInt(Hexadecimal.fromBuffer(array))
}

export const toHex = (n: bigint | number | string) => {
    if (typeof n === 'string' && n.endsWith('.')) {
        n = n.slice(0, -1)
    }

    return '0x' + BigInt(n).toString(16)
}
