import { keys } from '@zeal/toolkit/Object'

export type Language =
    | 'en-GB'
    | 'bg-BG'
    | 'fr-FR'
    | 'fr-BE'
    | 'de-DE'
    | 'es-ES'
    | 'ca-ES'
    | 'pt-BR'
    | 'pt-PT'
    | 'it-IT'
    | 'hr-HR'
    | 'hu-HU'
    | 'is-IS'
    | 'lb-LU'
    | 'lt-LT'
    | 'lv-LV'
    | 'mt-MT'
    | 'nb-NO'
    | 'nl-BE'
    | 'nl-NL'
    | 'pl-PL'
    | 'uk-UA'
    | 'sv-SE'
    | 'cs-CZ'
    | 'da-DK'
    | 'el-GR'
    | 'et-EE'
    | 'fi-FI'
    | 'af-ZA'
    | 'ro-RO'
    | 'sk-SK'
    | 'sl-SI'
    | 'sq-AL'
    | 'sr-RS'
    | 'tr-TR'
    | 'ga-IE'

export type LanguageRule = {
    decimalSeparator: ',' | '.'
    digitGroupingSeparator: ',' | '.' | '\u00A0' | '\u202F' | "'"
    currencyPlacement: 'head' | 'tail'
}

const LANGUAGE_RULES_MAP: Record<Language, LanguageRule> = {
    'en-GB': {
        decimalSeparator: '.',
        digitGroupingSeparator: ',',
        currencyPlacement: 'head',
    },
    'bg-BG': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'fr-FR': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u202F',
        currencyPlacement: 'tail',
    },
    'fr-BE': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u202F',
        currencyPlacement: 'tail',
    },
    'de-DE': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'tail',
    },
    'es-ES': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'tail',
    },
    'ca-ES': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'tail',
    },
    'pt-BR': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'head',
    },
    'pt-PT': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'it-IT': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'tail',
    },
    'hr-HR': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'tail',
    },
    'hu-HU': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'is-IS': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'lb-LU': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'lt-LT': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'lv-LV': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'mt-MT': {
        decimalSeparator: '.',
        digitGroupingSeparator: ',',
        currencyPlacement: 'tail',
    },
    'nb-NO': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'nl-BE': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'tail',
    },
    'nl-NL': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'tail',
    },
    'pl-PL': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'uk-UA': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'sv-SE': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'af-ZA': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'head',
    },
    'cs-CZ': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'da-DK': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'tail',
    },
    'el-GR': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'tail',
    },
    'et-EE': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'fi-FI': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'ro-RO': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'tail',
    },
    'sk-SK': {
        decimalSeparator: ',',
        digitGroupingSeparator: '\u00A0',
        currencyPlacement: 'tail',
    },
    'sl-SI': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'tail',
    },
    'sq-AL': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'tail',
    },
    'sr-RS': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'tail',
    },
    'tr-TR': {
        decimalSeparator: ',',
        digitGroupingSeparator: '.',
        currencyPlacement: 'tail',
    },
    'ga-IE': {
        decimalSeparator: '.',
        digitGroupingSeparator: ',',
        currencyPlacement: 'head',
    },
}

export const SUPPORTED_LANGUAGES = keys(LANGUAGE_RULES_MAP)

export const getLanguageRule = (language: Language): LanguageRule => {
    return LANGUAGE_RULES_MAP[language]
}

export type FormattedMessagesMap = Record<string, string>

export type LanguageSettings = {
    currentSelectedLanguage: Language
    formattedMessagesMap: FormattedMessagesMap
}

export const DEFAULT_LANGUAGE: Language = 'en-GB'
