{"name": "@zeal/uikit", "version": "0.0.1", "license": "UNLICENSED", "private": true, "scripts": {"lint": "yarn eslint --cache --cache-location ../../.eslintcache/uikit.cache --max-warnings 0 .", "scss-types": "typed-scss-modules . --nameFormat none --exportType default"}, "lint-staged": {"*.{js,jsx,ts,tsx,mdx}": ["eslint --ignore-pattern '!.storybook' --max-warnings 0"]}, "peerDependencies": {"react": "*", "react-native": "*"}, "devDependencies": {"@types/big.js": "6.1.5", "@types/react-native-web": "0.19.1", "@zeal/eslint-config": "workspace:*", "eslint": "8.3.0", "lint-staged": "15.2.2", "rimraf": "5.0.0", "typescript": "5.5.3"}, "dependencies": {"@gorhom/portal": "1.0.14", "@zeal/toolkit": "workspace:*", "big.js": "6.2.1", "expo-blur": "14.0.3", "expo-camera": "16.0.18", "expo-crypto": "14.0.2", "expo-image": "2.0.7", "expo-linear-gradient": "14.0.2", "expo-navigation-bar": "4.0.9", "expo-splash-screen": "0.29.24", "expo-status-bar": "2.0.1", "lottie-react-native": "6.7.0", "lottie-web": "5.10.2", "react-intl": "6.2.10", "react-native-awesome-slider": "2.9.0", "react-native-draglist": "3.6.2", "react-native-gesture-handler": "2.20.2", "react-native-qr-svg": "1.0.1", "react-native-reanimated": "3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-shadow-2": "7.0.8", "react-native-svg": "15.8.0", "rifm": "0.12.1", "slate": "0.88.1", "slate-react": "0.88.2", "typed-scss-modules": "7.0.1"}}