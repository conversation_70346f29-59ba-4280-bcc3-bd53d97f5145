import React, { createContext, useContext } from 'react'
import { StyleSheet, Text as NativeText, TextStyle } from 'react-native'

import { notReachable } from '@zeal/toolkit'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

import { colors } from '../colors'
import { Extractor } from '../Extractor'
import { Language } from '../Language'
import { useLanguage } from '../Language/LanguageContext'

export type FontFamily =
    | 'Lexend-Bold'
    | 'Lexend-Medium'
    | 'Lexend-Regular'
    | 'Lexend-SemiBold'
    | 'Inter-Bold'
    | 'Inter-Medium'
    | 'Inter-Regular'
    | 'Inter-SemiBold'

const fontFamilyLexendMap: Record<Weight, FontFamily> = {
    bold: 'Lexend-Bold',
    medium: 'Lexend-Medium',
    regular: 'Lexend-Regular',
    semi_bold: 'Lexend-SemiBold',
}

const fontFamiltyInterMap: Record<Weight, FontFamily> = {
    bold: 'Inter-Bold',
    medium: 'Inter-Medium',
    regular: 'Inter-Regular',
    semi_bold: 'Inter-SemiBold',
}

const getFontFamilty = (weight: Weight, language: Language) => {
    switch (ZealPlatform.OS) {
        case 'ios':
        case 'android': {
            switch (language) {
                // Latin (Lexend)
                case 'en-GB':
                case 'fr-FR':
                case 'fr-BE':
                case 'de-DE':
                case 'es-ES':
                case 'ca-ES':
                case 'pt-BR':
                case 'pt-PT':
                case 'it-IT':
                case 'hr-HR':
                case 'hu-HU':
                case 'is-IS':
                case 'lb-LU':
                case 'lt-LT':
                case 'lv-LV':
                case 'mt-MT':
                case 'nb-NO':
                case 'nl-BE':
                case 'nl-NL':
                case 'pl-PL':
                case 'sv-SE':
                case 'cs-CZ':
                case 'da-DK':
                case 'et-EE':
                case 'fi-FI':
                case 'af-ZA':
                case 'ro-RO':
                case 'sk-SK':
                case 'sl-SI':
                case 'sq-AL':
                case 'sr-RS': // defaulting to Latin script per app decision
                case 'tr-TR':
                case 'ga-IE':
                    return fontFamilyLexendMap[weight]

                // Non-Latin (Inter)
                case 'uk-UA': // Cyrillic
                case 'bg-BG': // Cyrillic
                case 'el-GR': // Greek
                    return fontFamiltyInterMap[weight]

                default:
                    return notReachable(language)
            }
        }
        case 'web':
            return 'Lexend, Inter, sans-serif'

        default:
            return notReachable(ZealPlatform)
    }
}

export const getFontStyles = (
    weight: Weight,
    language: Language
): TextStyle => {
    const fontFamily = getFontFamilty(weight, language)

    switch (weight) {
        case 'bold': {
            switch (ZealPlatform.OS) {
                case 'ios':
                case 'web':
                    return {
                        fontFamily,
                        fontWeight: '700',
                    }
                case 'android':
                    return {
                        fontFamily,
                    }
                default:
                    return notReachable(ZealPlatform)
            }
        }
        case 'regular':
            return {
                fontFamily,
                fontWeight: '400',
            }
        case 'medium':
            return {
                fontFamily,
                fontWeight: '500',
            }
        case 'semi_bold':
            return {
                fontFamily,
                fontWeight: '600',
            }
        default:
            return notReachable(weight)
    }
}

export const styles = StyleSheet.create({
    base: {
        includeFontPadding: false,
        textAlignVertical: 'bottom',
        minWidth: 0,
    },
    variant_inherit: {},
    variant_caption1: {
        fontSize: 12,
        lineHeight: 15,
    },
    variant_caption2: {
        fontSize: 11,
        lineHeight: 14,
        letterSpacing: 0.0006 * 11,
    },
    variant_homeScreenTitle: {
        fontSize: 34,
        lineHeight: 43,
        letterSpacing: 0.136,
    },

    variant_largeTitle: {
        fontSize: 34,
        letterSpacing: 0.136,
    },

    variant_titleXL: {
        fontSize: 40,
    },

    variant_titleXXL: {
        fontSize: 84,
        lineHeight: 105,
    },

    variant_h1: {
        fontSize: 48,
        lineHeight: 56,
        letterSpacing: -1.44,
    },

    variant_h0: {
        fontSize: 60,
        lineHeight: 67,
        letterSpacing: -1.8,
    },

    variant_title1: {
        fontSize: 28,
        lineHeight: 35,
        letterSpacing: -(28 * 0.0038),
    },
    variant_title2: {
        fontSize: 22,
        lineHeight: 28,
        letterSpacing: -(22 * 0.0026),
    },

    variant_title3: {
        fontSize: 20,
        lineHeight: 25,
        letterSpacing: -0.09,
    },

    variant_callout: {
        fontSize: 16,
        lineHeight: 20,
        letterSpacing: -(16 * 0.0031),
    },

    variant_footnote: {
        fontSize: 13,
        lineHeight: 16,
        letterSpacing: -0.0104,
    },

    variant_input_footnote: {
        fontSize: 13,
        lineHeight: 40,
        letterSpacing: -0.0104,
    },

    variant_paragraph: {
        fontSize: 14,
        lineHeight: 18,
        letterSpacing: -0.0602,
    },

    weight_inherit: {},
    color_textOnDark: {
        color: colors.textOnDark,
    },

    color_gray100: {
        color: colors.gray100,
    },

    color_gray60: {
        color: colors.gray60,
    },

    color_gray20: {
        color: colors.gray20,
    },

    color_gray30: {
        color: colors.gray30,
    },

    color_gray10: {
        color: colors.gray10,
    },

    color_gray40: {
        color: colors.gray40,
    },

    color_gray50: {
        color: colors.gray50,
    },

    color_gray5: {
        color: colors.gray5,
    },

    color_teal40: {
        color: colors.teal40,
    },

    color_teal60: {
        color: colors.teal60,
    },

    color_teal30: {
        color: colors.teal30,
    },

    color_orange20: {
        color: colors.orange20,
    },

    color_orange30: {
        color: colors.orange30,
    },

    color_purple20: {
        color: colors.purple20,
    },

    color_purple40: {
        color: colors.purple40,
    },

    color_green10: {
        color: colors.green10,
    },

    color_green20: {
        color: colors.green20,
    },

    color_blue10: {
        color: colors.blue10,
    },

    color_blue25: {
        color: colors.blue25,
    },

    color_blue30: {
        color: colors.blue30,
    },

    color_textPrimary: {
        color: colors.textPrimary,
    },

    color_textDisabled: {
        color: colors.textDisabled,
    },

    color_textSecondary: {
        color: colors.textSecondary,
    },

    color_textOnColorSecondaryDisabled: {
        color: colors.textOnColorSecondaryDisabled,
    },

    color_iconDisabled: {
        color: colors.iconDisabled,
    },

    color_textError: {
        color: colors.textError,
    },

    color_textStatusCritical: {
        color: colors.textStatusCritical,
    },

    color_textStatusCriticalOnColor: {
        color: colors.textStatusCriticalOnColor,
    },

    color_textStatusCriticalOnColorHover: {
        color: colors.textStatusCriticalOnColorHover,
    },

    color_textStatusCriticalOnColorPressed: {
        color: colors.textStatusCriticalOnColorPressed,
    },

    color_textOnDarkPrimary: {
        color: colors.textOnDarkPrimary,
    },

    color_textOnDarkSecondary: {
        color: colors.textOnDarkSecondary,
    },

    color_textStatusWarning: {
        color: colors.textStatusWarning,
    },

    color_textStatusWarningOnColor: {
        color: colors.textStatusWarningOnColor,
    },

    color_textStatusWarningOnColorHover: {
        color: colors.textStatusWarningOnColorHover,
    },

    color_textStatusWarningOnColorPressed: {
        color: colors.textStatusWarningOnColorPressed,
    },

    color_textStatusNeutralOnColor: {
        color: colors.textStatusNeutralOnColor,
    },

    color_textStatusNeutralOnColorHover: {
        color: colors.textStatusNeutralOnColorHover,
    },

    color_textStatusNeutralOnColorPressed: {
        color: colors.textStatusNeutralOnColorPressed,
    },

    color_textStatusSuccess: {
        color: colors.textStatusSuccess,
    },

    color_textStatusSuccessOnColor: {
        color: colors.textStatusSuccessOnColor,
    },

    color_textStatusSuccessOnColorHover: {
        color: colors.textStatusSuccessOnColorHover,
    },

    color_textStatusSuccessOnColorPressed: {
        color: colors.textStatusSuccessOnColorPressed,
    },

    color_textAccent2: {
        color: colors.textAccent2,
    },

    color_iconDefault: {
        color: colors.iconDefault,
    },

    color_iconPressed: {
        color: colors.iconPressed,
    },

    color_darkActionSecondaryDefault: {
        color: colors.darkActionSecondaryDefault,
    },

    color_darkActionSecondaryHover: {
        color: colors.darkActionSecondaryHover,
    },

    color_darkActionSecondaryPressed: {
        color: colors.darkActionSecondaryPressed,
    },

    color_actionPrimaryPressed: {
        color: colors.actionPrimaryPressed,
    },

    color_red40: {
        color: colors.red40,
    },

    color_red30: {
        color: colors.red30,
    },

    color_green30: {
        color: colors.green30,
    },

    color_iconHover: {
        color: colors.iconHover,
    },
    color_textOnColorPrimiary: {
        color: colors.textOnColorPrimiary,
    },
    color_textOnColorSecondary: {
        color: colors.textOnColorSecondary,
    },
    color_textOnColorSecondaryHover: {
        color: colors.textOnColorSecondaryHover,
    },
    color_textOnColorSecondaryPressed: {
        color: colors.textOnColorSecondaryPressed,
    },
    color_networkPolygon: {
        color: colors.networkPolygon,
    },
    color_networkPolygonZkevm: {
        color: colors.networkPolygonZkevm,
    },
    color_networkLinea: {
        color: colors.networkLinea,
    },
    color_networkEthereum: {
        color: colors.networkEthereum,
    },
    color_networkOptimism: {
        color: colors.networkOptimism,
    },
    color_networkBSC: {
        color: colors.networkBSC,
    },
    color_networkGnosis: {
        color: colors.networkGnosis,
    },
    color_networkFantom: {
        color: colors.networkFantom,
    },
    color_networkArbitrum: {
        color: colors.networkArbitrum,
    },
    color_networkAvalanche: {
        color: colors.networkAvalanche,
    },
    color_networkAurora: {
        color: colors.networkAurora,
    },
    color_networkBase: {
        color: colors.networkBase,
    },
    color_networkBlast: {
        color: colors.networkBlast,
    },
    color_networkOPBNB: {
        color: colors.networkOPBNB,
    },
    color_networkMantle: {
        color: colors.networkOPBNB,
    },
    color_networkManta: {
        color: colors.networkOPBNB,
    },
    color_networkzkSync: {
        color: colors.networkzkSync,
    },
    color_textStatusWarningOnColorDisabled: {
        color: colors.textStatusWarningOnColorDisabled,
    },
    color_textOnPrimary: {
        color: colors.textOnPrimary,
    },

    align_center: {
        textAlign: 'center',
    },
    align_left: {
        textAlign: 'left',
    },
})

export type Weight = 'bold' | 'medium' | 'regular' | 'semi_bold'
export type Variant = Extractor<keyof typeof styles, 'variant'>
export type Color = Extractor<keyof typeof styles, 'color'>
type Align = Extractor<keyof typeof styles, 'align'>

export type TextStyles = {
    variant: Variant
    weight: Weight
    color: Color
}

export const DEFAULT_TEXT_STYLES: TextStyles = {
    color: 'textSecondary',
    weight: 'regular',
    variant: 'paragraph',
}

export const TextStyleInheritContext =
    createContext<TextStyles>(DEFAULT_TEXT_STYLES)

export const useTextStyleInheritContext = () =>
    useContext(TextStyleInheritContext)

export type Props = {
    id?: string
    children: React.ReactNode
    variant?: Variant
    weight?: Weight
    color?: Color
    align?: Align
    ellipsis?: boolean
    textDecorationLine?: 'none' | 'line-through' | 'underline'
}
export const Text = ({
    id,
    children,
    align = 'left',
    color,
    variant,
    weight,
    ellipsis,
    textDecorationLine = 'none',
}: Props) => {
    const { currentSelectedLanguage } = useLanguage()
    const textStylesContext = useTextStyleInheritContext()
    const currentStyles: TextStyles = {
        color: color || textStylesContext.color,
        variant: variant || textStylesContext.variant,
        weight: weight || textStylesContext.weight,
    }

    const fontFamily = getFontFamilty(
        currentStyles.weight,
        currentSelectedLanguage
    )
    const fontStyle = getFontStyles(
        currentStyles.weight,
        currentSelectedLanguage
    )

    return (
        <TextStyleInheritContext.Provider value={currentStyles}>
            <NativeText
                id={id}
                numberOfLines={ellipsis ? 1 : undefined}
                style={[
                    styles.base,
                    styles[`variant_${currentStyles.variant}`],
                    { ...fontStyle, fontFamily },
                    styles[`color_${currentStyles.color}`],
                    styles[`align_${align}`],
                    { textDecorationLine },
                ]}
            >
                {children}
            </NativeText>
        </TextStyleInheritContext.Provider>
    )
}
