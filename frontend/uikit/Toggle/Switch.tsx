import React from 'react'
import { StyleSheet, View } from 'react-native'

import { colors } from '@zeal/uikit/colors'

import { Extractor } from '../Extractor'

const styles = StyleSheet.create({
    track: {
        position: 'relative',
    },

    variantTrackOff_default: {
        backgroundColor: colors.iconDefault,
    },
    variantTrackOff_orange: {
        backgroundColor: colors.orange30,
    },

    track_regular: { width: 40, height: 20, borderRadius: 20 },
    track_small: { width: 32, height: 16, borderRadius: 16 },
    trackDisabled: {},

    variantTrackOn_default: {
        backgroundColor: colors.iconAccent2,
    },
    variantTrackOn_orange: {
        backgroundColor: colors.teal40,
    },

    handle: {
        position: 'absolute',
        left: 2,
        bottom: 2,
        backgroundColor: colors.surfaceDefault,
        // @ts-ignore FIXME @resetko-zeal
        transition: '0.3s',
    },
    handle_regular: { height: 16, width: 16, borderRadius: 8 },
    handle_small: { height: 12, width: 12, borderRadius: 6 },
    handleOn_regular: {
        transform: [{ translateX: 20 }],
    },
    handleOn_small: {
        transform: [{ translateX: 16 }],
    },
    handleDisabled: {},
})

type Size = 'regular' | 'small'

export type Props = {
    'aria-labelledby': string
    disabled?: boolean
    size: Size
    variant: Extractor<keyof typeof styles, 'variantTrackOn'>
    value: boolean
}

export const Switch = ({
    'aria-labelledby': labelledBy,
    disabled,
    variant,
    value,
    size,
}: Props) => {
    return (
        <View
            role="switch"
            aria-labelledby={labelledBy}
            style={[
                styles.track,
                styles[`track_${size}`],
                styles[`variantTrackOff_${variant}`],
                value && styles[`variantTrackOn_${variant}`],
                disabled && styles.trackDisabled,
            ]}
        >
            <View
                style={[
                    styles.handle,
                    styles[`handle_${size}`],
                    value && styles[`handleOn_${size}`],
                    disabled && styles.handleDisabled,
                ]}
            />
        </View>
    )
}
