import React from 'react'
import { ClipP<PERSON>, Defs, G, Path, Rect } from 'react-native-svg'

import { SvgIcon } from '@zeal/uikit/SvgIcon'

type Props = {
    size: number
}

export const MtPelerin = ({ size }: Props) => (
    <SvgIcon viewBox="0 0 73 72" width={size} height={size} fill="none">
        <G clipPath="url(#clip0_11723_1571)">
            <Rect
                width="72"
                height="72"
                transform="translate(0.5)"
                fill="#F8F9FC"
            />
            <Rect x="0.5" width="72" height="72" fill="#30C5FF" />
            <Path
                d="M47.3 36C47.3 29.8755 42.3351 24.9105 36.2105 24.9105C30.086 24.9105 25.1211 29.8755 25.1211 36C25.1211 42.1246 30.086 47.0895 36.2105 47.0895C42.3351 47.0895 47.3 42.1246 47.3 36ZM50.9211 36C50.9211 41.5601 47.836 46.3988 43.2847 48.9C47.836 51.4012 50.9211 56.24 50.9211 61.8H47.3C47.3 55.6755 42.3351 50.7105 36.2105 50.7105C30.086 50.7105 25.1211 55.6755 25.1211 61.8H21.5C21.5 56.2401 24.5848 51.4012 29.1359 48.9C24.5848 46.3988 21.5 41.5599 21.5 36C21.5 30.4401 24.5848 25.6012 29.1359 23.1C24.5848 20.5988 21.5 15.7599 21.5 10.2H25.1211C25.1211 16.3246 30.086 21.2895 36.2105 21.2895C42.3351 21.2895 47.3 16.3246 47.3 10.2H50.9211C50.9211 15.7601 47.836 20.5988 43.2847 23.1C47.836 25.6012 50.9211 30.44 50.9211 36Z"
                fill="white"
            />
        </G>
        <Defs>
            <ClipPath id="clip0_11723_1571">
                <Rect
                    width="72"
                    height="72"
                    fill="white"
                    transform="translate(0.5)"
                />
            </ClipPath>
        </Defs>
    </SvgIcon>
)
