import React from 'react'
import { Path } from 'react-native-svg'

import { SvgIcon } from '@zeal/uikit/SvgIcon'

import { Color, colors } from '../colors'

type Props = {
    size: number
    color?: Color
}

export const ArrowLeftRight = ({ size, color }: Props) => (
    <SvgIcon
        viewBox="0 0 19 18"
        fill="none"
        width={size}
        height={size}
        color={color && colors[color]}
    >
        <Path
            d="M7.25 14.25L2 9.00008L7.25 3.75016M11.75 3.75L17 8.99992L11.75 14.2498"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </SvgIcon>
)
