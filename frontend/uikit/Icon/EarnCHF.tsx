import React from 'react'
import { Path } from 'react-native-svg'

import { SvgIcon } from '@zeal/uikit/SvgIcon'

type Props = {
    size: number
}

export const EarnCHF = ({ size }: Props) => {
    return (
        <SvgIcon viewBox="0 0 120 120" fill="none" width={size} height={size}>
            <Path
                d="M60 120C93.1371 120 120 93.1371 120 60C120 26.8629 93.1371 0 60 0C26.8629 0 0 26.8629 0 60C0 93.1371 26.8629 120 60 120Z"
                fill="#D80027"
            />
            <Path
                d="M91.6086 49.8697H70.7391V29H49.8695V49.8697H29V70.7391H49.8695V91.6086H70.7391V70.7391H91.6086V49.8697Z"
                fill="white"
            />
        </SvgIcon>
    )
}
