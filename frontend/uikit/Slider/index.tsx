import React from 'react'
import { Insets, StyleSheet, View } from 'react-native'
import { Slider as SliderComponent } from 'react-native-awesome-slider'
import { useSharedValue } from 'react-native-reanimated'

import { CompressedButton } from '@zeal/uikit/Button'
import { Color, colors } from '@zeal/uikit/colors'
import { ArrowLeftRight } from '@zeal/uikit/Icon/ArrowLeftRight'

import { RangeInt } from '@zeal/toolkit/Range'

type Props<Start extends number, End extends number> = {
    minValue: Start
    maxValue: End

    initialValue: RangeInt<Start, End>
    onChange: (value: RangeInt<Start, End>) => void
    numSteps: number

    leftTrackColour: Color
    rightTrackColour: Color
}

const THUMB_SIZE = 44

const styles = StyleSheet.create({
    container: {
        borderRadius: 20,
    },
    thumbContainer: {
        height: THUMB_SIZE,
        width: THUMB_SIZE,
    },
})

const PAN_HIT_SLOP: Insets = {
    top: THUMB_SIZE / 2,
    bottom: THUMB_SIZE / 2,
    left: THUMB_SIZE / 2,
    right: THUMB_SIZE / 2,
}

export const Slider = <Start extends number, End extends number>({
    onChange,
    minValue,
    maxValue,
    leftTrackColour,
    rightTrackColour,
    initialValue,
    numSteps,
}: Props<Start, End>) => {
    const progress = useSharedValue<number>(initialValue)
    const min = useSharedValue<number>(minValue)
    const max = useSharedValue<number>(maxValue)

    return (
        <SliderComponent
            containerStyle={styles.container}
            theme={{
                minimumTrackTintColor: colors[leftTrackColour],
                maximumTrackTintColor: colors[rightTrackColour],
            }}
            onValueChange={(val) => onChange(val as RangeInt<Start, End>)}
            panHitSlop={PAN_HIT_SLOP}
            progress={progress}
            minimumValue={min}
            maximumValue={max}
            sliderHeight={8}
            thumbWidth={THUMB_SIZE}
            steps={numSteps}
            forceSnapToStep
            renderBubble={() => null}
            renderMark={() => null}
            renderThumb={() => (
                <View style={styles.thumbContainer}>
                    <CompressedButton size="regular" variant="secondary">
                        {({ size, color }) => (
                            <ArrowLeftRight size={size} color={color} />
                        )}
                    </CompressedButton>
                </View>
            )}
        />
    )
}
