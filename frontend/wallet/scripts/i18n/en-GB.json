{"Account.ListItem.details.label": "Details", "AddFromAddress.success": "<PERSON><PERSON> Saved", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count, plural, =0 {No wallets} one {{count} Wallet} other {{count} Wallets}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Secret Phrase {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Create new wallets from one of your existing Secret Phrases", "AddFromExistingSecretPhrase.SelectPhrase.title": "Pick a Secret Phrase", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "Your Secret Phrase can back up many wallets. Pick the ones you want to use.", "AddFromExistingSecretPhrase.WalletSelection.title": "Quick add a wallet", "AddFromExistingSecretPhrase.success": "Wallets added to Zeal", "AddFromHardwareWallet.subtitle": "Select your hardware wallet to connect to Zeal", "AddFromHardwareWallet.title": "Hardware Wallet", "AddFromNewSecretPhrase.WalletSelection.subtitle": "Select the wallets you want to import", "AddFromNewSecretPhrase.WalletSelection.title": "Import wallets", "AddFromNewSecretPhrase.accounts": "Wallets", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "A Secret Phrase acts like a keychain for millions of wallets, each with a unique private key.{br}{br}You can import as many wallets as you need now or add more later.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Secret Phrase Wallets", "AddFromNewSecretPhrase.subtitle": "Enter your Secret Phrase separated by spaces", "AddFromNewSecretPhrase.success_secret_phrase_added": "Secret Phrase added 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Wallets added to Zeal", "AddFromNewSecretPhrase.wallets": "Wallets", "AddFromPrivateKey.subtitle": "Enter your Private Key", "AddFromPrivateKey.success": "Private key added 🎉", "AddFromPrivateKey.title": "Restore wallet", "AddFromPrivateKey.typeOrPaste": "Type or paste here", "AddFromSecretPhrase.importWallets": "{count, plural,\n                                            =0 {No wallets selected}\n                                            one {Import wallet}\n                                            other {Import {count} wallets}}", "AddFromTrezor.AccountSelection.title": "Import Trezor wallets", "AddFromTrezor.hwWalletTip.subtitle": "A hardware wallet holds millions of wallets with different addresses. You can import as many wallets as you need now or add more later.", "AddFromTrezor.hwWalletTip.title": "Importing from Hardware Wallets", "AddFromTrezor.importAccounts": "{count, plural,\n                                    =0 {No wallets selected}\n                                    one {Import wallet}\n                                    other {Import {count} wallets}}", "AddFromTrezor.success": "Wallets added to Zeal", "ApprovalSpenderTypeCheck.failed.subtitle": "Likely a scam: spenders should be contracts", "ApprovalSpenderTypeCheck.failed.title": "Spender is a wallet, not a contract", "ApprovalSpenderTypeCheck.passed.subtitle": "Generally you approve assets to contracts", "ApprovalSpenderTypeCheck.passed.title": "Spender is a smart contract", "BestReturns.subtitle": "This swap provider will give you the highest output, including all fees.", "BestReturnsPopup.title": "Best returns", "BlacklistCheck.Failed.subtitle": "Malicious reports by <source></source>", "BlacklistCheck.Failed.title": "Site is blacklisted", "BlacklistCheck.Passed.subtitle": "No malicious reports by <source></source>", "BlacklistCheck.Passed.title": "Site is not blacklisted", "BlacklistCheck.failed.statusButton.label": "Site has been reported", "BridgeRoute.slippage": "Slippage {slippage}", "BridgeRoute.title": "Bridge provider", "CheckConfirmation.InProgress": "In progress...", "CheckConfirmation.success.splash": "Complete", "ChooseImportOrCreateSecretPhrase.subtitle": "Import a Secret Phrase or create a new one", "ChooseImportOrCreateSecretPhrase.title": "Add Secret Phrase", "ConfirmTransaction.Simuation.Skeleton.title": "Doing safety checks…", "ConnectionSafetyCheckResult.passed": "Safety Check passed", "ContactGnosisPaysupport": "Contact Gnosis Pay support", "CopyKeyButton.copied": "<PERSON>pied", "CopyKeyButton.copyYourKey": "Copy your key", "CopyKeyButton.copyYourPhrase": "Copy your phrase", "DAppVerificationCheck.Failed.subtitle": "Site isn’t listed on <source></source>", "DAppVerificationCheck.Failed.title": "Site wasn’t found in app registries", "DAppVerificationCheck.Passed.subtitle": "Site is listed on <source></source>", "DAppVerificationCheck.Passed.title": "Site appears in app registries", "DAppVerificationCheck.failed.statusButton.label": "Site wasn’t found in app registries", "ERC20.tokens.emptyState": "We found no tokens", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Priority Fee", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Last {period}: between {from} and {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Base Fee: {baseFee} • Safety buffer: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "We couldn’t get current Base Fee", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Higher than next Nonce. Will get stuck", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "Can’t set nonce lower than current nonce", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "We couldn’t calculate Priority Fee", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "We couldn’t get current Max fee", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "<PERSON>", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Might get stuck until network fees decrease", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "Max Base Fee", "EditFeeModal.Custom.gasLimit.title": "Gas Limit {gasLimit}", "EditFeeModal.Custom.title": "Advanced settings", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Will get stuck until Base Fee decreases", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Low fee. Might get stuck", "EditFeeModal.EditGasLimit.estimatedGas": "Est gas: {estimated} • Safety buffer: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Less than estimated limit. Transaction will fail", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Less than suggested limit. Transaction could fail", "EditFeeModal.EditGasLimit.subtitle": "Set the maximum amount of gas you’d want this transaction to use. Your transaction will fail if you set a limit lower than what it needs", "EditFeeModal.EditGasLimit.title": "Edit gas limit", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Less than minimum gas limit: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "Higher than next Nonce. Will get stuck", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "Can’t set nonce lower than current nonce", "EditFeeModal.EditNonce.subtitle": "Your transaction will get stuck if you set other than the next nonce", "EditFeeModal.EditNonce.title": "Edit nonce", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "Need {amount} to submit", "EditFeeModal.Header.Time.unknown": "Time Unknown", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Max {fee}", "EditFeeModal.Header.fee.unknown": "Fee unknown", "EditFeeModal.Header.subsequent_failed": "Estimates might be old, last refresh failed", "EditFeeModal.Layout.Header.ariaLabel": "Current fee", "EditFeeModal.MaxFee.subtitle": "The Max fee is the most you’d pay for a transaction, but you’ll usually pay the predicted fee. This extra buffer helps your transaction go through, even if the network slows down or becomes more expensive.", "EditFeeModal.MaxFee.title": "Max Network Fee", "EditFeeModal.SelectPreset.Time.unknown": "Time Unknown", "EditFeeModal.SelectPreset.ariaLabel": "Select fee preset", "EditFeeModal.SelectPreset.fast": "Fast", "EditFeeModal.SelectPreset.normal": "Normal", "EditFeeModal.SelectPreset.slow": "Slow", "EditFeeModal.ariaLabel": "Edit network fee", "FailedSimulation.Confirmation.Item.subtitle": "We had an internal error", "FailedSimulation.Confirmation.Item.title": "Couldn’t simulate transaction", "FailedSimulation.Confirmation.subtitle": "Are you sure you want to continue?", "FailedSimulation.Confirmation.title": "You are signing blind", "FailedSimulation.Title": "Simulation error", "FailedSimulation.footer.subtitle": "We had an internal error", "FailedSimulation.footer.title": "Couldn’t simulate transaction", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Need {amount} to submit transaction", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Might take long to process", "FeeForecastWidget.networkFee": "Network fee", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "We couldn’t calculate network fee", "FeeForecastWidget.subsequentFailed.message": "Estimates might be old, last refresh failed", "FeeForecastWidget.unknownDuration": "Unknown", "FeeForecastWidget.unknownFee": "Unknown", "GasCurrencySelector.balance": "Balance: {balance}", "GasCurrencySelector.networkFee": "Network fee", "GasCurrencySelector.payNetworkFeesUsing": "Pay network fees using", "GasCurrencySelector.removeDefaultGasToken.description": "Pay fees from the largest balance", "GasCurrencySelector.removeDefaultGasToken.title": "Automatic fee handling", "GasCurrencySelector.save": "Save", "GoogleDriveBackup.BeforeYouBegin.first_point": "If I forget my Zeal password, I’ll lose my assets forever", "GoogleDriveBackup.BeforeYouBegin.second_point": "If I lose access to my Google Drive or modify my Recovery File, I’ll lose my assets forever", "GoogleDriveBackup.BeforeYouBegin.subtitle": "Please understand and accept the following point on self-custody:", "GoogleDriveBackup.BeforeYouBegin.third_point": "Zeal can’t help me recover my Zeal password or my access to Google Drive", "GoogleDriveBackup.BeforeYouBegin.title": "Before you begin", "GoogleDriveBackup.loader.subtitle": "Please approve the request on Google Drive to upload your Recovery File", "GoogleDriveBackup.loader.title": "Waiting for approval...", "GoogleDriveBackup.success": "Backup successful 🎉", "MonitorOffRamp.overServiceTime": "Most transfers are completed within {estimated_time}, but sometimes they may take longer due to additional checks. This is normal and funds are safe while these checks are being made.{br}{br}If the transaction doesn’t complete within {support_soft_deadline}, please {contact_support}", "MonitorOnRamp.contactSupport": "Contact support", "MonitorOnRamp.from": "From", "MonitorOnRamp.fundsReceived": "Funds received", "MonitorOnRamp.overServiceTime": "Most transfers are completed within {estimated_time}, but sometimes they may take longer due to additional checks. This is normal and funds are safe while these checks are being made.{br}{br}If the transaction doesn’t complete within {support_soft_deadline}, please {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Sending to your wallet", "MonitorOnRamp.to": "To", "MonitorOnRamp.waitingForTransfer": "Waiting for you to transfer funds", "NftCollectionCheck.failed.subtitle": "Collection isn’t verified on <source></source>", "NftCollectionCheck.failed.title": "Collection is not verified", "NftCollectionCheck.passed.subtitle": "Collection is verified on <source></source>", "NftCollectionCheck.passed.title": "Collection is verified", "NftCollectionInfo.entireCollection": "Entire collection", "NoSigningKeyStore.createAccount": "Create Account", "NonceRangeError.biggerThanCurrent.message": "Transaction will get stuck", "NonceRangeError.lessThanCurrent.message": "Transaction will fail", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "Nonce is higher than current Nonce. Decrease Nonce to prevent transaction from getting stuck.", "NonceRangeErrorPopup.biggerThanCurrent.title": "Transaction will get stuck", "P2pReceiverTypeCheck.failed.subtitle": "Are you sending to the correct address?", "P2pReceiverTypeCheck.failed.title": "Receiver is smart contract, not wallet", "P2pReceiverTypeCheck.passed.subtitle": "Generally you send assets to other wallets", "P2pReceiverTypeCheck.passed.title": "Receiver is a wallet", "PasswordCheck.title": "Enter password", "PasswordChecker.subtitle": "Please enter your password to verify it’s you", "PermitExpirationCheck.failed.subtitle": "Keep short and only as long as you need", "PermitExpirationCheck.failed.title": "Long expiry time", "PermitExpirationCheck.passed.subtitle": "How long an app can use your tokens for", "PermitExpirationCheck.passed.title": "Expiry time not too long", "PrivateKeyValidationError.moreThanMaximumWords": "Max {count} words", "PrivateKeyValidationError.notValidPrivateKey": "This is not a valid private key", "PrivateKeyValidationError.secretPhraseIsInvalid": "Secret phrase is not valid", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Word #{index} misspelled or invalid", "PrivateKeyValidationError.wordsCount": "{count, plural,\n                =0 {}\n                one {{count} word}\n                other {{count} words}\n              }", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Secret Phrases and private keys are encrypted and never leave this device", "SecretPhraseReveal.header": "Write down Secret Phrase", "SecretPhraseReveal.hint": "Don’t share your phrase with anyone. Keep it safe and offline", "SecretPhraseReveal.skip.subtitle": "While you can do this later, if you lose this device before writing down your phrase, you’ll lose all assets you’ve added to this wallet", "SecretPhraseReveal.skip.takeTheRisk": "I’ll take the risk", "SecretPhraseReveal.skip.title": "<PERSON>p writing down phrase?", "SecretPhraseReveal.skip.writeDown": "Write down", "SecretPhraseReveal.skipForNow": "Skip for now", "SecretPhraseReveal.subheader": "Please write it down and keep it safely offline. We’ll then ask you to verify it.", "SecretPhraseReveal.verify": "Verify", "SelectCurrency.tokens": "Tokens", "SelectCurrency.tokens.emptyState": "We found no tokens", "SelectRoute.slippage": "Slippage {slippage}", "SelectRoutes.emptyState": "We found no routes for this swap", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Connect wallet", "SendERC20.labelAddress.inputPlaceholder": "Wallet label", "SendERC20.labelAddress.subtitle": "Label this wallet so you can find it later.", "SendERC20.labelAddress.title": "Label this wallet", "SendERC20.send_to": "Send to", "SendERC20.tokens": "Tokens", "SendOrReceive.bankTransfer.primaryText": "Bank Transfer", "SendOrReceive.bankTransfer.shortText": "Free, instant on-ramp and off-ramp", "SendOrReceive.bridge.primaryText": "Bridge", "SendOrReceive.bridge.shortText": "Transfer tokens between networks", "SendOrReceive.receive.primaryText": "Receive", "SendOrReceive.receive.shortText": "Receive tokens or collectibles", "SendOrReceive.send.primaryText": "Send", "SendOrReceive.send.shortText": "Send tokens to any address", "SendOrReceive.swap.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.swap.shortText": "Swap between tokens", "SendSafeTransaction.Confirm.loading": "Doing safety checks…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Encrypt a Recovery File with password", "SetupRecoveryKit.google.subtitle": "Synced {date}", "SetupRecoveryKit.google.title": "Google Drive backup", "SetupRecoveryKit.subtitle": "You’ll need at least one way to restore your account if you uninstall Zeal or switch devices", "SetupRecoveryKit.title": "Set Up Recovery Kit", "SetupRecoveryKit.writeDown.subtitle": "Write down Secret Phrase", "SetupRecoveryKit.writeDown.title": "Manual backup", "Sign.CheckSafeDeployment.activate": "Activate", "Sign.CheckSafeDeployment.subtitle": "Before you can sign in to an app or sign an off-chain message, you need to activate your device on this network. This happens after you have installed or recovered a smart wallet.", "Sign.CheckSafeDeployment.title": "Activate device on this network", "Sign.Simuation.Skeleton.title": "Doing safety checks…", "SignMessageSafetyCheckResult.passed": "Safety Checks Passed", "SignMessageSafetyChecksPopup.title.permits": "Permit safety checks", "SimulationFailedConfirmation.subtitle": "We simulated this transaction and found an issue that would cause it to fail. You can submit this transaction, but it will likely fail and you may lose your network fee.", "SimulationFailedConfirmation.title": "Transaction likely to fail", "SimulationNotSupported.Title": "Simulation not{br}supported on{br}{network}", "SimulationNotSupported.footer.subtitle": "You can still submit this transaction", "SimulationNotSupported.footer.title": "Simulation not supported", "SlippagePopup.custom": "Custom", "SlippagePopup.presetsHeader": "Swap slippage", "SlippagePopup.title": "Slippage settings", "SmartContractBlacklistCheck.failed.subtitle": "Malicious reports by <source></source>", "SmartContractBlacklistCheck.failed.title": "Contract is blacklisted", "SmartContractBlacklistCheck.passed.subtitle": "No malicious reports by <source></source>", "SmartContractBlacklistCheck.passed.title": "Contract is not blacklisted", "SuspiciousCharactersCheck.Failed.subtitle": "This is a common phishing tactic", "SuspiciousCharactersCheck.Failed.title": "We check for common phishing patterns", "SuspiciousCharactersCheck.Passed.subtitle": "We check for phishing attemps", "SuspiciousCharactersCheck.Passed.title": "Address has no unusual characters", "SuspiciousCharactersCheck.failed.statusButton.label": "Address has unusual characters ", "TokenVerificationCheck.failed.subtitle": "To<PERSON> isn’t listed on <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} is not verified by CoinGecko", "TokenVerificationCheck.passed.subtitle": "Token is listed on <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} is verified by CoinGecko", "TopupDapp.MonitorTransaction.success.splash": "Complete", "TransactionSafetyCheckResult.passed": "Safety Checks Passed", "TransactionSimulationCheck.failed.subtitle": "Error: {errorMessage}", "TransactionSimulationCheck.failed.title": "Transaction is likely to fail", "TransactionSimulationCheck.passed.subtitle": "Simulation done using <source></source>", "TransactionSimulationCheck.passed.title": "Transaction preview was successful", "TrezorError.trezor_action_cancelled.action": "Close", "TrezorError.trezor_action_cancelled.subtitle": "You rejected the transaction on your hardware wallet", "TrezorError.trezor_device_used_elsewhere.action": "S<PERSON> Trezor", "TrezorError.trezor_device_used_elsewhere.subtitle": "Make sure to close all other open sessions and retry syncing your <PERSON><PERSON>or", "TrezorError.trezor_method_cancelled.action": "S<PERSON> Trezor", "TrezorError.trezor_method_cancelled.subtitle": "Make sure to allow <PERSON><PERSON><PERSON> to export wallets to Zeal", "TrezorError.trezor_permissions_not_granted.action": "S<PERSON> Trezor", "TrezorError.trezor_permissions_not_granted.subtitle": "Please give Zeal permissions to see all wallets", "TrezorError.trezor_pin_cancelled.action": "S<PERSON> Trezor", "TrezorError.trezor_pin_cancelled.subtitle": "Session canceled on the device", "TrezorError.trezor_popup_closed.action": "S<PERSON> Trezor", "TrezorError.trezor_popup_closed.subtitle": "The Trezor dialogue closed unexpectedly", "TrxLikelyToFail.lessThanEstimatedGas.message": "Transaction will fail", "TrxLikelyToFail.lessThanMinimumGas.message": "Transaction will fail", "TrxLikelyToFail.lessThanSuggestedGas.message": "Likely to fail", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "Transaction Gas Limit is too low. Increase Gas Limit to suggested limit to prevent failure.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "Transaction is likely to fail", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Gas Limit is lower than estimated gas. Increase Gas Limit to suggested limit.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "Transaction will fail", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "Max Base Fee is lower than current base fee. Increase Max Base Fee to prevent transaction from getting stuck.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "Transaction will get stuck", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "Transaction Max Fee is too low. Increase Max Fee to prevent transaction from getting stuck.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "Transaction will get stuck", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Priority Fee is lower than recommended. Increase Priority Fee to speed up transaction.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "Transaction might take long to complete", "UnsupportedMobileNetworkLayout.gotIt": "Got it!", "UnsupportedMobileNetworkLayout.subtitle": "You can’t make transactions or sign messages on network with id {networkHexId} with mobile version of Zeal yet{br}{br}Switch to browser extension to be able to transact on this network, while we’re working hard on adding support for this network 🚀", "UnsupportedMobileNetworkLayout.title": "Network is not supported for mobile version of Zeal", "UnsupportedSafeNetworkLayout.subtitle": "You can’t make transactions or sign messages on {network} with a Zeal Smart Wallet{br}{br}Switch to a supported network or use a Legacy wallet.", "UnsupportedSafeNetworkLayoutk.title": "Network is not supported for Smart Wallet", "UserConfirmationPopup.goBack": "Cancel", "UserConfirmationPopup.submit": "Submit anyway", "ViewPrivateKey.header": "Private Key", "ViewPrivateKey.hint": "Don’t share your private key with anyone. Keep it safe and offline", "ViewPrivateKey.subheader.mobile": "Tap to reveal your Private Key", "ViewPrivateKey.subheader.web": "Hover to reveal your Private Key", "ViewPrivateKey.unblur.mobile": "Tap to reveal", "ViewPrivateKey.unblur.web": "Hover to reveal", "ViewSecretPhrase.PasswordChecker.subtitle": "Enter your password to encrypt the Recovery File. You’ll need to remember it in the future.", "ViewSecretPhrase.done": "Done", "ViewSecretPhrase.header": "Secret Phrase", "ViewSecretPhrase.hint": "Don’t share your phrase with anyone. Keep it safe and offline", "ViewSecretPhrase.subheader.mobile": "Tap to reveal your Secret Phrase", "ViewSecretPhrase.subheader.web": "Hover to reveal your Secret Phrase", "ViewSecretPhrase.unblur.mobile": "Tap to reveal", "ViewSecretPhrase.unblur.web": "Hover to reveal", "account-details.monerium": "Transfers are done using Monerium, an authorized and regulated EMI. <link>Learn more</link>", "account-details.unblock": "Transfers are done using Unblock, an authorised and registered exchange and custody service provider. <link>Learn more</link>", "account-selector.empty-state": "No wallets found", "account-top-up.select-currency.title": "Tokens", "account.accounts_not_found": "We couldn’t find any wallets", "account.accounts_not_found_search_valid_address": "Wallet is not in your list", "account.add.create_new_secret_phrase": "Create Secret Phrase", "account.add.create_new_secret_phrase.subtext": "A new 12-word secret phrase", "account.add.fromRecoveryKit.fileNotFound": "We couldn’t find your file", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "Try again", "account.add.fromRecoveryKit.fileNotFound.explanation": "Please check you logged in to the correct account that has a Zeal Backup folder", "account.add.fromRecoveryKit.fileNotValid": "Recovery File is not valid", "account.add.fromRecoveryKit.fileNotValid.explanation": "We checked your file and either it’s not the right type or it has been modified", "account.add.import_secret_phrase": "Import Secret Phrase", "account.add.import_secret_phrase.subtext": "Created on Zeal, Metamask, or others", "account.add.select_type.add_hardware_wallet": "Hardware Wallet", "account.add.select_type.existing_smart_wallet": "Existing Smart Wallet", "account.add.select_type.private_key": "Private Key", "account.add.select_type.seed_phrase": "Seed Phrase", "account.add.select_type.title": "Import wallet", "account.add.select_type.zeal_recovery_file": "Zeal Recovery File", "account.add.success.title": "New wallet created 🎉", "account.addLabel.header": "Name your wallet", "account.addLabel.labelError.labelAlreadyExist": "Label already exists. Try another label", "account.addLabel.labelError.maxStringLengthExceeded": "Max character count reached", "account.add_active_wallet.primary_text": "Add wallet", "account.add_active_wallet.short_text": "Create, Connect or Import Wallet", "account.add_from_ledger.success": "Wallets added to Zeal", "account.add_tracked_wallet.primary_text": "Add read-only wallet", "account.add_tracked_wallet.short_text": "See portfolio and activity", "account.button.unlabelled-wallet": "Unlabelled wallet", "account.create_wallet": "Create Wallet", "account.label.edit.title": "Edit wallet label", "account.recoveryKit.selectBackupFile.fileDate": "Created {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Recovery File is not valid", "account.recoveryKit.selectBackupFile.subtitle": "Select the Recovery File you want to restore", "account.recoveryKit.selectBackupFile.title": "Recovery File", "account.recoveryKit.success.recoveryFileFound": "Recovery File found 🎉", "account.select_type_of_account.create_eoa.short": "Legacy wallet for experts", "account.select_type_of_account.create_eoa.title": "Create Seed-phrase wallet", "account.select_type_of_account.create_safe_wallet.title": "Create Smart wallet", "account.select_type_of_account.existing_smart_wallet": "Existing Smart wallet", "account.select_type_of_account.hardware_wallet": "Hardware wallet", "account.select_type_of_account.header": "Add wallet", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Private key / Seed phrase", "account.select_type_of_account.read_only_wallet": "Read-only wallet", "account.select_type_of_account.read_only_wallet.short": "Preview any portfolio", "account.topup.title": "Add funds to Zeal", "account.view.error.refreshAssets": "Refresh", "account.widget.refresh": "Refresh", "account.widget.settings": "Settings", "accounts.view.copied-text": "Copied {formattedAddress}", "accounts.view.copiedAddress": "Copied {formattedAddress}", "action.accept": "Accept", "action.accpet": "Accept", "action.allow": "Allow", "action.back": "Back", "action.cancel": "Cancel", "action.card-activation.title": "Activate card", "action.claim": "<PERSON><PERSON><PERSON>", "action.close": "Close", "action.complete-steps": "Complete steps", "action.confirm": "Confirm", "action.continue": "Continue", "action.copy-address-understand": "Ok - Copy address", "action.deposit": "<PERSON><PERSON><PERSON><PERSON>", "action.done": "Done", "action.dontAllow": "Don’t Allow", "action.edit": "edit", "action.email-required": "Enter email", "action.enterPhoneNumber": "Enter phone number", "action.expand": "Expand", "action.fix": "Fix", "action.getStarted": "Get Started", "action.got_it": "Got it", "action.hide": "<PERSON>de", "action.import": "Import", "action.import-keys": "Import keys", "action.importKeys": "Import Keys", "action.minimize": "Minimize", "action.next": "Next", "action.ok": "OK", "action.reduceAmount": "Reduce to max", "action.refreshWebsite": "Refresh website", "action.remove": "Remove", "action.remove-account": "Remove account", "action.requestCode": "Request code", "action.resend_code": "Resend code", "action.resend_code_with_time": "Resend code {time}", "action.retry": "Retry", "action.reveal": "Reveal", "action.save": "Save", "action.save_changes": "Save RPC", "action.search": "Search", "action.seeAll": "See all", "action.select": "Select", "action.send": "Send", "action.skip": "<PERSON><PERSON>", "action.submit": "Submit", "action.understood": "I understand", "action.update": "Update", "action.update-gnosis-pay-owner.complete": "Complete steps", "action.zeroAmount": "Enter amount", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Collectibles", "action_bar_title.tokens": "Tokens", "action_bar_title.transaction_request": "Transaction request", "activate-monerium.loading": "Setting up your personal account", "activate-monerium.success.title": "Monerium enabled", "activate-physical-card-widget.subtitle": "Delivery can take 3 weeks", "activate-physical-card-widget.title": "Activate physical card", "activate-smart-wallet.title": "Activate wallet", "active_and_tracked_wallets.title": "Zeal covers all your fees on {network}, allowing you to transact for free!", "activity.approval-amount.revoked": "Revoked", "activity.approval-amount.unlimited": "Unlimited", "activity.approval.approved_for": "Approved for", "activity.approval.approved_for_with_target": "Approved {approvedTo}", "activity.approval.revoked_for": "Revoked for", "activity.bank.serviceProvider": "Service provider", "activity.bridge.serviceProvider": "Service provider", "activity.cashback.period": "Cashback Period", "activity.filter.card": "Card", "activity.rate": "Rate", "activity.receive.receivedFrom": "Received from", "activity.send.sendTo": "Sent to", "activity.smartContract.unknown": "Unknown contract", "activity.smartContract.usingContract": "Using contract", "activity.subtitle.pending_timer": "{timerString} Pending", "activity.title.arbitrary_smart_contract_interaction": "{function} on {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Unknown contract interaction", "activity.title.bridge.from": "Bridge from {token}", "activity.title.bridge.to": "Bridge to {token}", "activity.title.buy": "Buy {asset}", "activity.title.card_owners_updated": "Card owners updated", "activity.title.card_spend_limit_updated": "Card spend limit set", "activity.title.cashback_deposit": "Deposit to <PERSON><PERSON>", "activity.title.cashback_reward": "Rewarded Cashback", "activity.title.cashback_withdraw": "Withdraw from Cashback", "activity.title.claimed_reward": "Claimed reward", "activity.title.deployed_smart_wallet_gnosis": "Account created", "activity.title.deposit_from_bank": "Deposit from Bank", "activity.title.deposit_into_card": "Deposit to <PERSON>", "activity.title.deposit_into_earn": "Deposit to {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} on {smartContract}", "activity.title.from": "From {sender}", "activity.title.pendidng_areward_claim": "Claiming reward", "activity.title.pendidng_breward_claim": "Claiming reward", "activity.title.recharge_disabledh": "Card recharge disabled", "activity.title.recharge_set": "Recharge target set", "activity.title.recovered_smart_wallet_gnosis": "New device installation", "activity.title.send_pending": "To {receiver}", "activity.title.send_to_bank": "To Bank", "activity.title.swap": "Buy {token}", "activity.title.to": "To {receiver}", "activity.title.withdraw_from_card": "Withdraw from Card", "activity.title.withdraw_from_earn": "Withdraw from {earn}", "activity.transaction.networkFees": "Network fees", "activity.transaction.state": "Completed Transaction", "activity.transaction.state.completed": "Completed Transaction", "activity.transaction.state.failed": "Failed Transaction", "add-account.section.import.header": "Import", "add-another-card-owner": "Add another card owner", "add-another-card-owner.Recommended.footnote": "Add your Zeal wallet as an additional owner to your Gnosis Pay card", "add-another-card-owner.Recommended.primaryText": "Add Zeal to Gnosis Pay", "add-another-card-owner.recommended": "Recommended", "add-owner.confirmation.subtitle": "For security, settings changes take 3 minutes to process, during which your card will be temporarily frozen, and payments won’t be possible.", "add-owner.confirmation.title": "Your card will be frozen for 3min while settings update", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Can’t add wallet, it’s already in use", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "Try another wallet", "add.account.backup.decrypt.success": "Wallet restored", "add.account.backup.password.passwordIncorrectMessage": "Password is incorrect", "add.account.backup.password.subtitle": "Please enter the password you used to encrypt your Recovery File", "add.account.backup.password.title": "Enter password", "add.account.google.login.subtitle": "Please approve request on Google Drive to sync your Recovery File", "add.account.google.login.title": "Waiting for approval...", "add.readonly.already_added": "Wallet already added", "add.readonly.continue": "Continue", "add.readonly.empty": "Enter an address or ENS", "addBankRecipient.title": "Add bank recipient", "add_funds.deposit_from_bank_account": "Deposit from bank account", "add_funds.from_another_wallet": "From another wallet", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Connect to topup dApp", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Connect any wallet to the Zeal topup dApp and quickly send funds to your wallet", "add_funds.from_crypto_wallet.header": "From another wallet", "add_funds.from_crypto_wallet.header.show_wallet_address": "Show your wallet address", "add_funds.from_exchange.header": "Send from exchange", "add_funds.from_exchange.header.copy_wallet_address": "Copy your Zeal address", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance etc", "add_funds.from_exchange.header.open_exchange": "Open exchange app or site", "add_funds.from_exchange.header.selected_token": "Send {token} to Zeal", "add_funds.from_exchange.header.selected_token.subtitle": "On {network}", "add_funds.from_exchange.header.send_selected_token": "Send supported token", "add_funds.from_exchange.header.send_selected_token.subtitle": "Select supported token & network", "add_funds.import_wallet": "Import existing crypto wallet", "add_funds.title": "Fund your account", "add_funds.transfer_from_exchange": "Transfer from exchange", "address.add.header": "See your wallet in Zeal{br}with read-only mode", "address.add.subheader": "Enter your address or ENS to see your assets on all EVM networks in one place. Create or import more wallets later.", "address_book.change_account.bank_transfers.header": "Bank recipients", "address_book.change_account.bank_transfers.primary": "Bank recipient", "address_book.change_account.cta": "Track wallet", "address_book.change_account.search_placeholder": "Add or search address", "address_book.change_account.tracked_header": "Read-only wallets", "address_book.change_account.wallets_header": "Active wallets", "app-association-check-failed.modal.cta": "Try again", "app-association-check-failed.modal.subtitle": "Please try again. Connectivity issues is causing delays when fetching your Passkeys. If the issue persists then restart Zeal and try one more time.", "app-association-check-failed.modal.subtitle.creation": "Please try again. Connectivity issues is causing delays in Passkey creation. If the issue persists then restart Zeal and try one more time.", "app-association-check-failed.modal.title.creation": "Your device failed to create a passkey", "app-association-check-failed.modal.title.signing": "Your device failed to load passkeys", "app.app_protocol_group.borrowed_tokens": "Borrowed tokens", "app.app_protocol_group.claimable_amount": "Claimable amount", "app.app_protocol_group.health_rate": "Health rate", "app.app_protocol_group.lending": "Lending", "app.app_protocol_group.locked_tokens": "Locked tokens", "app.app_protocol_group.nfts": "Collectibles", "app.app_protocol_group.reward_tokens": "Reward tokens", "app.app_protocol_group.supplied_tokens": "Supplied tokens", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Vesting token", "app.appsGroupHeader.discoverMore": "Discover more", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Search or enter URL", "app.error-banner.cory": "Copy error data", "app.error-banner.retry": "Retry", "app.list_item.rewards": "Rewards {value}", "app.position_details.health_rate.description": "The health is calculated by dividing the amount of your loan by the value of your collateral.", "app.position_details.health_rate.title": "What is health rate?", "approval.edit-limit.label": "Edit spend limit", "approval.permit_info": "Permit information", "approval.spend-limit.edit-modal.cancel": "Cancel", "approval.spend-limit.edit-modal.limit-label": "Spend limit", "approval.spend-limit.edit-modal.max-limit-error": "Warning, high limit", "approval.spend-limit.edit-modal.revert": "Revert changes", "approval.spend-limit.edit-modal.set-to-unlimited": "Set to Unlimited", "approval.spend-limit.edit-modal.submit": "Save changes", "approval.spend-limit.edit-modal.title": "Edit permissions", "approval.spend_limit_info": "What is spend limit?", "approval.what_are_approvals": "What are Approvals?", "apps_list.page.emptyState": "No active apps", "backpace.removeLastDigit": "Remove last digit", "backup-banner.backup_now": "Back up", "backup-banner.risk_losing_funds": "Backup now or risk losing funds", "backup-banner.title": "<PERSON>et not backed up", "backupRecoverySmartWallet.noExportPrivateKeys": "Automatic backup: Your Smart Wallet is saved as a passkey - no seed phrase or private key needed.", "backupRecoverySmartWallet.safeContracts": "Multi-key security: Zeal wallets run on Safe contracts, so several devices can approve a transaction. No single point of failure.", "backupRecoverySmartWallet.security": "Multiple devices: You can use your wallet on multiple devices with the Passkey. Each device gets its own private key.", "backupRecoverySmartWallet.showLocalPrivateKey": "Expert mode: You can export this device’s private key, use it in another wallet and connect on <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Show private key</Key>", "backupRecoverySmartWallet.storingKeys": "Cloud-synced: The passkey is stored securely in iCloud, Google Password Manager, or your password manager.", "backupRecoverySmartWallet.title": "Smart Wallet Backup & Recovery", "balance-change.card.titile": "Card", "balanceChange.pending": "Pending", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Select service provider", "bank-transfer.change-deposit-receiver.subtitle": "This wallet will receive all bank deposits", "bank-transfer.change-deposit-receiver.title": "Set receiving wallet", "bank-transfer.change-owner.subtitle": "This wallet is used to sign into and recover your bank transfer account", "bank-transfer.change-owner.title": "Set account owner", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Any and all bank deposits you send to Zeal will be received by this wallet.", "bank-transfer.configrm-change-deposit-receiver.title": "Change receiving wallet", "bank-transfer.configrm-change-owner.subtitle": "Are you sure you want to change account owner. This wallet is used to sign into and recover your bank transfer account.", "bank-transfer.configrm-change-owner.title": "Change account owner", "bank-transfer.deposit.widget.status.complete": "Complete", "bank-transfer.deposit.widget.status.funds_received": "Funds received", "bank-transfer.deposit.widget.status.sending_to_wallet": "Sending to wallet", "bank-transfer.deposit.widget.status.transfer-on-hold": "Transfer on hold", "bank-transfer.deposit.widget.status.transfer-received": "Sending to wallet", "bank-transfer.deposit.widget.subtitle": "{from} to {to}", "bank-transfer.deposit.widget.title": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.intro.bulletlist.point_1": "Set up with Unblock", "bank-transfer.intro.bulletlist.point_2": "Transfer between EUR/GBP and more than 10 tokens", "bank-transfer.intro.bulletlist.point_3": "0% Fees up to $5k monthly, 0.2% after that", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Sending to bank", "bank-transfer.withdrawal.widget.status.in-progress": "Making transfer", "bank-transfer.withdrawal.widget.status.on-hold": "Transfer on hold", "bank-transfer.withdrawal.widget.status.success": "Complete", "bank-transfer.withdrawal.widget.subtitle": "{from} to {to}", "bank-transfer.withdrawal.widget.title": "<PERSON><PERSON><PERSON>", "bank-transfers.bank-account-actions.remove-this-account": "Remove this account", "bank-transfers.bank-account-actions.switch-to-this-account": "Switch to this account", "bank-transfers.deposit.fees-for-less-than-5k": "Fees for $5k or less", "bank-transfers.deposit.fees-for-more-than-5k": "Fees for more than $5k", "bank-transfers.set-receiving-bank.title": "Set receiving bank", "bank-transfers.settings.account_owner": "Account owner", "bank-transfers.settings.receiver_of_bank_deposits": "Receiver of bank deposits", "bank-transfers.settings.receiver_of_withdrawals": "Receiver of withdrawals", "bank-transfers.settings.registered_email": "Registered Email", "bank-transfers.settings.title": "Bank transfer settings", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} Account", "bank-transfers.setup.bank-account": "Bank account", "bankTransfer.withdraw.max_loading": "Max: {amount}", "bank_details_do_not_match.got_it": "Got it", "bank_details_do_not_match.subtitle": "The sort code and account number don’t match. Please double check that details were added correctly and try again.", "bank_details_do_not_match.title": "Bank details don’t match", "bank_tranfsers.select_country_of_residence.country_not_supported": "Sorry, bank transfers aren’t supported in {country} yet", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Open your banking app", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Send {fiatCurrencyCode} to your account", "bank_transfer.deposit.header": "{fullName}’s personal account&nbsp;details", "bank_transfer.kyc_status_widget.subtitle": "Bank transfers", "bank_transfer.kyc_status_widget.title": "Verifying identity", "bank_transfer.personal_details.date_of_birth": "Date of birth", "bank_transfer.personal_details.date_of_birth.invalid_format": "Date is invalid", "bank_transfer.personal_details.date_of_birth.too_young": "You must be at least 18 years old", "bank_transfer.personal_details.first_name": "First name", "bank_transfer.personal_details.last_name": "Last name", "bank_transfer.personal_details.title": "Your details", "bank_transfer.reference.label": "Reference (Optional)", "bank_transfer.reference_message": "<PERSON>t from Zeal", "bank_transfer.residence_details.address": "Your address", "bank_transfer.residence_details.city": "City", "bank_transfer.residence_details.country_of_residence": "Country of residence", "bank_transfer.residence_details.country_placeholder": "Country", "bank_transfer.residence_details.postcode": "Postcode", "bank_transfer.residence_details.street": "Street", "bank_transfer.residence_details.your_residence": "Your residence", "bank_transfers.choose-wallet.continue": "Continue", "bank_transfers.choose-wallet.test": "Add wallet", "bank_transfers.choose-wallet.warning.subtitle": "You can only link one wallet at a time. You won’t be able to change the linked wallet.", "bank_transfers.choose-wallet.warning.title": "Choose your wallet wisely", "bank_transfers.choose_wallet.subtitle": "Choose the wallet you want to link your bank account to for direct transfers. ", "bank_transfers.choose_wallet.title": "Choose wallet", "bank_transfers.continue": "Continue", "bank_transfers.currency_is_currently_not_supported": "Continue", "bank_transfers.deposit-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.account-name": "Account name", "bank_transfers.deposit.account-number-copied": "Copied Account Number", "bank_transfers.deposit.amount-input": "Amount to deposit", "bank_transfers.deposit.amount-output": "Destination amount", "bank_transfers.deposit.amount-output.error": "error", "bank_transfers.deposit.buttet-point.receive-crypto": "Receive {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "Continue", "bank_transfers.deposit.currency-not-supported.subtitle": "Bank deposits from {code} have been disabled until further notice.", "bank_transfers.deposit.currency-not-supported.title": "{code} deposits currently not supported", "bank_transfers.deposit.default-token.balance": "Balance {amount}", "bank_transfers.deposit.deposit-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.enter_amount": "Enter Amount", "bank_transfers.deposit.iban-copied": "Copied IBAN", "bank_transfers.deposit.increase-amount": "Minimum transfer is {limit}", "bank_transfers.deposit.loading": "Loading", "bank_transfers.deposit.max-limit-reached": "Amount exceeds max transfer limit", "bank_transfers.deposit.modal.kyc.button-text": "Get started", "bank_transfers.deposit.modal.kyc.text": "To verify your identity we’ll need some personal details and documentation. This usually only takes a couple of minutes to submit.", "bank_transfers.deposit.modal.kyc.title": "Verify your identity to increase limits", "bank_transfers.deposit.reduce_amount": "Reduce amount", "bank_transfers.deposit.show-account.account-number": "Account number", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Sort code", "bank_transfers.deposit.sort-code-copied": "Copied Sort Code", "bank_transfers.deposit.withdraw-header": "Withdraw", "bank_transfers.failed_to_load_fee": "Unknown", "bank_transfers.fees": "Fees", "bank_transfers.increase-amount": "Minimum transfer is {limit}", "bank_transfers.insufficient-funds": "Insufficient funds", "bank_transfers.select_country_of_residence.title": "Where do you live?", "bank_transfers.setup.cta": "Set up bank transfers", "bank_transfers.setup.enter-amount": "Enter amount", "bank_transfers.source_of_funds.form.business_income": "Business income", "bank_transfers.source_of_funds.form.other": "Other", "bank_transfers.source_of_funds.form.pension": "Pension", "bank_transfers.source_of_funds.form.salary": "Salary", "bank_transfers.source_of_funds.form.title": "Your source of funds", "bank_transfers.source_of_funds_description.placeholder": "Describe source of funds...", "bank_transfers.source_of_funds_description.title": "Tell us more about your source of funds", "bank_transfers.withdraw-header": "Withdraw", "bank_transfers.withdraw.amount-input": "Amount to withdraw", "bank_transfers.withdraw.max-limit-reached": "Amount exceeds max transfer limit", "bank_transfers.withdrawal.verify-id": "Reduce amount", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Maximum input limit exceeded", "banner.above_maximum_limit.maximum_limit_per_deposit": "This is the maximum limit per deposit", "banner.above_maximum_limit.subtitle": "Maximum input limit exceeded", "banner.above_maximum_limit.title": "Reduce the amount to {amount} or less", "banner.above_maximum_limit.title.default": "Reduce the amount", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Minimum input limit exceeded", "banner.below_minimum_limit.minimum_limit_for_token": "This is the minimum limit for this token", "banner.below_minimum_limit.title": "Increase amount to {amount} or more", "banner.below_minimum_limit.title.default": "Increase the amount", "breaard.in_porgress.info_popup.cta": "Spend to earn {earn}", "breaard.in_porgress.info_popup.footnote": "By using Zeal and Gnosis Pay card you agree to the terms and conditions of this rewards campaign.", "breaward.in_porgress.info_popup.bullet_point_1": "Spend {remaining} within the next {time} to claim this reward.", "breaward.in_porgress.info_popup.bullet_point_2": "Only valid Gnosis Pay purchases count towards your spend amount.", "breaward.in_porgress.info_popup.bullet_point_3": "After claiming your reward it will be sent to your Zeal account.", "breaward.in_porgress.info_popup.header": "Earn {earn}, by spending {remaining}", "breward.celebration.for_spending": "For spending with your card", "breward.dc25-eligible-celebration.for_spending": "You are among the first {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "You weren’t in the first {limit} to spend", "breward.expired_banner.earn_by_spending": "Earn {earn} by spending {amount}", "breward.expired_banner.reward_expired": "{earn} reward expired", "breward.in_progress_banner.cta.title": "Spend to earn {earn}", "breward.ready_to_claim.error.try_again": "Try again", "breward.ready_to_claim.error_title": "Failed to claim reward", "breward.ready_to_claim.in_progress": "Claiming reward", "breward.ready_to_claim.youve_earned": "You’ve earned {earn}!", "breward_already_claimed.title": "<PERSON><PERSON> is already claimed. If you did not recieve reward token please contact support", "breward_cannotbe_claimed.title": "<PERSON><PERSON> cannot be claimed right now. Please try again later", "bridge.best_return": "Best return route", "bridge.best_serivce_time": "Best service time route", "bridge.check_status.complete": "Complete", "bridge.check_status.progress_text": "Bridging {from} to {to}", "bridge.remove_topup": "Remove <PERSON>up", "bridge.request_status.completed": "Completed", "bridge.request_status.pending": "Pending", "bridge.widget.completed": "Complete", "bridge.widget.currencies": "{from} to {to}", "bridge_rote.widget.title": "Bridge", "browse.discover_more_apps": "Discover more Apps", "browse.google_search_term": "Search \"{searchTerm}\"", "brward.celebration.you_earned": "You’ve earned", "brward.expired_banner.subtitle": "Better luck next time", "brward.in_progress_banner.subtitle": "Expires in {expiredInFormatted}", "buy": "Buy", "buy.enter_amount": "Enter Amount", "buy.loading": "Loading...", "buy.no_routes_found": "No routes found", "buy.not_enough_balance": "Not enough balance", "buy.select-currency.title": "Select token", "buy.select-to-currency.title": "Buy tokens", "buy_form.title": "Buy token", "cancelled-card.create-card-button.primary": "Get new virtual card", "cancelled-card.switch-card-button.primary": "Switch card", "cancelled-card.switch-card-button.short-text": "You have another active card", "card": "Card", "card-add-cash.confirm-stage.banner.no-routes-found": "No routes, try a different token or amount", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "You need {amount} more {symbol} to pay fees", "card-add-cash.confirm-stage.banner.value-loss": "You’ll lose {loss} of value", "card-add-cash.confirm-stage.banner.value-loss.revert": "<PERSON><PERSON>", "card-add-cash.edit-stage.cta.cancel": "Cancel", "card-add-cash.edit-stage.cta.continue": "Continue", "card-add-cash.edit-stage.cta.enter-amount": "Enter amount", "card-add-cash.edit-stage.cta.reduce-to-max": "Reduce to max", "card-add-cash.edit-staget.banner.no-routes-found": "No routes, try a different token or amount", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "We sent the transaction request to your hardware wallet. Please continue there.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Sign hardware wallet", "card-balance": "Balance: {balance}", "card-cashback.status.title": "Deposit into Cashback", "card-copy-safe-address.copy_address": "Copy address", "card-copy-safe-address.copy_address.done": "<PERSON>pied", "card-copy-safe-address.warning.description": "This address can only receive {cardAsset} on Gnosis Chain. Don’t send assets on other networks to this address. They will be lost.", "card-copy-safe-address.warning.header": "Only send {cardAsset} on Gnosis Chain", "card-marketing-card.center.subtitle": "FX Fees", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "Interest", "card-marketing-card.right.subtitle": "Signup Gift", "card-marketing-card.title": "Europe’s high interest VISA card", "card-marketing-tile.get-started": "Get started", "card-select-from-token-title": "Select From token", "card-top-up.banner.subtitle.completed": "Completed", "card-top-up.banner.subtitle.failed": "Failed", "card-top-up.banner.subtitle.pending": "{timerString} Pending", "card-top-up.banner.title": "Depositing {amount}", "card-topup.select-token.emptyState": "We found no tokens", "card.activate.card_number_not_valid": "Card number not valid. Check and try again.", "card.activate.invalid_card_number": "Invalid card number.", "card.activation.activate_physical_card": "Activate Physical card", "card.add-cash.amount-to-withdraw": "Top up amount", "card.add-from-earn-form.title": "Add cash to Card", "card.add-from-earn-form.withdraw-to-card": "Continue", "card.add-from-earn.amount-to-withdraw": "Amount to withdraw to Card", "card.add-from-earn.enter-amount": "Enter Amount", "card.add-from-earn.loading": "Loading", "card.add-from-earn.max-label": "Balance: {amount}", "card.add-from-earn.no-routes-found": "No routes found", "card.add-from-earn.not-enough-balance": "Not enough balance", "card.add-owner.queued": "Add owner queued", "card.add-to-wallet-flow.subtitle": "Make payments from your wallet", "card.add-to-wallet.copy-card-number": "Copy Card number below", "card.add-to-wallet.title": "Add to {platformName} Wallet", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "CANCELLED", "card.card-owner-not-found.disconnect-btn": "Disconnect Card from Zeal", "card.card-owner-not-found.subtitle": "To continue using your Gnosis Pay card in Zeal, please update your card owner to reconnect it", "card.card-owner-not-found.title": "Reconnect Card", "card.card-owner-not-found.update-owner-btn": "Update Card Owner", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} in {date}", "card.cashback.widgetNoCashback.subtitle": "Deposit to start earning", "card.cashback.widgetNoCashback.title": "Get up to {defaultPercentage} Cashback", "card.cashback.widgetcashbackValue.rewards": "{amount} pending", "card.cashback.widgetcashbackValue.title": "{percentage} Cashback", "card.choose-wallet.connect_card": "Connect card", "card.choose-wallet.create-new": "Add a new wallet as owner", "card.choose-wallet.import-another-wallet": "Import another wallet", "card.choose-wallet.import-current-owner": "Import current card owner", "card.choose-wallet.import-current-owner.sub-text": "Import private keys or seed phrase that owns your Gnosis Pay card", "card.choose-wallet.title": "Select wallet to manage your card", "card.connectWalletToCardGuide": "Copy wallet address", "card.connectWalletToCardGuide.addGnosisPayOwner": "Add Gnosis Pay Owner", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Open Gnosispay.com with your other wallet{br}2. <PERSON>lick “Account”{br}3. <PERSON>lick “Account details”{br}4. <PERSON>lick “Edit”, next to “Account Owner”, and{br}5. <PERSON>lick “Add address”{br}6. Paste your Zeal address and click save", "card.connectWalletToCardGuide.header": "Connect {account} to Gnosis Pay Card", "card.connect_card.start": "Connect existing Gnosis Pay Card", "card.copiedAddress": "Copied {formattedAddress}", "card.disconnect-account.title": "Disconnect account", "card.hw-wallet-support-drop.add-owner-btn": "Add new owner to Card", "card.hw-wallet-support-drop.disconnect-btn": "Disconnect Card from Zeal", "card.hw-wallet-support-drop.subtitle": "To continue using your Gnosis Pay Card in Zeal please add another owner to your Card that is not a Hardware Wallet.", "card.hw-wallet-support-drop.title": "Zeal no longer supports Hardware wallets for Card", "card.kyc.continue": "Continue setup", "card.list_item.title": "Card", "card.onboarded.transactions.empty.description": "Your payment activity will appear here", "card.onboarded.transactions.empty.title": "Activity", "card.order.continue": "Continue card order", "card.order.free_virtual_card": "Get free virtual card now", "card.order.start": "Order card for free", "card.owner-not-imported.cancel": "Cancel", "card.owner-not-imported.import": "Import wallet", "card.owner-not-imported.subtitle": "To authorise this transaction, link the owner wallet of your Gnosis Pay account to Zeal. Note: This is separate from your usual Gnosis Pay wallet sign-in.", "card.owner-not-imported.title": "Add Gnosis Pay account owner", "card.page.order_free_physical_card": "Order free physical card", "card.pin.change_pin_at_atm": "The PIN can be changed at selected ATMs", "card.pin.timeout": "Screen will close in {seconds} sec", "card.quick-actions.add-assets": "Add cash", "card.quick-actions.add-cash": "Add cash", "card.quick-actions.details": "Details", "card.quick-actions.freeze": "Freeze", "card.quick-actions.freezing": "Freezing", "card.quick-actions.unfreeze": "Unfreeze", "card.quick-actions.unfreezing": "Unfreezing", "card.quick-actions.withdraw": "Withdraw", "card.read-only-detected.create-new": "Add a new wallet as owner", "card.read-only-detected.import-current-owner": "Import keys for {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Import private keys or seed phrase of wallet {address}", "card.read-only-detected.title": "Card detected on read-only wallet. Select wallet to manage card", "card.remove-owner.queued": "Remove owner queued", "card.settings.disconnect-from-zeal": "Disconnect from Zeal", "card.settings.edit-owners": "Change card owners", "card.settings.getCard": "Get another Card", "card.settings.getCard.subtitle": "Virtual or physical cards", "card.settings.notRecharging": "Not recharging", "card.settings.notifications.subtitle": "Get payment notifications", "card.settings.notifications.title": "Card notifications", "card.settings.page.title": "Card Settings", "card.settings.select-card.cancelled-cards": "Cancelled cards", "card.settings.setAutoRecharge": "Set auto-recharge", "card.settings.show-card-address": "Show card address", "card.settings.spend-limit": "Set spend limit", "card.settings.spend-limit-title": "Current daily limit: {limit}", "card.settings.switch-active-card": "Switch active card", "card.settings.switch-active-card-description": "Active card: {card}", "card.settings.switch-card.card-item.cancelled": "Cancelled", "card.settings.switch-card.card-item.frozen": "Frozen", "card.settings.switch-card.card-item.title": "Gnosis Pay Card", "card.settings.switch-card.card-item.title.physical": "Physical Card", "card.settings.switch-card.card-item.title.virtual": "Virtual Card", "card.settings.switch-card.title": "Select card", "card.settings.targetBalance": "Target balance: {threshold}", "card.settings.view-pin": "View PIN", "card.settings.view-pin-description": "Always protect your PIN", "card.title": "Card", "card.transactions.header": "Card transactions", "card.transactions.see_all": "See all transactions", "card.virtual": "VIRTUAL", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Cashback is sent to your card at the start of the week after it was earned.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "The more you deposit the more you earn on every purchase.", "cardCashback.onboarding.title": "Get up to {percentage} Cashback", "cardCashbackWithdraw.amount": "Withdraw amount", "cardCashbackWithdraw.header": "Withdraw {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "Card is blocked and could not be activated", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "Up to {percentage}", "cardWidget.startEarning": "Start earning", "cardWithdraw.amount": "Withdraw amount", "cardWithdraw.header": "Withdraw from card", "cardWithdraw.selectWithdrawWallet.title": "Choose wallet to{br}withdraw to", "cardWithdraw.success.cta": "Close", "cardWithdraw.success.subtitle": "For security, all withdrawals from the Gnosis Pay card take 3 minutes to process", "cardWithdraw.success.title": "This change will take 3 minutes", "card_top_up_trx.send": "Send", "card_top_up_trx.to": "To", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "Expiry date", "cards.card_number": "Card number", "cards.choose-wallet.no-active-accounts": "You don’t have any active wallets", "cards.copied_card_number": "Copied card number", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Exceeds daily limit", "cards.transactions.decline_reason.incorrect_pin": "Incorrect PIN", "cards.transactions.decline_reason.incorrect_security_code": "Incorrect security code", "cards.transactions.decline_reason.invalid_amount": "Invalid amount", "cards.transactions.decline_reason.low_balance": "Low balance", "cards.transactions.decline_reason.other": "Declined", "cards.transactions.decline_reason.pin_tries_exceeded": "PIN tries exceeded", "cards.transactions.status.refund": "Refund", "cards.transactions.status.reversal": "Reversal", "cashback-deposit.trx.title": "Deposit into Cashback", "cashback-estimate.text": "This is an estimate and NOT a guaranteed payouts. All publicly known cashback rules are applied, but Gnosis Pay may exclude transactions at their discretion. A maximum spend of {amount} per week qualifies for Cashback even if the estimate for this transaction would indicate a higher total amount.", "cashback-estimate.text.fallback": "This is an estimate and NOT a guaranteed payouts. All publicly known cashback rules are applied, but Gnosis Pay may exclude transactions at their discretion", "cashback-estimate.title": "Cashback estimate", "cashback-onbarding-tersm.subtitle": "Your Card transaction data will be shared with <PERSON><PERSON><PERSON><PERSON>, who’s responsible for distributing Cashback rewards. By clicking accept you agree to Gnosis DAO Cashback <terms>Terms and Conditions</terms>", "cashback-onbarding-tersm.title": "Terms of use and Privacy", "cashback-tx-activity.retry": "Retry", "cashback-unconfirmed-payments-info.subtitle": "Payments will qualify for Cashback when they are settled with the merchant. Until then they show as unconfirmed payments. Unsettled payments do not qualify for cashback.", "cashback-unconfirmed-payments-info.title": "Unconfirmed card payments", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "<PERSON><PERSON><PERSON><PERSON>", "cashback.activity.title": "Recent activity", "cashback.activity.withdrawal": "<PERSON><PERSON><PERSON>", "cashback.deposit": "<PERSON><PERSON><PERSON><PERSON>", "cashback.deposit.amount.label": "Deposit amount", "cashback.deposit.change": "{from} to {to}", "cashback.deposit.confirmation.subtitle": "Cashback rates update once a week. Deposit now to increase next week’s Cashback.", "cashback.deposit.confirmation.title": "You’ll start earning {percentage} on {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Swap tokens into {currency} on {network} Chain", "cashback.deposit.get.tokens.title": "Get {currency} tokens", "cashback.deposit.header": "Deposit {currency}", "cashback.deposit.max_label": "Max: {amount}", "cashback.deposit.select-wallet.title": "Choose wallet to deposit from", "cashback.deposit.yourcashback": "Your Cashback", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "Choose wallet to{br}withdraw to", "cashback.transaction-details.network-label": "Network", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "From", "cashback.transaction-details.top-row.label-rewards": "Cashback period", "cashback.transaction-details.top-row.label-withdrawal": "To", "cashback.transaction-details.transaction": "Transaction ID", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Cashback transactions", "cashback.withdraw": "Withdraw", "cashback.withdraw.confirmation.cashback_reduction": "Cashback for this week, including what you’ve already earned, will reduce from {before} to {after}", "cashback.withdraw.queued": "<PERSON><PERSON><PERSON> queued", "cashback.withdrawal.change": "{from} to {to}", "cashback.withdrawal.confirmation.subtitle": "Start withdrawal of {amount} with a 3 minute delay. This will reduce your cashback to {after}.", "cashback.withdrawal.confirmation.title": "Cashback will decrease if you withdraw GNO", "cashback.withdrawal.delayTransaction.title": "Start GNO withdrawal with{br} a 3 minute delay", "cashback.withdrawal.withdraw": "Withdraw", "cashback.withdrawal.yourcashback": "Your Cashback", "celebration.aave": "Earned with <PERSON><PERSON>", "celebration.cashback.subtitle": "Delivered in {code}", "celebration.cashback.subtitleGNO": "{amount} last earned", "celebration.chf": "Earned with Frankencoin", "celebration.lido": "Earned with Lido", "celebration.sky": "Earned with Sky", "celebration.title": "Total Cashback", "celebration.well_done.title": "Well done!", "change-withdrawal-account.add-new-account": "Add another bank account", "change-withdrawal-account.item.shortText": "{currency} Account", "check-confirmation.approve.footer.for": "For", "checkConfirmation.title": "Transaction result", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin & Ether", "collateral.ethereum": "Ethereum", "collateral.other": "Other", "collateral.rwa": "Real World Assets", "collateral.stablecoins": "Stablecoins (USD pegged)", "collateral.us-t-bills": "US T-Bills", "confirm-bank-transfer-recipient.bullet-1": "No fees on digital EUR", "confirm-bank-transfer-recipient.bullet-2": "Deposits to {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Share Gnosis Pay account details with Monerium, an authorized and regulated EMI. <link>Learn more</link>", "confirm-bank-transfer-recipient.bullet-4": "Accept Monerium <link>terms of service</link>", "confirm-bank-transfer-recipient.title": "Accept terms", "confirm-change-withdrawal-account.cancel": "Cancel", "confirm-change-withdrawal-account.confirm": "Confirm", "confirm-change-withdrawal-account.saving": "Saving", "confirm-change-withdrawal-account.subtitle": "All withdrawals you send from Zeal will be received by this bank account.", "confirm-change-withdrawal-account.title": "Change receiving bank", "confirm-ramove-withdrawal-account.title": "Remove bank account", "confirm-remove-withdrawal-account.subtitle": "These bank account details will be removed from Zeal. You can add it again anytime.", "confirmTransaction.finalNetworkFee": "Network fee", "confirmTransaction.importKeys": "Import keys", "confirmTransaction.networkFee": "Network fee", "confirmation.title": "Send {amount} to {recipient}", "conflicting-monerium-account.add-owner": "Add as Gnosis Pay Owner", "conflicting-monerium-account.create-wallet": "Create a new smart wallet", "conflicting-monerium-account.disconnect-card": "Disconnect card from Zeal and reconnect with new owner", "conflicting-monerium-account.header": "{wallet} linked to another Monerium account", "conflicting-monerium-account.subtitle": "Change your Gnosis Pay owner wallet", "connection.diconnected.got_it": "Got it!", "connection.diconnected.page1.subtitle": "Zeal works everywhere Metamask works. Simply connect as you would with Metamask", "connection.diconnected.page1.title": "How to connect with <PERSON><PERSON>?", "connection.diconnected.page2.subtitle": "You’ll see lot of options. <PERSON><PERSON> might be one of them. If <PERSON><PERSON> doesn’t appear...", "connection.diconnected.page2.title": "Click Connect Wallet", "connection.diconnected.page3.subtitle": "We’ll prompt a connection with <PERSON><PERSON>. <PERSON><PERSON><PERSON> or Injected should work as well. Try it!", "connection.diconnected.page3.title": "<PERSON><PERSON>", "connectionSafetyCheck.tag.caution": "Caution", "connectionSafetyCheck.tag.danger": "Danger", "connectionSafetyCheck.tag.passed": "Passed", "connectionSafetyConfirmation.subtitle": "Are you sure you want to continue?", "connectionSafetyConfirmation.title": "This site looks dangerous", "connection_state.connect.cancel": "Cancel", "connection_state.connect.changeToMetamask": "Change to MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Change to MetaMask", "connection_state.connect.connect_button": "Connect", "connection_state.connect.expanded.connected": "Connected", "connection_state.connect.expanded.title": "Connect", "connection_state.connect.safetyChecksLoading": "Checking site safety", "connection_state.connect.safetyChecksLoadingError": "Couldn’t complete safety checks", "connection_state.connected.expanded.disconnectButton": "Disconnect Zeal", "connection_state.connected.expanded.title": "Connected", "copied-diagnostics": "Copied diagnostics", "copy-diagnostics": "Copy diagnostics", "counterparty.component.add_recipient_primary_text": "Add bank recipient", "counterparty.country": "Country", "counterparty.countryTitle": "Recipient’s country", "counterparty.currency": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.delete.success.title": "Removed", "counterparty.edit.success.title": "Changes saved", "counterparty.errors.country_required": "Required Country", "counterparty.errors.first_name.invalid": "First name should be longer", "counterparty.errors.last_name.invalid": "Last name should be longer", "counterparty.first_name": "First name", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Send to bank", "countrySelector.noCountryFound": "No country found", "countrySelector.title": "Choose country", "create-passkey.cta": "Create passkey", "create-passkey.extension.cta": "Continue", "create-passkey.footnote": "Powered by", "create-passkey.mobile.cta": "Start Security Setup", "create-passkey.steps.enable-recovery": "Set up cloud recovery", "create-passkey.steps.setup-biometrics": "Enable biometric security", "create-passkey.subtitle": "Passkeys are more secure than passwords, and encrypted in cloud storage for easy recovery.", "create-passkey.title": "Secure account", "create-smart-wallet": "Create Smart wallet", "create-userop.progress.text": "Creating", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Continue in Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Go to Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "You’ve already started your card order. Head back to the Gnosis Pay site to complete it.", "create_recharge_preferences.card": "Card", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "Earn {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "Hold cash", "create_recharge_preferences.link_accounts_title": "Link accounts", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "Your card will NOT recharge automatically after each payment.", "create_recharge_preferences.not_configured_title": "Earn & Spend", "create_recharge_preferences.recharge_from_earn_accounts_description": "Your card recharges automatically after each payment from your Earn account.", "create_recharge_preferences.subtitle": "per year", "creating-account.loading": "Creating account", "creating-gnosis-pay-account": "Creating account", "currencies.bridge.select_routes.emptyState": "We found no routes for this bridge", "currency.add_currency.add_token": "Add token", "currency.add_currency.not_a_valid_address": "This is not a valid token address", "currency.add_currency.token_decimals_feild": "Token decimals", "currency.add_currency.token_feild": "Token address", "currency.add_currency.token_symbol_feild": "Token symbol", "currency.add_currency.update_token": "Update token", "currency.add_custom.remove_token.cta": "Remove token", "currency.add_custom.remove_token.header": "Remove token", "currency.add_custom.remove_token.subtitle": "Your wallet will still hold any balance of this token but it will be hidden from your Zeal portfolio balances.", "currency.add_custom.token_removed": "Token removed", "currency.add_custom.token_updated": "Token updated", "currency.balance_label": "Balance: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "Your bank transfer has successfully transferred {fiat} to {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "You’ve received {crypto}", "currency.bankTransfer.deposit_status.success": "Received in your wallet", "currency.bankTransfer.deposit_status.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.check_bank_account": "Check your bank account", "currency.bankTransfer.off_ramp.complete": "Complete", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Sending to your bank", "currency.bankTransfer.off_ramp.transferring_to_currency": "Transferring to {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "The funds should have arrived in your bank account by now.", "currency.bankTransfer.withdrawal_status.success": "Sent to your bank", "currency.bankTransfer.withdrawal_status.title": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.email": "Email address", "currency.bank_transfer.create_unblock_user.email_invalid": "Invalid email", "currency.bank_transfer.create_unblock_user.email_missing": "Required", "currency.bank_transfer.create_unblock_user.first_name": "First name", "currency.bank_transfer.create_unblock_user.first_name_missing": "Required", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "Only letters, numbers, spaces, and - . , & ( ) ’ allowed.", "currency.bank_transfer.create_unblock_user.last_name": "Family name", "currency.bank_transfer.create_unblock_user.last_name_missing": "Required", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "Only letters, numbers, spaces, and - . , & ( ) ’ allowed.", "currency.bank_transfer.create_unblock_user.note": "By continuing you accept <PERSON><PERSON><PERSON>s (our banking partner) <terms>Terms</terms> and <policy>Privacy Policy</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Spell your name exactly like in your bank account", "currency.bank_transfer.create_unblock_user.title": "Link your bank account", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Account number", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Bank country", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Preferred currency", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Sort code", "currency.bank_transfer.create_unblock_withdraw_account.success": "Account set up", "currency.bank_transfer.create_unblock_withdraw_account.title": "Link your bank account", "currency.bank_transfer.residence-form.address-required": "Required", "currency.bank_transfer.residence-form.address-unsupported-char": "Only letters, numbers, spaces, and , ; {apostrophe} - \\\\ allowed.", "currency.bank_transfer.residence-form.city-required": "Required", "currency.bank_transfer.residence-form.city-unsupported-char": "Only letters, numbers, spaces, and . , - & ( ) {apostrophe} allowed.", "currency.bank_transfer.residence-form.postcode-invalid": "Invalid postcode", "currency.bank_transfer.residence-form.postcode-required": "Required", "currency.bank_transfer.validation.invalid.account_number": "Invalid account number", "currency.bank_transfer.validation.invalid.iban": "Invalid IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Invalid sort code", "currency.bridge.amount_label": "Amount to bridge", "currency.bridge.best_returns.subtitle": "This bridge provider will give you the highest output, including all fees.", "currency.bridge.best_returns_popup.title": "Best returns", "currency.bridge.bridge_from": "From", "currency.bridge.bridge_gas_fee_loading_failed": "We had issues loading gas fee", "currency.bridge.bridge_low_slippage": "Very low slippage. Try to increase it", "currency.bridge.bridge_provider": "Transfer provider", "currency.bridge.bridge_provider_loading_failed": "We had issues loading providers", "currency.bridge.bridge_settings": "Bridge settings", "currency.bridge.bridge_status.subtitle": "Using {name}", "currency.bridge.bridge_status.title": "Bridge", "currency.bridge.bridge_to": "To", "currency.bridge.fastest_route_popup.subtitle": "This bridge provider will give you the fastest transaction route.", "currency.bridge.fastest_route_popup.title": "Fastest route", "currency.bridge.from": "From", "currency.bridge.success": "Complete", "currency.bridge.title": "Bridge", "currency.bridge.to": "To", "currency.bridge.topup": "Top up {symbol}", "currency.bridge.withdrawal_status.title": "<PERSON><PERSON><PERSON>", "currency.card.card_top_up_status.title": "Add cash to card", "currency.destination_amount": "Destination amount", "currency.hide_currency.confirm.subtitle": "Hide this token from your portfolio. You can always unhide it anytime.", "currency.hide_currency.confirm.title": "Hide token", "currency.hide_currency.success.title": "Token hidden", "currency.label": "Label (Optional)", "currency.last_name": "Last name", "currency.max_loading": "Max:", "currency.swap.amount_to_swap": "Amount to swap", "currency.swap.best_return": "Best return route", "currency.swap.destination_amount": "Destination amount", "currency.swap.header": "<PERSON><PERSON><PERSON>", "currency.swap.max_label": "Balance: {amount}", "currency.swap.provider.header": "Swap provider", "currency.swap.select_to_token": "Select token", "currency.swap.swap_gas_fee_loading_failed": "We had issues loading gas fee", "currency.swap.swap_provider_loading_failed": "We had issues loading providers", "currency.swap.swap_settings": "Swap settings", "currency.swap.swap_slippage_too_low": "Very low slippage. Try to increase it", "currency.swaps_io_native_token_swap.subtitle": "Using Swaps.IO", "currency.swaps_io_native_token_swap.title": "Send", "currency.withdrawal.amount_from": "From", "currency.withdrawal.amount_to": "To", "currencySelector.title": "Choose currency", "dApp.wallet-does-not-support-chain.subtitle": "Your wallet does not seem to support {network}. Try connecting with a different wallet, or use Zeal instead.", "dApp.wallet-does-not-support-chain.title": "Unsupported Network", "dapp.connection.manage.confirm.disconnect.all.cta": "Disconnect all", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Are you sure you want to disconnect all connections?", "dapp.connection.manage.confirm.disconnect.all.title": "Disconnect all", "dapp.connection.manage.connection_list.main.button.title": "Disconnect", "dapp.connection.manage.connection_list.no_connections": "You have no connected apps", "dapp.connection.manage.connection_list.section.button.title": "Disconnect all", "dapp.connection.manage.connection_list.section.title": "Active", "dapp.connection.manage.connection_list.title": "Connections", "dapp.connection.manage.disconnect.success.title": "Apps Disconnected", "dapp.metamask_mode.title": "MetaMask Mode", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "Interest", "dc25-card-marketing-card.right.subtitle": "100 people", "dc25-card-marketing-card.title": "First 100 to spend €50 will earn {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "You wont be able to do this action for 3 min. For security reasons, any card settings changes or withdrawals take 3 minutes to be processed.", "delayQueueBusyBanner.processing-yout-action.title": "Processing your action, please wait", "delayQueueBusyWidget.cardFrozen": "Card frozen", "delayQueueBusyWidget.processingAction": "Processing your action", "delayQueueFailedBanner.action-incomplete.get-support": "Get support", "delayQueueFailedBanner.action-incomplete.subtitle": "Sorry, something went wrong with your withdrawal or settings update. Please contact support on Discord.", "delayQueueFailedBanner.action-incomplete.title": "Action incomplete", "delayQueueFailedWidget.actionIncomplete.title": "Card action incomplete", "delayQueueFailedWidget.cardFrozen.subtitle": "Card frozen", "delayQueueFailedWidget.contactSupport": "Contact support", "delay_queue_busy.subtitle": "For security reasons, any card settings changes or withdrawals take 3 minutes to be processed. During which your card is frozen.", "delay_queue_busy.title": "Your action is being processed", "delay_queue_failed.contact_support": "Contact support", "delay_queue_failed.subtitle": "Sorry, something went wrong with your withdrawal or settings update. Please contact support on Discord.", "delay_queue_failed.title": "Contact support", "deploy-earn-form-smart-wallet.in-progress.title": "Preparing Earn", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "disconnect-card-popup.cancel": "Cancel", "disconnect-card-popup.disconnect": "Disconnect", "disconnect-card-popup.subtitle": "This will remove your Card from the Zeal app. Your wallet will still be connected to your card in the Gnosis Pay app. You can reconnect your Card anytime.", "disconnect-card-popup.title": "Disconnect card", "distance.long.days": "{count} days", "distance.long.hours": "{count} hours", "distance.long.minutes": "{count} minutes", "distance.long.months": "{count} months", "distance.long.seconds": "{count} seconds", "distance.long.years": "{count} years", "distance.short.days": "{count} d", "distance.short.hours": "{count} h", "distance.short.minutes": "{count} min", "distance.short.months": "{count} m", "distance.short.seconds": "{count} sec", "distance.short.years": "{count} y", "duration.short.days": "{count}d", "duration.short.hours": "{count}h", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn-deposit-view.into": "Into", "earn-deposit-view.to": "To", "earn-deposit.swap.transfer-provider": "Transfer provider", "earn-taker-investment-details.accrued-realtime": "Accrued in realtime", "earn-taker-investment-details.asset-class": "Asset class", "earn-taker-investment-details.asset-coverage-ratio": "Asset coverage ratio", "earn-taker-investment-details.asset-reserve": "Asset reserve", "earn-taker-investment-details.base_currency.label": "Base currency", "earn-taker-investment-details.chf.description": "Earn interest on your CHF by depositing zCHF into Frankencoin - a trusted digital money market. Interest is generated from low-risk, overcollateralised lons on Frankencoin and paid in real time. Your funds stay safe in a secure sub-account that only you control.", "earn-taker-investment-details.chf.description.with_address_link": "Earn interest on your CHF by depositing zCHF into Frankencoin - a trusted digital money market. Interest is generated from low-risk, overcollateralised lons on Frankencoin and paid in real time. Your funds stay safe in a secure sub-account <link>(copy 0x)</link> that only you control.", "earn-taker-investment-details.chf.label": "Digital Swiss Franc", "earn-taker-investment-details.collateral-composition": "Collateral composition", "earn-taker-investment-details.depositor-obligations": "Depositor obligations", "earn-taker-investment-details.eure.description": "Earn interest on your euros by depositing EURe into Aave - a trusted digital money market. EURe is a fully regulated euro stablecoin issued by Monerium and backed 1:1 in safeguarded accounts. Interest is generated from low-risk, overcollateralized loans on Aave and paid in real time. Your funds stay in a secure sub-account that only you control.", "earn-taker-investment-details.eure.description.with_address_link": "Earn interest on your euros by depositing EURe into Aave - a trusted digital money market. EURe is a fully regulated euro stablecoin issued by Monerium and backed 1:1 in safeguarded accounts. Interest is generated from low-risk, overcollateralized loans on Aave and paid in real time. Your funds stay in a secure sub-account <link>(copy 0x)</link> that only you control.", "earn-taker-investment-details.eure.label": "Digital Euro (EURe)", "earn-taker-investment-details.faq": "FAQ", "earn-taker-investment-details.fixed-income": "Fixed income", "earn-taker-investment-details.issuer": "Issuer", "earn-taker-investment-details.key-facts": "Key facts", "earn-taker-investment-details.liquidity": "Liquidity", "earn-taker-investment-details.operator": "Market Operator", "earn-taker-investment-details.projected-yield": "Projected annual yield", "earn-taker-investment-details.see-other-faq": "See all other FAQ’s", "earn-taker-investment-details.see-realtime": "See realtime data", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Trailing 12 month yield", "earn-taker-investment-details.total-collateral": "Total Collateral", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "Total ZCHF supply", "earn-taker-investment-details.total_deposits": "Total Aave deposits", "earn-taker-investment-details.usd.description": "Sky is a digital money market offering stable, US dollar-denominated yields from short-duration U.S. Treasuries and overcollateralized lending—with no crypto volatility, 24/7 fund access, and transparent, on-chain backing.", "earn-taker-investment-details.usd.description.with_address_link": "Sky is a digital money market offering stable, US dollar-denominated yields from short-duration U.S. Treasuries and overcollateralized lending—with no crypto volatility, 24/7 fund access, and transparent, on-chain backing. Investments are in a sub-account <link>(copy 0x)</link> controlled by you.", "earn-taker-investment-details.usd.ftx-difference": "How is this different from FTX, Celsius, BlockFi, or Luna?", "earn-taker-investment-details.usd.high-returns": "How can the returns be so high, especially compared to traditional banks?", "earn-taker-investment-details.usd.how-is-backed": "How is Sky USD backed, and what happens to my money if Zeal goes bankrupt?", "earn-taker-investment-details.usd.income-sources": "Income sources 2024", "earn-taker-investment-details.usd.insurance": "Are my funds insured or guaranteed by any entity (like FDIC or similar) ?", "earn-taker-investment-details.usd.label": "Digital US Dollar", "earn-taker-investment-details.usd.lose-principal": "Can I realistically lose my principal, and under what circumstances?", "earn-taker-investment-details.variable-rate": "Variable-rate lending", "earn-taker-investment-details.withdraw-anytime": "Withdraw anytime", "earn-taker-investment-details.yield": "Yield", "earn-withdrawal-view.approve.for": "For", "earn-withdrawal-view.approve.into": "Into", "earn-withdrawal-view.swap.into": "Into", "earn-withdrawal-view.withdraw.to": "To", "earn.add_another_asset.title": "Select Earning asset", "earn.add_asset": "Add asset", "earn.asset_view.title": "<PERSON><PERSON><PERSON>", "earn.base-currency-popup.text": "The base currency is how your deposits, yield, and transactions are valued and recorded. If you deposit in a different currency (such as EUR into USD), your funds are immediately converted into the base currency using current exchange rates. After conversion, your balance remains stable in the base currency, but future withdrawals may involve currency conversions again.", "earn.base-currency-popup.title": "Base currency", "earn.card-recharge.disabled.list-item.title": "Auto-recharge disabled", "earn.card-recharge.enabled.list-item.title": "Auto-recharge enabled", "earn.choose_wallet_to_deposit.title": "Deposit from", "earn.config.currency.eth": "Earn <PERSON>", "earn.config.currency.on_chain_address_subtitle": "Onchain address", "earn.config.currency.us_dollars": "Set up bank transfers", "earn.configured_widget.current_apy.title": "Current APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} Annual", "earn.confirm.currency.cta": "<PERSON><PERSON><PERSON><PERSON>", "earn.currency.eth": "Earn <PERSON>", "earn.deploy.status.title": "Create Earn account", "earn.deploy.status.title_with_taker": "Create {title} Earn account", "earn.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn.deposit.amount_to_deposit": "Amount to deposit", "earn.deposit.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn.deposit.enter_amount": "Enter Amount", "earn.deposit.no_routes_found": "No routes found", "earn.deposit.not_enough_balance": "Not enough balance", "earn.deposit.select-currency.title": "Select token to deposit", "earn.deposit.select_account.title": "Select Earn account", "earn.desposit_form.title": "Deposit into Earn", "earn.earn_deposit.status.title": "Deposit into Earn", "earn.earn_deposit.trx.title": "Deposit into Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "Withdraw funds anytime", "earn.earn_withdraw.status.title": "Withdraw from <PERSON><PERSON><PERSON>ccount", "earn.earn_withdraw.trx.title.approval": "Approve withdrawal", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Withdraw into {asset}", "earn.earn_withdraw.trx.title.withdrawal": "Withdraw from Earn", "earn.recharge.cta": "Save changes", "earn.recharge.earn_not_configured.enable_some_account.error": "Enable account", "earn.recharge.earn_not_configured.enter_amount.error": "Enter amount", "earn.recharge.select_taker.header": "Recharge card in order from", "earn.recharge_card_tag.on": "on", "earn.recharge_card_tag.recharge": "Recharge", "earn.recharge_card_tag.recharge_not_configured": "Auto-Recharge", "earn.recharge_card_tag.recharge_off": "Recharge off", "earn.recharge_card_tag.recharged": "Recharged", "earn.recharge_card_tag.recharging": "Recharging", "earn.recharge_configured.disable.trx.title": "Disable Auto-Recharge", "earn.recharge_configured.trx.disclaimer": "When you use your card, a Cowswap auction is created to buy the same amount as your payment using your Earn assets. This auction process typically gets you the best market rate, but be aware that the onchain rate may differ from real-world exchange rates.", "earn.recharge_configured.trx.subtitle": "After each payment, cash will automatically be added from your Earn account(s) to keep your card balance at {value}", "earn.recharge_configured.trx.title": "Set Auto-Recharge to {value}", "earn.recharge_configured.updated.trx.title": "Save Recharge Settings", "earn.risk-banner.subtitle": "This is a self-custodial product with no regulatory protection against loss.", "earn.risk-banner.title": "Understand the risks", "earn.set_recharge.status.title": "Set Auto-Recharge", "earn.setup_reacharge.input.disable.label": "Disable", "earn.setup_reacharge.input.label": "Target card balance", "earn.setup_reacharge_form.title": "Auto-Recharge keeps your{br}card at the same balance", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Hold wstETH (Staked ETH) on Gnosis Chain, and lend via Lido.", "earn.taker-bulletlist.point_1": "Earn {apyValue} annually. Returns vary with market.", "earn.taker-bulletlist.point_3": "Zeal charges no fees.", "earn.taker-historical-returns": "Historical Returns", "earn.taker-historical-returns.chf": "Growth of CHF to USD", "earn.taker-investment-tile.apy.perYear": "per year", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Earning from Aave on Gnosis Chain", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Earning from Frankencoin on Gnosis Chain", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Earning from Lido on Gnosis Chain", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Earning from Maker on Gnosis Chain", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "Eth earn", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH Earn", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "US Treasuries (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "While extremely rare, it’s theoretically possible. Your funds are protected by strict risk management and high collateralization. The realistic worst-case scenario would involve unprecedented market conditions, such as multiple stablecoins losing their peg simultaneously—something that has never happened before.", "earn.usd.can-I-lose-my-principal-popup.title": "Can I realistically lose my principal, and under what circumstances?", "earn.usd.ftx-difference-popup.text": "Sky is fundamentally different. Unlike FTX, Celsius, BlockFi, or Luna—which relied heavily on centralized custody, opaque asset management, and risky leveraged positions—Sky USD utilizes transparent, audited, decentralized smart contracts and maintains full on-chain transparency. You retain complete self-custodial control, significantly reducing counterparty risks associated with centralized failures.", "earn.usd.ftx-difference-popup.title": "How is this different from FTX, Celsius, BlockFi, or Luna?", "earn.usd.high-returns-popup.text": "Sky USD generates yields primarily through decentralized finance (DeFi) protocols, which automate peer-to-peer lending and liquidity provision, removing traditional banking overhead and intermediaries. These efficiencies, combined with robust risk controls, allow significantly higher returns compared to traditional banks.", "earn.usd.high-returns-popup.title": "How can the returns be so high, especially compared to traditional banks?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD is fully backed and over-collateralised by a combination of digital assets held in secure smart contracts real world assets like US Treasuries. Reserves can be audited real-time onchain even from within Zeal, providing transparency and security. In the unlikely event Zeal shuts down, your assets remain secured on-chain, fully under your control, and accessible through other compatible wallets.", "earn.usd.how-is-sky-backed-popup.title": "How is Sky USD backed, and what happens to my money if Zeal goes bankrupt?", "earn.usd.insurance-popup.text": "Sky USD funds aren’t FDIC insured or backed by traditional governmental guarantees because it’s a digital asset-based account, not a conventional bank account. Instead, Sky manages all risk mitigation through audited smart contracts and carefully vetted DeFi protocols, ensuring assets remain secure and transparent.", "earn.usd.insurance-popup.title": "Are my funds insured or guaranteed by any entity (like FDIC or similar)?", "earn.usd.lending-operations-popup.text": "Sky USD generates yield by lending stablecoins through decentralized lending markets like Morpho and Spark. Your stablecoins are loaned out to borrowers who deposit significantly more collateral—such as ETH or BTC—than the value of their loan. This approach, called overcollateralization, ensures there’s always sufficient collateral to cover loans, greatly reducing risk. The collected interest and occasional liquidation fees paid by borrowers provide reliable, transparent, and secure returns.", "earn.usd.lending-operations-popup.title": "Lending operations", "earn.usd.market-making-operations-popup.text": "Sky USD earns additional yield by participating in decentralized exchanges (AMMs) such as Curve or Uniswap. By providing liquidity—placing your stablecoins into pools that facilitate crypto trading—Sky USD captures fees generated from trades. These liquidity pools are selected carefully to minimize volatility, primarily using stablecoin-to-stablecoin pairs to significantly reduce risks like impermanent loss, keeping your assets both safe and accessible.", "earn.usd.market-making-operations-popup.title": "Market Making Operations", "earn.usd.treasury-operations-popup.text": "Sky USD generates stable, consistent yield through strategic treasury investments. Part of your stablecoin deposits are allocated to safe, low-risk real-world assets—primarily short-term government bonds and highly secure credit instruments. This approach, similar to traditional banking, ensures predictable and reliable yield. Your assets remain secure, liquid, and transparently managed.", "earn.usd.treasury-operations-popup.title": "Treasury Operations", "earn.view_earn.card_rechard_off": "Off", "earn.view_earn.card_rechard_on": "On", "earn.view_earn.card_recharge": "Card Recharge", "earn.view_earn.total_balance_label": "Earning {percentage} per year", "earn.view_earn.total_earnings_label": "Total earnings", "earn.withdraw": "Withdraw", "earn.withdraw.amount_to_withdraw": "Amount to withdraw", "earn.withdraw.enter_amount": "Enter Amount", "earn.withdraw.loading": "Loading", "earn.withdraw.no_routes_found": "No routes found", "earn.withdraw.not_enough_balance": "Not enough balance", "earn.withdraw.select-currency.title": "Select token", "earn.withdraw.select_to_token": "Select token", "earn.withdraw.withdraw": "Withdraw", "earn.withdraw_form.title": "Withdraw from Earn", "earnings-view.earnings": "Total Earnings", "edit-account-owners.add-owner.add-wallet": "Add owner", "edit-account-owners.add-owner.add_wallet": "Add Wallet", "edit-account-owners.add-owner.title": "Add Card Owner", "edit-account-owners.card-owners": "Card owners", "edit-account-owners.external-wallet": "External wallet", "editBankRecipient.title": "Edit recipient", "editNetwork.addCustomRPC": "Add custom RPC node", "editNetwork.cannot_verify.subtitle": "The custom RPC node is not responding properly. Check the URL and try again.", "editNetwork.cannot_verify.title": "We can’t verify RPC Node", "editNetwork.cannot_verify.try_again": "Try again", "editNetwork.customRPCNode": "Custom RPC node", "editNetwork.defaultRPC": "Default RPC", "editNetwork.networkRPC": "Network RPC", "editNetwork.rpc_url.cannot_be_empty": "Required", "editNetwork.rpc_url.not_a_valid_https_url": "Must be a valid HTTP(S) URL", "editNetwork.safetyWarning.subtitle": "Zeal can’t ensure the privacy, reliability and safety of custom RPCs. Are you sure you want to use a custom RPC node?", "editNetwork.safetyWarning.title": "Custom RPCs can be unsafe", "editNetwork.zealRPCNode": "Zeal RPC Node", "editNetworkRpc.headerTitle": "Custom RPC Node", "editNetworkRpc.rpcNodeUrl": "RPC Node URL", "editing-locked.modal.description": "Unlike Approval transactions, Permits do not allow you to edit the Spend Limit or Expiry Time. Make sure you trust a dApp before submitting a Permit.", "editing-locked.modal.title": "Editing locked", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Enabling recharge", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Recharge enabled", "enterCardnumber": "Enter card number", "error.connectivity_error.subtitle": "Please check your internet connection and try again.", "error.connectivity_error.title": "No internet connection", "error.decrypt_incorrect_password.title": "Incorrect password", "error.encrypted_object_invalid_format.title": "Corrupted data", "error.failed_to_fetch_google_auth_token.title": "We couldn’t get access", "error.list.item.cta.action": "Retry", "error.trezor_action_cancelled.title": "Transaction rejected", "error.trezor_device_used_elsewhere.title": "Device is being used in another session", "error.trezor_method_cancelled.title": "Couldn’t sync <PERSON><PERSON><PERSON>", "error.trezor_permissions_not_granted.title": "Couldn’t sync <PERSON><PERSON><PERSON>", "error.trezor_pin_cancelled.title": "Couldn’t sync <PERSON><PERSON><PERSON>", "error.trezor_popup_closed.title": "Couldn’t sync <PERSON><PERSON><PERSON>", "error.unblock_account_number_and_sort_code_mismatch": "Account number and sort code mismatch", "error.unblock_can_not_change_details_after_kyc": "Can not change details after KYC", "error.unblock_hard_kyc_failure": "Unexpected KYC state", "error.unblock_invalid_faster_payment_configuration.title": "This bank does not support Faster Payments", "error.unblock_invalid_iban": "Invalid IBAN", "error.unblock_session_expired.title": "Unblock session expired", "error.unblock_user_with_address_already_exists.title": "Account already set up for address", "error.unblock_user_with_such_email_already_exists.title": "User with such email already exists", "error.unknown_error.error_message": "Error message: ", "error.unknown_error.subtitle": "Sorry! If you need urgent help, please contact support and share below details.", "error.unknown_error.title": "System error", "eth-cost-warning-modal.subtitle": "Smart wallets work on Ethereum, but fees are very high and we STRONGLY recommend using other networks instead.", "eth-cost-warning-modal.title": "Avoid Ethereum - network fees are high", "exchange.form.button.chain_unsupported": "Chain unsupported", "exchange.form.button.refreshing": "Refreshing", "exchange.form.error.asset_not_supported.button": "Select another asset", "exchange.form.error.asset_not_supported.description": "Bridge does not support bridging this asset.", "exchange.form.error.asset_not_supported.title": "Asset not supported", "exchange.form.error.bridge_quote_timeout.button": "Select another asset", "exchange.form.error.bridge_quote_timeout.description": "Try another pair of tokens", "exchange.form.error.bridge_quote_timeout.title": "No exchange found", "exchange.form.error.different_receiver_not_supported.button": "Remove alternative recipient", "exchange.form.error.different_receiver_not_supported.description": "This exchange doesn’t support sending to another address.", "exchange.form.error.different_receiver_not_supported.title": "Send and receive address must be the same", "exchange.form.error.insufficient_input_amount.button": "Increase amount", "exchange.form.error.insufficient_liquidity.button": "Reduce amount", "exchange.form.error.insufficient_liquidity.description": "The bridge doesn’t have enough assets. Try a smaller amount.", "exchange.form.error.insufficient_liquidity.title": "Amount too high", "exchange.form.error.max_amount_exceeded.button": "Reduce amount", "exchange.form.error.max_amount_exceeded.description": "The maximum amount has been exceeded.", "exchange.form.error.max_amount_exceeded.title": "Amount too high", "exchange.form.error.min_amount_not_met.button": "Increase amount", "exchange.form.error.min_amount_not_met.description": "The minimum exchange amount for this token is not met.", "exchange.form.error.min_amount_not_met.description_with_amount": "The minimum exchange amount is {amount}.", "exchange.form.error.min_amount_not_met.title": "Amount too low", "exchange.form.error.min_amount_not_met.title_increase": "Increase amount", "exchange.form.error.no_routes_found.button": "Select another asset", "exchange.form.error.no_routes_found.description": "There is no exchange route available for this token/network combination.", "exchange.form.error.no_routes_found.title": "No exchange available", "exchange.form.error.not_enough_balance.button": "Reduce amount", "exchange.form.error.not_enough_balance.description": "You do not have enough of this asset for the transaction.", "exchange.form.error.not_enough_balance.title": "Not enough balance", "exchange.form.error.slippage_passed_is_too_low.button": "Increase slippage", "exchange.form.error.slippage_passed_is_too_low.description": "Allowed slippage is too low for this asset.", "exchange.form.error.slippage_passed_is_too_low.title": "Slippage too low", "exchange.form.error.socket_internal_error.button": "Try again later", "exchange.form.error.socket_internal_error.description": "Bridging partner is experiencing issues. Try again later.", "exchange.form.error.socket_internal_error.title": "Error at bridging partner", "exchange.form.error.stargatev2_requires_fee_in_native": "Add {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Add {amount} to complete transaction", "exchange.form.error.stargatev2_requires_fee_in_native.title": "Need more {symbol}", "expiration-info.modal.description": "Expiry time is how long an app can use your tokens. When the time’s up, they lose access until you say otherwise. To stay secure, keep expiry time short.", "expiration-info.modal.title": "What is expiry time?", "expiration-time.high.modal.text": "Expiry times should be short and based on how long you’ll actually need. Long times are risky, giving scammers more chance to misuse your tokens.", "expiration-time.high.modal.title": "Long expiry time", "failed.transaction.content": "Transaction likely to fail", "fee.unknown": "Unknown", "feedback-request.leave-message": "Leave a message", "feedback-request.not-now": "Not now", "feedback-request.title": "Thanks! How can we improve Zeal?", "float.input.period": "Decimal separator", "gnosis-activate-card.info-popup.subtitle": "For your first transaction you must insert your card and enter your PIN. After that contactless payments will work.", "gnosis-activate-card.info-popup.title": "First payment requires Chip & PIN", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "Enter your card number to activate it.", "gnosis-activate-card.title": "Card number", "gnosis-pay-re-kyc-widget.btn-text": "Verify", "gnosis-pay-re-kyc-widget.title.not-started": "Verify your identity", "gnosis-pay.login.cta": "Connect existing account", "gnosis-pay.login.title": "You already have a Gnosis Pay account", "gnosis-signup.confirm.subtitle": "Look for an email from Gnosis Pay, it might be hiding in your spam folder.", "gnosis-signup.confirm.title": "Didn’t receive verification email?", "gnosis-signup.continue": "Continue", "gnosis-signup.dont_link_accounts": "Don’t link accounts", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "Enter <EMAIL>", "gnosis-signup.enter-email.title": "Enter email", "gnosis-signup.title": "I have read and agree to Gnosis Pay’s <linkGnosisTNC>Terms & Conditions</linkGnosisTNC> <monovateTerms>Card Holder Terms</monovateTerms> and <linkMonerium>Monerium’s Terms & Conditions</linkMonerium>.", "gnosis-signup.verify-email.title": "Verify email", "gnosis.confirm.subtitle": "Didn’t receive code? Check your phone number is correct", "gnosis.confirm.title": "Code sent to {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Verify", "gnosisPayAccountStatus.success.title": "Card imported", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay is not yet available in your country", "gnosisPayNoActiveCardsFound.title": "No active cards", "gnosis_pay_card_delay_relay_not_empty_error.title": "Your transaction could not be processed right now. Please try again later", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "Card not possible", "gnosiskyc.modal.approved.activate-free-card": "Activate free card", "gnosiskyc.modal.approved.button-text": "Deposit from bank account", "gnosiskyc.modal.approved.title": "Your personal account details have been created", "gnosiskyc.modal.failed.close": "Close", "gnosiskyc.modal.failed.title": "Sorry, our partner Gnosis Pay cannot create an account for you", "gnosiskyc.modal.in-progress.title": "ID verification can take 24hrs or longer. Please be patient", "goToSettingsPopup.settings": "Settings", "goToSettingsPopup.title": "Enable notifications in your device settings anytime", "google_file.error.failed_to_fetch_auth_token.button_title": "Try again", "google_file.error.failed_to_fetch_auth_token.subtitle": "To allow us to use your Recovery File, please grant access on your personal cloud.", "google_file.error.failed_to_fetch_auth_token.title": "We couldn’t get access", "hidden_tokens.widget.emptyState": "No hidden tokens", "how_to_connect_to_metamask.got_it": "OK, got it", "how_to_connect_to_metamask.story.subtitle": "Easily switch between Zeal and other wallets anytime.", "how_to_connect_to_metamask.story.title": "Zeal works alongside other wallets", "how_to_connect_to_metamask.why_switch": "Why switch between Zeal and other wallets?", "how_to_connect_to_metamask.why_switch.description": "No matter which wallet you choose, you’ll always get Zeals Safety Checks protecting you from malicious sites and transactions.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "We know its hard to take the leap and start using a new wallet. So we made it easy to use Zeal alongside your existing wallet. Switch anytime.", "import-bank-transfer-owner.banner.title": "The wallet connected to bank transfers has been changed. To continue with bank transfers on this device import your wallet.", "import-bank-transfer-owner.title": "Import wallet to use bank transfers on this device", "import_gnosispay_wallet.add-another-card-owner.footnote": "Import private key or seed phrase that owns your Gnosis Pay card", "import_gnosispay_wallet.primaryText": "Import Gnosis Pay wallet", "injected-wallet": "Browser wallet", "intercom.getHelp": "Get help", "invalid_iban.got_it": "Got it", "invalid_iban.subtitle": "The IBAN entered is not valid. Please double check that details were added correctly and try again.", "invalid_iban.title": "Invalid IBAN", "keypad-0": "Keypad key 0", "keypad-1": "Keypad key 1", "keypad-2": "Keypad key 2", "keypad-3": "Keypad key 3", "keypad-4": "Keypad key 4", "keypad-5": "Keypad key 5", "keypad-6": "Keypad key 6", "keypad-7": "Keypad key 7", "keypad-8": "Keypad key 8", "keypad-9": "Keypad key 9", "keypad.biometric-button": "Keypad biometric button", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Secret Phrase secured 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "View phrase", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Keep a safe offline copy of your Secret Phrase so you can recover your assets later", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "Don’t try to guess the word", "keystore.write_secret_phrase.before_you_begin.first_point": "I understand that anyone with my Secret Phrase can transfer my assets", "keystore.write_secret_phrase.before_you_begin.second_point": "I’m responsible for keeping my Secret Phrase secret and safe", "keystore.write_secret_phrase.before_you_begin.subtitle": "Please read and accept the following points:", "keystore.write_secret_phrase.before_you_begin.third_point": "I’m in a private place with no people or cameras around me", "keystore.write_secret_phrase.before_you_begin.title": "Before you begin", "keystore.write_secret_phrase.secret_phrase_test.title": "What is word {count} in your Secret Phrase?", "keystore.write_secret_phrase.test_ps.lets_do_it": "Let’s do it", "keystore.write_secret_phrase.test_ps.subtitle": "You’ll need your Secret Phrase to restore your account in this or other devices. Let’s test that your Secret Phrase is written correctly.", "keystore.write_secret_phrase.test_ps.subtitle2": "We’ll ask you for {count} words in your phrase.", "keystore.write_secret_phrase.test_ps.title": "Test Account Recovery", "kyc.modal.approved.button-text": "Do bank transfer", "kyc.modal.approved.subtitle": "Your verification is complete you can now do limitless bank transfers.", "kyc.modal.approved.title": "Bank transfers unlocked", "kyc.modal.continue-with-partner.button-text": "Continue", "kyc.modal.continue-with-partner.subtitle": "We’ll now forward you to our partner to collect your documentation and complete verification application.", "kyc.modal.continue-with-partner.title": "Continue with our partner", "kyc.modal.failed.unblock.subtitle": "<PERSON><PERSON> has not approved your identity verification and is unable to provide you with bank transfer services", "kyc.modal.failed.unblock.title": "Unblock application not approved", "kyc.modal.paused.button-text": "Update details", "kyc.modal.paused.subtitle": "It looks like some of your information is wrong. Please try again and double-check your details before submitting.", "kyc.modal.paused.title": "Your details look wrong", "kyc.modal.pending.button-text": "Close", "kyc.modal.pending.subtitle": "Verification normally takes less than 10 minutes to complete, but sometimes it can take a bit longer.", "kyc.modal.pending.title": "We’ll keep you updated", "kyc.modal.required.cta": "Start verification", "kyc.modal.required.subtitle": "You’ve reached the transaction limit. Please verify your identity to continue. This usually only takes a couple of minutes and requires some personal details and documentation.", "kyc.modal.required.title": "Identity verification required", "kyc.submitted": "Application Submitted", "kyc.submitted_short": "Submitted", "kyc_status.completed_status": "Complete", "kyc_status.failed_status": "Failed", "kyc_status.paused_status": "Review", "kyc_status.subtitle": "Bank transfers", "kyc_status.subtitle.wrong_details": "Wrong details", "kyc_status.subtitle_in_progress": "In progress", "kyc_status.title": "Verifying identity", "label.close": "Close", "label.saving": "Saving...", "labels.this-month": "This month", "labels.today": "Today", "labels.yesterday": "Yesterday", "language.selector.title": "Language", "ledger.account_loaded.imported": "Imported", "ledger.add.success.title": "<PERSON><PERSON> successfully connected 🎉", "ledger.connect.cta": "Sync Ledger", "ledger.connect.step1": "Connect Ledger to your device", "ledger.connect.step2": "Open the Ethereum app on Ledger", "ledger.connect.step3": "Then sync your Ledger 👇", "ledger.connect.subtitle": "Follow these steps to import your Ledger wallets to Zeal", "ledger.connect.title": "Connect Ledger to Zeal", "ledger.error.ledger_is_locked.subtitle": "Unlock Ledger and open the Ethereum app", "ledger.error.ledger_is_locked.title": "<PERSON>ger is locked", "ledger.error.ledger_not_connected.action": "Sync Ledger", "ledger.error.ledger_not_connected.subtitle": "Connect your hardware wallet to your device and open the Ethereum app", "ledger.error.ledger_not_connected.title": "Ledger is not connected", "ledger.error.ledger_running_non_eth_app.title": "Ethereum app not opened", "ledger.error.user_trx_denied_by_user.action": "Close", "ledger.error.user_trx_denied_by_user.subtitle": "You rejected the transaction on your hardware wallet", "ledger.error.user_trx_denied_by_user.title": "Transaction rejected", "ledger.hd_path.bip44.subtitle": "e.g. <PERSON>, Trezor", "ledger.hd_path.bip44.title": "BIP44 Standard", "ledger.hd_path.ledger_live.subtitle": "<PERSON><PERSON><PERSON>", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Legacy", "ledger.hd_path.phantom.subtitle": "e.g. Phantom", "ledger.select.hd_path.subtitle": "HD paths are the way by which hardware wallets sort their accounts. This similar to how an index sorts pages in a book.", "ledger.select.hd_path.title": "Select HD Path", "ledger.select_account.import_wallets_count": "{count, plural,\n              =0 {No wallets selected}\n              one {Import wallet}\n              other {Import {count} wallets}}", "ledger.select_account.path_settings": "Path Settings", "ledger.select_account.subtitle": "Don’t see the wallets you expect? Try changing the path settings", "ledger.select_account.subtitle.group_header": "Wallets", "ledger.select_account.title": "Import Ledger wallets", "legend.lending-operations": "Lending Operations", "legend.market_making-operations": "Market Making Operations", "legend.treasury-operations": "Treasury Operations", "link-existing-monerium-account-sign.button": "<PERSON>", "link-existing-monerium-account-sign.subtitle": "You already have a Monerium account.", "link-existing-monerium-account-sign.title": "Link Zeal to your existing Monerium account", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "You already have a Monerium account. Please visit the Monerium app to complete setup.", "link-existing-monerium-account.title": "Go to Monerium to link your account", "loading.pin": "Loading PIN...", "lockScreen.passwordIncorrectMessage": "Password is incorrect", "lockScreen.passwordRequiredMessage": "Password Required", "lockScreen.unlock.header": "Unlock", "lockScreen.unlock.subheader": "Use your password to unlock <PERSON><PERSON>", "mainTabs.activity.label": "Activity", "mainTabs.browse.label": "Browse", "mainTabs.browse.title": "Browse", "mainTabs.card.label": "Card", "mainTabs.portfolio.label": "Portfolio", "mainTabs.rewards.label": "Rewards", "makeSpendable.cta": "Make spendable", "makeSpendable.holdAsCash": "Hold as cash", "makeSpendable.shortText": "Earning {apy} per year", "makeSpendable.title": "{amount} received", "merchantCategory.agriculture": "Agriculture", "merchantCategory.alcohol": "Alcohol", "merchantCategory.antiques": "Antiques", "merchantCategory.appliances": "Appliances", "merchantCategory.artGalleries": "Art Galleries", "merchantCategory.autoRepair": "Auto repair", "merchantCategory.autoRepairService": "Auto Repair Service", "merchantCategory.beautyFitnessSpas": "Beauty, Fitness & Spas", "merchantCategory.beautyPersonalCare": "Beauty & Personal Care", "merchantCategory.billiard": "Billiard", "merchantCategory.books": "Books", "merchantCategory.bowling": "Bowling", "merchantCategory.businessProfessionalServices": "Business & Professional Services", "merchantCategory.carRental": "Car Rental", "merchantCategory.carWash": "Car wash", "merchantCategory.cars": "Cars", "merchantCategory.casino": "Casino", "merchantCategory.casinoGambling": "Casino & Gambling", "merchantCategory.cellular": "Cellular", "merchantCategory.charity": "Charity", "merchantCategory.childcare": "Childcare", "merchantCategory.cigarette": "Cigarette", "merchantCategory.cinema": "Cinema", "merchantCategory.cinemaEvents": "Cinema & Events", "merchantCategory.cleaning": "Cleaning", "merchantCategory.cleaningMaintenance": "Cleaning & Maintenance", "merchantCategory.clothes": "<PERSON><PERSON><PERSON>", "merchantCategory.clothingServices": "Clothing Services", "merchantCategory.communicationServices": "Communication Services", "merchantCategory.construction": "Construction", "merchantCategory.cosmetics": "Cosmetics", "merchantCategory.craftsArtSupplies": "Crafts & Art Supplies", "merchantCategory.datingServices": "Dating Services", "merchantCategory.delivery": "Delivery", "merchantCategory.dentist": "Dentist", "merchantCategory.departmentStores": "Department Stores", "merchantCategory.directMarketingSubscription": "Direct Marketing & Subscription", "merchantCategory.discountStores": "Discount Stores", "merchantCategory.drugs": "Drugs", "merchantCategory.dutyFree": "Duty Free", "merchantCategory.education": "Education", "merchantCategory.electricity": "Electricity", "merchantCategory.electronics": "Electronics", "merchantCategory.emergencyServices": "Emergency Services", "merchantCategory.equipmentRental": "Equipment Rental", "merchantCategory.evCharging": "EV Charging", "merchantCategory.financialInstitutions": "Financial institutions", "merchantCategory.financialProfessionalServices": "Financial & Professional Services", "merchantCategory.finesPenalties": "Fines & Penalties", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "Flights", "merchantCategory.flowers": "Flowers", "merchantCategory.flowersGarden": "Flowers & Garden", "merchantCategory.food": "Food", "merchantCategory.freight": "Freight", "merchantCategory.fuel": "Fuel", "merchantCategory.funeralServices": "Funeral Services", "merchantCategory.furniture": "Furniture", "merchantCategory.games": "Games", "merchantCategory.gas": "Gas", "merchantCategory.generalMerchandiseRetail": "General Merchandise & Retail", "merchantCategory.gifts": "Gifts", "merchantCategory.government": "Government", "merchantCategory.governmentServices": "Government Services", "merchantCategory.hardware": "Hardware", "merchantCategory.healthMedicine": "Health & Medicine", "merchantCategory.homeImprovement": "Home Improvement", "merchantCategory.homeServices": "Home Services", "merchantCategory.hotel": "Hotel", "merchantCategory.housing": "Housing", "merchantCategory.insurance": "Insurance", "merchantCategory.internet": "Internet", "merchantCategory.kids": "Kids", "merchantCategory.laundry": "<PERSON><PERSON><PERSON>", "merchantCategory.laundryCleaningServices": "Laundry & Cleaning Services", "merchantCategory.legalGovernmentFees": "Legal & Government Fees", "merchantCategory.luxuries": "Luxuries", "merchantCategory.luxuriesCollectibles": "Luxuries & Collectibles", "merchantCategory.magazines": "Magazines", "merchantCategory.magazinesNews": "Magazines & News", "merchantCategory.marketplaces": "Marketplaces", "merchantCategory.media": "Media", "merchantCategory.medicine": "Medicine", "merchantCategory.mobileHomes": "Mobile Homes", "merchantCategory.moneyTransferCrypto": "Money Transfer & Crypto", "merchantCategory.musicRelated": "Music Related", "merchantCategory.musicalInstruments": "Musical instruments", "merchantCategory.optics": "Optics", "merchantCategory.organizationsClubs": "Organizations & Clubs", "merchantCategory.other": "Other", "merchantCategory.parking": "Parking", "merchantCategory.pawnShops": "Pawn Shops", "merchantCategory.pets": "Pets", "merchantCategory.photoServicesSupplies": "Photo Services & Supplies", "merchantCategory.postalServices": "Postal Services", "merchantCategory.professionalServicesOther": "Professional Services (Other)", "merchantCategory.publicTransport": "Public transport", "merchantCategory.purchases": "Purchases", "merchantCategory.purchasesMiscServices": "Purchases & Misc Services", "merchantCategory.recreationServices": "Recreation Services", "merchantCategory.religiousGoods": "Religious Goods", "merchantCategory.secondhandRetail": "Secondhand Retail", "merchantCategory.shoeHatRepair": "Shoe & Hat Repair", "merchantCategory.shoeRepair": "Shoe repair", "merchantCategory.softwareApps": "Software & Apps", "merchantCategory.specializedRepairs": "Specialized Repairs", "merchantCategory.sport": "Sport", "merchantCategory.sportingGoods": "Sporting goods", "merchantCategory.sportingGoodsRecreation": "Sporting Goods & Recreation", "merchantCategory.sportsClubsFields": "Sports Clubs & Fields", "merchantCategory.stationaryPrinting": "Stationary & Printing", "merchantCategory.stationery": "Stationery", "merchantCategory.storage": "Storage", "merchantCategory.taxes": "Taxes", "merchantCategory.taxi": "Taxi", "merchantCategory.telecomEquipment": "Telecom Equipment", "merchantCategory.telephony": "Telephony", "merchantCategory.tobacco": "Tobacco", "merchantCategory.tollRoad": "Toll road", "merchantCategory.tourismAttractionsAmusement": "Tourism, Attractions & Amusement", "merchantCategory.towing": "Towing", "merchantCategory.toys": "Toys", "merchantCategory.toysHobbies": "Toys & Hobbies", "merchantCategory.trafficFine": "Traffic fine", "merchantCategory.train": "Train", "merchantCategory.travelAgency": "Travel agency", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, Radio & Streaming", "merchantCategory.utilities": "Utilities", "merchantCategory.waterTransport": "Water transport", "merchantCategory.wholesaleClubs": "Wholesale Clubs", "metaMask.subtitle": "Enable MetaMask Mode to redirect all MetaMask connections to Zeal. Clicking MetaMask in dApps will then connect to Zeal.", "metaMask.title": "Can’t connect with <PERSON><PERSON>?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Open your banking app", "monerium-bank-deposit.buttet-point.receive-crypto": "Receive digital EUR", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Send {fiatCurrencyCode} to your account", "monerium-bank-deposit.deposit-account-country": "Country", "monerium-bank-deposit.header": "{full<PERSON><PERSON>}’s personal account", "monerium-bank-details.account-name": "Account name", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "Copied BIC/SWIFT", "monerium-bank-details.bic_swift_copied": "Copied BIC/SWIFT", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "Copied IBAN", "monerium-bank-details.to-wallet": "To wallet", "monerium-bank-details.transfer-fee": "Transfer fee", "monerium-bank-transfer.enable-card.bullet-1": "Complete identity verification", "monerium-bank-transfer.enable-card.bullet-2": "Get personal account details", "monerium-bank-transfer.enable-card.bullet-3": "Deposit from bank account", "monerium-card-delay-relay.success.cta": "Close", "monerium-card-delay-relay.success.subtitle": "For security reasons, card settings changes take 3 minutes to be processed.", "monerium-card-delay-relay.success.title": "Come back in 3min to continue Monerium setup", "monerium-deposit.account-details-info-popup.bullet-point-1": "Any {fiatCurrencyCode} you send to this account will automatically convert into {cryptoCurrencyCode} tokens on {cryptoCurrencyChain} Chain and sent to your wallet", "monerium-deposit.account-details-info-popup.bullet-point-2": "ONLY SEND {fiatCurrencyCode} ({fiatCurrencySymbol}) to your account", "monerium-deposit.account-details-info-popup.title": "Your account details", "monerium.check_order_status.sending": "Sending", "monerium.not-eligible.cta": "Back", "monerium.not-eligible.subtitle": "Monerium is unable to open an account for you. Please select an alternative provider.", "monerium.not-eligible.title": "Try a different provider", "monerium.setup-card.cancel": "Cancel", "monerium.setup-card.continue": "Continue", "monerium.setup-card.create_account": "Create Account", "monerium.setup-card.login": "Log in to Gnosis Pay", "monerium.setup-card.subtitle": "Create or log in to your Gnosis Pay account to enable instant bank deposits.", "monerium.setup-card.subtitle_personal_account": "Get your personal account with Gnosis Pay in minutes:", "monerium.setup-card.title": "Enable bank deposits", "moneriumDepositSuccess.goToWallet": "Go to wallet", "moneriumDepositSuccess.title": "{symbol} received", "moneriumInfo.fees": "You get 0% fees", "moneriumInfo.registration": "Monerium is authorized and regulated as an Electronic Money Institution under the Icelandic Electronic Money Act No. 17/2013 <link>Learn more</link>", "moneriumInfo.selfCustody": "The digital cash that you receive is self custodied and no one else will have control over your asset", "moneriumWithdrawRejected.supportText": "We couldn’t complete your transfer. Please try again and If it still doesn’t work then <link>contact support.</link>", "moneriumWithdrawRejected.title": "Transfer reverted", "moneriumWithdrawRejected.tryAgain": "Try again", "moneriumWithdrawSuccess.supportText": "It may take 24 hrs for your{br}recipient to receive funds", "moneriumWithdrawSuccess.title": "<PERSON><PERSON>", "monerium_enable_banner.text": "Activate bank transfers now", "monerium_error_address_re_link_required.title": "Wallet needs to be re-linked to Monerium", "monerium_error_duplicate_order.title": "Duplicate order", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "&lt; {limit}", "mt-pelerin-fork.options.chf.primary": "Swiss Franc", "mt-pelerin-fork.options.chf.short": "Instant & Free with Mt Pelerin", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Instant & Free with Monerium", "mt-pelerin-fork.title": "What do you want to deposit?", "mtPelerinProviderInfo.fees": "You pay 0% fees", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd is affiliated with SO-FIT, a self-regulatory body recognized by the Swiss Financial Authority (FINMA) under the Anti-Money Laundering Act. <link>Learn more</link>", "mtPelerinProviderInfo.selfCustody": "The digital cash that you receive is self custodied and no one else will have control over your assets", "network-fee-widget.title": "Fees", "network.edit.verifying_rpc": "Verifying RPC", "network.editRpc.predefined_network_info.subtitle": "Like a VPN, <PERSON><PERSON> uses RPCs that prevent your personal data from being tracked.{br}{br}Zeal’s Default RPCs are reliable battle tested RPC providers.", "network.editRpc.predefined_network_info.title": "Zeal privacy RPC", "network.filter.update_rpc_success": "RPC Node saved", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "All Networks", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "Add networks", "networks.filter.add_modal.chain_list.subtitle": "Add any EVM networks", "networks.filter.add_modal.chain_list.title": "Go to Chainlist", "networks.filter.add_modal.dapp_tip.subtitle": "In your favourite dApps, simply switch to the EVM network you want to use and <PERSON><PERSON> will ask you if you want to add it to your wallet.", "networks.filter.add_modal.dapp_tip.title": "Or add a network from any dApp", "networks.filter.add_networks.subtitle": "All EVM networks supported", "networks.filter.add_networks.title": "Add networks", "networks.filter.add_test_networks.title": "Add testnets", "networks.filter.tab.netwokrs": "Networks", "networks.filter.testnets.title": "Testnets", "nft.widget.emptystate": "No collectibles in wallet", "nft_collection.change_account_picture.subtitle": "Are you sure you want to update your profile picture?", "nft_collection.change_account_picture.title": "Update Profile picture to NFT", "nfts.allNfts.pricingPopup.description": "Collectible prices are based on the most recently traded price.", "nfts.allNfts.pricingPopup.title": "Collectible Pricing", "no-passkeys-found.modal.cta": "Close", "no-passkeys-found.modal.subtitle": "We are unable to detect any Zeal passkeys on this device. Make sure you are signed into the cloud account that you used to create your smart wallet.", "no-passkeys-found.modal.title": "No passkeys found", "notValidEmail.title": "Not a valid email address", "notValidPhone.title": "This is not a valid phone number", "notification-settings.title": "Notification settings", "notification-settings.toggles.active-wallets": "Active wallets", "notification-settings.toggles.bank-transfers": "Bank transfers", "notification-settings.toggles.card-payments": "Card payments", "notification-settings.toggles.readonly-wallets": "Read only wallets", "ntft.groupHeader.text": "Collectibles", "on_ramp.crypto_completed": "Completed", "on_ramp.fiat_completed": "Completed", "onboarding-widget.subtitle.card_created_from_order.left": "Visa card", "onboarding-widget.subtitle.card_created_from_order.right": "Activate card", "onboarding-widget.subtitle.card_order_ready.left": "Physical Visa card", "onboarding-widget.subtitle.default": "Bank transfers & Visa Card", "onboarding-widget.title.card-order-in-progress": "Continue card order", "onboarding-widget.title.card_created_from_order": "Card has been sent", "onboarding-widget.title.kyc_approved": "Transfers & Card ready", "onboarding-widget.title.kyc_failed": "Account not possible", "onboarding-widget.title.kyc_not_started": "Continue setup", "onboarding-widget.title.kyc_started_documents_requested": "Complete verification", "onboarding-widget.title.kyc_started_resubmission_requested": "Retry verification", "onboarding-widget.title.kyc_started_verification_in_progress": "Verifying identity", "onboarding.loginOrCreateAccount.amountOfAssets": "$10bn+ of assets", "onboarding.loginOrCreateAccount.cards.subtitle": "Only available in certain regions. By continuing you accept our <Terms>Terms</Terms> & <PrivacyPolicy>Privacy Policy</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Visa card with high{br}returns and no fees", "onboarding.loginOrCreateAccount.createAccount": "Create Account", "onboarding.loginOrCreateAccount.earn.subtitle": "Returns vary; capital at risk. By continuing you accept our <Terms>Terms</Terms> & <PrivacyPolicy>Privacy Policy</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "Earning {percent} per year{br}Trusted by {currencySymbol}5bn+", "onboarding.loginOrCreateAccount.earningPerYear": "Earning {percent}{br}per year", "onboarding.loginOrCreateAccount.login": "<PERSON><PERSON>", "onboarding.loginOrCreateAccount.trading.subtitle": "Capital at risk. By continuing you accept our <Terms>Terms</Terms> & <PrivacyPolicy>Privacy Policy</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Invest in everything,{br}BTC to S&P", "onboarding.loginOrCreateAccount.trustedBy": "Digital money markets{br}Trusted by {assets}", "onboarding.wallet_stories.close": "Close", "onboarding.wallet_stories.previous": "Previous", "order-earn-deposit-bridge.deposit": "<PERSON><PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.into": "Into", "otpIncorrectMessage": "Confirmation code is incorrect", "passkey-creation-not-possible.modal.close": "Close", "passkey-creation-not-possible.modal.subtitle": "We were unable to create a passkey for your wallet. Please make sure your device supports passkeys and try again. <link>Contact support</link> if the issue persists.", "passkey-creation-not-possible.modal.title": "Unable to create passkey", "passkey-not-supported-in-mobile-browser.modal.cta": "Download Zeal", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Smart wallets aren’t supported on mobile browsers.", "passkey-not-supported-in-mobile-browser.modal.title": "Download Zeal app to continue", "passkey-recovery.recovering.deploy-signer.loading-text": "Verifying passkey", "passkey-recovery.recovering.loading-text": "Recovering wallet", "passkey-recovery.recovering.signer-not-found.subtitle": "We couldn’t link your passkey to an active wallet. If you have funds in your wallet, contact the Zeal team for support.", "passkey-recovery.recovering.signer-not-found.title": "No wallet found", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "Try a different passkey", "passkey-recovery.select-passkey.banner.subtitle": "Make sure you’re logged into the correct account on your device. Passkeys are account-specific.", "passkey-recovery.select-passkey.banner.title": "Don’t see your wallet’s passkey?", "passkey-recovery.select-passkey.continue": "Select passkey", "passkey-recovery.select-passkey.subtitle": "Select the passkey linked to your wallet to regain access.", "passkey-recovery.select-passkey.title": "Select Passkey", "passkey-story_1.subtitle": "With a Smart Wallet you can pay network fees in most tokens, and don’t have to worry about gas.", "passkey-story_1.title": "Forget gas - pay network fees in most tokens", "passkey-story_2.subtitle": "Built on Safe’s industry-leading smart contracts that secure more than $100 billion in more than 20 million wallets.", "passkey-story_2.title": "Secured by Safe", "passkey-story_3.subtitle": "Smart Wallets work on major Ethereum-compatible networks. Check the supported networks before sending assets.", "passkey-story_3.title": "Major EVM networks supported", "password.add.header": "Create password", "password.add.includeLowerAndUppercase": "Lower and upper case letters", "password.add.includesNumberOrSpecialChar": "One number or symbol", "password.add.info.subtitle": "We don’t send your password to our servers or back it up for you", "password.add.info.t_and_c": "By continuing you accept our <Terms>Terms</Terms> & <PrivacyPolicy>Privacy Policy</PrivacyPolicy>", "password.add.info.title": "Your password stays in this device", "password.add.inputPlaceholder": "Create password", "password.add.shouldContainsMinCharsCheck": "10+ characters", "password.add.subheader": "You’ll use your password to unlock <PERSON><PERSON>", "password.add.success.title": "Password created 🔥", "password.confirm.header": "Confirm password", "password.confirm.passwordDidNotMatch": "Passwords must match", "password.confirm.subheader": "Enter your password one more time", "password.create_pin.subtitle": "This passcode locks the Zeal app", "password.create_pin.title": "Create your passcode", "password.enter_pin.title": "Enter passcode", "password.incorrectPin": "Incorrect passcode", "password.pin_is_not_same": "Passcode doesn’t match", "password.placeholder.enter": "Enter password", "password.placeholder.reenter": "Re-enter password", "password.re_enter_pin.subtitle": "Enter the same passcode again", "password.re_enter_pin.title": "Confirm passcode", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "Pending", "pending-send.details.pending": "Pending", "pending-send.details.processing": "Processing", "permit-info.modal.description": "Permits are requests that if signed, allow apps to move your tokens on your behalf, for example, to make a swap.{br}Permits are similar to Approvals but they don’t cost you any network fees to sign.", "permit-info.modal.title": "What are Permits?", "permit.edit-expiration": "Edit {currency} expiration", "permit.edit-limit": "Edit {currency} spend limit", "permit.edit-modal.expiresIn": "Expires in…", "permit.expiration-warning": "{currency} expiration warning", "permit.expiration.info": "{currency} expiration info", "permit.expiration.never": "Never", "permit.spend-limit.info": "{currency} spend limit info", "permit.spend-limit.warning": "{currency} spend limit warning", "phoneNumber.title": "phone number", "physicalCardOrderFlow.cardOrdered": "Card ordered", "physicalCardOrderFlow.city": "City", "physicalCardOrderFlow.orderCard": "Order Card", "physicalCardOrderFlow.postcode": "Postcode", "physicalCardOrderFlow.shippingAddress.subtitle": "Where your card will be sent", "physicalCardOrderFlow.shippingAddress.title": "Shipping address", "physicalCardOrderFlow.street": "Street", "placeholderDapps.1inch.description": "Exchange using the best routes", "placeholderDapps.aave.description": "Lend and borrow tokens", "placeholderDapps.bungee.description": "Bridge networks via the best routes", "placeholderDapps.compound.description": "Lend and borrow tokens", "placeholderDapps.cowswap.description": "Exchange at best rates on Gnosis", "placeholderDapps.gnosis-pay.description": "Manage your Gnosis Pay card", "placeholderDapps.jumper.description": "Bridge networks via the best routes", "placeholderDapps.lido.description": "Stake ETH for more ETH", "placeholderDapps.monerium.description": "eMoney and Bank transfers", "placeholderDapps.odos.description": "Exchange using the best routes", "placeholderDapps.stargate.description": "Bridge or Stake for <14% APY", "placeholderDapps.uniswap.description": "One of the most popular exchanges", "pleaseAllowNotifications.cardPayments": "Card payments", "pleaseAllowNotifications.customiseInSettings": "Customise in settings", "pleaseAllowNotifications.enable": "Enable", "pleaseAllowNotifications.forWalletActivity": "For wallet activity", "pleaseAllowNotifications.title": "Get wallet notifications", "pleaseAllowNotifications.whenReceivingAssets": "When receiving assets", "portfolio.quick-actions.add_funds": "Add Funds", "portfolio.quick-actions.buy": "Buy", "portfolio.quick-actions.deposit": "<PERSON><PERSON><PERSON><PERSON>", "portfolio.quick-actions.send": "Send", "portfolio.view.lastRefreshed": "Refreshed {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Top up your testnet AVAX", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Go to Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "Top up your testnet BNB", "portfolio.view.topupTestNet.BscTestnet.secondary": "Go to Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Top up your testnet SepETH", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Go to Sepolia Faucet", "portfolio.view.topupTestNet.FantomTestnet.primary": "Top up your testnet FTM", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Go to Faucet", "privateKeyConfirmation.banner.subtitle": "Anyone one who has your private key has access to your wallet and your funds. Only scammers ask for your Private Key.", "privateKeyConfirmation.banner.title": "I understand the risks", "privateKeyConfirmation.title": "NEVER SHARE your Private Key with anyone", "rating-request.not-now": "Not now", "rating-request.title": "Likely to recommend <PERSON><PERSON>?", "receive_funds.address-text": "This is your unique wallet address. You can safely share it with others.", "receive_funds.copy_address": "Copy wallet address", "receive_funds.network-warning.eoa.subtitle": "<link>View standard network list</link>. Assets sent on non-EVM networks will be lost.", "receive_funds.network-warning.eoa.title": "All Ethereum-based networks supported", "receive_funds.network-warning.scw.subtitle": "<link>View supported networks</link>. Assets sent on other networks will be lost.", "receive_funds.network-warning.scw.title": "Important: Use supported networks only", "receive_funds.scan_qr_code": "Scan a QR code", "receiving.in.days": "Receiving in {days}d", "receiving.this.week": "Receiving this week", "receiving.today": "Receiving today", "reference.error.maximum_number_of_characters_exceeded": "Too many characters", "referral-code.placeholder": "Paste invite link", "referral-code.subtitle": "Click your friend’s link again, or paste the link below. We want to make sure you get your rewards.", "referral-code.title": "Did a friend send you {bR<PERSON>ard}?", "rekyc.verification_deadline.subtitle": "Complete verification within {daysUntil} days to keep using your card.", "rekyc.verification_required.subtitle": "Complete verification to continue using your card.", "reminder.fund": "💸 Add funds — start earning 6% instantly", "reminder.onboarding": "🏁 Finish setup — earn 6% on your deposits", "remove-owner.confirmation.subtitle": "For security, settings changes take 3 minutes to process, during which your card will be temporarily frozen, and payments won’t be possible.", "remove-owner.confirmation.title": "Your card will be frozen for 3min while settings update", "restore-smart-wallet.wallet-recovered": "Wallet recovered", "rewardClaimCelebration.claimedTitle": "<PERSON><PERSON><PERSON> already claimed", "rewardClaimCelebration.subtitle": "For inviting friends", "rewardClaimCelebration.title": "You’ve earned", "rewards-warning.subtitle": "Removing this account will pause access to any linked rewards. You can restore the account anytime to claim them.", "rewards-warning.title": "You’ll lose access to your rewards", "rewards.copiedInviteLink": "Copied invite link", "rewards.createAccount": "Copy invite link", "rewards.header.subtitle": "We’ll send {a<PERSON><PERSON><PERSON>} to you and {bReward} to your friend, when they spend {bSpendLimitReward}.", "rewards.header.title": "Get {amountA}{br}Give {amountB}", "rewards.sendInvite": "Send invite", "rewards.sendInviteTip": "Pick a friend and we’ll give them {bAmount}", "route.fees": "Fees {fees}", "routesNotFound.description": "The exchange route for the {from}-{to} network combination is not available.", "routesNotFound.title": "No exchange route available", "rpc.OrderBuySignMessage.subtitle": "Using Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Using Swaps.IO", "rpc.addCustomNetwork.addNetwork": "Add network", "rpc.addCustomNetwork.chainId": "Chain ID", "rpc.addCustomNetwork.nativeToken": "Native token", "rpc.addCustomNetwork.networkName": "Network name", "rpc.addCustomNetwork.operationDescription": "Allows this website to add a network to your wallet. <PERSON><PERSON> cannot check the safety of custom networks, make sure you understand the risks.", "rpc.addCustomNetwork.rpcUrl": "RPC URL", "rpc.addCustomNetwork.subtitle": "Using {name}", "rpc.addCustomNetwork.title": "Add network", "rpc.send_token.network_not_supported.subtitle": "We’re working to enable transactions on this network. Thank you for your patience 🙏", "rpc.send_token.network_not_supported.title": "Network coming soon", "rpc.send_token.send_or_receive.settings": "Settings", "rpc.sign.accept": "Accept", "rpc.sign.cannot_parse_message.body": "We couldn’t decode this message. Only accept this request if you trust this app.{br}{br}Messages can be used to log you in to an app, but can also give apps control over your tokens.", "rpc.sign.cannot_parse_message.header": "Proceed with caution", "rpc.sign.import_private_key": "Import keys", "rpc.sign.subtitle": "For {name}", "rpc.sign.title": "Sign", "safe-creation.success.title": "Wallet created", "safe-safety-checks-popup.title": "Transaction Safety Checks", "safetyChecksPopup.title": "Site Safety Checks", "scan_qr_code.description": "Scan wallet QR or connect to an App", "scan_qr_code.show_qr_code": "Show my QR code", "scan_qr_code.tryAgain": "Try again", "scan_qr_code.unlockCamera": "Unlock camera", "screen-lock-missing.modal.close": "Close", "screen-lock-missing.modal.subtitle": "Your device requires a screen lock to use passkeys. Please set up a screen lock and try again.", "screen-lock-missing.modal.title": "Screen lock missing", "seedConfirmation.banner.subtitle": "Anyone one who has your Secret Phrase has access to your wallet and your funds. Only scammers ask for your Secret Phrase.", "seedConfirmation.title": "NEVER SHARE your Secret Phrase with anyone", "select-active-owner.subtitle": "You have multiple wallets linked to your card. Select one to connect to <PERSON>eal. You can switch anytime.", "select-active-owner.title": "Select wallet", "select-card.title": "Select card", "select-crypto-currency-title": "Select token", "select-token.title": "Select token", "selectEarnAccount.chf.description.steps": "· Withdraw funds 24/7, no lockups {br}· Interest accrues every second {br}· Overprotected deposits in <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} per year in CHF", "selectEarnAccount.eur.description.steps": "· Withdraw funds 24/7, no lockups {br}· Interest accrues every second {br}· Overprotected loans with <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} per year in EUR", "selectEarnAccount.subtitle": "You can change anytime", "selectEarnAccount.title": "Select Currency", "selectEarnAccount.usd.description.steps": "· Withdraw funds 24/7, no lockups {br}· Interest accrues every second {br}· Overprotected deposits in <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} per year in USD", "selectEarnAccount.zero.description_general": "Hold digital cash without earning interest", "selectEarnAccount.zero.title": "0% per year", "selectRechargeThreshold.button.enterAmount": "Enter amount", "selectRechargeThreshold.button.setTo": "Set to {amount}", "selectRechargeThreshold.description.line1": "When your card drops below {amount}, it automatically recharges back to {amount} from your Earn account.", "selectRechargeThreshold.description.line2": "A lower target keeps more in your Earn account (earning 3%). You can change this anytime.", "selectRechargeThreshold.title": "Set card target balance", "select_currency_to_withdraw.select_token_to_withdraw": "Select token to withdraw", "send-card-token.form.send": "Send", "send-card-token.form.send-amount": "Top up amount", "send-card-token.form.title": "Add cash to Card", "send-card-token.form.to-address": "Card", "send-safe-transaction.network-fee-widget.error": "You need {amount} or choose another token", "send-safe-transaction.network-fee-widget.no-fee": "No fees", "send-safe-transaction.network-fee-widget.title": "Fees", "send-safe-transaction.network_fee_widget.title": "Network fee", "send.banner.fees": "You need {amount} more {currency} to pay fees", "send.banner.toAddressNotSupportedNetwork.subtitle": "The recipients wallet does not support {network}. Change to a supported token.", "send.banner.toAddressNotSupportedNetwork.title": "Network not support for recipient", "send.banner.walletNotSupportedNetwork.subtitle": "Smart wallets can’t make transactions on {network}. Change to a supported token.", "send.banner.walletNotSupportedNetwork.title": "Token network not supported", "send.empty-portfolio.empty-state": "We found no tokens", "send.empty-portfolio.header": "Tokens", "send.titile": "Send", "sendLimit.success.subtitle": "Your daily spend limit will be updated in 3 minutes. You can continue spending within your current limit until then.", "sendLimit.success.title": "This change will take 3 minutes", "send_crypto.form.disconnected.cta.addFunds": "Add funds", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Switch to {network}", "send_crypto.form.disconnected.label": "Amount to transfer", "send_to.qr_code.description": "Scan a QR code to send to a wallet", "send_to.qr_code.title": "Scan QR code", "send_to_card.header": "Send to Card address", "send_to_card.select_sender.add_wallet": "Add wallet", "send_to_card.select_sender.header": "Select sender", "send_to_card.select_sender.search.default_placeholder": "Search address or ENS", "send_to_card.select_sender.show_card_address_button_description": "Show card address", "send_token.form.select-address": "Select address", "send_token.form.send-amount": "Send amount", "send_token.form.title": "Send", "setLimit.amount.error.zero_amount": "You won’t be able to make any payments", "setLimit.error.max_limit_reached": "Set limit to max {amount}", "setLimit.error.same_as_current_limit": "Same as current limit", "setLimit.placeholder": "Current: {amount}", "setLimit.submit": "<PERSON>", "setLimit.submit.error.amount_required": "Enter Amount", "setLimit.subtitle": "This is the amount you can spend per day with your card.", "setLimit.title": "Set daily spend limit", "settings.accounts": "Accounts", "settings.accountsSeeAll": "See all", "settings.addAccount": "Add wallet", "settings.card": "Card Settings", "settings.connections": "App Connections", "settings.currency": "<PERSON><PERSON><PERSON>", "settings.default_currency_selector.title": "<PERSON><PERSON><PERSON><PERSON>", "settings.discord": "Discord", "settings.experimentalMode": "Experimental mode", "settings.experimentalMode.subtitle": "Test new features", "settings.language": "Language", "settings.lockZeal": "Lock Zeal", "settings.notifications": "Notifications", "settings.open_expanded_view": "Open expanded view", "settings.privacyPolicy": "Privacy Policy", "settings.settings": "Settings", "settings.termsOfUse": "Terms of Use", "settings.twitter": "𝕏 / Twitter", "settings.version": "Version {version} env: {env}", "setup-card.confirmation": "Get Virtual Card", "setup-card.confirmation.subtitle": "Make payments online and add to your {type} wallet for contactless payments.", "setup-card.getCard": "Get card", "setup-card.order.physicalCard": "Physical card", "setup-card.order.physicalCard.steps": "· A physical VISA Gnosis Pay {br}· Takes up to 3 weeks to ship to you {br}· Use for in person payments and with ATMs. {br}· Add to Apple/Google wallet (only for supported countries", "setup-card.order.subtitle1": "You can use several cards at the same time", "setup-card.order.title": "What type of card?", "setup-card.order.virtualCard": "Virtual card", "setup-card.order.virtual_card.steps": "· Digital VISA Gnosis Pay {br}· Use instantly for online payments {br}· Add to Apple/Google wallet (only for supported countries)", "setup-card.orderCard": "Order card", "setup-card.virtual-card": "Get Virtual Card", "setup.notifs.fakeAndroid.title": "Notifications for payments and incoming transfers", "setup.notifs.fakeIos.subtitle": "Zeal can alert you when you receive cash, or spend with your Visa card. You can change this later.", "setup.notifs.fakeIos.title": "Notifications for payments and incoming transfers", "sign.PermitAllowanceItem.spendLimit": "Spend limit", "sign.ledger.subtitle": "We sent the transaction request to your hardware wallet. Please continue there.", "sign.ledger.title": "Sign hardware wallet", "sign.passkey.subtitle": "Your browser should prompt you to sign with the passkey associated with this wallet. Please continue there.", "sign.passkey.title": "Select passkey", "signal_aborted_for_uknown_reason.title": "Network request cancelled", "simulatedTransaction.BridgeTrx.info.title": "Bridge", "simulatedTransaction.CardTopUp.info.title": "Add cash to card", "simulatedTransaction.CardTopUpTrx.info.title": "Add cash to card", "simulatedTransaction.NftCollectionApproval.approve": "Approve NFT collection", "simulatedTransaction.OrderBuySignMessage.title": "Buy", "simulatedTransaction.OrderCardTopupSignMessage.title": "Add to card", "simulatedTransaction.OrderEarnDepositBridge.title": "Deposit into Earn", "simulatedTransaction.P2PTransaction.info.title": "Send", "simulatedTransaction.PermitSignMessage.title": "Permit", "simulatedTransaction.SingleNftApproval.approve": "Approve NFT", "simulatedTransaction.UnknownSignMessage.title": "Sign", "simulatedTransaction.Withdrawal.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.approval.title": "Approve", "simulatedTransaction.approve.info.title": "Approve", "simulatedTransaction.p2p.info.account": "To", "simulatedTransaction.p2p.info.unlabelledAccount": "Unlabelled wallet", "simulatedTransaction.unknown.info.receive": "Receive", "simulatedTransaction.unknown.info.send": "Send", "simulatedTransaction.unknown.using": "Using {app}", "simulation.approval.modal.text": "When you accept an approval you are giving permission for or a specific app/smart contract to use your tokens or NFTs in future transactions.", "simulation.approval.modal.title": "What are Approvals?", "simulation.approval.spend-limit.label": "Spend limit", "simulation.approve.footer.for": "For", "simulation.approve.unlimited": "Unlimited", "simulationNotAvailable.title": "Unknown Action", "smart-wallet-activation-view.on": "On", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password may block access to your wallet", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Use Apple or Google to safely setup Zeal", "smart-wallet.passkey-notice.title": "Avoid 1Password", "spend-limits.high.modal.text": "Set a spend limit close to the amount of tokens you’ll actually use with an app or smart contract. High limits are risky and can make it easier for scammers to steal your tokens.", "spend-limits.high.modal.text_sign_message": "Spend limit should be close to the amount of tokens you’ll actually use with an app or smart contract. High limits are risky and can make it easier for scammers to steal your tokens.", "spend-limits.high.modal.title": "High spend limit", "spend-limits.modal.text": "Spend limit is how many tokens an app can use on your behalf. You can change or remove this limit anytime. To stay secure, keep Spend limits close to the amount of tokens you’ll actually use with an app.", "spend-limits.modal.title": "What is spend limit?", "spent-limit-info.modal.description": "Spend limit is how many tokens an app can use on your behalf. You can change or remove this limit anytime. To stay secure, keep Spend limits close to the amount of tokens you’ll actually use with an app.", "spent-limit-info.modal.title": "What is spend limit?", "sswaps-io.transfer-provider": "Transfer provider", "storage.accountDetails.activateWallet": "Activate wallet", "storage.accountDetails.changeWalletLabel": "Change wallet Label", "storage.accountDetails.deleteWallet": "Remove wallet", "storage.accountDetails.setup_recovery_kit": "Recovery Kit", "storage.accountDetails.showPrivateKey": "Show Private Key", "storage.accountDetails.showWalletAddress": "Show wallet address", "storage.accountDetails.smartBackup": "Backup & Recovery", "storage.accountDetails.viewSsecretPhrase": "View Secret Phrase", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets?", "storage.manageAccounts.title": "Wallets", "submit-userop.progress.text": "Sending", "submit.error.amount_high": "Amount too high", "submit.error.amount_hight": "Amount too hight", "submit.error.amount_low": "Amount too low", "submit.error.amount_required": "Enter Amount", "submit.error.maximum_number_of_characters_exceeded": "Reduce message characters", "submit.error.not_enough_balance": "Not enough balance", "submit.error.recipient_required": "Recipient required", "submit.error.routes_not_found": "Routes not found", "submitSafeTransaction.monitor.title": "Transaction result", "submitSafeTransaction.sign.title": "Transaction result", "submitSafeTransaction.state.sending": "Sending", "submitSafeTransaction.state.sign": "Creating", "submitSafeTransaction.submittingToRelayer.title": "Transaction result", "submitTransaction.cancel": "Cancel", "submitTransaction.cancel.attemptingToStop": "Attempting to stop", "submitTransaction.cancel.failedToStop": "Failed to stop", "submitTransaction.cancel.stopped": "Stopped", "submitTransaction.cancel.title": "Transaction preview", "submitTransaction.failed.banner.description": "The network cancelled this transaction unexpectedly. Try again or contact us.", "submitTransaction.failed.banner.title": "Transaction failed", "submitTransaction.failed.execution_reverted.title": "The app had an error", "submitTransaction.failed.execution_reverted_without_message.title": "The app had an error", "submitTransaction.failed.out_of_gas.description": "Network cancelled transaction because it used more network fees than expected", "submitTransaction.failed.out_of_gas.title": "Network error", "submitTransaction.sign.title": "Transaction result", "submitTransaction.speedUp": "Speed up", "submitTransaction.state.addedToQueue": "Added to queue", "submitTransaction.state.addedToQueue.short": "Queued", "submitTransaction.state.cancelled": "Stopped", "submitTransaction.state.complete": "{currencyCode} added to Zeal", "submitTransaction.state.complete.subtitle": "Check your Zeal portfolio", "submitTransaction.state.completed": "Complete", "submitTransaction.state.failed": "Failed", "submitTransaction.state.includedInBlock": "Included in block", "submitTransaction.state.includedInBlock.short": "In block", "submitTransaction.state.replaced": "Replaced", "submitTransaction.state.sendingToNetwork": "Sending to network", "submitTransaction.stop": "Stop", "submitTransaction.submit": "Submit", "submitted-user-operation.state.bundled": "Queueing", "submitted-user-operation.state.completed": "Completed", "submitted-user-operation.state.failed": "Failed", "submitted-user-operation.state.pending": "Relaying", "submitted-user-operation.state.rejected": "Rejected", "submittedTransaction.failed.title": "Transaction failed", "success_splash.card_activated": "Card activated", "supportFork.give-feedback.title": "Give feedback", "supportFork.itercom.description": "<PERSON>eal handles questions about deposits, Earn, rewards, or anything else", "supportFork.itercom.title": "Wallet questions", "supportFork.title": "Get help with", "supportFork.zendesk.subtitle": "Gnosis Pay handles questions about card payments, identity checks, or refunds", "supportFork.zendesk.title": "Card payments & identity", "supported-networks.ethereum.warning": "High fees", "supportedNetworks.networks": "Supported networks", "supportedNetworks.oneAddressForAllNetworks": "One address for all networks", "supportedNetworks.receiveAnyAssets": "Receive any assets from supported networks directly to your Zeal wallet using the same address", "swap.form.error.no_routes_found": "No routes found", "swap.form.error.not_enough_balance": "Not enough balance", "swaps-io-details.bank.serviceProvider": "Service provider", "swaps-io-details.details.processing": "Processing", "swaps-io-details.pending": "Pending", "swaps-io-details.rate": "Rate", "swaps-io-details.serviceProvider": "Service provider", "swaps-io-details.transaction.from.processing": "Strated Transaction", "swaps-io-details.transaction.networkFees": "Network fees", "swaps-io-details.transaction.state.completed-transaction": "Completed Transaction", "swaps-io-details.transaction.state.started-transaction": "Started Transaction", "swaps-io-details.transaction.to.processing": "Completed Transaction", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Deposit should complete soon. Kinetex is still processing your transaction.", "swapsIO.monitoring.awaitingLiqSend.title": "Delayed", "swapsIO.monitoring.awaitingRecive.title": "Relaying", "swapsIO.monitoring.awaitingSend.title": "Queueing", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Tokens were sent to Kinetex, but will be returned soon. Kinetex couldn’t complete the destination transaction.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Returning tokens", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Tokens have not been transferred due to an unknown error. Please try again.", "swapsIO.monitoring.cancelledNoSlash.title": "Tokens returned", "swapsIO.monitoring.cancelledSlashed.subtitle": "Tokens have been returned. Kinetex couldn’t complete the destination transaction.", "swapsIO.monitoring.cancelledSlashed.title": "Tokens returned", "swapsIO.monitoring.completed.title": "Completed", "taker-metadata.earn": "Earn in digital USD with Sky", "taker-metadata.earn.aave": "Earn in digital EUR with Aave", "taker-metadata.earn.aave.cashout24": "Cash out instantly, 24/7", "taker-metadata.earn.aave.trusted": "Trusted with $27B, 2+ years", "taker-metadata.earn.aave.yield": "Yield accrues every seccond", "taker-metadata.earn.chf": "Earn in digital CHF", "taker-metadata.earn.chf.cashout24": "Cash out instantly, 24/7", "taker-metadata.earn.chf.trusted": "Trusted with Fr. 28M", "taker-metadata.earn.chf.yield": "Yield accrues every seccond", "taker-metadata.earn.usd.cashout24": "Cash out instantly, 24/7", "taker-metadata.earn.usd.trusted": "Trusted with $10.7B, 5+ years", "taker-metadata.earn.usd.yield": "Yield accrues every seccond", "test": "<PERSON><PERSON><PERSON><PERSON>", "to.titile": "To", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "Assets", "token.groupHeader.titleWithSum": "Assets {sum}", "token.hidden_tokens.page.title": "Hidden tokens", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "Add token", "token.widget.cashback_empty": "No transactions yet", "token.widget.emptyState": "No tokens in wallet", "tokens.cash": "Cash", "top-up-card-from-earn-view.approve.for": "For", "top-up-card-from-earn-view.approve.into": "Into", "top-up-card-from-earn-view.swap.from": "From", "top-up-card-from-earn-view.swap.to": "To", "top-up-card-from-earn-view.withdraw.to": "To", "top-up-card-from-earn.trx.title.approval": "Approve swap", "top-up-card-from-earn.trx.title.swap": "Add to card", "top-up-card-from-earn.trx.title.withdrawal": "Withdraw from Earn", "topUpDapp.connectWallet": "Connect wallet", "topup-fee-breakdown.bungee-fee": "External provider fee", "topup-fee-breakdown.header": "Transaction fee", "topup-fee-breakdown.network-fee": "Network fee", "topup-fee-breakdown.total-fee": "Total fee", "topup.continue-in-wallet": "Continue in your wallet", "topup.send.title": "Send", "topup.submit-transaction.close": "Close", "topup.submit-transaction.sent-to-wallet": "Send {amount}", "topup.to": "To", "topup.transaction.complete.close": "Close", "topup.transaction.complete.try-again": "Try again", "transaction-request.nonce-too-low.modal.button-text": "Close", "transaction-request.nonce-too-low.modal.text": "A transaction with the same serial number (nonce) has already been completed, so you can no longer submit this transaction. This can happen if you make transactions close to each other or if you’re trying to speed up or cancel a transaction that has already been completed.", "transaction-request.nonce-too-low.modal.title": "Transaction with same nonce has been completed", "transaction-request.replaced.modal.button-text": "Close", "transaction-request.replaced.modal.text": "We are not able to track the status of this transaction. Either it has been replaced by another transaction or the RPC node is having issues.", "transaction-request.replaced.modal.title": "Could not find transaction status", "transaction.activity.details.modal.close": "Close", "transaction.cancel_popup.cancel": "No, wait", "transaction.cancel_popup.confirm": "Yes, stop", "transaction.cancel_popup.description": "To stop, you need to pay a new network fee instead of the original fee of {oldFee}", "transaction.cancel_popup.description_without_original": "To stop, you need to pay a new network fee", "transaction.cancel_popup.not_supported.subtitle": "Stopping transactions are not supported on {network}", "transaction.cancel_popup.not_supported.title": "Not supported", "transaction.cancel_popup.stopping_fee": "Network stopping fee", "transaction.cancel_popup.title": "Stop transaction?", "transaction.in-progress": "In progress", "transaction.inProgress": "In progress", "transaction.speed_up_popup.cancel": "No, wait", "transaction.speed_up_popup.confirm": "Yes, speed up", "transaction.speed_up_popup.description": "To speed up, you need to pay a new network fee instead of the original fee of {amount}", "transaction.speed_up_popup.description_without_original": "To speed up, you need to pay a new network fee", "transaction.speed_up_popup.seed_up_fee_title": "Network speed up fee", "transaction.speed_up_popup.title": "Speed up transaction?", "transaction.speedup_popup.not_supported.subtitle": "Transaction speed ups are not supported on {network}", "transaction.speedup_popup.not_supported.title": "Not supported", "transaction.subTitle.failed": "Failed", "transactionDetails.cashback.not-qualified": "Not qualified", "transactionDetails.cashback.paid": "{amount} paid", "transactionDetails.cashback.pending": "{amount} pending", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "Unknown", "transactionDetails.cashback_estimate": "Cashback estimate", "transactionDetails.category": "Category", "transactionDetails.exchangeRate": "Exchange rate", "transactionDetails.location": "Location", "transactionDetails.payment-approved": "Payment Approved", "transactionDetails.payment-declined": "Payment Declined", "transactionDetails.payment-reversed": "Payment Reversed", "transactionDetails.recharge.amountSentFromEarn.title": "Amount sent from Earn", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "Recharged to card", "transactionDetails.recharge.rate.title": "Rate", "transactionDetails.recharge.transactionId.title": "Transaction ID", "transactionDetails.refund": "Refund", "transactionDetails.reversal": "Reversal", "transactionDetails.transactionCurrency": "Transaction currency", "transactionDetails.transactionId": "Transaction ID", "transactionDetails.type": "Transaction", "transactionRequestWidget.approve.subtitle": "For {target}", "transactionRequestWidget.p2p.subtitle": "To {target}", "transactionRequestWidget.unknown.subtitle": "Using {target}", "transactionSafetyChecksPopup.title": "Transaction Safety Checks", "transactions.main.activity.title": "Activity", "transactions.page.hiddenActivity.title": "Hidden activity", "transactions.page.title": "Activity", "transactions.viewTRXHistory.emptyState": "No transactions yet", "transactions.viewTRXHistory.errorMessage": "We failed to load your transactions history", "transactions.viewTRXHistory.hidden.emptyState": "No hidden transactions", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Activity not supported for testnets", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Activity not supported for testnets{br}<link>Go to block explorer</link>", "transfer_provider": "Transfer provider", "transfer_setup_with_different_wallet.subtitle": "Bank transfers are setup with a different wallet. You can only have one wallet connected to transfers.", "transfer_setup_with_different_wallet.swtich_and_continue": "Switch and continue", "transfer_setup_with_different_wallet.title": "Switch wallet", "tx-sent-to-wallet.button": "Close", "tx-sent-to-wallet.subtitle": "Continue in {wallet}", "unblockProviderInfo.fees": "You get the lowest fees possible: 0% up to $5k per month and 0.2% above that.", "unblockProviderInfo.registration": "Unblock is registered & authorised by FNTT to provide VASP exchange and custody services, and are a registered MSB provider with US Fincen. <link>Learn more</link>", "unblockProviderInfo.selfCustody": "The digital cash that you receive is self custodied and no one else will have control over your asset", "unblock_invalid_faster_payment_configuration.subtitle": "The bank account you provided does not support European SEPA transfers or UK Faster Payments. Please provide another account", "unblock_invalid_faster_payment_configuration.title": "Different account required", "unknownTransaction.primaryText": "Card transaction", "unsupportedCountry.subtitle": "Bank transfers are not yet available in your country.", "unsupportedCountry.title": "Unavailable in {country}", "update-app-popup.subtitle": "The latest update is packed with fixes, features and more magic. Update to the latest version and level up your Zeal.", "update-app-popup.title": "Update Zeal version", "update-app-popup.update-now": "Update Now", "user_associated_with_other_merchant.subtitle": "This wallet cannot be used for bank transfers. Please use another wallet or report on our Discord for support and updates.", "user_associated_with_other_merchant.title": "Wallet cannot be used", "user_associated_with_other_merchant.try_with_another_wallet": "Try with another wallet", "user_email_already_exists.subtitle": "You’ve already setup bank transfer with another wallet. Please try again with the wallet you used previously.", "user_email_already_exists.title": "Transfers setup with a different wallet", "user_email_already_exists.try_with_another_wallet": "Try with another wallet", "validation.invalid.iban": "Invalid IBAN", "validation.required": "Required", "validation.required.first_name": "Required First name", "validation.required.iban": "Required IBAN", "validation.required.last_name": "Required Last name", "verify-passkey.cta": "Verify passkey", "verify-passkey.subtitle": "Verify that your passkey is created and properly secured.", "verify-passkey.title": "Verify passkey", "view-cashback.cashback-next-cycle": "Cashback rate in {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "Deposit to get cashback", "view-cashback.pending": "{money} Pending", "view-cashback.pending-rewards.not_paid": "Receiving in {days}d", "view-cashback.pending-rewards.paid": "Received this week", "view-cashback.received-rewards": "Received rewards", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Unconfirmed payments", "view-cashback.upcoming": "Upcoming {money}", "virtual-card-order.configure-safe.loading-text": "Creating card", "virtual-card-order.create-order.loading-text": "Activating card", "virtual-card-order.create-order.success-text": "Card activated", "virtualCard.activateCard": "Activate Card", "walletDeleteConfirm.main_action": "Remove", "walletDeleteConfirm.subtitle": "You’ll have to import again to view portfolio or make transactions", "walletDeleteConfirm.title": "Remove wallet?", "walletSetting.header": "Wallet Settings", "wallet_connect.connect.cancel": "Cancel", "wallet_connect.connect.connect_button": "Connect Zeal", "wallet_connect.connect.title": "Connect", "wallet_connect.connected.title": "Connected", "wallet_connect_add_chain_missing.title": "Network not supported", "wallet_connect_proposal_expired.title": "Connection expired", "withdraw": "Withdraw", "withdraw.confirmation.close": "Cancel", "withdraw.confirmation.continue": "Confirm", "withdrawal_request.completed": "Completed", "withdrawal_request.pending": "Pending", "zeal-dapp.connect-wallet.cta.primary.connecting": "Connecting...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Connect", "zeal-dapp.connect-wallet.cta.secondary": "Cancel", "zeal-dapp.connect-wallet.title": "Connect wallet to continue", "zealSmartWalletInfo.gas": "Pay gas with many tokens; use popular ERC20 tokens on supported chains to pay for network fees, not just native tokens", "zealSmartWalletInfo.recover": "No Seed Phrases; Recover using biometric passkey from your password manager, iCloud or Google account.", "zealSmartWalletInfo.selfCustodial": "Completely self-custodial; Passkey signatures are validated on-chain to minimise central dependencies.", "zealSmartWalletInfo.title": "About Zeal smart wallets", "zeal_a_rewards_already_claimed_error.title": "<PERSON><PERSON> already claimed", "zwidget.minimizedDisconnected.label": "Zeal Disconnected"}