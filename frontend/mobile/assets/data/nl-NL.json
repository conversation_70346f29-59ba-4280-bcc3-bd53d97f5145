{"Account.ListItem.details.label": "Details", "AddFromAddress.success": "Portemon<PERSON>", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{<PERSON>n wallets} one{{count} wallet} other{{count} wallets}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Geheime zin {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Maak nieuwe wallets aan vanuit een van je bestaande geheime zinnen", "AddFromExistingSecretPhrase.SelectPhrase.title": "Kies een geheime zin", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "Je geheime zin kan meerdere wallets beveiligen. <PERSON><PERSON> welke je wilt gebruiken.", "AddFromExistingSecretPhrase.WalletSelection.title": "Snel een wallet toevoegen", "AddFromExistingSecretPhrase.success": "Wallets toegevoegd aan <PERSON>", "AddFromHardwareWallet.subtitle": "Selecteer je hardware wallet om te verbinden met <PERSON><PERSON>", "AddFromHardwareWallet.title": "Hardware wallet", "AddFromNewSecretPhrase.WalletSelection.subtitle": "Selecteer de portemonnees die je wilt importeren", "AddFromNewSecretPhrase.WalletSelection.title": "Portemonnees importeren", "AddFromNewSecretPhrase.accounts": "Portemonnees", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "Een geheime zin werkt als een sleutelhanger voor miljoenen portemonnees, elk met een unieke privésleutel.{br}{br}Je kunt nu zoveel portemonnees importeren als je wilt, of er later meer toevoegen.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Geheime zin-portemonnees", "AddFromNewSecretPhrase.subtitle": "<PERSON><PERSON>r je geheime zin in, met woorden gescheiden door spaties", "AddFromNewSecretPhrase.success_secret_phrase_added": "Geheime zin toegevoegd 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Portemon<PERSON> toe<PERSON>d aan <PERSON>", "AddFromNewSecretPhrase.wallets": "Wallets", "AddFromPrivateKey.subtitle": "<PERSON><PERSON><PERSON> je privésleutel in", "AddFromPrivateKey.success": "Privésleutel toegevoegd 🎉", "AddFromPrivateKey.title": "<PERSON><PERSON><PERSON>", "AddFromPrivateKey.typeOrPaste": "Typ of plak hier", "AddFromSecretPhrase.importWallets": "{count,plural,=0{<PERSON><PERSON> wallets geselecteerd} one{Importeer wallet} other{Importeer {count} wallets}}", "AddFromTrezor.AccountSelection.title": "Trezor-portemonnees importeren", "AddFromTrezor.hwWalletTip.subtitle": "Een hardware-port<PERSON><PERSON> bevat mil<PERSON>enen portemon<PERSON> met verschillende adressen. Je kunt nu zoveel portemonnees importeren als je wilt, of er later meer toevoegen.", "AddFromTrezor.hwWalletTip.title": "Importeren vanuit hardware-portemonnees", "AddFromTrezor.importAccounts": "{count,plural,=0{<PERSON><PERSON> wallets geselecteerd} one{Importeer wallet} other{Importeer {count} wallets}}", "AddFromTrezor.success": "Portemon<PERSON> toe<PERSON>d aan <PERSON>", "ApprovalSpenderTypeCheck.failed.subtitle": "Waarschijnlijk een scam: de partij hoort een contract te zijn", "ApprovalSpenderTypeCheck.failed.title": "Partij is een wallet, geen contract", "ApprovalSpenderTypeCheck.passed.subtitle": "Je geeft meestal toestemming aan contracten", "ApprovalSpenderTypeCheck.passed.title": "Partij is een smart contract", "BestReturns.subtitle": "Deze aanbieder geeft het hoogste rendement, inclusief alle kosten.", "BestReturnsPopup.title": "Beste rendement", "BlacklistCheck.Failed.subtitle": "Kwaadaardige meldingen door <source></source>", "BlacklistCheck.Failed.title": "Site staat op een zwarte lijst", "BlacklistCheck.Passed.subtitle": "<PERSON>n k<PERSON>ardige meldingen door <source></source>", "BlacklistCheck.Passed.title": "Site staat niet op een zwarte lijst", "BlacklistCheck.failed.statusButton.label": "Site is gerapporteerd", "BridgeRoute.slippage": "Slippage {slippage}", "BridgeRoute.title": "Bridge provider", "CheckConfirmation.InProgress": "Bezig...", "CheckConfirmation.success.splash": "Voltooid", "ChooseImportOrCreateSecretPhrase.subtitle": "Importeer een geheime zin of maak een nieuwe aan", "ChooseImportOrCreateSecretPhrase.title": "Geheime zin toevoegen", "ConfirmTransaction.Simuation.Skeleton.title": "Veiligheidscontroles uitvoeren…", "ConnectionSafetyCheckResult.passed": "Veiligheidscontrole geslaagd", "ContactGnosisPaysupport": "Contacteer Gnosis Pay", "CopyKeyButton.copied": "Gekopieerd", "CopyKeyButton.copyYourKey": "<PERSON><PERSON><PERSON> je sleutel", "CopyKeyButton.copyYourPhrase": "<PERSON><PERSON><PERSON> je frase", "DAppVerificationCheck.Failed.subtitle": "Site staat niet vermeld op <source></source>", "DAppVerificationCheck.Failed.title": "Site niet gevonden in app-registers", "DAppVerificationCheck.Passed.subtitle": "Site staat vermeld op <source></source>", "DAppVerificationCheck.Passed.title": "Site staat in app-registers", "DAppVerificationCheck.failed.statusButton.label": "Site niet gevonden in app-registers", "ERC20.tokens.emptyState": "We hebben geen tokens gevonden", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Priority Fee", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Laatste {period}: tussen {from} en {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Basiskosten: {baseFee} • Veiligheidsbuffer: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Kon huidige basis<PERSON>ten niet ophalen", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Hoger dan volgende Nonce. Loopt vast", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "<PERSON><PERSON> kan niet lager zijn dan de huidige", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "We konden de Priority Fee niet berekenen", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Kon huidige max. kosten niet ophalen", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "<PERSON>. kosten", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Kan vast<PERSON>en tot netwerkkosten dalen", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "<PERSON><PERSON>", "EditFeeModal.Custom.gasLimit.title": "Gaslimiet {gasLimit}", "EditFeeModal.Custom.title": "Geavanceerde instellingen", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Loopt vast tot basiskosten dalen", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Lage vergoeding. Loopt mogelijk vast", "EditFeeModal.EditGasLimit.estimatedGas": "Geschat gas: {estimated} • Veiligheidsbuffer: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Lager dan geschatte limiet. Transactie mislukt", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Lager dan voorgestelde limiet. Transactie kan mislukken", "EditFeeModal.EditGasLimit.subtitle": "Stel de maximale hoeveelheid gas in voor deze transactie. Je transactie mislukt als de limiet te laag is.", "EditFeeModal.EditGasLimit.title": "Gaslimiet wijzigen", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Lager dan minimale gaslimiet: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "Hoger dan volgende nonce. Loopt vast", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "<PERSON><PERSON> kan niet lager zijn dan de huidige", "EditFeeModal.EditNonce.subtitle": "Je transactie loopt vast als je een andere dan de volgende nonce instelt", "EditFeeModal.EditNonce.title": "<PERSON><PERSON> wi<PERSON>en", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "Nodig: {amount} om te verzenden", "EditFeeModal.Header.Time.unknown": "Tij<PERSON> onbekend", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Max. {fee}", "EditFeeModal.Header.fee.unknown": "<PERSON><PERSON>", "EditFeeModal.Header.subsequent_failed": "Schattingen zijn mogelijk verouderd, laatste update mislukt", "EditFeeModal.Layout.Header.ariaLabel": "<PERSON><PERSON>ge kosten", "EditFeeModal.MaxFee.subtitle": "De max. kosten is het meeste dat je betaalt voor een transactie, maar meestal betaal je de voorspelde kosten. Deze extra buffer helpt je transactie door te voeren, zelfs als het netwerk vertraagt of duurder wordt.", "EditFeeModal.MaxFee.title": "Maximale netwerkkosten", "EditFeeModal.SelectPreset.Time.unknown": "Tij<PERSON> onbekend", "EditFeeModal.SelectPreset.ariaLabel": "Kostenvoorkeur selecteren", "EditFeeModal.SelectPreset.fast": "Snel", "EditFeeModal.SelectPreset.normal": "Normaal", "EditFeeModal.SelectPreset.slow": "<PERSON><PERSON><PERSON>", "EditFeeModal.ariaLabel": "Netwerkkosten wijzigen", "FailedSimulation.Confirmation.Item.subtitle": "Er was een interne fout", "FailedSimulation.Confirmation.Item.title": "Kon transactie niet simuleren", "FailedSimulation.Confirmation.subtitle": "Weet je zeker dat je wilt doorgaan?", "FailedSimulation.Confirmation.title": "<PERSON> ondertekent blind", "FailedSimulation.Title": "Simulatiefout", "FailedSimulation.footer.subtitle": "Er was een interne fout", "FailedSimulation.footer.title": "Kon transactie niet simuleren", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Nodig {amount} om transactie te versturen", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Verwerking kan lang duren", "FeeForecastWidget.networkFee": "Netwerkkosten", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Kon netwerkkosten niet berekenen", "FeeForecastWidget.subsequentFailed.message": "Schattingen zijn mogelijk verou<PERSON>d, vern<PERSON><PERSON> mislukt", "FeeForecastWidget.unknownDuration": "Onbekend", "FeeForecastWidget.unknownFee": "Onbekend", "GasCurrencySelector.balance": "Saldo: {balance}", "GasCurrencySelector.networkFee": "Netwerkkosten", "GasCurrencySelector.payNetworkFeesUsing": "Netwerkkosten betalen met", "GasCurrencySelector.removeDefaultGasToken.description": "<PERSON>al kosten van het grootste saldo", "GasCurrencySelector.removeDefaultGasToken.title": "Automatische kostenafhandeling", "GasCurrencySelector.save": "Opsla<PERSON>", "GoogleDriveBackup.BeforeYouBegin.first_point": "Als ik mijn <PERSON>-wacht<PERSON><PERSON> vergeet, ben ik mijn tegoeden voorgoed kwijt", "GoogleDriveBackup.BeforeYouBegin.second_point": "Als ik de toegang tot mijn Google Drive verlies of mijn herstelbestand wijzig, ben ik mijn tegoeden voorgoed kwijt", "GoogleDriveBackup.BeforeYouBegin.subtitle": "<PERSON><PERSON> en accepteer de volgende punten over privé beheer:", "GoogleDriveBackup.BeforeYouBegin.third_point": "<PERSON>eal kan me niet helpen mijn <PERSON>-wachtwoord of mijn toegang tot Google Drive te herstellen", "GoogleDriveBackup.BeforeYouBegin.title": "Voor<PERSON>t je begint", "GoogleDriveBackup.loader.subtitle": "<PERSON><PERSON> het verz<PERSON><PERSON> in Google Drive goed om je herstelbestand te uploaden", "GoogleDriveBackup.loader.title": "Wachten op goedkeuring...", "GoogleDriveBackup.success": "Back-up ges<PERSON><PERSON>d 🎉", "MonitorOffRamp.overServiceTime": "De meeste overboekingen worden binnen  voltooid {estimated_time}, maar soms duren ze langer door extra controles. Dit is normaal en je geld is veilig tijdens deze controles.{br}{br}Als de transactie niet binnen  is voltooid {support_soft_deadline}, neem dan {contact_support}", "MonitorOnRamp.contactSupport": "Neem contact op met support", "MonitorOnRamp.from": "<PERSON>", "MonitorOnRamp.fundsReceived": "Geld ontvangen", "MonitorOnRamp.overServiceTime": "De meeste overboekingen zijn voltooid binnen {estimated_time}, maar soms duren ze langer door extra controles. Dit is normaal en je geld is veilig tijdens deze controles.{br}{br}Als de transactie niet is voltooid binnen {support_soft_deadline}, neem dan {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Verzenden naar je wallet", "MonitorOnRamp.to": "<PERSON>ar", "MonitorOnRamp.waitingForTransfer": "Wachten op jouw overboeking", "NftCollectionCheck.failed.subtitle": "Collectie is niet geverifieerd op <source></source>", "NftCollectionCheck.failed.title": "Collectie is niet geverifieerd", "NftCollectionCheck.passed.subtitle": "Collectie is geverifieerd op <source></source>", "NftCollectionCheck.passed.title": "Collectie is geverifieerd", "NftCollectionInfo.entireCollection": "<PERSON><PERSON> collectie", "NoSigningKeyStore.createAccount": "Account aan<PERSON>ken", "NonceRangeError.biggerThanCurrent.message": "Transactie loopt vast", "NonceRangeError.lessThanCurrent.message": "Transactie mislukt", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "<PERSON>ce is hoger dan de huidige. Verlaag de nonce om vastlopen te voorkomen.", "NonceRangeErrorPopup.biggerThanCurrent.title": "Transactie loopt vast", "P2pReceiverTypeCheck.failed.subtitle": "<PERSON>uur je naar het juiste adres?", "P2pReceiverTypeCheck.failed.title": "Ontvanger is smart contract, geen wallet", "P2pReceiverTypeCheck.passed.subtitle": "Meestal stuur je activa naar andere wallets", "P2pReceiverTypeCheck.passed.title": "Ontvanger is een wallet", "PasswordCheck.title": "<PERSON><PERSON><PERSON> wacht<PERSON> in", "PasswordChecker.subtitle": "<PERSON><PERSON>r je wachtwoord in ter verificatie.", "PermitExpirationCheck.failed.subtitle": "Houd het kort en alleen zo lang als nodig", "PermitExpirationCheck.failed.title": "<PERSON> ve<PERSON>", "PermitExpirationCheck.passed.subtitle": "Hoelang een app je tokens kan gebruiken", "PermitExpirationCheck.passed.title": "Vervaltijd is niet te lang", "PrivateKeyValidationError.moreThanMaximumWords": "<PERSON>. {count} woorden", "PrivateKeyValidationError.notValidPrivateKey": "Dit is geen geldige privésleutel", "PrivateKeyValidationError.secretPhraseIsInvalid": "Geheime zin is niet geldig", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Woord #{index} is verkeerd gespeld of ongeldig", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} woord} other{{count} woorden}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Geheime zinnen en privésleutels worden versleuteld en verlaten dit apparaat nooit", "SecretPhraseReveal.header": "<PERSON><PERSON><PERSON><PERSON><PERSON> je geheime zin op", "SecretPhraseReveal.hint": "<PERSON><PERSON> je zin met ni<PERSON><PERSON>. <PERSON><PERSON><PERSON> hem <PERSON> en offline", "SecretPhraseReveal.skip.subtitle": "Je kunt dit later doen, maar als je dit apparaat verliest voordat je de zin hebt opgeschreven, verlies je alle tegoeden in deze portemonnee", "SecretPhraseReveal.skip.takeTheRisk": "Risico nemen", "SecretPhraseReveal.skip.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>?", "SecretPhraseReveal.skip.writeDown": "Opschrij<PERSON>", "SecretPhraseReveal.skipForNow": "<PERSON><PERSON>", "SecretPhraseReveal.subheader": "<PERSON><PERSON><PERSON><PERSON><PERSON> de zin op en bewaar hem veilig offline. Daarna vragen we je hem te verifiëren.", "SecretPhraseReveal.verify": "Verifiëren", "SelectCurrency.tokens": "Tokens", "SelectCurrency.tokens.emptyState": "We hebben geen tokens gevonden", "SelectRoute.slippage": "Slippage {slippage}", "SelectRoutes.emptyState": "Geen routes gevonden voor deze swap", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Verbind portemon<PERSON>", "SendERC20.labelAddress.inputPlaceholder": "Wallet-label", "SendERC20.labelAddress.subtitle": "Label deze wallet om hem later te vinden.", "SendERC20.labelAddress.title": "<PERSON>f deze wallet een label", "SendERC20.send_to": "Verstuur naar", "SendERC20.tokens": "Tokens", "SendOrReceive.bankTransfer.primaryText": "Bankoverboeking", "SendOrReceive.bankTransfer.shortText": "<PERSON><PERSON><PERSON>, direct geld storten en opnemen", "SendOrReceive.bridge.primaryText": "Bridge", "SendOrReceive.bridge.shortText": "Tokens overboeken tussen netwerken", "SendOrReceive.receive.primaryText": "Ontvangen", "SendOrReceive.receive.shortText": "Ontvang tokens of collectibles", "SendOrReceive.send.primaryText": "Verzenden", "SendOrReceive.send.shortText": "Verzend tokens naar elk adres", "SendOrReceive.swap.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.swap.shortText": "Wissel tussen tokens", "SendSafeTransaction.Confirm.loading": "Veiligheidscontroles uitvoeren…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Versleutel herstelbestand met wachtwoord", "SetupRecoveryKit.google.subtitle": "Gesynchroniseerd {date}", "SetupRecoveryKit.google.title": "Google Drive-back-up", "SetupRecoveryKit.subtitle": "Je hebt minstens é<PERSON> nodig als je Zeal verwijdert of van apparaat wisselt", "SetupRecoveryKit.title": "Herstelkit instellen", "SetupRecoveryKit.writeDown.subtitle": "Geheime zin opschrijven", "SetupRecoveryKit.writeDown.title": "Hand<PERSON><PERSON> back-up", "Sign.CheckSafeDeployment.activate": "<PERSON><PERSON>", "Sign.CheckSafeDeployment.subtitle": "Voordat je kunt inloggen bij een app of een off-chain bericht kunt ondertekenen, moet je je apparaat op dit netwerk activeren. Dit gebeurt nadat je een Smart Wallet hebt geïnstalleerd of hersteld.", "Sign.CheckSafeDeployment.title": "Activeer apparaat op dit netwerk", "Sign.Simuation.Skeleton.title": "Veiligheidscontroles uitvoeren…", "SignMessageSafetyCheckResult.passed": "Veiligheidscontroles geslaagd", "SignMessageSafetyChecksPopup.title.permits": "Veiligheidscontroles Permit", "SimulationFailedConfirmation.subtitle": "We hebben deze transactie gesimuleerd en een probleem gevonden waardoor deze zal mislukken. Je kunt de transactie indienen, maar deze mislukt waarschijnlijk en je kunt je netwerkkosten kwijtraken.", "SimulationFailedConfirmation.title": "Transactie mislukt waarschijnlijk", "SimulationNotSupported.Title": "Simulatie niet {br}ondersteund op{br}{network}", "SimulationNotSupported.footer.subtitle": "Je kunt deze transactie nog steeds indienen", "SimulationNotSupported.footer.title": "Simulatie niet ondersteund", "SlippagePopup.custom": "Aangepast", "SlippagePopup.presetsHeader": "Swap slippage", "SlippagePopup.title": "Slippage-instellingen", "SmartContractBlacklistCheck.failed.subtitle": "Kwaadaardige meldingen door <source></source>", "SmartContractBlacklistCheck.failed.title": "Contract staat op een zwarte lijst", "SmartContractBlacklistCheck.passed.subtitle": "<PERSON>n k<PERSON>ardige meldingen door <source></source>", "SmartContractBlacklistCheck.passed.title": "Contract staat niet op een zwarte lijst", "SuspiciousCharactersCheck.Failed.subtitle": "Dit is een veelge<PERSON><PERSON><PERSON>te phishing-tactiek", "SuspiciousCharactersCheck.Failed.title": "We controleren op phishing-patronen", "SuspiciousCharactersCheck.Passed.subtitle": "We controleren op phishing-pogingen", "SuspiciousCharactersCheck.Passed.title": "Adres bevat geen ongebruikelijke tekens", "SuspiciousCharactersCheck.failed.statusButton.label": "Adres bevat ongebruikelijke tekens ", "TokenVerificationCheck.failed.subtitle": "Token staat niet op <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} is niet geverifieerd door CoinGecko", "TokenVerificationCheck.passed.subtitle": "Token staat op <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} is geverifieerd door CoinGecko", "TopupDapp.MonitorTransaction.success.splash": "Voltooid", "TransactionSafetyCheckResult.passed": "Veiligheidscontroles geslaagd", "TransactionSimulationCheck.failed.subtitle": "Fout: {errorMessage}", "TransactionSimulationCheck.failed.title": "Transactie mislukt waarschijnlijk", "TransactionSimulationCheck.passed.subtitle": "Simulatie uitgevoerd met <source></source>", "TransactionSimulationCheck.passed.title": "Transactievoorbeeld succesvol", "TrezorError.trezor_action_cancelled.action": "Sluiten", "TrezorError.trezor_action_cancelled.subtitle": "Je hebt de transactie op je hardware wallet afgewezen", "TrezorError.trezor_device_used_elsewhere.action": "Synchronisee<PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "<PERSON>org dat je alle andere sessies sluit en probeer je Trezor opnieuw te synchroniseren", "TrezorError.trezor_method_cancelled.action": "Synchronisee<PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "<PERSON><PERSON> toestemming om wallets naar Zeal te exporteren", "TrezorError.trezor_permissions_not_granted.action": "Synchronisee<PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "<PERSON><PERSON> toestemming om alle wallets te zien", "TrezorError.trezor_pin_cancelled.action": "Synchronisee<PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "<PERSON><PERSON> gean<PERSON> op het apparaat", "TrezorError.trezor_popup_closed.action": "Synchronisee<PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "Het Trezor-dialoogvenster sloot onverwacht", "TrxLikelyToFail.lessThanEstimatedGas.message": "Transactie mislukt", "TrxLikelyToFail.lessThanMinimumGas.message": "Transactie mislukt", "TrxLikelyToFail.lessThanSuggestedGas.message": "Mislukt waarschijnlijk", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "Gaslimiet van transactie is te laag. Verhoog de gaslimiet naar de voorgestelde limiet om mislukken te voorkomen.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "Transactie mislukt waarschijnlijk", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Gaslimiet is lager dan geschat gas. Verhoog de gaslimiet naar de voorgestelde limiet.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "Transactie mislukt", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "<PERSON><PERSON> basis<PERSON> is lager dan de huidige basiskosten. Verhoog de max. basiskosten om te voorkomen dat de transactie vastloopt.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "Transactie loopt vast", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "Max. transactiekosten is te laag. Verhoog de max. kosten om te voorkomen dat de transactie vastloopt.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "Transactie loopt vast", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Prioriteitskosten is lager dan aanbevolen. Verhoog de prioriteitskosten om de transactie te versnellen.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "Transactie kan lang duren", "UnsupportedMobileNetworkLayout.gotIt": "Oké!", "UnsupportedMobileNetworkLayout.subtitle": "Je kunt nog geen transacties uitvoeren of berichten ondertekenen op het netwerk met ID {networkHexId} met de mobiele versie van Zeal{br}{br}Gebruik de browserextensie om transacties op dit netwerk uit te voeren. We werken er hard aan om dit netwerk ook mobiel te ondersteunen 🚀", "UnsupportedMobileNetworkLayout.title": "Netwerk niet ondersteund op mobiele versie van Zeal", "UnsupportedSafeNetworkLayout.subtitle": "Je kunt geen transacties uitvoeren of berichten ondertekenen op {network} met een Zeal Smart Wallet{br}{br}<PERSON><PERSON><PERSON> over naar een ondersteund netwerk of gebruik een Legacy wallet.", "UnsupportedSafeNetworkLayoutk.title": "Netwerk niet ondersteund voor Smart Wallet", "UserConfirmationPopup.goBack": "<PERSON><PERSON><PERSON>", "UserConfirmationPopup.submit": "Dien toch in", "ViewPrivateKey.header": "Privésleutel", "ViewPrivateKey.hint": "<PERSON><PERSON> je pri<PERSON><PERSON><PERSON><PERSON><PERSON> met ni<PERSON><PERSON>. <PERSON><PERSON><PERSON> hem <PERSON> en offline", "ViewPrivateKey.subheader.mobile": "Tik om je privésleutel te onthullen", "ViewPrivateKey.subheader.web": "<PERSON><PERSON> de muis erboven om je privésleutel te onthullen", "ViewPrivateKey.unblur.mobile": "Tik om te onthullen", "ViewPrivateKey.unblur.web": "<PERSON><PERSON> muis erboven", "ViewSecretPhrase.PasswordChecker.subtitle": "Voer je wachtwoord in om het herstelbestand te versleutelen. Je moet dit wachtwoord onthouden voor de toekomst.", "ViewSecretPhrase.done": "<PERSON><PERSON><PERSON>", "ViewSecretPhrase.header": "Geheime zin", "ViewSecretPhrase.hint": "<PERSON><PERSON> je zin met ni<PERSON><PERSON>. <PERSON><PERSON><PERSON> hem <PERSON> en offline", "ViewSecretPhrase.subheader.mobile": "Tik om je geheime zin te onthullen", "ViewSecretPhrase.subheader.web": "<PERSON><PERSON> de muis erboven om je geheime zin te onthullen", "ViewSecretPhrase.unblur.mobile": "Tik om te onthullen", "ViewSecretPhrase.unblur.web": "<PERSON><PERSON> muis erboven", "account-details.monerium": "Overboekingen worden uitgevoerd via Monerium, een geautoriseerde en gereguleerde EMI. <link>Meer informatie</link>", "account-details.unblock": "Overboekingen worden gedaan via Unblock, een geautoriseerde en geregistreerde aanbieder van wissel- en bewaardiensten. <link>Meer informatie</link>", "account-selector.empty-state": "<PERSON>n port<PERSON> gevonden", "account-top-up.select-currency.title": "Tokens", "account.accounts_not_found": "We konden geen portemonnees vinden", "account.accounts_not_found_search_valid_address": "<PERSON><PERSON><PERSON> staat niet in je lijst", "account.add.create_new_secret_phrase": "Geheime zin aanmaken", "account.add.create_new_secret_phrase.subtext": "<PERSON><PERSON> nieuwe geheime zin van 12 woorden", "account.add.fromRecoveryKit.fileNotFound": "We konden je bestand niet vinden", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "<PERSON><PERSON><PERSON> opnieuw", "account.add.fromRecoveryKit.fileNotFound.explanation": "Check het account met de Zeal Backup-map.", "account.add.fromRecoveryKit.fileNotValid": "Herstelbestand is niet geldig", "account.add.fromRecoveryKit.fileNotValid.explanation": "Je bestand is niet het juiste type of is gewijzigd.", "account.add.import_secret_phrase": "Geheime zin importeren", "account.add.import_secret_phrase.subtext": "Gemaakt op Zeal, Metamask of andere", "account.add.select_type.add_hardware_wallet": "Hardware-port<PERSON><PERSON>", "account.add.select_type.existing_smart_wallet": "Bestaande Smart Wallet", "account.add.select_type.private_key": "Privésleutel", "account.add.select_type.seed_phrase": "Seed-zin", "account.add.select_type.title": "Portemonnee importeren", "account.add.select_type.zeal_recovery_file": "<PERSON><PERSON>d", "account.add.success.title": "Nieuwe wallet aangemaakt 🎉", "account.addLabel.header": "<PERSON><PERSON> je portemonnee een naam", "account.addLabel.labelError.labelAlreadyExist": "<PERSON><PERSON> bestaat al. <PERSON> een andere naam.", "account.addLabel.labelError.maxStringLengthExceeded": "Maximum aantal tekens bereikt", "account.add_active_wallet.primary_text": "<PERSON><PERSON><PERSON>", "account.add_active_wallet.short_text": "<PERSON><PERSON><PERSON><PERSON>, verbinden of importeren", "account.add_from_ledger.success": "Wallets toegevoegd aan <PERSON>", "account.add_tracked_wallet.primary_text": "Alleen-lezen port<PERSON>", "account.add_tracked_wallet.short_text": "Bekijk portfolio en activiteit", "account.button.unlabelled-wallet": "Portemonnee zonder label", "account.create_wallet": "<PERSON>et a<PERSON>", "account.label.edit.title": "<PERSON><PERSON> port<PERSON> bewerken", "account.recoveryKit.selectBackupFile.fileDate": "Aangemaakt {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Herstelbestand is niet geldig", "account.recoveryKit.selectBackupFile.subtitle": "Kies het herstelbestand om terug te zetten.", "account.recoveryKit.selectBackupFile.title": "Herstelbestand", "account.recoveryKit.success.recoveryFileFound": "Herstelbestand gevonden 🎉", "account.select_type_of_account.create_eoa.short": "<PERSON><PERSON> portemon<PERSON> voor experts", "account.select_type_of_account.create_eoa.title": "Seed-zin port<PERSON><PERSON> a<PERSON>", "account.select_type_of_account.create_safe_wallet.title": "Smart Wallet aanmaken", "account.select_type_of_account.existing_smart_wallet": "Bestaande Smart wallet", "account.select_type_of_account.hardware_wallet": "Hardware wallet", "account.select_type_of_account.header": "<PERSON><PERSON><PERSON>", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Privésleutel / Seed phrase", "account.select_type_of_account.read_only_wallet": "<PERSON><PERSON><PERSON>le<PERSON> port<PERSON>", "account.select_type_of_account.read_only_wallet.short": "Bekijk elke portfolio", "account.topup.title": "Geld toevo<PERSON>n aan <PERSON>", "account.view.error.refreshAssets": "<PERSON><PERSON><PERSON><PERSON>", "account.widget.refresh": "<PERSON><PERSON><PERSON><PERSON>", "account.widget.settings": "Instellingen", "accounts.view.copied-text": "Gekopieerd {formattedAddress}", "accounts.view.copiedAddress": "Gekopieerd {formattedAddress}", "action.accept": "Accept<PERSON><PERSON>", "action.accpet": "Accepteer", "action.allow": "<PERSON><PERSON><PERSON>", "action.back": "Terug", "action.cancel": "<PERSON><PERSON><PERSON>", "action.card-activation.title": "<PERSON><PERSON>", "action.claim": "Claimen", "action.close": "Sluiten", "action.complete-steps": "Afronden", "action.confirm": "Bevestig", "action.continue": "Doorgaan", "action.copy-address-understand": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> ad<PERSON>", "action.deposit": "<PERSON><PERSON><PERSON>", "action.done": "<PERSON><PERSON><PERSON>", "action.dontAllow": "<PERSON><PERSON>", "action.edit": "wij<PERSON>", "action.email-required": "Voer e-mailadres in", "action.enterPhoneNumber": "Voer telefoonnummer in", "action.expand": "Uitvouwen", "action.fix": "<PERSON><PERSON><PERSON>", "action.getStarted": "<PERSON><PERSON>", "action.got_it": "Begrepen", "action.hide": "Verbergen", "action.import": "Importeren", "action.import-keys": "Sleutels importeren", "action.importKeys": "Sleutels Importeren", "action.minimize": "Minimaliseren", "action.next": "Volgende", "action.ok": "OK", "action.reduceAmount": "Verlaag naar max", "action.refreshWebsite": "Website vernieuwen", "action.remove": "Verwijderen", "action.remove-account": "Account verwij<PERSON>en", "action.requestCode": "Code aanvragen", "action.resend_code": "Code opnieuw verzenden", "action.resend_code_with_time": "Opnieu<PERSON> verzenden {time}", "action.retry": "Opnieuw proberen", "action.reveal": "<PERSON><PERSON>", "action.save": "Opsla<PERSON>", "action.save_changes": "RPC opslaan", "action.search": "<PERSON><PERSON>", "action.seeAll": "Bekijk alles", "action.select": "Selecteer", "action.send": "Verstuur", "action.skip": "Overslaan", "action.submit": "Verzenden", "action.understood": "<PERSON>k begri<PERSON>p het", "action.update": "Update", "action.update-gnosis-pay-owner.complete": "Voltooid", "action.zeroAmount": "<PERSON><PERSON>r bedrag in", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Verzamelobjecten", "action_bar_title.tokens": "Tokens", "action_bar_title.transaction_request": "Transactieverzoek", "activate-monerium.loading": "Je persoonlijke account wordt ingesteld", "activate-monerium.success.title": "Monerium ingeschakeld", "activate-physical-card-widget.subtitle": "Levering kan 3 weken duren", "activate-physical-card-widget.title": "Fysieke kaart <PERSON>ren", "activate-smart-wallet.title": "<PERSON><PERSON><PERSON>", "active_and_tracked_wallets.title": "Zeal dekt al je kosten op {network}, zodat je gratis transacties kunt doen!", "activity.approval-amount.revoked": "Ingetrokken", "activity.approval-amount.unlimited": "Onbeperkt", "activity.approval.approved_for": "Goedgekeurd voor", "activity.approval.approved_for_with_target": "Goedgekeurd {approvedTo}", "activity.approval.revoked_for": "Ingetrokken voor", "activity.bank.serviceProvider": "Dienstverlener", "activity.bridge.serviceProvider": "Dienstverlener", "activity.cashback.period": "Cashbackperiode", "activity.filter.card": "<PERSON><PERSON>", "activity.rate": "Wisselkoers", "activity.receive.receivedFrom": "Ontvangen van", "activity.send.sendTo": "Verzonden naar", "activity.smartContract.unknown": "Onbekend contract", "activity.smartContract.usingContract": "Gebruikt contract", "activity.subtitle.pending_timer": "{timerString} In behandeling", "activity.title.arbitrary_smart_contract_interaction": "{function} op {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Onbekende contractinteractie", "activity.title.bridge.from": "Bridge van {token}", "activity.title.bridge.to": "Bridge naar {token}", "activity.title.buy": "Ko<PERSON> {asset}", "activity.title.card_owners_updated": "Kaarthouders bijgewerkt", "activity.title.card_spend_limit_updated": "Bestedingslimiet kaart ingesteld", "activity.title.cashback_deposit": "Storting naar <PERSON>back", "activity.title.cashback_reward": "Cashback beloning", "activity.title.cashback_withdraw": "Opname uit Cashback", "activity.title.claimed_reward": "Beloning geclaimd", "activity.title.deployed_smart_wallet_gnosis": "Account a<PERSON><PERSON><PERSON><PERSON>", "activity.title.deposit_from_bank": "Storting vanaf bank", "activity.title.deposit_into_card": "Stort<PERSON> op kaart", "activity.title.deposit_into_earn": "Storting naar {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} op {smartContract}", "activity.title.from": "<PERSON> {sender}", "activity.title.pendidng_areward_claim": "Beloning claimen", "activity.title.pendidng_breward_claim": "Beloning claimen", "activity.title.recharge_disabledh": "Automatisch opwaarderen kaart uitgeschakeld", "activity.title.recharge_set": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ing<PERSON>", "activity.title.recovered_smart_wallet_gnosis": "Nieuw apparaat geïnstalleerd", "activity.title.send_pending": "Naar {receiver}", "activity.title.send_to_bank": "Naar bank", "activity.title.swap": "<PERSON><PERSON> {token}", "activity.title.to": "Naar {receiver}", "activity.title.withdraw_from_card": "<PERSON><PERSON> van <PERSON>", "activity.title.withdraw_from_earn": "Opname van {earn}", "activity.transaction.networkFees": "Netwerkkosten", "activity.transaction.state": "Voltooide transactie", "activity.transaction.state.completed": "Voltooide transactie", "activity.transaction.state.failed": "Mislukte transactie", "add-account.section.import.header": "Importeren", "add-another-card-owner": "Nog een ka<PERSON>ige<PERSON> toe<PERSON>n", "add-another-card-owner.Recommended.footnote": "Voeg je Zeal wallet toe als extra eigenaar van je Gnosis Pay-kaart", "add-another-card-owner.Recommended.primaryText": "<PERSON><PERSON><PERSON> toe aan <PERSON>", "add-another-card-owner.recommended": "Aanbevolen", "add-owner.confirmation.subtitle": "Uit veiligheid duurt het 3 minuten om de instellingen te wijzigen. Je kaart wordt tijdelijk geblokkeerd en betalingen zijn niet mogelijk.", "add-owner.confirmation.title": "<PERSON> kaart is 3 min. geblokkeerd tijdens de update", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Kan wallet niet toevoegen, is al in gebruik", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "<PERSON><PERSON>r een andere wallet", "add.account.backup.decrypt.success": "<PERSON><PERSON><PERSON>teld", "add.account.backup.password.passwordIncorrectMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> is onjuist", "add.account.backup.password.subtitle": "<PERSON><PERSON>r het wachtwoord in dat je gebruikte om je herstelbestand te versleutelen", "add.account.backup.password.title": "<PERSON><PERSON><PERSON> wacht<PERSON> in", "add.account.google.login.subtitle": "<PERSON><PERSON> het verzoek op Google Drive goed om je herstelbestand te synchroniseren", "add.account.google.login.title": "Wachten op goedkeuring...", "add.readonly.already_added": "Wallet al toegevoegd", "add.readonly.continue": "Doorgaan", "add.readonly.empty": "<PERSON><PERSON><PERSON> een adres of ENS in", "addBankRecipient.title": "Bankontvanger toe<PERSON>n", "add_funds.deposit_from_bank_account": "<PERSON><PERSON><PERSON> vanaf bankrekening", "add_funds.from_another_wallet": "Vanaf een andere wallet", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "<PERSON><PERSON><PERSON><PERSON> met top-up dApp", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Verbind een wallet met de Zeal top-up dApp en stuur snel geld naar je wallet", "add_funds.from_crypto_wallet.header": "Vanaf een andere wallet", "add_funds.from_crypto_wallet.header.show_wallet_address": "Toon je wallet<PERSON><PERSON>", "add_funds.from_exchange.header": "Versturen vanaf een exchange", "add_funds.from_exchange.header.copy_wallet_address": "<PERSON><PERSON><PERSON> je <PERSON>-adres", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance, etc.", "add_funds.from_exchange.header.open_exchange": "Open de app of site van je exchange", "add_funds.from_exchange.header.selected_token": "Vers<PERSON>ur {token} naar <PERSON>", "add_funds.from_exchange.header.selected_token.subtitle": "Op {network}", "add_funds.from_exchange.header.send_selected_token": "Verstuur ondersteunde token", "add_funds.from_exchange.header.send_selected_token.subtitle": "Selecteer ondersteunde token & netwerk", "add_funds.import_wallet": "Bestaande crypto wallet importeren", "add_funds.title": "Geld toevoegen aan je account", "add_funds.transfer_from_exchange": "Overboeken vanaf een exchange", "address.add.header": "Bekijk je wallet in Zeal{br}in alleen-lezen modus", "address.add.subheader": "<PERSON><PERSON><PERSON> je adres of ENS in om je assets op alle EVM-netwerken op één plek te zien. Je kunt later meer wallets aanmaken of importeren.", "address_book.change_account.bank_transfers.header": "Bankontvangers", "address_book.change_account.bank_transfers.primary": "Bankontvanger", "address_book.change_account.cta": "Wallet volgen", "address_book.change_account.search_placeholder": "<PERSON><PERSON> of zoeken", "address_book.change_account.tracked_header": "Alleen-lezen port<PERSON>", "address_book.change_account.wallets_header": "<PERSON><PERSON><PERSON>", "app-association-check-failed.modal.cta": "<PERSON><PERSON><PERSON> opnieuw", "app-association-check-failed.modal.subtitle": "Probeer het opnieuw. Verbindingsproblemen veroorzaken vertraging bij het ophalen van je Passkeys. Als het pro<PERSON><PERSON> a<PERSON>, herstart <PERSON> en probeer het nogmaals.", "app-association-check-failed.modal.subtitle.creation": "Probeer het opnieuw. Verbindingsproblemen veroorzaken vertraging bij het aanmaken van je Passkey. Als het probleem aan<PERSON>, herstart Z<PERSON> en probeer het nogmaals.", "app-association-check-failed.modal.title.creation": "Je apparaat kon geen passkey aanmaken", "app-association-check-failed.modal.title.signing": "Je apparaat kon geen passkeys laden", "app.app_protocol_group.borrowed_tokens": "Geleende tokens", "app.app_protocol_group.claimable_amount": "Claimbaar bedrag", "app.app_protocol_group.health_rate": "Gezondheidsscore", "app.app_protocol_group.lending": "<PERSON><PERSON>", "app.app_protocol_group.locked_tokens": "Vastgezette tokens", "app.app_protocol_group.nfts": "Verzamelobjecten", "app.app_protocol_group.reward_tokens": "Beloningstokens", "app.app_protocol_group.supplied_tokens": "Ingelegde tokens", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Vesting token", "app.appsGroupHeader.discoverMore": "<PERSON><PERSON><PERSON> meer", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "<PERSON><PERSON> of voer URL in", "app.error-banner.cory": "<PERSON><PERSON><PERSON> f<PERSON>", "app.error-banner.retry": "Opnieuw proberen", "app.list_item.rewards": "Beloningen {value}", "app.position_details.health_rate.description": "De health rate wordt berekend door het bedrag van je lening te delen door de waarde van je onderpand.", "app.position_details.health_rate.title": "Wat is de health rate?", "approval.edit-limit.label": "Bestedingslimiet bewerken", "approval.permit_info": "Informatie over permit", "approval.spend-limit.edit-modal.cancel": "<PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.limit-label": "Bestedingslimiet", "approval.spend-limit.edit-modal.max-limit-error": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hoge limiet", "approval.spend-limit.edit-modal.revert": "Wijzigingen ongedaan maken", "approval.spend-limit.edit-modal.set-to-unlimited": "Instellen op Onbeperkt", "approval.spend-limit.edit-modal.submit": "Wijzigingen opslaan", "approval.spend-limit.edit-modal.title": "Toestemmingen bewerken", "approval.spend_limit_info": "Wat is een bestedingslimiet?", "approval.what_are_approvals": "Wat zijn goed<PERSON>uringen?", "apps_list.page.emptyState": "<PERSON>n actieve apps", "backpace.removeLastDigit": "Verwijder laatste cijfer", "backup-banner.backup_now": "Back-up maken", "backup-banner.risk_losing_funds": "Maak nu back-up, riskeer geen geldverlies.", "backup-banner.title": "<PERSON><PERSON> back-up van <PERSON><PERSON>", "backupRecoverySmartWallet.noExportPrivateKeys": "Automatische back-up: je <PERSON> wordt opgeslagen als passkey. Geen geheime zin of privésleutel nodig.", "backupRecoverySmartWallet.safeContracts": "Beveiliging met meerdere sleutels: Zeal-wallets draaien op Safe-contracten, zodat meerdere apparaten een transactie kunnen goedkeuren. Geen 'single point of failure'.", "backupRecoverySmartWallet.security": "Meerdere apparaten: je kunt je wallet op meerdere apparaten g<PERSON><PERSON><PERSON><PERSON> met de passkey. Elk apparaat krijgt zijn eigen privésleutel.", "backupRecoverySmartWallet.showLocalPrivateKey": "Expertmodus: je kunt de privésleutel van dit apparaat exporteren, geb<PERSON><PERSON>n in een andere wallet en verbinden via <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Toon privésleutel</Key>", "backupRecoverySmartWallet.storingKeys": "Gesynchroniseerd in de cloud: de passkey wordt veilig opgeslagen in iCloud, Google Wachtwoordmanager of je wachtwoordmanager.", "backupRecoverySmartWallet.title": "Back-up & herstel voor Smart Wallet", "balance-change.card.titile": "<PERSON><PERSON>", "balanceChange.pending": "In behandeling", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "<PERSON><PERSON>", "bank-transfer.change-deposit-receiver.subtitle": "Deze wallet ontvangt alle bankstortingen", "bank-transfer.change-deposit-receiver.title": "Stel ontvangende wallet in", "bank-transfer.change-owner.subtitle": "Deze wallet wordt gebruikt om in te loggen en je bankrekening te herstellen", "bank-transfer.change-owner.title": "Stel accounteigenaar in", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Alle bankstortingen die je naar <PERSON> stuurt, worden door deze wallet ontvangen.", "bank-transfer.configrm-change-deposit-receiver.title": "Wijzig ontvangende wallet", "bank-transfer.configrm-change-owner.subtitle": "Weet je zeker dat je de accounteigenaar wilt wijzigen? Deze wallet wordt gebruikt om in te loggen en je bankrekening te herstellen.", "bank-transfer.configrm-change-owner.title": "Wijzig accounteigenaar", "bank-transfer.deposit.widget.status.complete": "Voltooid", "bank-transfer.deposit.widget.status.funds_received": "Geld ontvangen", "bank-transfer.deposit.widget.status.sending_to_wallet": "Verzenden naar wallet", "bank-transfer.deposit.widget.status.transfer-on-hold": "Overboeking in de wacht", "bank-transfer.deposit.widget.status.transfer-received": "Verzenden naar wallet", "bank-transfer.deposit.widget.subtitle": "{from} naar {to}", "bank-transfer.deposit.widget.title": "Storting", "bank-transfer.intro.bulletlist.point_1": "Instellen met <PERSON>", "bank-transfer.intro.bulletlist.point_2": "Wissel EUR/GBP en 10+ tokens", "bank-transfer.intro.bulletlist.point_3": "0% kosten tot $5k p/m, daarna 0,2%", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Verzenden naar bank", "bank-transfer.withdrawal.widget.status.in-progress": "Overboeking bezig", "bank-transfer.withdrawal.widget.status.on-hold": "Overboeking in de wacht", "bank-transfer.withdrawal.widget.status.success": "Voltooid", "bank-transfer.withdrawal.widget.subtitle": "{from} naar {to}", "bank-transfer.withdrawal.widget.title": "Opname", "bank-transfers.bank-account-actions.remove-this-account": "Verwijder dit account", "bank-transfers.bank-account-actions.switch-to-this-account": "<PERSON><PERSON><PERSON> over naar dit account", "bank-transfers.deposit.fees-for-less-than-5k": "Kosten voor $5k of minder", "bank-transfers.deposit.fees-for-more-than-5k": "Kosten voor meer dan $5k", "bank-transfers.set-receiving-bank.title": "Stel ontvangende bank in", "bank-transfers.settings.account_owner": "Accounteigenaar", "bank-transfers.settings.receiver_of_bank_deposits": "Ontvanger van bankstortingen", "bank-transfers.settings.receiver_of_withdrawals": "Ontvan<PERSON> van opnames", "bank-transfers.settings.registered_email": "Geregistreerd e-mailadres", "bank-transfers.settings.title": "Instellingen voor bankoverboekingen", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} rekening", "bank-transfers.setup.bank-account": "Bankrekening", "bankTransfer.withdraw.max_loading": "Max: {amount}", "bank_details_do_not_match.got_it": "<PERSON><PERSON>", "bank_details_do_not_match.subtitle": "De sorteercode en het rekeningnummer komen niet overeen. Controleer of de gegevens juist zijn ingevoerd en probeer het opnieuw.", "bank_details_do_not_match.title": "Bankgegevens komen niet overeen", "bank_tranfsers.select_country_of_residence.country_not_supported": "Sorry, bankoverboekingen worden niet ondersteund in {country} nog", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Open je bankieren app", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Stuur {fiatCurrencyCode} naar je rekening", "bank_transfer.deposit.header": "{fullName}''s per<PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON><PERSON>s", "bank_transfer.kyc_status_widget.subtitle": "Bankoverboekingen", "bank_transfer.kyc_status_widget.title": "Identiteit verifiëren", "bank_transfer.personal_details.date_of_birth": "Geboortedatum", "bank_transfer.personal_details.date_of_birth.invalid_format": "Ongeldige datum", "bank_transfer.personal_details.date_of_birth.too_young": "Je moet minimaal 18 jaar oud zijn", "bank_transfer.personal_details.first_name": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.personal_details.last_name": "Achternaam", "bank_transfer.personal_details.title": "<PERSON> gegevens", "bank_transfer.reference.label": "Refer<PERSON>ie (optioneel)", "bank_transfer.reference_message": "Verzonden via Zeal", "bank_transfer.residence_details.address": "Je adres", "bank_transfer.residence_details.city": "Woonplaats", "bank_transfer.residence_details.country_of_residence": "<PERSON> van verblijf", "bank_transfer.residence_details.country_placeholder": "Land", "bank_transfer.residence_details.postcode": "Postcode", "bank_transfer.residence_details.street": "Straat", "bank_transfer.residence_details.your_residence": "<PERSON>", "bank_transfers.choose-wallet.continue": "Doorgaan", "bank_transfers.choose-wallet.test": "Wallet toevoegen", "bank_transfers.choose-wallet.warning.subtitle": "Je kunt maar één wallet tegelijk koppelen. Je kunt de gekoppelde wallet niet wijzigen.", "bank_transfers.choose-wallet.warning.title": "<PERSON><PERSON> je <PERSON> zorgvuldig", "bank_transfers.choose_wallet.subtitle": "Kies wallet voor directe overboekingen. ", "bank_transfers.choose_wallet.title": "<PERSON><PERSON> wallet", "bank_transfers.continue": "Doorgaan", "bank_transfers.currency_is_currently_not_supported": "Doorgaan", "bank_transfers.deposit-header": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.account-name": "<PERSON><PERSON>", "bank_transfers.deposit.account-number-copied": "Rekeningnummer gekopieerd", "bank_transfers.deposit.amount-input": "Te storten bedrag", "bank_transfers.deposit.amount-output": "Te ontvangen bedrag", "bank_transfers.deposit.amount-output.error": "fout", "bank_transfers.deposit.buttet-point.receive-crypto": "Ontvang {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "Doorgaan", "bank_transfers.deposit.currency-not-supported.subtitle": "Bankstortingen vanuit {code} zijn tot nader order uitgeschakeld.", "bank_transfers.deposit.currency-not-supported.title": "{code} stortingen momenteel niet ondersteund", "bank_transfers.deposit.default-token.balance": "Saldo {amount}", "bank_transfers.deposit.deposit-header": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.enter_amount": "<PERSON><PERSON>r bedrag in", "bank_transfers.deposit.iban-copied": "IBAN gekopieerd", "bank_transfers.deposit.increase-amount": "Minimale overboeking is {limit}", "bank_transfers.deposit.loading": "Laden", "bank_transfers.deposit.max-limit-reached": "Maximale overboekingslimiet bereikt", "bank_transfers.deposit.modal.kyc.button-text": "Starten", "bank_transfers.deposit.modal.kyc.text": "Om je identiteit te verifiëren, hebben we enkele persoonlijke gegevens en documenten nodig. Het indienen duurt meestal maar een paar minuten.", "bank_transfers.deposit.modal.kyc.title": "Verifieer je identiteit voor hogere limieten", "bank_transfers.deposit.reduce_amount": "Verlaag bedrag", "bank_transfers.deposit.show-account.account-number": "Rekeningnummer", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Sorteercode", "bank_transfers.deposit.sort-code-copied": "Sorteercode gekopieerd", "bank_transfers.deposit.withdraw-header": "Opnemen", "bank_transfers.failed_to_load_fee": "Onbekend", "bank_transfers.fees": "<PERSON><PERSON>", "bank_transfers.increase-amount": "Minimale overboeking is {limit}", "bank_transfers.insufficient-funds": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "bank_transfers.select_country_of_residence.title": "Waar woon je?", "bank_transfers.setup.cta": "Overboeken instellen", "bank_transfers.setup.enter-amount": "<PERSON><PERSON>r bedrag in", "bank_transfers.source_of_funds.form.business_income": "Inkomsten uit onderneming", "bank_transfers.source_of_funds.form.other": "<PERSON>", "bank_transfers.source_of_funds.form.pension": "<PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.salary": "<PERSON><PERSON>", "bank_transfers.source_of_funds.form.title": "<PERSON><PERSON><PERSON><PERSON> van je vermogen", "bank_transfers.source_of_funds_description.placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON> van je vermogen...", "bank_transfers.source_of_funds_description.title": "<PERSON><PERSON><PERSON> ons meer over de herko<PERSON>t van je vermogen", "bank_transfers.withdraw-header": "Opnemen", "bank_transfers.withdraw.amount-input": "Op te nemen bedrag", "bank_transfers.withdraw.max-limit-reached": "Maximale overboekingslimiet bereikt", "bank_transfers.withdrawal.verify-id": "Verlaag bedrag", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Maximale invoerlimiet overschreden", "banner.above_maximum_limit.maximum_limit_per_deposit": "Dit is de maximale limiet per storting", "banner.above_maximum_limit.subtitle": "Maximale invoerlimiet overschreden", "banner.above_maximum_limit.title": "V<PERSON><PERSON><PERSON> het bedrag naar {amount} of minder", "banner.above_maximum_limit.title.default": "Verlaag het bedrag", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Minimale invoerlimiet niet bereikt", "banner.below_minimum_limit.minimum_limit_for_token": "<PERSON><PERSON> is de minimale limiet voor deze token", "banner.below_minimum_limit.title": "Verhoog het bedrag naar {amount} of meer", "banner.below_minimum_limit.title.default": "Verhoog het bedrag", "breaard.in_porgress.info_popup.cta": "<PERSON>f uit en verdien {earn}", "breaard.in_porgress.info_popup.footnote": "Door Zeal en de Gnosis Pay-kaart te geb<PERSON>, ga je ak<PERSON><PERSON> met de voorwaarden van deze beloningscampagne.", "breaward.in_porgress.info_popup.bullet_point_1": "Geef {remaining} uit binnen de komende {time} om deze beloning te claimen.", "breaward.in_porgress.info_popup.bullet_point_2": "Alleen geldige Gnosis Pay-aankopen tellen mee voor je bestedingsbedrag.", "breaward.in_porgress.info_popup.bullet_point_3": "Nadat je je beloning hebt geclaimd, wordt deze naar je Zeal-account gestuurd.", "breaward.in_porgress.info_popup.header": "<PERSON><PERSON> {earn}, door {remaining}", "breward.celebration.for_spending": "<PERSON><PERSON> met je kaart", "breward.dc25-eligible-celebration.for_spending": "Je bent een van de e<PERSON> {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "Je was niet bij de eerste {limit} die uitgaven", "breward.expired_banner.earn_by_spending": "<PERSON><PERSON> {earn} met een uitgave van {amount}", "breward.expired_banner.reward_expired": "{earn} beloning verlopen", "breward.in_progress_banner.cta.title": "<PERSON>f uit en verdien {earn}", "breward.ready_to_claim.error.try_again": "<PERSON><PERSON><PERSON> opnieuw", "breward.ready_to_claim.error_title": "Beloning claimen mislukt", "breward.ready_to_claim.in_progress": "Beloning wordt geclaimd", "breward.ready_to_claim.youve_earned": "Je hebt {earn} verdiend!", "breward_already_claimed.title": "Beloning is al geclaimd. Als je de beloning niet hebt ontvangen, neem dan contact op met support", "breward_cannotbe_claimed.title": "Beloning kan nu niet worden geclaimd. <PERSON><PERSON>r het later opnieuw", "bridge.best_return": "Route met beste opbrengst", "bridge.best_serivce_time": "Route met snelste transactietijd", "bridge.check_status.complete": "Voltooid", "bridge.check_status.progress_text": "Bridging {from} naar {to}", "bridge.remove_topup": "Opwaardering verwijderen", "bridge.request_status.completed": "Voltooid", "bridge.request_status.pending": "In behandeling", "bridge.widget.completed": "Voltooid", "bridge.widget.currencies": "{from} naar {to}", "bridge_rote.widget.title": "Bridge", "browse.discover_more_apps": "Ontdek meer apps", "browse.google_search_term": "<PERSON><PERSON> \"{searchTerm}\"", "brward.celebration.you_earned": "Je hebt verdiend", "brward.expired_banner.subtitle": "Volgende keer beter", "brward.in_progress_banner.subtitle": "<PERSON><PERSON><PERSON><PERSON> over {expiredInFormatted}", "buy": "<PERSON><PERSON>", "buy.enter_amount": "<PERSON><PERSON>r bedrag in", "buy.loading": "Laden...", "buy.no_routes_found": "Geen routes gevonden", "buy.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "buy.select-currency.title": "Selecteer token", "buy.select-to-currency.title": "Tokens kopen", "buy_form.title": "Token kopen", "cancelled-card.create-card-button.primary": "<PERSON><PERSON><PERSON> nieuwe <PERSON> kaart aan", "cancelled-card.switch-card-button.primary": "<PERSON><PERSON><PERSON>", "cancelled-card.switch-card-button.short-text": "Je hebt nog een actieve kaart", "card": "<PERSON><PERSON>", "card-add-cash.confirm-stage.banner.no-routes-found": "Geen routes, probeer een andere token of een ander bedrag", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Je hebt {amount} meer {symbol} nodig voor de kosten", "card-add-cash.confirm-stage.banner.value-loss": "Je verliest {loss} aan waarde", "card-add-cash.confirm-stage.banner.value-loss.revert": "Ongedaan maken", "card-add-cash.edit-stage.cta.cancel": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.continue": "Doorgaan", "card-add-cash.edit-stage.cta.enter-amount": "<PERSON><PERSON> bedrag", "card-add-cash.edit-stage.cta.reduce-to-max": "Zet op max", "card-add-cash.edit-staget.banner.no-routes-found": "Geen routes, probeer een andere token of een ander bedrag", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Bevestig op je hardware wallet.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Ondertekenen op hardware wallet", "card-balance": "Saldo: {balance}", "card-cashback.status.title": "<PERSON><PERSON><PERSON> op <PERSON>", "card-copy-safe-address.copy_address": "<PERSON><PERSON><PERSON>", "card-copy-safe-address.copy_address.done": "Gekopieerd", "card-copy-safe-address.warning.description": "Dit adres kan alleen {cardAsset} ontvangen op Gnosis Chain. Stuur geen assets van andere netwerken naar dit adres. Deze gaan verloren.", "card-copy-safe-address.warning.header": "<PERSON><PERSON><PERSON> alleen {cardAsset} op Gnosis Chain", "card-marketing-card.center.subtitle": "Wisselkosten", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "Rente", "card-marketing-card.right.subtitle": "Aanmeldcadeau", "card-marketing-card.title": "Europa's VI<PERSON><PERSON><PERSON><PERSON> met hoge rente", "card-marketing-tile.get-started": "Start", "card-select-from-token-title": "Selecteer token", "card-top-up.banner.subtitle.completed": "Voltooid", "card-top-up.banner.subtitle.failed": "Mislukt", "card-top-up.banner.subtitle.pending": "{timerString} In behandeling", "card-top-up.banner.title": "<PERSON><PERSON><PERSON> {amount}", "card-topup.select-token.emptyState": "Geen tokens gevonden", "card.activate.card_number_not_valid": "Ongeldig kaartnummer. Probeer opnieuw.", "card.activate.invalid_card_number": "Ongeldig kaartnummer.", "card.activation.activate_physical_card": "Fysieke kaart <PERSON>ren", "card.add-cash.amount-to-withdraw": "Bedrag voor opwaarderen", "card.add-from-earn-form.title": "<PERSON><PERSON> toe<PERSON>n aan kaart", "card.add-from-earn-form.withdraw-to-card": "Doorgaan", "card.add-from-earn.amount-to-withdraw": "Bedrag om op te nemen naar kaart", "card.add-from-earn.enter-amount": "<PERSON><PERSON>r bedrag in", "card.add-from-earn.loading": "Laden", "card.add-from-earn.max-label": "Saldo: {amount}", "card.add-from-earn.no-routes-found": "Geen routes gevonden", "card.add-from-earn.not-enough-balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "card.add-owner.queued": "Toevoegen eigenaar in wachtrij", "card.add-to-wallet-flow.subtitle": "<PERSON>al vanuit je wallet", "card.add-to-wallet.copy-card-number": "<PERSON><PERSON><PERSON> kaartnummer hieronder", "card.add-to-wallet.title": "Voeg toe aan {platformName} Wallet", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "GEANNULEERD", "card.card-owner-not-found.disconnect-btn": "<PERSON><PERSON> ka<PERSON>", "card.card-owner-not-found.subtitle": "Update eigenaar om kaart opnieuw te verbinden.", "card.card-owner-not-found.title": "<PERSON>er<PERSON>d kaart op<PERSON>w", "card.card-owner-not-found.update-owner-btn": "Update <PERSON><PERSON><PERSON><PERSON><PERSON>", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} in {date}", "card.cashback.widgetNoCashback.subtitle": "Stort om te beginnen met verdienen", "card.cashback.widgetNoCashback.title": "Krijg tot {defaultPercentage} cashback", "card.cashback.widgetcashbackValue.rewards": "{amount} in behandeling", "card.cashback.widgetcashbackValue.title": "{percentage} cashback", "card.choose-wallet.connect_card": "<PERSON><PERSON>", "card.choose-wallet.create-new": "Nieuwe wallet als eigenaar toe<PERSON>n", "card.choose-wallet.import-another-wallet": "Importeer een andere portemon<PERSON>", "card.choose-wallet.import-current-owner": "<PERSON><PERSON><PERSON> ka<PERSON>ige<PERSON>ar importeren", "card.choose-wallet.import-current-owner.sub-text": "Importeer priv<PERSON><PERSON><PERSON>ls of seed phrase van de eigenaar van je G<PERSON>-<PERSON>art", "card.choose-wallet.title": "Selecteer wallet om je kaart te beheren", "card.connectWalletToCardGuide": "<PERSON><PERSON><PERSON>", "card.connectWalletToCardGuide.addGnosisPayOwner": "Voeg Gnosis Pay-eigenaar toe", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Open Gnosispay.com met je andere wallet{br}2. <PERSON><PERSON> op 'Account''{br}3. <PERSON><PERSON> op 'Accountgegevens''{br}4. <PERSON><PERSON> op 'Bewerken' naast 'Accounteigenaar' en{br}5. <PERSON><PERSON> op 'Adres toevoegen''{br}6. <PERSON><PERSON> je Zeal-adres en klik op opslaan", "card.connectWalletToCardGuide.header": "<PERSON><PERSON> {account} aan <PERSON>", "card.connect_card.start": "<PERSON><PERSON> bestaande kaart", "card.copiedAddress": "Gekopieerd {formattedAddress}", "card.disconnect-account.title": "Ontkoppel account", "card.hw-wallet-support-drop.add-owner-btn": "Nieuwe eigenaar toe<PERSON>n", "card.hw-wallet-support-drop.disconnect-btn": "<PERSON><PERSON> ka<PERSON>", "card.hw-wallet-support-drop.subtitle": "Voeg een eigenaar toe die geen hardware wallet is.", "card.hw-wallet-support-drop.title": "Zeal ondersteunt geen hardware wallets meer voor kaarten.", "card.kyc.continue": "<PERSON><PERSON> verde<PERSON> met instellen", "card.list_item.title": "<PERSON><PERSON>", "card.onboarded.transactions.empty.description": "Je betalingsactiviteit verschijnt hier", "card.onboarded.transactions.empty.title": "Activiteit", "card.order.continue": "<PERSON><PERSON> <PERSON><PERSON><PERSON> met bestellen", "card.order.free_virtual_card": "<PERSON><PERSON><PERSON> kaart nu", "card.order.start": "<PERSON><PERSON> gratis kaart", "card.owner-not-imported.cancel": "<PERSON><PERSON><PERSON>", "card.owner-not-imported.import": "<PERSON><PERSON><PERSON><PERSON>", "card.owner-not-imported.subtitle": "Koppel de owner wallet van je Gnosis Pay-account aan <PERSON> om deze transactie te autoriseren. Let op: dit staat los van je gebruikelijke Gnosis Pay-login.", "card.owner-not-imported.title": "Eigenaar Gnosis Pay-account toevoegen", "card.page.order_free_physical_card": "Bestel gratis fys. kaart", "card.pin.change_pin_at_atm": "Pincode kan bij geselecteerde geldautomaten worden gewijzigd", "card.pin.timeout": "Scherm sluit over {seconds} sec", "card.quick-actions.add-assets": "<PERSON><PERSON><PERSON>", "card.quick-actions.add-cash": "<PERSON><PERSON><PERSON>", "card.quick-actions.details": "Details", "card.quick-actions.freeze": "Blokkeren", "card.quick-actions.freezing": "Blokkeren...", "card.quick-actions.unfreeze": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.unfreezing": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "card.quick-actions.withdraw": "Opnemen", "card.read-only-detected.create-new": "Nieuwe wallet als eigenaar toe<PERSON>n", "card.read-only-detected.import-current-owner": "Importeer sleutels voor {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Importeer priv<PERSON><PERSON><PERSON>ls of seed phrase van wallet {address}", "card.read-only-detected.title": "<PERSON><PERSON> g<PERSON>teerd op read-only wallet. Selecteer wallet om kaart te beheren", "card.remove-owner.queued": "Eigenaar verwijderen in wachtrij", "card.settings.disconnect-from-zeal": "<PERSON><PERSON> Z<PERSON>", "card.settings.edit-owners": "Wijzig kaarteigenaren", "card.settings.getCard": "<PERSON>raag nog een kaart aan", "card.settings.getCard.subtitle": "Virtuele of fysieke kaarten", "card.settings.notRecharging": "Automatisch opwaarderen uit", "card.settings.notifications.subtitle": "<PERSON><PERSON><PERSON><PERSON>ificaties", "card.settings.notifications.title": "Kaartnotificati<PERSON>", "card.settings.page.title": "Kaartinstellingen", "card.settings.select-card.cancelled-cards": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>n", "card.settings.setAutoRecharge": "Automatisch opwaarderen instellen", "card.settings.show-card-address": "<PERSON><PERSON> ka<PERSON>", "card.settings.spend-limit": "Besteedlimiet instellen", "card.settings.spend-limit-title": "Huidige daglimiet: {limit}", "card.settings.switch-active-card": "<PERSON>issel actieve kaart", "card.settings.switch-active-card-description": "<PERSON><PERSON><PERSON> kaart: {card}", "card.settings.switch-card.card-item.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.frozen": "<PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.title": "Gnosis Pay Card", "card.settings.switch-card.card-item.title.physical": "<PERSON>ys<PERSON><PERSON> kaart", "card.settings.switch-card.card-item.title.virtual": "<PERSON>irt<PERSON><PERSON>", "card.settings.switch-card.title": "Selecteer kaart", "card.settings.targetBalance": "<PERSON><PERSON>aldo: {threshold}", "card.settings.view-pin": "Pincode bekijken", "card.settings.view-pin-description": "Bescherm je pincode altijd", "card.title": "<PERSON><PERSON>", "card.transactions.header": "Kaarttransacties", "card.transactions.see_all": "Bekijk alle transacties", "card.virtual": "VIRTUEEL", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Je ontvangt je cashback op je kaart aan het begin van de week nadat je het hebt verdiend.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "Hoe meer je stort, hoe meer je verdient bij elke aankoop.", "cardCashback.onboarding.title": "<PERSON><PERSON><PERSON><PERSON> tot {percentage} cashback", "cardCashbackWithdraw.amount": "Opnamebedrag", "cardCashbackWithdraw.header": "Neem {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "<PERSON><PERSON>, activatie mislukt", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "Tot {percentage}", "cardWidget.startEarning": "<PERSON><PERSON> met ve<PERSON><PERSON>n", "cardWithdraw.amount": "Op te nemen bedrag", "cardWithdraw.header": "<PERSON><PERSON><PERSON> van <PERSON>", "cardWithdraw.selectWithdrawWallet.title": "<PERSON>es wallet om naar{br}op te nemen", "cardWithdraw.success.cta": "Sluiten", "cardWithdraw.success.subtitle": "Uit veiligheid duurt het 3 minuten om opnames van de Gnosis Pay-kaart te verwerken", "cardWithdraw.success.title": "<PERSON><PERSON> wij<PERSON>ing duurt 3 minuten", "card_top_up_trx.send": "Verzenden", "card_top_up_trx.to": "<PERSON>ar", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "Vervaldatum", "cards.card_number": "Kaartnummer", "cards.choose-wallet.no-active-accounts": "Je hebt geen actieve portemonnees", "cards.copied_card_number": "Kaartnummer gekopieerd", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "<PERSON><PERSON><PERSON><PERSON> overs<PERSON>en", "cards.transactions.decline_reason.incorrect_pin": "Onjuiste pincode", "cards.transactions.decline_reason.incorrect_security_code": "Onju<PERSON>e beveiligingscode", "cards.transactions.decline_reason.invalid_amount": "Ongeldig bedrag", "cards.transactions.decline_reason.low_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "cards.transactions.decline_reason.other": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cards.transactions.decline_reason.pin_tries_exceeded": "Te vaak foute pincode", "cards.transactions.status.refund": "Terugbetaling", "cards.transactions.status.reversal": "Terugboeking", "cashback-deposit.trx.title": "<PERSON><PERSON><PERSON> op <PERSON>", "cashback-estimate.text": "Dit is een schatting en GEEN gegarandeerde uitbetalingen. Alle openbare cashback-regels worden toegepast, maar Gnosis Pay kan naar eigen inzicht transacties uitsluiten. Een maximale besteding van {amount} per week komt in aanmerking voor cashback, zelfs als de schatting voor deze transactie een hoger totaalbedrag aangeeft.", "cashback-estimate.text.fallback": "<PERSON><PERSON> is een schatting, geen garantie. Gnosis Pay kan transacties uitsluiten, ook als alle regels zijn toegepast.", "cashback-estimate.title": "Cashback-schatting", "cashback-onbarding-tersm.subtitle": "Je kaarttransactiegegevens worden gede<PERSON> met <PERSON><PERSON><PERSON><PERSON>, die verantwoordelijk is voor het uitkeren van cashback. Door op accepteren te klikken, ga je ak<PERSON><PERSON> met de Gnosis DAO Cashback <terms>Algemene voorwaarden</terms>", "cashback-onbarding-tersm.title": "Gebruiksvoorwaarden en privacy", "cashback-tx-activity.retry": "<PERSON><PERSON><PERSON> opnieuw", "cashback-unconfirmed-payments-info.subtitle": "Betalingen komen in aanmerking voor cashback zodra ze zijn verrekend met de verkoper. Tot die tijd worden ze weergegeven als onbevestigde betalingen. Niet-verrekende betalingen komen niet in aanmerking voor cashback.", "cashback-unconfirmed-payments-info.title": "Onbevestigde kaartbetalingen", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "Storting", "cashback.activity.title": "Recente activiteit", "cashback.activity.withdrawal": "Opname", "cashback.deposit": "<PERSON><PERSON><PERSON>", "cashback.deposit.amount.label": "Stortingsbedrag", "cashback.deposit.change": "{from} naar {to}", "cashback.deposit.confirmation.subtitle": "Cashback-percentages worden eenmaal per week bijgewerkt. Stort nu om de cashback van volgende week te verhogen.", "cashback.deposit.confirmation.title": "<PERSON> <PERSON><PERSON> met het verdi<PERSON><PERSON> van {percentage} op {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Wissel tokens om naar {currency} op {network} Chain", "cashback.deposit.get.tokens.title": "Verkrijg {currency} tokens", "cashback.deposit.header": "Stort {currency}", "cashback.deposit.max_label": "Max: {amount}", "cashback.deposit.select-wallet.title": "Kies een wallet om vanaf te storten", "cashback.deposit.yourcashback": "<PERSON><PERSON><PERSON> cashback", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "Kies een wallet om{br}naar op te nemen", "cashback.transaction-details.network-label": "Netwerk", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "<PERSON>", "cashback.transaction-details.top-row.label-rewards": "Cashbackperiode", "cashback.transaction-details.top-row.label-withdrawal": "<PERSON><PERSON>", "cashback.transaction-details.transaction": "Transactie-ID", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Cashbacktransacties", "cashback.withdraw": "Opnemen", "cashback.withdraw.confirmation.cashback_reduction": "De cashback voor deze week, inclusief wat je al hebt verdiend, wordt verlaagd van {before} naar {after}", "cashback.withdraw.queued": "Opname in wachtrij", "cashback.withdrawal.change": "{from} naar {to}", "cashback.withdrawal.confirmation.subtitle": "Start opname van {amount} met 3 minuten vertraging. Dit verlaagt je cashback naar {after}.", "cashback.withdrawal.confirmation.title": "Cashback daalt als je GNO opneemt", "cashback.withdrawal.delayTransaction.title": "Start GNO-opname met{br} 3 minuten vertraging", "cashback.withdrawal.withdraw": "Opnemen", "cashback.withdrawal.yourcashback": "<PERSON><PERSON><PERSON> cashback", "celebration.aave": "<PERSON><PERSON> met <PERSON><PERSON>", "celebration.cashback.subtitle": "Uitgekeerd in {code}", "celebration.cashback.subtitleGNO": "{amount} laatst verdiend", "celebration.chf": "<PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON>", "celebration.lido": "<PERSON><PERSON> met <PERSON><PERSON>", "celebration.sky": "<PERSON><PERSON> met Sky", "celebration.title": "To<PERSON>al aan cashback", "celebration.well_done.title": "Goed gedaan!", "change-withdrawal-account.add-new-account": "Voeg nog een bankrekening toe", "change-withdrawal-account.item.shortText": "{currency} rekening", "check-confirmation.approve.footer.for": "Voor", "checkConfirmation.title": "Transactieresultaat", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin & Ether", "collateral.ethereum": "Ethereum", "collateral.other": "<PERSON><PERSON>", "collateral.rwa": "Real World Assets", "collateral.stablecoins": "Stablecoins (gekoppeld aan USD)", "collateral.us-t-bills": "US T-Bills", "confirm-bank-transfer-recipient.bullet-1": "Geen kosten op digitale EUR", "confirm-bank-transfer-recipient.bullet-2": "Stortingen naar {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "<PERSON><PERSON> de accountgege<PERSON>s van G<PERSON> Pay met Monerium, een geautoriseerde en gereguleerde EMI. <link>Meer informatie</link>", "confirm-bank-transfer-recipient.bullet-4": "Accepteer de Monerium <link>servicevoorwaarden</link>", "confirm-bank-transfer-recipient.title": "Voorwaarden accepteren", "confirm-change-withdrawal-account.cancel": "<PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.confirm": "Bevestig", "confirm-change-withdrawal-account.saving": "Opsla<PERSON>", "confirm-change-withdrawal-account.subtitle": "Alle opnames die je vanuit Zeal verstuurt, worden op deze bankrekening ontvangen.", "confirm-change-withdrawal-account.title": "Wijzig ontvangende bank", "confirm-ramove-withdrawal-account.title": "Verwijder bankrekening", "confirm-remove-withdrawal-account.subtitle": "Deze bankrekeninggegevens worden uit Zeal verwijderd. Je kunt ze altijd opnieuw toevoegen.", "confirmTransaction.finalNetworkFee": "Netwerkkosten", "confirmTransaction.importKeys": "Sleutels importeren", "confirmTransaction.networkFee": "Netwerkkosten", "confirmation.title": "Vers<PERSON>ur {amount} naar {recipient}", "conflicting-monerium-account.add-owner": "Toevoegen als Gnosis Pay-eigenaar", "conflicting-monerium-account.create-wallet": "Nieuwe Smart Wallet aanmaken", "conflicting-monerium-account.disconnect-card": "<PERSON><PERSON> en opnieuw verbinden met nieuwe eigenaar", "conflicting-monerium-account.header": "{wallet} is gekoppeld aan een ander Monerium-account", "conflicting-monerium-account.subtitle": "<PERSON><PERSON><PERSON><PERSON> je Gnosis Pay-eigenaar port<PERSON>", "connection.diconnected.got_it": "Oké!", "connection.diconnected.page1.subtitle": "Zeal werkt overal waar MetaMask werkt. Ver<PERSON>d zoals je met MetaMask zou doen.", "connection.diconnected.page1.title": "Hoe verbind je met <PERSON><PERSON>?", "connection.diconnected.page2.subtitle": "Je ziet veel opties. Zeal is er misschien <PERSON> van. Als Zeal niet verschijnt...", "connection.diconnected.page2.title": "Klik op 'Connect Wallet'", "connection.diconnected.page3.subtitle": "We vragen je dan om te verbinden met <PERSON><PERSON>. 'Browser' of 'Injected' werkt ook. <PERSON>beer het!", "connection.diconnected.page3.title": "<PERSON><PERSON>", "connectionSafetyCheck.tag.caution": "Let op", "connectionSafetyCheck.tag.danger": "<PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.passed": "Geslaagd", "connectionSafetyConfirmation.subtitle": "Weet je zeker dat je door wilt gaan?", "connectionSafetyConfirmation.title": "Deze site lijkt gevaarlijk", "connection_state.connect.cancel": "<PERSON><PERSON><PERSON>", "connection_state.connect.changeToMetamask": "Wissel naar MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Wissel naar MetaMask", "connection_state.connect.connect_button": "Verbinden", "connection_state.connect.expanded.connected": "Verbonden", "connection_state.connect.expanded.title": "Verbinden", "connection_state.connect.safetyChecksLoading": "Siteveiligheid controleren", "connection_state.connect.safetyChecksLoadingError": "Veiligheidscontroles mislukt", "connection_state.connected.expanded.disconnectButton": "<PERSON><PERSON>", "connection_state.connected.expanded.title": "Verbonden", "copied-diagnostics": "Diagnostische gegevens gekopieerd", "copy-diagnostics": "Diagnostische gegevens kopiëren", "counterparty.component.add_recipient_primary_text": "Bankontvanger toe<PERSON>n", "counterparty.country": "Land", "counterparty.countryTitle": "Land van ontvanger", "counterparty.currency": "Valuta", "counterparty.delete.success.title": "Verwijderd", "counterparty.edit.success.title": "Wijzigingen opgeslagen", "counterparty.errors.country_required": "Land is verplicht", "counterparty.errors.first_name.invalid": "<PERSON><PERSON><PERSON><PERSON> moet langer zijn", "counterparty.errors.last_name.invalid": "Achternaam moet langer zijn", "counterparty.first_name": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Versturen naar bank", "countrySelector.noCountryFound": "Geen land gevonden", "countrySelector.title": "Kies land", "create-passkey.cta": "<PERSON><PERSON> a<PERSON>", "create-passkey.extension.cta": "Doorgaan", "create-passkey.footnote": "Mogelijk gemaakt door", "create-passkey.mobile.cta": "Start beveiligingssetup", "create-passkey.steps.enable-recovery": "Cloudherstel instellen", "create-passkey.steps.setup-biometrics": "Biometrische beveiliging inschakelen", "create-passkey.subtitle": "Passkeys zijn veiliger dan wachtwoorden en versleuteld in de cloud voor eenvoudig herstel.", "create-passkey.title": "Account beveiligen", "create-smart-wallet": "Maak <PERSON> Wallet aan", "create-userop.progress.text": "Aanmaken", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Ga verder in Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Ga naar Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "Je bent al een kaartbestelling gestart. Ga terug naar de Gnosis Pay-site om het af te ronden.", "create_recharge_preferences.card": "<PERSON><PERSON>", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "<PERSON>en {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "<PERSON><PERSON> a<PERSON>", "create_recharge_preferences.link_accounts_title": "<PERSON><PERSON>", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "Je kaart wordt NIET automatisch opgewaardeerd na elke betaling.", "create_recharge_preferences.not_configured_title": "Verdien & Geef uit", "create_recharge_preferences.recharge_from_earn_accounts_description": "<PERSON> kaart wordt na elke betaling automatisch opgewaardeerd vanuit je Earn-rekening.", "create_recharge_preferences.subtitle": "per jaar", "creating-account.loading": "Account wordt aangemaakt", "creating-gnosis-pay-account": "Account aan<PERSON>ken", "currencies.bridge.select_routes.emptyState": "Geen routes gevonden voor deze bridge", "currency.add_currency.add_token": "Token toe<PERSON>n", "currency.add_currency.not_a_valid_address": "Dit is geen geldig <PERSON>", "currency.add_currency.token_decimals_feild": "Token decimalen", "currency.add_currency.token_feild": "Tokenadres", "currency.add_currency.token_symbol_feild": "Tokensymbool", "currency.add_currency.update_token": "Token bijwerken", "currency.add_custom.remove_token.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.add_custom.remove_token.header": "Token verwijderen", "currency.add_custom.remove_token.subtitle": "Je portemonnee behoudt het saldo van de<PERSON> token, maar het wordt verborgen in je Zeal-portfolio.", "currency.add_custom.token_removed": "Token verwi<PERSON>derd", "currency.add_custom.token_updated": "Token bijgewerkt", "currency.balance_label": "Saldo: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "Je bankoverboeking heeft succesvol {fiat} omgezet naar {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "Je hebt ontvangen {crypto}", "currency.bankTransfer.deposit_status.success": "Ontvangen in je wallet", "currency.bankTransfer.deposit_status.title": "Storting", "currency.bankTransfer.off_ramp.check_bank_account": "Controleer je bankrekening", "currency.bankTransfer.off_ramp.complete": "Voltooid", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Verzenden naar je bank", "currency.bankTransfer.off_ramp.transferring_to_currency": "Overboeken naar {to<PERSON>urrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Het geld zou nu op je bankrekening moeten staan.", "currency.bankTransfer.withdrawal_status.success": "Verzonden naar je bank", "currency.bankTransfer.withdrawal_status.title": "Opname", "currency.bank_transfer.create_unblock_user.email": "E-mailadres", "currency.bank_transfer.create_unblock_user.email_invalid": "Ongeldig e-mailadres", "currency.bank_transfer.create_unblock_user.email_missing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_missing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "Alleen letters, cijfers, spaties en - . , & ( ) ' toegestaan.", "currency.bank_transfer.create_unblock_user.last_name": "Achternaam", "currency.bank_transfer.create_unblock_user.last_name_missing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "Alleen letters, cijfers, spaties en - . , & ( ) ' toegestaan.", "currency.bank_transfer.create_unblock_user.note": "Door verder te gaan accepteer je <PERSON>'s (onze bankpartner) <terms>Voorwaarden</terms> en <policy>Privacybeleid</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Spel je naam precies zoals op je bankrekening", "currency.bank_transfer.create_unblock_user.title": "<PERSON><PERSON> je bankrekening", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Rekeningnummer", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Land van de bank", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Voorkeursvaluta", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Sorteercode", "currency.bank_transfer.create_unblock_withdraw_account.success": "Account ingesteld", "currency.bank_transfer.create_unblock_withdraw_account.title": "<PERSON><PERSON> je bankrekening", "currency.bank_transfer.residence-form.address-required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.address-unsupported-char": "Alleen letters, cijfers, spaties en , ; {apostrophe} - \\\\ toegestaan.", "currency.bank_transfer.residence-form.city-required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.city-unsupported-char": "Alleen letters, cijfers, spaties en . , - & ( ) {apostrophe} toegestaan.", "currency.bank_transfer.residence-form.postcode-invalid": "Ongeldige postcode", "currency.bank_transfer.residence-form.postcode-required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.validation.invalid.account_number": "Ongeldig rekeningnummer", "currency.bank_transfer.validation.invalid.iban": "Ongeldige IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Ongeldige sorteercode", "currency.bridge.amount_label": "Bedrag voor bridge", "currency.bridge.best_returns.subtitle": "Deze bridge provider geeft je de hoogste opbrengst, inclusief alle kosten.", "currency.bridge.best_returns_popup.title": "Beste opbrengst", "currency.bridge.bridge_from": "<PERSON>", "currency.bridge.bridge_gas_fee_loading_failed": "Probleem bij het laden van netwerkkosten", "currency.bridge.bridge_low_slippage": "Zeer lage slippage. Probeer deze te verhogen", "currency.bridge.bridge_provider": "A<PERSON><PERSON>der overboeking", "currency.bridge.bridge_provider_loading_failed": "Laden van providers is mislukt", "currency.bridge.bridge_settings": "Bridge-instellingen", "currency.bridge.bridge_status.subtitle": "Via {name}", "currency.bridge.bridge_status.title": "Bridge", "currency.bridge.bridge_to": "<PERSON>ar", "currency.bridge.fastest_route_popup.subtitle": "Deze bridge provider geeft je de snelste transactieroute.", "currency.bridge.fastest_route_popup.title": "Snelste route", "currency.bridge.from": "<PERSON>", "currency.bridge.success": "Voltooid", "currency.bridge.title": "Bridge", "currency.bridge.to": "<PERSON>ar", "currency.bridge.topup": "Waardeer op {symbol}", "currency.bridge.withdrawal_status.title": "Opname", "currency.card.card_top_up_status.title": "<PERSON><PERSON> toe<PERSON>n aan kaart", "currency.destination_amount": "Te ontvangen bedrag", "currency.hide_currency.confirm.subtitle": "<PERSON><PERSON><PERSON> deze token in je portfolio. Je kunt dit op elk moment ongedaan maken.", "currency.hide_currency.confirm.title": "Token verbergen", "currency.hide_currency.success.title": "Token verborgen", "currency.label": "Label (optioneel)", "currency.last_name": "Achternaam", "currency.max_loading": "Max:", "currency.swap.amount_to_swap": "Bedrag om te swappen", "currency.swap.best_return": "Route met beste rendement", "currency.swap.destination_amount": "Te ontvangen bedrag", "currency.swap.header": "<PERSON><PERSON><PERSON>", "currency.swap.max_label": "Saldo: {amount}", "currency.swap.provider.header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.swap.select_to_token": "Selecteer token", "currency.swap.swap_gas_fee_loading_failed": "Probleem bij het laden van netwerkkosten", "currency.swap.swap_provider_loading_failed": "Probleem bij het laden van a<PERSON>ders", "currency.swap.swap_settings": "Swap-instellingen", "currency.swap.swap_slippage_too_low": "Zeer lage slippage. Probeer te verhogen.", "currency.swaps_io_native_token_swap.subtitle": "Via Swaps.IO", "currency.swaps_io_native_token_swap.title": "Verzenden", "currency.withdrawal.amount_from": "<PERSON>", "currency.withdrawal.amount_to": "<PERSON>ar", "currencySelector.title": "<PERSON><PERSON> valuta", "dApp.wallet-does-not-support-chain.subtitle": "Je portemonnee lijkt {network} niet te ondersteunen. <PERSON><PERSON>r een andere portemonnee te verbinden of gebruik Zeal.", "dApp.wallet-does-not-support-chain.title": "Niet-ondersteund netwerk", "dapp.connection.manage.confirm.disconnect.all.cta": "<PERSON><PERSON><PERSON><PERSON>", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Weet je zeker dat je alle verbindingen wilt verbreken?", "dapp.connection.manage.confirm.disconnect.all.title": "<PERSON><PERSON>", "dapp.connection.manage.connection_list.main.button.title": "<PERSON><PERSON><PERSON><PERSON>", "dapp.connection.manage.connection_list.no_connections": "Je hebt geen verbonden apps", "dapp.connection.manage.connection_list.section.button.title": "<PERSON><PERSON>", "dapp.connection.manage.connection_list.section.title": "Actief", "dapp.connection.manage.connection_list.title": "Verbindingen", "dapp.connection.manage.disconnect.success.title": "<PERSON><PERSON>", "dapp.metamask_mode.title": "MetaMask-modus", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "Rente", "dc25-card-marketing-card.right.subtitle": "100 personen", "dc25-card-marketing-card.title": "De eerste 100 die €50 uitgeven, krijgen {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Je kunt deze actie 3 minuten niet uitvoeren. Vanwege veiligheidsredenen duurt het 3 minuten om kaartinstellingen of opnames te verwerken.", "delayQueueBusyBanner.processing-yout-action.title": "Je actie wordt verwerkt, even geduld", "delayQueueBusyWidget.cardFrozen": "<PERSON><PERSON>", "delayQueueBusyWidget.processingAction": "Je actie wordt verwerkt", "delayQueueFailedBanner.action-incomplete.get-support": "Vraag om hulp", "delayQueueFailedBanner.action-incomplete.subtitle": "Sorry, er is iets mis<PERSON><PERSON><PERSON> met je opname of de update van je instellingen. Neem contact op met support via Discord.", "delayQueueFailedBanner.action-incomplete.title": "Actie niet voltooid", "delayQueueFailedWidget.actionIncomplete.title": "Kaart<PERSON>ie niet voltooid", "delayQueueFailedWidget.cardFrozen.subtitle": "<PERSON><PERSON>", "delayQueueFailedWidget.contactSupport": "Neem contact op", "delay_queue_busy.subtitle": "Om veiligheidsredenen duurt het 3 minuten om kaartinstellingen te wijzigen of geld op te nemen. Gedurende die tijd is je kaart bevroren.", "delay_queue_busy.title": "Je actie wordt verwerkt", "delay_queue_failed.contact_support": "Support", "delay_queue_failed.subtitle": "Sorry, er is iets mis<PERSON><PERSON><PERSON> met je opname of de update van je instellingen. Neem contact op met support via Discord.", "delay_queue_failed.title": "Neem contact op met support", "deploy-earn-form-smart-wallet.in-progress.title": "Earn voorbereiden", "deposit": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.cancel": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.disconnect": "<PERSON><PERSON><PERSON><PERSON>", "disconnect-card-popup.subtitle": "<PERSON><PERSON><PERSON> verwijder je je kaart uit de Zeal-app. Je wallet blijft verbonden met je kaart in de Gnosis Pay-app. Je kunt je kaart op elk moment opnieuw koppelen.", "disconnect-card-popup.title": "<PERSON><PERSON>", "distance.long.days": "{count} dagen", "distance.long.hours": "{count} uur", "distance.long.minutes": "{count} minuten", "distance.long.months": "{count} maanden", "distance.long.seconds": "{count} seconden", "distance.long.years": "{count} jaar", "distance.short.days": "{count} d", "distance.short.hours": "{count} u", "distance.short.minutes": "{count} min", "distance.short.months": "{count} m", "distance.short.seconds": "{count} sec", "distance.short.years": "{count} j", "duration.short.days": "{count}d", "duration.short.hours": "{count}u", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "Storting", "earn-deposit-view.into": "In", "earn-deposit-view.to": "<PERSON>ar", "earn-deposit.swap.transfer-provider": "A<PERSON><PERSON>der overboeking", "earn-taker-investment-details.accrued-realtime": "Realtime opgebouwd", "earn-taker-investment-details.asset-class": "Activaklasse", "earn-taker-investment-details.asset-coverage-ratio": "Dekkingsgraad", "earn-taker-investment-details.asset-reserve": "Activareserve", "earn-taker-investment-details.base_currency.label": "Basisvaluta", "earn-taker-investment-details.chf.description": "Verdien rente op je CHF door zCHF te storten in Frankencoin, een vertrouwde digitale geldmarkt. De rente komt uit leningen met laag risico en extra onderpand op Frankencoin en wordt direct uitbetaald. Je geld staat veilig op een beveiligde sub-rekening waar alleen jij toegang toe hebt.", "earn-taker-investment-details.chf.description.with_address_link": "Verdien rente op je CHF door zCHF te storten in Frankencoin, een vertrouwde digitale geldmarkt. De rente komt uit leningen met een laag risico en extra onderpand op Frankencoin en wordt direct uitbetaald. Je geld staat veilig op een beveiligde sub-rekening <link>(kopieer 0x)</link> waar alleen jij toegang toe hebt.", "earn-taker-investment-details.chf.label": "Digitale Zwitserse Frank", "earn-taker-investment-details.collateral-composition": "Samenstelling onderpand", "earn-taker-investment-details.depositor-obligations": "Verplichtingen aan spaarders", "earn-taker-investment-details.eure.description": "Verdien rente op je euro's door EURe te storten in Aave - een vertrouwde digitale geldmarkt. EURe is een volledig gereguleerde euro-stablecoin, uitgegeven door Monerium en 1:1 gedekt op beveiligde rekeningen. De rente komt van leningen met een laag risico en overcollateralisatie op Aave, en wordt realtime uitbetaald. Je geld staat op een veilige sub-rekening waar alleen jij controle over hebt.", "earn-taker-investment-details.eure.description.with_address_link": "Verdien rente op je euro's door EURe te storten in Aave - een vertrouwde digitale geldmarkt. EURe is een volledig gereguleerde euro-stablecoin, uitgegeven door Monerium en 1:1 gedekt op beveiligde rekeningen. De rente komt van leningen met een laag risico en overcollateralisatie op Aave, en wordt realtime uitbetaald. Je geld staat op een veilige sub-rekening <link>(kopieer 0x)</link> waar alleen jij controle over hebt.", "earn-taker-investment-details.eure.label": "Digitale Euro (EURe)", "earn-taker-investment-details.faq": "FAQ", "earn-taker-investment-details.fixed-income": "Vastrentend", "earn-taker-investment-details.issuer": "Uitgever", "earn-taker-investment-details.key-facts": "Belangrijkste feiten", "earn-taker-investment-details.liquidity": "Liquiditeit", "earn-taker-investment-details.operator": "Marktoperator", "earn-taker-investment-details.projected-yield": "Verwacht jaarlijks rendement", "earn-taker-investment-details.see-other-faq": "Bekijk alle andere FAQ's", "earn-taker-investment-details.see-realtime": "Bekijk realtime data", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Rendement afgelopen 12 maanden", "earn-taker-investment-details.total-collateral": "Totaal onderpand", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "Totale ZCHF-voorraad", "earn-taker-investment-details.total_deposits": "Totaal Aave-stortingen", "earn-taker-investment-details.usd.description": "Sky is een digitale geldmarkt met stabiele rendementen in Amerikaanse dollars, afkomstig van kortlopende Amerikaanse staatsobligaties en leningen met overcollateralisatie. Zonder cryptovolatiliteit, 24/7 toegang tot je geld en transparante on-chain dekking.", "earn-taker-investment-details.usd.description.with_address_link": "Sky is een digitale geldmarkt met stabiele rendementen in Amerikaanse dollars, afkomstig van kortlopende Amerikaanse staatsobligaties en leningen met overcollateralisatie. Zonder cryptovolatiliteit, 24/7 toegang tot je geld en transparante on-chain dekking. Beleggingen staan op een sub-rekening <link>(kopieer 0x)</link> waar jij de controle over hebt.", "earn-taker-investment-details.usd.ftx-difference": "Wat is het ve<PERSON><PERSON> met <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> of Luna?", "earn-taker-investment-details.usd.high-returns": "Hoe kan het rendement zo hoog zijn, <PERSON><PERSON><PERSON> met traditionele banken?", "earn-taker-investment-details.usd.how-is-backed": "Hoe wordt Sky USD gedekt en wat gebeurt er met mijn geld als Zeal failliet gaat?", "earn-taker-investment-details.usd.income-sources": "Inkomstenbronnen 2024", "earn-taker-investment-details.usd.insurance": "Is mijn geld verz<PERSON><PERSON> of gegarandeerd door een instantie (zoals het DGS)?", "earn-taker-investment-details.usd.label": "Digitale Amerikaanse Dollar", "earn-taker-investment-details.usd.lose-principal": "Kan ik mijn inleg echt verliezen, en zo ja, hoe?", "earn-taker-investment-details.variable-rate": "<PERSON><PERSON> met variabele rente", "earn-taker-investment-details.withdraw-anytime": "<PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.yield": "Rendement", "earn-withdrawal-view.approve.for": "Voor", "earn-withdrawal-view.approve.into": "In", "earn-withdrawal-view.swap.into": "In", "earn-withdrawal-view.withdraw.to": "<PERSON>ar", "earn.add_another_asset.title": "Selecteer asset voor Earn", "earn.add_asset": "Activa toevoegen", "earn.asset_view.title": "<PERSON><PERSON><PERSON>", "earn.base-currency-popup.text": "De basisvaluta is de munteenheid waarin je stortingen, rendement en transacties worden gewaardeerd. Als je in een andere valuta stort (zoals EUR in een USD-product), wordt je geld direct omgezet naar de basisvaluta tegen de huidige wisselkoers. Je saldo blijft dan stabiel in de basisvaluta, maar bij opnames kan er weer een omzetting nodig zijn.", "earn.base-currency-popup.title": "Basisvaluta", "earn.card-recharge.disabled.list-item.title": "Automatisch opwaarderen uitgeschakeld", "earn.card-recharge.enabled.list-item.title": "Automatisch opwaarderen ingeschakeld", "earn.choose_wallet_to_deposit.title": "<PERSON><PERSON><PERSON>", "earn.config.currency.eth": "Verdien Ethereum", "earn.config.currency.on_chain_address_subtitle": "On-chain adres", "earn.config.currency.us_dollars": "Bankoverboekingen instellen", "earn.configured_widget.current_apy.title": "Huidige APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} per jaar", "earn.confirm.currency.cta": "<PERSON><PERSON><PERSON>", "earn.currency.eth": "Verdien Ethereum", "earn.deploy.status.title": "Earn-account aanmaken", "earn.deploy.status.title_with_taker": "Maak {title} Earn-account", "earn.deposit": "<PERSON><PERSON><PERSON>", "earn.deposit.amount_to_deposit": "Te storten bedrag", "earn.deposit.deposit": "<PERSON><PERSON><PERSON>", "earn.deposit.enter_amount": "<PERSON><PERSON>r bedrag in", "earn.deposit.no_routes_found": "Geen routes gevonden", "earn.deposit.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "earn.deposit.select-currency.title": "Selecteer token om te storten", "earn.deposit.select_account.title": "Selecteer Earn-account", "earn.desposit_form.title": "Storten in Earn", "earn.earn_deposit.status.title": "Storten op Earn", "earn.earn_deposit.trx.title": "Storten op Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "<PERSON><PERSON><PERSON> geld op", "earn.earn_withdraw.status.title": "<PERSON>ne<PERSON>-account", "earn.earn_withdraw.trx.title.approval": "Opname goed<PERSON>uren", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Opnemen naar {asset}", "earn.earn_withdraw.trx.title.withdrawal": "<PERSON><PERSON><PERSON>", "earn.recharge.cta": "Wijzigingen opslaan", "earn.recharge.earn_not_configured.enable_some_account.error": "Activeer account", "earn.recharge.earn_not_configured.enter_amount.error": "<PERSON><PERSON>r bedrag in", "earn.recharge.select_taker.header": "<PERSON><PERSON><PERSON><PERSON> kaart op in volgorde van", "earn.recharge_card_tag.on": "aan", "earn.recharge_card_tag.recharge": "Opwaarderen", "earn.recharge_card_tag.recharge_not_configured": "Automatisch opwaarderen", "earn.recharge_card_tag.recharge_off": "Opwaarderen uit", "earn.recharge_card_tag.recharged": "Opgewaardeerd", "earn.recharge_card_tag.recharging": "Opladen", "earn.recharge_configured.disable.trx.title": "Automatisch opwaarderen uitschakelen", "earn.recharge_configured.trx.disclaimer": "<PERSON><PERSON> je je kaart geb<PERSON>t, wordt er een Cowswap-veiling aangemaakt om met je Earn-tegoeden hetzelfde bedrag als je betaling te kopen. Dit veilingproces levert je doorgaans de beste marktkoers op, maar houd er rekening mee dat de on-chain koers kan afwijken van de reële wisselk<PERSON>rsen.", "earn.recharge_configured.trx.subtitle": "Na elke betaling wordt er automatisch geld van je Earn-account(s) toegevoegd om je kaartsaldo op {value}", "earn.recharge_configured.trx.title": "Automatisch opwaarderen instellen op {value}", "earn.recharge_configured.updated.trx.title": "Opwaardeerinstellingen opslaan", "earn.risk-banner.subtitle": "Dit is een product in eigen beheer, zonder wettelijke bescherming tegen verlies.", "earn.risk-banner.title": "Begrijp de risico's", "earn.set_recharge.status.title": "Automatisch aanvullen instellen", "earn.setup_reacharge.input.disable.label": "Uitschakelen", "earn.setup_reacharge.input.label": "<PERSON><PERSON><PERSON>", "earn.setup_reacharge_form.title": "Automatisch opwaarderen houdt je {br} kaart op hetzelfde saldo", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Houd wstETH (Staked ETH) aan op Gnosis Chain en leen uit via Lido.", "earn.taker-bulletlist.point_1": "Verdien {apyValue} per jaar. Rendement varieert met de markt.", "earn.taker-bulletlist.point_3": "Zeal rekent geen kosten.", "earn.taker-historical-returns": "Historisch rendement", "earn.taker-historical-returns.chf": "<PERSON><PERSON><PERSON> van CHF naar USD", "earn.taker-investment-tile.apy.perYear": "per jaar", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "<PERSON><PERSON><PERSON>", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Verdienen via Aave op Gnosis Chain", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "<PERSON><PERSON><PERSON> met Frankencoin op Gnosis Chain", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Verdienen via Lido op Gnosis Chain", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Verdienen via Maker op Gnosis Chain", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "Eth earn", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH Earn", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "Amerikaanse staatsobligaties (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "<PERSON><PERSON><PERSON> extreem zeldzaam, is het theoretisch mogelijk. Je geld wordt beschermd door streng risicobeheer en een hoge dekkingsgraad. Het meest realistische 'worst-case'-scenario zou een extreme marktsituatie zijn, zoals meerdere stablecoins die tegelijk hun koppeling verliezen. <PERSON><PERSON><PERSON> is nog nooit gebeurd.", "earn.usd.can-I-lose-my-principal-popup.title": "Kan ik mijn inleg echt verliezen, en zo ja, hoe?", "earn.usd.ftx-difference-popup.text": "Sky is fundamenteel anders. In tegenstelling tot FTX, <PERSON><PERSON><PERSON>, BlockFi of Luna, die afhankelijk waren van centraal beheer, ondoorzichtig vermogensbeheer en risicovolle posities, gebruikt Sky USD transparante, gecontroleerde, decentrale smart contracts. Alles is volledig on-chain inzichtelijk. <PERSON><PERSON> behoudt de volledige controle, wat het tegenpartijrisico van centrale platformen aanzienlijk verkleint.", "earn.usd.ftx-difference-popup.title": "Wat is het ve<PERSON><PERSON> met <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> of Luna?", "earn.usd.high-returns-popup.text": "Sky USD genereert rendement voornamelijk via decentrale financiële (DeFi) protocollen. Deze automatiseren peer-to-peer leningen en liquiditeitsverschaffing, waardoor traditionele bankkosten en tussenpersonen wegvallen. De<PERSON> efficiëntie, <PERSON><PERSON><PERSON><PERSON><PERSON> met robuuste risicocontroles, maakt aanzienlijk hogere rendementen mogelijk dan bij traditionele banken.", "earn.usd.high-returns-popup.title": "Hoe kan het rendement zo hoog zijn, <PERSON><PERSON><PERSON> met traditionele banken?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD is volledig gedekt en heeft extra onderpand in de vorm van een combinatie van digitale activa in veilige smart contracts en activa uit de echte wereld, zoals Amerikaanse staatsobligaties. De reserves kunnen realtime on-chain worden gecontroleerd, zelfs vanuit Zeal. Dit zorgt voor transparantie en zekerheid. Mocht Zeal er onverhoopt mee stoppen, dan blijft je vermogen veilig on-chain staan, volledig onder jouw controle en toegankelijk via andere compatibele wallets.", "earn.usd.how-is-sky-backed-popup.title": "Hoe wordt Sky USD gedekt en wat gebeurt er met mijn geld als Zeal failliet gaat?", "earn.usd.insurance-popup.text": "Sky USD-tegoeden zijn niet verzekerd door het DGS of andere overheidsgaranties, omdat het een rekening op basis van digitale activa is, geen traditionele bankrekening. In plaats daarvan beheert Sky alle risico's via gecontroleerde smart contracts en zorgvuldig geselecteerde DeFi-protocollen, waardoor activa veilig en transparant blijven.", "earn.usd.insurance-popup.title": "Is mijn geld verz<PERSON><PERSON> of gegarandeerd door een instantie (zoals het DGS)?", "earn.usd.lending-operations-popup.text": "Sky USD genereert rendement door stablecoins uit te lenen via decentrale leenplatformen zoals Morpho en Spark. Jouw stablecoins worden uitgeleend aan leners die aanzienlijk meer onderpand storten (zoals ETH of BTC) dan de waarde van hun lening. <PERSON><PERSON> a<PERSON>, 'overcollateralisatie' geno<PERSON><PERSON>, zorgt ervoor dat er altijd voldoende onderpand is om leningen te dekken, wat het risico sterk verkleint. De ontvangen rente en incidentele liquidatiekosten van leners zorgen voor een betrouwbaar, transparant en veilig rendement.", "earn.usd.lending-operations-popup.title": "Uitleenactiviteiten", "earn.usd.market-making-operations-popup.text": "Sky USD verdient extra rendement door deel te nemen aan decentrale beurzen (AMM's) zoals Curve of Uniswap. Door liquiditeit te verschaffen - je stablecoins in pools te plaatsen die cryptohandel mogelijk maken - ontvangt Sky USD een deel van de transactiekosten. Deze liquiditeitspools worden zorgvuldig geselecteerd om de volatiliteit te minimaliseren, voornamelijk door stablecoin-naar-stablecoin-paren te gebruiken. Dit vermindert risico's zoals 'impermanent loss' aanzien<PERSON><PERSON>, waardoor je vermogen veilig en toegankelijk blijft.", "earn.usd.market-making-operations-popup.title": "Marketmaking-activiteiten", "earn.usd.treasury-operations-popup.text": "Sky USD genereert een stabiel en consistent rendement door strategische treasury-beleggingen. <PERSON><PERSON> deel van je stablecoin-stortingen wordt toegewezen aan veilige, laag-risico activa uit de echte wereld, voornamelijk kortlopende staatsobligaties en zeer veilige kredietinstrumenten. <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> met traditioneel bankieren, zorgt voor een voorspelbaar en betrouwbaar rendement. Je vermogen blijft veilig, liquide en wordt transparant beheerd.", "earn.usd.treasury-operations-popup.title": "Treasury-activiteiten", "earn.view_earn.card_rechard_off": "Uit", "earn.view_earn.card_rechard_on": "<PERSON><PERSON>", "earn.view_earn.card_recharge": "<PERSON><PERSON>", "earn.view_earn.total_balance_label": "Verdient {percentage} per jaar", "earn.view_earn.total_earnings_label": "<PERSON>e verdi<PERSON>ten", "earn.withdraw": "Opnemen", "earn.withdraw.amount_to_withdraw": "Bedrag om op te nemen", "earn.withdraw.enter_amount": "<PERSON><PERSON>r bedrag in", "earn.withdraw.loading": "Laden", "earn.withdraw.no_routes_found": "Geen routes gevonden", "earn.withdraw.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "earn.withdraw.select-currency.title": "Selecteer token", "earn.withdraw.select_to_token": "Selecteer token", "earn.withdraw.withdraw": "Opnemen", "earn.withdraw_form.title": "Opnemen uit Earn", "earnings-view.earnings": "Totale opbrengsten", "edit-account-owners.add-owner.add-wallet": "<PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.add_wallet": "Wallet toevoegen", "edit-account-owners.add-owner.title": "Kaarteigenaar <PERSON>", "edit-account-owners.card-owners": "Kaarteigenaren", "edit-account-owners.external-wallet": "Externe wallet", "editBankRecipient.title": "Ontvanger bewerken", "editNetwork.addCustomRPC": "Aangepaste RPC-node toevoegen", "editNetwork.cannot_verify.subtitle": "De RPC-node reageert niet. Controleer de URL en probeer het opnieuw.", "editNetwork.cannot_verify.title": "We kunnen RPC-node niet veri<PERSON>", "editNetwork.cannot_verify.try_again": "Opnieuw", "editNetwork.customRPCNode": "Aangepaste RPC-node", "editNetwork.defaultRPC": "Standaard RPC", "editNetwork.networkRPC": "Netwerk-RPC", "editNetwork.rpc_url.cannot_be_empty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editNetwork.rpc_url.not_a_valid_https_url": "Moet een geldige HTTP(S) URL zijn", "editNetwork.safetyWarning.subtitle": "Zeal kan de privacy, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> en veiligh<PERSON><PERSON> van aangepaste RPC's niet garanderen. Weet je zeker dat je een aangepaste RPC-node wilt gebruiken?", "editNetwork.safetyWarning.title": "Aangepaste RPC's kunnen onveilig zijn", "editNetwork.zealRPCNode": "Zeal RPC-node", "editNetworkRpc.headerTitle": "Aangepaste RPC-node", "editNetworkRpc.rpcNodeUrl": "RPC-node URL", "editing-locked.modal.description": "<PERSON> dan bij <PERSON><PERSON><PERSON>uringstransacties, kun je bij Permits de bestedingslimiet of vervaltijd niet wijzigen. Vertrouw een dApp voordat je een Permit indient.", "editing-locked.modal.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> niet mogelijk", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Opwaarderen inschakelen", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Opwaardere<PERSON> ingeschakeld", "enterCardnumber": "<PERSON><PERSON><PERSON> ka<PERSON>nummer in", "error.connectivity_error.subtitle": "Controleer je internetverbinding en probeer het opnieuw.", "error.connectivity_error.title": "<PERSON>n internetverbinding", "error.decrypt_incorrect_password.title": "Onju<PERSON> wachtwoord", "error.encrypted_object_invalid_format.title": "Beschadigde gegevens", "error.failed_to_fetch_google_auth_token.title": "We kregen geen toegang", "error.list.item.cta.action": "Opnieuw proberen", "error.trezor_action_cancelled.title": "Transactie afgewezen", "error.trezor_device_used_elsewhere.title": "Apparaat wordt in een andere sessie gebruikt", "error.trezor_method_cancelled.title": "<PERSON>n T<PERSON> niet synchroniseren", "error.trezor_permissions_not_granted.title": "<PERSON>n T<PERSON> niet synchroniseren", "error.trezor_pin_cancelled.title": "<PERSON>n T<PERSON> niet synchroniseren", "error.trezor_popup_closed.title": "<PERSON>n T<PERSON> niet synchroniseren", "error.unblock_account_number_and_sort_code_mismatch": "Rekeningnummer en sort code komen niet overeen", "error.unblock_can_not_change_details_after_kyc": "Na KYC kun je gegevens niet meer wijzigen", "error.unblock_hard_kyc_failure": "Onverwachte KYC-status", "error.unblock_invalid_faster_payment_configuration.title": "Deze bank ondersteunt geen Faster Payments", "error.unblock_invalid_iban": "Ongeldige IBAN", "error.unblock_session_expired.title": "Unblock-se<PERSON> verlopen", "error.unblock_user_with_address_already_exists.title": "Account al ingesteld voor dit adres", "error.unblock_user_with_such_email_already_exists.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> met dit e-mailadres bestaat al", "error.unknown_error.error_message": "Foutmelding: ", "error.unknown_error.subtitle": "Sorry! <PERSON><PERSON>m voor dringende hulp contact op met support en deel de onderstaande details.", "error.unknown_error.title": "Systeemfout", "eth-cost-warning-modal.subtitle": "Smart Wallets werken op Ethereum, maar de kosten zijn erg hoog. We raden STERK aan om andere netwerken te gebruiken.", "eth-cost-warning-modal.title": "Vermijd Ethereum - netwerkkosten zijn hoog", "exchange.form.button.chain_unsupported": "Chain niet ondersteund", "exchange.form.button.refreshing": "<PERSON><PERSON><PERSON><PERSON>", "exchange.form.error.asset_not_supported.button": "Kies een ander activum", "exchange.form.error.asset_not_supported.description": "Bridge ondersteunt deze asset niet.", "exchange.form.error.asset_not_supported.title": "<PERSON><PERSON> niet on<PERSON>teund", "exchange.form.error.bridge_quote_timeout.button": "Kies een ander activum", "exchange.form.error.bridge_quote_timeout.description": "<PERSON><PERSON><PERSON> een ander <PERSON>ar", "exchange.form.error.bridge_quote_timeout.title": "<PERSON><PERSON> w<PERSON>lk<PERSON>rs gevonden", "exchange.form.error.different_receiver_not_supported.button": "<PERSON><PERSON><PERSON><PERSON><PERSON> and<PERSON> on<PERSON>", "exchange.form.error.different_receiver_not_supported.description": "Deze exchange ondersteunt verzenden naar een ander adres niet.", "exchange.form.error.different_receiver_not_supported.title": "Verzend- en ontvangstadres moeten gelijk zijn", "exchange.form.error.insufficient_input_amount.button": "Verhoog bedrag", "exchange.form.error.insufficient_liquidity.button": "Verlaag bedrag", "exchange.form.error.insufficient_liquidity.description": "De bridge heeft onvoldoende assets. Probeer een lager bedrag.", "exchange.form.error.insufficient_liquidity.title": "Bedrag te hoog", "exchange.form.error.max_amount_exceeded.button": "Verlaag bedrag", "exchange.form.error.max_amount_exceeded.description": "Het maximale bedrag is overschreden.", "exchange.form.error.max_amount_exceeded.title": "Bedrag te hoog", "exchange.form.error.min_amount_not_met.button": "Verhoog bedrag", "exchange.form.error.min_amount_not_met.description": "Het minimale wisselbedrag voor dit token is niet bereikt.", "exchange.form.error.min_amount_not_met.description_with_amount": "Het minimale wisselbedrag is {amount}.", "exchange.form.error.min_amount_not_met.title": "Bedrag te laag", "exchange.form.error.min_amount_not_met.title_increase": "Verhoog bedrag", "exchange.form.error.no_routes_found.button": "Kies een ander activum", "exchange.form.error.no_routes_found.description": "Er is geen wisselroute beschikbaar voor deze token/netwerk-combinatie.", "exchange.form.error.no_routes_found.title": "<PERSON>n wisselroute beschik<PERSON>ar", "exchange.form.error.not_enough_balance.button": "Verlaag bedrag", "exchange.form.error.not_enough_balance.description": "Je hebt onvoldo<PERSON>e saldo van deze asset voor de transactie.", "exchange.form.error.not_enough_balance.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "exchange.form.error.slippage_passed_is_too_low.button": "Verhoog slippage", "exchange.form.error.slippage_passed_is_too_low.description": "Toegestane slippage is te laag voor deze asset.", "exchange.form.error.slippage_passed_is_too_low.title": "Slippage te laag", "exchange.form.error.socket_internal_error.button": "<PERSON><PERSON><PERSON> het later opnieuw", "exchange.form.error.socket_internal_error.description": "<PERSON><PERSON> bridge-partner he<PERSON>t problemen. <PERSON><PERSON><PERSON> het later opnieuw.", "exchange.form.error.socket_internal_error.title": "Fout bij bridge-partner", "exchange.form.error.stargatev2_requires_fee_in_native": "Voeg toe {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Voeg {amount} toe om de transactie te voltooien", "exchange.form.error.stargatev2_requires_fee_in_native.title": "<PERSON><PERSON>  nodig {symbol}", "expiration-info.modal.description": "De vervaltijd bepaalt hoe lang een app je tokens kan gebruiken. <PERSON><PERSON><PERSON> vervalt de toegang, tenzij je opnieuw toestemming geeft. Ho<PERSON> voor de veiligheid de vervaltijd kort.", "expiration-info.modal.title": "Wat is vervaltijd?", "expiration-time.high.modal.text": "Vervaltijden moeten kort zijn, afgestemd op hoe lang je het nodig hebt. Lange tijden zijn riskant en geven oplichters meer kans om je tokens te misbruiken.", "expiration-time.high.modal.title": "<PERSON> ve<PERSON>", "failed.transaction.content": "Transactie mislukt waarschijnlijk", "fee.unknown": "Onbekend", "feedback-request.leave-message": "Bericht achterlaten", "feedback-request.not-now": "Niet nu", "feedback-request.title": "Bedankt! Hoe kunnen we Zeal verbeteren?", "float.input.period": "Decimaal scheidingsteken", "gnosis-activate-card.info-popup.subtitle": "Eerste betaling: steek kaart in en gebruik pincode. Daarna werkt contactloos.", "gnosis-activate-card.info-popup.title": "Eerste betaling vereist chip & pincode", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "<PERSON><PERSON>r je kaartnummer in om te activeren.", "gnosis-activate-card.title": "Kaartnummer", "gnosis-pay-re-kyc-widget.btn-text": "Verifiëren", "gnosis-pay-re-kyc-widget.title.not-started": "Verifieer je identiteit", "gnosis-pay.login.cta": "Bestaand account koppelen", "gnosis-pay.login.title": "Je hebt al een Gnosis Pay-account", "gnosis-signup.confirm.subtitle": "<PERSON><PERSON> naar een e-mail van Gnosis Pay, deze kan in je spamfolder zitten.", "gnosis-signup.confirm.title": "Geen verificatie-e-mail ontvangen?", "gnosis-signup.continue": "Ga verder", "gnosis-signup.dont_link_accounts": "<PERSON><PERSON> k<PERSON>en", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "<PERSON><PERSON><PERSON> <EMAIL> in", "gnosis-signup.enter-email.title": "Voer e-mailadres in", "gnosis-signup.title": "Ik ga ak<PERSON><PERSON> met de Gnosis Pay <linkGnosisTNC>Voorwaarden</linkGnosisTNC> <monovateTerms>Kaarthoudervoorwaarden</monovateTerms> en <linkMonerium>Monerium's VW</linkMonerium>.", "gnosis-signup.verify-email.title": "Verifieer e-mailadres", "gnosis.confirm.subtitle": "Geen code ontvangen? Controleer of je telefoonnummer klopt", "gnosis.confirm.title": "Code verzonden naar {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Verifiëren", "gnosisPayAccountStatus.success.title": "<PERSON><PERSON>", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay is nog niet besch<PERSON> in jouw land", "gnosisPayNoActiveCardsFound.title": "<PERSON><PERSON> actieve ka<PERSON>n", "gnosis_pay_card_delay_relay_not_empty_error.title": "Je transactie kon nu niet verwerkt worden. <PERSON><PERSON><PERSON> het later opnieuw", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "<PERSON><PERSON> niet mogel<PERSON>jk", "gnosiskyc.modal.approved.activate-free-card": "<PERSON>er gratis kaart", "gnosiskyc.modal.approved.button-text": "<PERSON><PERSON><PERSON> van bankrek<PERSON>", "gnosiskyc.modal.approved.title": "Je persoonlijke account is aangemaakt", "gnosiskyc.modal.failed.close": "Sluiten", "gnosiskyc.modal.failed.title": "Sorry, onze partner Gnosis Pay kan geen account voor je aanmaken", "gnosiskyc.modal.in-progress.title": "ID-check duurt soms 24+ uur. Even geduld.", "goToSettingsPopup.settings": "Instellingen", "goToSettingsPopup.title": "<PERSON><PERSON><PERSON> meldingen in je instellingen in", "google_file.error.failed_to_fetch_auth_token.button_title": "<PERSON><PERSON><PERSON> opnieuw", "google_file.error.failed_to_fetch_auth_token.subtitle": "<PERSON><PERSON> toegang tot je persoonlijke cloud, zodat we je herstelbestand kunnen gebruiken.", "google_file.error.failed_to_fetch_auth_token.title": "We kregen geen toegang", "hidden_tokens.widget.emptyState": "Geen verborgen tokens", "how_to_connect_to_metamask.got_it": "<PERSON><PERSON>, begrepen", "how_to_connect_to_metamask.story.subtitle": "Wissel op elk moment eenvoudig tussen Zeal en andere wallets.", "how_to_connect_to_metamask.story.title": "Zeal werkt naast andere wallets", "how_to_connect_to_metamask.why_switch": "Waarom wisselen tussen Zeal en andere wallets?", "how_to_connect_to_metamask.why_switch.description": "<PERSON>lke portemonnee je ook kiest, je krijgt altijd de veiligheidscontroles van Zeal die je beschermen tegen schadelijke sites en transacties.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "We weten dat het lastig is om de overstap te maken naar een nieuwe portemonnee. Daarom hebben we het makkelijk gemaakt om Zeal naast je huidige portemonnee te gebruiken. Wisse<PERSON> wanneer je wilt.", "import-bank-transfer-owner.banner.title": "Importeer je wallet om bankoverschrijvingen te blijven gebruiken.", "import-bank-transfer-owner.title": "Importeer wallet voor bank overboeken", "import_gnosispay_wallet.add-another-card-owner.footnote": "Importeer priv<PERSON><PERSON><PERSON>ls of seed phrase van de eigenaar van je G<PERSON>-<PERSON>art", "import_gnosispay_wallet.primaryText": "Importeer Gnosis Pay-wallet", "injected-wallet": "Browserportemonnee", "intercom.getHelp": "<PERSON><PERSON><PERSON> vragen", "invalid_iban.got_it": "<PERSON><PERSON>", "invalid_iban.subtitle": "De ingevoerde IBAN is niet geldig. Controleer of de gegevens juist zijn en probeer het opnieuw.", "invalid_iban.title": "Ongeldige IBAN", "keypad-0": "Toetsenblok toets 0", "keypad-1": "Toetsenblok toets 1", "keypad-2": "Toetsenblok toets 2", "keypad-3": "Toetsenblok toets 3", "keypad-4": "Toetsenblok toets 4", "keypad-5": "Toetsenblok toets 5", "keypad-6": "Toetsenblok toets 6", "keypad-7": "Toetsenblok toets 7", "keypad-8": "Toetsenblok toets 8", "keypad-9": "Toetsenblok toets 9", "keypad.biometric-button": "Biometrische knop op toetsenbord", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Geheime zin beveiligd 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "Bekijk zin", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "<PERSON><PERSON><PERSON> een veil<PERSON>, offline kopie van je geheime zin zodat je later je tegoeden kunt herstellen", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "<PERSON><PERSON>r het woord niet te raden", "keystore.write_secret_phrase.before_you_begin.first_point": "<PERSON><PERSON> beg<PERSON><PERSON><PERSON> dat <PERSON><PERSON><PERSON> met mijn geheime zin mijn tegoeden kan overboeken", "keystore.write_secret_phrase.before_you_begin.second_point": "<PERSON><PERSON> ben zelf verantwoordelijk voor het geheim en veilig houden van mijn geheime zin", "keystore.write_secret_phrase.before_you_begin.subtitle": "Lees en accepteer de volgende punten:", "keystore.write_secret_phrase.before_you_begin.third_point": "<PERSON>k ben op een priv<PERSON><PERSON><PERSON> zonder and<PERSON> mensen of camera's om me heen", "keystore.write_secret_phrase.before_you_begin.title": "Voor<PERSON>t je begint", "keystore.write_secret_phrase.secret_phrase_test.title": "Wat is woord {count} in je geheime zin?", "keystore.write_secret_phrase.test_ps.lets_do_it": "Laten we beginnen", "keystore.write_secret_phrase.test_ps.subtitle": "Je hebt je geheime zin nodig om je account op dit of andere apparaten te herstellen. We testen nu of je de zin correct hebt opgeschreven.", "keystore.write_secret_phrase.test_ps.subtitle2": "We vragen je om {count} woorden uit je zin.", "keystore.write_secret_phrase.test_ps.title": "Account hers<PERSON>len testen", "kyc.modal.approved.button-text": "Bankoverboeking doen", "kyc.modal.approved.subtitle": "Je verificatie is voltooid. Je kunt nu onbeperkt bankoverboekingen doen.", "kyc.modal.approved.title": "Bankoverboekingen ontgrendeld", "kyc.modal.continue-with-partner.button-text": "Doorgaan", "kyc.modal.continue-with-partner.subtitle": "We sturen je nu door naar onze partner om je documentatie te verzamelen en de verificatie af te ronden.", "kyc.modal.continue-with-partner.title": "<PERSON>a verder bij onze partner", "kyc.modal.failed.unblock.subtitle": "Unblock heeft je identiteitsverificatie niet goedgekeurd en kan je geen bankoverboekingen aanbieden", "kyc.modal.failed.unblock.title": "Unblock-a<PERSON><PERSON><PERSON><PERSON> niet goedge<PERSON>d", "kyc.modal.paused.button-text": "Gegevens bijwerken", "kyc.modal.paused.subtitle": "Het lijkt erop dat sommige van je gegevens onjuist zijn. Probeer het opnieuw en controleer je gegevens voordat je ze indient.", "kyc.modal.paused.title": "<PERSON> gegevens lijken onjuist", "kyc.modal.pending.button-text": "Sluiten", "kyc.modal.pending.subtitle": "Verificatie duurt normaal gesproken minder dan 10 minuten, maar kan soms iets langer duren.", "kyc.modal.pending.title": "We houden je op de hoogte", "kyc.modal.required.cta": "Start verificatie", "kyc.modal.required.subtitle": "Je hebt de transactielimiet bereikt. Verifieer je identiteit om verder te gaan. Dit duurt meestal een paar minuten en vereist enkele persoonlijke gegevens en documenten.", "kyc.modal.required.title": "Identiteitsverificatie vereist", "kyc.submitted": "Aanvraag ingediend", "kyc.submitted_short": "Ingediend", "kyc_status.completed_status": "Voltooid", "kyc_status.failed_status": "Mislukt", "kyc_status.paused_status": "Beoordeling", "kyc_status.subtitle": "Bankoverboekingen", "kyc_status.subtitle.wrong_details": "<PERSON><PERSON><PERSON><PERSON>", "kyc_status.subtitle_in_progress": "Wordt verwerkt", "kyc_status.title": "Identiteit verifiëren", "label.close": "Sluiten", "label.saving": "Opslaan...", "labels.this-month": "Deze maand", "labels.today": "Vandaag", "labels.yesterday": "Gisteren", "language.selector.title": "Taal", "ledger.account_loaded.imported": "Geïmporteerd", "ledger.add.success.title": "Ledger succesvol verbonden 🎉", "ledger.connect.cta": "Ledger synchroniseren", "ledger.connect.step1": "<PERSON><PERSON><PERSON><PERSON> met je apparaat", "ledger.connect.step2": "Open de Ethereum-app op je Ledger", "ledger.connect.step3": "Synchroniseer dan je Ledger 👇", "ledger.connect.subtitle": "Volg deze stappen om je Ledger-wallets te importeren in Zeal", "ledger.connect.title": "<PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON>", "ledger.error.ledger_is_locked.subtitle": "Ontgrendel Ledger en open de Ethereum-app", "ledger.error.ledger_is_locked.title": "Ledger is vergrendeld", "ledger.error.ledger_not_connected.action": "Synchroniseer Ledger", "ledger.error.ledger_not_connected.subtitle": "Verbind je hardware wallet met je apparaat en open de Ethereum-app", "ledger.error.ledger_not_connected.title": "Ledger is niet verbonden", "ledger.error.ledger_running_non_eth_app.title": "Ethereum-app niet geopend", "ledger.error.user_trx_denied_by_user.action": "Sluiten", "ledger.error.user_trx_denied_by_user.subtitle": "Je hebt de transactie op je hardware wallet afgewezen", "ledger.error.user_trx_denied_by_user.title": "Transactie afgewezen", "ledger.hd_path.bip44.subtitle": "bijv. <PERSON>, Trezor", "ledger.hd_path.bip44.title": "BIP<PERSON>", "ledger.hd_path.ledger_live.subtitle": "Standaard", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Legacy", "ledger.hd_path.phantom.subtitle": "bijv. Phantom", "ledger.select.hd_path.subtitle": "HD-paden zijn de manier waarop hardware-portemonnees hun accounts sorteren. <PERSON><PERSON> is <PERSON><PERSON><PERSON><PERSON><PERSON> met hoe een index pagina's in een boek sorteert.", "ledger.select.hd_path.title": "Selecteer HD-pad", "ledger.select_account.import_wallets_count": "{count,plural,=0{<PERSON><PERSON> wallets geselecteerd} one{Importeer wallet} other{Importeer {count} wallets}}", "ledger.select_account.path_settings": "Pad-instellingen", "ledger.select_account.subtitle": "Zie je niet de portemonnees die je verwacht? <PERSON><PERSON>r de pad-instellingen te wijzigen.", "ledger.select_account.subtitle.group_header": "Portemonnees", "ledger.select_account.title": "Ledger-portemonnees importeren", "legend.lending-operations": "Uitleenactiviteiten", "legend.market_making-operations": "Marketmaking-activiteiten", "legend.treasury-operations": "Treasury-activiteiten", "link-existing-monerium-account-sign.button": "<PERSON><PERSON>", "link-existing-monerium-account-sign.subtitle": "Je hebt al een Monerium-account.", "link-existing-monerium-account-sign.title": "<PERSON><PERSON> Z<PERSON> aan je bestaande Monerium-account", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Je hebt al een Monerium-account. Ga naar de Monerium-app om de installatie te voltooien.", "link-existing-monerium-account.title": "Ga naar Monerium om je account te koppelen", "loading.pin": "Pincode laden...", "lockScreen.passwordIncorrectMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> is onjuist", "lockScreen.passwordRequiredMessage": "Wachtwoord vereist", "lockScreen.unlock.header": "Ontgrendelen", "lockScreen.unlock.subheader": "Gebruik je wachtwoord om Z<PERSON> te ontgrendelen", "mainTabs.activity.label": "Activiteit", "mainTabs.browse.label": "Ontdek", "mainTabs.browse.title": "Ontdek", "mainTabs.card.label": "<PERSON><PERSON>", "mainTabs.portfolio.label": "Portfolio", "mainTabs.rewards.label": "Beloningen", "makeSpendable.cta": "<PERSON><PERSON>", "makeSpendable.holdAsCash": "<PERSON><PERSON><PERSON> als saldo", "makeSpendable.shortText": "Verdien {apy} per jaar", "makeSpendable.title": "{amount} ontvangen", "merchantCategory.agriculture": "Landbouw", "merchantCategory.alcohol": "Alcohol", "merchantCategory.antiques": "<PERSON><PERSON>", "merchantCategory.appliances": "Huishoudelijke apparaten", "merchantCategory.artGalleries": "Kunstgalerijen", "merchantCategory.autoRepair": "Autoreparatie", "merchantCategory.autoRepairService": "Autoreparatiedienst", "merchantCategory.beautyFitnessSpas": "Schoonheid, Fitness & Spa's", "merchantCategory.beautyPersonalCare": "Schoonheid & Persoonlijke verzorging", "merchantCategory.billiard": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.books": "<PERSON><PERSON><PERSON>", "merchantCategory.bowling": "<PERSON><PERSON>", "merchantCategory.businessProfessionalServices": "Zakelijke & Professionele diensten", "merchantCategory.carRental": "Autoverhuur", "merchantCategory.carWash": "Autowasstraat", "merchantCategory.cars": "Auto's", "merchantCategory.casino": "Casino", "merchantCategory.casinoGambling": "Casino & Gokken", "merchantCategory.cellular": "Mobiele telefonie", "merchantCategory.charity": "<PERSON><PERSON>n", "merchantCategory.childcare": "Kinderopvang", "merchantCategory.cigarette": "Sigaretten", "merchantCategory.cinema": "Bioscoop", "merchantCategory.cinemaEvents": "Bioscoop & Evenementen", "merchantCategory.cleaning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.cleaningMaintenance": "Schoonmaak & Onderhoud", "merchantCategory.clothes": "<PERSON><PERSON><PERSON>", "merchantCategory.clothingServices": "Kledingdiensten", "merchantCategory.communicationServices": "Communicatiediensten", "merchantCategory.construction": "<PERSON><PERSON><PERSON>", "merchantCategory.cosmetics": "Cosmetica", "merchantCategory.craftsArtSupplies": "Hobby- & Kunstbenodigdheden", "merchantCategory.datingServices": "Datingdiensten", "merchantCategory.delivery": "Bezorging", "merchantCategory.dentist": "Tandarts", "merchantCategory.departmentStores": "Warenhuizen", "merchantCategory.directMarketingSubscription": "Direct Marketing & Abonnement", "merchantCategory.discountStores": "Discountwin<PERSON>s", "merchantCategory.drugs": "Medicijn<PERSON>", "merchantCategory.dutyFree": "Duty Free", "merchantCategory.education": "Onderwi<PERSON><PERSON>", "merchantCategory.electricity": "Elektriciteit", "merchantCategory.electronics": "Elektronica", "merchantCategory.emergencyServices": "Hu<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.equipmentRental": "<PERSON><PERSON><PERSON><PERSON> apparatuur", "merchantCategory.evCharging": "EV opladen", "merchantCategory.financialInstitutions": "Financiële instellingen", "merchantCategory.financialProfessionalServices": "Financiële & Professionele Diensten", "merchantCategory.finesPenalties": "Boetes & Sancties", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "Vluchten", "merchantCategory.flowers": "Bloemen", "merchantCategory.flowersGarden": "Bloemen & Tuin", "merchantCategory.food": "Eten & Drinken", "merchantCategory.freight": "Vrachtvervoer", "merchantCategory.fuel": "<PERSON><PERSON><PERSON>", "merchantCategory.funeralServices": "Uitvaartdiensten", "merchantCategory.furniture": "<PERSON><PERSON><PERSON>", "merchantCategory.games": "Games", "merchantCategory.gas": "Benzine", "merchantCategory.generalMerchandiseRetail": "Algemene Handelswaar & Retail", "merchantCategory.gifts": "<PERSON><PERSON>", "merchantCategory.government": "<PERSON><PERSON><PERSON>", "merchantCategory.governmentServices": "Overheidsdiensten", "merchantCategory.hardware": "Bouwmarkt", "merchantCategory.healthMedicine": "Gezondheid & Geneeskunde", "merchantCategory.homeImprovement": "<PERSON><PERSON><PERSON>", "merchantCategory.homeServices": "<PERSON><PERSON><PERSON> aan huis", "merchantCategory.hotel": "Hotel", "merchantCategory.housing": "Huisvesting", "merchantCategory.insurance": "Verzekering", "merchantCategory.internet": "Internet", "merchantCategory.kids": "Kinderen", "merchantCategory.laundry": "<PERSON><PERSON><PERSON>", "merchantCategory.laundryCleaningServices": "Wasserij & Schoonmaakdiensten", "merchantCategory.legalGovernmentFees": "Juridische & Overheidskosten", "merchantCategory.luxuries": "Luxeartikelen", "merchantCategory.luxuriesCollectibles": "Luxe & Verzamelobjecten", "merchantCategory.magazines": "Tijdschriften", "merchantCategory.magazinesNews": "Tijdschriften & Nieuws", "merchantCategory.marketplaces": "Marktplaatsen", "merchantCategory.media": "Media", "merchantCategory.medicine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.mobileHomes": "Stacaravans", "merchantCategory.moneyTransferCrypto": "Geldoverboekingen & Crypto", "merchantCategory.musicRelated": "Muziek", "merchantCategory.musicalInstruments": "Muziekinstrumenten", "merchantCategory.optics": "Opticien", "merchantCategory.organizationsClubs": "Organisaties & Clubs", "merchantCategory.other": "<PERSON><PERSON>", "merchantCategory.parking": "<PERSON><PERSON>", "merchantCategory.pawnShops": "Pandjeshuizen", "merchantCategory.pets": "Huisdieren", "merchantCategory.photoServicesSupplies": "Fotodiensten & -benodigdheden", "merchantCategory.postalServices": "Postdiensten", "merchantCategory.professionalServicesOther": "Profess<PERSON><PERSON> (Overig)", "merchantCategory.publicTransport": "<PERSON><PERSON><PERSON> vervo<PERSON>", "merchantCategory.purchases": "Aankopen", "merchantCategory.purchasesMiscServices": "Aankopen & Diverse Diensten", "merchantCategory.recreationServices": "Recreatied<PERSON>ten", "merchantCategory.religiousGoods": "Religieuze Artikelen", "merchantCategory.secondhandRetail": "Tweedehands Winkels", "merchantCategory.shoeHatRepair": "Schoen- & Hoedenreparatie", "merchantCategory.shoeRepair": "Schoenreparatie", "merchantCategory.softwareApps": "Software & Apps", "merchantCategory.specializedRepairs": "Gespecialiseerde <PERSON>", "merchantCategory.sport": "Sport", "merchantCategory.sportingGoods": "Sportartikelen", "merchantCategory.sportingGoodsRecreation": "Sportartikelen & Recreatie", "merchantCategory.sportsClubsFields": "Sportclubs & -velden", "merchantCategory.stationaryPrinting": "Kantoorartikelen & Drukwerk", "merchantCategory.stationery": "Kantoorartikelen", "merchantCategory.storage": "Opslag", "merchantCategory.taxes": "Belastingen", "merchantCategory.taxi": "Taxi", "merchantCategory.telecomEquipment": "Telecomapparatuur", "merchantCategory.telephony": "Telefonie", "merchantCategory.tobacco": "Tabak", "merchantCategory.tollRoad": "Tolweg", "merchantCategory.tourismAttractionsAmusement": "Toerisme, Attracties & Vermaak", "merchantCategory.towing": "Sleepdiensten", "merchantCategory.toys": "Speelgoed", "merchantCategory.toysHobbies": "Speelgoed & Hobby's", "merchantCategory.trafficFine": "Verkeersboete", "merchantCategory.train": "<PERSON><PERSON><PERSON>", "merchantCategory.travelAgency": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.tv": "Tv", "merchantCategory.tvRadioStreaming": "Tv, Radio & Streaming", "merchantCategory.utilities": "Nutsvoorzieningen", "merchantCategory.waterTransport": "Vervoer over water", "merchantCategory.wholesaleClubs": "Groothandelsclubs", "metaMask.subtitle": "<PERSON><PERSON><PERSON> MetaMask-modus in om alle MetaMask-verbindingen om te leiden naar Zeal. <PERSON><PERSON> je in dApps op MetaMask klikt, verbind je met <PERSON><PERSON>.", "metaMask.title": "Lu<PERSON> verbinden met <PERSON><PERSON> niet?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Open je bankieren-app", "monerium-bank-deposit.buttet-point.receive-crypto": "Ontvang digitale EUR", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Stuur {fiatCurrencyCode} naar je account", "monerium-bank-deposit.deposit-account-country": "Land", "monerium-bank-deposit.header": "{fullName}''s perso<PERSON><PERSON><PERSON><PERSON> account", "monerium-bank-details.account-name": "<PERSON><PERSON>", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT gekopieerd", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT gekopieerd", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN gekopieerd", "monerium-bank-details.to-wallet": "Naar wallet", "monerium-bank-details.transfer-fee": "Overboekingskosten", "monerium-bank-transfer.enable-card.bullet-1": "Voltooi identiteitsverificatie", "monerium-bank-transfer.enable-card.bullet-2": "Ontvang persoonlijke accountgegevens", "monerium-bank-transfer.enable-card.bullet-3": "<PERSON>ort vanaf je bankrekening", "monerium-card-delay-relay.success.cta": "Sluiten", "monerium-card-delay-relay.success.subtitle": "Om veiligheidsredenen duurt het 3 minuten om kaartinstellingen te verwerken.", "monerium-card-delay-relay.success.title": "Kom over 3 min terug om de Monerium-installatie te voltooien", "monerium-deposit.account-details-info-popup.bullet-point-1": "Elke {fiatCurrencyCode} die je naar deze rekening stuurt, wordt automatisch omgezet in {cryptoCurrencyCode} tokens op {cryptoCurrencyChain} Chain en naar je wallet verzonden", "monerium-deposit.account-details-info-popup.bullet-point-2": "STUUR ALLEEN {fiatCurrencyCode} ({fiatCurrencySymbol}) naar je account", "monerium-deposit.account-details-info-popup.title": "<PERSON>", "monerium.check_order_status.sending": "Verzenden", "monerium.not-eligible.cta": "Terug", "monerium.not-eligible.subtitle": "Monerium kan geen account voor je openen. Kies een andere provider.", "monerium.not-eligible.title": "Probeer een andere provider", "monerium.setup-card.cancel": "<PERSON><PERSON><PERSON>", "monerium.setup-card.continue": "Doorgaan", "monerium.setup-card.create_account": "Account aan<PERSON>ken", "monerium.setup-card.login": "Inloggen bij Gnosis Pay", "monerium.setup-card.subtitle": "Maak een Gnosis Pay-account aan of log in om directe bankstortingen mogelijk te maken.", "monerium.setup-card.subtitle_personal_account": "Open in minuten je Gnosis Pay-account:", "monerium.setup-card.title": "Bankstortingen inschakelen", "moneriumDepositSuccess.goToWallet": "<PERSON><PERSON>", "moneriumDepositSuccess.title": "{symbol} ontvangen", "moneriumInfo.fees": "Je betaalt 0% kosten", "moneriumInfo.registration": "Monerium is geautoriseerd en gereguleerd als een Elektronische Geldinstelling onder de IJslandse wet op elektronisch geld nr. 17/2013 <link>Meer informatie</link>", "moneriumInfo.selfCustody": "Het digitale geld dat je ontvangt, staat in je privé portemonnee. <PERSON><PERSON><PERSON> anders kan erbij.", "moneriumWithdrawRejected.supportText": "We konden je overboeking niet voltooien. <PERSON>beer het opnieuw en als het nog steeds niet werkt, <link>neem contact op met support.</link>", "moneriumWithdrawRejected.title": "Overboeking teruggedraaid", "moneriumWithdrawRejected.tryAgain": "<PERSON><PERSON><PERSON> opnieuw", "moneriumWithdrawSuccess.supportText": "Het kan 24 uur duren voordat je {br}ontvanger het geld ontvangt", "moneriumWithdrawSuccess.title": "Verzonden", "monerium_enable_banner.text": "Activeer bankoverboekingen nu", "monerium_error_address_re_link_required.title": "Wallet moet opnieuw worden gekoppeld aan Monerium", "monerium_error_duplicate_order.title": "Dubbele order", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "<PERSON><PERSON><PERSON><PERSON>", "mt-pelerin-fork.options.chf.short": "Direct & gratis met Mt Pelerin", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Direct & gratis met Monerium", "mt-pelerin-fork.title": "Wat wil je storten?", "mtPelerinProviderInfo.fees": "Je betaalt 0% kosten", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd is aangesloten bij SO-FIT, een zelfregulerende organisatie die is erkend door de Zwitserse financiële autoriteit (FINMA) onder de antiwitwaswet. <link>Meer informatie</link>", "mtPelerinProviderInfo.selfCustody": "Het digitale geld dat je ontvangt, beheer je zelf en niemand anders heeft controle over je tegoeden.", "network-fee-widget.title": "<PERSON><PERSON>", "network.edit.verifying_rpc": "RPC verifiëren", "network.editRpc.predefined_network_info.subtitle": "Net als een VPN gebruikt Zeal RPC's die voorkomen dat je persoonlijke gegevens worden gevolgd.{br}{br}De standaard RPC's van Zeal zijn betrouwbare, in de praktijk geteste RPC-providers.", "network.editRpc.predefined_network_info.title": "Zeal privacy-RPC", "network.filter.update_rpc_success": "RPC-node opgeslagen", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "Alle netwerken", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "Netwerken toevoegen", "networks.filter.add_modal.chain_list.subtitle": "Voeg alle EVM-netwerken toe", "networks.filter.add_modal.chain_list.title": "Ga naar Chainlist", "networks.filter.add_modal.dapp_tip.subtitle": "<PERSON><PERSON><PERSON> in je favoriete dApps over naar het gewenste EVM-netwerk. Zeal vraagt dan of je het wilt toevoegen aan je portemonnee.", "networks.filter.add_modal.dapp_tip.title": "Of voeg een netwerk toe vanuit een dApp", "networks.filter.add_networks.subtitle": "Alle EVM-netwerken ondersteund", "networks.filter.add_networks.title": "Netwerken toevoegen", "networks.filter.add_test_networks.title": "<PERSON><PERSON><PERSON>", "networks.filter.tab.netwokrs": "Netwerken", "networks.filter.testnets.title": "<PERSON><PERSON><PERSON>", "nft.widget.emptystate": "<PERSON><PERSON> verz<PERSON> in portemonnee", "nft_collection.change_account_picture.subtitle": "Weet je zeker dat je je profielfoto wilt bijwerken?", "nft_collection.change_account_picture.title": "Profielfoto bijwerken naar NFT", "nfts.allNfts.pricingPopup.description": "Prij<PERSON> van verzamelobjecten zijn geb<PERSON>erd op de meest recent verhandelde prijs.", "nfts.allNfts.pricingPopup.title": "<PERSON><PERSON><PERSON><PERSON> van verzamelobjecten", "no-passkeys-found.modal.cta": "Sluiten", "no-passkeys-found.modal.subtitle": "We kunnen geen Zeal-passkeys op dit apparaat vinden. <PERSON>org ervoor dat je bent aangemeld bij het cloudaccount waarmee je je Smart Wallet hebt gemaakt.", "no-passkeys-found.modal.title": "<PERSON>n passkeys gevonden", "notValidEmail.title": "<PERSON><PERSON> geldig e-mailadres", "notValidPhone.title": "Dit is geen geldig telefoonnummer", "notification-settings.title": "Meldingsinstellingen", "notification-settings.toggles.active-wallets": "<PERSON><PERSON><PERSON>", "notification-settings.toggles.bank-transfers": "Bankoverboekingen", "notification-settings.toggles.card-payments": "<PERSON><PERSON><PERSON>aling<PERSON>", "notification-settings.toggles.readonly-wallets": "Volgportemonnees", "ntft.groupHeader.text": "Verzamelobjecten", "on_ramp.crypto_completed": "Voltooid", "on_ramp.fiat_completed": "Voltooid", "onboarding-widget.subtitle.card_created_from_order.left": "Visa-kaart", "onboarding-widget.subtitle.card_created_from_order.right": "<PERSON><PERSON>", "onboarding-widget.subtitle.card_order_ready.left": "Fysieke Visa-kaart", "onboarding-widget.subtitle.default": "Bankoverboekingen & Visa-kaart", "onboarding-widget.title.card-order-in-progress": "Kaartbestelling afronden", "onboarding-widget.title.card_created_from_order": "<PERSON><PERSON> is verzonden", "onboarding-widget.title.kyc_approved": "Overboekingen & kaart gereed", "onboarding-widget.title.kyc_failed": "Account niet m<PERSON>jk", "onboarding-widget.title.kyc_not_started": "Installatie afronden", "onboarding-widget.title.kyc_started_documents_requested": "Verificatie voltooien", "onboarding-widget.title.kyc_started_resubmission_requested": "Verificatie opnieuw proberen", "onboarding-widget.title.kyc_started_verification_in_progress": "Identiteit verifiëren", "onboarding.loginOrCreateAccount.amountOfAssets": "$10+ mld. aan tegoeden", "onboarding.loginOrCreateAccount.cards.subtitle": "<PERSON><PERSON> in bepaalde regio's. Door verder te gaan, accepteer je onze <Terms>Voorwaarden</Terms> & <PrivacyPolicy>Privacybeleid</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Visa-<PERSON><PERSON> met hoog{br}rendement en geen kosten", "onboarding.loginOrCreateAccount.createAccount": "Account aan<PERSON>ken", "onboarding.loginOrCreateAccount.earn.subtitle": "Rendementen variëren; kapitaal loopt risico. Door verder te gaan, accepteer je onze <Terms>Voorwaarden</Terms> & <PrivacyPolicy>Privacybeleid</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "<PERSON>en {percent} per jaar{br}Vertrouwd door {currencySymbol}5+ mld.", "onboarding.loginOrCreateAccount.earningPerYear": "Verdien {percent}{br}per jaar", "onboarding.loginOrCreateAccount.login": "Inloggen", "onboarding.loginOrCreateAccount.trading.subtitle": "Kapitaal loopt risico. Door verder te gaan, accepteer je onze <Terms>Voorwaarden</Terms> & <PrivacyPolicy>Privacybeleid</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Investeer in alles,{br}van BTC tot S&P", "onboarding.loginOrCreateAccount.trustedBy": "Digitale geldmarkten{br}Vertrouwd door {assets}", "onboarding.wallet_stories.close": "Sluiten", "onboarding.wallet_stories.previous": "Vorige", "order-earn-deposit-bridge.deposit": "Storting", "order-earn-deposit-bridge.into": "In", "otpIncorrectMessage": "Bevestigingscode is onjuist", "passkey-creation-not-possible.modal.close": "Sluiten", "passkey-creation-not-possible.modal.subtitle": "We konden geen passkey voor je wallet aanmaken. Zorg dat je apparaat passkeys ondersteunt en probeer het opnieuw. <link>Neem contact op met support</link> als het probleem aanhoudt.", "passkey-creation-not-possible.modal.title": "Kan geen passkey aanmaken", "passkey-not-supported-in-mobile-browser.modal.cta": "Download Zeal", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Smart Wallets worden niet ondersteund in mobiele browsers.", "passkey-not-supported-in-mobile-browser.modal.title": "Download de Zeal-app om door te gaan", "passkey-recovery.recovering.deploy-signer.loading-text": "<PERSON>key verifiëren", "passkey-recovery.recovering.loading-text": "<PERSON><PERSON><PERSON>", "passkey-recovery.recovering.signer-not-found.subtitle": "We konden je passkey niet aan een actieve portemonnee koppelen. Als je geld in je portemonnee hebt, neem dan contact op met het Zeal-team voor ondersteuning.", "passkey-recovery.recovering.signer-not-found.title": "<PERSON><PERSON>", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "<PERSON><PERSON><PERSON> and<PERSON> passkey", "passkey-recovery.select-passkey.banner.subtitle": "Zorg dat je op je apparaat bent ingelogd op het juiste account. Passkeys zijn accountspecifiek.", "passkey-recovery.select-passkey.banner.title": "Zie je de passkey van je portemonnee niet?", "passkey-recovery.select-passkey.continue": "Selecteer passkey", "passkey-recovery.select-passkey.subtitle": "Selecteer de passkey die aan je portemonnee is gekoppeld om weer toegang te krijgen.", "passkey-recovery.select-passkey.title": "Selecteer passkey", "passkey-story_1.subtitle": "Met een <PERSON> betaal je netwerkkosten met de meeste tokens. Je hoeft dus niet de standaardtoken van het netwerk te hebben.", "passkey-story_1.title": "Netwerkkosten? Betaal ze gewoon in de meeste tokens", "passkey-story_2.subtitle": "Gebouwd op de toonaangevende smart contracts van Safe, die meer dan $ 100 miljard beveiligen in meer dan 20 miljoen wallets.", "passkey-story_2.title": "Beveiligd door Safe", "passkey-story_3.subtitle": "Smart Wallets werken op de grote Ethereum-compatibele netwerken. Controleer de ondersteunde netwerken voordat je assets verstuurt.", "passkey-story_3.title": "Ondersteuning voor grote EVM-netwerken", "password.add.header": "Wachtwoord aanmaken", "password.add.includeLowerAndUppercase": "Kleine letters en hoofdletters", "password.add.includesNumberOrSpecialChar": "<PERSON><PERSON> c<PERSON> of symbool", "password.add.info.subtitle": "We sturen je wachtwoord niet naar onze servers en slaan het niet voor je op.", "password.add.info.t_and_c": "Door verder te gaan, accepteer je onze <Terms>Voorwaarden</Terms> & <PrivacyPolicy>Privacybeleid</PrivacyPolicy>", "password.add.info.title": "Je wachtwoord blijft op dit apparaat", "password.add.inputPlaceholder": "Wachtwoord aanmaken", "password.add.shouldContainsMinCharsCheck": "10+ tekens", "password.add.subheader": "Je gebruikt je wachtwoord om Z<PERSON> te ontgrendelen", "password.add.success.title": "Wachtwoord aangemaakt 🔥", "password.confirm.header": "Bevestig wachtwoord", "password.confirm.passwordDidNotMatch": "Wachtwoorden moeten overeenkomen", "password.confirm.subheader": "<PERSON><PERSON>r je wachtwoord nog een keer in", "password.create_pin.subtitle": "Deze toegangscode vergrendelt de Zeal-app", "password.create_pin.title": "Maak je toegangscode aan", "password.enter_pin.title": "<PERSON><PERSON>r toegangscode in", "password.incorrectPin": "<PERSON>ju<PERSON><PERSON>", "password.pin_is_not_same": "Toegangscode komt niet overeen", "password.placeholder.enter": "<PERSON><PERSON><PERSON> wacht<PERSON> in", "password.placeholder.reenter": "<PERSON><PERSON><PERSON> wachtwoord opnieuw in", "password.re_enter_pin.subtitle": "<PERSON><PERSON><PERSON> dezelfde toegangscode nogmaals in", "password.re_enter_pin.title": "Bevestig toegangscode", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "In behandeling", "pending-send.details.pending": "In behandeling", "pending-send.details.processing": "Wordt verwerkt", "permit-info.modal.description": "Permits zijn verzoeken waarmee apps, na ondertekening, namens jou je tokens kunnen verplaatsen, bijvoorbeeld voor een swap.{br}Permits lijken op goedkeuringen, maar het ondertekenen kost je geen netwerkkosten.", "permit-info.modal.title": "Wat zijn Permits?", "permit.edit-expiration": "Wijzig {currency} vervaltijd", "permit.edit-limit": "Bewerk {currency} bestedingslimiet", "permit.edit-modal.expiresIn": "<PERSON><PERSON><PERSON><PERSON> over…", "permit.expiration-warning": "{currency} waarschuwing vervaltijd", "permit.expiration.info": "{currency} vervaltijdinformatie", "permit.expiration.never": "Nooit", "permit.spend-limit.info": "{currency} bestedingslimietinformatie", "permit.spend-limit.warning": "{currency} waarschuwing bestedingslimiet", "phoneNumber.title": "telefoonnummer", "physicalCardOrderFlow.cardOrdered": "<PERSON><PERSON>", "physicalCardOrderFlow.city": "Stad", "physicalCardOrderFlow.orderCard": "<PERSON><PERSON> bestellen", "physicalCardOrderFlow.postcode": "Postcode", "physicalCardOrderFlow.shippingAddress.subtitle": "Hier wordt je kaart naartoe gestuurd", "physicalCardOrderFlow.shippingAddress.title": "Verzendadres", "physicalCardOrderFlow.street": "Straat", "placeholderDapps.1inch.description": "Wissel via de beste routes", "placeholderDapps.aave.description": "Leen en uitleen tokens", "placeholderDapps.bungee.description": "Bridge netwerken via de beste routes", "placeholderDapps.compound.description": "Leen en uitleen tokens", "placeholderDapps.cowswap.description": "Wissel tegen de beste koers op Gnosis", "placeholderDapps.gnosis-pay.description": "<PERSON><PERSON><PERSON> je <PERSON>-kaart", "placeholderDapps.jumper.description": "Bridge netwerken via de beste routes", "placeholderDapps.lido.description": "Stake ETH voor meer ETH", "placeholderDapps.monerium.description": "eMoney en bankoverboekingen", "placeholderDapps.odos.description": "Wissel via de beste routes", "placeholderDapps.stargate.description": "Bridge of stake voor <14% APY", "placeholderDapps.uniswap.description": "<PERSON><PERSON> exchanges", "pleaseAllowNotifications.cardPayments": "<PERSON><PERSON><PERSON>aling<PERSON>", "pleaseAllowNotifications.customiseInSettings": "Aanpassen in instellingen", "pleaseAllowNotifications.enable": "Inschakelen", "pleaseAllowNotifications.forWalletActivity": "Voor portemonnee-activiteit", "pleaseAllowNotifications.title": "Ontvang <PERSON>-meldingen", "pleaseAllowNotifications.whenReceivingAssets": "<PERSON><PERSON><PERSON> het ontvangen van te<PERSON>n", "portfolio.quick-actions.add_funds": "<PERSON><PERSON><PERSON><PERSON>", "portfolio.quick-actions.buy": "<PERSON><PERSON>", "portfolio.quick-actions.deposit": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.send": "<PERSON><PERSON><PERSON><PERSON>", "portfolio.view.lastRefreshed": "Vernieuwd {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Waardeer je testnet-AVAX op", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Ga naar Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "Waardeer je testnet-BNB op", "portfolio.view.topupTestNet.BscTestnet.secondary": "Ga naar Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Waar<PERSON>er je testnet-SepETH op", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Ga naar Sepolia Faucet", "portfolio.view.topupTestNet.FantomTestnet.primary": "Waardeer je testnet-FTM op", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Ga naar Faucet", "privateKeyConfirmation.banner.subtitle": "Je privésleutel geeft toegang tot je geld. Deel hem nooit.", "privateKeyConfirmation.banner.title": "<PERSON><PERSON><PERSON><PERSON> risico's", "privateKeyConfirmation.title": "DEEL NOOIT je privésleutel met anderen", "rating-request.not-now": "Niet nu", "rating-request.title": "Zou je <PERSON> a<PERSON>?", "receive_funds.address-text": "<PERSON>t is je unieke walletadres. Je kunt het veilig met andere<PERSON> delen.", "receive_funds.copy_address": "<PERSON><PERSON><PERSON>", "receive_funds.network-warning.eoa.subtitle": "<link><PERSON><PERSON><PERSON> lij<PERSON> met netwerken</link>. Crypto verzonden op niet-EVM-netwerken gaat verloren.", "receive_funds.network-warning.eoa.title": "Alle op Ethereum gebaseerde netwerken worden ondersteund", "receive_funds.network-warning.scw.subtitle": "<link>Bekijk ondersteunde netwerken</link>. Crypto verzonden op andere netwerken gaat verloren.", "receive_funds.network-warning.scw.title": "Belangrijk: gebruik alleen ondersteunde netwerken", "receive_funds.scan_qr_code": "Scan een QR-code", "receiving.in.days": "Je ontvangt het over {days}d", "receiving.this.week": "Je ontvangt het deze week", "receiving.today": "<PERSON> ontvangt het vandaag", "reference.error.maximum_number_of_characters_exceeded": "Te veel tekens", "referral-code.placeholder": "Plak uitnodigingslink", "referral-code.subtitle": "Klik nogmaals op de link van je vriend of plak de link hieronder. We willen zeker weten dat je je beloningen krijgt.", "referral-code.title": "Heeft een vriend je {bReward} gestuurd?", "rekyc.verification_deadline.subtitle": "Voltooi verificatie binnen {daysUntil} dagen om je kaart te blijven geb<PERSON>iken.", "rekyc.verification_required.subtitle": "Voltooi verificatie om je kaart te kunnen blijven gebruiken.", "reminder.fund": "💸 Geld toevoegen — verdien direct 6%", "reminder.onboarding": "🏁 Maak installatie af — verdien 6% op je stortingen", "remove-owner.confirmation.subtitle": "Uit veiligheid duurt het verwerken van wijzigingen 3 minuten. <PERSON> kaart wordt tijdelijk geblokkeerd en betalingen zijn niet mogelijk.", "remove-owner.confirmation.title": "Je kaart wordt 3 min. geblokkeerd tijdens de update", "restore-smart-wallet.wallet-recovered": "<PERSON><PERSON><PERSON>teld", "rewardClaimCelebration.claimedTitle": "Beloningen al geclaimd", "rewardClaimCelebration.subtitle": "Voor het uitnodigen van vrienden", "rewardClaimCelebration.title": "Je hebt verdiend", "rewards-warning.subtitle": "Als je dit account ver<PERSON><PERSON><PERSON><PERSON>, pauzeer je de toegang tot gekoppelde beloningen. Je kunt het account altijd herstellen om ze te claimen.", "rewards-warning.title": "Je verliest toegang tot je beloningen", "rewards.copiedInviteLink": "Uitnodigingslink gekopieerd", "rewards.createAccount": "<PERSON><PERSON><PERSON> uitnodi<PERSON>link", "rewards.header.subtitle": "We sturen {aR<PERSON><PERSON>} naar jou en {bReward} naar je vriend, wanneer die {bSpendLimitReward} uitgeeft.", "rewards.header.title": "Krijg {amountA}{br}Geef {amountB}", "rewards.sendInvite": "Verstuur uitnodiging", "rewards.sendInviteTip": "Kies een vriend en we geven diegene {bAmount}", "route.fees": "Netwerkkosten {fees}", "routesNotFound.description": "De wisselroute voor de {from}-{to} netwerkcombinatie is niet be<PERSON><PERSON>.", "routesNotFound.title": "<PERSON>n wisselroute beschik<PERSON>ar", "rpc.OrderBuySignMessage.subtitle": "Via Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Via Swaps.IO", "rpc.addCustomNetwork.addNetwork": "Netwerk toe", "rpc.addCustomNetwork.chainId": "Chain ID", "rpc.addCustomNetwork.nativeToken": "Native token", "rpc.addCustomNetwork.networkName": "Netwerknaam", "rpc.addCustomNetwork.operationDescription": "Geeft deze website toestemming een netwerk aan je portemonnee toe te voegen. Zeal kan de veiligheid van custom netwerken niet controleren, zorg dat je de risico's begrijpt.", "rpc.addCustomNetwork.rpcUrl": "RPC URL", "rpc.addCustomNetwork.subtitle": "Via {name}", "rpc.addCustomNetwork.title": "Netwerk toevoegen", "rpc.send_token.network_not_supported.subtitle": "We werken eraan om transacties op dit netwerk mogelijk te maken. Bedankt voor je geduld 🙏", "rpc.send_token.network_not_supported.title": "Netwerk binnenkort beschikbaar", "rpc.send_token.send_or_receive.settings": "Instellingen", "rpc.sign.accept": "Accept<PERSON><PERSON>", "rpc.sign.cannot_parse_message.body": "We konden dit bericht niet decoderen. Accepteer dit verzoek alleen als je deze app vertrouwt.{br}{br}Berichten kunnen je inloggen bij een app, maar kunnen apps ook controle over je tokens geven.", "rpc.sign.cannot_parse_message.header": "Ga voorzichtig te werk", "rpc.sign.import_private_key": "Sleutels importeren", "rpc.sign.subtitle": "<PERSON><PERSON> {name}", "rpc.sign.title": "Ondertekenen", "safe-creation.success.title": "<PERSON><PERSON> a<PERSON>", "safe-safety-checks-popup.title": "Veiligheidscontroles transactie", "safetyChecksPopup.title": "Veiligheidscontroles site", "scan_qr_code.description": "Scan een wallet-QR of verbind met een app", "scan_qr_code.show_qr_code": "Toon mijn QR-code", "scan_qr_code.tryAgain": "<PERSON><PERSON><PERSON> opnieuw", "scan_qr_code.unlockCamera": "Ontgrendel camera", "screen-lock-missing.modal.close": "Sluiten", "screen-lock-missing.modal.subtitle": "Je apparaat vereist een schermvergrendeling om passkeys te gebruiken. <PERSON><PERSON> dit in en probeer het opnieuw.", "screen-lock-missing.modal.title": "Schermvergrendeling ontbreekt", "seedConfirmation.banner.subtitle": "Je geheime zin geeft toegang tot je geld. Deel hem nooit.", "seedConfirmation.title": "DEEL NOOIT je geheime zin met anderen", "select-active-owner.subtitle": "Je hebt meerdere wallets aan je kaart gekoppeld. Selecteer er een om met Zeal te verbinden. Je kunt altijd wisselen.", "select-active-owner.title": "Selecteer wallet", "select-card.title": "Selecteer kaart", "select-crypto-currency-title": "Kies token", "select-token.title": "Selecteer token", "selectEarnAccount.chf.description.steps": "· 24/7 geld op<PERSON>, zonder wachttijd {br}· Rente groeit elke seconde aan {br}· Extra goed beschermde stortingen in <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} per jaar in CHF", "selectEarnAccount.eur.description.steps": "· Geld opnemen kan 24/7, geen lockups {br}· Rente g<PERSON>eit elke seconde aan {br}· Extra gedekte leningen met <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} per jaar in EUR", "selectEarnAccount.subtitle": "Je kunt altijd wijzigen", "selectEarnAccount.title": "Selecteer valuta", "selectEarnAccount.usd.description.steps": "· Geld opnemen kan 24/7, geen lockups {br}· Rente groeit elke seconde aan {br}· Extra gedekte stortingen in <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} per jaar in USD", "selectEarnAccount.zero.description_general": "<PERSON>ud digitaal geld aan zonder rente te verdienen", "selectEarnAccount.zero.title": "0% per jaar", "selectRechargeThreshold.button.enterAmount": "<PERSON><PERSON>r bedrag in", "selectRechargeThreshold.button.setTo": "Instellen op {amount}", "selectRechargeThreshold.description.line1": "Als je kaartsaldo onder de {amount} komt, wordt het automatisch opgewaardeerd tot {amount} vanuit je Earn-rekening.", "selectRechargeThreshold.description.line2": "<PERSON>en lager doelbedrag betekent meer in je Earn-rekening (die 3% verdient). Je kunt dit altijd wijzigen.", "selectRechargeThreshold.title": "<PERSON><PERSON> doel<PERSON> kaart in", "select_currency_to_withdraw.select_token_to_withdraw": "Selecteer token voor opname", "send-card-token.form.send": "Verzenden", "send-card-token.form.send-amount": "Opwaardeerbedrag", "send-card-token.form.title": "<PERSON><PERSON> toe<PERSON>n aan kaart", "send-card-token.form.to-address": "<PERSON><PERSON>", "send-safe-transaction.network-fee-widget.error": "Je hebt {amount} nodig of kies een andere token", "send-safe-transaction.network-fee-widget.no-fee": "<PERSON><PERSON> kosten", "send-safe-transaction.network-fee-widget.title": "<PERSON><PERSON>", "send-safe-transaction.network_fee_widget.title": "Netwerkkosten", "send.banner.fees": "Je hebt {amount} meer {currency} nodig om de kosten te betalen", "send.banner.toAddressNotSupportedNetwork.subtitle": "<PERSON> port<PERSON><PERSON> van de ontvanger ondersteunt {network} niet. Wissel naar een ondersteunde token.", "send.banner.toAddressNotSupportedNetwork.title": "Netwerk niet ondersteund door ontvanger", "send.banner.walletNotSupportedNetwork.subtitle": "Smart Wallets kunnen geen transacties doen op {network}. Wissel naar een ondersteunde token.", "send.banner.walletNotSupportedNetwork.title": "Tokennetwerk niet ondersteund", "send.empty-portfolio.empty-state": "We hebben geen tokens gevonden", "send.empty-portfolio.header": "Tokens", "send.titile": "Verzenden", "sendLimit.success.subtitle": "<PERSON> dagli<PERSON>t is over 3 minuten bijgewerkt. Tot dan geldt je huidige limiet.", "sendLimit.success.title": "<PERSON><PERSON> wij<PERSON>ing duurt 3 minuten", "send_crypto.form.disconnected.cta.addFunds": "Geld toe<PERSON>n", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Wissel naar {network}", "send_crypto.form.disconnected.label": "Over te boeken bedrag", "send_to.qr_code.description": "Scan een QR-code om naar een portemonnee te sturen", "send_to.qr_code.title": "Scan QR-code", "send_to_card.header": "<PERSON><PERSON><PERSON> naar ka<PERSON>", "send_to_card.select_sender.add_wallet": "Wallet toevoegen", "send_to_card.select_sender.header": "Selecteer afzender", "send_to_card.select_sender.search.default_placeholder": "<PERSON><PERSON> of ENS", "send_to_card.select_sender.show_card_address_button_description": "<PERSON><PERSON> ka<PERSON>", "send_token.form.select-address": "Selecteer adres", "send_token.form.send-amount": "Verstuur bedrag", "send_token.form.title": "<PERSON><PERSON><PERSON><PERSON>", "setLimit.amount.error.zero_amount": "Je kunt dan geen betalingen doen", "setLimit.error.max_limit_reached": "Stel limiet in op max {amount}", "setLimit.error.same_as_current_limit": "Gelijk aan huidige limiet", "setLimit.placeholder": "Huidig: {amount}", "setLimit.submit": "Limiet instellen", "setLimit.submit.error.amount_required": "<PERSON><PERSON>r bedrag in", "setLimit.subtitle": "<PERSON><PERSON> is je dagelijkse bestedingsbedrag.", "setLimit.title": "Dagelijkse besteedlimiet instellen", "settings.accounts": "Accounts", "settings.accountsSeeAll": "<PERSON><PERSON> be<PERSON>en", "settings.addAccount": "<PERSON><PERSON><PERSON>", "settings.card": "Kaartinstellingen", "settings.connections": "App-verbindingen", "settings.currency": "Standaardvaluta", "settings.default_currency_selector.title": "Valuta", "settings.discord": "Discord", "settings.experimentalMode": "Experimentele modus", "settings.experimentalMode.subtitle": "Test nieuwe functies", "settings.language": "Taal", "settings.lockZeal": "Zeal vergrendelen", "settings.notifications": "Meldingen", "settings.open_expanded_view": "Uitgebreide weergave openen", "settings.privacyPolicy": "Privacybeleid", "settings.settings": "Instellingen", "settings.termsOfUse": "Gebruiksvoorwaarden", "settings.twitter": "𝕏 / Twitter", "settings.version": "Versie {version} env: {env}", "setup-card.confirmation": "Virt<PERSON><PERSON> kaart a<PERSON>n", "setup-card.confirmation.subtitle": "Doe online betalingen en voeg toe aan je {type} wallet voor contactloze betalingen.", "setup-card.getCard": "<PERSON><PERSON>", "setup-card.order.physicalCard": "<PERSON>ys<PERSON><PERSON> kaart", "setup-card.order.physicalCard.steps": "· Een fysieke VISA Gnosis Pay-kaart {br}· Levering duurt tot 3 weken {br}· Gebruik voor betalingen in winkels en bij pinautomaten. {br}· Voeg toe aan Apple/Google Wallet (alleen in ondersteunde landen)", "setup-card.order.subtitle1": "Je kunt meerdere kaarten tegelijk gebruiken", "setup-card.order.title": "Welk type kaart?", "setup-card.order.virtualCard": "<PERSON>irt<PERSON><PERSON>", "setup-card.order.virtual_card.steps": "· Een digitale VISA Gnosis Pay-kaart {br}· Direct te gebruiken voor online betalingen {br}· Voeg toe aan Apple/Google Wallet (alleen in ondersteunde landen)", "setup-card.orderCard": "<PERSON><PERSON> bestellen", "setup-card.virtual-card": "Virt<PERSON><PERSON> kaart a<PERSON>n", "setup.notifs.fakeAndroid.title": "Notificaties voor betalingen en inkomende overboekingen", "setup.notifs.fakeIos.subtitle": "Zeal kan je een melding sturen als je geld ont<PERSON>gt of uitgeeft met je <PERSON>-kaart. Je kunt dit later wijzigen.", "setup.notifs.fakeIos.title": "Notificaties voor betalingen en inkomende overboekingen", "sign.PermitAllowanceItem.spendLimit": "Bestedingslimiet", "sign.ledger.subtitle": "Verzoek verzonden naar je hardware-wallet.", "sign.ledger.title": "Ondertekenen met hardware-wallet", "sign.passkey.subtitle": "Je <PERSON> vraagt je nu om te ondertekenen met de passkey van deze wallet. Ga daar verder.", "sign.passkey.title": "Selecteer passkey", "signal_aborted_for_uknown_reason.title": "Netwerkverzoek geannuleerd", "simulatedTransaction.BridgeTrx.info.title": "Bridge", "simulatedTransaction.CardTopUp.info.title": "<PERSON><PERSON> toe<PERSON>n aan kaart", "simulatedTransaction.CardTopUpTrx.info.title": "<PERSON><PERSON> toe<PERSON>n aan kaart", "simulatedTransaction.NftCollectionApproval.approve": "NFT-<PERSON><PERSON> go<PERSON>", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "<PERSON><PERSON><PERSON><PERSON> aan kaart", "simulatedTransaction.OrderEarnDepositBridge.title": "Storten in Earn", "simulatedTransaction.P2PTransaction.info.title": "Verzenden", "simulatedTransaction.PermitSignMessage.title": "Permit", "simulatedTransaction.SingleNftApproval.approve": "NFT goedkeuren", "simulatedTransaction.UnknownSignMessage.title": "Ondertekenen", "simulatedTransaction.Withdrawal.info.title": "Opname", "simulatedTransaction.approval.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.approve.info.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.p2p.info.account": "<PERSON>ar", "simulatedTransaction.p2p.info.unlabelledAccount": "<PERSON><PERSON><PERSON><PERSON> port<PERSON>", "simulatedTransaction.unknown.info.receive": "Ontvangen", "simulatedTransaction.unknown.info.send": "Verzenden", "simulatedTransaction.unknown.using": "Via {app}", "simulation.approval.modal.text": "<PERSON>neer je een goedkeuring accepteert, geef je een specifieke app of smart contract toestemming om je tokens of NFT's in toekomstige transacties te gebruiken.", "simulation.approval.modal.title": "Wat zijn goed<PERSON>uringen?", "simulation.approval.spend-limit.label": "Bestedingslimiet", "simulation.approve.footer.for": "Voor", "simulation.approve.unlimited": "Onbeperkt", "simulationNotAvailable.title": "On<PERSON>ende actie", "smart-wallet-activation-view.on": "Op", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1<PERSON><PERSON><PERSON> kan de toegang tot je portemon<PERSON> blokkeren", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Gebruik Apple of Google om Zeal veilig in te stellen", "smart-wallet.passkey-notice.title": "Vermijd 1Password", "spend-limits.high.modal.text": "Stel een bestedingslimiet in die dicht bij je verwachte gebruik ligt. Hoge limieten zijn riskant en maken het voor oplichters makkelijker om je tokens te stelen.", "spend-limits.high.modal.text_sign_message": "De bestedingslimiet moet in de buurt liggen van het aantal tokens dat je echt gebruikt met een app. Hoge limieten zijn riskant en maken het makkelijker voor oplichters om je tokens te stelen.", "spend-limits.high.modal.title": "Hoge bestedingslimiet", "spend-limits.modal.text": "Een bestedingslimiet is het aantal tokens dat een app namens jou mag gebruiken. Je kunt deze limiet altijd wijzigen of verwijderen. Houd voor je veiligheid de limiet dicht bij het aantal tokens dat je da<PERSON><PERSON><PERSON><PERSON><PERSON> met een app gebruikt.", "spend-limits.modal.title": "Wat is een bestedingslimiet?", "spent-limit-info.modal.description": "Een bestedingslimiet is het aantal tokens dat een app namens jou mag gebruiken. Je kunt deze limiet altijd wijzigen of verwijderen. Ho<PERSON> voor de veiligheid de limiet dicht bij het aantal tokens dat je daadwerkelijk gebruikt met een app.", "spent-limit-info.modal.title": "Wat is een bestedingslimiet?", "sswaps-io.transfer-provider": "Transactieaan<PERSON>der", "storage.accountDetails.activateWallet": "Activeer wallet", "storage.accountDetails.changeWalletLabel": "Wijzig walletlabel", "storage.accountDetails.deleteWallet": "Verwijder wallet", "storage.accountDetails.setup_recovery_kit": "Herstelkit", "storage.accountDetails.showPrivateKey": "<PERSON>n privésle<PERSON>l", "storage.accountDetails.showWalletAddress": "<PERSON>n <PERSON>", "storage.accountDetails.smartBackup": "Back-up & herstel", "storage.accountDetails.viewSsecretPhrase": "Bekijk geheime zin", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets?", "storage.manageAccounts.title": "Wallets", "submit-userop.progress.text": "Verzenden", "submit.error.amount_high": "Bedrag te hoog", "submit.error.amount_hight": "Bedrag te hoog", "submit.error.amount_low": "Bedrag te laag", "submit.error.amount_required": "<PERSON><PERSON>r bedrag in", "submit.error.maximum_number_of_characters_exceeded": "Verkort het bericht", "submit.error.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "submit.error.recipient_required": "Ontvanger vereist", "submit.error.routes_not_found": "Geen routes gevonden", "submitSafeTransaction.monitor.title": "Transactieresultaat", "submitSafeTransaction.sign.title": "Transactieresultaat", "submitSafeTransaction.state.sending": "Verzenden", "submitSafeTransaction.state.sign": "Aanmaken", "submitSafeTransaction.submittingToRelayer.title": "Transactieresultaat", "submitTransaction.cancel": "<PERSON><PERSON><PERSON>", "submitTransaction.cancel.attemptingToStop": "Bezig met stoppen", "submitTransaction.cancel.failedToStop": "<PERSON><PERSON> mislukt", "submitTransaction.cancel.stopped": "Gestopt", "submitTransaction.cancel.title": "Transactievoorbeeld", "submitTransaction.failed.banner.description": "Netwerk annuleerde dit. <PERSON><PERSON>r opnieuw of neem contact met ons op.", "submitTransaction.failed.banner.title": "Transactie mislukt", "submitTransaction.failed.execution_reverted.title": "De app had een fout", "submitTransaction.failed.execution_reverted_without_message.title": "De app had een fout", "submitTransaction.failed.out_of_gas.description": "Geannuleerd: netwerkkosten te hoog.", "submitTransaction.failed.out_of_gas.title": "Netwerkfout", "submitTransaction.sign.title": "Transactieresultaat", "submitTransaction.speedUp": "Versnellen", "submitTransaction.state.addedToQueue": "Toege<PERSON><PERSON><PERSON> aan wacht<PERSON>j", "submitTransaction.state.addedToQueue.short": "In wachtrij", "submitTransaction.state.cancelled": "Gestopt", "submitTransaction.state.complete": "{currencyCode} toege<PERSON><PERSON>d aan <PERSON>", "submitTransaction.state.complete.subtitle": "Controleer j<PERSON>-portfolio", "submitTransaction.state.completed": "Voltooid", "submitTransaction.state.failed": "Mislukt", "submitTransaction.state.includedInBlock": "Opgenomen in blok", "submitTransaction.state.includedInBlock.short": "In blok", "submitTransaction.state.replaced": "Vervangen", "submitTransaction.state.sendingToNetwork": "Verzenden naar netwerk", "submitTransaction.stop": "Stoppen", "submitTransaction.submit": "Verzenden", "submitted-user-operation.state.bundled": "In wachtrij", "submitted-user-operation.state.completed": "Voltooid", "submitted-user-operation.state.failed": "Mislukt", "submitted-user-operation.state.pending": "Wordt doorgestuurd", "submitted-user-operation.state.rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submittedTransaction.failed.title": "Transactie mislukt", "success_splash.card_activated": "<PERSON><PERSON>", "supportFork.give-feedback.title": "Feedback geven", "supportFork.itercom.description": "<PERSON><PERSON><PERSON> over storten, Earn of beloningen.", "supportFork.itercom.title": "<PERSON><PERSON><PERSON> over de port<PERSON><PERSON>", "supportFork.title": "<PERSON><PERSON><PERSON> bij", "supportFork.zendesk.subtitle": "<PERSON><PERSON>, ID-checks & refunds.", "supportFork.zendesk.title": "Kaartbetalingen & identiteit", "supported-networks.ethereum.warning": "Hoge kosten", "supportedNetworks.networks": "Ondersteunde netwerken", "supportedNetworks.oneAddressForAllNetworks": "<PERSON>én ad<PERSON> voor alle netwerken", "supportedNetworks.receiveAnyAssets": "Ontvang alle assets van ondersteunde netwerken direct in je Zeal-<PERSON><PERSON><PERSON> met he<PERSON><PERSON><PERSON> adres", "swap.form.error.no_routes_found": "Geen routes gevonden", "swap.form.error.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "swaps-io-details.bank.serviceProvider": "Dienstverlener", "swaps-io-details.details.processing": "Verwerken", "swaps-io-details.pending": "In behandeling", "swaps-io-details.rate": "Wisselkoers", "swaps-io-details.serviceProvider": "Dienstverlener", "swaps-io-details.transaction.from.processing": "Gestarte transactie", "swaps-io-details.transaction.networkFees": "Netwerkkosten", "swaps-io-details.transaction.state.completed-transaction": "Voltooide transactie", "swaps-io-details.transaction.state.started-transaction": "Gestarte transactie", "swaps-io-details.transaction.to.processing": "Voltooide transactie", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Storting wordt snel voltooid. Kinetex verwerkt je transactie nog.", "swapsIO.monitoring.awaitingLiqSend.title": "Vertraagd", "swapsIO.monitoring.awaitingRecive.title": "Doorgeven", "swapsIO.monitoring.awaitingSend.title": "In wachtrij", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Tokens zijn naar Kinetex verzonden, maar worden snel teruggestuurd. Kinetex kon de bestemmingstransactie niet voltooien.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Tokens retourneren", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Tokens zijn niet overgeboekt door een onbekende fout. Probeer het opnieuw.", "swapsIO.monitoring.cancelledNoSlash.title": "To<PERSON>s geretourneerd", "swapsIO.monitoring.cancelledSlashed.subtitle": "Tokens zijn gere<PERSON>d. Kinetex kon de bestemmingstransactie niet voltooien.", "swapsIO.monitoring.cancelledSlashed.title": "To<PERSON>s geretourneerd", "swapsIO.monitoring.completed.title": "Voltooid", "taker-metadata.earn": "Verdien in digitale USD met Sky", "taker-metadata.earn.aave": "Verdien in digitale EUR met Aave", "taker-metadata.earn.aave.cashout24": "Direct opnemen, 24/7", "taker-metadata.earn.aave.trusted": "Vertrouwd met $27 miljard, 2+ jaar", "taker-metadata.earn.aave.yield": "Ren<PERSON><PERSON> gro<PERSON> elke seconde", "taker-metadata.earn.chf": "Verdien in digitale CHF", "taker-metadata.earn.chf.cashout24": "Direct uitbetalen, 24/7", "taker-metadata.earn.chf.trusted": "Toevertrouwd: Fr. 28M", "taker-metadata.earn.chf.yield": "Ren<PERSON><PERSON> gro<PERSON> elke seconde", "taker-metadata.earn.usd.cashout24": "Direct opnemen, 24/7", "taker-metadata.earn.usd.trusted": "Vertrouwd met $10,7 miljard, 5+ jaar", "taker-metadata.earn.usd.yield": "Ren<PERSON><PERSON> gro<PERSON> elke seconde", "test": "<PERSON><PERSON><PERSON>", "to.titile": "<PERSON>ar", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "Activa", "token.groupHeader.titleWithSum": "Activa {sum}", "token.hidden_tokens.page.title": "Verborgen tokens", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "Token toe<PERSON>n", "token.widget.cashback_empty": "Nog geen transacties", "token.widget.emptyState": "Geen tokens in portemonnee", "tokens.cash": "Geld", "top-up-card-from-earn-view.approve.for": "Voor", "top-up-card-from-earn-view.approve.into": "In", "top-up-card-from-earn-view.swap.from": "<PERSON>", "top-up-card-from-earn-view.swap.to": "<PERSON>ar", "top-up-card-from-earn-view.withdraw.to": "<PERSON>ar", "top-up-card-from-earn.trx.title.approval": "<PERSON><PERSON><PERSON> go<PERSON>", "top-up-card-from-earn.trx.title.swap": "<PERSON><PERSON><PERSON><PERSON> aan kaart", "top-up-card-from-earn.trx.title.withdrawal": "<PERSON><PERSON><PERSON>", "topUpDapp.connectWallet": "Verbind portemon<PERSON>", "topup-fee-breakdown.bungee-fee": "Kosten externe provider", "topup-fee-breakdown.header": "Transactiekosten", "topup-fee-breakdown.network-fee": "Netwerkkosten", "topup-fee-breakdown.total-fee": "Totale kosten", "topup.continue-in-wallet": "Ga verder in je portemonnee", "topup.send.title": "<PERSON><PERSON><PERSON><PERSON>", "topup.submit-transaction.close": "Sluiten", "topup.submit-transaction.sent-to-wallet": "Verzonden naar {amount}", "topup.to": "<PERSON><PERSON>", "topup.transaction.complete.close": "Sluiten", "topup.transaction.complete.try-again": "<PERSON><PERSON><PERSON> opnieuw", "transaction-request.nonce-too-low.modal.button-text": "Sluiten", "transaction-request.nonce-too-low.modal.text": "Een transactie met hetzelf<PERSON> serienummer (nonce) is al voltooid, dus je kunt deze transactie niet meer indienen. Dit kan gebeuren als je transacties kort na elkaar uitvoert of als je een transactie probeert te versnellen of te annuleren die al is voltooid.", "transaction-request.nonce-too-low.modal.title": "Transactie al voltooid (zelfde nonce)", "transaction-request.replaced.modal.button-text": "Sluiten", "transaction-request.replaced.modal.text": "We kunnen de status van deze transactie niet volgen. Deze is mogelijk vervangen door een andere transactie of de RPC-node heeft problemen.", "transaction-request.replaced.modal.title": "Kan transactiestatus niet vinden", "transaction.activity.details.modal.close": "Sluiten", "transaction.cancel_popup.cancel": "Nee, wacht", "transaction.cancel_popup.confirm": "Ja, stoppen", "transaction.cancel_popup.description": "<PERSON>m te stoppen, moet je nieuwe netwerkkosten betalen in plaats van de oorspronkelijke kosten van {oldFee}", "transaction.cancel_popup.description_without_original": "<PERSON>m te stoppen, moet je nieuwe netwerkkosten betalen", "transaction.cancel_popup.not_supported.subtitle": "Stoppen niet ondersteund op {network}", "transaction.cancel_popup.not_supported.title": "<PERSON><PERSON>", "transaction.cancel_popup.stopping_fee": "Netwerkkosten voor stoppen", "transaction.cancel_popup.title": "Transactie stoppen?", "transaction.in-progress": "Bezig met verwerken", "transaction.inProgress": "Bezig met verwerken", "transaction.speed_up_popup.cancel": "Nee, wacht", "transaction.speed_up_popup.confirm": "Ja, versnel", "transaction.speed_up_popup.description": "<PERSON><PERSON> te ve<PERSON><PERSON>, beta<PERSON> je nieuwe netwerkkosten in plaats van de oorspronkelijke kosten van {amount}", "transaction.speed_up_popup.description_without_original": "<PERSON><PERSON> te ve<PERSON>, beta<PERSON> je nieuwe netwerkkosten", "transaction.speed_up_popup.seed_up_fee_title": "Netwerkkosten versnelling", "transaction.speed_up_popup.title": "Transactie versnellen?", "transaction.speedup_popup.not_supported.subtitle": "Transacties versnellen wordt niet ondersteund op {network}", "transaction.speedup_popup.not_supported.title": "<PERSON><PERSON>", "transaction.subTitle.failed": "Mislukt", "transactionDetails.cashback.not-qualified": "<PERSON><PERSON>", "transactionDetails.cashback.paid": "{amount} betaald", "transactionDetails.cashback.pending": "{amount} in behandeling", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "Onbekend", "transactionDetails.cashback_estimate": "Cashback-schatting", "transactionDetails.category": "Categorie", "transactionDetails.exchangeRate": "Wisselkoers", "transactionDetails.location": "Locatie", "transactionDetails.payment-approved": "<PERSON><PERSON> goedge<PERSON>urd", "transactionDetails.payment-declined": "Betaling afgewezen", "transactionDetails.payment-reversed": "<PERSON>ling teruggedraaid", "transactionDetails.recharge.amountSentFromEarn.title": "<PERSON><PERSON> verstuurd vanuit Earn", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "Opgewaardeerd naar kaart", "transactionDetails.recharge.rate.title": "<PERSON><PERSON>", "transactionDetails.recharge.transactionId.title": "Transactie-ID", "transactionDetails.refund": "Terugbetaling", "transactionDetails.reversal": "Terugboeking", "transactionDetails.transactionCurrency": "Valuta transactie", "transactionDetails.transactionId": "Transactie-ID", "transactionDetails.type": "Transactie", "transactionRequestWidget.approve.subtitle": "Voor {target}", "transactionRequestWidget.p2p.subtitle": "Aan {target}", "transactionRequestWidget.unknown.subtitle": "Via {target}", "transactionSafetyChecksPopup.title": "Veiligheidscontroles transactie", "transactions.main.activity.title": "Activiteit", "transactions.page.hiddenActivity.title": "Verborgen activiteit", "transactions.page.title": "Activiteit", "transactions.viewTRXHistory.emptyState": "Nog geen transacties", "transactions.viewTRXHistory.errorMessage": "Het <PERSON> van je transactiegeschiedenis is mislukt", "transactions.viewTRXHistory.hidden.emptyState": "<PERSON>n verborgen transacties", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Activiteit niet ondersteund voor testnets", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Activiteit niet ondersteund voor testnets{br}<link>Ga naar block explorer</link>", "transfer_provider": "A<PERSON><PERSON>der overboeking", "transfer_setup_with_different_wallet.subtitle": "Bankoverboekingen zijn <PERSON> met een andere wallet. Je kunt maar één wallet verbinden voor overboekingen.", "transfer_setup_with_different_wallet.swtich_and_continue": "<PERSON><PERSON><PERSON> en doorgaan", "transfer_setup_with_different_wallet.title": "<PERSON><PERSON><PERSON> van <PERSON>", "tx-sent-to-wallet.button": "Sluiten", "tx-sent-to-wallet.subtitle": "Ga verder in {wallet}", "unblockProviderInfo.fees": "<PERSON> krijgt de laagst mogelijke kosten: 0% tot $5k per maand en 0,2% daarboven.", "unblockProviderInfo.registration": "Unblock is geregistreerd en geautoriseerd door FNTT voor VASP-uitwisseling en bewaardiensten, en is een geregistreerde MSB-provider bij de Amerikaanse Fincen. <link>Meer info</link>", "unblockProviderInfo.selfCustody": "Het digitale geld dat je ontvangt is in eigen beheer en niemand anders heeft controle over je bezit", "unblock_invalid_faster_payment_configuration.subtitle": "De bankrekening die je hebt opgegeven ondersteunt geen Europese SEPA-overboekingen of UK Faster Payments. Geef een andere rekening op.", "unblock_invalid_faster_payment_configuration.title": "And<PERSON> rekening vereist", "unknownTransaction.primaryText": "Kaarttransactie", "unsupportedCountry.subtitle": "Bankoverschrijvingen zijn nog niet besch<PERSON> in jouw land.", "unsupportedCountry.title": "<PERSON><PERSON> in {country}", "update-app-popup.subtitle": "De nieuwste update bevat belangrijke verbeteringen en nieuwe functies. Update nu voor de beste ervaring.", "update-app-popup.title": "Update Zeal", "update-app-popup.update-now": "Nu updaten", "user_associated_with_other_merchant.subtitle": "Deze wallet kan niet worden gebruikt voor bankoverboekingen. Gebruik een andere wallet of meld het op onze Discord voor ondersteuning.", "user_associated_with_other_merchant.title": "<PERSON>et kan niet worden gebruikt", "user_associated_with_other_merchant.try_with_another_wallet": "<PERSON><PERSON>r een andere wallet", "user_email_already_exists.subtitle": "Je hebt bankoverboekingen al ingesteld met een andere wallet. <PERSON><PERSON><PERSON> het opnieuw met de wallet die je eerder gebruikte.", "user_email_already_exists.title": "Overboekingen ingeste<PERSON> met andere wallet", "user_email_already_exists.try_with_another_wallet": "Probeer andere wallet", "validation.invalid.iban": "Ongeldige IBAN", "validation.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validation.required.first_name": "Voornaam is verplicht", "validation.required.iban": "IBAN is verplicht", "validation.required.last_name": "Achternaam is verplicht", "verify-passkey.cta": "<PERSON>key verifiëren", "verify-passkey.subtitle": "Verifieer dat je passkey is aangemaakt en goed is beveiligd.", "verify-passkey.title": "<PERSON>key verifiëren", "view-cashback.cashback-next-cycle": "Cashbackpercentage over {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "Stort om cashback te ontvangen", "view-cashback.pending": "{money} In behandeling", "view-cashback.pending-rewards.not_paid": "Je ontvangt het over {days}d", "view-cashback.pending-rewards.paid": "Deze week ontvangen", "view-cashback.received-rewards": "Ontvangen cashback", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Onbevestigde betalingen", "view-cashback.upcoming": "Aankomende {money}", "virtual-card-order.configure-safe.loading-text": "<PERSON><PERSON> wordt aangemaakt", "virtual-card-order.create-order.loading-text": "<PERSON><PERSON> wordt geactiveerd", "virtual-card-order.create-order.success-text": "<PERSON><PERSON>", "virtualCard.activateCard": "<PERSON><PERSON> kaart", "walletDeleteConfirm.main_action": "Verwijderen", "walletDeleteConfirm.subtitle": "Je moet de wallet opnieuw importeren om je portfolio te zien of transacties te doen", "walletDeleteConfirm.title": "Wallet verwijderen?", "walletSetting.header": "Wallet-instellingen", "wallet_connect.connect.cancel": "<PERSON><PERSON><PERSON>", "wallet_connect.connect.connect_button": "Ver<PERSON><PERSON>", "wallet_connect.connect.title": "Verbinden", "wallet_connect.connected.title": "Verbonden", "wallet_connect_add_chain_missing.title": "Netwerk niet ondersteund", "wallet_connect_proposal_expired.title": "Verbinding verlopen", "withdraw": "Opnemen", "withdraw.confirmation.close": "<PERSON><PERSON><PERSON>", "withdraw.confirmation.continue": "Bevestigen", "withdrawal_request.completed": "Voltooid", "withdrawal_request.pending": "In behandeling", "zeal-dapp.connect-wallet.cta.primary.connecting": "Verbinden...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Verbinden", "zeal-dapp.connect-wallet.cta.secondary": "<PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.title": "Verbind portemonnee om door te gaan", "zealSmartWalletInfo.gas": "Betaal netwerkkosten met veel tokens; gebruik populaire ERC20-tokens op ondersteunde chains om de kosten te betalen, niet alleen de standaardtokens.", "zealSmartWalletInfo.recover": "<PERSON><PERSON> geh<PERSON>; <PERSON><PERSON> met een biometrische passkey vanuit je wachtwoordmanager, iCloud of Google-account.", "zealSmartWalletInfo.selfCustodial": "Volledig in eigen beheer; passkey-handtekeningen worden on-chain gevalideerd om centrale afhankelijkheden te minimaliseren.", "zealSmartWalletInfo.title": "Over Zeal Smart Wallets", "zeal_a_rewards_already_claimed_error.title": "Beloning al geclaimd", "zwidget.minimizedDisconnected.label": "Zeal niet verbonden"}