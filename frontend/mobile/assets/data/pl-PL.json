{"Account.ListItem.details.label": "Szczegóły", "AddFromAddress.success": "Portfel zapisany", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{<PERSON><PERSON> portfeli} one{{count} portfel} other{{count} portfeli}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Fraza odzyskiwania {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Utwórz nowe portfele, używając jednej z istniejących Fraz odzyskiwania", "AddFromExistingSecretPhrase.SelectPhrase.title": "Wybierz Frazę odzyskiwania", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "Twoja Fraza odzyskiwania może służyć jako kopia zapasowa wielu portfeli. Wybierz te, których chcesz używać.", "AddFromExistingSecretPhrase.WalletSelection.title": "Szybko dodaj portfel", "AddFromExistingSecretPhrase.success": "Portfele dodane do Zeal", "AddFromHardwareWallet.subtitle": "Wybierz swój portfel sprzętowy, aby połączyć się z Zeal", "AddFromHardwareWallet.title": "Portfel sprzętowy", "AddFromNewSecretPhrase.WalletSelection.subtitle": "Wybierz portfele do zaimportowania", "AddFromNewSecretPhrase.WalletSelection.title": "Zaimportuj portfele", "AddFromNewSecretPhrase.accounts": "Portfele", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "Tajna fraza działa jak pęk kluczy do milionów portfeli, z których każdy ma unikalny klucz prywatny.{br}{br}Możesz zaimportować dowolną liczbę portfeli teraz lub dodać więcej później.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Portfele z tajnej frazy", "AddFromNewSecretPhrase.subtitle": "<PERSON><PERSON>z tajną frazę, oddzielając słowa spacjami", "AddFromNewSecretPhrase.success_secret_phrase_added": "<PERSON><PERSON><PERSON> ta<PERSON> frazę 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Dodano portfele do Zeal", "AddFromNewSecretPhrase.wallets": "Portfele", "AddFromPrivateKey.subtitle": "Wpisz swój klucz prywatny", "AddFromPrivateKey.success": "Dodano klucz prywatny 🎉", "AddFromPrivateKey.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>l", "AddFromPrivateKey.typeOrPaste": "<PERSON><PERSON>z lub wklej tutaj", "AddFromSecretPhrase.importWallets": "{count,plural,=0{<PERSON><PERSON> portfeli} one{Importuj portfel} other{Importuj {count} portfeli}}", "AddFromTrezor.AccountSelection.title": "Zaimportuj portfele Trezor", "AddFromTrezor.hwWalletTip.subtitle": "Portfel sprzętowy przechowuje miliony portfeli o różnych adresach. Możesz zaimportować dowolną liczbę portfeli teraz lub dodać więcej później.", "AddFromTrezor.hwWalletTip.title": "Importowanie z portfeli sprzętowych", "AddFromTrezor.importAccounts": "{count,plural,=0{<PERSON><PERSON> portfeli} one{Zaimportuj portfel} other{Zaimportuj {count} portfeli}}", "AddFromTrezor.success": "Dodano portfele do Zeal", "ApprovalSpenderTypeCheck.failed.subtitle": "Prawdopodobnie oszustwo: dostęp do tokenów powinny mieć kontrakty", "ApprovalSpenderTypeCheck.failed.title": "Uprawniony to portfel, a nie kontrakt", "ApprovalSpenderTypeCheck.passed.subtitle": "Zazwyczaj zatwierdzasz dostęp do aktywów dla kontraktów", "ApprovalSpenderTypeCheck.passed.title": "Uprawniony to smart kontrakt", "BestReturns.subtitle": "Ten dostawca swapa zapewni Ci najwyższy zysk, w<PERSON><PERSON><PERSON><PERSON><PERSON> w to wszystkie opłaty.", "BestReturnsPopup.title": "Najlepszy zysk", "BlacklistCheck.Failed.subtitle": "Zgłoszenia szkodliwości przez <source></source>", "BlacklistCheck.Failed.title": "Strona jest na czarnej liście", "BlacklistCheck.Passed.subtitle": "Brak zgłoszeń szkodliwości przez <source></source>", "BlacklistCheck.Passed.title": "Strona nie jest na czarnej liście", "BlacklistCheck.failed.statusButton.label": "Strona została zgłoszona", "BridgeRoute.slippage": "Poślizg {slippage}", "BridgeRoute.title": "Dostawca bridge", "CheckConfirmation.InProgress": "W toku...", "CheckConfirmation.success.splash": "<PERSON><PERSON><PERSON>", "ChooseImportOrCreateSecretPhrase.subtitle": "Zaimportuj tajną frazę lub utwórz nową", "ChooseImportOrCreateSecretPhrase.title": "<PERSON><PERSON><PERSON> fraz<PERSON>", "ConfirmTransaction.Simuation.Skeleton.title": "Sprawdzanie bezpieczeństwa…", "ConnectionSafetyCheckResult.passed": "Kontrola bezpieczeństwa zaliczona", "ContactGnosisPaysupport": "Kontakt z Gnosis Pay", "CopyKeyButton.copied": "Skopiowano", "CopyKeyButton.copyYourKey": "Skopiuj klucz", "CopyKeyButton.copyYourPhrase": "Skopiuj swoją frazę", "DAppVerificationCheck.Failed.subtitle": "Strona nie jest wymieniona na <source></source>", "DAppVerificationCheck.Failed.title": "Nie znaleziono strony w rejestrach aplikacji", "DAppVerificationCheck.Passed.subtitle": "Strona jest wymieniona na <source></source>", "DAppVerificationCheck.Passed.title": "Strona widnieje w rejestrach aplikacji", "DAppVerificationCheck.failed.statusButton.label": "Nie znaleziono strony w rejestrach aplikacji", "ERC20.tokens.emptyState": "Nie znaleziono tokenów", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "<PERSON>ła<PERSON>", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Ostatnie {period}: mi<PERSON><PERSON><PERSON> {from} a {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Opłata bazowa: {baseFee} • <PERSON><PERSON><PERSON>ństwa: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "<PERSON>e udało się pobrać aktualnej opłaty bazowej", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Wyższy niż następny Nonce. Transakcja utknie", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "<PERSON>ce nie może być niższy od obecnego", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "<PERSON>e udało się obliczyć opłaty priorytetowej", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "<PERSON>e udało się pobrać aktualnej opłaty maksymalnej", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "<PERSON><PERSON><PERSON>", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Może utknąć do czasu spadku opłat sieciowych", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "Maks. opłata bazowa", "EditFeeModal.Custom.gasLimit.title": "Limit gazu {gasLimit}", "EditFeeModal.Custom.title": "Ustawi<PERSON>e", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Utknie do czasu spadku opłaty bazowej", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Niska opłata. Transakcja może utknąć", "EditFeeModal.EditGasLimit.estimatedGas": "Szac. gaz: {estimated} • <PERSON><PERSON><PERSON> bez<PERSON>ństwa: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Poniżej szacowanego limitu. Transakcja nie powiedzie się", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Poniżej sugerowanego limitu. Transakcja może się nie udać", "EditFeeModal.EditGasLimit.subtitle": "Ustaw maks. ilo<PERSON>ć gazu dla tej transakcji. <PERSON><PERSON><PERSON> ustawi<PERSON> za niski limit, transakcja nie powiedzie się", "EditFeeModal.EditGasLimit.title": "Edytuj limit gazu", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Poniżej minimalnego limitu opłaty: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "Wyższy niż następny Nonce. Utknie", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "<PERSON>ce nie może być niższy od obecnego", "EditFeeModal.EditNonce.subtitle": "<PERSON><PERSON><PERSON> ustawisz inne Nonce niż kolejne, transakcja utknie", "EditFeeModal.EditNonce.title": "<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "<PERSON><PERSON><PERSON><PERSON> {amount} , by <PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.Header.Time.unknown": "<PERSON>ez<PERSON><PERSON> czas", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Maks. {fee}", "EditFeeModal.Header.fee.unknown": "<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.Header.subsequent_failed": "Szacunki mogą by<PERSON> ni<PERSON>, odświeżenie nie powiodło się", "EditFeeModal.Layout.Header.ariaLabel": "Bieżąca opłata", "EditFeeModal.MaxFee.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> opłata to naj<PERSON><PERSON><PERSON><PERSON> kwota, j<PERSON><PERSON>, ale <PERSON><PERSON><PERSON> b<PERSON> to przewidywana opłata. Ten dodatkowy bufor pomaga w realizacji transakcji, nawet gdy sieć zwalnia lub staje się droż<PERSON>a.", "EditFeeModal.MaxFee.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> opła<PERSON> si<PERSON>", "EditFeeModal.SelectPreset.Time.unknown": "<PERSON>ez<PERSON><PERSON> czas", "EditFeeModal.SelectPreset.ariaLabel": "<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.SelectPreset.fast": "<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.SelectPreset.normal": "Normalnie", "EditFeeModal.SelectPreset.slow": "Wolno", "EditFeeModal.ariaLabel": "<PERSON><PERSON><PERSON><PERSON> sieciow<PERSON>", "FailedSimulation.Confirmation.Item.subtitle": "Wys<PERSON>ą<PERSON>ł błąd wewnętrzny", "FailedSimulation.Confirmation.Item.title": "Symulacja transakcji nie powiodła się", "FailedSimulation.Confirmation.subtitle": "<PERSON>zy na pewno chcesz kontynuować?", "FailedSimulation.Confirmation.title": "Podpisujesz w ciemno", "FailedSimulation.Title": "Błąd symulacji", "FailedSimulation.footer.subtitle": "Wys<PERSON>ą<PERSON>ł błąd wewnętrzny", "FailedSimulation.footer.title": "<PERSON>e udało się zasymulować transakcji", "FeeForecastWidget.NotEnoughBalance.errorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {amount} , aby <PERSON><PERSON><PERSON> transakcję", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Przetwarzanie może potrwać dłużej", "FeeForecastWidget.networkFee": "<PERSON><PERSON><PERSON>", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "<PERSON>e udało się obliczyć opłaty sieciowej", "FeeForecastWidget.subsequentFailed.message": "Szacunki mogą by<PERSON> ni<PERSON>, ostatnia aktualizacja nie powiodła się", "FeeForecastWidget.unknownDuration": "<PERSON><PERSON><PERSON><PERSON>", "FeeForecastWidget.unknownFee": "<PERSON><PERSON><PERSON><PERSON>", "GasCurrencySelector.balance": "Saldo: {balance}", "GasCurrencySelector.networkFee": "<PERSON><PERSON><PERSON>", "GasCurrencySelector.payNetworkFeesUsing": "<PERSON><PERSON><PERSON>ć opłaty sieciowe za pomocą", "GasCurrencySelector.removeDefaultGasToken.description": "Płać opłaty z największego salda", "GasCurrencySelector.removeDefaultGasToken.title": "Automatyczne opłaty", "GasCurrencySelector.save": "<PERSON><PERSON><PERSON><PERSON>", "GoogleDriveBackup.BeforeYouBegin.first_point": "Je<PERSON><PERSON> zapomnę hasła do <PERSON>, na zawsze utracę swoje środki", "GoogleDriveBackup.BeforeYouBegin.second_point": "Jeśli utracę dostęp do Dysku Google lub zmodyfikuję plik odzyskiwania, na zawsze utracę swoje środki", "GoogleDriveBackup.BeforeYouBegin.subtitle": "Zrozum i zaakceptuj następujący punkt dotyczący samodzielnej pieczy nad środkami:", "GoogleDriveBackup.BeforeYouBegin.third_point": "Zeal nie może mi pomóc w odzyskaniu hasła do Zeal ani dostępu do Dysku Google", "GoogleDriveBackup.BeforeYouBegin.title": "Zanim zaczniesz", "GoogleDriveBackup.loader.subtitle": "Zatwierdź na Dysku Google prośbę o przesłanie pliku odzyskiwania", "GoogleDriveBackup.loader.title": "Oczekiwanie na zatwierdzenie...", "GoogleDriveBackup.success": "Kopia zapasowa gotowa 🎉", "MonitorOffRamp.overServiceTime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> przelewów jest realizowana w ciągu {estimated_time}, ale czasami mogą potrwać dłużej z powodu dodatkowych kontroli. To normalne, a środki są bezpieczne w trakcie tych weryfikacji.{br}{br}Je<PERSON><PERSON> transakcja nie zostanie zrealizowana w ciągu {support_soft_deadline}, {contact_support}", "MonitorOnRamp.contactSupport": "Skontaktuj się z pomocą", "MonitorOnRamp.from": "Z", "MonitorOnRamp.fundsReceived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MonitorOnRamp.overServiceTime": "W<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> przelewów jest realizowana w ciągu {estimated_time}, ale czasami mogą potrwać dłużej z powodu dodatkowych weryfikacji. Jest to normalne, a środki są bezpieczne na czas tych kontroli.{br}{br}Jeśli transakcja nie zostanie zakończona w ciągu {support_soft_deadline}, {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Wysyłanie do Twojego portfela", "MonitorOnRamp.to": "Do", "MonitorOnRamp.waitingForTransfer": "Oczekiwanie na Twój przelew środków", "NftCollectionCheck.failed.subtitle": "Kolekcja nie jest zweryfikowana na <source></source>", "NftCollectionCheck.failed.title": "Kolekcja nie jest zweryfikowana", "NftCollectionCheck.passed.subtitle": "Kolekcja jest zweryfikowana na <source></source>", "NftCollectionCheck.passed.title": "Kolekcja jest zweryfikowana", "NftCollectionInfo.entireCollection": "Cała kolekcja", "NoSigningKeyStore.createAccount": "Utwórz konto", "NonceRangeError.biggerThanCurrent.message": "Transakcja utknie", "NonceRangeError.lessThanCurrent.message": "Transakcja nie powiedzie się", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "<PERSON><PERSON> jest wyższy od obecnego. <PERSON>m<PERSON><PERSON><PERSON>, aby transakcja nie utknęła.", "NonceRangeErrorPopup.biggerThanCurrent.title": "Transakcja utknie", "P2pReceiverTypeCheck.failed.subtitle": "<PERSON>zy na pewno wysyłasz na właściwy adres?", "P2pReceiverTypeCheck.failed.title": "Odbiorca to smart kontrakt, a nie portfel", "P2pReceiverTypeCheck.passed.subtitle": "Zazwyczaj wysyłasz aktywa do innych portfeli", "P2pReceiverTypeCheck.passed.title": "Odbiorca to portfel", "PasswordCheck.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasło", "PasswordChecker.subtitle": "Wprowadź swoje hasło, aby potwierdzić, że to Ty", "PermitExpirationCheck.failed.subtitle": "Ustaw krótki termin, tylko na niezbędny czas", "PermitExpirationCheck.failed.title": "Długi termin wygaśnięcia", "PermitExpirationCheck.passed.subtitle": "Jak długo aplikacja może używać Twoich tokenów", "PermitExpirationCheck.passed.title": "<PERSON><PERSON><PERSON> w<PERSON>śnięcia nie jest zbyt długi", "PrivateKeyValidationError.moreThanMaximumWords": "Maks. {count} słów", "PrivateKeyValidationError.notValidPrivateKey": "To nie jest prawidłowy klucz prywatny", "PrivateKeyValidationError.secretPhraseIsInvalid": "Tajna fraza jest nieprawidłowa", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Słowo #{index} jest błędne lub ni<PERSON><PERSON>idłowe", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} słowo} other{{count} słów}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Tajne frazy i klucze prywatne są szyfrowane i nie opuszczają tego urządzenia.", "SecretPhraseReveal.header": "Zapisz tajną frazę", "SecretPhraseReveal.hint": "Nie udostępniaj nikomu swojej frazy. Przechowuj ją bezpiecznie w trybie offline.", "SecretPhraseReveal.skip.subtitle": "<PERSON><PERSON><PERSON><PERSON> to zrobić później, ale jeśli utracisz to urządzenie przed zapisaniem frazy, stracisz wszystkie środki dodane do tego portfela.", "SecretPhraseReveal.skip.takeTheRisk": "Ryzykuję", "SecretPhraseReveal.skip.title": "<PERSON><PERSON><PERSON>ć zapisywanie frazy?", "SecretPhraseReveal.skip.writeDown": "<PERSON><PERSON><PERSON><PERSON>", "SecretPhraseReveal.skipForNow": "Pomiń teraz", "SecretPhraseReveal.subheader": "Zapisz ją i przechowuj bezpiecznie offline. Następnie poprosimy Cię o jej weryfikację.", "SecretPhraseReveal.verify": "Weryfiku<PERSON>", "SelectCurrency.tokens": "Tokeny", "SelectCurrency.tokens.emptyState": "Nie znaleziono tokenów", "SelectRoute.slippage": "Poślizg {slippage}", "SelectRoutes.emptyState": "Nie znaleźliśmy żadnych tras dla tego swapa", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Połącz portfel", "SendERC20.labelAddress.inputPlaceholder": "Etykieta portfela", "SendERC20.labelAddress.subtitle": "Dodaj etykietę do portfela, by <PERSON><PERSON><PERSON><PERSON> go <PERSON>.", "SendERC20.labelAddress.title": "Dodaj etykietę do portfela", "SendERC20.send_to": "Wyślij do", "SendERC20.tokens": "Tokeny", "SendOrReceive.bankTransfer.primaryText": "Przelew bankowy", "SendOrReceive.bankTransfer.shortText": "Darmowe, natychmiastowe wpłaty i wypłaty", "SendOrReceive.bridge.primaryText": "Bridge", "SendOrReceive.bridge.shortText": "Przesyłaj tokeny między si<PERSON>i", "SendOrReceive.receive.primaryText": "<PERSON><PERSON><PERSON><PERSON>", "SendOrReceive.receive.shortText": "<PERSON><PERSON><PERSON><PERSON> lub kole<PERSON>", "SendOrReceive.send.primaryText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SendOrReceive.send.shortText": "Wysyłaj tokeny na dowolny adres", "SendOrReceive.swap.primaryText": "Wymień", "SendOrReceive.swap.shortText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SendSafeTransaction.Confirm.loading": "Trwa kontrola bezpieczeństwa…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Zaszyfruj plik odzyskiwania hasłem", "SetupRecoveryKit.google.subtitle": "Zsynchronizowano {date}", "SetupRecoveryKit.google.title": "Kopia zapasowa na Dysku Google", "SetupRecoveryKit.subtitle": "Potrzebujesz co najmniej jednego sposobu na odzyskanie konta, je<PERSON><PERSON> odinstalujesz Zeal lub zmienisz urządzenie.", "SetupRecoveryKit.title": "Skonfiguruj zestaw do odzyskiwania", "SetupRecoveryKit.writeDown.subtitle": "Zapisz tajną frazę", "SetupRecoveryKit.writeDown.title": "Ręczna kopia zapasowa", "Sign.CheckSafeDeployment.activate": "Aktywuj", "Sign.CheckSafeDeployment.subtitle": "Zanim zalogujesz się do aplikacji lub podpiszesz wiadomość poza siecią, musisz aktywować urządzenie w tej sieci. Zrobisz to po zainstalowaniu lub odzyskaniu portfela Smart Wallet.", "Sign.CheckSafeDeployment.title": "Aktywuj urządzenie w tej sieci", "Sign.Simuation.Skeleton.title": "Trwa kontrola bezpieczeństwa…", "SignMessageSafetyCheckResult.passed": "Kontrole bezpieczeństwa zaliczone", "SignMessageSafetyChecksPopup.title.permits": "Kontrole bezpieczeństwa Permit", "SimulationFailedConfirmation.subtitle": "Symulacja tej transakcji wykazała błąd, kt<PERSON><PERSON> spowoduje jej niepowodzenie. Moż<PERSON><PERSON> j<PERSON> wys<PERSON>, ale prawdopodobnie się nie uda i możesz stracić opłatę sieciową.", "SimulationFailedConfirmation.title": "Transakcja prawdopodobnie się nie powiedzie", "SimulationNotSupported.Title": "<PERSON><PERSON><PERSON><PERSON><PERSON> nie jest{br}obsługiwana w{br}{network}", "SimulationNotSupported.footer.subtitle": "Nadal moż<PERSON><PERSON> wysła<PERSON> tę transakcję", "SimulationNotSupported.footer.title": "Symulacja nie jest obsługiwana", "SlippagePopup.custom": "Niestandardowy", "SlippagePopup.presetsHeader": "Poślizg przy wymianie", "SlippagePopup.title": "Ustawienia poślizgu", "SmartContractBlacklistCheck.failed.subtitle": "Zgłoszenia złośliwej aktywności od <source></source>", "SmartContractBlacklistCheck.failed.title": "Kontrakt jest na czarnej liście", "SmartContractBlacklistCheck.passed.subtitle": "Brak zgłoszeń o złośliwej aktywności od <source></source>", "SmartContractBlacklistCheck.passed.title": "Kontraktu nie ma na czarnej liście", "SuspiciousCharactersCheck.Failed.subtitle": "To częsta taktyka phishingu", "SuspiciousCharactersCheck.Failed.title": "Sprawd<PERSON><PERSON> popularne wzorce phishingu", "SuspiciousCharactersCheck.Passed.subtitle": "Sprawdzamy pod kątem prób phishingu", "SuspiciousCharactersCheck.Passed.title": "Adres nie zawiera nietypowych znaków", "SuspiciousCharactersCheck.failed.statusButton.label": "Ad<PERSON> zawiera nietypowe znaki ", "TokenVerificationCheck.failed.subtitle": "Token nie jest na liście <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} nie jest zweryfikowany przez CoinGecko", "TokenVerificationCheck.passed.subtitle": "Token jest na liście <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} jest zweryfikowany przez CoinGecko", "TopupDapp.MonitorTransaction.success.splash": "<PERSON><PERSON><PERSON>", "TransactionSafetyCheckResult.passed": "Kontrole bezpieczeństwa zaliczone", "TransactionSimulationCheck.failed.subtitle": "Błąd: {errorMessage}", "TransactionSimulationCheck.failed.title": "Transakcja prawdopodobnie się nie powiedzie", "TransactionSimulationCheck.passed.subtitle": "Symulację wykonano przy użyciu <source></source>", "TransactionSimulationCheck.passed.title": "Symulacja transakcji powiodła się", "TrezorError.trezor_action_cancelled.action": "Zamknij", "TrezorError.trezor_action_cancelled.subtitle": "Odrzucono transakcję na portfelu sprzętowym", "TrezorError.trezor_device_used_elsewhere.action": "S<PERSON>hroni<PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "Zamknij wszystkie inne otwarte sesje i spróbuj ponownie zsynchronizować Trezora.", "TrezorError.trezor_method_cancelled.action": "S<PERSON>hroni<PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "Upew<PERSON>j <PERSON>ę, że zezwalasz Trezorowi na eksport portfeli do Zeal.", "TrezorError.trezor_permissions_not_granted.action": "S<PERSON>hroni<PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "Nadaj Zeal uprawnienia do wyświetlania wszystkich portfeli.", "TrezorError.trezor_pin_cancelled.action": "S<PERSON>hroni<PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "Sesja anulowana na urządzeniu", "TrezorError.trezor_popup_closed.action": "S<PERSON>hroni<PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "Okno dialogowe Trezora zostało nieoczekiwanie zamknięte.", "TrxLikelyToFail.lessThanEstimatedGas.message": "Transakcja nie powiedzie się", "TrxLikelyToFail.lessThanMinimumGas.message": "Transakcja nie powiedzie się", "TrxLikelyToFail.lessThanSuggestedGas.message": "Prawdopodobnie się не powiedzie", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "Limit gazu jest za niski. Zwiększ go do sugerowanego poziomu, aby uniknąć niepowodzenia.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "Transakcja prawdopodobnie się nie powiedzie", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Limit gazu jest niższy od szacowanego. Zwiększ go do sugerowanego poziomu.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "Transakcja nie powiedzie się", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "Maks. opłata podstawowa jest niższa od bieżącej. Zwiększ ją, aby transakcja nie utknęła.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "Transakcja utknie", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "Maks. opłata za transakcję jest za niska. Zwiększ ją, aby transakcja nie utknęła.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "Transakcja utknie", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Opłata priorytetowa jest niższa od zalecanej. Zwiększ ją, aby prz<PERSON><PERSON><PERSON><PERSON> transakcję.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "Realizacja transakcji może potrwać dłużej", "UnsupportedMobileNetworkLayout.gotIt": "Rozumiem!", "UnsupportedMobileNetworkLayout.subtitle": "Nie możesz jeszcze wykonywać transakcji ani podpisywać wiadomości w sieci o ID {networkHexId} w mobilnej wersji Zeal{br}{br}Przełącz się na rozszerzenie przeglądarki, aby móc przeprowadzać transakcje w tej sieci, podczas gdy my ciężko pracujemy nad dodaniem jej obsługi 🚀", "UnsupportedMobileNetworkLayout.title": "Sieć nie jest obsługiwana w mobilnej wersji Zeal", "UnsupportedSafeNetworkLayout.subtitle": "<PERSON>e moż<PERSON><PERSON> wykonywać transakcji ani podpisywać wiadomości w sieci {network} za pomocą portfela Zeal Smart Wallet. {br}{br}Przełącz się na obsługiwaną sieć lub użyj portfela Legacy.", "UnsupportedSafeNetworkLayoutk.title": "<PERSON><PERSON><PERSON> nieobsługiwana przez Smart Wallet", "UserConfirmationPopup.goBack": "<PERSON><PERSON><PERSON>", "UserConfirmationPopup.submit": "Wyślij i tak", "ViewPrivateKey.header": "Klucz prywatny", "ViewPrivateKey.hint": "Nie udostępniaj nikomu swojego klucza prywatnego. Przechowuj go bezpiecznie w trybie offline.", "ViewPrivateKey.subheader.mobile": "<PERSON><PERSON><PERSON><PERSON>, aby zob<PERSON><PERSON>ć swój klucz prywatny", "ViewPrivateKey.subheader.web": "<PERSON><PERSON><PERSON><PERSON>, aby z<PERSON><PERSON><PERSON><PERSON> swój klucz prywatny", "ViewPrivateKey.unblur.mobile": "Dot<PERSON><PERSON><PERSON>, a<PERSON> z<PERSON><PERSON><PERSON>", "ViewPrivateKey.unblur.web": "Najedź, aby zobaczyć", "ViewSecretPhrase.PasswordChecker.subtitle": "W<PERSON>row<PERSON><PERSON> hasło, aby zaszyfrować plik odzyskiwania. Będziesz go potrzebować w przyszłości.", "ViewSecretPhrase.done": "<PERSON><PERSON><PERSON>", "ViewSecretPhrase.header": "<PERSON><PERSON><PERSON> fraza", "ViewSecretPhrase.hint": "Nie udostępniaj nikomu swojej frazy. Przechowuj ją bezpiecznie w trybie offline.", "ViewSecretPhrase.subheader.mobile": "Dot<PERSON><PERSON><PERSON>, aby z<PERSON><PERSON><PERSON><PERSON> swoją tajną frazę", "ViewSecretPhrase.subheader.web": "<PERSON><PERSON><PERSON><PERSON>, aby zob<PERSON><PERSON>ć swoją tajną frazę", "ViewSecretPhrase.unblur.mobile": "Dot<PERSON><PERSON><PERSON>, a<PERSON> z<PERSON><PERSON><PERSON>", "ViewSecretPhrase.unblur.web": "Najedź, aby zobaczyć", "account-details.monerium": "Przelewy realizuje Monerium, licencjonowana instytucja pieniądza elektronicznego. <link>Dowiedz się więcej</link>", "account-details.unblock": "Przelewy są realizowane przez Unblock, autoryzowanego i zarejestrowanego dostawcę usług wymiany i przechowywania. <link>Dowiedz się więcej</link>", "account-selector.empty-state": "Nie znaleziono portfeli", "account-top-up.select-currency.title": "Tokeny", "account.accounts_not_found": "Nie znaleźliśmy żadnych portfeli", "account.accounts_not_found_search_valid_address": "Tego portfela nie ma na Twojej liście", "account.add.create_new_secret_phrase": "Utwórz tajn<PERSON> frazę", "account.add.create_new_secret_phrase.subtext": "Nowa 12-w<PERSON><PERSON><PERSON> tajna fraza", "account.add.fromRecoveryKit.fileNotFound": "Nie znaleziono pliku", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "Spróbuj ponownie", "account.add.fromRecoveryKit.fileNotFound.explanation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy to konto ma folder Zeal Backup", "account.add.fromRecoveryKit.fileNotValid": "Plik odzyskiwania jest nieprawidłowy", "account.add.fromRecoveryKit.fileNotValid.explanation": "Plik jest niewłaściwego typu lub został zmodyfikowany", "account.add.import_secret_phrase": "Zaimportuj tajną frazę", "account.add.import_secret_phrase.subtext": "Utworzoną w Zeal, Metamask lub innych", "account.add.select_type.add_hardware_wallet": "Portfel sprzętowy", "account.add.select_type.existing_smart_wallet": "Istniejący Smart Wallet", "account.add.select_type.private_key": "Klucz prywatny", "account.add.select_type.seed_phrase": "Fraza seed", "account.add.select_type.title": "Zaimportuj portfel", "account.add.select_type.zeal_recovery_file": "Plik odzyskiwania Zeal", "account.add.success.title": "Nowy portfel utworzony 🎉", "account.addLabel.header": "Nazwij swój portfel", "account.addLabel.labelError.labelAlreadyExist": "Ta nazwa już istnieje. Wybierz inną", "account.addLabel.labelError.maxStringLengthExceeded": "Osiągnięto limit znaków", "account.add_active_wallet.primary_text": "<PERSON><PERSON><PERSON>", "account.add_active_wallet.short_text": "Utw<PERSON>rz, połącz lub zaimportuj portfel", "account.add_from_ledger.success": "Portfele dodane do Zeal", "account.add_tracked_wallet.primary_text": "Dodaj portfel tylko do odczytu", "account.add_tracked_wallet.short_text": "Zobacz portfolio i aktywność", "account.button.unlabelled-wallet": "Portfel bez etykiety", "account.create_wallet": "Utwórz portfel", "account.label.edit.title": "Edytuj nazwę portfela", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Plik odzyskiwania jest nieprawidłowy", "account.recoveryKit.selectBackupFile.subtitle": "Wybierz plik odzyskiwania do przywrócenia", "account.recoveryKit.selectBackupFile.title": "Plik odzyskiwania", "account.recoveryKit.success.recoveryFileFound": "Znaleziono plik odzyskiwania 🎉", "account.select_type_of_account.create_eoa.short": "Tradycyjny portfel dla ekspertów", "account.select_type_of_account.create_eoa.title": "Utwórz portfel z frazą seed", "account.select_type_of_account.create_safe_wallet.title": "Utwórz Smart Wallet", "account.select_type_of_account.existing_smart_wallet": "Istniejący Smart Wallet", "account.select_type_of_account.hardware_wallet": "Portfel sprzętowy", "account.select_type_of_account.header": "<PERSON><PERSON><PERSON>", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Klucz prywatny / fraza odzyskiwania", "account.select_type_of_account.read_only_wallet": "Portfel tylko do odczytu", "account.select_type_of_account.read_only_wallet.short": "Podgląd dowolnego portfolio", "account.topup.title": "<PERSON><PERSON><PERSON> k<PERSON>", "account.view.error.refreshAssets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "account.widget.refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "account.widget.settings": "Ustawienia", "accounts.view.copied-text": "Skopiowano {formattedAddress}", "accounts.view.copiedAddress": "Skopiowano {formattedAddress}", "action.accept": "Ak<PERSON>pt<PERSON>j", "action.accpet": "Ak<PERSON>pt<PERSON>j", "action.allow": "Zezwól", "action.back": "Wstecz", "action.cancel": "<PERSON><PERSON><PERSON>", "action.card-activation.title": "Aktywuj kartę", "action.claim": "<PERSON><PERSON><PERSON><PERSON>", "action.close": "Zamknij", "action.complete-steps": "Ukończ kroki", "action.confirm": "Potwierdź", "action.continue": "<PERSON><PERSON>", "action.copy-address-understand": "OK, skopiuj adres", "action.deposit": "<PERSON><PERSON><PERSON><PERSON>", "action.done": "<PERSON><PERSON><PERSON>", "action.dontAllow": "<PERSON><PERSON>", "action.edit": "<PERSON><PERSON><PERSON><PERSON>", "action.email-required": "Wpisz e-mail", "action.enterPhoneNumber": "Wpisz numer telefonu", "action.expand": "Rozwiń", "action.fix": "<PERSON><PERSON><PERSON>", "action.getStarted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.got_it": "<PERSON><PERSON><PERSON><PERSON>", "action.hide": "<PERSON><PERSON><PERSON><PERSON>", "action.import": "Import<PERSON>j", "action.import-keys": "Importuj klucze", "action.importKeys": "Importuj klucze", "action.minimize": "Minimalizuj", "action.next": "<PERSON><PERSON>", "action.ok": "OK", "action.reduceAmount": "Zmniejsz do maksimum", "action.refreshWebsite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ę", "action.remove": "Usuń", "action.remove-account": "Us<PERSON>ń konto", "action.requestCode": "Poproś o kod", "action.resend_code": "Wyślij kod ponownie", "action.resend_code_with_time": "<PERSON><PERSON><PERSON><PERSON><PERSON> kod ponownie {time}", "action.retry": "Spróbuj ponownie", "action.reveal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.save": "<PERSON><PERSON><PERSON><PERSON>", "action.save_changes": "Zapisz RPC", "action.search": "Szukaj", "action.seeAll": "<PERSON><PERSON><PERSON><PERSON> wszystko", "action.select": "<PERSON><PERSON><PERSON><PERSON>", "action.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.skip": "Pomiń", "action.submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.understood": "<PERSON><PERSON><PERSON><PERSON>", "action.update": "Zaktualizuj", "action.update-gnosis-pay-owner.complete": "Ukończ kroki", "action.zeroAmount": "Wpisz kwotę", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action_bar_title.tokens": "Tokeny", "action_bar_title.transaction_request": "Prośba o transakcję", "activate-monerium.loading": "Konfiguruję Twoje konto osobiste", "activate-monerium.success.title": "Monerium włączone", "activate-physical-card-widget.subtitle": "Dostawa może potrwać do 3 tygodni", "activate-physical-card-widget.title": "Aktywuj kartę fizyczną", "activate-smart-wallet.title": "Aktywuj portfel", "active_and_tracked_wallets.title": "Zeal pokrywa wszystkie Twoje opłaty na {network}, co pozwala Ci na darmowe transakcje!", "activity.approval-amount.revoked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.approval-amount.unlimited": "<PERSON><PERSON> limitu", "activity.approval.approved_for": "Zatwierdzono dla", "activity.approval.approved_for_with_target": "Zatwierdzono dla {approvedTo}", "activity.approval.revoked_for": "Cofnię<PERSON> dla", "activity.bank.serviceProvider": "Dostawca usługi", "activity.bridge.serviceProvider": "Dostawca usługi", "activity.cashback.period": "<PERSON><PERSON> cashback<PERSON>", "activity.filter.card": "Karta", "activity.rate": "<PERSON><PERSON>", "activity.receive.receivedFrom": "Otrzymano od", "activity.send.sendTo": "Wysłano do", "activity.smartContract.unknown": "Nieznany kontrakt", "activity.smartContract.usingContract": "Używany kontrakt", "activity.subtitle.pending_timer": "{timerString} Oczekująca", "activity.title.arbitrary_smart_contract_interaction": "{function} na {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Interakcja z nieznanym kontraktem", "activity.title.bridge.from": "Bridge z {token}", "activity.title.bridge.to": "Bridge do {token}", "activity.title.buy": "Kupiono {asset}", "activity.title.card_owners_updated": "Zaktualizowano właścicieli karty", "activity.title.card_spend_limit_updated": "Ustawiono limit wydatków karty", "activity.title.cashback_deposit": "Wpłata na Cashback", "activity.title.cashback_reward": "Nagroda Cashback", "activity.title.cashback_withdraw": "Wypłata z Cashback", "activity.title.claimed_reward": "Odebrano nagrodę", "activity.title.deployed_smart_wallet_gnosis": "Utworzono konto", "activity.title.deposit_from_bank": "Wpłata z banku", "activity.title.deposit_into_card": "Wpłata na kartę", "activity.title.deposit_into_earn": "<PERSON><PERSON><PERSON><PERSON> na {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} na {smartContract}", "activity.title.from": "Od {sender}", "activity.title.pendidng_areward_claim": "<PERSON><PERSON><PERSON><PERSON><PERSON> na<PERSON>", "activity.title.pendidng_breward_claim": "<PERSON><PERSON><PERSON><PERSON><PERSON> na<PERSON>", "activity.title.recharge_disabledh": "Doładowanie karty wyłączone", "activity.title.recharge_set": "Ustawiono cel doładowania", "activity.title.recovered_smart_wallet_gnosis": "Instalacja na nowym urządzeniu", "activity.title.send_pending": "Do {receiver}", "activity.title.send_to_bank": "Do banku", "activity.title.swap": "<PERSON><PERSON><PERSON> {token}", "activity.title.to": "Do {receiver}", "activity.title.withdraw_from_card": "Wypłata z karty", "activity.title.withdraw_from_earn": "<PERSON><PERSON><PERSON><PERSON><PERSON> z {earn}", "activity.transaction.networkFees": "<PERSON><PERSON><PERSON>", "activity.transaction.state": "Zakończona transakcja", "activity.transaction.state.completed": "Zakończona transakcja", "activity.transaction.state.failed": "Nieudana <PERSON>", "add-account.section.import.header": "Import", "add-another-card-owner": "Dodaj kolejnego właściciela karty", "add-another-card-owner.Recommended.footnote": "<PERSON><PERSON><PERSON> jako właściciela karty Gnosis Pay", "add-another-card-owner.Recommended.primaryText": "<PERSON><PERSON><PERSON> do Gnosis Pay", "add-another-card-owner.recommended": "Zalecane", "add-owner.confirmation.subtitle": "Ze względów bezpieczeństwa zmiana ustawień zajmie 3 minuty. W tym czasie Twoja karta będzie tymczasowo zablokowana i płatności nie będą możliwe.", "add-owner.confirmation.title": "Twoja karta będzie zablokowana na 3 minuty podczas aktualizacji ustawień", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Portfel już w użyciu, nie można do<PERSON>", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "Spróbuj z innym portfelem", "add.account.backup.decrypt.success": "Portfel przywrócony", "add.account.backup.password.passwordIncorrectMessage": "<PERSON><PERSON><PERSON> jest nieprawidłowe", "add.account.backup.password.subtitle": "Podaj hasło do Twojego pliku odzyskiwania", "add.account.backup.password.title": "<PERSON><PERSON><PERSON> hasło", "add.account.google.login.subtitle": "Zatwierdź na Google Drive synchronizację pliku", "add.account.google.login.title": "Oczekiwanie na zatwierdzenie...", "add.readonly.already_added": "Portfel już dodany", "add.readonly.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add.readonly.empty": "Wprowadź adres lub <PERSON>NS", "addBankRecipient.title": "Do<PERSON>j o<PERSON><PERSON><PERSON>", "add_funds.deposit_from_bank_account": "Wpłata z konta bankowego", "add_funds.from_another_wallet": "Z innego portfela", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Połącz z aplikacją do doładowań", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Połącz dowolny portfel z aplikacją do doładowań Zeal i szybko wyślij środki do swojego portfela", "add_funds.from_crypto_wallet.header": "Z innego portfela", "add_funds.from_crypto_wallet.header.show_wallet_address": "Pokaż adres swojego portfela", "add_funds.from_exchange.header": "Wyślij z giełdy", "add_funds.from_exchange.header.copy_wallet_address": "Skopiuj sw<PERSON>j ad<PERSON>", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance itp.", "add_funds.from_exchange.header.open_exchange": "Otw<PERSON>rz aplikację lub stronę giełdy", "add_funds.from_exchange.header.selected_token": "<PERSON><PERSON><PERSON><PERSON><PERSON> {token} do Zeal", "add_funds.from_exchange.header.selected_token.subtitle": "W sieci {network}", "add_funds.from_exchange.header.send_selected_token": "<PERSON><PERSON>ś<PERSON>j obsługiwany token", "add_funds.from_exchange.header.send_selected_token.subtitle": "Wybierz obsługiwany token i sieć", "add_funds.import_wallet": "Zaimportuj istniejący portfel krypto", "add_funds.title": "<PERSON><PERSON><PERSON> swoje konto", "add_funds.transfer_from_exchange": "Przelew z giełdy", "address.add.header": "Zobacz swój portfel w Zeal{br}w trybie tylko do odczytu", "address.add.subheader": "Wprowadź swój adres lub ENS, aby z<PERSON><PERSON><PERSON><PERSON> swoje aktywa ze wszystkich sieci EVM w jednym miejscu. Później możesz utworzyć lub zaimportować więcej portfeli.", "address_book.change_account.bank_transfers.header": "Odbiorcy bankowi", "address_book.change_account.bank_transfers.primary": "Odbiorca bankowy", "address_book.change_account.cta": "Ś<PERSON>ź portfel", "address_book.change_account.search_placeholder": "<PERSON><PERSON><PERSON> lub wys<PERSON><PERSON> adres", "address_book.change_account.tracked_header": "Portfele tylko do odczytu", "address_book.change_account.wallets_header": "Aktywne portfele", "app-association-check-failed.modal.cta": "Spróbuj ponownie", "app-association-check-failed.modal.subtitle": "Spróbuj ponownie. Problemy z łącznością powodują opóźnienia w pobieraniu kluczy Passkey. Jeśli problem będzie się pow<PERSON>ł, uruchom Zeal ponownie.", "app-association-check-failed.modal.subtitle.creation": "Spróbuj ponownie. Problemy z łącznością powodują opóźnienia w tworzeniu klucza Passkey. Jeśli problem będzie się powtarzał, uruchom Zeal ponownie.", "app-association-check-failed.modal.title.creation": "Urządzenie nie utworzyło klucza Passkey", "app-association-check-failed.modal.title.signing": "Urządzenie nie wczytało kluczy Passkey", "app.app_protocol_group.borrowed_tokens": "Pożyczone tokeny", "app.app_protocol_group.claimable_amount": "Kwota do odebrania", "app.app_protocol_group.health_rate": "Wskaźnik kondycji", "app.app_protocol_group.lending": "Pożyczki", "app.app_protocol_group.locked_tokens": "Zablokowane token<PERSON>", "app.app_protocol_group.nfts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.reward_tokens": "Tokeny w nagrodę", "app.app_protocol_group.supplied_tokens": "Dostarczone tokeny", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Nabywany token", "app.appsGroupHeader.discoverMore": "<PERSON>dk<PERSON><PERSON> więcej", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "<PERSON><PERSON>j lub wpisz adres URL", "app.error-banner.cory": "<PERSON><PERSON><PERSON><PERSON> bł<PERSON>", "app.error-banner.retry": "Ponów", "app.list_item.rewards": "Nagrody {value}", "app.position_details.health_rate.description": "To wynik dzielenia kwoty pożyczki przez wartość zabezpieczenia.", "app.position_details.health_rate.title": "<PERSON><PERSON>m jest wskaźnik kondycji?", "approval.edit-limit.label": "Edytuj limit wydatków", "approval.permit_info": "Informacje o pozwoleniu", "approval.spend-limit.edit-modal.cancel": "<PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.limit-label": "Limit wydatków", "approval.spend-limit.edit-modal.max-limit-error": "Uwaga, wysoki limit", "approval.spend-limit.edit-modal.revert": "Cofnij zmiany", "approval.spend-limit.edit-modal.set-to-unlimited": "Ustaw bez limitu", "approval.spend-limit.edit-modal.submit": "Zapisz zmiany", "approval.spend-limit.edit-modal.title": "<PERSON><PERSON><PERSON><PERSON>", "approval.spend_limit_info": "Co to jest limit wydatków?", "approval.what_are_approvals": "Czym są zatwierdzenia?", "apps_list.page.emptyState": "Brak aktywnych aplikacji", "backpace.removeLastDigit": "Usuń ostatnią cyfrę", "backup-banner.backup_now": "Utwórz kopię zapasową", "backup-banner.risk_losing_funds": "Utwórz kopię zapasową, inaczej ryzykujesz utratę środków", "backup-banner.title": "Portfel bez kopii zapasowej", "backupRecoverySmartWallet.noExportPrivateKeys": "Automatyczna kopia zapasowa: <PERSON><PERSON><PERSON>j Smart Wallet jest zapisywany jako passkey – fraza odzyskiwania ani klucz prywatny nie są potrzebne.", "backupRecoverySmartWallet.safeContracts": "Bezpieczeństwo z wieloma kluczami: portfele Zeal działają na kontraktach Safe, więc kilka urządzeń może zatwierdzić transakcję. Brak jednego punktu awarii.", "backupRecoverySmartWallet.security": "Wiele urządzeń: możesz używać swojego portfela na wielu urządzeniach za pomocą Passkey. Każde urządzenie otrzymuje własny klucz prywatny.", "backupRecoverySmartWallet.showLocalPrivateKey": "Tryb eksperta: mo<PERSON><PERSON>z wyeksportować klucz prywatny tego urządzenia, uż<PERSON>ć go w innym portfelu i połączyć się na <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Pokaż klucz prywatny</Key>", "backupRecoverySmartWallet.storingKeys": "Synchronizacja w chmurze: Passkey jest bezpiecznie przechowywany w iCloud, Mened<PERSON><PERSON><PERSON> haseł Google lub <PERSON><PERSON> menedżerze haseł.", "backupRecoverySmartWallet.title": "Kopia zapasowa i odzyskiwanie Smart Wallet", "balance-change.card.titile": "Karta", "balanceChange.pending": "Oczek<PERSON><PERSON><PERSON><PERSON>", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "<PERSON><PERSON><PERSON><PERSON> usług", "bank-transfer.change-deposit-receiver.subtitle": "Na ten portfel trafią wszystkie wpłaty.", "bank-transfer.change-deposit-receiver.title": "Ustaw portfel do odbioru", "bank-transfer.change-owner.subtitle": "Służy do logowania i odzyskiwania konta.", "bank-transfer.change-owner.title": "Ustaw właściciela konta", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Wpłaty na Zeal trafią na ten portfel.", "bank-transfer.configrm-change-deposit-receiver.title": "Zmień portfel do odbioru", "bank-transfer.configrm-change-owner.subtitle": "Zmienić właściciela? Służy do logowania.", "bank-transfer.configrm-change-owner.title": "Zmień właściciela konta", "bank-transfer.deposit.widget.status.complete": "Zakończono", "bank-transfer.deposit.widget.status.funds_received": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfer.deposit.widget.status.sending_to_wallet": "Wysyłanie do portfela", "bank-transfer.deposit.widget.status.transfer-on-hold": "Przelew wstrzymany", "bank-transfer.deposit.widget.status.transfer-received": "Wysyłanie do portfela", "bank-transfer.deposit.widget.subtitle": "{from} na {to}", "bank-transfer.deposit.widget.title": "<PERSON><PERSON>ła<PERSON>", "bank-transfer.intro.bulletlist.point_1": "Konfiguracja z Unblock", "bank-transfer.intro.bulletlist.point_2": "Przelewaj między EUR/GBP a ponad 10 tokenami", "bank-transfer.intro.bulletlist.point_3": "0% opłat do 5 tys. $ miesię<PERSON>nie, potem 0,2%", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Wysyłanie do banku", "bank-transfer.withdrawal.widget.status.in-progress": "Przelew w toku", "bank-transfer.withdrawal.widget.status.on-hold": "Przelew wstrzymany", "bank-transfer.withdrawal.widget.status.success": "Ukończono", "bank-transfer.withdrawal.widget.subtitle": "{from} do {to}", "bank-transfer.withdrawal.widget.title": "<PERSON><PERSON><PERSON>ła<PERSON>", "bank-transfers.bank-account-actions.remove-this-account": "Usuń to konto", "bank-transfers.bank-account-actions.switch-to-this-account": "Przełącz na to konto", "bank-transfers.deposit.fees-for-less-than-5k": "Opłaty za kwoty do 5 tys. $", "bank-transfers.deposit.fees-for-more-than-5k": "Opłaty za kwoty powyżej 5 tys. $", "bank-transfers.set-receiving-bank.title": "Ustaw bank odbiorcy", "bank-transfers.settings.account_owner": "Właściciel konta", "bank-transfers.settings.receiver_of_bank_deposits": "Odbiorca wpłat bankowych", "bank-transfers.settings.receiver_of_withdrawals": "Odbiorca wypłat", "bank-transfers.settings.registered_email": "Zarejestrowany e-mail", "bank-transfers.settings.title": "Ustawienia przelewów bankowych", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} Konto", "bank-transfers.setup.bank-account": "Konto bankowe", "bankTransfer.withdraw.max_loading": "Maks.: {amount}", "bank_details_do_not_match.got_it": "<PERSON><PERSON><PERSON><PERSON>", "bank_details_do_not_match.subtitle": "Kod banku i numer konta nie pasują do siebie. Sprawdź, czy dane zostały wprowadzone poprawnie i spróbuj ponownie.", "bank_details_do_not_match.title": "Dane bankowe nie pasują", "bank_tranfsers.select_country_of_residence.country_not_supported": "Przelewy bankowe nie są obsługiwane w {country} jeszcze", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Otwórz swoją aplikację bankową", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Wyślij {fiatCurrencyCode} na swoje konto", "bank_transfer.deposit.header": "{fullName}: dane konta&nbsp;osobistego", "bank_transfer.kyc_status_widget.subtitle": "Przelewy bankowe", "bank_transfer.kyc_status_widget.title": "Weryfikacja toż<PERSON>ści", "bank_transfer.personal_details.date_of_birth": "Data urodzenia", "bank_transfer.personal_details.date_of_birth.invalid_format": "Nieprawidłowa data", "bank_transfer.personal_details.date_of_birth.too_young": "Musisz mieć ukończone 18 lat", "bank_transfer.personal_details.first_name": "<PERSON><PERSON><PERSON>", "bank_transfer.personal_details.last_name": "Nazwisko", "bank_transfer.personal_details.title": "<PERSON><PERSON> dane", "bank_transfer.reference.label": "<PERSON><PERSON><PERSON> (opcjonalnie)", "bank_transfer.reference_message": "Wysłano z Zeal", "bank_transfer.residence_details.address": "<PERSON><PERSON><PERSON><PERSON> adres", "bank_transfer.residence_details.city": "<PERSON><PERSON>", "bank_transfer.residence_details.country_of_residence": "<PERSON>raj <PERSON>", "bank_transfer.residence_details.country_placeholder": "<PERSON><PERSON>", "bank_transfer.residence_details.postcode": "<PERSON><PERSON>", "bank_transfer.residence_details.street": "Ulica", "bank_transfer.residence_details.your_residence": "Twoje miejsce zamieszkania", "bank_transfers.choose-wallet.continue": "<PERSON><PERSON>", "bank_transfers.choose-wallet.test": "<PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.warning.subtitle": "Możesz połączyć tylko jeden portfel naraz. Nie będzie można zmienić połączonego portfela.", "bank_transfers.choose-wallet.warning.title": "Wybierz portfel z rozwagą", "bank_transfers.choose_wallet.subtitle": "Wybierz portfel do połączenia z kontem. ", "bank_transfers.choose_wallet.title": "<PERSON><PERSON>bierz portfel", "bank_transfers.continue": "<PERSON><PERSON>", "bank_transfers.currency_is_currently_not_supported": "<PERSON><PERSON>", "bank_transfers.deposit-header": "<PERSON><PERSON>ła<PERSON>", "bank_transfers.deposit.account-name": "Nazwa konta", "bank_transfers.deposit.account-number-copied": "Skopiowano numer konta", "bank_transfers.deposit.amount-input": "Kwota do wpłaty", "bank_transfers.deposit.amount-output": "<PERSON><PERSON>ta do<PERSON>", "bank_transfers.deposit.amount-output.error": "błąd", "bank_transfers.deposit.buttet-point.receive-crypto": "Otrzymasz {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "<PERSON><PERSON>", "bank_transfers.deposit.currency-not-supported.subtitle": "Wpłaty bankowe w {code} zostały wyłączone do odwołania.", "bank_transfers.deposit.currency-not-supported.title": "{code} wpłaty obecnie nieobsługiwane", "bank_transfers.deposit.default-token.balance": "Saldo {amount}", "bank_transfers.deposit.deposit-header": "<PERSON><PERSON>ła<PERSON>", "bank_transfers.deposit.enter_amount": "Wprowadź kwotę", "bank_transfers.deposit.iban-copied": "Skopiowano IBAN", "bank_transfers.deposit.increase-amount": "Minimalny przelew to {limit}", "bank_transfers.deposit.loading": "Ładowanie", "bank_transfers.deposit.max-limit-reached": "Kwota przekracza maksymalny limit przelewu", "bank_transfers.deposit.modal.kyc.button-text": "Rozpocznij", "bank_transfers.deposit.modal.kyc.text": "Do weryfikacji tożsamości potrzebujemy Twoich danych i dokumentów. Przesłanie ich zajmuje zwykle kilka minut.", "bank_transfers.deposit.modal.kyc.title": "Zweryfiku<PERSON>, aby zwięks<PERSON><PERSON> limity", "bank_transfers.deposit.reduce_amount": "Zmniejsz kwotę", "bank_transfers.deposit.show-account.account-number": "<PERSON><PERSON>r konta", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Kod banku", "bank_transfers.deposit.sort-code-copied": "Skopiowano kod banku", "bank_transfers.deposit.withdraw-header": "<PERSON><PERSON><PERSON>ła<PERSON>", "bank_transfers.failed_to_load_fee": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.fees": "<PERSON><PERSON><PERSON>", "bank_transfers.increase-amount": "Minimalny przelew to {limit}", "bank_transfers.insufficient-funds": "Niewystarczające środki", "bank_transfers.select_country_of_residence.title": "Gdzie mieszkasz?", "bank_transfers.setup.cta": "Skonfiguruj przelewy", "bank_transfers.setup.enter-amount": "Wprowadź kwotę", "bank_transfers.source_of_funds.form.business_income": "Dochód z działalności gospodarczej", "bank_transfers.source_of_funds.form.other": "<PERSON><PERSON>", "bank_transfers.source_of_funds.form.pension": "Emerytura", "bank_transfers.source_of_funds.form.salary": "Wynagrodzenie", "bank_transfers.source_of_funds.form.title": "<PERSON>je źródło środków", "bank_transfers.source_of_funds_description.placeholder": "Opisz źródło środków...", "bank_transfers.source_of_funds_description.title": "Opisz nam swoje źródło środków", "bank_transfers.withdraw-header": "<PERSON><PERSON><PERSON>ła<PERSON>", "bank_transfers.withdraw.amount-input": "Kwota do wypłaty", "bank_transfers.withdraw.max-limit-reached": "Kwota przekracza maksymalny limit przelewu", "bank_transfers.withdrawal.verify-id": "Zmniejsz kwotę", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Przekroczono maksymalny limit", "banner.above_maximum_limit.maximum_limit_per_deposit": "To jest maks<PERSON><PERSON>ny limit dla jednej wpłaty", "banner.above_maximum_limit.subtitle": "Przekroczono maksymalny limit", "banner.above_maximum_limit.title": "Zmniejsz kwotę do {amount} lub mniej", "banner.above_maximum_limit.title.default": "Zmniejsz kwotę", "banner.below_minimum_limit.minimum_input_limit_exceeded": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "banner.below_minimum_limit.minimum_limit_for_token": "To jest minimalny limit dla tego tokena", "banner.below_minimum_limit.title": "Zwiększ kwotę do {amount} lub więcej", "banner.below_minimum_limit.title.default": "Zwiększ kwotę", "breaard.in_porgress.info_popup.cta": "<PERSON><PERSON><PERSON><PERSON>, by <PERSON><PERSON><PERSON><PERSON> {earn}", "breaard.in_porgress.info_popup.footnote": "Używaj<PERSON><PERSON> Zeal/Gnosis Pay akceptujesz warunki.", "breaward.in_porgress.info_popup.bullet_point_1": "<PERSON><PERSON><PERSON> {remaining} w cią<PERSON> najb<PERSON>ższ<PERSON> {time} , by ode<PERSON><PERSON> tę nagro<PERSON>.", "breaward.in_porgress.info_popup.bullet_point_2": "Liczą się tylko zakupy kartą Gnosis Pay.", "breaward.in_porgress.info_popup.bullet_point_3": "Po odebraniu nagroda trafi na konto Zeal.", "breaward.in_porgress.info_popup.header": "<PERSON><PERSON><PERSON><PERSON> {earn}, w<PERSON><PERSON><PERSON><PERSON> {remaining}", "breward.celebration.for_spending": "Za płacenie kartą", "breward.dc25-eligible-celebration.for_spending": "<PERSON><PERSON><PERSON> w gronie pierwszych {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "<PERSON><PERSON> j<PERSON> w gronie pierws<PERSON>ch {limit} wyd<PERSON><PERSON><PERSON>ch", "breward.expired_banner.earn_by_spending": "<PERSON><PERSON><PERSON><PERSON> {earn} w<PERSON><PERSON><PERSON><PERSON> {amount}", "breward.expired_banner.reward_expired": "{earn} nagroda wygasła", "breward.in_progress_banner.cta.title": "<PERSON><PERSON><PERSON><PERSON>, by <PERSON><PERSON><PERSON><PERSON> {earn}", "breward.ready_to_claim.error.try_again": "Spróbuj ponownie", "breward.ready_to_claim.error_title": "<PERSON>e udało się odebrać nagrody", "breward.ready_to_claim.in_progress": "<PERSON><PERSON><PERSON><PERSON><PERSON> na<PERSON>", "breward.ready_to_claim.youve_earned": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {earn}!", "breward_already_claimed.title": "Nagroda została już odebrana. <PERSON><PERSON><PERSON> nie otrzy<PERSON>ł<PERSON>ś tokena, skontaktuj się z obsługą klienta.", "breward_cannotbe_claimed.title": "Nie można teraz odebrać nagrody. Spróbuj ponownie później.", "bridge.best_return": "Trasa z najlepszym zwrotem", "bridge.best_serivce_time": "Najszybsza trasa", "bridge.check_status.complete": "Ukończono", "bridge.check_status.progress_text": "Bridge {from} do {to}", "bridge.remove_topup": "<PERSON><PERSON>ń doładowanie", "bridge.request_status.completed": "Ukończono", "bridge.request_status.pending": "Oczek<PERSON><PERSON><PERSON><PERSON>", "bridge.widget.completed": "Ukończono", "bridge.widget.currencies": "{from} do {to}", "bridge_rote.widget.title": "Bridge", "browse.discover_more_apps": "Odkryj więcej aplikacji", "browse.google_search_term": "<PERSON><PERSON>j \"{searchTerm}\"", "brward.celebration.you_earned": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "brward.expired_banner.subtitle": "Następnym razem się uda", "brward.in_progress_banner.subtitle": "Wygasa za {expiredInFormatted}", "buy": "<PERSON><PERSON>", "buy.enter_amount": "Wprowadź kwotę", "buy.loading": "Ładowanie...", "buy.no_routes_found": "Nie znaleziono tras", "buy.not_enough_balance": "Niewystarczaj<PERSON>ce saldo", "buy.select-currency.title": "<PERSON><PERSON><PERSON><PERSON>", "buy.select-to-currency.title": "<PERSON><PERSON>", "buy_form.title": "<PERSON><PERSON> token", "cancelled-card.create-card-button.primary": "Zamów nową kartę wirtualną", "cancelled-card.switch-card-button.primary": "Zmień kartę", "cancelled-card.switch-card-button.short-text": "Ma<PERSON> inną aktywną kartę", "card": "Karta", "card-add-cash.confirm-stage.banner.no-routes-found": "<PERSON><PERSON>, spróbuj z innym tokenem lub kwotą", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {amount} wi<PERSON><PERSON><PERSON> {symbol} , aby u<PERSON><PERSON><PERSON> op<PERSON>ty", "card-add-cash.confirm-stage.banner.value-loss": "<PERSON><PERSON><PERSON><PERSON> {loss} war<PERSON><PERSON><PERSON>", "card-add-cash.confirm-stage.banner.value-loss.revert": "Cof<PERSON>j", "card-add-cash.edit-stage.cta.cancel": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.continue": "<PERSON><PERSON>", "card-add-cash.edit-stage.cta.enter-amount": "Wpisz kwotę", "card-add-cash.edit-stage.cta.reduce-to-max": "Do maks.", "card-add-cash.edit-staget.banner.no-routes-found": "<PERSON><PERSON>, spróbuj z innym tokenem lub kwotą", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Żądanie transakcji wysłano do portfela sprzętowego. Kontynuuj na urządzeniu.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Podpisz w portfelu sprzętowym", "card-balance": "Saldo: {balance}", "card-cashback.status.title": "Wpłata na cashback", "card-copy-safe-address.copy_address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card-copy-safe-address.copy_address.done": "Skopiowano", "card-copy-safe-address.warning.description": "Ten adres może odbierać tylko {cardAsset} w sieci Gnosis Chain. Nie wysyłaj na ten adres aktywów z innych sieci. Zostaną utracone.", "card-copy-safe-address.warning.header": "<PERSON><PERSON><PERSON><PERSON><PERSON> {cardAsset} w sieci G<PERSON> Chain", "card-marketing-card.center.subtitle": "Opłaty FX", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "Oprocentowanie", "card-marketing-card.right.subtitle": "Prezent na start", "card-marketing-card.title": "Wysoko oprocentowana karta VISA w Europie", "card-marketing-tile.get-started": "Zacznij", "card-select-from-token-title": "<PERSON><PERSON>bierz token źródłowy", "card-top-up.banner.subtitle.completed": "Ukończono", "card-top-up.banner.subtitle.failed": "Niepowodzenie", "card-top-up.banner.subtitle.pending": "{timerString} W toku", "card-top-up.banner.title": "W<PERSON><PERSON><PERSON><PERSON> {amount}", "card-topup.select-token.emptyState": "Nie znaleziono tokenów", "card.activate.card_number_not_valid": "Błędny numer karty. Sprawdź i spróbuj ponownie.", "card.activate.invalid_card_number": "Nieprawidłowy numer karty.", "card.activation.activate_physical_card": "Aktywuj kartę fizyczną", "card.add-cash.amount-to-withdraw": "Kwota doładowania", "card.add-from-earn-form.title": "<PERSON><PERSON><PERSON>j kart<PERSON>", "card.add-from-earn-form.withdraw-to-card": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "card.add-from-earn.amount-to-withdraw": "Kwota do wypłaty na kartę", "card.add-from-earn.enter-amount": "Wprowadź kwotę", "card.add-from-earn.loading": "Ładowanie", "card.add-from-earn.max-label": "Saldo: {amount}", "card.add-from-earn.no-routes-found": "Nie znaleziono trasy", "card.add-from-earn.not-enough-balance": "Niewystarczaj<PERSON>ce saldo", "card.add-owner.queued": "Dodawanie właściciela w kolejce", "card.add-to-wallet-flow.subtitle": "Płać ze swojego portfela", "card.add-to-wallet.copy-card-number": "Skopiuj numer karty poniżej", "card.add-to-wallet.title": "<PERSON><PERSON><PERSON> do {platformName} Wallet", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "ANULOWANO", "card.card-owner-not-found.disconnect-btn": "Odłącz kartę od Zeal", "card.card-owner-not-found.subtitle": "Aby dalej używać karty w Zeal, zaktualizuj jej właściciela i połącz ją ponownie.", "card.card-owner-not-found.title": "Połącz kartę ponownie", "card.card-owner-not-found.update-owner-btn": "Zmień właściciela karty", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} za {date}", "card.cashback.widgetNoCashback.subtitle": "<PERSON><PERSON><PERSON><PERSON>, by <PERSON><PERSON><PERSON><PERSON><PERSON>", "card.cashback.widgetNoCashback.title": "Zyskaj do {defaultPercentage} cashbacku", "card.cashback.widgetcashbackValue.rewards": "{amount} oczekuj<PERSON>ce", "card.cashback.widgetcashbackValue.title": "{percentage} cashbacku", "card.choose-wallet.connect_card": "Połącz kartę", "card.choose-wallet.create-new": "Dodaj nowy portfel jako właściciela", "card.choose-wallet.import-another-wallet": "Zaimportuj inny portfel", "card.choose-wallet.import-current-owner": "Zaimportuj obecnego właściciela karty", "card.choose-wallet.import-current-owner.sub-text": "Importuj klucze/frazę seed w<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.choose-wallet.title": "Wybierz portfel do zarządzania kartą", "card.connectWalletToCardGuide": "Skopiuj ad<PERSON>", "card.connectWalletToCardGuide.addGnosisPayOwner": "Dodaj właściciela Gnosis Pay", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Otwórz Gnosispay.com w swoim drugim portfelu{br}2. <PERSON><PERSON><PERSON><PERSON> „Konto”{br}3. <PERSON><PERSON><PERSON><PERSON> „Szczegóły konta”{br}4. <PERSON><PERSON><PERSON><PERSON> „Edytuj” obok „Właściciel konta” i{br}5. <PERSON><PERSON><PERSON><PERSON> „Dodaj adres”{br}6. <PERSON><PERSON><PERSON> swój adres Zeal i kliknij Zapisz", "card.connectWalletToCardGuide.header": "Połącz {account} z kartą Gnosis Pay", "card.connect_card.start": "Połącz kartę Gnosis Pay", "card.copiedAddress": "Skopiowano {formattedAddress}", "card.disconnect-account.title": "Odłącz konto", "card.hw-wallet-support-drop.add-owner-btn": "Dodaj właściciela karty", "card.hw-wallet-support-drop.disconnect-btn": "Odłącz kartę od Zeal", "card.hw-wallet-support-drop.subtitle": "Aby dalej używać karty w Zeal, dodaj do niej właściciela niebędącego portfelem sprzętowym.", "card.hw-wallet-support-drop.title": "Zeal nie wspiera już portfeli sprzętowych dla karty", "card.kyc.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kon<PERSON>gu<PERSON>", "card.list_item.title": "Karta", "card.onboarded.transactions.empty.description": "Twoja historia płatności pojawi się tutaj", "card.onboarded.transactions.empty.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "card.order.continue": "Dokończ zamówienie karty", "card.order.free_virtual_card": "Zamów wirtualną za darmo", "card.order.start": "Zamów kartę za darmo", "card.owner-not-imported.cancel": "<PERSON><PERSON><PERSON>", "card.owner-not-imported.import": "Import<PERSON>j", "card.owner-not-imported.subtitle": "Aby autoryzować tę transakcję, po<PERSON><PERSON><PERSON> z Zeal portfel właściciela konta Gnosis Pay. Uwaga: To osobna operacja niż logowanie do portfela Gnosis Pay.", "card.owner-not-imported.title": "Dodaj właściciela konta Gnosis Pay", "card.page.order_free_physical_card": "Zamów fizyczną za darmo", "card.pin.change_pin_at_atm": "PIN można zmienić w wybranych bankomatach", "card.pin.timeout": "Ekran zamknie się za {seconds} s", "card.quick-actions.add-assets": "<PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.add-cash": "<PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.details": "Szczegóły", "card.quick-actions.freeze": "Zablokuj", "card.quick-actions.freezing": "Blokowanie", "card.quick-actions.unfreeze": "Odblokuj", "card.quick-actions.unfreezing": "Odblokowywanie", "card.quick-actions.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.read-only-detected.create-new": "Dodaj nowy portfel jako właściciela", "card.read-only-detected.import-current-owner": "Importuj klucze do {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Importuj klucze/frazę seed portfela {address}", "card.read-only-detected.title": "Karta w portfelu read-only. Wybierz.", "card.remove-owner.queued": "Usuwanie właściciela w kolejce", "card.settings.disconnect-from-zeal": "Odłącz od Zeal", "card.settings.edit-owners": "Zmień właścicieli karty", "card.settings.getCard": "Zamów kolejną kartę", "card.settings.getCard.subtitle": "<PERSON><PERSON><PERSON> wirtualne lub fizy<PERSON>ne", "card.settings.notRecharging": "Brak autodoładowania", "card.settings.notifications.subtitle": "Otrzymuj powiadomienia o płatnościach", "card.settings.notifications.title": "Powiadomienia z karty", "card.settings.page.title": "Ustawienia karty", "card.settings.select-card.cancelled-cards": "<PERSON><PERSON><PERSON><PERSON> karty", "card.settings.setAutoRecharge": "Ustaw autodoładowanie", "card.settings.show-card-address": "Pokaż adres karty", "card.settings.spend-limit": "Ustaw limit wydatków", "card.settings.spend-limit-title": "Obecny limit dzienny: {limit}", "card.settings.switch-active-card": "Zmień aktywną kartę", "card.settings.switch-active-card-description": "Aktywna karta: {card}", "card.settings.switch-card.card-item.cancelled": "Anulowana", "card.settings.switch-card.card-item.frozen": "Zablokowana", "card.settings.switch-card.card-item.title": "Karta Gnosis Pay", "card.settings.switch-card.card-item.title.physical": "<PERSON><PERSON>", "card.settings.switch-card.card-item.title.virtual": "<PERSON><PERSON> wirtualna", "card.settings.switch-card.title": "<PERSON><PERSON>bierz kartę", "card.settings.targetBalance": "Docelowe saldo: {threshold}", "card.settings.view-pin": "Pokaż PIN", "card.settings.view-pin-description": "Zawsze chroń swój PIN", "card.title": "Karta", "card.transactions.header": "Transakcje kartą", "card.transactions.see_all": "Zobacz wszystkie transakcje", "card.virtual": "WIRTUALNA", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Cashback jest wysyłany na Twoją kartę na początku tygodnia po jego uzyskaniu.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "<PERSON>m więcej wpła<PERSON>z, tym więcej zarobisz na każdym zakupie.", "cardCashback.onboarding.title": "Zyskaj do {percentage} cashbacku", "cardCashbackWithdraw.amount": "Kwota wypłaty", "cardCashbackWithdraw.header": "<PERSON><PERSON><PERSON><PERSON><PERSON> {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "Karta jest zablokowana i nie można jej aktywować", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "Do {percentage}", "cardWidget.startEarning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardWithdraw.amount": "Kwota wypłaty", "cardWithdraw.header": "Wypłać z karty", "cardWithdraw.selectWithdrawWallet.title": "W<PERSON>bierz portfel do{br} wyp<PERSON><PERSON>", "cardWithdraw.success.cta": "Zamknij", "cardWithdraw.success.subtitle": "Ze względów bezpieczeństwa przetwarzanie wszystkich wypłat z karty Gnosis Pay zajmuje 3 minuty", "cardWithdraw.success.title": "Ta zmiana zajmie 3 minuty", "card_top_up_trx.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card_top_up_trx.to": "Do", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "<PERSON>", "cards.card_number": "<PERSON><PERSON>r karty", "cards.choose-wallet.no-active-accounts": "Nie masz żadnych aktywnych portfeli", "cards.copied_card_number": "Skopiowano numer karty", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Przekroczono limit dzienny", "cards.transactions.decline_reason.incorrect_pin": "Nieprawidłowy PIN", "cards.transactions.decline_reason.incorrect_security_code": "Nieprawidłowy kod CVV", "cards.transactions.decline_reason.invalid_amount": "Nieprawidłowa kwota", "cards.transactions.decline_reason.low_balance": "Niewystarczaj<PERSON>ce saldo", "cards.transactions.decline_reason.other": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cards.transactions.decline_reason.pin_tries_exceeded": "Przekroczono limit prób PIN", "cards.transactions.status.refund": "Zwrot środków", "cards.transactions.status.reversal": "Cofnięcie transakcji", "cashback-deposit.trx.title": "Wpłata na Cashback", "cashback-estimate.text": "To szacunkowa kwota, a nie gwarancja wypłaty. Zastosowaliśmy wszystkie publicznie znane zasady cashbacku, ale Gnosis Pay może wykluczyć transakcje według własnego uznania. Maksymalne wydatki w wysokości {amount} tygodniowo kwalifikuje się do cashbacku, nawet jeśli szacunkowa kwota dla tej transakcji wskazywałaby na wyższą sumę.", "cashback-estimate.text.fallback": "Kwota szacunkowa. Decyzja Gnosis Pay.", "cashback-estimate.title": "S<PERSON>owany cashback", "cashback-onbarding-tersm.subtitle": "Dane o transakcjach Twojej karty zostaną udostępnione firmie Karpatkey, która odpowiada za dystrybucję nagród cashback. Klikając „Akceptuję”, wyrażasz zgodę na warunki programu cashback Gnosis DAO <terms>Regulamin</terms>", "cashback-onbarding-tersm.title": "Regulamin i prywatność", "cashback-tx-activity.retry": "Spróbuj ponownie", "cashback-unconfirmed-payments-info.subtitle": "Płatności kwalifikują się do programu cashback po ich rozliczeniu ze sprzedawcą. Do tego czasu widnieją jako płatności niepotwierdzone. Nierozliczone płatności nie kwalifikują się do programu cashback.", "cashback-unconfirmed-payments-info.title": "Niepotwierdzone płatności kartą", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "<PERSON><PERSON>ła<PERSON>", "cashback.activity.title": "Ostatnia aktywność", "cashback.activity.withdrawal": "<PERSON><PERSON><PERSON>ła<PERSON>", "cashback.deposit": "<PERSON><PERSON><PERSON><PERSON>", "cashback.deposit.amount.label": "Kwota wpłaty", "cashback.deposit.change": "{from} na {to}", "cashback.deposit.confirmation.subtitle": "Stawki cashbacku są aktualizowane raz w tygodniu. Wp<PERSON><PERSON> teraz, aby zwięks<PERSON> cashback w przyszłym tygodniu.", "cashback.deposit.confirmation.title": "Zacz<PERSON><PERSON> {percentage} od {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Wymień tokeny na {currency} w sieci {network} Chain", "cashback.deposit.get.tokens.title": "Zdobądź {currency} tokeny", "cashback.deposit.header": "W<PERSON><PERSON><PERSON> {currency}", "cashback.deposit.max_label": "Maks.: {amount}", "cashback.deposit.select-wallet.title": "Wybierz portfel do wpłaty", "cashback.deposit.yourcashback": "<PERSON><PERSON><PERSON><PERSON> cashback", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "<PERSON><PERSON><PERSON><PERSON> portfel{br} do wyp<PERSON>ty", "cashback.transaction-details.network-label": "<PERSON><PERSON><PERSON>", "cashback.transaction-details.reward-period": "{start} – {end}", "cashback.transaction-details.top-row.label-deposit": "Od", "cashback.transaction-details.top-row.label-rewards": "<PERSON><PERSON> cashback<PERSON>", "cashback.transaction-details.top-row.label-withdrawal": "Do", "cashback.transaction-details.transaction": "ID transakcji", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Transak<PERSON>je cashback", "cashback.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.withdraw.confirmation.cashback_reduction": "Cashback za ten tydzień, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ju<PERSON>arobione <PERSON>, zmniejszy się z {before} do {after}", "cashback.withdraw.queued": "Wypłata w kolejce", "cashback.withdrawal.change": "{from} na {to}", "cashback.withdrawal.confirmation.subtitle": "Rozpocznij wypłatę {amount} z 3-minutowym opóźnieniem. Zmniejszy to Twój cashback do {after}.", "cashback.withdrawal.confirmation.title": "Cashback z<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>, je<PERSON><PERSON> wypłacisz GNO", "cashback.withdrawal.delayTransaction.title": "Rozpocznij wypłatę GNO z{br} 3-minutowym opóźnieniem", "cashback.withdrawal.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.withdrawal.yourcashback": "<PERSON><PERSON><PERSON><PERSON> cashback", "celebration.aave": "Zarobione z Aave", "celebration.cashback.subtitle": "Dostarczono w {code}", "celebration.cashback.subtitleGNO": "{amount} ostatnio zarobione", "celebration.chf": "Zarobione z Frankencoin", "celebration.lido": "Zarobione z Lido", "celebration.sky": "Zarobione ze Sky", "celebration.title": "Całkowity cashback", "celebration.well_done.title": "<PERSON><PERSON><PERSON>na robota!", "change-withdrawal-account.add-new-account": "Dodaj kolejne konto bankowe", "change-withdrawal-account.item.shortText": "{currency} Konto", "check-confirmation.approve.footer.for": "Dla", "checkConfirmation.title": "Wynik transakcji", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin i Ether", "collateral.ethereum": "Ethereum", "collateral.other": "<PERSON><PERSON>", "collateral.rwa": "Rzeczywiste aktywa", "collateral.stablecoins": "Stablecoiny (powiązane z USD)", "collateral.us-t-bills": "<PERSON><PERSON> s<PERSON>bowe <PERSON>", "confirm-bank-transfer-recipient.bullet-1": "Zero opłat za cyfrowe EUR", "confirm-bank-transfer-recipient.bullet-2": "Wpłaty na {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Udostępnij dane konta Gnosis Pay firmie Monerium, autoryzowanej i regulowanej Instytucji Pieniądza Elektronicznego. <link>Dowiedz się więcej</link>", "confirm-bank-transfer-recipient.bullet-4": "Zaak<PERSON>pt<PERSON>j <link>regulamin Monerium</link>", "confirm-bank-transfer-recipient.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.cancel": "<PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.confirm": "Potwierdź", "confirm-change-withdrawal-account.saving": "Zapisuję", "confirm-change-withdrawal-account.subtitle": "Wszystkie wypłaty z Zeal trafią na to konto.", "confirm-change-withdrawal-account.title": "Zmień bank odbiorcy", "confirm-ramove-withdrawal-account.title": "Usuń konto bankowe", "confirm-remove-withdrawal-account.subtitle": "Dane konta usuniemy. Możesz je dodać znów.", "confirmTransaction.finalNetworkFee": "<PERSON><PERSON><PERSON>", "confirmTransaction.importKeys": "Importuj klucze", "confirmTransaction.networkFee": "<PERSON><PERSON><PERSON>", "confirmation.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> {amount} do {recipient}", "conflicting-monerium-account.add-owner": "Dodaj jako właściciela Gnosis Pay", "conflicting-monerium-account.create-wallet": "Utwórz nowy Smart Wallet", "conflicting-monerium-account.disconnect-card": "Odłącz kartę od Zeal, połącz z nowym.", "conflicting-monerium-account.header": "{wallet} jest połączony z innym kontem Monerium", "conflicting-monerium-account.subtitle": "Zmień portfel właściciela Gnosis Pay", "connection.diconnected.got_it": "Rozumiem!", "connection.diconnected.page1.subtitle": "Zeal działa wszę<PERSON>zie tam, gdzie Met<PERSON>sk. Po prostu połącz się tak samo jak z Metamask.", "connection.diconnected.page1.title": "Jak połączyć się z Zeal?", "connection.diconnected.page2.subtitle": "Zobaczysz wiele opcji. Zeal może być jedną z nich. Je<PERSON>li Zeal się nie pojawi...", "connection.diconnected.page2.title": "Klik<PERSON>j „Połącz portfel”", "connection.diconnected.page3.subtitle": "Zaproponujemy połączenie z Zeal. „<PERSON>rowser” lub „Injected” też powinny zadziałać. Spróbuj!", "connection.diconnected.page3.title": "<PERSON><PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.caution": "Ostrożnie", "connectionSafetyCheck.tag.danger": "Niebezpieczeństwo", "connectionSafetyCheck.tag.passed": "<PERSON><PERSON><PERSON><PERSON>", "connectionSafetyConfirmation.subtitle": "<PERSON>zy na pewno chcesz kontynuować?", "connectionSafetyConfirmation.title": "Ta strona wygląda na niebezpieczną", "connection_state.connect.cancel": "<PERSON><PERSON><PERSON>", "connection_state.connect.changeToMetamask": "Zmień na MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Zmień na MetaMask", "connection_state.connect.connect_button": "Połącz", "connection_state.connect.expanded.connected": "Połącz<PERSON>", "connection_state.connect.expanded.title": "Połącz", "connection_state.connect.safetyChecksLoading": "Sprawdzanie bezpieczeństwa strony", "connection_state.connect.safetyChecksLoadingError": "<PERSON>e udało się ukończyć kontroli bezpieczeństwa", "connection_state.connected.expanded.disconnectButton": "Rozłącz Zeal", "connection_state.connected.expanded.title": "Połącz<PERSON>", "copied-diagnostics": "Skopiowano diagnostykę", "copy-diagnostics": "<PERSON><PERSON><PERSON><PERSON> diagnostykę", "counterparty.component.add_recipient_primary_text": "Do<PERSON>j o<PERSON><PERSON><PERSON>", "counterparty.country": "<PERSON><PERSON>", "counterparty.countryTitle": "<PERSON><PERSON>", "counterparty.currency": "<PERSON><PERSON><PERSON>", "counterparty.delete.success.title": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.edit.success.title": "Zapisano zmiany", "counterparty.errors.country_required": "<PERSON><PERSON><PERSON><PERSON> kraj", "counterparty.errors.first_name.invalid": "<PERSON><PERSON><PERSON> p<PERSON>o by<PERSON> dłu<PERSON>", "counterparty.errors.last_name.invalid": "Nazwisko powinno być dłuż<PERSON>e", "counterparty.first_name": "<PERSON><PERSON><PERSON>", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Wyślij na konto bankowe", "countrySelector.noCountryFound": "Nie znaleziono kraju", "countrySelector.title": "<PERSON><PERSON><PERSON>rz kraj", "create-passkey.cta": "Utw<PERSON>rz passkey", "create-passkey.extension.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "create-passkey.footnote": "Obsługiwane przez", "create-passkey.mobile.cta": "Utwórz klucz dostępu", "create-passkey.steps.enable-recovery": "Skonfiguruj odzyskiwanie w chmurze", "create-passkey.steps.setup-biometrics": "Włącz zabezpieczenia biometryczne", "create-passkey.subtitle": "Passkeys są bezpieczniejsze niż hasła i szyfrowane w chmurze, co ułatwia ich odzyskanie.", "create-passkey.title": "Zabezpiecz konto", "create-smart-wallet": "Utwórz Smart Wallet", "create-userop.progress.text": "Tworzenie", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Przejdź do Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "Dokończ zamówienie karty w Gnosis Pay", "create_recharge_preferences.card": "Karta", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "<PERSON><PERSON><PERSON><PERSON> {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "<PERSON><PERSON><PERSON><PERSON>", "create_recharge_preferences.link_accounts_title": "Połącz konta", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "Twoja karta NIE będzie doładowywać się automatycznie po każdej płatności.", "create_recharge_preferences.not_configured_title": "Zarabiaj i wydawaj", "create_recharge_preferences.recharge_from_earn_accounts_description": "Twoja karta doładowuje się automatycznie po każdej płatności z Twojego konta Earn.", "create_recharge_preferences.subtitle": "rocznie", "creating-account.loading": "Tworz<PERSON>e konta", "creating-gnosis-pay-account": "Tworz<PERSON>e konta", "currencies.bridge.select_routes.emptyState": "<PERSON><PERSON> znaleźliśmy tras dla tego bridge", "currency.add_currency.add_token": "<PERSON><PERSON><PERSON> token", "currency.add_currency.not_a_valid_address": "To nie jest prawidłowy adres tokena", "currency.add_currency.token_decimals_feild": "Liczba miejsc po przecinku tokena", "currency.add_currency.token_feild": "<PERSON><PERSON>a", "currency.add_currency.token_symbol_feild": "Symbol tokena", "currency.add_currency.update_token": "Aktualizuj token", "currency.add_custom.remove_token.cta": "Us<PERSON>ń token", "currency.add_custom.remove_token.header": "Us<PERSON>ń token", "currency.add_custom.remove_token.subtitle": "Token będzie ukryty, ale saldo pozostanie.", "currency.add_custom.token_removed": "To<PERSON> us<PERSON>", "currency.add_custom.token_updated": "Token zak<PERSON>alizowany", "currency.balance_label": "Saldo: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "Twój przelew bankowy został pomyślnie zrealizowany, przeliczając {fiat} na {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "Otrzymano {crypto}", "currency.bankTransfer.deposit_status.success": "Otrzymano w portfelu", "currency.bankTransfer.deposit_status.title": "<PERSON><PERSON>ła<PERSON>", "currency.bankTransfer.off_ramp.check_bank_account": "Sprawdź swoje konto bankowe", "currency.bankTransfer.off_ramp.complete": "Ukończono", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Wysyłanie do Twojego banku", "currency.bankTransfer.off_ramp.transferring_to_currency": "Przesyłanie do {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Środki powinny już być na Twoim koncie.", "currency.bankTransfer.withdrawal_status.success": "Wysłano do Twojego banku", "currency.bankTransfer.withdrawal_status.title": "<PERSON><PERSON><PERSON>ła<PERSON>", "currency.bank_transfer.create_unblock_user.email": "Adres e-mail", "currency.bank_transfer.create_unblock_user.email_invalid": "Nieprawidłowy e-mail", "currency.bank_transfer.create_unblock_user.email_missing": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_missing": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "<PERSON><PERSON><PERSON> litery, cyfry, spacje, -.,&()' ", "currency.bank_transfer.create_unblock_user.last_name": "Nazwisko", "currency.bank_transfer.create_unblock_user.last_name_missing": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "<PERSON><PERSON><PERSON> litery, cyfry, spacje, -.,&()' ", "currency.bank_transfer.create_unblock_user.note": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, akceptujesz warunki Unblock: <terms>Regulamin</terms> i <policy>Polityk<PERSON></policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Podaj imię i nazwisko jak w banku.", "currency.bank_transfer.create_unblock_user.title": "Połącz swoje konto bankowe", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "<PERSON><PERSON>r konta", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Kraj banku", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "<PERSON>fer<PERSON><PERSON> waluta", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Kod banku", "currency.bank_transfer.create_unblock_withdraw_account.success": "<PERSON><PERSON> s<PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.title": "Połącz swoje konto bankowe", "currency.bank_transfer.residence-form.address-required": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.address-unsupported-char": "<PERSON><PERSON><PERSON>y, c<PERSON><PERSON>ry, spacje i , ; {apostrophe} - \\\\ są dozwolone.", "currency.bank_transfer.residence-form.city-required": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.city-unsupported-char": "<PERSON><PERSON><PERSON>y, c<PERSON><PERSON><PERSON>, spacje i . , - & ( ) {apostrophe} są do<PERSON>wo<PERSON>.", "currency.bank_transfer.residence-form.postcode-invalid": "Nieprawidłowy kod pocztowy", "currency.bank_transfer.residence-form.postcode-required": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.validation.invalid.account_number": "Nieprawidłowy numer konta", "currency.bank_transfer.validation.invalid.iban": "Nieprawidłowy IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Nieprawidłowy kod banku", "currency.bridge.amount_label": "Kwota do bridge'u", "currency.bridge.best_returns.subtitle": "Największa kwota po odliczeniu opłat.", "currency.bridge.best_returns_popup.title": "Najlepszy zwrot", "currency.bridge.bridge_from": "Z", "currency.bridge.bridge_gas_fee_loading_failed": "Błąd ładowania opłaty sieciowej", "currency.bridge.bridge_low_slippage": "Zbyt niski poślizg. Spróbuj go zwiększyć.", "currency.bridge.bridge_provider": "Dostawca przelewu", "currency.bridge.bridge_provider_loading_failed": "Wystąpił problem z ładowaniem dostawców", "currency.bridge.bridge_settings": "Ustawienia Bridge", "currency.bridge.bridge_status.subtitle": "<PERSON><PERSON><PERSON> {name}", "currency.bridge.bridge_status.title": "Bridge", "currency.bridge.bridge_to": "Do", "currency.bridge.fastest_route_popup.subtitle": "Ten dostawca jest najszybszy.", "currency.bridge.fastest_route_popup.title": "Najszybsza trasa", "currency.bridge.from": "Z", "currency.bridge.success": "Ukończono", "currency.bridge.title": "Bridge", "currency.bridge.to": "Do", "currency.bridge.topup": "<PERSON><PERSON><PERSON><PERSON> {symbol}", "currency.bridge.withdrawal_status.title": "<PERSON><PERSON><PERSON>ła<PERSON>", "currency.card.card_top_up_status.title": "<PERSON><PERSON><PERSON>j kart<PERSON>", "currency.destination_amount": "<PERSON><PERSON>ta do<PERSON>", "currency.hide_currency.confirm.subtitle": "Ukryj token. Moż<PERSON>z go potem odkryć.", "currency.hide_currency.confirm.title": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "currency.hide_currency.success.title": "Token ukryty", "currency.label": "Etykieta (opcjonalnie)", "currency.last_name": "Nazwisko", "currency.max_loading": "Maks.:", "currency.swap.amount_to_swap": "Kwota do wymiany", "currency.swap.best_return": "Trasa z najlepszym zyskiem", "currency.swap.destination_amount": "<PERSON><PERSON>ta do<PERSON>", "currency.swap.header": "<PERSON><PERSON><PERSON>", "currency.swap.max_label": "Saldo: {amount}", "currency.swap.provider.header": "Dostawca swapa", "currency.swap.select_to_token": "<PERSON><PERSON><PERSON><PERSON>", "currency.swap.swap_gas_fee_loading_failed": "Błąd ładowania opłaty sieciowej", "currency.swap.swap_provider_loading_failed": "Błąd ładowania dostawców", "currency.swap.swap_settings": "Ustawienia wymiany", "currency.swap.swap_slippage_too_low": "Bardzo niski poślizg. Spróbuj go zwiększyć", "currency.swaps_io_native_token_swap.subtitle": "Przez Swaps.IO", "currency.swaps_io_native_token_swap.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.withdrawal.amount_from": "Z", "currency.withdrawal.amount_to": "Do", "currencySelector.title": "<PERSON><PERSON><PERSON><PERSON> wa<PERSON>", "dApp.wallet-does-not-support-chain.subtitle": "Wygląda na to, że Twój portfel nie obsługuje {network}. Spróbuj połączyć się z innym portfelem lub użyj Zeal.", "dApp.wallet-does-not-support-chain.title": "Nieobsługiwana sieć", "dapp.connection.manage.confirm.disconnect.all.cta": "Rozłącz", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Czy na pewno chcesz rozłączyć wszystkie połączenia?", "dapp.connection.manage.confirm.disconnect.all.title": "Rozłącz wszystko", "dapp.connection.manage.connection_list.main.button.title": "Rozłącz", "dapp.connection.manage.connection_list.no_connections": "<PERSON>e masz połączonych aplikacji", "dapp.connection.manage.connection_list.section.button.title": "Rozłącz wszystko", "dapp.connection.manage.connection_list.section.title": "Aktywne", "dapp.connection.manage.connection_list.title": "Połączenia", "dapp.connection.manage.disconnect.success.title": "Aplikacje rozłączone", "dapp.metamask_mode.title": "<PERSON><PERSON>", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "Oprocentowanie", "dc25-card-marketing-card.right.subtitle": "100 osób", "dc25-card-marketing-card.title": "<PERSON><PERSON><PERSON> 100 osób, które wyda 50 €, otrzyma {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Ta akcja będzie niedostępna przez 3 minuty. Ze względów bezpieczeństwa przetworzenie zmian w ustawieniach karty lub wypłat zajmuje 3 minuty.", "delayQueueBusyBanner.processing-yout-action.title": "Przetwarzamy Twoją akcję, poczekaj", "delayQueueBusyWidget.cardFrozen": "Karta zablokowana", "delayQueueBusyWidget.processingAction": "Przetwarzanie Twojej a<PERSON>cji", "delayQueueFailedBanner.action-incomplete.get-support": "Uzyskaj pomoc", "delayQueueFailedBanner.action-incomplete.subtitle": "Coś poszło nie tak z wypłatą lub aktualizacją ustawień. Skontaktuj się z pomocą na Discordzie.", "delayQueueFailedBanner.action-incomplete.title": "Operacja nieukończona", "delayQueueFailedWidget.actionIncomplete.title": "Akcja na karcie nieukończona", "delayQueueFailedWidget.cardFrozen.subtitle": "Karta zablokowana", "delayQueueFailedWidget.contactSupport": "Skontaktuj się z pomocą", "delay_queue_busy.subtitle": "Ze względów bezpieczeństwa zmiany ustawień karty i wypłaty trwają 3 minuty. W tym czasie Twoja karta jest zamrożona.", "delay_queue_busy.title": "Twoja operacja jest przetwarzana", "delay_queue_failed.contact_support": "<PERSON><PERSON><PERSON><PERSON>", "delay_queue_failed.subtitle": "Coś poszło nie tak z wypłatą lub aktualizacją ustawień. Skontaktuj się z pomocą na Discordzie.", "delay_queue_failed.title": "Skontaktuj się z pomocą", "deploy-earn-form-smart-wallet.in-progress.title": "Przygotowywanie Earn", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "disconnect-card-popup.cancel": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.disconnect": "Odłącz", "disconnect-card-popup.subtitle": "Spowoduje to usunięcie karty z aplikacji Zeal. Twój portfel nadal będzie połączony z kartą w aplikacji Gnosis Pay. Możesz ponownie połączyć kartę w dowolnym momencie.", "disconnect-card-popup.title": "Odłącz kartę", "distance.long.days": "{count} dni", "distance.long.hours": "{count} godzin", "distance.long.minutes": "{count} minut", "distance.long.months": "{count} <PERSON><PERSON><PERSON><PERSON>", "distance.long.seconds": "{count} sekund", "distance.long.years": "{count} lat", "distance.short.days": "{count} d", "distance.short.hours": "{count} h", "distance.short.minutes": "{count} min", "distance.short.months": "{count} m", "distance.short.seconds": "{count} s", "distance.short.years": "{count} r", "duration.short.days": "{count}d", "duration.short.hours": "{count}h", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "Wpłacasz", "earn-deposit-view.into": "Na", "earn-deposit-view.to": "Do", "earn-deposit.swap.transfer-provider": "Dostawca przelewu", "earn-taker-investment-details.accrued-realtime": "Naliczane na bieżąco", "earn-taker-investment-details.asset-class": "Klasa aktywów", "earn-taker-investment-details.asset-coverage-ratio": "Współczynnik pokrycia aktywów", "earn-taker-investment-details.asset-reserve": "Rezerwa aktywów", "earn-taker-investment-details.base_currency.label": "<PERSON><PERSON>uta bazowa", "earn-taker-investment-details.chf.description": "Zarabiaj na swoich CHF, wpłacając zCHF do Frankencoin – zaufanego cyfrowego rynku pieniężnego. Zysk jest generowany z niskiego ryzyka, nadzabezpieczonych pożyczek na Frankencoin i wypłacany w czasie rzeczywistym. Twoje środki pozostają bezpieczne na subkoncie, które tylko Ty kontrolujesz.", "earn-taker-investment-details.chf.description.with_address_link": "Zarabiaj na swoich CHF, wpłacając zCHF do Frankencoin – zaufanego cyfrowego rynku pieniężnego. Zysk jest generowany z niskiego ryzyka, nadzabezpieczonych pożyczek na Frankencoin i wypłacany w czasie rzeczywistym. Twoje środki pozostają bezpieczne na subkoncie <link>(skopiuj 0x)</link> nad którym tylko Ty masz kontrolę.", "earn-taker-investment-details.chf.label": "<PERSON><PERSON><PERSON><PERSON> frank s<PERSON>", "earn-taker-investment-details.collateral-composition": "Skład <PERSON>", "earn-taker-investment-details.depositor-obligations": "Zobowiązania wobec deponentów", "earn-taker-investment-details.eure.description": "Zarabiaj odsetki od swoich euro, wpłacając EURe do Aave – zaufanego cyfrowego rynku pieniężnego. EURe to w pełni regulowany stablecoin euro emitowany przez Monerium i zabezpieczony w stosunku 1:1 na chronionych kontach. Odsetki są generowane z nisko ryzykownych, nadzabezpieczonych pożyczek na Aave i wypłacane w czasie rzeczywistym. Twoje środki pozostają na bezpiecznym subkoncie, które tylko Ty kontrolujesz.", "earn-taker-investment-details.eure.description.with_address_link": "Zarabiaj odsetki od swoich euro, wpłacając EURe do Aave – zaufanego cyfrowego rynku pieniężnego. EURe to w pełni regulowany stablecoin euro emitowany przez Monerium i zabezpieczony w stosunku 1:1 na chronionych kontach. Odsetki są generowane z nisko ryzykownych, nadzabezpieczonych pożyczek na Aave i wypłacane w czasie rzeczywistym. Twoje środki pozostają na bezpiecznym subkoncie <link>(kopiuj 0x)</link> , które tylko Ty kontrolujesz.", "earn-taker-investment-details.eure.label": "Cyfrowe euro (EURe)", "earn-taker-investment-details.faq": "FAQ", "earn-taker-investment-details.fixed-income": "Stały dochód", "earn-taker-investment-details.issuer": "Emitent", "earn-taker-investment-details.key-facts": "Najważniejsze informacje", "earn-taker-investment-details.liquidity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.operator": "Operator r<PERSON><PERSON>", "earn-taker-investment-details.projected-yield": "Prognozowany roczny zysk", "earn-taker-investment-details.see-other-faq": "Zobacz wszystkie inne FAQ", "earn-taker-investment-details.see-realtime": "Zobacz dane na żywo", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Zysk za ostatnie 12 miesięcy", "earn-taker-investment-details.total-collateral": "Całkowite zabezpieczenie", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "Całkowita podaż ZCHF", "earn-taker-investment-details.total_deposits": "Suma depozytów w Aave", "earn-taker-investment-details.usd.description": "Sky to cyfrowy rynek pieniężny oferujący stabilne zyski w dolarach amerykańskich z krótkoterminowych amerykańskich obligacji skarbowych i nadzabezpieczonych pożyczek – bez zmienności typowej dla krypto, z dostępem do środków 24/7 i przejrzystym zabezpieczeniem on-chain.", "earn-taker-investment-details.usd.description.with_address_link": "Sky to cyfrowy rynek pieniężny oferujący stabilne zyski w dolarach amerykańskich z krótkoterminowych amerykańskich obligacji skarbowych i nadzabezpieczonych pożyczek – bez zmienności typowej dla krypto, z dostępem do środków 24/7 i przejrzystym zabezpieczeniem on-chain. Inwestycje znajdują się na subkoncie <link>(kopiuj 0x)</link> , które Ty kontrolujesz.", "earn-taker-investment-details.usd.ftx-difference": "<PERSON><PERSON><PERSON> to się różni od FTX, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> czy Luna?", "earn-taker-investment-details.usd.high-returns": "<PERSON><PERSON> to mo<PERSON>li<PERSON>, że zyski są tak wysokie, zwłaszcza w porównaniu z tradycyjnymi bankami?", "earn-taker-investment-details.usd.how-is-backed": "J<PERSON>e jest zabezpieczenie Sky USD i co stanie się z moimi pieniędzmi, je<PERSON><PERSON> Zeal zbankrutuje?", "earn-taker-investment-details.usd.income-sources": "Źródła dochodu 2024", "earn-taker-investment-details.usd.insurance": "<PERSON>zy moje środki są ubezpieczone lub gwarantowane przez jakąkolwiek instytucję (jak FDIC lub podobną)?", "earn-taker-investment-details.usd.label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON> am<PERSON>", "earn-taker-investment-details.usd.lose-principal": "<PERSON>zy realnie mogę stracić wpłacony kapitał i w jakich okolicznościach?", "earn-taker-investment-details.variable-rate": "Pożyczki o zmiennym oprocentowaniu", "earn-taker-investment-details.withdraw-anytime": "Wypłacaj w dowolnym momencie", "earn-taker-investment-details.yield": "Zysk", "earn-withdrawal-view.approve.for": "Dla", "earn-withdrawal-view.approve.into": "Na", "earn-withdrawal-view.swap.into": "Na", "earn-withdrawal-view.withdraw.to": "Do", "earn.add_another_asset.title": "Wybierz aktywa do zarabiania", "earn.add_asset": "<PERSON><PERSON><PERSON>", "earn.asset_view.title": "<PERSON><PERSON><PERSON><PERSON>", "earn.base-currency-popup.text": "Waluta bazowa to waluta, w której wyceniane i rejestrowane są Twoje depozyty, zyski i transakcje. <PERSON><PERSON><PERSON> wpła<PERSON>z środki w innej walucie (np. EUR na konto w USD), zostaną one natychmiast przeliczone na walutę bazową po bieżącym kursie. Po przewalutowaniu Twoje saldo pozostaje stabilne w walucie bazowej, ale przyszłe wypłaty mogą ponownie wiązać się z przewalutowaniem.", "earn.base-currency-popup.title": "<PERSON><PERSON>uta bazowa", "earn.card-recharge.disabled.list-item.title": "Automatyczne doładowanie wyłączone", "earn.card-recharge.enabled.list-item.title": "Automatyczne doładowanie włączone", "earn.choose_wallet_to_deposit.title": "Wpłać z", "earn.config.currency.eth": "Zarabiaj na Ethereum", "earn.config.currency.on_chain_address_subtitle": "Adres on-chain", "earn.config.currency.us_dollars": "Skonfiguruj przelewy bankowe", "earn.configured_widget.current_apy.title": "Aktualne APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} rocznie", "earn.confirm.currency.cta": "<PERSON><PERSON><PERSON><PERSON>", "earn.currency.eth": "Zarabiaj na Ethereum", "earn.deploy.status.title": "Utwórz konto Earn", "earn.deploy.status.title_with_taker": "Utwórz {title} konto <PERSON>n", "earn.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn.deposit.amount_to_deposit": "Kwota do wpłaty", "earn.deposit.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn.deposit.enter_amount": "Wpisz kwotę", "earn.deposit.no_routes_found": "Nie znaleziono tras", "earn.deposit.not_enough_balance": "Niewystarczaj<PERSON>ce saldo", "earn.deposit.select-currency.title": "Wybierz token do wpłaty", "earn.deposit.select_account.title": "<PERSON><PERSON><PERSON><PERSON> konto Earn", "earn.desposit_form.title": "Wpłata do Earn", "earn.earn_deposit.status.title": "Wpłata na konto Earn", "earn.earn_deposit.trx.title": "Wpłać do Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "Wypłacaj środki w dowolnym momencie", "earn.earn_withdraw.status.title": "Wypłata z konta Earn", "earn.earn_withdraw.trx.title.approval": "Zatwierdź wypłatę", "earn.earn_withdraw.trx.title.withdraw_into_asset": "<PERSON><PERSON><PERSON><PERSON><PERSON> jako {asset}", "earn.earn_withdraw.trx.title.withdrawal": "Wypłać z Earn", "earn.recharge.cta": "Zapisz zmiany", "earn.recharge.earn_not_configured.enable_some_account.error": "Włącz konto", "earn.recharge.earn_not_configured.enter_amount.error": "Wpisz kwotę", "earn.recharge.select_taker.header": "Zasilaj kartę w kolejności z", "earn.recharge_card_tag.on": "wł.", "earn.recharge_card_tag.recharge": "Do<PERSON>do<PERSON><PERSON>", "earn.recharge_card_tag.recharge_not_configured": "Autodoładowanie", "earn.recharge_card_tag.recharge_off": "Doładowanie wył.", "earn.recharge_card_tag.recharged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharging": "Do<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.recharge_configured.disable.trx.title": "Wyłącz autodoładowanie", "earn.recharge_configured.trx.disclaimer": "<PERSON><PERSON><PERSON><PERSON>, tworzona jest aukcja Cowswap w celu zakupu tej samej kwoty, co <PERSON><PERSON>, przy użyciu Twoich aktywów Earn. Ten proces aukcyjny zazwyczaj zapewnia najlepszy kurs rynkowy, ale pamiętaj, że kurs w sieci może różnić się od rzeczywistych kursów wymiany.", "earn.recharge_configured.trx.subtitle": "Po każdej płatności środki będą automatycznie dodawane z Twoich kont Earn, aby utr<PERSON><PERSON><PERSON> saldo karty na poziomie {value}", "earn.recharge_configured.trx.title": "Ustaw autodoładowanie na {value}", "earn.recharge_configured.updated.trx.title": "Zap<PERSON>z ustawienia doładowania", "earn.risk-banner.subtitle": "Ten produkt nie jest regulowany i nie zapewnia ochrony przed stratą.", "earn.risk-banner.title": "Poznaj ryzyka", "earn.set_recharge.status.title": "Ustaw autodoładowanie", "earn.setup_reacharge.input.disable.label": "Wyłącz", "earn.setup_reacharge.input.label": "Docelowe saldo karty", "earn.setup_reacharge_form.title": "Autozasilanie dba o to, by <PERSON><PERSON>{br}karta mia<PERSON> z<PERSON> to samo saldo", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Trzymaj wstETH (stakowane ETH) na Gnosis Chain i pożyczaj przez Lido.", "earn.taker-bulletlist.point_1": "Zarabiaj {apyValue} rocznie. Zyski zależą od rynku.", "earn.taker-bulletlist.point_3": "Zeal nie pobiera żadnych opłat.", "earn.taker-historical-returns": "Historyczne zyski", "earn.taker-historical-returns.chf": "Wzrost CHF do USD", "earn.taker-investment-tile.apy.perYear": "rocznie", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Zarabianie z Aave na Gnosis Chain", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Zarabianie z Frankencoin na Gnosis Chain", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Zarabianie z Lido na Gnosis Chain", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Zarabianie z Maker na Gnosis Chain", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "Zarabianie na ETH", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH Earn", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "Amerykańskie obligacje skarbowe (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "<PERSON><PERSON><PERSON> jest to ni<PERSON><PERSON><PERSON> rzadkie, te<PERSON><PERSON><PERSON><PERSON> jest to możliwe. Twoje środki są chronione przez ścisłe zarządzanie ryzykiem i wysokie zabezpieczenie. Realistyczny czarny scenariusz obejmowałby bezprecedensowe warunki rynkowe, takie jak jednoczesna utrata powiązania z dolarem przez wiele stablecoinów – co nigdy wcześniej się nie zdarzyło.", "earn.usd.can-I-lose-my-principal-popup.title": "<PERSON>zy realnie mogę stracić wpłacony kapitał i w jakich okolicznościach?", "earn.usd.ftx-difference-popup.text": "Sky jest fundamentalnie inne. W przeciwieństwie do FTX, <PERSON><PERSON><PERSON>, BlockFi czy Luna, które opierały się na scentralizowanym przechowywaniu, nieprzejrzystym zarządzaniu aktywami i ryzykownych pozycjach lewarowanych, Sky USD wykorzystuje przejrzyste, audytowane, zdecentralizowane smart kontrakty i zachowuje pełną transparentność on-chain. Zachowujesz pełną kontrolę nad swoimi środkami w prywatnym portfelu, co znacznie zmniejsza ryzyko kontrahenta związane z upadkiem scentralizowanych podmiotów.", "earn.usd.ftx-difference-popup.title": "<PERSON><PERSON><PERSON> to się różni od FTX, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> czy Luna?", "earn.usd.high-returns-popup.text": "Sky USD generuje zyski głównie poprzez protokoły zdecentralizowanych finansów (DeFi), które automatyzują pożyczki peer-to-peer i dostarczanie płynności, eliminując koszty i pośredników tradycyjnej bankowości. Ta wydajność, w połączeniu z solidną kontrolą ryzyka, pozwala na znacznie wyższe zyski w porównaniu z tradycyjnymi bankami.", "earn.usd.high-returns-popup.title": "<PERSON><PERSON> to mo<PERSON>li<PERSON>, że zyski są tak wysokie, zwłaszcza w porównaniu z tradycyjnymi bankami?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD jest w pełni zabezpieczone i nadzabezpieczone przez kombinację aktywów cyfrowych przechowywanych w bezpiecznych smart kontraktach oraz aktywów ze świata rzeczywistego, takich jak amerykańskie obligacje skarbowe. Rezerwy można audytować w czasie rzeczywistym on-chain, nawet z poziomu Zeal, co zapewnia przejrzystość i bezpieczeństwo. W mało prawdopodobnym przypadku zamknięcia Zeal, Twoje aktywa pozostają zabezpieczone on-chain, pod Twoją pełną kontrolą i dostępne za pośrednictwem innych kompatybilnych portfeli.", "earn.usd.how-is-sky-backed-popup.title": "J<PERSON>e jest zabezpieczenie Sky USD i co stanie się z moimi pieniędzmi, je<PERSON><PERSON> Zeal zbankrutuje?", "earn.usd.insurance-popup.text": "Środki Sky USD nie są ubezpieczone przez FDIC ani objęte tradycyjnymi gwarancjami rządowymi, poni<PERSON><PERSON><PERSON> jest to konto oparte na aktywach cyfrowych, a nie tradycyjne konto bankowe. <PERSON><PERSON><PERSON> tego, Sky zarządza ryzykiem poprzez audytowane smart kontrakty i starannie zweryfikowane protokoły DeFi, zapewniając bezpieczeństwo i przejrzystość aktywów.", "earn.usd.insurance-popup.title": "<PERSON>zy moje środki są ubezpieczone lub gwarantowane przez jakąkolwiek instytucję (jak FDIC lub podobną)?", "earn.usd.lending-operations-popup.text": "Sky USD generuje zysk, pożyczając stablecoiny na zdecentralizowanych rynkach pożyczkowych, takich jak Morpho i Spark. Twoje stablecoiny są pożyczane pożyczkobiorcom, którzy wpłacają znacznie wyższe zabezpieczenie – takie jak ETH lub BTC – niż wartość ich pożyczki. To podejście, zwane nadzabezpieczeniem, zapewnia, że zawsze jest wystarczająco dużo zabezpieczenia na pokrycie pożyczek, co znacznie zmniejsza ryzyko. Zebrane odsetki i okazjonalne opłaty likwidacyjne od pożyczkobiorców zapewniają wiarygodne, przejrzyste i bezpieczne zyski.", "earn.usd.lending-operations-popup.title": "Operacje pożyczkowe", "earn.usd.market-making-operations-popup.text": "Sky USD zarabia dodatkowy zysk, uczest<PERSON><PERSON><PERSON><PERSON> w zdecentralizowanych giełdach (AMM), takich jak Curve czy Uniswap. Dostarczając płynność – umieszczając Twoje stablecoiny w pulach, które ułatwiają handel krypto – Sky USD przechwytuje opłaty generowane z transakcji. Te pule płynności są starannie dobierane, aby zminimal<PERSON><PERSON>, głównie przy użyciu par stablecoin-stablecoin, aby znacznie zmniejszyć ryzyko, takie jak nietrwała strata, utrzymując Twoje aktywa bezpieczne i dostępne.", "earn.usd.market-making-operations-popup.title": "Operacje market making", "earn.usd.treasury-operations-popup.text": "Sky USD generuje stabilny, stały zysk poprzez strategiczne inwestycje skarbowe. Część Twoich depozytów w stablecoinach jest alokowana w bezpieczne, nisko ryzykowne aktywa ze świata rzeczywistego – głównie krótkoterminowe obligacje rządowe i wysoce bezpieczne instrumenty kredytowe. To podejście, podobne do tradycyjnej bankowości, zapewnia przewidywalny i wiarygodny zysk. Twoje aktywa pozostają bezpieczne, płynne i zarządzane w przejrzysty sposób.", "earn.usd.treasury-operations-popup.title": "<PERSON><PERSON><PERSON>", "earn.view_earn.card_rechard_off": "W<PERSON>ł.", "earn.view_earn.card_rechard_on": "Wł.", "earn.view_earn.card_recharge": "Doładowanie karty", "earn.view_earn.total_balance_label": "Zyskujesz {percentage} rocznie", "earn.view_earn.total_earnings_label": "Całkowity zysk", "earn.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.withdraw.amount_to_withdraw": "Kwota do wypłaty", "earn.withdraw.enter_amount": "Wprowadź kwotę", "earn.withdraw.loading": "Ładowanie", "earn.withdraw.no_routes_found": "Nie znaleziono ścieżek", "earn.withdraw.not_enough_balance": "Niewystarczaj<PERSON>ce saldo", "earn.withdraw.select-currency.title": "<PERSON><PERSON><PERSON><PERSON>", "earn.withdraw.select_to_token": "<PERSON><PERSON><PERSON><PERSON>", "earn.withdraw.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.withdraw_form.title": "Wypłata z Earn", "earnings-view.earnings": "Całkowite zyski", "edit-account-owners.add-owner.add-wallet": "Do<PERSON>j w<PERSON>ś<PERSON>la", "edit-account-owners.add-owner.add_wallet": "Do<PERSON>j w<PERSON>ś<PERSON>la", "edit-account-owners.add-owner.title": "Dodaj właściciela karty", "edit-account-owners.card-owners": "Właściciele karty", "edit-account-owners.external-wallet": "Portfel zewnętrzny", "editBankRecipient.title": "<PERSON><PERSON><PERSON><PERSON>", "editNetwork.addCustomRPC": "Dodaj niestandardowy węzeł RPC", "editNetwork.cannot_verify.subtitle": "Niestandardowy węzeł RPC nie odpowiada prawidłowo. Sprawdź adres URL i spróbuj ponownie.", "editNetwork.cannot_verify.title": "Nie możemy zweryfikować węzła RPC", "editNetwork.cannot_verify.try_again": "Ponów", "editNetwork.customRPCNode": "Niestandardowy węzeł RPC", "editNetwork.defaultRPC": "Domyślny RPC", "editNetwork.networkRPC": "RPC sieci", "editNetwork.rpc_url.cannot_be_empty": "<PERSON><PERSON><PERSON><PERSON>", "editNetwork.rpc_url.not_a_valid_https_url": "Wprowadź prawidłowy adres URL HTTP(S)", "editNetwork.safetyWarning.subtitle": "Zeal nie może zagwara<PERSON><PERSON>, niez<PERSON>odnoś<PERSON> ani bezpieczeństwa niestandardowych RPC. Czy na pewno chcesz użyć niestandardowego węzła RPC?", "editNetwork.safetyWarning.title": "Niestandardowe RPC mogą by<PERSON> nieb<PERSON>", "editNetwork.zealRPCNode": "Węzeł RPC Zeal", "editNetworkRpc.headerTitle": "Niestandardowy węzeł RPC", "editNetworkRpc.rpcNodeUrl": "Adres URL węzła RPC", "editing-locked.modal.description": "W przeciwieństwie do transakcji zatwierdzających (Approval), pozwolenia (Permit) nie pozwalają na edycję limitu wydatków ani czasu wygaśnięcia. Upewnij <PERSON>ę, że ufasz aplikacji, zanim wyślesz pozwolenie.", "editing-locked.modal.title": "Ed<PERSON><PERSON>ja zablokowana", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Włączanie zasilania", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Zasilanie włączone", "enterCardnumber": "Wpisz numer karty", "error.connectivity_error.subtitle": "Sprawdź połączenie z internetem i spróbuj ponownie.", "error.connectivity_error.title": "Brak połączenia z internetem", "error.decrypt_incorrect_password.title": "Nieprawidłowe hasło", "error.encrypted_object_invalid_format.title": "Usz<PERSON>d<PERSON> dane", "error.failed_to_fetch_google_auth_token.title": "<PERSON>e udało się uzyskać dostępu", "error.list.item.cta.action": "Ponów", "error.trezor_action_cancelled.title": "Transakcja odrzucona", "error.trezor_device_used_elsewhere.title": "Urządzenie jest używane w innej sesji", "error.trezor_method_cancelled.title": "<PERSON><PERSON> udało się zsynchronizować Trezora", "error.trezor_permissions_not_granted.title": "<PERSON><PERSON> udało się zsynchronizować Trezora", "error.trezor_pin_cancelled.title": "<PERSON><PERSON> udało się zsynchronizować Trezora", "error.trezor_popup_closed.title": "<PERSON><PERSON> udało się zsynchronizować Trezora", "error.unblock_account_number_and_sort_code_mismatch": "Niezgodny numer konta i kod banku", "error.unblock_can_not_change_details_after_kyc": "<PERSON>e moż<PERSON>z zmienić danych po weryfikacji.", "error.unblock_hard_kyc_failure": "Nieoczekiwany stan KYC", "error.unblock_invalid_faster_payment_configuration.title": "Ten bank nie obsługuje Faster Payments", "error.unblock_invalid_iban": "Nieprawidłowy IBAN", "error.unblock_session_expired.title": "<PERSON><PERSON><PERSON> wygasła", "error.unblock_user_with_address_already_exists.title": "Konto jest już przypisane do tego adresu", "error.unblock_user_with_such_email_already_exists.title": "Użytkownik o tym e-mailu już istnieje", "error.unknown_error.error_message": "Komunikat błędu: ", "error.unknown_error.subtitle": "Przepraszamy! Jeśli potrzebujesz pilnej pomocy, skontaktuj się z obsługą i podaj poniższe dane.", "error.unknown_error.title": "Błąd systemu", "eth-cost-warning-modal.subtitle": "Portfele Smart Wallet działają na Ethereum, ale opłaty są bardzo wysokie. Zdecydowanie zalecamy korzystanie z innych sieci.", "eth-cost-warning-modal.title": "Unikaj Ethereum – wysokie opłaty sieciowe", "exchange.form.button.chain_unsupported": "<PERSON><PERSON><PERSON>", "exchange.form.button.refreshing": "Odświeżanie", "exchange.form.error.asset_not_supported.button": "Wybierz inne aktywo", "exchange.form.error.asset_not_supported.description": "Bridge nie obsługuje tego aktywa.", "exchange.form.error.asset_not_supported.title": "Aktywo nieobsługiwane", "exchange.form.error.bridge_quote_timeout.button": "Wybierz inne aktywo", "exchange.form.error.bridge_quote_timeout.description": "Spróbuj innej pary tokenów", "exchange.form.error.bridge_quote_timeout.title": "Nie znaleziono wymiany", "exchange.form.error.different_receiver_not_supported.button": "Usuń alternatywnego odbiorcę", "exchange.form.error.different_receiver_not_supported.description": "Nie można wysłać na inny adres.", "exchange.form.error.different_receiver_not_supported.title": "Adres wysyłki i odbioru musi być ten sam.", "exchange.form.error.insufficient_input_amount.button": "Zwiększ kwotę", "exchange.form.error.insufficient_liquidity.button": "Zmniejsz kwotę", "exchange.form.error.insufficient_liquidity.description": "Za mało aktywów w bridge. Zmniejsz kwotę.", "exchange.form.error.insufficient_liquidity.title": "Zbyt duża kwota", "exchange.form.error.max_amount_exceeded.button": "Zmniejsz kwotę", "exchange.form.error.max_amount_exceeded.description": "Przekroczono maksymalną kwotę.", "exchange.form.error.max_amount_exceeded.title": "Zbyt duża kwota", "exchange.form.error.min_amount_not_met.button": "Zwiększ kwotę", "exchange.form.error.min_amount_not_met.description": "<PERSON><PERSON> o<PERSON>ągni<PERSON><PERSON> minimalnej kwoty wymiany.", "exchange.form.error.min_amount_not_met.description_with_amount": "Minimalna kwota wymiany to {amount}.", "exchange.form.error.min_amount_not_met.title": "Zbyt mała kwota", "exchange.form.error.min_amount_not_met.title_increase": "Zwiększ kwotę", "exchange.form.error.no_routes_found.button": "Wybierz inne aktywo", "exchange.form.error.no_routes_found.description": "<PERSON>rak trasy dla tej pary tokena i sieci.", "exchange.form.error.no_routes_found.title": "<PERSON><PERSON> <PERSON>tę<PERSON><PERSON>j wym<PERSON>y", "exchange.form.error.not_enough_balance.button": "Zmniejsz kwotę", "exchange.form.error.not_enough_balance.description": "Nie masz wystarczająco tego aktywa.", "exchange.form.error.not_enough_balance.title": "Niewystarczaj<PERSON>ce saldo", "exchange.form.error.slippage_passed_is_too_low.button": "Zwiększ poślizg", "exchange.form.error.slippage_passed_is_too_low.description": "Poślizg jest za niski dla tego aktywa.", "exchange.form.error.slippage_passed_is_too_low.title": "Zbyt niski poślizg", "exchange.form.error.socket_internal_error.button": "Spróbuj ponownie później", "exchange.form.error.socket_internal_error.description": "Wystąpił błąd partnera. Spróbuj później.", "exchange.form.error.socket_internal_error.title": "Błąd u partnera bridge'u", "exchange.form.error.stargatev2_requires_fee_in_native": "<PERSON><PERSON><PERSON> {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "<PERSON><PERSON><PERSON> {amount} , aby <PERSON><PERSON><PERSON> transakcję", "exchange.form.error.stargatev2_requires_fee_in_native.title": "Potrzeba więcej {symbol}", "expiration-info.modal.description": "<PERSON>zas wygaśnięcia określa, jak długo aplikacja może używać Twoich tokenów. Po tym czasie traci dostęp. Dla bezpieczeństwa ustawiaj krótki czas wygaśnięcia.", "expiration-info.modal.title": "<PERSON><PERSON><PERSON> jest czas wygaśnięcia?", "expiration-time.high.modal.text": "<PERSON>zas wygaśnięcia powinien być krótki i oparty na tym, jak długo będziesz go potrzebować. Długie okresy są ryzykowne i dają oszustom więcej szans na niewłaściwe wykorzystanie Twoich tokenów.", "expiration-time.high.modal.title": "Długi czas wygaśnięcia", "failed.transaction.content": "Transakcja prawdopodobnie się nie powiedzie", "fee.unknown": "<PERSON><PERSON><PERSON><PERSON>", "feedback-request.leave-message": "Zostaw wiadom<PERSON>ść", "feedback-request.not-now": "<PERSON><PERSON> te<PERSON>", "feedback-request.title": "Dzięki! Jak możemy ulepszyć Zeal?", "float.input.period": "Separator <PERSON>", "gnosis-activate-card.info-popup.subtitle": "Przy pierwszej transakcji musisz włożyć kartę i podać PIN. Potem płatności zbliżeniowe będą działa<PERSON>.", "gnosis-activate-card.info-popup.title": "Pierwsza płatność wymaga chipa i PIN-u", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "<PERSON><PERSON>z numer karty, aby j<PERSON> a<PERSON>y<PERSON>.", "gnosis-activate-card.title": "<PERSON><PERSON>r karty", "gnosis-pay-re-kyc-widget.btn-text": "Zweryfikuj", "gnosis-pay-re-kyc-widget.title.not-started": "Zweryfikuj swoją to<PERSON><PERSON>", "gnosis-pay.login.cta": "Połącz istniejące konto", "gnosis-pay.login.title": "Masz j<PERSON>ż konto Gnosis Pay", "gnosis-signup.confirm.subtitle": "Sprawdź e-mail od Gnosis Pay w spamie", "gnosis-signup.confirm.title": "Nie masz e-maila weryfikacyjnego?", "gnosis-signup.continue": "<PERSON><PERSON>", "gnosis-signup.dont_link_accounts": "Nie łącz kont", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "Wpisz twó***********", "gnosis-signup.enter-email.title": "Podaj e-mail", "gnosis-signup.title": "Przeczytałem i akceptuję <linkGnosisTNC>Regulamin</linkGnosisTNC> <monovateTerms>Warunki posiadacza karty</monovateTerms> oraz <linkMonerium>Regulamin Monerium</linkMonerium>.", "gnosis-signup.verify-email.title": "Zweryfikuj e-mail", "gnosis.confirm.subtitle": "Nie masz kodu? Sprawdź numer telefonu.", "gnosis.confirm.title": "Kod wysłano na numer {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Weryfiku<PERSON>", "gnosisPayAccountStatus.success.title": "<PERSON><PERSON>", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay nie jest dostępne w <PERSON><PERSON> kraju", "gnosisPayNoActiveCardsFound.title": "Brak aktywnych kart", "gnosis_pay_card_delay_relay_not_empty_error.title": "Nie można teraz przetworzyć Twojej transakcji. Spróbuj ponownie później.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "<PERSON>e można wydać karty", "gnosiskyc.modal.approved.activate-free-card": "Aktywuj darmową kartę", "gnosiskyc.modal.approved.button-text": "Wpłać z konta bankowego", "gnosiskyc.modal.approved.title": "Utworzono dane Twojego konta osobistego", "gnosiskyc.modal.failed.close": "Zamknij", "gnosiskyc.modal.failed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nasz partner <PERSON><PERSON> Pay nie może utworzyć dla Ciebie konta", "gnosiskyc.modal.in-progress.title": "Weryfikacja tożsamości może potrwać 24 godziny lub dłużej. Dziękujemy za cierpliwość.", "goToSettingsPopup.settings": "Ustawienia", "goToSettingsPopup.title": "Powiadomienia możesz włączyć w ustawieniach urządzenia w dowolnym momencie", "google_file.error.failed_to_fetch_auth_token.button_title": "Spróbuj ponownie", "google_file.error.failed_to_fetch_auth_token.subtitle": "<PERSON><PERSON> użyć pliku odzyskiwania, zezwól na dostęp do Twojej chmury.", "google_file.error.failed_to_fetch_auth_token.title": "<PERSON>e udało się uzyskać dostępu", "hidden_tokens.widget.emptyState": "Brak ukrytych tokenów", "how_to_connect_to_metamask.got_it": "OK, rozumiem", "how_to_connect_to_metamask.story.subtitle": "Przełączaj się łatwo między Zeal a innymi portfelami w dowolnym momencie.", "how_to_connect_to_metamask.story.title": "Zeal działa z innymi portfelami", "how_to_connect_to_metamask.why_switch": "Dlaczego przełączać się między Zeal a innymi portfelami?", "how_to_connect_to_metamask.why_switch.description": "Niezależnie od tego, który portfel wybierzesz, Kontrole Bezpieczeństwa Zeal zawsze będą Cię chronić przed złośliwymi stronami i transakcjami.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "<PERSON><PERSON><PERSON>, że trudno jest zrobić pierwszy krok i zacząć używać nowego portfela. Dlatego ułatwiliśmy korzystanie z Zeal obok Twojego obecnego portfela. Przełączaj się w każdej chwili.", "import-bank-transfer-owner.banner.title": "Zmieniono portfel. Importuj go, by <PERSON><PERSON><PERSON> z przelewów.", "import-bank-transfer-owner.title": "Importuj <PERSON>fel, aby używać przelewów bankowych na tym urządzeniu", "import_gnosispay_wallet.add-another-card-owner.footnote": "Importuj klucz/frazę seed właś<PERSON><PERSON>la", "import_gnosispay_wallet.primaryText": "Importuj portfel Gnosis Pay", "injected-wallet": "Portfel przeglądarki", "intercom.getHelp": "Uzyskaj pomoc", "invalid_iban.got_it": "<PERSON><PERSON><PERSON><PERSON>", "invalid_iban.subtitle": "Wprowadzony IBAN jest nieprawidłowy. Sprawdź, czy dane zostały wprowadzone poprawnie i spróbuj ponownie.", "invalid_iban.title": "Nieprawidłowy IBAN", "keypad-0": "Klawisz 0", "keypad-1": "Klawisz 1", "keypad-2": "Klawisz 2", "keypad-3": "Klawisz 3", "keypad-4": "Klawisz 4", "keypad-5": "Klawisz 5", "keypad-6": "Klawisz 6", "keypad-7": "Klawisz 7", "keypad-8": "Klawisz 8", "keypad-9": "Klawisz 9", "keypad.biometric-button": "Przycisk biometryczny klawiatury", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Tajna fraza zabezpieczona 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "Zobacz frazę", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Przechowuj bezpieczną kopię offline swojej tajnej frazy, aby móc później odzyskać swoje środki", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "Nie próbuj zgadywać słowa", "keystore.write_secret_phrase.before_you_begin.first_point": "<PERSON><PERSON><PERSON><PERSON>, że każda osoba posiadająca moją tajną frazę może przelać moje środki", "keystore.write_secret_phrase.before_you_begin.second_point": "Odpowiadam za bezpieczeństwo i poufność mojej tajnej frazy", "keystore.write_secret_phrase.before_you_begin.subtitle": "Przeczytaj i zaakceptuj poniższe punkty:", "keystore.write_secret_phrase.before_you_begin.third_point": "Jestem w prywatnym miejscu, bez innych osób i kamer w pobliżu", "keystore.write_secret_phrase.before_you_begin.title": "Zanim zaczniesz", "keystore.write_secret_phrase.secret_phrase_test.title": "<PERSON><PERSON><PERSON><PERSON> to słowo numer {count} w <PERSON><PERSON>j tajnej frazie?", "keystore.write_secret_phrase.test_ps.lets_do_it": "<PERSON><PERSON><PERSON><PERSON><PERSON> to", "keystore.write_secret_phrase.test_ps.subtitle": "Będziesz potrzebować swojej tajnej frazy do odtworzenia konta na tym lub innych urządzeniach. Sprawdźmy, czy Twoja tajna fraza jest zapisana poprawnie.", "keystore.write_secret_phrase.test_ps.subtitle2": "Zweryfiku<PERSON>my {count} słów z Twojej frazy.", "keystore.write_secret_phrase.test_ps.title": "Test odzyskiwania konta", "kyc.modal.approved.button-text": "Zrób przelew bankowy", "kyc.modal.approved.subtitle": "Weryfikacja ukończona. Przelewy bez limitu.", "kyc.modal.approved.title": "Przelewy bankowe odblokowane", "kyc.modal.continue-with-partner.button-text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc.modal.continue-with-partner.subtitle": "Teraz przekierujemy Cię do naszego partnera, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "kyc.modal.continue-with-partner.title": "Kontynuuj z naszym partnerem", "kyc.modal.failed.unblock.subtitle": "Unblock odr<PERSON>cił Twoją weryfikację.", "kyc.modal.failed.unblock.title": "Wniosek Unblock odrzucony", "kyc.modal.paused.button-text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dane", "kyc.modal.paused.subtitle": "Sprawdź swoje dane i spróbuj ponownie.", "kyc.modal.paused.title": "Twoje dane wyglądają na błędne", "kyc.modal.pending.button-text": "Zamknij", "kyc.modal.pending.subtitle": "Weryfikacja trwa zwykle do 10 minut.", "kyc.modal.pending.title": "Będziemy Cię <PERSON>", "kyc.modal.required.cta": "Rozpocznij <PERSON>fi<PERSON>ę", "kyc.modal.required.subtitle": "Osiągnięto limit. Zweryfikuj <PERSON><PERSON>.", "kyc.modal.required.title": "Wymagana weryfikacja to<PERSON><PERSON>ci", "kyc.submitted": "Wniosek złożony", "kyc.submitted_short": "Przesłano", "kyc_status.completed_status": "Ukończono", "kyc_status.failed_status": "Niepowodzenie", "kyc_status.paused_status": "Do weryfikacji", "kyc_status.subtitle": "Przelewy bankowe", "kyc_status.subtitle.wrong_details": "Błędne dane", "kyc_status.subtitle_in_progress": "W toku", "kyc_status.title": "Weryfikacja toż<PERSON>ści", "label.close": "Zamknij", "label.saving": "Zapisuję...", "labels.this-month": "<PERSON> tym <PERSON>", "labels.today": "D<PERSON>ś", "labels.yesterday": "<PERSON><PERSON><PERSON><PERSON>", "language.selector.title": "Język", "ledger.account_loaded.imported": "Zaimportowano", "ledger.add.success.title": "Ledger pomyślnie połączony 🎉", "ledger.connect.cta": "Synchronizuj z Ledger", "ledger.connect.step1": "Podłącz Ledger do swojego urządzenia", "ledger.connect.step2": "Otwórz aplikację Ethereum na Ledgerze", "ledger.connect.step3": "Następnie zsynchronizuj z Ledger 👇", "ledger.connect.subtitle": "Wykonaj te kroki, aby z<PERSON><PERSON><PERSON><PERSON> swoje portfele Ledger do Zeal", "ledger.connect.title": "Połącz Ledger z Zeal", "ledger.error.ledger_is_locked.subtitle": "Odblokuj Ledgera i otwórz apkę Ethereum", "ledger.error.ledger_is_locked.title": "Ledger jest z<PERSON><PERSON><PERSON><PERSON>y", "ledger.error.ledger_not_connected.action": "Synchronizuj <PERSON>", "ledger.error.ledger_not_connected.subtitle": "Podłącz portfel sprzętowy do urządzenia i otwórz apkę Ethereum.", "ledger.error.ledger_not_connected.title": "Ledger nie jest podł<PERSON>ony", "ledger.error.ledger_running_non_eth_app.title": "Aplikacja Ethereum nie jest otwarta", "ledger.error.user_trx_denied_by_user.action": "Zamknij", "ledger.error.user_trx_denied_by_user.subtitle": "Odrzucono transakcję na portfelu sprzętowym", "ledger.error.user_trx_denied_by_user.title": "Transakcja odrzucona", "ledger.hd_path.bip44.subtitle": "np. <PERSON>, Trezor", "ledger.hd_path.bip44.title": "Standard BIP44", "ledger.hd_path.ledger_live.subtitle": "Domyślna", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "<PERSON><PERSON>", "ledger.hd_path.phantom.subtitle": "np. Phantom", "ledger.select.hd_path.subtitle": "Ścieżki HD to sposób sortowania kont przez portfele sprzętowe. Działają podobnie jak indeks w książce.", "ledger.select.hd_path.title": "<PERSON><PERSON><PERSON>rz ścieżkę HD", "ledger.select_account.import_wallets_count": "{count,plural,=0{<PERSON><PERSON> portfeli} one{Zaimportuj portfel} other{Zaimportuj {count} portfeli}}", "ledger.select_account.path_settings": "Ustawienia ścieżki", "ledger.select_account.subtitle": "Nie widzisz portfeli? Zmień ścieżkę", "ledger.select_account.subtitle.group_header": "Portfele", "ledger.select_account.title": "Zaimportuj portfele Ledger", "legend.lending-operations": "Operacje pożyczkowe", "legend.market_making-operations": "Operacje market making", "legend.treasury-operations": "<PERSON><PERSON><PERSON>", "link-existing-monerium-account-sign.button": "Połącz z Zeal", "link-existing-monerium-account-sign.subtitle": "Masz j<PERSON> konto Monerium.", "link-existing-monerium-account-sign.title": "Połącz Zeal ze swoim kontem Monerium", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Masz konto Monerium. Dokończ w ich apce.", "link-existing-monerium-account.title": "Przejdź do Monerium, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> konto", "loading.pin": "Ładowanie PIN-u...", "lockScreen.passwordIncorrectMessage": "Nieprawidłowe hasło", "lockScreen.passwordRequiredMessage": "<PERSON><PERSON><PERSON><PERSON>o", "lockScreen.unlock.header": "Odblokuj", "lockScreen.unlock.subheader": "<PERSON><PERSON><PERSON><PERSON>, aby o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Zeal", "mainTabs.activity.label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mainTabs.browse.label": "Przeglądaj", "mainTabs.browse.title": "Przeglądaj", "mainTabs.card.label": "Karta", "mainTabs.portfolio.label": "Portfolio", "mainTabs.rewards.label": "<PERSON><PERSON><PERSON>", "makeSpendable.cta": "Wykorzystaj środki", "makeSpendable.holdAsCash": "<PERSON><PERSON><PERSON> jako <PERSON>", "makeSpendable.shortText": "Zaro<PERSON> {apy} rocznie", "makeSpendable.title": "{amount} otr<PERSON>mano", "merchantCategory.agriculture": "Rolnictwo", "merchantCategory.alcohol": "Alkohol", "merchantCategory.antiques": "<PERSON><PERSON><PERSON>", "merchantCategory.appliances": "AGD", "merchantCategory.artGalleries": "Galerie sztuki", "merchantCategory.autoRepair": "<PERSON><PERSON><PERSON>", "merchantCategory.autoRepairService": "<PERSON><PERSON><PERSON>", "merchantCategory.beautyFitnessSpas": "<PERSON>rod<PERSON>, fitness i SPA", "merchantCategory.beautyPersonalCare": "Uroda i higiena osobista", "merchantCategory.billiard": "<PERSON><PERSON><PERSON>", "merchantCategory.books": "Książki", "merchantCategory.bowling": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.businessProfessionalServices": "Usługi biznesowe i profesjonalne", "merchantCategory.carRental": "Wypożyczalnia samochodów", "merchantCategory.carWash": "<PERSON><PERSON><PERSON>", "merchantCategory.cars": "Samochody", "merchantCategory.casino": "<PERSON><PERSON><PERSON>", "merchantCategory.casinoGambling": "Kasyno i hazard", "merchantCategory.cellular": "Telefonia komórkowa", "merchantCategory.charity": "Darowizny", "merchantCategory.childcare": "Opieka nad dziećmi", "merchantCategory.cigarette": "Pa<PERSON><PERSON><PERSON>", "merchantCategory.cinema": "<PERSON><PERSON>", "merchantCategory.cinemaEvents": "Kino i wydarzenia", "merchantCategory.cleaning": "Sprzątanie", "merchantCategory.cleaningMaintenance": "Sprzątanie i konserwacja", "merchantCategory.clothes": "Ubrania", "merchantCategory.clothingServices": "Usługi odzieżowe", "merchantCategory.communicationServices": "Usługi komunikacyjne", "merchantCategory.construction": "Budownictwo", "merchantCategory.cosmetics": "Kosm<PERSON><PERSON>", "merchantCategory.craftsArtSupplies": "Rękodzieło i artykuły plastyczne", "merchantCategory.datingServices": "<PERSON><PERSON><PERSON>", "merchantCategory.delivery": "Dostawa", "merchantCategory.dentist": "Stomatolog", "merchantCategory.departmentStores": "<PERSON><PERSON>", "merchantCategory.directMarketingSubscription": "Marketing bezpośredni i subskrypcje", "merchantCategory.discountStores": "<PERSON><PERSON><PERSON> dyskontowe", "merchantCategory.drugs": "Apteka", "merchantCategory.dutyFree": "Sklepy bezcłowe", "merchantCategory.education": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.electricity": "Prąd", "merchantCategory.electronics": "Elektronika", "merchantCategory.emergencyServices": "Służby ratunkowe", "merchantCategory.equipmentRental": "Wypożyczalnia sprzętu", "merchantCategory.evCharging": "Ładowanie pojazdów elektrycznych", "merchantCategory.financialInstitutions": "Instytucje finansowe", "merchantCategory.financialProfessionalServices": "Usługi finansowe i profesjonalne", "merchantCategory.finesPenalties": "Grzywny i kary", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "<PERSON><PERSON>", "merchantCategory.flowers": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.flowersGarden": "Kwiaty i ogród", "merchantCategory.food": "<PERSON><PERSON><PERSON>", "merchantCategory.freight": "Transport towarowy", "merchantCategory.fuel": "Paliwo", "merchantCategory.funeralServices": "Usługi pogrzebowe", "merchantCategory.furniture": "Meble", "merchantCategory.games": "G<PERSON>", "merchantCategory.gas": "Paliwo", "merchantCategory.generalMerchandiseRetail": "Towary ogólne i sprzedaż detaliczna", "merchantCategory.gifts": "Prezenty", "merchantCategory.government": "Urząd", "merchantCategory.governmentServices": "Usługi rządowe", "merchantCategory.hardware": "Sprzęt i narzędzia", "merchantCategory.healthMedicine": "Zdrowie i medycyna", "merchantCategory.homeImprovement": "Remont i wyposażenie domu", "merchantCategory.homeServices": "Usługi dla domu", "merchantCategory.hotel": "Hotel", "merchantCategory.housing": "Mieszkanie", "merchantCategory.insurance": "Ubezpieczenie", "merchantCategory.internet": "Internet", "merchantCategory.kids": "<PERSON><PERSON><PERSON>", "merchantCategory.laundry": "Pralnia", "merchantCategory.laundryCleaningServices": "Pralnia i sprzątanie", "merchantCategory.legalGovernmentFees": "Opłaty prawne i urzędowe", "merchantCategory.luxuries": "Luksusowe", "merchantCategory.luxuriesCollectibles": "Dobra luksusowe i kolekcjonerskie", "merchantCategory.magazines": "Czasopisma", "merchantCategory.magazinesNews": "Prasa i wiadomości", "merchantCategory.marketplaces": "<PERSON><PERSON> handlowe", "merchantCategory.media": "Media", "merchantCategory.medicine": "Me<PERSON><PERSON><PERSON>", "merchantCategory.mobileHomes": "<PERSON><PERSON> mobilne", "merchantCategory.moneyTransferCrypto": "Przelewy i krypto", "merchantCategory.musicRelated": "Muzyka", "merchantCategory.musicalInstruments": "Instrumenty muzyczne", "merchantCategory.optics": "Optyk", "merchantCategory.organizationsClubs": "Organizacje i kluby", "merchantCategory.other": "<PERSON><PERSON>", "merchantCategory.parking": "Parking", "merchantCategory.pawnShops": "Lombardy", "merchantCategory.pets": "Zwierzęta", "merchantCategory.photoServicesSupplies": "Usługi i materiały fotograficzne", "merchantCategory.postalServices": "Usługi pocztowe", "merchantCategory.professionalServicesOther": "Usługi profesjonalne (inne)", "merchantCategory.publicTransport": "Transport publiczny", "merchantCategory.purchases": "Zakupy", "merchantCategory.purchasesMiscServices": "Zakupy i różne usługi", "merchantCategory.recreationServices": "Usługi rekreacyjne", "merchantCategory.religiousGoods": "Artykuły religijne", "merchantCategory.secondhandRetail": "Artykuły używane", "merchantCategory.shoeHatRepair": "Naprawa obuwia i kapeluszy", "merchantCategory.shoeRepair": "Naprawa obuwia", "merchantCategory.softwareApps": "Oprogramowanie i aplikacje", "merchantCategory.specializedRepairs": "Naprawy specjalistyczne", "merchantCategory.sport": "Sport", "merchantCategory.sportingGoods": "Artykuły sportowe", "merchantCategory.sportingGoodsRecreation": "Artykuły sportowe i rekreacja", "merchantCategory.sportsClubsFields": "Kluby i obiekty sportowe", "merchantCategory.stationaryPrinting": "Artykuły papiernicze i drukarskie", "merchantCategory.stationery": "Artykuły papiernicze", "merchantCategory.storage": "Przechowywanie", "merchantCategory.taxes": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.taxi": "Taksówka", "merchantCategory.telecomEquipment": "Sprzęt telekomunikacyjny", "merchantCategory.telephony": "Telefonia", "merchantCategory.tobacco": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.tollRoad": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>a", "merchantCategory.tourismAttractionsAmusement": "Turystyka, atrakcje i rozrywka", "merchantCategory.towing": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.toys": "Zabawki", "merchantCategory.toysHobbies": "Zabawki i hobby", "merchantCategory.trafficFine": "<PERSON><PERSON><PERSON> drogowy", "merchantCategory.train": "Pociąg", "merchantCategory.travelAgency": "Biuro podróży", "merchantCategory.tv": "Telewizja", "merchantCategory.tvRadioStreaming": "Telewizja, radio i streaming", "merchantCategory.utilities": "Media", "merchantCategory.waterTransport": "Transport wodny", "merchantCategory.wholesaleClubs": "<PERSON><PERSON><PERSON>", "metaMask.subtitle": "Włącz Tryb MetaMask, aby przekierować wszystkie połączenia MetaMask do Zeal. Kliknięcie MetaMask w dApps połączy Cię wtedy z Zeal.", "metaMask.title": "Nie możesz połączyć się z Zeal?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Otwórz swoją aplikację bankową", "monerium-bank-deposit.buttet-point.receive-crypto": "Odbierz cyfrowe EUR", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Wyślij {fiatCurrencyCode} na swoje konto", "monerium-bank-deposit.deposit-account-country": "<PERSON><PERSON>", "monerium-bank-deposit.header": "{fullName} – konto oso<PERSON>te", "monerium-bank-details.account-name": "<PERSON><PERSON><PERSON> rachunku", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "Skopiowano BIC/SWIFT", "monerium-bank-details.bic_swift_copied": "Skopiowano BIC/SWIFT", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "Skopiowano IBAN", "monerium-bank-details.to-wallet": "Do portfela", "monerium-bank-details.transfer-fee": "Opłata za przelew", "monerium-bank-transfer.enable-card.bullet-1": "Ukończ weryfikację to<PERSON>", "monerium-bank-transfer.enable-card.bullet-2": "<PERSON><PERSON><PERSON><PERSON><PERSON> dane konta osobis<PERSON>go", "monerium-bank-transfer.enable-card.bullet-3": "Wpłać z konta bankowego", "monerium-card-delay-relay.success.cta": "Zamknij", "monerium-card-delay-relay.success.subtitle": "Zabezpieczenie: zmiana karty trwa 3 min.", "monerium-card-delay-relay.success.title": "<PERSON>róć za 3 min, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> konfigurac<PERSON><PERSON>.", "monerium-deposit.account-details-info-popup.bullet-point-1": "Wysłane na to konto {fiatCurrencyCode} zostaną automatycznie zamienione na {cryptoCurrencyCode} tokeny w sieci {cryptoCurrencyChain} i wysłane do Twojego portfela", "monerium-deposit.account-details-info-popup.bullet-point-2": "WYSYŁAJ TYLKO {fiatCurrencyCode} ({fiatCurrencySymbol}) na swoje konto", "monerium-deposit.account-details-info-popup.title": "<PERSON>", "monerium.check_order_status.sending": "Wysyłanie", "monerium.not-eligible.cta": "<PERSON><PERSON><PERSON><PERSON>", "monerium.not-eligible.subtitle": "Monerium nie może otworzyć Ci konta.", "monerium.not-eligible.title": "Spróbuj z innym dostawcą", "monerium.setup-card.cancel": "<PERSON><PERSON><PERSON>", "monerium.setup-card.continue": "<PERSON><PERSON>", "monerium.setup-card.create_account": "Utwórz konto", "monerium.setup-card.login": "Zaloguj się do Gnosis Pay", "monerium.setup-card.subtitle": "Utwórz lub zaloguj się w Gnosis Pay.", "monerium.setup-card.subtitle_personal_account": "Załóż konto osobiste w Gnosis Pay w kilka minut:", "monerium.setup-card.title": "Włącz wpłaty bankowe", "moneriumDepositSuccess.goToWallet": "Przejdź do portfela", "moneriumDepositSuccess.title": "{symbol} otr<PERSON><PERSON>o", "moneriumInfo.fees": "0% opłat", "moneriumInfo.registration": "Monerium jest autoryzowaną i regulowaną Instytucją Pieniądza Elektronicznego zgodnie z islandzką ustawą o pieniądzu elektronicznym nr 17/2013 <link>Dowiedz się więcej</link>", "moneriumInfo.selfCustody": "Otrzymywana cyfrowa gotówka jest przechowywana w prywatnym portfelu i nikt inny nie ma kontroli nad Twoimi środkami.", "moneriumWithdrawRejected.supportText": "Błąd przelewu. Spróbuj znów. Je<PERSON><PERSON> nic, <link>kontakt z obsługą.</link>", "moneriumWithdrawRejected.title": "Przelew został cofnięty", "moneriumWithdrawRejected.tryAgain": "Spróbuj ponownie", "moneriumWithdrawSuccess.supportText": "<PERSON><PERSON><PERSON> do 24 godz., z<PERSON><PERSON>ó<PERSON> {br}odbiorca otrzyma środki", "moneriumWithdrawSuccess.title": "Wysłano", "monerium_enable_banner.text": "Aktywuj przelewy bankowe", "monerium_error_address_re_link_required.title": "Połącz portfel ponownie z Monerium", "monerium_error_duplicate_order.title": "Zduplikowane zlecenie", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "<PERSON>", "mt-pelerin-fork.options.chf.short": "Błyskawicznie i za darmo z Mt Pelerin", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Błyskawicznie i za darmo z Monerium", "mt-pelerin-fork.title": "Co chcesz wpłacić?", "mtPelerinProviderInfo.fees": "Płacisz 0% prowizji", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd jest powiązana z SO-FIT, organem samoregulacyjnym uznawanym przez Szwajcarski Urząd Nadzoru Rynku Finansowego (FINMA) zgodnie z ustawą o przeciwdziałaniu praniu pieniędzy. <link>Dowiedz się więcej</link>", "mtPelerinProviderInfo.selfCustody": "Otrzymana cyfrowa gotówka jest przechowywana na Twoim prywatnym portfelu i nikt inny nie będzie miał kontroli nad Twoimi aktywami.", "network-fee-widget.title": "<PERSON><PERSON><PERSON>", "network.edit.verifying_rpc": "Weryfikowanie RPC", "network.editRpc.predefined_network_info.subtitle": "<PERSON><PERSON><PERSON><PERSON> jak <PERSON>, <PERSON><PERSON> RPC, które uniemożliwiają śledzenie Twoich danych osobowych.{br}{br}Domyślne RPC Zeal to niezawodni i przetestowani dostawcy RPC.", "network.editRpc.predefined_network_info.title": "RPC prywatności Zeal", "network.filter.update_rpc_success": "Zapisano węzeł RPC", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "Wszystkie sieci", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "<PERSON><PERSON><PERSON>", "networks.filter.add_modal.chain_list.subtitle": "Dodaj dowolne sieci EVM", "networks.filter.add_modal.chain_list.title": "Przejdź do Chainlist", "networks.filter.add_modal.dapp_tip.subtitle": "W swoich ulubionych dApps po prostu przełącz się na sieć EVM, której chcesz użyć, a Zeal zapyta, czy chcesz dodać ją do swojego portfela.", "networks.filter.add_modal.dapp_tip.title": "Lub dodaj sieć z dowolnej dApp", "networks.filter.add_networks.subtitle": "Obsługiwane wszystkie sieci EVM", "networks.filter.add_networks.title": "<PERSON><PERSON><PERSON>", "networks.filter.add_test_networks.title": "<PERSON><PERSON><PERSON> si<PERSON>i testowe", "networks.filter.tab.netwokrs": "<PERSON><PERSON><PERSON>", "networks.filter.testnets.title": "<PERSON><PERSON><PERSON> testowe", "nft.widget.emptystate": "Brak kolekcji w portfelu", "nft_collection.change_account_picture.subtitle": "Czy na pewno chcesz zaktualizować swoje zdjęcie profilowe?", "nft_collection.change_account_picture.title": "Ustaw NFT jako zdj<PERSON><PERSON> profilowe", "nfts.allNfts.pricingPopup.description": "Ceny kolekcji są oparte na ostatniej cenie transakcyjnej.", "nfts.allNfts.pricingPopup.title": "<PERSON><PERSON><PERSON><PERSON>", "no-passkeys-found.modal.cta": "Zamknij", "no-passkeys-found.modal.subtitle": "Nie wykryliśmy na tym urządzeniu kluczy dostępu Zeal. Upewnij się, że logujesz się na konto w chmurze użyte do utworzenia Smart Wallet.", "no-passkeys-found.modal.title": "Nie znaleziono kluczy dostępu", "notValidEmail.title": "Nieprawidłowy adres e-mail", "notValidPhone.title": "To nie jest prawidłowy numer telefonu", "notification-settings.title": "Ustawienia powiadomień", "notification-settings.toggles.active-wallets": "Aktywne portfele", "notification-settings.toggles.bank-transfers": "Przelewy bankowe", "notification-settings.toggles.card-payments": "Płatności kartą", "notification-settings.toggles.readonly-wallets": "Portfele tylko do odczytu", "ntft.groupHeader.text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "on_ramp.crypto_completed": "Zakończono", "on_ramp.fiat_completed": "Zakończono", "onboarding-widget.subtitle.card_created_from_order.left": "Karta Visa", "onboarding-widget.subtitle.card_created_from_order.right": "Aktywuj kartę", "onboarding-widget.subtitle.card_order_ready.left": "Fizyczna karta Visa", "onboarding-widget.subtitle.default": "Przelewy bankowe i karta Visa", "onboarding-widget.title.card-order-in-progress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> karty", "onboarding-widget.title.card_created_from_order": "<PERSON>rta została wysłana", "onboarding-widget.title.kyc_approved": "Przelewy i karta gotowe", "onboarding-widget.title.kyc_failed": "Nie można utworzyć konta", "onboarding-widget.title.kyc_not_started": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kon<PERSON>gu<PERSON>", "onboarding-widget.title.kyc_started_documents_requested": "Dokończ weryfikację", "onboarding-widget.title.kyc_started_resubmission_requested": "Ponów weryfikację", "onboarding-widget.title.kyc_started_verification_in_progress": "Weryfikacja toż<PERSON>ści", "onboarding.loginOrCreateAccount.amountOfAssets": "Aktywa o wartości ponad 10 mld USD", "onboarding.loginOrCreateAccount.cards.subtitle": "Dostępne tylko w niektórych regionach. Kont<PERSON>uu<PERSON><PERSON><PERSON>, akcept<PERSON><PERSON><PERSON> nasz <Terms><PERSON><PERSON>min</Terms> i <PrivacyPolicy>Politykę prywat<PERSON>ś<PERSON></PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Karta Visa z wysokimi{br}zwrotami i bez opłat", "onboarding.loginOrCreateAccount.createAccount": "Utwórz konto", "onboarding.loginOrCreateAccount.earn.subtitle": "Zwroty są zmienne; kapitał jest zagrożony. Kontynuując, akcept<PERSON><PERSON><PERSON>sz <Terms>Regulamin</Terms> i <PrivacyPolicy>Politykę prywat<PERSON>ści</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "<PERSON><PERSON>bki {percent} rocznie{br}Zaufało nam {currencySymbol}ponad 5 mld", "onboarding.loginOrCreateAccount.earningPerYear": "<PERSON><PERSON><PERSON><PERSON> {percent}{br} rocznie", "onboarding.loginOrCreateAccount.login": "<PERSON><PERSON><PERSON><PERSON>", "onboarding.loginOrCreateAccount.trading.subtitle": "Ka<PERSON>ał jest zagrożony. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ak<PERSON><PERSON><PERSON><PERSON><PERSON>sz <Terms>Regulamin</Terms> i <PrivacyPolicy>Polityk<PERSON> pry<PERSON></PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Inwestuj we wszystko,{br}od BTC po S&P", "onboarding.loginOrCreateAccount.trustedBy": "Cyfrowe rynki pieniężne{br}Zaufało nam {assets}", "onboarding.wallet_stories.close": "Zamknij", "onboarding.wallet_stories.previous": "Wstecz", "order-earn-deposit-bridge.deposit": "<PERSON><PERSON>ła<PERSON>", "order-earn-deposit-bridge.into": "Do", "otpIncorrectMessage": "<PERSON><PERSON> jest błędny", "passkey-creation-not-possible.modal.close": "Zamknij", "passkey-creation-not-possible.modal.subtitle": "Nie udało się utworzyć klucza Passkey dla portfela. Upewnij się, że urządzenie obsługuje Passkey i spróbuj ponownie. <link>Kontakt z obsługą</link> jeśli problem nie ustąpi.", "passkey-creation-not-possible.modal.title": "Nie można utworzyć klucza Passkey", "passkey-not-supported-in-mobile-browser.modal.cta": "<PERSON><PERSON><PERSON>", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Portfele Smart Wallet nie są obsługiwane w przeglądarkach mobilnych.", "passkey-not-supported-in-mobile-browser.modal.title": "<PERSON><PERSON><PERSON><PERSON>, aby k<PERSON><PERSON>", "passkey-recovery.recovering.deploy-signer.loading-text": "Weryfikowanie klucza dostępu", "passkey-recovery.recovering.loading-text": "Odzyskiwanie portfela", "passkey-recovery.recovering.signer-not-found.subtitle": "<PERSON>e udało się połączyć klucza dostępu z aktywnym portfelem. <PERSON><PERSON><PERSON>, skontaktuj się z zespołem Zeal.", "passkey-recovery.recovering.signer-not-found.title": "Nie znaleziono portfela", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "Spróbuj z innym kluczem", "passkey-recovery.select-passkey.banner.subtitle": "Sprawdź konto na urządzeniu. Klucze są powiązane z kontem.", "passkey-recovery.select-passkey.banner.title": "Nie widzisz klucza dostępu do portfela?", "passkey-recovery.select-passkey.continue": "<PERSON><PERSON>bierz k<PERSON>ępu", "passkey-recovery.select-passkey.subtitle": "Wybierz klucz dostępu powiązany z portfelem, by <PERSON><PERSON><PERSON><PERSON><PERSON> dostęp.", "passkey-recovery.select-passkey.title": "<PERSON><PERSON>bierz k<PERSON>ępu", "passkey-story_1.subtitle": "Dzięki Smart Wallet opłaty sieciowe zapłacisz w wię<PERSON><PERSON><PERSON>ci tokenów, więc nie musisz się o nie mart<PERSON>.", "passkey-story_1.title": "Zapomnij o kłopotach z opłatami sieciowymi – płać w większości tokenów.", "passkey-story_2.subtitle": "Oparty na wiodących w branży smart kontraktach Safe, które zabezpieczają ponad 100 mld USD w ponad 20 milionach portfeli.", "passkey-story_2.title": "Zabezpieczone przez Safe", "passkey-story_3.subtitle": "Smart Wallets działają w głównych sieciach kompatybilnych z Ethereum. Sprawdź obsługiwane sieci przed wysłaniem aktywów.", "passkey-story_3.title": "Obsługa głównych sieci EVM", "password.add.header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "password.add.includeLowerAndUppercase": "Małe i wielkie litery", "password.add.includesNumberOrSpecialChar": "Jedna cyfra lub symbol", "password.add.info.subtitle": "Nie wysyłamy Twojego hasła na nasze serwery ani nie tworzymy jego kopii zapasowej", "password.add.info.t_and_c": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ak<PERSON><PERSON><PERSON><PERSON><PERSON>sz <Terms><PERSON><PERSON>min</Terms> i <PrivacyPolicy>Polityk<PERSON></PrivacyPolicy>", "password.add.info.title": "Twoje hasło pozostaje na tym urządzeniu", "password.add.inputPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "password.add.shouldContainsMinCharsCheck": "10+ znaków", "password.add.subheader": "Będziesz używać tego hasła do odblokowywania Zeal", "password.add.success.title": "Hasło utworzone 🔥", "password.confirm.header": "Potwierd<PERSON> hasło", "password.confirm.passwordDidNotMatch": "Hasła muszą być takie same", "password.confirm.subheader": "Wpisz hasło jeszcze raz", "password.create_pin.subtitle": "Ten kod dostępu blokuje aplikację Zeal", "password.create_pin.title": "Utwórz swój kod dostępu", "password.enter_pin.title": "Wprowadź kod dostępu", "password.incorrectPin": "Nieprawidłowy kod dostępu", "password.pin_is_not_same": "<PERSON>d dos<PERSON> nie pasuje", "password.placeholder.enter": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasło", "password.placeholder.reenter": "Wprowadź hasło ponownie", "password.re_enter_pin.subtitle": "Wprowadź ponownie ten sam kod dostępu", "password.re_enter_pin.title": "Potwierdź kod dostępu", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "Oczekująca", "pending-send.details.pending": "Oczekująca", "pending-send.details.processing": "Przetwarzanie", "permit-info.modal.description": "Permits to <PERSON><PERSON><PERSON>, które po podpisaniu pozwalają aplikacjom przenosić Twoje tokeny w <PERSON><PERSON>, np. w celu dokonania wymiany.{br}Permits są pod<PERSON>ne do Zgód, ale ich podpisanie nie wiąże się z opłatami sieciowymi.", "permit-info.modal.title": "Czym są Permits?", "permit.edit-expiration": "Edytuj {currency} wygaśnięcie", "permit.edit-limit": "Edytuj {currency} limit wydatków", "permit.edit-modal.expiresIn": "<PERSON><PERSON><PERSON><PERSON> za…", "permit.expiration-warning": "{currency} ostrzeżenie o wygaśnięciu", "permit.expiration.info": "{currency} informacje o wygaśnięciu", "permit.expiration.never": "<PERSON><PERSON><PERSON>", "permit.spend-limit.info": "{currency} informacje o limicie wydatków", "permit.spend-limit.warning": "{currency} ostrzeżenie o limicie wydatków", "phoneNumber.title": "numer telefonu", "physicalCardOrderFlow.cardOrdered": "<PERSON><PERSON> z<PERSON>", "physicalCardOrderFlow.city": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "physicalCardOrderFlow.orderCard": "Zamów kartę", "physicalCardOrderFlow.postcode": "<PERSON><PERSON>", "physicalCardOrderFlow.shippingAddress.subtitle": "Tu wyślemy Twoją kartę", "physicalCardOrderFlow.shippingAddress.title": "<PERSON><PERSON>", "physicalCardOrderFlow.street": "Ulica", "placeholderDapps.1inch.description": "W<PERSON>ieniaj, korzystając z najlepszych tras", "placeholderDapps.aave.description": "Pożyczaj i wypożyczaj tokeny", "placeholderDapps.bungee.description": "Używaj bridge'a między sieciami po najlepszych trasach", "placeholderDapps.compound.description": "Pożyczaj i wypożyczaj tokeny", "placeholderDapps.cowswap.description": "Wymieniaj po najlepszych kursach na Gnosis", "placeholderDapps.gnosis-pay.description": "Zarządzaj swoją kartą Gnosis Pay", "placeholderDapps.jumper.description": "Używaj bridge'a między sieciami po najlepszych trasach", "placeholderDapps.lido.description": "Stejkuj ETH, by <PERSON><PERSON><PERSON><PERSON> więcej ETH", "placeholderDapps.monerium.description": "Pieniądz elektroniczny i przelewy bankowe", "placeholderDapps.odos.description": "W<PERSON>ieniaj, korzystając z najlepszych tras", "placeholderDapps.stargate.description": "Użyj bridge'a lub stejk<PERSON>j, by <PERSON><PERSON><PERSON><PERSON> <14% APY", "placeholderDapps.uniswap.description": "Jedna z najpopularniejszych giełd", "pleaseAllowNotifications.cardPayments": "Płatności kartą", "pleaseAllowNotifications.customiseInSettings": "Dostosuj w ustawieniach", "pleaseAllowNotifications.enable": "Włącz", "pleaseAllowNotifications.forWalletActivity": "Dla aktywności portfela", "pleaseAllowNotifications.title": "Otrzymuj powiadomienia z portfela", "pleaseAllowNotifications.whenReceivingAssets": "Podczas otrzymywania aktywów", "portfolio.quick-actions.add_funds": "<PERSON><PERSON><PERSON><PERSON>", "portfolio.quick-actions.buy": "<PERSON><PERSON>", "portfolio.quick-actions.deposit": "<PERSON><PERSON><PERSON><PERSON>", "portfolio.quick-actions.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portfolio.view.lastRefreshed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Doładuj testnetowy AVAX", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Przejdź do Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "Doładuj testnetowy BNB", "portfolio.view.topupTestNet.BscTestnet.secondary": "Przejdź do Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Doładuj testnetowy SepETH", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Przejdź do Sepolia Faucet", "portfolio.view.topupTestNet.FantomTestnet.primary": "Doładuj testnetowy FTM", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Przejdź do Faucet", "privateKeyConfirmation.banner.subtitle": "<PERSON><PERSON><PERSON>, kto ma Twój klucz prywatny, ma dostęp do Twoich środków. Tylko oszuści o niego proszą.", "privateKeyConfirmation.banner.title": "<PERSON><PERSON><PERSON><PERSON>", "privateKeyConfirmation.title": "NIGDY nie udostępniaj nikomu swojego klucza prywatnego", "rating-request.not-now": "<PERSON><PERSON> te<PERSON>", "rating-request.title": "<PERSON><PERSON>?", "receive_funds.address-text": "To jest T<PERSON><PERSON><PERSON> unikalny adres portfela. Możesz go bezpiecznie udostępniać innym.", "receive_funds.copy_address": "Skopiuj ad<PERSON>", "receive_funds.network-warning.eoa.subtitle": "<link><PERSON><PERSON><PERSON><PERSON> <PERSON>ę standardowych sieci</link>. Aktywa wysłane w sieciach innych niż EVM zostaną utracone.", "receive_funds.network-warning.eoa.title": "Wszystkie sieci oparte na Ethereum są obsługiwane", "receive_funds.network-warning.scw.subtitle": "<link><PERSON><PERSON><PERSON>z obsługiwane sieci</link>. Aktywa wysłane w innych sieciach zostaną utracone.", "receive_funds.network-warning.scw.title": "Ważne: używaj tylko obsługiwanych sieci", "receive_funds.scan_qr_code": "Zeskanuj kod QR", "receiving.in.days": "Od<PERSON><PERSON><PERSON> za {days}d", "receiving.this.week": "Odbiór w tym tygodniu", "receiving.today": "<PERSON>d<PERSON><PERSON><PERSON>", "reference.error.maximum_number_of_characters_exceeded": "Zbyt wiele znaków", "referral-code.placeholder": "Wklej link z zaproszeniem", "referral-code.subtitle": "Ponownie kliknij link lub wklej go niżej.", "referral-code.title": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>łał <PERSON>i {bReward}?", "rekyc.verification_deadline.subtitle": "Dokończ weryfikację w ciągu {daysUntil} dni, aby dalej k<PERSON> z karty.", "rekyc.verification_required.subtitle": "Dokończ weryfikację, aby dalej korzy<PERSON> z karty.", "reminder.fund": "💸 Do<PERSON>j <PERSON> — zacz<PERSON>j zara<PERSON>ć 6% natychmiast", "reminder.onboarding": "🏁 Dokończ konfigurację — zarabiaj 6% od swoich depozytów", "remove-owner.confirmation.subtitle": "Blokada karty na 3 min. (zmiana ustawień)", "remove-owner.confirmation.title": "Karta zostanie zablokowana na 3 min.", "restore-smart-wallet.wallet-recovered": "Portfel odzyskany", "rewardClaimCelebration.claimedTitle": "Nagrody już odebrano", "rewardClaimCelebration.subtitle": "Za zapraszanie znajomych", "rewardClaimCelebration.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rewards-warning.subtitle": "Usunięcie tego konta wstrzyma dostęp do wszelkich powiązanych nagród. Możesz przywrócić konto w dowolnym momencie, aby je odebrać.", "rewards-warning.title": "Utracisz dostęp do swoich nagród", "rewards.copiedInviteLink": "Skopiowano link z zaproszeniem", "rewards.createAccount": "Kopiuj link zaproszenia", "rewards.header.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> {aReward} <PERSON>bie i {bReward} <PERSON><PERSON><PERSON>, gdy wyda on {bSpendLimitReward}.", "rewards.header.title": "<PERSON><PERSON><PERSON><PERSON> {amountA}{br} Daj {amountB}", "rewards.sendInvite": "<PERSON><PERSON><PERSON><PERSON><PERSON> zaproszenie", "rewards.sendInviteTip": "<PERSON><PERSON><PERSON><PERSON>, a damy mu {bAmount}", "route.fees": "Opłaty {fees}", "routesNotFound.description": "Trasa wymiany dla połączenia sieci {from}–{to} nie jest dostę<PERSON>na.", "routesNotFound.title": "<PERSON><PERSON> dostę<PERSON><PERSON>j trasy wym<PERSON>y", "rpc.OrderBuySignMessage.subtitle": "Przez Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Przez Swaps.IO", "rpc.addCustomNetwork.addNetwork": "<PERSON><PERSON><PERSON>", "rpc.addCustomNetwork.chainId": "ID łańcucha", "rpc.addCustomNetwork.nativeToken": "Token natywny", "rpc.addCustomNetwork.networkName": "<PERSON><PERSON><PERSON>", "rpc.addCustomNetwork.operationDescription": "Ta strona chce dodać sieć do Twojego portfela. Zeal nie może sprawdzić bezpieczeństwa niestandardowych sieci. Upew<PERSON>j <PERSON>, że rozumiesz ryzyko.", "rpc.addCustomNetwork.rpcUrl": "Adres URL RPC", "rpc.addCustomNetwork.subtitle": "Używając {name}", "rpc.addCustomNetwork.title": "<PERSON><PERSON><PERSON>", "rpc.send_token.network_not_supported.subtitle": "Pracujemy nad włączeniem transakcji w tej sieci. Dziękujemy za cierpliwość 🙏", "rpc.send_token.network_not_supported.title": "Sieć już wkró<PERSON>ce", "rpc.send_token.send_or_receive.settings": "Ustawienia", "rpc.sign.accept": "Ak<PERSON>pt<PERSON>j", "rpc.sign.cannot_parse_message.body": "Nie udało się odkodować tej wiadomości. Zaakceptuj to <PERSON><PERSON><PERSON><PERSON> tylko, jeśli ufasz tej aplikacji.{br}{br}Wiadomości mogą słu<PERSON> do logowania, ale też dawać aplikacjom kontrolę nad Twoimi tokenami.", "rpc.sign.cannot_parse_message.header": "Zach<PERSON><PERSON> ostrożność", "rpc.sign.import_private_key": "Importuj klucze", "rpc.sign.subtitle": "<PERSON><PERSON> {name}", "rpc.sign.title": "Prośba o podpis", "safe-creation.success.title": "Portfel utworzony", "safe-safety-checks-popup.title": "Kontrole bezpieczeństwa transakcji", "safetyChecksPopup.title": "Kontrole bezpieczeństwa strony", "scan_qr_code.description": "Zeskanuj kod QR portfela lub połącz się z aplikacją", "scan_qr_code.show_qr_code": "Pokaż mój kod QR", "scan_qr_code.tryAgain": "Spróbuj ponownie", "scan_qr_code.unlockCamera": "Odblokuj aparat", "screen-lock-missing.modal.close": "Zamknij", "screen-lock-missing.modal.subtitle": "Twoje urządzenie wymaga blokady ekranu, aby używać kluczy Passkey. Ustaw blokadę i spróbuj ponownie.", "screen-lock-missing.modal.title": "<PERSON><PERSON> blokady ekranu", "seedConfirmation.banner.subtitle": "<PERSON><PERSON><PERSON>, kto ma <PERSON>ją tajną frazę, ma dostęp do <PERSON>ich środków. Tylko oszuści o nią proszą.", "seedConfirmation.title": "NIGDY nie udostępniaj nikomu swojej tajnej frazy", "select-active-owner.subtitle": "Wybierz portfel do połączenia z Zeal.", "select-active-owner.title": "<PERSON><PERSON>bierz portfel", "select-card.title": "<PERSON><PERSON>bierz kartę", "select-crypto-currency-title": "<PERSON><PERSON><PERSON><PERSON>", "select-token.title": "<PERSON><PERSON><PERSON><PERSON>", "selectEarnAccount.chf.description.steps": "· Wypłacaj środki 24/7, bez okresów blokady {br}· Odsetki naliczane co sekundę {br}· Nadzabezpieczone depozyty w <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} rocznie w CHF", "selectEarnAccount.eur.description.steps": "· Wypłacaj środki 24/7, bez blokad {br}· Odsetki naliczane co sekundę {br}· Nadzabezpieczone pożyczki w <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} rocznie w EUR", "selectEarnAccount.subtitle": "Możesz zmienić w każdej chwili", "selectEarnAccount.title": "<PERSON><PERSON><PERSON><PERSON> wa<PERSON>", "selectEarnAccount.usd.description.steps": "· Wypłacaj środki 24/7, bez blokad {br}· Odsetki naliczane co sekundę {br}· Nadzabezpieczone depozyty w <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} roc<PERSON><PERSON> w USD", "selectEarnAccount.zero.description_general": "Trzymaj cyfrowe środki bez zarabiania odsetek", "selectEarnAccount.zero.title": "0% rocznie", "selectRechargeThreshold.button.enterAmount": "Wprowadź kwotę", "selectRechargeThreshold.button.setTo": "Ustaw na {amount}", "selectRechargeThreshold.description.line1": "<PERSON><PERSON> saldo <PERSON>j karty spadnie poniżej {amount}, zostanie ona automatycznie doładowana do kwoty {amount} z Twojego konta Earn.", "selectRechargeThreshold.description.line2": "Niższy próg docelowy pozwala trzymać więcej środków na koncie Earn (zarabiającym 3%). <PERSON><PERSON><PERSON><PERSON> to zmienić w każdej chwili.", "selectRechargeThreshold.title": "Ustaw docelowe saldo karty", "select_currency_to_withdraw.select_token_to_withdraw": "Wybierz token do wypłaty", "send-card-token.form.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "send-card-token.form.send-amount": "Kwota doładowania", "send-card-token.form.title": "<PERSON><PERSON><PERSON>j kart<PERSON>", "send-card-token.form.to-address": "Karta", "send-safe-transaction.network-fee-widget.error": "Potrze<PERSON><PERSON><PERSON> {amount} lub wybierz inny token", "send-safe-transaction.network-fee-widget.no-fee": "<PERSON><PERSON>", "send-safe-transaction.network-fee-widget.title": "<PERSON><PERSON><PERSON>", "send-safe-transaction.network_fee_widget.title": "<PERSON><PERSON><PERSON>", "send.banner.fees": "Potrzebujesz {amount} więcej {currency} na opłaty", "send.banner.toAddressNotSupportedNetwork.subtitle": "Portfel odbiorcy nie obsługuje {network}. Zmień na obsługiwany token.", "send.banner.toAddressNotSupportedNetwork.title": "Odbiorca nie obsługuje tej sieci", "send.banner.walletNotSupportedNetwork.subtitle": "Smart Wallet nie obsługuje transakcji w sieci {network}. Zmień na obsługiwany token.", "send.banner.walletNotSupportedNetwork.title": "Sieć tokena nie jest obsługiwana", "send.empty-portfolio.empty-state": "Nie znaleziono żadnych tokenów", "send.empty-portfolio.header": "Tokeny", "send.titile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendLimit.success.subtitle": "Twój dzienny limit wydatków zostanie zaktualizowany za 3 minuty. Do tego czasu obowiązuje obecny limit.", "sendLimit.success.title": "Ta zmiana potrwa 3 minuty", "send_crypto.form.disconnected.cta.addFunds": "<PERSON><PERSON><PERSON>", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Przełącz na {network}", "send_crypto.form.disconnected.label": "Kwo<PERSON> p<PERSON>elewu", "send_to.qr_code.description": "Zeskanuj kod QR, aby wysłać do portfela", "send_to.qr_code.title": "Zeskanuj kod QR", "send_to_card.header": "Wyślij na adres karty", "send_to_card.select_sender.add_wallet": "<PERSON><PERSON><PERSON>", "send_to_card.select_sender.header": "Wybierz nadawcę", "send_to_card.select_sender.search.default_placeholder": "<PERSON><PERSON><PERSON> ad<PERSON><PERSON> lub <PERSON>", "send_to_card.select_sender.show_card_address_button_description": "Pokaż adres karty", "send_token.form.select-address": "<PERSON><PERSON><PERSON><PERSON> adres", "send_token.form.send-amount": "Kwota do wysłania", "send_token.form.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setLimit.amount.error.zero_amount": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "setLimit.error.max_limit_reached": "Ustaw maks. limit {amount}", "setLimit.error.same_as_current_limit": "Limit bez zmian", "setLimit.placeholder": "Obecnie: {amount}", "setLimit.submit": "Ustaw limit", "setLimit.submit.error.amount_required": "Wpisz kwotę", "setLimit.subtitle": "To kwo<PERSON>, kt<PERSON><PERSON>ą możesz wydać dziennie swoją kartą.", "setLimit.title": "Ustaw dzienny limit wydatków", "settings.accounts": "Konta", "settings.accountsSeeAll": "Zobacz wszystkie", "settings.addAccount": "<PERSON><PERSON><PERSON>", "settings.card": "Ustawienia karty", "settings.connections": "Połączenia z aplikacjami", "settings.currency": "<PERSON><PERSON><PERSON><PERSON><PERSON> waluta", "settings.default_currency_selector.title": "<PERSON><PERSON><PERSON>", "settings.discord": "Discord", "settings.experimentalMode": "Tryb eksperymentalny", "settings.experimentalMode.subtitle": "<PERSON><PERSON><PERSON> nowe funk<PERSON>je", "settings.language": "Język", "settings.lockZeal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.notifications": "Powiadomienia", "settings.open_expanded_view": "Otwórz widok rozszerzony", "settings.privacyPolicy": "Polityka p<PERSON>watności", "settings.settings": "Ustawienia", "settings.termsOfUse": "Warunki użytkowania", "settings.twitter": "𝕏 / Twitter", "settings.version": "We<PERSON><PERSON> {version} śr.: {env}", "setup-card.confirmation": "<PERSON><PERSON><PERSON><PERSON> kartę wirtualną", "setup-card.confirmation.subtitle": "Płać online i dodaj do swojego portfela {type} , by <PERSON><PERSON><PERSON><PERSON>.", "setup-card.getCard": "Zamów kartę", "setup-card.order.physicalCard": "<PERSON><PERSON>", "setup-card.order.physicalCard.steps": "· Fizyczna karta VISA Gnosis Pay {br}· Wysyłka do 3 tygodni {br}· Do płatności w sklepach i bankomatach. {br}· Dodaj do portfela Apple/Google (tylko w obsługiwanych krajach", "setup-card.order.subtitle1": "Możesz używać kilku kart naraz", "setup-card.order.title": "<PERSON><PERSON> karty?", "setup-card.order.virtualCard": "<PERSON><PERSON> wirtualna", "setup-card.order.virtual_card.steps": "· Cyfrowa karta VISA Gnosis Pay {br}· Do natychmiastowych płatności online {br}· Dodaj do portfela Apple/Google (tylko w obsługiwanych krajach)", "setup-card.orderCard": "Zamów kartę", "setup-card.virtual-card": "<PERSON><PERSON><PERSON><PERSON> kartę wirtualną", "setup.notifs.fakeAndroid.title": "Powiadomienia o płatnościach i przelewach przychodzących", "setup.notifs.fakeIos.subtitle": "Zeal może powiadamiać Cię o otrzymaniu gotówki lub o wydatkach kartą Visa. Możesz to zmienić później.", "setup.notifs.fakeIos.title": "Powiadomienia o płatnościach i przelewach przychodzących", "sign.PermitAllowanceItem.spendLimit": "Limit wydatków", "sign.ledger.subtitle": "Wysłano żądanie. Kontynuuj na portfelu.", "sign.ledger.title": "Podpisz na portfelu sprzętowym", "sign.passkey.subtitle": "Przeglądarka poprosi Cię o podpisanie za pomocą klucza Passkey powiązanego z tym portfelem. Kontynuuj tam.", "sign.passkey.title": "Wybierz klu<PERSON> Passkey", "signal_aborted_for_uknown_reason.title": "Żądanie sieciowe anulowane", "simulatedTransaction.BridgeTrx.info.title": "Bridge", "simulatedTransaction.CardTopUp.info.title": "<PERSON><PERSON><PERSON>j kart<PERSON>", "simulatedTransaction.CardTopUpTrx.info.title": "Dodaj środki do karty", "simulatedTransaction.NftCollectionApproval.approve": "Zatwierdź kolekcję NFT", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "Dodaj do karty", "simulatedTransaction.OrderEarnDepositBridge.title": "Wpłać do Earn", "simulatedTransaction.P2PTransaction.info.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.PermitSignMessage.title": "Pozwolenie", "simulatedTransaction.SingleNftApproval.approve": "Zatwierdź NFT", "simulatedTransaction.UnknownSignMessage.title": "Podpisz", "simulatedTransaction.Withdrawal.info.title": "<PERSON><PERSON><PERSON>ła<PERSON>", "simulatedTransaction.approval.title": "Zatwierdź", "simulatedTransaction.approve.info.title": "Zatwierdź", "simulatedTransaction.p2p.info.account": "Do", "simulatedTransaction.p2p.info.unlabelledAccount": "Portfel bez etykiety", "simulatedTransaction.unknown.info.receive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.info.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.using": "<PERSON><PERSON><PERSON> {app}", "simulation.approval.modal.text": "Zatwierdzaj<PERSON><PERSON> zgodę, zezwalasz aplikacji lub smart kontraktowi na używanie Twoich tokenów lub NFT w przyszłych transakcjach.", "simulation.approval.modal.title": "<PERSON>zym są zgody?", "simulation.approval.spend-limit.label": "Limit wydatków", "simulation.approve.footer.for": "Dla", "simulation.approve.unlimited": "<PERSON><PERSON> limitu", "simulationNotAvailable.title": "Nieznana operacja", "smart-wallet-activation-view.on": "<PERSON> sieci", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password może zablokować dostęp do Twojego portfela", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Użyj Apple lub Google, aby bez<PERSON><PERSON>nie skonfigurow<PERSON>ć Z<PERSON>", "smart-wallet.passkey-notice.title": "Unikaj 1Password", "spend-limits.high.modal.text": "Ustaw limit wydatków zbliżony do kwoty tokenów, której faktycznie będziesz używać w aplikacji lub smart kontrakcie. Wysokie limity są ryzykowne i mogą ułatwić oszustom kradzież Twoich tokenów.", "spend-limits.high.modal.text_sign_message": "Limit wydatków powinien być zbliżony do liczby tokenów, których faktycznie będziesz używać w aplikacji lub smart kontrakcie. Wysokie limity są ryzykowne i mogą ułatwić oszustom kradzież Twoich tokenów.", "spend-limits.high.modal.title": "Wysoki limit wydatków", "spend-limits.modal.text": "Limit wydatków określa, ile tokenów aplikacja może użyć w Twoim imieniu. Możesz zmienić lub usunąć ten limit w dowolnym momencie. Dla bezpieczeństwa utrzymuj limity wydatków na poziomie zbliżonym do kwoty, której faktycznie będziesz używać w aplikacji.", "spend-limits.modal.title": "Co to jest limit wydatków?", "spent-limit-info.modal.description": "Limit wydatków określa, ile tokenów aplikacja może użyć w Twoim imieniu. Moż<PERSON>z zmienić lub usunąć ten limit w dowolnym momencie. Aby zachować bezpieczeństwo, utrzymuj limity wydatków na poziomie zbliżonym do liczby tokenów, których faktycznie będziesz używać w aplikacji.", "spent-limit-info.modal.title": "C<PERSON>m jest limit wydatków?", "sswaps-io.transfer-provider": "Dostawca przelewu", "storage.accountDetails.activateWallet": "Aktywuj portfel", "storage.accountDetails.changeWalletLabel": "Zmień etykietę portfela", "storage.accountDetails.deleteWallet": "Usuń portfel", "storage.accountDetails.setup_recovery_kit": "Zestaw odzyskiwania", "storage.accountDetails.showPrivateKey": "Pokaż klucz prywatny", "storage.accountDetails.showWalletAddress": "Pokaż adres portfela", "storage.accountDetails.smartBackup": "Kopia zapasowa i odzyskiwanie", "storage.accountDetails.viewSsecretPhrase": "Zobacz Frazę odzyskiwania", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets?", "storage.manageAccounts.title": "Portfele", "submit-userop.progress.text": "Wysyłanie", "submit.error.amount_high": "Kwota zbyt wysoka", "submit.error.amount_hight": "Zbyt duża kwota", "submit.error.amount_low": "Kwota zbyt niska", "submit.error.amount_required": "Wprowadź kwotę", "submit.error.maximum_number_of_characters_exceeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> tre<PERSON> w<PERSON>", "submit.error.not_enough_balance": "Niewystarczaj<PERSON>ce saldo", "submit.error.recipient_required": "Wymagany odbiorca", "submit.error.routes_not_found": "Nie znaleziono trasy", "submitSafeTransaction.monitor.title": "Wynik transakcji", "submitSafeTransaction.sign.title": "Wynik transakcji", "submitSafeTransaction.state.sending": "Wysyłanie", "submitSafeTransaction.state.sign": "Tworzenie", "submitSafeTransaction.submittingToRelayer.title": "Wynik transakcji", "submitTransaction.cancel": "<PERSON><PERSON><PERSON>", "submitTransaction.cancel.attemptingToStop": "Próba zatrzymania", "submitTransaction.cancel.failedToStop": "<PERSON><PERSON> udało się zatrzymać", "submitTransaction.cancel.stopped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.cancel.title": "Podgląd transakcji", "submitTransaction.failed.banner.description": "<PERSON>eć anulowała transakcję. Spróbuj ponownie lub napisz do nas.", "submitTransaction.failed.banner.title": "Transakcja nieudana", "submitTransaction.failed.execution_reverted.title": "Aplikacja napotkała błąd", "submitTransaction.failed.execution_reverted_without_message.title": "Aplikacja napotkała błąd", "submitTransaction.failed.out_of_gas.description": "Przekroczono limit opłat sieciowych.", "submitTransaction.failed.out_of_gas.title": "<PERSON>ł<PERSON><PERSON> sieci", "submitTransaction.sign.title": "Wynik transakcji", "submitTransaction.speedUp": "Prz<PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue": "Dodano do kolejki", "submitTransaction.state.addedToQueue.short": "W kolejce", "submitTransaction.state.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.complete": "{currencyCode} dodano do Zeal", "submitTransaction.state.complete.subtitle": "Sprawdź swoje portfolio Zeal", "submitTransaction.state.completed": "<PERSON><PERSON><PERSON>", "submitTransaction.state.failed": "Niepowodzenie", "submitTransaction.state.includedInBlock": "Włączono do bloku", "submitTransaction.state.includedInBlock.short": "W bloku", "submitTransaction.state.replaced": "Zastąpiono", "submitTransaction.state.sendingToNetwork": "Wysyłanie do sieci", "submitTransaction.stop": "Zatrzymaj", "submitTransaction.submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.bundled": "W kolejce", "submitted-user-operation.state.completed": "Zakończono", "submitted-user-operation.state.failed": "Niepowodzenie", "submitted-user-operation.state.pending": "Przekazywanie", "submitted-user-operation.state.rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submittedTransaction.failed.title": "Transakcja nieudana", "success_splash.card_activated": "Karta aktywowana", "supportFork.give-feedback.title": "Prz<PERSON><PERSON> opinię", "supportFork.itercom.description": "Pytania o wpłaty, Earn, nagrody itp.", "supportFork.itercom.title": "Pytania o portfel", "supportFork.title": "Uzyskaj pomoc w zakresie", "supportFork.zendesk.subtitle": "Płatności kartą, <PERSON><PERSON><PERSON><PERSON>, zwroty", "supportFork.zendesk.title": "Płatności kartą i tożsamość", "supported-networks.ethereum.warning": "Wysokie opłaty", "supportedNetworks.networks": "Obsługiwane sieci", "supportedNetworks.oneAddressForAllNetworks": "Jeden adres dla wszystkich sieci", "supportedNetworks.receiveAnyAssets": "Wszyst<PERSON> sieci, jeden adres do odbioru.", "swap.form.error.no_routes_found": "Nie znaleziono tras", "swap.form.error.not_enough_balance": "Niewystarczaj<PERSON>ce saldo", "swaps-io-details.bank.serviceProvider": "Dostawca usługi", "swaps-io-details.details.processing": "Przetwarzanie", "swaps-io-details.pending": "Oczek<PERSON><PERSON><PERSON><PERSON>", "swaps-io-details.rate": "<PERSON><PERSON>", "swaps-io-details.serviceProvider": "Dostawca usługi", "swaps-io-details.transaction.from.processing": "Rozpoczęta transakcja", "swaps-io-details.transaction.networkFees": "<PERSON><PERSON><PERSON>", "swaps-io-details.transaction.state.completed-transaction": "Zakończona transakcja", "swaps-io-details.transaction.state.started-transaction": "Rozpoczęta transakcja", "swaps-io-details.transaction.to.processing": "Zakończona transakcja", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Wpłata w toku. Kinetex ją przetwarza.", "swapsIO.monitoring.awaitingLiqSend.title": "Opóźnione", "swapsIO.monitoring.awaitingRecive.title": "Przekazywanie", "swapsIO.monitoring.awaitingSend.title": "W kolejce", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Tokeny wrócą. Błąd po stronie Kinetex.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Zwracanie tokenów", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Błąd transferu. Spróbuj ponownie.", "swapsIO.monitoring.cancelledNoSlash.title": "Tokeny zwrócone", "swapsIO.monitoring.cancelledSlashed.subtitle": "Tokeny zwrócone. Błąd po stronie Kinetex.", "swapsIO.monitoring.cancelledSlashed.title": "Tokeny zwrócone", "swapsIO.monitoring.completed.title": "Ukończono", "taker-metadata.earn": "Zarabiaj w cyfrowym USD ze Sky", "taker-metadata.earn.aave": "Zarabiaj w cyfrowym EUR z Aave", "taker-metadata.earn.aave.cashout24": "<PERSON>y<PERSON><PERSON><PERSON><PERSON>, 24/7", "taker-metadata.earn.aave.trusted": "Zaufanie: 27 mld $, 2+ lata na rynku", "taker-metadata.earn.aave.yield": "Zysk naliczany co sekundę", "taker-metadata.earn.chf": "Zarabiaj w cyfrowym CHF", "taker-metadata.earn.chf.cashout24": "<PERSON>y<PERSON><PERSON><PERSON><PERSON>, 24/7", "taker-metadata.earn.chf.trusted": "Zaufane: 28 mln Fr.", "taker-metadata.earn.chf.yield": "Zysk naliczany co sekundę", "taker-metadata.earn.usd.cashout24": "<PERSON>y<PERSON><PERSON><PERSON><PERSON>, 24/7", "taker-metadata.earn.usd.trusted": "Zaufanie: 10,7 mld $, 5+ lat na rynku", "taker-metadata.earn.usd.yield": "Zysk naliczany co sekundę", "test": "<PERSON><PERSON><PERSON><PERSON>", "to.titile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "Aktywa", "token.groupHeader.titleWithSum": "Aktywa {sum}", "token.hidden_tokens.page.title": "<PERSON>k<PERSON><PERSON>", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "<PERSON><PERSON><PERSON> token", "token.widget.cashback_empty": "Brak transakcji", "token.widget.emptyState": "Brak tokenów w portfelu", "tokens.cash": "Gotówka", "top-up-card-from-earn-view.approve.for": "Dla", "top-up-card-from-earn-view.approve.into": "Na", "top-up-card-from-earn-view.swap.from": "Z", "top-up-card-from-earn-view.swap.to": "Do", "top-up-card-from-earn-view.withdraw.to": "Do", "top-up-card-from-earn.trx.title.approval": "Zatwierdź zamianę", "top-up-card-from-earn.trx.title.swap": "Dodaj do karty", "top-up-card-from-earn.trx.title.withdrawal": "Wypłać z Earn", "topUpDapp.connectWallet": "Połącz portfel", "topup-fee-breakdown.bungee-fee": "Opłata dostawcy zewnętrznego", "topup-fee-breakdown.header": "<PERSON><PERSON><PERSON>", "topup-fee-breakdown.network-fee": "<PERSON><PERSON><PERSON>", "topup-fee-breakdown.total-fee": "<PERSON><PERSON><PERSON>", "topup.continue-in-wallet": "Kontynuuj w swoim portfelu", "topup.send.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "topup.submit-transaction.close": "Zamknij", "topup.submit-transaction.sent-to-wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON> {amount}", "topup.to": "Do", "topup.transaction.complete.close": "Zamknij", "topup.transaction.complete.try-again": "Spróbuj ponownie", "transaction-request.nonce-too-low.modal.button-text": "Zamknij", "transaction-request.nonce-too-low.modal.text": "Ta transakcja została już wykonana.", "transaction-request.nonce-too-low.modal.title": "Transakcja z tym nonce już istnieje", "transaction-request.replaced.modal.button-text": "Zamknij", "transaction-request.replaced.modal.text": "Nie można śledzić statusu transakcji.", "transaction-request.replaced.modal.title": "Nie można znaleźć statusu transakcji", "transaction.activity.details.modal.close": "Zamknij", "transaction.cancel_popup.cancel": "Nie, czekaj", "transaction.cancel_popup.confirm": "Tak przerwij", "transaction.cancel_popup.description": "<PERSON><PERSON><PERSON><PERSON> nową opłatę, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. {oldFee}", "transaction.cancel_popup.description_without_original": "Zatrzymanie wymaga nowej opłaty sieciowej", "transaction.cancel_popup.not_supported.subtitle": "Ta sieć nie wspiera zatrzymywania. {network}", "transaction.cancel_popup.not_supported.title": "Nieobsługiwane", "transaction.cancel_popup.stopping_fee": "Opłata sieciowa za zatrzymanie", "transaction.cancel_popup.title": "<PERSON><PERSON><PERSON>ymać transakcję?", "transaction.in-progress": "W toku", "transaction.inProgress": "W toku", "transaction.speed_up_popup.cancel": "Nie, czekaj", "transaction.speed_up_popup.confirm": "Prz<PERSON><PERSON><PERSON>", "transaction.speed_up_popup.description": "<PERSON><PERSON>, mus<PERSON><PERSON> nową opłatę sieciową zamiast pierwotnej opłaty w wysokości {amount}", "transaction.speed_up_popup.description_without_original": "<PERSON><PERSON><PERSON>, mus<PERSON><PERSON> nową opłatę sieciową", "transaction.speed_up_popup.seed_up_fee_title": "Opłata za przyspieszenie", "transaction.speed_up_popup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> transakcję?", "transaction.speedup_popup.not_supported.subtitle": "Przyspieszanie transakcji nie jest obsługiwane w sieci {network}", "transaction.speedup_popup.not_supported.title": "Nieobsługiwane", "transaction.subTitle.failed": "Niepowodzenie", "transactionDetails.cashback.not-qualified": "<PERSON><PERSON>", "transactionDetails.cashback.paid": "{amount} w<PERSON><PERSON><PERSON><PERSON>o", "transactionDetails.cashback.pending": "{amount} oczekuje", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "<PERSON><PERSON><PERSON><PERSON>", "transactionDetails.cashback_estimate": "S<PERSON>owany cashback", "transactionDetails.category": "Kategoria", "transactionDetails.exchangeRate": "<PERSON><PERSON> w<PERSON>y", "transactionDetails.location": "Lokalizacja", "transactionDetails.payment-approved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zatwierdzona", "transactionDetails.payment-declined": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> odrzu<PERSON>", "transactionDetails.payment-reversed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cofnię<PERSON>", "transactionDetails.recharge.amountSentFromEarn.title": "Kwota wysłana z konta Earn", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "Doładowano na kartę", "transactionDetails.recharge.rate.title": "<PERSON><PERSON>", "transactionDetails.recharge.transactionId.title": "ID transakcji", "transactionDetails.refund": "Zwrot środków", "transactionDetails.reversal": "Cofnięcie", "transactionDetails.transactionCurrency": "<PERSON><PERSON><PERSON>", "transactionDetails.transactionId": "ID transakcji", "transactionDetails.type": "Transakcja", "transactionRequestWidget.approve.subtitle": "Dla {target}", "transactionRequestWidget.p2p.subtitle": "Do {target}", "transactionRequestWidget.unknown.subtitle": "<PERSON><PERSON><PERSON> {target}", "transactionSafetyChecksPopup.title": "Kontrola bezpieczeństwa transakcji", "transactions.main.activity.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transactions.page.hiddenActivity.title": "<PERSON>k<PERSON><PERSON>", "transactions.page.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transactions.viewTRXHistory.emptyState": "Brak transakcji", "transactions.viewTRXHistory.errorMessage": "Błąd wczytywania historii transakcji", "transactions.viewTRXHistory.hidden.emptyState": "Brak ukrytych transakcji", "transactions.viewTRXHistory.noTxHistoryForTestNets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nie jest obsługiwana w sieciach testowych", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nie jest obsługiwana w sieciach testowych{br}<link>Eksplorator sieci</link>", "transfer_provider": "Dostawca przelewu", "transfer_setup_with_different_wallet.subtitle": "Przelewy są powiązane z innym portfelem.", "transfer_setup_with_different_wallet.swtich_and_continue": "Przełącz i kontynuuj", "transfer_setup_with_different_wallet.title": "Zmień portfel", "tx-sent-to-wallet.button": "Zamknij", "tx-sent-to-wallet.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w {wallet}", "unblockProviderInfo.fees": "0% do 5 tys. $/mies., a 0,2% powyżej.", "unblockProviderInfo.registration": "Unblock jest regulowany przez FNTT i Fincen. <link>Dowiedz się więcej</link>", "unblockProviderInfo.selfCustody": "Środki trafiają na Twój prywatny portfel.", "unblock_invalid_faster_payment_configuration.subtitle": "Podane konto bankowe nie obsługuje europejskich przelewów SEPA ani brytyjskich Faster Payments. Podaj inne konto.", "unblock_invalid_faster_payment_configuration.title": "Wymagane inne konto", "unknownTransaction.primaryText": "Transakcja kartą", "unsupportedCountry.subtitle": "Przelewy bankowe nie są jeszcze dostępne w Twoim kraju.", "unsupportedCountry.title": "Niedostępne w {country}", "update-app-popup.subtitle": "Najnowsza aktualizacja zawiera poprawki i nowe funkcje. Zaktualizuj aplikację, aby w pełni wykorzystać możliwości Zeal.", "update-app-popup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "update-app-popup.update-now": "Zaktualizuj te<PERSON>", "user_associated_with_other_merchant.subtitle": "Ten portfel nie może być używany do przelewów bankowych. Użyj innego portfela lub zgłoś się na naszym Discordzie, aby uzyskać wsparcie i aktualizacje.", "user_associated_with_other_merchant.title": "Nie można użyć portfela", "user_associated_with_other_merchant.try_with_another_wallet": "Spróbuj z innym portfelem", "user_email_already_exists.subtitle": "Ustawiono już przelewy na innym portfelu.", "user_email_already_exists.title": "Przelewy ustawione na innym portfelu", "user_email_already_exists.try_with_another_wallet": "Spróbuj z innym portfelem", "validation.invalid.iban": "Nieprawidłowy IBAN", "validation.required": "<PERSON><PERSON><PERSON><PERSON>", "validation.required.first_name": "Wymagane imię", "validation.required.iban": "Wymagany IBAN", "validation.required.last_name": "Wymagane nazwisko", "verify-passkey.cta": "Zweryfikuj passkey", "verify-passkey.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy Twój passkey został utworzony i jest odpowiednio zabezpieczony.", "verify-passkey.title": "Weryfikacja passkey", "view-cashback.cashback-next-cycle": "Stawka cashbacku za {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "<PERSON><PERSON><PERSON><PERSON>, a<PERSON> <PERSON><PERSON><PERSON> cashback", "view-cashback.pending": "{money} Oczekuje", "view-cashback.pending-rewards.not_paid": "Od<PERSON><PERSON><PERSON> za {days}d", "view-cashback.pending-rewards.paid": "Odebrano w tym tygodniu", "view-cashback.received-rewards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Niepotwierdzone płatności", "view-cashback.upcoming": "Na<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {money}", "virtual-card-order.configure-safe.loading-text": "Tworzenie karty", "virtual-card-order.create-order.loading-text": "Aktywacja karty", "virtual-card-order.create-order.success-text": "Karta aktywowana", "virtualCard.activateCard": "Aktywuj kartę", "walletDeleteConfirm.main_action": "Usuń", "walletDeleteConfirm.subtitle": "Musisz go ponownie zaimport<PERSON>ć, a<PERSON> z<PERSON><PERSON><PERSON> portfolio lub do<PERSON> transakcji", "walletDeleteConfirm.title": "<PERSON><PERSON><PERSON>ć portfel?", "walletSetting.header": "Ustawienia portfela", "wallet_connect.connect.cancel": "<PERSON><PERSON><PERSON>", "wallet_connect.connect.connect_button": "Połącz Zeal", "wallet_connect.connect.title": "Połącz", "wallet_connect.connected.title": "Połącz<PERSON>", "wallet_connect_add_chain_missing.title": "<PERSON><PERSON><PERSON>", "wallet_connect_proposal_expired.title": "Połączenie wygasło", "withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withdraw.confirmation.close": "<PERSON><PERSON><PERSON>", "withdraw.confirmation.continue": "Potwierdź", "withdrawal_request.completed": "Zakończono", "withdrawal_request.pending": "Oczek<PERSON><PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.cta.primary.connecting": "Łączenie...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Połącz", "zeal-dapp.connect-wallet.cta.secondary": "<PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.title": "Połącz portfel, aby kontynuować", "zealSmartWalletInfo.gas": "<PERSON><PERSON>ć opłaty sieciowe w wielu tokenach; używaj popularnych tokenów ERC20 w obsługiwanych sieciach, a nie tylko tokenów natywnych.", "zealSmartWalletInfo.recover": "Brak Fraz odzyskiwania; odzyskaj dostęp za pomocą biometrycznego passkeya z menedżera haseł, iCloud lub konta Google.", "zealSmartWalletInfo.selfCustodial": "Pełna kontrola nad środkami; podpisy Passkey są weryfikowane w łańcuchu bloków, aby zminimalizować centralne zależności.", "zealSmartWalletInfo.title": "Informacje o Smart Wallets Zeal", "zeal_a_rewards_already_claimed_error.title": "Nagroda już odebrana", "zwidget.minimizedDisconnected.label": "Zeal rozł<PERSON>ony"}