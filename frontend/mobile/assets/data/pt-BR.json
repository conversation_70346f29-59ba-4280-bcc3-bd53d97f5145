{"Account.ListItem.details.label": "<PERSON><PERSON><PERSON>", "AddFromAddress.success": "Car<PERSON>ira salva", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{<PERSON><PERSON><PERSON><PERSON> carteira} one{{count} carteira} other{{count} carteiras}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Frase secreta {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Crie carteiras de uma Frase Secreta salva.", "AddFromExistingSecretPhrase.SelectPhrase.title": "Escolha uma Fr<PERSON>", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "Sua frase secreta pode guardar várias carteiras. Escolha as que você quer usar.", "AddFromExistingSecretPhrase.WalletSelection.title": "Adição rápida de carteira", "AddFromExistingSecretPhrase.success": "Carteiras adicionadas à Zeal", "AddFromHardwareWallet.subtitle": "Selecione sua carteira de hardware para conectar ao Zeal", "AddFromHardwareWallet.title": "Carteira de hardware", "AddFromNewSecretPhrase.WalletSelection.subtitle": "Selecione as carteiras para importar.", "AddFromNewSecretPhrase.WalletSelection.title": "Importar carteiras", "AddFromNewSecretPhrase.accounts": "Carteiras", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "A Frase Secreta é um chaveiro de carteiras.{br}{br}Importe carteiras agora ou mais tarde.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Carteiras de Frase Secreta", "AddFromNewSecretPhrase.subtitle": "Digite sua Frase Secreta com espaços.", "AddFromNewSecretPhrase.success_secret_phrase_added": "Frase Secreta adicionada 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Carteiras adicionadas à Zeal", "AddFromNewSecretPhrase.wallets": "Carteiras", "AddFromPrivateKey.subtitle": "Digite sua chave privada", "AddFromPrivateKey.success": "Chave privada adicionada 🎉", "AddFromPrivateKey.title": "<PERSON><PERSON><PERSON>", "AddFromPrivateKey.typeOrPaste": "Digite ou cole aqui", "AddFromSecretPhrase.importWallets": "{count,plural,=0{Nenhuma selecionada} one{Importar carteira} other{Importar {count} carteiras}}", "AddFromTrezor.AccountSelection.title": "Importar carteiras Trezor", "AddFromTrezor.hwWalletTip.subtitle": "Uma carteira de hardware armazena milhões de carteiras com endereços diferentes. Importe quantas precisar agora ou adicione mais depois.", "AddFromTrezor.hwWalletTip.title": "Importando de carteiras de hardware", "AddFromTrezor.importAccounts": "{count,plural,=0{Nenhuma selecionada} one{Importar carteira} other{Importar {count} carteiras}}", "AddFromTrezor.success": "Carteiras adicionadas ao Zeal", "ApprovalSpenderTypeCheck.failed.subtitle": "Provavelmente um golpe: aprovadores devem ser contratos", "ApprovalSpenderTypeCheck.failed.title": "O aprovador é uma carteira, não um contrato", "ApprovalSpenderTypeCheck.passed.subtitle": "<PERSON><PERSON><PERSON><PERSON>, você aprova ativos para contratos", "ApprovalSpenderTypeCheck.passed.title": "O aprovador é um contrato inteligente", "BestReturns.subtitle": "<PERSON><PERSON>, com todas as taxas inclusas.", "BestReturnsPopup.title": "<PERSON><PERSON> retorno", "BlacklistCheck.Failed.subtitle": "Denúncias de atividade maliciosa por <source></source>", "BlacklistCheck.Failed.title": "Site na lista de bloqueio", "BlacklistCheck.Passed.subtitle": "Nenhuma denúncia de atividade maliciosa por <source></source>", "BlacklistCheck.Passed.title": "Site não está na lista de bloqueio", "BlacklistCheck.failed.statusButton.label": "Este site foi denunciado", "BridgeRoute.slippage": "Slippage {slippage}", "BridgeRoute.title": "<PERSON><PERSON><PERSON> bridge", "CheckConfirmation.InProgress": "Em andamento...", "CheckConfirmation.success.splash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ChooseImportOrCreateSecretPhrase.subtitle": "Importe uma frase secreta ou crie outra", "ChooseImportOrCreateSecretPhrase.title": "Adicionar frase secreta", "ConfirmTransaction.Simuation.Skeleton.title": "Fazendo verificações de segurança…", "ConnectionSafetyCheckResult.passed": "Verificação de segurança aprovada", "ContactGnosisPaysupport": "Suporte Gnosis Pay", "CopyKeyButton.copied": "Copiado", "CopyKeyButton.copyYourKey": "Copiar sua chave", "CopyKeyButton.copyYourPhrase": "Copiar sua frase", "DAppVerificationCheck.Failed.subtitle": "O site não está listado em <source></source>", "DAppVerificationCheck.Failed.title": "Site não encontrado nos registros de apps", "DAppVerificationCheck.Passed.subtitle": "O site está listado em <source></source>", "DAppVerificationCheck.Passed.title": "Site consta nos registros de apps", "DAppVerificationCheck.failed.statusButton.label": "Site não encontrado nos registros de apps", "ERC20.tokens.emptyState": "Nenhum token encontrado", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Taxa de Prioridade", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Últimos {period}: entre {from} e {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Taxa Base: {baseFee} • Margem de segurança: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Não conseguimos obter a Taxa Base atual", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "<PERSON>or que o próximo Nonce. A transação vai travar", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "Não é possível definir um nonce menor que o atual", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "Não conseguimos calcular a Taxa de Prioridade", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Não conseguimos obter a taxa máxima atual", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "Taxa Máxima", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Pode travar até que as taxas de rede diminuam", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "Taxa Base Máxima", "EditFeeModal.Custom.gasLimit.title": "Limite <PERSON> {gasLimit}", "EditFeeModal.Custom.title": "Configurações avançadas", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Vai travar até que a Taxa Base diminua", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Taxa baixa. Pode travar", "EditFeeModal.EditGasLimit.estimatedGas": "Gás est.: {estimated} • Margem de segurança: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Menor que o limite estimado. A transação vai falhar", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Menor que o limite sugerido. A transação pode falhar", "EditFeeModal.EditGasLimit.subtitle": "Defina o valor máximo de taxa de rede que esta transação pode usar. A transação falhará se você definir um limite menor que o necessário", "EditFeeModal.EditGasLimit.title": "Editar limite da taxa de rede", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "<PERSON>or que o limite mínimo de <PERSON>: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "<PERSON>or que o próximo Nonce. A transação vai travar", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "Não é possível definir um nonce menor que o atual", "EditFeeModal.EditNonce.subtitle": "Sua transação vai travar com outro nonce", "EditFeeModal.EditNonce.title": "<PERSON><PERSON> nonce", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "Precisa de {amount} para enviar", "EditFeeModal.Header.Time.unknown": "Tempo Desconhecido", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Máx. {fee}", "EditFeeModal.Header.fee.unknown": "Taxa desconhecida", "EditFeeModal.Header.subsequent_failed": "Estimativa antiga: a atualização falhou", "EditFeeModal.Layout.Header.ariaLabel": "Taxa atual", "EditFeeModal.MaxFee.subtitle": "A taxa máxima é o valor mais alto que você pode pagar por uma transação, mas geralmente você pagará a taxa prevista. Essa margem extra ajuda sua transação a ser concluída, mesmo que a rede fique mais lenta ou mais cara.", "EditFeeModal.MaxFee.title": "Taxa de Rede Máxima", "EditFeeModal.SelectPreset.Time.unknown": "Tempo Desconhecido", "EditFeeModal.SelectPreset.ariaLabel": "Selecionar predefinição de taxa", "EditFeeModal.SelectPreset.fast": "<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.SelectPreset.normal": "Normal", "EditFeeModal.SelectPreset.slow": "<PERSON><PERSON>", "EditFeeModal.ariaLabel": "Editar taxa de rede", "FailedSimulation.Confirmation.Item.subtitle": "Ocorreu um erro interno", "FailedSimulation.Confirmation.Item.title": "Não foi possível simular a transação", "FailedSimulation.Confirmation.subtitle": "Tem certeza de que quer continuar?", "FailedSimulation.Confirmation.title": "Você está assinando às cegas", "FailedSimulation.Title": "Erro na simulação", "FailedSimulation.footer.subtitle": "Ocorreu um erro interno", "FailedSimulation.footer.title": "Não foi possível simular a transação", "FeeForecastWidget.NotEnoughBalance.errorMessage": "É preciso ter {amount} para enviar a transação", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Pode levar tempo para processar", "FeeForecastWidget.networkFee": "Taxa de rede", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Não foi possível calcular a taxa de rede", "FeeForecastWidget.subsequentFailed.message": "As estimativas podem estar desatualizadas, a última atualização falhou", "FeeForecastWidget.unknownDuration": "Desconhecido", "FeeForecastWidget.unknownFee": "Desconhecida", "GasCurrencySelector.balance": "Saldo: {balance}", "GasCurrencySelector.networkFee": "Taxa de rede", "GasCurrencySelector.payNetworkFeesUsing": "Pagar taxas de rede com", "GasCurrencySelector.removeDefaultGasToken.description": "Pague taxas com o maior saldo", "GasCurrencySelector.removeDefaultGasToken.title": "Gerenciamento automático de taxas", "GasCurrencySelector.save": "<PERSON><PERSON>", "GoogleDriveBackup.BeforeYouBegin.first_point": "Se eu esquecer minha senha do <PERSON>, perderei meus ativos para sempre", "GoogleDriveBackup.BeforeYouBegin.second_point": "Se eu perder o acesso ao meu Google Drive ou modificar meu Arquivo de Recuperação, perderei meus ativos para sempre", "GoogleDriveBackup.BeforeYouBegin.subtitle": "Por favor, entenda e aceite o seguinte ponto sobre carteira privada:", "GoogleDriveBackup.BeforeYouBegin.third_point": "O Zeal não pode me ajudar a recuperar minha senha do Zeal ou meu acesso ao Google Drive", "GoogleDriveBackup.BeforeYouBegin.title": "<PERSON><PERSON> começa<PERSON>", "GoogleDriveBackup.loader.subtitle": "Por favor, aprove a solicitação no Google Drive para enviar seu Arquivo de Recuperação", "GoogleDriveBackup.loader.title": "Aguardando <PERSON>...", "GoogleDriveBackup.success": "Backup concluído com sucesso 🎉", "MonitorOffRamp.overServiceTime": "A maioria das transferências é concluída em {estimated_time}, mas às vezes podem levar mais tempo devido a verificações adicionais. Isso é normal e os fundos estão seguros enquanto essas verificações são feitas.{br}{br}Se a transação não for concluída em {support_soft_deadline}, por favor, {contact_support}", "MonitorOnRamp.contactSupport": "Fale com o suporte", "MonitorOnRamp.from": "De", "MonitorOnRamp.fundsReceived": "Fundos recebidos", "MonitorOnRamp.overServiceTime": "A maioria das transferências é concluída em {estimated_time}, mas às vezes podem demorar mais devido a verificações adicionais. Isso é normal e seus fundos estão seguros enquanto essas verificações são feitas.{br}{br}Se a transação não for concluída em {support_soft_deadline}, por favor {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Enviando para sua carteira", "MonitorOnRamp.to": "Para", "MonitorOnRamp.waitingForTransfer": "Aguardando você transferir os fundos", "NftCollectionCheck.failed.subtitle": "Coleção não verificada em <source></source>", "NftCollectionCheck.failed.title": "A coleção não é verificada", "NftCollectionCheck.passed.subtitle": "Coleção verificada em <source></source>", "NftCollectionCheck.passed.title": "A coleção é verificada", "NftCollectionInfo.entireCollection": "Coleção inteira", "NoSigningKeyStore.createAccount": "<PERSON><PERSON><PERSON>", "NonceRangeError.biggerThanCurrent.message": "A transação ficará travada", "NonceRangeError.lessThanCurrent.message": "A transação vai falhar", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "O Nonce é maior que o atual. Reduza o Nonce para evitar que a transação fique travada.", "NonceRangeErrorPopup.biggerThanCurrent.title": "A transação ficará travada", "P2pReceiverTypeCheck.failed.subtitle": "Você está enviando para o endereço certo?", "P2pReceiverTypeCheck.failed.title": "Destinatário é um contrato, não uma carteira", "P2pReceiverTypeCheck.passed.subtitle": "<PERSON><PERSON><PERSON><PERSON>, você envia ativos para outras carteiras", "P2pReceiverTypeCheck.passed.title": "O destinatário é uma carteira", "PasswordCheck.title": "Digite a senha", "PasswordChecker.subtitle": "Por favor, digite sua senha para confirmar que é você", "PermitExpirationCheck.failed.subtitle": "Mantenha o prazo curto, só o necessário", "PermitExpirationCheck.failed.title": "Prazo de expiração longo", "PermitExpirationCheck.passed.subtitle": "Por quanto tempo um app pode usar seus tokens", "PermitExpirationCheck.passed.title": "Prazo de expiração não é muito longo", "PrivateKeyValidationError.moreThanMaximumWords": "Máx. {count} palavras", "PrivateKeyValidationError.notValidPrivateKey": "Esta chave privada não é válida", "PrivateKeyValidationError.secretPhraseIsInvalid": "A frase secreta não é válida", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Palavra #{index} incorreta ou inválida", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} palavra} other{{count} palavras}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Frases e chaves são criptografadas aqui.", "SecretPhraseReveal.header": "Anote a Frase Secreta", "SecretPhraseReveal.hint": "Não compartilhe sua frase com ninguém. Guarde-a segura e offline.", "SecretPhraseReveal.skip.subtitle": "Você pode fazer isso depois, mas se perder este aparelho antes de anotar sua frase, perderá todos os ativos que adicionou a esta carteira.", "SecretPhraseReveal.skip.takeTheRisk": "Vou arri<PERSON>r", "SecretPhraseReveal.skip.title": "Pular a anotação da frase?", "SecretPhraseReveal.skip.writeDown": "<PERSON><PERSON><PERSON>", "SecretPhraseReveal.skipForNow": "Pular agora", "SecretPhraseReveal.subheader": "Anote e guarde em um lugar seguro e offline. <PERSON><PERSON><PERSON>, vamos pedir que você a verifique.", "SecretPhraseReveal.verify": "Verificar", "SelectCurrency.tokens": "Tokens", "SelectCurrency.tokens.emptyState": "Nenhum token encontrado", "SelectRoute.slippage": "Slippage {slippage}", "SelectRoutes.emptyState": "Nenhuma rota encontrada para este swap", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Conectar carteira", "SendERC20.labelAddress.inputPlaceholder": "Nome da carteira", "SendERC20.labelAddress.subtitle": "Dê um nome a esta carteira para encontrá-la mais tarde.", "SendERC20.labelAddress.title": "Nomear esta carteira", "SendERC20.send_to": "Enviar para", "SendERC20.tokens": "Tokens", "SendOrReceive.bankTransfer.primaryText": "Transferência bancária", "SendOrReceive.bankTransfer.shortText": "Depósito e saque grátis e instantâneo", "SendOrReceive.bridge.primaryText": "Bridge", "SendOrReceive.bridge.shortText": "Transferir tokens entre redes", "SendOrReceive.receive.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.receive.shortText": "Receber tokens ou colecionáveis", "SendOrReceive.send.primaryText": "Enviar", "SendOrReceive.send.shortText": "Enviar tokens para qualquer endereço", "SendOrReceive.swap.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.swap.shortText": "Trocar tokens", "SendSafeTransaction.Confirm.loading": "Fazendo verificações de segurança…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Criptografar Arquivo de Recuperação com senha", "SetupRecoveryKit.google.subtitle": "Sincronizado {date}", "SetupRecoveryKit.google.title": "Backup no Google Drive", "SetupRecoveryKit.subtitle": "Você precisará de pelo menos uma forma de restaurar sua conta se desinstalar o Zeal ou trocar de dispositivo", "SetupRecoveryKit.title": "Configurar Kit de Recuperação", "SetupRecoveryKit.writeDown.subtitle": "Anote a Frase Secreta", "SetupRecoveryKit.writeDown.title": "Backup manual", "Sign.CheckSafeDeployment.activate": "Ativar", "Sign.CheckSafeDeployment.subtitle": "Antes de fazer login em um app ou assinar uma mensagem off-chain, você precisa ativar seu dispositivo nesta rede. Isso acontece depois de instalar ou recuperar uma Smart Wallet.", "Sign.CheckSafeDeployment.title": "Ativar dispositivo nesta rede", "Sign.Simuation.Skeleton.title": "Fazendo verificações de segurança…", "SignMessageSafetyCheckResult.passed": "Verificações de Segurança Aprovadas", "SignMessageSafetyChecksPopup.title.permits": "Verificações de segurança de permissão", "SimulationFailedConfirmation.subtitle": "Simulamos esta transação e encontramos um problema que a faria falhar. Você pode enviar a transação, mas ela provavelmente vai falhar e você poderá perder sua taxa de rede.", "SimulationFailedConfirmation.title": "É provável que a transação falhe", "SimulationNotSupported.Title": "Simulação não{br}suportada em{br}{network}", "SimulationNotSupported.footer.subtitle": "Você ainda pode enviar esta transação", "SimulationNotSupported.footer.title": "Simulação não suportada", "SlippagePopup.custom": "Personalizado", "SlippagePopup.presetsHeader": "Slippage da troca", "SlippagePopup.title": "Ajustes de slippage", "SmartContractBlacklistCheck.failed.subtitle": "Denúncias de atividade maliciosa por <source></source>", "SmartContractBlacklistCheck.failed.title": "Contrato na lista de bloqueio", "SmartContractBlacklistCheck.passed.subtitle": "Nenhuma denúncia de atividade maliciosa por <source></source>", "SmartContractBlacklistCheck.passed.title": "Contrato não está na lista de bloqueio", "SuspiciousCharactersCheck.Failed.subtitle": "Essa é uma tática comum de phishing", "SuspiciousCharactersCheck.Failed.title": "Verificamos padrões comuns de phishing", "SuspiciousCharactersCheck.Passed.subtitle": "Verificamos tentativas de phishing", "SuspiciousCharactersCheck.Passed.title": "Endereço sem caracteres incomuns", "SuspiciousCharactersCheck.failed.statusButton.label": "O endereço tem caracteres incomuns ", "TokenVerificationCheck.failed.subtitle": "O token não está listado em <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} não é verificado pela CoinGecko", "TokenVerificationCheck.passed.subtitle": "O token está listado em <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} é verificado pela CoinGecko", "TopupDapp.MonitorTransaction.success.splash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TransactionSafetyCheckResult.passed": "Verificações de Segurança Aprovadas", "TransactionSimulationCheck.failed.subtitle": "Erro: {errorMessage}", "TransactionSimulationCheck.failed.title": "A transação provavelmente vai falhar", "TransactionSimulationCheck.passed.subtitle": "Simulação feita usando <source></source>", "TransactionSimulationCheck.passed.title": "A prévia da transação foi bem-sucedida", "TrezorError.trezor_action_cancelled.action": "<PERSON><PERSON><PERSON>", "TrezorError.trezor_action_cancelled.subtitle": "Você rejeitou a transação na sua hardware wallet", "TrezorError.trezor_device_used_elsewhere.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "<PERSON><PERSON> to<PERSON> as outras sessões abertas e tente sincronizar sua Trezor novamente", "TrezorError.trezor_method_cancelled.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "Permita que a Trezor exporte carteiras para o Zeal", "TrezorError.trezor_permissions_not_granted.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "<PERSON><PERSON> permis<PERSON> ao Zeal para ver todas as carteiras", "TrezorError.trezor_pin_cancelled.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "Sessão cancelada no dispositivo", "TrezorError.trezor_popup_closed.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "A janela da Trezor fechou inesperadamente", "TrxLikelyToFail.lessThanEstimatedGas.message": "A transação vai falhar", "TrxLikelyToFail.lessThanMinimumGas.message": "A transação vai falhar", "TrxLikelyToFail.lessThanSuggestedGas.message": "Provavelmente vai falhar", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "O limite da taxa de rede da transação é muito baixo. Aumente o limite para o sugerido para evitar falhas.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "É provável que a transação falhe", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "O limite da taxa de rede é menor que o estimado. Aumente o limite para o valor sugerido.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "A transação vai falhar", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "A Taxa Base Máxima é menor que a taxa base atual. Aumente-a para evitar que a transação fique travada.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "A transação ficará travada", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "A Taxa Máxima da transação é muito baixa. Aumente a Taxa Máxima para evitar que a transação fique travada.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "A transação ficará travada", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "A Taxa de Prioridade é menor que a recomendada. Aumente a Taxa de Prioridade para acelerar a transação.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "A transação pode demorar para ser concluída", "UnsupportedMobileNetworkLayout.gotIt": "Entendi!", "UnsupportedMobileNetworkLayout.subtitle": "Você ainda não pode fazer transações ou assinar mensagens na rede com id {networkHexId} com a versão móvel do Zeal{br}{br}Mude para a extensão do navegador para transacionar nesta rede, enquanto trabalhamos para adicionar suporte a ela 🚀", "UnsupportedMobileNetworkLayout.title": "Rede não compatível com a versão móvel do Zeal", "UnsupportedSafeNetworkLayout.subtitle": "Você não pode fazer transações ou assinar mensagens na rede {network} com uma Zeal Smart Wallet{br}{br}Mude para uma rede compatível ou use uma carteira Legacy.", "UnsupportedSafeNetworkLayoutk.title": "Rede não compatível com a Smart Wallet", "UserConfirmationPopup.goBack": "<PERSON><PERSON><PERSON>", "UserConfirmationPopup.submit": "Prosseguir", "ViewPrivateKey.header": "Chave privada", "ViewPrivateKey.hint": "Não compartilhe sua chave privada com ninguém. Guarde-a segura e offline.", "ViewPrivateKey.subheader.mobile": "Toque para revelar sua chave privada", "ViewPrivateKey.subheader.web": "Passe o mouse para revelar sua chave privada", "ViewPrivateKey.unblur.mobile": "Toque para revelar", "ViewPrivateKey.unblur.web": "Passe o mouse para revelar", "ViewSecretPhrase.PasswordChecker.subtitle": "Digite sua senha para criptografar o Arquivo de Recuperação. Você precisará se lembrar dela no futuro.", "ViewSecretPhrase.done": "Pronto", "ViewSecretPhrase.header": "Frase Secreta", "ViewSecretPhrase.hint": "Não compartilhe sua frase com ninguém. Guarde-a segura e offline.", "ViewSecretPhrase.subheader.mobile": "Toque para revelar sua Frase Secreta", "ViewSecretPhrase.subheader.web": "Passe o mouse para revelar sua Frase Secreta", "ViewSecretPhrase.unblur.mobile": "Toque para revelar", "ViewSecretPhrase.unblur.web": "Passe o mouse para revelar", "account-details.monerium": "As transferências são feitas com Monerium, uma IME autorizada e regulamentada. <link>Saiba mais</link>", "account-details.unblock": "As transferências são feitas pela Unblock, um provedor de serviços de câmbio e custódia autorizado e registrado. <link>Saiba mais</link>", "account-selector.empty-state": "<PERSON><PERSON><PERSON><PERSON> carteira encontrada", "account-top-up.select-currency.title": "Tokens", "account.accounts_not_found": "Não encontramos nenhuma carteira", "account.accounts_not_found_search_valid_address": "A carteira não está na sua lista", "account.add.create_new_secret_phrase": "Criar frase secreta", "account.add.create_new_secret_phrase.subtext": "Uma nova frase secreta de 12 palavras", "account.add.fromRecoveryKit.fileNotFound": "Não encontramos seu arquivo", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "Tentar novamente", "account.add.fromRecoveryKit.fileNotFound.explanation": "Use a conta certa com o backup da Zeal.", "account.add.fromRecoveryKit.fileNotValid": "Arquivo de recuperação inválido", "account.add.fromRecoveryKit.fileNotValid.explanation": "Tipo de arquivo errado ou modificado.", "account.add.import_secret_phrase": "Importar frase secreta", "account.add.import_secret_phrase.subtext": "<PERSON><PERSON><PERSON> na <PERSON>, Metamask ou outras", "account.add.select_type.add_hardware_wallet": "Carteira de hardware", "account.add.select_type.existing_smart_wallet": "Smart Wallet existente", "account.add.select_type.private_key": "Chave privada", "account.add.select_type.seed_phrase": "Frase de recuperação", "account.add.select_type.title": "Importar carteira", "account.add.select_type.zeal_recovery_file": "Arquivo de Recuperação Zeal", "account.add.success.title": "Nova carteira criada 🎉", "account.addLabel.header": "Dê um nome à sua carteira", "account.addLabel.labelError.labelAlreadyExist": "Este nome já existe. Tente outro.", "account.addLabel.labelError.maxStringLengthExceeded": "Número máximo de caracteres atingido", "account.add_active_wallet.primary_text": "<PERSON><PERSON><PERSON><PERSON>", "account.add_active_wallet.short_text": "<PERSON><PERSON><PERSON>, conectar ou importar carteira", "account.add_from_ledger.success": "Carteiras adicionadas ao Zeal", "account.add_tracked_wallet.primary_text": "Adicionar carteira somente leitura", "account.add_tracked_wallet.short_text": "Ver portfólio e atividades", "account.button.unlabelled-wallet": "Carteira sem nome", "account.create_wallet": "<PERSON><PERSON><PERSON>", "account.label.edit.title": "Editar nome da carteira", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON> em {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Arquivo de recuperação inválido", "account.recoveryKit.selectBackupFile.subtitle": "Selecione o arquivo de recuperação.", "account.recoveryKit.selectBackupFile.title": "Arquivo de recuperação", "account.recoveryKit.success.recoveryFileFound": "Arquivo de recuperação encontrado 🎉", "account.select_type_of_account.create_eoa.short": "Carteira legado para especialistas", "account.select_type_of_account.create_eoa.title": "Criar carteira com Frase Secreta", "account.select_type_of_account.create_safe_wallet.title": "Criar Smart Wallet", "account.select_type_of_account.existing_smart_wallet": "Smart Wallet existente", "account.select_type_of_account.hardware_wallet": "Carteira de hardware", "account.select_type_of_account.header": "<PERSON><PERSON><PERSON><PERSON>", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Chave privada / Frase de recuperação", "account.select_type_of_account.read_only_wallet": "Carteira somente leitura", "account.select_type_of_account.read_only_wallet.short": "Visualize qualquer portfólio", "account.topup.title": "Adicionar fundos ao Zeal", "account.view.error.refreshAssets": "<PERSON><PERSON><PERSON><PERSON>", "account.widget.refresh": "<PERSON><PERSON><PERSON><PERSON>", "account.widget.settings": "Configurações", "accounts.view.copied-text": "Copiado {formattedAddress}", "accounts.view.copiedAddress": "Copiado {formattedAddress}", "action.accept": "Aceitar", "action.accpet": "Aceitar", "action.allow": "<PERSON><PERSON><PERSON>", "action.back": "Voltar", "action.cancel": "<PERSON><PERSON><PERSON>", "action.card-activation.title": "Ativar cart<PERSON>", "action.claim": "Resgatar", "action.close": "<PERSON><PERSON><PERSON>", "action.complete-steps": "Concluir", "action.confirm": "Confirmar", "action.continue": "<PERSON><PERSON><PERSON><PERSON>", "action.copy-address-understand": "<PERSON> - <PERSON><PERSON><PERSON>", "action.deposit": "Depositar", "action.done": "Pronto", "action.dontAllow": "Não permitir", "action.edit": "editar", "action.email-required": "Informe o e-mail", "action.enterPhoneNumber": "Informar telefone", "action.expand": "Expandir", "action.fix": "<PERSON><PERSON><PERSON><PERSON>", "action.getStarted": "<PERSON><PERSON><PERSON>", "action.got_it": "<PERSON><PERSON><PERSON>", "action.hide": "Ocultar", "action.import": "Importar", "action.import-keys": "Importar chaves", "action.importKeys": "Importar chaves", "action.minimize": "<PERSON><PERSON><PERSON>", "action.next": "<PERSON><PERSON><PERSON><PERSON>", "action.ok": "OK", "action.reduceAmount": "Usar valor máximo", "action.refreshWebsite": "Atualizar site", "action.remove": "Remover", "action.remove-account": "Remover conta", "action.requestCode": "Solicitar código", "action.resend_code": "Reenviar código", "action.resend_code_with_time": "<PERSON><PERSON><PERSON><PERSON> có<PERSON> {time}", "action.retry": "Tentar novamente", "action.reveal": "<PERSON><PERSON><PERSON>", "action.save": "<PERSON><PERSON>", "action.save_changes": "Salvar RPC", "action.search": "Buscar", "action.seeAll": "Ver tudo", "action.select": "Selecionar", "action.send": "Enviar", "action.skip": "<PERSON><PERSON>", "action.submit": "Enviar", "action.understood": "<PERSON><PERSON><PERSON>", "action.update": "<PERSON><PERSON><PERSON><PERSON>", "action.update-gnosis-pay-owner.complete": "Concluir", "action.zeroAmount": "Inserir valor", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Colecionáveis", "action_bar_title.tokens": "Tokens", "action_bar_title.transaction_request": "Solicitação de transação", "activate-monerium.loading": "Configurando sua conta pessoal", "activate-monerium.success.title": "Monerium ativado", "activate-physical-card-widget.subtitle": "A entrega pode levar 3 semanas", "activate-physical-card-widget.title": "Ativar cartão físico", "activate-smart-wallet.title": "Ativar carteira", "active_and_tracked_wallets.title": "A Zeal cobre todas as suas taxas na {network}, permitindo que você transacione de graça!", "activity.approval-amount.revoked": "Revogada", "activity.approval-amount.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "activity.approval.approved_for": "Aprovado para", "activity.approval.approved_for_with_target": "Aprovado para {approvedTo}", "activity.approval.revoked_for": "Revogado para", "activity.bank.serviceProvider": "<PERSON><PERSON><PERSON>", "activity.bridge.serviceProvider": "<PERSON><PERSON><PERSON>", "activity.cashback.period": "Período do cashback", "activity.filter.card": "Cartão", "activity.rate": "Cotação", "activity.receive.receivedFrom": "Recebido de", "activity.send.sendTo": "Enviado para", "activity.smartContract.unknown": "Contrato desconhecido", "activity.smartContract.usingContract": "<PERSON>ando contrato", "activity.subtitle.pending_timer": "{timerString} Pendente", "activity.title.arbitrary_smart_contract_interaction": "{function} em {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Interação com contrato desconhecido", "activity.title.bridge.from": "Bridge de {token}", "activity.title.bridge.to": "Bridge para {token}", "activity.title.buy": "Compra de {asset}", "activity.title.card_owners_updated": "Titulares do cartão atualizados", "activity.title.card_spend_limit_updated": "Limite de gastos do cartão definido", "activity.title.cashback_deposit": "<PERSON><PERSON><PERSON><PERSON> no Cashback", "activity.title.cashback_reward": "Cashback recebido", "activity.title.cashback_withdraw": "<PERSON><PERSON>", "activity.title.claimed_reward": "Recompensa resgatada", "activity.title.deployed_smart_wallet_gnosis": "<PERSON>ta criada", "activity.title.deposit_from_bank": "Depósito do banco", "activity.title.deposit_into_card": "Depósito no cartão", "activity.title.deposit_into_earn": "<PERSON><PERSON><PERSON><PERSON> em {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} em {smartContract}", "activity.title.from": "De {sender}", "activity.title.pendidng_areward_claim": "Resgatando recompensa", "activity.title.pendidng_breward_claim": "Resgatando recompensa", "activity.title.recharge_disabledh": "Recarga do cartão desativada", "activity.title.recharge_set": "Meta de recarga definida", "activity.title.recovered_smart_wallet_gnosis": "Instalação em novo dispositivo", "activity.title.send_pending": "Para {receiver}", "activity.title.send_to_bank": "Para o banco", "activity.title.swap": "Compra de {token}", "activity.title.to": "Para {receiver}", "activity.title.withdraw_from_card": "Saque do cartão", "activity.title.withdraw_from_earn": "<PERSON>que de {earn}", "activity.transaction.networkFees": "Taxas de rede", "activity.transaction.state": "Transação concluída", "activity.transaction.state.completed": "Transação concluída", "activity.transaction.state.failed": "Falha na transação", "add-account.section.import.header": "Importar", "add-another-card-owner": "Adicionar outro titular do cartão", "add-another-card-owner.Recommended.footnote": "Adicione sua carteira Zeal como coproprietária do seu cartão Gnosis Pay", "add-another-card-owner.Recommended.primaryText": "Adicionar <PERSON> ao Gnosis Pay", "add-another-card-owner.recommended": "Recomendado", "add-owner.confirmation.subtitle": "Por segurança, as alterações levam 3 minutos para serem processadas. Durante esse tempo, seu cartão será temporariamente bloqueado e pagamentos não serão possíveis.", "add-owner.confirmation.title": "Seu cartão será bloqueado por 3 min para a atualização", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Não foi possível adicionar a carteira, ela já está em uso", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "Tente outra carteira", "add.account.backup.decrypt.success": "<PERSON><PERSON>ira restaurada", "add.account.backup.password.passwordIncorrectMessage": "Senha incorreta", "add.account.backup.password.subtitle": "Digite a senha do arquivo de recuperação.", "add.account.backup.password.title": "Digite a senha", "add.account.google.login.subtitle": "Aprove a solicitação no Google Drive.", "add.account.google.login.title": "Aguardando <PERSON>...", "add.readonly.already_added": "Carteira já adicionada", "add.readonly.continue": "<PERSON><PERSON><PERSON><PERSON>", "add.readonly.empty": "Digite um endereço ou ENS", "addBankRecipient.title": "<PERSON><PERSON><PERSON><PERSON>", "add_funds.deposit_from_bank_account": "Depositar da conta bancária", "add_funds.from_another_wallet": "De outra carteira", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Conectar ao dApp de recarga", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Use o dApp para receber de outra carteira.", "add_funds.from_crypto_wallet.header": "De outra carteira", "add_funds.from_crypto_wallet.header.show_wallet_address": "Mostrar o endereço da sua carteira", "add_funds.from_exchange.header": "Enviar de uma exchange", "add_funds.from_exchange.header.copy_wallet_address": "Copie seu endere<PERSON>o Zeal", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance etc.", "add_funds.from_exchange.header.open_exchange": "<PERSON>bra o app ou site da exchange", "add_funds.from_exchange.header.selected_token": "Enviar {token} para a Zeal", "add_funds.from_exchange.header.selected_token.subtitle": "Em {network}", "add_funds.from_exchange.header.send_selected_token": "Enviar token compatível", "add_funds.from_exchange.header.send_selected_token.subtitle": "Selecione o token e a rede compatíveis", "add_funds.import_wallet": "Importar carteira de cripto existente", "add_funds.title": "Adicionar fundos à sua conta", "add_funds.transfer_from_exchange": "Transferir de uma exchange", "address.add.header": "Veja sua carteira na Zeal{br}em modo de visualização", "address.add.subheader": "Digite seu endereço ou ENS para ver seus ativos de todas as redes EVM em um só lugar. Crie ou importe mais carteiras depois.", "address_book.change_account.bank_transfers.header": "Destinatários bancários", "address_book.change_account.bank_transfers.primary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "address_book.change_account.cta": "<PERSON><PERSON><PERSON><PERSON>", "address_book.change_account.search_placeholder": "Adicionar ou buscar endereço", "address_book.change_account.tracked_header": "Carteiras somente leitura", "address_book.change_account.wallets_header": "Carteiras ativas", "app-association-check-failed.modal.cta": "Tentar novamente", "app-association-check-failed.modal.subtitle": "Tente novamente. Problemas de conexão estão atrasando a busca das suas Passkeys. Se o erro persistir, reinicie o Zeal e tente mais uma vez.", "app-association-check-failed.modal.subtitle.creation": "Tente novamente. Problemas de conexão estão atrasando a criação da Passkey. Se o erro persistir, reinicie o Zeal e tente mais uma vez.", "app-association-check-failed.modal.title.creation": "Seu dispositivo não conseguiu criar uma passkey", "app-association-check-failed.modal.title.signing": "Seu dispositivo não conseguiu carregar as passkeys", "app.app_protocol_group.borrowed_tokens": "Tokens emprestados", "app.app_protocol_group.claimable_amount": "Valor a receber", "app.app_protocol_group.health_rate": "Taxa de saúde", "app.app_protocol_group.lending": "Emprést<PERSON>", "app.app_protocol_group.locked_tokens": "Tokens bloqueados", "app.app_protocol_group.nfts": "Colecionáveis", "app.app_protocol_group.reward_tokens": "Tokens de recompensa", "app.app_protocol_group.supplied_tokens": "Tokens fornecidos", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Token em vesting", "app.appsGroupHeader.discoverMore": "Des<PERSON>bra mais", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Pesquisar ou digitar URL", "app.error-banner.cory": "Copiar dados do erro", "app.error-banner.retry": "Tentar novamente", "app.list_item.rewards": "Recompensas {value}", "app.position_details.health_rate.description": "A saúde é calculada dividindo o valor do seu empréstimo pelo valor da sua garantia.", "app.position_details.health_rate.title": "O que é taxa de saúde?", "approval.edit-limit.label": "Editar limite de gastos", "approval.permit_info": "Informações do Permit", "approval.spend-limit.edit-modal.cancel": "<PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.limit-label": "Limite de gastos", "approval.spend-limit.edit-modal.max-limit-error": "Atenção: limite alto", "approval.spend-limit.edit-modal.revert": "Reverter alteraç<PERSON>es", "approval.spend-limit.edit-modal.set-to-unlimited": "Definir como ilimitado", "approval.spend-limit.edit-modal.submit": "<PERSON><PERSON>", "approval.spend-limit.edit-modal.title": "<PERSON><PERSON>", "approval.spend_limit_info": "O que é limite de gastos?", "approval.what_are_approvals": "O que são aprovações?", "apps_list.page.emptyState": "Nenhum app ativo", "backpace.removeLastDigit": "Remover último dí<PERSON>", "backup-banner.backup_now": "Fazer backup", "backup-banner.risk_losing_funds": "Faça backup agora ou arrisque perder seus fundos", "backup-banner.title": "<PERSON><PERSON>ira sem backup", "backupRecoverySmartWallet.noExportPrivateKeys": "Backup automático: sua Smart Wallet é salva como uma passkey. Não precisa de frase semente nem de chave privada.", "backupRecoverySmartWallet.safeContracts": "Segurança com múltiplas chaves: as carteiras Zeal usam contratos Safe, permitindo que vários dispositivos aprovem uma transação. Sem ponto único de falha.", "backupRecoverySmartWallet.security": "Vários dispositivos: você pode usar sua carteira em vários dispositivos com a Passkey. Cada um recebe sua própria chave privada.", "backupRecoverySmartWallet.showLocalPrivateKey": "Modo expert: você pode exportar a chave privada deste dispositivo, usá-la em outra carteira e conectar em <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Mostrar chave privada</Key>", "backupRecoverySmartWallet.storingKeys": "Sincronização na nuvem: a passkey é armazenada com segurança no iCloud, Gerenciador de senhas do Google ou no seu gerenciador de senhas.", "backupRecoverySmartWallet.title": "Backup e recuperação da Smart Wallet", "balance-change.card.titile": "Cartão", "balanceChange.pending": "Pendente", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Selecione o provedor de serviço", "bank-transfer.change-deposit-receiver.subtitle": "Esta carteira receberá todos os depósitos bancários", "bank-transfer.change-deposit-receiver.title": "Definir carteira de recebimento", "bank-transfer.change-owner.subtitle": "Esta carteira é usada para entrar e recuperar sua conta de transferência bancária", "bank-transfer.change-owner.title": "Definir proprietário da conta", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Todos os depósitos bancários que você enviar para a Zeal serão recebidos por esta carteira.", "bank-transfer.configrm-change-deposit-receiver.title": "Alterar carteira de recebimento", "bank-transfer.configrm-change-owner.subtitle": "Tem certeza de que quer alterar o proprietário da conta? Esta carteira é usada para entrar e recuperar sua conta de transferência bancária.", "bank-transfer.configrm-change-owner.title": "Alterar proprietá<PERSON> da conta", "bank-transfer.deposit.widget.status.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfer.deposit.widget.status.funds_received": "Fundos recebidos", "bank-transfer.deposit.widget.status.sending_to_wallet": "Enviando para a carteira", "bank-transfer.deposit.widget.status.transfer-on-hold": "Transferência em espera", "bank-transfer.deposit.widget.status.transfer-received": "Enviando para a carteira", "bank-transfer.deposit.widget.subtitle": "{from} para {to}", "bank-transfer.deposit.widget.title": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.intro.bulletlist.point_1": "Configurado com Unblock", "bank-transfer.intro.bulletlist.point_2": "Transfira entre EUR/GBP e mais de 10 tokens", "bank-transfer.intro.bulletlist.point_3": "Taxa de 0% até US$ 5 mil por mês, 0,2% depois disso", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Enviando para o banco", "bank-transfer.withdrawal.widget.status.in-progress": "Transferência em andamento", "bank-transfer.withdrawal.widget.status.on-hold": "Transferência em análise", "bank-transfer.withdrawal.widget.status.success": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfer.withdrawal.widget.subtitle": "{from} para {to}", "bank-transfer.withdrawal.widget.title": "<PERSON><PERSON>", "bank-transfers.bank-account-actions.remove-this-account": "Remover esta conta", "bank-transfers.bank-account-actions.switch-to-this-account": "Mudar para esta conta", "bank-transfers.deposit.fees-for-less-than-5k": "Taxas para US$ 5 mil ou menos", "bank-transfers.deposit.fees-for-more-than-5k": "Taxas para mais de US$ 5 mil", "bank-transfers.set-receiving-bank.title": "Definir banco de recebimento", "bank-transfers.settings.account_owner": "Propriet<PERSON><PERSON> da <PERSON>ta", "bank-transfers.settings.receiver_of_bank_deposits": "Re<PERSON>bedor de depósitos bancários", "bank-transfers.settings.receiver_of_withdrawals": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "bank-transfers.settings.registered_email": "E-mail cadastrado", "bank-transfers.settings.title": "Configurações de transferência bancária", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} Conta", "bank-transfers.setup.bank-account": "Conta bancária", "bankTransfer.withdraw.max_loading": "Máx.: {amount}", "bank_details_do_not_match.got_it": "<PERSON><PERSON><PERSON>", "bank_details_do_not_match.subtitle": "O código do banco e o número da conta não correspondem. Verifique os dados e tente novamente.", "bank_details_do_not_match.title": "Dados bancários não correspondem", "bank_tranfsers.select_country_of_residence.country_not_supported": "<PERSON><PERSON><PERSON><PERSON>, transferências bancárias não estão disponíveis em {country} ainda", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Abra seu aplicativo do banco", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Envie {fiatCurrencyCode} para a sua conta", "bank_transfer.deposit.header": "{fullName}<PERSON><PERSON> da conta pessoal", "bank_transfer.kyc_status_widget.subtitle": "Transferências bancárias", "bank_transfer.kyc_status_widget.title": "Verificando identidade", "bank_transfer.personal_details.date_of_birth": "Data de nascimento", "bank_transfer.personal_details.date_of_birth.invalid_format": "Data inválida", "bank_transfer.personal_details.date_of_birth.too_young": "Você precisa ter pelo menos 18 anos", "bank_transfer.personal_details.first_name": "Nome", "bank_transfer.personal_details.last_name": "Sobrenome", "bank_transfer.personal_details.title": "Seus dados", "bank_transfer.reference.label": "Referência (Opcional)", "bank_transfer.reference_message": "Enviado do Zeal", "bank_transfer.residence_details.address": "<PERSON><PERSON> endere<PERSON>", "bank_transfer.residence_details.city": "Cidade", "bank_transfer.residence_details.country_of_residence": "País de residência", "bank_transfer.residence_details.country_placeholder": "<PERSON><PERSON>", "bank_transfer.residence_details.postcode": "CEP", "bank_transfer.residence_details.street": "<PERSON><PERSON>", "bank_transfer.residence_details.your_residence": "Sua residência", "bank_transfers.choose-wallet.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.test": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.warning.subtitle": "Você só pode vincular uma carteira por vez. Não será possível alterar a carteira vinculada.", "bank_transfers.choose-wallet.warning.title": "Escolha sua carteira com atenção", "bank_transfers.choose_wallet.subtitle": "Escolha a carteira para vincular à sua conta e fazer transferências diretas. ", "bank_transfers.choose_wallet.title": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.currency_is_currently_not_supported": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit-header": "Depositar", "bank_transfers.deposit.account-name": "Nome da conta", "bank_transfers.deposit.account-number-copied": "Número da conta copiado", "bank_transfers.deposit.amount-input": "Valor a depositar", "bank_transfers.deposit.amount-output": "Valor de destino", "bank_transfers.deposit.amount-output.error": "erro", "bank_transfers.deposit.buttet-point.receive-crypto": "Receba {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.currency-not-supported.subtitle": "Depósitos bancários de {code} foram desativados até novo aviso.", "bank_transfers.deposit.currency-not-supported.title": "{code} depósitos indisponíveis no momento", "bank_transfers.deposit.default-token.balance": "Saldo {amount}", "bank_transfers.deposit.deposit-header": "Depositar", "bank_transfers.deposit.enter_amount": "Insira o valor", "bank_transfers.deposit.iban-copied": "IBAN copiado", "bank_transfers.deposit.increase-amount": "A transferência mínima é de {limit}", "bank_transfers.deposit.loading": "Carregando", "bank_transfers.deposit.max-limit-reached": "O valor excede o limite máximo de transferência", "bank_transfers.deposit.modal.kyc.button-text": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.modal.kyc.text": "Para verificar sua identidade, precisaremos de alguns dados pessoais e documentos. O envio costuma levar apenas alguns minutos.", "bank_transfers.deposit.modal.kyc.title": "Verifique sua identidade para aumentar seus limites", "bank_transfers.deposit.reduce_amount": "Reduza o valor", "bank_transfers.deposit.show-account.account-number": "Número da conta", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Sort code", "bank_transfers.deposit.sort-code-copied": "Sort code copiado", "bank_transfers.deposit.withdraw-header": "<PERSON><PERSON>", "bank_transfers.failed_to_load_fee": "Desconhecido", "bank_transfers.fees": "Taxas", "bank_transfers.increase-amount": "A transferência mínima é de {limit}", "bank_transfers.insufficient-funds": "<PERSON><PERSON> insuficiente", "bank_transfers.select_country_of_residence.title": "Onde você mora?", "bank_transfers.setup.cta": "Configurar transferências", "bank_transfers.setup.enter-amount": "Insira o valor", "bank_transfers.source_of_funds.form.business_income": "Renda empresarial", "bank_transfers.source_of_funds.form.other": "Outro", "bank_transfers.source_of_funds.form.pension": "Aposentadoria", "bank_transfers.source_of_funds.form.salary": "<PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.title": "Origem dos seus fundos", "bank_transfers.source_of_funds_description.placeholder": "Descreva a origem dos fundos...", "bank_transfers.source_of_funds_description.title": "Conte-nos mais sobre a origem dos seus fundos", "bank_transfers.withdraw-header": "<PERSON><PERSON>", "bank_transfers.withdraw.amount-input": "Valor para sacar", "bank_transfers.withdraw.max-limit-reached": "Limite de transferência excedido", "bank_transfers.withdrawal.verify-id": "<PERSON><PERSON>ir valor", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Limite máximo de entrada excedido", "banner.above_maximum_limit.maximum_limit_per_deposit": "Este é o limite máximo por depósito", "banner.above_maximum_limit.subtitle": "Limite máximo de entrada excedido", "banner.above_maximum_limit.title": "<PERSON>uza o valor para {amount} ou menos", "banner.above_maximum_limit.title.default": "Reduza o valor", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Valor mínimo n<PERSON> atingido", "banner.below_minimum_limit.minimum_limit_for_token": "Este é o limite mínimo para este token", "banner.below_minimum_limit.title": "Aumente o valor para {amount} ou mais", "banner.below_minimum_limit.title.default": "Aumente o valor", "breaard.in_porgress.info_popup.cta": "Gaste para ganhar {earn}", "breaard.in_porgress.info_popup.footnote": "Ao usar o Zeal e o cartão Gnosis Pay, você concorda com os termos e condições desta campanha de recompensas.", "breaward.in_porgress.info_popup.bullet_point_1": "Gaste {remaining} nos próximos {time} para resgatar esta recompensa.", "breaward.in_porgress.info_popup.bullet_point_2": "Apenas compras com Gnosis Pay contam para o valor gasto.", "breaward.in_porgress.info_popup.bullet_point_3": "Após resgatar sua recompensa, ela será enviada para sua conta Zeal.", "breaward.in_porgress.info_popup.header": "<PERSON><PERSON><PERSON> {earn}, gastan<PERSON> {remaining}", "breward.celebration.for_spending": "Por gastar com seu cartão", "breward.dc25-eligible-celebration.for_spending": "Você está entre os primeiros {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "Você não ficou entre os primeiros {limit} a gastar", "breward.expired_banner.earn_by_spending": "Ganhe {earn} gastando {amount}", "breward.expired_banner.reward_expired": "{earn} recompensa expirou", "breward.in_progress_banner.cta.title": "Gaste para ganhar {earn}", "breward.ready_to_claim.error.try_again": "Tentar novamente", "breward.ready_to_claim.error_title": "Falha ao resgatar recompensa", "breward.ready_to_claim.in_progress": "Resgatando recompensa", "breward.ready_to_claim.youve_earned": "Você ganhou {earn}!", "breward_already_claimed.title": "Recompensa já resgatada. Se não a recebeu, fale com o suporte.", "breward_cannotbe_claimed.title": "Resgate indisponível. Tente mais tarde.", "bridge.best_return": "Rota com melhor retorno", "bridge.best_serivce_time": "Rota mais rápida", "bridge.check_status.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge.check_status.progress_text": "Fazendo bridge de {from} para {to}", "bridge.remove_topup": "Remover recarga", "bridge.request_status.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge.request_status.pending": "Pendente", "bridge.widget.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge.widget.currencies": "{from} para {to}", "bridge_rote.widget.title": "Bridge", "browse.discover_more_apps": "Descubra mais apps", "browse.google_search_term": "Pesquisar \"{searchTerm}\"", "brward.celebration.you_earned": "<PERSON><PERSON><PERSON> ganhou", "brward.expired_banner.subtitle": "Mais sorte da próxima vez", "brward.in_progress_banner.subtitle": "Expira em {expiredInFormatted}", "buy": "<PERSON><PERSON><PERSON>", "buy.enter_amount": "Insira o valor", "buy.loading": "Carregando...", "buy.no_routes_found": "Nenhuma rota encontrada", "buy.not_enough_balance": "<PERSON><PERSON> insuficiente", "buy.select-currency.title": "Selecionar token", "buy.select-to-currency.title": "Comprar tokens", "buy_form.title": "Comprar token", "cancelled-card.create-card-button.primary": "Pedir novo cartão virtual", "cancelled-card.switch-card-button.primary": "T<PERSON>car de <PERSON>", "cancelled-card.switch-card-button.short-text": "Você tem outro cartão ativo", "card": "Cartão", "card-add-cash.confirm-stage.banner.no-routes-found": "Nenhuma rota encontrada, tente outro token ou valor", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Você precisa de {amount} a mais {symbol} para pagar as taxas", "card-add-cash.confirm-stage.banner.value-loss": "Voc<PERSON> perderá {loss} em valor", "card-add-cash.confirm-stage.banner.value-loss.revert": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.cancel": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.continue": "<PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.enter-amount": "Insira valor", "card-add-cash.edit-stage.cta.reduce-to-max": "<PERSON><PERSON>", "card-add-cash.edit-staget.banner.no-routes-found": "Nenhuma rota encontrada, tente outro token ou valor", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Enviamos o pedido à sua hardware wallet. Continue por lá.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Assine na hardware wallet", "card-balance": "Saldo: {balance}", "card-cashback.status.title": "<PERSON><PERSON><PERSON><PERSON> no Cashback", "card-copy-safe-address.copy_address": "<PERSON><PERSON><PERSON>", "card-copy-safe-address.copy_address.done": "Copiado", "card-copy-safe-address.warning.description": "Este endereço só pode receber {cardAsset} na Gnosis Chain. Não envie ativos de outras redes para este endereço. Eles serão perdidos.", "card-copy-safe-address.warning.header": "<PERSON><PERSON> a<PERSON> {cardAsset} na Gnosis Chain", "card-marketing-card.center.subtitle": "Taxas de câmbio", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "Rendimento", "card-marketing-card.right.subtitle": "Bônus de cadastro", "card-marketing-card.title": "O cartão VISA da Europa com alto rendimento", "card-marketing-tile.get-started": "<PERSON><PERSON><PERSON>", "card-select-from-token-title": "Selecionar token de origem", "card-top-up.banner.subtitle.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card-top-up.banner.subtitle.failed": "Fal<PERSON>", "card-top-up.banner.subtitle.pending": "{timerString} Pendente", "card-top-up.banner.title": "Depositando {amount}", "card-topup.select-token.emptyState": "Nenhum token encontrado", "card.activate.card_number_not_valid": "Cartão inválido. Verifique e tente de novo.", "card.activate.invalid_card_number": "Número de cartão inválido.", "card.activation.activate_physical_card": "Ativar cartão físico", "card.add-cash.amount-to-withdraw": "<PERSON>or da recarga", "card.add-from-earn-form.title": "Adicionar <PERSON> ao cartão", "card.add-from-earn-form.withdraw-to-card": "<PERSON><PERSON><PERSON><PERSON>", "card.add-from-earn.amount-to-withdraw": "Valor a sacar para o cartão", "card.add-from-earn.enter-amount": "Insira o valor", "card.add-from-earn.loading": "Carregando", "card.add-from-earn.max-label": "Saldo: {amount}", "card.add-from-earn.no-routes-found": "Nenhuma rota encontrada", "card.add-from-earn.not-enough-balance": "<PERSON><PERSON> insuficiente", "card.add-owner.queued": "Adição de proprietário na fila", "card.add-to-wallet-flow.subtitle": "Faça pagamentos com sua carteira", "card.add-to-wallet.copy-card-number": "Copie o número do cartão abaixo", "card.add-to-wallet.title": "Adicionar ao {platformName} Wallet", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "CANCELADO", "card.card-owner-not-found.disconnect-btn": "Remover cartão do Zeal", "card.card-owner-not-found.subtitle": "Para continuar usando seu cartão Gnosis Pay no Zeal, atualize o proprietário para reconectá-lo", "card.card-owner-not-found.title": "Reconectar cartão", "card.card-owner-not-found.update-owner-btn": "<PERSON><PERSON><PERSON>r <PERSON>", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} em {date}", "card.cashback.widgetNoCashback.subtitle": "Deposite para começar a ganhar", "card.cashback.widgetNoCashback.title": "<PERSON><PERSON><PERSON> até {defaultPercentage} de Cashback", "card.cashback.widgetcashbackValue.rewards": "{amount} pendentes", "card.cashback.widgetcashbackValue.title": "{percentage} de Cashback", "card.choose-wallet.connect_card": "Conectar cartão", "card.choose-wallet.create-new": "Adicionar nova carteira como titular", "card.choose-wallet.import-another-wallet": "Importar outra carteira", "card.choose-wallet.import-current-owner": "Importar proprietário atual do cartão", "card.choose-wallet.import-current-owner.sub-text": "Importe chaves/frase da carteira titular", "card.choose-wallet.title": "Selecione a carteira para gerenciar seu cartão", "card.connectWalletToCardGuide": "<PERSON><PERSON><PERSON> endere<PERSON> da carteira", "card.connectWalletToCardGuide.addGnosisPayOwner": "Adicionar proprie<PERSON><PERSON>", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Abra Gnosispay.com com sua outra carteira{br}2. Clique em “Conta”{br}3. Clique em “Detalhes da conta”{br}4. Clique em “Editar”, ao lado de “Proprietário da conta”, e{br}5. Clique em “Adicionar endereço”{br}6. Cole seu endereço Zeal e clique em salvar", "card.connectWalletToCardGuide.header": "Conecte {account} ao cartão Gnosis Pay", "card.connect_card.start": "Conectar Gnosis Pay Card", "card.copiedAddress": "Endereço copiado {formattedAddress}", "card.disconnect-account.title": "Desconectar conta", "card.hw-wallet-support-drop.add-owner-btn": "Adicionar novo dono", "card.hw-wallet-support-drop.disconnect-btn": "Remover cartão do Zeal", "card.hw-wallet-support-drop.subtitle": "Para continuar usando seu Cartão Gnosis Pay no Zeal, adicione outro proprietário que não seja uma Hardware Wallet.", "card.hw-wallet-support-drop.title": "O Zeal não tem mais suporte a Hardware Wallets para o Cartão", "card.kyc.continue": "Contin<PERSON>r configura<PERSON>", "card.list_item.title": "Cartão", "card.onboarded.transactions.empty.description": "Suas atividades de pagamento aparecerão aqui", "card.onboarded.transactions.empty.title": "Atividade", "card.order.continue": "<PERSON><PERSON><PERSON><PERSON> pedido <PERSON>", "card.order.free_virtual_card": "Cartão virtual gratuito", "card.order.start": "Pedir <PERSON> gr<PERSON>", "card.owner-not-imported.cancel": "<PERSON><PERSON><PERSON>", "card.owner-not-imported.import": "Importar", "card.owner-not-imported.subtitle": "Para autorizar esta transação, conecte a carteira proprietária da sua conta Gnosis Pay ao Zeal. Atenção: este passo é diferente do seu login habitual da carteira Gnosis Pay.", "card.owner-not-imported.title": "Adicionar proprietário da conta Gnosis Pay", "card.page.order_free_physical_card": "Peça cartão físico grátis", "card.pin.change_pin_at_atm": "O PIN pode ser mudado em ATMs específicos.", "card.pin.timeout": "A tela fechará em {seconds} s", "card.quick-actions.add-assets": "<PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.add-cash": "<PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.details": "<PERSON><PERSON><PERSON>", "card.quick-actions.freeze": "Bloquear", "card.quick-actions.freezing": "Bloqueando", "card.quick-actions.unfreeze": "Desb<PERSON>que<PERSON>", "card.quick-actions.unfreezing": "Des<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.withdraw": "<PERSON><PERSON>", "card.read-only-detected.create-new": "Adicionar nova carteira como titular", "card.read-only-detected.import-current-owner": "Importar chaves para {wallet}", "card.read-only-detected.import-current-owner.sub-text": "<PERSON><PERSON>rte as chaves/frase da carteira {address}", "card.read-only-detected.title": "Gerenciar cartão de carteira de leitura", "card.remove-owner.queued": "Remoção de proprietário na fila", "card.settings.disconnect-from-zeal": "Desconectar do Zeal", "card.settings.edit-owners": "Alterar proprietários do cartão", "card.settings.getCard": "Pedir outro cartão", "card.settings.getCard.subtitle": "Cartões virtuais ou físicos", "card.settings.notRecharging": "Recarga automática desativada", "card.settings.notifications.subtitle": "Receber notificações de pagamento", "card.settings.notifications.title": "Notificações do cartão", "card.settings.page.title": "Configurações do Cartão", "card.settings.select-card.cancelled-cards": "Cartões cancelados", "card.settings.setAutoRecharge": "Definir recarga automática", "card.settings.show-card-address": "Mostrar endereço do cartão", "card.settings.spend-limit": "Definir limite de gastos", "card.settings.spend-limit-title": "Limite <PERSON><PERSON><PERSON> atual: {limit}", "card.settings.switch-active-card": "Trocar cartão ativo", "card.settings.switch-active-card-description": "Cartão ativo: {card}", "card.settings.switch-card.card-item.cancelled": "Cancelado", "card.settings.switch-card.card-item.frozen": "<PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.title": "Cartão Gnosis Pay", "card.settings.switch-card.card-item.title.physical": "Cartão Físico", "card.settings.switch-card.card-item.title.virtual": "Cartão Virtual", "card.settings.switch-card.title": "Selecionar cartão", "card.settings.targetBalance": "Saldo alvo: {threshold}", "card.settings.view-pin": "Ver PIN", "card.settings.view-pin-description": "Proteja sempre seu PIN", "card.title": "Cartão", "card.transactions.header": "Transações do cartão", "card.transactions.see_all": "<PERSON><PERSON> to<PERSON> as transaçõ<PERSON>", "card.virtual": "VIRTUAL", "cardCashback.onboarding.bullets.cashback_sent_weekly": "O cashback é enviado para seu cartão no início da semana seguinte à da compra.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "Quanto maior o seu depósito, mais você ganha em cada compra.", "cardCashback.onboarding.title": "<PERSON><PERSON><PERSON> até {percentage} de Cashback", "cardCashbackWithdraw.amount": "Valor do saque", "cardCashbackWithdraw.header": "Sacar {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "Cartão bloqueado, não foi possível ativar", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "Até {percentage}", "cardWidget.startEarning": "Comece a render", "cardWithdraw.amount": "Valor do saque", "cardWithdraw.header": "Sacar do cartão", "cardWithdraw.selectWithdrawWallet.title": "Escolha a carteira para{br}receber o saque", "cardWithdraw.success.cta": "<PERSON><PERSON><PERSON>", "cardWithdraw.success.subtitle": "<PERSON><PERSON>, saques levam 3 min.", "cardWithdraw.success.title": "A alteração leva 3 minutos", "card_top_up_trx.send": "De", "card_top_up_trx.to": "Para", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "Data de validade", "cards.card_number": "Número do cartão", "cards.choose-wallet.no-active-accounts": "Você não tem carteiras ativas", "cards.copied_card_number": "Número do cartão copiado", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Limite di<PERSON><PERSON> excedido", "cards.transactions.decline_reason.incorrect_pin": "PIN incorreto", "cards.transactions.decline_reason.incorrect_security_code": "Código de segurança incorreto", "cards.transactions.decline_reason.invalid_amount": "Valor <PERSON>", "cards.transactions.decline_reason.low_balance": "<PERSON><PERSON> insuficiente", "cards.transactions.decline_reason.other": "Recusada", "cards.transactions.decline_reason.pin_tries_exceeded": "Tentativas de PIN excedidas", "cards.transactions.status.refund": "Reembolso", "cards.transactions.status.reversal": "Estorno", "cashback-deposit.trx.title": "<PERSON><PERSON><PERSON><PERSON> no Cashback", "cashback-estimate.text": "Esta é uma estimativa e NÃO um pagamento garantido. <PERSON><PERSON> as regras de cashback conhecidas são aplicadas, mas o Gnosis Pay pode excluir transações a seu critério. Um gasto máximo de {amount} por semana se qualifica para Cashback, mesmo que a estimativa para esta transação indique um valor total maior.", "cashback-estimate.text.fallback": "Esta é uma estimativa e NÃO um pagamento garantido. <PERSON><PERSON> as regras de cashback conhecidas são aplicadas, mas o Gnosis Pay pode excluir transações a seu critério", "cashback-estimate.title": "Estimativa de cashback", "cashback-onbarding-tersm.subtitle": "Os dados das transações do seu cartão serão compartilhados com a Karpatkey, que distribui o Cashback. Ao aceitar, você concorda com os <terms>Termos e Cond. do Cashback da Gnosis DAO</terms>", "cashback-onbarding-tersm.title": "Termos de uso e Privacidade", "cashback-tx-activity.retry": "Tentar novamente", "cashback-unconfirmed-payments-info.subtitle": "Os pagamentos se qualificam para o Cashback quando são liquidados com o vendedor. Até lá, eles aparecem como pagamentos não confirmados. Pagamentos não liquidados não se qualificam para o cashback.", "cashback-unconfirmed-payments-info.title": "Pagamentos de cartão não confirmados", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "<PERSON><PERSON><PERSON><PERSON>", "cashback.activity.title": "Atividade recente", "cashback.activity.withdrawal": "<PERSON><PERSON>", "cashback.deposit": "Depositar", "cashback.deposit.amount.label": "Valor do depósito", "cashback.deposit.change": "{from} para {to}", "cashback.deposit.confirmation.subtitle": "As taxas de Cashback são atualizadas uma vez por semana. Deposite agora para aumentar o Cashback da próxima semana.", "cashback.deposit.confirmation.title": "<PERSON>oc<PERSON> a ganhar {percentage} em {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Troque tokens por {currency} na {network} Chain", "cashback.deposit.get.tokens.title": "Obtenha {currency} tokens", "cashback.deposit.header": "Depositar {currency}", "cashback.deposit.max_label": "Máx.: {amount}", "cashback.deposit.select-wallet.title": "Escolha a carteira de origem do depósito", "cashback.deposit.yourcashback": "<PERSON><PERSON>", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "Escolha a carteira para{br}sacar", "cashback.transaction-details.network-label": "Rede", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "De", "cashback.transaction-details.top-row.label-rewards": "<PERSON><PERSON><PERSON>back", "cashback.transaction-details.top-row.label-withdrawal": "Para", "cashback.transaction-details.transaction": "ID da transação", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Transações de Cashback", "cashback.withdraw": "<PERSON><PERSON>", "cashback.withdraw.confirmation.cashback_reduction": "O cashback desta semana, incluindo o que você j<PERSON> gan<PERSON>, será reduzido de {before} para {after}", "cashback.withdraw.queued": "Saque na fila", "cashback.withdrawal.change": "{from} para {to}", "cashback.withdrawal.confirmation.subtitle": "Iniciar saque de {amount} com um atraso de 3 minutos. <PERSON><PERSON> reduzirá seu cashback para {after}.", "cashback.withdrawal.confirmation.title": "O cashback diminuirá se você sacar GNO", "cashback.withdrawal.delayTransaction.title": "Iniciar saque de GNO com{br} um atraso de 3 minutos", "cashback.withdrawal.withdraw": "<PERSON><PERSON>", "cashback.withdrawal.yourcashback": "<PERSON><PERSON>", "celebration.aave": "Ganho com Aave", "celebration.cashback.subtitle": "Entregue em {code}", "celebration.cashback.subtitleGNO": "{amount} ganhos por último", "celebration.chf": "Rendimento com Frankencoin", "celebration.lido": "Ganho com Lido", "celebration.sky": "Ganho com Sky", "celebration.title": "Cashback total", "celebration.well_done.title": "<PERSON><PERSON> bem!", "change-withdrawal-account.add-new-account": "Adicionar outra conta bancária", "change-withdrawal-account.item.shortText": "{currency} Conta", "check-confirmation.approve.footer.for": "Para", "checkConfirmation.title": "Resultado da transação", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin e Ether", "collateral.ethereum": "Ethereum", "collateral.other": "Outros", "collateral.rwa": "Ativos do Mundo Real", "collateral.stablecoins": "Stablecoins (pareadas ao USD)", "collateral.us-t-bills": "Títulos do Tesouro dos EUA", "confirm-bank-transfer-recipient.bullet-1": "Sem taxas em EUR digital", "confirm-bank-transfer-recipient.bullet-2": "<PERSON><PERSON><PERSON><PERSON><PERSON> para {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Compartilhar dados da conta Gnosis Pay com Monerium, uma IME autorizada e regulamentada. <link>Sai<PERSON> mais</link>", "confirm-bank-transfer-recipient.bullet-4": "Aceitar os <link>termos de serviço</link>", "confirm-bank-transfer-recipient.title": "Aceitar termos", "confirm-change-withdrawal-account.cancel": "<PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.confirm": "Confirmar", "confirm-change-withdrawal-account.saving": "<PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.subtitle": "Todos os saques que você fizer da Zeal serão recebidos por esta conta bancária.", "confirm-change-withdrawal-account.title": "Alterar banco de recebimento", "confirm-ramove-withdrawal-account.title": "Remover conta bancária", "confirm-remove-withdrawal-account.subtitle": "Os dados desta conta bancária serão removidos da Zeal. Você pode adicioná-la novamente a qualquer momento.", "confirmTransaction.finalNetworkFee": "Taxa de rede", "confirmTransaction.importKeys": "Importar chaves", "confirmTransaction.networkFee": "Taxa de rede", "confirmation.title": "Enviar {amount} para {recipient}", "conflicting-monerium-account.add-owner": "Adicionar como Proprietário <PERSON>", "conflicting-monerium-account.create-wallet": "Criar uma nova smart wallet", "conflicting-monerium-account.disconnect-card": "Desconecte o cartão do Zeal e reconecte com o novo proprietário", "conflicting-monerium-account.header": "{wallet} vinculado a outra conta Monerium", "conflicting-monerium-account.subtitle": "Altere sua carteira de proprietário Gnosis Pay", "connection.diconnected.got_it": "Entendi!", "connection.diconnected.page1.subtitle": "O Zeal funciona onde a MetaMask funciona. Conecte-se como você faria com a MetaMask.", "connection.diconnected.page1.title": "Como se conectar com o Zeal?", "connection.diconnected.page2.subtitle": "Você verá muitas opções. O Zeal pode ser uma delas. Se o Zeal não aparecer...", "connection.diconnected.page2.title": "Clique em Conectar Carteira", "connection.diconnected.page3.subtitle": "Solicitaremos uma conexão com o Zeal. Navegador ou Injetado também devem funcionar. Experimente!", "connection.diconnected.page3.title": "Escolha <PERSON>", "connectionSafetyCheck.tag.caution": "Atenção", "connectionSafetyCheck.tag.danger": "Perigo", "connectionSafetyCheck.tag.passed": "<PERSON><PERSON><PERSON>", "connectionSafetyConfirmation.subtitle": "Tem certeza de que quer continuar?", "connectionSafetyConfirmation.title": "Este site parece perigoso", "connection_state.connect.cancel": "<PERSON><PERSON><PERSON>", "connection_state.connect.changeToMetamask": "Mudar para MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Mudar para MetaMask", "connection_state.connect.connect_button": "Conectar", "connection_state.connect.expanded.connected": "Conectado", "connection_state.connect.expanded.title": "Conectar", "connection_state.connect.safetyChecksLoading": "Verificando a segurança do site", "connection_state.connect.safetyChecksLoadingError": "Não foi possível concluir as verificações de segurança", "connection_state.connected.expanded.disconnectButton": "Desconectar Zeal", "connection_state.connected.expanded.title": "Conectado", "copied-diagnostics": "Diagnóstico copiado", "copy-diagnostics": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.component.add_recipient_primary_text": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.country": "<PERSON><PERSON>", "counterparty.countryTitle": "País do destinatário", "counterparty.currency": "<PERSON><PERSON>", "counterparty.delete.success.title": "Removido", "counterparty.edit.success.title": "Alterações salvas", "counterparty.errors.country_required": "País é obrigatório", "counterparty.errors.first_name.invalid": "O nome deve ser mais longo", "counterparty.errors.last_name.invalid": "O sobrenome deve ser mais longo", "counterparty.first_name": "Nome", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Enviar para o banco", "countrySelector.noCountryFound": "Nenhum país encontrado", "countrySelector.title": "Selecionar país", "create-passkey.cta": "<PERSON><PERSON><PERSON> passkey", "create-passkey.extension.cta": "<PERSON><PERSON><PERSON><PERSON>", "create-passkey.footnote": "Tecnologia de", "create-passkey.mobile.cta": "Configurar <PERSON>gu<PERSON>", "create-passkey.steps.enable-recovery": "Configurar recuperação na nuvem", "create-passkey.steps.setup-biometrics": "Ativar segurança biométrica", "create-passkey.subtitle": "Passkeys: mais seguras e salvas na nuvem.", "create-passkey.title": "Proteger conta", "create-smart-wallet": "Criar Smart Wallet", "create-userop.progress.text": "<PERSON><PERSON><PERSON>", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Continuar na Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Ir para Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "Finalize seu pedido na Gnosis Pay.", "create_recharge_preferences.card": "Cartão", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "Rende {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "<PERSON><PERSON> saldo", "create_recharge_preferences.link_accounts_title": "Vincular contas", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "Seu cartão NÃO será recarregado automaticamente após cada pagamento.", "create_recharge_preferences.not_configured_title": "Renda e Gaste", "create_recharge_preferences.recharge_from_earn_accounts_description": "Seu cartão é recarregado automaticamente da sua conta Renda após cada pagamento.", "create_recharge_preferences.subtitle": "por ano", "creating-account.loading": "<PERSON><PERSON><PERSON> conta", "creating-gnosis-pay-account": "<PERSON><PERSON><PERSON> conta", "currencies.bridge.select_routes.emptyState": "Nenhuma rota encontrada para este bridge", "currency.add_currency.add_token": "Adicionar token", "currency.add_currency.not_a_valid_address": "Endereço de token inválido", "currency.add_currency.token_decimals_feild": "Decimais do token", "currency.add_currency.token_feild": "Endereço do token", "currency.add_currency.token_symbol_feild": "Símbolo do token", "currency.add_currency.update_token": "Atualizar token", "currency.add_custom.remove_token.cta": "Remover", "currency.add_custom.remove_token.header": "Remover token", "currency.add_custom.remove_token.subtitle": "O saldo do token será oculto do portfólio", "currency.add_custom.token_removed": "Token removido", "currency.add_custom.token_updated": "Token atualizado", "currency.balance_label": "Saldo: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "Sua transferência bancária enviou com sucesso {fiat} para {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "Voc<PERSON> recebeu {crypto}", "currency.bankTransfer.deposit_status.success": "Recebido na sua carteira", "currency.bankTransfer.deposit_status.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.check_bank_account": "Verifique sua conta bancária", "currency.bankTransfer.off_ramp.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Enviando para seu banco", "currency.bankTransfer.off_ramp.transferring_to_currency": "Transferindo para {to<PERSON>urrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "O dinheiro já deve ter chegado à sua conta bancária.", "currency.bankTransfer.withdrawal_status.success": "Enviado para seu banco", "currency.bankTransfer.withdrawal_status.title": "<PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.email": "E-mail", "currency.bank_transfer.create_unblock_user.email_invalid": "E-mail inválido", "currency.bank_transfer.create_unblock_user.email_missing": "Obrigatório", "currency.bank_transfer.create_unblock_user.first_name": "Nome", "currency.bank_transfer.create_unblock_user.first_name_missing": "Obrigatório", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "Apenas letras, números, espaços e -.,&()'", "currency.bank_transfer.create_unblock_user.last_name": "Sobrenome", "currency.bank_transfer.create_unblock_user.last_name_missing": "Obrigatório", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "Apenas letras, números, espaços e -.,&()'", "currency.bank_transfer.create_unblock_user.note": "<PERSON><PERSON> continua<PERSON>, você aceita o<PERSON> da Unblock (nosso parceiro bancário) <terms>Termos</terms> e a <policy>Política de Privacidade</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Escreva seu nome como na conta do banco.", "currency.bank_transfer.create_unblock_user.title": "Vincule sua conta bancária", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Número da conta", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "País do banco", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Moeda preferencial", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Código do banco (sort code)", "currency.bank_transfer.create_unblock_withdraw_account.success": "Conta configurada", "currency.bank_transfer.create_unblock_withdraw_account.title": "Vincule sua conta bancária", "currency.bank_transfer.residence-form.address-required": "Obrigatório", "currency.bank_transfer.residence-form.address-unsupported-char": "Apenas letras, números, espaços e , ; {apostrophe} - \\\\ são permitidos.", "currency.bank_transfer.residence-form.city-required": "Obrigatório", "currency.bank_transfer.residence-form.city-unsupported-char": "Apenas letras, números, espaços e . , - & ( ) {apostrophe} são permitidos.", "currency.bank_transfer.residence-form.postcode-invalid": "Código postal inválido", "currency.bank_transfer.residence-form.postcode-required": "Obrigatório", "currency.bank_transfer.validation.invalid.account_number": "Número de conta inválido", "currency.bank_transfer.validation.invalid.iban": "IBAN inválido", "currency.bank_transfer.validation.invalid.sort_code": "Código do banco (sort code) inválido", "currency.bridge.amount_label": "Valor para a bridge", "currency.bridge.best_returns.subtitle": "Provedor com maior retorno, incluindo taxas.", "currency.bridge.best_returns_popup.title": "<PERSON><PERSON> retorno", "currency.bridge.bridge_from": "De", "currency.bridge.bridge_gas_fee_loading_failed": "Erro ao carregar a taxa de rede", "currency.bridge.bridge_low_slippage": "Slippage muito baixo. Tente aumentar.", "currency.bridge.bridge_provider": "<PERSON><PERSON><PERSON> transfer<PERSON>", "currency.bridge.bridge_provider_loading_failed": "Tivemos problemas para carregar os provedores", "currency.bridge.bridge_settings": "Configurações da bridge", "currency.bridge.bridge_status.subtitle": "<PERSON><PERSON><PERSON> {name}", "currency.bridge.bridge_status.title": "Bridge", "currency.bridge.bridge_to": "Para", "currency.bridge.fastest_route_popup.subtitle": "<PERSON><PERSON> provedor oferece a rota mais rápida.", "currency.bridge.fastest_route_popup.title": "Rota mais rápida", "currency.bridge.from": "De", "currency.bridge.success": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bridge.title": "Bridge", "currency.bridge.to": "Para", "currency.bridge.topup": "<PERSON><PERSON><PERSON><PERSON> {symbol}", "currency.bridge.withdrawal_status.title": "<PERSON><PERSON>", "currency.card.card_top_up_status.title": "Adicionar <PERSON> ao cartão", "currency.destination_amount": "Valor de destino", "currency.hide_currency.confirm.subtitle": "Oculte o token. Reexiba quando quiser.", "currency.hide_currency.confirm.title": "Ocultar token", "currency.hide_currency.success.title": "Token oculto", "currency.label": "Nome (Opcional)", "currency.last_name": "Sobrenome", "currency.max_loading": "Máx.:", "currency.swap.amount_to_swap": "Valor do swap", "currency.swap.best_return": "Rota com melhor retorno", "currency.swap.destination_amount": "Valor de destino", "currency.swap.header": "<PERSON><PERSON><PERSON>", "currency.swap.max_label": "Saldo: {amount}", "currency.swap.provider.header": "<PERSON><PERSON><PERSON> de <PERSON>", "currency.swap.select_to_token": "Selecionar token", "currency.swap.swap_gas_fee_loading_failed": "Erro ao carregar a taxa de rede", "currency.swap.swap_provider_loading_failed": "Erro ao carregar os provedores", "currency.swap.swap_settings": "Ajustes de swap", "currency.swap.swap_slippage_too_low": "Slippage muito baixo. Tente aumentar.", "currency.swaps_io_native_token_swap.subtitle": "Usando Swaps.IO", "currency.swaps_io_native_token_swap.title": "Enviar", "currency.withdrawal.amount_from": "De", "currency.withdrawal.amount_to": "Para", "currencySelector.title": "<PERSON><PERSON><PERSON><PERSON> moeda", "dApp.wallet-does-not-support-chain.subtitle": "Parece que sua carteira não é compatível com {network}. Tente conectar com outra carteira ou use o Zeal.", "dApp.wallet-does-not-support-chain.title": "Rede não compatível", "dapp.connection.manage.confirm.disconnect.all.cta": "Desconectar", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Tem certeza de que deseja desconectar todas as conex<PERSON>es?", "dapp.connection.manage.confirm.disconnect.all.title": "Desconectar tudo", "dapp.connection.manage.connection_list.main.button.title": "Desconectar", "dapp.connection.manage.connection_list.no_connections": "Você não tem apps conectados", "dapp.connection.manage.connection_list.section.button.title": "Desconectar tudo", "dapp.connection.manage.connection_list.section.title": "Ativas", "dapp.connection.manage.connection_list.title": "Conexões", "dapp.connection.manage.disconnect.success.title": "Apps desconectados", "dapp.metamask_mode.title": "<PERSON><PERSON>", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "Rendimento", "dc25-card-marketing-card.right.subtitle": "100 pessoas", "dc25-card-marketing-card.title": "Os 100 primeiros que gastarem €50 ganharão {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Você não poderá realizar esta ação por 3 min. <PERSON>r seguran<PERSON>, alterações nas configurações do cartão ou saques levam 3 minutos para serem processados.", "delayQueueBusyBanner.processing-yout-action.title": "Processando sua ação, aguarde", "delayQueueBusyWidget.cardFrozen": "Cartão congelado", "delayQueueBusyWidget.processingAction": "Processando sua ação", "delayQueueFailedBanner.action-incomplete.get-support": "Fale com o suporte", "delayQueueFailedBanner.action-incomplete.subtitle": "Algo deu errado com seu saque ou atualização de configurações. Fale com o suporte no Discord.", "delayQueueFailedBanner.action-incomplete.title": "Ação incompleta", "delayQueueFailedWidget.actionIncomplete.title": "Ação no cartão incompleta", "delayQueueFailedWidget.cardFrozen.subtitle": "Cartão congelado", "delayQueueFailedWidget.contactSupport": "Con<PERSON>r suporte", "delay_queue_busy.subtitle": "Por seguran<PERSON>, alterações nas configurações do cartão ou saques levam 3 minutos para serem processados. Durante esse tempo, seu cartão fica congelado.", "delay_queue_busy.title": "Sua ação está sendo processada", "delay_queue_failed.contact_support": "Suporte", "delay_queue_failed.subtitle": "Algo deu errado com seu saque ou atualização de configurações. Fale com o suporte no Discord.", "delay_queue_failed.title": "Fale com o suporte", "deploy-earn-form-smart-wallet.in-progress.title": "Preparando o Earn", "deposit": "Depositar", "disconnect-card-popup.cancel": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.disconnect": "Desconectar", "disconnect-card-popup.subtitle": "Isso removerá seu Cartão do app Zeal. Sua carteira continuará conectada ao seu cartão no app Gnosis Pay. Você pode reconectar seu Cartão a qualquer momento.", "disconnect-card-popup.title": "Desconectar cartão", "distance.long.days": "{count} dias", "distance.long.hours": "{count} horas", "distance.long.minutes": "{count} minutos", "distance.long.months": "{count} meses", "distance.long.seconds": "{count} segundos", "distance.long.years": "{count} anos", "distance.short.days": "{count} d", "distance.short.hours": "{count} h", "distance.short.minutes": "{count} min", "distance.short.months": "{count} m", "distance.short.seconds": "{count} seg", "distance.short.years": "{count} a", "duration.short.days": "{count}d", "duration.short.hours": "{count}h", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "Depositar", "earn-deposit-view.into": "Em", "earn-deposit-view.to": "Para", "earn-deposit.swap.transfer-provider": "<PERSON><PERSON><PERSON> transfer<PERSON>", "earn-taker-investment-details.accrued-realtime": "Acumulado em tempo real", "earn-taker-investment-details.asset-class": "Classe de ativo", "earn-taker-investment-details.asset-coverage-ratio": "Índice de cobertura de ativos", "earn-taker-investment-details.asset-reserve": "Reserva de ativos", "earn-taker-investment-details.base_currency.label": "Moeda base", "earn-taker-investment-details.chf.description": "Ganhe juros em seus CHF depositando zCHF na Frankencoin, um mercado monetário digital confiável. Os juros são gerados por empréstimos de baixo risco e com excesso de garantia na Frankencoin, pagos em tempo real. Seus fundos ficam seguros em uma subconta que só você controla.", "earn-taker-investment-details.chf.description.with_address_link": "Ganhe juros em seus CHF depositando zCHF na Frankencoin, um mercado monetário digital confiável. Os juros são gerados por empréstimos de baixo risco e com excesso de garantia na Frankencoin, pagos em tempo real. Seus fundos ficam seguros em uma subconta <link>(copiar 0x)</link> que só você controla.", "earn-taker-investment-details.chf.label": "Franco Suíço Digital", "earn-taker-investment-details.collateral-composition": "Composição da garantia", "earn-taker-investment-details.depositor-obligations": "Obrigações do depositante", "earn-taker-investment-details.eure.description": "Renda juros sobre seus euros depositando EURe no Aave, um mercado monetário digital confiável. O EURe é uma stablecoin de euro totalmente regulamentada, emitida pela Monerium e com lastro de 1:1 em contas protegidas. Os juros são gerados a partir de empréstimos de baixo risco e com sobrecolateralização no Aave, e pagos em tempo real. Seus fundos permanecem em uma subconta segura que só você controla.", "earn-taker-investment-details.eure.description.with_address_link": "Renda juros sobre seus euros depositando EURe no Aave, um mercado monetário digital confiável. O EURe é uma stablecoin de euro totalmente regulamentada, emitida pela Monerium e com lastro de 1:1 em contas protegidas. Os juros são gerados a partir de empréstimos de baixo risco e com sobrecolateralização no Aave, e pagos em tempo real. Seus fundos permanecem em uma subconta segura <link>(copiar 0x)</link> que só você controla.", "earn-taker-investment-details.eure.label": "Euro Digital (EURe)", "earn-taker-investment-details.faq": "<PERSON><PERSON><PERSON><PERSON> frequentes", "earn-taker-investment-details.fixed-income": "Renda fixa", "earn-taker-investment-details.issuer": "Emissor", "earn-taker-investment-details.key-facts": "Informações principais", "earn-taker-investment-details.liquidity": "Liquidez", "earn-taker-investment-details.operator": "Operador de mercado", "earn-taker-investment-details.projected-yield": "Rendimento anual projetado", "earn-taker-investment-details.see-other-faq": "<PERSON><PERSON> <PERSON><PERSON> as d<PERSON><PERSON><PERSON> frequentes", "earn-taker-investment-details.see-realtime": "Ver dados em tempo real", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Rendimento dos últimos 12 meses", "earn-taker-investment-details.total-collateral": "Garantia Total", "earn-taker-investment-details.total-deposits": "$27.253.300.208", "earn-taker-investment-details.total-zchf-supply": "Fornecimento total de ZCHF", "earn-taker-investment-details.total_deposits": "Total de depósitos Aave", "earn-taker-investment-details.usd.description": "Sky é um mercado monetário digital que oferece rendimentos estáveis em dólar americano, gerados por Títulos do Tesouro dos EUA de curta duração e empréstimos com garantia extra — sem a volatilidade de cripto, com acesso 24/7 aos fundos e lastro transparente on-chain.", "earn-taker-investment-details.usd.description.with_address_link": "Sky é um mercado monetário digital que oferece rendimentos estáveis em dólar americano, gerados por Títulos do Tesouro dos EUA de curta duração e empréstimos com garantia extra — sem a volatilidade de cripto, com acesso 24/7 aos fundos e lastro transparente on-chain. Os investimentos são feitos em uma subconta <link>(copiar 0x)</link> controlada por você.", "earn-taker-investment-details.usd.ftx-difference": "Qual a diferença em relação a FTX, <PERSON><PERSON><PERSON>, <PERSON><PERSON>i ou Luna?", "earn-taker-investment-details.usd.high-returns": "Como os rendimentos podem ser tão altos, comparados aos bancos tradicionais?", "earn-taker-investment-details.usd.how-is-backed": "Qual o lastro do Sky USD e o que acontece com meu dinheiro se a Zeal falir?", "earn-taker-investment-details.usd.income-sources": "Fontes de renda 2024", "earn-taker-investment-details.usd.insurance": "Meus fundos são segurados ou garantidos por alguma entidade (como o FGC)?", "earn-taker-investment-details.usd.label": "Dólar Americano Digital", "earn-taker-investment-details.usd.lose-principal": "Eu posso realmente perder meu investimento inicial? Em que circunstâncias?", "earn-taker-investment-details.variable-rate": "Empréstimo com taxa variável", "earn-taker-investment-details.withdraw-anytime": "Saque a qualquer momento", "earn-taker-investment-details.yield": "Rendimento", "earn-withdrawal-view.approve.for": "Para", "earn-withdrawal-view.approve.into": "Em", "earn-withdrawal-view.swap.into": "Para", "earn-withdrawal-view.withdraw.to": "Para", "earn.add_another_asset.title": "Selecione o ativo de rendimento", "earn.add_asset": "Adicionar ativo", "earn.asset_view.title": "Rendimentos", "earn.base-currency-popup.text": "A moeda base é como seus depósitos, rendimentos e transações são avaliados e registrados. Se você depositar em uma moeda diferente (como EUR em USD), seus fundos são convertidos imediatamente para a moeda base usando as taxas de câmbio atuais. Após a conversão, seu saldo permanece estável na moeda base, mas saques futuros podem envolver novas conversões de moeda.", "earn.base-currency-popup.title": "Moeda base", "earn.card-recharge.disabled.list-item.title": "Recarga automática desativada", "earn.card-recharge.enabled.list-item.title": "Recarga automática ativada", "earn.choose_wallet_to_deposit.title": "Depositar de", "earn.config.currency.eth": "Renda com Ethereum", "earn.config.currency.on_chain_address_subtitle": "Endereço on-chain", "earn.config.currency.us_dollars": "Configurar transferências bancárias", "earn.configured_widget.current_apy.title": "APY atual", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} Anual", "earn.confirm.currency.cta": "Depositar", "earn.currency.eth": "Renda com Ethereum", "earn.deploy.status.title": "<PERSON><PERSON><PERSON> conta <PERSON>n", "earn.deploy.status.title_with_taker": "<PERSON><PERSON><PERSON> conta {title} de rendimentos", "earn.deposit": "Depositar", "earn.deposit.amount_to_deposit": "Valor a depositar", "earn.deposit.deposit": "Depositar", "earn.deposit.enter_amount": "Insira o valor", "earn.deposit.no_routes_found": "Nenhuma rota encontrada", "earn.deposit.not_enough_balance": "<PERSON><PERSON> insuficiente", "earn.deposit.select-currency.title": "Selecione o token para depositar", "earn.deposit.select_account.title": "Selecione a conta Earn", "earn.desposit_form.title": "Depositar no Earn", "earn.earn_deposit.status.title": "Depósito em conta de rendimentos", "earn.earn_deposit.trx.title": "Depositar em Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "Saque seu dinheiro a qualquer momento", "earn.earn_withdraw.status.title": "Saque da conta de rendimentos", "earn.earn_withdraw.trx.title.approval": "<PERSON><PERSON><PERSON>que", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Sacar como {asset}", "earn.earn_withdraw.trx.title.withdrawal": "<PERSON><PERSON>", "earn.recharge.cta": "<PERSON><PERSON>", "earn.recharge.earn_not_configured.enable_some_account.error": "Ative uma conta", "earn.recharge.earn_not_configured.enter_amount.error": "Insira um valor", "earn.recharge.select_taker.header": "Recarregar cartão na ordem de", "earn.recharge_card_tag.on": "ativada", "earn.recharge_card_tag.recharge": "Recarga", "earn.recharge_card_tag.recharge_not_configured": "Recarga automática", "earn.recharge_card_tag.recharge_off": "Recarga desativada", "earn.recharge_card_tag.recharged": "<PERSON><PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharging": "Recarregando", "earn.recharge_configured.disable.trx.title": "Desativar recarga automática", "earn.recharge_configured.trx.disclaimer": "Ao usar seu cartão, um leilão Cowswap é criado para comprar o mesmo valor do seu pagamento usando seus ativos do Earn. Esse processo geralmente oferece a melhor cotação do mercado, mas a cotação na blockchain pode ser diferente das taxas de câmbio do mundo real.", "earn.recharge_configured.trx.subtitle": "Após cada pagamento, dinheiro será adicionado automaticamente da sua(s) conta(s) Earn para manter o saldo do seu cartão em {value}", "earn.recharge_configured.trx.title": "Definir recarga automática para {value}", "earn.recharge_configured.updated.trx.title": "Salvar configurações de recarga", "earn.risk-banner.subtitle": "Este é um produto de carteira privada, sem proteção regulatória contra perdas.", "earn.risk-banner.title": "Entenda os riscos", "earn.set_recharge.status.title": "Definir recarga automática", "earn.setup_reacharge.input.disable.label": "Desativar", "earn.setup_reacharge.input.label": "Saldo alvo do cartão", "earn.setup_reacharge_form.title": "A Recarga Automática mantém seu {br}cartão com o mesmo saldo", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Mantenha wstETH (ETH em stake) na Gnosis Chain e empreste via Lido.", "earn.taker-bulletlist.point_1": "Renda {apyValue} ao ano. Os retornos variam com o mercado.", "earn.taker-bulletlist.point_3": "O Zeal não cobra taxas.", "earn.taker-historical-returns": "Rendimentos históricos", "earn.taker-historical-returns.chf": "Crescimento de CHF para USD", "earn.taker-investment-tile.apy.perYear": "por ano", "earn.takerAPY": "{takerApy} de APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "Depositar", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Rendimento do Aave na Gnosis Chain", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Rendimento com Frankencoin na Gnosis Chain", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Rendimento do Lido na Gnosis Chain", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Rendimento do Maker na Gnosis Chain", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "Renda com ETH", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "Renda com ETH", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "Títulos do Tesouro Americano (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Embora extremamente raro, é teoricamente possível. Seus fundos são protegidos por um gerenciamento de risco rigoroso e alta colateralização. O pior cenário realista envolveria condições de mercado sem precedentes, como várias stablecoins perdendo sua paridade simultaneamente — algo que nunca aconteceu antes.", "earn.usd.can-I-lose-my-principal-popup.title": "Eu posso realmente perder meu investimento inicial? Em que circunstâncias?", "earn.usd.ftx-difference-popup.text": "O Sky é fundamentalmente diferente. Difer<PERSON> de FTX, <PERSON><PERSON><PERSON>, BlockFi ou Luna — que dependiam muito de custódia centralizada, gestão de ativos opaca e posições alavancadas de risco — o Sky USD utiliza smart contracts descentralizados, auditados e transparentes, e mantém total transparência on-chain. Você mantém controle total da sua carteira privada, reduzindo significativamente os riscos de contraparte associados a falhas centralizadas.", "earn.usd.ftx-difference-popup.title": "Qual a diferença para FT<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ou <PERSON>?", "earn.usd.high-returns-popup.text": "O Sky USD gera rendimentos principalmente por meio de protocolos de finanças descentralizadas (DeFi), que automatizam empréstimos P2P e provisionamento de liquidez, eliminando custos e intermediários bancários tradicionais. Essa eficiência, combinada com controles de risco robustos, permite retornos significativamente mais altos em comparação com os bancos tradicionais.", "earn.usd.high-returns-popup.title": "Como os rendimentos podem ser tão altos, comparados aos bancos tradicionais?", "earn.usd.how-is-sky-backed-popup.text": "O Sky USD é totalmente lastreado e supercolateralizado por uma combinação de ativos digitais em smart contracts seguros e ativos do mundo real, como Títulos do Tesouro dos EUA. As reservas podem ser auditadas em tempo real on-chain, até mesmo de dentro do Zeal, oferecendo transparência e segurança. No caso improvável de a Zeal encerrar as atividades, seus ativos permanecem seguros on-chain, totalmente sob seu controle e acessíveis por outras carteiras compatíveis.", "earn.usd.how-is-sky-backed-popup.title": "Qual o lastro do Sky USD e o que acontece com meu dinheiro se a Zeal falir?", "earn.usd.insurance-popup.text": "Os fundos do Sky USD não têm seguro do FDIC nem garantias governamentais tradicionais, pois é uma conta baseada em ativos digitais, não uma conta bancária convencional. Em vez disso, o Sky gerencia toda a mitigação de riscos por meio de smart contracts auditados e protocolos DeFi cuidadosamente selecionados, garantindo que os ativos permaneçam seguros e transparentes.", "earn.usd.insurance-popup.title": "Meus fundos têm algum seguro ou garantia (como FDIC ou similar)?", "earn.usd.lending-operations-popup.text": "O Sky USD gera rendimento emprestando stablecoins em mercados de empréstimo descentralizados como Morpho e Spark. Suas stablecoins são emprestadas a tomadores que depositam uma garantia — como ETH ou BTC — significativamente maior que o valor do empréstimo. Essa abordagem, chamada de sobrecolateralização, garante que sempre haja garantia suficiente para cobrir os empréstimos, reduzindo muito o risco. Os juros cobrados e as taxas de liquidação ocasionais pagas pelos tomadores proporcionam retornos confiáveis, transparentes e seguros.", "earn.usd.lending-operations-popup.title": "Operações de empréstimo", "earn.usd.market-making-operations-popup.text": "O Sky USD obtém rendimento adicional ao participar de corretoras descentralizadas (AMMs) como a Curve ou a Uniswap. Ao fornecer liquidez — colocando suas stablecoins em pools que facilitam a negociação de cripto — o Sky USD captura as taxas geradas pelas transações. Esses pools de liquidez são selecionados cuidadosamente para minimizar a volatilidade, usando principalmente pares de stablecoin para stablecoin para reduzir significativamente riscos como a perda impermanente, mantendo seus ativos seguros e acessíveis.", "earn.usd.market-making-operations-popup.title": "Operações de Market Making", "earn.usd.treasury-operations-popup.text": "O Sky USD gera rendimento estável e consistente por meio de investimentos estratégicos de tesouraria. Parte dos seus depósitos de stablecoin é alocada em ativos do mundo real seguros e de baixo risco — principalmente títulos públicos de curto prazo e instrumentos de crédito de alta segurança. Essa abordagem, semelhante à dos bancos tradicionais, garante um rendimento previsível e confiável. Seus ativos permanecem seguros, líquidos e gerenciados com transparência.", "earn.usd.treasury-operations-popup.title": "Operações de Tesouraria", "earn.view_earn.card_rechard_off": "Desativado", "earn.view_earn.card_rechard_on": "<PERSON><PERSON>do", "earn.view_earn.card_recharge": "Recarga com cartão", "earn.view_earn.total_balance_label": "Rendendo {percentage} por ano", "earn.view_earn.total_earnings_label": "<PERSON><PERSON><PERSON> to<PERSON>", "earn.withdraw": "<PERSON><PERSON>", "earn.withdraw.amount_to_withdraw": "Valor para sacar", "earn.withdraw.enter_amount": "Insira o valor", "earn.withdraw.loading": "Carregando", "earn.withdraw.no_routes_found": "Nenhuma rota encontrada", "earn.withdraw.not_enough_balance": "<PERSON><PERSON> insuficiente", "earn.withdraw.select-currency.title": "Selecionar token", "earn.withdraw.select_to_token": "Selecionar token", "earn.withdraw.withdraw": "<PERSON><PERSON>", "earn.withdraw_form.title": "Sacar do Earn", "earnings-view.earnings": "Rendimentos totais", "edit-account-owners.add-owner.add-wallet": "<PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.add_wallet": "<PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.title": "Adicionar Proprietário do Cartão", "edit-account-owners.card-owners": "Proprietários do cartão", "edit-account-owners.external-wallet": "Carteira externa", "editBankRecipient.title": "<PERSON><PERSON>", "editNetwork.addCustomRPC": "Adicionar nó RPC personalizado", "editNetwork.cannot_verify.subtitle": "O nó RPC personalizado não está respondendo. Verifique a URL e tente de novo.", "editNetwork.cannot_verify.title": "Não foi possível verificar o nó RPC", "editNetwork.cannot_verify.try_again": "Retentar", "editNetwork.customRPCNode": "Nó RPC personalizado", "editNetwork.defaultRPC": "RPC padrão", "editNetwork.networkRPC": "RPC da rede", "editNetwork.rpc_url.cannot_be_empty": "Obrigatório", "editNetwork.rpc_url.not_a_valid_https_url": "Deve ser um URL HTTP(S) válido", "editNetwork.safetyWarning.subtitle": "A Zeal não pode garantir a privacidade, a confiabilidade e a segurança de RPCs personalizados. Tem certeza de que quer usar um nó RPC personalizado?", "editNetwork.safetyWarning.title": "RPCs personalizados podem ser inseguros", "editNetwork.zealRPCNode": "<PERSON>ó RPC da Zeal", "editNetworkRpc.headerTitle": "Nó RPC personalizado", "editNetworkRpc.rpcNodeUrl": "URL do nó RPC", "editing-locked.modal.description": "Diferente das transações de Aprovação, os Permits não permitem editar o Limite de Gastos ou o Prazo de Validade. Certifique-se de que confia no dApp antes de enviar um Permit.", "editing-locked.modal.title": "Edição bloqueada", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Ativando recarga", "enable-recharge-for-smart-wallet.recharge-enabled.title": "<PERSON><PERSON><PERSON> ativada", "enterCardnumber": "Digite o nº do cartão", "error.connectivity_error.subtitle": "Confira sua internet e tente de novo.", "error.connectivity_error.title": "Sem conexão com a internet", "error.decrypt_incorrect_password.title": "Senha incorreta", "error.encrypted_object_invalid_format.title": "Dados corrompidos", "error.failed_to_fetch_google_auth_token.title": "Não conseguimos obter acesso", "error.list.item.cta.action": "Tentar novamente", "error.trezor_action_cancelled.title": "Transação rejeitada", "error.trezor_device_used_elsewhere.title": "Dispositivo em uso em outra sessão", "error.trezor_method_cancelled.title": "Não foi possível sincronizar a Trezor", "error.trezor_permissions_not_granted.title": "Não foi possível sincronizar a Trezor", "error.trezor_pin_cancelled.title": "Não foi possível sincronizar a Trezor", "error.trezor_popup_closed.title": "Não foi possível sincronizar a Trezor", "error.unblock_account_number_and_sort_code_mismatch": "Dados bancários não conferem.", "error.unblock_can_not_change_details_after_kyc": "Não é possível alterar dados após o KYC", "error.unblock_hard_kyc_failure": "Status de KYC inesperado", "error.unblock_invalid_faster_payment_configuration.title": "Este banco não aceita Faster Payments", "error.unblock_invalid_iban": "IBAN inválido", "error.unblock_session_expired.title": "Sessão Unblock expirada", "error.unblock_user_with_address_already_exists.title": "Conta já configurada para o endereço", "error.unblock_user_with_such_email_already_exists.title": "Já existe um usuário com este e-mail", "error.unknown_error.error_message": "Mensagem de erro: ", "error.unknown_error.subtitle": "Se precisar de ajuda urgente, fale com o suporte e compartilhe os detalhes abaixo.", "error.unknown_error.title": "Erro de sistema", "eth-cost-warning-modal.subtitle": "Smart Wallets funcionam na Ethereum, mas as taxas são muito altas. É ALTAMENTE recomendável usar outras redes.", "eth-cost-warning-modal.title": "Evite a Ethereum - taxas de rede altas", "exchange.form.button.chain_unsupported": "Rede não suportada", "exchange.form.button.refreshing": "Atualizando", "exchange.form.error.asset_not_supported.button": "Selecione outro ativo", "exchange.form.error.asset_not_supported.description": "O bridge não suporta este ativo.", "exchange.form.error.asset_not_supported.title": "Ativo não suportado", "exchange.form.error.bridge_quote_timeout.button": "Selecione outro ativo", "exchange.form.error.bridge_quote_timeout.description": "Tente outro par de tokens", "exchange.form.error.bridge_quote_timeout.title": "Nenhuma troca encontrada", "exchange.form.error.different_receiver_not_supported.button": "Remover destinatário alternativo", "exchange.form.error.different_receiver_not_supported.description": "Este provedor não aceita envio para outro endereço.", "exchange.form.error.different_receiver_not_supported.title": "Os endereços de envio e recebimento devem ser os mesmos.", "exchange.form.error.insufficient_input_amount.button": "Aumentar valor", "exchange.form.error.insufficient_liquidity.button": "<PERSON><PERSON>ir valor", "exchange.form.error.insufficient_liquidity.description": "Bridge sem fundos. Tente um valor menor.", "exchange.form.error.insufficient_liquidity.title": "Valor muito alto", "exchange.form.error.max_amount_exceeded.button": "<PERSON><PERSON>ir valor", "exchange.form.error.max_amount_exceeded.description": "O valor máximo foi excedido.", "exchange.form.error.max_amount_exceeded.title": "Valor muito alto", "exchange.form.error.min_amount_not_met.button": "Aumentar valor", "exchange.form.error.min_amount_not_met.description": "Valor de troca abaixo do mínimo.", "exchange.form.error.min_amount_not_met.description_with_amount": "O valor mínimo de troca <PERSON> {amount}.", "exchange.form.error.min_amount_not_met.title": "Valor muito baixo", "exchange.form.error.min_amount_not_met.title_increase": "Aumente o valor", "exchange.form.error.no_routes_found.button": "Selecione outro ativo", "exchange.form.error.no_routes_found.description": "Sem rota de troca para este token/rede.", "exchange.form.error.no_routes_found.title": "Nenhuma troca disponível", "exchange.form.error.not_enough_balance.button": "<PERSON><PERSON>ir valor", "exchange.form.error.not_enough_balance.description": "Saldo insuficiente deste ativo.", "exchange.form.error.not_enough_balance.title": "<PERSON><PERSON> insuficiente", "exchange.form.error.slippage_passed_is_too_low.button": "Aumentar slippage", "exchange.form.error.slippage_passed_is_too_low.description": "O slippage permitido é muito baixo.", "exchange.form.error.slippage_passed_is_too_low.title": "Slippage muito baixo", "exchange.form.error.socket_internal_error.button": "Tente novamente mais tarde", "exchange.form.error.socket_internal_error.description": "O parceiro de bridge está com problemas. Tente mais tarde.", "exchange.form.error.socket_internal_error.title": "Erro no parceiro de bridge", "exchange.form.error.stargatev2_requires_fee_in_native": "Adicionar {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Adicione {amount} para concluir a transação", "exchange.form.error.stargatev2_requires_fee_in_native.title": "Precisa de mais {symbol}", "expiration-info.modal.description": "O prazo de validade é por quanto tempo um app pode usar seus tokens. Quando o prazo acaba, ele perde o acesso até que você autorize novamente. Para sua segurança, mantenha o prazo de validade curto.", "expiration-info.modal.title": "O que é prazo de validade?", "expiration-time.high.modal.text": "Prazos de validade devem ser curtos e baseados em quanto tempo você realmente precisa. Prazos longos são arriscados e dão aos golpistas mais chances de usar seus tokens indevidamente.", "expiration-time.high.modal.title": "Prazo de validade longo", "failed.transaction.content": "Transação provavelmente falhará", "fee.unknown": "Desconhecido", "feedback-request.leave-message": "Deixar mensagem", "feedback-request.not-now": "<PERSON><PERSON><PERSON> n<PERSON>", "feedback-request.title": "Obrigado! Como podemos melhorar o Zeal?", "float.input.period": "Separador decimal", "gnosis-activate-card.info-popup.subtitle": "Na primeira transação, insira o cartão e digite sua senha. De<PERSON><PERSON> disso, o pagamento por aproximação funcionará.", "gnosis-activate-card.info-popup.title": "Primeiro pagamento exige chip e senha", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "Digite o número do cartão para ativá-lo.", "gnosis-activate-card.title": "Número do cartão", "gnosis-pay-re-kyc-widget.btn-text": "Verificar", "gnosis-pay-re-kyc-widget.title.not-started": "Verifique sua identidade", "gnosis-pay.login.cta": "Conectar conta existente", "gnosis-pay.login.title": "Você já tem uma conta Gnosis Pay", "gnosis-signup.confirm.subtitle": "Procure por um e-mail do Gnosis Pay, ele pode estar na sua caixa de spam.", "gnosis-signup.confirm.title": "Não recebeu o e-mail de verificação?", "gnosis-signup.continue": "<PERSON><PERSON><PERSON><PERSON>", "gnosis-signup.dont_link_accounts": "Não vincular contas", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "Digite <EMAIL>", "gnosis-signup.enter-email.title": "Informe seu e-mail", "gnosis-signup.title": "Li e concordo com <linkGnosisTNC>os Termos e Condições da Gnosis Pay</linkGnosisTNC> <monovateTerms>os Termos do Titular do Cartão</monovateTerms> e <linkMonerium>os Termos e Condições da Monerium</linkMonerium>.", "gnosis-signup.verify-email.title": "Verificar e-mail", "gnosis.confirm.subtitle": "Não recebeu? Confira seu número.", "gnosis.confirm.title": "Código enviado para {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Verificar", "gnosisPayAccountStatus.success.title": "Cartão importado", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay ainda não está disponível no seu país", "gnosisPayNoActiveCardsFound.title": "Nenhum cart<PERSON> ativo", "gnosis_pay_card_delay_relay_not_empty_error.title": "Sua transação falhou. Tente mais tarde.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "Cartão não disponível", "gnosiskyc.modal.approved.activate-free-card": "Ativar cartão grátis", "gnosiskyc.modal.approved.button-text": "Depositar do banco", "gnosiskyc.modal.approved.title": "Os dados da sua conta pessoal foram criados", "gnosiskyc.modal.failed.close": "<PERSON><PERSON><PERSON>", "gnosiskyc.modal.failed.title": "Nosso parceiro Gnosis Pay não pôde criar uma conta para você", "gnosiskyc.modal.in-progress.title": "A verificação de identidade pode levar 24h ou mais. Agradecemos a sua paciência.", "goToSettingsPopup.settings": "<PERSON><PERSON><PERSON><PERSON>", "goToSettingsPopup.title": "Ative notificações nos ajustes do aparelho.", "google_file.error.failed_to_fetch_auth_token.button_title": "Tentar novamente", "google_file.error.failed_to_fetch_auth_token.subtitle": "Para usarmos seu Arquivo de Recuperação, por favor, autorize o acesso na sua nuvem pessoal.", "google_file.error.failed_to_fetch_auth_token.title": "Não conseguimos obter acesso", "hidden_tokens.widget.emptyState": "Nenhum token oculto", "how_to_connect_to_metamask.got_it": "OK, entendi", "how_to_connect_to_metamask.story.subtitle": "Alterne entre a Zeal e outras carteiras.", "how_to_connect_to_metamask.story.title": "A Zeal funciona com outras carteiras", "how_to_connect_to_metamask.why_switch": "Por que alternar entre o Zeal e outras carteiras?", "how_to_connect_to_metamask.why_switch.description": "As Checagens da Zeal te protegem sempre.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "É fácil usar a Zeal com sua outra carteira.", "import-bank-transfer-owner.banner.title": "A carteira de transferências mudou. Importe-a para continuar as transferências neste dispositivo.", "import-bank-transfer-owner.title": "Importe a carteira para usar transferências neste dispositivo", "import_gnosispay_wallet.add-another-card-owner.footnote": "Importe a chave privada ou frase semente que é dona do seu cartão Gnosis Pay", "import_gnosispay_wallet.primaryText": "Importar carteira Gnosis Pay", "injected-wallet": "Carteira do navegador", "intercom.getHelp": "Obter ajuda", "invalid_iban.got_it": "<PERSON><PERSON><PERSON>", "invalid_iban.subtitle": "O IBAN inserido não é válido. Verifique se os dados foram inseridos corretamente e tente novamente.", "invalid_iban.title": "IBAN inválido", "keypad-0": "Tecla 0 do teclado", "keypad-1": "Tecla 1 do teclado", "keypad-2": "Tecla 2 do teclado", "keypad-3": "Tecla 3 do teclado", "keypad-4": "Tecla 4 do teclado", "keypad-5": "Tecla 5 do teclado", "keypad-6": "Tecla 6 do teclado", "keypad-7": "Tecla 7 do teclado", "keypad-8": "Tecla 8 do teclado", "keypad-9": "Tecla 9 do teclado", "keypad.biometric-button": "Botão biométrico do teclado", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Frase Secreta segura 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "Ver frase", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Guarde uma cópia offline da Frase Secreta.", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "Não tente adivinhar a palavra", "keystore.write_secret_phrase.before_you_begin.first_point": "Entendo que qualquer pessoa com minha Frase Secreta pode transferir meus ativos", "keystore.write_secret_phrase.before_you_begin.second_point": "Sou responsável por manter minha Frase Secreta segura e em sigilo", "keystore.write_secret_phrase.before_you_begin.subtitle": "Leia e aceite os pontos a seguir:", "keystore.write_secret_phrase.before_you_begin.third_point": "Estou em local privado, sem pessoas ou câmeras.", "keystore.write_secret_phrase.before_you_begin.title": "<PERSON><PERSON> começa<PERSON>", "keystore.write_secret_phrase.secret_phrase_test.title": "Qual é a palavra {count} da sua Frase Secreta?", "keystore.write_secret_phrase.test_ps.lets_do_it": "Vamos lá", "keystore.write_secret_phrase.test_ps.subtitle": "Você precisará da sua Frase Secreta para restaurar sua conta neste ou em outros aparelhos. Vamos testar se você a anotou corretamente.", "keystore.write_secret_phrase.test_ps.subtitle2": "Vamos pedir {count} palavras da sua frase.", "keystore.write_secret_phrase.test_ps.title": "Testar recuperação de conta", "kyc.modal.approved.button-text": "Fazer transferência", "kyc.modal.approved.subtitle": "Sua verificação foi concluída. Agora você pode fazer transferências bancárias ilimitadas.", "kyc.modal.approved.title": "Transferências bancárias liberadas", "kyc.modal.continue-with-partner.button-text": "<PERSON><PERSON><PERSON><PERSON>", "kyc.modal.continue-with-partner.subtitle": "Vamos redirecionar você ao nosso parceiro para coletar seus documentos e concluir a verificação.", "kyc.modal.continue-with-partner.title": "Continue com nosso parceiro", "kyc.modal.failed.unblock.subtitle": "A Unblock não aprovou sua verificação de identidade e não pode oferecer os serviços de transferência bancária.", "kyc.modal.failed.unblock.title": "Solicitação na Unblock não aprovada", "kyc.modal.paused.button-text": "<PERSON><PERSON><PERSON><PERSON> dad<PERSON>", "kyc.modal.paused.subtitle": "Parece que algumas das suas informações estão incorretas. Tente novamente e verifique seus dados antes de enviar.", "kyc.modal.paused.title": "Seus dados parecem incorretos", "kyc.modal.pending.button-text": "<PERSON><PERSON><PERSON>", "kyc.modal.pending.subtitle": "A verificação costuma levar menos de 10 minutos, mas às vezes pode demorar um pouco mais.", "kyc.modal.pending.title": "Manteremos você informado", "kyc.modal.required.cta": "Iniciar verificação", "kyc.modal.required.subtitle": "Você atingiu o limite de transações. Verifique sua identidade para continuar. Geralmente leva só alguns minutos e requer alguns dados e documentos.", "kyc.modal.required.title": "Verificação de identidade necessária", "kyc.submitted": "Cadastro enviado", "kyc.submitted_short": "Enviado", "kyc_status.completed_status": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc_status.failed_status": "Recusado", "kyc_status.paused_status": "<PERSON>", "kyc_status.subtitle": "Transferências bancárias", "kyc_status.subtitle.wrong_details": "Dados incorretos", "kyc_status.subtitle_in_progress": "Em andamento", "kyc_status.title": "Verificando identidade", "label.close": "<PERSON><PERSON><PERSON>", "label.saving": "Salvando...", "labels.this-month": "<PERSON><PERSON> mês", "labels.today": "Hoje", "labels.yesterday": "Ontem", "language.selector.title": "Idioma", "ledger.account_loaded.imported": "Importada", "ledger.add.success.title": "Ledger conectado com sucesso 🎉", "ledger.connect.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.connect.step1": "Conecte a Ledger ao seu dispositivo", "ledger.connect.step2": "Abra o app Ethereum na Ledger", "ledger.connect.step3": "<PERSON><PERSON><PERSON>, sincronize sua Led<PERSON> 👇", "ledger.connect.subtitle": "Siga estes passos para importar suas carteiras Ledger para o Zeal", "ledger.connect.title": "Conectar Ledger ao Zeal", "ledger.error.ledger_is_locked.subtitle": "Desbloqueie a Ledger e abra o app Ethereum", "ledger.error.ledger_is_locked.title": "Ledger blo<PERSON>", "ledger.error.ledger_not_connected.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.error.ledger_not_connected.subtitle": "Conecte sua Ledger e abra o app Ethereum.", "ledger.error.ledger_not_connected.title": "Ledger não está conectada", "ledger.error.ledger_running_non_eth_app.title": "App Ethereum não está aberto", "ledger.error.user_trx_denied_by_user.action": "<PERSON><PERSON><PERSON>", "ledger.error.user_trx_denied_by_user.subtitle": "Você rejeitou a transação na sua hardware wallet.", "ledger.error.user_trx_denied_by_user.title": "Transação rejeitada", "ledger.hd_path.bip44.subtitle": "Ex: Metamask, Trezor", "ledger.hd_path.bip44.title": "Padrão BIP44", "ledger.hd_path.ledger_live.subtitle": "Padrão", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "<PERSON><PERSON>", "ledger.hd_path.phantom.subtitle": "Ex: Phantom", "ledger.select.hd_path.subtitle": "Caminhos HD são como as carteiras de hardware organizam suas contas. É parecido com um índice que organiza as páginas de um livro.", "ledger.select.hd_path.title": "Selecionar caminho HD", "ledger.select_account.import_wallets_count": "{count,plural,=0{Nenhuma selecionada} one{Importar carteira} other{Importar {count} carteiras}}", "ledger.select_account.path_settings": "Configurações de caminho", "ledger.select_account.subtitle": "<PERSON><PERSON> vê as carteiras que esperava? <PERSON><PERSON> mudar as configurações de caminho", "ledger.select_account.subtitle.group_header": "Carteiras", "ledger.select_account.title": "Importar carteiras Ledger", "legend.lending-operations": "Operações de empréstimo", "legend.market_making-operations": "Operações de Market Making", "legend.treasury-operations": "Operações de tesouraria", "link-existing-monerium-account-sign.button": "Vincular Zeal", "link-existing-monerium-account-sign.subtitle": "Você já tem uma conta Monerium.", "link-existing-monerium-account-sign.title": "Vincule o Zeal à sua conta Monerium existente", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Você já tem uma conta Monerium. Acesse o app Monerium para concluir a configuração.", "link-existing-monerium-account.title": "Acesse o Monerium para vincular sua conta", "loading.pin": "Carregando PIN...", "lockScreen.passwordIncorrectMessage": "Senha incorreta", "lockScreen.passwordRequiredMessage": "<PERSON><PERSON> necess<PERSON>", "lockScreen.unlock.header": "Desb<PERSON>que<PERSON>", "lockScreen.unlock.subheader": "Use sua senha para desbloquear o Zeal", "mainTabs.activity.label": "Atividade", "mainTabs.browse.label": "Explorar", "mainTabs.browse.title": "Explorar", "mainTabs.card.label": "Cartão", "mainTabs.portfolio.label": "Portfólio", "mainTabs.rewards.label": "Recompensas", "makeSpendable.cta": "Disponibilizar saldo", "makeSpendable.holdAsCash": "Manter na carteira", "makeSpendable.shortText": "Rendendo {apy} ao ano", "makeSpendable.title": "{amount} recebidos", "merchantCategory.agriculture": "Agricultura", "merchantCategory.alcohol": "Bebidas alcoólicas", "merchantCategory.antiques": "Antiguidades", "merchantCategory.appliances": "Eletrodomésticos", "merchantCategory.artGalleries": "Galerias de Arte", "merchantCategory.autoRepair": "Reparo automotivo", "merchantCategory.autoRepairService": "Serviços Automotivos", "merchantCategory.beautyFitnessSpas": "Beleza, Fitness e Spas", "merchantCategory.beautyPersonalCare": "Beleza e Cuidados Pessoais", "merchantCategory.billiard": "<PERSON><PERSON>", "merchantCategory.books": "<PERSON><PERSON>", "merchantCategory.bowling": "<PERSON><PERSON>", "merchantCategory.businessProfessionalServices": "Serviços Empresariais e Profissionais", "merchantCategory.carRental": "<PERSON><PERSON><PERSON>", "merchantCategory.carWash": "Lava-rápido", "merchantCategory.cars": "<PERSON><PERSON>", "merchantCategory.casino": "Cassino", "merchantCategory.casinoGambling": "Cassino e Jogos de Azar", "merchantCategory.cellular": "<PERSON><PERSON><PERSON>", "merchantCategory.charity": "Caridade", "merchantCategory.childcare": "Cuidados <PERSON>", "merchantCategory.cigarette": "Cigarros", "merchantCategory.cinema": "Cinema", "merchantCategory.cinemaEvents": "Cinema e Eventos", "merchantCategory.cleaning": "Limpeza", "merchantCategory.cleaningMaintenance": "Limpeza e Manutenção", "merchantCategory.clothes": "<PERSON><PERSON><PERSON>", "merchantCategory.clothingServices": "Serviços de Vestuário", "merchantCategory.communicationServices": "Serviços de Comunicação", "merchantCategory.construction": "Construção", "merchantCategory.cosmetics": "Cosmé<PERSON><PERSON>", "merchantCategory.craftsArtSupplies": "Artesanato e Materiais de Arte", "merchantCategory.datingServices": "Serviços de Relacionamento", "merchantCategory.delivery": "Entrega", "merchantCategory.dentist": "<PERSON><PERSON>", "merchantCategory.departmentStores": "Lojas de Departamento", "merchantCategory.directMarketingSubscription": "Marketing Direto e Assinaturas", "merchantCategory.discountStores": "Lojas de Desconto", "merchantCategory.drugs": "Farm<PERSON><PERSON><PERSON>", "merchantCategory.dutyFree": "Duty Free", "merchantCategory.education": "Educação", "merchantCategory.electricity": "Eletricidade", "merchantCategory.electronics": "Eletrônicos", "merchantCategory.emergencyServices": "Serviços de Emergência", "merchantCategory.equipmentRental": "Aluguel de Equipamentos", "merchantCategory.evCharging": "Recarga de Veículo Elétrico", "merchantCategory.financialInstitutions": "Instituições financeiras", "merchantCategory.financialProfessionalServices": "Serviços Financeiros e Profissionais", "merchantCategory.finesPenalties": "Multas e Penalidades", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "<PERSON><PERSON>", "merchantCategory.flowers": "Flores", "merchantCategory.flowersGarden": "Flores e Jardinagem", "merchantCategory.food": "Alimentação", "merchantCategory.freight": "Frete", "merchantCategory.fuel": "Combustível", "merchantCategory.funeralServices": "Serviços Funerários", "merchantCategory.furniture": "Móveis", "merchantCategory.games": "Jogos", "merchantCategory.gas": "Combustível", "merchantCategory.generalMerchandiseRetail": "Mercadorias e Varejo em Geral", "merchantCategory.gifts": "Presentes", "merchantCategory.government": "Governo", "merchantCategory.governmentServices": "Serviços Governamentais", "merchantCategory.hardware": "<PERSON><PERSON><PERSON>", "merchantCategory.healthMedicine": "Saúde e Medicina", "merchantCategory.homeImprovement": "Reforma Residencial", "merchantCategory.homeServices": "Serviços Domésticos", "merchantCategory.hotel": "Hotel", "merchantCategory.housing": "Moradia", "merchantCategory.insurance": "<PERSON><PERSON><PERSON>", "merchantCategory.internet": "Internet", "merchantCategory.kids": "Infantil", "merchantCategory.laundry": "Lavanderia", "merchantCategory.laundryCleaningServices": "Lavanderia e Limpeza", "merchantCategory.legalGovernmentFees": "Taxas Legais e Governamentais", "merchantCategory.luxuries": "Artigos de Luxo", "merchantCategory.luxuriesCollectibles": "Luxo e Colecionáveis", "merchantCategory.magazines": "Revistas", "merchantCategory.magazinesNews": "Revistas e Notícias", "merchantCategory.marketplaces": "Marketplaces", "merchantCategory.media": "Mí<PERSON>", "merchantCategory.medicine": "<PERSON><PERSON><PERSON>", "merchantCategory.mobileHomes": "Casas Móveis", "merchantCategory.moneyTransferCrypto": "Transferência e Cripto", "merchantCategory.musicRelated": "Relacionado a Música", "merchantCategory.musicalInstruments": "Instrumentos musicais", "merchantCategory.optics": "Ótica", "merchantCategory.organizationsClubs": "Organizações e Clubes", "merchantCategory.other": "Outros", "merchantCategory.parking": "Estacionamento", "merchantCategory.pawnShops": "Lojas de Penhores", "merchantCategory.pets": "Pets", "merchantCategory.photoServicesSupplies": "Serviços e Suprimentos de Fotografia", "merchantCategory.postalServices": "Serviços Postais", "merchantCategory.professionalServicesOther": "Serviços Profissionais (Outros)", "merchantCategory.publicTransport": "Transporte Público", "merchantCategory.purchases": "Compras", "merchantCategory.purchasesMiscServices": "Compras e Serviços Diversos", "merchantCategory.recreationServices": "Serviços de Lazer", "merchantCategory.religiousGoods": "<PERSON><PERSON><PERSON>", "merchantCategory.secondhandRetail": "Varejo de Segunda Mão", "merchantCategory.shoeHatRepair": "Conserto de Sapatos e Chapéus", "merchantCategory.shoeRepair": "Sapataria", "merchantCategory.softwareApps": "Software e Apps", "merchantCategory.specializedRepairs": "Reparos <PERSON>", "merchantCategory.sport": "Esporte", "merchantCategory.sportingGoods": "Artigos Esportivos", "merchantCategory.sportingGoodsRecreation": "Artigos Esportivos e Lazer", "merchantCategory.sportsClubsFields": "Clubes e Campos Esportivos", "merchantCategory.stationaryPrinting": "Papelaria e Gráfica", "merchantCategory.stationery": "Papelaria", "merchantCategory.storage": "Armazenamento", "merchantCategory.taxes": "Impostos", "merchantCategory.taxi": "Táxi", "merchantCategory.telecomEquipment": "Equipamentos de Telecomunicação", "merchantCategory.telephony": "Telefonia", "merchantCategory.tobacco": "Tabaco", "merchantCategory.tollRoad": "Pedágio", "merchantCategory.tourismAttractionsAmusement": "Turismo, Atrações e Lazer", "merchantCategory.towing": "<PERSON><PERSON><PERSON>", "merchantCategory.toys": "Brinquedos", "merchantCategory.toysHobbies": "Brinquedos e Hobbies", "merchantCategory.trafficFine": "Multa de trânsito", "merchantCategory.train": "Trem", "merchantCategory.travelAgency": "Agência de Viagens", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, Rádio e Streaming", "merchantCategory.utilities": "Serviços Públicos", "merchantCategory.waterTransport": "Transporte Aquático", "merchantCategory.wholesaleClubs": "Clubes de Atacado", "metaMask.subtitle": "Ative o Modo MetaMask para redirecionar todas as conexões da MetaMask para o Zeal. Clicar em MetaMask nos dApps conectará ao Zeal.", "metaMask.title": "Não consegue se conectar com o Zeal?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Abra seu app do banco", "monerium-bank-deposit.buttet-point.receive-crypto": "Receba EUR digital", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Envie {fiatCurrencyCode} para sua conta", "monerium-bank-deposit.deposit-account-country": "<PERSON><PERSON>", "monerium-bank-deposit.header": "{fullName} - conta pessoal", "monerium-bank-details.account-name": "Nome da conta", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT copiado", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT copiado", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN copiado", "monerium-bank-details.to-wallet": "Para a carteira", "monerium-bank-details.transfer-fee": "Taxa de transferência", "monerium-bank-transfer.enable-card.bullet-1": "Conclua a verificação de identidade", "monerium-bank-transfer.enable-card.bullet-2": "Receba os dados da sua conta pessoal", "monerium-bank-transfer.enable-card.bullet-3": "Deposite da sua conta bancária", "monerium-card-delay-relay.success.cta": "<PERSON><PERSON><PERSON>", "monerium-card-delay-relay.success.subtitle": "Por segurança, as alterações nas configurações do cartão levam 3 minutos para serem processadas.", "monerium-card-delay-relay.success.title": "Volte em 3 min para continuar a configuração do Monerium", "monerium-deposit.account-details-info-popup.bullet-point-1": "Qualquer {fiatCurrencyCode} que você enviar para esta conta será convertido automaticamente em {cryptoCurrencyCode} tokens em {cryptoCurrencyChain} Chain e enviados para a sua carteira", "monerium-deposit.account-details-info-popup.bullet-point-2": "ENVIE APENAS {fiatCurrencyCode} ({fiatCurrencySymbol}) para a sua conta", "monerium-deposit.account-details-info-popup.title": "Dados da sua conta", "monerium.check_order_status.sending": "Enviando", "monerium.not-eligible.cta": "Voltar", "monerium.not-eligible.subtitle": "A Monerium não pôde abrir uma conta para você. Por favor, selecione um provedor alternativo.", "monerium.not-eligible.title": "Tente um provedor diferente", "monerium.setup-card.cancel": "<PERSON><PERSON><PERSON>", "monerium.setup-card.continue": "<PERSON><PERSON><PERSON><PERSON>", "monerium.setup-card.create_account": "C<PERSON><PERSON> conta", "monerium.setup-card.login": "Entrar na Gnosis Pay", "monerium.setup-card.subtitle": "Crie ou entre na sua conta Gnosis Pay para habilitar depósitos bancários instantâneos.", "monerium.setup-card.subtitle_personal_account": "Abra sua conta pessoal com Gnosis Pay em minutos:", "monerium.setup-card.title": "Habilitar depósitos bancários", "moneriumDepositSuccess.goToWallet": "<PERSON>r para a carteira", "moneriumDepositSuccess.title": "{symbol} recebidos", "moneriumInfo.fees": "Você tem 0% de taxas", "moneriumInfo.registration": "O Monerium é autorizado e regulamentado como uma Instituição de Moeda Eletrônica (IME) sob a Lei de Moeda Eletrônica da Islândia nº 17/2013 <link>Saiba mais</link>", "moneriumInfo.selfCustody": "O dinheiro digital que você recebe é de sua custódia e ninguém mais terá controle sobre seu ativo.", "moneriumWithdrawRejected.supportText": "Não foi possível concluir sua transferência. Tente novamente e, se ainda não funcionar, <link>fale com o suporte.</link>", "moneriumWithdrawRejected.title": "Transferência revertida", "moneriumWithdrawRejected.tryAgain": "Tentar novamente", "moneriumWithdrawSuccess.supportText": "<PERSON><PERSON> levar até 24h para o seu{br}destinatário receber os fundos", "moneriumWithdrawSuccess.title": "Enviado", "monerium_enable_banner.text": "Ative transferências bancárias agora", "monerium_error_address_re_link_required.title": "A carteira precisa ser reconectada à Monerium", "monerium_error_duplicate_order.title": "Pedido duplicado", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "Franco Suíço", "mt-pelerin-fork.options.chf.short": "Instantâneo e grátis com Mt Pelerin", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Instantâneo e grátis com Monerium", "mt-pelerin-fork.title": "O que você quer depositar?", "mtPelerinProviderInfo.fees": "Você paga 0% de taxas", "mtPelerinProviderInfo.registration": "O Mt Pelerin Group Ltd é afiliado à SO-FIT, um órgão de autorregulação reconhecido pela Autoridade Suíça de Supervisão do Mercado Financeiro (FINMA) sob a Lei de Combate à Lavagem de Dinheiro. <link>Saiba mais</link>", "mtPelerinProviderInfo.selfCustody": "O dinheiro digital que você recebe fica na sua carteira privada e ninguém mais terá controle sobre seus ativos", "network-fee-widget.title": "Taxas", "network.edit.verifying_rpc": "Verificando RPC", "network.editRpc.predefined_network_info.subtitle": "Como uma VPN, nosso RPC protege seus dados.{br}{br}Nossos RPCs padr<PERSON> s<PERSON> confiáveis.", "network.editRpc.predefined_network_info.title": "RPC de privacidade da Zeal", "network.filter.update_rpc_success": "Nó RPC salvo", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "<PERSON><PERSON> as redes", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "Adicionar redes", "networks.filter.add_modal.chain_list.subtitle": "Adicione qualquer rede EVM", "networks.filter.add_modal.chain_list.title": "<PERSON>r para <PERSON>list", "networks.filter.add_modal.dapp_tip.subtitle": "Mude de rede num dApp para adicioná-la.", "networks.filter.add_modal.dapp_tip.title": "Ou adicione uma rede de qualquer dApp", "networks.filter.add_networks.subtitle": "Compatível com todas as redes EVM", "networks.filter.add_networks.title": "Adicionar redes", "networks.filter.add_test_networks.title": "<PERSON><PERSON><PERSON>r testnets", "networks.filter.tab.netwokrs": "Redes", "networks.filter.testnets.title": "Testnets", "nft.widget.emptystate": "Nenhum colecionável na carteira", "nft_collection.change_account_picture.subtitle": "Tem certeza de que quer atualizar sua foto de perfil?", "nft_collection.change_account_picture.title": "Atualizar foto de perfil para NFT", "nfts.allNfts.pricingPopup.description": "Os preços dos colecionáveis são baseados no último valor negociado.", "nfts.allNfts.pricingPopup.title": "Preços de colecionáveis", "no-passkeys-found.modal.cta": "<PERSON><PERSON><PERSON>", "no-passkeys-found.modal.subtitle": "Não conseguimos detectar nenhuma passkey da Zeal neste dispositivo. Verifique se você está conectado na conta da nuvem que usou para criar sua smart wallet.", "no-passkeys-found.modal.title": "<PERSON><PERSON><PERSON><PERSON> passkey encontrada", "notValidEmail.title": "Endereço de e-mail inválido", "notValidPhone.title": "Este número de telefone não é válido", "notification-settings.title": "Configurações de notificação", "notification-settings.toggles.active-wallets": "Carteiras ativas", "notification-settings.toggles.bank-transfers": "Transferências bancárias", "notification-settings.toggles.card-payments": "Pagamentos com cartão", "notification-settings.toggles.readonly-wallets": "Carteiras somente leitura", "ntft.groupHeader.text": "Colecionáveis", "on_ramp.crypto_completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "on_ramp.fiat_completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onboarding-widget.subtitle.card_created_from_order.left": "Cartão Visa", "onboarding-widget.subtitle.card_created_from_order.right": "Ativar cart<PERSON>", "onboarding-widget.subtitle.card_order_ready.left": "Cartão Visa físico", "onboarding-widget.subtitle.default": "Transferências bancárias e Cartão Visa", "onboarding-widget.title.card-order-in-progress": "Continuar pedido do cartão", "onboarding-widget.title.card_created_from_order": "Cartão enviado", "onboarding-widget.title.kyc_approved": "Transferências e Cartão prontos", "onboarding-widget.title.kyc_failed": "Não foi possível criar a conta", "onboarding-widget.title.kyc_not_started": "Contin<PERSON>r configura<PERSON>", "onboarding-widget.title.kyc_started_documents_requested": "Concluir verificação", "onboarding-widget.title.kyc_started_resubmission_requested": "Refazer verificação", "onboarding-widget.title.kyc_started_verification_in_progress": "Verificando identidade", "onboarding.loginOrCreateAccount.amountOfAssets": "Mais de US$ 10 bi em ativos", "onboarding.loginOrCreateAccount.cards.subtitle": "Disponível apenas em algumas regiões. Ao continuar, você aceita nossos <Terms>Termos</Terms> e <PrivacyPolicy>Política de Privacidade</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Cartão Visa com altos{br}rendimentos e sem taxas", "onboarding.loginOrCreateAccount.createAccount": "C<PERSON><PERSON> conta", "onboarding.loginOrCreateAccount.earn.subtitle": "Os rendimentos variam; capital em risco. Ao continuar, você aceita nossos <Terms>Termos</Terms> e <PrivacyPolicy>Política de Privacidade</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "Rendendo {percent} por ano{br}Com a confiança de {currencySymbol}mais de 5 bi", "onboarding.loginOrCreateAccount.earningPerYear": "Rendendo {percent}{br}por ano", "onboarding.loginOrCreateAccount.login": "Entrar", "onboarding.loginOrCreateAccount.trading.subtitle": "Capital em risco. Ao continuar, você aceita nossos <Terms>Termos</Terms> e <PrivacyPolicy>Política de Privacidade</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Invista em tudo,{br}de BTC a S&P", "onboarding.loginOrCreateAccount.trustedBy": "Mercados monetários digitais{br}Com a confiança de {assets}", "onboarding.wallet_stories.close": "<PERSON><PERSON><PERSON>", "onboarding.wallet_stories.previous": "Anterior", "order-earn-deposit-bridge.deposit": "<PERSON><PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.into": "Para", "otpIncorrectMessage": "Código de confirmação incorreto", "passkey-creation-not-possible.modal.close": "<PERSON><PERSON><PERSON>", "passkey-creation-not-possible.modal.subtitle": "Não foi possível criar uma passkey para sua carteira. Verifique se seu dispositivo é compatível com passkeys e tente novamente. <link>Fale com o suporte</link> se o problema persistir.", "passkey-creation-not-possible.modal.title": "Não foi possível criar a passkey", "passkey-not-supported-in-mobile-browser.modal.cta": "Baixar o Zeal", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Smart Wallets não são compatíveis com navegadores de celular.", "passkey-not-supported-in-mobile-browser.modal.title": "Baixe o app Zeal para continuar", "passkey-recovery.recovering.deploy-signer.loading-text": "Verificando passkey", "passkey-recovery.recovering.loading-text": "<PERSON><PERSON><PERSON><PERSON>", "passkey-recovery.recovering.signer-not-found.subtitle": "Não foi possível vincular sua passkey a uma carteira ativa. Se tiver saldo na carteira, entre em contato com a equipe Zeal para receber suporte.", "passkey-recovery.recovering.signer-not-found.title": "<PERSON><PERSON><PERSON><PERSON> carteira encontrada", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "Tentar com outra passkey", "passkey-recovery.select-passkey.banner.subtitle": "Confira se você está na conta certa no dispositivo. As passkeys são específicas da conta.", "passkey-recovery.select-passkey.banner.title": "Não está vendo a passkey da sua carteira?", "passkey-recovery.select-passkey.continue": "Selecionar passkey", "passkey-recovery.select-passkey.subtitle": "Selecione a passkey vinculada à sua carteira para recuperar o acesso.", "passkey-recovery.select-passkey.title": "Selecionar Passkey", "passkey-story_1.subtitle": "Com a Smart Wallet, você pode pagar as taxas de rede usando a maioria dos tokens, sem se preocupar.", "passkey-story_1.title": "<PERSON><PERSON> as taxas de rede com a maioria dos tokens", "passkey-story_2.subtitle": "Baseada nos contratos inteligentes líderes de mercado da Safe, que protegem mais de US$ 100 bilhões em mais de 20 milhões de carteiras.", "passkey-story_2.title": "Protegida pela Safe", "passkey-story_3.subtitle": "As Smart Wallets funcionam nas principais redes compatíveis com Ethereum. Verifique as redes suportadas antes de enviar ativos.", "passkey-story_3.title": "Compatível com as principais redes EVM", "password.add.header": "<PERSON><PERSON><PERSON><PERSON>", "password.add.includeLowerAndUppercase": "Letras maiúsculas e minúsculas", "password.add.includesNumberOrSpecialChar": "Um número ou símbolo", "password.add.info.subtitle": "Sua senha não é enviada ou salva por nós.", "password.add.info.t_and_c": "<PERSON><PERSON> continuar, você aceita nossos <Terms>Termos</Terms> e <PrivacyPolicy>Política de Privacidade</PrivacyPolicy>", "password.add.info.title": "Sua senha fica apenas neste aparelho.", "password.add.inputPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "password.add.shouldContainsMinCharsCheck": "10+ caracteres", "password.add.subheader": "Use sua senha para desbloquear a Zeal.", "password.add.success.title": "<PERSON><PERSON> criada 🔥", "password.confirm.header": "Confirmar <PERSON><PERSON><PERSON>", "password.confirm.passwordDidNotMatch": "As senhas devem ser iguais", "password.confirm.subheader": "Digite sua senha mais uma vez", "password.create_pin.subtitle": "Este código de acesso bloqueia o app Zeal.", "password.create_pin.title": "Crie seu código de acesso", "password.enter_pin.title": "Digite o código", "password.incorrectPin": "Código incorreto", "password.pin_is_not_same": "Os códigos não são iguais.", "password.placeholder.enter": "Digite a senha", "password.placeholder.reenter": "Digite a senha novamente", "password.re_enter_pin.subtitle": "Digite o mesmo código novamente", "password.re_enter_pin.title": "Confirme o código", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "Pendente", "pending-send.details.pending": "Pendente", "pending-send.details.processing": "Processando", "permit-info.modal.description": "Permissões são solicitações que, se assinadas, permitem que apps movam seus tokens em seu nome, por exemplo, para fazer uma troca.{br}As permissões são parecidas com as Aprovações, mas não custam taxas de rede para assinar.", "permit-info.modal.title": "O que são Permissões?", "permit.edit-expiration": "Editar {currency} prazo de validade", "permit.edit-limit": "Editar {currency} limite de gastos", "permit.edit-modal.expiresIn": "<PERSON><PERSON>ra em…", "permit.expiration-warning": "{currency} aviso de validade", "permit.expiration.info": "{currency} informações de validade", "permit.expiration.never": "Nunca", "permit.spend-limit.info": "{currency} informações do limite de gastos", "permit.spend-limit.warning": "{currency} aviso do limite de gastos", "phoneNumber.title": "número de telefone", "physicalCardOrderFlow.cardOrdered": "Cartão solicitado", "physicalCardOrderFlow.city": "Cidade", "physicalCardOrderFlow.orderCard": "<PERSON><PERSON><PERSON>", "physicalCardOrderFlow.postcode": "CEP", "physicalCardOrderFlow.shippingAddress.subtitle": "Onde seu cartão será enviado", "physicalCardOrderFlow.shippingAddress.title": "Endereço de entrega", "physicalCardOrderFlow.street": "<PERSON><PERSON>", "placeholderDapps.1inch.description": "Exchange usando as melhores rotas", "placeholderDapps.aave.description": "Empreste e pegue tokens emprestados", "placeholderDapps.bungee.description": "Bridge entre redes pelas melhores rotas", "placeholderDapps.compound.description": "Empreste e pegue tokens emprestados", "placeholderDapps.cowswap.description": "Exchange com as melhores taxas na Gnosis", "placeholderDapps.gnosis-pay.description": "Gerencie seu cartão Gnosis Pay", "placeholderDapps.jumper.description": "Bridge entre redes pelas melhores rotas", "placeholderDapps.lido.description": "Faça stake de ETH para ganhar mais ETH", "placeholderDapps.monerium.description": "Dinheiro eletrônico e transferências bancárias", "placeholderDapps.odos.description": "Exchange usando as melhores rotas", "placeholderDapps.stargate.description": "Bridge ou Stake para <14% APY", "placeholderDapps.uniswap.description": "<PERSON><PERSON> das exchanges mais populares", "pleaseAllowNotifications.cardPayments": "Pagamentos com cartão", "pleaseAllowNotifications.customiseInSettings": "Personalize nos ajustes", "pleaseAllowNotifications.enable": "Ativar", "pleaseAllowNotifications.forWalletActivity": "Para atividades da carteira", "pleaseAllowNotifications.title": "Receba notificações da carteira", "pleaseAllowNotifications.whenReceivingAssets": "Ao receber ativos", "portfolio.quick-actions.add_funds": "<PERSON><PERSON><PERSON><PERSON>", "portfolio.quick-actions.buy": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.deposit": "Depositar", "portfolio.quick-actions.send": "Enviar", "portfolio.view.lastRefreshed": "Atualizado {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Recarregue seu AVAX de testnet", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Ir para o Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "Recarregue seu BNB de testnet", "portfolio.view.topupTestNet.BscTestnet.secondary": "Ir para o Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Recarregue seu SepETH de testnet", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Ir para o Faucet da Sepolia", "portfolio.view.topupTestNet.FantomTestnet.primary": "Recarregue seu FTM de testnet", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Ir para o Faucet", "privateKeyConfirmation.banner.subtitle": "Sua chave privada dá acesso total aos seus fundos. Só golpistas a pedem.", "privateKeyConfirmation.banner.title": "Entendo os riscos", "privateKeyConfirmation.title": "NUNCA compartilhe sua chave privada com ninguém", "rating-request.not-now": "<PERSON><PERSON><PERSON> n<PERSON>", "rating-request.title": "Você recomendaria o Zeal?", "receive_funds.address-text": "Este é o seu endereço de carteira único. Você pode compartilhá-lo com outras pessoas com segurança.", "receive_funds.copy_address": "<PERSON><PERSON><PERSON>", "receive_funds.network-warning.eoa.subtitle": "<link>Ver redes padrão</link>. Ativos enviados em redes não-EVM serão perdidos.", "receive_funds.network-warning.eoa.title": "Compatível com todas as redes baseadas em Ethereum", "receive_funds.network-warning.scw.subtitle": "<link>Ver redes válidas</link>. Ativos enviados em outras redes serão perdidos.", "receive_funds.network-warning.scw.title": "Atenção: use apenas redes compatíveis", "receive_funds.scan_qr_code": "Escanear QR code", "receiving.in.days": "Recebimento em {days}d", "receiving.this.week": "Recebimento esta semana", "receiving.today": "Recebimento hoje", "reference.error.maximum_number_of_characters_exceeded": "Excesso de caracteres", "referral-code.placeholder": "<PERSON> o link de convite", "referral-code.subtitle": "Clique no link do seu amigo novamente ou cole o link abaixo. Queremos garantir que você receba suas recompensas.", "referral-code.title": "Um amigo te enviou {bReward}?", "rekyc.verification_deadline.subtitle": "Conclua a verificação em até {daysUntil} dias para continuar usando seu cartão.", "rekyc.verification_required.subtitle": "Conclua a verificação para continuar usando seu cartão.", "reminder.fund": "💸 Adicione fundos e comece a render 6%.", "reminder.onboarding": "🏁 Termine a configuração e renda 6%.", "remove-owner.confirmation.subtitle": "Por segurança, as alterações levam 3 minutos para serem processadas. Durante esse tempo, seu cartão será temporariamente bloqueado e pagamentos não serão possíveis.", "remove-owner.confirmation.title": "Seu cartão será bloqueado por 3 min para a atualização", "restore-smart-wallet.wallet-recovered": "Carteira recuperada", "rewardClaimCelebration.claimedTitle": "Recompensas j<PERSON> resgatadas", "rewardClaimCelebration.subtitle": "Por convidar amigos", "rewardClaimCelebration.title": "<PERSON><PERSON><PERSON> ganhou", "rewards-warning.subtitle": "A remoção desta conta pausará o acesso a quaisquer recompensas vinculadas. Você pode restaurar a conta a qualquer momento para resgatá-las.", "rewards-warning.title": "Você perderá o acesso às suas recompensas", "rewards.copiedInviteLink": "Link de convite copiado", "rewards.createAccount": "Copiar link de convite", "rewards.header.subtitle": "Enviaremos {aReward} para você e {bReward} para seu amigo, quando ele gastar {bSpendLimitReward}.", "rewards.header.title": "<PERSON><PERSON><PERSON> {amountA}{br}Dê {amountB}", "rewards.sendInvite": "Enviar convite", "rewards.sendInviteTip": "Escolha um amigo e nós daremos a ele {bAmount}", "route.fees": "Taxas {fees}", "routesNotFound.description": "A rota de troca para a combinação {from}-{to} não está disponível.", "routesNotFound.title": "Nenhuma rota de troca disponível", "rpc.OrderBuySignMessage.subtitle": "Usando Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Usando Swaps.IO", "rpc.addCustomNetwork.addNetwork": "<PERSON><PERSON><PERSON><PERSON>", "rpc.addCustomNetwork.chainId": "ID da chain", "rpc.addCustomNetwork.nativeToken": "Token nativo", "rpc.addCustomNetwork.networkName": "Nome da rede", "rpc.addCustomNetwork.operationDescription": "Permite que este site adicione uma rede à sua carteira. O Zeal não pode verificar a segurança de redes personalizadas, então entenda os riscos.", "rpc.addCustomNetwork.rpcUrl": "URL do RPC", "rpc.addCustomNetwork.subtitle": "<PERSON><PERSON><PERSON> {name}", "rpc.addCustomNetwork.title": "Adicionar rede", "rpc.send_token.network_not_supported.subtitle": "Estamos trabalhando para habilitar transações nesta rede. Agradecemos sua paciência 🙏", "rpc.send_token.network_not_supported.title": "Rede em breve", "rpc.send_token.send_or_receive.settings": "<PERSON><PERSON><PERSON><PERSON>", "rpc.sign.accept": "Aceitar", "rpc.sign.cannot_parse_message.body": "Não conseguimos decodificar esta mensagem. Aceite esta solicitação apenas se você confiar neste app.{br}{br}As mensagens podem ser usadas para fazer login em um app, mas também podem dar aos apps controle sobre seus tokens.", "rpc.sign.cannot_parse_message.header": "Prossiga com atenção", "rpc.sign.import_private_key": "Importar chaves", "rpc.sign.subtitle": "Para {name}", "rpc.sign.title": "<PERSON><PERSON><PERSON>", "safe-creation.success.title": "<PERSON><PERSON>ira <PERSON>riad<PERSON>", "safe-safety-checks-popup.title": "Verificações de Segurança da Transação", "safetyChecksPopup.title": "Verificações de Segurança do Site", "scan_qr_code.description": "Escaneie um QR de carteira ou conecte a um app", "scan_qr_code.show_qr_code": "Mostrar meu QR code", "scan_qr_code.tryAgain": "Tentar novamente", "scan_qr_code.unlockCamera": "<PERSON><PERSON><PERSON> c<PERSON>", "screen-lock-missing.modal.close": "<PERSON><PERSON><PERSON>", "screen-lock-missing.modal.subtitle": "Seu dispositivo precisa de um bloqueio de tela para usar passkeys. Configure um e tente novamente.", "screen-lock-missing.modal.title": "Falta o bloqueio de tela", "seedConfirmation.banner.subtitle": "Sua Frase Secreta dá acesso total aos seus fundos. Só golpistas a pedem.", "seedConfirmation.title": "NUNCA compartilhe sua Frase Secreta com ninguém", "select-active-owner.subtitle": "Escolha uma carteira. Pode trocar depois.", "select-active-owner.title": "Selecionar carteira", "select-card.title": "Selecionar cartão", "select-crypto-currency-title": "Selecionar token", "select-token.title": "Selecionar token", "selectEarnAccount.chf.description.steps": "· Saque 24h por dia, 7 dias por semana, sem bloqueios {br}· Juros rendem a cada segundo {br}· Depósitos superprotegidos em <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} ao ano em CHF", "selectEarnAccount.eur.description.steps": "· Saque 24h, sem bloqueios {br}· Juros rendem a cada segundo {br}· Empréstimos superprotegidos com <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} por ano em EUR", "selectEarnAccount.subtitle": "Você pode mudar a qualquer momento", "selectEarnAccount.title": "Selecione a Moeda", "selectEarnAccount.usd.description.steps": "· Saque 24h, sem bloqueios {br}· Juros rendem a cada segundo {br}· Depósitos superprotegidos em <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} por ano em USD", "selectEarnAccount.zero.description_general": "Mantenha saldo digital sem rendimentos", "selectEarnAccount.zero.title": "0% ao ano", "selectRechargeThreshold.button.enterAmount": "Informar valor", "selectRechargeThreshold.button.setTo": "Definir para {amount}", "selectRechargeThreshold.description.line1": "Quando o saldo do seu cartão ficar abaixo de {amount}, ele recarrega automaticamente para {amount} usando sua conta Renda.", "selectRechargeThreshold.description.line2": "Um valor menor mantém mais saldo na sua conta Renda (rendendo 3%). Você pode alterar isso a qualquer momento.", "selectRechargeThreshold.title": "Definir saldo alvo do cartão", "select_currency_to_withdraw.select_token_to_withdraw": "Selecione o token para sacar", "send-card-token.form.send": "Enviar", "send-card-token.form.send-amount": "<PERSON>or da recarga", "send-card-token.form.title": "Adicionar <PERSON> ao cartão", "send-card-token.form.to-address": "Cartão", "send-safe-transaction.network-fee-widget.error": "Você precisa de {amount} ou escolha outro token", "send-safe-transaction.network-fee-widget.no-fee": "Sem taxas", "send-safe-transaction.network-fee-widget.title": "Taxas", "send-safe-transaction.network_fee_widget.title": "Taxa de rede", "send.banner.fees": "Você precisa de {amount} mais {currency} para pagar as taxas", "send.banner.toAddressNotSupportedNetwork.subtitle": "A carteira do destinatário não é compatível com {network}. Troque por um token compatível.", "send.banner.toAddressNotSupportedNetwork.title": "Rede não compatível com o destinatário", "send.banner.walletNotSupportedNetwork.subtitle": "Smart Wallets não podem fazer transações na rede {network}. Troque por um token compatível.", "send.banner.walletNotSupportedNetwork.title": "Rede do token não compatível", "send.empty-portfolio.empty-state": "Nenhum token encontrado", "send.empty-portfolio.header": "Tokens", "send.titile": "A enviar", "sendLimit.success.subtitle": "Seu limite diário de gastos será atualizado em 3 minutos. Até lá, você pode continuar gastando dentro do seu limite atual.", "sendLimit.success.title": "A alteração levará 3 minutos", "send_crypto.form.disconnected.cta.addFunds": "Adicionar fundos", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "<PERSON><PERSON> para {network}", "send_crypto.form.disconnected.label": "Valor a transferir", "send_to.qr_code.description": "Escaneie um QR code para enviar a uma carteira", "send_to.qr_code.title": "Escanear QR code", "send_to_card.header": "Enviar para o endereço do cartão", "send_to_card.select_sender.add_wallet": "<PERSON><PERSON><PERSON><PERSON>", "send_to_card.select_sender.header": "Selecionar remetente", "send_to_card.select_sender.search.default_placeholder": "Buscar endereço ou ENS", "send_to_card.select_sender.show_card_address_button_description": "Mostrar endereço do cartão", "send_token.form.select-address": "Selecionar endereço", "send_token.form.send-amount": "Valor do envio", "send_token.form.title": "Enviar", "setLimit.amount.error.zero_amount": "Você não conseguirá fazer pagamentos", "setLimit.error.max_limit_reached": "Definir limite máximo {amount}", "setLimit.error.same_as_current_limit": "Igual ao limite atual", "setLimit.placeholder": "Atual: {amount}", "setLimit.submit": "Definir Limite", "setLimit.submit.error.amount_required": "Insira um valor", "setLimit.subtitle": "Seu limite de gastos diários com o cartão.", "setLimit.title": "Definir limite de gasto diário", "settings.accounts": "<PERSON><PERSON>", "settings.accountsSeeAll": "Ver tudo", "settings.addAccount": "<PERSON><PERSON><PERSON><PERSON>", "settings.card": "Configurações do cartão", "settings.connections": "Conexões de apps", "settings.currency": "Moe<PERSON> pad<PERSON>", "settings.default_currency_selector.title": "<PERSON><PERSON>", "settings.discord": "Discord", "settings.experimentalMode": "Modo experimental", "settings.experimentalMode.subtitle": "Teste novas funcionalidades", "settings.language": "Idioma", "settings.lockZeal": "Bloquear Zeal", "settings.notifications": "Notificações", "settings.open_expanded_view": "A<PERSON>r em tela cheia", "settings.privacyPolicy": "Política de Privacidade", "settings.settings": "Configurações", "settings.termsOfUse": "Termos de Uso", "settings.twitter": "𝕏 / Twitter", "settings.version": "Vers<PERSON> {version} amb: {env}", "setup-card.confirmation": "Obter Cartão Virtual", "setup-card.confirmation.subtitle": "Faça pagamentos online e adicione à sua {type} carteira para pagamentos por aproximação.", "setup-card.getCard": "<PERSON><PERSON><PERSON>", "setup-card.order.physicalCard": "Cartão físico", "setup-card.order.physicalCard.steps": "· Um VISA Gnosis Pay físico {br}· Leva até 3 semanas para chegar {br}· Use em pagamentos presenciais e caixas eletrônicos. {br}· Adicione à carteira Apple/Google (apenas para países compatíveis", "setup-card.order.subtitle1": "Você pode usar vários cartões ao mesmo tempo", "setup-card.order.title": "Qual tipo de cartão?", "setup-card.order.virtualCard": "Cartão virtual", "setup-card.order.virtual_card.steps": "· Um VISA Gnosis Pay digital {br}· Use na hora para pagamentos online {br}· Adicione à carteira Apple/Google (apenas para países compatíveis)", "setup-card.orderCard": "<PERSON><PERSON><PERSON>", "setup-card.virtual-card": "Obter Cartão Virtual", "setup.notifs.fakeAndroid.title": "Notificações de pagamentos e transferências recebidas", "setup.notifs.fakeIos.subtitle": "O Zeal pode te avisar quando você receber dinheiro ou gastar com seu cartão Visa. Você pode mudar isso depois.", "setup.notifs.fakeIos.title": "Notificações de pagamentos e transferências recebidas", "sign.PermitAllowanceItem.spendLimit": "Limite de gastos", "sign.ledger.subtitle": "Pedido enviado. Continue na sua wallet.", "sign.ledger.title": "Assine na hardware wallet", "sign.passkey.subtitle": "Seu navegador deve pedir para você assinar com a passkey associada a esta carteira. Continue por lá.", "sign.passkey.title": "<PERSON><PERSON><PERSON><PERSON> a passkey", "signal_aborted_for_uknown_reason.title": "Solicitação de rede cancelada", "simulatedTransaction.BridgeTrx.info.title": "Bridge", "simulatedTransaction.CardTopUp.info.title": "Adicionar <PERSON> ao cartão", "simulatedTransaction.CardTopUpTrx.info.title": "Adicionar <PERSON> ao cartão", "simulatedTransaction.NftCollectionApproval.approve": "Aprovar coleção de NFT", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "Adicionar ao cart<PERSON>", "simulatedTransaction.OrderEarnDepositBridge.title": "Depositar em Earn", "simulatedTransaction.P2PTransaction.info.title": "Enviar", "simulatedTransaction.PermitSignMessage.title": "Permit", "simulatedTransaction.SingleNftApproval.approve": "Aprovar NFT", "simulatedTransaction.UnknownSignMessage.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.Withdrawal.info.title": "<PERSON><PERSON>", "simulatedTransaction.approval.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.approve.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.p2p.info.account": "Para", "simulatedTransaction.p2p.info.unlabelledAccount": "Carteira sem nome", "simulatedTransaction.unknown.info.receive": "A receber", "simulatedTransaction.unknown.info.send": "A enviar", "simulatedTransaction.unknown.using": "Usando {app}", "simulation.approval.modal.text": "Ao aceitar uma aprovação, você dá permissão para que um app/contrato inteligente específico use seus tokens ou NFTs em transações futuras.", "simulation.approval.modal.title": "O que são aprovações?", "simulation.approval.spend-limit.label": "Limite de gastos", "simulation.approve.footer.for": "Para", "simulation.approve.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "simulationNotAvailable.title": "Ação desconhecida", "smart-wallet-activation-view.on": "Em", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "O 1Password pode bloquear o acesso à sua carteira", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Use Apple ou Google pra configurar o Zeal.", "smart-wallet.passkey-notice.title": "Evite o 1Password", "spend-limits.high.modal.text": "Defina um limite de gastos próximo ao valor que você realmente usará com um app ou contrato inteligente. Limites altos são arriscados e podem facilitar o roubo dos seus tokens por golpistas.", "spend-limits.high.modal.text_sign_message": "O limite de gastos deve ser próximo da quantidade de tokens que você realmente usará com um app ou contrato inteligente. Limites altos são arriscados e facilitam o roubo de seus tokens por golpistas.", "spend-limits.high.modal.title": "Limite de gastos alto", "spend-limits.modal.text": "O limite de gastos define quantos tokens um aplicativo pode usar em seu nome. Você pode alterar ou remover esse limite a qualquer momento. Para sua segurança, mantenha os limites de gastos próximos da quantidade de tokens que você realmente vai usar com um aplicativo.", "spend-limits.modal.title": "O que é limite de gastos?", "spent-limit-info.modal.description": "O limite de gastos é a quantidade de tokens que um app pode usar em seu nome. Você pode alterar ou remover esse limite a qualquer momento. Para sua segurança, mantenha os limites de gastos próximos da quantidade de tokens que você realmente usará com um app.", "spent-limit-info.modal.title": "O que é limite de gastos?", "sswaps-io.transfer-provider": "Provedor de transferência", "storage.accountDetails.activateWallet": "Ativar carteira", "storage.accountDetails.changeWalletLabel": "Alterar nome da carteira", "storage.accountDetails.deleteWallet": "Remover carteira", "storage.accountDetails.setup_recovery_kit": "Kit de Recuperação", "storage.accountDetails.showPrivateKey": "Mostrar Chave Privada", "storage.accountDetails.showWalletAddress": "Mostrar endereço da carteira", "storage.accountDetails.smartBackup": "Backup e Recuperação", "storage.accountDetails.viewSsecretPhrase": "<PERSON>er <PERSON><PERSON> Secreta", "storage.accountDetails.zealSmartWallets": "<PERSON> que são as Smart Wallets da Zeal?", "storage.manageAccounts.title": "Carteiras", "submit-userop.progress.text": "Enviando", "submit.error.amount_high": "Valor muito alto", "submit.error.amount_hight": "Valor muito alto", "submit.error.amount_low": "Valor muito baixo", "submit.error.amount_required": "Insira o valor", "submit.error.maximum_number_of_characters_exceeded": "Reduza os caracteres da mensagem", "submit.error.not_enough_balance": "<PERSON><PERSON> insuficiente", "submit.error.recipient_required": "Destina<PERSON><PERSON><PERSON> o<PERSON>", "submit.error.routes_not_found": "Nenhuma rota encontrada", "submitSafeTransaction.monitor.title": "Resultado da transação", "submitSafeTransaction.sign.title": "Resultado da transação", "submitSafeTransaction.state.sending": "Enviando", "submitSafeTransaction.state.sign": "<PERSON><PERSON><PERSON>", "submitSafeTransaction.submittingToRelayer.title": "Resultado da transação", "submitTransaction.cancel": "<PERSON><PERSON><PERSON>", "submitTransaction.cancel.attemptingToStop": "<PERSON><PERSON><PERSON> parar", "submitTransaction.cancel.failedToStop": "<PERSON>alha ao parar", "submitTransaction.cancel.stopped": "Interrompida", "submitTransaction.cancel.title": "Prévia da transação", "submitTransaction.failed.banner.description": "A rede cancelou esta transação inesperadamente. Tente de novo ou fale conosco.", "submitTransaction.failed.banner.title": "A transação falhou", "submitTransaction.failed.execution_reverted.title": "O app apresentou um erro", "submitTransaction.failed.execution_reverted_without_message.title": "O app apresentou um erro", "submitTransaction.failed.out_of_gas.description": "Cancelado por exceder a taxa de rede.", "submitTransaction.failed.out_of_gas.title": "Erro na rede", "submitTransaction.sign.title": "Resultado da transação", "submitTransaction.speedUp": "<PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue": "Adicionado à fila", "submitTransaction.state.addedToQueue.short": "Na fila", "submitTransaction.state.cancelled": "Interrompida", "submitTransaction.state.complete": "{currencyCode} adicionado ao <PERSON>", "submitTransaction.state.complete.subtitle": "Con<PERSON>ra seu port<PERSON><PERSON><PERSON>", "submitTransaction.state.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.failed": "Fal<PERSON>", "submitTransaction.state.includedInBlock": "Incluído no bloco", "submitTransaction.state.includedInBlock.short": "No bloco", "submitTransaction.state.replaced": "Substituída", "submitTransaction.state.sendingToNetwork": "Enviando para a rede", "submitTransaction.stop": "<PERSON><PERSON>", "submitTransaction.submit": "Enviar", "submitted-user-operation.state.bundled": "Na fila", "submitted-user-operation.state.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.failed": "Fal<PERSON>", "submitted-user-operation.state.pending": "Transmitindo", "submitted-user-operation.state.rejected": "<PERSON><PERSON><PERSON><PERSON>", "submittedTransaction.failed.title": "Falha na transação", "success_splash.card_activated": "Cartão ativado", "supportFork.give-feedback.title": "Enviar feedback", "supportFork.itercom.description": "Dúvidas sobre depósitos, <PERSON><PERSON>n e mais.", "supportFork.itercom.title": "Dúvidas sobre a carteira", "supportFork.title": "Ajuda com", "supportFork.zendesk.subtitle": "Dúvidas de pagamentos, identidade, etc.", "supportFork.zendesk.title": "Pagamentos e identidade", "supported-networks.ethereum.warning": "Taxas altas", "supportedNetworks.networks": "Redes compatíveis", "supportedNetworks.oneAddressForAllNetworks": "Um endereço para todas as redes", "supportedNetworks.receiveAnyAssets": "Receba qualquer ativo de redes compatíveis diretamente na sua carteira Zeal, usando o mesmo endereço", "swap.form.error.no_routes_found": "Nenhuma rota encontrada", "swap.form.error.not_enough_balance": "<PERSON><PERSON> insuficiente", "swaps-io-details.bank.serviceProvider": "<PERSON><PERSON><PERSON>", "swaps-io-details.details.processing": "Processando", "swaps-io-details.pending": "Pendente", "swaps-io-details.rate": "Cotação", "swaps-io-details.serviceProvider": "<PERSON><PERSON><PERSON>", "swaps-io-details.transaction.from.processing": "Transação iniciada", "swaps-io-details.transaction.networkFees": "Taxas de rede", "swaps-io-details.transaction.state.completed-transaction": "Transação concluída", "swaps-io-details.transaction.state.started-transaction": "Transação iniciada", "swaps-io-details.transaction.to.processing": "Transação concluída", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Depósito quase lá. Kinetex processando.", "swapsIO.monitoring.awaitingLiqSend.title": "<PERSON><PERSON><PERSON>", "swapsIO.monitoring.awaitingRecive.title": "Transmitindo", "swapsIO.monitoring.awaitingSend.title": "Na fila", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Tokens voltando. Kinetex não concluiu.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Devolvendo tokens", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Erro: tokens não enviados. Tente de novo.", "swapsIO.monitoring.cancelledNoSlash.title": "Tokens devolvidos", "swapsIO.monitoring.cancelledSlashed.subtitle": "Tokens devolvidos. Kinetex não concluiu.", "swapsIO.monitoring.cancelledSlashed.title": "Tokens devolvidos", "swapsIO.monitoring.completed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "taker-metadata.earn": "Renda em dólar digital com Sky", "taker-metadata.earn.aave": "Renda em euro digital com Aave", "taker-metadata.earn.aave.cashout24": "<PERSON><PERSON>, 24/7", "taker-metadata.earn.aave.trusted": "US$ 27 bi em ativos, 2+ anos de confiança", "taker-metadata.earn.aave.yield": "Rendimento acumula a cada segundo", "taker-metadata.earn.chf": "Renda em CHF digital", "taker-metadata.earn.chf.cashout24": "<PERSON><PERSON>, 24h por dia", "taker-metadata.earn.chf.trusted": "Confiado com Fr. 28 mi", "taker-metadata.earn.chf.yield": "Rendimento acumula a cada segundo", "taker-metadata.earn.usd.cashout24": "<PERSON><PERSON>, 24/7", "taker-metadata.earn.usd.trusted": "US$ 10,7 bi em ativos, 5+ anos de confiança", "taker-metadata.earn.usd.yield": "Rendimento acumula a cada segundo", "test": "Depositar", "to.titile": "A receber", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "Ativos", "token.groupHeader.titleWithSum": "Ativos {sum}", "token.hidden_tokens.page.title": "Tokens ocultos", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "Adicionar token", "token.widget.cashback_empty": "Nenhuma transação ainda", "token.widget.emptyState": "Nenhum token na carteira", "tokens.cash": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.approve.for": "Para", "top-up-card-from-earn-view.approve.into": "Em", "top-up-card-from-earn-view.swap.from": "De", "top-up-card-from-earn-view.swap.to": "Para", "top-up-card-from-earn-view.withdraw.to": "Para", "top-up-card-from-earn.trx.title.approval": "<PERSON><PERSON><PERSON> troca", "top-up-card-from-earn.trx.title.swap": "Adicionar ao cart<PERSON>", "top-up-card-from-earn.trx.title.withdrawal": "<PERSON><PERSON>", "topUpDapp.connectWallet": "Conectar carteira", "topup-fee-breakdown.bungee-fee": "Taxa de provedor externo", "topup-fee-breakdown.header": "Taxa da transação", "topup-fee-breakdown.network-fee": "Taxa de rede", "topup-fee-breakdown.total-fee": "Taxa total", "topup.continue-in-wallet": "Continue na sua carteira", "topup.send.title": "Enviar", "topup.submit-transaction.close": "<PERSON><PERSON><PERSON>", "topup.submit-transaction.sent-to-wallet": "Enviar {amount}", "topup.to": "Para", "topup.transaction.complete.close": "<PERSON><PERSON><PERSON>", "topup.transaction.complete.try-again": "Tentar novamente", "transaction-request.nonce-too-low.modal.button-text": "<PERSON><PERSON><PERSON>", "transaction-request.nonce-too-low.modal.text": "Uma transação com o mesmo número de série (nonce) já foi concluída, então você não pode mais enviar esta. Isso pode acontecer ao fazer transações em sequência ou ao tentar acelerar ou cancelar uma que já foi concluída.", "transaction-request.nonce-too-low.modal.title": "Transação com mesmo nonce foi concluída", "transaction-request.replaced.modal.button-text": "<PERSON><PERSON><PERSON>", "transaction-request.replaced.modal.text": "Não conseguimos rastrear o status desta transação. Ela pode ter sido substituída por outra ou o nó RPC está com problemas.", "transaction-request.replaced.modal.title": "Status da transação não encontrado", "transaction.activity.details.modal.close": "<PERSON><PERSON><PERSON>", "transaction.cancel_popup.cancel": "Não, espere", "transaction.cancel_popup.confirm": "<PERSON>m, parar", "transaction.cancel_popup.description": "Para parar, você precisa pagar uma nova taxa de rede em vez da taxa original de {oldFee}", "transaction.cancel_popup.description_without_original": "Para parar, você precisa pagar uma nova taxa de rede", "transaction.cancel_popup.not_supported.subtitle": "Não é possível parar transações em {network}", "transaction.cancel_popup.not_supported.title": "Não disponível", "transaction.cancel_popup.stopping_fee": "Taxa de rede para parar", "transaction.cancel_popup.title": "Parar transação?", "transaction.in-progress": "Em andamento", "transaction.inProgress": "Em andamento", "transaction.speed_up_popup.cancel": "Não, espere", "transaction.speed_up_popup.confirm": "<PERSON><PERSON>, ace<PERSON>e", "transaction.speed_up_popup.description": "Para acelerar, você precisa pagar uma nova taxa de rede em vez da taxa original de {amount}", "transaction.speed_up_popup.description_without_original": "Para acelerar, você precisa pagar uma nova taxa de rede", "transaction.speed_up_popup.seed_up_fee_title": "Taxa de rede para acelerar", "transaction.speed_up_popup.title": "Acelerar transação?", "transaction.speedup_popup.not_supported.subtitle": "A aceleração de transações não é suportada na {network}", "transaction.speedup_popup.not_supported.title": "Não suportado", "transaction.subTitle.failed": "Fal<PERSON>", "transactionDetails.cashback.not-qualified": "Não qualificada", "transactionDetails.cashback.paid": "{amount} pago", "transactionDetails.cashback.pending": "{amount} pendente", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "Desconhecido", "transactionDetails.cashback_estimate": "Estimativa de cashback", "transactionDetails.category": "Categoria", "transactionDetails.exchangeRate": "Taxa de câmbio", "transactionDetails.location": "Local", "transactionDetails.payment-approved": "Pagamento aprovado", "transactionDetails.payment-declined": "Pagamento recusado", "transactionDetails.payment-reversed": "Pagamento estornado", "transactionDetails.recharge.amountSentFromEarn.title": "Valor enviado do Earn", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "<PERSON><PERSON><PERSON><PERSON> no cartão", "transactionDetails.recharge.rate.title": "Taxa", "transactionDetails.recharge.transactionId.title": "ID da transação", "transactionDetails.refund": "Reembolso", "transactionDetails.reversal": "Estorno", "transactionDetails.transactionCurrency": "Moeda da transação", "transactionDetails.transactionId": "ID da transação", "transactionDetails.type": "Transação", "transactionRequestWidget.approve.subtitle": "Para {target}", "transactionRequestWidget.p2p.subtitle": "Para {target}", "transactionRequestWidget.unknown.subtitle": "<PERSON><PERSON><PERSON> {target}", "transactionSafetyChecksPopup.title": "Verificações de Segurança da Transação", "transactions.main.activity.title": "Atividade", "transactions.page.hiddenActivity.title": "Atividade oculta", "transactions.page.title": "Atividade", "transactions.viewTRXHistory.emptyState": "Nenhuma transação ainda", "transactions.viewTRXHistory.errorMessage": "Não foi possível carregar seu histórico de transações", "transactions.viewTRXHistory.hidden.emptyState": "Nenhuma transação oculta", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Atividade não disponível para testnets", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Atividade não disponível para testnets{br}<link>Ver no explorer</link>", "transfer_provider": "<PERSON><PERSON><PERSON> transfer<PERSON>", "transfer_setup_with_different_wallet.subtitle": "As transferências bancárias estão configuradas em outra carteira. Você só pode ter uma carteira conectada para transferências.", "transfer_setup_with_different_wallet.swtich_and_continue": "Trocar e continuar", "transfer_setup_with_different_wallet.title": "<PERSON><PERSON><PERSON> de carteira", "tx-sent-to-wallet.button": "<PERSON><PERSON><PERSON>", "tx-sent-to-wallet.subtitle": "Continue em {wallet}", "unblockProviderInfo.fees": "Você tem as menores taxas possíveis: 0% até US$ 5 mil por mês e 0,2% acima disso.", "unblockProviderInfo.registration": "A Unblock é registrada e autorizada pela FNTT para fornecer serviços de câmbio e custódia VASP e é um provedor MSB registrado na Fincen dos EUA. <link>Saiba mais</link>", "unblockProviderInfo.selfCustody": "O dinheiro digital que você recebe fica em sua custódia privada e ninguém mais terá controle sobre seu ativo.", "unblock_invalid_faster_payment_configuration.subtitle": "A conta bancária informada não é compatível com transferências SEPA (Europa) ou Faster Payments (Reino Unido). Por favor, informe outra conta.", "unblock_invalid_faster_payment_configuration.title": "É necessária outra conta", "unknownTransaction.primaryText": "Transação com cartão", "unsupportedCountry.subtitle": "Transferências bancárias ainda não estão disponíveis no seu país.", "unsupportedCountry.title": "Indisponível em {country}", "update-app-popup.subtitle": "A nova atualização tem correções, novidades e mais magia. Atualize para a versão mais recente e turbine seu Zeal.", "update-app-popup.title": "Atualizar versão do Zeal", "update-app-popup.update-now": "Atual<PERSON>r agora", "user_associated_with_other_merchant.subtitle": "Esta carteira não pode ser usada para transferências bancárias. Use outra ou fale conosco no Discord para suporte e atualizações.", "user_associated_with_other_merchant.title": "Não é possível usar esta carteira", "user_associated_with_other_merchant.try_with_another_wallet": "Tentar com outra carteira", "user_email_already_exists.subtitle": "Você já configurou a transferência bancária com outra carteira. Tente novamente com a carteira que usou antes.", "user_email_already_exists.title": "Transferências em outra carteira", "user_email_already_exists.try_with_another_wallet": "Tentar com outra carteira", "validation.invalid.iban": "IBAN inválido", "validation.required": "Obrigatório", "validation.required.first_name": "Nome é obrigatório", "validation.required.iban": "IBAN é obrigatório", "validation.required.last_name": "Sobrenome é obrigatório", "verify-passkey.cta": "Verificar passkey", "verify-passkey.subtitle": "Verifique se sua passkey foi criada e está protegida.", "verify-passkey.title": "Verificar passkey", "view-cashback.cashback-next-cycle": "Taxa de Cashback em {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "Deposite para receber cashback", "view-cashback.pending": "{money} Pendente", "view-cashback.pending-rewards.not_paid": "Recebimento em {days}d", "view-cashback.pending-rewards.paid": "Recebido esta semana", "view-cashback.received-rewards": "Recompensas recebidas", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Pagamentos não confirmados", "view-cashback.upcoming": "Próxi<PERSON> {money}", "virtual-card-order.configure-safe.loading-text": "<PERSON><PERSON><PERSON>", "virtual-card-order.create-order.loading-text": "Ativando <PERSON>ão", "virtual-card-order.create-order.success-text": "Cartão ativado", "virtualCard.activateCard": "At<PERSON>r <PERSON>", "walletDeleteConfirm.main_action": "Remover", "walletDeleteConfirm.subtitle": "Para usar a carteira, importe-a novamente.", "walletDeleteConfirm.title": "Remover carteira?", "walletSetting.header": "Configurações da Carteira", "wallet_connect.connect.cancel": "<PERSON><PERSON><PERSON>", "wallet_connect.connect.connect_button": "Conectar", "wallet_connect.connect.title": "Conectar", "wallet_connect.connected.title": "Conectado", "wallet_connect_add_chain_missing.title": "Rede não suportada", "wallet_connect_proposal_expired.title": "Conexão expirada", "withdraw": "<PERSON><PERSON>", "withdraw.confirmation.close": "<PERSON><PERSON><PERSON>", "withdraw.confirmation.continue": "Confirmar", "withdrawal_request.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withdrawal_request.pending": "Pendente", "zeal-dapp.connect-wallet.cta.primary.connecting": "Conectando...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Conectar", "zeal-dapp.connect-wallet.cta.secondary": "<PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.title": "Conecte a carteira para continuar", "zealSmartWalletInfo.gas": "Pague a taxa de rede com vários tokens; use tokens ERC20 populares em redes compatíveis para pagar as taxas de rede, não apenas os tokens nativos.", "zealSmartWalletInfo.recover": "Sem Frases Secretas; recupere usando a chave de acesso biométrica do seu gerenciador de senhas, iCloud ou conta Google.", "zealSmartWalletInfo.selfCustodial": "Totalmente privada; as assinaturas da chave de acesso são validadas na blockchain para minimizar dependências centrais.", "zealSmartWalletInfo.title": "<PERSON><PERSON> as Smart Wallets da Zeal", "zeal_a_rewards_already_claimed_error.title": "Recompensa já resgatada", "zwidget.minimizedDisconnected.label": "Zeal desconectado"}