{"Account.ListItem.details.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AddFromAddress.success": "Denarnica shranjena", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{Brez denarnic} one{{count} denarnica} other{{count} denarnic}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Skrivna fraza {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Ustvari nove denarnice iz ene od obstoječih Skrivnih fraz", "AddFromExistingSecretPhrase.SelectPhrase.title": "Izberi Skrivno frazo", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "Tvoja Skrivna fraza lahko varnostno kopira več denarnic. <PERSON><PERSON><PERSON><PERSON> tiste, ki jih ž<PERSON><PERSON>l<PERSON>.", "AddFromExistingSecretPhrase.WalletSelection.title": "<PERSON><PERSON> dodaj denar<PERSON>o", "AddFromExistingSecretPhrase.success": "Denarnice dodane v Zeal", "AddFromHardwareWallet.subtitle": "Izberi svojo strojno denarnico za povezavo z Zealom", "AddFromHardwareWallet.title": "Strojna denarnica", "AddFromNewSecretPhrase.WalletSelection.subtitle": "<PERSON><PERSON><PERSON>i denarnice, ki jih <PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.WalletSelection.title": "Uvozi denarnice", "AddFromNewSecretPhrase.accounts": "Denarnice", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "Skrivna fraza deluje kot obesek za ključe za milijone denarnic, vsaka z edinstvenim zasebnim ključem.{br}{br}<PERSON><PERSON><PERSON><PERSON> lahko <PERSON> den<PERSON>, koli<PERSON> jih pot<PERSON>bu<PERSON>, ali pa jih dodaš več pozneje.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Denarnice s skrivno frazo", "AddFromNewSecretPhrase.subtitle": "Vnesi svojo skrivno frazo, besede <PERSON><PERSON><PERSON> s <PERSON>sledki", "AddFromNewSecretPhrase.success_secret_phrase_added": "Skrivna fraza do<PERSON>a 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Denarnice dodane v Zeal", "AddFromNewSecretPhrase.wallets": "Denarnice", "AddFromPrivateKey.subtitle": "Vnesi svoj zasebni ključ", "AddFromPrivateKey.success": "Zasebni ključ dodan 🎉", "AddFromPrivateKey.title": "<PERSON><PERSON><PERSON>i denarnico", "AddFromPrivateKey.typeOrPaste": "Vtipkaj ali prilepi tukaj", "AddFromSecretPhrase.importWallets": "{count,plural,=0{Ni <PERSON>h denarnic} one{Uvozi denarnico} other{Uvozi {count} denarnic}}", "AddFromTrezor.AccountSelection.title": "<PERSON><PERSON><PERSON> denar<PERSON>e T<PERSON>or", "AddFromTrezor.hwWalletTip.subtitle": "Strojna denarnica hrani milijone denarnic z različnimi naslovi. <PERSON><PERSON><PERSON><PERSON> la<PERSON>ko to<PERSON> denar<PERSON>, kolikor jih potrebuješ, ali pa jih dodaš več pozneje.", "AddFromTrezor.hwWalletTip.title": "Uvoz iz strojne denarnice", "AddFromTrezor.importAccounts": "{count,plural,=0{Ni <PERSON>h denarnic} one{Uvozi denarnico} other{Uvozi {count} denarnic}}", "AddFromTrezor.success": "Denarnice dodane v Zeal", "ApprovalSpenderTypeCheck.failed.subtitle": "Verjetno prevara: porabniki bi morale biti pogodbe", "ApprovalSpenderTypeCheck.failed.title": "Porabnik je denarnica, ne pogodba", "ApprovalSpenderTypeCheck.passed.subtitle": "Običajno sredstva odobriš pogodbam", "ApprovalSpenderTypeCheck.passed.title": "Porabnik je pametna pogodba", "BestReturns.subtitle": "Ta ponudnik zamenjave ti bo zagotovil najvišji donos, vključno z vsemi stroški.", "BestReturnsPopup.title": "Najboljši donos", "BlacklistCheck.Failed.subtitle": "Zlonamerne prijave s strani <source></source>", "BlacklistCheck.Failed.title": "Stran je na črnem seznamu", "BlacklistCheck.Passed.subtitle": "Brez zlonamernih prijav s strani <source></source>", "BlacklistCheck.Passed.title": "Stran ni na črnem seznamu", "BlacklistCheck.failed.statusButton.label": "Stran je bila prijavljena", "BridgeRoute.slippage": "Zdrs {slippage}", "BridgeRoute.title": "Ponudnik mostu", "CheckConfirmation.InProgress": "V teku...", "CheckConfirmation.success.splash": "<PERSON><PERSON><PERSON><PERSON>", "ChooseImportOrCreateSecretPhrase.subtitle": "Uvozi skrivno frazo ali ustvari novo", "ChooseImportOrCreateSecretPhrase.title": "Dodaj skrivno frazo", "ConfirmTransaction.Simuation.Skeleton.title": "<PERSON>z<PERSON><PERSON><PERSON> varnostnih pre<PERSON>…", "ConnectionSafetyCheckResult.passed": "Varnostno preverjanje uspešno", "ContactGnosisPaysupport": "Kontaktiraj Gnosis Pay", "CopyKeyButton.copied": "<PERSON><PERSON><PERSON>", "CopyKeyButton.copyYourKey": "<PERSON><PERSON><PERSON> svoj ključ", "CopyKeyButton.copyYourPhrase": "<PERSON><PERSON><PERSON> svojo frazo", "DAppVerificationCheck.Failed.subtitle": "Stran ni navedena na <source></source>", "DAppVerificationCheck.Failed.title": "Strani ni v registrih aplikacij", "DAppVerificationCheck.Passed.subtitle": "Stran je navedena na <source></source>", "DAppVerificationCheck.Passed.title": "Stran je v registrih aplikacij", "DAppVerificationCheck.failed.statusButton.label": "Strani ni v registrih aplikacij", "ERC20.tokens.emptyState": "Nismo našli noben<PERSON>", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Prednostna provizija", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Zad<PERSON>h {period}: med {from} in {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Osnovna provizija: {baseFee} • Varnostna rezerva: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Nismo mogli pridobiti trenutne osnovne provizije", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Višji od naslednjega Nonce. Zastalo bo.", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "<PERSON>ce ne more biti nižji od trenutnega.", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "Prednostne provizije ni mogoče izračunati", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Nismo mogli pridobiti trenutne najvišje provizije", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "Najvišja provizija", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Lahko se zatakne, dokler se omrežnine ne znižajo", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "Najvišja osnovna provizija", "EditFeeModal.Custom.gasLimit.title": "Limit plina {gasLimit}", "EditFeeModal.Custom.title": "Napredne nastavitve", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Zataknilo se bo, dokler se osnovna provizija ne zniža", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Nizka provizija. Lahko zastane.", "EditFeeModal.EditGasLimit.estimatedGas": "Ocenjen plin: {estimated} • Varnostni medpomnilnik: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Manj od ocenjene omejitve. Transakcija ne bo uspela", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Manj od priporočene omejitve. Transakcija lahko ne uspe", "EditFeeModal.EditGasLimit.subtitle": "Nastavi najviš<PERSON> količino plina, ki jo ž<PERSON><PERSON> porabiti za to transakcijo. Če nastaviš prenizek limit, bo transakcija neuspešna", "EditFeeModal.EditGasLimit.title": "Uredi limit plina", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Manj od najnižje omejitve porabe: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "Višje od naslednjega Nonce. Zataknilo se bo", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "Nonce ne moreš nastaviti nižje od trenutnega", "EditFeeModal.EditNonce.subtitle": "Če ne nastaviš naslednjega noncea, se bo tvoja transakcija zataknila", "EditFeeModal.EditNonce.title": "<PERSON><PERSON><PERSON> nonce", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "Potrebuješ {amount} za oddajo", "EditFeeModal.Header.Time.unknown": "Neznan čas", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Največ {fee}", "EditFeeModal.Header.fee.unknown": "Neznana provizija", "EditFeeModal.Header.subsequent_failed": "Ocene so morda <PERSON>, zadnja osvežitev ni uspela", "EditFeeModal.Layout.Header.ariaLabel": "Trenutna provizija", "EditFeeModal.MaxFee.subtitle": "Najvišja provizija je največ, kar bo<PERSON> pla<PERSON>al za transakcijo, a običajno plačaš predvideno provizijo. Ta dodatni medpomnilnik pomaga, da se transakcija izvede, tudi če se omrežje upočasni ali postane dražje.", "EditFeeModal.MaxFee.title": "Najvišja omrežnina", "EditFeeModal.SelectPreset.Time.unknown": "Neznan čas", "EditFeeModal.SelectPreset.ariaLabel": "Izberi prednastavitev provizije", "EditFeeModal.SelectPreset.fast": "<PERSON><PERSON>", "EditFeeModal.SelectPreset.normal": "Normalno", "EditFeeModal.SelectPreset.slow": "Počasi", "EditFeeModal.ariaLabel": "Uredi omrežnino", "FailedSimulation.Confirmation.Item.subtitle": "<PERSON><PERSON><PERSON><PERSON> je do notranje napake", "FailedSimulation.Confirmation.Item.title": "Transakcije ni bilo mogoče simulirati", "FailedSimulation.Confirmation.subtitle": "<PERSON>, da <PERSON><PERSON><PERSON>?", "FailedSimulation.Confirmation.title": "Podpisuješ na slepo", "FailedSimulation.Title": "Napaka pri simulaciji", "FailedSimulation.footer.subtitle": "<PERSON><PERSON><PERSON><PERSON> je do notranje napake", "FailedSimulation.footer.title": "Transakcije ni bilo mogoče simulirati", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Potrebuješ {amount} za oddajo transakcije", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "<PERSON><PERSON><PERSON>va lahko traja dlje", "FeeForecastWidget.networkFee": "Omrežnina", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Nismo mogli izračunati omrežnine", "FeeForecastWidget.subsequentFailed.message": "Ocene so morda <PERSON>, zadnja osvežitev ni uspela", "FeeForecastWidget.unknownDuration": "Neznano", "FeeForecastWidget.unknownFee": "Neznano", "GasCurrencySelector.balance": "<PERSON><PERSON>: {balance}", "GasCurrencySelector.networkFee": "Omrežnina", "GasCurrencySelector.payNetworkFeesUsing": "Plačaj omrežnine z", "GasCurrencySelector.removeDefaultGasToken.description": "Plačaj omrežnine iz največjega stanja", "GasCurrencySelector.removeDefaultGasToken.title": "Samodejno upravljanje omrežnin", "GasCurrencySelector.save": "<PERSON><PERSON><PERSON>", "GoogleDriveBackup.BeforeYouBegin.first_point": "Če pozabim g<PERSON>lo za <PERSON>, za vedno izgubim svoja sredstva.", "GoogleDriveBackup.BeforeYouBegin.second_point": "Če izgubim dostop do storitve Google Drive ali spremenim obnovitveno datoteko, za vedno izgubim svoja sredstva.", "GoogleDriveBackup.BeforeYouBegin.subtitle": "<PERSON><PERSON><PERSON>, da r<PERSON><PERSON><PERSON> in sprejmeš naslednje točke o zasebni denarnici:", "GoogleDriveBackup.BeforeYouBegin.third_point": "Zeal mi ne more pomagati pri obnovitvi gesla za Zeal ali dostopa do storitve Google Drive.", "GoogleDriveBackup.BeforeYouBegin.title": "<PERSON>den z<PERSON>č<PERSON>š", "GoogleDriveBackup.loader.subtitle": "Prosimo, odobri zahtevo v storitvi Google Drive za nalaganje obnovitvene datoteke.", "GoogleDriveBackup.loader.title": "Čakanje na odobritev ...", "GoogleDriveBackup.success": "Varnostna kopija uspešna 🎉", "MonitorOffRamp.overServiceTime": "<PERSON><PERSON><PERSON><PERSON> prenosov je končanih v {estimated_time}, v<PERSON><PERSON>h pa lahko zaradi dodatnih preverjanj trajajo dlje. To je obi<PERSON><PERSON><PERSON> in sredstva so med temi preverjanji na varnem.{br}{br}Če se transakcija ne zaključi v {support_soft_deadline}, prosim {contact_support}", "MonitorOnRamp.contactSupport": "Obrni se na podporo", "MonitorOnRamp.from": "Iz", "MonitorOnRamp.fundsReceived": "Sredstva prejeta", "MonitorOnRamp.overServiceTime": "<PERSON><PERSON><PERSON><PERSON> prenosov je končanih v {estimated_time}, v<PERSON><PERSON>h pa lahko zaradi dodatnih preverjanj trajajo dlje. To je normalno in sredstva so med preverjanji na varnem.{br}{br}Če se transakcija ne zaključi v {support_soft_deadline}, prosim {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Pošiljanje v tvojo denarnico", "MonitorOnRamp.to": "V", "MonitorOnRamp.waitingForTransfer": "Čakamo na tvoj prenos sredstev", "NftCollectionCheck.failed.subtitle": "Zbirka ni preverjena na <source></source>", "NftCollectionCheck.failed.title": "Zbirka ni preverjena", "NftCollectionCheck.passed.subtitle": "Zbirka je preverjena na <source></source>", "NftCollectionCheck.passed.title": "Zbirka je preverjena", "NftCollectionInfo.entireCollection": "Celotna z<PERSON>", "NoSigningKeyStore.createAccount": "Ustvari račun", "NonceRangeError.biggerThanCurrent.message": "Transakcija se bo zataknila", "NonceRangeError.lessThanCurrent.message": "Transakcija ne bo uspela", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "<PERSON>ce je višji od trenutnega. Znižaj ga, da se transakcija ne zatakne.", "NonceRangeErrorPopup.biggerThanCurrent.title": "Transakcija se bo zataknila", "P2pReceiverTypeCheck.failed.subtitle": "<PERSON> poš<PERSON><PERSON> na pravi naslov?", "P2pReceiverTypeCheck.failed.title": "Prejemnik je pametna pogodba, ne denarnica", "P2pReceiverTypeCheck.passed.subtitle": "Običajno sredstva pošiljaš v druge denarnice", "P2pReceiverTypeCheck.passed.title": "Prejemnik je denarnica", "PasswordCheck.title": "Vnesi geslo", "PasswordChecker.subtitle": "<PERSON><PERSON><PERSON> s<PERSON>, da <PERSON><PERSON><PERSON>, da si to ti", "PermitExpirationCheck.failed.subtitle": "<PERSON>j bo kratko in samo toliko, kolikor potrebuješ", "PermitExpirationCheck.failed.title": "<PERSON><PERSON><PERSON>", "PermitExpirationCheck.passed.subtitle": "Kako dolgo lahko aplikacija uporablja tvoje žetone", "PermitExpirationCheck.passed.title": "Čas poteka ni predolg", "PrivateKeyValidationError.moreThanMaximumWords": "<PERSON><PERSON><PERSON><PERSON> {count} besed", "PrivateKeyValidationError.notValidPrivateKey": "To ni veljaven zasebni ključ", "PrivateKeyValidationError.secretPhraseIsInvalid": "Skrivna fraza ni veljavna", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Beseda #{index} je napačno napisana ali nevel<PERSON>vna", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} beseda} other{{count} besed}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Skrivne fraze in zasebni ključi so šifrirani in nikoli ne zapustijo te naprave", "SecretPhraseReveal.header": "Zapiši Skrivno frazo", "SecretPhraseReveal.hint": "Ne deli svoje fraze z nikomer. Hrani jo varno in brez povezave.", "SecretPhraseReveal.skip.subtitle": "To lahko stori<PERSON>, a <PERSON>e izgu<PERSON><PERSON> to napravo, preden si zap<PERSON><PERSON><PERSON><PERSON> frazo, bo<PERSON> izgu<PERSON> vsa sredstva, ki si jih dodal v to denarnico.", "SecretPhraseReveal.skip.takeTheRisk": "Tvegam", "SecretPhraseReveal.skip.title": "Preskočim zapisovanje fraze?", "SecretPhraseReveal.skip.writeDown": "Zapiši", "SecretPhraseReveal.skipForNow": "Preskoči", "SecretPhraseReveal.subheader": "<PERSON>si<PERSON>, z<PERSON><PERSON><PERSON><PERSON> jo in jo hrani varno brez povezave. Nato te bomo prosili, da jo <PERSON><PERSON>.", "SecretPhraseReveal.verify": "<PERSON><PERSON><PERSON>", "SelectCurrency.tokens": "Žetoni", "SelectCurrency.tokens.emptyState": "Nismo našli noben<PERSON>", "SelectRoute.slippage": "Zdrs {slippage}", "SelectRoutes.emptyState": "<PERSON>a to zamenjavo nismo našli poti", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Poveži denarnico", "SendERC20.labelAddress.inputPlaceholder": "Oznaka denarnice", "SendERC20.labelAddress.subtitle": "<PERSON><PERSON><PERSON><PERSON>, da jo la<PERSON> na<PERSON>.", "SendERC20.labelAddress.title": "Oz<PERSON>či to denarnico", "SendERC20.send_to": "Pošlji na", "SendERC20.tokens": "Žetoni", "SendOrReceive.bankTransfer.primaryText": "Ban<PERSON><PERSON>", "SendOrReceive.bankTransfer.shortText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, takojšen vstop in izstop", "SendOrReceive.bridge.primaryText": "Most", "SendOrReceive.bridge.shortText": "<PERSON>nos <PERSON>ov med omrežji", "SendOrReceive.receive.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.receive.shortText": "Prejmi žetone ali zbirateljske predmete", "SendOrReceive.send.primaryText": "Pošlji", "SendOrReceive.send.shortText": "Pošlji žetone na kateri koli naslov", "SendOrReceive.swap.primaryText": "Zamenjaj", "SendOrReceive.swap.shortText": "Zamenjaj med žetoni", "SendSafeTransaction.Confirm.loading": "Izvajam varnostno preverjanje...", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Šifriraj datoteko za obnovitev z geslom", "SetupRecoveryKit.google.subtitle": "Sinhronizirano {date}", "SetupRecoveryKit.google.title": "Varnostna kopija Google Drive", "SetupRecoveryKit.subtitle": "Potrebuješ vsaj en način za obnovitev računa, če odstraniš Zeal ali zamenjaš napravo.", "SetupRecoveryKit.title": "Nastavi komplet za obnovitev", "SetupRecoveryKit.writeDown.subtitle": "Zapiši Skrivno frazo", "SetupRecoveryKit.writeDown.title": "Ročna varnostna kopija", "Sign.CheckSafeDeployment.activate": "Aktiviraj", "Sign.CheckSafeDeployment.subtitle": "Preden se lahko prijaviš v aplikacijo ali podpišeš sporočilo zunaj verige, moraš aktivirati svojo napravo v tem omrežju. To se zgodi po namestitvi ali obnovitvi pametne denarnice.", "Sign.CheckSafeDeployment.title": "Aktiviraj napravo v tem omrežju", "Sign.Simuation.Skeleton.title": "Izvajam varnostno preverjanje...", "SignMessageSafetyCheckResult.passed": "Varnostno preverjanje uspešno", "SignMessageSafetyChecksPopup.title.permits": "Varnostno preverjanje dovoljenja", "SimulationFailedConfirmation.subtitle": "Simulirali smo to transakcijo in odkrili težavo, zaradi katere bo neuspe<PERSON>na. Transakcijo lahko odd<PERSON>, vendar bo verjetno neuspe<PERSON> in lahko izgubiš omrežnino.", "SimulationFailedConfirmation.title": "Transakcija bo verjet<PERSON>", "SimulationNotSupported.Title": "Simulacija ni{br}podprta na{br}{network}", "SimulationNotSupported.footer.subtitle": "To transak<PERSON><PERSON> vs<PERSON><PERSON>", "SimulationNotSupported.footer.title": "Simulacija ni podprta", "SlippagePopup.custom": "Po meri", "SlippagePopup.presetsHeader": "Zdrs pri menjavi", "SlippagePopup.title": "Nastavitve zdrsa", "SmartContractBlacklistCheck.failed.subtitle": "Zlonamerne prijave s strani <source></source>", "SmartContractBlacklistCheck.failed.title": "Pogodba je na črnem seznamu", "SmartContractBlacklistCheck.passed.subtitle": "Brez zlonamernih prijav s strani <source></source>", "SmartContractBlacklistCheck.passed.title": "Pogodba ni na črnem seznamu", "SuspiciousCharactersCheck.Failed.subtitle": "To je pogosta taktika za »phishing«.", "SuspiciousCharactersCheck.Failed.title": "Preverjamo za pogoste vzorce za phishing.", "SuspiciousCharactersCheck.Passed.subtitle": "Preverjamo za poskuse »phishinga«.", "SuspiciousCharactersCheck.Passed.title": "Naslov ne vsebuje nenavadnih znakov", "SuspiciousCharactersCheck.failed.statusButton.label": "Na<PERSON>lov vsebuje nenavadne znake ", "TokenVerificationCheck.failed.subtitle": "Žeton ni naveden na <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} ni preverjen s strani CoinGecko", "TokenVerificationCheck.passed.subtitle": "Žeton je naveden na <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} je preverjen s strani CoinGecko", "TopupDapp.MonitorTransaction.success.splash": "<PERSON><PERSON><PERSON><PERSON>", "TransactionSafetyCheckResult.passed": "Varnostno preverjanje uspešno", "TransactionSimulationCheck.failed.subtitle": "Napaka: {errorMessage}", "TransactionSimulationCheck.failed.title": "Transakcija verjetno ne bo uspela", "TransactionSimulationCheck.passed.subtitle": "Simulacija izvedena z uporabo <source></source>", "TransactionSimulationCheck.passed.title": "Predogled transakcije je bil uspešen", "TrezorError.trezor_action_cancelled.action": "<PERSON><PERSON><PERSON>", "TrezorError.trezor_action_cancelled.subtitle": "Transakcijo si zavrnil na svoji strojni denarnici", "TrezorError.trezor_device_used_elsewhere.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "Zapri vse druge odprte seje in ponovno poskusi sinhronizirati Trezor", "TrezorError.trezor_method_cancelled.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "Dovoli Trezorju izvoz denarnic v Zeal", "TrezorError.trezor_permissions_not_granted.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "<PERSON><PERSON><PERSON> ap<PERSON><PERSON><PERSON> dostop do vseh denarnic", "TrezorError.trezor_pin_cancelled.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "Seja preklicana na napravi", "TrezorError.trezor_popup_closed.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "Dialog Trezor se je nepričakovano zaprl", "TrxLikelyToFail.lessThanEstimatedGas.message": "Transakcija ne bo uspela", "TrxLikelyToFail.lessThanMinimumGas.message": "Transakcija ne bo uspela", "TrxLikelyToFail.lessThanSuggestedGas.message": "Verjetno ne bo uspela", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "Limit plina transakcije je prenizek. Povečaj limit plina na priporočeno vrednost, da prep<PERSON><PERSON><PERSON>š ne<PERSON>.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "Transakcija bo verjet<PERSON>", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Limit plina je nižji od ocenjenega plina. Povečaj limit plina na priporočeno vrednost.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "Transakcija bo neuspe<PERSON>na", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "Najvišja osnovna provizija je nižja od trenutne. Povečaj najvišjo osnovno provizijo, da se transakcija ne zatakne.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "Transakcija se bo zataknila", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "Najvišja provizija transakcije je prenizka. Povečaj najvišjo provizijo, da se transakcija ne zatakne.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "Transakcija se bo zataknila", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Prioritetna provizija je nižja od priporočene. Povečaj prioritetno provizijo, da pospešiš transakcijo.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "Dokončanje transakcije lahko traja dlje", "UnsupportedMobileNetworkLayout.gotIt": "Razumem!", "UnsupportedMobileNetworkLayout.subtitle": "Ne moreš opravljati transakcij ali podpisovati sporočil na omrežju z ID-jem {networkHexId} z mobilno različico Zeal zaenkrat še ne{br}{br}Preklopi na razširitev za brskalnik za transakcije na tem omrežju, medtem ko si prizadevamo dodati podporo zanj 🚀", "UnsupportedMobileNetworkLayout.title": "Omrežje ni podprto v mobilni različici Zeal", "UnsupportedSafeNetworkLayout.subtitle": "Ne moreš izvajati transakcij ali podpisovati sporočil na {network} z denarnico Zeal Smart Wallet{br}{br}Preklopi na podprto omrežje ali uporabi denarnico Legacy.", "UnsupportedSafeNetworkLayoutk.title": "Omrežje ni podprto za Smart Wallet", "UserConfirmationPopup.goBack": "Prekliči", "UserConfirmationPopup.submit": "<PERSON><PERSON><PERSON>", "ViewPrivateKey.header": "Zasebni ključ", "ViewPrivateKey.hint": "Ne deli svojega zasebnega ključa z nikomer. Hrani ga varno in brez povezave.", "ViewPrivateKey.subheader.mobile": "Dotakni se za prikaz Zasebnega ključa", "ViewPrivateKey.subheader.web": "Z miško se pomakni za prikaz Zasebnega ključa", "ViewPrivateKey.unblur.mobile": "Dotakni se za prikaz", "ViewPrivateKey.unblur.web": "Pokaži z miško", "ViewSecretPhrase.PasswordChecker.subtitle": "Vnesi svoje geslo za šifriranje obnovitvene datoteke. Zapomniti si ga boš <PERSON> za prihodnjo uporabo.", "ViewSecretPhrase.done": "<PERSON><PERSON><PERSON><PERSON>", "ViewSecretPhrase.header": "Skrivna fraza", "ViewSecretPhrase.hint": "Ne deli svoje fraze z nikomer. Hrani jo varno in brez povezave.", "ViewSecretPhrase.subheader.mobile": "Dotakni se za prikaz Skrivne fraze", "ViewSecretPhrase.subheader.web": "Z miško se pomakni za prikaz Skrivne fraze", "ViewSecretPhrase.unblur.mobile": "Dotakni se za prikaz", "ViewSecretPhrase.unblur.web": "Pokaži z miško", "account-details.monerium": "Prenosi potekajo prek Moneriuma, p<PERSON><PERSON><PERSON><PERSON><PERSON> in regulirane institucije. <link>Več o tem</link>", "account-details.unblock": "Prenose izvaja Unblock, p<PERSON><PERSON><PERSON><PERSON><PERSON> in registriran ponudnik storitev menjave in hrambe. <link>Več o tem</link>", "account-selector.empty-state": "Ni najdenih denarnic", "account-top-up.select-currency.title": "Žetoni", "account.accounts_not_found": "Nismo našli nobene denarnice", "account.accounts_not_found_search_valid_address": "Denarnica ni na tvojem seznamu", "account.add.create_new_secret_phrase": "Ustvari skrivno frazo", "account.add.create_new_secret_phrase.subtext": "Nova 12-besedna skrivna fraza", "account.add.fromRecoveryKit.fileNotFound": "Datoteke nismo na<PERSON>li", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "Poskusi znova", "account.add.fromRecoveryKit.fileNotFound.explanation": "Preveri pravi račun z mapo Zeal Backup.", "account.add.fromRecoveryKit.fileNotValid": "Datoteka za obnovitev ni veljavna", "account.add.fromRecoveryKit.fileNotValid.explanation": "Datoteka je napačne vrste ali spremenjena.", "account.add.import_secret_phrase": "Uvozi skrivno frazo", "account.add.import_secret_phrase.subtext": "Ustvarjeno v Zeal, Metamask ali drugih", "account.add.select_type.add_hardware_wallet": "Strojna denarnica", "account.add.select_type.existing_smart_wallet": "Obstoječa Smart Wallet", "account.add.select_type.private_key": "Zasebni ključ", "account.add.select_type.seed_phrase": "Začetna fraza", "account.add.select_type.title": "Uvozi denarnico", "account.add.select_type.zeal_recovery_file": "Datoteka za obnovitev Zeal", "account.add.success.title": "Nova denarnica ustvarjena 🎉", "account.addLabel.header": "Poimenuj svojo denarnico", "account.addLabel.labelError.labelAlreadyExist": "Oznaka že obstaja. Poskusi z drugo.", "account.addLabel.labelError.maxStringLengthExceeded": "Doseženo je največje število znakov", "account.add_active_wallet.primary_text": "<PERSON><PERSON><PERSON>", "account.add_active_wallet.short_text": "<PERSON><PERSON><PERSON><PERSON>, poveži ali uvozi denarnico", "account.add_from_ledger.success": "Denarnice dodane v Zeal", "account.add_tracked_wallet.primary_text": "<PERSON><PERSON>j denarnico samo za branje", "account.add_tracked_wallet.short_text": "Oglej si portfelj in dejavnost", "account.button.unlabelled-wallet": "Neoznačena denarnica", "account.create_wallet": "Ustvari denarnico", "account.label.edit.title": "<PERSON><PERSON><PERSON> oz<PERSON> denar<PERSON>", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Datoteka za obnovitev ni veljavna", "account.recoveryKit.selectBackupFile.subtitle": "<PERSON><PERSON><PERSON><PERSON>, ki jo <PERSON><PERSON><PERSON> o<PERSON>.", "account.recoveryKit.selectBackupFile.title": "Datoteka za obnovitev", "account.recoveryKit.success.recoveryFileFound": "Datoteka za obnovitev najdena 🎉", "account.select_type_of_account.create_eoa.short": "Starejša denarnica za poznavalce", "account.select_type_of_account.create_eoa.title": "Ustvari denarnico z začetno frazo", "account.select_type_of_account.create_safe_wallet.title": "Ustvari Smart wallet", "account.select_type_of_account.existing_smart_wallet": "Obstoječa Smart wallet", "account.select_type_of_account.hardware_wallet": "Strojna denarnica", "account.select_type_of_account.header": "<PERSON><PERSON><PERSON>", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Zasebni ključ / Semenska fraza", "account.select_type_of_account.read_only_wallet": "Denarnica samo za branje", "account.select_type_of_account.read_only_wallet.short": "<PERSON><PERSON> ka<PERSON>ega koli portfelja", "account.topup.title": "Dodaj sredstva v Zeal", "account.view.error.refreshAssets": "Osveži", "account.widget.refresh": "Osveži", "account.widget.settings": "Nastavitve", "accounts.view.copied-text": "<PERSON><PERSON><PERSON> {formattedAddress}", "accounts.view.copiedAddress": "<PERSON><PERSON><PERSON> {formattedAddress}", "action.accept": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.accpet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.allow": "<PERSON><PERSON><PERSON>", "action.back": "<PERSON><PERSON><PERSON>", "action.cancel": "Prekliči", "action.card-activation.title": "Aktiviraj kartico", "action.claim": "<PERSON><PERSON><PERSON><PERSON>", "action.close": "<PERSON><PERSON><PERSON>", "action.complete-steps": "<PERSON><PERSON><PERSON><PERSON>", "action.confirm": "<PERSON><PERSON><PERSON>", "action.continue": "<PERSON><PERSON><PERSON><PERSON>", "action.copy-address-understand": "V redu, kopiraj naslov", "action.deposit": "<PERSON><PERSON><PERSON>", "action.done": "<PERSON><PERSON><PERSON><PERSON>", "action.dontAllow": "Ne dovoli", "action.edit": "u<PERSON>i", "action.email-required": "Vnesi e-pošto", "action.enterPhoneNumber": "Vnesi telefonsko številko", "action.expand": "Razširi", "action.fix": "Popravi", "action.getStarted": "Začni", "action.got_it": "Razumem", "action.hide": "<PERSON>k<PERSON><PERSON>", "action.import": "<PERSON><PERSON><PERSON>", "action.import-keys": "Uvozi ključe", "action.importKeys": "Uvozi ključe", "action.minimize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.next": "<PERSON><PERSON><PERSON><PERSON>", "action.ok": "V redu", "action.reduceAmount": "Zmanjšaj na največ", "action.refreshWebsite": "Osveži spletno stran", "action.remove": "Odstrani", "action.remove-account": "Odstrani račun", "action.requestCode": "Zahtevaj kodo", "action.resend_code": "Ponovno pošlji kodo", "action.resend_code_with_time": "Ponovno pošlji kodo {time}", "action.retry": "Poskusi znova", "action.reveal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.save": "<PERSON><PERSON><PERSON>", "action.save_changes": "Shrani RPC", "action.search": "<PERSON><PERSON><PERSON><PERSON>", "action.seeAll": "Prikaži vse", "action.select": "Izberi", "action.send": "Pošlji", "action.skip": "Preskoči", "action.submit": "<PERSON><PERSON><PERSON>", "action.understood": "Razumem", "action.update": "<PERSON><PERSON><PERSON><PERSON>", "action.update-gnosis-pay-owner.complete": "<PERSON><PERSON><PERSON><PERSON>", "action.zeroAmount": "Vnesi znesek", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Zbirateljski predmeti", "action_bar_title.tokens": "Žetoni", "action_bar_title.transaction_request": "Zahteva za transakcijo", "activate-monerium.loading": "Pripravljamo tvoj osebni račun", "activate-monerium.success.title": "Monerium omogočen", "activate-physical-card-widget.subtitle": "Dostava lahko traja 3 tedne", "activate-physical-card-widget.title": "Aktiviraj fizično kartico", "activate-smart-wallet.title": "Aktiviraj denarnico", "active_and_tracked_wallets.title": "Zeal krije vse tvoje omrežnine na {network}, zato lahko transakcije opravljaš brezplačno!", "activity.approval-amount.revoked": "Preklicano", "activity.approval-amount.unlimited": "Neomejeno", "activity.approval.approved_for": "Odobreno za", "activity.approval.approved_for_with_target": "Odobreno {approvedTo}", "activity.approval.revoked_for": "Preklicano za", "activity.bank.serviceProvider": "Ponudnik storitev", "activity.bridge.serviceProvider": "Ponudnik storitev", "activity.cashback.period": "Obdobje vračila denarja", "activity.filter.card": "Kartica", "activity.rate": "<PERSON><PERSON><PERSON>", "activity.receive.receivedFrom": "Prejeto od", "activity.send.sendTo": "Poslano", "activity.smartContract.unknown": "<PERSON><PERSON><PERSON><PERSON> pogo<PERSON>", "activity.smartContract.usingContract": "Uporaba pogodbe", "activity.subtitle.pending_timer": "{timerString} V teku", "activity.title.arbitrary_smart_contract_interaction": "{function} na {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Neznana interakcija s pogodbo", "activity.title.bridge.from": "Most iz {token}", "activity.title.bridge.to": "Most na {token}", "activity.title.buy": "Nakup {asset}", "activity.title.card_owners_updated": "Lastniki kartice posodobljeni", "activity.title.card_spend_limit_updated": "Omejitev porabe kartice nastavljena", "activity.title.cashback_deposit": "Polog v Vračilo denarja", "activity.title.cashback_reward": "Prejeto vračilo denarja", "activity.title.cashback_withdraw": "Dvig iz Vračila denarja", "activity.title.claimed_reward": "Prevzeta nagrada", "activity.title.deployed_smart_wallet_gnosis": "<PERSON><PERSON><PERSON>", "activity.title.deposit_from_bank": "Polog z banke", "activity.title.deposit_into_card": "Polog na kartico", "activity.title.deposit_into_earn": "Polog v {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} na {smartContract}", "activity.title.from": "Od {sender}", "activity.title.pendidng_areward_claim": "Prevzem nagrade", "activity.title.pendidng_breward_claim": "Prevzem nagrade", "activity.title.recharge_disabledh": "Polnjenje kartice onemogočeno", "activity.title.recharge_set": "Cilj polnjenja nastavljen", "activity.title.recovered_smart_wallet_gnosis": "Namestitev nove naprave", "activity.title.send_pending": "<PERSON>a {receiver}", "activity.title.send_to_bank": "Na banko", "activity.title.swap": "Na<PERSON><PERSON> {token}", "activity.title.to": "<PERSON>a {receiver}", "activity.title.withdraw_from_card": "<PERSON><PERSON><PERSON> s kartice", "activity.title.withdraw_from_earn": "Dvig iz {earn}", "activity.transaction.networkFees": "Omrežnine", "activity.transaction.state": "Zaključena transakcija", "activity.transaction.state.completed": "Dokončana transakcija", "activity.transaction.state.failed": "Neuspela transakcija", "add-account.section.import.header": "Uvoz", "add-another-card-owner": "<PERSON><PERSON><PERSON>a lastnika kartice", "add-another-card-owner.Recommended.footnote": "Dodaj svojo denarnico Zeal kot dodatnega lastnika kartice Gnosis Pay", "add-another-card-owner.Recommended.primaryText": "Dodaj Zeal v Gnosis Pay", "add-another-card-owner.recommended": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add-owner.confirmation.subtitle": "Zaradi varnosti traja obdelava sprememb nastavitev 3 minute. V tem času bo tvoja kartica začasno zamrznjena in plačila ne bodo mogoča.", "add-owner.confirmation.title": "Kartica bo zamrznjena za 3 min med posodabljanjem nastavitev", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Denarnice ni mogoče dodati, ker je že v uporabi", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "Poskusi z drugo denarnico", "add.account.backup.decrypt.success": "Denarnica obnovljena", "add.account.backup.password.passwordIncorrectMessage": "Geslo ni pravilno", "add.account.backup.password.subtitle": "<PERSON><PERSON><PERSON>, s katerim si <PERSON>ral datoteko za obnovitev", "add.account.backup.password.title": "Vnesi geslo", "add.account.google.login.subtitle": "Odobri zahtevo v Google Drive za sinhronizacijo datoteke za obnovitev", "add.account.google.login.title": "Čakam na odobritev ...", "add.readonly.already_added": "Denarnica je že dodana", "add.readonly.continue": "<PERSON><PERSON><PERSON><PERSON>", "add.readonly.empty": "Vnesi na<PERSON>lov ali <PERSON>", "addBankRecipient.title": "<PERSON><PERSON>j <PERSON>ga prejemnika", "add_funds.deposit_from_bank_account": "Polog z bančnega računa", "add_funds.from_another_wallet": "Iz druge denarnice", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Poveži z dApp za polnjenje", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Poveži katero koli denarnico z dApp za polnjenje Zeal in hitro pošlji sredstva v svojo denarnico", "add_funds.from_crypto_wallet.header": "Iz druge denarnice", "add_funds.from_crypto_wallet.header.show_wallet_address": "Prikaži naslov denarnice", "add_funds.from_exchange.header": "Pošlji z borze", "add_funds.from_exchange.header.copy_wallet_address": "<PERSON><PERSON><PERSON> s<PERSON>j Z<PERSON> na<PERSON>lov", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance itd.", "add_funds.from_exchange.header.open_exchange": "Odpri aplik<PERSON><PERSON><PERSON> ali spletno stran borze", "add_funds.from_exchange.header.selected_token": "Pošlji {token} v Zeal", "add_funds.from_exchange.header.selected_token.subtitle": "Na {network}", "add_funds.from_exchange.header.send_selected_token": "Pošlji podprt žeton", "add_funds.from_exchange.header.send_selected_token.subtitle": "Izberi podprt žeton in omrežje", "add_funds.import_wallet": "Uvozi obstoječo kripto denarnico", "add_funds.title": "Napolni svoj račun", "add_funds.transfer_from_exchange": "Prenos z borze", "address.add.header": "Oglej si svojo denarnico v Zealu{br}v načinu samo za branje", "address.add.subheader": "Vnesi svoj naslov ali <PERSON>, da si na enem mestu ogledaš svoja sredstva na vseh omrežjih EVM. Več denarnic lahko ustvariš ali uvoziš pozneje.", "address_book.change_account.bank_transfers.header": "Bančni prejemniki", "address_book.change_account.bank_transfers.primary": "Bančni prejemnik", "address_book.change_account.cta": "Sledi denarnici", "address_book.change_account.search_placeholder": "<PERSON><PERSON><PERSON> ali poišči na<PERSON>lov", "address_book.change_account.tracked_header": "Denarnice samo za branje", "address_book.change_account.wallets_header": "Aktivne denarnice", "app-association-check-failed.modal.cta": "Poskusi znova", "app-association-check-failed.modal.subtitle": "Poskusi znova. Težave s povezljivostjo povzročajo zamude pri pridobivanju tvojih ključev Passkey. Če težava ne izgine, znova zaženi Zeal in poskusi še enkrat.", "app-association-check-failed.modal.subtitle.creation": "Poskusi znova. Težave s povezljivostjo povzročajo zamude pri ustvarjanju ključa Passkey. Če težava ne izgine, znova zaženi Zeal in poskusi še enkrat.", "app-association-check-failed.modal.title.creation": "Tvoja naprava ni uspela ustvariti ključa Passkey", "app-association-check-failed.modal.title.signing": "Tvoja naprava ni uspela naložiti ključev Passkey", "app.app_protocol_group.borrowed_tokens": "Izposojeni žetoni", "app.app_protocol_group.claimable_amount": "Znesek za prevzem", "app.app_protocol_group.health_rate": "Stopnja zdravja", "app.app_protocol_group.lending": "<PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.locked_tokens": "Zaklenjeni žetoni", "app.app_protocol_group.nfts": "Zbirateljski predmeti", "app.app_protocol_group.reward_tokens": "Nagradni žetoni", "app.app_protocol_group.supplied_tokens": "Vloženi žetoni", "app.app_protocol_group.tokens": "Žeton", "app.app_protocol_group.vesting_token": "Žeton v dodeljevanju", "app.appsGroupHeader.discoverMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Iš<PERSON>i ali vnesi URL", "app.error-banner.cory": "<PERSON><PERSON><PERSON> pod<PERSON> o napaki", "app.error-banner.retry": "Poskusi znova", "app.list_item.rewards": "Nagrade {value}", "app.position_details.health_rate.description": "Zdravje = posojilo / zavarovanje.", "app.position_details.health_rate.title": "Kaj je stopnja zdravja?", "approval.edit-limit.label": "<PERSON><PERSON><PERSON> pora<PERSON>", "approval.permit_info": "Informacije o dovoljenju", "approval.spend-limit.edit-modal.cancel": "Prekliči", "approval.spend-limit.edit-modal.limit-label": "<PERSON><PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.max-limit-error": "<PERSON><PERSON><PERSON><PERSON>, vis<PERSON> omejitev", "approval.spend-limit.edit-modal.revert": "Razveljavi spremembe", "approval.spend-limit.edit-modal.set-to-unlimited": "Nastavi na neomejeno", "approval.spend-limit.edit-modal.submit": "<PERSON><PERSON><PERSON> sprem<PERSON>", "approval.spend-limit.edit-modal.title": "Uredi dovoljenja", "approval.spend_limit_info": "<PERSON><PERSON> je o<PERSON><PERSON>ev porabe?", "approval.what_are_approvals": "Kaj so odobritve?", "apps_list.page.emptyState": "Ni aktivnih aplikacij", "backpace.removeLastDigit": "Odstrani zadnjo <PERSON>ko", "backup-banner.backup_now": "Zavaruj", "backup-banner.risk_losing_funds": "Zavaruj zdaj ali tvegaj izgubo sredstev", "backup-banner.title": "Denarnica ni zavarovana", "backupRecoverySmartWallet.noExportPrivateKeys": "Samodejno varnostno kopiranje: Tvoja Smart Wallet je shranjena kot pristopni ključ – skrivna fraza ali zasebni ključi niso potrebni.", "backupRecoverySmartWallet.safeContracts": "Varnost z več ključi: denarnice Zeal delujejo na pogodbah Safe, zato lahko več naprav odobri transakcijo. Ni ene same točke napake.", "backupRecoverySmartWallet.security": "Več naprav: svojo denarnico lahko uporabljaš na več napravah s pristopnim ključem. Vsaka naprava dobi svoj zasebni ključ.", "backupRecoverySmartWallet.showLocalPrivateKey": "Način za strokovnjake: zasebni ključ te naprave lahko izvoziš, ga uporabiš v drugi denarnici in se povežeš na <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Prikaži zasebni ključ</Key>", "backupRecoverySmartWallet.storingKeys": "Sinhronizirano v oblaku: pristopni ključ je varno shranjen v iCloudu, Google Upravitelju gesel ali v tvojem upravitelju gesel.", "backupRecoverySmartWallet.title": "Varnostno kopiranje in obnovitev Smart Wallet", "balance-change.card.titile": "Kartica", "balanceChange.pending": "V teku", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Izberi ponudnika storitev", "bank-transfer.change-deposit-receiver.subtitle": "Ta denarnica bo prejela vse bančne pologe", "bank-transfer.change-deposit-receiver.title": "Nastavi denarnico za prejemanje", "bank-transfer.change-owner.subtitle": "S to denarnico se prijaviš in obnoviš svoj račun za bančne prenose.", "bank-transfer.change-owner.title": "Nastavi lastnika računa", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Ta denarnica bo prejela vse tvoje bančne pologe v Zeal.", "bank-transfer.configrm-change-deposit-receiver.title": "Spremeni denarnico za prejemanje", "bank-transfer.configrm-change-owner.subtitle": "<PERSON>, da <PERSON><PERSON><PERSON> lastnika računa? Ta denarnica se uporablja za prijavo in obnovitev tvojega računa za bančne prenose.", "bank-transfer.configrm-change-owner.title": "Spremeni lastnika računa", "bank-transfer.deposit.widget.status.complete": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.deposit.widget.status.funds_received": "Sredstva prejeta", "bank-transfer.deposit.widget.status.sending_to_wallet": "Pošiljanje v denarnico", "bank-transfer.deposit.widget.status.transfer-on-hold": "Prenos na čakanju", "bank-transfer.deposit.widget.status.transfer-received": "Pošiljanje v denarnico", "bank-transfer.deposit.widget.subtitle": "{from} v {to}", "bank-transfer.deposit.widget.title": "<PERSON><PERSON>", "bank-transfer.intro.bulletlist.point_1": "Nastavitev z Unblock", "bank-transfer.intro.bulletlist.point_2": "Prenos med EUR/GBP in več kot 10 žetoni", "bank-transfer.intro.bulletlist.point_3": "0 % provizije do 5 tisoč $ mesečno, nato 0,2 %", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Pošiljanje na banko", "bank-transfer.withdrawal.widget.status.in-progress": "<PERSON>z<PERSON><PERSON><PERSON>", "bank-transfer.withdrawal.widget.status.on-hold": "Prenos na čakanju", "bank-transfer.withdrawal.widget.status.success": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.withdrawal.widget.subtitle": "{from} v {to}", "bank-transfer.withdrawal.widget.title": "Izplač<PERSON>", "bank-transfers.bank-account-actions.remove-this-account": "Odstrani ta račun", "bank-transfers.bank-account-actions.switch-to-this-account": "Preklopi na ta račun", "bank-transfers.deposit.fees-for-less-than-5k": "Provizije za 5 tisoč $ ali manj", "bank-transfers.deposit.fees-for-more-than-5k": "Provizije za več kot 5 tisoč $", "bank-transfers.set-receiving-bank.title": "Nastavi banko za prejemanje", "bank-transfers.settings.account_owner": "Lastnik računa", "bank-transfers.settings.receiver_of_bank_deposits": "Prejemnik bančnih pologov", "bank-transfers.settings.receiver_of_withdrawals": "Prejemnik izplačil", "bank-transfers.settings.registered_email": "Registriran e-poštni naslov", "bank-transfers.settings.title": "Nastavitve bančnega nakazila", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} račun", "bank-transfers.setup.bank-account": "Bančni račun", "bankTransfer.withdraw.max_loading": "Maks.: {amount}", "bank_details_do_not_match.got_it": "Razumem", "bank_details_do_not_match.subtitle": "Koda banke in številka računa se ne ujemata. Preveri, ali so podatki pravilno vneseni, in poskusi znova.", "bank_details_do_not_match.title": "Bančni podatki se ne ujemajo", "bank_tranfsers.select_country_of_residence.country_not_supported": "Žal bančni prenosi v {country} še niso podprti.", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Odpri svojo bančno aplikacijo", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Pošlji {fiatCurrencyCode} na svoj račun", "bank_transfer.deposit.header": "{fullName}''s podatki o osebnem&nbsp;ra<PERSON><PERSON>u", "bank_transfer.kyc_status_widget.subtitle": "Bančna nakazila", "bank_transfer.kyc_status_widget.title": "Preverjanje identitete", "bank_transfer.personal_details.date_of_birth": "<PERSON><PERSON> r<PERSON>", "bank_transfer.personal_details.date_of_birth.invalid_format": "<PERSON><PERSON> je nevel<PERSON>ven", "bank_transfer.personal_details.date_of_birth.too_young": "<PERSON><PERSON><PERSON> biti star vs<PERSON> 18 let.", "bank_transfer.personal_details.first_name": "Ime", "bank_transfer.personal_details.last_name": "Priimek", "bank_transfer.personal_details.title": "T<PERSON>ji podat<PERSON>", "bank_transfer.reference.label": "Referenca (neobvezno)", "bank_transfer.reference_message": "Poslano iz Zeal", "bank_transfer.residence_details.address": "Tvoj naslov", "bank_transfer.residence_details.city": "Mesto", "bank_transfer.residence_details.country_of_residence": "Država bivanja", "bank_transfer.residence_details.country_placeholder": "Država", "bank_transfer.residence_details.postcode": "Poštna številka", "bank_transfer.residence_details.street": "Ulica", "bank_transfer.residence_details.your_residence": "T<PERSON>je <PERSON>", "bank_transfers.choose-wallet.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.test": "<PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.warning.subtitle": "Naenkrat lahko povežeš samo eno denarnico. Povezane denarnice ne boš mogel spremeniti.", "bank_transfers.choose-wallet.warning.title": "Premišljeno izberi denarnico", "bank_transfers.choose_wallet.subtitle": "Izberi denarnico za neposredna nakazila. ", "bank_transfers.choose_wallet.title": "Izberi denarnico", "bank_transfers.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.currency_is_currently_not_supported": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit-header": "<PERSON><PERSON>", "bank_transfers.deposit.account-name": "<PERSON><PERSON>", "bank_transfers.deposit.account-number-copied": "Številka računa kopirana", "bank_transfers.deposit.amount-input": "Znesek za polog", "bank_transfers.deposit.amount-output": "Ciljni znesek", "bank_transfers.deposit.amount-output.error": "napaka", "bank_transfers.deposit.buttet-point.receive-crypto": "Prejmi {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.currency-not-supported.subtitle": "Bančni pologi iz {code} so do nadaljnjega onemogočeni.", "bank_transfers.deposit.currency-not-supported.title": "{code} <PERSON>gi trenutno niso podprti", "bank_transfers.deposit.default-token.balance": "<PERSON><PERSON> {amount}", "bank_transfers.deposit.deposit-header": "<PERSON><PERSON>", "bank_transfers.deposit.enter_amount": "Vnesi znesek", "bank_transfers.deposit.iban-copied": "IBAN kopiran", "bank_transfers.deposit.increase-amount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pre<PERSON> je {limit}", "bank_transfers.deposit.loading": "Nalaganje", "bank_transfers.deposit.max-limit-reached": "Znesek presega največjo omejitev prenosa", "bank_transfers.deposit.modal.kyc.button-text": "Začni", "bank_transfers.deposit.modal.kyc.text": "Za preverjanje tvoje identitete bomo potrebovali nekaj osebnih podatkov in dokumentacijo. Oddaja običajno traja le nekaj minut.", "bank_transfers.deposit.modal.kyc.title": "Preveri svojo identiteto za višje limite", "bank_transfers.deposit.reduce_amount": "Zmanjšaj znesek", "bank_transfers.deposit.show-account.account-number": "Številka računa", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Koda banke", "bank_transfers.deposit.sort-code-copied": "Koda banke kopirana", "bank_transfers.deposit.withdraw-header": "<PERSON><PERSON><PERSON>", "bank_transfers.failed_to_load_fee": "Neznano", "bank_transfers.fees": "Provizije", "bank_transfers.increase-amount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pre<PERSON> je {limit}", "bank_transfers.insufficient-funds": "Nezadostna s<PERSON>", "bank_transfers.select_country_of_residence.title": "<PERSON><PERSON>?", "bank_transfers.setup.cta": "<PERSON><PERSON><PERSON> prenose", "bank_transfers.setup.enter-amount": "Vnesi znesek", "bank_transfers.source_of_funds.form.business_income": "Poslovni dohodek", "bank_transfers.source_of_funds.form.other": "Drugo", "bank_transfers.source_of_funds.form.pension": "Pokojnina", "bank_transfers.source_of_funds.form.salary": "P<PERSON>ča", "bank_transfers.source_of_funds.form.title": "T<PERSON>j vir sredstev", "bank_transfers.source_of_funds_description.placeholder": "Opiši vir sredstev...", "bank_transfers.source_of_funds_description.title": "Povej nam več o svojem viru sredstev", "bank_transfers.withdraw-header": "<PERSON><PERSON><PERSON>", "bank_transfers.withdraw.amount-input": "Znesek za dvig", "bank_transfers.withdraw.max-limit-reached": "Znesek presega največjo omejitev prenosa", "bank_transfers.withdrawal.verify-id": "Zmanjšaj znesek", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Presežena je najvišja omejitev vnosa", "banner.above_maximum_limit.maximum_limit_per_deposit": "To je najvi<PERSON><PERSON> omejitev na polog", "banner.above_maximum_limit.subtitle": "Presežena je najvišja omejitev vnosa", "banner.above_maximum_limit.title": "Zmanjšaj znesek na {amount} ali manj", "banner.above_maximum_limit.title.default": "Zmanjšaj znesek", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Najnižja omejitev vnosa ni dosežena", "banner.below_minimum_limit.minimum_limit_for_token": "To je najn<PERSON><PERSON><PERSON> omejitev za ta žeton", "banner.below_minimum_limit.title": "Povečaj znesek na {amount} ali več", "banner.below_minimum_limit.title.default": "Povečaj znesek", "breaard.in_porgress.info_popup.cta": "Porabi in zasluži {earn}", "breaard.in_porgress.info_popup.footnote": "Z uporabo denarnice Zeal in kartice Gnosis Pay se strinjaš s pogoji te nagradne kampanje.", "breaward.in_porgress.info_popup.bullet_point_1": "Porabi {remaining} v na<PERSON><PERSON><PERSON><PERSON> {time} , da prev<PERSON><PERSON><PERSON> to nagrado.", "breaward.in_porgress.info_popup.bullet_point_2": "Za porabljen znesek se štejejo samo veljavni nakupi s kartico Gnosis Pay.", "breaward.in_porgress.info_popup.bullet_point_3": "<PERSON> boš prevzel nagrado, jo bomo poslali na tvoj račun Zeal.", "breaward.in_porgress.info_popup.header": "<PERSON><PERSON><PERSON><PERSON><PERSON> {earn} s porabo {remaining}", "breward.celebration.for_spending": "<PERSON><PERSON> <PERSON> s tvojo kartico", "breward.dc25-eligible-celebration.for_spending": "Si med prvimi {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "Nisi med prvimi {limit} po porabi", "breward.expired_banner.earn_by_spending": "<PERSON><PERSON><PERSON><PERSON><PERSON> {earn} s porabo {amount}", "breward.expired_banner.reward_expired": "{earn} nagrada je potekla", "breward.in_progress_banner.cta.title": "Porabi in zasluži {earn}", "breward.ready_to_claim.error.try_again": "Poskusi znova", "breward.ready_to_claim.error_title": "Prevzem nagrade ni uspel", "breward.ready_to_claim.in_progress": "Prevzemanje nagrade", "breward.ready_to_claim.youve_earned": "<PERSON><PERSON><PERSON><PERSON><PERSON> si {earn}!", "breward_already_claimed.title": "Nagrada je že prevzeta. Če nisi prejel žetona nagrade, se obrni na podporo.", "breward_cannotbe_claimed.title": "Nagrade trenutno ni mogoče prevzeti. Poskusi znova pozneje.", "bridge.best_return": "Pot z najboljšim donosom", "bridge.best_serivce_time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pot", "bridge.check_status.complete": "<PERSON><PERSON><PERSON><PERSON>", "bridge.check_status.progress_text": "Premostitev {from} v {to}", "bridge.remove_topup": "Odstrani polnitev", "bridge.request_status.completed": "<PERSON><PERSON><PERSON><PERSON>", "bridge.request_status.pending": "V teku", "bridge.widget.completed": "<PERSON><PERSON><PERSON><PERSON>", "bridge.widget.currencies": "{from} v {to}", "bridge_rote.widget.title": "Most", "browse.discover_more_apps": "Odk<PERSON><PERSON> ve<PERSON> aplikacij", "browse.google_search_term": "<PERSON><PERSON><PERSON><PERSON> »{searchTerm}«", "brward.celebration.you_earned": "<PERSON>as<PERSON><PERSON><PERSON> si", "brward.expired_banner.subtitle": "Več sreče prihodnjič", "brward.in_progress_banner.subtitle": "Poteče čez {expiredInFormatted}", "buy": "<PERSON><PERSON>", "buy.enter_amount": "Vnesi znesek", "buy.loading": "Nalaganje ...", "buy.no_routes_found": "Ni najdenih poti", "buy.not_enough_balance": "Nezadostno stanje", "buy.select-currency.title": "Izberi žeton", "buy.select-to-currency.title": "<PERSON><PERSON>", "buy_form.title": "<PERSON><PERSON>", "cancelled-card.create-card-button.primary": "Naroči novo virtualno kartico", "cancelled-card.switch-card-button.primary": "Zamenjaj kartico", "cancelled-card.switch-card-button.short-text": "<PERSON><PERSON><PERSON> eno aktivno kartico", "card": "Kartica", "card-add-cash.confirm-stage.banner.no-routes-found": "<PERSON>, poskusi z drugim žetonom ali zneskom", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Potrebuješ {amount} ve<PERSON> {symbol} za plačilo omrežnine", "card-add-cash.confirm-stage.banner.value-loss": "<PERSON><PERSON><PERSON><PERSON> boš {loss} v<PERSON><PERSON><PERSON>", "card-add-cash.confirm-stage.banner.value-loss.revert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.cancel": "Prekliči", "card-add-cash.edit-stage.cta.continue": "<PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.enter-amount": "Vnesi znesek", "card-add-cash.edit-stage.cta.reduce-to-max": "Na<PERSON>vi max", "card-add-cash.edit-staget.banner.no-routes-found": "<PERSON>, poskusi z drugim žetonom ali zneskom", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Potrdi transakcijo na strojni denarnici.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Podpiši s strojno denarnico", "card-balance": "<PERSON><PERSON>: {balance}", "card-cashback.status.title": "Polog v Vračilo denarja", "card-copy-safe-address.copy_address": "<PERSON><PERSON><PERSON>", "card-copy-safe-address.copy_address.done": "<PERSON><PERSON><PERSON>", "card-copy-safe-address.warning.description": "Ta naslov lahko prejme samo {cardAsset} v verigi Gnosis. Na ta naslov ne pošiljaj sredstev iz drugih omrežij. Izgubljena bodo.", "card-copy-safe-address.warning.header": "Pošlji samo {cardAsset} v verigi Gnosis", "card-marketing-card.center.subtitle": "Provizije FX", "card-marketing-card.center.title": "0 %", "card-marketing-card.left.subtitle": "<PERSON><PERSON><PERSON>", "card-marketing-card.right.subtitle": "<PERSON><PERSON> ob prijavi", "card-marketing-card.title": "Evropska VISA kartica z visokimi obrestmi", "card-marketing-tile.get-started": "Začni", "card-select-from-token-title": "Izberi žeton za polog", "card-top-up.banner.subtitle.completed": "<PERSON><PERSON><PERSON><PERSON>", "card-top-up.banner.subtitle.failed": "Neuspešno", "card-top-up.banner.subtitle.pending": "{timerString} V teku", "card-top-up.banner.title": "<PERSON><PERSON><PERSON> {amount}", "card-topup.select-token.emptyState": "Nismo našli noben<PERSON>", "card.activate.card_number_not_valid": "Napačna številka kartice. Poskusi znova.", "card.activate.invalid_card_number": "Neveljavna številka kartice.", "card.activation.activate_physical_card": "Aktiviraj kartico", "card.add-cash.amount-to-withdraw": "Znesek za naložitev", "card.add-from-earn-form.title": "Dodaj denar na kartico", "card.add-from-earn-form.withdraw-to-card": "<PERSON><PERSON><PERSON><PERSON>", "card.add-from-earn.amount-to-withdraw": "Znesek za dvig na kartico", "card.add-from-earn.enter-amount": "Vnesi znesek", "card.add-from-earn.loading": "Nalaganje", "card.add-from-earn.max-label": "Stanje: {amount}", "card.add-from-earn.no-routes-found": "Ni najdenih poti", "card.add-from-earn.not-enough-balance": "Nezadostno stanje", "card.add-owner.queued": "Dodajanje lastnika v čakalni vrsti", "card.add-to-wallet-flow.subtitle": "Plačuj iz svoje denarnice", "card.add-to-wallet.copy-card-number": "<PERSON><PERSON><PERSON> spodnjo številko kartice", "card.add-to-wallet.title": "Dodaj v {platformName} denarnico", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "PREKLICANO", "card.card-owner-not-found.disconnect-btn": "Prekini povezavo s Zeal", "card.card-owner-not-found.subtitle": "Za uporabo v Zeal posodobi lastnika kartice in jo ponovno poveži.", "card.card-owner-not-found.title": "Ponovno poveži kartico", "card.card-owner-not-found.update-owner-btn": "Posodobi lastnika kartice", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} v {date}", "card.cashback.widgetNoCashback.subtitle": "Položi sredstva in začni služiti", "card.cashback.widgetNoCashback.title": "Prejmi do {defaultPercentage} vračila denarja", "card.cashback.widgetcashbackValue.rewards": "{amount} v teku", "card.cashback.widgetcashbackValue.title": "{percentage} vrač<PERSON> denarja", "card.choose-wallet.connect_card": "Poveži kartico", "card.choose-wallet.create-new": "Dodaj novo denarnico kot lastnika", "card.choose-wallet.import-another-wallet": "Uvozi drugo denarnico", "card.choose-wallet.import-current-owner": "Uvozi trenutnega lastnika kartice", "card.choose-wallet.import-current-owner.sub-text": "Uvozi zasebne ključe ali izvorno frazo, ki je lastnik tvoje kartice Gnosis Pay", "card.choose-wallet.title": "Izberi denarnico za upravljanje kartice", "card.connectWalletToCardGuide": "<PERSON><PERSON><PERSON> denarnic<PERSON>", "card.connectWalletToCardGuide.addGnosisPayOwner": "<PERSON><PERSON><PERSON><PERSON>", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Odpri Gnosispay.com s svojo drugo denarnico{br}2. Klik<PERSON> na »Račun«{br}3. Klikni na »Podrobnosti računa«{br}4. Klikni na »Uredi« zraven »Lastnik računa« in{br}5. <PERSON><PERSON><PERSON> na »Dodaj naslov«{br}6. Prilepi svoj Zeal naslov in klikni na shrani", "card.connectWalletToCardGuide.header": "<PERSON><PERSON><PERSON><PERSON> {account} s kartico Gnosis Pay", "card.connect_card.start": "Poveži kartico Gnosis Pay", "card.copiedAddress": "<PERSON><PERSON><PERSON> {formattedAddress}", "card.disconnect-account.title": "Prekini povezavo z računom", "card.hw-wallet-support-drop.add-owner-btn": "Dodaj novega lastnika", "card.hw-wallet-support-drop.disconnect-btn": "Prekini povezavo s Zeal", "card.hw-wallet-support-drop.subtitle": "Za uporabo v Zeal dodaj lastnika, ki ni strojna denarnica.", "card.hw-wallet-support-drop.title": "Zeal ne podpira več strojnih denarnic za kartico.", "card.kyc.continue": "Nadaljuj z nastavitvijo", "card.list_item.title": "Kartica", "card.onboarded.transactions.empty.description": "Tvoja plačilna dejavnost bo prikazana tukaj", "card.onboarded.transactions.empty.title": "Dejavnost", "card.order.continue": "<PERSON><PERSON><PERSON><PERSON>", "card.order.free_virtual_card": "Naroči brezpl. virtualno", "card.order.start": "Naroči brezplačno kartico", "card.owner-not-imported.cancel": "Prekliči", "card.owner-not-imported.import": "Uvozi račun", "card.owner-not-imported.subtitle": "Za avtorizacijo te transakcije poveži lastniško denarnico svojega računa Gnosis Pay z Zeal. Opomba: To je ločeno od tvoje običajne prijave v denarnico Gnosis Pay.", "card.owner-not-imported.title": "Dodaj lastnika računa Gnosis Pay", "card.page.order_free_physical_card": "Naroči brezplačno fizično", "card.pin.change_pin_at_atm": "Kodo PIN lahko spremeniš na izbranih bankomatih", "card.pin.timeout": "<PERSON><PERSON><PERSON> se bo zap<PERSON> čez {seconds} s", "card.quick-actions.add-assets": "<PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.add-cash": "<PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.details": "<PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.freeze": "<PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.freezing": "Zamrzovanje", "card.quick-actions.unfreeze": "Odmrzni", "card.quick-actions.unfreezing": "Odmrzovanje", "card.quick-actions.withdraw": "<PERSON><PERSON><PERSON>", "card.read-only-detected.create-new": "Dodaj novo denarnico kot lastnika", "card.read-only-detected.import-current-owner": "Uvozi ključe za {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Uvozi zasebne ključe ali izvorno frazo denarnice {address}", "card.read-only-detected.title": "Zaznana kartica v denarnici samo za branje. Izberi denarnico za upravljanje kartice.", "card.remove-owner.queued": "Odstranitev lastnika v čakalni vrsti", "card.settings.disconnect-from-zeal": "Prekini povezavo z Zeal", "card.settings.edit-owners": "Spremeni lastnike kartice", "card.settings.getCard": "Naroči novo kartico", "card.settings.getCard.subtitle": "Virtualne ali fizične kartice", "card.settings.notRecharging": "Samodejno polnjenje izklopljeno", "card.settings.notifications.subtitle": "Prejemaj obvestila o plačilih", "card.settings.notifications.title": "O<PERSON><PERSON><PERSON><PERSON> kartice", "card.settings.page.title": "Nastavitve kartice", "card.settings.select-card.cancelled-cards": "Preklicane kartice", "card.settings.setAutoRecharge": "Na<PERSON>vi sa<PERSON>jno polnjenje", "card.settings.show-card-address": "Prikaži naslov kartice", "card.settings.spend-limit": "Nastavi limit porabe", "card.settings.spend-limit-title": "Trenutni dnevni limit: {limit}", "card.settings.switch-active-card": "Zamenjaj aktivno kartico", "card.settings.switch-active-card-description": "Aktivna kartica: {card}", "card.settings.switch-card.card-item.cancelled": "Preklicano", "card.settings.switch-card.card-item.frozen": "Zamrznjeno", "card.settings.switch-card.card-item.title": "Kartica <PERSON>", "card.settings.switch-card.card-item.title.physical": "Fizična kartica", "card.settings.switch-card.card-item.title.virtual": "Virtualna kartica", "card.settings.switch-card.title": "Izberi kartico", "card.settings.targetBalance": "Ciljno stanje: {threshold}", "card.settings.view-pin": "Prikaži PIN", "card.settings.view-pin-description": "Vedno zaščiti svojo kodo PIN", "card.title": "Kartica", "card.transactions.header": "Transakcije s kartico", "card.transactions.see_all": "Prikaži vse transakcije", "card.virtual": "VIRTUALNA", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Vračilo denarja je na tvojo kartico poslano na začetku tedna po tistem, v katerem si ga zaslužil.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "Več kot polo<PERSON>, več z<PERSON> z vsakim nakupom.", "cardCashback.onboarding.title": "Prejmi do {percentage} vračila denarja", "cardCashbackWithdraw.amount": "Znesek dviga", "cardCashbackWithdraw.header": "Dvig {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "Kartica je blokirana in je ni bilo mogoče aktivirati", "cardWidget.cashback": "Vračilo denar<PERSON>", "cardWidget.cashbackUpToDefaultPercentage": "Do {percentage}", "cardWidget.startEarning": "Začni služiti", "cardWithdraw.amount": "Znesek dviga", "cardWithdraw.header": "<PERSON><PERSON><PERSON> s kartice", "cardWithdraw.selectWithdrawWallet.title": "<PERSON>zberi denarnico za{br}dvig sredstev", "cardWithdraw.success.cta": "<PERSON><PERSON><PERSON>", "cardWithdraw.success.subtitle": "<PERSON><PERSON><PERSON> varnosti obdelava vseh dvigov s kartice Gnosis Pay traja 3 minute", "cardWithdraw.success.title": "Ta sprememba bo trajala 3 minute", "card_top_up_trx.send": "Pošl<PERSON>š", "card_top_up_trx.to": "Prejemnik", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "<PERSON><PERSON>", "cards.card_number": "Številka kartice", "cards.choose-wallet.no-active-accounts": "<PERSON><PERSON><PERSON> aktivnih denarnic", "cards.copied_card_number": "Številka kartice kopirana", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Presega dnevni limit.", "cards.transactions.decline_reason.incorrect_pin": "Napačen PIN", "cards.transactions.decline_reason.incorrect_security_code": "Napačna varnostna koda", "cards.transactions.decline_reason.invalid_amount": "Neveljaven znesek", "cards.transactions.decline_reason.low_balance": "Premalo sredstev", "cards.transactions.decline_reason.other": "Zavrnjeno", "cards.transactions.decline_reason.pin_tries_exceeded": "Preseženo število poskusov PIN", "cards.transactions.status.refund": "Povrač<PERSON>", "cards.transactions.status.reversal": "Stornacija", "cashback-deposit.trx.title": "Polog v Vračilo denarja", "cashback-estimate.text": "To je ocena in NE zajamčeno izplačilo. Upoštevana so vsa javno znana pravila za vračilo denarja, vendar lahko Gnosis Pay po lastni presoji izključi transakcije. Najvišja poraba v vi<PERSON><PERSON> {amount} na teden se upošteva za vračilo denarja, tudi če bi ocena za to transakcijo kazala na višji skupni znesek.", "cashback-estimate.text.fallback": "To je ocena in NE zajamčeno izplačilo. Upoštevana so vsa javno znana pravila za vračilo denarja, vendar lahko Gnosis Pay po lastni presoji izključi transakcije.", "cashback-estimate.title": "Ocena vračila denarja", "cashback-onbarding-tersm.subtitle": "Podatki o transakcijah tvoje kartice bodo deljeni s <PERSON><PERSON><PERSON><PERSON><PERSON>, ki je odgovoren za razdeljevanje nagrad za vračilo denarja. S klikom na »Sprejmi« potr<PERSON><PERSON>š, da za Gnosis DAO Cashback veljajo <terms>Pogoji in določila</terms>", "cashback-onbarding-tersm.title": "Pogoji uporabe in zasebnost", "cashback-tx-activity.retry": "Poskusi znova", "cashback-unconfirmed-payments-info.subtitle": "Plačila so upravičena do vračila denarja, ko so poravnana s trgovcem. Do takrat so prikazana kot nepotrjena plačila. Neporavnana plačila niso upravičena do vračila denarja.", "cashback-unconfirmed-payments-info.title": "Nepotrjena plačila s kartico", "cashback.activity.cashback": "Vračilo denar<PERSON>", "cashback.activity.deposit": "<PERSON><PERSON>", "cashback.activity.title": "<PERSON><PERSON><PERSON>", "cashback.activity.withdrawal": "<PERSON><PERSON><PERSON>", "cashback.deposit": "<PERSON><PERSON><PERSON>", "cashback.deposit.amount.label": "Znesek pologa", "cashback.deposit.change": "{from} v {to}", "cashback.deposit.confirmation.subtitle": "Stopnje vračila denarja se posodobijo enkrat na teden. <PERSON><PERSON><PERSON> z<PERSON>j, da povečaš vračilo denarja za naslednji teden.", "cashback.deposit.confirmation.title": "<PERSON><PERSON><PERSON><PERSON> bo<PERSON> {percentage} na dan {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Zamenjaj žetone v {currency} v verigi {network} ", "cashback.deposit.get.tokens.title": "Pridobi žetone {currency} ", "cashback.deposit.header": "Polog {currency}", "cashback.deposit.max_label": "Maks.: {amount}", "cashback.deposit.select-wallet.title": "Izberi denarnico za polog", "cashback.deposit.yourcashback": "Tvoje vračilo denarja", "cashback.header": "Vračilo denar<PERSON>", "cashback.selectWithdrawWallet.title": "<PERSON>zberi denarnico za {br}dvig", "cashback.transaction-details.network-label": "Omrežje", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "Od", "cashback.transaction-details.top-row.label-rewards": "Obdobje vračila denarja", "cashback.transaction-details.top-row.label-withdrawal": "<PERSON>a", "cashback.transaction-details.transaction": "ID transakcije", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Transakcije vračila denarja", "cashback.withdraw": "<PERSON><PERSON><PERSON>", "cashback.withdraw.confirmation.cashback_reduction": "Vračilo denarja za ta teden, vk<PERSON><PERSON><PERSON><PERSON> s tem, kar si že <PERSON>, se bo z<PERSON> z {before} na {after}", "cashback.withdraw.queued": "Dvig v čakalni vrsti", "cashback.withdrawal.change": "{from} na {to}", "cashback.withdrawal.confirmation.subtitle": "Začni z dvigom {amount} s 3-minutno zakasnitvijo. To bo znižalo tvoje vračilo denarja na {after}.", "cashback.withdrawal.confirmation.title": "Vračilo denarja se bo <PERSON>, če dvigneš GNO", "cashback.withdrawal.delayTransaction.title": "Začni dvig GNO s{br} 3-min<PERSON><PERSON> zakasnitvijo", "cashback.withdrawal.withdraw": "Izplačaj", "cashback.withdrawal.yourcashback": "Tvoje vračilo denarja", "celebration.aave": "Zasluženo z Aave", "celebration.cashback.subtitle": "Izplačano v {code}", "celebration.cashback.subtitleGNO": "{amount} zadnje zasluženo", "celebration.chf": "Zasluženo s Frankencoinom", "celebration.lido": "Zasluženo z Lido", "celebration.sky": "Zasluženo s Sky", "celebration.title": "Skupno vračilo denarja", "celebration.well_done.title": "Odlično!", "change-withdrawal-account.add-new-account": "Dodaj nov bančni račun", "change-withdrawal-account.item.shortText": "{currency} račun", "check-confirmation.approve.footer.for": "<PERSON>a", "checkConfirmation.title": "Rezultat transakcije", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin in Ether", "collateral.ethereum": "Ethereum", "collateral.other": "Drugo", "collateral.rwa": "Realna sredstva", "collateral.stablecoins": "Stabilni k<PERSON>nci (vezani na USD)", "collateral.us-t-bills": "Ameriške zakladne menice", "confirm-bank-transfer-recipient.bullet-1": "Brez provizij za digitalni EUR", "confirm-bank-transfer-recipient.bullet-2": "Pologi v {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Deli podatke o računu Gnosis Pay z Moneriumom, poo<PERSON><PERSON><PERSON><PERSON> in regulirano institucijo za elektronski denar (EMI). <link>Več o tem</link>", "confirm-bank-transfer-recipient.bullet-4": "<PERSON><PERSON><PERSON><PERSON><PERSON> <link>pogoje uporabe</link>", "confirm-bank-transfer-recipient.title": "Sprejmi pogoje", "confirm-change-withdrawal-account.cancel": "Prekliči", "confirm-change-withdrawal-account.confirm": "<PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.saving": "Shranjujem", "confirm-change-withdrawal-account.subtitle": "Vsi dvigi iz Zeala bodo prejeti na ta bančni račun.", "confirm-change-withdrawal-account.title": "Spremeni banko za prejemanje", "confirm-ramove-withdrawal-account.title": "Odstrani bančni račun", "confirm-remove-withdrawal-account.subtitle": "Podatki o tem bančnem računu bodo odstranjeni iz Zeala. Lahko ga ponovno dodaš kadar<PERSON>.", "confirmTransaction.finalNetworkFee": "Omrežnina", "confirmTransaction.importKeys": "Uvozi ključe", "confirmTransaction.networkFee": "Omrežnina", "confirmation.title": "<PERSON><PERSON><PERSON><PERSON> {amount} za {recipient}", "conflicting-monerium-account.add-owner": "<PERSON><PERSON>j kot lastnika Gnosis Pay", "conflicting-monerium-account.create-wallet": "Ustvari novo pametno denarnico", "conflicting-monerium-account.disconnect-card": "Odklopi kartico iz Zeal in jo ponovno poveži z novim lastnikom", "conflicting-monerium-account.header": "{wallet} povezan z drugim računom Monerium", "conflicting-monerium-account.subtitle": "Zamenjaj last<PERSON>ško denarnico Gnosis Pay", "connection.diconnected.got_it": "Razumem!", "connection.diconnected.page1.subtitle": "Zeal deluje povsod, kjer deluje Metamask. Preprosto se poveži kot z Metamaskom.", "connection.diconnected.page1.title": "<PERSON>ko se povežem z Zeal?", "connection.diconnected.page2.subtitle": "Prikazalo se bo veliko možnosti. Zeal bo morda ena izmed njih. Če se Zeal ne pojavi ...", "connection.diconnected.page2.title": "Klikni Poveži denarnico", "connection.diconnected.page3.subtitle": "Pozvali te bomo k povezavi z Zeal. Delovati bi morali tudi brskalnik ali »Injected«. Poskusi!", "connection.diconnected.page3.title": "Izberi Metamask", "connectionSafetyCheck.tag.caution": "Previd<PERSON>", "connectionSafetyCheck.tag.danger": "<PERSON><PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.passed": "Opravljeno", "connectionSafetyConfirmation.subtitle": "<PERSON> res ž<PERSON>?", "connectionSafetyConfirmation.title": "Ta stran je videti nevarna", "connection_state.connect.cancel": "Prekliči", "connection_state.connect.changeToMetamask": "Preklopi na MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Preklopi na MetaMask", "connection_state.connect.connect_button": "Poveži", "connection_state.connect.expanded.connected": "Povezano", "connection_state.connect.expanded.title": "Poveži", "connection_state.connect.safetyChecksLoading": "Preverjam varnost strani", "connection_state.connect.safetyChecksLoadingError": "Ni bilo mogoče preveriti varnosti", "connection_state.connected.expanded.disconnectButton": "Prekini povezavo z Zeal", "connection_state.connected.expanded.title": "Povezano", "copied-diagnostics": "Diagnostika kopirana", "copy-diagnostics": "<PERSON><PERSON><PERSON>", "counterparty.component.add_recipient_primary_text": "<PERSON><PERSON>j <PERSON>ga prejemnika", "counterparty.country": "Država", "counterparty.countryTitle": "Država prejemnika", "counterparty.currency": "Valuta", "counterparty.delete.success.title": "Odstranjeno", "counterparty.edit.success.title": "Spremembe shran<PERSON>ne", "counterparty.errors.country_required": "Država je obvezna", "counterparty.errors.first_name.invalid": "<PERSON>me mora biti dalj<PERSON>e", "counterparty.errors.last_name.invalid": "Priimek mora biti daljši", "counterparty.first_name": "Ime", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Pošlji na banko", "countrySelector.noCountryFound": "Ni najdenih držav", "countrySelector.title": "Izberi državo", "create-passkey.cta": "Ustvari pristopni ključ", "create-passkey.extension.cta": "<PERSON><PERSON><PERSON><PERSON>", "create-passkey.footnote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create-passkey.mobile.cta": "Nastavi varnostni ključ", "create-passkey.steps.enable-recovery": "Nastavi obnovitev v oblaku", "create-passkey.steps.setup-biometrics": "Omogoči biometrično varnost", "create-passkey.subtitle": "Pristopni ključi so varnejši od gesel in so šifrirani v shrambi v oblaku za lažjo obnovitev.", "create-passkey.title": "Zavaruj račun", "create-smart-wallet": "Ustvari Smart Wallet", "create-userop.progress.text": "Ustvarjan<PERSON>", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Nadaljuj v Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Pojdi na Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "Naročilo kartice si že začel. Pojdi na spletno stran Gnosis Pay, da ga dokončaš.", "create_recharge_preferences.card": "Kartica", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "<PERSON><PERSON><PERSON><PERSON><PERSON> {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "Zadrži denar", "create_recharge_preferences.link_accounts_title": "Poveži račune", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "Tvoja kartica se NE bo samodejno polnila po vsakem plačilu.", "create_recharge_preferences.not_configured_title": "Zasluži in porabi", "create_recharge_preferences.recharge_from_earn_accounts_description": "Tvoja kartica se po vsakem plačilu samodejno napolni iz tvojega Earn računa.", "create_recharge_preferences.subtitle": "na leto", "creating-account.loading": "Ustvarjanje računa", "creating-gnosis-pay-account": "Ustvarjanje računa", "currencies.bridge.select_routes.emptyState": "Za ta most nismo našli nobenih poti", "currency.add_currency.add_token": "<PERSON><PERSON><PERSON>", "currency.add_currency.not_a_valid_address": "To ni veljaven naslov žetona", "currency.add_currency.token_decimals_feild": "Decimalke žetona", "currency.add_currency.token_feild": "Na<PERSON>lov žetona", "currency.add_currency.token_symbol_feild": "Simbol žetona", "currency.add_currency.update_token": "Posodobi žeton", "currency.add_custom.remove_token.cta": "Odstrani", "currency.add_custom.remove_token.header": "Odstrani žeton", "currency.add_custom.remove_token.subtitle": "Tvoja denarnica bo še vedno hranila stanje tega žetona, vendar bo skrito v portfelju Zeal.", "currency.add_custom.token_removed": "Žeton odstranjen", "currency.add_custom.token_updated": "Žeton posodobljen", "currency.balance_label": "Stanje: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "<PERSON><PERSON><PERSON> banč<PERSON> prenos je uspešno prenesel {fiat} v {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "<PERSON><PERSON><PERSON> si {crypto}", "currency.bankTransfer.deposit_status.success": "Prejeto v tvojo denarnico", "currency.bankTransfer.deposit_status.title": "<PERSON><PERSON>", "currency.bankTransfer.off_ramp.check_bank_account": "Preveri svoj bančni račun", "currency.bankTransfer.off_ramp.complete": "<PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Pošiljanje na tvojo banko", "currency.bankTransfer.off_ramp.transferring_to_currency": "Prenos v {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Sredstva bi morala biti že na tvojem bančnem računu.", "currency.bankTransfer.withdrawal_status.success": "Poslano na tvojo banko", "currency.bankTransfer.withdrawal_status.title": "Izplač<PERSON>", "currency.bank_transfer.create_unblock_user.email": "E-poštni naslov", "currency.bank_transfer.create_unblock_user.email_invalid": "Neveljavna e-pošta", "currency.bank_transfer.create_unblock_user.email_missing": "Obvezno", "currency.bank_transfer.create_unblock_user.first_name": "Ime", "currency.bank_transfer.create_unblock_user.first_name_missing": "Obvezno", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "Dovoljene so samo <PERSON>, <PERSON><PERSON><PERSON><PERSON>, pre<PERSON><PERSON><PERSON> in - . , & ( ) '.", "currency.bank_transfer.create_unblock_user.last_name": "Priimek", "currency.bank_transfer.create_unblock_user.last_name_missing": "Obvezno", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "Dovoljene so samo <PERSON>, <PERSON><PERSON><PERSON><PERSON>, pre<PERSON><PERSON><PERSON> in - . , & ( ) '.", "currency.bank_transfer.create_unblock_user.note": "Z nadaljevanjem s<PERSON><PERSON><PERSON><PERSON><PERSON> (naš bančni partner) <terms>Pogoje uporabe</terms> in <policy>Pravilnik o zasebnosti</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Vpiši svoje ime natančno tako, kot je na tvojem bančnem računu.", "currency.bank_transfer.create_unblock_user.title": "Poveži svoj bančni račun", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Številka računa", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Država banke", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Prednostna valuta", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Koda banke", "currency.bank_transfer.create_unblock_withdraw_account.success": "Ra<PERSON><PERSON> je nastavljen", "currency.bank_transfer.create_unblock_withdraw_account.title": "Poveži svoj bančni račun", "currency.bank_transfer.residence-form.address-required": "Obvezno polje", "currency.bank_transfer.residence-form.address-unsupported-char": "Dovoljene so samo <PERSON>, <PERSON><PERSON><PERSON><PERSON>, presle<PERSON>ki in , ; {apostrophe} - \\\\.", "currency.bank_transfer.residence-form.city-required": "Obvezno polje", "currency.bank_transfer.residence-form.city-unsupported-char": "<PERSON><PERSON><PERSON><PERSON><PERSON> so le <PERSON>, <PERSON><PERSON><PERSON><PERSON>, pre<PERSON><PERSON><PERSON> in . , - & ( ) {apostrophe} .", "currency.bank_transfer.residence-form.postcode-invalid": "Neveljavna poštna številka", "currency.bank_transfer.residence-form.postcode-required": "Obvezno polje", "currency.bank_transfer.validation.invalid.account_number": "Neveljavna številka računa", "currency.bank_transfer.validation.invalid.iban": "Neveljaven IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Neveljavna koda banke", "currency.bridge.amount_label": "Znesek za premostitev", "currency.bridge.best_returns.subtitle": "Ta ponudnik mostu ti bo zagotovil najvišji izkupiček, vključno z vsemi provizijami.", "currency.bridge.best_returns_popup.title": "Najboljši don<PERSON>i", "currency.bridge.bridge_from": "Iz", "currency.bridge.bridge_gas_fee_loading_failed": "Pri nalaganju omrežnine je prišlo do težav", "currency.bridge.bridge_low_slippage": "Zelo nizek zdrs. Poskusi ga povečati", "currency.bridge.bridge_provider": "Ponudnik prenosa", "currency.bridge.bridge_provider_loading_failed": "Pri nalaganju ponudnikov je prišlo do težav", "currency.bridge.bridge_settings": "Nastavitve mostu", "currency.bridge.bridge_status.subtitle": "<PERSON><PERSON> {name}", "currency.bridge.bridge_status.title": "Most", "currency.bridge.bridge_to": "V", "currency.bridge.fastest_route_popup.subtitle": "Ta ponudnik mostu ti bo zagotovil najhitrejšo pot transakcije.", "currency.bridge.fastest_route_popup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pot", "currency.bridge.from": "Iz", "currency.bridge.success": "<PERSON><PERSON><PERSON><PERSON>", "currency.bridge.title": "Most", "currency.bridge.to": "V", "currency.bridge.topup": "<PERSON><PERSON><PERSON> {symbol}", "currency.bridge.withdrawal_status.title": "<PERSON><PERSON><PERSON>", "currency.card.card_top_up_status.title": "Dodaj denar na kartico", "currency.destination_amount": "Ciljni znesek", "currency.hide_currency.confirm.subtitle": "Skrij ta žeton iz svojega portfelja. Kadarkoli ga lahko znova prikažeš.", "currency.hide_currency.confirm.title": "Sk<PERSON>j <PERSON>", "currency.hide_currency.success.title": "Žeton skrit", "currency.label": "Oznaka (neobvezno)", "currency.last_name": "Priimek", "currency.max_loading": "Maks.:", "currency.swap.amount_to_swap": "Znesek za zamenjavo", "currency.swap.best_return": "Pot z najboljšim donosom", "currency.swap.destination_amount": "Ciljni znesek", "currency.swap.header": "Zamenjaj", "currency.swap.max_label": "Stanje: {amount}", "currency.swap.provider.header": "Ponudnik zamenjave", "currency.swap.select_to_token": "Izberi žeton", "currency.swap.swap_gas_fee_loading_failed": "Pri nalaganju omrežnine je prišlo do težav", "currency.swap.swap_provider_loading_failed": "Pri nalaganju ponudnikov je prišlo do težav", "currency.swap.swap_settings": "Nastavitve zamenjave", "currency.swap.swap_slippage_too_low": "Zelo nizek zdrs. Poskusi ga povečati.", "currency.swaps_io_native_token_swap.subtitle": "Preko Swaps.IO", "currency.swaps_io_native_token_swap.title": "<PERSON><PERSON><PERSON>", "currency.withdrawal.amount_from": "Iz", "currency.withdrawal.amount_to": "V", "currencySelector.title": "Izberi valuto", "dApp.wallet-does-not-support-chain.subtitle": "<PERSON><PERSON><PERSON>, da tvoja denarnica ne podpira {network}. Poveži drugo denarnico ali uporabi Zeal.", "dApp.wallet-does-not-support-chain.title": "Nepodprto omrežje", "dapp.connection.manage.confirm.disconnect.all.cta": "Prekini vse", "dapp.connection.manage.confirm.disconnect.all.subtitle": "<PERSON> res ž<PERSON><PERSON> vse povezave?", "dapp.connection.manage.confirm.disconnect.all.title": "Prekini vse povezave", "dapp.connection.manage.connection_list.main.button.title": "Prekini povezavo", "dapp.connection.manage.connection_list.no_connections": "Ni<PERSON>š povezanih aplikacij", "dapp.connection.manage.connection_list.section.button.title": "Prekini vse povezave", "dapp.connection.manage.connection_list.section.title": "Aktivne", "dapp.connection.manage.connection_list.title": "<PERSON><PERSON><PERSON>", "dapp.connection.manage.disconnect.success.title": "<PERSON><PERSON><PERSON>", "dapp.metamask_mode.title": "<PERSON><PERSON><PERSON>", "dc25-card-marketing-card.center.subtitle": "Vračilo denar<PERSON>", "dc25-card-marketing-card.center.title": "4 %", "dc25-card-marketing-card.left.subtitle": "<PERSON><PERSON><PERSON>", "dc25-card-marketing-card.right.subtitle": "100 ljudi", "dc25-card-marketing-card.title": "Prvih 100, ki porabi 50 €, prejme {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Tega dejanja ne boš mogel izvesti 3 minute. Zaradi varnostnih razlogov obdelava sprememb nastavitev kartice ali dvigov traja 3 minute.", "delayQueueBusyBanner.processing-yout-action.title": "Obdel<PERSON>jem tvoje de<PERSON>, prosim poč<PERSON>j", "delayQueueBusyWidget.cardFrozen": "Kartica je zamrznjena", "delayQueueBusyWidget.processingAction": "Obdelovanje tvojega dejanja", "delayQueueFailedBanner.action-incomplete.get-support": "Poišči pomoč", "delayQueueFailedBanner.action-incomplete.subtitle": "P<PERSON>šlo je do napake pri dvigu ali posodobitvi nastavitev. Za pomoč se obrni na podporo na Discordu.", "delayQueueFailedBanner.action-incomplete.title": "Dejanje ni zaključeno", "delayQueueFailedWidget.actionIncomplete.title": "Dejanje s kartico ni dokončano", "delayQueueFailedWidget.cardFrozen.subtitle": "Kartica je zamrznjena", "delayQueueFailedWidget.contactSupport": "Obrni se na podporo", "delay_queue_busy.subtitle": "Zaradi varnosti traja obdelava sprememb nastavitev kartice ali dvigov 3 minute. Medtem je tvoja kartica zamrznjena.", "delay_queue_busy.title": "Tvoje dejanje je v obdelavi", "delay_queue_failed.contact_support": "Podpora", "delay_queue_failed.subtitle": "P<PERSON>šlo je do napake pri dvigu ali posodobitvi nastavitev. Za pomoč se obrni na podporo na Discordu.", "delay_queue_failed.title": "Obrni se na podporo", "deploy-earn-form-smart-wallet.in-progress.title": "Priprava storitve E<PERSON>n", "deposit": "<PERSON><PERSON>", "disconnect-card-popup.cancel": "Prekliči", "disconnect-card-popup.disconnect": "Odk<PERSON><PERSON>", "disconnect-card-popup.subtitle": "S tem boš odstranil kartico iz aplikacije Zeal. Tvoja denarnica bo še vedno povezana s kartico v aplikaciji Gnosis Pay. Kartico lahko kadar koli ponovno povežeš.", "disconnect-card-popup.title": "Odklopi kartico", "distance.long.days": "{count} dni", "distance.long.hours": "{count} ur", "distance.long.minutes": "{count} minut", "distance.long.months": "{count} mesecev", "distance.long.seconds": "{count} sekund", "distance.long.years": "{count} let", "distance.short.days": "{count} d", "distance.short.hours": "{count} h", "distance.short.minutes": "{count} min", "distance.short.months": "{count} m", "distance.short.seconds": "{count} sek", "distance.short.years": "{count} l", "duration.short.days": "{count}d", "duration.short.hours": "{count}h", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "<PERSON><PERSON>", "earn-deposit-view.into": "V", "earn-deposit-view.to": "<PERSON>a", "earn-deposit.swap.transfer-provider": "Ponudnik prenosa", "earn-taker-investment-details.accrued-realtime": "Narašča v realnem času", "earn-taker-investment-details.asset-class": "Naložbeni razred", "earn-taker-investment-details.asset-coverage-ratio": "Razmerje kritja sredstev", "earn-taker-investment-details.asset-reserve": "Rezerva sredstev", "earn-taker-investment-details.base_currency.label": "Osnovna valuta", "earn-taker-investment-details.chf.description": "Zasluži obresti na svoje CHF z vplačilom zCHF v Frankencoin – zaupanja vreden digitalni denarni trg. Obresti se ustvarjajo iz nizko tveganih, prekomerno zavarovanih posojil na Frankencoinu in so izplačane v realnem času. Tvoja sredstva so varna na varnem podračunu, ki ga upravljaš samo ti.", "earn-taker-investment-details.chf.description.with_address_link": "Zasluži obresti na svoje CHF z vplačilom zCHF v Frankencoin – zaupanja vreden digitalni denarni trg. Obresti se ustvarjajo iz nizko tveganih, prekomerno zavarovanih posojil na Frankencoinu in so izplačane v realnem času. Tvoja sredstva so varna na varnem podračunu <link>(kopiraj 0x)</link> , ki ga upravljaš samo ti.", "earn-taker-investment-details.chf.label": "<PERSON><PERSON> frank", "earn-taker-investment-details.collateral-composition": "Sestava zavarovanja", "earn-taker-investment-details.depositor-obligations": "Obveznosti do vlagateljev", "earn-taker-investment-details.eure.description": "Zasluži obresti na svoje evre z vlaganjem EURe v Aave – zaupanja vreden digitalni denarni trg. EURe je v celoti reguliran evrski stabilni kovanec, ki ga izdaja Monerium in ima kritje 1:1 na varovanih računih. Obresti se ustvarjajo z nizko tveganimi, prekomerno zavarovanimi posojili na Aave in se izplačujejo v realnem času. Tvoja sredstva ostanejo na varnem podračunu, ki ga nadzoruješ samo ti.", "earn-taker-investment-details.eure.description.with_address_link": "Zasluži obresti na svoje evre z vlaganjem EURe v Aave – zaupanja vreden digitalni denarni trg. EURe je v celoti reguliran evrski stabilni kovanec, ki ga izdaja Monerium in ima kritje 1:1 na varovanih računih. Obresti se ustvarjajo z nizko tveganimi, prekomerno zavarovanimi posojili na Aave in se izplačujejo v realnem času. Tvoja sredstva ostanejo na varnem podračunu <link>(kopiraj 0x)</link> , ki ga nadzoruješ samo ti.", "earn-taker-investment-details.eure.label": "Digitalni evro (EURe)", "earn-taker-investment-details.faq": "Pogosta vprašanja", "earn-taker-investment-details.fixed-income": "<PERSON><PERSON>ni don<PERSON>", "earn-taker-investment-details.issuer": "Izdajatelj", "earn-taker-investment-details.key-facts": "Ključna dejstva", "earn-taker-investment-details.liquidity": "Likvidnost", "earn-taker-investment-details.operator": "Upravljavec trga", "earn-taker-investment-details.projected-yield": "Predviden letni donos", "earn-taker-investment-details.see-other-faq": "O<PERSON>j si vsa ostala pogosta vprašanja", "earn-taker-investment-details.see-realtime": "Oglej si podatke v realnem času", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Donos v zadnjih 12 mesecih", "earn-taker-investment-details.total-collateral": "Skupno zavarovanje", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "Skupna ponudba ZCHF", "earn-taker-investment-details.total_deposits": "Skupni pologi v Aave", "earn-taker-investment-details.usd.description": "Sky je digitalni denarni trg, ki ponuja stabilne donose v ameriških dolarjih iz kratkoročnih ameriških zakladnih menic in prekomerno zavarovanih posojil – brez kripto volatilnosti, z dostopom do sredstev 24/7 in s preglednim kritjem na verigi.", "earn-taker-investment-details.usd.description.with_address_link": "Sky je digitalni denarni trg, ki ponuja stabilne donose v ameriških dolarjih iz kratkoročnih ameriških zakladnih menic in prekomerno zavarovanih posojil – brez kripto volatilnosti, z dostopom do sredstev 24/7 in s preglednim kritjem na verigi. Naložbe so na podračunu <link>(kopiraj 0x)</link> , ki ga nadzoruješ ti.", "earn-taker-investment-details.usd.ftx-difference": "V čem se to razlikuje od FTX, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ali <PERSON>?", "earn-taker-investment-details.usd.high-returns": "<PERSON><PERSON> so lahko donosi tako visoki, še posebej v primerjavi s tradicionalnimi bankami?", "earn-taker-investment-details.usd.how-is-backed": "Kakšno je kritje za Sky USD in kaj se zgodi z mojim denarjem, če Zeal bankrotira?", "earn-taker-investment-details.usd.income-sources": "Viri prihodkov 2024", "earn-taker-investment-details.usd.insurance": "Ali so moja sredstva zavarovana ali zajamčena s strani katere koli institucije (kot je FDIC ali podobno)?", "earn-taker-investment-details.usd.label": "<PERSON>ni ameriš<PERSON> dolar", "earn-taker-investment-details.usd.lose-principal": "Ali lahko realno izgubim svojo glavnico in pod kakšnimi pogoji?", "earn-taker-investment-details.variable-rate": "Posojanje s spremenljivo obrestno mero", "earn-taker-investment-details.withdraw-anytime": "<PERSON><PERSON><PERSON>", "earn-taker-investment-details.yield": "Donos", "earn-withdrawal-view.approve.for": "<PERSON>a", "earn-withdrawal-view.approve.into": "V", "earn-withdrawal-view.swap.into": "V", "earn-withdrawal-view.withdraw.to": "Prejemnik", "earn.add_another_asset.title": "Izberi sredstvo za Earn", "earn.add_asset": "Dodaj sredstvo", "earn.asset_view.title": "Zasluži", "earn.base-currency-popup.text": "Osnovna valuta je valuta, v kateri so ovrednoteni in zabeleženi tvoji pologi, don<PERSON><PERSON> in transakcije. Če nakažeš sredstva v drugi valuti (na primer EUR v USD), se ta takoj pretvorijo v osnovno valuto po trenutnem menjalnem tečaju. Po pretvorbi ostane tvoje stanje stabilno v osnovni valuti, vendar lahko prihodnji dvigi ponovno vključujejo pretvorbe valut.", "earn.base-currency-popup.title": "Osnovna valuta", "earn.card-recharge.disabled.list-item.title": "Samodejno polnjenje <PERSON>", "earn.card-recharge.enabled.list-item.title": "Samodejno polnjenje omogočeno", "earn.choose_wallet_to_deposit.title": "Položi iz", "earn.config.currency.eth": "Zasluži z Ethereumom", "earn.config.currency.on_chain_address_subtitle": "Naslov na verigi", "earn.config.currency.us_dollars": "<PERSON><PERSON><PERSON> prenose", "earn.configured_widget.current_apy.title": "Trenutni APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} Letno", "earn.confirm.currency.cta": "<PERSON><PERSON><PERSON>", "earn.currency.eth": "Zasluži Ethereum", "earn.deploy.status.title": "Ustvari Earn ra<PERSON>", "earn.deploy.status.title_with_taker": "<PERSON><PERSON><PERSON><PERSON> ra<PERSON> {title} za donose", "earn.deposit": "<PERSON><PERSON>", "earn.deposit.amount_to_deposit": "Znesek pologa", "earn.deposit.deposit": "<PERSON><PERSON>", "earn.deposit.enter_amount": "Vnesi znesek", "earn.deposit.no_routes_found": "Ni najdenih poti", "earn.deposit.not_enough_balance": "Nezadostno stanje", "earn.deposit.select-currency.title": "Izberi žeton za polog", "earn.deposit.select_account.title": "Izberi račun Earn", "earn.desposit_form.title": "Polog v Earn", "earn.earn_deposit.status.title": "Polog v Donose", "earn.earn_deposit.trx.title": "Polog v Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "<PERSON><PERSON><PERSON> s<PERSON> kadar<PERSON>", "earn.earn_withdraw.status.title": "Dvig z računa za donose", "earn.earn_withdraw.trx.title.approval": "<PERSON><PERSON><PERSON> dvig", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Dvig v {asset}", "earn.earn_withdraw.trx.title.withdrawal": "Dvig iz Earn", "earn.recharge.cta": "<PERSON><PERSON><PERSON> sprem<PERSON>", "earn.recharge.earn_not_configured.enable_some_account.error": "Omogoči račun", "earn.recharge.earn_not_configured.enter_amount.error": "Vnesi znesek", "earn.recharge.select_taker.header": "Napolni kartico po vrstnem redu iz", "earn.recharge_card_tag.on": "vklopljeno", "earn.recharge_card_tag.recharge": "Polnjenje", "earn.recharge_card_tag.recharge_not_configured": "Samodejno polnjenje", "earn.recharge_card_tag.recharge_off": "Polnjenje izklopljeno", "earn.recharge_card_tag.recharged": "Napolnjeno", "earn.recharge_card_tag.recharging": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.recharge_configured.disable.trx.title": "Onemogoči samodejno polnjenje", "earn.recharge_configured.trx.disclaimer": "<PERSON> uporabiš kartico, se ustvari dražba Cowswap za nakup enakega zneska, kot je tvoje plačilo, z uporabo tvojih sredstev Earn. Ta postopek dražbe ti običajno zagotovi najboljši tržni tečaj, vendar se zavedaj, da se lahko tečaj v verigi razlikuje od realnih menjalnih tečajev.", "earn.recharge_configured.trx.subtitle": "Po vsakem plačilu bo denar samodejno dodan z tvojih računov Earn, da se ohrani stanje na kartici na {value}", "earn.recharge_configured.trx.title": "Nastavi sa<PERSON>dejno polnjenje na {value}", "earn.recharge_configured.updated.trx.title": "Shrani nastavitve polnjenja", "earn.risk-banner.subtitle": "To je produkt z zasebnim skrbništvom brez regulativne zaščite pred izgubo.", "earn.risk-banner.title": "<PERSON><PERSON><PERSON> t<PERSON>a", "earn.set_recharge.status.title": "Na<PERSON>vi sa<PERSON>jno polnjenje", "earn.setup_reacharge.input.disable.label": "One<PERSON><PERSON><PERSON><PERSON>", "earn.setup_reacharge.input.label": "Ciljno stanje kartice", "earn.setup_reacharge_form.title": "Samodejno polnjenje ohranja tvojo{br} kartico na enakem stanju", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Hrani wstETH (Staked ETH) na verigi Gnosis in posojaj preko Lido.", "earn.taker-bulletlist.point_1": "Zasluži {apyValue} letno. Donosi se spreminjajo s trgom.", "earn.taker-bulletlist.point_3": "Zeal ne zaračunava provizij.", "earn.taker-historical-returns": "Zgodovinski donosi", "earn.taker-historical-returns.chf": "Rast CHF v primerjavi z USD", "earn.taker-investment-tile.apy.perYear": "na leto", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "<PERSON><PERSON><PERSON>", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Služenje z Aave na verigi Gnosis", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Zaslužek s Frankencoinom na verigi Gnosis", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Služenje z Lido na verigi Gnosis", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Služenje z Maker na verigi Gnosis", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "Earn <PERSON>", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH Earn", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "Ameriške zakladnice (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Čeprav je izjemno redko, je teoretično mogoče. Tvoja sredstva so za<PERSON><PERSON><PERSON>na s strogim obvladovanjem tveganj in visokim zavarovanjem. Realističen najslabši scenarij bi vključeval tržne razmere brez primere, na primer, da bi več stabilnih kovancev hkrati izgubilo svojo vezavo – kar se še nikoli ni zgodilo.", "earn.usd.can-I-lose-my-principal-popup.title": "Ali lahko realno izgubim svojo glavnico in pod kakšnimi pogoji?", "earn.usd.ftx-difference-popup.text": "Sky je bistveno drugačen. <PERSON>a raz<PERSON> od FTX, <PERSON><PERSON><PERSON>, BlockFi ali Luna, ki so se močno zanašali na centralizirano skrbništvo, netransparentno upravljanje sredstev in tvegane pozicije s finančnim vzvodom, Sky USD uporablja pregledne, revidirane, decentralizirane pametne pogodbe in ohranja polno preglednost na verigi. Ohraniš popoln nadzor nad svojo zasebno denarnico, kar z<PERSON>no zmanjša tveganja nasprotne stranke, povezana z napakami centraliziranih sistemov.", "earn.usd.ftx-difference-popup.title": "V čem se to razlikuje od FTX, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ali <PERSON>?", "earn.usd.high-returns-popup.text": "Sky USD ustvarja donose predvsem preko protokolov decentraliziranih financ (DeFi), ki avtomatizirajo medsebojno posojanje in zagotavljanje likvidnosti ter tako odpravljajo režijske stroške tradicionalnega bančništva in posrednike. Ta učinkovitost v kombinaciji z zanesljivim obvladovanjem tveganj omogoča znatno višje donose v primerjavi s tradicionalnimi bankami.", "earn.usd.high-returns-popup.title": "<PERSON><PERSON> so lahko donosi tako visoki, še posebej v primerjavi s tradicionalnimi bankami?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD ima polno kritje in je prekomerno zavarovan s kombinacijo digitalnih sredstev v varnih pametnih pogodbah in sredstev iz resničnega sveta, kot so ameriške zakladne menice. Rezerve je mogoče revidirati v realnem času na verigi, tudi znotraj aplikacije Zeal, kar zagotavlja preglednost in varnost. V malo verjetnem primeru, da bi Zeal prenehal delovati, tvoja sredstva ostanejo zavarovana na verigi, pod tvojim popolnim nadzorom in dostopna preko drugih združljivih denarnic.", "earn.usd.how-is-sky-backed-popup.title": "Kakšno je kritje za Sky USD in kaj se zgodi z mojim denarjem, če Zeal bankrotira?", "earn.usd.insurance-popup.text": "Sredstva v Sky USD niso zavarovana s strani FDIC ali podprta s tradicionalnimi vladnimi jamstvi, ker gre za račun, ki temelji na digitalnih sredstvih, in ne za običajen bančni račun. Namesto tega Sky obvladuje vsa tveganja preko revidiranih pametnih pogodb in skrbno preverjenih DeFi protokolov, kar <PERSON><PERSON>, da sredstva ostanejo varna in pregledna.", "earn.usd.insurance-popup.title": "Ali so moja sredstva zavarovana ali zajamčena s strani katere koli institucije (kot je FDIC ali podobno)?", "earn.usd.lending-operations-popup.text": "Sky USD ustvarja donos s posojanjem stabilnih kovancev preko decentraliziranih posojilnih trgov, kot sta Morpho in Spark. Tvoji stabilni kovanci so posojeni posojilojemalcem, ki polo<PERSON>i<PERSON> znatno več zavarovanja – kot sta ETH ali BTC – od vrednosti njihovega posojila. <PERSON>, im<PERSON><PERSON> pre<PERSON>avarovanje, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da je vedno na voljo dovolj zavarovanja za kritje posojil, kar močno zmanj<PERSON> tveganje. <PERSON><PERSON><PERSON> obresti in občasne likvidacijske provizije, ki jih pla<PERSON>, zagotavljajo zanesljive, pregledne in varne donose.", "earn.usd.lending-operations-popup.title": "Posojilne <PERSON>", "earn.usd.market-making-operations-popup.text": "Sky USD ustvarja dodaten donos s sodelovanjem na decentraliziranih borzah (AMM), kot sta Curve ali Uniswap. Z zagotavljanjem likvidnosti – z vlaganjem tvojih stabilnih kovancev v sklade, ki omogočajo trgovanje s kriptovalutami – Sky USD pridobiva provizije, ustvarjene s posli. Ti likvidnostni skladi so skrbno izbrani za zmanjšanje volatilnosti, pri čemer se uporabljajo predvsem pari stabilnih kovancev, da se znatno zmanjšajo tveganja, kot je nestalna izguba, in tako ohranijo tvoja sredstva varna in dostopna.", "earn.usd.market-making-operations-popup.title": "Dejavnosti ustvarjanja trga", "earn.usd.treasury-operations-popup.text": "Sky USD ustvarja stabilen in dosleden donos s strateškimi naložbami v zakladništvo. Del tvojih vlog v stabilnih kovancih je dodeljen varnim, nizko tveganim sredstvom iz resničnega sveta – predvsem kratkoročnim državnim obveznicam in visoko varnim kreditnim instrumentom. Ta pristop, podoben tradicionalnemu bančništvu, zagotavlja predvidljiv in zanesljiv donos. Tvoja sredstva ostajajo varna, likvidna in pregledno upravljana.", "earn.usd.treasury-operations-popup.title": "Zakladniške dejavnosti", "earn.view_earn.card_rechard_off": "<PERSON><PERSON><PERSON><PERSON>", "earn.view_earn.card_rechard_on": "Vklop", "earn.view_earn.card_recharge": "Polnjenje kartice", "earn.view_earn.total_balance_label": "<PERSON><PERSON><PERSON><PERSON>ek {percentage} na leto", "earn.view_earn.total_earnings_label": "Skupni zaslužek", "earn.withdraw": "<PERSON><PERSON><PERSON>", "earn.withdraw.amount_to_withdraw": "Znesek za dvig", "earn.withdraw.enter_amount": "Vnesi znesek", "earn.withdraw.loading": "Nalaganje", "earn.withdraw.no_routes_found": "Ni najdenih poti", "earn.withdraw.not_enough_balance": "Ni dovolj sredstev", "earn.withdraw.select-currency.title": "Izberi žeton", "earn.withdraw.select_to_token": "Izberi žeton", "earn.withdraw.withdraw": "<PERSON><PERSON><PERSON>", "earn.withdraw_form.title": "Dvig iz Earn", "earnings-view.earnings": "Skupni zaslužek", "edit-account-owners.add-owner.add-wallet": "<PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.add_wallet": "<PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.title": "<PERSON><PERSON><PERSON>a kartice", "edit-account-owners.card-owners": "Lastniki kartice", "edit-account-owners.external-wallet": "Zunanja denarnica", "editBankRecipient.title": "<PERSON><PERSON><PERSON>", "editNetwork.addCustomRPC": "Dodaj RPC vozlišče po meri", "editNetwork.cannot_verify.subtitle": "RPC vozlišče po meri se ne odziva. Preveri URL in poskusi znova.", "editNetwork.cannot_verify.title": "Ne moremo preveriti vozlišča RPC", "editNetwork.cannot_verify.try_again": "<PERSON><PERSON><PERSON>", "editNetwork.customRPCNode": "RPC vozlišče po meri", "editNetwork.defaultRPC": "Privzeti RPC", "editNetwork.networkRPC": "Omrežni RPC", "editNetwork.rpc_url.cannot_be_empty": "Obvezno", "editNetwork.rpc_url.not_a_valid_https_url": "<PERSON><PERSON><PERSON> m<PERSON> veljaven HTTP(S) URL", "editNetwork.safetyWarning.subtitle": "Zeal ne more zagotoviti zasebnosti, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in varnosti RPC-jev po meri. <PERSON>, da <PERSON><PERSON><PERSON> uporabiti RPC vozlišče po meri?", "editNetwork.safetyWarning.title": "RPC-ji po meri so lahko ne<PERSON>", "editNetwork.zealRPCNode": "RPC vozlišče Zeal", "editNetworkRpc.headerTitle": "RPC vozlišče po meri", "editNetworkRpc.rpcNodeUrl": "URL RPC vozlišča", "editing-locked.modal.description": "Za razliko od transakcij odobritve, dovoljenja ne omogočajo urejanja omejitve porabe ali časa poteka. Preden oddaš do<PERSON>, se <PERSON><PERSON><PERSON><PERSON><PERSON>, da z<PERSON><PERSON>š aplikaciji dApp.", "editing-locked.modal.title": "<PERSON><PERSON><PERSON><PERSON>len<PERSON>", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Omogočanje polnjenja", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Polnjenje o<PERSON>čeno", "enterCardnumber": "Vnesi številko kartice", "error.connectivity_error.subtitle": "Preveri svojo internetno povezavo in poskusi znova.", "error.connectivity_error.title": "Ni internetne povezave", "error.decrypt_incorrect_password.title": "Napačno geslo", "error.encrypted_object_invalid_format.title": "Poškodovani podatki", "error.failed_to_fetch_google_auth_token.title": "Nismo mogli pridobiti dostopa", "error.list.item.cta.action": "Poskusi znova", "error.trezor_action_cancelled.title": "Transakcija zavrnjena", "error.trezor_device_used_elsewhere.title": "Naprava se uporablja v drugi seji", "error.trezor_method_cancelled.title": "Trezorja ni bilo mogoče sinhronizirati", "error.trezor_permissions_not_granted.title": "Trezorja ni bilo mogoče sinhronizirati", "error.trezor_pin_cancelled.title": "Trezorja ni bilo mogoče sinhronizirati", "error.trezor_popup_closed.title": "Trezorja ni bilo mogoče sinhronizirati", "error.unblock_account_number_and_sort_code_mismatch": "Številka računa in koda banke se ne ujemata", "error.unblock_can_not_change_details_after_kyc": "Po KYC postopku spremembe niso mogoče.", "error.unblock_hard_kyc_failure": "Nepričakovano stanje KYC", "error.unblock_invalid_faster_payment_configuration.title": "Ta banka ne podpira storitve Faster Payments", "error.unblock_invalid_iban": "Neveljaven IBAN", "error.unblock_session_expired.title": "<PERSON><PERSON> je potekla", "error.unblock_user_with_address_already_exists.title": "Račun za ta naslov je že nastavljen", "error.unblock_user_with_such_email_already_exists.title": "Uporabnik s tem e-poštnim naslovom že obstaja", "error.unknown_error.error_message": "Sporočilo o napaki: ", "error.unknown_error.subtitle": "Oprosti! Če potrebuješ nujno pomoč, se obrni na podporo in jim posreduj spodnje podatke.", "error.unknown_error.title": "Sistemska napaka", "eth-cost-warning-modal.subtitle": "Pametne denarnice delujejo na omrežju Ethereum, vendar so omrežnine zelo visoke in MOČNO priporočamo uporabo drugih omrežij.", "eth-cost-warning-modal.title": "Izogibaj se omrežju Ethereum - omrežnine so visoke", "exchange.form.button.chain_unsupported": "Omrežje ni podprto", "exchange.form.button.refreshing": "Osveževanje", "exchange.form.error.asset_not_supported.button": "Izberi drugo sredstvo", "exchange.form.error.asset_not_supported.description": "Most ne podpira premostitve tega sredstva.", "exchange.form.error.asset_not_supported.title": "Sredstvo ni podrto", "exchange.form.error.bridge_quote_timeout.button": "Izberi drugo sredstvo", "exchange.form.error.bridge_quote_timeout.description": "Poskusi z drugim parom žetonov", "exchange.form.error.bridge_quote_timeout.title": "<PERSON><PERSON>va ni najdena", "exchange.form.error.different_receiver_not_supported.button": "Odstrani drugega prejemnika", "exchange.form.error.different_receiver_not_supported.description": "Ta menjava ne podpira pošiljanja na drug naslov.", "exchange.form.error.different_receiver_not_supported.title": "Naslov za pošiljanje in prejemanje mora biti isti", "exchange.form.error.insufficient_input_amount.button": "Povečaj znesek", "exchange.form.error.insufficient_liquidity.button": "Zmanjšaj znesek", "exchange.form.error.insufficient_liquidity.description": "Most nima dovolj sredstev. Poskusi z manjšim zneskom.", "exchange.form.error.insufficient_liquidity.title": "Znesek je previsok", "exchange.form.error.max_amount_exceeded.button": "Zmanjšaj znesek", "exchange.form.error.max_amount_exceeded.description": "Najvišji znesek je bil presežen.", "exchange.form.error.max_amount_exceeded.title": "Znesek je previsok", "exchange.form.error.min_amount_not_met.button": "Povečaj znesek", "exchange.form.error.min_amount_not_met.description": "Najnižji znesek menjave za ta žeton ni dosežen.", "exchange.form.error.min_amount_not_met.description_with_amount": "Najnižji znesek menjave je {amount}.", "exchange.form.error.min_amount_not_met.title": "Znesek je prenizek", "exchange.form.error.min_amount_not_met.title_increase": "Povečaj znesek", "exchange.form.error.no_routes_found.button": "Izberi drugo sredstvo", "exchange.form.error.no_routes_found.description": "<PERSON>a to komb<PERSON><PERSON>jo ž<PERSON>a/om<PERSON>ž<PERSON> ni na voljo nobene poti menjave.", "exchange.form.error.no_routes_found.title": "Menjava ni na voljo", "exchange.form.error.not_enough_balance.button": "Zmanjšaj znesek", "exchange.form.error.not_enough_balance.description": "<PERSON><PERSON> to transakcijo nimaš dovolj tega sredstva.", "exchange.form.error.not_enough_balance.title": "Nezadostno stanje", "exchange.form.error.slippage_passed_is_too_low.button": "Povečaj zdrs", "exchange.form.error.slippage_passed_is_too_low.description": "Dovoljen zdrs je za to sredstvo prenizek.", "exchange.form.error.slippage_passed_is_too_low.title": "Z<PERSON>s je prenizek", "exchange.form.error.socket_internal_error.button": "Poskusi znova pozneje", "exchange.form.error.socket_internal_error.description": "Partner za premostitev ima težave. Poskusi znova pozneje.", "exchange.form.error.socket_internal_error.title": "Napaka pri partnerju za premostitev", "exchange.form.error.stargatev2_requires_fee_in_native": "<PERSON><PERSON><PERSON> {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Dodaj {amount} za dokončanje transakcije", "exchange.form.error.stargatev2_requires_fee_in_native.title": "Po<PERSON><PERSON><PERSON><PERSON>č {symbol}", "expiration-info.modal.description": "Čas poteka določa, kako dolgo lahko aplikacija uporablja tvoje žetone. Ko se čas izteče, i<PERSON><PERSON><PERSON><PERSON> dostop, dokler ne odobriš drugače. Za večjo varnost naj bo čas poteka kratek.", "expiration-info.modal.title": "Kaj je čas poteka?", "expiration-time.high.modal.text": "Čas poteka naj bo kratek in prilagojen dejanski potrebi. <PERSON><PERSON><PERSON> roki so tvegani, saj imajo golju<PERSON> več časa za zlorabo tvojih žetonov.", "expiration-time.high.modal.title": "<PERSON><PERSON><PERSON>", "failed.transaction.content": "Transakcija bo verjet<PERSON>", "fee.unknown": "Neznano", "feedback-request.leave-message": "Pusti sporočilo", "feedback-request.not-now": "Ne zdaj", "feedback-request.title": "H<PERSON>a! Kako lahko izboljšamo Zeal?", "float.input.period": "Decimalno <PERSON>ilo", "gnosis-activate-card.info-popup.subtitle": "Pri prvi transakciji moraš vstaviti kartico in vnesti PIN. Nato bodo delovala brezstična plačila.", "gnosis-activate-card.info-popup.title": "Prvo plačilo zah<PERSON>va čip in PIN", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "V<PERSON>i <PERSON> kart<PERSON>, da jo aktiv<PERSON>.", "gnosis-activate-card.title": "Številka kartice", "gnosis-pay-re-kyc-widget.btn-text": "<PERSON>ver<PERSON>", "gnosis-pay-re-kyc-widget.title.not-started": "Preveri svojo identiteto", "gnosis-pay.login.cta": "Poveži obstoječi račun", "gnosis-pay.login.title": "Račun Gnosis Pay že imaš", "gnosis-signup.confirm.subtitle": "Poišči e-pošto od Gnosis Pay. Morda se skriva med neželeno pošto.", "gnosis-signup.confirm.title": "<PERSON><PERSON> prejel potrditvene e-pošte?", "gnosis-signup.continue": "<PERSON><PERSON><PERSON><PERSON>", "gnosis-signup.dont_link_accounts": "Ne poveži računov", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "Vnesi <EMAIL>", "gnosis-signup.enter-email.title": "Vnesi e-pošto", "gnosis-signup.title": "Prebral sem in se strinjam s <linkGnosisTNC>Pogoji poslovanja</linkGnosisTNC> <monovateTerms>Pogoji za imetnika kartice</monovateTerms> in <linkMonerium>Pogoji poslovanja Monerium</linkMonerium>.", "gnosis-signup.verify-email.title": "Potrdi e-pošto", "gnosis.confirm.subtitle": "<PERSON>si prejel kode? Preveri, ali je telefonska številka pravilna.", "gnosis.confirm.title": "Koda poslana na {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "<PERSON>ver<PERSON>", "gnosisPayAccountStatus.success.title": "Kartica uvožena", "gnosisPayIsNotAvailableInThisCountry.title": "Storitev GnosisPay v tvoji državi še ni na voljo", "gnosisPayNoActiveCardsFound.title": "Ni aktivnih kartic", "gnosis_pay_card_delay_relay_not_empty_error.title": "Tvoje transakcije trenutno ni mogoče obdelati. Poskusi znova pozneje.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "Kartica ni na voljo", "gnosiskyc.modal.approved.activate-free-card": "Aktiviraj zastonj kartico", "gnosiskyc.modal.approved.button-text": "Polog z bančnega računa", "gnosiskyc.modal.approved.title": "Tvoji osebni podatki o računu so bili ustvarjeni", "gnosiskyc.modal.failed.close": "<PERSON><PERSON><PERSON>", "gnosiskyc.modal.failed.title": "Žal naš partner Gnosis Pay ne more ustvariti računa zate", "gnosiskyc.modal.in-progress.title": "Preverjanje identitete lahko traja 24 ur ali več. Prosimo za potrpljenje.", "goToSettingsPopup.settings": "Nastavitve", "goToSettingsPopup.title": "Obvestila lahko kadarkoli omogočiš v nastavitvah naprave", "google_file.error.failed_to_fetch_auth_token.button_title": "Poskusi znova", "google_file.error.failed_to_fetch_auth_token.subtitle": "Da bi lahko uporabili tvojo datoteko za obnovitev, dovoli dostop do svojega osebnega oblaka.", "google_file.error.failed_to_fetch_auth_token.title": "Nismo mogli pridobiti dostopa", "hidden_tokens.widget.emptyState": "<PERSON> skrit<PERSON>", "how_to_connect_to_metamask.got_it": "OK, razumem", "how_to_connect_to_metamask.story.subtitle": "<PERSON>ost<PERSON><PERSON> preklapljaj med <PERSON> in drugimi denarnicami kadarkoli.", "how_to_connect_to_metamask.story.title": "Zeal deluje skupaj z drugimi denarnicami", "how_to_connect_to_metamask.why_switch": "<PERSON><PERSON><PERSON> med <PERSON> in drugimi denarnicami?", "how_to_connect_to_metamask.why_switch.description": "<PERSON>e glede na to, katero denar<PERSON>, te bodo varnostna preverjanja Zeal vedno ščitila pred zlonamernimi stranmi in transakcijami.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "<PERSON><PERSON><PERSON>, da je težko narediti preskok in začeti uporabljati novo denarnico. Zato smo poenostavili uporabo denarnice Zeal poleg tvoje obstoječe. Preklopi kadarkoli.", "import-bank-transfer-owner.banner.title": "Za nadaljevanje bančnih prenosov uvozite spremenjeno denarnico.", "import-bank-transfer-owner.title": "Uvozi denarnico za uporabo bančnih prenosov na tej napravi", "import_gnosispay_wallet.add-another-card-owner.footnote": "Uvozi zasebni ključ ali izvorno frazo, ki je lastnik tvoje kartice Gnosis Pay", "import_gnosispay_wallet.primaryText": "Uvozi denarnico G<PERSON>", "injected-wallet": "Denarnica v brskalniku", "intercom.getHelp": "Poišči pomoč", "invalid_iban.got_it": "Razumem", "invalid_iban.subtitle": "Vneseni IBAN ni veljaven. Preveri, ali so podatki pravilno vneseni, in poskusi znova.", "invalid_iban.title": "Neveljaven IBAN", "keypad-0": "Tipka 0", "keypad-1": "Tipka 1", "keypad-2": "Tipka 2", "keypad-3": "Tipka 3", "keypad-4": "Tipka 4", "keypad-5": "Tipka 5", "keypad-6": "Tipka 6", "keypad-7": "Tipka 7", "keypad-8": "Tipka 8", "keypad-9": "Tipka 9", "keypad.biometric-button": "Biometrični gumb na tipkovnici", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Skrivna fraza je zavarovana 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "Ogled fraze", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Varno kopijo svoje Skrivne fraze hrani brez povezave, da bo<PERSON> lahko kasneje obnovil svoja sredstva.", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "Ne poskušaj uganiti besede", "keystore.write_secret_phrase.before_you_begin.first_point": "<PERSON><PERSON><PERSON><PERSON>, da <PERSON><PERSON><PERSON> vs<PERSON>, ki ima mojo Skrivno frazo, prenese moja sredstva.", "keystore.write_secret_phrase.before_you_begin.second_point": "Odgovoren sem za varovanje in zaupnost svoje Skrivne fraze.", "keystore.write_secret_phrase.before_you_begin.subtitle": "Prosimo, preberi in sprejmi naslednje točke:", "keystore.write_secret_phrase.before_you_begin.third_point": "Sem na zasebnem mestu, kjer ni ljudi ali kamer.", "keystore.write_secret_phrase.before_you_begin.title": "<PERSON>den z<PERSON>č<PERSON>š", "keystore.write_secret_phrase.secret_phrase_test.title": "<PERSON><PERSON> je beseda {count} v tvoji Skrivni frazi?", "keystore.write_secret_phrase.test_ps.lets_do_it": "Gremo", "keystore.write_secret_phrase.test_ps.subtitle": "Svojo Skrivno frazo boš potreboval za obnovitev računa na tej ali drugih napravah. Preverimo, ali je tvoja Skrivna fraza pravilno zapisana.", "keystore.write_secret_phrase.test_ps.subtitle2": "Vprašali te bomo po {count} besedah v tvoji frazi.", "keystore.write_secret_phrase.test_ps.title": "Preizkus obnovitve računa", "kyc.modal.approved.button-text": "Opravi ban<PERSON>", "kyc.modal.approved.subtitle": "Tvoje preverjanje je kon<PERSON>, z<PERSON>j lahko opravljaš neomejene bančne prenose.", "kyc.modal.approved.title": "Bančni prenosi odklenjeni", "kyc.modal.continue-with-partner.button-text": "<PERSON><PERSON><PERSON><PERSON>", "kyc.modal.continue-with-partner.subtitle": "Z<PERSON><PERSON> te bomo preusmerili k našemu partnerju, da zbere tvojo dokumentacijo in dokonča vlogo za preverjanje.", "kyc.modal.continue-with-partner.title": "Nadaljuj z našim partnerjem", "kyc.modal.failed.unblock.subtitle": "Unblock ni odobril tvojega preverjanja identitete in ti ne more zagotoviti storitev bančnih prenosov.", "kyc.modal.failed.unblock.title": "Vloga Unblock ni odobrena", "kyc.modal.paused.button-text": "Posodobi podatke", "kyc.modal.paused.subtitle": "<PERSON><PERSON> se, da so nekateri tvoji podatki napačni. Poskusi z<PERSON> in pred oddajo dvakrat preveri podatke.", "kyc.modal.paused.title": "Tvoji podatki so videti napačni", "kyc.modal.pending.button-text": "<PERSON><PERSON><PERSON>", "kyc.modal.pending.subtitle": "Preverjanje običajno traja manj kot 10 minut, v<PERSON><PERSON><PERSON> pa lahko traja malo dlje.", "kyc.modal.pending.title": "Obveščali te bomo", "kyc.modal.required.cta": "Začni preverjanje", "kyc.modal.required.subtitle": "Dosegel si omejitev transakcij. Za nadaljevanje potrdi svojo identiteto. To običajno traja nekaj minut in zahteva osebne podatke ter dokumentacijo.", "kyc.modal.required.title": "Potrebno preverjanje identitete", "kyc.submitted": "<PERSON><PERSON><PERSON> oddana", "kyc.submitted_short": "<PERSON><PERSON>", "kyc_status.completed_status": "<PERSON><PERSON><PERSON><PERSON>", "kyc_status.failed_status": "Neuspešno", "kyc_status.paused_status": "V pregledu", "kyc_status.subtitle": "Bančna nakazila", "kyc_status.subtitle.wrong_details": "Napačni podatki", "kyc_status.subtitle_in_progress": "V teku", "kyc_status.title": "Preverjanje identitete", "label.close": "<PERSON><PERSON><PERSON>", "label.saving": "Se shranjuje", "labels.this-month": "Ta mesec", "labels.today": "<PERSON><PERSON>", "labels.yesterday": "Včeraj", "language.selector.title": "<PERSON><PERSON><PERSON>", "ledger.account_loaded.imported": "Uvoženo", "ledger.add.success.title": "Ledger us<PERSON><PERSON><PERSON> povezan 🎉", "ledger.connect.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.connect.step1": "Poveži Ledger s svojo napravo", "ledger.connect.step2": "Odpri aplikacijo Ethereum na Ledgerju", "ledger.connect.step3": "<PERSON><PERSON> svoj Ledger 👇", "ledger.connect.subtitle": "Sledi tem korakom za uvoz svojih denarnic Ledger v Zeal", "ledger.connect.title": "Poveži Ledger z Zealom", "ledger.error.ledger_is_locked.subtitle": "Odkleni Ledger in odpri aplikacijo Ethereum", "ledger.error.ledger_is_locked.title": "Ledger je zaklen<PERSON>n", "ledger.error.ledger_not_connected.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.error.ledger_not_connected.subtitle": "Poveži svojo strojno denarnico z napravo in odpri aplikacijo Ethereum", "ledger.error.ledger_not_connected.title": "Ledger ni povezan", "ledger.error.ledger_running_non_eth_app.title": "Aplikacija Ethereum ni odprta", "ledger.error.user_trx_denied_by_user.action": "<PERSON><PERSON><PERSON>", "ledger.error.user_trx_denied_by_user.subtitle": "Transakcijo si zavrnil na svoji strojni denarnici", "ledger.error.user_trx_denied_by_user.title": "Transakcija zavrnjena", "ledger.hd_path.bip44.subtitle": "npr. <PERSON>, Trezor", "ledger.hd_path.bip44.title": "Standard BIP44", "ledger.hd_path.ledger_live.subtitle": "Privzeto", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "<PERSON><PERSON>ova<PERSON>", "ledger.hd_path.phantom.subtitle": "npr. Phantom", "ledger.select.hd_path.subtitle": "HD-poti so <PERSON>, kako strojne denarnice razvrščajo svoje račune. Podobno kot kazalo razvršča strani v knjigi.", "ledger.select.hd_path.title": "Izberi HD-pot", "ledger.select_account.import_wallets_count": "{count,plural,=0{Ni <PERSON>h denarnic} one{Uvozi denarnico} other{Uvozi {count} denarnic}}", "ledger.select_account.path_settings": "Nastavitve poti", "ledger.select_account.subtitle": "Ne vidiš pričakovanih denarnic? Poskusi spremeniti nastavitve poti", "ledger.select_account.subtitle.group_header": "Denarnice", "ledger.select_account.title": "<PERSON><PERSON><PERSON> denar<PERSON>e Ledger", "legend.lending-operations": "Posojilne <PERSON>", "legend.market_making-operations": "Dejavnosti ustvarjanja trga", "legend.treasury-operations": "Zakladniške dejavnosti", "link-existing-monerium-account-sign.button": "<PERSON><PERSON><PERSON><PERSON>", "link-existing-monerium-account-sign.subtitle": "Račun Monerium že imaš.", "link-existing-monerium-account-sign.title": "Poveži Zeal s svojim obstoječim računom Monerium", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Račun Monerium že imaš. Za dokončanje nastavitve obišči aplikacijo Monerium.", "link-existing-monerium-account.title": "Pojdi v Monerium in poveži svoj račun", "loading.pin": "Nalaganje kode PIN...", "lockScreen.passwordIncorrectMessage": "Geslo je <PERSON>", "lockScreen.passwordRequiredMessage": "Potrebno je geslo", "lockScreen.unlock.header": "<PERSON><PERSON><PERSON><PERSON>", "lockScreen.unlock.subheader": "Za odklepanje Zeala uporabi svoje geslo", "mainTabs.activity.label": "Dejavnost", "mainTabs.browse.label": "<PERSON><PERSON><PERSON>", "mainTabs.browse.title": "<PERSON><PERSON><PERSON>", "mainTabs.card.label": "Kartica", "mainTabs.portfolio.label": "Portfelj", "mainTabs.rewards.label": "Nagrade", "makeSpendable.cta": "Omogoči porabo", "makeSpendable.holdAsCash": "Zadrži kot gotovino", "makeSpendable.shortText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {apy} na leto", "makeSpendable.title": "{amount} prejeto", "merchantCategory.agriculture": "Kmetijstvo", "merchantCategory.alcohol": "Alkohol", "merchantCategory.antiques": "Starine", "merchantCategory.appliances": "Gospodinjski aparati", "merchantCategory.artGalleries": "Umetnostne galerije", "merchantCategory.autoRepair": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.autoRepairService": "Avtoservisne storitve", "merchantCategory.beautyFitnessSpas": "<PERSON><PERSON><PERSON>, fitnes in toplice", "merchantCategory.beautyPersonalCare": "Le<PERSON>a in osebna nega", "merchantCategory.billiard": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.books": "<PERSON><PERSON><PERSON>", "merchantCategory.bowling": "Kegljanje", "merchantCategory.businessProfessionalServices": "Poslovne in strokovne storitve", "merchantCategory.carRental": "<PERSON><PERSON><PERSON>", "merchantCategory.carWash": "Avtopralnica", "merchantCategory.cars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.casino": "Igralnica", "merchantCategory.casinoGambling": "Igralnica in igre na srečo", "merchantCategory.cellular": "Mobilna telefonija", "merchantCategory.charity": "Dobrodelnost", "merchantCategory.childcare": "Varstvo otrok", "merchantCategory.cigarette": "Cigarete", "merchantCategory.cinema": "<PERSON><PERSON>", "merchantCategory.cinemaEvents": "<PERSON>no in dogodki", "merchantCategory.cleaning": "Čiščenje", "merchantCategory.cleaningMaintenance": "Čiščenje in vzdrževanje", "merchantCategory.clothes": "Oblačila", "merchantCategory.clothingServices": "Storitve za oblačila", "merchantCategory.communicationServices": "Komunikacijske storitve", "merchantCategory.construction": "Gradbeništvo", "merchantCategory.cosmetics": "Kozmetika", "merchantCategory.craftsArtSupplies": "Rokodelstvo in umetniški pripomočki", "merchantCategory.datingServices": "Storitve za zmenke", "merchantCategory.delivery": "<PERSON><PERSON><PERSON>", "merchantCategory.dentist": "Zobozdravnik", "merchantCategory.departmentStores": "Veleblagovnice", "merchantCategory.directMarketingSubscription": "Neposredno trženje in naročnine", "merchantCategory.discountStores": "<PERSON><PERSON><PERSON>", "merchantCategory.drugs": "<PERSON><PERSON><PERSON>", "merchantCategory.dutyFree": "Brezcarinska prodaja", "merchantCategory.education": "Izobraževanje", "merchantCategory.electricity": "Elektrika", "merchantCategory.electronics": "Elektronika", "merchantCategory.emergencyServices": "Nujne storitve", "merchantCategory.equipmentRental": "Izposoja opreme", "merchantCategory.evCharging": "Polnjenje električnih vozil", "merchantCategory.financialInstitutions": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.financialProfessionalServices": "Finančne in strokovne storitve", "merchantCategory.finesPenalties": "Globe in kazni", "merchantCategory.fitness": "Fitnes", "merchantCategory.flights": "Leti", "merchantCategory.flowers": "Cvetje", "merchantCategory.flowersGarden": "Cvetje in vrt", "merchantCategory.food": "<PERSON><PERSON>", "merchantCategory.freight": "Prevoz tovora", "merchantCategory.fuel": "Gorivo", "merchantCategory.funeralServices": "Pogrebne storitve", "merchantCategory.furniture": "Pohištvo", "merchantCategory.games": "Igre", "merchantCategory.gas": "Gorivo", "merchantCategory.generalMerchandiseRetail": "Splošno blago in maloprodaja", "merchantCategory.gifts": "Darila", "merchantCategory.government": "<PERSON><PERSON>", "merchantCategory.governmentServices": "Državne storitve", "merchantCategory.hardware": "Tehnično blago", "merchantCategory.healthMedicine": "Zdravje in medicina", "merchantCategory.homeImprovement": "Izboljšave za dom", "merchantCategory.homeServices": "Storitve za dom", "merchantCategory.hotel": "Hotel", "merchantCategory.housing": "Stanovanje", "merchantCategory.insurance": "Zavarovanje", "merchantCategory.internet": "Internet", "merchantCategory.kids": "<PERSON><PERSON><PERSON>", "merchantCategory.laundry": "Pralnica", "merchantCategory.laundryCleaningServices": "Pralnice in čistilnice", "merchantCategory.legalGovernmentFees": "Pravne in državne takse", "merchantCategory.luxuries": "Luksuz", "merchantCategory.luxuriesCollectibles": "Luksuzni in zbirateljski predmeti", "merchantCategory.magazines": "<PERSON><PERSON><PERSON>", "merchantCategory.magazinesNews": "Revije in časopisi", "merchantCategory.marketplaces": "Tržnice", "merchantCategory.media": "<PERSON><PERSON><PERSON>", "merchantCategory.medicine": "Medicina", "merchantCategory.mobileHomes": "<PERSON><PERSON><PERSON>", "merchantCategory.moneyTransferCrypto": "Prenos denarja in kripto", "merchantCategory.musicRelated": "Glasba", "merchantCategory.musicalInstruments": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.optics": "Optika", "merchantCategory.organizationsClubs": "Organizacije in klubi", "merchantCategory.other": "Drugo", "merchantCategory.parking": "Parkiranje", "merchantCategory.pawnShops": "Zastavljalnice", "merchantCategory.pets": "Hišni ljubljenčki", "merchantCategory.photoServicesSupplies": "Fotografske storitve in oprema", "merchantCategory.postalServices": "Poštne storitve", "merchantCategory.professionalServicesOther": "Strokovne storitve (drugo)", "merchantCategory.publicTransport": "Javni prevoz", "merchantCategory.purchases": "<PERSON><PERSON><PERSON>", "merchantCategory.purchasesMiscServices": "<PERSON><PERSON><PERSON> in razne storitve", "merchantCategory.recreationServices": "Rekreacijske storitve", "merchantCategory.religiousGoods": "Nabožni predmeti", "merchantCategory.secondhandRetail": "Trgovine z rabljenim blagom", "merchantCategory.shoeHatRepair": "Popravilo čevljev in klobukov", "merchantCategory.shoeRepair": "Popravilo <PERSON>v", "merchantCategory.softwareApps": "Programska oprema in aplikacije", "merchantCategory.specializedRepairs": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.sport": "Šport", "merchantCategory.sportingGoods": "Športna oprema", "merchantCategory.sportingGoodsRecreation": "Športna oprema in rekreacija", "merchantCategory.sportsClubsFields": "Športni klubi in igrišča", "merchantCategory.stationaryPrinting": "Pisarniški material in tisk", "merchantCategory.stationery": "Pisarniški material", "merchantCategory.storage": "Skladiščenje", "merchantCategory.taxes": "<PERSON><PERSON><PERSON>", "merchantCategory.taxi": "<PERSON><PERSON><PERSON>", "merchantCategory.telecomEquipment": "Telekomunikacijska oprema", "merchantCategory.telephony": "Telefonija", "merchantCategory.tobacco": "Tobačni izdelki", "merchantCategory.tollRoad": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.tourismAttractionsAmusement": "Turizem, znamenitosti in zabava", "merchantCategory.towing": "<PERSON>leka vozil", "merchantCategory.toys": "Igrač<PERSON>", "merchantCategory.toysHobbies": "Igrače in hobiji", "merchantCategory.trafficFine": "<PERSON><PERSON><PERSON>", "merchantCategory.train": "<PERSON><PERSON>", "merchantCategory.travelAgency": "Potovalna agencija", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, radio in pretakanje", "merchantCategory.utilities": "Komunalne storitve", "merchantCategory.waterTransport": "V<PERSON><PERSON> prevoz", "merchantCategory.wholesaleClubs": "Veleprodajni klubi", "metaMask.subtitle": "Omogoči način MetaMask za preusmeritev vseh povezav MetaMask na Zeal. S klikom na MetaMask v dApps se boš povezal z Zeal.", "metaMask.title": "Se ne moreš povezati z Zeal?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Odpri svojo bančno aplikacijo", "monerium-bank-deposit.buttet-point.receive-crypto": "Prejmi digitalne EUR", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Pošlji {fiatCurrencyCode} na svoj račun", "monerium-bank-deposit.deposit-account-country": "Država", "monerium-bank-deposit.header": "{fullName} – oseb<PERSON> ra<PERSON>un", "monerium-bank-details.account-name": "<PERSON><PERSON>", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT kopiran", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT koda kopirana", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN kopiran", "monerium-bank-details.to-wallet": "V denarnico", "monerium-bank-details.transfer-fee": "Provizija za prenos", "monerium-bank-transfer.enable-card.bullet-1": "Zaključi preverjanje identitete", "monerium-bank-transfer.enable-card.bullet-2": "Pridobi podatke o osebnem računu", "monerium-bank-transfer.enable-card.bullet-3": "Položi z bančnega računa", "monerium-card-delay-relay.success.cta": "<PERSON><PERSON><PERSON>", "monerium-card-delay-relay.success.subtitle": "Zaradi varnosti traja obdelava sprememb nastavitev kartice 3 minute.", "monerium-card-delay-relay.success.title": "Vrni se čez 3 minute, da nadaljuješ z nastavitvijo Moneriuma", "monerium-deposit.account-details-info-popup.bullet-point-1": "Vsak {fiatCurrencyCode} , ki ga po<PERSON><PERSON><PERSON><PERSON> na ta račun, se samodejno pretvori v {cryptoCurrencyCode} žetone na {cryptoCurrencyChain} verigi in pošlje v tvojo denarnico", "monerium-deposit.account-details-info-popup.bullet-point-2": "POŠLJI SAMO {fiatCurrencyCode} ({fiatCurrencySymbol}) na svoj račun", "monerium-deposit.account-details-info-popup.title": "Podatki o tvojem računu", "monerium.check_order_status.sending": "Pošiljanje", "monerium.not-eligible.cta": "<PERSON><PERSON><PERSON>", "monerium.not-eligible.subtitle": "Monerium ti ne more odpreti računa. Izberi drugega ponudnika.", "monerium.not-eligible.title": "Poskusi z drugim ponudnikom", "monerium.setup-card.cancel": "Prekliči", "monerium.setup-card.continue": "<PERSON><PERSON><PERSON><PERSON>", "monerium.setup-card.create_account": "Ustvari račun", "monerium.setup-card.login": "Prijava v Gnosis Pay", "monerium.setup-card.subtitle": "Ustvari ali se prijavi v svoj račun Gnosis Pay za takojšnje bančne pologe.", "monerium.setup-card.subtitle_personal_account": "Pridobi svoj osebni račun Gnosis Pay v nekaj minutah:", "monerium.setup-card.title": "Omogoči bančne pologe", "moneriumDepositSuccess.goToWallet": "Pojdi v denarnico", "moneriumDepositSuccess.title": "{symbol} prejeto", "moneriumInfo.fees": "Provizije so 0 %", "moneriumInfo.registration": "Monerium je pooblaščena in regulirana institucija za elektronski denar v skladu z islandskim Zakonom o elektronskem denarju št. 17/2013 <link>Več o tem</link>", "moneriumInfo.selfCustody": "<PERSON><PERSON>, ki ga prej<PERSON>, je v tvoji zasebni hrambi in nihče drug nima nadzora nad tvojimi sredstvi.", "moneriumWithdrawRejected.supportText": "Tvojega prenosa nismo mogli izvesti. Poskusi z<PERSON> in če še vedno ne deluje, <link>se obrni na podporo.</link>", "moneriumWithdrawRejected.title": "Prenos razveljavljen", "moneriumWithdrawRejected.tryAgain": "Poskusi znova", "moneriumWithdrawSuccess.supportText": "<PERSON><PERSON><PERSON> traj<PERSON> do 24 ur, da tvoj{br}prejemnik prejme sredstva", "moneriumWithdrawSuccess.title": "Poslano", "monerium_enable_banner.text": "Aktiviraj ban<PERSON>ne prenose zdaj", "monerium_error_address_re_link_required.title": "Denarnico je treba znova povezati z Moneriumom", "monerium_error_duplicate_order.title": "Podvojeno naročilo", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "Švicars<PERSON> frank", "mt-pelerin-fork.options.chf.short": "Takoj in brezplačno z Mt Pelerinom", "mt-pelerin-fork.options.euro.primary": "<PERSON><PERSON><PERSON>", "mt-pelerin-fork.options.euro.short": "Takoj in brezplačno z Moneriumom", "mt-pelerin-fork.title": "<PERSON><PERSON> <PERSON><PERSON>?", "mtPelerinProviderInfo.fees": "Plačaš 0 % provizije", "mtPelerinProviderInfo.registration": "Družba Mt Pelerin Group Ltd je povezana s SO-FIT, samoregulativnim organom, ki ga priznava švicarski organ za finančni trg (FINMA) v skladu z zakonom o preprečevanju pranja denarja. <link>Več o tem</link>", "mtPelerinProviderInfo.selfCustody": "<PERSON>ni <PERSON>, ki ga pre<PERSON>, je v tvoji lastni hrambi in nihče drug nima nadzora nad tvojimi sredstvi", "network-fee-widget.title": "Provizije", "network.edit.verifying_rpc": "Preverjanje R<PERSON>-ja", "network.editRpc.predefined_network_info.subtitle": "<PERSON><PERSON><PERSON><PERSON> kot VPN, <PERSON><PERSON> upora<PERSON>lja RPC-je, ki preprečujejo sledenje tvojim osebnim podatkom.{br}{br}Zealovi privzeti RPC-ji so z<PERSON><PERSON><PERSON><PERSON> in preizkušeni ponudniki.", "network.editRpc.predefined_network_info.title": "Zeal RPC za zasebnost", "network.filter.update_rpc_success": "RPC vozlišče shranjeno", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "Vsa omrežja", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "Dodaj omrežja", "networks.filter.add_modal.chain_list.subtitle": "Do<PERSON>j ka<PERSON>li omrežje EVM", "networks.filter.add_modal.chain_list.title": "<PERSON><PERSON><PERSON> na Chainlist", "networks.filter.add_modal.dapp_tip.subtitle": "V svojih priljubljenih dApps preklopi na želeno omrežje EVM in Zeal te bo vprašal, ali ga želiš dodati v denarnico.", "networks.filter.add_modal.dapp_tip.title": "Ali dodaj omrežje iz katerekoli dApp", "networks.filter.add_networks.subtitle": "Podprta so vsa omrežja EVM", "networks.filter.add_networks.title": "Dodaj omrežja", "networks.filter.add_test_networks.title": "Dodaj testna omrežja", "networks.filter.tab.netwokrs": "Omrežja", "networks.filter.testnets.title": "Testna omrežja", "nft.widget.emptystate": "V denarnici ni zbirateljskih predmetov", "nft_collection.change_account_picture.subtitle": "<PERSON><PERSON> <PERSON><PERSON> posodobiti profilno sliko?", "nft_collection.change_account_picture.title": "Posodobi profilno sliko v NFT", "nfts.allNfts.pricingPopup.description": "Cene zbirateljskih predmetov temeljijo na zadnji ceni trgovanja.", "nfts.allNfts.pricingPopup.title": "<PERSON><PERSON> pred<PERSON>", "no-passkeys-found.modal.cta": "<PERSON><PERSON><PERSON>", "no-passkeys-found.modal.subtitle": "Na tej napravi ne moremo zaznati nobenih ključev Zeal. Prepričaj se, da si prijavljen v račun v oblaku, s katerim si ustvaril svojo Smart wallet.", "no-passkeys-found.modal.title": "Ni najdenih klju<PERSON>ev", "notValidEmail.title": "Neveljaven e-poštni naslov", "notValidPhone.title": "To ni veljavna telefonska številka", "notification-settings.title": "Nastavi<PERSON><PERSON> obvestil", "notification-settings.toggles.active-wallets": "Aktivne denarnice", "notification-settings.toggles.bank-transfers": "Bančna nakazila", "notification-settings.toggles.card-payments": "Plačila s kartico", "notification-settings.toggles.readonly-wallets": "Denarnice samo za branje", "ntft.groupHeader.text": "Zbirateljski predmeti", "on_ramp.crypto_completed": "<PERSON><PERSON><PERSON><PERSON>", "on_ramp.fiat_completed": "<PERSON><PERSON><PERSON><PERSON>", "onboarding-widget.subtitle.card_created_from_order.left": "Kartica Visa", "onboarding-widget.subtitle.card_created_from_order.right": "Aktiviraj kartico", "onboarding-widget.subtitle.card_order_ready.left": "Fizična kartica Visa", "onboarding-widget.subtitle.default": "Bančni prenosi in kartica Visa", "onboarding-widget.title.card-order-in-progress": "Nadaljuj z naročilom kartice", "onboarding-widget.title.card_created_from_order": "Kartica je bila poslana", "onboarding-widget.title.kyc_approved": "Prenosi in kartica so pripravljeni", "onboarding-widget.title.kyc_failed": "Ustvarjanje računa ni mogoče", "onboarding-widget.title.kyc_not_started": "Nadaljuj z nastavitvijo", "onboarding-widget.title.kyc_started_documents_requested": "Dokon<PERSON><PERSON>", "onboarding-widget.title.kyc_started_resubmission_requested": "<PERSON><PERSON><PERSON>", "onboarding-widget.title.kyc_started_verification_in_progress": "Preverjanje identitete", "onboarding.loginOrCreateAccount.amountOfAssets": "Sredstva v vrednosti 10+ mrd $", "onboarding.loginOrCreateAccount.cards.subtitle": "Na voljo le v določenih regijah. Z nadaljevanjem sprejemaš naše <Terms>Pogoje</Terms> in <PrivacyPolicy>Pravilnik o zasebnosti</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Visa kartica z visokimi{br}donosi in brez provizij", "onboarding.loginOrCreateAccount.createAccount": "Ustvari račun", "onboarding.loginOrCreateAccount.earn.subtitle": "Donosi se razlikujejo; kapital je izpostavljen tveganju. Z nadaljevanjem sprejemaš naše <Terms>Pogoje</Terms> in <PrivacyPolicy>Pravilnik o zasebnosti</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> {percent} na leto{br}Zaupa nam {currencySymbol}5+ mrd", "onboarding.loginOrCreateAccount.earningPerYear": "<PERSON><PERSON><PERSON><PERSON><PERSON> {percent}{br}na leto", "onboarding.loginOrCreateAccount.login": "<PERSON><PERSON><PERSON><PERSON>", "onboarding.loginOrCreateAccount.trading.subtitle": "Kapital je izpostavljen tveganju. Z nadaljevanjem sprejemaš naše <Terms>Pogoje</Terms> in <PrivacyPolicy>Pravilnik o zasebnosti</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Naložbe v vse,{br}od BTC do S&P", "onboarding.loginOrCreateAccount.trustedBy": "Digitalni denarni trgi{br}Zaupa nam {assets}", "onboarding.wallet_stories.close": "<PERSON><PERSON><PERSON>", "onboarding.wallet_stories.previous": "<PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.deposit": "<PERSON><PERSON>", "order-earn-deposit-bridge.into": "V", "otpIncorrectMessage": "Potrditvena koda je napačna", "passkey-creation-not-possible.modal.close": "<PERSON><PERSON><PERSON>", "passkey-creation-not-possible.modal.subtitle": "Nismo mogli ustvariti ključa Passkey za tvojo denarnico. Prepri<PERSON><PERSON> se, da tvoja naprava podpira ključe Passkey, in poskusi znova. <link>Piši podpori</link> če se težava ponovi.", "passkey-creation-not-possible.modal.title": "Ključa Passkey ni mogoče ustvariti", "passkey-not-supported-in-mobile-browser.modal.cta": "<PERSON><PERSON><PERSON>", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Pametne denarnice niso podprte v mobilnih brskalnikih.", "passkey-not-supported-in-mobile-browser.modal.title": "<PERSON>a na<PERSON><PERSON><PERSON> prenesi a<PERSON><PERSON><PERSON><PERSON><PERSON>", "passkey-recovery.recovering.deploy-signer.loading-text": "Preverjanje ključa", "passkey-recovery.recovering.loading-text": "Obnavljanje denarnice", "passkey-recovery.recovering.signer-not-found.subtitle": "Tvojega ključa nismo mogli povezati z aktivno denarnico. Če imaš sredstva v denarnici, se za podporo obrni na ekipo Zeal.", "passkey-recovery.recovering.signer-not-found.title": "Denarnica ni najdena", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "Poskusi z drugim ključem", "passkey-recovery.select-passkey.banner.subtitle": "Prepričaj se, da si v napravi prijavljen v pravi račun. Ključi so vezani na račun.", "passkey-recovery.select-passkey.banner.title": "Ne vidiš ključa svoje denarnice?", "passkey-recovery.select-passkey.continue": "Izberi ključ", "passkey-recovery.select-passkey.subtitle": "<PERSON><PERSON><PERSON><PERSON> ključ, povezan z denarnico, da ponovno prid<PERSON><PERSON>š dostop.", "passkey-recovery.select-passkey.title": "Izberi ključ", "passkey-story_1.subtitle": "S Smart Wallet lahko omrežnino plačaš z večino žetonov in ti ni treba skrbeti za stroške transakcij.", "passkey-story_1.title": "Brez skrbi za provizije – omrežnino plačaj z večino žetonov", "passkey-story_2.subtitle": "Zgrajeno na vodilnih pametnih pogodbah Safe, ki varujejo več kot 100 milijard $ v več kot 20 milijonih denarnic.", "passkey-story_2.title": "Zavarovano s Safe", "passkey-story_3.subtitle": "Smart Wallets delujejo na večjih omrežjih, združljivih z Ethereumom. Pred pošiljanjem sredstev preveri podprta omrežja.", "passkey-story_3.title": "Podprta večja omrežja EVM", "password.add.header": "Ustvar<PERSON> g<PERSON>lo", "password.add.includeLowerAndUppercase": "Male in velike črke", "password.add.includesNumberOrSpecialChar": "<PERSON><PERSON> š<PERSON>vil<PERSON> ali simbol", "password.add.info.subtitle": "Tvojega gesla ne pošiljamo na naše strežnike in ne shranjujemo varnostne kopije.", "password.add.info.t_and_c": "Z nadaljevanjem sprejemaš naše <Terms>Pogoje</Terms> in <PrivacyPolicy>Pravilnik o zasebnosti</PrivacyPolicy>", "password.add.info.title": "Tvoje geslo ostane na tej napravi", "password.add.inputPlaceholder": "Ustvar<PERSON> g<PERSON>lo", "password.add.shouldContainsMinCharsCheck": "10+ znakov", "password.add.subheader": "S svojim geslom boš o<PERSON><PERSON>", "password.add.success.title": "<PERSON><PERSON><PERSON> 🔥", "password.confirm.header": "<PERSON><PERSON><PERSON> g<PERSON>", "password.confirm.passwordDidNotMatch": "Gesli se morata ujemati", "password.confirm.subheader": "Ponovno vnesi svoje geslo", "password.create_pin.subtitle": "Ta koda zaklene a<PERSON>", "password.create_pin.title": "Ustvari svojo kodo", "password.enter_pin.title": "Vnesi kodo", "password.incorrectPin": "Napačna koda", "password.pin_is_not_same": "Kodi se ne ujemata", "password.placeholder.enter": "Vnesi geslo", "password.placeholder.reenter": "Ponovno vnesi geslo", "password.re_enter_pin.subtitle": "Ponovno vnesi isto kodo", "password.re_enter_pin.title": "<PERSON><PERSON><PERSON> kodo", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "V teku", "pending-send.details.pending": "V teku", "pending-send.details.processing": "<PERSON><PERSON><PERSON><PERSON>", "permit-info.modal.description": "Dovoljenja so zah<PERSON><PERSON>, s katerimi aplikacijam omogočiš premikanje tvojih žetonov, na primer za izvedbo zamenjave.{br}Dovoljenja so podobna odobritvam, vendar za njihov podpis ne plač<PERSON>š omrežnine.", "permit-info.modal.title": "<PERSON><PERSON> so do<PERSON><PERSON><PERSON><PERSON> (Permits)?", "permit.edit-expiration": "Uredi {currency} potek", "permit.edit-limit": "Uredi {currency} limit porabe", "permit.edit-modal.expiresIn": "Poteče čez…", "permit.expiration-warning": "{currency} opozorilo o poteku", "permit.expiration.info": "{currency} informacije o poteku", "permit.expiration.never": "Nik<PERSON>", "permit.spend-limit.info": "{currency} informacije o limitu porabe", "permit.spend-limit.warning": "{currency} opozorilo o limitu porabe", "phoneNumber.title": "telefonska številka", "physicalCardOrderFlow.cardOrdered": "Kartica naročena", "physicalCardOrderFlow.city": "Mesto", "physicalCardOrderFlow.orderCard": "Naroči kartico", "physicalCardOrderFlow.postcode": "Poštna številka", "physicalCardOrderFlow.shippingAddress.subtitle": "Naslov za dostavo tvoje kartice", "physicalCardOrderFlow.shippingAddress.title": "Naslov za dostavo", "physicalCardOrderFlow.street": "Ulica", "placeholderDapps.1inch.description": "Menjaj po najboljših poteh", "placeholderDapps.aave.description": "Posojaj in si izposojaj žetone", "placeholderDapps.bungee.description": "Poveži omrežja po najboljših poteh", "placeholderDapps.compound.description": "Posojaj in si izposojaj žetone", "placeholderDapps.cowswap.description": "Menjaj po najboljših tečajih na Gnosis", "placeholderDapps.gnosis-pay.description": "Upravljaj svojo kartico Gnosis Pay", "placeholderDapps.jumper.description": "Poveži omrežja po najboljših poteh", "placeholderDapps.lido.description": "Zastavi ETH za več ETH", "placeholderDapps.monerium.description": "eDenar in bančni prenosi", "placeholderDapps.odos.description": "Menjaj po najboljših poteh", "placeholderDapps.stargate.description": "Poveži ali zastavi za <14 % APY", "placeholderDapps.uniswap.description": "Ena najbolj priljubl<PERSON><PERSON>h borz", "pleaseAllowNotifications.cardPayments": "Plačila s kartico", "pleaseAllowNotifications.customiseInSettings": "Prilagodi v nastavitvah", "pleaseAllowNotifications.enable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pleaseAllowNotifications.forWalletActivity": "Za dejavnost denarnice", "pleaseAllowNotifications.title": "<PERSON>je<PERSON><PERSON> obves<PERSON>a denar<PERSON>e", "pleaseAllowNotifications.whenReceivingAssets": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "portfolio.quick-actions.add_funds": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.buy": "<PERSON><PERSON>", "portfolio.quick-actions.deposit": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.send": "Pošlji", "portfolio.view.lastRefreshed": "Osveženo {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Napolni svoj testni AVAX", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Pojdi na Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "Napolni svoj testni BNB", "portfolio.view.topupTestNet.BscTestnet.secondary": "Pojdi na Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Napolni svoj testni SepETH", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Pojdi na Sepolia Faucet", "portfolio.view.topupTestNet.FantomTestnet.primary": "Napolni svoj testni FTM", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Pojdi na Faucet", "privateKeyConfirmation.banner.subtitle": "Kdor ima vaš zasebni ključ, ima dostop do denarnice in sredstev. Zahtevajo ga le prevaranti.", "privateKeyConfirmation.banner.title": "Razumem tveganja", "privateKeyConfirmation.title": "Svojega Zasebnega ključa NE DELI z nikomer", "rating-request.not-now": "Ne zdaj", "rating-request.title": "<PERSON><PERSON> p<PERSON><PERSON>č<PERSON>?", "receive_funds.address-text": "To je tvoj edinstven naslov denarnice. Varno ga lahko deliš z drugimi.", "receive_funds.copy_address": "<PERSON><PERSON><PERSON> denarnic<PERSON>", "receive_funds.network-warning.eoa.subtitle": "<link>Poglej seznam omrežij</link>. Sredstva, poslana na omrežjih, ki niso EVM, bodo izgubljena.", "receive_funds.network-warning.eoa.title": "Podprta so vsa omrež<PERSON>, ki temeljijo na Ethereumu", "receive_funds.network-warning.scw.subtitle": "<link>Poglej podprta omrežja</link>. <PERSON><PERSON><PERSON><PERSON>, poslana na drugih omrežjih, bodo izgubljena.", "receive_funds.network-warning.scw.title": "Pomembno: Uporabljaj samo podprta omrežja", "receive_funds.scan_qr_code": "Skeniraj kodo QR", "receiving.in.days": "<PERSON><PERSON><PERSON> {days} d", "receiving.this.week": "Prejem ta teden", "receiving.today": "<PERSON><PERSON><PERSON> danes", "reference.error.maximum_number_of_characters_exceeded": "<PERSON><PERSON><PERSON>", "referral-code.placeholder": "Prilepi povezavo povabila", "referral-code.subtitle": "Ponovno klikni na prijateljevo povezavo ali jo prilepi spodaj. <PERSON><PERSON><PERSON>, da prejmeš svoje nagrade.", "referral-code.title": "Ti je prija<PERSON>j poslal {bReward}?", "rekyc.verification_deadline.subtitle": "Opravi preverjanje v {daysUntil} dneh, da bo<PERSON> lahko še naprej uporabljal svojo kartico.", "rekyc.verification_required.subtitle": "<PERSON><PERSON><PERSON>, da bo<PERSON> lahko še naprej uporabljal svojo kartico.", "reminder.fund": "💸 Dodaj sredstva — takoj začni služiti 6 %", "reminder.onboarding": "🏁 Dokončaj nastavitev — zasluži 6 % na pologe", "remove-owner.confirmation.subtitle": "Zaradi varnosti traja obdelava sprememb nastavitev 3 minute. V tem času bo tvoja kartica začasno zamrznjena in plačila ne bodo mogoča.", "remove-owner.confirmation.title": "Tvoja kartica bo zamrznjena za 3 min, medtem ko se nastavitve posodabljajo", "restore-smart-wallet.wallet-recovered": "Denarnica obnovljena", "rewardClaimCelebration.claimedTitle": "Nagrade so že prevzete", "rewardClaimCelebration.subtitle": "Za vabljenje prijateljev", "rewardClaimCelebration.title": "<PERSON>as<PERSON><PERSON><PERSON> si", "rewards-warning.subtitle": "Odstranitev tega računa bo začasno ustavila dostop do vseh povezanih nagrad. Račun lahko kadar koli ob<PERSON>, da jih prevzam<PERSON>.", "rewards-warning.title": "<PERSON>z<PERSON><PERSON> boš dostop do svojih nagrad", "rewards.copiedInviteLink": "Povezava povabila kopirana", "rewards.createAccount": "<PERSON><PERSON><PERSON> p<PERSON> povabila", "rewards.header.subtitle": "Po<PERSON>li ti bomo {aReward} in {bReward} tvo<PERSON><PERSON> pri<PERSON>, ko bo porabil {bSpendLimitReward}.", "rewards.header.title": "Prejmi {amountA}{br}Podari {amountB}", "rewards.sendInvite": "Pošlji povabilo", "rewards.sendInviteTip": "Izberi prijatelja in podarili mu bomo {bAmount}", "route.fees": "<PERSON><PERSON><PERSON><PERSON> {fees}", "routesNotFound.description": "Pot menjave za kombinacijo omrežij {from}–{to} ni na voljo.", "routesNotFound.title": "Pot menjave ni na voljo", "rpc.OrderBuySignMessage.subtitle": "Prek Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Prek Swaps.IO", "rpc.addCustomNetwork.addNetwork": "<PERSON><PERSON><PERSON>", "rpc.addCustomNetwork.chainId": "ID verige", "rpc.addCustomNetwork.nativeToken": "Domači žeton", "rpc.addCustomNetwork.networkName": "<PERSON><PERSON>", "rpc.addCustomNetwork.operationDescription": "To spletno mesto lahko doda omrežje v tvojo denarnico. Zeal ne more preveriti varnosti omrežij po meri, zato se prepri<PERSON>, da raz<PERSON>š tveganja.", "rpc.addCustomNetwork.rpcUrl": "URL RPC", "rpc.addCustomNetwork.subtitle": "Uporaba {name}", "rpc.addCustomNetwork.title": "Dodaj omrežje", "rpc.send_token.network_not_supported.subtitle": "Prizadevamo si omogočiti transakcije na tem omrežju. Hvala za potrpežljivost 🙏", "rpc.send_token.network_not_supported.title": "Omrežje bo kmalu na voljo", "rpc.send_token.send_or_receive.settings": "Nastavitve", "rpc.sign.accept": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rpc.sign.cannot_parse_message.body": "Tega sporočila nismo mogli dekodirati. Sprejmi to zahtevo samo, če zaupaš tej aplikaciji.{br}{br}Sporočila se lahko uporabijo za prijavo v aplikacijo, lahko pa aplikacijam omogočijo tudi nadzor nad tvojimi žetoni.", "rpc.sign.cannot_parse_message.header": "<PERSON><PERSON><PERSON><PERSON>", "rpc.sign.import_private_key": "Uvozi ključe", "rpc.sign.subtitle": "Za {name}", "rpc.sign.title": "Podpiši", "safe-creation.success.title": "Denarnica ustvarjena", "safe-safety-checks-popup.title": "Varnostno preverjanje transakcije", "safetyChecksPopup.title": "Varnostno preverjanje strani", "scan_qr_code.description": "Skeniraj kodo QR denarnice ali se poveži z aplikacijo", "scan_qr_code.show_qr_code": "Prikaži mojo kodo QR", "scan_qr_code.tryAgain": "Poskusi znova", "scan_qr_code.unlockCamera": "Omogoči kamero", "screen-lock-missing.modal.close": "<PERSON><PERSON><PERSON>", "screen-lock-missing.modal.subtitle": "Za uporabo ključev Passkey mora imeti tvoja naprava zaklenjen zaslon. Nastavi zaklepanje zaslona in poskusi znova.", "screen-lock-missing.modal.title": "Manjka zaklepanje zaslona", "seedConfirmation.banner.subtitle": "Kdor ima vašo Skrivno frazo, ima dostop do denarnice in sredstev. Zahtevajo jo le prevaranti.", "seedConfirmation.title": "Svoje Skrivne fraze NE DELI z nikomer", "select-active-owner.subtitle": "S svojo kartico imaš povezanih več denarnic. <PERSON><PERSON><PERSON><PERSON> eno, da jo pove<PERSON>š z Zealom. Zamenjaš jo lahko kadar<PERSON>.", "select-active-owner.title": "Izberi denarnico", "select-card.title": "Izberi kartico", "select-crypto-currency-title": "Izberi žeton", "select-token.title": "Izberi žeton", "selectEarnAccount.chf.description.steps": "· Dviguj sredstva 24/7, brez obdo<PERSON><PERSON> zaklepanja {br}· Obresti se obračunavajo vsako sekundo {br}· Prekomerno zavarovani depoziti v <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} letno v CHF", "selectEarnAccount.eur.description.steps": "· <PERSON><PERSON>g s<PERSON> 24/7, brez ob<PERSON><PERSON><PERSON> zaklepanja {br}· Obresti se obračunavajo vsako sekundo {br}· Prekomerno zavarovana posojila z <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} na leto v EUR", "selectEarnAccount.subtitle": "Sp<PERSON><PERSON>š lah<PERSON> ka<PERSON>", "selectEarnAccount.title": "Izberi valuto", "selectEarnAccount.usd.description.steps": "· Dvig s<PERSON> 24/7, brez obdo<PERSON><PERSON> zaklepanja {br}· Obresti se obračunavajo vsako sekundo {br}· Prekomerno zavarovani depoziti v <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} na leto v USD", "selectEarnAccount.zero.description_general": "Hrani digitalni denar brez služenja obresti", "selectEarnAccount.zero.title": "0 % na leto", "selectRechargeThreshold.button.enterAmount": "Vnesi znesek", "selectRechargeThreshold.button.setTo": "Na<PERSON><PERSON> na {amount}", "selectRechargeThreshold.description.line1": "Ko stanje na tvoji kartici pade pod {amount}, se samodejno napolni nazaj na {amount} iz tvojega Earn računa.", "selectRechargeThreshold.description.line2": "Nižji ciljni znesek pomeni več sredstev na tvojem Earn računu (ki prinaša 3 % donos). To lahko kadarkoli spremeniš.", "selectRechargeThreshold.title": "Nastavi ciljno stanje na kartici", "select_currency_to_withdraw.select_token_to_withdraw": "Izberi žeton za dvig", "send-card-token.form.send": "Pošlji", "send-card-token.form.send-amount": "Znesek pologa", "send-card-token.form.title": "Dodaj denar na kartico", "send-card-token.form.to-address": "Kartica", "send-safe-transaction.network-fee-widget.error": "Potrebuješ {amount} ali izberi drug žeton", "send-safe-transaction.network-fee-widget.no-fee": "Brez omrežnine", "send-safe-transaction.network-fee-widget.title": "<PERSON><PERSON><PERSON><PERSON>", "send-safe-transaction.network_fee_widget.title": "Omrežnina", "send.banner.fees": "Potrebuješ {amount} več {currency} za plačilo omrežnine", "send.banner.toAddressNotSupportedNetwork.subtitle": "Denarnica prejemnika ne podpira omrežja {network}. Zamenjaj za podprt žeton.", "send.banner.toAddressNotSupportedNetwork.title": "Prejemnik ne podpira omrežja", "send.banner.walletNotSupportedNetwork.subtitle": "Pametne denarnice ne morejo opravljati transakcij na omrežju {network}. Zamenjaj za podprt žeton.", "send.banner.walletNotSupportedNetwork.title": "Omrežje žetona ni podprto", "send.empty-portfolio.empty-state": "Nismo našli noben<PERSON>", "send.empty-portfolio.header": "Žetoni", "send.titile": "Pošl<PERSON>š", "sendLimit.success.subtitle": "Tvoj dnevni limit porabe bo posodobljen v 3 minutah. Do takrat lahko porabljaš znotraj trenutnega limita.", "sendLimit.success.title": "S<PERSON><PERSON><PERSON><PERSON> bo trajala 3 minute", "send_crypto.form.disconnected.cta.addFunds": "<PERSON><PERSON><PERSON>", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Preklopi na {network}", "send_crypto.form.disconnected.label": "Znesek za prenos", "send_to.qr_code.description": "Skeniraj QR kodo za pošiljanje v denarnico", "send_to.qr_code.title": "Skeniraj kodo QR", "send_to_card.header": "Pošlji na naslov kartice", "send_to_card.select_sender.add_wallet": "<PERSON><PERSON><PERSON>", "send_to_card.select_sender.header": "Izberi pošiljatelja", "send_to_card.select_sender.search.default_placeholder": "Išči po naslovu ali ENS", "send_to_card.select_sender.show_card_address_button_description": "Prikaži naslov kartice", "send_token.form.select-address": "Izberi naslov", "send_token.form.send-amount": "Znesek za pošiljanje", "send_token.form.title": "Pošlji", "setLimit.amount.error.zero_amount": "<PERSON>e bo<PERSON> mogel opraviti nobenega plačila", "setLimit.error.max_limit_reached": "Nastavi limito na maks. {amount}", "setLimit.error.same_as_current_limit": "Enako kot trenutni limit", "setLimit.placeholder": "Trenutno: {amount}", "setLimit.submit": "Nastavi limit", "setLimit.submit.error.amount_required": "Vnesi znesek", "setLimit.subtitle": "To je z<PERSON>, ki ga lahko dnevno porabiš s svojo kartico.", "setLimit.title": "Nastavi dnevni limit porabe", "settings.accounts": "<PERSON><PERSON><PERSON>", "settings.accountsSeeAll": "Prikaži vse", "settings.addAccount": "<PERSON><PERSON><PERSON>", "settings.card": "Nastavitve kartice", "settings.connections": "Povezave z aplikacijami", "settings.currency": "Privzeta valuta", "settings.default_currency_selector.title": "Valuta", "settings.discord": "Discord", "settings.experimentalMode": "Eksperimentalni način", "settings.experimentalMode.subtitle": "Preizkusi nove funkcije", "settings.language": "<PERSON><PERSON><PERSON>", "settings.lockZeal": "<PERSON><PERSON><PERSON><PERSON>", "settings.notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.open_expanded_view": "Odpri razš<PERSON>jen pogled", "settings.privacyPolicy": "Politika zasebnosti", "settings.settings": "Nastavitve", "settings.termsOfUse": "Pogoji <PERSON>", "settings.twitter": "𝕏 / Twitter", "settings.version": "Različica {version} okolje: {env}", "setup-card.confirmation": "Pridobi virtualno kartico", "setup-card.confirmation.subtitle": "Plačuj prek spleta in dodaj v svojo {type} denarnico za brezstična plačila.", "setup-card.getCard": "Izberi kartico", "setup-card.order.physicalCard": "Fizična kartica", "setup-card.order.physicalCard.steps": "· Fizična kartica VISA Gnosis Pay {br}· Dostava traja do 3 tedne {br}· Za osebna plačila in na bankomatih. {br}· Dodaj v denarnico Apple/Google (le v podprtih državah)", "setup-card.order.subtitle1": "<PERSON><PERSON><PERSON> več kartic", "setup-card.order.title": "Kakšno vrsto kartice želiš?", "setup-card.order.virtualCard": "Virtualna kartica", "setup-card.order.virtual_card.steps": "· Digitalna kartica VISA Gnosis Pay {br}· Takojšnja uporaba za spletna plačila {br}· Dodaj v denarnico Apple/Google (le v podprtih državah)", "setup-card.orderCard": "Naroči kartico", "setup-card.virtual-card": "Pridobi virtualno kartico", "setup.notifs.fakeAndroid.title": "Obvestila za plačila in dohodne prenose", "setup.notifs.fakeIos.subtitle": "Zeal te lahko op<PERSON>, ko prej<PERSON><PERSON> denar ali plač<PERSON>š s kartico Visa. To lahko spremeniš kas<PERSON>je.", "setup.notifs.fakeIos.title": "Obvestila za plačila in dohodne prenose", "sign.PermitAllowanceItem.spendLimit": "Limit porabe", "sign.ledger.subtitle": "Poslano v strojno denarnico. Nadaljuj.", "sign.ledger.title": "Podpiši s strojno denarnico", "sign.passkey.subtitle": "Brskalnik te bo pozval k podpisu s ključem Passkey, pove<PERSON>im s to denarnico. Prosim, nadaljuj tam.", "sign.passkey.title": "Izberi ključ Passkey", "signal_aborted_for_uknown_reason.title": "Omrežna zahteva preklicana", "simulatedTransaction.BridgeTrx.info.title": "Most", "simulatedTransaction.CardTopUp.info.title": "Dodaj denar na kartico", "simulatedTransaction.CardTopUpTrx.info.title": "Dodaj denar na kartico", "simulatedTransaction.NftCollectionApproval.approve": "Odobri zbirko NFT", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "Dodaj na kartico", "simulatedTransaction.OrderEarnDepositBridge.title": "Polog v Earn", "simulatedTransaction.P2PTransaction.info.title": "Pošlji", "simulatedTransaction.PermitSignMessage.title": "Dovoljenje", "simulatedTransaction.SingleNftApproval.approve": "Odobri NFT", "simulatedTransaction.UnknownSignMessage.title": "Podpiši", "simulatedTransaction.Withdrawal.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.approval.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.approve.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.p2p.info.account": "Prejemnik", "simulatedTransaction.p2p.info.unlabelledAccount": "Neoznačena denarnica", "simulatedTransaction.unknown.info.receive": "Prejmeš", "simulatedTransaction.unknown.info.send": "Pošl<PERSON>š", "simulatedTransaction.unknown.using": "<PERSON>ko {app}", "simulation.approval.modal.text": "<PERSON> sprej<PERSON><PERSON>, da<PERSON>eni aplikaciji/pametni pogodbi dovoljenje za uporabo tvojih žetonov ali NFT-jev v prihodnjih transakcijah.", "simulation.approval.modal.title": "Kaj so odobritve?", "simulation.approval.spend-limit.label": "<PERSON><PERSON><PERSON><PERSON>", "simulation.approve.footer.for": "<PERSON>a", "simulation.approve.unlimited": "Neomejeno", "simulationNotAvailable.title": "<PERSON><PERSON><PERSON><PERSON>", "smart-wallet-activation-view.on": "Na", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password lahko blokira dostop do tvoje denarnice", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Za varno nastavitev Zeala uporabi Apple ali Google", "smart-wallet.passkey-notice.title": "Izogibaj se 1Password", "spend-limits.high.modal.text": "Nastavi omejitev porabe blizu z<PERSON><PERSON>, ki jih bo<PERSON> porabil z aplikacijo ali pametno pogodbo. Visoke omejitve so tvegane in lahko goljufom olajšajo krajo tvojih žetonov.", "spend-limits.high.modal.text_sign_message": "Limit porabe naj bo b<PERSON><PERSON> z<PERSON><PERSON>, ki jih bo<PERSON> uporabil z aplikacijo ali pametno pogodbo. Visoki limiti so tvegani in goljufom olajšajo krajo tvojih žetonov.", "spend-limits.high.modal.title": "<PERSON><PERSON>oka omejitev porabe", "spend-limits.modal.text": "Omejitev porabe določa, koliko žetonov lahko aplikacija porabi v tvojem imenu. To omejitev lahko kadarkoli spremeniš ali odstraniš. Za večjo varnost ohrani omejitve porabe blizu zneska, ki ga boš dejansko porabil z aplikacijo.", "spend-limits.modal.title": "<PERSON><PERSON> je o<PERSON><PERSON>ev porabe?", "spent-limit-info.modal.description": "Limit porabe določa, koliko žetonov lahko aplikacija porabi v tvojem imenu. Limit lahko kadarkoli spremeniš ali odstraniš. Za varnost ohrani limite porabe blizu zneska, ki ga dejansko uporabljaš.", "spent-limit-info.modal.title": "Kaj je limit porabe?", "sswaps-io.transfer-provider": "Ponudnik prenosa", "storage.accountDetails.activateWallet": "Aktiviraj denarnico", "storage.accountDetails.changeWalletLabel": "Spremeni oznako denarnice", "storage.accountDetails.deleteWallet": "Odstrani denarnico", "storage.accountDetails.setup_recovery_kit": "Komplet za obnovitev", "storage.accountDetails.showPrivateKey": "Prikaži zasebni ključ", "storage.accountDetails.showWalletAddress": "Prikaži naslov denarnice", "storage.accountDetails.smartBackup": "Varnostno kopiranje in obnovitev", "storage.accountDetails.viewSsecretPhrase": "Prikaži Skrivno frazo", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets?", "storage.manageAccounts.title": "Denarnice", "submit-userop.progress.text": "Pošiljanje", "submit.error.amount_high": "Znesek je previsok", "submit.error.amount_hight": "Znesek je previsok", "submit.error.amount_low": "Znesek je prenizek", "submit.error.amount_required": "Vnesi znesek", "submit.error.maximum_number_of_characters_exceeded": "Zmanjšaj število znakov v sporočilu", "submit.error.not_enough_balance": "Nezadostno stanje", "submit.error.recipient_required": "Prejemnik je obvezen", "submit.error.routes_not_found": "<PERSON><PERSON> niso najdene", "submitSafeTransaction.monitor.title": "Rezultat transakcije", "submitSafeTransaction.sign.title": "Rezultat transakcije", "submitSafeTransaction.state.sending": "Pošiljanje", "submitSafeTransaction.state.sign": "Ustvarjan<PERSON>", "submitSafeTransaction.submittingToRelayer.title": "Rezultat transakcije", "submitTransaction.cancel": "Prekliči", "submitTransaction.cancel.attemptingToStop": "Poskus zaustavitve", "submitTransaction.cancel.failedToStop": "Zaustavitev neuspešna", "submitTransaction.cancel.stopped": "Zaustavljeno", "submitTransaction.cancel.title": "Predogled transakcije", "submitTransaction.failed.banner.description": "Omrežje je preklicalo transakcijo. Poskusi znova ali nam piši.", "submitTransaction.failed.banner.title": "Transakcija neuspešna", "submitTransaction.failed.execution_reverted.title": "Aplikacija je javila napako", "submitTransaction.failed.execution_reverted_without_message.title": "Aplikacija je javila napako", "submitTransaction.failed.out_of_gas.description": "Preklicano zaradi previsoke omrežnine.", "submitTransaction.failed.out_of_gas.title": "Napaka omrežja", "submitTransaction.sign.title": "Rezultat transakcije", "submitTransaction.speedUp": "Pospeši", "submitTransaction.state.addedToQueue": "Dodano v čakalno vrsto", "submitTransaction.state.addedToQueue.short": "V čakalni vrsti", "submitTransaction.state.cancelled": "Zaustavljeno", "submitTransaction.state.complete": "{currencyCode} dodano v Zeal", "submitTransaction.state.complete.subtitle": "Preveri svoj portfelj v Zeal", "submitTransaction.state.completed": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.failed": "Neuspešno", "submitTransaction.state.includedInBlock": "Vključeno v blok", "submitTransaction.state.includedInBlock.short": "V bloku", "submitTransaction.state.replaced": "Zamenjano", "submitTransaction.state.sendingToNetwork": "Pošiljanje v omrežje", "submitTransaction.stop": "<PERSON><PERSON><PERSON>", "submitTransaction.submit": "<PERSON><PERSON><PERSON>", "submitted-user-operation.state.bundled": "V čakalni vrsti", "submitted-user-operation.state.completed": "<PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.failed": "Neuspešno", "submitted-user-operation.state.pending": "Prenašanje", "submitted-user-operation.state.rejected": "Zavrnjeno", "submittedTransaction.failed.title": "Transakcija neuspešna", "success_splash.card_activated": "Kartica aktivirana", "supportFork.give-feedback.title": "<PERSON><PERSON><PERSON> m<PERSON>", "supportFork.itercom.description": "Zeal: <PERSON><PERSON><PERSON><PERSON> o pologi<PERSON>, don<PERSON><PERSON> itd.", "supportFork.itercom.title": "Vprašanja o denarnici", "supportFork.title": "Poišči pomoč za", "supportFork.zendesk.subtitle": "Gnosis Pay: plačila, identiteta, vračila.", "supportFork.zendesk.title": "Plačila s kartico in identiteta", "supported-networks.ethereum.warning": "<PERSON><PERSON><PERSON> p<PERSON>", "supportedNetworks.networks": "Podprta omrežja", "supportedNetworks.oneAddressForAllNetworks": "En naslov za vsa omrežja", "supportedNetworks.receiveAnyAssets": "Prejemaj sredstva iz podprtih omrežij neposredno v denarnico Zeal z istim naslovom.", "swap.form.error.no_routes_found": "Ni najdenih poti", "swap.form.error.not_enough_balance": "<PERSON><PERSON><PERSON>j sredstev", "swaps-io-details.bank.serviceProvider": "Ponudnik storitev", "swaps-io-details.details.processing": "V obdelavi", "swaps-io-details.pending": "V teku", "swaps-io-details.rate": "<PERSON><PERSON><PERSON>", "swaps-io-details.serviceProvider": "Ponudnik storitev", "swaps-io-details.transaction.from.processing": "Začeta transakcija", "swaps-io-details.transaction.networkFees": "Omrežnine", "swaps-io-details.transaction.state.completed-transaction": "Dokončana transakcija", "swaps-io-details.transaction.state.started-transaction": "Začeta transakcija", "swaps-io-details.transaction.to.processing": "Dokončana transakcija", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Polog bi moral biti kmalu končan. Kinetex še vedno obdeluje tvojo transakcijo.", "swapsIO.monitoring.awaitingLiqSend.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swapsIO.monitoring.awaitingRecive.title": "Prenašanje", "swapsIO.monitoring.awaitingSend.title": "V čakalni vrsti", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Žetoni so bili pos<PERSON>, vendar bodo kmalu vrnjeni. Kinetex ni mogel dokončati ciljne transakcije.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Vrač<PERSON><PERSON>", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Žetoni niso bili preneseni zaradi neznane napake. Poskusi znova.", "swapsIO.monitoring.cancelledNoSlash.title": "Žetoni vrnjeni", "swapsIO.monitoring.cancelledSlashed.subtitle": "Žetoni so bili vrnjeni. Kinetex ni mogel dokončati ciljne transakcije.", "swapsIO.monitoring.cancelledSlashed.title": "Žetoni vrnjeni", "swapsIO.monitoring.completed.title": "<PERSON><PERSON><PERSON><PERSON>", "taker-metadata.earn": "Zasluži z digitalnimi USD s Sky", "taker-metadata.earn.aave": "Zasluži z digitalnimi EUR z Aave", "taker-metadata.earn.aave.cashout24": "Takojšnje izplačilo, 24/7", "taker-metadata.earn.aave.trusted": "Zaupano 27 mrd. $, 2+ leti", "taker-metadata.earn.aave.yield": "Donos se obračunava vsako sekundo", "taker-metadata.earn.chf": "Zasluži v digitalnih CHF", "taker-metadata.earn.chf.cashout24": "Takojšnje izplačilo, 24/7", "taker-metadata.earn.chf.trusted": "Zaupanja vredno z 28 mio CHF", "taker-metadata.earn.chf.yield": "Donos se obračunava vsako sekundo", "taker-metadata.earn.usd.cashout24": "Takojšnje izplačilo, 24/7", "taker-metadata.earn.usd.trusted": "Zaupano 10,7 mrd. $, 5+ let", "taker-metadata.earn.usd.yield": "Donos se obračunava vsako sekundo", "test": "<PERSON><PERSON>", "to.titile": "Prejmeš", "token.groupHeader.cashback": "Vračilo denar<PERSON>", "token.groupHeader.title": "Sredstva", "token.groupHeader.titleWithSum": "Sredstva {sum}", "token.hidden_tokens.page.title": "Skriti žetoni", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "<PERSON><PERSON><PERSON>", "token.widget.cashback_empty": "Še ni transakcij", "token.widget.emptyState": "V denarnici ni žetonov", "tokens.cash": "Gotovina", "top-up-card-from-earn-view.approve.for": "<PERSON>a", "top-up-card-from-earn-view.approve.into": "V", "top-up-card-from-earn-view.swap.from": "Iz", "top-up-card-from-earn-view.swap.to": "V", "top-up-card-from-earn-view.withdraw.to": "Prejemnik", "top-up-card-from-earn.trx.title.approval": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn.trx.title.swap": "Dodaj na kartico", "top-up-card-from-earn.trx.title.withdrawal": "Dvig iz Earn", "topUpDapp.connectWallet": "Poveži denarnico", "topup-fee-breakdown.bungee-fee": "Provizija zunanjega ponudnika", "topup-fee-breakdown.header": "Provizija transakcije", "topup-fee-breakdown.network-fee": "Omrežnina", "topup-fee-breakdown.total-fee": "Skupna provizija", "topup.continue-in-wallet": "Nadaljuj v svoji denarnici", "topup.send.title": "Pošlji", "topup.submit-transaction.close": "<PERSON><PERSON><PERSON>", "topup.submit-transaction.sent-to-wallet": "Pošlji {amount}", "topup.to": "<PERSON>a", "topup.transaction.complete.close": "<PERSON><PERSON><PERSON>", "topup.transaction.complete.try-again": "Poskusi znova", "transaction-request.nonce-too-low.modal.button-text": "<PERSON><PERSON><PERSON>", "transaction-request.nonce-too-low.modal.text": "Transakcija z enako serijsko številko (nonce) je že bila zaključena, zato te transakcije ne moreš več oddati. To se lahko z<PERSON>di, če izvajaš transakcije drugo za drugo ali če poskušaš pospešiti ali preklicati transakcijo, ki je že zaključena.", "transaction-request.nonce-too-low.modal.title": "Transakcija z istim noncem že zaključena.", "transaction-request.replaced.modal.button-text": "<PERSON><PERSON><PERSON>", "transaction-request.replaced.modal.text": "Statusa te transakcije ne moremo spremljati. Ali jo je zamenjala druga transakcija ali pa ima vozlišče RPC težave.", "transaction-request.replaced.modal.title": "Ni mogoče najti statusa transakcije", "transaction.activity.details.modal.close": "<PERSON><PERSON><PERSON>", "transaction.cancel_popup.cancel": "Ne, počakaj", "transaction.cancel_popup.confirm": "Da, ustavi", "transaction.cancel_popup.description": "Za ustavitev plačaj novo omrežnino namesto {oldFee}", "transaction.cancel_popup.description_without_original": "Za zaustavitev plačaj novo omrežnino.", "transaction.cancel_popup.not_supported.subtitle": "Zaustavitev transakcij ni podprta na {network}", "transaction.cancel_popup.not_supported.title": "<PERSON>o", "transaction.cancel_popup.stopping_fee": "Omrežnina za zaustavitev", "transaction.cancel_popup.title": "<PERSON><PERSON><PERSON><PERSON>?", "transaction.in-progress": "V teku", "transaction.inProgress": "V teku", "transaction.speed_up_popup.cancel": "Ne, počakaj", "transaction.speed_up_popup.confirm": "Da, pospeši", "transaction.speed_up_popup.description": "Za pospešitev moraš plačati novo omrežnino namesto prvotne v višini {amount}", "transaction.speed_up_popup.description_without_original": "Za pospešitev moraš plačati novo omrežnino", "transaction.speed_up_popup.seed_up_fee_title": "Omrežnina za pospešitev", "transaction.speed_up_popup.title": "Pospeši transakcijo?", "transaction.speedup_popup.not_supported.subtitle": "Pospeševanje transakcij ni podprto na {network}", "transaction.speedup_popup.not_supported.title": "<PERSON>o", "transaction.subTitle.failed": "Neuspešno", "transactionDetails.cashback.not-qualified": "Ni <PERSON>", "transactionDetails.cashback.paid": "{amount} i<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionDetails.cashback.pending": "{amount} v čakanju", "transactionDetails.cashback.title": "Vračilo denar<PERSON>", "transactionDetails.cashback.unknown": "Neznano", "transactionDetails.cashback_estimate": "Ocena vračila denarja", "transactionDetails.category": "Kategorija", "transactionDetails.exchangeRate": "Menjalni tečaj", "transactionDetails.location": "Lokacija", "transactionDetails.payment-approved": "<PERSON><PERSON><PERSON><PERSON>", "transactionDetails.payment-declined": "Plač<PERSON>avrn<PERSON>", "transactionDetails.payment-reversed": "<PERSON><PERSON><PERSON><PERSON>", "transactionDetails.recharge.amountSentFromEarn.title": "Znesek, poslan iz Earn računa", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "Napolnjeno na kartico", "transactionDetails.recharge.rate.title": "<PERSON><PERSON><PERSON>", "transactionDetails.recharge.transactionId.title": "ID transakcije", "transactionDetails.refund": "Vrač<PERSON>", "transactionDetails.reversal": "Stornacija", "transactionDetails.transactionCurrency": "Valuta transakcije", "transactionDetails.transactionId": "ID transakcije", "transactionDetails.type": "Transakcija", "transactionRequestWidget.approve.subtitle": "Za {target}", "transactionRequestWidget.p2p.subtitle": "Za {target}", "transactionRequestWidget.unknown.subtitle": "Prek {target}", "transactionSafetyChecksPopup.title": "Varnostna preverjanja transakcije", "transactions.main.activity.title": "Aktivnost", "transactions.page.hiddenActivity.title": "Skrita aktivnost", "transactions.page.title": "Aktivnost", "transactions.viewTRXHistory.emptyState": "Še ni transakcij", "transactions.viewTRXHistory.errorMessage": "Nalaganje zgodovine transakcij ni uspelo", "transactions.viewTRXHistory.hidden.emptyState": "Ni skritih transakcij", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Aktivnost za testna omrežja ni podprta", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Aktivnost za testna omrežja ni podprta{br}<link>Odpri explorer</link>", "transfer_provider": "Ponudnik prenosa", "transfer_setup_with_different_wallet.subtitle": "Bančni prenosi so nastavljeni z drugo denarnico. S prenosi je lahko povezana samo ena denarnica.", "transfer_setup_with_different_wallet.swtich_and_continue": "Preklopi in nadaljuj", "transfer_setup_with_different_wallet.title": "Zamenjaj denarnico", "tx-sent-to-wallet.button": "<PERSON><PERSON><PERSON>", "tx-sent-to-wallet.subtitle": "<PERSON><PERSON><PERSON><PERSON> v {wallet}", "unblockProviderInfo.fees": "Zagotovljene imaš najnižje možne provizije: 0 % do 5 tisoč $ na mesec in 0,2 % nad tem zneskom.", "unblockProviderInfo.registration": "Unblock je registriran in pooblaščen s strani FNTT za opravljanje storitev menjave in hrambe VASP ter je registriran ponudnik MSB pri ameriškem Fincenu. <link>Več o tem</link>", "unblockProviderInfo.selfCustody": "<PERSON><PERSON>, ki ga prej<PERSON>, je v tvoji zasebni hrambi in nihče drug nima nadzora nad tvojimi sredstvi.", "unblock_invalid_faster_payment_configuration.subtitle": "Navedeni bančni račun ne podpira evropskih prenosov SEPA ali britanskih hitrih plačil (Faster Payments). Navedi drug račun.", "unblock_invalid_faster_payment_configuration.title": "Potreben je drug račun", "unknownTransaction.primaryText": "Transakcija s kartico", "unsupportedCountry.subtitle": "Bančni prenosi v tvoji državi še niso na voljo.", "unsupportedCountry.title": "Ni na voljo v {country}", "update-app-popup.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> poso<PERSON><PERSON>ev prinaša popravke, nove funkcije in še več čarovnije. Posodobi na najnovejšo različico in izboljšaj svojo izkušnjo z Zealom.", "update-app-popup.title": "Posodobi različico Zeal", "update-app-popup.update-now": "Posodobi zdaj", "user_associated_with_other_merchant.subtitle": "Te denarnice ni mogoče uporabiti za bančne prenose. Uporabi drugo denarnico ali se za podporo in posodobitve obrni na naš Discord.", "user_associated_with_other_merchant.title": "Denarnice ni mogoče uporabiti", "user_associated_with_other_merchant.try_with_another_wallet": "Poskusi z drugo denarnico", "user_email_already_exists.subtitle": "Bančne prenose si že nastavil z drugo denarnico. Poskusi znova z denarnico, ki si jo uporabil prej.", "user_email_already_exists.title": "Prenosi nastavljeni z drugo denarnico", "user_email_already_exists.try_with_another_wallet": "Poskusi z drugo denarnico", "validation.invalid.iban": "Neveljaven IBAN", "validation.required": "Obvezno", "validation.required.first_name": "<PERSON>me je obvezno", "validation.required.iban": "IBAN je obvezen", "validation.required.last_name": "Priimek je obvezen", "verify-passkey.cta": "Potrdi pristopni ključ", "verify-passkey.subtitle": "<PERSON><PERSON><PERSON>, ali je tvoj pristopni ključ ustvarjen in ustrezno zavarovan.", "verify-passkey.title": "Potrdi pristopni ključ", "view-cashback.cashback-next-cycle": "Stopnja vračila denarja čez {time}", "view-cashback.no-cashback": "0 %", "view-cashback.no-cashback.subtitle": "Položi za vračilo denarja", "view-cashback.pending": "{money} v čakanju", "view-cashback.pending-rewards.not_paid": "<PERSON><PERSON><PERSON> {days} d", "view-cashback.pending-rewards.paid": "Prejeto ta teden", "view-cashback.received-rewards": "Prejete nagrade", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Nepotrjena p<PERSON>čila", "view-cashback.upcoming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {money}", "virtual-card-order.configure-safe.loading-text": "Ustvarjanje kartice", "virtual-card-order.create-order.loading-text": "Aktiviranje kartice", "virtual-card-order.create-order.success-text": "Kartica aktivirana", "virtualCard.activateCard": "Aktiviraj kartico", "walletDeleteConfirm.main_action": "Odstrani", "walletDeleteConfirm.subtitle": "<PERSON><PERSON> ogled port<PERSON>lja ali izvajanje transakcij jo boš <PERSON> ponovno uvoziti", "walletDeleteConfirm.title": "Odstranim denarnico?", "walletSetting.header": "Nastavitve denarnice", "wallet_connect.connect.cancel": "Prekliči", "wallet_connect.connect.connect_button": "<PERSON><PERSON><PERSON><PERSON>", "wallet_connect.connect.title": "Poveži", "wallet_connect.connected.title": "Povezano", "wallet_connect_add_chain_missing.title": "Omrežje ni podprto", "wallet_connect_proposal_expired.title": "Povezava je potekla", "withdraw": "<PERSON><PERSON><PERSON>", "withdraw.confirmation.close": "Prekliči", "withdraw.confirmation.continue": "<PERSON><PERSON><PERSON>", "withdrawal_request.completed": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal_request.pending": "V teku", "zeal-dapp.connect-wallet.cta.primary.connecting": "Povezovanje...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Poveži", "zeal-dapp.connect-wallet.cta.secondary": "Prekliči", "zeal-dapp.connect-wallet.title": "Poveži denarnico za nadaljevanje", "zealSmartWalletInfo.gas": "Plačaj omrežnino z mnogimi žetoni; uporabljaj priljubljene žetone ERC20 na podprtih verigah za plačilo omrežnine, ne le osnovnih žetonov", "zealSmartWalletInfo.recover": "Brez Skrivnih fraz; obnovitev z biometričnim pristopnim ključem iz tvojega upravitelja gesel, iClouda ali Google računa.", "zealSmartWalletInfo.selfCustodial": "Popolnoma zasebna denarnica; Podpisi pristopnega ključa se preverjajo na verigi blokov, da se zmanjša centralna odvisnost.", "zealSmartWalletInfo.title": "O denarnicah Zeal Smart Wallet", "zeal_a_rewards_already_claimed_error.title": "Nagrada je že prevzeta", "zwidget.minimizedDisconnected.label": "Zeal nima povezave"}