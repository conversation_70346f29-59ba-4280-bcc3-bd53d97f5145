{"Account.ListItem.details.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AddFromAddress.success": "Piniginė išsaugota", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{Nėra piniginių} one{{count} piniginė} other{{count} piniginių}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "<PERSON><PERSON><PERSON><PERSON> frazė {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Sukurk naujas pinigines iš esamos slaptosios frazės", "AddFromExistingSecretPhrase.SelectPhrase.title": "Pasirink slaptąją frazę", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "Tavo slaptoji frazė gali apsaugoti daug piniginių. <PERSON><PERSON><PERSON> tas, kurias nori naudoti.", "AddFromExistingSecretPhrase.WalletSelection.title": "Greitai pridė<PERSON>", "AddFromExistingSecretPhrase.success": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> į Zeal", "AddFromHardwareWallet.subtitle": "Pasirink aparatinę pinigin<PERSON>, kurią nori prijungti prie Zeal", "AddFromHardwareWallet.title": "Aparatinė pinigi<PERSON>ė", "AddFromNewSecretPhrase.WalletSelection.subtitle": "Pa<PERSON>ink pinigines, kurias nori <PERSON>ti", "AddFromNewSecretPhrase.WalletSelection.title": "Importuoti pinigines", "AddFromNewSecretPhrase.accounts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "Slaptoji frazė veikia kaip raktų pakabukas milijonams piniginių, kurių kiekviena turi unikalų privatų raktą.{br}{br}Gali importuoti tiek piniginių, kiek tau reikia dabar, arba pridėti daugiau vėliau.", "AddFromNewSecretPhrase.secretPhraseTip.title": "<PERSON><PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.subtitle": "Įvesk savo slaptą<PERSON>ą frazę, <PERSON><PERSON><PERSON><PERSON> atskirdamas tarpais", "AddFromNewSecretPhrase.success_secret_phrase_added": "<PERSON><PERSON><PERSON><PERSON> fraz<PERSON> prid<PERSON>ta 🎉", "AddFromNewSecretPhrase.success_wallets_added": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> į Zeal", "AddFromNewSecretPhrase.wallets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AddFromPrivateKey.subtitle": "Įvesk savo privatų raktą", "AddFromPrivateKey.success": "Privatus raktas pridėtas 🎉", "AddFromPrivateKey.title": "Atkurti piniginę", "AddFromPrivateKey.typeOrPaste": "Įrašyk arba įklijuok čia", "AddFromSecretPhrase.importWallets": "{count,plural,=0{Nepasirinkta piniginių} one{Importuoti piniginę} other{Importuoti {count} piniginių}}", "AddFromTrezor.AccountSelection.title": "Importuoti Trezor <PERSON>", "AddFromTrezor.hwWalletTip.subtitle": "Aparatinėje piniginėje saugomi milijonai piniginių su skirtingais adresais. Gali importuoti tiek piniginių, kiek reikia dabar, arba pridėti daugiau vėliau.", "AddFromTrezor.hwWalletTip.title": "Importavimas iš aparatinių piniginių", "AddFromTrezor.importAccounts": "{count,plural,=0{Nepasirinkta piniginių} one{Importuoti piniginę} other{Importuoti {count} pinigines}}", "AddFromTrezor.success": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> į Zeal", "ApprovalSpenderTypeCheck.failed.subtitle": "Tikėtina apgaulė: <PERSON><PERSON><PERSON><PERSON> t<PERSON> valdyti sutartys", "ApprovalSpenderTypeCheck.failed.title": "Lėš<PERSON> valdytoja<PERSON> – piniginė, o ne sutartis", "ApprovalSpenderTypeCheck.passed.subtitle": "Paprastai turtą tvirtinate sutartims", "ApprovalSpenderTypeCheck.passed.title": "Lėš<PERSON> v<PERSON> – išmanioji sutartis", "BestReturns.subtitle": "<PERSON><PERSON> keitimo tie<PERSON> p<PERSON> g<PERSON>, įskaitant visus moke<PERSON>.", "BestReturnsPopup.title": "Geriausia grąža", "BlacklistCheck.Failed.subtitle": "Kenksmingi pranešimai iš <source></source>", "BlacklistCheck.Failed.title": "Svetainė įtraukta į juodąjį sąrašą", "BlacklistCheck.Passed.subtitle": "Nėra kenksmingų pranešimų iš <source></source>", "BlacklistCheck.Passed.title": "Svetainė neįtraukta į juodąjį sąrašą", "BlacklistCheck.failed.statusButton.label": "<PERSON><PERSON> svet<PERSON> p<PERSON>", "BridgeRoute.slippage": "Slydimas {slippage}", "BridgeRoute.title": "Bridge teik<PERSON>", "CheckConfirmation.InProgress": "Vykdoma...", "CheckConfirmation.success.splash": "Atlikta", "ChooseImportOrCreateSecretPhrase.subtitle": "Importuok slaptąją frazę arba sukurk naują", "ChooseImportOrCreateSecretPhrase.title": "<PERSON><PERSON><PERSON><PERSON> frazę", "ConfirmTransaction.Simuation.Skeleton.title": "<PERSON><PERSON><PERSON><PERSON> saugumo <PERSON>...", "ConnectionSafetyCheckResult.passed": "<PERSON><PERSON><PERSON><PERSON> pat<PERSON>", "ContactGnosisPaysupport": "Susisiekti su Gnosis Pay", "CopyKeyButton.copied": "Nukopijuota", "CopyKeyButton.copyYourKey": "Kopijuoti raktą", "CopyKeyButton.copyYourPhrase": "Kopijuoti savo frazę", "DAppVerificationCheck.Failed.subtitle": "Svetainė neįtraukta į <source></source>", "DAppVerificationCheck.Failed.title": "Svetainė nerasta programėlių registruose", "DAppVerificationCheck.Passed.subtitle": "Svetainė įtraukta į <source></source>", "DAppVerificationCheck.Passed.title": "Svetainė yra programėlių registruose", "DAppVerificationCheck.failed.statusButton.label": "Svetainė nerasta programėlių registruose", "ERC20.tokens.emptyState": "Žetonų nerasta", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "<PERSON><PERSON><PERSON> m<PERSON>", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "<PERSON><PERSON><PERSON><PERSON> {period}: nuo {from} iki {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "<PERSON>zin<PERSON> mokestis: {baseFee} • Saugumo rezervas: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Nepavyko gauti da<PERSON><PERSON>io bazi<PERSON> m<PERSON>", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Didesnis už <PERSON>ą „Nonce“. Gali įstrigti", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "„Nonce“ negali būti ma<PERSON> už dabartinį.", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "Nepavyko a<PERSON>čiuoti prioriteto mokesč<PERSON>", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Nepavyko gauti da<PERSON><PERSON><PERSON> ma<PERSON><PERSON>", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "<PERSON><PERSON><PERSON> m<PERSON>is", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Gali įstrigti, kol sumažės tinklo m<PERSON>č<PERSON>i", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "<PERSON><PERSON><PERSON> bazi<PERSON> m<PERSON>", "EditFeeModal.Custom.gasLimit.title": "<PERSON><PERSON><PERSON> limitas {gasLimit}", "EditFeeModal.Custom.title": "Išplėstiniai nustatymai", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Įstrigs, kol suma<PERSON>ės bazinis moke<PERSON>is", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Ma<PERSON>as mokestis. Gali įstrigti", "EditFeeModal.EditGasLimit.estimatedGas": "Apyt. dujos: {estimated} • Saugumo rezervas: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Mažiau nei numatytas limitas. Pavedimas nepavyks", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Mažiau nei si<PERSON> limit<PERSON>. Pavedimas gali nepavykti", "EditFeeModal.EditGasLimit.subtitle": "Nustatyk didžiausią dujų kiekį šiam pavedimui. Jei limitas bus per mažas, pavedimas nepavyks.", "EditFeeModal.EditGasLimit.title": "Redaguoti dujų limitą", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "<PERSON><PERSON><PERSON><PERSON> nei minimalus tinklo moke<PERSON> limitas: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "Didesnis nei kitas „Nonce“. Pavedimas įstrigs", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "Negalima nustatyti mažesnio „Nonce“ nei da<PERSON>is", "EditFeeModal.EditNonce.subtitle": "<PERSON>vo pavedima<PERSON> įstrigs, jei nustatysi ne kitą Nonce", "EditFeeModal.EditNonce.title": "Redagu<PERSON><PERSON>", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "<PERSON><PERSON><PERSON> {amount} , kad pateiktum", "EditFeeModal.Header.Time.unknown": "<PERSON><PERSON>", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Maks. {fee}", "EditFeeModal.Header.fee.unknown": "<PERSON><PERSON><PERSON>", "EditFeeModal.Header.subsequent_failed": "Apskaičiavimai gali būti p<PERSON>, p<PERSON><PERSON><PERSON> atnaujinima<PERSON>", "EditFeeModal.Layout.Header.ariaLabel": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>is", "EditFeeModal.MaxFee.subtitle": "<PERSON><PERSON><PERSON><PERSON> mokestis yra did<PERSON>a, k<PERSON><PERSON> mokėsi už <PERSON>, bet dažniausiai mokėsi numatytą mokestį. Šis papildomas rezervas padeda paved<PERSON>ui įvykti, net jei tinklas sulėtėja ar pabrangsta.", "EditFeeModal.MaxFee.title": "<PERSON><PERSON><PERSON><PERSON> tin<PERSON> m<PERSON>", "EditFeeModal.SelectPreset.Time.unknown": "<PERSON><PERSON>", "EditFeeModal.SelectPreset.ariaLabel": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>č<PERSON>", "EditFeeModal.SelectPreset.fast": "G<PERSON><PERSON>", "EditFeeModal.SelectPreset.normal": "Normalus", "EditFeeModal.SelectPreset.slow": "Lėtas", "EditFeeModal.ariaLabel": "Redaguoti tinklo mokestį", "FailedSimulation.Confirmation.Item.subtitle": "Įvyko vid<PERSON><PERSON> k<PERSON>a", "FailedSimulation.Confirmation.Item.title": "Nepavyko simuli<PERSON>", "FailedSimulation.Confirmation.subtitle": "Ar tikrai nori tęsti?", "FailedSimulation.Confirmation.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> aklai", "FailedSimulation.Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FailedSimulation.footer.subtitle": "Įvyko vid<PERSON><PERSON> k<PERSON>a", "FailedSimulation.footer.title": "Nepavyko simuli<PERSON>", "FeeForecastWidget.NotEnoughBalance.errorMessage": "<PERSON><PERSON><PERSON> {amount} <PERSON><PERSON><PERSON>", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Apdorojimas gali užtrukti", "FeeForecastWidget.networkFee": "<PERSON><PERSON><PERSON>", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Nepavyko a<PERSON>kaičiuoti tinklo m<PERSON>", "FeeForecastWidget.subsequentFailed.message": "Skaičiavimai gali būti pase<PERSON>, atnaujinti ne<PERSON>", "FeeForecastWidget.unknownDuration": "Nežinoma", "FeeForecastWidget.unknownFee": "Nežinoma", "GasCurrencySelector.balance": "Likutis: {balance}", "GasCurrencySelector.networkFee": "<PERSON><PERSON><PERSON>", "GasCurrencySelector.payNetworkFeesUsing": "<PERSON><PERSON><PERSON><PERSON> m<PERSON> naudo<PERSON>", "GasCurrencySelector.removeDefaultGasToken.description": "<PERSON><PERSON><PERSON><PERSON> m<PERSON> iš <PERSON> likučio", "GasCurrencySelector.removeDefaultGasToken.title": "Automatinis moke<PERSON>čių tvarkymas", "GasCurrencySelector.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "GoogleDriveBackup.BeforeYouBegin.first_point": "<PERSON><PERSON> pami<PERSON><PERSON><PERSON> savo <PERSON> slaptažodį, visam laikui prarasiu savo turtą", "GoogleDriveBackup.BeforeYouBegin.second_point": "<PERSON>i prarasiu prieigą prie „Google Drive“ arba pakeisiu atkūrimo failą, visam laikui prarasiu savo turtą", "GoogleDriveBackup.BeforeYouBegin.subtitle": "Suprask ir sutik su šiuo teiginiu apie privačią piniginę:", "GoogleDriveBackup.BeforeYouBegin.third_point": "Zeal negalės man pad<PERSON><PERSON> atkurti Zeal slaptažodžio ar prieigos prie „Google Drive“", "GoogleDriveBackup.BeforeYouBegin.title": "<PERSON><PERSON><PERSON> p<PERSON>", "GoogleDriveBackup.loader.subtitle": "Patvirtink užklausą „Google Drive“, kad įkeltum atkūrimo failą", "GoogleDriveBackup.loader.title": "<PERSON><PERSON><PERSON>...", "GoogleDriveBackup.success": "Atsarginė kopija s<PERSON>kminga 🎉", "MonitorOffRamp.overServiceTime": "Dauguma pavedimų atliekami per {estimated_time}, tačiau kartais dėl papildomų patikrinimų jie gali užtrukti ilgiau. Tai normalu, o l<PERSON>š<PERSON> yra saug<PERSON>, kol atliekami šie patikrinimai.{br}{br}Jei operacija nebus baigta per {support_soft_deadline}, {contact_support}", "MonitorOnRamp.contactSupport": "Susisiek su palaikymo komanda", "MonitorOnRamp.from": "<PERSON><PERSON>", "MonitorOnRamp.fundsReceived": "Lėš<PERSON> g<PERSON>", "MonitorOnRamp.overServiceTime": "Dauguma pavedimų atliekami per {estimated_time}, bet gali užtrukti ilgiau. Lėšos saugios.{br}{br}Jei pavedimas nebus atliktas per {support_soft_deadline}, prašome {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Siunčiama į tavo piniginę", "MonitorOnRamp.to": "Į", "MonitorOnRamp.waitingForTransfer": "Lau<PERSON>ama, kol pervesi lėšas", "NftCollectionCheck.failed.subtitle": "Kolekcija nepatvirtinta <source></source>", "NftCollectionCheck.failed.title": "Kolekcija nepatvirtinta", "NftCollectionCheck.passed.subtitle": "Kolekcija patvirtinta <source></source>", "NftCollectionCheck.passed.title": "Kolek<PERSON><PERSON>", "NftCollectionInfo.entireCollection": "Visa kolekcija", "NoSigningKeyStore.createAccount": "Sukurti paskyrą", "NonceRangeError.biggerThanCurrent.message": "Pavedimas įstrigs", "NonceRangeError.lessThanCurrent.message": "Pavedimas ne<PERSON>vy<PERSON>", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "„Nonce“ yra didesnis nei da<PERSON>. Sumažink „Nonce“, kad <PERSON> neįstrigtų.", "NonceRangeErrorPopup.biggerThanCurrent.title": "Pavedimas įstrigs", "P2pReceiverTypeCheck.failed.subtitle": "Ar siunti teisingu adresu?", "P2pReceiverTypeCheck.failed.title": "Gavėjas – <PERSON><PERSON><PERSON><PERSON><PERSON>, o ne piniginė", "P2pReceiverTypeCheck.passed.subtitle": "Paprastai turtą siunti į kitas pinigines", "P2pReceiverTypeCheck.passed.title": "Gavėjas – piniginė", "PasswordCheck.title": "Įvesti slaptažodį", "PasswordChecker.subtitle": "Įvesk savo slaptažodį, kad <PERSON>, jog tai tu.", "PermitExpirationCheck.failed.subtitle": "Nustatyk trumpą ir tik reikiamą laikotarpį", "PermitExpirationCheck.failed.title": "Ilgas galiojimo laikas", "PermitExpirationCheck.passed.subtitle": "<PERSON><PERSON> laiko <PERSON><PERSON><PERSON><PERSON> gal<PERSON>s naudoti tavo žetonus", "PermitExpirationCheck.passed.title": "Galiojimo laikas n<PERSON>ra per ilgas", "PrivateKeyValidationError.moreThanMaximumWords": "Daugiausia {count} žodžių", "PrivateKeyValidationError.notValidPrivateKey": "Tai nėra galiojantis privatus raktas", "PrivateKeyValidationError.secretPhraseIsInvalid": "<PERSON><PERSON><PERSON> frazė yra ne<PERSON>", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Žodis #{index} parašytas su klaida arba neteisingas", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} žod<PERSON>} other{{count} ž<PERSON><PERSON><PERSON><PERSON>}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "<PERSON><PERSON><PERSON><PERSON> fraz<PERSON>s ir privatūs raktai yra užšif<PERSON>oti ir niekada nepalieka šio įrenginio", "SecretPhraseReveal.header": "Užsirašyk slaptažodžio frazę", "SecretPhraseReveal.hint": "Niekam neatskleisk savo frazės. Laikyk ją saugiai neprisijungęs prie interneto", "SecretPhraseReveal.skip.subtitle": "Nors gali tai pad<PERSON>ti v<PERSON>, p<PERSON><PERSON>ęs šį įrenginį prieš už<PERSON> fraz<PERSON>, prarasi visą turtą, kurį įkėlei į šią piniginę.", "SecretPhraseReveal.skip.takeTheRisk": "Rizikuosiu", "SecretPhraseReveal.skip.title": "Praleisti frazės užrašymą?", "SecretPhraseReveal.skip.writeDown": "Užsirašyti", "SecretPhraseReveal.skipForNow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SecretPhraseReveal.subheader": "Užsirašyk ją ir laikyk saugiai neprisijungęs prie interneto. Tada paprašysime ją pat<PERSON>.", "SecretPhraseReveal.verify": "<PERSON><PERSON><PERSON><PERSON>", "SelectCurrency.tokens": "Žetonai", "SelectCurrency.tokens.emptyState": "Žetonų nerasta", "SelectRoute.slippage": "Slydimas {slippage}", "SelectRoutes.emptyState": "<PERSON><PERSON> keit<PERSON> neradome joki<PERSON> ma<PERSON>š<PERSON>", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "<PERSON>rijun<PERSON><PERSON> pin<PERSON>", "SendERC20.labelAddress.inputPlaceholder": "Pi<PERSON>gin<PERSON><PERSON> p<PERSON>", "SendERC20.labelAddress.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>igi<PERSON>ę, kad vėliau ją rastum.", "SendERC20.labelAddress.title": "Pažymėti šią piniginę", "SendERC20.send_to": "Siųsti kam", "SendERC20.tokens": "Valiutos", "SendOrReceive.bankTransfer.primaryText": "Banko <PERSON>", "SendOrReceive.bankTransfer.shortText": "<PERSON><PERSON><PERSON>, nemokamas įnešimas/iš<PERSON><PERSON><PERSON>", "SendOrReceive.bridge.primaryText": "Bridge", "SendOrReceive.bridge.shortText": "Perkelk tokenus tarp tinklų", "SendOrReceive.receive.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.receive.shortText": "<PERSON><PERSON><PERSON> <PERSON><PERSON> ar kolek<PERSON>us daiktus", "SendOrReceive.send.primaryText": "Si<PERSON>sti", "SendOrReceive.send.shortText": "Siųsk tokenus bet kuriuo adresu", "SendOrReceive.swap.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.swap.shortText": "Keisk vienus tokenus į kitus", "SendSafeTransaction.Confirm.loading": "Atliekamos saug<PERSON>o pat<PERSON>…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Užšifruoti atkūrimo failą slaptažodžiu", "SetupRecoveryKit.google.subtitle": "Sin<PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "SetupRecoveryKit.google.title": "„Google Drive“ atsarginė kopija", "SetupRecoveryKit.subtitle": "<PERSON> reikės bent vieno būdo atkurti p<PERSON>ą, jei p<PERSON><PERSON><PERSON> arba pakeisi įrenginį", "SetupRecoveryKit.title": "Nustatyti atkūrimo rinkinį", "SetupRecoveryKit.writeDown.subtitle": "Užsirašyti slaptažodžio frazę", "SetupRecoveryKit.writeDown.title": "<PERSON><PERSON><PERSON> kopija", "Sign.CheckSafeDeployment.activate": "Aktyvuoti", "Sign.CheckSafeDeployment.subtitle": "Prieš prisijungdamas prie programėlės ar pasira<PERSON> ne tinklo žinutę, turi aktyvuoti savo įrenginį šiame tinkle. Tai atliekama įdiegus arba atkūrus išmaniąją piniginę.", "Sign.CheckSafeDeployment.title": "Aktyvuoti įrenginį šiame tinkle", "Sign.Simuation.Skeleton.title": "Atliekamos saug<PERSON>o pat<PERSON>…", "SignMessageSafetyCheckResult.passed": "<PERSON><PERSON><PERSON><PERSON>", "SignMessageSafetyChecksPopup.title.permits": "Leidimų saugumo patikros", "SimulationFailedConfirmation.subtitle": "Mes simuliavome šį pavedimą ir aptikome problemą, d<PERSON><PERSON> kurios jis nepavyks. <PERSON><PERSON> pateikt<PERSON> paved<PERSON>, bet jis greičiausiai nepavyks ir gali prarasti tinklo mokestį.", "SimulationFailedConfirmation.title": "<PERSON><PERSON><PERSON><PERSON>, kad <PERSON><PERSON>", "SimulationNotSupported.Title": "Simulia<PERSON><PERSON>{br}palaikoma{br}{network}", "SimulationNotSupported.footer.subtitle": "Vis tiek gali pateikti šį pavedimą", "SimulationNotSupported.footer.title": "Simuliacija <PERSON>", "SlippagePopup.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SlippagePopup.presetsHeader": "<PERSON><PERSON><PERSON>", "SlippagePopup.title": "Nuok<PERSON><PERSON> n<PERSON>", "SmartContractBlacklistCheck.failed.subtitle": "Piktybiniai pranešimai iš <source></source>", "SmartContractBlacklistCheck.failed.title": "Sutartis įtraukta į juodąjį sąrašą", "SmartContractBlacklistCheck.passed.subtitle": "Nėra piktybinių pranešimų iš <source></source>", "SmartContractBlacklistCheck.passed.title": "Sutartis neįtraukta į juodąjį sąrašą", "SuspiciousCharactersCheck.Failed.subtitle": "Tai įprasta sukčiavimo taktika", "SuspiciousCharactersCheck.Failed.title": "Tikriname d<PERSON>l įprastų sukčiavimo šablonų", "SuspiciousCharactersCheck.Passed.subtitle": "Tikriname d<PERSON>l bandymų sukčiauti", "SuspiciousCharactersCheck.Passed.title": "Adrese nėra neįprastų simbolių", "SuspiciousCharactersCheck.failed.statusButton.label": "Neįprasti simboliai adrese ", "TokenVerificationCheck.failed.subtitle": "Žetonas neįtrauktas į sąrašą <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} nepatvirtintas CoinGecko", "TokenVerificationCheck.passed.subtitle": "Žetonas įtrauktas į sąrašą <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} patvirtintas CoinGecko", "TopupDapp.MonitorTransaction.success.splash": "Atlikta", "TransactionSafetyCheckResult.passed": "<PERSON><PERSON><PERSON><PERSON>", "TransactionSimulationCheck.failed.subtitle": "Klaida: {errorMessage}", "TransactionSimulationCheck.failed.title": "<PERSON><PERSON><PERSON><PERSON>, kad <PERSON><PERSON>", "TransactionSimulationCheck.passed.subtitle": "Simuliacija atlikta naudojant <source></source>", "TransactionSimulationCheck.passed.title": "<PERSON><PERSON><PERSON>", "TrezorError.trezor_action_cancelled.action": "Uždaryti", "TrezorError.trezor_action_cancelled.subtitle": "Atmetei pavedimą savo aparatinėje piniginėje", "TrezorError.trezor_device_used_elsewhere.action": "Sin<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "Uždaryk visas kitas atidarytas sesijas ir bandyk sinchronizuoti Trezor dar kartą", "TrezorError.trezor_method_cancelled.action": "Sin<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "Leisk Trezor eksportuoti pinigines į Zeal", "TrezorError.trezor_permissions_not_granted.action": "Sin<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "<PERSON><PERSON><PERSON> leidimus matyti visas pinigines", "TrezorError.trezor_pin_cancelled.action": "Sin<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "<PERSON><PERSON><PERSON> įrenginyje", "TrezorError.trezor_popup_closed.action": "Sin<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "<PERSON><PERSON><PERSON> dialogo langas netikėtai užsidarė", "TrxLikelyToFail.lessThanEstimatedGas.message": "Pavedimas ne<PERSON>vy<PERSON>", "TrxLikelyToFail.lessThanMinimumGas.message": "Pavedimas ne<PERSON>vy<PERSON>", "TrxLikelyToFail.lessThanSuggestedGas.message": "<PERSON><PERSON> ne<PERSON>i", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "Pavedimo duj<PERSON> limitas yra per mažas. Padidink dujų limitą iki <PERSON>, kad išvengtum ne<PERSON>ėkmės.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "<PERSON><PERSON><PERSON><PERSON>, kad <PERSON><PERSON>", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Dujų limitas yra mažesnis nei apskaičiuotas. Padidink dujų limitą iki si<PERSON>lo<PERSON>.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "Pavedimas ne<PERSON>vy<PERSON>", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "<PERSON><PERSON><PERSON><PERSON> bazinis mokestis yra mažesnis už dabartinį. Padidink maksimalų bazinį mokestį, kad paved<PERSON><PERSON> neįstrigtų.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "Pavedimas įstrigs", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "<PERSON><PERSON><PERSON><PERSON> paved<PERSON> moke<PERSON> yra per mažas. Padidink maksimalų mokestį, kad pavedima<PERSON> neįstrigtų.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "Pavedimas įstrigs", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Prioriteto mokestis yra maž<PERSON> nei rekomenduojama. Padidink prioriteto mokestį, kad paspartintum pavedimą.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "Pavedimas gali užtrukti ilgai", "UnsupportedMobileNetworkLayout.gotIt": "Supratau!", "UnsupportedMobileNetworkLayout.subtitle": "Kol kas negali atlikti operacijų ar pasirašyti pranešimų tinkle su ID {networkHexId} su Zeal mobiliąja versija.{br}{br}Naudok narš<PERSON> plėtinį, kad galėtum atlikti operacijas šiame tinkle, kol mes intensyviai dirbame, kad pridėtume šio tinklo palaikymą 🚀", "UnsupportedMobileNetworkLayout.title": "Tinklas <PERSON> Zeal mobiliojoje versijoje", "UnsupportedSafeNetworkLayout.subtitle": "Negali atlikti pavedimų ar pasirašyti pranešimų tinkle {network} su Zeal išmaniąja pinigine{br}{br}Perjunk į palaikomą tinklą arba naudok senąją piniginę.", "UnsupportedSafeNetworkLayoutk.title": "Tinklas nepalaikomas išmaniosioms piniginėms", "UserConfirmationPopup.goBack": "<PERSON><PERSON><PERSON><PERSON>", "UserConfirmationPopup.submit": "<PERSON><PERSON><PERSON><PERSON>", "ViewPrivateKey.header": "<PERSON><PERSON><PERSON><PERSON> raktas", "ViewPrivateKey.hint": "Niekam neatskleisk savo privataus rakto. Laikyk jį saugiai neprisijungęs prie interneto", "ViewPrivateKey.subheader.mobile": "Paliesk, kad pamatytum savo privatų raktą", "ViewPrivateKey.subheader.web": "Užvesk pelę, kad pamatytum savo privatų raktą", "ViewPrivateKey.unblur.mobile": "Paliesk, kad pamatytum", "ViewPrivateKey.unblur.web": "Užvesk pelę, kad pama<PERSON>", "ViewSecretPhrase.PasswordChecker.subtitle": "Įvesk slaptažodį, kad užš<PERSON>ruotum atkūrimo failą. Jį reikės atsiminti ateityje.", "ViewSecretPhrase.done": "Atlikta", "ViewSecretPhrase.header": "Slaptažodžio frazė", "ViewSecretPhrase.hint": "Niekam neatskleisk savo frazės. Laikyk ją saugiai neprisijungęs prie interneto", "ViewSecretPhrase.subheader.mobile": "<PERSON>liesk, kad pamatytum savo slaptažodžio frazę", "ViewSecretPhrase.subheader.web": "Užves<PERSON> pelę, kad pamatytum savo slaptažodžio frazę", "ViewSecretPhrase.unblur.mobile": "Paliesk, kad pamatytum", "ViewSecretPhrase.unblur.web": "Užvesk pelę, kad pama<PERSON>", "account-details.monerium": "Pavedimai atliekami per Monerium, autorizuotą ir reguliuojamą EMI. <link>Sužinok daugiau</link>", "account-details.unblock": "Pa<PERSON>imus teikia Unblock paslaugų teikėjas. <link><PERSON><PERSON>inok daugiau</link>", "account-selector.empty-state": "Piniginių nerasta", "account-top-up.select-currency.title": "Žetonai", "account.accounts_not_found": "Nepavyko rasti jokių piniginių", "account.accounts_not_found_search_valid_address": "Piniginės nėra tavo sąraše", "account.add.create_new_secret_phrase": "Sukurti slaptąją frazę", "account.add.create_new_secret_phrase.subtext": "Nauja 12 žodžių slaptoji frazė", "account.add.fromRecoveryKit.fileNotFound": "Nepavyko rasti tavo failo", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "Bandyti dar kartą", "account.add.fromRecoveryKit.fileNotFound.explanation": "<PERSON><PERSON><PERSON><PERSON>, ar prisijungta prie teisingos paskyros su Zeal Backup aplanku.", "account.add.fromRecoveryKit.fileNotValid": "Atkūrimo failas yra net<PERSON>s", "account.add.fromRecoveryKit.fileNotValid.explanation": "Patikrinome failą: jis netinkamo tipo arba buvo pakeistas.", "account.add.import_secret_phrase": "Importuo<PERSON> fraz<PERSON>", "account.add.import_secret_phrase.subtext": "<PERSON><PERSON><PERSON>, Metamask ar kitur", "account.add.select_type.add_hardware_wallet": "Aparatinė pinigi<PERSON>ė", "account.add.select_type.existing_smart_wallet": "Esama Smart Wallet", "account.add.select_type.private_key": "<PERSON><PERSON><PERSON><PERSON> raktas", "account.add.select_type.seed_phrase": "Pradinė frazė", "account.add.select_type.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "account.add.select_type.zeal_recovery_file": "Zeal atk<PERSON><PERSON>o failas", "account.add.success.title": "<PERSON><PERSON><PERSON> nauja piniginė 🎉", "account.addLabel.header": "Pavadink savo piniginę", "account.addLabel.labelError.labelAlreadyExist": "Toks pavadinimas jau yra. Išbandyk kitą", "account.addLabel.labelError.maxStringLengthExceeded": "Pasiektas maksimalus simbolių skaičius", "account.add_active_wallet.primary_text": "<PERSON><PERSON><PERSON><PERSON>", "account.add_active_wallet.short_text": "<PERSON><PERSON><PERSON>, prijunk arba importuok piniginę", "account.add_from_ledger.success": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> į Zeal", "account.add_tracked_wallet.primary_text": "<PERSON><PERSON><PERSON><PERSON>", "account.add_tracked_wallet.short_text": "<PERSON><PERSON> portfelį ir aktyvu<PERSON>ą", "account.button.unlabelled-wallet": "<PERSON><PERSON><PERSON><PERSON> be pavadinimo", "account.create_wallet": "<PERSON><PERSON><PERSON><PERSON> pinigi<PERSON>", "account.label.edit.title": "Redaguoti piniginė<PERSON> p<PERSON>", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON> {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Atkūrimo failas yra net<PERSON>s", "account.recoveryKit.selectBackupFile.subtitle": "Pasirink norimą atkurti atkūrimo failą.", "account.recoveryKit.selectBackupFile.title": "Atk<PERSON><PERSON><PERSON> failas", "account.recoveryKit.success.recoveryFileFound": "Atkūrimo failas rastas 🎉", "account.select_type_of_account.create_eoa.short": "Tradicinė pinigi<PERSON>ė ekspertams", "account.select_type_of_account.create_eoa.title": "Sukurti pradinės fraz<PERSON>", "account.select_type_of_account.create_safe_wallet.title": "Sukurti Smart Wallet", "account.select_type_of_account.existing_smart_wallet": "Esama Smart Wallet", "account.select_type_of_account.hardware_wallet": "Aparatinė pinigi<PERSON>ė", "account.select_type_of_account.header": "<PERSON><PERSON><PERSON><PERSON>", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Privatus rak<PERSON> / slapta frazė", "account.select_type_of_account.read_only_wallet": "Tik peržiūrai skirta piniginė", "account.select_type_of_account.read_only_wallet.short": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bet kurį portfolio", "account.topup.title": "Pa<PERSON><PERSON><PERSON>i Zeal sąskaitą", "account.view.error.refreshAssets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "account.widget.refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "account.widget.settings": "Nustatymai", "accounts.view.copied-text": "Nukopijuota {formattedAddress}", "accounts.view.copiedAddress": "Nukopijuota {formattedAddress}", "action.accept": "<PERSON><PERSON><PERSON><PERSON>", "action.accpet": "<PERSON><PERSON><PERSON><PERSON>", "action.allow": "<PERSON><PERSON><PERSON>", "action.back": "Atgal", "action.cancel": "<PERSON><PERSON><PERSON><PERSON>", "action.card-activation.title": "Aktyvuoti kortelę", "action.claim": "<PERSON><PERSON><PERSON><PERSON>", "action.close": "Uždaryti", "action.complete-steps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.confirm": "<PERSON><PERSON><PERSON><PERSON>", "action.continue": "<PERSON><PERSON><PERSON><PERSON>", "action.copy-address-understand": "Gerai – kopijuoti adresą", "action.deposit": "Įnešti", "action.done": "Atlikta", "action.dontAllow": "<PERSON><PERSON><PERSON><PERSON>", "action.edit": "<PERSON><PERSON><PERSON><PERSON>", "action.email-required": "Įvesti el. paštą", "action.enterPhoneNumber": "Įvesti telefono numerį", "action.expand": "Išskleisti", "action.fix": "<PERSON><PERSON><PERSON><PERSON>", "action.getStarted": "<PERSON><PERSON><PERSON><PERSON>", "action.got_it": "<PERSON><PERSON><PERSON><PERSON>", "action.hide": "Slėpti", "action.import": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.import-keys": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "action.importKeys": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "action.minimize": "Sumažinti", "action.next": "Toliau", "action.ok": "G<PERSON><PERSON>", "action.reduceAmount": "Sumažinti iki maks.", "action.refreshWebsite": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "action.remove": "<PERSON><PERSON><PERSON><PERSON>", "action.remove-account": "Pašalinti p<PERSON>yrą", "action.requestCode": "<PERSON><PERSON><PERSON> k<PERSON>", "action.resend_code": "Siųsti kodą dar kartą", "action.resend_code_with_time": "Siųsti kodą dar kartą {time}", "action.retry": "Bandyti dar kartą", "action.reveal": "<PERSON><PERSON><PERSON>", "action.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "action.save_changes": "Saugoti RPC", "action.search": "Pa<PERSON>š<PERSON>", "action.seeAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> visus", "action.select": "<PERSON><PERSON><PERSON><PERSON>", "action.send": "Si<PERSON>sti", "action.skip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.submit": "Pat<PERSON><PERSON><PERSON>", "action.understood": "<PERSON><PERSON><PERSON><PERSON>", "action.update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.update-gnosis-pay-owner.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.zeroAmount": "Įvesti sumą", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action_bar_title.tokens": "Žetonai", "action_bar_title.transaction_request": "Transakcijos užklausa", "activate-monerium.loading": "<PERSON><PERSON><PERSON> tavo as<PERSON>ė paskyra", "activate-monerium.success.title": "Monerium įjungta", "activate-physical-card-widget.subtitle": "Pristatymas gali užtrukti 3 savaites", "activate-physical-card-widget.title": "Aktyvinti fizinę kortelę", "activate-smart-wallet.title": "Aktyvuoti piniginę", "active_and_tracked_wallets.title": "Zeal padengia visus tavo moke<PERSON> {network}, tod<PERSON><PERSON> gali atlikti transakcijas nemokamai!", "activity.approval-amount.revoked": "<PERSON><PERSON><PERSON><PERSON>", "activity.approval-amount.unlimited": "Neribot<PERSON>", "activity.approval.approved_for": "<PERSON><PERSON><PERSON><PERSON>", "activity.approval.approved_for_with_target": "Patvir<PERSON>ta {approvedTo}", "activity.approval.revoked_for": "<PERSON><PERSON><PERSON><PERSON>", "activity.bank.serviceProvider": "Paslaugų teikėjas", "activity.bridge.serviceProvider": "Paslaugų teikėjas", "activity.cashback.period": "Cashback la<PERSON><PERSON><PERSON>", "activity.filter.card": "Kortelė", "activity.rate": "<PERSON><PERSON><PERSON>", "activity.receive.receivedFrom": "<PERSON><PERSON><PERSON>", "activity.send.sendTo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.smartContract.unknown": "<PERSON><PERSON>ž<PERSON><PERSON> sutar<PERSON>", "activity.smartContract.usingContract": "<PERSON><PERSON><PERSON><PERSON>", "activity.subtitle.pending_timer": "{timerString} Vykdoma", "activity.title.arbitrary_smart_contract_interaction": "{function} {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Nežinomos sutarties operacija", "activity.title.bridge.from": "Bridge iš {token}", "activity.title.bridge.to": "Bridge į {token}", "activity.title.buy": "Pirkta {asset}", "activity.title.card_owners_updated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.title.card_spend_limit_updated": "Nustatytas <PERSON>", "activity.title.cashback_deposit": "Įnašas į Cashback", "activity.title.cashback_reward": "<PERSON><PERSON><PERSON> cashback", "activity.title.cashback_withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.title.claimed_reward": "Atsiimtas apdovanojimas", "activity.title.deployed_smart_wallet_gnosis": "Pa<PERSON>ra sukurta", "activity.title.deposit_from_bank": "Įnašas iš banko", "activity.title.deposit_into_card": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.title.deposit_into_earn": "Įnašas į {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} {smartContract}", "activity.title.from": "<PERSON><PERSON> {sender}", "activity.title.pendidng_areward_claim": "Atsiimamas <PERSON>", "activity.title.pendidng_breward_claim": "Atsiimamas <PERSON>", "activity.title.recharge_disabledh": "Ko<PERSON><PERSON><PERSON><PERSON>", "activity.title.recharge_set": "Nustatytas papildymo tik<PERSON>s", "activity.title.recovered_smart_wallet_gnosis": "<PERSON><PERSON>jo įrenginio pridėjimas", "activity.title.send_pending": "<PERSON><PERSON> {receiver}", "activity.title.send_to_bank": "Į banką", "activity.title.swap": "Pirkta {token}", "activity.title.to": "<PERSON><PERSON> {receiver}", "activity.title.withdraw_from_card": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.title.withdraw_from_earn": "<PERSON><PERSON><PERSON><PERSON><PERSON> iš {earn}", "activity.transaction.networkFees": "<PERSON><PERSON><PERSON>", "activity.transaction.state": "Įvykdyta operacija", "activity.transaction.state.completed": "Užbaigta operacija", "activity.transaction.state.failed": "Neįvykdyta operacija", "add-account.section.import.header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add-another-card-owner": "Pridėti <PERSON> kortel<PERSON> sa<PERSON>", "add-another-card-owner.Recommended.footnote": "Pridėk savo Zeal piniginę kaip papildomą Gnosis Pay kortelės savininką", "add-another-card-owner.Recommended.primaryText": "<PERSON><PERSON><PERSON><PERSON> į Gnosis Pay", "add-another-card-owner.recommended": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add-owner.confirmation.subtitle": "Saugumo sumetimais nustatymų pakeitimai apdorojami 3 minutes. Tuo metu tavo kortelė bus laikinai užšaldyta ir mokėjimai negalimi.", "add-owner.confirmation.title": "Kortelė bus užšaldyta 3 min., kol atnaujinami nustatymai", "add-readonly-signer-if-not-exist.error.already_in_use.title": "<PERSON><PERSON><PERSON><PERSON>, ji jau naudo<PERSON><PERSON>", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "Bandyti kitą piniginę", "add.account.backup.decrypt.success": "Piniginė atkurta", "add.account.backup.password.passwordIncorrectMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add.account.backup.password.subtitle": "Įvesk slaptažodį, kurį naudojai savo atkūrimo failui užš<PERSON>i", "add.account.backup.password.title": "Įvesti slaptažodį", "add.account.google.login.subtitle": "Patvirtink užklausą „Google Drive“, kad sinchronizuotum savo atkūrimo failą", "add.account.google.login.title": "<PERSON><PERSON><PERSON>...", "add.readonly.already_added": "<PERSON><PERSON><PERSON><PERSON> j<PERSON> p<PERSON>", "add.readonly.continue": "<PERSON><PERSON><PERSON><PERSON>", "add.readonly.empty": "Įvesk adresą arba ENS", "addBankRecipient.title": "Pridėti banko gavėją", "add_funds.deposit_from_bank_account": "Įnešti iš banko sąskaitos", "add_funds.from_another_wallet": "<PERSON><PERSON>", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Prisijungti prie papildymo dApp", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Prijunk bet kurią piniginę prie Zeal papildymo dApp ir greitai siųsk lėšas", "add_funds.from_crypto_wallet.header": "<PERSON><PERSON>", "add_funds.from_crypto_wallet.header.show_wallet_address": "Rodyti savo piniginės ad<PERSON>", "add_funds.from_exchange.header": "Siųsti i<PERSON> k<PERSON>", "add_funds.from_exchange.header.copy_wallet_address": "Kopijuoti savo Zeal adresą", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance ir kt.", "add_funds.from_exchange.header.open_exchange": "Atidaryti keityklos programėlę ar svetainę", "add_funds.from_exchange.header.selected_token": "<PERSON><PERSON><PERSON><PERSON> {token} į Zeal", "add_funds.from_exchange.header.selected_token.subtitle": "Tinkle {network}", "add_funds.from_exchange.header.send_selected_token": "Siųsti palaikomą žetoną", "add_funds.from_exchange.header.send_selected_token.subtitle": "Pasirinkti palaikomą žetoną ir tinklą", "add_funds.import_wallet": "Importuoti esamą kriptovaliutų piniginę", "add_funds.title": "Papildyti sąskaitą", "add_funds.transfer_from_exchange": "Pavedimas i<PERSON>", "address.add.header": "<PERSON><PERSON> savo pinigin<PERSON>{br}tik skaitymo re<PERSON>u", "address.add.subheader": "Įvesk adresą ar ENS ir matyk savo turtą visuose EVM tinkluose. Vėliau galėsi pridėti daugiau piniginių.", "address_book.change_account.bank_transfers.header": "Banko gavėjai", "address_book.change_account.bank_transfers.primary": "Banko gav<PERSON>", "address_book.change_account.cta": "Steb<PERSON><PERSON>", "address_book.change_account.search_placeholder": "Pridėti arba ieškoti adreso", "address_book.change_account.tracked_header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "address_book.change_account.wallets_header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app-association-check-failed.modal.cta": "Bandyti dar kartą", "app-association-check-failed.modal.subtitle": "Bandyk dar kartą. Dėl ryšio problemų vėluoja slaptažodžių gavimas. <PERSON><PERSON> problema išlieka, paleisk Zeal iš naujo ir bandyk dar kartą.", "app-association-check-failed.modal.subtitle.creation": "Bandyk dar kartą. Dėl ryšio problemų vėluoja slaptažodžio kūrimas. <PERSON><PERSON> problema i<PERSON>, paleisk Zeal iš naujo ir bandyk dar kartą.", "app-association-check-failed.modal.title.creation": "Įrenginiui nepavyko sukurti slap<PERSON>žodžio", "app-association-check-failed.modal.title.signing": "Įrenginiui nepavyko įkelti slaptažodžių", "app.app_protocol_group.borrowed_tokens": "Pasiskolinti žetonai", "app.app_protocol_group.claimable_amount": "<PERSON><PERSON><PERSON><PERSON> suma", "app.app_protocol_group.health_rate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.lending": "<PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.locked_tokens": "Užrakinti žetonai", "app.app_protocol_group.nfts": "Kolekcionuojami da<PERSON>", "app.app_protocol_group.reward_tokens": "Apdovanojimų žetonai", "app.app_protocol_group.supplied_tokens": "Suteikti <PERSON>", "app.app_protocol_group.tokens": "Žetonas", "app.app_protocol_group.vesting_token": "<PERSON>ždirba<PERSON>", "app.appsGroupHeader.discoverMore": "Atrask daugiau", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Ieškok arba įvesk URL", "app.error-banner.cory": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> duomen<PERSON>", "app.error-banner.retry": "Bandyti dar kartą", "app.list_item.rewards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {value}", "app.position_details.health_rate.description": "Būk<PERSON><PERSON> a<PERSON>kaičiuojama paskolos sumą padalijus iš tavo įkeisto turto vertės.", "app.position_details.health_rate.title": "<PERSON><PERSON> yra b<PERSON><PERSON><PERSON><PERSON><PERSON>?", "approval.edit-limit.label": "Keisti išlaidų limitą", "approval.permit_info": "<PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.cancel": "<PERSON><PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.limit-label": "Išlaidų limitas", "approval.spend-limit.edit-modal.max-limit-error": "Įspėjimas, didelis limitas", "approval.spend-limit.edit-modal.revert": "Grąžinti pakeitimus", "approval.spend-limit.edit-modal.set-to-unlimited": "Nustatyti <PERSON>", "approval.spend-limit.edit-modal.submit": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "approval.spend-limit.edit-modal.title": "<PERSON><PERSON><PERSON>", "approval.spend_limit_info": "Kas yra i<PERSON> limit<PERSON>?", "approval.what_are_approvals": "Kas yra pat<PERSON>?", "apps_list.page.emptyState": "Nėra aktyvių programėlių", "backpace.removeLastDigit": "Pa<PERSON>linti paskutinį skaitmenį", "backup-banner.backup_now": "Sukurti kopiją", "backup-banner.risk_losing_funds": "<PERSON><PERSON><PERSON>, antraip rizikuoji prarasti lėša<PERSON>", "backup-banner.title": "Piniginės atsarginė kopija nesukurta", "backupRecoverySmartWallet.noExportPrivateKeys": "Automatinė atsarginė kopija: tavo Smart Wallet išsaugoma kaip passkey – nereikia slaptosios frazės ar privataus rakto.", "backupRecoverySmartWallet.safeContracts": "Kelių raktų saugumas: <PERSON><PERSON> pin<PERSON>s veikia su <PERSON>, todėl operaciją gali patvirtinti keli įrenginiai. Nėra vieno gedimo <PERSON>.", "backupRecoverySmartWallet.security": "Keli įrenginiai: su Passkey gali naudoti piniginę keliuose įrenginiuose. Kiekvienas įrenginys gauna savo privatų raktą.", "backupRecoverySmartWallet.showLocalPrivateKey": "Eksperto režimas: gali eksportuoti šio įrenginio privatų raktą, naudoti jį kitoje piniginėje ir prisijungti per <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Rodyti privatų raktą</Key>", "backupRecoverySmartWallet.storingKeys": "Sinchronizuojama debesyje: passkey saugiai laikomas iCloud, Google Password Manager arba tavo slaptažodžių tvarkyklėje.", "backupRecoverySmartWallet.title": "Smart Wallet atsarginė kopija ir atkūrimas", "balance-change.card.titile": "Kortelė", "balanceChange.pending": "<PERSON><PERSON><PERSON>", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Pasirink paslaugų teikėją", "bank-transfer.change-deposit-receiver.subtitle": "Ši piniginė gaus visus banko indėlius", "bank-transfer.change-deposit-receiver.title": "Nustatyk gaunančią piniginę", "bank-transfer.change-owner.subtitle": "<PERSON><PERSON> – prisijungimui ir atk<PERSON>ui", "bank-transfer.change-owner.title": "Nustatyk paskyros sa<PERSON>", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Visi banko indėliai į Zeal bus gauti čia.", "bank-transfer.configrm-change-deposit-receiver.title": "Pakeisti gaunančią piniginę", "bank-transfer.configrm-change-owner.subtitle": "Ar tikrai nori pakeisti paskyros savininką? Ši piniginė skirta prisijungti ir atkurti banko pavedimų paskyrą.", "bank-transfer.configrm-change-owner.title": "Pakeisti p<PERSON>yr<PERSON>", "bank-transfer.deposit.widget.status.complete": "Atlikta", "bank-transfer.deposit.widget.status.funds_received": "Lėš<PERSON> g<PERSON>", "bank-transfer.deposit.widget.status.sending_to_wallet": "Siunčiama į piniginę", "bank-transfer.deposit.widget.status.transfer-on-hold": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.deposit.widget.status.transfer-received": "Siunčiama į piniginę", "bank-transfer.deposit.widget.subtitle": "{from} į {to}", "bank-transfer.deposit.widget.title": "Įnešimas", "bank-transfer.intro.bulletlist.point_1": "Nustatoma su Unblock", "bank-transfer.intro.bulletlist.point_2": "Pervedimai tarp EUR/GBP ir >10 žetonų", "bank-transfer.intro.bulletlist.point_3": "0 % mok. iki 5 tūkst. $ / mėn., po to 0,2 %", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Siunčiama į banką", "bank-transfer.withdrawal.widget.status.in-progress": "Vyk<PERSON><PERSON>", "bank-transfer.withdrawal.widget.status.on-hold": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.withdrawal.widget.status.success": "Atlikta", "bank-transfer.withdrawal.widget.subtitle": "{from} į {to}", "bank-transfer.withdrawal.widget.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfers.bank-account-actions.remove-this-account": "Pašalinti šią p<PERSON>ą", "bank-transfers.bank-account-actions.switch-to-this-account": "Perjungti į šią paskyrą", "bank-transfers.deposit.fees-for-less-than-5k": "Mokesčiai sumai iki 5 tūkst. $", "bank-transfers.deposit.fees-for-more-than-5k": "Mokesčiai sumai virš 5 tūkst. $", "bank-transfers.set-receiving-bank.title": "Nustatyk gaunantį banką", "bank-transfers.settings.account_owner": "<PERSON><PERSON><PERSON>", "bank-transfers.settings.receiver_of_bank_deposits": "Banko įnešimų gavėjas", "bank-transfers.settings.receiver_of_withdrawals": "Išėmimų gavėjas", "bank-transfers.settings.registered_email": "Registruotas el. paštas", "bank-transfers.settings.title": "Banko pavedimų nustatymai", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} sąskaita", "bank-transfers.setup.bank-account": "Banko sąskaita", "bankTransfer.withdraw.max_loading": "Maks.: {amount}", "bank_details_do_not_match.got_it": "<PERSON><PERSON><PERSON><PERSON>", "bank_details_do_not_match.subtitle": "Kodas ir sąskaita nesutampa. Bandyk vėl.", "bank_details_do_not_match.title": "Banko duomenys nesutampa", "bank_tranfsers.select_country_of_residence.country_not_supported": "<PERSON><PERSON><PERSON><PERSON><PERSON>, banko pavedimai ne<PERSON>aikomi {country} dar", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Atidaryk savo banko programėlę", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Siųsk {fiatCurrencyCode} į savo sąskaitą", "bank_transfer.deposit.header": "{fullName} as<PERSON><PERSON><PERSON>s s<PERSON>skaitos&nbsp;duomenys", "bank_transfer.kyc_status_widget.subtitle": "Bank<PERSON>", "bank_transfer.kyc_status_widget.title": "Tvirtinama tap<PERSON>ė", "bank_transfer.personal_details.date_of_birth": "Gimimo data", "bank_transfer.personal_details.date_of_birth.invalid_format": "Neteisinga data", "bank_transfer.personal_details.date_of_birth.too_young": "<PERSON><PERSON> bent 18 metų amžiaus", "bank_transfer.personal_details.first_name": "Vardas", "bank_transfer.personal_details.last_name": "Pa<PERSON><PERSON>", "bank_transfer.personal_details.title": "Tavo duomenys", "bank_transfer.reference.label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (nebūtina)", "bank_transfer.reference_message": "<PERSON>š<PERSON><PERSON><PERSON> i<PERSON>", "bank_transfer.residence_details.address": "Tavo adresas", "bank_transfer.residence_details.city": "Miestas", "bank_transfer.residence_details.country_of_residence": "Gyvenamoji <PERSON>", "bank_transfer.residence_details.country_placeholder": "<PERSON><PERSON>", "bank_transfer.residence_details.postcode": "<PERSON><PERSON><PERSON> k<PERSON>", "bank_transfer.residence_details.street": "Gatvė", "bank_transfer.residence_details.your_residence": "Tavo gyvenamoji vieta", "bank_transfers.choose-wallet.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.test": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.warning.subtitle": "Susiek tik vieną piniginę – jos ne<PERSON>.", "bank_transfers.choose-wallet.warning.title": "<PERSON><PERSON><PERSON>", "bank_transfers.choose_wallet.subtitle": "Pasirink piniginę banko pavedimams. ", "bank_transfers.choose_wallet.title": "<PERSON><PERSON><PERSON>", "bank_transfers.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.currency_is_currently_not_supported": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit-header": "Įnešimas", "bank_transfers.deposit.account-name": "Sąskaitos pavadinimas", "bank_transfers.deposit.account-number-copied": "Sąskaitos numeris nukopijuotas", "bank_transfers.deposit.amount-input": "Pildoma suma", "bank_transfers.deposit.amount-output": "<PERSON><PERSON><PERSON> suma", "bank_transfers.deposit.amount-output.error": "k<PERSON>a", "bank_transfers.deposit.buttet-point.receive-crypto": "Gauk {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.currency-not-supported.subtitle": "Banko pervedimai su {code} laikinai išjungti.", "bank_transfers.deposit.currency-not-supported.title": "{code} perved<PERSON>i šiuo metu nepal<PERSON>", "bank_transfers.deposit.default-token.balance": "Likutis: {amount}", "bank_transfers.deposit.deposit-header": "Įnešimas", "bank_transfers.deposit.enter_amount": "Įvesk sumą", "bank_transfers.deposit.iban-copied": "IBAN nukopijuotas", "bank_transfers.deposit.increase-amount": "Minimalus pavedimas: {limit}", "bank_transfers.deposit.loading": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.max-limit-reached": "Suma viršija maksimalų pavedimo limitą", "bank_transfers.deposit.modal.kyc.button-text": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.modal.kyc.text": "Norėdami patvirtinti tavo tapatybę, paprašysime kai kurių asmeninių duomenų ir dokumentų. Pateikimas paprastai trunka vos kelias minutes.", "bank_transfers.deposit.modal.kyc.title": "<PERSON><PERSON><PERSON><PERSON>, kad padidintum limitus", "bank_transfers.deposit.reduce_amount": "Sumažink sumą", "bank_transfers.deposit.show-account.account-number": "Sąskaitos numeris", "bank_transfers.deposit.show-account.bic": "BIC / SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "bank_transfers.deposit.sort-code-copied": "Rūš<PERSON>vi<PERSON> koda<PERSON> nuk<PERSON>", "bank_transfers.deposit.withdraw-header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.failed_to_load_fee": "Nežinoma", "bank_transfers.fees": "Mokesčiai", "bank_transfers.increase-amount": "Minimalus pavedimas: {limit}", "bank_transfers.insufficient-funds": "Nepakanka lėšų", "bank_transfers.select_country_of_residence.title": "Kur tu gyveni?", "bank_transfers.setup.cta": "Aktyvu<PERSON><PERSON>", "bank_transfers.setup.enter-amount": "Įvesk sumą", "bank_transfers.source_of_funds.form.business_income": "<PERSON><PERSON><PERSON> ve<PERSON>", "bank_transfers.source_of_funds.form.other": "<PERSON><PERSON>", "bank_transfers.source_of_funds.form.pension": "Pensija", "bank_transfers.source_of_funds.form.salary": "Atlyginimas", "bank_transfers.source_of_funds.form.title": "Tavo lėšų šaltinis", "bank_transfers.source_of_funds_description.placeholder": "Apibūdink lėšų šaltinį...", "bank_transfers.source_of_funds_description.title": "Papasakok daugiau apie savo lėšų šaltinį", "bank_transfers.withdraw-header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.withdraw.amount-input": "<PERSON><PERSON><PERSON><PERSON> suma", "bank_transfers.withdraw.max-limit-reached": "Suma viršija maksimalų pavedimo limitą", "bank_transfers.withdrawal.verify-id": "Sumažink sumą", "banner.above_maximum_limit.maximum_input_limit_exceeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON> įvesties limitas", "banner.above_maximum_limit.maximum_limit_per_deposit": "Tai maksimalus limitas vienam indėliui", "banner.above_maximum_limit.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON> įvesties limitas", "banner.above_maximum_limit.title": "Sumažink sumą iki {amount} arba mažiau", "banner.above_maximum_limit.title.default": "Sumažink sumą", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Minimalus įvesties limitas nepasiektas", "banner.below_minimum_limit.minimum_limit_for_token": "<PERSON> yra minimalus šio tokeno limitas", "banner.below_minimum_limit.title": "Padidink sumą iki {amount} arba daugiau", "banner.below_minimum_limit.title.default": "Padidink sumą", "breaard.in_porgress.info_popup.cta": "Iš<PERSON><PERSON> ir gauk {earn}", "breaard.in_porgress.info_popup.footnote": "Naudodamas Zeal ir Gnosis Pay kortelę sutinki su šios apdovanojimų kampanijos ta<PERSON>yklėmis ir sąlygomis.", "breaward.in_porgress.info_popup.bullet_point_1": "Išleisk {remaining} per kitas {time} , kad atsiimtum šį apdovano<PERSON>.", "breaward.in_porgress.info_popup.bullet_point_2": "Į išlaidų sumą įskaičiuojami tik galiojantys Gnosis Pay pirkiniai.", "breaward.in_porgress.info_popup.bullet_point_3": "Atsiė<PERSON>, jis bus išsiųstas į tavo Zeal paskyrą.", "breaward.in_porgress.info_popup.header": "Gauk {earn} i<PERSON><PERSON><PERSON><PERSON><PERSON> {remaining}", "breward.celebration.for_spending": "<PERSON><PERSON> kortele", "breward.dc25-eligible-celebration.for_spending": "Tu esi tarp pirmųjų {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "Tu nebuvai tarp pirmų<PERSON>ų {limit} , kurie <PERSON>", "breward.expired_banner.earn_by_spending": "Gauk {earn} i<PERSON><PERSON><PERSON><PERSON><PERSON> {amount}", "breward.expired_banner.reward_expired": "{earn} a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "breward.in_progress_banner.cta.title": "Iš<PERSON><PERSON> ir gauk {earn}", "breward.ready_to_claim.error.try_again": "Bandyti dar kartą", "breward.ready_to_claim.error_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "breward.ready_to_claim.in_progress": "Atsiimamas <PERSON>", "breward.ready_to_claim.youve_earned": "Tu gavai {earn}!", "breward_already_claimed.title": "Apdovanojimas jau atsiimtas. Jei negavai apdovanojimo <PERSON>, susisiek su pagalba", "breward_cannotbe_claimed.title": "Apdovan<PERSON><PERSON><PERSON> dabar at<PERSON>imti negalima. Bandyk vėliau", "bridge.best_return": "Didžiausios grąžos ma<PERSON>", "bridge.best_serivce_time": "Greič<PERSON><PERSON><PERSON>", "bridge.check_status.complete": "Užbaigta", "bridge.check_status.progress_text": "Vykdomas Bridge {from} į {to}", "bridge.remove_topup": "Pašalinti papildymą", "bridge.request_status.completed": "Atlikta", "bridge.request_status.pending": "Vykdoma", "bridge.widget.completed": "Užbaigta", "bridge.widget.currencies": "{from} į {to}", "bridge_rote.widget.title": "Bridge", "browse.discover_more_apps": "Atrask daugiau programėlių", "browse.google_search_term": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{searchTerm}“", "brward.celebration.you_earned": "Tu gavai", "brward.expired_banner.subtitle": "Sėkmės kitą kartą", "brward.in_progress_banner.subtitle": "<PERSON><PERSON><PERSON> dar {expiredInFormatted}", "buy": "<PERSON><PERSON><PERSON>", "buy.enter_amount": "Įvesk sumą", "buy.loading": "<PERSON><PERSON><PERSON><PERSON>...", "buy.no_routes_found": "Maršrutų nerasta", "buy.not_enough_balance": "<PERSON>epaka<PERSON><PERSON> likutis", "buy.select-currency.title": "<PERSON><PERSON><PERSON><PERSON> vali<PERSON>", "buy.select-to-currency.title": "<PERSON><PERSON><PERSON><PERSON>", "buy_form.title": "<PERSON>rk<PERSON>", "cancelled-card.create-card-button.primary": "Gauti naują <PERSON> kortel<PERSON>", "cancelled-card.switch-card-button.primary": "<PERSON><PERSON><PERSON> k<PERSON>", "cancelled-card.switch-card-button.short-text": "<PERSON><PERSON> aktyvią kortelę", "card": "Kortelė", "card-add-cash.confirm-stage.banner.no-routes-found": "<PERSON><PERSON><PERSON><PERSON><PERSON> nėra, bandyk kitą valiutą arba sumą", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Tau reikia {amount} daugiau {symbol} m<PERSON><PERSON><PERSON><PERSON><PERSON>", "card-add-cash.confirm-stage.banner.value-loss": "<PERSON><PERSON><PERSON> {loss} vert<PERSON>s", "card-add-cash.confirm-stage.banner.value-loss.revert": "<PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.cancel": "<PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.continue": "<PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.enter-amount": "Įvesti sumą", "card-add-cash.edit-stage.cta.reduce-to-max": "<PERSON>ki maks.", "card-add-cash.edit-staget.banner.no-routes-found": "<PERSON><PERSON><PERSON><PERSON><PERSON> nėra, bandyk kitą valiutą arba sumą", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Išsiuntėme užklausą. Tęsk savo piniginėje.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Patvirtink aparatinėje piniginėje", "card-balance": "Likutis: {balance}", "card-cashback.status.title": "Įmokėti į cashback", "card-copy-safe-address.copy_address": "Kopijuot<PERSON> ad<PERSON>", "card-copy-safe-address.copy_address.done": "Nukopijuota", "card-copy-safe-address.warning.description": "Šiuo adresu galima gauti tik {cardAsset} Gnosis Chain tinkle. Nesiųsk lėšų iš kitų tinklų šiuo adresu. Jos bus prarastos.", "card-copy-safe-address.warning.header": "Siųsk tik {cardAsset} Gnosis Chain tinkle", "card-marketing-card.center.subtitle": "FX mokesčiai", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card-marketing-card.right.subtitle": "Registracijos <PERSON>", "card-marketing-card.title": "Europos VISA kortelė su didelėmis palūkanomis", "card-marketing-tile.get-started": "<PERSON><PERSON><PERSON><PERSON>", "card-select-from-token-title": "Pasirink<PERSON>", "card-top-up.banner.subtitle.completed": "Užbaigta", "card-top-up.banner.subtitle.failed": "Nepavyko", "card-top-up.banner.subtitle.pending": "{timerString} Vykdoma", "card-top-up.banner.title": "Įnešama {amount}", "card-topup.select-token.emptyState": "Tokenų nerasta", "card.activate.card_number_not_valid": "Neteisingas kortelės numeris. Bandyk vėl.", "card.activate.invalid_card_number": "Neteisingas k<PERSON> numeris.", "card.activation.activate_physical_card": "Aktyvinti fizinę kortelę", "card.add-cash.amount-to-withdraw": "Pa<PERSON><PERSON><PERSON>o suma", "card.add-from-earn-form.title": "Papild<PERSON><PERSON> kortelę", "card.add-from-earn-form.withdraw-to-card": "<PERSON><PERSON><PERSON><PERSON>", "card.add-from-earn.amount-to-withdraw": "<PERSON>šimama suma į kortelę", "card.add-from-earn.enter-amount": "Įvesk sumą", "card.add-from-earn.loading": "<PERSON><PERSON><PERSON><PERSON>", "card.add-from-earn.max-label": "Likutis: {amount}", "card.add-from-earn.no-routes-found": "Maršrutų nerasta", "card.add-from-earn.not-enough-balance": "Nepakanka lėšų", "card.add-owner.queued": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> eilė<PERSON>", "card.add-to-wallet-flow.subtitle": "Mokėk iš savo pinigin<PERSON>.", "card.add-to-wallet.copy-card-number": "Nukopijuok kortelės numerį žemiau", "card.add-to-wallet.title": "<PERSON><PERSON><PERSON><PERSON> į {platformName} Wallet", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "ATŠAUKTA", "card.card-owner-not-found.disconnect-btn": "Atjun<PERSON><PERSON> kortelę nuo Zeal", "card.card-owner-not-found.subtitle": "Kad naudotum kort<PERSON>, atnaujink savininką.", "card.card-owner-not-found.title": "Prijunk kortelę iš naujo", "card.card-owner-not-found.update-owner-btn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} po {date}", "card.cashback.widgetNoCashback.subtitle": "Įnešk l<PERSON><PERSON><PERSON> ir pradėk gauti", "card.cashback.widgetNoCashback.title": "Gauk iki {defaultPercentage} cashback", "card.cashback.widgetcashbackValue.rewards": "{amount} laukiama", "card.cashback.widgetcashbackValue.title": "{percentage} cashback", "card.choose-wallet.connect_card": "Prijun<PERSON><PERSON> kort<PERSON>", "card.choose-wallet.create-new": "Pridėti naują piniginę kaip sa<PERSON>", "card.choose-wallet.import-another-wallet": "Importuoti kit<PERSON> piniginę", "card.choose-wallet.import-current-owner": "Importuoti es<PERSON> k<PERSON> sa<PERSON>", "card.choose-wallet.import-current-owner.sub-text": "Importuok Gnosis Pay kortelės savininko privačius raktus arba pradinę frazę", "card.choose-wallet.title": "Pasirink piniginę kortelei valdyti", "card.connectWalletToCardGuide": "Kopijuoti piniginės <PERSON>", "card.connectWalletToCardGuide.addGnosisPayOwner": "Pridėti Gnosis Pay savininką", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Atsidaryk Gnosispay.com su kita savo pinigine{br}2. <PERSON><PERSON> „Paskyra“{br}3. <PERSON><PERSON> „Paskyros informacija“{br}4. <PERSON><PERSON> „<PERSON><PERSON><PERSON> sa<PERSON>“ spausk „<PERSON><PERSON>i“ ir{br}5. <PERSON><PERSON> „Pridėti adresą“{br}6. Įklijuok savo Zeal adresą ir spausk Iš<PERSON>ugoti", "card.connectWalletToCardGuide.header": "Prijunk {account} prie Gnosis Pay kortelės", "card.connect_card.start": "Prijungti Gnosis Pay", "card.copiedAddress": "Nukopijuota {formattedAddress}", "card.disconnect-account.title": "Atjungti paskyrą", "card.hw-wallet-support-drop.add-owner-btn": "Pridėti nauj<PERSON> sa<PERSON>", "card.hw-wallet-support-drop.disconnect-btn": "Atjun<PERSON><PERSON> kortelę nuo Zeal", "card.hw-wallet-support-drop.subtitle": "Pridėk nauj<PERSON> sa<PERSON> (ne aparatinę).", "card.hw-wallet-support-drop.title": "Zeal nebepalaiko aparatinių piniginių kortelei", "card.kyc.continue": "Tęsti <PERSON>", "card.list_item.title": "Kortelė", "card.onboarded.transactions.empty.description": "Tavo mokėjimų veikla bus rodoma čia", "card.onboarded.transactions.empty.title": "Veikla", "card.order.continue": "Tęsti kortelės užsakymą", "card.order.free_virtual_card": "<PERSON><PERSON><PERSON> k<PERSON>", "card.order.start": "<PERSON>ž<PERSON><PERSON><PERSON>", "card.owner-not-imported.cancel": "<PERSON><PERSON><PERSON><PERSON>", "card.owner-not-imported.import": "Importuok", "card.owner-not-imported.subtitle": "Norėdamas patvirtin<PERSON> operaciją, susiek savo Gnosis Pay paskyros savininko piniginę su Zeal. Pastaba: tai atskiras veiksmas nuo įprasto Gnosis Pay piniginės prisijungimo.", "card.owner-not-imported.title": "Pridėk Gnosis Pay paskyros savininką", "card.page.order_free_physical_card": "Užsakyti fizin<PERSON> kortel<PERSON>", "card.pin.change_pin_at_atm": "PIN kodą galima pakeisti tam tikruose bankomatuose", "card.pin.timeout": "E<PERSON>nas už<PERSON>ys po {seconds} sek.", "card.quick-actions.add-assets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.add-cash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.details": "<PERSON><PERSON><PERSON>", "card.quick-actions.freeze": "Užšaldyti", "card.quick-actions.freezing": "<PERSON>ž<PERSON><PERSON><PERSON>", "card.quick-actions.unfreeze": "Atšil<PERSON>ti", "card.quick-actions.unfreezing": "Atšildoma", "card.quick-actions.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.read-only-detected.create-new": "Pridėti naują piniginę kaip sa<PERSON>", "card.read-only-detected.import-current-owner": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>, <PERSON><PERSON> {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Importuok pinigi<PERSON>ė<PERSON> priva<PERSON> raktus arba pradinę frazę {address}", "card.read-only-detected.title": "Kortelė aptikta tik skaitymui skirtoje piniginėje. Pasirink piniginę kortelei valdyti", "card.remove-owner.queued": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> eil<PERSON>", "card.settings.disconnect-from-zeal": "Atjun<PERSON><PERSON> nuo <PERSON>", "card.settings.edit-owners": "<PERSON><PERSON><PERSON>", "card.settings.getCard": "<PERSON><PERSON><PERSON> kort<PERSON>", "card.settings.getCard.subtitle": "Virtualios ar fi<PERSON> k<PERSON>", "card.settings.notRecharging": "Nepapildoma", "card.settings.notifications.subtitle": "Gauk pranešimus apie mokėjimus", "card.settings.notifications.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.page.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>", "card.settings.select-card.cancelled-cards": "<PERSON><PERSON><PERSON><PERSON>", "card.settings.setAutoRecharge": "Nustatyti auto papildymą", "card.settings.show-card-address": "<PERSON><PERSON><PERSON> k<PERSON>el<PERSON>", "card.settings.spend-limit": "Nustatyti išlaidų limitą", "card.settings.spend-limit-title": "<PERSON><PERSON><PERSON><PERSON> dienos limitas: {limit}", "card.settings.switch-active-card": "Pakeisti aktyvią kortelę", "card.settings.switch-active-card-description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>: {card}", "card.settings.switch-card.card-item.cancelled": "<PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.frozen": "Užšaldyta", "card.settings.switch-card.card-item.title": "Gnosis Pay kortelė", "card.settings.switch-card.card-item.title.physical": "<PERSON><PERSON><PERSON> kortel<PERSON>", "card.settings.switch-card.card-item.title.virtual": "<PERSON><PERSON>", "card.settings.switch-card.title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "card.settings.targetBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON> likuti<PERSON>: {threshold}", "card.settings.view-pin": "Rodyti PIN", "card.settings.view-pin-description": "Visada saugok savo PIN kodą", "card.title": "Kortelė", "card.transactions.header": "Kortelės operacijos", "card.transactions.see_all": "Rodyti visas operacijas", "card.virtual": "VIRTUALI", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Cashback pervedamas į tavo kortelę savaitės pradžioje po to, kai jis buvo uždir<PERSON>as.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "<PERSON>o daugiau įneši, tuo daugiau gauni su kiekvienu pirkiniu.", "cardCashback.onboarding.title": "Gauk iki {percentage} cashback", "cardCashbackWithdraw.amount": "<PERSON>š<PERSON><PERSON><PERSON> suma", "cardCashbackWithdraw.header": "<PERSON><PERSON><PERSON><PERSON> {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "Kortelė užblokuota ir jos nepavyko aktyvuoti", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "<PERSON><PERSON> {percentage}", "cardWidget.startEarning": "Pradėk uždirbti", "cardWithdraw.amount": "<PERSON>š<PERSON><PERSON><PERSON> suma", "cardWithdraw.header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardWithdraw.selectWithdrawWallet.title": "Pasirink piniginę{br}l<PERSON><PERSON><PERSON> išėmimui", "cardWithdraw.success.cta": "Uždaryti", "cardWithdraw.success.subtitle": "Saugumo sumetimais visi išėmimai iš Gnosis Pay kortelės apdorojami 3 minutes", "cardWithdraw.success.title": "Šis pakeitimas užtruks 3 minutes", "card_top_up_trx.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card_top_up_trx.to": "<PERSON><PERSON>", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "<PERSON><PERSON><PERSON> iki", "cards.card_number": "<PERSON><PERSON><PERSON><PERSON><PERSON> numeris", "cards.choose-wallet.no-active-accounts": "Neturi aktyvių piniginių", "cards.copied_card_number": "<PERSON><PERSON><PERSON><PERSON><PERSON> numeris nukopi<PERSON>", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "<PERSON><PERSON><PERSON><PERSON><PERSON> die<PERSON> limitas", "cards.transactions.decline_reason.incorrect_pin": "Neteisingas PIN kodas", "cards.transactions.decline_reason.incorrect_security_code": "Neteisingas saugos kodas", "cards.transactions.decline_reason.invalid_amount": "Neteisinga suma", "cards.transactions.decline_reason.low_balance": "<PERSON>epaka<PERSON><PERSON> likutis", "cards.transactions.decline_reason.other": "Atmes<PERSON>", "cards.transactions.decline_reason.pin_tries_exceeded": "Viršytas PIN bandymų skaičius", "cards.transactions.status.refund": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cards.transactions.status.reversal": "<PERSON>ša<PERSON>mas", "cashback-deposit.trx.title": "Įnašas į Cashback", "cashback-estimate.text": "Tai yra įvertis, o NE garantuota išmoka. Taikomos visos viešai žinomos „cashback“ taisyklė<PERSON>, ta<PERSON><PERSON><PERSON> „Gnosis Pay“ savo nuožiūra gali neįtraukti tam tikrų operacijų. Maksimali išleista suma {amount} per savaitę atitinka „cashback“ sąlygas, net jei šios operacijos įvertis rodytų didesnę bendrą sumą.", "cashback-estimate.text.fallback": "<PERSON>, o ne garantuota suma. Nors taikomos visos viešosios cashback ta<PERSON><PERSON><PERSON><PERSON><PERSON>, Gnosis Pay gali savo nuožiūra neįtraukti operacijų.", "cashback-estimate.title": "Numatomas cashback", "cashback-onbarding-tersm.subtitle": "Tavo kortelės operacijų duomenys bus bendrinami su Karpatkey, kuri atsaki<PERSON> už cashback apdovanojimų paskirstymą. Spausdamas „Sutinku“, sutinki su Gnosis DAO cashback <terms>sąlygomis</terms>", "cashback-onbarding-tersm.title": "Naudojimosi s<PERSON>lygos ir privatumas", "cashback-tx-activity.retry": "Bandyti dar kartą", "cashback-unconfirmed-payments-info.subtitle": "Mokėjimai atitiks cashback reikalavimus, kai bus atsiskaityta su prekybininku. <PERSON>ki tol jie rodomi kaip nepatvirtinti mokėjimai. <PERSON><PERSON> <PERSON>siskaitytus mokėjimus cashback neskiriamas.", "cashback-unconfirmed-payments-info.title": "Nepatvirtinti k<PERSON> m<PERSON>", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "Įnašas", "cashback.activity.title": "Paskutiniai veiksmai", "cashback.activity.withdrawal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.deposit": "Įnešti", "cashback.deposit.amount.label": "Įnašo suma", "cashback.deposit.change": "{from} į {to}", "cashback.deposit.confirmation.subtitle": "Cashback normos atnaujinamos kartą per savaitę. Įnešk dabar, kad padidintum kitos savaitės cashback.", "cashback.deposit.confirmation.title": "<PERSON><PERSON><PERSON><PERSON> gauti {percentage} nuo {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Iškeisk žetonus į {currency} tinkle {network} Chain", "cashback.deposit.get.tokens.title": "Gauk {currency} žetonų", "cashback.deposit.header": "Įnešti {currency}", "cashback.deposit.max_label": "Maks.: {amount}", "cashback.deposit.select-wallet.title": "Pasirink piniginę įnašui", "cashback.deposit.yourcashback": "Tavo cashback", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "Pasirink piniginę{br}iš<PERSON>mimu<PERSON>", "cashback.transaction-details.network-label": "Tin<PERSON><PERSON>", "cashback.transaction-details.reward-period": "{start} – {end}", "cashback.transaction-details.top-row.label-deposit": "<PERSON><PERSON>", "cashback.transaction-details.top-row.label-rewards": "Cashback la<PERSON><PERSON><PERSON>", "cashback.transaction-details.top-row.label-withdrawal": "<PERSON><PERSON>", "cashback.transaction-details.transaction": "Operacijos ID", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Cashback operacijos", "cashback.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "cashback.withdraw.confirmation.cashback_reduction": "<PERSON><PERSON> sa<PERSON> cashback, įskaitant tai, ką j<PERSON> gava<PERSON>, sum<PERSON><PERSON><PERSON><PERSON> nuo {before} iki {after}", "cashback.withdraw.queued": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.withdrawal.change": "{from} į {to}", "cashback.withdrawal.confirmation.subtitle": "<PERSON><PERSON><PERSON><PERSON> {amount} išėmimą su 3 minučių vėlavimu. Tai sumažins tavo cashback iki {after}.", "cashback.withdrawal.confirmation.title": "<PERSON><PERSON><PERSON><PERSON>, cashback <PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.withdrawal.delayTransaction.title": "Pradėti GNO išėmimą su{br} 3 minučių vėlavimu", "cashback.withdrawal.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.withdrawal.yourcashback": "Tavo cashback", "celebration.aave": "Uždirbta su Aave", "celebration.cashback.subtitle": "Pristatyta {code}", "celebration.cashback.subtitleGNO": "{amount} paskutinį kartą gauta", "celebration.chf": "Uždirbta su Frankencoin", "celebration.lido": "Uždirbta su Lido", "celebration.sky": "Uždirbta su Sky", "celebration.title": "Visas cashback", "celebration.well_done.title": "<PERSON>uiku!", "change-withdrawal-account.add-new-account": "Pridėk kitą banko sąskaitą", "change-withdrawal-account.item.shortText": "{currency} sąskaita", "check-confirmation.approve.footer.for": "<PERSON><PERSON>", "checkConfirmation.title": "<PERSON><PERSON><PERSON>", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin ir Ether", "collateral.ethereum": "Ethereum", "collateral.other": "<PERSON><PERSON>", "collateral.rwa": "Realaus pasaulio turtas", "collateral.stablecoins": "Stabiliosios moneto<PERSON> (susietos su USD)", "collateral.us-t-bills": "JAV iždo vekseliai", "confirm-bank-transfer-recipient.bullet-1": "Skaitmeniniams EUR mokesčiai netaikomi", "confirm-bank-transfer-recipient.bullet-2": "Įnašai į {wallet<PERSON>abe<PERSON>} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Bendrinti Gnosis Pay paskyros duomenis su Monerium – įgaliota ir reguliuojama EMI. <link>Sužinok daugiau</link>", "confirm-bank-transfer-recipient.bullet-4": "Sutikti su Monerium <link>paslaugų teikimo sąlygomis</link>", "confirm-bank-transfer-recipient.title": "<PERSON><PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.cancel": "<PERSON><PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.confirm": "<PERSON><PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.saving": "Saugoma", "confirm-change-withdrawal-account.subtitle": "Visi išėmimai iš Zeal bus gauti čia.", "confirm-change-withdrawal-account.title": "Pakeisti gaunantį banką", "confirm-ramove-withdrawal-account.title": "Pašalinti banko sąskaitą", "confirm-remove-withdrawal-account.subtitle": "Sąskaita bus pašalinta. Galėsi ją pridėti vėl.", "confirmTransaction.finalNetworkFee": "<PERSON><PERSON><PERSON>", "confirmTransaction.importKeys": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "confirmTransaction.networkFee": "<PERSON><PERSON><PERSON>", "confirmation.title": "<PERSON><PERSON><PERSON><PERSON> {amount} gav<PERSON><PERSON><PERSON> {recipient}", "conflicting-monerium-account.add-owner": "<PERSON><PERSON><PERSON><PERSON> kaip <PERSON> savininką", "conflicting-monerium-account.create-wallet": "Sukurti naują Smart Wallet", "conflicting-monerium-account.disconnect-card": "Atjunk kortelę nuo Zeal ir prijunk su nauju savininku", "conflicting-monerium-account.header": "{wallet} susieta su kita Monerium paskyra", "conflicting-monerium-account.subtitle": "Pakeisk Gnosis Pay savininko piniginę", "connection.diconnected.got_it": "Supratau!", "connection.diconnected.page1.subtitle": "Zeal veikia visur, kur veikia MetaMask. Tiesiog prisijunk kaip su MetaMask.", "connection.diconnected.page1.title": "<PERSON><PERSON> pris<PERSON> su <PERSON>?", "connection.diconnected.page2.subtitle": "<PERSON><PERSON><PERSON> daug parinkčių. Zeal gali būti viena iš jų. Jei Zeal nematai...", "connection.diconnected.page2.title": "Spausk „Connect Wallet“", "connection.diconnected.page3.subtitle": "Pasiūlysime prisijungti su Zeal. „Browser“ arba „Injected“ taip pat turėtų veikti. Išbandyk!", "connection.diconnected.page3.title": "Pasirink MetaMask", "connectionSafetyCheck.tag.caution": "Atsargiai", "connectionSafetyCheck.tag.danger": "<PERSON><PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.passed": "<PERSON><PERSON><PERSON><PERSON>", "connectionSafetyConfirmation.subtitle": "Ar tikrai nori tęsti?", "connectionSafetyConfirmation.title": "Ši svetainė atrodo pavojinga", "connection_state.connect.cancel": "<PERSON><PERSON><PERSON><PERSON>", "connection_state.connect.changeToMetamask": "Pakeisti į MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Pakeisti į MetaMask", "connection_state.connect.connect_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connection_state.connect.expanded.connected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connection_state.connect.expanded.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connection_state.connect.safetyChecksLoading": "<PERSON><PERSON><PERSON><PERSON>", "connection_state.connect.safetyChecksLoadingError": "Nepavyko atlikti saugumo patikrų", "connection_state.connected.expanded.disconnectButton": "Atsijungti nuo Z<PERSON>", "connection_state.connected.expanded.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "copied-diagnostics": "Diagnostika nukopijuota", "copy-diagnostics": "Kopijuoti diagnostiką", "counterparty.component.add_recipient_primary_text": "Pridėti banko gavėją", "counterparty.country": "<PERSON><PERSON>", "counterparty.countryTitle": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.currency": "Valiuta", "counterparty.delete.success.title": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.edit.success.title": "Pakeitimai išsaugoti", "counterparty.errors.country_required": "<PERSON><PERSON><PERSON> šalį", "counterparty.errors.first_name.invalid": "Vardas turi būti il<PERSON>", "counterparty.errors.last_name.invalid": "<PERSON><PERSON><PERSON> turi būti <PERSON>", "counterparty.first_name": "Vardas", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Siųsti į banką", "countrySelector.noCountryFound": "<PERSON><PERSON> ne<PERSON>ta", "countrySelector.title": "Pasirink šalį", "create-passkey.cta": "Sukurti prieigos raktą", "create-passkey.extension.cta": "<PERSON><PERSON><PERSON><PERSON>", "create-passkey.footnote": "Vei<PERSON>a su", "create-passkey.mobile.cta": "Pradėti saugumo s<PERSON>", "create-passkey.steps.enable-recovery": "Nustatyti atkūrimą iš debesies", "create-passkey.steps.setup-biometrics": "Įjungti biometrinį saugumą", "create-passkey.subtitle": "Prieigos raktai yra saugesni u<PERSON> <PERSON><PERSON><PERSON><PERSON>, užšifruoti debesies saugykloje, kad b<PERSON><PERSON><PERSON> lengva atkurti.", "create-passkey.title": "Apsaugoti paskyrą", "create-smart-wallet": "Sukurti Smart Wallet", "create-userop.progress.text": "<PERSON><PERSON><PERSON>", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "<PERSON><PERSON><PERSON><PERSON>", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Eiti į Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "Jau pradėjai kortelės užsakymą. Grįžk į Gnosis Pay svetainę, kad jį užbaigtum.", "create_recharge_preferences.card": "Kortelė", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "Uždirbk {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "<PERSON><PERSON><PERSON>", "create_recharge_preferences.link_accounts_title": "<PERSON><PERSON>", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "Tavo kortelė NEBUS papildoma automatiškai po kiekvieno mokėjimo.", "create_recharge_preferences.not_configured_title": "Uždirbk ir leisk", "create_recharge_preferences.recharge_from_earn_accounts_description": "Tavo kortelė automatiškai pasipildo po kiekvieno mokėjimo iš tavo „Earn“ paskyros.", "create_recharge_preferences.subtitle": "per metus", "creating-account.loading": "<PERSON><PERSON><PERSON> p<PERSON>", "creating-gnosis-pay-account": "<PERSON><PERSON><PERSON> p<PERSON>", "currencies.bridge.select_routes.emptyState": "<PERSON>iam <PERSON> maršrutų neradome", "currency.add_currency.add_token": "<PERSON><PERSON><PERSON><PERSON>", "currency.add_currency.not_a_valid_address": "Tai nėra galiojantis <PERSON>o adresas", "currency.add_currency.token_decimals_feild": "<PERSON><PERSON><PERSON>", "currency.add_currency.token_feild": "Tokeno adresas", "currency.add_currency.token_symbol_feild": "Tokeno simbolis", "currency.add_currency.update_token": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.add_custom.remove_token.cta": "<PERSON><PERSON><PERSON><PERSON>", "currency.add_custom.remove_token.header": "<PERSON><PERSON><PERSON><PERSON>", "currency.add_custom.remove_token.subtitle": "Tokenas bus paslėptas, bet liks piniginėje", "currency.add_custom.token_removed": "<PERSON><PERSON><PERSON> p<PERSON>", "currency.add_custom.token_updated": "<PERSON><PERSON><PERSON>", "currency.balance_label": "Likutis: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "Tavo banko pavedimu s<PERSON> pervesta {fiat} į {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "Gavai {crypto}", "currency.bankTransfer.deposit_status.success": "Gauta į tavo piniginę", "currency.bankTransfer.deposit_status.title": "Įnešimas", "currency.bankTransfer.off_ramp.check_bank_account": "Patikrink savo banko sąskaitą", "currency.bankTransfer.off_ramp.complete": "Atlikta", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Siunčiama į tavo banką", "currency.bankTransfer.off_ramp.transferring_to_currency": "<PERSON><PERSON><PERSON> į {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Lėšos jau turėjo pasiekti tavo banko sąskaitą.", "currency.bankTransfer.withdrawal_status.success": "Išsiųsta į tavo banką", "currency.bankTransfer.withdrawal_status.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.email": "<PERSON>. p<PERSON><PERSON><PERSON> ad<PERSON>", "currency.bank_transfer.create_unblock_user.email_invalid": "Neteisingas el. paštas", "currency.bank_transfer.create_unblock_user.email_missing": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name": "Vardas", "currency.bank_transfer.create_unblock_user.first_name_missing": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>, s<PERSON><PERSON><PERSON><PERSON>, tarpai ir - . , & ( ) '", "currency.bank_transfer.create_unblock_user.last_name": "Pa<PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_missing": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>, s<PERSON><PERSON><PERSON><PERSON>, tarpai ir - . , & ( ) '", "currency.bank_transfer.create_unblock_user.note": "Tęsdamas sutinki su Unblock (partnerio) <terms>Taisyklėmis</terms> ir <policy>Privatumo politika</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Vardą ra<PERSON>yk taip, kaip nurodyta banke.", "currency.bank_transfer.create_unblock_user.title": "Susiek savo banko sąskaitą", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Sąskaitos numeris", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "<PERSON><PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Pageidaujama valiuta", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.success": "Sąskaita paruošta", "currency.bank_transfer.create_unblock_withdraw_account.title": "Susiek savo banko sąskaitą", "currency.bank_transfer.residence-form.address-required": "Privaloma", "currency.bank_transfer.residence-form.address-unsupported-char": "<PERSON><PERSON><PERSON><PERSON><PERSON> tik <PERSON>, s<PERSON><PERSON><PERSON><PERSON>, tarpai ir , ; {apostrophe} - \\\\ leidžiama.", "currency.bank_transfer.residence-form.city-required": "Privaloma", "currency.bank_transfer.residence-form.city-unsupported-char": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, tarpai ir . , - & ( ) {apostrophe} leid<PERSON><PERSON><PERSON>.", "currency.bank_transfer.residence-form.postcode-invalid": "Neteisingas paš<PERSON> kodas", "currency.bank_transfer.residence-form.postcode-required": "Privaloma", "currency.bank_transfer.validation.invalid.account_number": "Netinkamas sąskaitos numeris", "currency.bank_transfer.validation.invalid.iban": "Netinkamas IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Netinkamas rūš<PERSON><PERSON> kodas", "currency.bridge.amount_label": "Bridge pervedimo suma", "currency.bridge.best_returns.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> suma, įskaičiavus m<PERSON><PERSON><PERSON><PERSON>.", "currency.bridge.best_returns_popup.title": "Didžiausia grąža", "currency.bridge.bridge_from": "<PERSON><PERSON>", "currency.bridge.bridge_gas_fee_loading_failed": "Nepavyko įkelti tinklo moke<PERSON>io", "currency.bridge.bridge_low_slippage": "Labai ma<PERSON>. Pabandyk padidinti.", "currency.bridge.bridge_provider": "<PERSON><PERSON><PERSON>", "currency.bridge.bridge_provider_loading_failed": "Nepavyko įkelti paslaugų teikėjų", "currency.bridge.bridge_settings": "Bridge nustatymai", "currency.bridge.bridge_status.subtitle": "<PERSON><PERSON><PERSON><PERSON> {name}", "currency.bridge.bridge_status.title": "Bridge", "currency.bridge.bridge_to": "Į", "currency.bridge.fastest_route_popup.subtitle": "<PERSON><PERSON> te<PERSON> siūlo g<PERSON> marš<PERSON>.", "currency.bridge.fastest_route_popup.title": "Greič<PERSON><PERSON><PERSON>", "currency.bridge.from": "<PERSON><PERSON>", "currency.bridge.success": "Užbaigta", "currency.bridge.title": "Bridge", "currency.bridge.to": "Į", "currency.bridge.topup": "<PERSON><PERSON><PERSON><PERSON><PERSON> {symbol}", "currency.bridge.withdrawal_status.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.card.card_top_up_status.title": "Papild<PERSON><PERSON> kortelę", "currency.destination_amount": "Galutinė suma", "currency.hide_currency.confirm.subtitle": "Paslėpk tokeną. Bet kada gal<PERSON>si jį parodyti.", "currency.hide_currency.confirm.title": "Slėpti tokeną", "currency.hide_currency.success.title": "<PERSON><PERSON><PERSON> p<PERSON>", "currency.label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (nebūtina)", "currency.last_name": "Pa<PERSON><PERSON>", "currency.max_loading": "Maks.:", "currency.swap.amount_to_swap": "<PERSON><PERSON>", "currency.swap.best_return": "Geriausios grąžos ma<PERSON>š<PERSON>tas", "currency.swap.destination_amount": "Galutinė suma", "currency.swap.header": "<PERSON><PERSON><PERSON>", "currency.swap.max_label": "Likutis: {amount}", "currency.swap.provider.header": "Keitimo <PERSON>", "currency.swap.select_to_token": "Pasirink tokeną", "currency.swap.swap_gas_fee_loading_failed": "Nepavyko įkelti tinklo moke<PERSON>io", "currency.swap.swap_provider_loading_failed": "Nepavyko įkelti teikėjų", "currency.swap.swap_settings": "Keitimo nustatymai", "currency.swap.swap_slippage_too_low": "Labai ma<PERSON>. Pabandyk padidinti.", "currency.swaps_io_native_token_swap.subtitle": "Naudojant Swaps.IO", "currency.swaps_io_native_token_swap.title": "Si<PERSON>sti", "currency.withdrawal.amount_from": "<PERSON><PERSON>", "currency.withdrawal.amount_to": "Į", "currencySelector.title": "<PERSON><PERSON><PERSON> vali<PERSON>ą", "dApp.wallet-does-not-support-chain.subtitle": "<PERSON><PERSON><PERSON>, tavo piniginė nepalai<PERSON> {network}. Bandyk prisijungti su kita pinigine arba naudok Zeal.", "dApp.wallet-does-not-support-chain.title": "<PERSON><PERSON><PERSON><PERSON>", "dapp.connection.manage.confirm.disconnect.all.cta": "<PERSON><PERSON><PERSON><PERSON>", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Ar tikrai nori atsijungti nuo visų programėlių?", "dapp.connection.manage.confirm.disconnect.all.title": "Atsijungti nuo visų", "dapp.connection.manage.connection_list.main.button.title": "<PERSON>si<PERSON><PERSON><PERSON>", "dapp.connection.manage.connection_list.no_connections": "Neturi prijungtų programėlių", "dapp.connection.manage.connection_list.section.button.title": "Atsijungti nuo visų", "dapp.connection.manage.connection_list.section.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dapp.connection.manage.connection_list.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dapp.connection.manage.disconnect.success.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dapp.metamask_mode.title": "MetaMask režimas", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dc25-card-marketing-card.right.subtitle": "100 žmonių", "dc25-card-marketing-card.title": "Pirmieji 100 išleidę 50 € gaus {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Šio veiksmo negalėsi atlikti 3 min. Dėl saugumo priežasčių bet koks kortelės nustatymų keitimas ar lėšų išėmimas apdorojamas 3 minutes.", "delayQueueBusyBanner.processing-yout-action.title": "<PERSON><PERSON> ve<PERSON>, p<PERSON><PERSON><PERSON> p<PERSON>ti", "delayQueueBusyWidget.cardFrozen": "Kortelė užšaldyta", "delayQueueBusyWidget.processingAction": "Apdor<PERSON><PERSON> tavo veiksmas", "delayQueueFailedBanner.action-incomplete.get-support": "<PERSON><PERSON><PERSON>", "delayQueueFailedBanner.action-incomplete.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, įvyko klaida atliekant lėš<PERSON> išėmimą ar atnaujinant nustatymus. Susisiek su palaikymo komanda per Discord.", "delayQueueFailedBanner.action-incomplete.title": "<PERSON><PERSON><PERSON><PERSON>", "delayQueueFailedWidget.actionIncomplete.title": "Kortelė<PERSON>", "delayQueueFailedWidget.cardFrozen.subtitle": "Kortelė užšaldyta", "delayQueueFailedWidget.contactSupport": "Susisiekti su pagalba", "delay_queue_busy.subtitle": "Saugumo sumetimais bet kokie kortelės nustatymų pakeitimai ar lėšų išėmimai apdorojami per 3 minutes. Tuo metu tavo kortelė yra įšaldoma.", "delay_queue_busy.title": "<PERSON><PERSON> ve<PERSON>", "delay_queue_failed.contact_support": "<PERSON><PERSON><PERSON><PERSON>", "delay_queue_failed.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, įvyko klaida atliekant lėš<PERSON> išėmimą ar atnaujinant nustatymus. Susisiek su palaikymo komanda per Discord.", "delay_queue_failed.title": "Susisiek su palaikymo komanda", "deploy-earn-form-smart-wallet.in-progress.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> „<PERSON><PERSON><PERSON>“", "deposit": "Įnešti", "disconnect-card-popup.cancel": "<PERSON><PERSON><PERSON><PERSON>", "disconnect-card-popup.disconnect": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.subtitle": "Tavo kortelė bus pašalinta iš Zeal programėlės. Tavo piniginė vis dar bus susieta su kortele Gnosis Pay programėlėje. Kortelę galėsi vėl prijungti bet kada.", "disconnect-card-popup.title": "Atsieti kortelę", "distance.long.days": "{count} dienos", "distance.long.hours": "{count} valandos", "distance.long.minutes": "{count} minut<PERSON>s", "distance.long.months": "{count} m<PERSON><PERSON><PERSON>", "distance.long.seconds": "{count} sekund<PERSON>s", "distance.long.years": "{count} metai", "distance.short.days": "{count} d.", "distance.short.hours": "{count} val.", "distance.short.minutes": "{count} min.", "distance.short.months": "{count} mėn.", "distance.short.seconds": "{count} sek.", "distance.short.years": "{count} m.", "duration.short.days": "{count}d", "duration.short.hours": "{count}v", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "Įnašas", "earn-deposit-view.into": "Į", "earn-deposit-view.to": "<PERSON><PERSON>", "earn-deposit.swap.transfer-provider": "<PERSON><PERSON><PERSON>", "earn-taker-investment-details.accrued-realtime": "Kaupiama realiu laiku", "earn-taker-investment-details.asset-class": "<PERSON><PERSON><PERSON>", "earn-taker-investment-details.asset-coverage-ratio": "<PERSON><PERSON><PERSON> pad<PERSON>", "earn-taker-investment-details.asset-reserve": "<PERSON><PERSON><PERSON>", "earn-taker-investment-details.base_currency.label": "<PERSON><PERSON><PERSON> valiuta", "earn-taker-investment-details.chf.description": "Uždirbk palūkanas nuo savo CHF, įnešdamas zCHF į Frankencoin – patikimą skaitmeninių pinigų rinką. Palūkanos gaunamos iš mažos rizikos, perteklinio užstato paskolų Frankencoin platformoje ir išmokamos realiu laiku. Tavo lėšos saugomos specialioje subsąskaitoje, kurią valdai tik tu.", "earn-taker-investment-details.chf.description.with_address_link": "Uždirbk palūkanas nuo savo CHF, įnešdamas zCHF į Frankencoin – patikimą skaitmeninių pinigų rinką. Palūkanos gaunamos iš mažos rizikos, perteklinio užstato paskolų Frankencoin platformoje ir išmokamos realiu laiku. Tavo lėšos saugomos specialioje subsąskaitoje <link>(kopijuoti 0x)</link> , kuri<PERSON> valdai tik tu.", "earn-taker-investment-details.chf.label": "Skaitmeninis Šveicarijos frank<PERSON>", "earn-taker-investment-details.collateral-composition": "Įkaito sud<PERSON>tis", "earn-taker-investment-details.depositor-obligations": "Indėlininkų įsipareigojimai", "earn-taker-investment-details.eure.description": "Uždirbk palūkanas nuo savo eurų įnešdamas EURe į Aave – patikimą skaitmeninių pinigų rinką. EURe yra visiškai reguliuojama euro stabili moneta, išleista Monerium ir padengta 1:1 apsaugotose sąskaitose. Palūkanos gaunamos iš mažos rizikos, perteklinio užstato paskolų Aave platformoje ir mokamos realiu laiku. Tavo lėšos lieka saugioje subsąskaitoje, kuri<PERSON> valdai tik tu.", "earn-taker-investment-details.eure.description.with_address_link": "Uždirbk palūkanas nuo savo eurų įnešdamas EURe į Aave – patikimą skaitmeninių pinigų rinką. EURe yra visiškai reguliuojama euro stabili moneta, išleista Monerium ir padengta 1:1 apsaugotose sąskaitose. Palūkanos gaunamos iš mažos rizikos, perteklinio užstato paskolų Aave platformoje ir mokamos realiu laiku. Tavo lėšos lieka saugioje subsąskaitoje <link>(kopijuoti 0x)</link> , kurią valdai tik tu.", "earn-taker-investment-details.eure.label": "Skait<PERSON><PERSON><PERSON> (EURe)", "earn-taker-investment-details.faq": "DUK", "earn-taker-investment-details.fixed-income": "<PERSON><PERSON><PERSON><PERSON> paja<PERSON>", "earn-taker-investment-details.issuer": "<PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.key-facts": "<PERSON><PERSON><PERSON><PERSON><PERSON> faktai", "earn-taker-investment-details.liquidity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.operator": "<PERSON><PERSON><PERSON>", "earn-taker-investment-details.projected-yield": "Prognozuojama metin<PERSON> g<PERSON>", "earn-taker-investment-details.see-other-faq": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> visus kitus DUK", "earn-taker-investment-details.see-realtime": "<PERSON><PERSON><PERSON> realaus laiko duomenis", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Paskutinių 12 mėn. grąža", "earn-taker-investment-details.total-collateral": "Visas užstatas", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "Visa ZCHF pasiūla", "earn-taker-investment-details.total_deposits": "Visi Aave indėliai", "earn-taker-investment-details.usd.description": "Sky yra skaitmeninių pinigų rinka, siūlanti stabilią, JAV doleriais išreikštą grąžą iš trumpalaikių JAV iždo vertybinių popierių ir perteklinio užstato skolinimo – be kriptovaliutų svyravimų, su 24/7 prieiga prie lėšų ir skaidriu, on-chain padengimu.", "earn-taker-investment-details.usd.description.with_address_link": "Sky yra skaitmeninių pinigų rinka, siūlanti stabilią, JAV doleriais išreikštą grąžą iš trumpalaikių JAV iždo vertybinių popierių ir perteklinio užstato skolinimo – be kriptovaliutų svyravimų, su 24/7 prieiga prie lėšų ir skaidriu, on-chain padengimu. Investicijos yra subsąskaitoje <link>(kopijuoti 0x)</link> , kurią valdai tu.", "earn-taker-investment-details.usd.ftx-difference": "<PERSON>o tai skiriasi nuo FT<PERSON>, <PERSON><PERSON><PERSON>, BlockFi ar Luna?", "earn-taker-investment-details.usd.high-returns": "<PERSON><PERSON> grąža gali būti to<PERSON>a <PERSON>, ypa<PERSON> palyginti su tradiciniais bankais?", "earn-taker-investment-details.usd.how-is-backed": "<PERSON><PERSON> padengtas Sky USD ir kas nutiks mano pinigams, j<PERSON> bankru<PERSON>?", "earn-taker-investment-details.usd.income-sources": "Pajamų šaltiniai 2024", "earn-taker-investment-details.usd.insurance": "Ar mano l<PERSON>š<PERSON> apdraustos ar garantuotos kokio nors subjekto (pvz., FDIC ar pan<PERSON>šaus)?", "earn-taker-investment-details.usd.label": "Skaitmeninis JAV doleris", "earn-taker-investment-details.usd.lose-principal": "Ar realu prarasti pagrindinę sumą ir kokiomis aplinkybėmis?", "earn-taker-investment-details.variable-rate": "Skolinimas su kintama palūkanų norma", "earn-taker-investment-details.withdraw-anytime": "<PERSON><PERSON><PERSON><PERSON><PERSON> bet kada", "earn-taker-investment-details.yield": "Grąža", "earn-withdrawal-view.approve.for": "<PERSON><PERSON>", "earn-withdrawal-view.approve.into": "Į", "earn-withdrawal-view.swap.into": "Į", "earn-withdrawal-view.withdraw.to": "<PERSON><PERSON>", "earn.add_another_asset.title": "Pasirink uždarbio turtą", "earn.add_asset": "<PERSON><PERSON><PERSON><PERSON> turt<PERSON>", "earn.asset_view.title": "<PERSON><PERSON><PERSON><PERSON>", "earn.base-currency-popup.text": "<PERSON>zinė valiuta yra valiuta, kuria vertinami ir registruojami tavo indėliai, gr<PERSON><PERSON><PERSON> ir transakcijos. Jei įneši lėšas kita valiuta (pvz., EUR į USD), jos nedel<PERSON>t konvertuojamos į bazinę valiutą pagal esamą kursą. Po konvertavimo tavo likutis išlieka stabilus bazinėje valiutoje, tačiau ateityje išsiimant lėšas vėl gali prireikti valiutos keitimo.", "earn.base-currency-popup.title": "<PERSON><PERSON><PERSON> valiuta", "earn.card-recharge.disabled.list-item.title": "Automatinis p<PERSON>il<PERSON> i<PERSON>", "earn.card-recharge.enabled.list-item.title": "Automatinis papildymas įjungtas", "earn.choose_wallet_to_deposit.title": "Įnešti iš", "earn.config.currency.eth": "Uždirbk Ethereum", "earn.config.currency.on_chain_address_subtitle": "On-chain adresas", "earn.config.currency.us_dollars": "Nustatyk banko pavedimus", "earn.configured_widget.current_apy.title": "Dabartinis APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} per metus", "earn.confirm.currency.cta": "Įnešti", "earn.currency.eth": "Uždirbk Ethereum", "earn.deploy.status.title": "Sukurti Earn p<PERSON>", "earn.deploy.status.title_with_taker": "<PERSON><PERSON><PERSON><PERSON> {title} <PERSON><PERSON><PERSON>", "earn.deposit": "Įnešti", "earn.deposit.amount_to_deposit": "Įnešama suma", "earn.deposit.deposit": "Įnešti", "earn.deposit.enter_amount": "Įvesk sumą", "earn.deposit.no_routes_found": "Maršrutų nerasta", "earn.deposit.not_enough_balance": "Nepakanka lėšų", "earn.deposit.select-currency.title": "Pasirink žetoną įnešimui", "earn.deposit.select_account.title": "Pasirinkti Earn s<PERSON>ska<PERSON>ą", "earn.desposit_form.title": "Įnešti į Uždarbį", "earn.earn_deposit.status.title": "Įmokėti į Earn", "earn.earn_deposit.trx.title": "Įnašas į Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "<PERSON><PERSON><PERSON><PERSON> bet kada", "earn.earn_withdraw.status.title": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>", "earn.earn_withdraw.trx.title.approval": "<PERSON><PERSON><PERSON><PERSON>", "earn.earn_withdraw.trx.title.withdraw_into_asset": "<PERSON><PERSON><PERSON><PERSON> į {asset}", "earn.earn_withdraw.trx.title.withdrawal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.recharge.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "earn.recharge.earn_not_configured.enable_some_account.error": "Aktyvuok paskyrą", "earn.recharge.earn_not_configured.enter_amount.error": "Įvesk sumą", "earn.recharge.select_taker.header": "Papildyti kortelę eilės tvarka iš", "earn.recharge_card_tag.on": "įjungtas", "earn.recharge_card_tag.recharge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharge_not_configured": "<PERSON><PERSON><PERSON><PERSON> papil<PERSON>", "earn.recharge_card_tag.recharge_off": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharging": "Pildoma", "earn.recharge_configured.disable.trx.title": "Išjungti automatinį papildymą", "earn.recharge_configured.trx.disclaimer": "<PERSON> naudo<PERSON> k<PERSON>, <PERSON><PERSON><PERSON><PERSON> au<PERSON>cionas, k<PERSON><PERSON> už <PERSON>vo Earn lėšas nupirksime sumą, lygią tavo mokėjimui. Šis aukcionas dažniausiai užtikrina geriausią rinkos kursą, tač<PERSON><PERSON> atkreipk dėmesį, kad tinklo kursas gali skirtis nuo realaus valiutų keitimo kurso.", "earn.recharge_configured.trx.subtitle": "Po kiekvieno mokėjimo lėšos bus automatiškai pridėtos iš tavo Earn pask<PERSON> (-ų), kad k<PERSON><PERSON> lik<PERSON> i<PERSON> {value}", "earn.recharge_configured.trx.title": "Nustatyti automatinį papildymą iki {value}", "earn.recharge_configured.updated.trx.title": "Išsaugoti papildymo nustatymus", "earn.risk-banner.subtitle": "Tai privati piniginė be reguliacinės apsaugos nuo praradimo.", "earn.risk-banner.title": "Suprask rizikas", "earn.set_recharge.status.title": "Nustatyti automatinį papildymą", "earn.setup_reacharge.input.disable.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.setup_reacharge.input.label": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "earn.setup_reacharge_form.title": "Automat<PERSON>s papildymas palaiko tavo {br} k<PERSON><PERSON><PERSON><PERSON> likutį", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Laikyk wstETH (Staked ETH) Gnosis Chain tinkle ir skolink per Lido.", "earn.taker-bulletlist.point_1": "Uždirbk {apyValue} per metus. Grąža kinta priklausomai nuo rinkos.", "earn.taker-bulletlist.point_3": "Zeal net<PERSON>ko j<PERSON> m<PERSON>.", "earn.taker-historical-returns": "Istorinė g<PERSON>a", "earn.taker-historical-returns.chf": "CHF augimas USD atžvilgiu", "earn.taker-investment-tile.apy.perYear": "per metus", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "Įnešti", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Uždarbis iš Aave Gnosis Chain tinkle", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Uždarbis iš <PERSON>coin Gnosis tinkle", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Uždarbis iš Lido Gnosis Chain tinkle", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Uždarbis iš Maker Gnosis Chain tinkle", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "Eth earn", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH Earn", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "JAV iždo vekseliai (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Nors tai itin reta, te<PERSON>š<PERSON> įmanoma. <PERSON><PERSON> l<PERSON>ša<PERSON> saugo griežtas rizikos valdymas ir didelis įkaito padengimas. Realistiškiausias blogiausias scenarijus būtų beprecedentės rink<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jei kelios stabiliosios monetos vienu metu prarastų susiejimą su valiuta – tai dar niekada nebuvo nutikę.", "earn.usd.can-I-lose-my-principal-popup.title": "Ar realu prarasti pagrindinę sumą ir kokiomis aplinkybėmis?", "earn.usd.ftx-difference-popup.text": "Sky iš esmės skiriasi. Priešingai nei FTX, <PERSON><PERSON><PERSON>, BlockFi ar Luna, kurios r<PERSON>mėsi centralizuotu saugojimu, neskaidriu turto valdymu ir rizikingomis svertinėmis pozicijomis, Sky USD naudoja skaidrias, audituotas, decentralizuotas išmaniąsias sutartis ir užtikrina visišką on-chain skaidrumą. Tu išlaikai visišką privačios piniginės kontrolę, o tai gerokai sumažina sandorio šalies riziką, susijusią su centralizuotų sistemų žlugimu.", "earn.usd.ftx-difference-popup.title": "<PERSON>o tai skiriasi nuo FT<PERSON>, <PERSON><PERSON><PERSON>, BlockFi ar Luna?", "earn.usd.high-returns-popup.text": "Sky USD grąžą generuoja daugiausia per decentralizuotų finansų (DeFi) protokolus, kurie automatizuoja tarpusavio skolinimą ir likvidumo teikimą, pašalindami tradicinės bankininkystės pridėtines išlaidas ir tarpininkus. <PERSON><PERSON>, kartu su tvirtomis rizikos kontrolės priemonėmis, leidžia pasiekti gerokai didesnę grąžą, palyginti su tradiciniais bankais.", "earn.usd.high-returns-popup.title": "<PERSON><PERSON> grąža gali būti to<PERSON>a <PERSON>, ypa<PERSON> palyginti su tradiciniais bankais?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD yra visiškai padengtas ir viršijantis įkaitą, kurį sudaro skaitmeninis turtas, laikomas saugiose išmaniosiose sutartyse, ir realus turtas, pvz., JAV iždo vertybiniai popieriai. Rezervus galima audituoti realiu laiku on-chain net iš Zeal programėlės, kas užtikrina skaidrumą ir saugumą. Mažai tikėtinu atveju, jei <PERSON> nutrauktų veiklą, tavo turtas liktų saugus on-chain, visiškai tavo kontroliuojamas ir pasiekiamas per kitas suderinamas pinigines.", "earn.usd.how-is-sky-backed-popup.title": "<PERSON><PERSON> padengtas Sky USD ir kas nutiks mano pinigams, j<PERSON> bankru<PERSON>?", "earn.usd.insurance-popup.text": "Sky USD lėšos nėra apdraustos FDIC ar paremtos tradicinėmis vyriausybinėmis garantijomis, nes tai yra skaitmeniniu turtu pagrįsta sąskaita, o ne įprasta banko sąskaita. Vietoj to, Sky valdo rizikos mažinimą per audituotas išmaniąsias sutartis ir atidžiai patikrintus DeFi protokolus, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad turtas išliktų saugus ir skaidrus.", "earn.usd.insurance-popup.title": "Ar mano l<PERSON>š<PERSON> apdraustos ar garantuotos kokio nors subjekto (pvz., FDIC ar pan<PERSON>šaus)?", "earn.usd.lending-operations-popup.text": "Sky USD generuoja grąžą skolindama stabiliąsias monetas per decentralizuotas skolinimo rinkas, to<PERSON><PERSON> ka<PERSON> ir Spark. Tavo stabiliosios monetos skolinamos skolininkams, kurie įneša gerokai didesnį įkaitą, pvz., ETH ar BTC, nei j<PERSON> paskolos vertė. <PERSON><PERSON>, vadinamas perteklinio užstato užtikrinimu, garantuoja, kad visada yra pakankamai įkaito paskoloms padengti, o tai labai sumažina riziką. Surinktos palūkanos ir kartais skolininkų mokami likvidavimo mokesčiai užtikrina patikimą, skaidrią ir saugią grąžą.", "earn.usd.lending-operations-popup.title": "Skolinimo operacijos", "earn.usd.market-making-operations-popup.text": "Sky USD gauna papildomą gr<PERSON><PERSON><PERSON> daly<PERSON>udama decentralizuotose keit<PERSON>lose (AMM), to<PERSON><PERSON> ka<PERSON> ar Uniswap. Teikdama likvidumą – įnešdama tavo stabiliąsias monetas į fondus, kurie paleng<PERSON> pre<PERSON> k<PERSON> – Sky USD surenka m<PERSON>, gaut<PERSON>. Šie likvidumo fondai yra kruopščiai atrenkami siekiant sumažinti svyravimus, daugiausia naudojant stabiliųjų monetų poras, kad būtų gerokai sumažinta rizika, pavyzdžiui, laikinasis nuostolis, ir tavo turtas liktų saugus ir prieinamas.", "earn.usd.market-making-operations-popup.title": "<PERSON><PERSON><PERSON> formavi<PERSON> operaci<PERSON>", "earn.usd.treasury-operations-popup.text": "Sky USD generuoja stabilią, nuose<PERSON><PERSON><PERSON> grąž<PERSON> per strategines iždo investicijas. Dalis tavo stabiliųjų monetų indėlių skiriama saugiam, mažos rizikos realiam turtui – daugiausia trumpalaikėms vyriausybės obligacijoms ir labai saugiems kredito instrumentams. <PERSON><PERSON> metoda<PERSON>, panašus į tradicinę bankininkystę, užtikrina nuspėjamą ir patikimą grąžą. Tavo turtas išlieka saugus, likvidus ir skaidriai valdomas.", "earn.usd.treasury-operations-popup.title": "<PERSON><PERSON><PERSON>", "earn.view_earn.card_rechard_off": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.view_earn.card_rechard_on": "Įjungtas", "earn.view_earn.card_recharge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.view_earn.total_balance_label": "Uždirbama {percentage} per metus", "earn.view_earn.total_earnings_label": "Visas uždarbis", "earn.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.withdraw.amount_to_withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON> suma", "earn.withdraw.enter_amount": "Įvesk sumą", "earn.withdraw.loading": "<PERSON><PERSON><PERSON><PERSON>", "earn.withdraw.no_routes_found": "Maršrutų nerasta", "earn.withdraw.not_enough_balance": "<PERSON>epaka<PERSON><PERSON> likutis", "earn.withdraw.select-currency.title": "Pasirinkti žetoną", "earn.withdraw.select_to_token": "Pasirinkti žetoną", "earn.withdraw.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.withdraw_form.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earnings-view.earnings": "Visas uždarbis", "edit-account-owners.add-owner.add-wallet": "<PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.add_wallet": "<PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "edit-account-owners.card-owners": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.external-wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editBankRecipient.title": "Redaguoti gavėją", "editNetwork.addCustomRPC": "Pridėti pasirinktinį RPC mazgą", "editNetwork.cannot_verify.subtitle": "Pasirinktinis RPC mazgas tinkamai neatsako. Patikrink URL ir bandyk dar kartą.", "editNetwork.cannot_verify.title": "Nepavyksta patikrinti RPC mazgo", "editNetwork.cannot_verify.try_again": "Bandyti vėl", "editNetwork.customRPCNode": "Pasirinktinis RPC mazgas", "editNetwork.defaultRPC": "Numatytasis RPC", "editNetwork.networkRPC": "Tinklo RPC", "editNetwork.rpc_url.cannot_be_empty": "<PERSON><PERSON><PERSON>", "editNetwork.rpc_url.not_a_valid_https_url": "<PERSON><PERSON> <PERSON><PERSON><PERSON> HTTP(S) URL adresas", "editNetwork.safetyWarning.subtitle": "Zeal negali u<PERSON><PERSON><PERSON><PERSON><PERSON> privatumo, pat<PERSON><PERSON>o ir saugumo naudojant nestandartinius RPC. Ar tikrai nori naudoti nestandartinį RPC mazgą?", "editNetwork.safetyWarning.title": "Nestandartiniai RPC gali būti ne<PERSON>s", "editNetwork.zealRPCNode": "Zeal RPC mazgas", "editNetworkRpc.headerTitle": "Nestandartinis RPC mazgas", "editNetworkRpc.rpcNodeUrl": "RPC mazgo URL", "editing-locked.modal.description": "Skirtingai nuo pat<PERSON><PERSON>, leidimai neleidžia redaguoti išlaidų limito ar galiojimo laiko. Prieš pateik<PERSON> leidim<PERSON> įsitikink, kad pasitiki programėle (dApp).", "editing-locked.modal.title": "Redagavimas <PERSON>ž<PERSON>", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Įjungiamas papildymas", "enable-recharge-for-smart-wallet.recharge-enabled.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> įjungtas", "enterCardnumber": "Įvesk kortelės numerį", "error.connectivity_error.subtitle": "Patikrink interneto ryšį ir bandyk dar kartą.", "error.connectivity_error.title": "Nėra interneto ryšio", "error.decrypt_incorrect_password.title": "<PERSON>ei<PERSON><PERSON>", "error.encrypted_object_invalid_format.title": "Sugadinti duomenys", "error.failed_to_fetch_google_auth_token.title": "Negal<PERSON>jome gauti p<PERSON>s", "error.list.item.cta.action": "Bandyti dar kartą", "error.trezor_action_cancelled.title": "Pavedimas atmestas", "error.trezor_device_used_elsewhere.title": "Įrenginys naudojamas kitoje se<PERSON>je", "error.trezor_method_cancelled.title": "Nepavyko sinchroniz<PERSON>ti <PERSON>or", "error.trezor_permissions_not_granted.title": "Nepavyko sinchroniz<PERSON>ti <PERSON>or", "error.trezor_pin_cancelled.title": "Nepavyko sinchroniz<PERSON>ti <PERSON>or", "error.trezor_popup_closed.title": "Nepavyko sinchroniz<PERSON>ti <PERSON>or", "error.unblock_account_number_and_sort_code_mismatch": "Sąskaitos numeris ir banko kodas nesutampa", "error.unblock_can_not_change_details_after_kyc": "Duomenų negalima keisti po KYC", "error.unblock_hard_kyc_failure": "Netikėta KYC būsena", "error.unblock_invalid_faster_payment_configuration.title": "Šis bankas nepalaiko greitųjų mokėjimų", "error.unblock_invalid_iban": "Neteisingas IBAN", "error.unblock_session_expired.title": "Unblock sesija baig<PERSON>si", "error.unblock_user_with_address_already_exists.title": "Paskyra šiam adresui jau sukurta", "error.unblock_user_with_such_email_already_exists.title": "Vartotojas su tokiu el. paštu jau egzistuoja", "error.unknown_error.error_message": "<PERSON><PERSON><PERSON>: ", "error.unknown_error.subtitle": "Atsiprašome! Jei reikia skubios pagalbos, susisiek su pagalba ir pasidalyk žemiau esančia informacija.", "error.unknown_error.title": "<PERSON><PERSON><PERSON>", "eth-cost-warning-modal.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> veikia Ethereum tinkle, ta<PERSON><PERSON><PERSON> moke<PERSON> yra labai did<PERSON>, to<PERSON><PERSON><PERSON> primygtinai rekomenduojame naudoti kitus tinklus.", "eth-cost-warning-modal.title": "Venk Ethereum – tinklo m<PERSON>č<PERSON> dideli", "exchange.form.button.chain_unsupported": "<PERSON><PERSON><PERSON>", "exchange.form.button.refreshing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exchange.form.error.asset_not_supported.button": "Pasirink kitą turtą", "exchange.form.error.asset_not_supported.description": "Bridge nepalaiko <PERSON>io turto.", "exchange.form.error.asset_not_supported.title": "<PERSON><PERSON><PERSON>", "exchange.form.error.bridge_quote_timeout.button": "Pasirink kitą turtą", "exchange.form.error.bridge_quote_timeout.description": "Išbandyk kitą tokenų porą", "exchange.form.error.bridge_quote_timeout.title": "Keitykla nerasta", "exchange.form.error.different_receiver_not_supported.button": "Pašalinti kitą gavėją", "exchange.form.error.different_receiver_not_supported.description": "Ši keityk<PERSON> nepalaiko siuntimo kitu adresu.", "exchange.form.error.different_receiver_not_supported.title": "Siuntėjo ir gavėjo adresai turi sutapti", "exchange.form.error.insufficient_input_amount.button": "Padidink sumą", "exchange.form.error.insufficient_liquidity.button": "Sumažink sumą", "exchange.form.error.insufficient_liquidity.description": "Trūksta turto. Bandyk mažesnę sumą.", "exchange.form.error.insufficient_liquidity.title": "<PERSON> suma", "exchange.form.error.max_amount_exceeded.button": "Sumažink sumą", "exchange.form.error.max_amount_exceeded.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> maksimali suma.", "exchange.form.error.max_amount_exceeded.title": "<PERSON> suma", "exchange.form.error.min_amount_not_met.button": "Padidink sumą", "exchange.form.error.min_amount_not_met.description": "Nepasiekta minimali šio žetono keitimo suma.", "exchange.form.error.min_amount_not_met.description_with_amount": "Minimali keitimo suma yra {amount}.", "exchange.form.error.min_amount_not_met.title": "Per maža suma", "exchange.form.error.min_amount_not_met.title_increase": "Padidink sumą", "exchange.form.error.no_routes_found.button": "Pasirink kitą turtą", "exchange.form.error.no_routes_found.description": "<PERSON><PERSON> der<PERSON> ma<PERSON> nėra.", "exchange.form.error.no_routes_found.title": "Keitykla nepasiekiama", "exchange.form.error.not_enough_balance.button": "Sumažink sumą", "exchange.form.error.not_enough_balance.description": "Transakcijai nepakanka šio turto.", "exchange.form.error.not_enough_balance.title": "<PERSON>epaka<PERSON><PERSON> likutis", "exchange.form.error.slippage_passed_is_too_low.button": "<PERSON><PERSON><PERSON><PERSON>", "exchange.form.error.slippage_passed_is_too_low.description": "<PERSON><PERSON> turtui slydi<PERSON> per maž<PERSON>.", "exchange.form.error.slippage_passed_is_too_low.title": "<PERSON><PERSON>", "exchange.form.error.socket_internal_error.button": "Bandyk vėliau", "exchange.form.error.socket_internal_error.description": "Bridge partneris susiduria su problemomis. Bandyk vėliau.", "exchange.form.error.socket_internal_error.title": "Bridge partner<PERSON>", "exchange.form.error.stargatev2_requires_fee_in_native": "<PERSON><PERSON><PERSON><PERSON> {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "<PERSON><PERSON><PERSON><PERSON> {amount} , kad užbaigtum operaciją", "exchange.form.error.stargatev2_requires_fee_in_native.title": "<PERSON><PERSON><PERSON> {symbol}", "expiration-info.modal.description": "Galiojimo la<PERSON>, kiek ilgai programėlė gali naudoti tavo žetonus. Pasibaigus šiam laik<PERSON>, prieiga panaikinama, kol vėl ją suteiksi. Saugumo <PERSON>, nustatyk trumpą galiojimo laik<PERSON>.", "expiration-info.modal.title": "Kas yra galiojimo laikas?", "expiration-time.high.modal.text": "Galiojimo laikas tur<PERSON>tų būti kuo trumpesnis. Ilgas galiojimas yra rizikingas ir suteikia sukčiams daugiau galimybių pasinaudoti tavo žetonais.", "expiration-time.high.modal.title": "Ilgas galiojimo laikas", "failed.transaction.content": "Operacija greičiausiai nepavyks", "fee.unknown": "Nežinoma", "feedback-request.leave-message": "<PERSON><PERSON><PERSON> atsiliepi<PERSON>", "feedback-request.not-now": "<PERSON><PERSON> <PERSON><PERSON>", "feedback-request.title": "Ačiū! <PERSON><PERSON> page<PERSON>?", "float.input.period": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gnosis-activate-card.info-popup.subtitle": "Pirmam mokėjimui įdėk kortelę ir suvesk PIN", "gnosis-activate-card.info-popup.title": "Pirmam mokėjimui reikia lustinės ir PIN", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "Įvesk kortelės numerį, kad j<PERSON> a<PERSON>.", "gnosis-activate-card.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> numeris", "gnosis-pay-re-kyc-widget.btn-text": "<PERSON><PERSON><PERSON><PERSON>", "gnosis-pay-re-kyc-widget.title.not-started": "Patvirtink savo tapatybę", "gnosis-pay.login.cta": "Prijungti esamą p<PERSON>ą", "gnosis-pay.login.title": "Jau turi Gnosis Pay paskyrą", "gnosis-signup.confirm.subtitle": "Ieškok Gnosis Pay <PERSON>, jis gali sl<PERSON>ptis <PERSON> a<PERSON>.", "gnosis-signup.confirm.title": "Negavai pat<PERSON><PERSON>?", "gnosis-signup.continue": "<PERSON><PERSON><PERSON><PERSON>", "gnosis-signup.dont_link_accounts": "Nesieti paskyrų", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "Įvesk <EMAIL>", "gnosis-signup.enter-email.title": "Įvesk el. paštą", "gnosis-signup.title": "Perskaičiau ir sutinku su Gnosis Pay <linkGnosisTNC>Taisyklėmis ir sąlygomis</linkGnosisTNC> <monovateTerms>Ko<PERSON><PERSON><PERSON><PERSON> turė<PERSON> sąlygomis</monovateTerms> bei <linkMonerium>Monerium Taisyklėmis ir sąlygomis</linkMonerium>.", "gnosis-signup.verify-email.title": "Patvirtink el. paštą", "gnosis.confirm.subtitle": "Negavai kodo? <PERSON><PERSON><PERSON><PERSON>, ar teisingas telefono numeris", "gnosis.confirm.title": "<PERSON><PERSON> išsiųstas į {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "<PERSON><PERSON><PERSON><PERSON>", "gnosisPayAccountStatus.success.title": "Kortel<PERSON> importuota", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay tavo šalyje dar nep<PERSON>inama", "gnosisPayNoActiveCardsFound.title": "Nėra aktyvių kortelių", "gnosis_pay_card_delay_relay_not_empty_error.title": "Tavo pavedimas šiuo metu negali būti a<PERSON>dor<PERSON>. Bandyk vėliau", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "Kortelė negalima", "gnosiskyc.modal.approved.activate-free-card": "Įjungti nemokamą kortelę", "gnosiskyc.modal.approved.button-text": "Papildyti iš banko", "gnosiskyc.modal.approved.title": "<PERSON><PERSON><PERSON>s sąskaitos duomenys sukurti", "gnosiskyc.modal.failed.close": "Uždaryti", "gnosiskyc.modal.failed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mūsų partneris Gnosis Pay negali tau sukurti paskyros", "gnosiskyc.modal.in-progress.title": "Asmens <PERSON>ės patvirtinimas gali užtrukti 24 valandas ar ilgiau. Prašome kantrybės", "goToSettingsPopup.settings": "Nustatymai", "goToSettingsPopup.title": "Pranešimus gali bet kada įjungti įrenginio nustatymuose.", "google_file.error.failed_to_fetch_auth_token.button_title": "Bandyti dar kartą", "google_file.error.failed_to_fetch_auth_token.subtitle": "Kad <PERSON>e naudoti tavo atkūrimo failą, suteik prieigą savo asmeniniame debes<PERSON>je.", "google_file.error.failed_to_fetch_auth_token.title": "Negal<PERSON>jome gauti p<PERSON>s", "hidden_tokens.widget.emptyState": "Nėra paslėptų žetonų", "how_to_connect_to_metamask.got_it": "Gerai, supratau", "how_to_connect_to_metamask.story.subtitle": "Lengvai perjunk tarp Zeal ir kitų piniginių bet kuriuo metu.", "how_to_connect_to_metamask.story.title": "Zeal veikia kartu su kitomis pinigi<PERSON>", "how_to_connect_to_metamask.why_switch": "Kodėl verta perjungti tarp Zeal ir kitų piniginių?", "how_to_connect_to_metamask.why_switch.description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> saugumo patik<PERSON> visada apsaugos tave nuo kenkėjiškų svetainių ir operacijų.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "<PERSON><PERSON><PERSON>, kad sunku ryžtis ir pradėti naudoti naują piniginę. Todėl supaprastinome Zeal naudojimą kartu su tavo esama pinigine. Perjunk bet kada.", "import-bank-transfer-owner.banner.title": "Prie pavedimų prijungta piniginė pasikeitė. Importuok ją da<PERSON>.", "import-bank-transfer-owner.title": "Importuok piniginę banko pavedimams", "import_gnosispay_wallet.add-another-card-owner.footnote": "Importuok privačius raktus arba pradinę frazę, kuriai priklauso tavo Gnosis Pay kortelė", "import_gnosispay_wallet.primaryText": "Importuoti Gnosis Pay piniginę", "injected-wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "intercom.getHelp": "<PERSON><PERSON><PERSON>", "invalid_iban.got_it": "<PERSON><PERSON><PERSON><PERSON>", "invalid_iban.subtitle": "Įvestas IBAN netinkamas. Bandyk vėl.", "invalid_iban.title": "Netinkamas IBAN", "keypad-0": "Klaviatūros klavišas 0", "keypad-1": "Klaviatūros klavišas 1", "keypad-2": "Klaviatūros <PERSON>ša<PERSON> 2", "keypad-3": "Klaviatūros klavišas 3", "keypad-4": "Klaviatū<PERSON> 4", "keypad-5": "Klaviat<PERSON><PERSON> 5", "keypad-6": "Klav<PERSON><PERSON><PERSON><PERSON> 6", "keypad-7": "Klaviat<PERSON><PERSON> 7", "keypad-8": "Klavia<PERSON><PERSON><PERSON> 8", "keypad-9": "Klavia<PERSON><PERSON><PERSON> 9", "keypad.biometric-button": "Klaviat<PERSON><PERSON> biometrinis my<PERSON>", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Slap<PERSON>žodžio frazė apsaugota 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Saugok slaptažodžio frazės kopiją neprisijungus prie interneto, kad vėliau galėtum atkurti savo turtą", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "Nebandyk atspėti žodžio", "keystore.write_secret_phrase.before_you_begin.first_point": "<PERSON><PERSON><PERSON><PERSON>, kad bet kas, turi<PERSON><PERSON> mano slap<PERSON><PERSON><PERSON>, gali pervesti mano turtą", "keystore.write_secret_phrase.before_you_begin.second_point": "<PERSON>su atsakingas (-a) už savo slaptažodžio frazės slap<PERSON>ą ir saugumą", "keystore.write_secret_phrase.before_you_begin.subtitle": "Perskaityk ir sutik su šiais teiginiais:", "keystore.write_secret_phrase.before_you_begin.third_point": "Esu privačioje vietoje, kur aplink nėra žmonių ar kamerų", "keystore.write_secret_phrase.before_you_begin.title": "<PERSON><PERSON><PERSON> p<PERSON>", "keystore.write_secret_phrase.secret_phrase_test.title": "<PERSON>ks yra {count} <PERSON><PERSON><PERSON> tavo slaptažodžio frazėje?", "keystore.write_secret_phrase.test_ps.lets_do_it": "<PERSON><PERSON><PERSON>", "keystore.write_secret_phrase.test_ps.subtitle": "Tau reikės <PERSON>, kad atkurtum paskyrą šiame ar kituose įrenginiuose. <PERSON><PERSON><PERSON><PERSON><PERSON>, ar te<PERSON> j<PERSON> u<PERSON>.", "keystore.write_secret_phrase.test_ps.subtitle2": "Paprašysime tavęs {count} <PERSON><PERSON><PERSON><PERSON><PERSON> iš tavo frazės.", "keystore.write_secret_phrase.test_ps.title": "Pa<PERSON><PERSON> atkū<PERSON> testas", "kyc.modal.approved.button-text": "Atlikti banko pavedimą", "kyc.modal.approved.subtitle": "Patvirtinta. Banko <PERSON>ribo<PERSON>mi.", "kyc.modal.approved.title": "Banko pavedimai atrakinti", "kyc.modal.continue-with-partner.button-text": "<PERSON><PERSON><PERSON><PERSON>", "kyc.modal.continue-with-partner.subtitle": "<PERSON>bar nukreipsime tave pas mūsų partnerį, kad pateiktum dokumentus ir užbaigtum patvirtinimo <PERSON>.", "kyc.modal.continue-with-partner.title": "Tęsk su mūsų partneriu", "kyc.modal.failed.unblock.subtitle": "Unblock nepatvirtino tavo tapatybės, todėl banko pavedimų paslaugos neteikiamos", "kyc.modal.failed.unblock.title": "Unblock paraiška nepatvirtinta", "kyc.modal.paused.button-text": "<PERSON><PERSON><PERSON><PERSON><PERSON> duomenis", "kyc.modal.paused.subtitle": "<PERSON><PERSON><PERSON><PERSON>, kad dalis tavo informacijos yra neteisinga. Bandyk dar kartą ir atidžiai patikrink duomenis prieš pat<PERSON>.", "kyc.modal.paused.title": "Tavo duomenys atrodo neteisingi", "kyc.modal.pending.button-text": "Uždaryti", "kyc.modal.pending.subtitle": "Patvirtinimas dažniausiai trunka iki 10 min., bet kartais gali užtrukti ilgiau.", "kyc.modal.pending.title": "Informuosime a<PERSON> eig<PERSON>", "kyc.modal.required.cta": "<PERSON><PERSON><PERSON><PERSON>", "kyc.modal.required.subtitle": "Pasiekei transakcijų limitą. <PERSON><PERSON><PERSON><PERSON>, pat<PERSON><PERSON><PERSON> tapatybę. Tai užtruks kelias minutes ir reikės asmens duomenų bei dokumentų.", "kyc.modal.required.title": "Reikalingas <PERSON>yb<PERSON>", "kyc.submitted": "Paraiška pateikta", "kyc.submitted_short": "<PERSON><PERSON><PERSON><PERSON>", "kyc_status.completed_status": "Atlikta", "kyc_status.failed_status": "Nepavyko", "kyc_status.paused_status": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc_status.subtitle": "Bank<PERSON>", "kyc_status.subtitle.wrong_details": "Neteisingi duomenys", "kyc_status.subtitle_in_progress": "Vykdoma", "kyc_status.title": "Tvirtinama tap<PERSON>ė", "label.close": "Uždaryti", "label.saving": "Saugoma...", "labels.this-month": "Šį mėnesį", "labels.today": "Šiandien", "labels.yesterday": "<PERSON><PERSON><PERSON>", "language.selector.title": "Kalba", "ledger.account_loaded.imported": "I<PERSON>rt<PERSON><PERSON>", "ledger.add.success.title": "Ledger sė<PERSON><PERSON> prijungtas 🎉", "ledger.connect.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.connect.step1": "Prijunk Ledger prie savo įrenginio", "ledger.connect.step2": "Ledger įrenginyje atidaryk Ethereum programėlę", "ledger.connect.step3": "<PERSON><PERSON> sinchroniz<PERSON>k savo Ledger 👇", "ledger.connect.subtitle": "<PERSON><PERSON>, kad importuotum savo Ledger pinigines į Zeal", "ledger.connect.title": "Prijungti Ledger prie Zeal", "ledger.error.ledger_is_locked.subtitle": "Atrakink Ledger ir atidaryk Ethereum programėlę", "ledger.error.ledger_is_locked.title": "Led<PERSON>", "ledger.error.ledger_not_connected.action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.error.ledger_not_connected.subtitle": "Prijunk aparatinę piniginę prie įrenginio ir atidaryk Ethereum programėlę", "ledger.error.ledger_not_connected.title": "Ledger <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.error.ledger_running_non_eth_app.title": "Ethereum programėlė <PERSON>", "ledger.error.user_trx_denied_by_user.action": "Uždaryti", "ledger.error.user_trx_denied_by_user.subtitle": "Atmetei pavedimą savo aparatinėje piniginėje", "ledger.error.user_trx_denied_by_user.title": "Pavedimas atmestas", "ledger.hd_path.bip44.subtitle": "pvz., Metamask, Trezor", "ledger.hd_path.bip44.title": "BIP44 standartas", "ledger.hd_path.ledger_live.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Senas formatas", "ledger.hd_path.phantom.subtitle": "pvz., Phantom", "ledger.select.hd_path.subtitle": "HD maršrutai – ta<PERSON> b<PERSON>, kuri<PERSON> aparatin<PERSON> rūš<PERSON> savo paskyras. Tai panašu į tai, ka<PERSON> <PERSON><PERSON> rūš<PERSON> knygos puslapi<PERSON>.", "ledger.select.hd_path.title": "Pasirink HD maršrutą", "ledger.select_account.import_wallets_count": "{count,plural,=0{Nepasirinkta piniginių} one{Importuoti piniginę} other{Importuoti {count} pinigines}}", "ledger.select_account.path_settings": "<PERSON><PERSON><PERSON><PERSON>", "ledger.select_account.subtitle": "Nematai laukiamų piniginių? Pabandyk pakeisti maršruto nustatymus", "ledger.select_account.subtitle.group_header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.select_account.title": "Importuoti Ledger pinigines", "legend.lending-operations": "Skolinimo operacijos", "legend.market_making-operations": "<PERSON><PERSON><PERSON> formavi<PERSON> operaci<PERSON>", "legend.treasury-operations": "<PERSON><PERSON><PERSON>", "link-existing-monerium-account-sign.button": "<PERSON><PERSON>", "link-existing-monerium-account-sign.subtitle": "Tu jau turi Monerium paskyrą.", "link-existing-monerium-account-sign.title": "Susiek Zeal su savo esama Monerium paskyra", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Tu jau turi Monerium paskyrą. Apsilankyk Monerium programėlėje, kad užbaigtum sąranką.", "link-existing-monerium-account.title": "Eik į Monerium ir susiek savo paskyrą", "loading.pin": "Kraunamas PIN...", "lockScreen.passwordIncorrectMessage": "<PERSON>ei<PERSON><PERSON>", "lockScreen.passwordRequiredMessage": "<PERSON><PERSON><PERSON><PERSON>", "lockScreen.unlock.header": "Atrakinti", "lockScreen.unlock.subheader": "Atrakink Zeal savo slaptažodžiu", "mainTabs.activity.label": "Veikla", "mainTabs.browse.label": "Naršyti", "mainTabs.browse.title": "Naršyti", "mainTabs.card.label": "Kortelė", "mainTabs.portfolio.label": "Port<PERSON>lis", "mainTabs.rewards.label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeSpendable.cta": "Aktyvuoti", "makeSpendable.holdAsCash": "<PERSON><PERSON><PERSON> ka<PERSON>", "makeSpendable.shortText": "<PERSON><PERSON><PERSON>bi {apy} per metus", "makeSpendable.title": "{amount} gauta", "merchantCategory.agriculture": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.alcohol": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.antiques": "Antikvariatas", "merchantCategory.appliances": "Buitinė technika", "merchantCategory.artGalleries": "<PERSON><PERSON>", "merchantCategory.autoRepair": "Automobilių remontas", "merchantCategory.autoRepairService": "Automobilių remonto paslaugos", "merchantCategory.beautyFitnessSpas": "<PERSON><PERSON><PERSON><PERSON>, sportas ir SPA", "merchantCategory.beautyPersonalCare": "<PERSON><PERSON><PERSON><PERSON> ir asmen<PERSON> p<PERSON>", "merchantCategory.billiard": "B<PERSON><PERSON><PERSON>", "merchantCategory.books": "Knygos", "merchantCategory.bowling": "Boulingas", "merchantCategory.businessProfessionalServices": "<PERSON><PERSON><PERSON> <PERSON>r profesin<PERSON> p<PERSON>", "merchantCategory.carRental": "Automobilių nuoma", "merchantCategory.carWash": "Automobilių plovykla", "merchantCategory.cars": "Automobiliai", "merchantCategory.casino": "<PERSON><PERSON><PERSON>", "merchantCategory.casinoGambling": "<PERSON><PERSON><PERSON> ir azartin<PERSON>i loš<PERSON>i", "merchantCategory.cellular": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.charity": "<PERSON><PERSON><PERSON>", "merchantCategory.childcare": "Vaikų priežiūra", "merchantCategory.cigarette": "Cigaretės", "merchantCategory.cinema": "<PERSON><PERSON>", "merchantCategory.cinemaEvents": "<PERSON><PERSON> ir re<PERSON>i", "merchantCategory.cleaning": "<PERSON><PERSON><PERSON>", "merchantCategory.cleaningMaintenance": "<PERSON><PERSON><PERSON> ir priež<PERSON>ū<PERSON>", "merchantCategory.clothes": "Drab<PERSON>ž<PERSON><PERSON>", "merchantCategory.clothingServices": "Drabužių paslaugos", "merchantCategory.communicationServices": "Ryšių paslaugos", "merchantCategory.construction": "Statybos", "merchantCategory.cosmetics": "Kosmetika", "merchantCategory.craftsArtSupplies": "Rankdarbiai ir meno reik<PERSON>ys", "merchantCategory.datingServices": "Pažinčių paslaugos", "merchantCategory.delivery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.dentist": "Odontologas", "merchantCategory.departmentStores": "Universalios parduotu<PERSON>s", "merchantCategory.directMarketingSubscription": "Tiesioginė rinkodara ir prenumerata", "merchantCategory.discountStores": "Nuolaid<PERSON> parduotuvės", "merchantCategory.drugs": "Vaistai", "merchantCategory.dutyFree": "Neapmuitinamos <PERSON>", "merchantCategory.education": "Švietimas", "merchantCategory.electricity": "Elektra", "merchantCategory.electronics": "Elektronika", "merchantCategory.emergencyServices": "S<PERSON>bios pagalbos tarnybos", "merchantCategory.equipmentRental": "Įrangos nuoma", "merchantCategory.evCharging": "Elektromobilių įkrovimas", "merchantCategory.financialInstitutions": "Finansų įstaigos", "merchantCategory.financialProfessionalServices": "Finansinės ir profesin<PERSON> p<PERSON>", "merchantCategory.finesPenalties": "<PERSON><PERSON><PERSON> ir nuobaudos", "merchantCategory.fitness": "Sportas", "merchantCategory.flights": "Skrydžiai", "merchantCategory.flowers": "Gėlės", "merchantCategory.flowersGarden": "Gėlės ir sodas", "merchantCategory.food": "<PERSON><PERSON><PERSON>", "merchantCategory.freight": "Krovinių gabenimas", "merchantCategory.fuel": "Degalai", "merchantCategory.funeralServices": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.furniture": "Baldai", "merchantCategory.games": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.gas": "Degalai", "merchantCategory.generalMerchandiseRetail": "<PERSON><PERSON><PERSON> ir ma<PERSON><PERSON>", "merchantCategory.gifts": "<PERSON><PERSON><PERSON>", "merchantCategory.government": "Valdžios institucijos", "merchantCategory.governmentServices": "Valstyb<PERSON><PERSON> p<PERSON>", "merchantCategory.hardware": "Techninė įranga", "merchantCategory.healthMedicine": "Sveikata ir medicina", "merchantCategory.homeImprovement": "Namų gerinimas", "merchantCategory.homeServices": "Nam<PERSON> paslau<PERSON>", "merchantCategory.hotel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.housing": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.insurance": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.internet": "Internetas", "merchantCategory.kids": "Vaikams", "merchantCategory.laundry": "Skalbykla", "merchantCategory.laundryCleaningServices": "Skalbimo ir valymo p<PERSON>", "merchantCategory.legalGovernmentFees": "Teisiniai ir v<PERSON><PERSON> m<PERSON>", "merchantCategory.luxuries": "Prabangos pre<PERSON>ės", "merchantCategory.luxuriesCollectibles": "Prabangos pre<PERSON> ir k<PERSON>", "merchantCategory.magazines": "Žurnalai", "merchantCategory.magazinesNews": "Žurnalai ir naujienos", "merchantCategory.marketplaces": "Prekyvietės", "merchantCategory.media": "Medija", "merchantCategory.medicine": "Medicina", "merchantCategory.mobileHomes": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.moneyTransferCrypto": "Pinigų pavedimai ir kripto", "merchantCategory.musicRelated": "Susiję su muzika", "merchantCategory.musicalInstruments": "<PERSON><PERSON><PERSON>", "merchantCategory.optics": "Optika", "merchantCategory.organizationsClubs": "Organizacijos ir klubai", "merchantCategory.other": "<PERSON><PERSON>", "merchantCategory.parking": "<PERSON><PERSON><PERSON>", "merchantCategory.pawnShops": "Lombardai", "merchantCategory.pets": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.photoServicesSupplies": "Fotografijos paslaugos ir reikmenys", "merchantCategory.postalServices": "<PERSON><PERSON><PERSON>", "merchantCategory.professionalServicesOther": "<PERSON><PERSON><PERSON><PERSON><PERSON> (kita)", "merchantCategory.publicTransport": "Viešasis transportas", "merchantCategory.purchases": "Pirkiniai", "merchantCategory.purchasesMiscServices": "Pirkiniai ir įvairios paslaugos", "merchantCategory.recreationServices": "<PERSON><PERSON><PERSON> p<PERSON>", "merchantCategory.religiousGoods": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.secondhandRetail": "Dėvėtų prekių mažmeninė prekyba", "merchantCategory.shoeHatRepair": "Batų ir kepurių ta<PERSON>ymas", "merchantCategory.shoeRepair": "Batų taisymas", "merchantCategory.softwareApps": "Programinė įranga ir programėl<PERSON>s", "merchantCategory.specializedRepairs": "Specializuotas remontas", "merchantCategory.sport": "Sportas", "merchantCategory.sportingGoods": "Sporto prekės", "merchantCategory.sportingGoodsRecreation": "Sporto prekė<PERSON> ir p<PERSON>", "merchantCategory.sportsClubsFields": "Sporto klubai ir aikštynai", "merchantCategory.stationaryPrinting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir <PERSON>", "merchantCategory.stationery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.storage": "Sand<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.taxes": "Mokesčiai", "merchantCategory.taxi": "<PERSON><PERSON><PERSON>", "merchantCategory.telecomEquipment": "Telekomunikacijų įranga", "merchantCategory.telephony": "Telefonija", "merchantCategory.tobacco": "Tabakas", "merchantCategory.tollRoad": "<PERSON><PERSON><PERSON> k<PERSON>", "merchantCategory.tourismAttractionsAmusement": "<PERSON><PERSON><PERSON>, lankytinos vietos ir pramogos", "merchantCategory.towing": "Vilkimas", "merchantCategory.toys": "<PERSON><PERSON><PERSON>", "merchantCategory.toysHobbies": "<PERSON><PERSON><PERSON> ir pom<PERSON>", "merchantCategory.trafficFine": "<PERSON><PERSON><PERSON> bauda", "merchantCategory.train": "Traukinys", "merchantCategory.travelAgency": "Kelionių agentūra", "merchantCategory.tv": "Televizija", "merchantCategory.tvRadioStreaming": "TV, radijas ir transliacijos", "merchantCategory.utilities": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.waterTransport": "Vandens transportas", "merchantCategory.wholesaleClubs": "Didmeninės prekybos klubai", "metaMask.subtitle": "Įjunk MetaM<PERSON> re<PERSON>, kad visos MetaMask jungtys būt<PERSON> nukreiptos į Zeal. Pa<PERSON>audus MetaM<PERSON>, prisijungsi prie Zeal.", "metaMask.title": "Nepavyksta prisijungti su Zeal?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Atidaryk savo banko programėlę", "monerium-bank-deposit.buttet-point.receive-crypto": "Gauk skaitmeninius EUR", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Siųsk {fiatCurrencyCode} į savo sąskaitą", "monerium-bank-deposit.deposit-account-country": "<PERSON><PERSON>", "monerium-bank-deposit.header": "{fullName} as<PERSON><PERSON><PERSON> s<PERSON>", "monerium-bank-details.account-name": "Sąskaitos pavadinimas", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC / SWIFT nukopijuotas", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT kodas nukopijuotas", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN nukopijuotas", "monerium-bank-details.to-wallet": "Į piniginę", "monerium-bank-details.transfer-fee": "<PERSON><PERSON><PERSON>", "monerium-bank-transfer.enable-card.bullet-1": "Užbaik tapatybės <PERSON>", "monerium-bank-transfer.enable-card.bullet-2": "Gauk <PERSON><PERSON> s<PERSON>ska<PERSON> duomenis", "monerium-bank-transfer.enable-card.bullet-3": "Įnešk l<PERSON><PERSON><PERSON> iš banko sąskaitos", "monerium-card-delay-relay.success.cta": "Uždaryti", "monerium-card-delay-relay.success.subtitle": "<PERSON><PERSON><PERSON><PERSON>, kortelės nustatymų pakeitimai apdorojami per 3 minutes.", "monerium-card-delay-relay.success.title": "Grįžk po 3 min. ir tęsk Monerium sąranką", "monerium-deposit.account-details-info-popup.bullet-point-1": "Bet kokie {fiatCurrencyCode} , kuri<PERSON>s siųsi į šią sąskaitą, bus automatiškai konvertuoti į {cryptoCurrencyCode} žetonus {cryptoCurrencyChain} tinkle ir išsiųsti į tavo piniginę", "monerium-deposit.account-details-info-popup.bullet-point-2": "SIŲSK TIK {fiatCurrencyCode} ({fiatCurrencySymbol}) į savo sąskaitą", "monerium-deposit.account-details-info-popup.title": "Tavo sąskaitos duomenys", "monerium.check_order_status.sending": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monerium.not-eligible.cta": "Atgal", "monerium.not-eligible.subtitle": "Monerium negali tau atidaryti sąskaitos. Pasirink kitą te<PERSON>ją.", "monerium.not-eligible.title": "Išbandyk kitą teikėją", "monerium.setup-card.cancel": "<PERSON><PERSON><PERSON><PERSON>", "monerium.setup-card.continue": "<PERSON><PERSON><PERSON><PERSON>", "monerium.setup-card.create_account": "Sukurti paskyrą", "monerium.setup-card.login": "Prisijunk prie Gnosis Pay", "monerium.setup-card.subtitle": "Sukurk arba prisijunk prie Gnosis Pay paskyros, kad įgalintum momentinius banko indėlius.", "monerium.setup-card.subtitle_personal_account": "Gauk asmeninę Gnosis Pay sąskaitą per kelias minutes:", "monerium.setup-card.title": "Įgalink banko indėlius", "moneriumDepositSuccess.goToWallet": "Eiti į piniginę", "moneriumDepositSuccess.title": "{symbol} gauta", "moneriumInfo.fees": "Tau taikomi 0 % mokesčiai", "moneriumInfo.registration": "Monerium yra įgaliota ir reguliuojama kaip elektroninių pinigų įstaiga pagal Islandijos elektroninių pinigų įstatymą Nr. 17/2013 <link>Sužinok daugiau</link>", "moneriumInfo.selfCustody": "Gauti skaitmeniniai pinigai yra tavo privačioje piniginėje ir niekas kitas negalės kontroliuoti tavo turto", "moneriumWithdrawRejected.supportText": "Negalėjome atlikti tavo pavedimo. Bandyk dar kartą. Jei vis tiek nepavy<PERSON>, <link>susisiek su pagalba.</link>", "moneriumWithdrawRejected.title": "Pavedimas atšauktas", "moneriumWithdrawRejected.tryAgain": "Bandyti dar kartą", "moneriumWithdrawSuccess.supportText": "G<PERSON> užtrukti 24 val., kol tavo{br}gavėjas gaus lėšas", "moneriumWithdrawSuccess.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monerium_enable_banner.text": "Aktyvuok banko pavedimus dabar", "monerium_error_address_re_link_required.title": "Piniginę reikia iš naujo susieti su Monerium", "monerium_error_duplicate_order.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "Šveicarijos frankas", "mt-pelerin-fork.options.chf.short": "Akimirksniu ir nemokamai su <PERSON> Pelerin", "mt-pelerin-fork.options.euro.primary": "<PERSON><PERSON><PERSON>", "mt-pelerin-fork.options.euro.short": "Akimirksniu ir nemokamai su Monerium", "mt-pelerin-fork.title": "Ką nori įnešti?", "mtPelerinProviderInfo.fees": "Moki 0 % mokesčių", "mtPelerinProviderInfo.registration": "„Mt Pelerin Group Ltd“ yra susijusi su SO-FIT, savireguliacijos institucija, pripažinta Šveicarijos finansų priežiūros institucijos (FINMA) pagal Kovos su pinigų plovimu įstatymą. <link>Sužinok daugiau</link>", "mtPelerinProviderInfo.selfCustody": "Gauti skaitmeniniai pinigai yra tavo privačioje piniginėje ir niekas kitas negal<PERSON>s valdyti tavo turto", "network-fee-widget.title": "Mokesčiai", "network.edit.verifying_rpc": "<PERSON><PERSON><PERSON><PERSON> RPC", "network.editRpc.predefined_network_info.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> na<PERSON><PERSON>, k<PERSON><PERSON> a<PERSON>go tavo asmeninius duomenis nuo sekimo.{br}{br}Zeal numaty<PERSON>ji RPC yra pat<PERSON>, laiko pat<PERSON>.", "network.editRpc.predefined_network_info.title": "Zeal privatumo RPC", "network.filter.update_rpc_success": "RPC mazgas išsaugotas", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "V<PERSON> tinklai", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "<PERSON><PERSON><PERSON><PERSON>", "networks.filter.add_modal.chain_list.subtitle": "Pridėk bet kokius EVM tinklus", "networks.filter.add_modal.chain_list.title": "Eiti į Chainlist", "networks.filter.add_modal.dapp_tip.subtitle": "Savo mėgstamose dApps tiesiog perjunk į norimą EVM tinklą ir <PERSON><PERSON> p<PERSON>, ar nori jį pridėti į savo piniginę.", "networks.filter.add_modal.dapp_tip.title": "Arba pridėk tinklą iš bet kurios dApp", "networks.filter.add_networks.subtitle": "Palaikomi visi EVM tinklai", "networks.filter.add_networks.title": "<PERSON><PERSON><PERSON><PERSON>", "networks.filter.add_test_networks.title": "<PERSON><PERSON><PERSON><PERSON> test<PERSON>", "networks.filter.tab.netwokrs": "<PERSON><PERSON><PERSON>", "networks.filter.testnets.title": "<PERSON><PERSON><PERSON>", "nft.widget.emptystate": "Piniginėje nėra kolekcijų", "nft_collection.change_account_picture.subtitle": "Ar tikrai nori atnaujinti profilio n<PERSON>?", "nft_collection.change_account_picture.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> profilio n<PERSON> į NFT", "nfts.allNfts.pricingPopup.description": "Kolekcionuojamų daiktų kainos pagrįstos naujausia prekybos kaina.", "nfts.allNfts.pricingPopup.title": "Kolekcionuojamų daiktų kainodara", "no-passkeys-found.modal.cta": "Uždaryti", "no-passkeys-found.modal.subtitle": "Šiame įrenginyje negalime aptikti jokių Zeal prieigos raktų. Įsitikink, kad esi prisijungęs prie debesies paskyros, k<PERSON><PERSON> naudojai kurdamas savo „Smart Wallet“.", "no-passkeys-found.modal.title": "Prieigos raktų nerasta", "notValidEmail.title": "Netinkamas el. pašto adresas", "notValidPhone.title": "Tai nėra galiojantis telefono numeris", "notification-settings.title": "Pranešimų nustatymai", "notification-settings.toggles.active-wallets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notification-settings.toggles.bank-transfers": "Bank<PERSON>", "notification-settings.toggles.card-payments": "Mokėjimai kortele", "notification-settings.toggles.readonly-wallets": "<PERSON><PERSON><PERSON><PERSON>", "ntft.groupHeader.text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "on_ramp.crypto_completed": "Atlikta", "on_ramp.fiat_completed": "Atlikta", "onboarding-widget.subtitle.card_created_from_order.left": "Visa kortelė", "onboarding-widget.subtitle.card_created_from_order.right": "Aktyvuoti kortelę", "onboarding-widget.subtitle.card_order_ready.left": "Fizinė Visa kortelė", "onboarding-widget.subtitle.default": "Banko pavedimai ir Visa kortelė", "onboarding-widget.title.card-order-in-progress": "Tęsti kortelės užsakymą", "onboarding-widget.title.card_created_from_order": "Kortelė i<PERSON>sta", "onboarding-widget.title.kyc_approved": "Pavedimai ir kort<PERSON> par<PERSON>", "onboarding-widget.title.kyc_failed": "<PERSON><PERSON><PERSON> sukurti negalima", "onboarding-widget.title.kyc_not_started": "Tęsti <PERSON>", "onboarding-widget.title.kyc_started_documents_requested": "Užbaik <PERSON>ą", "onboarding-widget.title.kyc_started_resubmission_requested": "Pakart<PERSON><PERSON>", "onboarding-widget.title.kyc_started_verification_in_progress": "Tikrinama <PERSON>", "onboarding.loginOrCreateAccount.amountOfAssets": "Daugiau nei 10 mlrd. USD turto", "onboarding.loginOrCreateAccount.cards.subtitle": "Prieinama tik tam tikruose regionuose. Tęsdamas (-a) sutinki su mūsų <Terms>Sąlygomis</Terms> ir <PrivacyPolicy>Privatumo politika</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Visa kortelė su didele{br}gr<PERSON><PERSON><PERSON> ir be mokesčių", "onboarding.loginOrCreateAccount.createAccount": "Sukurti paskyrą", "onboarding.loginOrCreateAccount.earn.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kapital<PERSON> kyla rizika. Tęs<PERSON>as (-a) sutinki su mūsų <Terms>Sąlygomis</Terms> ir <PrivacyPolicy>Privatumo politika</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "Uždarbis iki {percent} per metus{br}Pasitiki daugiau nei {currencySymbol}5 mlrd.+", "onboarding.loginOrCreateAccount.earningPerYear": "Uždarbis iki {percent}{br} per metus", "onboarding.loginOrCreateAccount.login": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onboarding.loginOrCreateAccount.trading.subtitle": "Kapitalui kyla rizika. Tęsdamas (-a) sutinki su mūsų <Terms>Sąlygomis</Terms> ir <PrivacyPolicy>Privatumo politika</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Investuok į viską,{br}nuo BTC iki S&P", "onboarding.loginOrCreateAccount.trustedBy": "Skaitmeninės <PERSON>{br}Pasitiki daugiau nei {assets}", "onboarding.wallet_stories.close": "Uždaryti", "onboarding.wallet_stories.previous": "<PERSON><PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.deposit": "Indėlis", "order-earn-deposit-bridge.into": "Į", "otpIncorrectMessage": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "passkey-creation-not-possible.modal.close": "Uždaryti", "passkey-creation-not-possible.modal.subtitle": "Nepavyko sukurti slaptažodžio tavo piniginei. Įsitikink, kad tavo įrenginys palaiko slap<PERSON>, ir bandyk dar kartą. <link>Susisiek su pagalba</link> jei problema išlieka.", "passkey-creation-not-possible.modal.title": "Nepavyksta sukurti slaptažodžio", "passkey-not-supported-in-mobile-browser.modal.cta": "Atsisiųsti Z<PERSON>", "passkey-not-supported-in-mobile-browser.modal.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> nepalaikomos mobiliosiose naršyklėse.", "passkey-not-supported-in-mobile-browser.modal.title": "Norėdamas t<PERSON>, atsisiųsk Zeal programėlę", "passkey-recovery.recovering.deploy-signer.loading-text": "Tikrinamas prieigos raktas", "passkey-recovery.recovering.loading-text": "<PERSON><PERSON><PERSON><PERSON>", "passkey-recovery.recovering.signer-not-found.subtitle": "Nepavyko susieti tavo prieigos rakto su aktyvia pinigine. Jei piniginėje turi lėšų, susisiek su Zeal komanda.", "passkey-recovery.recovering.signer-not-found.title": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON>ta", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "Bandykite kitą raktą", "passkey-recovery.select-passkey.banner.subtitle": "Prisijunk prie reikiamos paskyros. Raktai susieti su paskyra.", "passkey-recovery.select-passkey.banner.title": "Nematai savo piniginės prieigos rak<PERSON>?", "passkey-recovery.select-passkey.continue": "Pasirinkti prieigos rakt<PERSON>", "passkey-recovery.select-passkey.subtitle": "Pasirink prieigos rak<PERSON>, susietą su tavo pinigine, kad atgautum prieigą.", "passkey-recovery.select-passkey.title": "Pasirinkti prieigos rakt<PERSON>", "passkey-story_1.subtitle": "Su Smart Wallet tinklo mokesčius gali mokėti dauguma žetonų ir nesirūpinti dėl papildomų mokesčių.", "passkey-story_1.title": "Pamiršk tin<PERSON> m<PERSON> – mokėk dauguma žetonų", "passkey-story_2.subtitle": "<PERSON><PERSON><PERSON> naudojant Safe <PERSON><PERSON><PERSON><PERSON><PERSON>, kurie saugo daugiau nei 100 mlrd. USD vertės turtą daugiau nei 20 milijonų piniginių.", "passkey-story_2.title": "Apsaugota su Safe", "passkey-story_3.subtitle": "Smart Wallet veikia pagrindiniuose su Ethereum suderinamuose tinkluose. <PERSON><PERSON><PERSON>, patikrink palaiko<PERSON> tink<PERSON>.", "passkey-story_3.title": "Palaikomi pagrindiniai EVM tinklai", "password.add.header": "Sukurti slaptažodį", "password.add.includeLowerAndUppercase": "<PERSON><PERSON><PERSON><PERSON> ir didžiosios raidės", "password.add.includesNumberOrSpecialChar": "<PERSON><PERSON><PERSON> s<PERSON> arba simbolis", "password.add.info.subtitle": "<PERSON><PERSON> į savo serverius ir nekuriame jo at<PERSON> kopijos.", "password.add.info.t_and_c": "Tęsdamas (-a) sutinki su mūsų <Terms>Sąlygomis</Terms> ir <PrivacyPolicy>Privatumo politika</PrivacyPolicy>", "password.add.info.title": "<PERSON><PERSON> šiame įrenginyje", "password.add.inputPlaceholder": "Sukurti slaptažodį", "password.add.shouldContainsMinCharsCheck": "10+ simbolių", "password.add.subheader": "Slap<PERSON><PERSON><PERSON><PERSON><PERSON> atrakinsi Zeal", "password.add.success.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> suku<PERSON> 🔥", "password.confirm.header": "<PERSON><PERSON><PERSON><PERSON> slaptažodį", "password.confirm.passwordDidNotMatch": "Slaptažodžiai turi sutapti", "password.confirm.subheader": "Įvesk slaptažodį dar kartą", "password.create_pin.subtitle": "Šis kodas užrakina Zeal programėlę", "password.create_pin.title": "Sukurk savo kodą", "password.enter_pin.title": "Įvesti kodą", "password.incorrectPin": "Neteisingas kodas", "password.pin_is_not_same": "<PERSON><PERSON>", "password.placeholder.enter": "Įvesti slaptažodį", "password.placeholder.reenter": "Pakartoti slaptažodį", "password.re_enter_pin.subtitle": "Įvesk tą patį kodą dar kartą", "password.re_enter_pin.title": "<PERSON><PERSON><PERSON><PERSON> kod<PERSON>", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "Vykdoma", "pending-send.details.pending": "Vykdoma", "pending-send.details.processing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "permit-info.modal.description": "Leidimai – <PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> gali perkelti tavo turtą tavo vardu, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, atlikti keitimą.{br}Leidimai yra panaš<PERSON> į pat<PERSON><PERSON><PERSON>, ta<PERSON><PERSON><PERSON> juos pasi<PERSON>nt nereikia mokėti tinklo moke<PERSON>čių.", "permit-info.modal.title": "Kas yra leidimai?", "permit.edit-expiration": "Redaguoti {currency} galioji<PERSON> laik<PERSON>", "permit.edit-limit": "Redaguoti {currency} išlaidų limitą", "permit.edit-modal.expiresIn": "G<PERSON><PERSON> dar...", "permit.expiration-warning": "{currency} galio<PERSON><PERSON> laiko įspėjimas", "permit.expiration.info": "{currency} galiojimo informacija", "permit.expiration.never": "<PERSON><PERSON><PERSON>", "permit.spend-limit.info": "{currency} išlaidų limito informacija", "permit.spend-limit.warning": "{currency} išlaidų limito įspėjimas", "phoneNumber.title": "telefono numeris", "physicalCardOrderFlow.cardOrdered": "Kortelė užsaky<PERSON>", "physicalCardOrderFlow.city": "Miestas", "physicalCardOrderFlow.orderCard": "Užsakyti kortelę", "physicalCardOrderFlow.postcode": "<PERSON><PERSON><PERSON> k<PERSON>", "physicalCardOrderFlow.shippingAddress.subtitle": "Adresas, kuriuo bus siunčiama tavo kortelė", "physicalCardOrderFlow.shippingAddress.title": "Pristat<PERSON><PERSON>", "physicalCardOrderFlow.street": "Gatvė", "placeholderDapps.1inch.description": "Keisk valiutas geriausiais ma<PERSON>", "placeholderDapps.aave.description": "Skolink ir pasiskolink žetonų", "placeholderDapps.bungee.description": "Naudok Bridge tarp tinklų geriausiais maršrutais", "placeholderDapps.compound.description": "Skolink ir pasiskolink žetonų", "placeholderDapps.cowswap.description": "Keisk Gnosis geriausiais kursais", "placeholderDapps.gnosis-pay.description": "Valdyk savo Gnosis Pay kortelę", "placeholderDapps.jumper.description": "Naudok Bridge tarp tinklų geriausiais maršrutais", "placeholderDapps.lido.description": "Statyk ETH, kad gautum daugiau ETH", "placeholderDapps.monerium.description": "El. pinigai ir banko pavedimai", "placeholderDapps.odos.description": "Keisk valiutas geriausiais ma<PERSON>", "placeholderDapps.stargate.description": "Naudok Bridge arba statyk už <14% metinių palūkanų", "placeholderDapps.uniswap.description": "Viena populiariausių keityklų", "pleaseAllowNotifications.cardPayments": "Mokėjimai kortele", "pleaseAllowNotifications.customiseInSettings": "<PERSON><PERSON><PERSON> nustat<PERSON><PERSON><PERSON>", "pleaseAllowNotifications.enable": "Įjungti", "pleaseAllowNotifications.forWalletActivity": "<PERSON><PERSON>", "pleaseAllowNotifications.title": "Gauk <PERSON><PERSON>", "pleaseAllowNotifications.whenReceivingAssets": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.add_funds": "Įnešti", "portfolio.quick-actions.buy": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.deposit": "Įnešti", "portfolio.quick-actions.send": "Si<PERSON>sti", "portfolio.view.lastRefreshed": "<PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Papildyk savo testavimo tinklo AVAX", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Eiti į Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "Papildyk savo testavimo tinklo BNB", "portfolio.view.topupTestNet.BscTestnet.secondary": "Eiti į Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Papildyk savo testavimo tinklo SepETH", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Eiti į Sepolia Faucet", "portfolio.view.topupTestNet.FantomTestnet.primary": "Papildyk savo testavimo tinklo FTM", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Eiti į Faucet", "privateKeyConfirmation.banner.subtitle": "<PERSON><PERSON><PERSON> jū<PERSON>ų privatųjį raktą valdo jūsų piniginę ir l<PERSON>. <PERSON> prašo tik sukčiai.", "privateKeyConfirmation.banner.title": "Suprantu rizik<PERSON>", "privateKeyConfirmation.title": "NIEKADA niekam neatskleisk savo privataus rakto", "rating-request.not-now": "<PERSON><PERSON> <PERSON><PERSON>", "rating-request.title": "Ar rekomen<PERSON>?", "receive_funds.address-text": "Tai tavo unikalus pinigin<PERSON>s ad<PERSON>. Gali saugiai juo dalintis su kitais.", "receive_funds.copy_address": "Kopijuot<PERSON> ad<PERSON>", "receive_funds.network-warning.eoa.subtitle": "<link><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ti tinklų sąrašą</link>. <PERSON><PERSON><PERSON><PERSON>, išsiųstos ne EVM tinklais, bus prarastos.", "receive_funds.network-warning.eoa.title": "Palaikomi visi Ethereum pagrindo tinklai", "receive_funds.network-warning.scw.subtitle": "<link><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> palaikomus tinklus</link>. <PERSON><PERSON><PERSON><PERSON>, išsiųstos kitais tinklais, bus prarastos.", "receive_funds.network-warning.scw.title": "Svarbu: naudok tik palaikomus tinklus", "receive_funds.scan_qr_code": "Nuskaityti QR kodą", "receiving.in.days": "<PERSON><PERSON>i po {days} d.", "receiving.this.week": "<PERSON><PERSON><PERSON> savait<PERSON>", "receiving.today": "<PERSON><PERSON><PERSON>", "reference.error.maximum_number_of_characters_exceeded": "Per daug simbolių", "referral-code.placeholder": "Įklijuok pakvietimo nuorodą", "referral-code.subtitle": "Dar kartą spustelėk draugo nuorodą arba įklijuok ją žemiau. <PERSON><PERSON>, kad gausi a<PERSON>.", "referral-code.title": "Ar draugas tau atsiunt<PERSON> {bReward}?", "rekyc.verification_deadline.subtitle": "Užbaik patvir<PERSON>ą per {daysUntil} d., kad gal<PERSON>tum to<PERSON>u naudo<PERSON> kortele.", "rekyc.verification_required.subtitle": "<PERSON><PERSON><PERSON><PERSON>, kad <PERSON> to<PERSON>u naudo<PERSON> kort<PERSON>.", "reminder.fund": "💸 Įnešk lėšų — pradėk uždirbti 6 % iškart", "reminder.onboarding": "🏁 Pabaik sąranką — uždirbk 6 % nuo savo indėlių", "remove-owner.confirmation.subtitle": "Saugumo sumetimais nustatymų keitimas trunka 3 minutes. Tuo metu tavo kortelė bus laikinai užšaldyta, o mokėjimai negalimi.", "remove-owner.confirmation.title": "Kol bus atnaujinami nustatymai, tavo kortelė bus užšaldyta 3 min.", "restore-smart-wallet.wallet-recovered": "Piniginė atkurta", "rewardClaimCelebration.claimedTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jau <PERSON>", "rewardClaimCelebration.subtitle": "Už draugų pakvietimą", "rewardClaimCelebration.title": "Tu gavai", "rewards-warning.subtitle": "Paša<PERSON>us <PERSON> p<PERSON>, bus sustabdyta prieiga prie susietų apdovanojimų. Galėsi bet kada atkurti paskyrą ir juos atsi<PERSON>.", "rewards-warning.title": "Prarasi prieigą prie apdovanojimų", "rewards.copiedInviteLink": "Pakvietimo nuoroda nukopijuota", "rewards.createAccount": "Kopijuoti nuorodą", "rewards.header.subtitle": "<PERSON><PERSON><PERSON><PERSON>si<PERSON> {aReward} tau ir {bReward} tavo draugui, kai jis i<PERSON> {bSpendLimitReward}.", "rewards.header.title": "Gauk {amountA}{br}Duok {amountB}", "rewards.sendInvite": "Siųsti pakvietimą", "rewards.sendInviteTip": "Pasirink draugą ir mes jam duosime {bAmount}", "route.fees": "Mokesčiai {fees}", "routesNotFound.description": "<PERSON><PERSON><PERSON> ma<PERSON> {from}–{to} tinklų deriniui nepas<PERSON>.", "routesNotFound.title": "Keitimo ma<PERSON>š<PERSON>", "rpc.OrderBuySignMessage.subtitle": "Naudojant Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Naudojant Swaps.IO", "rpc.addCustomNetwork.addNetwork": "<PERSON><PERSON><PERSON><PERSON>", "rpc.addCustomNetwork.chainId": "Tinklo ID", "rpc.addCustomNetwork.nativeToken": "<PERSON>g<PERSON><PERSON><PERSON>", "rpc.addCustomNetwork.networkName": "<PERSON><PERSON><PERSON>", "rpc.addCustomNetwork.operationDescription": "<PERSON><PERSON> svet<PERSON> galės pridėti tinklą į tavo piniginę. Zeal negali patikrinti nestandartinių tinklų saugumo, todė<PERSON> įsitikink, kad supranti rizik<PERSON>.", "rpc.addCustomNetwork.rpcUrl": "RPC URL", "rpc.addCustomNetwork.subtitle": "<PERSON><PERSON><PERSON><PERSON> {name}", "rpc.addCustomNetwork.title": "<PERSON><PERSON><PERSON><PERSON>", "rpc.send_token.network_not_supported.subtitle": "Stengiamės įjungti pavedimus šiame tinkle. Dėkojame už kantrybę 🙏", "rpc.send_token.network_not_supported.title": "Tinklas netrukus veiks", "rpc.send_token.send_or_receive.settings": "Nustatymai", "rpc.sign.accept": "<PERSON><PERSON><PERSON><PERSON>", "rpc.sign.cannot_parse_message.body": "Nepavyko iššifruoti šios žinutės. Priimk užklausą tik jei pasitiki šia programėle.{br}{br}Žinutės gali būti naudojamos prisijungti prie programėlės, bet taip pat gali suteikti programėlėms prieigą prie tavo žetonų.", "rpc.sign.cannot_parse_message.header": "Tęsk atsargiai", "rpc.sign.import_private_key": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "rpc.sign.subtitle": "Skirta {name}", "rpc.sign.title": "Pasir<PERSON>š<PERSON><PERSON>", "safe-creation.success.title": "Piniginė sukurta", "safe-safety-checks-popup.title": "Transakcijos sa<PERSON>o pat<PERSON>", "safetyChecksPopup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "scan_qr_code.description": "Nuskaityk piniginės QR kodą arba prisijunk prie programėlės", "scan_qr_code.show_qr_code": "Rodyti mano QR kodą", "scan_qr_code.tryAgain": "Bandyti dar kartą", "scan_qr_code.unlockCamera": "Atrakinti kamerą", "screen-lock-missing.modal.close": "Uždaryti", "screen-lock-missing.modal.subtitle": "Kad <PERSON>tum naudoti <PERSON>, tavo įrenginiui reikalingas ekrano užraktas. Nustatyk ekrano užraktą ir bandyk dar kartą.", "screen-lock-missing.modal.title": "Nėra ekrano užrakto", "seedConfirmation.banner.subtitle": "Turintys jūs<PERSON> slapt<PERSON>j<PERSON> frazę valdo jūsų piniginę ir l<PERSON>. <PERSON><PERSON> p<PERSON> tik sukč<PERSON>.", "seedConfirmation.title": "NIEKADA niekam neatskleisk savo slaptažodžio frazės", "select-active-owner.subtitle": "Su tavo kortele susieta keletas piniginių. Pasirink vieną, k<PERSON>ą prijungsi prie Zeal. Pakeisti galėsi bet kada.", "select-active-owner.title": "<PERSON><PERSON><PERSON>", "select-card.title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "select-crypto-currency-title": "Pasirink žetoną", "select-token.title": "Pasirinkti žetoną", "selectEarnAccount.chf.description.steps": "· <PERSON><PERSON><PERSON><PERSON><PERSON> 24/7, j<PERSON><PERSON> įšaldymų {br}· Palūkanos kaupiasi kas sekundę {br}· Papildomai apsaugoti indėliai <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} per metus CHF", "selectEarnAccount.eur.description.steps": "· <PERSON><PERSON><PERSON><PERSON><PERSON> 24/7, j<PERSON><PERSON> įšaldymų {br}· Palūkanos kaupiasi kas sekundę {br}· Itin saugios paskolos su <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} per metus EUR", "selectEarnAccount.subtitle": "<PERSON>ali pakeisti bet kada", "selectEarnAccount.title": "<PERSON><PERSON><PERSON> vali<PERSON>ą", "selectEarnAccount.usd.description.steps": "· <PERSON><PERSON><PERSON><PERSON><PERSON> 24/7, j<PERSON><PERSON> įšaldymų {br}· Palūkanos kaupiasi kas sekundę {br}· <PERSON>in saugūs indėliai <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} per metus USD", "selectEarnAccount.zero.description_general": "Laikyk skaitmenines lėšas neuždirbdamas palūkanų", "selectEarnAccount.zero.title": "0% per metus", "selectRechargeThreshold.button.enterAmount": "Įvesti sumą", "selectRechargeThreshold.button.setTo": "Nustatyti {amount}", "selectRechargeThreshold.description.line1": "<PERSON> lik<PERSON> nuk<PERSON> {amount}, ji automatiškai pasipildys iki {amount} iš tavo „Earn“ paskyros.", "selectRechargeThreshold.description.line2": "Mažesnis tikslas leidžia daugiau lėšų laikyti „Earn“ paskyroje (uždirbant 3%). Gali tai pakeisti bet kada.", "selectRechargeThreshold.title": "Nustatyti kortelės tikslinį likutį", "select_currency_to_withdraw.select_token_to_withdraw": "<PERSON><PERSON><PERSON>, kurį turt<PERSON> i<PERSON>", "send-card-token.form.send": "Si<PERSON>sti", "send-card-token.form.send-amount": "Pa<PERSON><PERSON><PERSON>o suma", "send-card-token.form.title": "Papild<PERSON><PERSON> kortelę", "send-card-token.form.to-address": "Kortelė", "send-safe-transaction.network-fee-widget.error": "Tau reikia {amount} arba pasirink kitą turtą", "send-safe-transaction.network-fee-widget.no-fee": "Be mokesčių", "send-safe-transaction.network-fee-widget.title": "Mokesčiai", "send-safe-transaction.network_fee_widget.title": "<PERSON><PERSON><PERSON>", "send.banner.fees": "Tau reikia {amount} daugiau {currency} moke<PERSON><PERSON><PERSON><PERSON>", "send.banner.toAddressNotSupportedNetwork.subtitle": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON> {network}. Pakeisk į palaikomą valiutą.", "send.banner.toAddressNotSupportedNetwork.title": "Gavėjas nepalaiko š<PERSON>", "send.banner.walletNotSupportedNetwork.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> negali atlikti operacijų {network}. Pakeisk į palaikomą valiutą.", "send.banner.walletNotSupportedNetwork.title": "<PERSON><PERSON><PERSON>", "send.empty-portfolio.empty-state": "Nerasta jokių valiutų", "send.empty-portfolio.header": "Valiutos", "send.titile": "Si<PERSON>sti", "sendLimit.success.subtitle": "Naujas limitas įsigalios per 3 min.", "sendLimit.success.title": "Pakeitimas užtruks 3 minutes", "send_crypto.form.disconnected.cta.addFunds": "Pridėti lėšų", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Perjungti į {network}", "send_crypto.form.disconnected.label": "<PERSON><PERSON><PERSON> suma", "send_to.qr_code.description": "Nuskaityk QR kodą ir siųsk į piniginę", "send_to.qr_code.title": "Nuskaityti QR kodą", "send_to_card.header": "Siųsti į kortelės adresą", "send_to_card.select_sender.add_wallet": "<PERSON><PERSON><PERSON><PERSON>", "send_to_card.select_sender.header": "<PERSON><PERSON><PERSON><PERSON>", "send_to_card.select_sender.search.default_placeholder": "Ieškoti adreso arba ENS", "send_to_card.select_sender.show_card_address_button_description": "<PERSON><PERSON><PERSON> k<PERSON>el<PERSON>", "send_token.form.select-address": "<PERSON><PERSON><PERSON><PERSON>", "send_token.form.send-amount": "Siunčiama suma", "send_token.form.title": "Si<PERSON>sti", "setLimit.amount.error.zero_amount": "Negalėsi atlikti jokių mokėjimų", "setLimit.error.max_limit_reached": "Nustatyti maks. limitą {amount}", "setLimit.error.same_as_current_limit": "<PERSON><PERSON><PERSON> dabar<PERSON>iam limitui", "setLimit.placeholder": "Dabar<PERSON><PERSON>: {amount}", "setLimit.submit": "Nustatyti limitą", "setLimit.submit.error.amount_required": "Įvesk sumą", "setLimit.subtitle": "<PERSON><PERSON>, kurią gali išleisti per dieną.", "setLimit.title": "Nustatyti dienos išlaidų limitą", "settings.accounts": "<PERSON><PERSON><PERSON>", "settings.accountsSeeAll": "Matyti visas", "settings.addAccount": "<PERSON><PERSON><PERSON><PERSON>", "settings.card": "<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>", "settings.connections": "Programėlių susiejimai", "settings.currency": "<PERSON><PERSON><PERSON><PERSON><PERSON> valiuta", "settings.default_currency_selector.title": "Valiuta", "settings.discord": "Discord", "settings.experimentalMode": "Eksperimentinis režimas", "settings.experimentalMode.subtitle": "Išbandyk naujas funkcijas", "settings.language": "Kalba", "settings.lockZeal": "<PERSON><PERSON><PERSON><PERSON><PERSON> Z<PERSON>", "settings.notifications": "Pranešimai", "settings.open_expanded_view": "Atidaryti išplėstą vaizdą", "settings.privacyPolicy": "Privatumo politika", "settings.settings": "Nustatymai", "settings.termsOfUse": "Naudojimos<PERSON>", "settings.twitter": "𝕏 / Twitter", "settings.version": "<PERSON><PERSON><PERSON> {version} aplinka: {env}", "setup-card.confirmation": "<PERSON><PERSON><PERSON> kort<PERSON>", "setup-card.confirmation.subtitle": "Mokėk internetu ir pridėk prie savo {type} pin<PERSON><PERSON><PERSON><PERSON> bekon<PERSON>č<PERSON> mok<PERSON>.", "setup-card.getCard": "<PERSON><PERSON><PERSON>", "setup-card.order.physicalCard": "<PERSON><PERSON><PERSON> kortel<PERSON>", "setup-card.order.physicalCard.steps": "· Fizinė VISA Gnosis Pay {br}· Pristatymas trunka iki 3 savaičių {br}· Naudok mokėjimams gyvai ir bankomatuose. {br}· Pridėk į Apple/Google piniginę (tik palaikomose šalyse", "setup-card.order.subtitle1": "<PERSON><PERSON> naudoti kelias kortele<PERSON> vienu metu", "setup-card.order.title": "Kokio tipo kortelė<PERSON> nori?", "setup-card.order.virtualCard": "<PERSON><PERSON>", "setup-card.order.virtual_card.steps": "· Skaitmeninė VISA Gnosis Pay {br}· Iškart naudok mokėjimams internetu {br}· Pridėk į Apple/Google piniginę (tik palaikomose šalyse)", "setup-card.orderCard": "Užsakyti kortelę", "setup-card.virtual-card": "<PERSON><PERSON><PERSON> kort<PERSON>", "setup.notifs.fakeAndroid.title": "Pranešimai apie mokėjimus ir gaunamus pavedimus", "setup.notifs.fakeIos.subtitle": "Zeal gali tave įspėti, kai gauni g<PERSON>j<PERSON> arba išleidi pinigus su savo Visa kortele. Tai galėsi pakeisti vėliau.", "setup.notifs.fakeIos.title": "Pranešimai apie mokėjimus ir gaunamus pavedimus", "sign.PermitAllowanceItem.spendLimit": "Išlaidų limitas", "sign.ledger.subtitle": "Užklausa aparatinėje piniginėje. Tęsk ten.", "sign.ledger.title": "Patvirtinti aparatinė<PERSON>", "sign.passkey.subtitle": "Tavo naršyklė turėtų paraginti pasirašyti su šia pinigine susietu slaptažodžiu. Tęsk ten.", "sign.passkey.title": "Pa<PERSON>ink<PERSON> slaptažodį", "signal_aborted_for_uknown_reason.title": "Tinklo užklausa atšaukta", "simulatedTransaction.BridgeTrx.info.title": "Bridge", "simulatedTransaction.CardTopUp.info.title": "Papild<PERSON><PERSON> kortelę", "simulatedTransaction.CardTopUpTrx.info.title": "Papild<PERSON><PERSON> kortelę", "simulatedTransaction.NftCollectionApproval.approve": "Patvirtinti NFT kolekciją", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "Papild<PERSON><PERSON> kortelę", "simulatedTransaction.OrderEarnDepositBridge.title": "Indėlis į Earn", "simulatedTransaction.P2PTransaction.info.title": "Si<PERSON>sti", "simulatedTransaction.PermitSignMessage.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.SingleNftApproval.approve": "Patvirtinti NFT", "simulatedTransaction.UnknownSignMessage.title": "Pasir<PERSON>š<PERSON><PERSON>", "simulatedTransaction.Withdrawal.info.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.approval.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.approve.info.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.p2p.info.account": "<PERSON><PERSON>", "simulatedTransaction.p2p.info.unlabelledAccount": "Neužvadinta piniginė", "simulatedTransaction.unknown.info.receive": "<PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.info.send": "<PERSON>š<PERSON>ų<PERSON>", "simulatedTransaction.unknown.using": "<PERSON><PERSON><PERSON><PERSON> {app}", "simulation.approval.modal.text": "<PERSON>, su<PERSON><PERSON> le<PERSON> konkrečiai programėlei ar išmaniajai sutarčiai naudoti tavo žetonus ar NFT būsimose operacijose.", "simulation.approval.modal.title": "Kas yra pat<PERSON>?", "simulation.approval.spend-limit.label": "Išlaidų limitas", "simulation.approve.footer.for": "Skirta", "simulation.approve.unlimited": "Neribot<PERSON>", "simulationNotAvailable.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smart-wallet-activation-view.on": "Tinkle", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password gali blokuoti prieigą prie tavo piniginės", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Naudok „Apple“ arba „Google“, kad saugiai nustatytum Zeal", "smart-wallet.passkey-notice.title": "Venk 1Password", "spend-limits.high.modal.text": "Nustatyk išlaid<PERSON> limit<PERSON>, <PERSON><PERSON><PERSON> kiekiui, kurį ketini naudoti su programėle ar išmaniąja sutartimi. Dideli limitai yra rizikingi ir gali padėti sukčiams pavogti tavo žetonus.", "spend-limits.high.modal.text_sign_message": "Išlaidų limitas turėtų būti artimas žetonų kiekiui, kurį naudosi programėlėje ar išmaniojoje sutartyje. Dideli limitai yra rizikingi ir palengvina sukčiams pavogti tavo žetonus.", "spend-limits.high.modal.title": "<PERSON><PERSON><PERSON>", "spend-limits.modal.text": "<PERSON>š<PERSON><PERSON> limitas nurodo, kiek žetonų programėlė gali panaudoti tavo vardu. Šį limitą gali bet kada pakeisti arba panaikinti. Saugumo sum<PERSON>, nust<PERSON><PERSON>, art<PERSON><PERSON> kiekiui, kurį ketini naudoti.", "spend-limits.modal.title": "Kas yra i<PERSON> limit<PERSON>?", "spent-limit-info.modal.description": "<PERSON><PERSON><PERSON><PERSON> limitas nurodo, kiek žetonų programėlė gali naudoti tavo vardu. Šį limitą gali bet kada pakeisti ar pa<PERSON>linti. <PERSON>ug<PERSON>o <PERSON>, nust<PERSON><PERSON> limit<PERSON>, art<PERSON><PERSON> kiekiui, kurį ketini naudoti programėlėje.", "spent-limit-info.modal.title": "Kas yra i<PERSON> limit<PERSON>?", "sswaps-io.transfer-provider": "<PERSON><PERSON><PERSON>", "storage.accountDetails.activateWallet": "Aktyvuoti piniginę", "storage.accountDetails.changeWalletLabel": "<PERSON><PERSON><PERSON>", "storage.accountDetails.deleteWallet": "<PERSON><PERSON><PERSON><PERSON>", "storage.accountDetails.setup_recovery_kit": "Atk<PERSON><PERSON><PERSON>", "storage.accountDetails.showPrivateKey": "Rodyti privatų raktą", "storage.accountDetails.showWalletAddress": "<PERSON><PERSON><PERSON>", "storage.accountDetails.smartBackup": "Atsarginė kopija ir at<PERSON>", "storage.accountDetails.viewSsecretPhrase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fraz<PERSON>", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets?", "storage.manageAccounts.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submit-userop.progress.text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submit.error.amount_high": "<PERSON><PERSON> per <PERSON>", "submit.error.amount_hight": "<PERSON><PERSON> per <PERSON>", "submit.error.amount_low": "Suma per maža", "submit.error.amount_required": "Įvesk sumą", "submit.error.maximum_number_of_characters_exceeded": "Sutrumpink pranešimą", "submit.error.not_enough_balance": "Nepakanka lėšų", "submit.error.recipient_required": "<PERSON><PERSON><PERSON>", "submit.error.routes_not_found": "Maršrutų nerasta", "submitSafeTransaction.monitor.title": "Transakcijos rezultatas", "submitSafeTransaction.sign.title": "Transakcijos rezultatas", "submitSafeTransaction.state.sending": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitSafeTransaction.state.sign": "<PERSON><PERSON><PERSON>", "submitSafeTransaction.submittingToRelayer.title": "Transakcijos rezultatas", "submitTransaction.cancel": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.cancel.attemptingToStop": "<PERSON><PERSON> su<PERSON>", "submitTransaction.cancel.failedToStop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.cancel.stopped": "Sustabdyta", "submitTransaction.cancel.title": "<PERSON><PERSON><PERSON>", "submitTransaction.failed.banner.description": "Tinklas netikėtai atšaukė šį pavedimą. Bandyk dar kartą.", "submitTransaction.failed.banner.title": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.failed.execution_reverted.title": "Programėlėje įvyko klaida", "submitTransaction.failed.execution_reverted_without_message.title": "Programėlėje įvyko klaida", "submitTransaction.failed.out_of_gas.description": "<PERSON><PERSON><PERSON><PERSON>, nes vir<PERSON><PERSON> tin<PERSON> m<PERSON>.", "submitTransaction.failed.out_of_gas.title": "<PERSON><PERSON><PERSON>", "submitTransaction.sign.title": "<PERSON><PERSON><PERSON>", "submitTransaction.speedUp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue": "Pridėta į eilę", "submitTransaction.state.addedToQueue.short": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.cancelled": "Sustabdyta", "submitTransaction.state.complete": "{currencyCode} pridėta į Zeal", "submitTransaction.state.complete.subtitle": "Patikrink savo Zeal portfelį", "submitTransaction.state.completed": "Atlikta", "submitTransaction.state.failed": "Nepavyko", "submitTransaction.state.includedInBlock": "Įtraukta į bloką", "submitTransaction.state.includedInBlock.short": "Bloke", "submitTransaction.state.replaced": "<PERSON><PERSON><PERSON>", "submitTransaction.state.sendingToNetwork": "Siunčiama į tinklą", "submitTransaction.stop": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.submit": "Pat<PERSON><PERSON><PERSON>", "submitted-user-operation.state.bundled": "<PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.completed": "Atlikta", "submitted-user-operation.state.failed": "Nepavyko", "submitted-user-operation.state.pending": "<PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.rejected": "Atmes<PERSON>", "submittedTransaction.failed.title": "<PERSON><PERSON><PERSON><PERSON>", "success_splash.card_activated": "Kortelė aktyvuota", "supportFork.give-feedback.title": "Pateikti atsiliepimą", "supportFork.itercom.description": "Zeal atsako į klausimus apie įmokas, <PERSON><PERSON><PERSON>, premijas ir kt.", "supportFork.itercom.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supportFork.title": "<PERSON><PERSON><PERSON>", "supportFork.zendesk.subtitle": "Gnosis Pay: k<PERSON><PERSON><PERSON><PERSON>, tapatybė, grąžinimai.", "supportFork.zendesk.title": "Mokėjimai kortele ir tapatybė", "supported-networks.ethereum.warning": "Auk<PERSON><PERSON>", "supportedNetworks.networks": "<PERSON><PERSON><PERSON><PERSON>", "supportedNetworks.oneAddressForAllNetworks": "Vienas adresas visiems tinklams", "supportedNetworks.receiveAnyAssets": "Gauk turtą iš visų tinklų vienu adresu", "swap.form.error.no_routes_found": "Maršrutų nerasta", "swap.form.error.not_enough_balance": "Nepakanka lėšų", "swaps-io-details.bank.serviceProvider": "Paslaugų teikėjas", "swaps-io-details.details.processing": "Vykdoma", "swaps-io-details.pending": "<PERSON><PERSON><PERSON>", "swaps-io-details.rate": "<PERSON><PERSON><PERSON>", "swaps-io-details.serviceProvider": "Paslaugų teikėjas", "swaps-io-details.transaction.from.processing": "Pradėta operacija", "swaps-io-details.transaction.networkFees": "<PERSON><PERSON><PERSON>", "swaps-io-details.transaction.state.completed-transaction": "Užbaigta operacija", "swaps-io-details.transaction.state.started-transaction": "Pradėta operacija", "swaps-io-details.transaction.to.processing": "Užbaigta operacija", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Indėlis bus greitai. Kinetex apdoroja.", "swapsIO.monitoring.awaitingLiqSend.title": "Vėluoja", "swapsIO.monitoring.awaitingRecive.title": "<PERSON><PERSON><PERSON><PERSON>", "swapsIO.monitoring.awaitingSend.title": "<PERSON><PERSON><PERSON><PERSON>", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Tokenai grįš. Kinetex nebaigė pervedimo.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Tokenai nepervesti. Bandyk dar kartą.", "swapsIO.monitoring.cancelledNoSlash.title": "Tokenai grąžinti", "swapsIO.monitoring.cancelledSlashed.subtitle": "Tokenai grąžinti. Pervedimas nepavyko.", "swapsIO.monitoring.cancelledSlashed.title": "Tokenai grąžinti", "swapsIO.monitoring.completed.title": "Užbaigta", "taker-metadata.earn": "Uždirbk skaitmeniniais USD su Sky", "taker-metadata.earn.aave": "Uždirbk skaitmeniniais EUR su Aave", "taker-metadata.earn.aave.cashout24": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 24/7", "taker-metadata.earn.aave.trusted": "Patikėta 27 mlrd. $, 2+ metai", "taker-metadata.earn.aave.yield": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaupiama kas sekundę", "taker-metadata.earn.chf": "Uždirbk skaitmeniniais CHF", "taker-metadata.earn.chf.cashout24": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> akimirksniu, 24/7", "taker-metadata.earn.chf.trusted": "Patikėta 28 mln. Fr.", "taker-metadata.earn.chf.yield": "Pelnas kaupiasi kas sekundę", "taker-metadata.earn.usd.cashout24": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 24/7", "taker-metadata.earn.usd.trusted": "Patikėta 10,7 mlrd. $, 5+ metai", "taker-metadata.earn.usd.yield": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaupiama kas sekundę", "test": "Įnešti", "to.titile": "<PERSON><PERSON><PERSON>", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "<PERSON><PERSON><PERSON>", "token.groupHeader.titleWithSum": "Turtas {sum}", "token.hidden_tokens.page.title": "Paslėpti žetonai", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "Pridėti <PERSON>", "token.widget.cashback_empty": "Operacijų dar nėra", "token.widget.emptyState": "Piniginėje žetonų nėra", "tokens.cash": "<PERSON><PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.approve.for": "<PERSON><PERSON>", "top-up-card-from-earn-view.approve.into": "Į", "top-up-card-from-earn-view.swap.from": "<PERSON><PERSON>", "top-up-card-from-earn-view.swap.to": "Į", "top-up-card-from-earn-view.withdraw.to": "Į", "top-up-card-from-earn.trx.title.approval": "<PERSON><PERSON><PERSON><PERSON> keit<PERSON>", "top-up-card-from-earn.trx.title.swap": "Papild<PERSON><PERSON> kortelę", "top-up-card-from-earn.trx.title.withdrawal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "topUpDapp.connectWallet": "<PERSON>rijun<PERSON><PERSON> pin<PERSON>", "topup-fee-breakdown.bungee-fee": "<PERSON><PERSON><PERSON><PERSON>", "topup-fee-breakdown.header": "<PERSON><PERSON><PERSON>", "topup-fee-breakdown.network-fee": "<PERSON><PERSON><PERSON>", "topup-fee-breakdown.total-fee": "<PERSON><PERSON> m<PERSON>", "topup.continue-in-wallet": "Tęsk savo piniginėje", "topup.send.title": "Si<PERSON>sti", "topup.submit-transaction.close": "Uždaryti", "topup.submit-transaction.sent-to-wallet": "<PERSON><PERSON><PERSON><PERSON> {amount}", "topup.to": "<PERSON><PERSON>", "topup.transaction.complete.close": "Uždaryti", "topup.transaction.complete.try-again": "Bandyti dar kartą", "transaction-request.nonce-too-low.modal.button-text": "Uždaryti", "transaction-request.nonce-too-low.modal.text": "Pavedimas su tuo pačiu serijos numeriu (nonce) jau atliktas, to<PERSON><PERSON><PERSON> <PERSON>io pavedimo pateikti nebegalima. <PERSON><PERSON> gali nut<PERSON>, jei paved<PERSON> atlieki greitai vieną po kito arba bandai paspartinti ar atšaukti jau atliktą pavedimą.", "transaction-request.nonce-too-low.modal.title": "Pavedimas su tuo pačiu nonce atliktas", "transaction-request.replaced.modal.button-text": "Uždaryti", "transaction-request.replaced.modal.text": "Negalime sekti <PERSON>io <PERSON> b<PERSON>. Jis gal<PERSON> būti pakeistas kitu pavedimu arba RPC mazgas susiduria su problemomis.", "transaction-request.replaced.modal.title": "Nepavyko rasti <PERSON> b<PERSON>", "transaction.activity.details.modal.close": "Uždaryti", "transaction.cancel_popup.cancel": "N<PERSON>, palaukti", "transaction.cancel_popup.confirm": "<PERSON><PERSON><PERSON><PERSON>", "transaction.cancel_popup.description": "Sustabdymui reikia naujo tinklo mokesčio vietoj pradinio {oldFee}", "transaction.cancel_popup.description_without_original": "Sustabdymui reikia naujo tin<PERSON> m<PERSON>.", "transaction.cancel_popup.not_supported.subtitle": "Pavedimų stabdymas nepalaikomas tinkle {network}", "transaction.cancel_popup.not_supported.title": "Nepalaikoma", "transaction.cancel_popup.stopping_fee": "<PERSON><PERSON><PERSON> m<PERSON> už stabdy<PERSON>", "transaction.cancel_popup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>?", "transaction.in-progress": "Vykdoma", "transaction.inProgress": "Vykdoma", "transaction.speed_up_popup.cancel": "N<PERSON>, palaukti", "transaction.speed_up_popup.confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transaction.speed_up_popup.description": "<PERSON><PERSON>, reikia sumokėti naują tinklo mokestį vietoj pradinio moke<PERSON> {amount}", "transaction.speed_up_popup.description_without_original": "<PERSON><PERSON>, reikia sumokėti naują tinklo mokestį", "transaction.speed_up_popup.seed_up_fee_title": "<PERSON><PERSON><PERSON> pagre<PERSON><PERSON><PERSON> m<PERSON>", "transaction.speed_up_popup.title": "Pagreitinti operaciją?", "transaction.speedup_popup.not_supported.subtitle": "Operacijų greitinimas nepalaikomas {network}", "transaction.speedup_popup.not_supported.title": "Nepalaikoma", "transaction.subTitle.failed": "Nepavyko", "transactionDetails.cashback.not-qualified": "Nekvalifikuojama", "transactionDetails.cashback.paid": "{amount} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionDetails.cashback.pending": "{amount} laukiama", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "Nežinoma", "transactionDetails.cashback_estimate": "Numatomas cashback", "transactionDetails.category": "Kategorija", "transactionDetails.exchangeRate": "<PERSON><PERSON><PERSON>", "transactionDetails.location": "Vieta", "transactionDetails.payment-approved": "Mok<PERSON><PERSON><PERSON>", "transactionDetails.payment-declined": "Mokėjimas atmestas", "transactionDetails.payment-reversed": "Mokėjimas atšauktas", "transactionDetails.recharge.amountSentFromEarn.title": "<PERSON><PERSON>, išsiųsta iš „Earn“", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionDetails.recharge.rate.title": "<PERSON><PERSON><PERSON>", "transactionDetails.recharge.transactionId.title": "Operacijos ID", "transactionDetails.refund": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionDetails.reversal": "<PERSON>ša<PERSON>mas", "transactionDetails.transactionCurrency": "Operacijos valiuta", "transactionDetails.transactionId": "Operacijos ID", "transactionDetails.type": "Operacija", "transactionRequestWidget.approve.subtitle": "<PERSON><PERSON> {target}", "transactionRequestWidget.p2p.subtitle": "<PERSON><PERSON> {target}", "transactionRequestWidget.unknown.subtitle": "<PERSON><PERSON><PERSON><PERSON> {target}", "transactionSafetyChecksPopup.title": "<PERSON><PERSON><PERSON>", "transactions.main.activity.title": "Veikla", "transactions.page.hiddenActivity.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> veikla", "transactions.page.title": "Veikla", "transactions.viewTRXHistory.emptyState": "Kol kas jokių operacijų", "transactions.viewTRXHistory.errorMessage": "Nepavyko įkelti tavo operacijų istorijos", "transactions.viewTRXHistory.hidden.emptyState": "Nėra paslėptų operacijų", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Veikla nepalaikoma testavimo <PERSON>", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Veikla nepalaikoma testavimo tinkluose{br}<link>Į blokų naršyklę</link>", "transfer_provider": "<PERSON><PERSON><PERSON>", "transfer_setup_with_different_wallet.subtitle": "Banko pavedimai susieti su kita pinigine. Prie pavedimų gali būti prijungta tik viena piniginė.", "transfer_setup_with_different_wallet.swtich_and_continue": "<PERSON><PERSON><PERSON><PERSON> ir tęsti", "transfer_setup_with_different_wallet.title": "<PERSON><PERSON><PERSON><PERSON> pinigi<PERSON>", "tx-sent-to-wallet.button": "Uždaryti", "tx-sent-to-wallet.subtitle": "<PERSON><PERSON><PERSON> {wallet}", "unblockProviderInfo.fees": "Gauk mažiausius mokes<PERSON>: 0 % iki 5 tūkst. USD per mėnesį ir 0,2 % viršijus šią sumą.", "unblockProviderInfo.registration": "Unblock yra registruota ir FNTT įgaliota teikti VASP keitimo ir saugo<PERSON><PERSON> p<PERSON>, taip pat yra registruota MSB teikėja JAV Fincen. <link>Sužinok daugiau</link>", "unblockProviderInfo.selfCustody": "Gauti skaitmeniniai pinigai yra tavo privačioje piniginėje ir niekas kitas negalės kontroliuoti tavo turto.", "unblock_invalid_faster_payment_configuration.subtitle": "Tavo sąskaita netinka. Nurodyk kitą.", "unblock_invalid_faster_payment_configuration.title": "Reikalinga kita sąskaita", "unknownTransaction.primaryText": "Kortelės operacija", "unsupportedCountry.subtitle": "Banko pavedimai tavo šalyje dar neprieinami.", "unsupportedCountry.title": "Neprieinama {country}", "update-app-popup.subtitle": "Naujausiame atnaujinime gausu pataisymų, funkcijų ir kitų patobulinimų. Atnaujink į naujausią versiją ir pagerink savo Zeal patirtį.", "update-app-popup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> versij<PERSON>", "update-app-popup.update-now": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "user_associated_with_other_merchant.subtitle": "Ši piniginė netinka. Pasirink kitą.", "user_associated_with_other_merchant.title": "Piniginės negalima naudoti", "user_associated_with_other_merchant.try_with_another_wallet": "Bandyti su kita pinigine", "user_email_already_exists.subtitle": "Jau susiejai banko pavedimus su kita pinigine. Bandyk dar kartą su anksčiau naudota pinigine.", "user_email_already_exists.title": "Pavedimai susieti su kita pinigine", "user_email_already_exists.try_with_another_wallet": "Bandyti su kita pinigine", "validation.invalid.iban": "Neteisingas IBAN", "validation.required": "Privaloma", "validation.required.first_name": "<PERSON><PERSON><PERSON> var<PERSON>", "validation.required.iban": "<PERSON><PERSON><PERSON> nurodyti IBAN", "validation.required.last_name": "<PERSON><PERSON><PERSON> n<PERSON> p<PERSON>", "verify-passkey.cta": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "verify-passkey.subtitle": "<PERSON><PERSON><PERSON><PERSON>, kad tavo prieigos raktas yra sukurtas ir tinkamai apsaugotas.", "verify-passkey.title": "Patvirtin<PERSON> prieigo<PERSON> rak<PERSON>", "view-cashback.cashback-next-cycle": "Cashback norma po {time}", "view-cashback.no-cashback": "0 %", "view-cashback.no-cashback.subtitle": "Įnešk l<PERSON><PERSON><PERSON>, kad gautum cashback", "view-cashback.pending": "{money} <PERSON><PERSON><PERSON>", "view-cashback.pending-rewards.not_paid": "<PERSON><PERSON>i po {days} d.", "view-cashback.pending-rewards.paid": "<PERSON><PERSON><PERSON> sa<PERSON>", "view-cashback.received-rewards": "<PERSON><PERSON><PERSON>", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Nepatvirtin<PERSON>", "view-cashback.upcoming": "<PERSON><PERSON><PERSON><PERSON> {money}", "virtual-card-order.configure-safe.loading-text": "<PERSON><PERSON><PERSON>", "virtual-card-order.create-order.loading-text": "Aktyvina<PERSON> k<PERSON>", "virtual-card-order.create-order.success-text": "Kortelė aktyvinta", "virtualCard.activateCard": "Aktyvinti kortelę", "walletDeleteConfirm.main_action": "<PERSON><PERSON><PERSON><PERSON>", "walletDeleteConfirm.subtitle": "Norėdamas matyti portfelį ar atlikti operacijas, tur<PERSON><PERSON> ją import<PERSON>ti i<PERSON> naujo.", "walletDeleteConfirm.title": "<PERSON><PERSON>lint<PERSON> pinigi<PERSON>ę?", "walletSetting.header": "Pinigin<PERSON><PERSON> nustat<PERSON>", "wallet_connect.connect.cancel": "<PERSON><PERSON><PERSON><PERSON>", "wallet_connect.connect.connect_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wallet_connect.connect.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wallet_connect.connected.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wallet_connect_add_chain_missing.title": "<PERSON><PERSON><PERSON>", "wallet_connect_proposal_expired.title": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withdraw.confirmation.close": "<PERSON><PERSON><PERSON><PERSON>", "withdraw.confirmation.continue": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal_request.completed": "Užbaigta", "withdrawal_request.pending": "Vykdoma", "zeal-dapp.connect-wallet.cta.primary.connecting": "Jung<PERSON><PERSON>...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.cta.secondary": "<PERSON><PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.title": "Nor<PERSON><PERSON><PERSON>, prijunk piniginę", "zealSmartWalletInfo.gas": "Mokėk tinklo mokestį daugeliu žetonų; naudok populiarius ERC20 žetonus palaikomuose tinkluose, o ne tik vietinius žetonus.", "zealSmartWalletInfo.recover": "Jokių slaptųjų frazių; atkurk naudodamas biometrinį passkey iš savo slaptažodžių tvarkyklės, iCloud ar Google paskyros.", "zealSmartWalletInfo.selfCustodial": "Visiškai privati piniginė; Passkey parašai tvir<PERSON>mi tinkle, siekiant sumažinti centralizuotas priklausomybes.", "zealSmartWalletInfo.title": "Apie Zeal Smart Wallets", "zeal_a_rewards_already_claimed_error.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "zwidget.minimizedDisconnected.label": "Zeal atjungtas"}