{"Account.ListItem.details.label": "Detalles", "AddFromAddress.success": "<PERSON><PERSON><PERSON> guardado", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{Ning<PERSON> monedero} one{{count} monedero} other{{count} monederos}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Frase secreta {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Crea nuevos monederos desde una de tus frases secretas", "AddFromExistingSecretPhrase.SelectPhrase.title": "Elige una frase secreta", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "Tu frase secreta respalda muchos monederos. Elige los que quieras usar.", "AddFromExistingSecretPhrase.WalletSelection.title": "<PERSON><PERSON><PERSON>", "AddFromExistingSecretPhrase.success": "<PERSON><PERSON><PERSON> añadidos a Zeal", "AddFromHardwareWallet.subtitle": "Selecciona tu monedero físico para conectarlo a Zeal", "AddFromHardwareWallet.title": "Monedero físico", "AddFromNewSecretPhrase.WalletSelection.subtitle": "Selecciona los monederos que quieres importar", "AddFromNewSecretPhrase.WalletSelection.title": "Importar monederos", "AddFromNewSecretPhrase.accounts": "<PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "Una frase secreta funciona como un llavero para millones de monederos, cada uno con su clave privada única.{br}{br}Puedes importar tantos monederos como necesites ahora o añadir más después.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Monederos de frase secreta", "AddFromNewSecretPhrase.subtitle": "Introduce tu frase secreta con las palabras separadas por espacios", "AddFromNewSecretPhrase.success_secret_phrase_added": "Frase secreta añadida 🎉", "AddFromNewSecretPhrase.success_wallets_added": "<PERSON><PERSON><PERSON> añadidos a Zeal", "AddFromNewSecretPhrase.wallets": "<PERSON><PERSON><PERSON>", "AddFromPrivateKey.subtitle": "Introduce tu clave privada", "AddFromPrivateKey.success": "Clave privada añadida 🎉", "AddFromPrivateKey.title": "<PERSON><PERSON><PERSON>", "AddFromPrivateKey.typeOrPaste": "Escribe o pega aquí", "AddFromSecretPhrase.importWallets": "{count,plural,=0{Ningún monedero elegido} one{Importar monedero} other{Importar {count} monederos}}", "AddFromTrezor.AccountSelection.title": "Importar monederos de Trezor", "AddFromTrezor.hwWalletTip.subtitle": "Un monedero hardware contiene millones de monederos con direcciones diferentes. Puedes importar tantos monederos como necesites ahora o añadir más después.", "AddFromTrezor.hwWalletTip.title": "Importar desde monederos hardware", "AddFromTrezor.importAccounts": "{count,plural,=0{Ningún monedero selecc.} one{Importar monedero} other{Importar {count} wallets}}", "AddFromTrezor.success": "<PERSON><PERSON><PERSON> añadidos a Zeal", "ApprovalSpenderTypeCheck.failed.subtitle": "Posible estafa: quien gasta debe ser un contrato", "ApprovalSpenderTypeCheck.failed.title": "Quien gasta es un monedero, no un contrato", "ApprovalSpenderTypeCheck.passed.subtitle": "Normalmente, apruebas activos para contratos", "ApprovalSpenderTypeCheck.passed.title": "Quien gasta es un contrato inteligente", "BestReturns.subtitle": "Este proveedor de swap te dará el mayor rendimiento, incluyendo todas las comisiones.", "BestReturnsPopup.title": "<PERSON><PERSON><PERSON> re<PERSON>", "BlacklistCheck.Failed.subtitle": "Denuncias de actividad maliciosa por <source></source>", "BlacklistCheck.Failed.title": "Sitio en lista negra", "BlacklistCheck.Passed.subtitle": "Sin denuncias de actividad maliciosa por <source></source>", "BlacklistCheck.Passed.title": "Sitio no está en lista negra", "BlacklistCheck.failed.statusButton.label": "Sitio web denunciado", "BridgeRoute.slippage": "Slippage {slippage}", "BridgeRoute.title": "Prove<PERSON><PERSON> de bridge", "CheckConfirmation.InProgress": "En curso...", "CheckConfirmation.success.splash": "Completado", "ChooseImportOrCreateSecretPhrase.subtitle": "Importa una frase secreta o crea una nueva", "ChooseImportOrCreateSecretPhrase.title": "<PERSON><PERSON><PERSON> frase secreta", "ConfirmTransaction.Simuation.Skeleton.title": "Realizando comprobaciones de seguridad…", "ConnectionSafetyCheckResult.passed": "Análisis de seguridad superado", "ContactGnosisPaysupport": "<PERSON><PERSON> con Gnosis Pay", "CopyKeyButton.copied": "Copiado", "CopyKeyButton.copyYourKey": "Copia tu clave", "CopyKeyButton.copyYourPhrase": "Copia tu frase", "DAppVerificationCheck.Failed.subtitle": "El sitio no aparece en <source></source>", "DAppVerificationCheck.Failed.title": "Sitio no encontrado en registros de apps", "DAppVerificationCheck.Passed.subtitle": "El sitio aparece en <source></source>", "DAppVerificationCheck.Passed.title": "El sitio aparece en registros de apps", "DAppVerificationCheck.failed.statusButton.label": "Sitio no encontrado en registros de apps", "ERC20.tokens.emptyState": "No hemos encontrado tokens", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Comisión de prioridad", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Últimos {period}: entre {from} y {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Comisión base: {baseFee} • Margen de seguridad: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "No pudimos obtener la comisión base actual", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Nonce superior al siguiente. Se atascará.", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "El nonce no puede ser inferior al actual", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "No hemos podido calcular la comisión de prioridad", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "No pudimos obtener la comisión máxima actual", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "Comisión máxima", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Podría atascarse hasta que bajen las comisiones", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "Comisión base máxima", "EditFeeModal.Custom.gasLimit.title": "Límite de gas {gasLimit}", "EditFeeModal.Custom.title": "<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Se atascará hasta que baje la comisión base", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Comisión baja. Podría atascarse.", "EditFeeModal.EditGasLimit.estimatedGas": "Gas est.: {estimated} • Margen de seguridad: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Inferior al límite estimado. La transacción fallará", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Inferior al límite sugerido. La transacción podría fallar", "EditFeeModal.EditGasLimit.subtitle": "Define la cantidad máxima de gas para esta transacción. Si el límite es inferior al necesario, la transacción fallará", "EditFeeModal.EditGasLimit.title": "Editar límite de gas", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Inferior al límite mínimo de gas: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "Nonce superior al siguiente. Se atascará", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "No puedes usar un nonce inferior al actual", "EditFeeModal.EditNonce.subtitle": "Tu transacción se atascará si no usas el siguiente nonce", "EditFeeModal.EditNonce.title": "<PERSON><PERSON> nonce", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "Necesitas {amount} para enviar", "EditFeeModal.Header.Time.unknown": "Tiempo desconocido", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Máx. {fee}", "EditFeeModal.Header.fee.unknown": "Comisión desconocida", "EditFeeModal.Header.subsequent_failed": "Las estimaciones pueden ser antiguas, falló la última actualización", "EditFeeModal.Layout.Header.ariaLabel": "Comisión actual", "EditFeeModal.MaxFee.subtitle": "La comisión máxima es lo máximo que pagarás por una transacción, pero normalmente pagarás la comisión prevista. Este margen extra ayuda a que tu transacción se procese, aunque la red se ralentice o se encarezca.", "EditFeeModal.MaxFee.title": "Comisión de red máxima", "EditFeeModal.SelectPreset.Time.unknown": "Tiempo desconocido", "EditFeeModal.SelectPreset.ariaLabel": "Seleccionar comisión predefinida", "EditFeeModal.SelectPreset.fast": "<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.SelectPreset.normal": "Normal", "EditFeeModal.SelectPreset.slow": "<PERSON><PERSON>", "EditFeeModal.ariaLabel": "Editar comisión de red", "FailedSimulation.Confirmation.Item.subtitle": "Hemos tenido un error interno", "FailedSimulation.Confirmation.Item.title": "No se pudo simular la transacción", "FailedSimulation.Confirmation.subtitle": "¿Seguro que quieres continuar?", "FailedSimulation.Confirmation.title": "Estás firmando a ciegas", "FailedSimulation.Title": "Error de simulación", "FailedSimulation.footer.subtitle": "Hemos tenido un error interno", "FailedSimulation.footer.title": "No se pudo simular la transacción", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Necesitas {amount} para enviar la transacción", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Podría tardar en procesarse", "FeeForecastWidget.networkFee": "Comisión de red", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "No pudimos calcular la comisión de red", "FeeForecastWidget.subsequentFailed.message": "Las estimaciones podrían ser antiguas, falló la última actualización", "FeeForecastWidget.unknownDuration": "Desconocido", "FeeForecastWidget.unknownFee": "Desconocido", "GasCurrencySelector.balance": "Saldo: {balance}", "GasCurrencySelector.networkFee": "Comisión de red", "GasCurrencySelector.payNetworkFeesUsing": "Pagar comisiones de red con", "GasCurrencySelector.removeDefaultGasToken.description": "Paga las comisiones desde el saldo mayor", "GasCurrencySelector.removeDefaultGasToken.title": "Gestión automática de comisiones", "GasCurrencySelector.save": "Guardar", "GoogleDriveBackup.BeforeYouBegin.first_point": "Si olvido mi contraseña de Zeal, perderé mis activos para siempre", "GoogleDriveBackup.BeforeYouBegin.second_point": "Si pierdo el acceso a Google Drive o modifico mi archivo de recuperación, perderé mis activos para siempre", "GoogleDriveBackup.BeforeYouBegin.subtitle": "<PERSON> y acepta lo siguiente sobre la autocustodia:", "GoogleDriveBackup.BeforeYouBegin.third_point": "Zeal no puede ayudarme a recuperar mi contraseña de Zeal ni mi acceso a Google Drive", "GoogleDriveBackup.BeforeYouBegin.title": "<PERSON><PERSON> em<PERSON>zar", "GoogleDriveBackup.loader.subtitle": "Aprueba la solicitud en Google Drive para subir tu archivo de recuperación", "GoogleDriveBackup.loader.title": "Esperando aprobación...", "GoogleDriveBackup.success": "Copia de seguridad correcta 🎉", "MonitorOffRamp.overServiceTime": "La mayoría de las transferencias se completan en {estimated_time}, pero a veces pueden tardar más debido a comprobaciones adicionales. Es normal, y los fondos están seguros mientras se realizan.{br}{br}Si la transacción no se completa en {support_soft_deadline}, por favor, {contact_support}", "MonitorOnRamp.contactSupport": "Contactar con soporte", "MonitorOnRamp.from": "<PERSON><PERSON>", "MonitorOnRamp.fundsReceived": "Fondos recibidos", "MonitorOnRamp.overServiceTime": "La mayoría de transferencias se completan en {estimated_time}, pero a veces pueden tardar más por comprobaciones adicionales. Es normal y tus fondos están a salvo durante el proceso.{br}{br}Si la transacción no se completa en {support_soft_deadline}, por favor, {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Enviando a tu monedero", "MonitorOnRamp.to": "A", "MonitorOnRamp.waitingForTransfer": "Esperando a que transfieras los fondos", "NftCollectionCheck.failed.subtitle": "La colección no está verificada en <source></source>", "NftCollectionCheck.failed.title": "Colección no verificada", "NftCollectionCheck.passed.subtitle": "La colección está verificada en <source></source>", "NftCollectionCheck.passed.title": "Colección verificada", "NftCollectionInfo.entireCollection": "Colección completa", "NoSigningKeyStore.createAccount": "<PERSON><PERSON><PERSON> cuenta", "NonceRangeError.biggerThanCurrent.message": "La transacción se atascará", "NonceRangeError.lessThanCurrent.message": "La transacción fallará", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "El nonce es superior al actual. Redúcelo para evitar que la transacción se atasque.", "NonceRangeErrorPopup.biggerThanCurrent.title": "La transacción se atascará", "P2pReceiverTypeCheck.failed.subtitle": "¿Estás enviando a la dirección correcta?", "P2pReceiverTypeCheck.failed.title": "El destinatario es un contrato, no un monedero", "P2pReceiverTypeCheck.passed.subtitle": "Normalmente, envías activos a otros monederos", "P2pReceiverTypeCheck.passed.title": "El destinatario es un monedero", "PasswordCheck.title": "Introduce la contraseña", "PasswordChecker.subtitle": "Introduce tu contraseña para verificar que eres tú", "PermitExpirationCheck.failed.subtitle": "Que sea breve y solo por el tiempo necesario", "PermitExpirationCheck.failed.title": "Tiempo de caducidad largo", "PermitExpirationCheck.passed.subtitle": "Tiempo que una app puede usar tus tokens", "PermitExpirationCheck.passed.title": "Tiempo de caducidad no muy largo", "PrivateKeyValidationError.moreThanMaximumWords": "Máx. {count} palabras", "PrivateKeyValidationError.notValidPrivateKey": "Esta no es una clave privada válida", "PrivateKeyValidationError.secretPhraseIsInvalid": "La frase secreta no es válida", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Palabra #{index} mal escrita o no válida", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} palabra} other{{count} palabras}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Las frases secretas y las claves privadas se cifran y nunca salen de este dispositivo", "SecretPhraseReveal.header": "Apunta la frase secreta", "SecretPhraseReveal.hint": "No compartas tu frase con nadie. Guárdala en un lugar seguro y sin conexión", "SecretPhraseReveal.skip.subtitle": "<PERSON><PERSON><PERSON> hacerlo más tarde, pero si pierdes este dispositivo antes de apuntar tu frase, perderás todos los activos de este monedero", "SecretPhraseReveal.skip.takeTheRisk": "Me arriesgo", "SecretPhraseReveal.skip.title": "¿Omitir apuntar la frase?", "SecretPhraseReveal.skip.writeDown": "Apuntar", "SecretPhraseReveal.skipForNow": "<PERSON><PERSON><PERSON>", "SecretPhraseReveal.subheader": "Por favor, ap<PERSON><PERSON><PERSON> y guárdala en un lugar seguro sin conexión. Después te pediremos que la verifiques.", "SecretPhraseReveal.verify": "Verificar", "SelectCurrency.tokens": "Tokens", "SelectCurrency.tokens.emptyState": "No hemos encontrado tokens", "SelectRoute.slippage": "Slippage {slippage}", "SelectRoutes.emptyState": "No hemos encontrado rutas para este swap", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Conectar monedero", "SendERC20.labelAddress.inputPlaceholder": "Etiqueta del monedero", "SendERC20.labelAddress.subtitle": "Etiqueta este monedero para encontrarlo más tarde.", "SendERC20.labelAddress.title": "Etiquetar este monedero", "SendERC20.send_to": "Enviar a", "SendERC20.tokens": "Tokens", "SendOrReceive.bankTransfer.primaryText": "Transferencia bancaria", "SendOrReceive.bankTransfer.shortText": "Compra y venta de cripto, gratis e instantánea", "SendOrReceive.bridge.primaryText": "Bridge", "SendOrReceive.bridge.shortText": "Transfiere tokens entre redes", "SendOrReceive.receive.primaryText": "Recibir", "SendOrReceive.receive.shortText": "Recibe tokens o coleccionables", "SendOrReceive.send.primaryText": "Enviar", "SendOrReceive.send.shortText": "Envía tokens a cualquier dirección", "SendOrReceive.swap.primaryText": "Intercambiar", "SendOrReceive.swap.shortText": "Intercambia entre tokens", "SendSafeTransaction.Confirm.loading": "Realizando análisis de seguridad...", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Cifra un archivo de recuperación con contraseña", "SetupRecoveryKit.google.subtitle": "Sincronizado {date}", "SetupRecoveryKit.google.title": "Copia en Google Drive", "SetupRecoveryKit.subtitle": "Necesitarás al menos una forma de restaurar tu cuenta si desinstalas Zeal o cambias de dispositivo", "SetupRecoveryKit.title": "Configurar kit de recuperación", "SetupRecoveryKit.writeDown.subtitle": "Apunta la frase secreta", "SetupRecoveryKit.writeDown.title": "Copia de seguridad manual", "Sign.CheckSafeDeployment.activate": "Activar", "Sign.CheckSafeDeployment.subtitle": "Activa tu dispositivo en esta red para iniciar sesión en apps o firmar mensajes. Esto es necesario después de instalar o recuperar un Smart Wallet.", "Sign.CheckSafeDeployment.title": "Activar dispositivo en esta red", "Sign.Simuation.Skeleton.title": "Realizando análisis de seguridad...", "SignMessageSafetyCheckResult.passed": "Análisis de seguridad superado", "SignMessageSafetyChecksPopup.title.permits": "Análisis de seguridad de permisos", "SimulationFailedConfirmation.subtitle": "Hemos simulado esta transacción y detectado un problema que la haría fallar. Puedes enviarla, pero es probable que falle y pierdas la comisión de red.", "SimulationFailedConfirmation.title": "Es probable que la transacción falle", "SimulationNotSupported.Title": "Simulación no{br}soportada en{br}{network}", "SimulationNotSupported.footer.subtitle": "Aún puedes enviar esta transacción", "SimulationNotSupported.footer.title": "Simulación no soportada", "SlippagePopup.custom": "Personalizado", "SlippagePopup.presetsHeader": "Slippage del swap", "SlippagePopup.title": "Ajustes de slippage", "SmartContractBlacklistCheck.failed.subtitle": "Informes maliciosos de <source></source>", "SmartContractBlacklistCheck.failed.title": "El contrato está en una lista negra", "SmartContractBlacklistCheck.passed.subtitle": "Sin informes maliciosos de <source></source>", "SmartContractBlacklistCheck.passed.title": "El contrato no está en una lista negra", "SuspiciousCharactersCheck.Failed.subtitle": "Es una táctica de phishing habitual", "SuspiciousCharactersCheck.Failed.title": "Buscamos patrones de phishing comunes", "SuspiciousCharactersCheck.Passed.subtitle": "Comprobamos si hay intentos de phishing", "SuspiciousCharactersCheck.Passed.title": "La dirección no tiene caracteres inusuales", "SuspiciousCharactersCheck.failed.statusButton.label": "La dirección tiene caracteres inusuales ", "TokenVerificationCheck.failed.subtitle": "El token no está listado en <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} no está verificado por CoinGecko", "TokenVerificationCheck.passed.subtitle": "El token está listado en <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} está verificado por CoinGecko", "TopupDapp.MonitorTransaction.success.splash": "Completado", "TransactionSafetyCheckResult.passed": "Análisis de seguridad superado", "TransactionSimulationCheck.failed.subtitle": "Error: {errorMessage}", "TransactionSimulationCheck.failed.title": "Es probable que la transacción falle", "TransactionSimulationCheck.passed.subtitle": "Simulación realizada con <source></source>", "TransactionSimulationCheck.passed.title": "Previsualización de la transacción exitosa", "TrezorError.trezor_action_cancelled.action": "<PERSON><PERSON><PERSON>", "TrezorError.trezor_action_cancelled.subtitle": "Has rechazado la transacción en tu monedero físico", "TrezorError.trezor_device_used_elsewhere.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "Cierra las demás sesiones abiertas y vuelve a sincronizar tu Trezor", "TrezorError.trezor_method_cancelled.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "Asegúrate de permitir que Trezor exporte monederos a Zeal", "TrezorError.trezor_permissions_not_granted.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "Da permiso a Zeal para ver todos los monederos", "TrezorError.trezor_pin_cancelled.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "Sesión cancelada en el dispositivo", "TrezorError.trezor_popup_closed.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "El diálogo de Trezor se cerró inesperadamente", "TrxLikelyToFail.lessThanEstimatedGas.message": "La transacción fallará", "TrxLikelyToFail.lessThanMinimumGas.message": "La transacción fallará", "TrxLikelyToFail.lessThanSuggestedGas.message": "Es probable que falle", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "El límite de gas de la transacción es demasiado bajo. Auméntalo al límite sugerido para evitar que falle.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "Es probable que la transacción falle", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "El límite de gas es inferior al estimado. Aumenta el límite de gas al valor sugerido.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "La transacción fallará", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "La comisión base máxima es inferior a la actual. Auméntala para evitar que la transacción se atasque.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "La transacción se atascará", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "La comisión máxima de la transacción es demasiado baja. Auméntala para evitar que la transacción se atasque.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "La transacción se atascará", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "La comisión de prioridad es inferior a la recomendada. Auméntala para acelerar la transacción.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "La transacción podría tardar en completarse", "UnsupportedMobileNetworkLayout.gotIt": "¡Entendido!", "UnsupportedMobileNetworkLayout.subtitle": "No puedes hacer transacciones ni firmar mensajes en la red con ID {networkHexId} con la versión móvil de Zeal todavía.{br}{br}Usa la extensión de navegador para operar en esta red mientras trabajamos para añadirla 🚀", "UnsupportedMobileNetworkLayout.title": "Red no compatible con la versión móvil de Zeal", "UnsupportedSafeNetworkLayout.subtitle": "No puedes realizar transacciones ni firmar mensajes en {network} con un Smart Wallet de Zeal{br}{br}Cambia a una red compatible o usa un monedero Legacy.", "UnsupportedSafeNetworkLayoutk.title": "Red no compatible con Smart Wallet", "UserConfirmationPopup.goBack": "<PERSON><PERSON><PERSON>", "UserConfirmationPopup.submit": "Enviar igual", "ViewPrivateKey.header": "Clave privada", "ViewPrivateKey.hint": "No compartas tu clave privada con nadie. Guárdala en un lugar seguro y sin conexión.", "ViewPrivateKey.subheader.mobile": "Toca para ver tu clave privada", "ViewPrivateKey.subheader.web": "Pasa el cursor para ver tu clave privada", "ViewPrivateKey.unblur.mobile": "Toca para ver", "ViewPrivateKey.unblur.web": "Pasa el cursor para ver", "ViewSecretPhrase.PasswordChecker.subtitle": "Introduce tu contraseña para cifrar el archivo de recuperación. Tendrás que recordarla en el futuro.", "ViewSecretPhrase.done": "<PERSON><PERSON>", "ViewSecretPhrase.header": "Frase secreta", "ViewSecretPhrase.hint": "No compartas tu frase con nadie. Guárdala en un lugar seguro y sin conexión.", "ViewSecretPhrase.subheader.mobile": "Toca para ver tu frase secreta", "ViewSecretPhrase.subheader.web": "Pasa el cursor para ver tu frase secreta", "ViewSecretPhrase.unblur.mobile": "Toca para ver", "ViewSecretPhrase.unblur.web": "Pasa el cursor para ver", "account-details.monerium": "Las transferencias se realizan con Monerium, una EMI autorizada y regulada. <link>Saber más</link>", "account-details.unblock": "Las transferencias se realizan a través de Unblock, un proveedor de servicios de cambio y custodia autorizado y registrado. <link>Saber más</link>", "account-selector.empty-state": "No se encontraron monederos", "account-top-up.select-currency.title": "Tokens", "account.accounts_not_found": "No hemos encontrado ningún monedero", "account.accounts_not_found_search_valid_address": "El monedero no está en tu lista", "account.add.create_new_secret_phrase": "Crear frase secreta", "account.add.create_new_secret_phrase.subtext": "Una nueva frase secreta de 12 palabras", "account.add.fromRecoveryKit.fileNotFound": "No hemos encontrado tu archivo", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "<PERSON>ver a intentar", "account.add.fromRecoveryKit.fileNotFound.explanation": "Asegúrate de que estás en la cuenta con la carpeta de copia de seguridad de Zeal.", "account.add.fromRecoveryKit.fileNotValid": "El archivo de recuperación no es válido", "account.add.fromRecoveryKit.fileNotValid.explanation": "Hemos revisado tu archivo y no es del tipo correcto o ha sido modificado.", "account.add.import_secret_phrase": "Importar frase secreta", "account.add.import_secret_phrase.subtext": "<PERSON><PERSON><PERSON> en Zeal, Metamask u otros", "account.add.select_type.add_hardware_wallet": "Monedero hardware", "account.add.select_type.existing_smart_wallet": "Smart Wallet existente", "account.add.select_type.private_key": "Clave privada", "account.add.select_type.seed_phrase": "<PERSON><PERSON>lla", "account.add.select_type.title": "Importar monedero", "account.add.select_type.zeal_recovery_file": "Archivo de recuperación de Zeal", "account.add.success.title": "Nuevo monedero creado 🎉", "account.addLabel.header": "Ponle un nombre a tu monedero", "account.addLabel.labelError.labelAlreadyExist": "El nombre ya existe. Prueba con otro.", "account.addLabel.labelError.maxStringLengthExceeded": "Límite de caracteres alcanzado", "account.add_active_wallet.primary_text": "<PERSON><PERSON><PERSON>", "account.add_active_wallet.short_text": "<PERSON><PERSON>, conecta o importa un monedero", "account.add_from_ledger.success": "<PERSON><PERSON><PERSON> añadidos a Zeal", "account.add_tracked_wallet.primary_text": "<PERSON><PERSON><PERSON> monedero de solo lectura", "account.add_tracked_wallet.short_text": "Ver portafolio y actividad", "account.button.unlabelled-wallet": "Monedero sin etiqueta", "account.create_wallet": "<PERSON><PERSON><PERSON> mon<PERSON>", "account.label.edit.title": "Editar nombre del monedero", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON> {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "El archivo de recuperación no es válido", "account.recoveryKit.selectBackupFile.subtitle": "Selecciona el archivo de recuperación que quieres restaurar", "account.recoveryKit.selectBackupFile.title": "Archivo de recuperación", "account.recoveryKit.success.recoveryFileFound": "Archivo de recuperación encontrado 🎉", "account.select_type_of_account.create_eoa.short": "Monedero tradicional para expertos", "account.select_type_of_account.create_eoa.title": "<PERSON><PERSON><PERSON> monedero con frase semilla", "account.select_type_of_account.create_safe_wallet.title": "Crear Smart Wallet", "account.select_type_of_account.existing_smart_wallet": "Smart Wallet existente", "account.select_type_of_account.hardware_wallet": "Monedero de hardware", "account.select_type_of_account.header": "<PERSON><PERSON><PERSON>", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "<PERSON><PERSON><PERSON> p<PERSON> / <PERSON><PERSON> semilla", "account.select_type_of_account.read_only_wallet": "Monedero de solo lectura", "account.select_type_of_account.read_only_wallet.short": "Previsualiza cualquier portafolio", "account.topup.title": "<PERSON><PERSON><PERSON> a Zeal", "account.view.error.refreshAssets": "Actualizar", "account.widget.refresh": "Actualizar", "account.widget.settings": "<PERSON><PERSON><PERSON><PERSON>", "accounts.view.copied-text": "Copiado {formattedAddress}", "accounts.view.copiedAddress": "Copiado {formattedAddress}", "action.accept": "Aceptar", "action.accpet": "Aceptar", "action.allow": "<PERSON><PERSON><PERSON>", "action.back": "Atrás", "action.cancel": "<PERSON><PERSON><PERSON>", "action.card-activation.title": "Activar tarjeta", "action.claim": "<PERSON><PERSON><PERSON><PERSON>", "action.close": "<PERSON><PERSON><PERSON>", "action.complete-steps": "Completar", "action.confirm": "Confirmar", "action.continue": "<PERSON><PERSON><PERSON><PERSON>", "action.copy-address-understand": "OK, copiar dirección", "action.deposit": "Ingresar", "action.done": "<PERSON><PERSON>", "action.dontAllow": "No permitir", "action.edit": "editar", "action.email-required": "Introduce tu email", "action.enterPhoneNumber": "Introduce tu teléfono", "action.expand": "Expandir", "action.fix": "<PERSON><PERSON><PERSON><PERSON>", "action.getStarted": "Empezar", "action.got_it": "Entendido", "action.hide": "Ocultar", "action.import": "Importar", "action.import-keys": "Importar claves", "action.importKeys": "Importar claves", "action.minimize": "<PERSON><PERSON><PERSON>", "action.next": "Siguient<PERSON>", "action.ok": "OK", "action.reduceAmount": "Reducir al máximo", "action.refreshWebsite": "Actualizar web", "action.remove": "Eliminar", "action.remove-account": "Eliminar cuenta", "action.requestCode": "Solicitar código", "action.resend_code": "Reenviar código", "action.resend_code_with_time": "<PERSON><PERSON><PERSON><PERSON> có<PERSON> {time}", "action.retry": "Reintentar", "action.reveal": "Mostrar", "action.save": "Guardar", "action.save_changes": "Guardar RPC", "action.search": "Buscar", "action.seeAll": "Ver todo", "action.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.send": "Enviar", "action.skip": "<PERSON><PERSON><PERSON>", "action.submit": "Enviar", "action.understood": "Entendido", "action.update": "Actualizar", "action.update-gnosis-pay-owner.complete": "Completar", "action.zeroAmount": "Introduce un importe", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Coleccionables", "action_bar_title.tokens": "Tokens", "action_bar_title.transaction_request": "Solicitud de transacción", "activate-monerium.loading": "Configurando tu cuenta personal", "activate-monerium.success.title": "Monerium activado", "activate-physical-card-widget.subtitle": "La entrega puede tardar 3 semanas", "activate-physical-card-widget.title": "Activar tarjeta física", "activate-smart-wallet.title": "Activar monedero", "active_and_tracked_wallets.title": "Zeal cubre todas tus comisiones en {network}, ¡para que operes gratis!", "activity.approval-amount.revoked": "Revocado", "activity.approval-amount.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "activity.approval.approved_for": "Aprobado para", "activity.approval.approved_for_with_target": "Aprobado {approvedTo}", "activity.approval.revoked_for": "Revocado para", "activity.bank.serviceProvider": "<PERSON><PERSON><PERSON><PERSON> de servicios", "activity.bridge.serviceProvider": "<PERSON><PERSON><PERSON><PERSON> de servicios", "activity.cashback.period": "Periodo de cashback", "activity.filter.card": "Tarjeta", "activity.rate": "Tasa de cambio", "activity.receive.receivedFrom": "Recibido de", "activity.send.sendTo": "Enviado a", "activity.smartContract.unknown": "Contrato desconocido", "activity.smartContract.usingContract": "<PERSON>ando contrato", "activity.subtitle.pending_timer": "{timerString} Pendiente", "activity.title.arbitrary_smart_contract_interaction": "{function} en {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Interacción con contrato desconocido", "activity.title.bridge.from": "Bridge desde {token}", "activity.title.bridge.to": "Bridge a {token}", "activity.title.buy": "Compra de {asset}", "activity.title.card_owners_updated": "Titulares de la tarjeta actualizados", "activity.title.card_spend_limit_updated": "Límite de gasto de la tarjeta establecido", "activity.title.cashback_deposit": "<PERSON><PERSON><PERSON><PERSON> en Cashback", "activity.title.cashback_reward": "Cashback recibido", "activity.title.cashback_withdraw": "Retirada de Cashback", "activity.title.claimed_reward": "Recompensa reclamada", "activity.title.deployed_smart_wallet_gnosis": "<PERSON><PERSON>ta creada", "activity.title.deposit_from_bank": "Depósito desde el banco", "activity.title.deposit_into_card": "Depósito en tarjeta", "activity.title.deposit_into_earn": "<PERSON><PERSON><PERSON><PERSON> en {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} en {smartContract}", "activity.title.from": "De {sender}", "activity.title.pendidng_areward_claim": "Reclamando recompensa", "activity.title.pendidng_breward_claim": "Reclamando recompensa", "activity.title.recharge_disabledh": "Recarga de tarjeta desactivada", "activity.title.recharge_set": "Objetivo de recarga establecido", "activity.title.recovered_smart_wallet_gnosis": "Instalación de nuevo dispositivo", "activity.title.send_pending": "A {receiver}", "activity.title.send_to_bank": "Al banco", "activity.title.swap": "Compra de {token}", "activity.title.to": "A {receiver}", "activity.title.withdraw_from_card": "Retirada de la tarjeta", "activity.title.withdraw_from_earn": "<PERSON><PERSON><PERSON> de {earn}", "activity.transaction.networkFees": "Comisiones de red", "activity.transaction.state": "Transacción completada", "activity.transaction.state.completed": "Transacción completada", "activity.transaction.state.failed": "Transacción fallida", "add-account.section.import.header": "Importar", "add-another-card-owner": "<PERSON><PERSON><PERSON> de tarjeta", "add-another-card-owner.Recommended.footnote": "<PERSON><PERSON><PERSON> tu monedero Zeal como titular adicional", "add-another-card-owner.Recommended.primaryText": "<PERSON><PERSON><PERSON> a Gnosis Pay", "add-another-card-owner.recommended": "Recomendado", "add-owner.confirmation.subtitle": "Por seguridad, los cambios en la configuración tardan 3 minutos. Durante ese tiempo, tu tarjeta estará bloqueada y no podrás hacer pagos.", "add-owner.confirmation.title": "Tu tarjeta se bloqueará 3 min mientras se actualizan los ajustes", "add-readonly-signer-if-not-exist.error.already_in_use.title": "<PERSON><PERSON><PERSON> en uso, no se puede añadir", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "<PERSON>bar otro monedero", "add.account.backup.decrypt.success": "<PERSON><PERSON><PERSON>au<PERSON>", "add.account.backup.password.passwordIncorrectMessage": "La contraseña es incorrecta", "add.account.backup.password.subtitle": "Introduce la contraseña que usaste para cifrar tu archivo de recuperación", "add.account.backup.password.title": "Introduce la contraseña", "add.account.google.login.subtitle": "Aprueba la solicitud en Google Drive para sincronizar tu archivo de recuperación", "add.account.google.login.title": "Esperando aprobación...", "add.readonly.already_added": "<PERSON><PERSON><PERSON> ya añadido", "add.readonly.continue": "<PERSON><PERSON><PERSON><PERSON>", "add.readonly.empty": "Introduce una dirección o ENS", "addBankRecipient.title": "<PERSON><PERSON><PERSON> destinatar<PERSON> bancar<PERSON>", "add_funds.deposit_from_bank_account": "Ingresar desde una cuenta bancaria", "add_funds.from_another_wallet": "<PERSON><PERSON> o<PERSON> monedero", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Conectar a la dApp de recarga", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Conecta cualquier monedero a la dApp de recarga de Zeal y envía fondos rápidamente a tu monedero", "add_funds.from_crypto_wallet.header": "<PERSON><PERSON> o<PERSON> monedero", "add_funds.from_crypto_wallet.header.show_wallet_address": "Mostrar la dirección de tu monedero", "add_funds.from_exchange.header": "Enviar desde un exchange", "add_funds.from_exchange.header.copy_wallet_address": "Copiar tu dirección de Zeal", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance, etc.", "add_funds.from_exchange.header.open_exchange": "Abre la app o el sitio del exchange", "add_funds.from_exchange.header.selected_token": "Enviar {token} a Zeal", "add_funds.from_exchange.header.selected_token.subtitle": "En {network}", "add_funds.from_exchange.header.send_selected_token": "Enviar un token compatible", "add_funds.from_exchange.header.send_selected_token.subtitle": "Selecciona un token y red compatibles", "add_funds.import_wallet": "Importar un monedero existente", "add_funds.title": "<PERSON><PERSON><PERSON> fondos a tu cuenta", "add_funds.transfer_from_exchange": "Transferir desde un exchange", "address.add.header": "Ve tu monedero en Zeal{br}en modo de solo lectura", "address.add.subheader": "Introduce tu dirección o ENS para ver tus activos de todas las redes EVM en un solo lugar. Más tarde podrás crear o importar más monederos.", "address_book.change_account.bank_transfers.header": "Destinatarios <PERSON>", "address_book.change_account.bank_transfers.primary": "Destinatario bancario", "address_book.change_account.cta": "<PERSON><PERSON><PERSON>", "address_book.change_account.search_placeholder": "Añadir o buscar dirección", "address_book.change_account.tracked_header": "Monederos de solo lectura", "address_book.change_account.wallets_header": "Monederos activos", "app-association-check-failed.modal.cta": "Intentar de nuevo", "app-association-check-failed.modal.subtitle": "Vuelve a intentarlo. Los problemas de conexión están retrasando la obtención de tus Passkeys. Si el problema persiste, reinicia Zeal y prueba una vez más.", "app-association-check-failed.modal.subtitle.creation": "Vuelve a intentarlo. Los problemas de conexión están retrasando la creación de tu Passkey. Si el problema persiste, reinicia Zeal y prueba una vez más.", "app-association-check-failed.modal.title.creation": "Tu dispositivo no pudo crear la passkey", "app-association-check-failed.modal.title.signing": "Tu dispositivo no pudo cargar las passkeys", "app.app_protocol_group.borrowed_tokens": "Tokens prestados", "app.app_protocol_group.claimable_amount": "Cantidad reclamable", "app.app_protocol_group.health_rate": "Ratio de <PERSON>", "app.app_protocol_group.lending": "Préstamos", "app.app_protocol_group.locked_tokens": "Tokens bloqueados", "app.app_protocol_group.nfts": "Coleccionables", "app.app_protocol_group.reward_tokens": "Tokens de recompensa", "app.app_protocol_group.supplied_tokens": "Tokens aportados", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Token en adquisición", "app.appsGroupHeader.discoverMore": "Descubre más", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Busca o introduce una URL", "app.error-banner.cory": "<PERSON><PERSON><PERSON> datos del error", "app.error-banner.retry": "Reintentar", "app.list_item.rewards": "Recompensas {value}", "app.position_details.health_rate.description": "El factor de salud se calcula dividiendo el importe de tu préstamo entre el valor de tu garantía.", "app.position_details.health_rate.title": "¿Qué es el factor de salud?", "approval.edit-limit.label": "Editar límite de gasto", "approval.permit_info": "Información del permiso", "approval.spend-limit.edit-modal.cancel": "<PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.limit-label": "Límite de gasto", "approval.spend-limit.edit-modal.max-limit-error": "Atención, límite alto", "approval.spend-limit.edit-modal.revert": "Revertir cambios", "approval.spend-limit.edit-modal.set-to-unlimited": "Establecer como ilimitado", "approval.spend-limit.edit-modal.submit": "Guardar cambios", "approval.spend-limit.edit-modal.title": "<PERSON><PERSON> per<PERSON>", "approval.spend_limit_info": "¿Qué es el límite de gasto?", "approval.what_are_approvals": "¿Qué son las aprobaciones?", "apps_list.page.emptyState": "No hay aplicaciones activas", "backpace.removeLastDigit": "Eliminar últ<PERSON>", "backup-banner.backup_now": "Hacer copia", "backup-banner.risk_losing_funds": "Haz una copia o arriésgate a perder tus fondos", "backup-banner.title": "Monedero sin copia de seguridad", "backupRecoverySmartWallet.noExportPrivateKeys": "Copia de seguridad automática: tu Smart Wallet se guarda como una passkey, sin necesidad de frase secreta ni claves privadas.", "backupRecoverySmartWallet.safeContracts": "Seguridad multiclave: los monederos de Zeal usan contratos de Safe, por lo que varios dispositivos pueden aprobar una transacción. Sin un único punto de fallo.", "backupRecoverySmartWallet.security": "Varios dispositivos: puedes usar tu monedero en varios dispositivos con la passkey. Cada dispositivo obtiene su propia clave privada.", "backupRecoverySmartWallet.showLocalPrivateKey": "Modo experto: puedes exportar la clave privada de este dispositivo, usarla en otro monedero y conectarte en <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Mostrar clave privada</Key>", "backupRecoverySmartWallet.storingKeys": "Sincronizado en la nube: la passkey se guarda de forma segura en iCloud, el Gestor de contraseñas de Google o tu gestor de contraseñas.", "backupRecoverySmartWallet.title": "Copia de seguridad y recuperación del Smart Wallet", "balance-change.card.titile": "Tarjeta", "balanceChange.pending": "Pendiente", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "<PERSON><PERSON> de servicios", "bank-transfer.change-deposit-receiver.subtitle": "Este monedero recibirá todos los depósitos bancarios", "bank-transfer.change-deposit-receiver.title": "Elige monedero receptor", "bank-transfer.change-owner.subtitle": "Este monedero se usa para iniciar sesión y recuperar tu cuenta de transferencias bancarias", "bank-transfer.change-owner.title": "<PERSON><PERSON> monedero propietario", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Todos los depósitos bancarios que envíes a Zeal se recibirán en este monedero.", "bank-transfer.configrm-change-deposit-receiver.title": "Cambiar monedero receptor", "bank-transfer.configrm-change-owner.subtitle": "¿Seguro que quieres cambiar de monedero propietario? Este monedero se usa para iniciar sesión y recuperar tu cuenta de transferencias.", "bank-transfer.configrm-change-owner.title": "Cambiar monedero propietario", "bank-transfer.deposit.widget.status.complete": "Completado", "bank-transfer.deposit.widget.status.funds_received": "Fondos recibidos", "bank-transfer.deposit.widget.status.sending_to_wallet": "Enviando al monedero", "bank-transfer.deposit.widget.status.transfer-on-hold": "Transferencia en espera", "bank-transfer.deposit.widget.status.transfer-received": "Enviando al monedero", "bank-transfer.deposit.widget.subtitle": "{from} a {to}", "bank-transfer.deposit.widget.title": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.intro.bulletlist.point_1": "Configuración con Unblock", "bank-transfer.intro.bulletlist.point_2": "Transfiere entre EUR/GBP y más de 10 tokens", "bank-transfer.intro.bulletlist.point_3": "0 % de comisión hasta 5k $ mensuales, 0,2 % después", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Enviando al banco", "bank-transfer.withdrawal.widget.status.in-progress": "Realizando transferencia", "bank-transfer.withdrawal.widget.status.on-hold": "Transferencia en espera", "bank-transfer.withdrawal.widget.status.success": "Completada", "bank-transfer.withdrawal.widget.subtitle": "{from} a {to}", "bank-transfer.withdrawal.widget.title": "Re<PERSON><PERSON>", "bank-transfers.bank-account-actions.remove-this-account": "Eliminar esta cuenta", "bank-transfers.bank-account-actions.switch-to-this-account": "Cambiar a esta cuenta", "bank-transfers.deposit.fees-for-less-than-5k": "Comisiones para 5000 $ o menos", "bank-transfers.deposit.fees-for-more-than-5k": "Comisiones para más de 5000 $", "bank-transfers.set-receiving-bank.title": "Elige banco receptor", "bank-transfers.settings.account_owner": "Propietario de la cuenta", "bank-transfers.settings.receiver_of_bank_deposits": "Receptor de depósitos bancarios", "bank-transfers.settings.receiver_of_withdrawals": "Receptor de retiradas", "bank-transfers.settings.registered_email": "Email registrado", "bank-transfers.settings.title": "Ajustes de transferencia bancaria", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} Cuenta", "bank-transfers.setup.bank-account": "Cuenta bancaria", "bankTransfer.withdraw.max_loading": "Máx.: {amount}", "bank_details_do_not_match.got_it": "Entendido", "bank_details_do_not_match.subtitle": "El sort code y el número de cuenta no coinciden. Comprueba que los datos sean correctos e inténtalo de nuevo.", "bank_details_do_not_match.title": "Los datos bancarios no coinciden", "bank_tranfsers.select_country_of_residence.country_not_supported": "<PERSON> sentimo<PERSON>, las transferencias bancarias no están disponibles en {country} todavía", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Abre la app de tu banco", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Envía {fiatCurrencyCode} a tu cuenta", "bank_transfer.deposit.header": "{fullName}Datos de la cuenta&nbsp;personal", "bank_transfer.kyc_status_widget.subtitle": "Transferencias ban<PERSON>", "bank_transfer.kyc_status_widget.title": "Verificando identidad", "bank_transfer.personal_details.date_of_birth": "Fecha de nacimiento", "bank_transfer.personal_details.date_of_birth.invalid_format": "<PERSON>cha no válida", "bank_transfer.personal_details.date_of_birth.too_young": "<PERSON><PERSON> tener al menos 18 años", "bank_transfer.personal_details.first_name": "Nombre", "bank_transfer.personal_details.last_name": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.personal_details.title": "<PERSON><PERSON> datos", "bank_transfer.reference.label": "Referencia (Opcional)", "bank_transfer.reference_message": "Enviado desde Zeal", "bank_transfer.residence_details.address": "Tu dirección", "bank_transfer.residence_details.city": "Ciudad", "bank_transfer.residence_details.country_of_residence": "País de residencia", "bank_transfer.residence_details.country_placeholder": "<PERSON><PERSON>", "bank_transfer.residence_details.postcode": "Código postal", "bank_transfer.residence_details.street": "Calle", "bank_transfer.residence_details.your_residence": "Tu residencia", "bank_transfers.choose-wallet.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.test": "<PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.warning.subtitle": "Solo puedes vincular un monedero a la vez. No podrás cambiar el monedero vinculado.", "bank_transfers.choose-wallet.warning.title": "Elige bien tu monedero", "bank_transfers.choose_wallet.subtitle": "Vincula un monedero a tu cuenta bancaria. ", "bank_transfers.choose_wallet.title": "Elegir <PERSON>", "bank_transfers.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.currency_is_currently_not_supported": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.account-name": "Nombre de la cuenta", "bank_transfers.deposit.account-number-copied": "Número de cuenta copiado", "bank_transfers.deposit.amount-input": "Importe a depositar", "bank_transfers.deposit.amount-output": "Importe de destino", "bank_transfers.deposit.amount-output.error": "error", "bank_transfers.deposit.buttet-point.receive-crypto": "Recibe {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.currency-not-supported.subtitle": "Los depósitos bancarios desde {code} se han desactivado hasta nuevo aviso.", "bank_transfers.deposit.currency-not-supported.title": "{code} depósitos no disponibles actualmente", "bank_transfers.deposit.default-token.balance": "Saldo {amount}", "bank_transfers.deposit.deposit-header": "Depositar", "bank_transfers.deposit.enter_amount": "Introduce un importe", "bank_transfers.deposit.iban-copied": "IBAN copiado", "bank_transfers.deposit.increase-amount": "La transferencia mínima es {limit}", "bank_transfers.deposit.loading": "Cargando", "bank_transfers.deposit.max-limit-reached": "El importe supera el límite de transferencia", "bank_transfers.deposit.modal.kyc.button-text": "Empezar", "bank_transfers.deposit.modal.kyc.text": "Para verificar tu identidad, te pediremos algunos datos personales y documentación. Normalmente, el envío solo tarda un par de minutos.", "bank_transfers.deposit.modal.kyc.title": "Verifica tu identidad para aumentar los límites", "bank_transfers.deposit.reduce_amount": "Reduce el importe", "bank_transfers.deposit.show-account.account-number": "Número de cuenta", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Sort code", "bank_transfers.deposit.sort-code-copied": "Sort code copiado", "bank_transfers.deposit.withdraw-header": "<PERSON><PERSON><PERSON>", "bank_transfers.failed_to_load_fee": "Desconocido", "bank_transfers.fees": "Comisiones", "bank_transfers.increase-amount": "La transferencia mínima es {limit}", "bank_transfers.insufficient-funds": "Fondos insuficientes", "bank_transfers.select_country_of_residence.title": "¿Dónde vives?", "bank_transfers.setup.cta": "Configurar transferencias", "bank_transfers.setup.enter-amount": "Introduce el importe", "bank_transfers.source_of_funds.form.business_income": "Ingresos del negocio", "bank_transfers.source_of_funds.form.other": "<PERSON><PERSON>", "bank_transfers.source_of_funds.form.pension": "Pensión", "bank_transfers.source_of_funds.form.salary": "Salario", "bank_transfers.source_of_funds.form.title": "El origen de tus fondos", "bank_transfers.source_of_funds_description.placeholder": "Describe el origen de los fondos...", "bank_transfers.source_of_funds_description.title": "Danos más detalles sobre el origen de tus fondos", "bank_transfers.withdraw-header": "<PERSON><PERSON><PERSON>", "bank_transfers.withdraw.amount-input": "Importe a retirar", "bank_transfers.withdraw.max-limit-reached": "El importe supera el límite de transferencia", "bank_transfers.withdrawal.verify-id": "Reduce el importe", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Límite máximo de entrada superado", "banner.above_maximum_limit.maximum_limit_per_deposit": "Este es el límite máximo por depósito", "banner.above_maximum_limit.subtitle": "Límite máximo de entrada superado", "banner.above_maximum_limit.title": "Reduce el importe a {amount} o menos", "banner.above_maximum_limit.title.default": "Reduce el importe", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Límite mínimo no alcanzado", "banner.below_minimum_limit.minimum_limit_for_token": "Este es el límite mínimo para este token", "banner.below_minimum_limit.title": "Aumenta el importe a {amount} o más", "banner.below_minimum_limit.title.default": "Aumenta el importe", "breaard.in_porgress.info_popup.cta": "Gasta para ganar {earn}", "breaard.in_porgress.info_popup.footnote": "Al usar Zeal y la tarjeta Gnosis Pay, aceptas los términos y condiciones de esta campaña de recompensas.", "breaward.in_porgress.info_popup.bullet_point_1": "Gasta {remaining} en los próximos {time} para reclamar esta recompensa.", "breaward.in_porgress.info_popup.bullet_point_2": "Solo las compras válidas con Gnosis Pay cuentan para el importe de gasto.", "breaward.in_porgress.info_popup.bullet_point_3": "Tras reclamar tu recompensa, se enviará a tu cuenta de Zeal.", "breaward.in_porgress.info_popup.header": "<PERSON><PERSON> {earn}, gastan<PERSON> {remaining}", "breward.celebration.for_spending": "Por gastar con tu tarjeta", "breward.dc25-eligible-celebration.for_spending": "¡Estás entre los {limit} primeros!", "breward.dc25-non-eligible-celebration.for_spending": "No estuviste entre los {limit} primeros en gastar", "breward.expired_banner.earn_by_spending": "Gana {earn} gastando {amount}", "breward.expired_banner.reward_expired": "{earn} recompensa caducada", "breward.in_progress_banner.cta.title": "Gasta para ganar {earn}", "breward.ready_to_claim.error.try_again": "Reintentar", "breward.ready_to_claim.error_title": "Error al reclamar la recompensa", "breward.ready_to_claim.in_progress": "Reclamando recompensa", "breward.ready_to_claim.youve_earned": "¡Has ganado {earn}!", "breward_already_claimed.title": "Recompensa ya reclamada. Si no la recibiste, contacta con soporte.", "breward_cannotbe_claimed.title": "La recompensa no se puede reclamar ahora. Inténtalo de nuevo más tarde.", "bridge.best_return": "<PERSON>uta con mejor rendimiento", "bridge.best_serivce_time": "<PERSON>uta más rápida", "bridge.check_status.complete": "Completado", "bridge.check_status.progress_text": "Haciendo bridge de {from} a {to}", "bridge.remove_topup": "<PERSON><PERSON><PERSON> recarga", "bridge.request_status.completed": "Completado", "bridge.request_status.pending": "Pendiente", "bridge.widget.completed": "Completado", "bridge.widget.currencies": "{from} a {to}", "bridge_rote.widget.title": "Bridge", "browse.discover_more_apps": "Descubre más aplicaciones", "browse.google_search_term": "Buscar \"{searchTerm}\"", "brward.celebration.you_earned": "Has ganado", "brward.expired_banner.subtitle": "Más suerte la próxima vez", "brward.in_progress_banner.subtitle": "Caduca en {expiredInFormatted}", "buy": "<PERSON><PERSON><PERSON>", "buy.enter_amount": "Introduce un importe", "buy.loading": "Cargando...", "buy.no_routes_found": "No se han encontrado rutas", "buy.not_enough_balance": "<PERSON><PERSON> insuficiente", "buy.select-currency.title": "Se<PERSON><PERSON><PERSON><PERSON> token", "buy.select-to-currency.title": "Comprar tokens", "buy_form.title": "Comprar token", "cancelled-card.create-card-button.primary": "Obtener nueva tarjeta virtual", "cancelled-card.switch-card-button.primary": "Cambiar de tarjeta", "cancelled-card.switch-card-button.short-text": "Tienes otra tarjeta activa", "card": "Tarjeta", "card-add-cash.confirm-stage.banner.no-routes-found": "No hay rutas, prue<PERSON> con otro token o importe", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Necesitas {amount} más {symbol} para pagar las comisiones", "card-add-cash.confirm-stage.banner.value-loss": "<PERSON><PERSON><PERSON> {loss} de valor", "card-add-cash.confirm-stage.banner.value-loss.revert": "Revertir", "card-add-cash.edit-stage.cta.cancel": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.continue": "<PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.enter-amount": "Indica monto", "card-add-cash.edit-stage.cta.reduce-to-max": "Ajustar máx.", "card-add-cash.edit-staget.banner.no-routes-found": "No hay rutas, prue<PERSON> con otro token o importe", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Confirma la operación en tu monedero físico.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Firma en tu monedero físico", "card-balance": "Saldo: {balance}", "card-cashback.status.title": "Depositar en cashback", "card-copy-safe-address.copy_address": "<PERSON><PERSON><PERSON>", "card-copy-safe-address.copy_address.done": "Copiado", "card-copy-safe-address.warning.description": "Esta dirección solo puede recibir {cardAsset} en Gnosis Chain. No envíes activos de otras redes a esta dirección. Se perderán.", "card-copy-safe-address.warning.header": "Solo envía {cardAsset} en Gnosis Chain", "card-marketing-card.center.subtitle": "Comisiones FX", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "Intereses", "card-marketing-card.right.subtitle": "Regalo de bienvenida", "card-marketing-card.title": "Tarjeta VISA de alto interés en Europa", "card-marketing-tile.get-started": "Empezar", "card-select-from-token-title": "Seleccionar token de origen", "card-top-up.banner.subtitle.completed": "Completado", "card-top-up.banner.subtitle.failed": "Error", "card-top-up.banner.subtitle.pending": "{timerString} Pendiente", "card-top-up.banner.title": "Depositando {amount}", "card-topup.select-token.emptyState": "No hemos encontrado tokens", "card.activate.card_number_not_valid": "Tarjeta no válida. Revisa y prueba de nuevo.", "card.activate.invalid_card_number": "Número de tarjeta no válido.", "card.activation.activate_physical_card": "Activar tarjeta física", "card.add-cash.amount-to-withdraw": "Importe a ingresar", "card.add-from-earn-form.title": "<PERSON><PERSON><PERSON> saldo a la tarjeta", "card.add-from-earn-form.withdraw-to-card": "<PERSON><PERSON><PERSON><PERSON>", "card.add-from-earn.amount-to-withdraw": "Importe a retirar a la tarjeta", "card.add-from-earn.enter-amount": "Introduce un importe", "card.add-from-earn.loading": "Cargando", "card.add-from-earn.max-label": "Saldo: {amount}", "card.add-from-earn.no-routes-found": "No se han encontrado rutas", "card.add-from-earn.not-enough-balance": "<PERSON><PERSON> insuficiente", "card.add-owner.queued": "<PERSON><PERSON><PERSON> en cola", "card.add-to-wallet-flow.subtitle": "Haz pagos desde tu monedero.", "card.add-to-wallet.copy-card-number": "Copia el número de tarjeta a continuación", "card.add-to-wallet.title": "<PERSON><PERSON><PERSON> a {platformName} Wallet", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "CANCELADA", "card.card-owner-not-found.disconnect-btn": "Desconectar tarjeta", "card.card-owner-not-found.subtitle": "Actualiza el titular para reconectarla.", "card.card-owner-not-found.title": "Reconectar tarjeta", "card.card-owner-not-found.update-owner-btn": "Actualizar titular", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} en {date}", "card.cashback.widgetNoCashback.subtitle": "Deposita para empezar a ganar", "card.cashback.widgetNoCashback.title": "Consigue hasta un {defaultPercentage} de cashback", "card.cashback.widgetcashbackValue.rewards": "{amount} pendientes", "card.cashback.widgetcashbackValue.title": "{percentage} de cashback", "card.choose-wallet.connect_card": "Conectar tarjeta", "card.choose-wallet.create-new": "Añadir un monedero nuevo como titular", "card.choose-wallet.import-another-wallet": "Importar otro monedero", "card.choose-wallet.import-current-owner": "Importar titular actual de la tarjeta", "card.choose-wallet.import-current-owner.sub-text": "Importa claves o frase semilla del titular", "card.choose-wallet.title": "Selecciona un monedero para tu tarjeta", "card.connectWalletToCardGuide": "Copiar dirección del monedero", "card.connectWalletToCardGuide.addGnosisPayOwner": "<PERSON><PERSON><PERSON> de Gnosis Pay", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Abre Gnosispay.com con tu otro monedero{br}2. <PERSON>z clic en «Cuenta»{br}3. <PERSON>z clic en «Detalles de la cuenta»{br}4. <PERSON>z clic en «Editar», junto a «Titular de la cuenta», y{br}5. <PERSON>z clic en «Añadir dirección»{br}6. Pega tu dirección de Zeal y haz clic en guardar", "card.connectWalletToCardGuide.header": "Conectar {account} a la tarjeta Gnosis Pay", "card.connect_card.start": "Conectar tarjeta Gnosis", "card.copiedAddress": "Dirección copiada {formattedAddress}", "card.disconnect-account.title": "Desconectar cuenta", "card.hw-wallet-support-drop.add-owner-btn": "<PERSON><PERSON><PERSON> nue<PERSON> titular", "card.hw-wallet-support-drop.disconnect-btn": "Desconectar tarjeta", "card.hw-wallet-support-drop.subtitle": "Añade un titular que no sea monedero físico", "card.hw-wallet-support-drop.title": "Zeal ya no admite monederos físicos.", "card.kyc.continue": "Continuar configuración", "card.list_item.title": "Tarjeta", "card.onboarded.transactions.empty.description": "La actividad de tus pagos aparecerá aquí", "card.onboarded.transactions.empty.title": "Actividad", "card.order.continue": "Con<PERSON>uar pedido", "card.order.free_virtual_card": "Tarjeta virtual gratis", "card.order.start": "Pedir tarjeta gratis", "card.owner-not-imported.cancel": "<PERSON><PERSON><PERSON>", "card.owner-not-imported.import": "Importar", "card.owner-not-imported.subtitle": "Para autorizar esta transacción, vincula el monedero propietario de tu cuenta de Gnosis Pay a Zeal. Nota: esto es independiente de tu inicio de sesión habitual en Gnosis Pay.", "card.owner-not-imported.title": "Añadir propietario de la cuenta Gnosis Pay", "card.page.order_free_physical_card": "Tarjeta física gratis", "card.pin.change_pin_at_atm": "El PIN se puede cambiar en algunos cajeros", "card.pin.timeout": "La pantalla se cerrará en {seconds} s", "card.quick-actions.add-assets": "<PERSON><PERSON><PERSON>", "card.quick-actions.add-cash": "<PERSON><PERSON><PERSON>", "card.quick-actions.details": "Detalles", "card.quick-actions.freeze": "Bloquear", "card.quick-actions.freezing": "Bloqueando", "card.quick-actions.unfreeze": "Desb<PERSON>que<PERSON>", "card.quick-actions.unfreezing": "Des<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.withdraw": "<PERSON><PERSON><PERSON>", "card.read-only-detected.create-new": "Añadir un monedero nuevo como titular", "card.read-only-detected.import-current-owner": "Importar claves para {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Importa claves o frase del monedero {address}", "card.read-only-detected.title": "Elige un monedero para gestionar tu tarjeta", "card.remove-owner.queued": "Eliminación de titular en cola", "card.settings.disconnect-from-zeal": "Desconectar de Zeal", "card.settings.edit-owners": "Cambiar titulares de la tarjeta", "card.settings.getCard": "Pedir otra tarjeta", "card.settings.getCard.subtitle": "Tarjetas virtuales o físicas", "card.settings.notRecharging": "Sin recarga automática", "card.settings.notifications.subtitle": "Recibe notificaciones de pago", "card.settings.notifications.title": "Notificaciones de la tarjeta", "card.settings.page.title": "Ajustes de la tarjeta", "card.settings.select-card.cancelled-cards": "Tarjetas canceladas", "card.settings.setAutoRecharge": "Configurar recarga automática", "card.settings.show-card-address": "Mostrar dirección de la tarjeta", "card.settings.spend-limit": "Fijar límite de gasto", "card.settings.spend-limit-title": "Límite diario actual: {limit}", "card.settings.switch-active-card": "Cambiar de tarjeta activa", "card.settings.switch-active-card-description": "Tarjeta activa: {card}", "card.settings.switch-card.card-item.cancelled": "Cancelada", "card.settings.switch-card.card-item.frozen": "Congelada", "card.settings.switch-card.card-item.title": "Tarjeta <PERSON>", "card.settings.switch-card.card-item.title.physical": "Tarjeta física", "card.settings.switch-card.card-item.title.virtual": "Tarjeta virtual", "card.settings.switch-card.title": "Seleccionar tarjeta", "card.settings.targetBalance": "Saldo objetivo: {threshold}", "card.settings.view-pin": "Ver PIN", "card.settings.view-pin-description": "Protege siempre tu PIN", "card.title": "Tarjeta", "card.transactions.header": "Transacciones de la tarjeta", "card.transactions.see_all": "Ver todas las transacciones", "card.virtual": "VIRTUAL", "cardCashback.onboarding.bullets.cashback_sent_weekly": "El cashback se envía a tu tarjeta al inicio de la semana siguiente a la que lo ganaste.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "Cuanto más deposites, más ganas en cada compra.", "cardCashback.onboarding.title": "Consigue hasta un {percentage} de cashback", "cardCashbackWithdraw.amount": "Importe a retirar", "cardCashbackWithdraw.header": "Retirar {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "La tarjeta está bloqueada y no se pudo activar", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "Hasta un {percentage}", "cardWidget.startEarning": "Empieza a ganar", "cardWithdraw.amount": "Importe a retirar", "cardWithdraw.header": "Retirar de la tarjeta", "cardWithdraw.selectWithdrawWallet.title": "Elige el monedero{br}al que retirar", "cardWithdraw.success.cta": "<PERSON><PERSON><PERSON>", "cardWithdraw.success.subtitle": "Por seguridad, todas las retiradas de la tarjeta Gnosis Pay tardan 3 minutos en procesarse", "cardWithdraw.success.title": "Este cambio tardará 3 minutos", "card_top_up_trx.send": "Envío", "card_top_up_trx.to": "A", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "<PERSON><PERSON> de caducidad", "cards.card_number": "Número de tarjeta", "cards.choose-wallet.no-active-accounts": "No tienes monederos activos", "cards.copied_card_number": "Número de tarjeta copiado", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Supera el límite diario", "cards.transactions.decline_reason.incorrect_pin": "<PERSON><PERSON>o", "cards.transactions.decline_reason.incorrect_security_code": "Código de seguridad incorrecto", "cards.transactions.decline_reason.invalid_amount": "Importe no válido", "cards.transactions.decline_reason.low_balance": "<PERSON><PERSON> insuficiente", "cards.transactions.decline_reason.other": "<PERSON><PERSON><PERSON><PERSON>", "cards.transactions.decline_reason.pin_tries_exceeded": "Intentos de PIN superados", "cards.transactions.status.refund": "Reembolso", "cards.transactions.status.reversal": "Anulación", "cashback-deposit.trx.title": "Depositar en Cashback", "cashback-estimate.text": "Esta es una estimación y NO un pago garantizado. Se aplican todas las reglas de cashback conocidas públicamente, pero Gnosis Pay puede excluir transacciones a su discreción. Un gasto máximo de {amount} a la semana califica para el cashback, incluso si la estimación para esta transacción indicara un importe total mayor.", "cashback-estimate.text.fallback": "Esta es una estimación, no un pago garantizado. Se aplican todas las reglas de cashback conocidas, pero Gnosis Pay puede excluir transacciones a su discreción.", "cashback-estimate.title": "Estimación de cashback", "cashback-onbarding-tersm.subtitle": "Los datos de las transacciones de tu tarjeta se compartirán con <PERSON><PERSON>at<PERSON>, responsable de distribuir las recompensas de cashback. Al hacer clic en aceptar, aceptas los <terms>Términos y condiciones</terms>", "cashback-onbarding-tersm.title": "Términos de uso y privacidad", "cashback-tx-activity.retry": "Reintentar", "cashback-unconfirmed-payments-info.subtitle": "Los pagos optarán a cashback cuando se liquiden con el comercio. <PERSON><PERSON> entonces, se muestran como pagos no confirmados. Los pagos no liquidados no optan a cashback.", "cashback-unconfirmed-payments-info.title": "Pagos con tarjeta no confirmados", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "<PERSON><PERSON><PERSON><PERSON>", "cashback.activity.title": "Actividad reciente", "cashback.activity.withdrawal": "Re<PERSON><PERSON>", "cashback.deposit": "Depositar", "cashback.deposit.amount.label": "Importe a depositar", "cashback.deposit.change": "{from} a {to}", "cashback.deposit.confirmation.subtitle": "Las tasas de cashback se actualizan una vez a la semana. Deposita ahora para aumentar el cashback de la próxima semana.", "cashback.deposit.confirmation.title": "Empe<PERSON><PERSON> a ganar un {percentage} el {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Intercambia tokens por {currency} en {network} Chain", "cashback.deposit.get.tokens.title": "Conseguir {currency} tokens", "cashback.deposit.header": "Depositar {currency}", "cashback.deposit.max_label": "Máx.: {amount}", "cashback.deposit.select-wallet.title": "Elige el monedero desde el que depositar", "cashback.deposit.yourcashback": "Tu cashback", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "Elige el monedero al que {br}retirar", "cashback.transaction-details.network-label": "Red", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "<PERSON><PERSON>", "cashback.transaction-details.top-row.label-rewards": "Periodo de cashback", "cashback.transaction-details.top-row.label-withdrawal": "A", "cashback.transaction-details.transaction": "ID de la transacción", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Transacciones de cashback", "cashback.withdraw": "<PERSON><PERSON><PERSON>", "cashback.withdraw.confirmation.cashback_reduction": "El cashback de esta semana, incluido el que ya has ganado, se reducirá del {before} al {after}", "cashback.withdraw.queued": "Retirada en cola", "cashback.withdrawal.change": "{from} a {to}", "cashback.withdrawal.confirmation.subtitle": "Iniciar retirada de {amount} con un retraso de 3 minutos. Esto reducirá tu cashback al {after}.", "cashback.withdrawal.confirmation.title": "El cashback disminuir<PERSON> si retiras GNO", "cashback.withdrawal.delayTransaction.title": "Iniciar reti<PERSON> de GNO con{br} un retraso de 3 minutos", "cashback.withdrawal.withdraw": "<PERSON><PERSON><PERSON>", "cashback.withdrawal.yourcashback": "Tu cashback", "celebration.aave": "Ganado con Aave", "celebration.cashback.subtitle": "<PERSON><PERSON><PERSON> en {code}", "celebration.cashback.subtitleGNO": "{amount} ganados la última vez", "celebration.chf": "Ganado con Frankencoin", "celebration.lido": "Ganado con Lido", "celebration.sky": "Ganado con Sky", "celebration.title": "Cashback total", "celebration.well_done.title": "¡Bien hecho!", "change-withdrawal-account.add-new-account": "Añadir otra cuenta bancaria", "change-withdrawal-account.item.shortText": "{currency} Cuenta", "check-confirmation.approve.footer.for": "Para", "checkConfirmation.title": "Resultado de la transacción", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin y ether", "collateral.ethereum": "Ethereum", "collateral.other": "<PERSON><PERSON><PERSON>", "collateral.rwa": "Activos del mundo real", "collateral.stablecoins": "Stablecoins (ancladas al USD)", "collateral.us-t-bills": "Letras del Tesoro de EE. UU.", "confirm-bank-transfer-recipient.bullet-1": "Sin comisiones en EUR digitales", "confirm-bank-transfer-recipient.bullet-2": "Dep<PERSON><PERSON>s a {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Comparte los datos de tu cuenta Gnosis Pay con Monerium, una EMI autorizada y regulada. <link>Saber más</link>", "confirm-bank-transfer-recipient.bullet-4": "Aceptar los <link>términos del servicio</link>", "confirm-bank-transfer-recipient.title": "<PERSON><PERSON><PERSON> té<PERSON>s", "confirm-change-withdrawal-account.cancel": "<PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.confirm": "Confirmar", "confirm-change-withdrawal-account.saving": "Guardando", "confirm-change-withdrawal-account.subtitle": "Todos los retiros que envíes desde Zeal se recibirán en esta cuenta bancaria.", "confirm-change-withdrawal-account.title": "Cambiar banco receptor", "confirm-ramove-withdrawal-account.title": "Eliminar cuenta bancaria", "confirm-remove-withdrawal-account.subtitle": "Los datos de esta cuenta bancaria se eliminarán de Zeal. Puedes volver a añadirla cuando quieras.", "confirmTransaction.finalNetworkFee": "Comisión de red", "confirmTransaction.importKeys": "Importar claves", "confirmTransaction.networkFee": "Comisión de red", "confirmation.title": "Enviar {amount} a {recipient}", "conflicting-monerium-account.add-owner": "Añadir como titular de Gnosis Pay", "conflicting-monerium-account.create-wallet": "Crear un nuevo Smart Wallet", "conflicting-monerium-account.disconnect-card": "Desconecta la tarjeta de Zeal y vuelve a conectarla con el nuevo titular", "conflicting-monerium-account.header": "{wallet} vinculado a otra cuenta de Monerium", "conflicting-monerium-account.subtitle": "Cambia tu monedero de titular de Gnosis Pay", "connection.diconnected.got_it": "¡Entendido!", "connection.diconnected.page1.subtitle": "Zeal funciona donde funciona MetaMask. Conéctate como lo harías con MetaMask.", "connection.diconnected.page1.title": "¿Cómo conectarse con Zeal?", "connection.diconnected.page2.subtitle": "Verás muchas opciones. Zeal podría ser una de ellas. Si Zeal no aparece...", "connection.diconnected.page2.title": "Haz clic en Conectar monedero", "connection.diconnected.page3.subtitle": "Te pediremos que te conectes con Zeal. «Browser» o «Injected» también deberían funcionar. ¡Pruébalo!", "connection.diconnected.page3.title": "<PERSON><PERSON>", "connectionSafetyCheck.tag.caution": "Precaución", "connectionSafetyCheck.tag.danger": "<PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.passed": "Superado", "connectionSafetyConfirmation.subtitle": "¿Seguro que quieres continuar?", "connectionSafetyConfirmation.title": "Este sitio parece peligroso", "connection_state.connect.cancel": "<PERSON><PERSON><PERSON>", "connection_state.connect.changeToMetamask": "Cambiar a MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Cambiar a MetaMask", "connection_state.connect.connect_button": "Conectar", "connection_state.connect.expanded.connected": "Conectado", "connection_state.connect.expanded.title": "Conectar", "connection_state.connect.safetyChecksLoading": "Comprobando seguridad del sitio", "connection_state.connect.safetyChecksLoadingError": "No se pudo completar el análisis de seguridad", "connection_state.connected.expanded.disconnectButton": "Desconectar Zeal", "connection_state.connected.expanded.title": "Conectado", "copied-diagnostics": "Diagnósticos copiados", "copy-diagnostics": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.component.add_recipient_primary_text": "<PERSON><PERSON><PERSON> destinatar<PERSON> bancar<PERSON>", "counterparty.country": "<PERSON><PERSON>", "counterparty.countryTitle": "País del destinatario", "counterparty.currency": "Moneda", "counterparty.delete.success.title": "Eliminado", "counterparty.edit.success.title": "Cambios guardados", "counterparty.errors.country_required": "<PERSON><PERSON> obligatorio", "counterparty.errors.first_name.invalid": "El nombre debe ser más largo", "counterparty.errors.last_name.invalid": "El apellido debe ser más largo", "counterparty.first_name": "Nombre", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Enviar a banco", "countrySelector.noCountryFound": "No se encontraron países", "countrySelector.title": "Elige un país", "create-passkey.cta": "<PERSON><PERSON><PERSON> passkey", "create-passkey.extension.cta": "<PERSON><PERSON><PERSON><PERSON>", "create-passkey.footnote": "Con la tecnología de", "create-passkey.mobile.cta": "Iniciar configuración", "create-passkey.steps.enable-recovery": "Configurar recuperación en la nube", "create-passkey.steps.setup-biometrics": "Activar seguridad biométrica", "create-passkey.subtitle": "Las passkeys son más seguras que las contraseñas y se cifran en la nube para recuperarlas fácilmente.", "create-passkey.title": "Protege tu cuenta", "create-smart-wallet": "Crear Smart Wallet", "create-userop.progress.text": "<PERSON><PERSON><PERSON>", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Continuar en Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Ir a Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "Pedido iniciado. Complétalo en Gnosis Pay", "create_recharge_preferences.card": "Tarjeta", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "Gana {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "Mantener efectivo", "create_recharge_preferences.link_accounts_title": "Vincular cuentas", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "Tu tarjeta NO se recargará automáticamente después de cada pago.", "create_recharge_preferences.not_configured_title": "Gana y Gasta", "create_recharge_preferences.recharge_from_earn_accounts_description": "Tu tarjeta se recarga automáticamente después de cada pago desde tu cuenta Earn.", "create_recharge_preferences.subtitle": "al año", "creating-account.loading": "<PERSON><PERSON><PERSON> cuenta", "creating-gnosis-pay-account": "<PERSON><PERSON><PERSON> cuenta", "currencies.bridge.select_routes.emptyState": "No hemos encontrado rutas para este bridge", "currency.add_currency.add_token": "<PERSON><PERSON><PERSON> token", "currency.add_currency.not_a_valid_address": "No es una dirección de token válida", "currency.add_currency.token_decimals_feild": "Decimales del token", "currency.add_currency.token_feild": "Dirección del token", "currency.add_currency.token_symbol_feild": "Símbolo del token", "currency.add_currency.update_token": "Actualizar token", "currency.add_custom.remove_token.cta": "Eliminar", "currency.add_custom.remove_token.header": "Eliminar token", "currency.add_custom.remove_token.subtitle": "Tu monedero seguirá teniendo el saldo de este token, pero estará oculto en tu portafolio de Zeal.", "currency.add_custom.token_removed": "Token eliminado", "currency.add_custom.token_updated": "Token actualizado", "currency.balance_label": "Saldo: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "Tu transferencia bancaria ha convertido con éxito {fiat} a {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "Has recibido {crypto}", "currency.bankTransfer.deposit_status.success": "Recibido en tu monedero", "currency.bankTransfer.deposit_status.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.check_bank_account": "Consulta tu cuenta bancaria", "currency.bankTransfer.off_ramp.complete": "Completada", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Enviando a tu banco", "currency.bankTransfer.off_ramp.transferring_to_currency": "Transfiriendo a {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Los fondos ya deberían haber llegado a tu cuenta bancaria.", "currency.bankTransfer.withdrawal_status.success": "Enviado a tu banco", "currency.bankTransfer.withdrawal_status.title": "Re<PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.email": "Correo electrónico", "currency.bank_transfer.create_unblock_user.email_invalid": "Correo no válido", "currency.bank_transfer.create_unblock_user.email_missing": "Obligatorio", "currency.bank_transfer.create_unblock_user.first_name": "Nombre", "currency.bank_transfer.create_unblock_user.first_name_missing": "Obligatorio", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "Solo se admiten letras, n<PERSON>mer<PERSON>, espacios y - . , & ( ) '.", "currency.bank_transfer.create_unblock_user.last_name": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_missing": "Obligatorio", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "Solo se admiten letras, n<PERSON>mer<PERSON>, espacios y - . , & ( ) '.", "currency.bank_transfer.create_unblock_user.note": "<PERSON> continuar, ace<PERSON><PERSON> los <terms>T<PERSON>rm<PERSON>s</terms> de Unblock (nuestro socio bancario) y su <policy>Política de Privacidad</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Escribe tu nombre exactamente como aparece en tu cuenta bancaria", "currency.bank_transfer.create_unblock_user.title": "Vincula tu cuenta bancaria", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Número de cuenta", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "País del banco", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Moneda preferida", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Sort code", "currency.bank_transfer.create_unblock_withdraw_account.success": "Cuenta configurada", "currency.bank_transfer.create_unblock_withdraw_account.title": "Vincula tu cuenta bancaria", "currency.bank_transfer.residence-form.address-required": "Obligatorio", "currency.bank_transfer.residence-form.address-unsupported-char": "Solo letras, n<PERSON><PERSON><PERSON>, espacios y , ; {apostrophe} - \\\\ permitidos.", "currency.bank_transfer.residence-form.city-required": "Obligatorio", "currency.bank_transfer.residence-form.city-unsupported-char": "Solo letras, n<PERSON><PERSON><PERSON>, espacios y . , - & ( ) {apostrophe} permitidos.", "currency.bank_transfer.residence-form.postcode-invalid": "Código postal no válido", "currency.bank_transfer.residence-form.postcode-required": "Obligatorio", "currency.bank_transfer.validation.invalid.account_number": "Número de cuenta no válido", "currency.bank_transfer.validation.invalid.iban": "IBAN no válido", "currency.bank_transfer.validation.invalid.sort_code": "Sort code no válido", "currency.bridge.amount_label": "Cantidad para el Bridge", "currency.bridge.best_returns.subtitle": "<PERSON>ste proveedor de bridge te dará el mayor rendimiento, comisiones incluidas.", "currency.bridge.best_returns_popup.title": "<PERSON><PERSON><PERSON> re<PERSON>", "currency.bridge.bridge_from": "<PERSON><PERSON>", "currency.bridge.bridge_gas_fee_loading_failed": "Hubo problemas al cargar la comisión de red", "currency.bridge.bridge_low_slippage": "Slippage muy bajo. Prueba a aumentarlo", "currency.bridge.bridge_provider": "Prove<PERSON><PERSON> de la transferencia", "currency.bridge.bridge_provider_loading_failed": "Hubo problemas al cargar los proveedores", "currency.bridge.bridge_settings": "Ajustes del Bridge", "currency.bridge.bridge_status.subtitle": "<PERSON><PERSON><PERSON> {name}", "currency.bridge.bridge_status.title": "Bridge", "currency.bridge.bridge_to": "A", "currency.bridge.fastest_route_popup.subtitle": "Este proveedor de bridge te ofrece la ruta de transacción más rápida.", "currency.bridge.fastest_route_popup.title": "<PERSON>uta más rápida", "currency.bridge.from": "<PERSON><PERSON>", "currency.bridge.success": "Completado", "currency.bridge.title": "Bridge", "currency.bridge.to": "A", "currency.bridge.topup": "Recargar {symbol}", "currency.bridge.withdrawal_status.title": "Re<PERSON><PERSON>", "currency.card.card_top_up_status.title": "Añadir <PERSON> a la tarjeta", "currency.destination_amount": "Importe de destino", "currency.hide_currency.confirm.subtitle": "Oculta este token de tu portafolio. Podrás volver a mostrarlo cuando quieras.", "currency.hide_currency.confirm.title": "Ocultar token", "currency.hide_currency.success.title": "Token oculto", "currency.label": "Etiqueta (Opcional)", "currency.last_name": "Apellido", "currency.max_loading": "Máx.:", "currency.swap.amount_to_swap": "Importe a intercambiar", "currency.swap.best_return": "<PERSON>uta con mejor rendimiento", "currency.swap.destination_amount": "Importe de destino", "currency.swap.header": "Intercambiar", "currency.swap.max_label": "Saldo: {amount}", "currency.swap.provider.header": "Proveedor de swap", "currency.swap.select_to_token": "Se<PERSON><PERSON><PERSON><PERSON> token", "currency.swap.swap_gas_fee_loading_failed": "Hubo problemas al cargar la comisión de red", "currency.swap.swap_provider_loading_failed": "Hubo problemas al cargar los proveedores", "currency.swap.swap_settings": "Ajustes de intercambio", "currency.swap.swap_slippage_too_low": "Slippage muy bajo. Prueba a aumentarlo.", "currency.swaps_io_native_token_swap.subtitle": "Usando Swaps.IO", "currency.swaps_io_native_token_swap.title": "Enviar", "currency.withdrawal.amount_from": "<PERSON><PERSON>", "currency.withdrawal.amount_to": "A", "currencySelector.title": "Elige una divisa", "dApp.wallet-does-not-support-chain.subtitle": "Parece que tu monedero no es compatible con {network}. Prueba a conectar con otro monedero o usa Zeal.", "dApp.wallet-does-not-support-chain.title": "Red no compatible", "dapp.connection.manage.confirm.disconnect.all.cta": "Desconectar", "dapp.connection.manage.confirm.disconnect.all.subtitle": "¿Seguro que quieres desconectar todas las conexiones?", "dapp.connection.manage.confirm.disconnect.all.title": "Desconectar todo", "dapp.connection.manage.connection_list.main.button.title": "Desconectar", "dapp.connection.manage.connection_list.no_connections": "No tienes ninguna app conectada", "dapp.connection.manage.connection_list.section.button.title": "Desconectar todo", "dapp.connection.manage.connection_list.section.title": "Activas", "dapp.connection.manage.connection_list.title": "Conexiones", "dapp.connection.manage.disconnect.success.title": "Apps desconectadas", "dapp.metamask_mode.title": "<PERSON><PERSON>", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "Intereses", "dc25-card-marketing-card.right.subtitle": "100 personas", "dc25-card-marketing-card.title": "Los 100 primeros en gastar 50 € ganarán {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "No podrás realizar esta acción durante 3 min. Por motivos de seguridad, los cambios en la configuración de la tarjeta o las retiradas tardan 3 minutos en procesarse.", "delayQueueBusyBanner.processing-yout-action.title": "Procesando tu acción, espera", "delayQueueBusyWidget.cardFrozen": "Tarjeta blo<PERSON>", "delayQueueBusyWidget.processingAction": "Procesando tu acción", "delayQueueFailedBanner.action-incomplete.get-support": "Obtener ayuda", "delayQueueFailedBanner.action-incomplete.subtitle": "<PERSON> sentimo<PERSON>, algo ha fallado con tu retirada o con la actualización de los ajustes. Contacta con soporte en Discord.", "delayQueueFailedBanner.action-incomplete.title": "Acción incompleta", "delayQueueFailedWidget.actionIncomplete.title": "Acción de la tarjeta incompleta", "delayQueueFailedWidget.cardFrozen.subtitle": "Tarjeta blo<PERSON>", "delayQueueFailedWidget.contactSupport": "Contactar con soporte", "delay_queue_busy.subtitle": "Por seguridad, los cambios en los ajustes o las retiradas tardan 3 minutos en procesarse. Durante ese tiempo, tu tarjeta estará congelada.", "delay_queue_busy.title": "Tu acción se está procesando", "delay_queue_failed.contact_support": "Contactar", "delay_queue_failed.subtitle": "<PERSON> sentimo<PERSON>, algo ha fallado con tu retirada o con la actualización de los ajustes. Contacta con soporte en Discord.", "delay_queue_failed.title": "Contacta con soporte", "deploy-earn-form-smart-wallet.in-progress.title": "<PERSON><PERSON><PERSON><PERSON>", "deposit": "Depositar", "disconnect-card-popup.cancel": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.disconnect": "Desconectar", "disconnect-card-popup.subtitle": "Esto eliminará tu tarjeta de la app de Zeal. Tu monedero seguirá conectado a tu tarjeta en la app de Gnosis Pay. Puedes volver a conectar tu tarjeta cuando quieras.", "disconnect-card-popup.title": "Desconectar tarjeta", "distance.long.days": "{count} días", "distance.long.hours": "{count} horas", "distance.long.minutes": "{count} minutos", "distance.long.months": "{count} meses", "distance.long.seconds": "{count} segundos", "distance.long.years": "{count} a<PERSON>s", "distance.short.days": "{count} d", "distance.short.hours": "{count} h", "distance.short.minutes": "{count} min", "distance.short.months": "{count} m", "distance.short.seconds": "{count} seg", "distance.short.years": "{count} a", "duration.short.days": "{count}d", "duration.short.hours": "{count}h", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn-deposit-view.into": "En", "earn-deposit-view.to": "A", "earn-deposit.swap.transfer-provider": "<PERSON>ve<PERSON><PERSON> de transfer<PERSON>cias", "earn-taker-investment-details.accrued-realtime": "Acumulado en tiempo real", "earn-taker-investment-details.asset-class": "Clase de activo", "earn-taker-investment-details.asset-coverage-ratio": "Ratio de cobertura de activos", "earn-taker-investment-details.asset-reserve": "Reserva de activos", "earn-taker-investment-details.base_currency.label": "Moneda base", "earn-taker-investment-details.chf.description": "Gana intereses con tus CHF depositando zCHF en Frankencoin, un mercado monetario digital de confianza. Los intereses se generan a partir de préstamos de bajo riesgo y sobregarantizados en Frankencoin y se pagan en tiempo real. Tus fondos están seguros en una subcuenta segura que solo tú controlas.", "earn-taker-investment-details.chf.description.with_address_link": "Gana intereses con tus CHF depositando zCHF en Frankencoin, un mercado monetario digital de confianza. Los intereses se generan a partir de préstamos de bajo riesgo y sobregarantizados en Frankencoin y se pagan en tiempo real. Tus fondos están seguros en una subcuenta segura <link>(copiar 0x)</link> que solo tú controlas.", "earn-taker-investment-details.chf.label": "Franco suizo digital", "earn-taker-investment-details.collateral-composition": "Composición del colateral", "earn-taker-investment-details.depositor-obligations": "Obligaciones con depositantes", "earn-taker-investment-details.eure.description": "Gana intereses por tus euros depositando EURe en Aave, un mercado monetario digital de confianza. EURe es una stablecoin de euro totalmente regulada, emitida por Monerium y con respaldo 1:1 en cuentas protegidas. Los intereses se generan a partir de préstamos sobrecolateralizados de bajo riesgo en Aave y se pagan en tiempo real. Tus fondos permanecen en una subcuenta segura que solo tú controlas.", "earn-taker-investment-details.eure.description.with_address_link": "Gana intereses por tus euros depositando EURe en Aave, un mercado monetario digital de confianza. EURe es una stablecoin de euro totalmente regulada, emitida por Monerium y con respaldo 1:1 en cuentas protegidas. Los intereses se generan a partir de préstamos sobrecolateralizados de bajo riesgo en Aave y se pagan en tiempo real. Tus fondos permanecen en una subcuenta segura <link>(copiar 0x)</link> que solo tú controlas.", "earn-taker-investment-details.eure.label": "Euro digital (EURe)", "earn-taker-investment-details.faq": "Preguntas frecuentes", "earn-taker-investment-details.fixed-income": "Renta fija", "earn-taker-investment-details.issuer": "<PERSON><PERSON><PERSON>", "earn-taker-investment-details.key-facts": "<PERSON><PERSON> clave", "earn-taker-investment-details.liquidity": "Liquidez", "earn-taker-investment-details.operator": "Operador de mercado", "earn-taker-investment-details.projected-yield": "Rendimiento anual proyectado", "earn-taker-investment-details.see-other-faq": "Ver todas las preguntas frecuentes", "earn-taker-investment-details.see-realtime": "Ver datos en tiempo real", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Rendimiento de los últimos 12 meses", "earn-taker-investment-details.total-collateral": "Garantía total", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "Suministro total de ZCHF", "earn-taker-investment-details.total_deposits": "Depósitos totales en Aave", "earn-taker-investment-details.usd.description": "Sky es un mercado monetario digital que ofrece rendimientos estables denominados en dólares estadounidenses, procedentes de letras del Tesoro de EE. UU. a corto plazo y préstamos sobrecolateralizados. Todo sin la volatilidad de las cripto, con acceso a tus fondos 24/7 y un respaldo transparente en la cadena.", "earn-taker-investment-details.usd.description.with_address_link": "Sky es un mercado monetario digital que ofrece rendimientos estables denominados en dólares estadounidenses, procedentes de letras del Tesoro de EE. UU. a corto plazo y préstamos sobrecolateralizados. Todo sin la volatilidad de las cripto, con acceso a tus fondos 24/7 y un respaldo transparente en la cadena. Las inversiones están en una subcuenta <link>(copiar 0x)</link> que tú controlas.", "earn-taker-investment-details.usd.ftx-difference": "¿En qué se diferencia de FTX, <PERSON><PERSON><PERSON>, BlockFi o Luna?", "earn-taker-investment-details.usd.high-returns": "¿Cómo pueden ser los rendimientos tan altos, sobre todo en comparación con los bancos tradicionales?", "earn-taker-investment-details.usd.how-is-backed": "¿Qué respaldo tiene <PERSON> y qué pasaría con mi dinero si Zeal quebrara?", "earn-taker-investment-details.usd.income-sources": "Fuentes de ingresos 2024", "earn-taker-investment-details.usd.insurance": "¿Mis fondos están asegurados o garantizados por alguna entidad (como el FDIC o similar)?", "earn-taker-investment-details.usd.label": "Dólar estadounidense digital", "earn-taker-investment-details.usd.lose-principal": "¿Es realista pensar que puedo perder mi capital y en qué circunstancias?", "earn-taker-investment-details.variable-rate": "Préstamos a tipo variable", "earn-taker-investment-details.withdraw-anytime": "Retirar en cualquier momento", "earn-taker-investment-details.yield": "Rendimiento", "earn-withdrawal-view.approve.for": "Para", "earn-withdrawal-view.approve.into": "En", "earn-withdrawal-view.swap.into": "En", "earn-withdrawal-view.withdraw.to": "A", "earn.add_another_asset.title": "Selecciona un activo para Earn", "earn.add_asset": "Añadir activo", "earn.asset_view.title": "<PERSON><PERSON><PERSON>", "earn.base-currency-popup.text": "La moneda base es la divisa en la que se valoran y registran tus depósitos, rendimientos y transacciones. Si depositas en una moneda diferente (por ejemplo, EUR en una cuenta en USD), tus fondos se convierten inmediatamente a la moneda base al tipo de cambio actual. Tras la conversión, tu saldo permanece estable en la moneda base, pero las retiradas futuras pueden implicar nuevas conversiones de divisa.", "earn.base-currency-popup.title": "Moneda base", "earn.card-recharge.disabled.list-item.title": "Recarga automática desactivada", "earn.card-recharge.enabled.list-item.title": "Recarga automática activada", "earn.choose_wallet_to_deposit.title": "Depositar desde", "earn.config.currency.eth": "Gana con Ethereum", "earn.config.currency.on_chain_address_subtitle": "Dirección en la cadena", "earn.config.currency.us_dollars": "Configurar transferencias ban<PERSON>", "earn.configured_widget.current_apy.title": "APY actual", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} Anual", "earn.confirm.currency.cta": "Depositar", "earn.currency.eth": "Earn <PERSON>", "earn.deploy.status.title": "<PERSON><PERSON><PERSON> cuenta <PERSON>arn", "earn.deploy.status.title_with_taker": "<PERSON><PERSON><PERSON> {title} cuenta Earn", "earn.deposit": "Depositar", "earn.deposit.amount_to_deposit": "Importe a depositar", "earn.deposit.deposit": "Depositar", "earn.deposit.enter_amount": "Introduce un importe", "earn.deposit.no_routes_found": "No se han encontrado rutas", "earn.deposit.not_enough_balance": "<PERSON><PERSON> insuficiente", "earn.deposit.select-currency.title": "Selecciona un token para depositar", "earn.deposit.select_account.title": "Selecciona una cuenta de Earn", "earn.desposit_form.title": "Depositar en Earn", "earn.earn_deposit.status.title": "Depositar en Earn", "earn.earn_deposit.trx.title": "Depositar en Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "Retira fondos cuando quieras", "earn.earn_withdraw.status.title": "Retirar de la cuenta Earn", "earn.earn_withdraw.trx.title.approval": "<PERSON><PERSON><PERSON> reti<PERSON>", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Retirar en {asset}", "earn.earn_withdraw.trx.title.withdrawal": "<PERSON><PERSON><PERSON>", "earn.recharge.cta": "Guardar cambios", "earn.recharge.earn_not_configured.enable_some_account.error": "Activa una cuenta", "earn.recharge.earn_not_configured.enter_amount.error": "Introduce un importe", "earn.recharge.select_taker.header": "Recargar tarjeta en orden desde", "earn.recharge_card_tag.on": "activa", "earn.recharge_card_tag.recharge": "Recarga", "earn.recharge_card_tag.recharge_not_configured": "Recarga automática", "earn.recharge_card_tag.recharge_off": "Recarga desactivada", "earn.recharge_card_tag.recharged": "<PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharging": "Recargando", "earn.recharge_configured.disable.trx.title": "Desactivar recarga automática", "earn.recharge_configured.trx.disclaimer": "Al usar tu tarjeta, se crea una subasta en Cowswap con tus activos de Earn para cubrir el pago. Esto suele darte el mejor precio, pero el tipo de cambio on-chain puede variar del real.", "earn.recharge_configured.trx.subtitle": "Tras cada pago, se añadirá saldo automáticamente desde tus cuentas Earn para mantener tu tarjeta en {value}", "earn.recharge_configured.trx.title": "Fijar recarga automática en {value}", "earn.recharge_configured.updated.trx.title": "Guardar ajustes de recarga", "earn.risk-banner.subtitle": "Este es un producto de autocustodia sin protección regulatoria contra pérdidas.", "earn.risk-banner.title": "Entiende los riesgos", "earn.set_recharge.status.title": "Configurar recarga automática", "earn.setup_reacharge.input.disable.label": "Desactivar", "earn.setup_reacharge.input.label": "Saldo objetivo de la tarjeta", "earn.setup_reacharge_form.title": "La Recarga automática mantiene tu {br}tarjeta con el mismo saldo", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Mantén wstETH (ETH en staking) en Gnosis Chain y presta vía Lido.", "earn.taker-bulletlist.point_1": "Gana un {apyValue} anual. El rendimiento varía con el mercado.", "earn.taker-bulletlist.point_3": "Zeal no cobra comisiones.", "earn.taker-historical-returns": "Rendimiento histó<PERSON>", "earn.taker-historical-returns.chf": "Crecimiento de CHF a USD", "earn.taker-investment-tile.apy.perYear": "al año", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "Depositar", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Ganando con Aave en Gnosis Chain", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Ganando con Frankencoin en Gnosis Chain", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Ganando con Lido en Gnosis Chain", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Ganando con Maker en Gnosis Chain", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "<PERSON>arn con ETH", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "<PERSON>arn con ETH", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "Bonos del Tesoro de EE. UU. (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Aunque es muy poco frecuente, en teoría es posible. Tus fondos están protegidos por una gestión de riesgos estricta y una alta colateralización. El peor escenario realista implicaría condiciones de mercado sin precedentes, como que varias stablecoins perdieran su anclaje a la vez, algo que nunca ha sucedido.", "earn.usd.can-I-lose-my-principal-popup.title": "¿Es realista pensar que puedo perder mi capital y en qué circunstancias?", "earn.usd.ftx-difference-popup.text": "Sky es fundamentalmente diferente. A diferencia de FTX, <PERSON><PERSON><PERSON>, BlockFi o Luna (que dependían de la custodia centralizada, una gestión de activos opaca y posiciones apalancadas de riesgo), Sky USD utiliza smart contracts descentralizados, auditados y transparentes, y mantiene una total transparencia en la cadena. Tú mantienes el control total de tus fondos, lo que reduce significativamente los riesgos de contraparte asociados a las quiebras de entidades centralizadas.", "earn.usd.ftx-difference-popup.title": "¿En qué se diferencia de FTX, <PERSON><PERSON><PERSON>, BlockFi o Luna?", "earn.usd.high-returns-popup.text": "Sky USD genera rendimientos principalmente a través de protocolos de finanzas descentralizadas (DeFi), que automatizan los préstamos entre particulares y la provisión de liquidez, eliminando los costes indirectos y los intermediarios de la banca tradicional. Esta eficiencia, combinada con sólidos controles de riesgo, permite obtener rendimientos significativamente más altos en comparación con los bancos tradicionales.", "earn.usd.high-returns-popup.title": "¿Cómo pueden ser los rendimientos tan altos, sobre todo en comparación con los bancos tradicionales?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD está totalmente respaldado y sobrecolateralizado por una combinación de activos digitales custodiados en smart contracts seguros y activos del mundo real como las letras del Tesoro de EE. UU. Las reservas se pueden auditar en tiempo real en la cadena, incluso desde Zeal, lo que aporta transparencia y seguridad. En el improbable caso de que Zeal cierre, tus activos permanecen seguros en la cadena, bajo tu control total y accesibles a través de otros monederos compatibles.", "earn.usd.how-is-sky-backed-popup.title": "¿Qué respaldo tiene <PERSON> y qué pasaría con mi dinero si Zeal quebrara?", "earn.usd.insurance-popup.text": "Los fondos de Sky USD no están asegurados por el FDIC ni respaldados por garantías gubernamentales tradicionales, porque es una cuenta basada en activos digitales, no una cuenta bancaria convencional. En su lugar, Sky gestiona toda la mitigación de riesgos a través de smart contracts auditados y protocolos DeFi cuidadosamente seleccionados, garantizando que los activos permanezcan seguros y transparentes.", "earn.usd.insurance-popup.title": "¿Mis fondos están asegurados o garantizados por alguna entidad (como el FDIC o similar)?", "earn.usd.lending-operations-popup.text": "Sky USD genera rendimiento prestando stablecoins a través de mercados de préstamos descentralizados como Morpho y Spark. Tus stablecoins se prestan a prestatarios que depositan un colateral significativamente mayor (como ETH o BTC) que el valor de su préstamo. Este enfoque, llamado sobrecolateralización, garantiza que siempre haya suficiente colateral para cubrir los préstamos, reduciendo enormemente el riesgo. Los intereses cobrados y las comisiones de liquidación ocasionales que pagan los prestatarios proporcionan rendimientos fiables, transparentes y seguros.", "earn.usd.lending-operations-popup.title": "Operaciones de préstamo", "earn.usd.market-making-operations-popup.text": "Sky USD obtiene rendimiento adicional al participar en exchanges descentralizados (AMM) como Curve o Uniswap. Al aportar liquidez (colocando tus stablecoins en pools que facilitan el trading de cripto), Sky USD captura las comisiones generadas por las operaciones. Estos pools de liquidez se seleccionan cuidadosamente para minimizar la volatilidad, utilizando principalmente pares de stablecoin a stablecoin para reducir significativamente riesgos como el impermanent loss, manteniendo tus activos seguros y accesibles.", "earn.usd.market-making-operations-popup.title": "Operaciones de creación de mercado", "earn.usd.treasury-operations-popup.text": "Sky USD genera un rendimiento estable y constante a través de inversiones de tesorería estratégicas. Parte de tus depósitos en stablecoins se asigna a activos del mundo real seguros y de bajo riesgo, principalmente bonos gubernamentales a corto plazo e instrumentos de crédito de alta seguridad. Este enfoque, similar a la banca tradicional, garantiza un rendimiento predecible y fiable. Tus activos permanecen seguros, líquidos y gestionados de forma transparente.", "earn.usd.treasury-operations-popup.title": "Operaciones de tesorería", "earn.view_earn.card_rechard_off": "No", "earn.view_earn.card_rechard_on": "Sí", "earn.view_earn.card_recharge": "Recarga de tarjeta", "earn.view_earn.total_balance_label": "Ganando un {percentage} anual", "earn.view_earn.total_earnings_label": "Ganancias totales", "earn.withdraw": "<PERSON><PERSON><PERSON>", "earn.withdraw.amount_to_withdraw": "Cantidad a retirar", "earn.withdraw.enter_amount": "Introduce una cantidad", "earn.withdraw.loading": "Cargando", "earn.withdraw.no_routes_found": "No se han encontrado rutas", "earn.withdraw.not_enough_balance": "<PERSON><PERSON> insuficiente", "earn.withdraw.select-currency.title": "Se<PERSON><PERSON><PERSON><PERSON> token", "earn.withdraw.select_to_token": "Se<PERSON><PERSON><PERSON><PERSON> token", "earn.withdraw.withdraw": "<PERSON><PERSON><PERSON>", "earn.withdraw_form.title": "<PERSON><PERSON><PERSON>", "earnings-view.earnings": "Ganancias totales", "edit-account-owners.add-owner.add-wallet": "<PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.add_wallet": "<PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.title": "<PERSON><PERSON><PERSON> de la tarjeta", "edit-account-owners.card-owners": "Titulares de la tarjeta", "edit-account-owners.external-wallet": "Mon<PERSON>ro externo", "editBankRecipient.title": "Editar destinatario", "editNetwork.addCustomRPC": "<PERSON><PERSON><PERSON>o RPC personalizado", "editNetwork.cannot_verify.subtitle": "El nodo RPC personalizado no responde. Comprueba la URL y vuelve a intentarlo.", "editNetwork.cannot_verify.title": "No podemos verificar el nodo RPC", "editNetwork.cannot_verify.try_again": "Reintentar", "editNetwork.customRPCNode": "Nodo RPC personalizado", "editNetwork.defaultRPC": "RPC predeterminado", "editNetwork.networkRPC": "RPC de la red", "editNetwork.rpc_url.cannot_be_empty": "Obligatorio", "editNetwork.rpc_url.not_a_valid_https_url": "Debe ser una URL HTTP(S) válida", "editNetwork.safetyWarning.subtitle": "Zeal no puede garantizar la privacidad, fiabilidad y seguridad de los RPC personalizados. ¿Seguro que quieres usar un nodo RPC personalizado?", "editNetwork.safetyWarning.title": "Los RPC personalizados pueden ser inseguros", "editNetwork.zealRPCNode": "Nodo RPC de Zeal", "editNetworkRpc.headerTitle": "Nodo RPC personalizado", "editNetworkRpc.rpcNodeUrl": "URL del nodo RPC", "editing-locked.modal.description": "A diferencia de las Aprobaciones, los Permisos no permiten editar el límite de gasto ni el vencimiento. Asegúrate de confiar en la dApp antes de enviar un Permiso.", "editing-locked.modal.title": "Edición blo<PERSON>ada", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Activando recarga", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Recarga activada", "enterCardnumber": "Indica el n.º de tarjeta", "error.connectivity_error.subtitle": "Revisa tu conexión y vuelve a intentarlo.", "error.connectivity_error.title": "Sin conexión a internet", "error.decrypt_incorrect_password.title": "Contrase<PERSON>", "error.encrypted_object_invalid_format.title": "<PERSON><PERSON>", "error.failed_to_fetch_google_auth_token.title": "No hemos podido acceder", "error.list.item.cta.action": "Reintentar", "error.trezor_action_cancelled.title": "Transacción rechazada", "error.trezor_device_used_elsewhere.title": "Dispositivo en uso en otra sesión", "error.trezor_method_cancelled.title": "No se pudo sincronizar Trezor", "error.trezor_permissions_not_granted.title": "No se pudo sincronizar Trezor", "error.trezor_pin_cancelled.title": "No se pudo sincronizar Trezor", "error.trezor_popup_closed.title": "No se pudo sincronizar Trezor", "error.unblock_account_number_and_sort_code_mismatch": "Número de cuenta y sort code no coinciden", "error.unblock_can_not_change_details_after_kyc": "No puedes cambiar los datos tras el KYC.", "error.unblock_hard_kyc_failure": "Estado de KYC inesperado", "error.unblock_invalid_faster_payment_configuration.title": "Este banco no admite Faster Payments", "error.unblock_invalid_iban": "IBAN no válido", "error.unblock_session_expired.title": "La sesión de Unblock ha caducado", "error.unblock_user_with_address_already_exists.title": "Ya existe una cuenta para esta dirección", "error.unblock_user_with_such_email_already_exists.title": "Ya existe un usuario con este email", "error.unknown_error.error_message": "<PERSON><PERSON><PERSON> error: ", "error.unknown_error.subtitle": "Lo sentimos. Si necesitas ayuda urgente, contacta con soporte y comparte los siguientes datos.", "error.unknown_error.title": "Error del sistema", "eth-cost-warning-modal.subtitle": "Los Smart Wallets funcionan en Ethereum, pero las comisiones son muy altas. Te recomendamos encarecidamente usar otras redes.", "eth-cost-warning-modal.title": "Evita Ethereum: las comisiones de red son altas", "exchange.form.button.chain_unsupported": "Red no compatible", "exchange.form.button.refreshing": "Actualizando", "exchange.form.error.asset_not_supported.button": "Selecciona otro activo", "exchange.form.error.asset_not_supported.description": "Bridge no admite este activo.", "exchange.form.error.asset_not_supported.title": "Activo no admitido", "exchange.form.error.bridge_quote_timeout.button": "Selecciona otro activo", "exchange.form.error.bridge_quote_timeout.description": "Prueba con otro par de tokens", "exchange.form.error.bridge_quote_timeout.title": "No se encontró ningún exchange", "exchange.form.error.different_receiver_not_supported.button": "Quitar destinatario alternativo", "exchange.form.error.different_receiver_not_supported.description": "Este exchange no permite enviar a otra dirección.", "exchange.form.error.different_receiver_not_supported.title": "Las direcciones de envío y recepción deben ser la misma.", "exchange.form.error.insufficient_input_amount.button": "Aumentar cantidad", "exchange.form.error.insufficient_liquidity.button": "Reducir canti<PERSON>", "exchange.form.error.insufficient_liquidity.description": "El bridge no tiene suficientes activos. Prueba con un importe menor.", "exchange.form.error.insufficient_liquidity.title": "Importe demasiado alto", "exchange.form.error.max_amount_exceeded.button": "Reducir canti<PERSON>", "exchange.form.error.max_amount_exceeded.description": "Se ha superado la cantidad máxima.", "exchange.form.error.max_amount_exceeded.title": "Cantidad demasiado alta", "exchange.form.error.min_amount_not_met.button": "Aumentar cantidad", "exchange.form.error.min_amount_not_met.description": "No se alcanza la cantidad mínima de exchange para este token.", "exchange.form.error.min_amount_not_met.description_with_amount": "La cantidad mínima de exchange es {amount}.", "exchange.form.error.min_amount_not_met.title": "Cantidad demasiado baja", "exchange.form.error.min_amount_not_met.title_increase": "Aumentar cantidad", "exchange.form.error.no_routes_found.button": "Selecciona otro activo", "exchange.form.error.no_routes_found.description": "No hay ninguna ruta de exchange disponible para esta combinación de token/red.", "exchange.form.error.no_routes_found.title": "No hay exchanges disponibles", "exchange.form.error.not_enough_balance.button": "Reducir canti<PERSON>", "exchange.form.error.not_enough_balance.description": "No tienes suficientes activos para la transacción.", "exchange.form.error.not_enough_balance.title": "<PERSON><PERSON> insuficiente", "exchange.form.error.slippage_passed_is_too_low.button": "Aumentar slippage", "exchange.form.error.slippage_passed_is_too_low.description": "El slippage permitido es demasiado bajo para este activo.", "exchange.form.error.slippage_passed_is_too_low.title": "Slippage demasiado bajo", "exchange.form.error.socket_internal_error.button": "Inténtalo más tarde", "exchange.form.error.socket_internal_error.description": "El socio de Bridge tiene problemas. Inténtalo más tarde.", "exchange.form.error.socket_internal_error.title": "Error en el socio de Bridge", "exchange.form.error.stargatev2_requires_fee_in_native": "<PERSON><PERSON><PERSON> {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Añade {amount} para completar la transacción", "exchange.form.error.stargatev2_requires_fee_in_native.title": "Se necesita más {symbol}", "expiration-info.modal.description": "El vencimiento es el tiempo que una app puede usar tus tokens. <PERSON><PERSON><PERSON> se a<PERSON>, pierde el acceso hasta que lo autorices de nuevo. Para mayor seguridad, usa un vencimiento corto.", "expiration-info.modal.title": "¿Qué es el vencimiento?", "expiration-time.high.modal.text": "Los plazos de vencimiento deben ser cortos y basarse en el tiempo que realmente los necesites. Los plazos largos son arriesgados, ya que dan a los estafadores más oportunidades de hacer un mal uso de tus tokens.", "expiration-time.high.modal.title": "<PERSON><PERSON><PERSON> de vencimiento largo", "failed.transaction.content": "Es probable que la transacción falle", "fee.unknown": "Desconocido", "feedback-request.leave-message": "De<PERSON> un mensaje", "feedback-request.not-now": "<PERSON>ora no", "feedback-request.title": "¡Gracias! ¿Cómo podemos mejorar Zeal?", "float.input.period": "Separador decimal", "gnosis-activate-card.info-popup.subtitle": "Usa chip y PIN en la primera compra.", "gnosis-activate-card.info-popup.title": "El primer pago requiere chip y PIN", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "Introduce el número de tu tarjeta para activarla.", "gnosis-activate-card.title": "Número de tarjeta", "gnosis-pay-re-kyc-widget.btn-text": "Verificar", "gnosis-pay-re-kyc-widget.title.not-started": "Verifica tu identidad", "gnosis-pay.login.cta": "Conectar cuenta", "gnosis-pay.login.title": "Ya tienes una cuenta de Gnosis Pay", "gnosis-signup.confirm.subtitle": "Busca el e-mail en la carpeta de spam", "gnosis-signup.confirm.title": "¿No has recibido el e-mail?", "gnosis-signup.continue": "<PERSON><PERSON><PERSON><PERSON>", "gnosis-signup.dont_link_accounts": "No vincular cuentas", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "Introduce <EMAIL>", "gnosis-signup.enter-email.title": "Introduce tu e-mail", "gnosis-signup.title": "He leído y acepto los <linkGnosisTNC>Términos y Condic.</linkGnosisTNC> <monovateTerms>Términos del titular de la tarjeta</monovateTerms> y los <linkMonerium>T&C de Monerium</linkMonerium>.", "gnosis-signup.verify-email.title": "Verificar e-mail", "gnosis.confirm.subtitle": "¿Sin código? Revisa tu número de teléfono", "gnosis.confirm.title": "Código enviado a {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Verificar", "gnosisPayAccountStatus.success.title": "Tarjeta importada", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay aún no está disponible en tu país", "gnosisPayNoActiveCardsFound.title": "No hay tarjetas activas", "gnosis_pay_card_delay_relay_not_empty_error.title": "No se pudo procesar tu transacción. Vuelve a intentarlo más tarde.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "No es posible obtener la tarjeta", "gnosiskyc.modal.approved.activate-free-card": "Activar tarjeta gratis", "gnosiskyc.modal.approved.button-text": "Depositar desde el banco", "gnosiskyc.modal.approved.title": "Ya tienes los datos de tu cuenta personal", "gnosiskyc.modal.failed.close": "<PERSON><PERSON><PERSON>", "gnosiskyc.modal.failed.title": "<PERSON>, nuestro partner <PERSON><PERSON> Pay no puede crearte una cuenta", "gnosiskyc.modal.in-progress.title": "La verificación de identidad puede tardar 24 horas o más. Ten paciencia.", "goToSettingsPopup.settings": "<PERSON><PERSON><PERSON><PERSON>", "goToSettingsPopup.title": "Activa las notificaciones en los ajustes de tu dispositivo cuando quieras", "google_file.error.failed_to_fetch_auth_token.button_title": "Intentar de nuevo", "google_file.error.failed_to_fetch_auth_token.subtitle": "Para usar tu archivo de recuperación, concédenos acceso en tu nube personal.", "google_file.error.failed_to_fetch_auth_token.title": "No hemos podido acceder", "hidden_tokens.widget.emptyState": "No hay tokens ocultos", "how_to_connect_to_metamask.got_it": "OK, entendido", "how_to_connect_to_metamask.story.subtitle": "Cambia fácilmente entre Zeal y otros monederos cuando quieras.", "how_to_connect_to_metamask.story.title": "Zeal funciona junto a otros monederos", "how_to_connect_to_metamask.why_switch": "¿Por qué cambiar entre Zeal y otros monederos?", "how_to_connect_to_metamask.why_switch.description": "No importa qué monedero elijas, siempre tendrás los Controles de seguridad de Zeal protegiéndote de webs y transacciones maliciosas.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "Sabemos que es difícil dar el salto y empezar a usar un monedero nuevo. Por eso, hemos hecho que sea fácil usar Zeal junto a tu monedero actual. Cambia cuando quieras.", "import-bank-transfer-owner.banner.title": "Cambió el monedero. Para usar transferencias aquí, impórtalo.", "import-bank-transfer-owner.title": "Importa el monedero para usar transferencias en este dispositivo", "import_gnosispay_wallet.add-another-card-owner.footnote": "Importa clave o frase del titular", "import_gnosispay_wallet.primaryText": "Importar monedero Gnosis Pay", "injected-wallet": "Monedero del navegador", "intercom.getHelp": "Obtener ayuda", "invalid_iban.got_it": "Entendido", "invalid_iban.subtitle": "El IBAN introducido no es válido. Comprueba que los datos son correctos e inténtalo de nuevo.", "invalid_iban.title": "IBAN no válido", "keypad-0": "Tecla 0 del teclado", "keypad-1": "Tecla 1 del teclado", "keypad-2": "Tecla 2 del teclado", "keypad-3": "Tecla 3 del teclado", "keypad-4": "Tecla 4 del teclado", "keypad-5": "Tecla 5 del teclado", "keypad-6": "Tecla 6 del teclado", "keypad-7": "Tecla 7 del teclado", "keypad-8": "Tecla 8 del teclado", "keypad-9": "Tecla 9 del teclado", "keypad.biometric-button": "Botón biométrico del teclado", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Frase secreta guardada 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "Ver frase", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Guarda una copia segura y sin conexión de tu frase secreta para poder recuperar tus activos más adelante", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "No intentes adivinar la palabra", "keystore.write_secret_phrase.before_you_begin.first_point": "Entiendo que cualquiera con mi frase secreta puede transferir mis activos", "keystore.write_secret_phrase.before_you_begin.second_point": "Soy responsable de mantener mi frase secreta en un lugar seguro y privado", "keystore.write_secret_phrase.before_you_begin.subtitle": "<PERSON> y acepta los siguientes puntos:", "keystore.write_secret_phrase.before_you_begin.third_point": "Estoy en un lugar privado, sin gente ni cámaras a mi alrededor", "keystore.write_secret_phrase.before_you_begin.title": "<PERSON><PERSON> em<PERSON>zar", "keystore.write_secret_phrase.secret_phrase_test.title": "¿Cuál es la palabra {count} de tu frase secreta?", "keystore.write_secret_phrase.test_ps.lets_do_it": "Vamos allá", "keystore.write_secret_phrase.test_ps.subtitle": "Necesitarás tu frase secreta para restaurar tu cuenta en este u otros dispositivos. Vamos a comprobar que la has escrito correctamente.", "keystore.write_secret_phrase.test_ps.subtitle2": "Te pediremos {count} palabras de tu frase.", "keystore.write_secret_phrase.test_ps.title": "Probar recuperación de cuenta", "kyc.modal.approved.button-text": "<PERSON><PERSON>", "kyc.modal.approved.subtitle": "Tu verificación se ha completado. Ya puedes hacer transferencias bancarias sin límites.", "kyc.modal.approved.title": "Transferencias bancarias activadas", "kyc.modal.continue-with-partner.button-text": "<PERSON><PERSON><PERSON><PERSON>", "kyc.modal.continue-with-partner.subtitle": "Te redirigiremos a nuestro socio para que recojan tu documentación y completes la solicitud de verificación.", "kyc.modal.continue-with-partner.title": "Continuar con nuestro socio", "kyc.modal.failed.unblock.subtitle": "Unblock no ha aprobado tu verificación de identidad y no puede ofrecerte servicios de transferencia bancaria", "kyc.modal.failed.unblock.title": "Solicitud a Unblock no aprobada", "kyc.modal.paused.button-text": "<PERSON><PERSON><PERSON><PERSON>", "kyc.modal.paused.subtitle": "Parece que parte de tu información es incorrecta. Vuelve a intentarlo y comprueba bien tus datos antes de enviarlos.", "kyc.modal.paused.title": "<PERSON>s datos parecen <PERSON>os", "kyc.modal.pending.button-text": "<PERSON><PERSON><PERSON>", "kyc.modal.pending.subtitle": "La verificación suele tardar menos de 10 minutos, pero a veces puede llevar un poco más de tiempo.", "kyc.modal.pending.title": "Te mantendremos al día", "kyc.modal.required.cta": "Iniciar verificación", "kyc.modal.required.subtitle": "Has alcanzado el límite de transacciones. Verifica tu identidad para continuar. Suele llevar un par de minutos y requiere algunos datos y documentos personales.", "kyc.modal.required.title": "Se requiere verificación de identidad", "kyc.submitted": "Solicitud enviada", "kyc.submitted_short": "Enviado", "kyc_status.completed_status": "Completada", "kyc_status.failed_status": "Fallida", "kyc_status.paused_status": "En revisión", "kyc_status.subtitle": "Transferencias ban<PERSON>", "kyc_status.subtitle.wrong_details": "<PERSON><PERSON>", "kyc_status.subtitle_in_progress": "En curso", "kyc_status.title": "Verificando identidad", "label.close": "<PERSON><PERSON><PERSON>", "label.saving": "Guardando...", "labels.this-month": "<PERSON>ste mes", "labels.today": "Hoy", "labels.yesterday": "Ayer", "language.selector.title": "Idioma", "ledger.account_loaded.imported": "<PERSON><PERSON><PERSON><PERSON>", "ledger.add.success.title": "Ledger conectado correctamente 🎉", "ledger.connect.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.connect.step1": "Conecta Ledger a tu dispositivo", "ledger.connect.step2": "Abre la app de Ethereum en Ledger", "ledger.connect.step3": "<PERSON><PERSON>, sincroniza tu Ledger 👇", "ledger.connect.subtitle": "Sigue estos pasos para importar tus monederos de Ledger a Zeal", "ledger.connect.title": "Conectar Ledger a Zeal", "ledger.error.ledger_is_locked.subtitle": "Desbloquea tu Ledger y abre la app de Ethereum", "ledger.error.ledger_is_locked.title": "Ledger está bloqueado", "ledger.error.ledger_not_connected.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.error.ledger_not_connected.subtitle": "Conecta tu monedero físico y abre la app de Ethereum", "ledger.error.ledger_not_connected.title": "Ledger no está conectado", "ledger.error.ledger_running_non_eth_app.title": "La app de Ethereum no está abierta", "ledger.error.user_trx_denied_by_user.action": "<PERSON><PERSON><PERSON>", "ledger.error.user_trx_denied_by_user.subtitle": "Has rechazado la transacción en tu monedero físico", "ledger.error.user_trx_denied_by_user.title": "Transacción rechazada", "ledger.hd_path.bip44.subtitle": "p. ej., Metamask, Trezor", "ledger.hd_path.bip44.title": "Estándar BIP44", "ledger.hd_path.ledger_live.subtitle": "Predeterminado", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Heredado", "ledger.hd_path.phantom.subtitle": "p. ej., <PERSON>", "ledger.select.hd_path.subtitle": "Las rutas HD son la forma en que los monederos hardware organizan sus cuentas. Es parecido a cómo un índice organiza las páginas de un libro.", "ledger.select.hd_path.title": "Seleccionar ruta HD", "ledger.select_account.import_wallets_count": "{count,plural,=0{Ningún monedero selecc.} one{Importar monedero} other{Importar {count} wallets}}", "ledger.select_account.path_settings": "<PERSON>ju<PERSON>s de ruta", "ledger.select_account.subtitle": "¿No ves los monederos que esperabas? Prueba a cambiar los ajustes de ruta", "ledger.select_account.subtitle.group_header": "<PERSON><PERSON><PERSON>", "ledger.select_account.title": "Importar mon<PERSON><PERSON> Ledger", "legend.lending-operations": "Operaciones de préstamo", "legend.market_making-operations": "Operaciones de creación de mercado", "legend.treasury-operations": "Operaciones de tesorería", "link-existing-monerium-account-sign.button": "Vincular Zeal", "link-existing-monerium-account-sign.subtitle": "Ya tienes una cuenta de Monerium.", "link-existing-monerium-account-sign.title": "Vincula Zeal a tu cuenta de Monerium", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Ya tienes una cuenta de Monerium. Ve a la app de Monerium para completar la configuración.", "link-existing-monerium-account.title": "Ve a Monerium para vincular tu cuenta", "loading.pin": "Cargando PIN...", "lockScreen.passwordIncorrectMessage": "Contrase<PERSON>", "lockScreen.passwordRequiredMessage": "Se requiere contraseña", "lockScreen.unlock.header": "Desb<PERSON>que<PERSON>", "lockScreen.unlock.subheader": "Usa tu contraseña para desbloquear Zeal", "mainTabs.activity.label": "Actividad", "mainTabs.browse.label": "Explorar", "mainTabs.browse.title": "Explorar", "mainTabs.card.label": "Tarjeta", "mainTabs.portfolio.label": "<PERSON><PERSON>", "mainTabs.rewards.label": "Recompensas", "makeSpendable.cta": "Habilitar fondos", "makeSpendable.holdAsCash": "Mantener como efectivo", "makeSpendable.shortText": "<PERSON>ana un {apy} anual", "makeSpendable.title": "{amount} recibidos", "merchantCategory.agriculture": "Agricultura", "merchantCategory.alcohol": "Alcohol", "merchantCategory.antiques": "Antigüeda<PERSON>", "merchantCategory.appliances": "Electrodomésticos", "merchantCategory.artGalleries": "Galerías de arte", "merchantCategory.autoRepair": "Reparación de coches", "merchantCategory.autoRepairService": "Servicio de reparación de coches", "merchantCategory.beautyFitnessSpas": "Belleza, fitness y spas", "merchantCategory.beautyPersonalCare": "Belleza y cuidado personal", "merchantCategory.billiard": "<PERSON><PERSON>", "merchantCategory.books": "Libros", "merchantCategory.bowling": "<PERSON><PERSON>", "merchantCategory.businessProfessionalServices": "Servicios profesionales y de empresa", "merchantCategory.carRental": "Al<PERSON>ler de coches", "merchantCategory.carWash": "Lavado de coches", "merchantCategory.cars": "Coches", "merchantCategory.casino": "Casino", "merchantCategory.casinoGambling": "Casino y apuestas", "merchantCategory.cellular": "Móvil", "merchantCategory.charity": "Donativos", "merchantCategory.childcare": "Cuidado de niños", "merchantCategory.cigarette": "Tabaco", "merchantCategory.cinema": "Cine", "merchantCategory.cinemaEvents": "Cine y eventos", "merchantCategory.cleaning": "Limpieza", "merchantCategory.cleaningMaintenance": "Limpieza y mantenimiento", "merchantCategory.clothes": "Ropa", "merchantCategory.clothingServices": "Arreglos de ropa", "merchantCategory.communicationServices": "Servicios de comunicación", "merchantCategory.construction": "Construcción", "merchantCategory.cosmetics": "Cosmé<PERSON><PERSON>", "merchantCategory.craftsArtSupplies": "Manualidades y arte", "merchantCategory.datingServices": "Servicios de citas", "merchantCategory.delivery": "Reparto a domicilio", "merchantCategory.dentist": "<PERSON><PERSON>", "merchantCategory.departmentStores": "Grandes almacenes", "merchantCategory.directMarketingSubscription": "Marketing directo y suscripciones", "merchantCategory.discountStores": "Tiendas de descuento", "merchantCategory.drugs": "Farmacia", "merchantCategory.dutyFree": "Duty Free", "merchantCategory.education": "Educación", "merchantCategory.electricity": "Electricidad", "merchantCategory.electronics": "Electrónica", "merchantCategory.emergencyServices": "Servicios de emergencia", "merchantCategory.equipmentRental": "Alquiler de equipos", "merchantCategory.evCharging": "Carga de vehículos eléctricos", "merchantCategory.financialInstitutions": "Entidades financieras", "merchantCategory.financialProfessionalServices": "Servicios financieros y profesionales", "merchantCategory.finesPenalties": "Multas y sanciones", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "Vuelos", "merchantCategory.flowers": "Flores", "merchantCategory.flowersGarden": "Flores y jardín", "merchantCategory.food": "<PERSON><PERSON><PERSON>", "merchantCategory.freight": "Transporte de mercancías", "merchantCategory.fuel": "Combustible", "merchantCategory.funeralServices": "Servicios funerarios", "merchantCategory.furniture": "<PERSON><PERSON><PERSON>", "merchantCategory.games": "<PERSON><PERSON><PERSON>", "merchantCategory.gas": "Gasolina", "merchantCategory.generalMerchandiseRetail": "Mercancías generales y minoristas", "merchantCategory.gifts": "Regalos", "merchantCategory.government": "Gobierno", "merchantCategory.governmentServices": "<PERSON><PERSON><PERSON>", "merchantCategory.hardware": "Ferretería", "merchantCategory.healthMedicine": "Salud y medicina", "merchantCategory.homeImprovement": "Mejoras para el hogar", "merchantCategory.homeServices": "Servicios para el hogar", "merchantCategory.hotel": "Hotel", "merchantCategory.housing": "Vivienda", "merchantCategory.insurance": "<PERSON><PERSON><PERSON>", "merchantCategory.internet": "Internet", "merchantCategory.kids": "<PERSON><PERSON><PERSON>", "merchantCategory.laundry": "Lavandería", "merchantCategory.laundryCleaningServices": "Lavandería y limpieza", "merchantCategory.legalGovernmentFees": "Tasas legales y gubernamentales", "merchantCategory.luxuries": "<PERSON><PERSON>", "merchantCategory.luxuriesCollectibles": "Lujo y coleccionables", "merchantCategory.magazines": "Revistas", "merchantCategory.magazinesNews": "Revistas y noticias", "merchantCategory.marketplaces": "Marketplaces", "merchantCategory.media": "Medios", "merchantCategory.medicine": "Medicina", "merchantCategory.mobileHomes": "Casas móviles", "merchantCategory.moneyTransferCrypto": "Transferencias de dinero y cripto", "merchantCategory.musicRelated": "Música", "merchantCategory.musicalInstruments": "Instrumentos musicales", "merchantCategory.optics": "Óptica", "merchantCategory.organizationsClubs": "Organizaciones y clubes", "merchantCategory.other": "<PERSON><PERSON><PERSON>", "merchantCategory.parking": "Aparcamiento", "merchantCategory.pawnShops": "Casas de empeño", "merchantCategory.pets": "Mascotas", "merchantCategory.photoServicesSupplies": "Fotografía: servicios y suministros", "merchantCategory.postalServices": "Servicios postales", "merchantCategory.professionalServicesOther": "Ser<PERSON><PERSON> profesional<PERSON> (otros)", "merchantCategory.publicTransport": "Transporte público", "merchantCategory.purchases": "Compras", "merchantCategory.purchasesMiscServices": "Compras y servicios varios", "merchantCategory.recreationServices": "<PERSON><PERSON><PERSON> recreativos", "merchantCategory.religiousGoods": "<PERSON><PERSON><PERSON><PERSON> religio<PERSON>s", "merchantCategory.secondhandRetail": "Tiendas de segunda mano", "merchantCategory.shoeHatRepair": "Reparación de calzado y sombreros", "merchantCategory.shoeRepair": "Reparación de calzado", "merchantCategory.softwareApps": "Software y aplicaciones", "merchantCategory.specializedRepairs": "Reparaciones especializadas", "merchantCategory.sport": "Deporte", "merchantCategory.sportingGoods": "Artículos deportivos", "merchantCategory.sportingGoodsRecreation": "Artículos deportivos y ocio", "merchantCategory.sportsClubsFields": "Clubes y campos deportivos", "merchantCategory.stationaryPrinting": "Papelería e imprenta", "merchantCategory.stationery": "Papelería", "merchantCategory.storage": "Almacenamiento", "merchantCategory.taxes": "Impuestos", "merchantCategory.taxi": "Taxi", "merchantCategory.telecomEquipment": "Equipos de telecomunicaciones", "merchantCategory.telephony": "Telefonía", "merchantCategory.tobacco": "Tabaco", "merchantCategory.tollRoad": "<PERSON><PERSON><PERSON>", "merchantCategory.tourismAttractionsAmusement": "Turismo, atracciones y ocio", "merchantCategory.towing": "<PERSON><PERSON><PERSON>", "merchantCategory.toys": "<PERSON><PERSON><PERSON>", "merchantCategory.toysHobbies": "Juguetes y aficiones", "merchantCategory.trafficFine": "Multa de tráfico", "merchantCategory.train": "<PERSON>ren", "merchantCategory.travelAgency": "Agencia de viajes", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, radio y streaming", "merchantCategory.utilities": "Suministros", "merchantCategory.waterTransport": "Transporte acuático", "merchantCategory.wholesaleClubs": "Clubes de venta al por mayor", "metaMask.subtitle": "Activa el Modo MetaMask para redirigir todas las conexiones de MetaMask a Zeal. Al hacer clic en MetaMask en las dApps, te conectarás a Zeal.", "metaMask.title": "¿No puedes conectarte con Zeal?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Abre tu aplicación bancaria", "monerium-bank-deposit.buttet-point.receive-crypto": "Recibe EUR digitales", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Envía {fiatCurrencyCode} a tu cuenta", "monerium-bank-deposit.deposit-account-country": "<PERSON><PERSON>", "monerium-bank-deposit.header": "{fullName}Cuenta personal", "monerium-bank-details.account-name": "Nombre de la cuenta", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT copiado", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT copiado", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN copiado", "monerium-bank-details.to-wallet": "Al monedero", "monerium-bank-details.transfer-fee": "Comisión de transferencia", "monerium-bank-transfer.enable-card.bullet-1": "Completa la verificación de identidad", "monerium-bank-transfer.enable-card.bullet-2": "Obt<PERSON> los datos de tu cuenta personal", "monerium-bank-transfer.enable-card.bullet-3": "Deposita desde tu cuenta bancaria", "monerium-card-delay-relay.success.cta": "<PERSON><PERSON><PERSON>", "monerium-card-delay-relay.success.subtitle": "Por seguridad, los cambios en los ajustes de la tarjeta tardan 3 minutos en procesarse.", "monerium-card-delay-relay.success.title": "Vuelve en 3 min para seguir con la configuración de Monerium", "monerium-deposit.account-details-info-popup.bullet-point-1": "Cualquier {fiatCurrencyCode} que envíes a esta cuenta se convertirá automáticamente en {cryptoCurrencyCode} tokens en la {cryptoCurrencyChain} Chain y se enviará a tu monedero", "monerium-deposit.account-details-info-popup.bullet-point-2": "ENVÍA SOLO {fiatCurrencyCode} ({fiatCurrencySymbol}) a tu cuenta", "monerium-deposit.account-details-info-popup.title": "Datos de tu cuenta", "monerium.check_order_status.sending": "Enviando", "monerium.not-eligible.cta": "Volver", "monerium.not-eligible.subtitle": "Monerium no puede abrir una cuenta para ti. Por favor, elige otro proveedor.", "monerium.not-eligible.title": "Prueba con otro proveedor", "monerium.setup-card.cancel": "<PERSON><PERSON><PERSON>", "monerium.setup-card.continue": "<PERSON><PERSON><PERSON><PERSON>", "monerium.setup-card.create_account": "<PERSON><PERSON><PERSON> cuenta", "monerium.setup-card.login": "Acceder a Gnosis Pay", "monerium.setup-card.subtitle": "Crea o inicia sesión en tu cuenta de Gnosis Pay para activar los depósitos bancarios instantáneos.", "monerium.setup-card.subtitle_personal_account": "Consigue tu cuenta personal con Gnosis Pay en minutos:", "monerium.setup-card.title": "Activar depósitos bancarios", "moneriumDepositSuccess.goToWallet": "<PERSON><PERSON> al monedero", "moneriumDepositSuccess.title": "{symbol} recibido", "moneriumInfo.fees": "Tienes un 0 % en comisiones", "moneriumInfo.registration": "Monerium es una Entidad de Dinero Electrónico autorizada y regulada por la ley islandesa de dinero electrónico n.º 17/2013. <link>Saber más</link>", "moneriumInfo.selfCustody": "El dinero digital que recibes está bajo tu control y nadie más tendrá acceso a tus activos.", "moneriumWithdrawRejected.supportText": "No pudimos completar tu transferencia. Inténtalo de nuevo y, si sigue sin funcionar, <link>contacta con soporte.</link>", "moneriumWithdrawRejected.title": "Transferencia revertida", "moneriumWithdrawRejected.tryAgain": "Reintentar", "moneriumWithdrawSuccess.supportText": "<PERSON>uede tardar 24 h para que tu{br}destinatario reciba los fondos", "moneriumWithdrawSuccess.title": "Enviado", "monerium_enable_banner.text": "Activar transferencias bancarias", "monerium_error_address_re_link_required.title": "El monedero debe volver a vincularse a Monerium", "monerium_error_duplicate_order.title": "Orden duplicada", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "&lt; {limit}", "mt-pelerin-fork.options.chf.primary": "<PERSON>", "mt-pelerin-fork.options.chf.short": "Instantáneo y gratis con Mt Pelerin", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Instantáneo y gratis con Monerium", "mt-pelerin-fork.title": "¿Qué quieres depositar?", "mtPelerinProviderInfo.fees": "Pagas un 0 % de comisiones", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd está afiliada a SO-FIT, un organismo de autorregulación reconocido por la Autoridad Financiera Suiza (FINMA) en virtud de la Ley contra el Blanqueo de Capitales. <link>Más información</link>", "mtPelerinProviderInfo.selfCustody": "El dinero digital que recibes es tuyo y nadie más tendrá control sobre tus activos", "network-fee-widget.title": "Comisiones", "network.edit.verifying_rpc": "Verificando RPC", "network.editRpc.predefined_network_info.subtitle": "Al igual que una VPN, Zeal utiliza RPC que evitan que se rastreen tus datos personales.{br}{br}Los RPC predeterminados de Zeal son proveedores de RPC fiables y probados.", "network.editRpc.predefined_network_info.title": "RPC de privacidad de Zeal", "network.filter.update_rpc_success": "Nodo RPC guardado", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "Todas las redes", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "<PERSON><PERSON><PERSON> redes", "networks.filter.add_modal.chain_list.subtitle": "Añade cualquier red EVM", "networks.filter.add_modal.chain_list.title": "<PERSON><PERSON> a <PERSON>list", "networks.filter.add_modal.dapp_tip.subtitle": "En tus dApps favoritas, simplemente cambia a la red EVM que quieras usar y Zeal te preguntará si quieres añadirla a tu monedero.", "networks.filter.add_modal.dapp_tip.title": "O añade una red desde cualquier dApp", "networks.filter.add_networks.subtitle": "Compatible con todas las redes EVM", "networks.filter.add_networks.title": "<PERSON><PERSON><PERSON> redes", "networks.filter.add_test_networks.title": "<PERSON><PERSON><PERSON> test<PERSON>", "networks.filter.tab.netwokrs": "Redes", "networks.filter.testnets.title": "Testnets", "nft.widget.emptystate": "No hay coleccionables en el monedero", "nft_collection.change_account_picture.subtitle": "¿Seguro que quieres actualizar tu foto de perfil?", "nft_collection.change_account_picture.title": "Actualizar foto de perfil con NFT", "nfts.allNfts.pricingPopup.description": "Los precios se basan en la última operación.", "nfts.allNfts.pricingPopup.title": "Precio de los coleccionables", "no-passkeys-found.modal.cta": "<PERSON><PERSON><PERSON>", "no-passkeys-found.modal.subtitle": "No podemos detectar ninguna passkey de Zeal en este dispositivo. Asegúrate de haber iniciado sesión en la cuenta de la nube que usaste para crear tu Smart Wallet.", "no-passkeys-found.modal.title": "No se encontraron passkeys", "notValidEmail.title": "La dirección de e-mail no es válida", "notValidPhone.title": "Este número de teléfono no es válido", "notification-settings.title": "Ajustes de notificaciones", "notification-settings.toggles.active-wallets": "Monederos activos", "notification-settings.toggles.bank-transfers": "Transferencias ban<PERSON>", "notification-settings.toggles.card-payments": "Pagos con tarjeta", "notification-settings.toggles.readonly-wallets": "Monederos de solo lectura", "ntft.groupHeader.text": "Coleccionables", "on_ramp.crypto_completed": "Completado", "on_ramp.fiat_completed": "Completado", "onboarding-widget.subtitle.card_created_from_order.left": "Tarjeta Visa", "onboarding-widget.subtitle.card_created_from_order.right": "Activar tarjeta", "onboarding-widget.subtitle.card_order_ready.left": "Tarjeta Visa física", "onboarding-widget.subtitle.default": "Transferencias y tarjeta Visa", "onboarding-widget.title.card-order-in-progress": "Continuar pedido de la tarjeta", "onboarding-widget.title.card_created_from_order": "Tarjeta enviada", "onboarding-widget.title.kyc_approved": "Transferencias y tarjeta listas", "onboarding-widget.title.kyc_failed": "No es posible crear la cuenta", "onboarding-widget.title.kyc_not_started": "Continuar configuración", "onboarding-widget.title.kyc_started_documents_requested": "Completar verificación", "onboarding-widget.title.kyc_started_resubmission_requested": "Reintentar verificación", "onboarding-widget.title.kyc_started_verification_in_progress": "Verificando identidad", "onboarding.loginOrCreateAccount.amountOfAssets": "+10 mil millones de $ en activos", "onboarding.loginOrCreateAccount.cards.subtitle": "Solo disponible en algunas regiones. Al continuar, aceptas nuestros <Terms>Términos</Terms> y nuestra <PrivacyPolicy>Política de Privacidad</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Tarjeta Visa con alta{br}rentabilidad y sin comisiones", "onboarding.loginOrCreateAccount.createAccount": "<PERSON><PERSON><PERSON> cuenta", "onboarding.loginOrCreateAccount.earn.subtitle": "La rentabilidad varía; capital en riesgo. Al continuar, aceptas nuestros <Terms>Términos</Terms> y nuestra <PrivacyPolicy>Política de Privacidad</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "Gana un {percent} anual{br}Con la confianza de {currencySymbol}+5 mil millones de $", "onboarding.loginOrCreateAccount.earningPerYear": "<PERSON><PERSON> un {percent}{br} anual", "onboarding.loginOrCreateAccount.login": "In<PERSON><PERSON>", "onboarding.loginOrCreateAccount.trading.subtitle": "Capital en riesgo. Al continuar, aceptas nuestros <Terms>Términos</Terms> y nuestra <PrivacyPolicy>Política de Privacidad</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Invierte en todo,{br}de BTC a S&P", "onboarding.loginOrCreateAccount.trustedBy": "Mercados de dinero digital{br}Con la confianza de {assets}", "onboarding.wallet_stories.close": "<PERSON><PERSON><PERSON>", "onboarding.wallet_stories.previous": "Anterior", "order-earn-deposit-bridge.deposit": "<PERSON><PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.into": "En", "otpIncorrectMessage": "El código de confirmación es incorrecto", "passkey-creation-not-possible.modal.close": "<PERSON><PERSON><PERSON>", "passkey-creation-not-possible.modal.subtitle": "No hemos podido crear una passkey para tu monedero. Asegúrate de que tu dispositivo sea compatible e inténtalo de nuevo. <link>Contactar soporte</link> si el problema persiste.", "passkey-creation-not-possible.modal.title": "No se pudo crear la passkey", "passkey-not-supported-in-mobile-browser.modal.cta": "<PERSON><PERSON><PERSON>", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Los Smart Wallets no son compatibles con navegadores móviles.", "passkey-not-supported-in-mobile-browser.modal.title": "Descarga la app de Zeal para continuar", "passkey-recovery.recovering.deploy-signer.loading-text": "Verificando passkey", "passkey-recovery.recovering.loading-text": "<PERSON><PERSON><PERSON><PERSON> monedero", "passkey-recovery.recovering.signer-not-found.subtitle": "No hemos podido vincular tu passkey a un monedero activo. Si tienes fondos en tu monedero, contacta con el equipo de Zeal para obtener ayuda.", "passkey-recovery.recovering.signer-not-found.title": "No se encontró ningún monedero", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "Probar con otra passkey", "passkey-recovery.select-passkey.banner.subtitle": "Inicia sesión en la cuenta correcta del dispositivo. Las passkeys son específicas de cada cuenta.", "passkey-recovery.select-passkey.banner.title": "¿No ves la passkey de tu monedero?", "passkey-recovery.select-passkey.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON> passkey", "passkey-recovery.select-passkey.subtitle": "Selecciona la passkey vinculada a tu monedero para recuperar el acceso.", "passkey-recovery.select-passkey.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> passkey", "passkey-story_1.subtitle": "Con un Smart Wallet puedes pagar las comisiones de red con la mayoría de tokens y olvidarte de ellas.", "passkey-story_1.title": "Olvídate del gas: paga las comisiones de red con la mayoría de tokens", "passkey-story_2.subtitle": "Basado en los contratos inteligentes de Safe, líderes en el sector, que protegen más de 100 mil millones de dólares en más de 20 millones de monederos.", "passkey-story_2.title": "Protegido por <PERSON>", "passkey-story_3.subtitle": "Los Smart Wallets funcionan en las principales redes compatibles con Ethereum. Comprueba las redes compatibles antes de enviar activos.", "passkey-story_3.title": "Compatible con las principales redes EVM", "password.add.header": "<PERSON><PERSON><PERSON> con<PERSON>", "password.add.includeLowerAndUppercase": "Letras mayúsculas y minúsculas", "password.add.includesNumberOrSpecialChar": "Un número o símbolo", "password.add.info.subtitle": "No enviamos tu contraseña a nuestros servidores ni hacemos una copia por ti", "password.add.info.t_and_c": "<PERSON> continuar, aceptas nuestros <Terms>Términos</Terms> y nuestra <PrivacyPolicy>Política de Privacidad</PrivacyPolicy>", "password.add.info.title": "Tu contraseña se guarda en este dispositivo", "password.add.inputPlaceholder": "<PERSON><PERSON><PERSON> con<PERSON>", "password.add.shouldContainsMinCharsCheck": "10+ caracteres", "password.add.subheader": "Usarás tu contraseña para desbloquear Zeal", "password.add.success.title": "Contraseña creada 🔥", "password.confirm.header": "Confirmar con<PERSON>", "password.confirm.passwordDidNotMatch": "Las contraseñas deben coincidir", "password.confirm.subheader": "Introduce tu contraseña una vez más", "password.create_pin.subtitle": "Este código de acceso bloquea la app de Zeal", "password.create_pin.title": "Crea tu código de acceso", "password.enter_pin.title": "Introduce el código de acceso", "password.incorrectPin": "Código de acceso incorrecto", "password.pin_is_not_same": "El código de acceso no coincide", "password.placeholder.enter": "Introduce la contraseña", "password.placeholder.reenter": "Vuelve a introducir la contraseña", "password.re_enter_pin.subtitle": "Introduce el mismo código de acceso otra vez", "password.re_enter_pin.title": "Confirmar c<PERSON><PERSON> de acceso", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "Pendiente", "pending-send.details.pending": "Pendiente", "pending-send.details.processing": "Procesando", "permit-info.modal.description": "Los permisos son solicitudes que, si se firman, permiten a las apps mover tus tokens en tu nombre, por ejemplo, para un swap.{br}Los permisos son parecidos a las aprobaciones, pero firmarlos no tiene comisión de red.", "permit-info.modal.title": "¿Qué son los permisos?", "permit.edit-expiration": "Editar {currency} vencimiento", "permit.edit-limit": "Editar {currency} límite de gasto", "permit.edit-modal.expiresIn": "Vence en…", "permit.expiration-warning": "{currency} aviso de vencimiento", "permit.expiration.info": "{currency} información de vencimiento", "permit.expiration.never": "Nunca", "permit.spend-limit.info": "{currency} información del límite de gasto", "permit.spend-limit.warning": "{currency} advertencia del límite de gasto", "phoneNumber.title": "número de teléfono", "physicalCardOrderFlow.cardOrdered": "<PERSON><PERSON><PERSON><PERSON> pedida", "physicalCardOrderFlow.city": "Ciudad", "physicalCardOrderFlow.orderCard": "Pedir tarjeta", "physicalCardOrderFlow.postcode": "Código postal", "physicalCardOrderFlow.shippingAddress.subtitle": "Aquí se enviará tu tarjeta", "physicalCardOrderFlow.shippingAddress.title": "Dirección de envío", "physicalCardOrderFlow.street": "Calle", "placeholderDapps.1inch.description": "Intercambia con las mejores rutas", "placeholderDapps.aave.description": "Presta y pide prestados tokens", "placeholderDapps.bungee.description": "Usa el Bridge entre redes con las mejores rutas", "placeholderDapps.compound.description": "Presta y pide prestados tokens", "placeholderDapps.cowswap.description": "Intercambia a las mejores tasas en Gnosis", "placeholderDapps.gnosis-pay.description": "Gestiona tu tarjeta Gnosis Pay", "placeholderDapps.jumper.description": "Usa el Bridge entre redes con las mejores rutas", "placeholderDapps.lido.description": "Haz staking de ETH por más ETH", "placeholderDapps.monerium.description": "Dinero electrónico y transferencias", "placeholderDapps.odos.description": "Intercambia con las mejores rutas", "placeholderDapps.stargate.description": "Haz Bridge o staking por <14 % de APY", "placeholderDapps.uniswap.description": "Uno de los exchanges más populares", "pleaseAllowNotifications.cardPayments": "Pagos con tarjeta", "pleaseAllowNotifications.customiseInSettings": "Personalizar en ajustes", "pleaseAllowNotifications.enable": "Activar", "pleaseAllowNotifications.forWalletActivity": "Para la actividad del monedero", "pleaseAllowNotifications.title": "Recibe notificaciones del monedero", "pleaseAllowNotifications.whenReceivingAssets": "Al recibir activos", "portfolio.quick-actions.add_funds": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.buy": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.deposit": "Depositar", "portfolio.quick-actions.send": "Enviar", "portfolio.view.lastRefreshed": "Actualizado {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Recarga tu AVAX de testnet", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "<PERSON><PERSON> <PERSON>", "portfolio.view.topupTestNet.BscTestnet.primary": "Recarga tu BNB de testnet", "portfolio.view.topupTestNet.BscTestnet.secondary": "<PERSON><PERSON> <PERSON>", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Recarga tu SepETH de testnet", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "<PERSON><PERSON> al Faucet de Sepolia", "portfolio.view.topupTestNet.FantomTestnet.primary": "Recarga tu FTM de testnet", "portfolio.view.topupTestNet.FantomTestnet.secondary": "<PERSON><PERSON> <PERSON>", "privateKeyConfirmation.banner.subtitle": "Tu clave privada da acceso a tu cartera y fondos. Solo los estafadores te la pedirán.", "privateKeyConfirmation.banner.title": "Entiendo los riesgos", "privateKeyConfirmation.title": "NUNCA compartas tu clave privada con nadie", "rating-request.not-now": "<PERSON>ora no", "rating-request.title": "¿Recomendar<PERSON>?", "receive_funds.address-text": "Esta es la dirección única de tu monedero. Puedes compartirla de forma segura.", "receive_funds.copy_address": "<PERSON><PERSON><PERSON>", "receive_funds.network-warning.eoa.subtitle": "<link>Ver lista de redes</link>. Los activos enviados en redes no EVM se perderán.", "receive_funds.network-warning.eoa.title": "Compatible con todas las redes basadas en Ethereum", "receive_funds.network-warning.scw.subtitle": "<link>Ver redes</link>. Los activos enviados en otras redes se perderán.", "receive_funds.network-warning.scw.title": "Importante: usa solo redes compatibles", "receive_funds.scan_qr_code": "Escanear un código QR", "receiving.in.days": "Recibes en {days} d", "receiving.this.week": "Recibes esta semana", "receiving.today": "Recibes hoy", "reference.error.maximum_number_of_characters_exceeded": "<PERSON><PERSON><PERSON><PERSON> caracteres", "referral-code.placeholder": "Pega el enlace de invitación", "referral-code.subtitle": "Vuelve a hacer clic en el enlace de tu amigo o pégalo abajo. Queremos asegurarnos de que recibes tus recompensas.", "referral-code.title": "¿Te ha enviado un amigo {bReward}?", "rekyc.verification_deadline.subtitle": "Completa la verificación en un plazo de {daysUntil} días para seguir usando tu tarjeta.", "rekyc.verification_required.subtitle": "Verifica para seguir usando tu tarjeta", "reminder.fund": "💸 Añade fondos y empieza a ganar un 6 % al instante", "reminder.onboarding": "🏁 Termina la configuración y gana un 6 % en tus depósitos", "remove-owner.confirmation.subtitle": "Por seguridad, los cambios tardan 3 min en procesarse. Tu tarjeta se bloqueará temporalmente y no podrás realizar pagos.", "remove-owner.confirmation.title": "Tu tarjeta se bloqueará 3 min", "restore-smart-wallet.wallet-recovered": "<PERSON><PERSON><PERSON> recuperado", "rewardClaimCelebration.claimedTitle": "Recompensas ya reclamadas", "rewardClaimCelebration.subtitle": "Por invitar a amigos", "rewardClaimCelebration.title": "Has ganado", "rewards-warning.subtitle": "Eliminar esta cuenta pausará el acceso a las recompensas vinculadas. Puedes restaurar la cuenta en cualquier momento para reclamarlas.", "rewards-warning.title": "Perderás el acceso a tus recompensas", "rewards.copiedInviteLink": "Enlace de invitación copiado", "rewards.createAccount": "<PERSON><PERSON><PERSON> enlace", "rewards.header.subtitle": "Enviaremos {aReward} para ti y {bReward} para tu amigo cuando gaste {bSpendLimitReward}.", "rewards.header.title": "Recibe {amountA}{br}Dale {amountB}", "rewards.sendInvite": "Enviar invitación", "rewards.sendInviteTip": "Elige a un amigo y le daremos {bAmount}", "route.fees": "Comisiones {fees}", "routesNotFound.description": "La ruta de exchange para la combinación de redes {from}-{to} no está disponible.", "routesNotFound.title": "No hay rutas de exchange disponibles", "rpc.OrderBuySignMessage.subtitle": "A través de Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "A través de Swaps.IO", "rpc.addCustomNetwork.addNetwork": "<PERSON><PERSON><PERSON> red", "rpc.addCustomNetwork.chainId": "Chain ID", "rpc.addCustomNetwork.nativeToken": "Token nativo", "rpc.addCustomNetwork.networkName": "Nombre de la red", "rpc.addCustomNetwork.operationDescription": "Permite que esta web añada una red a tu monedero. Zeal no puede verificar la seguridad de redes personalizadas, asegúrate de entender los riesgos.", "rpc.addCustomNetwork.rpcUrl": "URL de RPC", "rpc.addCustomNetwork.subtitle": "<PERSON><PERSON><PERSON> {name}", "rpc.addCustomNetwork.title": "<PERSON><PERSON><PERSON> red", "rpc.send_token.network_not_supported.subtitle": "Estamos trabajando para habilitar transacciones en esta red. <PERSON><PERSON><PERSON> por tu paciencia 🙏", "rpc.send_token.network_not_supported.title": "Red disponible próximamente", "rpc.send_token.send_or_receive.settings": "<PERSON><PERSON><PERSON><PERSON>", "rpc.sign.accept": "Aceptar", "rpc.sign.cannot_parse_message.body": "No pudimos decodificar este mensaje. Acepta la solicitud solo si confías en esta app.{br}{br}Los mensajes pueden usarse para iniciar sesión en una app, pero también pueden darle el control de tus tokens.", "rpc.sign.cannot_parse_message.header": "Procede con precaución", "rpc.sign.import_private_key": "Importar claves", "rpc.sign.subtitle": "Para {name}", "rpc.sign.title": "<PERSON><PERSON><PERSON>", "safe-creation.success.title": "<PERSON><PERSON><PERSON> c<PERSON>o", "safe-safety-checks-popup.title": "Análisis de seguridad de la transacción", "safetyChecksPopup.title": "Análisis de seguridad del sitio", "scan_qr_code.description": "Escanea el QR de un monedero o conéctate a una app", "scan_qr_code.show_qr_code": "Mostrar mi código QR", "scan_qr_code.tryAgain": "Reintentar", "scan_qr_code.unlockCamera": "Activar <PERSON>", "screen-lock-missing.modal.close": "<PERSON><PERSON><PERSON>", "screen-lock-missing.modal.subtitle": "Tu dispositivo necesita un bloqueo de pantalla para usar passkeys. Configúralo y vuelve a intentarlo.", "screen-lock-missing.modal.title": "Falta el bloqueo de pantalla", "seedConfirmation.banner.subtitle": "Tu frase secreta da acceso a tu cartera y fondos. Solo los estafadores te la pedirán.", "seedConfirmation.title": "NUNCA compartas tu frase secreta con nadie", "select-active-owner.subtitle": "Tienes varios monederos vinculados a tu tarjeta. Elige uno para conectar a Zeal. Puedes cambiarlo cuando quieras.", "select-active-owner.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select-card.title": "Seleccionar tarjeta", "select-crypto-currency-title": "Se<PERSON><PERSON><PERSON><PERSON> token", "select-token.title": "Se<PERSON><PERSON><PERSON><PERSON> token", "selectEarnAccount.chf.description.steps": "· Retira fondos 24/7, sin bloqueos {br}· Los intereses se acumulan cada segundo {br}· Depósitos sobreprotegidos en <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} anual en CHF", "selectEarnAccount.eur.description.steps": "· Retira fondos 24/7, sin bloqueos {br}· Los intereses se acumulan cada segundo {br}· Préstamos sobregarantizados con <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} al año en EUR", "selectEarnAccount.subtitle": "<PERSON><PERSON><PERSON> camb<PERSON>lo cuando quieras", "selectEarnAccount.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectEarnAccount.usd.description.steps": "· Retira fondos 24/7, sin bloqueos {br}· Los intereses se acumulan cada segundo {br}· Depósitos sobregarantizados en <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} al año en USD", "selectEarnAccount.zero.description_general": "Mantén efectivo digital sin ganar intereses", "selectEarnAccount.zero.title": "0 % al año", "selectRechargeThreshold.button.enterAmount": "Introducir importe", "selectRechargeThreshold.button.setTo": "<PERSON>jar en {amount}", "selectRechargeThreshold.description.line1": "<PERSON>uando el saldo de tu tarjeta baja de {amount}, se recarga automáticamente hasta {amount} desde tu cuenta Earn.", "selectRechargeThreshold.description.line2": "Un objetivo más bajo mantiene más fondos en tu cuenta Earn (ganando un 3 %). <PERSON>uedes cambiarlo cuando quieras.", "selectRechargeThreshold.title": "Fijar saldo objetivo de la tarjeta", "select_currency_to_withdraw.select_token_to_withdraw": "Selecciona el token a retirar", "send-card-token.form.send": "Enviar", "send-card-token.form.send-amount": "Importe de la recarga", "send-card-token.form.title": "<PERSON><PERSON><PERSON> saldo a la tarjeta", "send-card-token.form.to-address": "Tarjeta", "send-safe-transaction.network-fee-widget.error": "Necesitas {amount} u otro token", "send-safe-transaction.network-fee-widget.no-fee": "Sin comisiones", "send-safe-transaction.network-fee-widget.title": "Comisiones", "send-safe-transaction.network_fee_widget.title": "Comisión de red", "send.banner.fees": "Necesitas {amount} más {currency} para pagar comisiones", "send.banner.toAddressNotSupportedNetwork.subtitle": "El monedero del destinatario no es compatible con {network}. Cambia a un token compatible.", "send.banner.toAddressNotSupportedNetwork.title": "Red no compatible para el destinatario", "send.banner.walletNotSupportedNetwork.subtitle": "Los Smart Wallets no pueden operar en {network}. Cambia a un token compatible.", "send.banner.walletNotSupportedNetwork.title": "Red del token no compatible", "send.empty-portfolio.empty-state": "No hemos encontrado tokens", "send.empty-portfolio.header": "Tokens", "send.titile": "Envío", "sendLimit.success.subtitle": "Tu nuevo límite se activará en 3 min.", "sendLimit.success.title": "El cambio tardará 3 minutos", "send_crypto.form.disconnected.cta.addFunds": "<PERSON><PERSON><PERSON>", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Cambiar a {network}", "send_crypto.form.disconnected.label": "Importe a transferir", "send_to.qr_code.description": "Escanea un código QR para enviar a un monedero", "send_to.qr_code.title": "Escanear código QR", "send_to_card.header": "Enviar a la dirección de la tarjeta", "send_to_card.select_sender.add_wallet": "<PERSON><PERSON><PERSON>", "send_to_card.select_sender.header": "Seleccionar remitente", "send_to_card.select_sender.search.default_placeholder": "Buscar dirección o ENS", "send_to_card.select_sender.show_card_address_button_description": "Mostrar dirección de la tarjeta", "send_token.form.select-address": "Seleccionar <PERSON>", "send_token.form.send-amount": "Importe a enviar", "send_token.form.title": "Enviar", "setLimit.amount.error.zero_amount": "No podrás realizar ningún pago", "setLimit.error.max_limit_reached": "Fijar límite máximo {amount}", "setLimit.error.same_as_current_limit": "Mi<PERSON> lí<PERSON> actual", "setLimit.placeholder": "Actual: {amount}", "setLimit.submit": "Fijar límite", "setLimit.submit.error.amount_required": "Indica un importe", "setLimit.subtitle": "Importe que puedes gastar al día.", "setLimit.title": "Fijar límite de gasto diario", "settings.accounts": "Cuentas", "settings.accountsSeeAll": "<PERSON>er to<PERSON>", "settings.addAccount": "<PERSON><PERSON><PERSON>", "settings.card": "Ajustes de la tarjeta", "settings.connections": "Conexiones de apps", "settings.currency": "<PERSON><PERSON> predeterminada", "settings.default_currency_selector.title": "Divisa", "settings.discord": "Discord", "settings.experimentalMode": "Modo experimental", "settings.experimentalMode.subtitle": "Prueba nuevas funciones", "settings.language": "Idioma", "settings.lockZeal": "Bloquear Zeal", "settings.notifications": "Notificaciones", "settings.open_expanded_view": "Abrir vista expandida", "settings.privacyPolicy": "Política de privacidad", "settings.settings": "<PERSON><PERSON><PERSON><PERSON>", "settings.termsOfUse": "T<PERSON><PERSON><PERSON>s de uso", "settings.twitter": "𝕏 / Twitter", "settings.version": "Versión {version} entorno: {env}", "setup-card.confirmation": "Obtener tarjeta virtual", "setup-card.confirmation.subtitle": "Haz pagos online y añádela a tu {type} wallet para pagos contactless.", "setup-card.getCard": "Conseguir tarjeta", "setup-card.order.physicalCard": "Tarjeta física", "setup-card.order.physicalCard.steps": "· Una VISA Gnosis Pay física {br}· Tarda hasta 3 semanas en llegar {br}· Para pagos en persona y cajeros. {br}· Añádela a Apple/Google Wallet (solo en países compatibles)", "setup-card.order.subtitle1": "Puedes usar varias tarjetas a la vez", "setup-card.order.title": "¿Qué tipo de tarjeta quieres?", "setup-card.order.virtualCard": "Tarjeta virtual", "setup-card.order.virtual_card.steps": "· Una VISA Gnosis Pay digital {br}· Úsala al instante para pagos online {br}· Añádela a Apple/Google Wallet (solo en países compatibles)", "setup-card.orderCard": "Pedir tarjeta", "setup-card.virtual-card": "Obtener tarjeta virtual", "setup.notifs.fakeAndroid.title": "Notificaciones de pagos y transferencias entrantes", "setup.notifs.fakeIos.subtitle": "Zeal puede avisarte cuando recibas dinero o gastes con tu tarjeta Visa. Puedes cambiarlo más tarde.", "setup.notifs.fakeIos.title": "Notificaciones de pagos y transferencias entrantes", "sign.PermitAllowanceItem.spendLimit": "Límite de gasto", "sign.ledger.subtitle": "Hemos enviado la solicitud a tu monedero físico. Continúa allí.", "sign.ledger.title": "Firmar en monedero físico", "sign.passkey.subtitle": "Tu navegador te pedirá que firmes con la passkey de este monedero. Continúa ahí.", "sign.passkey.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> passkey", "signal_aborted_for_uknown_reason.title": "Solicitud de red cancelada", "simulatedTransaction.BridgeTrx.info.title": "Bridge", "simulatedTransaction.CardTopUp.info.title": "Añadir <PERSON> a la tarjeta", "simulatedTransaction.CardTopUpTrx.info.title": "Añadir <PERSON> a la tarjeta", "simulatedTransaction.NftCollectionApproval.approve": "Aprobar colección de NFT", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "Añadir a la tarjeta", "simulatedTransaction.OrderEarnDepositBridge.title": "Depositar en Earn", "simulatedTransaction.P2PTransaction.info.title": "Enviar", "simulatedTransaction.PermitSignMessage.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.SingleNftApproval.approve": "Aprobar NFT", "simulatedTransaction.UnknownSignMessage.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.Withdrawal.info.title": "Re<PERSON><PERSON>", "simulatedTransaction.approval.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.approve.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.p2p.info.account": "A", "simulatedTransaction.p2p.info.unlabelledAccount": "Monedero sin etiquetar", "simulatedTransaction.unknown.info.receive": "Recibes", "simulatedTransaction.unknown.info.send": "Envías", "simulatedTransaction.unknown.using": "Usando {app}", "simulation.approval.modal.text": "Cuando aceptas una aprobación, das permiso a una app o contrato inteligente específico para usar tus tokens o NFT en futuras transacciones.", "simulation.approval.modal.title": "¿Qué son las aprobaciones?", "simulation.approval.spend-limit.label": "Límite de gasto", "simulation.approve.footer.for": "Para", "simulation.approve.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "simulationNotAvailable.title": "Acción desconocida", "smart-wallet-activation-view.on": "En", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password podría bloquear el acceso a tu monedero", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Usa Apple o Google para configurar Zeal de forma segura", "smart-wallet.passkey-notice.title": "Evita usar 1Password", "spend-limits.high.modal.text": "Establece un límite de gasto cercano a la cantidad de tokens que realmente usarás con una app o contrato inteligente. Los límites altos son arriesgados y pueden facilitar que los estafadores roben tus tokens.", "spend-limits.high.modal.text_sign_message": "El límite de gasto debe ser cercano a la cantidad de tokens que usarás con una app o contrato inteligente. Los límites altos son arriesgados y facilitan que los estafadores roben tus tokens.", "spend-limits.high.modal.title": "Límite de gasto alto", "spend-limits.modal.text": "El límite de gasto es la cantidad de tokens que una app puede usar en tu nombre. Puedes cambiar o eliminar este límite en cualquier momento. Para mayor seguridad, mantén los límites de gasto cerca de la cantidad de tokens que realmente usarás con una app.", "spend-limits.modal.title": "¿Qué es el límite de gasto?", "spent-limit-info.modal.description": "El límite de gasto es la cantidad de tokens que una app puede usar en tu nombre. Puedes cambiar o eliminar este límite en cualquier momento. Para mayor seguridad, mantén los límites de gasto cerca de la cantidad de tokens que realmente usarás con una app.", "spent-limit-info.modal.title": "¿Qué es el límite de gasto?", "sswaps-io.transfer-provider": "<PERSON>ve<PERSON><PERSON> de transfer<PERSON>cias", "storage.accountDetails.activateWallet": "Activar monedero", "storage.accountDetails.changeWalletLabel": "Cambiar etiqueta del monedero", "storage.accountDetails.deleteWallet": "Eliminar monedero", "storage.accountDetails.setup_recovery_kit": "Kit de recuperación", "storage.accountDetails.showPrivateKey": "Mostrar clave privada", "storage.accountDetails.showWalletAddress": "Mostrar dirección del monedero", "storage.accountDetails.smartBackup": "Copia de seguridad y recuperación", "storage.accountDetails.viewSsecretPhrase": "Ver frase secreta", "storage.accountDetails.zealSmartWallets": "¿Smart Wallets de Zeal?", "storage.manageAccounts.title": "<PERSON><PERSON><PERSON>", "submit-userop.progress.text": "Enviando", "submit.error.amount_high": "Importe demasiado alto", "submit.error.amount_hight": "Importe demasiado alto", "submit.error.amount_low": "Importe demasiado bajo", "submit.error.amount_required": "Introduce un importe", "submit.error.maximum_number_of_characters_exceeded": "Reduce los caracteres del mensaje", "submit.error.not_enough_balance": "<PERSON><PERSON> insuficiente", "submit.error.recipient_required": "Destinatario obligatorio", "submit.error.routes_not_found": "No se han encontrado rutas", "submitSafeTransaction.monitor.title": "Resultado de la transacción", "submitSafeTransaction.sign.title": "Resultado de la transacción", "submitSafeTransaction.state.sending": "Enviando", "submitSafeTransaction.state.sign": "<PERSON><PERSON><PERSON>", "submitSafeTransaction.submittingToRelayer.title": "Resultado de la transacción", "submitTransaction.cancel": "<PERSON><PERSON><PERSON>", "submitTransaction.cancel.attemptingToStop": "<PERSON>ten<PERSON><PERSON> de<PERSON>", "submitTransaction.cancel.failedToStop": "No se pudo detener", "submitTransaction.cancel.stopped": "Detenida", "submitTransaction.cancel.title": "Vista previa de la transacción", "submitTransaction.failed.banner.description": "La red canceló la transacción. Inténtalo de nuevo o contáctanos.", "submitTransaction.failed.banner.title": "La transacción ha fallado", "submitTransaction.failed.execution_reverted.title": "La app ha tenido un error", "submitTransaction.failed.execution_reverted_without_message.title": "La app ha tenido un error", "submitTransaction.failed.out_of_gas.description": "Comisiones de red superaron lo previsto", "submitTransaction.failed.out_of_gas.title": "Error de red", "submitTransaction.sign.title": "Resultado de la transacción", "submitTransaction.speedUp": "<PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue": "Añadida a la cola", "submitTransaction.state.addedToQueue.short": "En cola", "submitTransaction.state.cancelled": "Detenida", "submitTransaction.state.complete": "{currencyCode} a<PERSON><PERSON><PERSON>", "submitTransaction.state.complete.subtitle": "Consulta tu portafolio de Zeal", "submitTransaction.state.completed": "Completada", "submitTransaction.state.failed": "Fallida", "submitTransaction.state.includedInBlock": "Incluida en bloque", "submitTransaction.state.includedInBlock.short": "En bloque", "submitTransaction.state.replaced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.sendingToNetwork": "Enviando a la red", "submitTransaction.stop": "Detener", "submitTransaction.submit": "Enviar", "submitted-user-operation.state.bundled": "En cola", "submitted-user-operation.state.completed": "Completado", "submitted-user-operation.state.failed": "Fallido", "submitted-user-operation.state.pending": "Transmitiendo", "submitted-user-operation.state.rejected": "<PERSON><PERSON><PERSON><PERSON>", "submittedTransaction.failed.title": "La transacción ha fallado", "success_splash.card_activated": "Tarjeta activada", "supportFork.give-feedback.title": "Dar tu opinión", "supportFork.itercom.description": "Zeal resuelve dudas sobre dep<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, recompensas o cualquier otra cosa", "supportFork.itercom.title": "Dudas sobre el monedero", "supportFork.title": "Obtener ayuda con", "supportFork.zendesk.subtitle": "Gnosis Pay resuelve dudas sobre pagos con tarjeta, verificación de identidad o reembolsos", "supportFork.zendesk.title": "Pagos con tarjeta e identidad", "supported-networks.ethereum.warning": "Comisiones altas", "supportedNetworks.networks": "Redes compatibles", "supportedNetworks.oneAddressForAllNetworks": "Una dirección para todas las redes", "supportedNetworks.receiveAnyAssets": "Recibe activos de redes compatibles en tu monedero Zeal con la misma dirección", "swap.form.error.no_routes_found": "No se han encontrado rutas", "swap.form.error.not_enough_balance": "<PERSON><PERSON> insuficiente", "swaps-io-details.bank.serviceProvider": "<PERSON><PERSON><PERSON><PERSON> de servicios", "swaps-io-details.details.processing": "Procesando", "swaps-io-details.pending": "Pendiente", "swaps-io-details.rate": "Tasa de cambio", "swaps-io-details.serviceProvider": "<PERSON><PERSON><PERSON><PERSON> de servicios", "swaps-io-details.transaction.from.processing": "Transacción iniciada", "swaps-io-details.transaction.networkFees": "Comisiones de red", "swaps-io-details.transaction.state.completed-transaction": "Transacción completada", "swaps-io-details.transaction.state.started-transaction": "Transacción iniciada", "swaps-io-details.transaction.to.processing": "Transacción completada", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "El depósito se completará pronto. Kinetex todavía está procesando tu transacción.", "swapsIO.monitoring.awaitingLiqSend.title": "Retrasado", "swapsIO.monitoring.awaitingRecive.title": "Retransmitiendo", "swapsIO.monitoring.awaitingSend.title": "En cola", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Los tokens se enviaron a Kinetex, pero se devolverán pronto. Kinetex no pudo completar la transacción de destino.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Devolviendo tokens", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Los tokens no se han transferido por un error desconocido. Inténtalo de nuevo.", "swapsIO.monitoring.cancelledNoSlash.title": "Tokens devueltos", "swapsIO.monitoring.cancelledSlashed.subtitle": "Se han devuelto los tokens. Kinetex no pudo completar la transacción de destino.", "swapsIO.monitoring.cancelledSlashed.title": "Tokens devueltos", "swapsIO.monitoring.completed.title": "Completado", "taker-metadata.earn": "Gana en USD digital con Sky", "taker-metadata.earn.aave": "Gana en EUR digital con Aave", "taker-metadata.earn.aave.cashout24": "Retira al instante, 24/7", "taker-metadata.earn.aave.trusted": "Con la confianza de 27.000 M$, +2 años", "taker-metadata.earn.aave.yield": "El rendimiento se acumula cada segundo", "taker-metadata.earn.chf": "Gana en CHF digital", "taker-metadata.earn.chf.cashout24": "Retira al instante, 24/7", "taker-metadata.earn.chf.trusted": "Con la confianza de 28 M CHF", "taker-metadata.earn.chf.yield": "El rendimiento se acumula cada segundo", "taker-metadata.earn.usd.cashout24": "Retira al instante, 24/7", "taker-metadata.earn.usd.trusted": "Con la confianza de 10.700 M$, +5 años", "taker-metadata.earn.usd.yield": "El rendimiento se acumula cada segundo", "test": "Depositar", "to.titile": "A", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "Activos", "token.groupHeader.titleWithSum": "Activos {sum}", "token.hidden_tokens.page.title": "Tokens ocultos", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "<PERSON><PERSON><PERSON> token", "token.widget.cashback_empty": "Aún no hay transacciones", "token.widget.emptyState": "No hay tokens en el monedero", "tokens.cash": "Efectivo", "top-up-card-from-earn-view.approve.for": "Para", "top-up-card-from-earn-view.approve.into": "En", "top-up-card-from-earn-view.swap.from": "De", "top-up-card-from-earn-view.swap.to": "A", "top-up-card-from-earn-view.withdraw.to": "A", "top-up-card-from-earn.trx.title.approval": "Aprobar intercambio", "top-up-card-from-earn.trx.title.swap": "Añadir a la tarjeta", "top-up-card-from-earn.trx.title.withdrawal": "<PERSON><PERSON><PERSON>", "topUpDapp.connectWallet": "Conectar monedero", "topup-fee-breakdown.bungee-fee": "Comisión de proveedor externo", "topup-fee-breakdown.header": "Comisión de la transacción", "topup-fee-breakdown.network-fee": "Comisión de red", "topup-fee-breakdown.total-fee": "Comisión total", "topup.continue-in-wallet": "Continúa en tu monedero", "topup.send.title": "Enviar", "topup.submit-transaction.close": "<PERSON><PERSON><PERSON>", "topup.submit-transaction.sent-to-wallet": "Enviar {amount}", "topup.to": "A", "topup.transaction.complete.close": "<PERSON><PERSON><PERSON>", "topup.transaction.complete.try-again": "Reintentar", "transaction-request.nonce-too-low.modal.button-text": "<PERSON><PERSON><PERSON>", "transaction-request.nonce-too-low.modal.text": "Ya se ha completado una transacción con el mismo número de serie (nonce), por lo que no puedes enviar esta. Esto puede ocurrir si realizas transacciones muy seguidas o si intentas acelerar o cancelar una transacción ya completada.", "transaction-request.nonce-too-low.modal.title": "Ya se completó una transacción con el mismo nonce", "transaction-request.replaced.modal.button-text": "<PERSON><PERSON><PERSON>", "transaction-request.replaced.modal.text": "Imposible rastrear la transacción. Fue reemplazada o el nodo RPC tiene problemas.", "transaction-request.replaced.modal.title": "No se pudo encontrar el estado de la transacción", "transaction.activity.details.modal.close": "<PERSON><PERSON><PERSON>", "transaction.cancel_popup.cancel": "No, espera", "transaction.cancel_popup.confirm": "<PERSON><PERSON>, detener", "transaction.cancel_popup.description": "Para detenerla, paga una nueva comisión de red en lugar de la original de {oldFee}", "transaction.cancel_popup.description_without_original": "Para detenerla, tienes que pagar una nueva comisión de red", "transaction.cancel_popup.not_supported.subtitle": "La detención de transacciones no es compatible con {network}", "transaction.cancel_popup.not_supported.title": "No compatible", "transaction.cancel_popup.stopping_fee": "Comisión de red por detención", "transaction.cancel_popup.title": "¿Detener transacción?", "transaction.in-progress": "En curso", "transaction.inProgress": "En curso", "transaction.speed_up_popup.cancel": "No, espera", "transaction.speed_up_popup.confirm": "<PERSON><PERSON>, ace<PERSON>ar", "transaction.speed_up_popup.description": "Para acelerar, tienes que pagar una nueva comisión de red en lugar de la comisión original de {amount}", "transaction.speed_up_popup.description_without_original": "Para acelerar, tienes que pagar una nueva comisión de red", "transaction.speed_up_popup.seed_up_fee_title": "Comisión por acelerar", "transaction.speed_up_popup.title": "¿Acelerar transacción?", "transaction.speedup_popup.not_supported.subtitle": "La aceleración de transacciones no está disponible en {network}", "transaction.speedup_popup.not_supported.title": "No disponible", "transaction.subTitle.failed": "Fallida", "transactionDetails.cashback.not-qualified": "No cumple los requisitos", "transactionDetails.cashback.paid": "{amount} pagado", "transactionDetails.cashback.pending": "{amount} pendiente", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "Desconocido", "transactionDetails.cashback_estimate": "Estimación de cashback", "transactionDetails.category": "Categoría", "transactionDetails.exchangeRate": "Tipo de cambio", "transactionDetails.location": "Ubicación", "transactionDetails.payment-approved": "<PERSON><PERSON> a<PERSON>", "transactionDetails.payment-declined": "<PERSON><PERSON>o", "transactionDetails.payment-reversed": "Pago revertido", "transactionDetails.recharge.amountSentFromEarn.title": "Importe enviado desde Earn", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "Recargado a la tarjeta", "transactionDetails.recharge.rate.title": "Tipo de cambio", "transactionDetails.recharge.transactionId.title": "ID de transacción", "transactionDetails.refund": "Reembolso", "transactionDetails.reversal": "Reversión", "transactionDetails.transactionCurrency": "Moneda de la transacción", "transactionDetails.transactionId": "ID de transacción", "transactionDetails.type": "Transacción", "transactionRequestWidget.approve.subtitle": "Para {target}", "transactionRequestWidget.p2p.subtitle": "Para {target}", "transactionRequestWidget.unknown.subtitle": "<PERSON><PERSON><PERSON> {target}", "transactionSafetyChecksPopup.title": "Comprobaciones de seguridad de la transacción", "transactions.main.activity.title": "Actividad", "transactions.page.hiddenActivity.title": "Actividad oculta", "transactions.page.title": "Actividad", "transactions.viewTRXHistory.emptyState": "Aún no hay transacciones", "transactions.viewTRXHistory.errorMessage": "No pudimos cargar tu historial de transacciones", "transactions.viewTRXHistory.hidden.emptyState": "No hay transacciones ocultas", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Actividad no disponible para testnets", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Actividad no disponible para testnets{br}<link>Ver en explorador</link>", "transfer_provider": "<PERSON>ve<PERSON><PERSON> de transfer<PERSON>cias", "transfer_setup_with_different_wallet.subtitle": "Las transferencias bancarias están configuradas con otro monedero. Solo puedes tener un monedero conectado.", "transfer_setup_with_different_wallet.swtich_and_continue": "Cambiar y continuar", "transfer_setup_with_different_wallet.title": "Cam<PERSON><PERSON> de monedero", "tx-sent-to-wallet.button": "<PERSON><PERSON><PERSON>", "tx-sent-to-wallet.subtitle": "Continúa en {wallet}", "unblockProviderInfo.fees": "Consigues las comisiones más bajas: 0 % hasta 5k $ al mes y 0,2 % por encima.", "unblockProviderInfo.registration": "Unblock está registrado y autorizado por la FNTT para prestar servicios de custodia e intercambio VASP, y es un proveedor MSB registrado en la Fincen de EE. UU. <link>Saber más</link>", "unblockProviderInfo.selfCustody": "El dinero digital que recibes está en tu monedero privado y nadie más tendrá control sobre tus activos", "unblock_invalid_faster_payment_configuration.subtitle": "La cuenta bancaria que has indicado no admite transferencias SEPA europeas ni Faster Payments de Reino Unido. Por favor, usa otra cuenta", "unblock_invalid_faster_payment_configuration.title": "Se necesita otra cuenta", "unknownTransaction.primaryText": "Transacción con tarjeta", "unsupportedCountry.subtitle": "Las transferencias bancarias aún no están disponibles en tu país.", "unsupportedCountry.title": "No disponible en {country}", "update-app-popup.subtitle": "La última actualización incluye correcciones, funciones y más sorpresas. Actualiza y saca el máximo partido a Zeal.", "update-app-popup.title": "Actualiza la versión de Zeal", "update-app-popup.update-now": "<PERSON><PERSON><PERSON><PERSON>ora", "user_associated_with_other_merchant.subtitle": "Este monedero no puede usarse para transferencias bancarias. Usa otro monedero o contacta con nosotros en Discord para recibir ayuda.", "user_associated_with_other_merchant.title": "No se puede usar el monedero", "user_associated_with_other_merchant.try_with_another_wallet": "Probar con otro monedero", "user_email_already_exists.subtitle": "<PERSON> has configurado las transferencias bancarias con otro monedero. Inténtalo de nuevo con el monedero que usaste antes.", "user_email_already_exists.title": "Transferencias configuradas con otro monedero", "user_email_already_exists.try_with_another_wallet": "Probar con otro monedero", "validation.invalid.iban": "IBAN no válido", "validation.required": "Obligatorio", "validation.required.first_name": "Nombre obligatorio", "validation.required.iban": "IBAN obligatorio", "validation.required.last_name": "Apellido obligatorio", "verify-passkey.cta": "Verificar passkey", "verify-passkey.subtitle": "Verifica que tu passkey se ha creado y está bien protegida.", "verify-passkey.title": "Verificar passkey", "view-cashback.cashback-next-cycle": "<PERSON><PERSON> de cashback en {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "Deposita para obtener cashback", "view-cashback.pending": "{money} pendiente", "view-cashback.pending-rewards.not_paid": "Recibes en {days} d", "view-cashback.pending-rewards.paid": "Recibido esta semana", "view-cashback.received-rewards": "Recompensas recibidas", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Pagos no confirmados", "view-cashback.upcoming": "Próxi<PERSON> {money}", "virtual-card-order.configure-safe.loading-text": "<PERSON><PERSON><PERSON> tarjeta", "virtual-card-order.create-order.loading-text": "Activando tarjeta", "virtual-card-order.create-order.success-text": "Tarjeta activada", "virtualCard.activateCard": "Activar tarjeta", "walletDeleteConfirm.main_action": "Eliminar", "walletDeleteConfirm.subtitle": "Tendrás que importarlo de nuevo para ver tu portfolio o hacer transacciones.", "walletDeleteConfirm.title": "¿Eliminar monedero?", "walletSetting.header": "Ajustes del monedero", "wallet_connect.connect.cancel": "<PERSON><PERSON><PERSON>", "wallet_connect.connect.connect_button": "Conectar", "wallet_connect.connect.title": "Conectar", "wallet_connect.connected.title": "Conectado", "wallet_connect_add_chain_missing.title": "Red no compatible", "wallet_connect_proposal_expired.title": "La conexión ha caducado", "withdraw": "<PERSON><PERSON><PERSON>", "withdraw.confirmation.close": "<PERSON><PERSON><PERSON>", "withdraw.confirmation.continue": "Confirmar", "withdrawal_request.completed": "Completado", "withdrawal_request.pending": "Pendiente", "zeal-dapp.connect-wallet.cta.primary.connecting": "Conectando...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Conectar", "zeal-dapp.connect-wallet.cta.secondary": "<PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.title": "Conecta el monedero para continuar", "zealSmartWalletInfo.gas": "Paga la comisión de red con muchos tokens: usa tokens ERC20 populares en redes compatibles para pagar las comisiones, no solo los tokens nativos.", "zealSmartWalletInfo.recover": "Sin frases secretas: recupéralo usando una passkey biométrica desde tu gestor de contraseñas, iCloud o tu cuenta de Google.", "zealSmartWalletInfo.selfCustodial": "Totalmente privado: las firmas con passkey se validan on-chain para minimizar las dependencias centrales.", "zealSmartWalletInfo.title": "Acerca de los Smart Wallets de Zeal", "zeal_a_rewards_already_claimed_error.title": "Recompensa ya reclamada", "zwidget.minimizedDisconnected.label": "Zeal desconectado"}