{"Account.ListItem.details.label": "<PERSON><PERSON><PERSON>", "AddFromAddress.success": "Novčanik je spremljen", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{Nema novčanika} one{{count} novčanik} other{{count} novčanika}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "<PERSON>jna fraza {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Stvori nove novčanike iz jedne od postojećih tajnih fraza", "AddFromExistingSecretPhrase.SelectPhrase.title": "<PERSON><PERSON><PERSON><PERSON> tajnu frazu", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "Tvoja tajna fraza može pohraniti više novčanika. Odaberi koje ž<PERSON>š koris<PERSON>.", "AddFromExistingSecretPhrase.WalletSelection.title": "Brzo dodaj novčanik", "AddFromExistingSecretPhrase.success": "Novčanici dodani u Zeal", "AddFromHardwareWallet.subtitle": "Odaberi svoj hardverski novčanik za povezivanje sa Zealom", "AddFromHardwareWallet.title": "Hardverski novčanik", "AddFromNewSecretPhrase.WalletSelection.subtitle": "Odaberi novčanike koje želiš u<PERSON>", "AddFromNewSecretPhrase.WalletSelection.title": "Uvezi novčanike", "AddFromNewSecretPhrase.accounts": "Novčanici", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "Tajna fraza funkcionira kao privjesak za ključeve za milijune novčanika, od kojih svaki ima jedinstveni privatni ključ.{br}{br}<PERSON><PERSON><PERSON><PERSON> uvesti koliko god novčanika trebaš sada ili ih dodati kasnije.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Novčanici s tajnom frazom", "AddFromNewSecretPhrase.subtitle": "Unesi svoju tajnu frazu, riječi odvojene razmacima", "AddFromNewSecretPhrase.success_secret_phrase_added": "Ta<PERSON>a fraza dodana 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Novčanici dodani u Zeal", "AddFromNewSecretPhrase.wallets": "Novčanici", "AddFromPrivateKey.subtitle": "Unesi svoj privatni ključ", "AddFromPrivateKey.success": "Privatni ključ dodan 🎉", "AddFromPrivateKey.title": "Vrati no<PERSON>", "AddFromPrivateKey.typeOrPaste": "Upiši ili zalijepi ovdje", "AddFromSecretPhrase.importWallets": "{count,plural,=0{<PERSON>ema odabranih novčanika} one{Uvezi novčanik} other{Uvezi {count} novčanika}}", "AddFromTrezor.AccountSelection.title": "Uvezi Trezor novč<PERSON>", "AddFromTrezor.hwWalletTip.subtitle": "Hardverski novčanik sadrži milijune novčanika s različitim adresama. <PERSON><PERSON><PERSON><PERSON> uvesti koliko god novčanika trebaš sada ili ih dodati kasnije.", "AddFromTrezor.hwWalletTip.title": "Uvoz iz hardverskih novčanika", "AddFromTrezor.importAccounts": "{count,plural,=0{<PERSON>ema odabranih novčanika} one{Uvezi novčanik} other{Uvezi {count} novčanika}}", "AddFromTrezor.success": "Novčanici dodani u Zeal", "ApprovalSpenderTypeCheck.failed.subtitle": "Vjerojatna prijevara: odobrenja se inače daju ugovorima", "ApprovalSpenderTypeCheck.failed.title": "Odobrenje za novčanik, ne ugovor", "ApprovalSpenderTypeCheck.passed.subtitle": "Uglavnom odobravaš sredstva ugovorima", "ApprovalSpenderTypeCheck.passed.title": "Odobrenje za pametni ugovor", "BestReturns.subtitle": "Ovaj pružatelj zamjene dat će ti najveći iznos, uključujući sve naknade.", "BestReturnsPopup.title": "Najbolji povrat", "BlacklistCheck.Failed.subtitle": "Zlonamjerne prijave od <source></source>", "BlacklistCheck.Failed.title": "Stranica je na crnoj listi", "BlacklistCheck.Passed.subtitle": "Nema z<PERSON>amjernih prijava od <source></source>", "BlacklistCheck.Passed.title": "Stranica nije na crnoj listi", "BlacklistCheck.failed.statusButton.label": "Stranica je prijavljena", "BridgeRoute.slippage": "Proklizavanje {slippage}", "BridgeRoute.title": "Pružatelj usluge mosta", "CheckConfirmation.InProgress": "U tijeku...", "CheckConfirmation.success.splash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ChooseImportOrCreateSecretPhrase.subtitle": "<PERSON><PERSON>i tajnu frazu ili stvori novu", "ChooseImportOrCreateSecretPhrase.title": "<PERSON><PERSON><PERSON> ta<PERSON>u frazu", "ConfirmTransaction.Simuation.Skeleton.title": "Provode se sigurnosne provjere…", "ConnectionSafetyCheckResult.passed": "Sigurnosna provjera uspješna", "ContactGnosisPaysupport": "Kontaktiraj Gnosis Pay", "CopyKeyButton.copied": "<PERSON><PERSON><PERSON>", "CopyKeyButton.copyYourKey": "<PERSON><PERSON><PERSON> svoj ključ", "CopyKeyButton.copyYourPhrase": "<PERSON><PERSON><PERSON> s<PERSON> frazu", "DAppVerificationCheck.Failed.subtitle": "Stranica nije navedena na <source></source>", "DAppVerificationCheck.Failed.title": "Stranica nije pronađena u registrima aplikacija", "DAppVerificationCheck.Passed.subtitle": "Stranica je navedena na <source></source>", "DAppVerificationCheck.Passed.title": "Stranica se nalazi u registrima aplikacija", "DAppVerificationCheck.failed.statusButton.label": "Stranica nije pronađena u registrima aplikacija", "ERC20.tokens.emptyState": "<PERSON><PERSON> pro<PERSON>", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "<PERSON><PERSON><PERSON><PERSON> naknada", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "<PERSON>ad<PERSON>h {period}: iz<PERSON><PERSON>u {from} i {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "<PERSON><PERSON><PERSON><PERSON> naknada: {baseFee} • Sigurnosna rezerva: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Nismo mogli dohvatiti trenutnu osnovnu naknadu", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Veći od idućeg Noncea. Može zapeti.", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "<PERSON>ce ne može biti manji od trenutnog", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "Nismo mogli izračunati prioritetnu naknadu", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Nismo mogli dohvatiti trenutnu maksimalnu naknadu", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> naknada", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Može zapeti dok se mrežne naknade ne smanje", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> naknada", "EditFeeModal.Custom.gasLimit.title": "Ograničenje plina {gasLimit}", "EditFeeModal.Custom.title": "<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Zapet će dok se osnovna naknada ne smanji", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Niska naknada. Transakcija može zapeti", "EditFeeModal.EditGasLimit.estimatedGas": "Proc. plin: {estimated} • Sigurnosna rezerva: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Manje od procijenjenog limita. Transakcija neće uspjeti", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Manje od preporučenog limita. Transakcija bi mogla biti neuspješna", "EditFeeModal.EditGasLimit.subtitle": "Postavi maksimalnu količinu plina koju želi<PERSON> da ova transakcija iskoristi. Transakcija neće uspjeti ako postaviš niži limit od potrebnog", "EditFeeModal.EditGasLimit.title": "<PERSON>redi ograničenje plina", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Manje od minimalnog limita naknade: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "Više od sljedećeg Nonce-a. <PERSON> će", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "<PERSON>ce ne može biti manji od trenutnog", "EditFeeModal.EditNonce.subtitle": "Tvoja će transakcija zapeti ako postaviš nonce koji nije sljedeći po redu", "EditFeeModal.EditNonce.title": "<PERSON><PERSON><PERSON> nonce", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "Po<PERSON><PERSON><PERSON> je {amount} za slanje", "EditFeeModal.Header.Time.unknown": "<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Maks. {fee}", "EditFeeModal.Header.fee.unknown": "Nakna<PERSON> nepoz<PERSON>a", "EditFeeModal.Header.subsequent_failed": "Procjene su možda zastarjele, zadnje osvježavanje nije us<PERSON>jelo", "EditFeeModal.Layout.Header.ariaLabel": "<PERSON><PERSON><PERSON><PERSON> naknada", "EditFeeModal.MaxFee.subtitle": "Maksimalna naknada je najviše što ćeš platiti za transakciju, ali obično platiš predviđenu naknadu. Ova dodatna rezerva pomaže da transakcija prođe, čak i ako se mreža uspori ili poskupi.", "EditFeeModal.MaxFee.title": "Maksimalna mrežna naknada", "EditFeeModal.SelectPreset.Time.unknown": "<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.SelectPreset.ariaLabel": "Odaberi predložak naknade", "EditFeeModal.SelectPreset.fast": "Brz<PERSON>", "EditFeeModal.SelectPreset.normal": "Normalno", "EditFeeModal.SelectPreset.slow": "Sporo", "EditFeeModal.ariaLabel": "<PERSON><PERSON><PERSON> m<PERSON> naknadu", "FailedSimulation.Confirmation.Item.subtitle": "<PERSON><PERSON><PERSON> je do interne pogreške", "FailedSimulation.Confirmation.Item.title": "<PERSON><PERSON> bilo moguće simulirati transakciju", "FailedSimulation.Confirmation.subtitle": "<PERSON>si li siguran da ž<PERSON>š nastaviti?", "FailedSimulation.Confirmation.title": "Potpisu<PERSON>š <PERSON>li<PERSON>", "FailedSimulation.Title": "Pogreška simulacije", "FailedSimulation.footer.subtitle": "<PERSON><PERSON><PERSON> je do interne pogreške", "FailedSimulation.footer.title": "<PERSON><PERSON> bilo moguće simulirati transakciju", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Potrebno je {amount} za slanje transakcije", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Obrada bi mogla potrajati", "FeeForecastWidget.networkFee": "<PERSON><PERSON><PERSON><PERSON>", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Nismo mogli izračunati mrežnu naknadu", "FeeForecastWidget.subsequentFailed.message": "Procjene su možda stare, zadnje osvježavanje nije us<PERSON>jelo", "FeeForecastWidget.unknownDuration": "Nepoznato", "FeeForecastWidget.unknownFee": "Nepoznato", "GasCurrencySelector.balance": "<PERSON><PERSON>: {balance}", "GasCurrencySelector.networkFee": "<PERSON><PERSON><PERSON><PERSON>", "GasCurrencySelector.payNetworkFeesUsing": "Plati mrežne naknade koris<PERSON>i", "GasCurrencySelector.removeDefaultGasToken.description": "Plati naknade s najvećeg stanja", "GasCurrencySelector.removeDefaultGasToken.title": "Automatsko rukovanje naknadama", "GasCurrencySelector.save": "Sp<PERSON>i", "GoogleDriveBackup.BeforeYouBegin.first_point": "Ako zaboravim svoju Zeal lozinku, zauvijek ću izgubiti svoju imovinu", "GoogleDriveBackup.BeforeYouBegin.second_point": "Ako izgubim pristup svom Google Driveu ili izmijenim svoju datoteku za oporavak, zauvijek ću izgubiti svoju imovinu", "GoogleDriveBackup.BeforeYouBegin.subtitle": "Molimo te da razumiješ i prihvatiš sljedeću točku o samostalnom skrbništvu:", "GoogleDriveBackup.BeforeYouBegin.third_point": "Zeal mi ne može pomoći oporaviti moju Zeal lozinku niti moj pristup <PERSON> Driveu", "GoogleDriveBackup.BeforeYouBegin.title": "Prije nego š<PERSON> p<PERSON>š", "GoogleDriveBackup.loader.subtitle": "Odobri zahtjev na Google Driveu za prijenos datoteke za oporavak", "GoogleDriveBackup.loader.title": "Čekanje odobrenja...", "GoogleDriveBackup.success": "Sigurnosna kopija uspješna 🎉", "MonitorOffRamp.overServiceTime": "Većina prijenosa dovrši se unutar {estimated_time}, ali ponekad mogu potrajati duže zbog dodatnih provjera. To je normalno i sredstva su sigurna dok se te provjere obavljaju.{br}{br}Ako se transakcija ne dovrši unutar {support_soft_deadline}, molimo te {contact_support}", "MonitorOnRamp.contactSupport": "Kontaktiraj podršku", "MonitorOnRamp.from": "S", "MonitorOnRamp.fundsReceived": "Sredstva primljena", "MonitorOnRamp.overServiceTime": "Većina prijenosa je dovršena unutar {estimated_time}, no ponekad mogu potrajati duže zbog dodatnih provjera. To je normalno i sredstva su sigurna dok se provjere obavljaju.{br}{br}Ako se transakcija ne dovrši unutar {support_soft_deadline}, molimo {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Slanje u tvoj novčanik", "MonitorOnRamp.to": "Na", "MonitorOnRamp.waitingForTransfer": "Čeka se da prebaciš sredstva", "NftCollectionCheck.failed.subtitle": "Kolekcija nije verificirana na <source></source>", "NftCollectionCheck.failed.title": "Kolekcija nije verificirana", "NftCollectionCheck.passed.subtitle": "Kolekcija je verificirana na <source></source>", "NftCollectionCheck.passed.title": "Kolekcija je verificirana", "NftCollectionInfo.entireCollection": "Cijela k<PERSON>kci<PERSON>", "NoSigningKeyStore.createAccount": "Stvori račun", "NonceRangeError.biggerThanCurrent.message": "Transakcija će zapeti", "NonceRangeError.lessThanCurrent.message": "Transakcija neće uspjeti", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "<PERSON>ce je viši od trenutnog. Smanji <PERSON> kako transakcija ne bi zapela.", "NonceRangeErrorPopup.biggerThanCurrent.title": "Transakcija će zapeti", "P2pReceiverTypeCheck.failed.subtitle": "Š<PERSON><PERSON>š li na ispravnu adresu?", "P2pReceiverTypeCheck.failed.title": "Primatelj je pametni ugovor, ne novčanik", "P2pReceiverTypeCheck.passed.subtitle": "Uglavnom šalješ sredstva u druge novčanike", "P2pReceiverTypeCheck.passed.title": "Primatelj je novčanik", "PasswordCheck.title": "<PERSON><PERSON> lozinku", "PasswordChecker.subtitle": "Unesi svoju lozinku za potvrdu da si to ti.", "PermitExpirationCheck.failed.subtitle": "Neka bude kratko i samo onoliko dugo koliko trebaš", "PermitExpirationCheck.failed.title": "Dugo vrijeme isteka", "PermitExpirationCheck.passed.subtitle": "Koliko dugo aplikacija može koristiti tvoje tokene", "PermitExpirationCheck.passed.title": "<PERSON><PERSON>jeme isteka nije predugo", "PrivateKeyValidationError.moreThanMaximumWords": "Ma<PERSON>. {count} ri<PERSON><PERSON>i", "PrivateKeyValidationError.notValidPrivateKey": "Ovo nije važeći privatni ključ", "PrivateKeyValidationError.secretPhraseIsInvalid": "Tajna fraza nije važ<PERSON>ća", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Riječ #{index} je krivo napisana ili nevaž<PERSON>ća", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} rije<PERSON>} other{{count} riječi}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Tajne fraze i privatni ključevi su šifrirani i nikada ne napuštaju ovaj uređaj", "SecretPhraseReveal.header": "Zapiši tajnu frazu", "SecretPhraseReveal.hint": "Ne dijeli svoju frazu ni s kim. Čuvaj je na sigurnom i izvan mreže", "SecretPhraseReveal.skip.subtitle": "Iako to mo<PERSON><PERSON><PERSON> učiniti kasnije, ako izgubiš ovaj uređaj prije zapisivanja fraze, izgubit ćeš svu imovinu koju si dodao u ovaj novčanik", "SecretPhraseReveal.skip.takeTheRisk": "<PERSON><PERSON><PERSON>", "SecretPhraseReveal.skip.title": "Preskočiti zapisivanje fraze?", "SecretPhraseReveal.skip.writeDown": "Zapiši", "SecretPhraseReveal.skipForNow": "Preskoči", "SecretPhraseReveal.subheader": "Zapiši je i čuvaj na sigurnom izvan mreže. Zatim ćemo te zamoliti da je potvrdiš.", "SecretPhraseReveal.verify": "Potvrdi", "SelectCurrency.tokens": "Tokeni", "SelectCurrency.tokens.emptyState": "<PERSON><PERSON> pro<PERSON>", "SelectRoute.slippage": "Proklizavanje {slippage}", "SelectRoutes.emptyState": "Nismo pronašli rute za ovu zamjenu", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Poveži novčanik", "SendERC20.labelAddress.inputPlaceholder": "Oznaka novčanika", "SendERC20.labelAddress.subtitle": "Označi novčanik da ga lakše nađeš.", "SendERC20.labelAddress.title": "Označi ovaj novčanik", "SendERC20.send_to": "Pošalji na", "SendERC20.tokens": "Tokeni", "SendOrReceive.bankTransfer.primaryText": "Bankovni prijenos", "SendOrReceive.bankTransfer.shortText": "<PERSON><PERSON><PERSON><PERSON>, trenutan on-ramp i off-ramp", "SendOrReceive.bridge.primaryText": "Most", "SendOrReceive.bridge.shortText": "Prijenos tokena između mreža", "SendOrReceive.receive.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.receive.shortText": "Primi tokene ili kolekcionarske predmete", "SendOrReceive.send.primaryText": "Pošalji", "SendOrReceive.send.shortText": "Pošalji tokene na bilo koju adresu", "SendOrReceive.swap.primaryText": "Zamijeni", "SendOrReceive.swap.shortText": "Zamijeni tokene", "SendSafeTransaction.Confirm.loading": "Provjera sigurnosti u tijeku…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Šifriraj datoteku za oporavak lozinkom", "SetupRecoveryKit.google.subtitle": "Sinkronizirano {date}", "SetupRecoveryKit.google.title": "Sigurnosna kopija na Google Driveu", "SetupRecoveryKit.subtitle": "Trebat će ti barem jedan način za vraćanje računa ako deinstaliraš Zeal ili promijeniš uređaj", "SetupRecoveryKit.title": "Postavi komplet za oporavak", "SetupRecoveryKit.writeDown.subtitle": "Zapiši tajnu frazu", "SetupRecoveryKit.writeDown.title": "R<PERSON>čna sigurnosna kopija", "Sign.CheckSafeDeployment.activate": "Aktiviraj", "Sign.CheckSafeDeployment.subtitle": "Za prijavu ili potpisivanje poruke, prvo aktiviraj uređaj na ovoj mreži. To se radi nakon instalacije/oporavka pametnog novčanika.", "Sign.CheckSafeDeployment.title": "Aktiviraj uređaj na ovoj mreži", "Sign.Simuation.Skeleton.title": "Provjera sigurnosti u tijeku…", "SignMessageSafetyCheckResult.passed": "Sigurnosne provjere uspješne", "SignMessageSafetyChecksPopup.title.permits": "Sigurnosne provjere dozvole", "SimulationFailedConfirmation.subtitle": "Simulirali smo ovu transakciju i pronašli problem koji će uzrokovati neuspjeh. <PERSON><PERSON><PERSON>š poslati ovu transakciju, ali će vjerojatno biti neuspješna i možeš izgubiti mrežnu naknadu.", "SimulationFailedConfirmation.title": "Transakcija će vjerojatno biti neuspješna", "SimulationNotSupported.Title": "Simulacija nije{br}podržana na{br}{network}", "SimulationNotSupported.footer.subtitle": "I dalje možeš poslati ovu transakciju", "SimulationNotSupported.footer.title": "Simulacija nije podržana", "SlippagePopup.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SlippagePopup.presetsHeader": "Odstupanje pri zamjeni", "SlippagePopup.title": "Postavke odstupanja", "SmartContractBlacklistCheck.failed.subtitle": "Zlonamjerne prijave od strane <source></source>", "SmartContractBlacklistCheck.failed.title": "Ugovor je na crnoj listi", "SmartContractBlacklistCheck.passed.subtitle": "<PERSON>ema z<PERSON>jernih prijava od strane <source></source>", "SmartContractBlacklistCheck.passed.title": "Ugovor nije na crnoj listi", "SuspiciousCharactersCheck.Failed.subtitle": "Ovo je česta taktika za krađu identiteta", "SuspiciousCharactersCheck.Failed.title": "Provjeravamo uobičajene obrasce krađe identiteta", "SuspiciousCharactersCheck.Passed.subtitle": "Provjeravamo pokušaje krađe identiteta", "SuspiciousCharactersCheck.Passed.title": "<PERSON><PERSON><PERSON> nema neuobi<PERSON><PERSON>", "SuspiciousCharactersCheck.failed.statusButton.label": "<PERSON><PERSON><PERSON>ži neobične znakove ", "TokenVerificationCheck.failed.subtitle": "Token nije naveden na <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} nije verificiran od strane CoinGecko", "TokenVerificationCheck.passed.subtitle": "Token je naveden na <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} je verificiran od strane CoinGecko", "TopupDapp.MonitorTransaction.success.splash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TransactionSafetyCheckResult.passed": "Sigurnosne provjere uspješne", "TransactionSimulationCheck.failed.subtitle": "Greška: {errorMessage}", "TransactionSimulationCheck.failed.title": "Transakcija će vjerojatno biti neuspješna", "TransactionSimulationCheck.passed.subtitle": "Si<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> pomo<PERSON> <source></source>", "TransactionSimulationCheck.passed.title": "Pregled transakcije bio je uspješan", "TrezorError.trezor_action_cancelled.action": "Zatvori", "TrezorError.trezor_action_cancelled.subtitle": "Odbio si transakciju na svom hardverskom novčaniku", "TrezorError.trezor_device_used_elsewhere.action": "Sinkroniz<PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "Zatvori sve druge otvorene sesije i pokušaj ponovno sinkronizirati svoj Trezor", "TrezorError.trezor_method_cancelled.action": "Sinkroniz<PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "Dopusti Trezoru izvoz novčanika u Zeal", "TrezorError.trezor_permissions_not_granted.action": "Sinkroniz<PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "<PERSON>j <PERSON>u dopuštenja za sve novčanike.", "TrezorError.trezor_pin_cancelled.action": "Sinkroniz<PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "Sesija otkazana na uređaju", "TrezorError.trezor_popup_closed.action": "Sinkroniz<PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "Trezor dijalog se neočekivano zatvorio", "TrxLikelyToFail.lessThanEstimatedGas.message": "Transakcija neće uspjeti", "TrxLikelyToFail.lessThanMinimumGas.message": "Transakcija neće uspjeti", "TrxLikelyToFail.lessThanSuggestedGas.message": "<PERSON><PERSON><PERSON><PERSON>", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "Ograničenje plina za transakciju je prenisko. Povećaj ograničenje plina na predloženu vrijednost kako bi se spriječio neuspjeh.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "Transakcija će vjerojatno biti neuspješna", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Ograničenje plina niže je od procijenjenog. Povećaj ograničenje plina na predloženu vrijednost.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "Transakcija neće uspjeti", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "Maks. osnovna naknada niža je od trenutne. Povećaj maks. osnovnu naknadu kako transakcija ne bi zapela.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "Transakcija će zapeti", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "Maks<PERSON>lna naknada za transakciju je preniska. Povećaj maksimalnu naknadu kako transakcija ne bi zapela.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "Transakcija će zapeti", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Prioritetna naknada niža je od preporučene. Povećaj prioritetnu naknadu kako bi se transakcija ubrzala.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "Dovršetak transakcije bi mogao potrajati", "UnsupportedMobileNetworkLayout.gotIt": "Razumijem!", "UnsupportedMobileNetworkLayout.subtitle": "Ne možeš obavljati transakcije ni potpisivati poruke na mreži s ID-jem {networkHexId} putem mobilne verzije Zeala{br}{br}Prebaci se na proširenje za preglednik kako bi mogao obavljati transakcije na ovoj mreži, dok mi marljivo radimo na dodavanju podrške za nju 🚀", "UnsupportedMobileNetworkLayout.title": "Mreža nije podržana u mobilnoj verziji Zeala", "UnsupportedSafeNetworkLayout.subtitle": "Ne možeš obavljati transakcije ili potpisivati poruke na {network} sa Zeal pametnim novčanikom{br}{br}Prebaci se na podržanu mrežu ili koristi Legacy novčanik.", "UnsupportedSafeNetworkLayoutk.title": "Mreža nije podržana za pametni novčanik", "UserConfirmationPopup.goBack": "Odustani", "UserConfirmationPopup.submit": "Pošalji ipak", "ViewPrivateKey.header": "Privatni ključ", "ViewPrivateKey.hint": "Ne dijeli svoj privatni ključ ni s kim. Čuvaj ga na sigurnom i izvan mreže", "ViewPrivateKey.subheader.mobile": "Dodirni za prikaz privatnog ključa", "ViewPrivateKey.subheader.web": "Prijeđi mišem za prikaz privatnog ključa", "ViewPrivateKey.unblur.mobile": "Dodirni za prikaz", "ViewPrivateKey.unblur.web": "Prijeđi mišem za prikaz", "ViewSecretPhrase.PasswordChecker.subtitle": "Unesi lozinku za kriptiranje datoteke za oporavak. <PERSON><PERSON> je zapamtiti za ubuduće.", "ViewSecretPhrase.done": "Gotovo", "ViewSecretPhrase.header": "<PERSON><PERSON><PERSON> fraza", "ViewSecretPhrase.hint": "Ne dijeli svoju frazu ni s kim. Čuvaj je na sigurnom i izvan mreže", "ViewSecretPhrase.subheader.mobile": "Dodirni za prikaz tajne fraze", "ViewSecretPhrase.subheader.web": "Prijeđi mišem za prikaz tajne fraze", "ViewSecretPhrase.unblur.mobile": "Dodirni za prikaz", "ViewSecretPhrase.unblur.web": "Prijeđi mišem za prikaz", "account-details.monerium": "Prijenose vrši Monerium, ovlaštena EMI. <link>Saznaj više</link>", "account-details.unblock": "Prijenosi se obavljaju putem Unblocka, ovlaštenog i registriranog pružatelja usluga mjenjačnice i skrbništva. <link>Saznaj više</link>", "account-selector.empty-state": "<PERSON>je pronađen nijedan novčanik", "account-top-up.select-currency.title": "Tokeni", "account.accounts_not_found": "<PERSON>smo uspjeli pronaći novčanike", "account.accounts_not_found_search_valid_address": "Novčanik nije na tvom popisu", "account.add.create_new_secret_phrase": "<PERSON><PERSON><PERSON> tajnu frazu", "account.add.create_new_secret_phrase.subtext": "Nova tajna fraza od 12 riječi", "account.add.fromRecoveryKit.fileNotFound": "Nismo uspjeli pronaći tvoju datoteku", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "Pokušaj ponovno", "account.add.fromRecoveryKit.fileNotFound.explanation": "<PERSON><PERSON><PERSON><PERSON> is<PERSON><PERSON> ra<PERSON> s mapom Zeal Backup", "account.add.fromRecoveryKit.fileNotValid": "Datoteka za oporavak nije važeća", "account.add.fromRecoveryKit.fileNotValid.explanation": "Datoteka je krive vrste ili je izmijenjena", "account.add.import_secret_phrase": "<PERSON><PERSON><PERSON> tajnu frazu", "account.add.import_secret_phrase.subtext": "St<PERSON><PERSON> na Zealu, Metamasku ili drugdje", "account.add.select_type.add_hardware_wallet": "Hardverski novčanik", "account.add.select_type.existing_smart_wallet": "Postojeći Smart Wallet", "account.add.select_type.private_key": "Privatni ključ", "account.add.select_type.seed_phrase": "Početna fraza", "account.add.select_type.title": "Uvezi novčanik", "account.add.select_type.zeal_recovery_file": "Zeal datoteka za oporavak", "account.add.success.title": "Novi novčanik stvoren 🎉", "account.addLabel.header": "Imenuj svoj novčanik", "account.addLabel.labelError.labelAlreadyExist": "Oznaka već postoji. <PERSON><PERSON><PERSON><PERSON> s drugom.", "account.addLabel.labelError.maxStringLengthExceeded": "Dosegnut je maks<PERSON>lan broj z<PERSON>", "account.add_active_wallet.primary_text": "Dodaj novčanik", "account.add_active_wallet.short_text": "<PERSON><PERSON>i, poveži ili uvezi novčanik", "account.add_from_ledger.success": "Novčanici dodani u Zeal", "account.add_tracked_wallet.primary_text": "Dodaj novčanik samo za čitanje", "account.add_tracked_wallet.short_text": "Pregledaj portfelj i aktivnost", "account.button.unlabelled-wallet": "Neoznačeni novčanik", "account.create_wallet": "Stvori novčanik", "account.label.edit.title": "Uredi oznaku novčanika", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON><PERSON> {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Datoteka za oporavak nije važeća", "account.recoveryKit.selectBackupFile.subtitle": "Odaberi datoteku za oporavak", "account.recoveryKit.selectBackupFile.title": "Datoteka za oporavak", "account.recoveryKit.success.recoveryFileFound": "Datoteka za oporavak pronađena 🎉", "account.select_type_of_account.create_eoa.short": "Stariji novčanik za stručnjake", "account.select_type_of_account.create_eoa.title": "Stvori novčanik s početnom frazom", "account.select_type_of_account.create_safe_wallet.title": "Stvori Smart Wallet", "account.select_type_of_account.existing_smart_wallet": "Postojeći Smart Wallet", "account.select_type_of_account.hardware_wallet": "Hardverski novčanik", "account.select_type_of_account.header": "Dodaj novčanik", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Privatni ključ / početna fraza", "account.select_type_of_account.read_only_wallet": "Novčanik samo za čitanje", "account.select_type_of_account.read_only_wallet.short": "Pregledaj bilo koji <PERSON>felj", "account.topup.title": "Dodaj sredstva u Zeal", "account.view.error.refreshAssets": "Osvježi", "account.widget.refresh": "Osvježi", "account.widget.settings": "Postavke", "accounts.view.copied-text": "<PERSON><PERSON><PERSON> {formattedAddress}", "accounts.view.copiedAddress": "<PERSON><PERSON><PERSON> {formattedAddress}", "action.accept": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.accpet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.allow": "<PERSON><PERSON><PERSON>", "action.back": "Natrag", "action.cancel": "Odustani", "action.card-activation.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> karticu", "action.claim": "Zatraži", "action.close": "Zatvori", "action.complete-steps": "<PERSON><PERSON><PERSON> korake", "action.confirm": "Potvrdi", "action.continue": "<PERSON><PERSON><PERSON>", "action.copy-address-understand": "U redu - kopiraj adresu", "action.deposit": "<PERSON><PERSON><PERSON>", "action.done": "Gotovo", "action.dontAllow": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "action.edit": "u<PERSON>i", "action.email-required": "Unesi e-mail", "action.enterPhoneNumber": "Unesi broj telefona", "action.expand": "Proširi", "action.fix": "<PERSON><PERSON><PERSON>", "action.getStarted": "<PERSON><PERSON><PERSON>", "action.got_it": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.hide": "<PERSON><PERSON><PERSON><PERSON>", "action.import": "<PERSON><PERSON><PERSON>", "action.import-keys": "Uvezi ključeve", "action.importKeys": "Uvezi ključeve", "action.minimize": "<PERSON><PERSON><PERSON>", "action.next": "<PERSON><PERSON>", "action.ok": "U redu", "action.reduceAmount": "S<PERSON><PERSON> na maks.", "action.refreshWebsite": "Osvježi stranicu", "action.remove": "Ukloni", "action.remove-account": "Ukloni račun", "action.requestCode": "Zatraži kôd", "action.resend_code": "Ponovno pošalji k<PERSON>d", "action.resend_code_with_time": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> kôd {time}", "action.retry": "<PERSON><PERSON><PERSON>", "action.reveal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.save": "Sp<PERSON>i", "action.save_changes": "Spremi RPC", "action.search": "Traži", "action.seeAll": "Prikaži sve", "action.select": "<PERSON><PERSON><PERSON><PERSON>", "action.send": "Pošalji", "action.skip": "Preskoči", "action.submit": "Potvrdi", "action.understood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.update": "<PERSON><PERSON><PERSON><PERSON>", "action.update-gnosis-pay-owner.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.zeroAmount": "Unesi iznos", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Kolekcionarski predmeti", "action_bar_title.tokens": "Tokeni", "action_bar_title.transaction_request": "Zahtjev za transakciju", "activate-monerium.loading": "Postavljanje tvog osobnog računa", "activate-monerium.success.title": "Monerium o<PERSON>gu<PERSON>en", "activate-physical-card-widget.subtitle": "Dostava može potrajati 3 tjedna", "activate-physical-card-widget.title": "Aktiviraj fizičku karticu", "activate-smart-wallet.title": "Aktiviraj novčanik", "active_and_tracked_wallets.title": "Zeal pokriva sve tvoje naknade na {network}, što ti omogućuje besplatne transakcije!", "activity.approval-amount.revoked": "Opozvano", "activity.approval-amount.unlimited": "Neo<PERSON><PERSON><PERSON><PERSON>", "activity.approval.approved_for": "Odobreno za", "activity.approval.approved_for_with_target": "Odobreno {approvedTo}", "activity.approval.revoked_for": "Opozvano za", "activity.bank.serviceProvider": "Pružate<PERSON>j <PERSON>", "activity.bridge.serviceProvider": "Pružate<PERSON>j <PERSON>", "activity.cashback.period": "Razdoblje povrata novca", "activity.filter.card": "Kartica", "activity.rate": "<PERSON><PERSON><PERSON>", "activity.receive.receivedFrom": "Primljeno od", "activity.send.sendTo": "Poslano na", "activity.smartContract.unknown": "Nepoznati ugovor", "activity.smartContract.usingContract": "<PERSON><PERSON><PERSON> se ugovor", "activity.subtitle.pending_timer": "{timerString} Na čekanju", "activity.title.arbitrary_smart_contract_interaction": "{function} na {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Nepoznata interakcija s ugovorom", "activity.title.bridge.from": "Most s {token}", "activity.title.bridge.to": "Most na {token}", "activity.title.buy": "<PERSON><PERSON> {asset}", "activity.title.card_owners_updated": "Vlasnici kartice ažurirani", "activity.title.card_spend_limit_updated": "Postavljen limit potrošnje kartice", "activity.title.cashback_deposit": "Polog na povrat novca", "activity.title.cashback_reward": "Nagradni povrat novca", "activity.title.cashback_withdraw": "Isplata s povrata novca", "activity.title.claimed_reward": "Zatražena nagrada", "activity.title.deployed_smart_wallet_gnosis": "<PERSON><PERSON><PERSON> st<PERSON>en", "activity.title.deposit_from_bank": "Polog iz banke", "activity.title.deposit_into_card": "Polog na karticu", "activity.title.deposit_into_earn": "Polog na {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} na {smartContract}", "activity.title.from": "Od {sender}", "activity.title.pendidng_areward_claim": "Preuzimanje nagrade", "activity.title.pendidng_breward_claim": "Preuzimanje nagrade", "activity.title.recharge_disabledh": "Nadoplata kartice onemogućena", "activity.title.recharge_set": "Postavljen cilj nadoplate", "activity.title.recovered_smart_wallet_gnosis": "Instalacija novog uređaja", "activity.title.send_pending": "<PERSON>a {receiver}", "activity.title.send_to_bank": "U banku", "activity.title.swap": "<PERSON><PERSON> {token}", "activity.title.to": "<PERSON>a {receiver}", "activity.title.withdraw_from_card": "Is<PERSON><PERSON><PERSON> s kartice", "activity.title.withdraw_from_earn": "<PERSON><PERSON><PERSON>a s {earn}", "activity.transaction.networkFees": "<PERSON><PERSON><PERSON><PERSON> naknade", "activity.transaction.state": "Završena transakcija", "activity.transaction.state.completed": "Završena transakcija", "activity.transaction.state.failed": "Neuspjela transakcija", "add-account.section.import.header": "<PERSON><PERSON><PERSON>", "add-another-card-owner": "Dodaj drugog vlasnika kartice", "add-another-card-owner.Recommended.footnote": "Dodaj svoj Zeal novčanik kao dodatnog vlasnika na Gnosis Pay karticu", "add-another-card-owner.Recommended.primaryText": "Do<PERSON>j <PERSON> na Gnosis Pay", "add-another-card-owner.recommended": "Preporučeno", "add-owner.confirmation.subtitle": "<PERSON><PERSON>, prom<PERSON>ne postavki traju 3 minute. Za to vrijeme tvoja će kartica biti privremeno zamrznuta i plaćanja neće biti moguća.", "add-owner.confirmation.title": "Tvoja kartica bit će zamrznuta 3 min dok se postavke ažuriraju", "add-readonly-signer-if-not-exist.error.already_in_use.title": "<PERSON><PERSON> mogu<PERSON>e dodati novčanik, već se koristi", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "Probaj drugi", "add.account.backup.decrypt.success": "Novčanik je vraćen", "add.account.backup.password.passwordIncorrectMessage": "Lozinka je netočna", "add.account.backup.password.subtitle": "Unesi lozinku koju si koristio/la za šifriranje svoje datoteke za oporavak", "add.account.backup.password.title": "<PERSON><PERSON> lozinku", "add.account.google.login.subtitle": "Odobri zahtjev na Google Driveu za sinkronizaciju svoje datoteke za oporavak", "add.account.google.login.title": "Čekam odobrenje...", "add.readonly.already_added": "Novčanik je već dodan", "add.readonly.continue": "<PERSON><PERSON><PERSON>", "add.readonly.empty": "Unesi adresu ili E<PERSON>", "addBankRecipient.title": "<PERSON><PERSON><PERSON> prima<PERSON>", "add_funds.deposit_from_bank_account": "Polog s bankovnog računa", "add_funds.from_another_wallet": "Iz drugog novčanika", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Poveži se s dApp za nadoplatu", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Poveži bilo koji novčanik na Zeal dApp za nadoplatu i brzo pošalji sredstva u svoj novčanik", "add_funds.from_crypto_wallet.header": "Iz drugog novčanika", "add_funds.from_crypto_wallet.header.show_wallet_address": "Prikaži adresu svog novčanika", "add_funds.from_exchange.header": "Pošalji s mjenjačnice", "add_funds.from_exchange.header.copy_wallet_address": "<PERSON><PERSON><PERSON> s<PERSON> ad<PERSON>", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance itd.", "add_funds.from_exchange.header.open_exchange": "Otvori aplikaciju ili stranicu mjenjačnice", "add_funds.from_exchange.header.selected_token": "Pošalji {token} u Zeal", "add_funds.from_exchange.header.selected_token.subtitle": "Na {network}", "add_funds.from_exchange.header.send_selected_token": "Pošalji podržani token", "add_funds.from_exchange.header.send_selected_token.subtitle": "Odaberi podržani token i mrežu", "add_funds.import_wallet": "Uvezi postojeći kripto novčanik", "add_funds.title": "Uplati na svoj račun", "add_funds.transfer_from_exchange": "Prijenos s mjenjačnice", "address.add.header": "Pregledaj svoj novčanik u Zealu{br}u načinu samo za čitanje", "address.add.subheader": "Unesi svoju adresu ili ENS i pregledaj svoju imovinu na svim EVM mrežama na jednom mjestu. Kasnije možeš stvoriti ili uvesti više novčanika.", "address_book.change_account.bank_transfers.header": "Bankovni primatelji", "address_book.change_account.bank_transfers.primary": "Bankovni primatelj", "address_book.change_account.cta": "Prati novč<PERSON>k", "address_book.change_account.search_placeholder": "<PERSON><PERSON><PERSON> ili pretraži ad<PERSON>u", "address_book.change_account.tracked_header": "Novčanici samo za čitanje", "address_book.change_account.wallets_header": "Aktivni novčanici", "app-association-check-failed.modal.cta": "Pokušaj ponovno", "app-association-check-failed.modal.subtitle": "Pokušaj ponovno. Problemi s vezom usporavaju dohvaćanje pristupnih ključeva. <PERSON><PERSON>, ponovno pokreni Zeal.", "app-association-check-failed.modal.subtitle.creation": "Pokušaj ponovno. Problemi s vezom usporavaju izradu pristupnog ključa. <PERSON><PERSON>, ponovno pokreni Zeal.", "app-association-check-failed.modal.title.creation": "Uređaj nije uspio izraditi pristupni ključ", "app-association-check-failed.modal.title.signing": "Uređaj nije učitao pristupne ključeve", "app.app_protocol_group.borrowed_tokens": "Posuđ<PERSON>", "app.app_protocol_group.claimable_amount": "Iznos za preuzimanje", "app.app_protocol_group.health_rate": "Stopa zdravlja", "app.app_protocol_group.lending": "Pozajmljivanje", "app.app_protocol_group.locked_tokens": "Zaključani tokeni", "app.app_protocol_group.nfts": "Kolekcionarski predmeti", "app.app_protocol_group.reward_tokens": "Nagradni <PERSON>", "app.app_protocol_group.supplied_tokens": "Priloženi tokeni", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Token u stjecanju", "app.appsGroupHeader.discoverMore": "Otkrij više", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Pretraži ili unesi URL", "app.error-banner.cory": "Ko<PERSON>raj podatke o pogrešci", "app.error-banner.retry": "Pokušaj ponovno", "app.list_item.rewards": "Nagrade {value}", "app.position_details.health_rate.description": "To je omjer tvog zajma i kolaterala.", "app.position_details.health_rate.title": "<PERSON>to je omjer zaduženosti?", "approval.edit-limit.label": "Uredi limit potrošnje", "approval.permit_info": "Informacije o dozvoli", "approval.spend-limit.edit-modal.cancel": "Odustani", "approval.spend-limit.edit-modal.limit-label": "Limit <PERSON>", "approval.spend-limit.edit-modal.max-limit-error": "Upozorenje, visok limit", "approval.spend-limit.edit-modal.revert": "Poništi promjene", "approval.spend-limit.edit-modal.set-to-unlimited": "Postavi na neograničeno", "approval.spend-limit.edit-modal.submit": "S<PERSON><PERSON>i promje<PERSON>", "approval.spend-limit.edit-modal.title": "Uredi dopuštenja", "approval.spend_limit_info": "Što je limit potrošnje?", "approval.what_are_approvals": "<PERSON>to su odobrenja?", "apps_list.page.emptyState": "Nema aktivnih aplikacija", "backpace.removeLastDigit": "Ukloni zadnju znamenku", "backup-banner.backup_now": "Izradi kopiju", "backup-banner.risk_losing_funds": "Izradi sigurnosnu kopiju ili riskiraš gubitak sredstava", "backup-banner.title": "Novčanik nema sigurnosnu kopiju", "backupRecoverySmartWallet.noExportPrivateKeys": "Automatska sigurnosna kopija: Tvoj Smart Wallet sprema se kao pristupni ključ – tajna fraza ili privatni ključ nisu potrebni.", "backupRecoverySmartWallet.safeContracts": "Sigurnost s više kl<PERSON>: Zeal novčanici rade na Safe ugovorima, tako da više uređaja može odobriti transakciju. Nema jedne toč<PERSON> k<PERSON>a.", "backupRecoverySmartWallet.security": "Više uređaja: Svoj novčanik možeš koristiti na više uređaja pomoću pristupnog ključa. Svaki uređaj dobiva vlastiti privatni ključ.", "backupRecoverySmartWallet.showLocalPrivateKey": "Stručni način: <PERSON><PERSON><PERSON>š izvesti privatni ključ ovog uređaja, koristiti ga u drugom novčaniku i povezati se na <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Prikaži privatni ključ</Key>", "backupRecoverySmartWallet.storingKeys": "Sinkronizirano u oblaku: Pristupni ključ sigurno je pohranjen u iCloudu, Google upravitelju lozinki ili tvom upravitelju lozinki.", "backupRecoverySmartWallet.title": "Sigurnosna kopija i oporavak Smart Walleta", "balance-change.card.titile": "Kartica", "balanceChange.pending": "Na čekanju", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Odaberi pružatelja usluge", "bank-transfer.change-deposit-receiver.subtitle": "Ovaj će novčanik primati sve bankovne pologe", "bank-transfer.change-deposit-receiver.title": "Postavi novčanik za primanje", "bank-transfer.change-owner.subtitle": "Ovaj novčanik služi za prijavu i oporavak tvog računa za bankovni prijenos", "bank-transfer.change-owner.title": "Postavi vlasnika računa", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Sve bankovne pologe koje šalješ na Zeal primit će ovaj novčanik.", "bank-transfer.configrm-change-deposit-receiver.title": "Promijeni novčanik za primanje", "bank-transfer.configrm-change-owner.subtitle": "Ž<PERSON>š li promijeniti vlasnika računa? Koristi se za prijavu i oporavak.", "bank-transfer.configrm-change-owner.title": "Promijeni vlasnika računa", "bank-transfer.deposit.widget.status.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfer.deposit.widget.status.funds_received": "Sredstva primljena", "bank-transfer.deposit.widget.status.sending_to_wallet": "Slanje u novčanik", "bank-transfer.deposit.widget.status.transfer-on-hold": "Prijenos na čekanju", "bank-transfer.deposit.widget.status.transfer-received": "Slanje u novčanik", "bank-transfer.deposit.widget.subtitle": "{from} u {to}", "bank-transfer.deposit.widget.title": "<PERSON><PERSON>", "bank-transfer.intro.bulletlist.point_1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfer.intro.bulletlist.point_2": "Prijenos između EUR/GBP i više od 10 tokena", "bank-transfer.intro.bulletlist.point_3": "0 % naknade do 5 tisuća $ mjesečno, 0,2 % nakon toga", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Slanje u banku", "bank-transfer.withdrawal.widget.status.in-progress": "Prijenos u tijeku", "bank-transfer.withdrawal.widget.status.on-hold": "Prijenos na čekanju", "bank-transfer.withdrawal.widget.status.success": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfer.withdrawal.widget.subtitle": "{from} u {to}", "bank-transfer.withdrawal.widget.title": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfers.bank-account-actions.remove-this-account": "Ukloni ovaj račun", "bank-transfers.bank-account-actions.switch-to-this-account": "Prebaci se na ovaj račun", "bank-transfers.deposit.fees-for-less-than-5k": "Naknade za 5 tisuća $ ili manje", "bank-transfers.deposit.fees-for-more-than-5k": "Naknade za više od 5 tisuća $", "bank-transfers.set-receiving-bank.title": "Postavi banku za primanje", "bank-transfers.settings.account_owner": "Vlasnik računa", "bank-transfers.settings.receiver_of_bank_deposits": "Primatelj bankovnih pologa", "bank-transfers.settings.receiver_of_withdrawals": "Primatelj isplat<PERSON>", "bank-transfers.settings.registered_email": "Registrirana e-pošta", "bank-transfers.settings.title": "Postavke bankovnog prijenosa", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} račun", "bank-transfers.setup.bank-account": "Bankovni račun", "bankTransfer.withdraw.max_loading": "Maks.: {amount}", "bank_details_do_not_match.got_it": "U redu", "bank_details_do_not_match.subtitle": "Sort kod i broj računa se ne podudaraju. Provjeri jesu li podaci točno uneseni i pokušaj ponovno.", "bank_details_do_not_match.title": "Bankovni podaci se ne podudaraju", "bank_tranfsers.select_country_of_residence.country_not_supported": "<PERSON><PERSON><PERSON><PERSON>, bankovni prijenosi nisu podržani u {country} još", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Otvori svoju bankovnu aplikaciju", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Pošalji {fiatCurrencyCode} na svoj račun", "bank_transfer.deposit.header": "{fullName} osobni podaci&nbsp;računa", "bank_transfer.kyc_status_widget.subtitle": "Bankovni prijenosi", "bank_transfer.kyc_status_widget.title": "Provjera identiteta", "bank_transfer.personal_details.date_of_birth": "<PERSON><PERSON> ro<PERSON>", "bank_transfer.personal_details.date_of_birth.invalid_format": "Datum je neva<PERSON>i", "bank_transfer.personal_details.date_of_birth.too_young": "<PERSON><PERSON>š imati najmanje 18 godina", "bank_transfer.personal_details.first_name": "Ime", "bank_transfer.personal_details.last_name": "Prezime", "bank_transfer.personal_details.title": "Tvoji podaci", "bank_transfer.reference.label": "Referenca (neobavezno)", "bank_transfer.reference_message": "Poslano sa Zeala", "bank_transfer.residence_details.address": "Tvoja adresa", "bank_transfer.residence_details.city": "Grad", "bank_transfer.residence_details.country_of_residence": "Zemlja prebivališta", "bank_transfer.residence_details.country_placeholder": "Zemlja", "bank_transfer.residence_details.postcode": "Poštanski broj", "bank_transfer.residence_details.street": "Ulica", "bank_transfer.residence_details.your_residence": "Tvoje prebivalište", "bank_transfers.choose-wallet.continue": "<PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.test": "Dodaj novčanik", "bank_transfers.choose-wallet.warning.subtitle": "<PERSON><PERSON><PERSON>š povezati samo jedan novčanik. Povezani novčanik nećeš moći promijeniti.", "bank_transfers.choose-wallet.warning.title": "Pažljivo odaberi novčanik", "bank_transfers.choose_wallet.subtitle": "Odaberi novčanik za bankovne prijenose. ", "bank_transfers.choose_wallet.title": "Odaberi novčanik", "bank_transfers.continue": "<PERSON><PERSON><PERSON>", "bank_transfers.currency_is_currently_not_supported": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit-header": "<PERSON><PERSON>", "bank_transfers.deposit.account-name": "Ime na računu", "bank_transfers.deposit.account-number-copied": "Broj računa kopiran", "bank_transfers.deposit.amount-input": "Iznos za polog", "bank_transfers.deposit.amount-output": "Odredišni iznos", "bank_transfers.deposit.amount-output.error": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.buttet-point.receive-crypto": "Primi {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.currency-not-supported.subtitle": "Bankovni polozi iz {code} su onemogućeni do daljnjega.", "bank_transfers.deposit.currency-not-supported.title": "{code} <PERSON><PERSON> trenutno nisu podrž<PERSON>", "bank_transfers.deposit.default-token.balance": "<PERSON><PERSON> {amount}", "bank_transfers.deposit.deposit-header": "<PERSON><PERSON>", "bank_transfers.deposit.enter_amount": "Unesi iznos", "bank_transfers.deposit.iban-copied": "IBAN kopiran", "bank_transfers.deposit.increase-amount": "Minimalni prijenos je {limit}", "bank_transfers.deposit.loading": "Učitavanje", "bank_transfers.deposit.max-limit-reached": "Iznos premašuje maksimalni limit prijenosa", "bank_transfers.deposit.modal.kyc.button-text": "Započni", "bank_transfers.deposit.modal.kyc.text": "Za provjeru tvog identiteta trebat će nam neki osobni podaci i dokumentacija. Slanje obično traje samo nekoliko minuta.", "bank_transfers.deposit.modal.kyc.title": "Potvrdi svoj identitet za veće limite", "bank_transfers.deposit.reduce_amount": "Smanji <PERSON>", "bank_transfers.deposit.show-account.account-number": "Broj računa", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Sort kod", "bank_transfers.deposit.sort-code-copied": "Sort kod kopiran", "bank_transfers.deposit.withdraw-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.failed_to_load_fee": "Nepoznato", "bank_transfers.fees": "Naknade", "bank_transfers.increase-amount": "Minimalni prijenos je {limit}", "bank_transfers.insufficient-funds": "Nedovoljno sredstava", "bank_transfers.select_country_of_residence.title": "G<PERSON>je živ<PERSON>?", "bank_transfers.setup.cta": "Postavi bankovni prijenos", "bank_transfers.setup.enter-amount": "Unesi iznos", "bank_transfers.source_of_funds.form.business_income": "Poslovni prihod", "bank_transfers.source_of_funds.form.other": "Ostalo", "bank_transfers.source_of_funds.form.pension": "Mirovina", "bank_transfers.source_of_funds.form.salary": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.title": "Tvoj izvor sredstava", "bank_transfers.source_of_funds_description.placeholder": "Opiši izvor sredstava...", "bank_transfers.source_of_funds_description.title": "Reci nam više o svom izvoru sredstava", "bank_transfers.withdraw-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.withdraw.amount-input": "Iznos za isplatu", "bank_transfers.withdraw.max-limit-reached": "Iznos premašuje maksimalni limit prijenosa", "bank_transfers.withdrawal.verify-id": "Smanji <PERSON>", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Prekoračen je maksimalni limit unosa", "banner.above_maximum_limit.maximum_limit_per_deposit": "Ovo je maksimalni limit po uplati", "banner.above_maximum_limit.subtitle": "Prekoračen je maksimalni limit unosa", "banner.above_maximum_limit.title": "Smanji iz<PERSON> na {amount} ili manje", "banner.above_maximum_limit.title.default": "Smanji <PERSON>", "banner.below_minimum_limit.minimum_input_limit_exceeded": "<PERSON><PERSON>ut minimalni limit unosa", "banner.below_minimum_limit.minimum_limit_for_token": "Ovo je minimalni limit za ovaj token", "banner.below_minimum_limit.title": "Povećaj iznos na {amount} ili više", "banner.below_minimum_limit.title.default": "Povećaj iznos", "breaard.in_porgress.info_popup.cta": "Potroši i zaradi {earn}", "breaard.in_porgress.info_popup.footnote": "Korištenjem Zeala i Gnosis Pay kartice pristaješ na uvjete ove nagradne kampanje.", "breaward.in_porgress.info_popup.bullet_point_1": "Po<PERSON>š<PERSON> {remaining} u roku od {time} da biste zatražili ovu nagradu.", "breaward.in_porgress.info_popup.bullet_point_2": "Računaju se samo kupnje putem Gnosis Paya.", "breaward.in_porgress.info_popup.bullet_point_3": "Tražena nagrada šalje se na Zeal račun.", "breaward.in_porgress.info_popup.header": "<PERSON><PERSON><PERSON><PERSON> {earn}, tro<PERSON><PERSON><PERSON><PERSON> {remaining}", "breward.celebration.for_spending": "Za trošenje karticom", "breward.dc25-eligible-celebration.for_spending": "<PERSON><PERSON><PERSON> prvih si {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "<PERSON><PERSON> među prvih {limit} koji su potrošili", "breward.expired_banner.earn_by_spending": "<PERSON><PERSON><PERSON> {earn} trošenjem {amount}", "breward.expired_banner.reward_expired": "{earn} nagrada je istekla", "breward.in_progress_banner.cta.title": "Potroši i zaradi {earn}", "breward.ready_to_claim.error.try_again": "Pokušaj ponovno", "breward.ready_to_claim.error_title": "Preuzimanje nagrade neuspješno", "breward.ready_to_claim.in_progress": "Preuzimanje nagrade", "breward.ready_to_claim.youve_earned": "Zaradio si {earn}!", "breward_already_claimed.title": "Nagrada je već preuzeta. <PERSON>ko nisi primio <PERSON>, javi se <PERSON>.", "breward_cannotbe_claimed.title": "Nagradu nije moguće preuzeti. Pokušaj kasnije.", "bridge.best_return": "Ruta s najboljim povratom", "bridge.best_serivce_time": "Ruta s najbržim vremenom usluge", "bridge.check_status.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge.check_status.progress_text": "Premošćivanje {from} u {to}", "bridge.remove_topup": "Ukloni nadoplatu", "bridge.request_status.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge.request_status.pending": "Na čekanju", "bridge.widget.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge.widget.currencies": "{from} u {to}", "bridge_rote.widget.title": "Most", "browse.discover_more_apps": "Otkrij više aplikacija", "browse.google_search_term": "<PERSON><PERSON><PERSON><PERSON> \"{searchTerm}\"", "brward.celebration.you_earned": "Zaradio si", "brward.expired_banner.subtitle": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>e drugi put", "brward.in_progress_banner.subtitle": "Istječe za {expiredInFormatted}", "buy": "<PERSON><PERSON>", "buy.enter_amount": "Unesi iznos", "buy.loading": "Učitavanje...", "buy.no_routes_found": "<PERSON><PERSON> prona<PERSON><PERSON> ruta", "buy.not_enough_balance": "Nedovoljno stanje", "buy.select-currency.title": "Odaberi token", "buy.select-to-currency.title": "<PERSON><PERSON>", "buy_form.title": "Kupi token", "cancelled-card.create-card-button.primary": "Zatraži novu virtualnu karticu", "cancelled-card.switch-card-button.primary": "<PERSON><PERSON><PERSON><PERSON> kartic<PERSON>", "cancelled-card.switch-card-button.short-text": "<PERSON><PERSON><PERSON> još jednu aktivnu karticu", "card": "Kartica", "card-add-cash.confirm-stage.banner.no-routes-found": "<PERSON><PERSON> ruta, p<PERSON><PERSON><PERSON> s drugim tokenom ili iznosom", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Potrebno ti je {amount} vi<PERSON>e {symbol} za plaćanje naknada", "card-add-cash.confirm-stage.banner.value-loss": "<PERSON><PERSON><PERSON><PERSON>e<PERSON> {loss} vrijednosti", "card-add-cash.confirm-stage.banner.value-loss.revert": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.cancel": "Odustani", "card-add-cash.edit-stage.cta.continue": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.enter-amount": "Unesi iznos", "card-add-cash.edit-stage.cta.reduce-to-max": "Svedi na max", "card-add-cash.edit-staget.banner.no-routes-found": "<PERSON><PERSON> ruta, p<PERSON><PERSON><PERSON> s drugim tokenom ili iznosom", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Zahtjev je poslan na tvoj hardverski novčanik. Nastavi tamo.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Potpiši na hardverskom novčaniku", "card-balance": "<PERSON><PERSON>: {balance}", "card-cashback.status.title": "Polog u povrat novca", "card-copy-safe-address.copy_address": "<PERSON><PERSON><PERSON>", "card-copy-safe-address.copy_address.done": "<PERSON><PERSON><PERSON>", "card-copy-safe-address.warning.description": "Ova adresa može primiti samo {cardAsset} na Gnosis Chainu. Ne šalji sredstva s drugih mreža na ovu adresu. Bit će izgubljena.", "card-copy-safe-address.warning.header": "<PERSON><PERSON><PERSON> samo {cardAsset} na Gnosis Chainu", "card-marketing-card.center.subtitle": "Naknade za tečaj", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "<PERSON><PERSON>", "card-marketing-card.right.subtitle": "Poklon za prijavu", "card-marketing-card.title": "Europska VISA kartica s visokom kamatom", "card-marketing-tile.get-started": "Započni", "card-select-from-token-title": "Odaberi token za slanje", "card-top-up.banner.subtitle.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card-top-up.banner.subtitle.failed": "Neuspješno", "card-top-up.banner.subtitle.pending": "{timerString} U tijeku", "card-top-up.banner.title": "<PERSON>lata u tijeku {amount}", "card-topup.select-token.emptyState": "<PERSON><PERSON> pro<PERSON>", "card.activate.card_number_not_valid": "Broj kartice nije važeći. Provjeri i pokušaj ponovno.", "card.activate.invalid_card_number": "Nevažeći broj kartice.", "card.activation.activate_physical_card": "Aktiviraj fizičku karticu", "card.add-cash.amount-to-withdraw": "Iznos nadoplate", "card.add-from-earn-form.title": "Dodaj s<PERSON>stva na karticu", "card.add-from-earn-form.withdraw-to-card": "<PERSON><PERSON><PERSON>", "card.add-from-earn.amount-to-withdraw": "Iznos za isplatu na karticu", "card.add-from-earn.enter-amount": "Unesi iznos", "card.add-from-earn.loading": "Učitavanje", "card.add-from-earn.max-label": "Stanje: {amount}", "card.add-from-earn.no-routes-found": "<PERSON><PERSON> ruta", "card.add-from-earn.not-enough-balance": "Nedovoljno sredstava", "card.add-owner.queued": "Dodavanje vlasnika u redu čekanja", "card.add-to-wallet-flow.subtitle": "Plaćaj iz svog novčanika.", "card.add-to-wallet.copy-card-number": "<PERSON><PERSON><PERSON> broj kartice ispod", "card.add-to-wallet.title": "<PERSON><PERSON>j u {platformName} Wallet", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "OTKAZANO", "card.card-owner-not-found.disconnect-btn": "Odspoji karticu sa Zeala", "card.card-owner-not-found.subtitle": "Za nastavak korištenja Gnosis Pay kartice u Zealu, ažuriraj vlasnika kartice kako bi je ponovno povezao", "card.card-owner-not-found.title": "Ponovno poveži karticu", "card.card-owner-not-found.update-owner-btn": "Ažuriraj vlasnika kartice", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} za {date}", "card.cashback.widgetNoCashback.subtitle": "Uplati za početak zarade", "card.cashback.widgetNoCashback.title": "Ostvari do {defaultPercentage} povrata novca", "card.cashback.widgetcashbackValue.rewards": "{amount} na čekanju", "card.cashback.widgetcashbackValue.title": "{percentage} povrat novca", "card.choose-wallet.connect_card": "Poveži karticu", "card.choose-wallet.create-new": "Dodaj novi novčanik kao vlasnika", "card.choose-wallet.import-another-wallet": "Uvezi drugi novčanik", "card.choose-wallet.import-current-owner": "Uvezi trenutnog vlasnika kartice", "card.choose-wallet.import-current-owner.sub-text": "Uvezi privatne ključeve ili seed frazu koja posjeduje tvoju Gnosis Pay karticu", "card.choose-wallet.title": "Odaberi novčanik za upravljanje karticom", "card.connectWalletToCardGuide": "<PERSON><PERSON><PERSON> nov<PERSON>", "card.connectWalletToCardGuide.addGnosisPayOwner": "Dodaj Gnosis Pay vlasnika", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Otvori Gnosispay.com sa svojim drugim novčanikom{br}2. Klikni na „Račun”{br}3. Klikni na „Podaci o računu”{br}4. Klikni na „Uredi” pokraj „Vlasnik računa” i{br}5. Klikni na „Dodaj adresu”{br}6. Zalijepi svoju Zeal adresu i klikni na Spremi", "card.connectWalletToCardGuide.header": "Po<PERSON><PERSON><PERSON> {account} s Gnosis Pay karticom", "card.connect_card.start": "Spoji Gnosis Pay karticu", "card.copiedAddress": "<PERSON><PERSON><PERSON> {formattedAddress}", "card.disconnect-account.title": "Odspoji račun", "card.hw-wallet-support-drop.add-owner-btn": "Dodaj novog vlasnika", "card.hw-wallet-support-drop.disconnect-btn": "Odspoji karticu sa Zeala", "card.hw-wallet-support-drop.subtitle": "Za nastavak korištenja Gnosis Pay kartice u Zealu, molimo dodaj drugog vlasnika kartici koji nije hardverski novčanik.", "card.hw-wallet-support-drop.title": "Zeal više ne podržava hardverske novčanike za karticu", "card.kyc.continue": "<PERSON><PERSON><PERSON>", "card.list_item.title": "Kartica", "card.onboarded.transactions.empty.description": "Tvoja aktivnost plaćanja prikazat će se ovdje", "card.onboarded.transactions.empty.title": "Aktivnost", "card.order.continue": "Nastavi narudžbu kartice", "card.order.free_virtual_card": "<PERSON><PERSON><PERSON> be<PERSON>nu e-karticu", "card.order.start": "Na<PERSON><PERSON><PERSON> karticu besplatno", "card.owner-not-imported.cancel": "Odustani", "card.owner-not-imported.import": "Uvezi račun", "card.owner-not-imported.subtitle": "Za autorizaciju ove transakcije, poveži vlasnički novčanik svog Gnosis Pay računa sa Zealom. Napomena: Ovo je odvojeno od tvoje uobičajene prijave u Gnosis Pay novčanik.", "card.owner-not-imported.title": "Dodaj vlasnika Gnosis Pay računa", "card.page.order_free_physical_card": "Na<PERSON><PERSON>i besplatnu fizičku", "card.pin.change_pin_at_atm": "PIN se može promijeniti na odabranim bankomatima", "card.pin.timeout": "<PERSON><PERSON>lon će se zatvoriti za {seconds} s", "card.quick-actions.add-assets": "Nadoplat<PERSON>", "card.quick-actions.add-cash": "Nadoplat<PERSON>", "card.quick-actions.details": "<PERSON><PERSON><PERSON>", "card.quick-actions.freeze": "<PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.freezing": "Zamrzavanje", "card.quick-actions.unfreeze": "Odmrzni", "card.quick-actions.unfreezing": "Odmrzavanje", "card.quick-actions.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "card.read-only-detected.create-new": "Dodaj novi novčanik kao vlasnika", "card.read-only-detected.import-current-owner": "Uvezi ključeve za {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Uvezi privatne ključeve ili seed frazu novčanika {address}", "card.read-only-detected.title": "Kartica otkrivena na novčaniku samo za čitanje. Odaberi novčanik za upravljanje karticom", "card.remove-owner.queued": "Uklanjanje vlasnika u redu čekanja", "card.settings.disconnect-from-zeal": "Odspoji sa Zeala", "card.settings.edit-owners": "Promijeni vlasnike kartice", "card.settings.getCard": "<PERSON><PERSON><PERSON> drugu karticu", "card.settings.getCard.subtitle": "Virtualne ili fizičke kartice", "card.settings.notRecharging": "Ne nadopunjuje se", "card.settings.notifications.subtitle": "Primaj obavijesti o plaćanju", "card.settings.notifications.title": "Obavijesti kartice", "card.settings.page.title": "Postavke kartice", "card.settings.select-card.cancelled-cards": "Otkazane kartice", "card.settings.setAutoRecharge": "Postavi automatsku nadoplatu", "card.settings.show-card-address": "Prikaži adresu kartice", "card.settings.spend-limit": "Postavi limit potrošnje", "card.settings.spend-limit-title": "Trenutni dnevni limit: {limit}", "card.settings.switch-active-card": "Promijeni aktivnu karticu", "card.settings.switch-active-card-description": "Aktivna kartica: {card}", "card.settings.switch-card.card-item.cancelled": "Otkazana", "card.settings.switch-card.card-item.frozen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.title": "Gnosis Pay kartica", "card.settings.switch-card.card-item.title.physical": "Fizička kartica", "card.settings.switch-card.card-item.title.virtual": "Virtualna kartica", "card.settings.switch-card.title": "<PERSON><PERSON><PERSON><PERSON> karticu", "card.settings.targetBalance": "Ciljano stanje: {threshold}", "card.settings.view-pin": "Prikaži PIN", "card.settings.view-pin-description": "Uvijek zaštiti svoj PIN", "card.title": "Kartica", "card.transactions.header": "Transakcije kartice", "card.transactions.see_all": "Prikaži sve transakcije", "card.virtual": "VIRTUALNA", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Povrat novca šalje se na tvoju karticu početkom tjedna nakon što je zarađen.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "<PERSON><PERSON> više up<PERSON>, to više zarađuješ pri svakoj kupnji.", "cardCashback.onboarding.title": "Ostvari do {percentage} povrata novca", "cardCashbackWithdraw.amount": "Iznos za isplatu", "cardCashbackWithdraw.header": "Isplati {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "Kartica je blokirana i nije se mogla aktivirati", "cardWidget.cashback": "Povrat novca", "cardWidget.cashbackUpToDefaultPercentage": "Do {percentage}", "cardWidget.startEarning": "Počni zarađivati", "cardWithdraw.amount": "<PERSON><PERSON><PERSON> isplate", "cardWithdraw.header": "Is<PERSON><PERSON><PERSON> s kartice", "cardWithdraw.selectWithdrawWallet.title": "Odaberi novčanik za{br}isplatu", "cardWithdraw.success.cta": "Zatvori", "cardWithdraw.success.subtitle": "<PERSON><PERSON> si<PERSON> razloga, sve isplate s Gnosis Pay kartice obrađuju se 3 minute", "cardWithdraw.success.title": "<PERSON>va promjena potrajat će 3 minute", "card_top_up_trx.send": "Pošalji", "card_top_up_trx.to": "<PERSON>a", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "<PERSON><PERSON> isteka", "cards.card_number": "Broj kartice", "cards.choose-wallet.no-active-accounts": "Nemaš aktivnih novčanika", "cards.copied_card_number": "Broj kartice kopiran", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Prekoračen dnevni limit", "cards.transactions.decline_reason.incorrect_pin": "Neispravan PIN", "cards.transactions.decline_reason.incorrect_security_code": "Neispravan sigurnosni kod", "cards.transactions.decline_reason.invalid_amount": "Nevažeći iznos", "cards.transactions.decline_reason.low_balance": "Nedovoljno sredstava", "cards.transactions.decline_reason.other": "Odbijeno", "cards.transactions.decline_reason.pin_tries_exceeded": "Prekoračen broj pokušaja PIN-a", "cards.transactions.status.refund": "<PERSON><PERSON><PERSON>", "cards.transactions.status.reversal": "Storniranje", "cashback-deposit.trx.title": "Polog na povrat novca", "cashback-estimate.text": "Ovo je procjena, a NE zajamčena isplata. Primjenjuju se sva javno poznata pravila o povratu novca, no Gnosis Pay može isključiti transakcije prema vlastitom nahođenju. Maksimalna potrošnja od {amount} tjedno ispunjava uvjete za povrat novca, čak i ako bi procjena za ovu transakciju ukazivala na veći ukupan iznos.", "cashback-estimate.text.fallback": "<PERSON><PERSON> je procjena, a NE zajamčena isplata. Primjenjuju se sva javno poznata pravila za povrat novca, ali <PERSON> može isključiti transakcije prema vlastitom nahođenju.", "cashback-estimate.title": "Procjena povrata novca", "cashback-onbarding-tersm.subtitle": "Podaci o transakcijama tvoje kartice dijelit će se s tvrtko<PERSON> Karpatkey, koja je odgovorna za distribuciju nagrada za povrat novca. Klikom na Prihvati pristaješ na Gnosis DAO Cashback <terms>Uvjete i odredbe</terms>", "cashback-onbarding-tersm.title": "Uvjeti korištenja i privatnost", "cashback-tx-activity.retry": "Pokušaj ponovo", "cashback-unconfirmed-payments-info.subtitle": "Plaćanja ispunjavaju uvjete za povrat novca kada se podmire s trgovcem. Do tada se prikazuju kao nepotvrđena plaćanja. Nepodmirena plaćanja ne ispunjavaju uvjete za povrat novca.", "cashback-unconfirmed-payments-info.title": "Nepotvrđena plaćanja karticom", "cashback.activity.cashback": "Povrat novca", "cashback.activity.deposit": "<PERSON><PERSON><PERSON>", "cashback.activity.title": "Nedavna aktivnost", "cashback.activity.withdrawal": "<PERSON><PERSON><PERSON><PERSON>", "cashback.deposit": "<PERSON><PERSON><PERSON>", "cashback.deposit.amount.label": "<PERSON><PERSON><PERSON> uplate", "cashback.deposit.change": "{from} na {to}", "cashback.deposit.confirmation.subtitle": "Stope povrata novca ažuriraju se jednom tjedno. Uplati sada kako bi povećao povrat novca za sljedeći tjedan.", "cashback.deposit.confirmation.title": "Počet <PERSON> {percentage} od {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Zamijeni tokene u {currency} na {network} Chain", "cashback.deposit.get.tokens.title": "Nabavi {currency} tokene", "cashback.deposit.header": "Uplati {currency}", "cashback.deposit.max_label": "Max: {amount}", "cashback.deposit.select-wallet.title": "Odaberi novčanik za uplatu", "cashback.deposit.yourcashback": "Tvoj povrat novca", "cashback.header": "Povrat novca", "cashback.selectWithdrawWallet.title": "Odaberi novčanik{br}za isplatu", "cashback.transaction-details.network-label": "Mreža", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "S", "cashback.transaction-details.top-row.label-rewards": "Razdoblje povrata novca", "cashback.transaction-details.top-row.label-withdrawal": "Na", "cashback.transaction-details.transaction": "ID transakcije", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Transakcije povrata novca", "cashback.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "cashback.withdraw.confirmation.cashback_reduction": "Povrat novca za ovaj t<PERSON>, uklju<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>, s<PERSON><PERSON> se s {before} na {after}", "cashback.withdraw.queued": "Isplata u redu čekanja", "cashback.withdrawal.change": "{from} na {to}", "cashback.withdrawal.confirmation.subtitle": "Započni isplatu od {amount} s odgodom od 3 minute. To <PERSON>e smanjiti tvoj povrat novca na {after}.", "cashback.withdrawal.confirmation.title": "Povrat novca će se smanjiti ako isplatiš GNO", "cashback.withdrawal.delayTransaction.title": "Započni isplatu GNO-a s{br} odgodom od 3 minute", "cashback.withdrawal.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "cashback.withdrawal.yourcashback": "Tvoj povrat novca", "celebration.aave": "Zarađeno uz Aave", "celebration.cashback.subtitle": "Isporučeno u {code}", "celebration.cashback.subtitleGNO": "{amount} z<PERSON><PERSON><PERSON> zarađeno", "celebration.chf": "Zarađeno uz Frankencoin", "celebration.lido": "Zarađeno uz Lido", "celebration.sky": "Zarađeno uz Sky", "celebration.title": "Ukupan povrat novca", "celebration.well_done.title": "Bravo!", "change-withdrawal-account.add-new-account": "Dodaj drugi bankovni račun", "change-withdrawal-account.item.shortText": "{currency} račun", "check-confirmation.approve.footer.for": "<PERSON>a", "checkConfirmation.title": "Rezultat transakcije", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin i Ether", "collateral.ethereum": "Ethereum", "collateral.other": "Ostalo", "collateral.rwa": "<PERSON><PERSON><PERSON>", "collateral.stablecoins": "Stablecoini (vezani uz USD)", "collateral.us-t-bills": "Američke trezorske mjenice", "confirm-bank-transfer-recipient.bullet-1": "Bez naknada za digitalni EUR", "confirm-bank-transfer-recipient.bullet-2": "Uplate na {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Dijeljenje podataka o Gnosis Pay računu s Moneriumom, ovlaštenom i reguliranom EMI institucijom. <link>Saznaj više</link>", "confirm-bank-transfer-recipient.bullet-4": "Prihvati Monerium <link>uvjete usluge</link>", "confirm-bank-transfer-recipient.title": "Prihvati uvjete", "confirm-change-withdrawal-account.cancel": "Odustani", "confirm-change-withdrawal-account.confirm": "Potvrdi", "confirm-change-withdrawal-account.saving": "Spremanje", "confirm-change-withdrawal-account.subtitle": "Sve isplate iz Zeala primit će ovaj bankovni račun.", "confirm-change-withdrawal-account.title": "Promijeni banku za primanje", "confirm-ramove-withdrawal-account.title": "Ukloni bankovni račun", "confirm-remove-withdrawal-account.subtitle": "Podaci o računu bit će uklonjeni iz Zeala. Možeš ih opet dodati bilo kada.", "confirmTransaction.finalNetworkFee": "<PERSON><PERSON><PERSON><PERSON>", "confirmTransaction.importKeys": "Uvezi ključeve", "confirmTransaction.networkFee": "<PERSON><PERSON><PERSON><PERSON>", "confirmation.title": "<PERSON><PERSON><PERSON><PERSON> {amount} primatelju {recipient}", "conflicting-monerium-account.add-owner": "<PERSON><PERSON>j kao vlas<PERSON>a Gnosis Paya", "conflicting-monerium-account.create-wallet": "Stvori novi pametni novčanik", "conflicting-monerium-account.disconnect-card": "Odspoji karticu sa Z<PERSON>a i ponovno je spoji s novim vlasnikom", "conflicting-monerium-account.header": "{wallet} povezan s <PERSON>im <PERSON> računom", "conflicting-monerium-account.subtitle": "Promijeni vlasnički novčanik Gnosis Paya", "connection.diconnected.got_it": "Razumijem!", "connection.diconnected.page1.subtitle": "Zeal radi svugdje gdje radi i MetaMask. Jednostavno se poveži kao i s MetaMaskom.", "connection.diconnected.page1.title": "Kako se povezati sa <PERSON>ealom?", "connection.diconnected.page2.subtitle": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> mnogo opcija. Zeal bi mogao biti jedna od njih. Ako se Zeal ne pojavi...", "connection.diconnected.page2.title": "Klikni Poveži novčanik", "connection.diconnected.page3.subtitle": "Zatražit ćemo povezivanje sa Zealom. Opcije Browser ili Injected bi također trebale raditi. Isprobaj!", "connection.diconnected.page3.title": "Odaberi MetaMask", "connectionSafetyCheck.tag.caution": "<PERSON><PERSON>", "connectionSafetyCheck.tag.danger": "Opasnost", "connectionSafetyCheck.tag.passed": "Us<PERSON>ješno", "connectionSafetyConfirmation.subtitle": "<PERSON>si li siguran da ž<PERSON>š nastaviti?", "connectionSafetyConfirmation.title": "Ova stranica se čini opasnom", "connection_state.connect.cancel": "Odustani", "connection_state.connect.changeToMetamask": "Prebaci na MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Prebaci na MetaMask", "connection_state.connect.connect_button": "Poveži se", "connection_state.connect.expanded.connected": "<PERSON><PERSON><PERSON>", "connection_state.connect.expanded.title": "Poveži se", "connection_state.connect.safetyChecksLoading": "Provjera sigurnosti stranice", "connection_state.connect.safetyChecksLoadingError": "Si<PERSON><PERSON>sne provjere nisu dovršene", "connection_state.connected.expanded.disconnectButton": "<PERSON><PERSON><PERSON><PERSON>", "connection_state.connected.expanded.title": "<PERSON><PERSON><PERSON>", "copied-diagnostics": "Dijagnostika kop<PERSON>na", "copy-diagnostics": "<PERSON><PERSON><PERSON>", "counterparty.component.add_recipient_primary_text": "Dodaj bankovnog primatelja", "counterparty.country": "Država", "counterparty.countryTitle": "Država primatelja", "counterparty.currency": "Valuta", "counterparty.delete.success.title": "Uklonjeno", "counterparty.edit.success.title": "Promjene spremljene", "counterparty.errors.country_required": "Država je obavezna", "counterparty.errors.first_name.invalid": "<PERSON><PERSON> mora biti du<PERSON>e", "counterparty.errors.last_name.invalid": "<PERSON>zime mora biti du<PERSON>e", "counterparty.first_name": "Ime", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Pošalji u banku", "countrySelector.noCountryFound": "<PERSON>je pronađena nijedna država", "countrySelector.title": "Odaberi državu", "create-passkey.cta": "Stvori pristupni ključ", "create-passkey.extension.cta": "<PERSON><PERSON><PERSON>", "create-passkey.footnote": "Pokreće", "create-passkey.mobile.cta": "Poč<PERSON> si<PERSON> post<PERSON>u", "create-passkey.steps.enable-recovery": "Postavi oporavak u oblaku", "create-passkey.steps.setup-biometrics": "Omogući biometrijsku sigurnost", "create-passkey.subtitle": "Pristupni ključevi sigurniji su od lozinki i kriptirani su u pohrani u oblaku za lakši oporavak.", "create-passkey.title": "<PERSON><PERSON><PERSON><PERSON>", "create-smart-wallet": "Kreiraj Smart Wallet", "create-userop.progress.text": "<PERSON><PERSON><PERSON><PERSON>", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Nastavi na Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Idi na Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "Već si započeo narudžbu kartice. Vrati se na Gnosis Pay stranicu da je dovršiš.", "create_recharge_preferences.card": "Kartica", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "Zaradi {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "<PERSON><PERSON><PERSON><PERSON><PERSON> gotovinu", "create_recharge_preferences.link_accounts_title": "Poveži račune", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "Tvoja se kartica NEĆE automatski nadopunjavati nakon svakog plaćanja.", "create_recharge_preferences.not_configured_title": "Zarada i potrošnja", "create_recharge_preferences.recharge_from_earn_accounts_description": "Tvoja se kartica automatski nadopunjuje nakon svakog plaćanja s tvog Earn računa.", "create_recharge_preferences.subtitle": "godišnje", "creating-account.loading": "Stvaranje računa", "creating-gnosis-pay-account": "Kreiranje računa", "currencies.bridge.select_routes.emptyState": "Nismo pronašli rute za ovaj most", "currency.add_currency.add_token": "<PERSON><PERSON><PERSON> token", "currency.add_currency.not_a_valid_address": "Ovo nije važeća adresa tokena", "currency.add_currency.token_decimals_feild": "<PERSON><PERSON><PERSON>a", "currency.add_currency.token_feild": "<PERSON><PERSON><PERSON>a", "currency.add_currency.token_symbol_feild": "Simbol tokena", "currency.add_currency.update_token": "<PERSON><PERSON><PERSON><PERSON>", "currency.add_custom.remove_token.cta": "Ukloni token", "currency.add_custom.remove_token.header": "Ukloni token", "currency.add_custom.remove_token.subtitle": "Tvoj novčanik će i dalje čuvati stanje ovog tokena, ali će biti skriven iz prikaza stanja tvog Zeal portfelja.", "currency.add_custom.token_removed": "To<PERSON> uk<PERSON>n", "currency.add_custom.token_updated": "<PERSON><PERSON>", "currency.balance_label": "Stanje: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "Tvoj bankovni prijenos je uspješno prebacio {fiat} u {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "Primio si {crypto}", "currency.bankTransfer.deposit_status.success": "Primljeno u tvoj novčanik", "currency.bankTransfer.deposit_status.title": "<PERSON><PERSON>", "currency.bankTransfer.off_ramp.check_bank_account": "Provjeri svoj bankovni račun", "currency.bankTransfer.off_ramp.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Slanje u tvoju banku", "currency.bankTransfer.off_ramp.transferring_to_currency": "Prijenos u {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Sredstva su do sada trebala stići na tvoj bankovni račun.", "currency.bankTransfer.withdrawal_status.success": "Poslano u tvoju banku", "currency.bankTransfer.withdrawal_status.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.email": "Adresa e-pošte", "currency.bank_transfer.create_unblock_user.email_invalid": "Nevažeća e-pošta", "currency.bank_transfer.create_unblock_user.email_missing": "Obavezno", "currency.bank_transfer.create_unblock_user.first_name": "Ime", "currency.bank_transfer.create_unblock_user.first_name_missing": "Obavezno", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, raz<PERSON><PERSON> i - . , & ( ) '.", "currency.bank_transfer.create_unblock_user.last_name": "Prezime", "currency.bank_transfer.create_unblock_user.last_name_missing": "Obavezno", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, raz<PERSON><PERSON> i - . , & ( ) '.", "currency.bank_transfer.create_unblock_user.note": "Nastavkom prihvać<PERSON> (naš bankarski partner) <terms>Uvjete</terms> i <policy>Pravila privatnosti</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Upiši svoje ime točno kao na bankovnom računu", "currency.bank_transfer.create_unblock_user.title": "Poveži svoj bankovni račun", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Broj računa", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Država banke", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Preferirana valuta", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Sort kod", "currency.bank_transfer.create_unblock_withdraw_account.success": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.title": "Poveži svoj bankovni račun", "currency.bank_transfer.residence-form.address-required": "Obavezno", "currency.bank_transfer.residence-form.address-unsupported-char": "<PERSON><PERSON>, bro<PERSON><PERSON>, r<PERSON><PERSON><PERSON> i , ; {apostrophe} - \\\\ su dopušteni.", "currency.bank_transfer.residence-form.city-required": "Obavezno", "currency.bank_transfer.residence-form.city-unsupported-char": "<PERSON><PERSON>, br<PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON> i . , - & ( ) {apostrophe} su dopušteni.", "currency.bank_transfer.residence-form.postcode-invalid": "Nevažeći poštanski broj", "currency.bank_transfer.residence-form.postcode-required": "Obavezno", "currency.bank_transfer.validation.invalid.account_number": "Nevažeći broj računa", "currency.bank_transfer.validation.invalid.iban": "Nevažeći IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Nevažeći sort kod", "currency.bridge.amount_label": "Iznos za most", "currency.bridge.best_returns.subtitle": "Ovaj pružatelj usluge mosta dat će ti najveći iznos, uključujući sve naknade.", "currency.bridge.best_returns_popup.title": "Najbolji povrati", "currency.bridge.bridge_from": "S", "currency.bridge.bridge_gas_fee_loading_failed": "Imali smo <PERSON>a s učitavanjem mrežne naknade", "currency.bridge.bridge_low_slippage": "Vrlo nisko proklizavanje. Pokušaj ga povećati", "currency.bridge.bridge_provider": "Pružatelj usluge prijenosa", "currency.bridge.bridge_provider_loading_failed": "Imali smo problema s učitavanjem pružatelja usluga", "currency.bridge.bridge_settings": "Postavke mosta", "currency.bridge.bridge_status.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> {name}", "currency.bridge.bridge_status.title": "Most", "currency.bridge.bridge_to": "Na", "currency.bridge.fastest_route_popup.subtitle": "Ovaj pružatelj usluge mosta pružit će ti najbržu transakcijsku rutu.", "currency.bridge.fastest_route_popup.title": "Najbrža ruta", "currency.bridge.from": "Od", "currency.bridge.success": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bridge.title": "Most", "currency.bridge.to": "Na", "currency.bridge.topup": "<PERSON><PERSON><PERSON><PERSON><PERSON> {symbol}", "currency.bridge.withdrawal_status.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.card.card_top_up_status.title": "Dodaj s<PERSON>stva na karticu", "currency.destination_amount": "Odredišni iznos", "currency.hide_currency.confirm.subtitle": "Sakrij ovaj token iz svog portfelja. Uvijek ga možeš ponovno prikazati.", "currency.hide_currency.confirm.title": "<PERSON><PERSON><PERSON><PERSON> token", "currency.hide_currency.success.title": "Token skriven", "currency.label": "Oznaka (neobavezno)", "currency.last_name": "Prezime", "currency.max_loading": "Maks.:", "currency.swap.amount_to_swap": "Iznos za zamjenu", "currency.swap.best_return": "Ruta s najboljim povratom", "currency.swap.destination_amount": "Odredišni iznos", "currency.swap.header": "<PERSON><PERSON><PERSON><PERSON>", "currency.swap.max_label": "Stanje: {amount}", "currency.swap.provider.header": "Pružatelj zamjene", "currency.swap.select_to_token": "Odaberi token", "currency.swap.swap_gas_fee_loading_failed": "Imali smo <PERSON>a s učitavanjem mrežne naknade", "currency.swap.swap_provider_loading_failed": "Imali smo problema s učitavanjem pružatelja usluga", "currency.swap.swap_settings": "Postavke zamjene", "currency.swap.swap_slippage_too_low": "Proklizavanje je prenisko. Pokušaj ga povećati.", "currency.swaps_io_native_token_swap.subtitle": "Koristeći Swaps.IO", "currency.swaps_io_native_token_swap.title": "Pošalji", "currency.withdrawal.amount_from": "Od", "currency.withdrawal.amount_to": "<PERSON>a", "currencySelector.title": "<PERSON><PERSON><PERSON><PERSON> valutu", "dApp.wallet-does-not-support-chain.subtitle": "Čini se da tvoj novčanik ne podržava {network}. Poveži drugi novčanik ili koristi Zeal.", "dApp.wallet-does-not-support-chain.title": "Nepodržana m<PERSON>a", "dapp.connection.manage.confirm.disconnect.all.cta": "Odspoji sve", "dapp.connection.manage.confirm.disconnect.all.subtitle": "<PERSON>si li siguran da ž<PERSON>š odspojiti sve veze?", "dapp.connection.manage.confirm.disconnect.all.title": "Odspoji sve", "dapp.connection.manage.connection_list.main.button.title": "Odspoji", "dapp.connection.manage.connection_list.no_connections": "Nemaš povezanih aplikacija", "dapp.connection.manage.connection_list.section.button.title": "Odspoji sve", "dapp.connection.manage.connection_list.section.title": "Aktivne", "dapp.connection.manage.connection_list.title": "Veze", "dapp.connection.manage.disconnect.success.title": "Aplikacije odspojene", "dapp.metamask_mode.title": "MetaMask način rada", "dc25-card-marketing-card.center.subtitle": "Povrat novca", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "<PERSON><PERSON>", "dc25-card-marketing-card.right.subtitle": "100 ljudi", "dc25-card-marketing-card.title": "Prvih 100 koji potroše 50 € dobivaju {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Ovu radnju nećeš moći izvršiti 3 minute. <PERSON><PERSON>, obrada promjena postavki kartice ili isplata traje 3 minute.", "delayQueueBusyBanner.processing-yout-action.title": "Obrađujem tvoju radnju, molim pričekaj", "delayQueueBusyWidget.cardFrozen": "Kartica zamrznuta", "delayQueueBusyWidget.processingAction": "Obrada tvog zahtjeva", "delayQueueFailedBanner.action-incomplete.get-support": "Zatraži podršku", "delayQueueFailedBanner.action-incomplete.subtitle": "<PERSON><PERSON><PERSON><PERSON>, do<PERSON><PERSON> je do pogreške prilikom isplate ili ažuriranja postavki. Obrati se podršci na Discordu.", "delayQueueFailedBanner.action-incomplete.title": "Radnja nije dovršena", "delayQueueFailedWidget.actionIncomplete.title": "Radnja na kartici nije dovršena", "delayQueueFailedWidget.cardFrozen.subtitle": "Kartica zamrznuta", "delayQueueFailedWidget.contactSupport": "Kontaktiraj podršku", "delay_queue_busy.subtitle": "<PERSON><PERSON>, obrada promjena postavki kartice ili isplata traje 3 minute. <PERSON>a to vrijeme kartica je zamrznuta.", "delay_queue_busy.title": "Tvoja radnja se obrađuje", "delay_queue_failed.contact_support": "<PERSON><PERSON><PERSON>", "delay_queue_failed.subtitle": "<PERSON><PERSON><PERSON><PERSON>, do<PERSON><PERSON> je do pogreške prilikom isplate ili ažuriranja postavki. Obrati se podršci na Discordu.", "delay_queue_failed.title": "Obrati se podršci", "deploy-earn-form-smart-wallet.in-progress.title": "<PERSON><PERSON><PERSON><PERSON>", "deposit": "<PERSON><PERSON>", "disconnect-card-popup.cancel": "Odustani", "disconnect-card-popup.disconnect": "Odspoji", "disconnect-card-popup.subtitle": "Ovo će ukloniti tvoju karticu iz aplikacije Zeal. Tvoj novčanik ostat će povezan s karticom u aplikaciji Gnosis Pay. Karticu možeš ponovno povezati bilo kada.", "disconnect-card-popup.title": "Odspoji karticu", "distance.long.days": "{count} dana", "distance.long.hours": "{count} sati", "distance.long.minutes": "{count} minuta", "distance.long.months": "{count} m<PERSON><PERSON><PERSON>", "distance.long.seconds": "{count} sekundi", "distance.long.years": "{count} godina", "distance.short.days": "{count} d", "distance.short.hours": "{count} h", "distance.short.minutes": "{count} min", "distance.short.months": "{count} mj", "distance.short.seconds": "{count} s", "distance.short.years": "{count} g", "duration.short.days": "{count}d", "duration.short.hours": "{count}h", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "<PERSON><PERSON>", "earn-deposit-view.into": "U", "earn-deposit-view.to": "Na", "earn-deposit.swap.transfer-provider": "Pružatelj prijenosa", "earn-taker-investment-details.accrued-realtime": "Obračunato u stvarnom vremenu", "earn-taker-investment-details.asset-class": "<PERSON><PERSON><PERSON> imovine", "earn-taker-investment-details.asset-coverage-ratio": "<PERSON><PERSON><PERSON> p<PERSON>a imovine", "earn-taker-investment-details.asset-reserve": "Rezerva imovine", "earn-taker-investment-details.base_currency.label": "Osnovna valuta", "earn-taker-investment-details.chf.description": "Zaradi kamate na svoj CHF polaganjem zCHF-a na Frankencoin - pouzdano digitalno tržište novca. Kamate se generiraju iz niskorizičnih, prekomjerno kolateraliziranih zajmova na Frankencoinu i isplaćuju u stvarnom vremenu. Tvoja sredstva su na sigurnom na podračunu koji samo ti kontroliraš.", "earn-taker-investment-details.chf.description.with_address_link": "Zaradi kamate na svoj CHF polaganjem zCHF-a na Frankencoin - pouzdano digitalno tržište novca. Kamate se generiraju iz niskorizičnih, prekomjerno kolateraliziranih zajmova na Frankencoinu i isplaćuju u stvarnom vremenu. Tvoja sredstva su na sigurnom na podračunu <link>(kopiraj 0x)</link> koji samo ti kontroliraš.", "earn-taker-investment-details.chf.label": "<PERSON>ni <PERSON> franak", "earn-taker-investment-details.collateral-composition": "Sastav kolaterala", "earn-taker-investment-details.depositor-obligations": "Obveze prema deponentima", "earn-taker-investment-details.eure.description": "Zaradi kamatu na svoje eure polaganjem EURe u Aave - pouzdano digitalno tržište novca. EURe je potpuno reguliran euro stablecoin koji izdaje Monerium s pokrićem 1:1 na zaštićenim računima. Kamata se generira iz niskorizičnih, prekomjerno kolateraliziranih zajmova na Aaveu i isplaćuje se u stvarnom vremenu. Tvoja sredstva ostaju na sigurnom podračunu koji samo ti kontroliraš.", "earn-taker-investment-details.eure.description.with_address_link": "Zaradi kamatu na svoje eure polaganjem EURe u Aave - pouzdano digitalno tržište novca. EURe je potpuno reguliran euro stablecoin koji izdaje Monerium s pokrićem 1:1 na zaštićenim računima. Kamata se generira iz niskorizičnih, prekomjerno kolateraliziranih zajmova na Aaveu i isplaćuje se u stvarnom vremenu. Tvoja sredstva ostaju na sigurnom podračunu <link>(kopiraj 0x)</link> koji samo ti kontroliraš.", "earn-taker-investment-details.eure.label": "Digitalni euro (EURe)", "earn-taker-investment-details.faq": "Česta pitanja", "earn-taker-investment-details.fixed-income": "Fiksni prihod", "earn-taker-investment-details.issuer": "Izdavatelj", "earn-taker-investment-details.key-facts": "Ključne činjenice", "earn-taker-investment-details.liquidity": "Likvidnost", "earn-taker-investment-details.operator": "Tržišni operater", "earn-taker-investment-details.projected-yield": "Projicirani godišnji prinos", "earn-taker-investment-details.see-other-faq": "Pogledaj sva ostala česta pitanja", "earn-taker-investment-details.see-realtime": "Pogledaj podatke u stvarnom vremenu", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Prinos u zadnjih 12 mjeseci", "earn-taker-investment-details.total-collateral": "Ukupni kolateral", "earn-taker-investment-details.total-deposits": "27.253.300.208 $", "earn-taker-investment-details.total-zchf-supply": "Ukupna ponuda ZCHF-a", "earn-taker-investment-details.total_deposits": "Ukupni polozi u Aave", "earn-taker-investment-details.usd.description": "Sky je digitalno tržište novca koje nudi stabilne prinose denominirane u američkim dolarima od kratkoročnih američkih trezorskih zapisa i prekomjerno kolateraliziranih zajmova — bez kripto volatilnosti, s pristupom sredstvima 24/7 i transparentnim pokrićem na lancu.", "earn-taker-investment-details.usd.description.with_address_link": "Sky je digitalno tržište novca koje nudi stabilne prinose denominirane u američkim dolarima od kratkoročnih američkih trezorskih zapisa i prekomjerno kolateraliziranih zajmova — bez kripto volatilnosti, s pristupom sredstvima 24/7 i transparentnim pokrićem na lancu. Ulaganja su na podračunu <link>(kopiraj 0x)</link> koji ti kontroliraš.", "earn-taker-investment-details.usd.ftx-difference": "<PERSON>ko se ovo razlikuje od FTX-a, <PERSON><PERSON><PERSON><PERSON>, BlockFi-ja ili <PERSON>?", "earn-taker-investment-details.usd.high-returns": "<PERSON><PERSON> prinosi mogu biti tako visoki, posebno u usporedbi s tradicionalnim bankama?", "earn-taker-investment-details.usd.how-is-backed": "Kako <PERSON> ima pokriće i što se događa s mojim novcem ako Zeal bankrotira?", "earn-taker-investment-details.usd.income-sources": "Izvori prihoda 2024.", "earn-taker-investment-details.usd.insurance": "Jesu li moja sredstva osigurana ili zajamčena od strane bilo kojeg subjekta (poput FDIC-a ili sličnog)?", "earn-taker-investment-details.usd.label": "Digitalni američki dolar", "earn-taker-investment-details.usd.lose-principal": "Mogu li realno izgubiti svoju glavnicu i pod kojim okolnostima?", "earn-taker-investment-details.variable-rate": "Pozajmljivanje s promjenjivom stopom", "earn-taker-investment-details.withdraw-anytime": "Isplata u bilo kojem trenutku", "earn-taker-investment-details.yield": "<PERSON><PERSON><PERSON>", "earn-withdrawal-view.approve.for": "<PERSON>a", "earn-withdrawal-view.approve.into": "U", "earn-withdrawal-view.swap.into": "U", "earn-withdrawal-view.withdraw.to": "<PERSON>a", "earn.add_another_asset.title": "Odaberi imovinu za zaradu", "earn.add_asset": "<PERSON><PERSON><PERSON>", "earn.asset_view.title": "Z<PERSON><PERSON>", "earn.base-currency-popup.text": "Osnovna valuta je način na koji se vrednuju i bilježe tvoji polozi, prinosi i transakcije. Ako položiš sredstva u drugoj valuti (kao što je EUR u USD), tvoja se sredstva odmah pretvaraju u osnovnu valutu po trenutnim tečajevima. Nakon konverzije, tvoje stanje ostaje stabilno u osnovnoj valuti, ali budu<PERSON>e isplate mogu ponovno uključivati konverzije valuta.", "earn.base-currency-popup.title": "Osnovna valuta", "earn.card-recharge.disabled.list-item.title": "Automatska nadoplata isključena", "earn.card-recharge.enabled.list-item.title": "Automatska nadoplata uključena", "earn.choose_wallet_to_deposit.title": "Položi iz", "earn.config.currency.eth": "Zaradi Ethereum", "earn.config.currency.on_chain_address_subtitle": "Adresa na lancu", "earn.config.currency.us_dollars": "Postavi bankovne prijenose", "earn.configured_widget.current_apy.title": "Trenutni APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} godišnje", "earn.confirm.currency.cta": "<PERSON><PERSON><PERSON>", "earn.currency.eth": "Zaradi Ethereum", "earn.deploy.status.title": "Stvori Earn ra<PERSON>", "earn.deploy.status.title_with_taker": "<PERSON><PERSON><PERSON><PERSON> {title} <PERSON><PERSON><PERSON> r<PERSON>", "earn.deposit": "<PERSON><PERSON>", "earn.deposit.amount_to_deposit": "Iznos za polog", "earn.deposit.deposit": "<PERSON><PERSON>", "earn.deposit.enter_amount": "Unesi iznos", "earn.deposit.no_routes_found": "<PERSON><PERSON> prona<PERSON><PERSON> ruta", "earn.deposit.not_enough_balance": "Nedovoljno sredstava", "earn.deposit.select-currency.title": "Odaberi token za polog", "earn.deposit.select_account.title": "Odaberi Earn ra<PERSON>", "earn.desposit_form.title": "Polog u Zaradu", "earn.earn_deposit.status.title": "Polog u Earn", "earn.earn_deposit.trx.title": "Polog na Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "Isplati sredstva bilo kada", "earn.earn_withdraw.status.title": "Isplata s Earn računa", "earn.earn_withdraw.trx.title.approval": "<PERSON><PERSON><PERSON> is<PERSON>", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Isplati u {asset}", "earn.earn_withdraw.trx.title.withdrawal": "<PERSON><PERSON><PERSON><PERSON> s <PERSON>n", "earn.recharge.cta": "S<PERSON><PERSON>i promje<PERSON>", "earn.recharge.earn_not_configured.enable_some_account.error": "Omogući račun", "earn.recharge.earn_not_configured.enter_amount.error": "Unesi iznos", "earn.recharge.select_taker.header": "Nadoplati karticu redom iz", "earn.recharge_card_tag.on": "uklj.", "earn.recharge_card_tag.recharge": "Nadoplat<PERSON>", "earn.recharge_card_tag.recharge_not_configured": "Automatska nadoplata", "earn.recharge_card_tag.recharge_off": "Nadoplata isključena", "earn.recharge_card_tag.recharged": "Nadopunjeno", "earn.recharge_card_tag.recharging": "<PERSON><PERSON><PERSON><PERSON><PERSON> se", "earn.recharge_configured.disable.trx.title": "Onemogući automatsku nadoplatu", "earn.recharge_configured.trx.disclaimer": "<PERSON><PERSON> koris<PERSON>, p<PERSON><PERSON><PERSON><PERSON> se Cowswap aukcija za kupnju istog iznosa kao tvoje plaćanje, koristeći tvoja Earn sredstva. Ovaj proces ti obično osigurava najbolji tržišni tečaj, ali onchain tečaj se može razlikovati od stvarnih tečajeva.", "earn.recharge_configured.trx.subtitle": "Nakon svakog plaćanja, gotovina će se automatski dodati s tvojih Earn računa kako bi stanje tvoje kartice ostalo na {value}", "earn.recharge_configured.trx.title": "Postavi automatsku nadoplatu na {value}", "earn.recharge_configured.updated.trx.title": "Spremi postavke nadoplate", "earn.risk-banner.subtitle": "Privatni proizvod bez zaštite od gubitka.", "earn.risk-banner.title": "Upoznaj se s rizicima", "earn.set_recharge.status.title": "Postavi automatsku nadoplatu", "earn.setup_reacharge.input.disable.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.setup_reacharge.input.label": "Ciljano stanje kartice", "earn.setup_reacharge_form.title": "Automatska nadoplata održava tvoju {br} karticu na istom stanju", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Drži wstETH (Staked ETH) na Gnosis Chainu i posuđuj putem Lida.", "earn.taker-bulletlist.point_1": "Zaradi {apyValue} godišnje. Povrati ovise o tr<PERSON><PERSON>š<PERSON>.", "earn.taker-bulletlist.point_3": "Zeal ne naplaćuje naknade.", "earn.taker-historical-returns": "Povijesni povrati", "earn.taker-historical-returns.chf": "Rast CHF u odnosu na USD", "earn.taker-investment-tile.apy.perYear": "godišnje", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "<PERSON><PERSON><PERSON>", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Zarada putem Aavea na Gnosis Chainu", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Zarada putem Frankencoina na Gnosis Chainu", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Zarada putem Lida na Gnosis Chainu", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Zarada putem MakerDAO-a na Gnosis Chainu", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "Eth zarada", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH zarada", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "Američke državne obveznice (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Iako izni<PERSON> rijetko, teoretski je moguće. Tvoja su sredstva zaštićena strogim upravljanjem rizicima i visokom kolateralizacijom. Realan najgori scenarij uključivao bi tržišne uvjete bez presedana, kao što je istovremeni gubitak vezanosti više stablecoina za valutu — nešto što se nikada prije nije dogodilo.", "earn.usd.can-I-lose-my-principal-popup.title": "Mogu li realno izgubiti svoju glavnicu i pod kojim okolnostima?", "earn.usd.ftx-difference-popup.text": "Sky je fundamentalno drugačiji. Za razliku od FTX-a, <PERSON><PERSON><PERSON>a, BlockFi-ja ili <PERSON> — koji su se uvelike oslanjali na centralizirano skrbništvo, netransparentno upravljanje imovinom i rizične pozicije s polugom — Sky USD koristi transparentne, revidirane, decentralizirane pametne ugovore i održava potpunu transparentnost na lancu. Zadržavaš potpunu kontrolu nad svojim privatnim novčanikom, značajno smanjujući rizike druge ugovorne strane povezane s centraliziranim neuspjesima.", "earn.usd.ftx-difference-popup.title": "<PERSON>ko se ovo razlikuje od FTX-a, <PERSON><PERSON><PERSON><PERSON>, BlockFi-ja ili <PERSON>?", "earn.usd.high-returns-popup.text": "Sky USD generira prinose prvenstveno putem protokola decentraliziranih financija (DeFi), koji automatiziraju P2P pozajmljivanje i osiguravanje likvidnosti, eliminirajući tradicionalne bankarske troškove i posrednike. Te učinkovitosti, u kombinaciji s robusnim kontrolama rizika, omogućuju značajno veće prinose u usporedbi s tradicionalnim bankama.", "earn.usd.high-returns-popup.title": "<PERSON><PERSON> prinosi mogu biti tako visoki, posebno u usporedbi s tradicionalnim bankama?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD ima puno pokriće i prekomjerno je kolateraliziran kombinacijom digitalne imovine koja se drži u sigurnim pametnim ugovorima i imovine iz stvarnog svijeta poput američkih trezorskih zapisa. Rezerve se mogu revidirati u stvarnom vremenu na lancu, čak i unutar Zeala, pružajući transparentnost i sigurnost. U malo vjerojatnom slučaju da Zeal prestane s radom, tvoja imovina ostaje osigurana na lancu, u potpunosti pod tvojom kontrolom i dostupna putem drugih kompatibilnih novčanika.", "earn.usd.how-is-sky-backed-popup.title": "Kako <PERSON> ima pokriće i što se događa s mojim novcem ako Zeal bankrotira?", "earn.usd.insurance-popup.text": "Sredstva u Sky USD-u nisu osigurana od strane FDIC-a niti imaju tradicionalna državna jamstva jer se radi o računu temeljenom na digitalnoj imovini, a ne o konvencionalnom bankovnom računu. <PERSON><PERSON><PERSON>, Sky upravlja svim mjerama za smanjenje rizika putem revidiranih pametnih ugovora i pažljivo provjerenih DeFi protokola, osiguravajući da imovina ostane sigurna i transparentna.", "earn.usd.insurance-popup.title": "Jesu li moja sredstva osigurana ili zajamčena od strane bilo kojeg subjekta (poput FDIC-a ili sličnog)?", "earn.usd.lending-operations-popup.text": "Sky USD generira prinos pozajmljivanjem stablecoina putem decentraliziranih tržišta za pozajmljivanje kao što su Morpho i Spark. Tvoji stablecoini se pozajmljuju zajmoprimcima koji polažu znatno više kolaterala — poput ETH-a ili BTC-a — od vrijednosti svog zajma. <PERSON><PERSON><PERSON>rist<PERSON>, nazvan prekomjerna kolateralizacija, osigurava da uvijek postoji dovoljno kolaterala za pokriće zajmova, što uvelike smanjuje rizik. Prikupljene kamate i povremene likvidacijske naknade koje plaćaju zajmoprimci pružaju pouzdane, transparentne i sigurne prinose.", "earn.usd.lending-operations-popup.title": "Poslovi pozajmljivanja", "earn.usd.market-making-operations-popup.text": "Sky USD ostvaruje dodatni prinos sudjelovanjem na decentraliziranim mjenjačnicama (AMM) kao što su Curve ili Uniswap. Pružanjem likvidnosti — polaganjem tvojih stablecoina u bazene koji olakšavaju trgovanje kriptovalutama — Sky USD prikuplja naknade generirane iz trgovina. Ovi bazeni likvidnosti pažljivo su odabrani kako bi se minimizirala volatilnost, prvenstveno koristeći parove stablecoina za stablecoine kako bi se značajno smanjili rizici poput nestalnog gubitka, čime tvoja imovina ostaje sigurna i dostupna.", "earn.usd.market-making-operations-popup.title": "Poslovi održavanja tržišta", "earn.usd.treasury-operations-popup.text": "Sky USD generira stabilan, dosljedan prinos kroz strateška trezorska ulaganja. Dio tvojih stablecoin pologa alocira se u sigurnu, niskorizičnu imovinu iz stvarnog svijeta — prvenstveno kratkoročne državne obveznice i visoko sigurne kreditne instrumente. <PERSON><PERSON><PERSON>, sličan tradicionalnom bankarstvu, osigurava predvidljiv i pouzdan prinos. Tvoja imovina ostaje sigurna, likvidna i transparentno upravljana.", "earn.usd.treasury-operations-popup.title": "Trezorske operacije", "earn.view_earn.card_rechard_off": "Isklj.", "earn.view_earn.card_rechard_on": "Uklj.", "earn.view_earn.card_recharge": "Nadoplata kartice", "earn.view_earn.total_balance_label": "Zarada {percentage} godišnje", "earn.view_earn.total_earnings_label": "Ukupna zarada", "earn.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "earn.withdraw.amount_to_withdraw": "Iznos za isplatu", "earn.withdraw.enter_amount": "Unesi iznos", "earn.withdraw.loading": "Učitavanje", "earn.withdraw.no_routes_found": "<PERSON><PERSON> ruta", "earn.withdraw.not_enough_balance": "Nedovoljno sredstava", "earn.withdraw.select-currency.title": "Odaberi token", "earn.withdraw.select_to_token": "Odaberi token", "earn.withdraw.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "earn.withdraw_form.title": "Isplata s Earn računa", "earnings-view.earnings": "Ukupna zarada", "edit-account-owners.add-owner.add-wallet": "<PERSON><PERSON>j v<PERSON>a", "edit-account-owners.add-owner.add_wallet": "<PERSON><PERSON>j v<PERSON>a", "edit-account-owners.add-owner.title": "Dodaj vlasnika kartice", "edit-account-owners.card-owners": "Vlasnici kartice", "edit-account-owners.external-wallet": "Vanjski novčanik", "editBankRecipient.title": "<PERSON><PERSON><PERSON> prima<PERSON>", "editNetwork.addCustomRPC": "Dodaj prilagođeni RPC čvor", "editNetwork.cannot_verify.subtitle": "Prilagođeni RPC čvor ne odgovara ispravno. Provjeri URL i pokušaj ponovno.", "editNetwork.cannot_verify.title": "Ne možemo potvrditi RPC čvor", "editNetwork.cannot_verify.try_again": "<PERSON><PERSON><PERSON>", "editNetwork.customRPCNode": "Prilagođeni RPC čvor", "editNetwork.defaultRPC": "Zadani RPC", "editNetwork.networkRPC": "Mrežni RPC", "editNetwork.rpc_url.cannot_be_empty": "Obavezno", "editNetwork.rpc_url.not_a_valid_https_url": "Mora biti važeći HTTP(S) URL", "editNetwork.safetyWarning.subtitle": "Oprez: prilagođeni RPC. Nastaviti?", "editNetwork.safetyWarning.title": "Prilagođeni RPC-ovi mogu biti nesigurni", "editNetwork.zealRPCNode": "Zeal RPC čvor", "editNetworkRpc.headerTitle": "Prilagođeni RPC čvor", "editNetworkRpc.rpcNodeUrl": "URL RPC čvora", "editing-locked.modal.description": "Za razliku od transakcija odobrenja, dozvole ne dopuštaju uređivanje limita potrošnje ili vremena isteka. Provjeri vjeruješ li dAppu prije slanja dozvole.", "editing-locked.modal.title": "Uređivanje zakl<PERSON>", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Omogućavanje nadoplate", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Nadoplat<PERSON>", "enterCardnumber": "Unesi broj kartice", "error.connectivity_error.subtitle": "Provjeri vezu i pokušaj ponovno.", "error.connectivity_error.title": "Nema internetske veze", "error.decrypt_incorrect_password.title": "Netočna lozin<PERSON>", "error.encrypted_object_invalid_format.title": "Oštećeni podaci", "error.failed_to_fetch_google_auth_token.title": "Nismo mogli dobiti pristup", "error.list.item.cta.action": "Pokušaj ponovno", "error.trezor_action_cancelled.title": "Transakcija odbijena", "error.trezor_device_used_elsewhere.title": "Uređ<PERSON> se koristi u drugoj sesiji", "error.trezor_method_cancelled.title": "<PERSON><PERSON> m<PERSON>e sinkronizirati Trezor", "error.trezor_permissions_not_granted.title": "<PERSON><PERSON> m<PERSON>e sinkronizirati Trezor", "error.trezor_pin_cancelled.title": "<PERSON><PERSON> m<PERSON>e sinkronizirati Trezor", "error.trezor_popup_closed.title": "<PERSON><PERSON> m<PERSON>e sinkronizirati Trezor", "error.unblock_account_number_and_sort_code_mismatch": "Broj računa i 'sort code' nisu usklađ<PERSON>", "error.unblock_can_not_change_details_after_kyc": "<PERSON><PERSON> mogu<PERSON>e mijenjati podatke nakon KYC-a", "error.unblock_hard_kyc_failure": "Neočekivano KYC stanje", "error.unblock_invalid_faster_payment_configuration.title": "Ova banka ne podržava Brza plaćanja", "error.unblock_invalid_iban": "Nevažeći IBAN", "error.unblock_session_expired.title": "Unblock sesija je istekla", "error.unblock_user_with_address_already_exists.title": "Ra<PERSON><PERSON> je već postavljen za ovu adresu", "error.unblock_user_with_such_email_already_exists.title": "Koris<PERSON> s tom e-adresom već postoji", "error.unknown_error.error_message": "<PERSON><PERSON><PERSON>: ", "error.unknown_error.subtitle": "<PERSON><PERSON> <PERSON>, javi se <PERSON> s podacima.", "error.unknown_error.title": "Sistemska pogreška", "eth-cost-warning-modal.subtitle": "Pametni novčanici rade na Ethereumu, ali naknade su vrlo visoke. Preporučujemo druge mreže.", "eth-cost-warning-modal.title": "Visoke mrežne naknade na Ethereumu", "exchange.form.button.chain_unsupported": "Mreža nije podržana", "exchange.form.button.refreshing": "Osvježavanje", "exchange.form.error.asset_not_supported.button": "Oda<PERSON>i drugu imovinu", "exchange.form.error.asset_not_supported.description": "Most ne podržava premošćivanje ove imovine.", "exchange.form.error.asset_not_supported.title": "Imovina nije podržana", "exchange.form.error.bridge_quote_timeout.button": "Oda<PERSON>i drugu imovinu", "exchange.form.error.bridge_quote_timeout.description": "<PERSON><PERSON><PERSON><PERSON> s drugim parom tokena", "exchange.form.error.bridge_quote_timeout.title": "<PERSON><PERSON> pro<PERSON> z<PERSON>", "exchange.form.error.different_receiver_not_supported.button": "Ukloni alternativnog primatelja", "exchange.form.error.different_receiver_not_supported.description": "Ova mjenjačnica ne podržava slanje na drugu adresu.", "exchange.form.error.different_receiver_not_supported.title": "Adresa za slanje i primanje mora biti ista", "exchange.form.error.insufficient_input_amount.button": "Povećaj iznos", "exchange.form.error.insufficient_liquidity.button": "Smanji <PERSON>", "exchange.form.error.insufficient_liquidity.description": "Most nema dovoljno sredstava. Pokušaj s manjim iznosom.", "exchange.form.error.insufficient_liquidity.title": "<PERSON>znos je previsok", "exchange.form.error.max_amount_exceeded.button": "Smanji <PERSON>", "exchange.form.error.max_amount_exceeded.description": "Maksimalni iznos je prekoračen.", "exchange.form.error.max_amount_exceeded.title": "Prevelik iznos", "exchange.form.error.min_amount_not_met.button": "Povećaj iznos", "exchange.form.error.min_amount_not_met.description": "Minimalni iznos zamjene za ovaj token nije dosegnut.", "exchange.form.error.min_amount_not_met.description_with_amount": "Minimalni iznos zamjene je {amount}.", "exchange.form.error.min_amount_not_met.title": "Premalen iznos", "exchange.form.error.min_amount_not_met.title_increase": "Povećaj iznos", "exchange.form.error.no_routes_found.button": "Oda<PERSON>i drugu imovinu", "exchange.form.error.no_routes_found.description": "<PERSON>ema dostupne rute za zamjenu za ovu kombinaciju tokena/mrež<PERSON>.", "exchange.form.error.no_routes_found.title": "<PERSON><PERSON>", "exchange.form.error.not_enough_balance.button": "Smanji <PERSON>", "exchange.form.error.not_enough_balance.description": "<PERSON><PERSON>š do<PERSON>l<PERSON>o ove imovine za transakciju.", "exchange.form.error.not_enough_balance.title": "Nedovoljno stanje", "exchange.form.error.slippage_passed_is_too_low.button": "Poveć<PERSON> slippage", "exchange.form.error.slippage_passed_is_too_low.description": "Dopušteno proklizavanje je prenisko za ovu imovinu.", "exchange.form.error.slippage_passed_is_too_low.title": "Proklizavanje je prenisko", "exchange.form.error.socket_internal_error.button": "Pokušaj ponovno kasnije", "exchange.form.error.socket_internal_error.description": "Partner za most ima poteškoća. Pokušaj ponovno kasnije.", "exchange.form.error.socket_internal_error.title": "Greška kod partnera za most", "exchange.form.error.stargatev2_requires_fee_in_native": "<PERSON><PERSON><PERSON> {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Dodaj {amount} za dovršetak transakcije", "exchange.form.error.stargatev2_requires_fee_in_native.title": "Potre<PERSON><PERSON> je više {symbol}", "expiration-info.modal.description": "Vrijeme isteka je razdoblje u kojem aplikacija može koristiti tvoje tokene. Kada vrijeme iste<PERSON>, gube pristup dok ne odobriš drugačije. <PERSON><PERSON>, neka vrijeme isteka bude kratko.", "expiration-info.modal.title": "<PERSON>to je vrijeme isteka?", "expiration-time.high.modal.text": "Vrijeme isteka trebalo bi biti kratko i ovisiti o tome koliko ti je stvarno potrebno. Duga razdoblja su rizična jer daju prevarantima više prilika za zloupotrebu tvojih tokena.", "expiration-time.high.modal.title": "Dugo vrijeme isteka", "failed.transaction.content": "Transakcija će vjerojatno biti neuspješna", "fee.unknown": "Nepoznato", "feedback-request.leave-message": "O<PERSON>vi poruku", "feedback-request.not-now": "<PERSON>e sada", "feedback-request.title": "Hvala! Kako možemo poboljšati Zeal?", "float.input.period": "Decimalni separator", "gnosis-activate-card.info-popup.subtitle": "Za prvu transakciju moraš umetnuti karticu i unijeti PIN. Nakon toga će beskontaktno plaćanje raditi.", "gnosis-activate-card.info-popup.title": "Prvo plaćanje zahtijeva čip i PIN", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "Unesi broj svoje kartice da je aktiviraš.", "gnosis-activate-card.title": "Broj kartice", "gnosis-pay-re-kyc-widget.btn-text": "Verificiraj", "gnosis-pay-re-kyc-widget.title.not-started": "Verificiraj svoj identitet", "gnosis-pay.login.cta": "Poveži postojeći račun", "gnosis-pay.login.title": "Već imaš <PERSON> Pay račun", "gnosis-signup.confirm.subtitle": "Potraži e-mail od Gnosis Paya, možda se skriva u neželjenoj pošti.", "gnosis-signup.confirm.title": "<PERSON>je stigao e-mail za verifikaciju?", "gnosis-signup.continue": "<PERSON><PERSON><PERSON>", "gnosis-signup.dont_link_accounts": "Ne povezuj račune", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "<PERSON>esi <EMAIL>", "gnosis-signup.enter-email.title": "Unesi e-mail", "gnosis-signup.title": "Pročitao sam i slažem se s Gnosis Pay <linkGnosisTNC>Uvjetima korištenja</linkGnosisTNC> <monovateTerms>Uvjetima za vlasnike kartica</monovateTerms> i <linkMonerium>Moneriumovim Uvjetima korištenja</linkMonerium>.", "gnosis-signup.verify-email.title": "Verificiraj e-mail", "gnosis.confirm.subtitle": "<PERSON>je stigao kod? Provjeri je li broj telefona točan", "gnosis.confirm.title": "<PERSON><PERSON>d poslan na {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Potvrdi", "gnosisPayAccountStatus.success.title": "Kart<PERSON>", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay nije dostupan u tvojoj zemlji", "gnosisPayNoActiveCardsFound.title": "<PERSON>ema aktiv<PERSON> kartica", "gnosis_pay_card_delay_relay_not_empty_error.title": "Tvoju transakciju trenutno nije moguće obraditi. Pokušaj ponovno kasnije", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "Kartica nije moguća", "gnosiskyc.modal.approved.activate-free-card": "<PERSON><PERSON><PERSON><PERSON><PERSON> gratis karticu", "gnosiskyc.modal.approved.button-text": "Uplati s bankovnog računa", "gnosiskyc.modal.approved.title": "T<PERSON>j osobni račun je stvoren", "gnosiskyc.modal.failed.close": "Zatvori", "gnosiskyc.modal.failed.title": "<PERSON><PERSON><PERSON><PERSON>, naš partner <PERSON><PERSON> Pay ne može stvoriti račun za tebe", "gnosiskyc.modal.in-progress.title": "Provjera identiteta može potrajati 24 sata ili duže. Molimo te za strpljenje.", "goToSettingsPopup.settings": "Postavke", "goToSettingsPopup.title": "Obavijesti možeš omogućiti bilo kada u postavkama uređaja", "google_file.error.failed_to_fetch_auth_token.button_title": "Pokušaj ponovno", "google_file.error.failed_to_fetch_auth_token.subtitle": "Dopusti pristup oblaku za datoteku oporavka.", "google_file.error.failed_to_fetch_auth_token.title": "Nismo mogli dobiti pristup", "hidden_tokens.widget.emptyState": "<PERSON><PERSON>", "how_to_connect_to_metamask.got_it": "OK, razumijem", "how_to_connect_to_metamask.story.subtitle": "Lako se prebacuj između Zeala i drugih novčanika u bilo kojem trenutku.", "how_to_connect_to_metamask.story.title": "Zeal radi uz druge novčanike", "how_to_connect_to_metamask.why_switch": "Za<PERSON><PERSON> se prebacivati između Zeala i drugih novčanika?", "how_to_connect_to_metamask.why_switch.description": "Bez obzira koji novčanik odabereš, <PERSON>ealove sigurnosne provjere uvijek te štite od zlonamjernih stranica i transakcija.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "Znamo da je teško napraviti korak i početi koristiti novi novčanik. <PERSON>ato smo olakšali korištenje Zeala uz tvoj postojeći novčanik. Prebaci se bilo kada.", "import-bank-transfer-owner.banner.title": "Uvezi novčanik za nastavak bankovnih prijenosa.", "import-bank-transfer-owner.title": "Uvezi novčanik za korištenje bankovnih prijenosa na ovom uređaju", "import_gnosispay_wallet.add-another-card-owner.footnote": "Uvezi privatni ključ ili seed frazu koja posjeduje tvoju Gnosis Pay karticu", "import_gnosispay_wallet.primaryText": "Uvezi Gnosis Pay novčanik", "injected-wallet": "Novčanik u pregledniku", "intercom.getHelp": "Zatraži pomoć", "invalid_iban.got_it": "U redu", "invalid_iban.subtitle": "Uneseni IBAN nije važ<PERSON>ći. Provjeri jesu li podaci točno uneseni i pokušaj ponovno.", "invalid_iban.title": "Nevažeći IBAN", "keypad-0": "Tipka 0", "keypad-1": "Tipka 1", "keypad-2": "Tipka 2", "keypad-3": "Tipka 3", "keypad-4": "Tipka 4", "keypad-5": "Tipka 5", "keypad-6": "Tipka 6", "keypad-7": "Tipka 7", "keypad-8": "Tipka 8", "keypad-9": "Tipka 9", "keypad.biometric-button": "Biometrijski gumb na tipkovnici", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Tajna fraza osigu<PERSON> 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "Prikaži frazu", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Čuvaj sigurnu izvanmrežnu kopiju svoje tajne fraze kako bi kasnije mogao oporaviti svoju imovinu", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "Ne pokušavaj pogoditi riječ", "keystore.write_secret_phrase.before_you_begin.first_point": "Razu<PERSON><PERSON><PERSON> da svatko tko ima moju tajnu frazu može prenijeti moju imovinu", "keystore.write_secret_phrase.before_you_begin.second_point": "Odgovoran sam za čuvanje svoje tajne fraze tajnom i sigurnom", "keystore.write_secret_phrase.before_you_begin.subtitle": "Pročitaj i prihvati sljedeće točke:", "keystore.write_secret_phrase.before_you_begin.third_point": "Nalazim se na privatnom mjestu bez ljudi ili kamera oko mene", "keystore.write_secret_phrase.before_you_begin.title": "Prije nego š<PERSON> p<PERSON>š", "keystore.write_secret_phrase.secret_phrase_test.title": "<PERSON><PERSON> je riječ {count} u tvojoj tajnoj frazi?", "keystore.write_secret_phrase.test_ps.lets_do_it": "<PERSON><PERSON><PERSON>", "keystore.write_secret_phrase.test_ps.subtitle": "Trebat će ti tvoja tajna fraza za vraćanje računa na ovom ili drugim uređajima. Testirajmo je li tvoja tajna fraza ispravno zapisana.", "keystore.write_secret_phrase.test_ps.subtitle2": "Pitat ćemo te za {count} riječi u tvojoj frazi.", "keystore.write_secret_phrase.test_ps.title": "Testiraj oporavak računa", "kyc.modal.approved.button-text": "Izvrši bankovni prijenos", "kyc.modal.approved.subtitle": "Verifikacija je dovršena. Sada možeš neograničeno vršiti bankovne prijenose.", "kyc.modal.approved.title": "Bankovni prijenosi otključani", "kyc.modal.continue-with-partner.button-text": "<PERSON><PERSON><PERSON>", "kyc.modal.continue-with-partner.subtitle": "Sada ćemo te preusmjeriti našem partneru radi prikupljanja dokumentacije i dovršetka prijave za provjeru.", "kyc.modal.continue-with-partner.title": "<PERSON><PERSON><PERSON> s našim <PERSON>om", "kyc.modal.failed.unblock.subtitle": "Unblock nije odobrio tvoju verifikaciju i ne može ti pružiti uslugu bankovnog prijenosa.", "kyc.modal.failed.unblock.title": "Unblock prijava nije odobrena", "kyc.modal.paused.button-text": "<PERSON><PERSON><PERSON><PERSON>", "kyc.modal.paused.subtitle": "Izgleda da su neki podaci netočni. Pokušaj ponovno i provjeri ih prije slanja.", "kyc.modal.paused.title": "Čini se da su podaci netočni", "kyc.modal.pending.button-text": "Zatvori", "kyc.modal.pending.subtitle": "Verifikacija obično traje manje od 10 minuta, ali ponekad može potrajati malo duže.", "kyc.modal.pending.title": "Obavijestit ćemo te", "kyc.modal.required.cta": "Započni verifikaciju", "kyc.modal.required.subtitle": "Dosegnuo si limit transakcija. Verificiraj identitet za nastavak. To traje par minuta i zahtijeva osobne podatke i dokumente.", "kyc.modal.required.title": "Potrebna verifikacija identiteta", "kyc.submitted": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "kyc.submitted_short": "Poslano", "kyc_status.completed_status": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc_status.failed_status": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc_status.paused_status": "Pregled", "kyc_status.subtitle": "Bankovni prijenosi", "kyc_status.subtitle.wrong_details": "Pogrešni podaci", "kyc_status.subtitle_in_progress": "U tijeku", "kyc_status.title": "Provjera identiteta", "label.close": "Zatvori", "label.saving": "Spremanje...", "labels.this-month": "<PERSON><PERSON><PERSON>", "labels.today": "<PERSON><PERSON>", "labels.yesterday": "<PERSON><PERSON><PERSON>", "language.selector.title": "<PERSON><PERSON><PERSON>", "ledger.account_loaded.imported": "Uvezeno", "ledger.add.success.title": "Ledger <PERSON><PERSON><PERSON><PERSON><PERSON> povezan 🎉", "ledger.connect.cta": "Sinkron<PERSON><PERSON><PERSON>", "ledger.connect.step1": "Poveži Ledger sa svojim uređajem", "ledger.connect.step2": "Otvori Ethereum aplikaciju na Ledgeru", "ledger.connect.step3": "Zatim sinkroniziraj svoj Ledger 👇", "ledger.connect.subtitle": "Slijedi ove korake za uvoz svojih Ledger novčanika u Zeal", "ledger.connect.title": "Poveži Ledger sa Zealom", "ledger.error.ledger_is_locked.subtitle": "Otključaj Ledger i otvori Ethereum app.", "ledger.error.ledger_is_locked.title": "Ledger je z<PERSON>", "ledger.error.ledger_not_connected.action": "Sinkron<PERSON><PERSON><PERSON>", "ledger.error.ledger_not_connected.subtitle": "Poveži Ledger i otvori Ethereum aplikaciju.", "ledger.error.ledger_not_connected.title": "Ledger nije povezan", "ledger.error.ledger_running_non_eth_app.title": "Ethereum aplikacija nije otvorena", "ledger.error.user_trx_denied_by_user.action": "Zatvori", "ledger.error.user_trx_denied_by_user.subtitle": "Odbio si transakciju na svom uređaju.", "ledger.error.user_trx_denied_by_user.title": "Transakcija odbijena", "ledger.hd_path.bip44.subtitle": "npr. <PERSON>, Trezor", "ledger.hd_path.bip44.title": "BIP44 standard", "ledger.hd_path.ledger_live.subtitle": "Zadano", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Naslijeđ<PERSON>", "ledger.hd_path.phantom.subtitle": "npr. Phantom", "ledger.select.hd_path.subtitle": "HD putanje su način na koji hardverski novčanici sortiraju svoje račune. Slično je načinu na koji indeks sortira stranice u knjizi.", "ledger.select.hd_path.title": "Odaberi HD putanju", "ledger.select_account.import_wallets_count": "{count,plural,=0{<PERSON>ema odabranih novčanika} one{Uvezi novčanik} other{Uvezi {count} novčanika}}", "ledger.select_account.path_settings": "<PERSON><PERSON><PERSON>", "ledger.select_account.subtitle": "Ne vidiš očekivane novčanike? Pokušaj promijeniti postavke putanje", "ledger.select_account.subtitle.group_header": "Novčanici", "ledger.select_account.title": "Uvezi Ledger novčanike", "legend.lending-operations": "Poslovi pozajmljivanja", "legend.market_making-operations": "Poslovi održavanja tržišta", "legend.treasury-operations": "Trezorske operacije", "link-existing-monerium-account-sign.button": "<PERSON><PERSON><PERSON><PERSON>", "link-existing-monerium-account-sign.subtitle": "<PERSON><PERSON><PERSON> imaš <PERSON> račun.", "link-existing-monerium-account-sign.title": "Poveži Zeal sa svojim postojećim Monerium računom", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Već imaš Monerium račun. Posjeti aplikaciju Monerium da dovršiš <PERSON>avl<PERSON>je.", "link-existing-monerium-account.title": "Otvori Monerium da povežeš račun", "loading.pin": "Učitavanje PIN-a...", "lockScreen.passwordIncorrectMessage": "Lozinka je netočna", "lockScreen.passwordRequiredMessage": "Potrebna je lozinka", "lockScreen.unlock.header": "Otkl<PERSON><PERSON><PERSON>", "lockScreen.unlock.subheader": "Upotrijebi lozinku za otključavanje Zeala", "mainTabs.activity.label": "Aktivnost", "mainTabs.browse.label": "<PERSON><PERSON><PERSON>", "mainTabs.browse.title": "<PERSON><PERSON><PERSON>", "mainTabs.card.label": "Kartica", "mainTabs.portfolio.label": "Portfelj", "mainTabs.rewards.label": "Nagrade", "makeSpendable.cta": "<PERSON><PERSON><PERSON>", "makeSpendable.holdAsCash": "<PERSON><PERSON><PERSON><PERSON><PERSON> kao gotovinu", "makeSpendable.shortText": "Zarada {apy} godišnje", "makeSpendable.title": "{amount} p<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.agriculture": "Poljoprivreda", "merchantCategory.alcohol": "Alkohol", "merchantCategory.antiques": "Antik<PERSON><PERSON><PERSON>", "merchantCategory.appliances": "Kuć<PERSON><PERSON> aparati", "merchantCategory.artGalleries": "Umjetničke galerije", "merchantCategory.autoRepair": "Popravak automobila", "merchantCategory.autoRepairService": "Servis za popravak automobila", "merchantCategory.beautyFitnessSpas": "Ljepota, fitness i spa", "merchantCategory.beautyPersonalCare": "Ljepota i osobna njega", "merchantCategory.billiard": "<PERSON><PERSON><PERSON>", "merchantCategory.books": "<PERSON><PERSON><PERSON>", "merchantCategory.bowling": "Ku<PERSON>lan<PERSON>", "merchantCategory.businessProfessionalServices": "Poslovne i profesionalne usluge", "merchantCategory.carRental": "Najam automobila", "merchantCategory.carWash": "Autopraonica", "merchantCategory.cars": "Automobili", "merchantCategory.casino": "<PERSON><PERSON><PERSON>", "merchantCategory.casinoGambling": "Kasino i kockanje", "merchantCategory.cellular": "<PERSON><PERSON><PERSON>", "merchantCategory.charity": "Dobrotvorne svrhe", "merchantCategory.childcare": "<PERSON><PERSON><PERSON> o djeci", "merchantCategory.cigarette": "Cigarete", "merchantCategory.cinema": "<PERSON><PERSON>", "merchantCategory.cinemaEvents": "Kino i događaji", "merchantCategory.cleaning": "Čišćenje", "merchantCategory.cleaningMaintenance": "Čišćenje i održavanje", "merchantCategory.clothes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.clothingServices": "Usluge vezane za odjeću", "merchantCategory.communicationServices": "Komunikacijske usluge", "merchantCategory.construction": "Gradnja", "merchantCategory.cosmetics": "Kozmetika", "merchantCategory.craftsArtSupplies": "Obrt i umjetnički pribor", "merchantCategory.datingServices": "Usluge za upoznavanje", "merchantCategory.delivery": "<PERSON><PERSON><PERSON>", "merchantCategory.dentist": "Zubar", "merchantCategory.departmentStores": "<PERSON><PERSON>", "merchantCategory.directMarketingSubscription": "Izravni marketing i pretplata", "merchantCategory.discountStores": "<PERSON><PERSON><PERSON>ne trgo<PERSON>e", "merchantCategory.drugs": "Lijekovi", "merchantCategory.dutyFree": "Duty Free", "merchantCategory.education": "Obrazovanje", "merchantCategory.electricity": "Struja", "merchantCategory.electronics": "Elektronika", "merchantCategory.emergencyServices": "<PERSON><PERSON>", "merchantCategory.equipmentRental": "Najam opreme", "merchantCategory.evCharging": "Punjenje el. vozila", "merchantCategory.financialInstitutions": "Financijske institucije", "merchantCategory.financialProfessionalServices": "Financijske i profesionalne usluge", "merchantCategory.finesPenalties": "Novčane kazne i penali", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "<PERSON><PERSON>", "merchantCategory.flowers": "Cvijeće", "merchantCategory.flowersGarden": "Cvijeće i vrt", "merchantCategory.food": "<PERSON><PERSON>", "merchantCategory.freight": "Prijevoz tereta", "merchantCategory.fuel": "Gorivo", "merchantCategory.funeralServices": "Pogrebne usluge", "merchantCategory.furniture": "Namješ<PERSON>j", "merchantCategory.games": "Igre", "merchantCategory.gas": "Gorivo", "merchantCategory.generalMerchandiseRetail": "Opća roba i maloprodaja", "merchantCategory.gifts": "Pokloni", "merchantCategory.government": "<PERSON><PERSON>", "merchantCategory.governmentServices": "Državne službe", "merchantCategory.hardware": "Željezarija", "merchantCategory.healthMedicine": "Zdravlje i medicina", "merchantCategory.homeImprovement": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.homeServices": "Usluge za kućanstvo", "merchantCategory.hotel": "Hotel", "merchantCategory.housing": "Stanovanje", "merchantCategory.insurance": "Osiguranje", "merchantCategory.internet": "Internet", "merchantCategory.kids": "Djeca", "merchantCategory.laundry": "Praonica rublja", "merchantCategory.laundryCleaningServices": "Praonica i usluge čišćenja", "merchantCategory.legalGovernmentFees": "Pravne i državne naknade", "merchantCategory.luxuries": "Luksuz", "merchantCategory.luxuriesCollectibles": "Luksuz i kolekcionarstvo", "merchantCategory.magazines": "Časopisi", "merchantCategory.magazinesNews": "Časopisi i vijesti", "merchantCategory.marketplaces": "Tržnice", "merchantCategory.media": "<PERSON><PERSON><PERSON>", "merchantCategory.medicine": "Medicina", "merchantCategory.mobileHomes": "Mobilne kućice", "merchantCategory.moneyTransferCrypto": "Prijenos novca i kripto", "merchantCategory.musicRelated": "Glazba", "merchantCategory.musicalInstruments": "Glazbeni instrumenti", "merchantCategory.optics": "Optika", "merchantCategory.organizationsClubs": "Organizacije i klubovi", "merchantCategory.other": "Ostalo", "merchantCategory.parking": "Parking", "merchantCategory.pawnShops": "Zalagaonice", "merchantCategory.pets": "L<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.photoServicesSupplies": "Foto usluge i oprema", "merchantCategory.postalServices": "Poštanske usluge", "merchantCategory.professionalServicesOther": "Profesionalne <PERSON> (ostalo)", "merchantCategory.publicTransport": "Javni prijevoz", "merchantCategory.purchases": "<PERSON><PERSON><PERSON>", "merchantCategory.purchasesMiscServices": "Kupnja i razne usluge", "merchantCategory.recreationServices": "Uslug<PERSON> rekreacije", "merchantCategory.religiousGoods": "Vjerski proizvodi", "merchantCategory.secondhandRetail": "Trgovina rabljenom robom", "merchantCategory.shoeHatRepair": "Popravak obuće i šešira", "merchantCategory.shoeRepair": "Popravak obuće", "merchantCategory.softwareApps": "Softver i aplikacije", "merchantCategory.specializedRepairs": "Specijalizirani popravci", "merchantCategory.sport": "Sport", "merchantCategory.sportingGoods": "Sportska oprema", "merchantCategory.sportingGoodsRecreation": "Sportska oprema i rekreacija", "merchantCategory.sportsClubsFields": "Sportski klubovi i tereni", "merchantCategory.stationaryPrinting": "Pribor i tisak", "merchantCategory.stationery": "Pisaći pribor", "merchantCategory.storage": "Skladištenje", "merchantCategory.taxes": "<PERSON><PERSON><PERSON>", "merchantCategory.taxi": "<PERSON><PERSON><PERSON>", "merchantCategory.telecomEquipment": "Telekomunikacijska oprema", "merchantCategory.telephony": "Telefonija", "merchantCategory.tobacco": "<PERSON><PERSON>", "merchantCategory.tollRoad": "Cestarina", "merchantCategory.tourismAttractionsAmusement": "Turizam, atrakcije i zabava", "merchantCategory.towing": "Vučna služ<PERSON>", "merchantCategory.toys": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.toysHobbies": "Igračke i hobiji", "merchantCategory.trafficFine": "Prometna kazna", "merchantCategory.train": "<PERSON><PERSON>", "merchantCategory.travelAgency": "Putnička agencija", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, radio i streaming", "merchantCategory.utilities": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.waterTransport": "Vodeni prijevoz", "merchantCategory.wholesaleClubs": "Veleprodajni klubovi", "metaMask.subtitle": "Omogući MetaMask način rada kako bi sve MetaMask veze preusmjerio na Zeal. Klikom na MetaMask u dApps aplikacijama povezat ćeš se sa Zealom.", "metaMask.title": "Ne možeš se povezati sa Zealom?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Otvori svoju bankarsku aplikaciju", "monerium-bank-deposit.buttet-point.receive-crypto": "Primi digitalni EUR", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Pošalji {fiatCurrencyCode} na svoj račun", "monerium-bank-deposit.deposit-account-country": "Država", "monerium-bank-deposit.header": "{fullName} osobni račun", "monerium-bank-details.account-name": "Naziv računa", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT kopiran", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT kod kopiran", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN kopiran", "monerium-bank-details.to-wallet": "U novčanik", "monerium-bank-details.transfer-fee": "Naknada za prijenos", "monerium-bank-transfer.enable-card.bullet-1": "Dov<PERSON>ši provjeru identiteta", "monerium-bank-transfer.enable-card.bullet-2": "Preuzmi osobne podatke o računu", "monerium-bank-transfer.enable-card.bullet-3": "Uplati s bankovnog računa", "monerium-card-delay-relay.success.cta": "Zatvori", "monerium-card-delay-relay.success.subtitle": "<PERSON><PERSON><PERSON>, obrada promjena postavki kartice traje 3 minute.", "monerium-card-delay-relay.success.title": "Nastavi postavljanje Moneriuma za 3 min", "monerium-deposit.account-details-info-popup.bullet-point-1": "Svaki {fiatCurrencyCode} ko<PERSON> <PERSON><PERSON><PERSON><PERSON> na ovaj račun automatski će se pretvoriti u {cryptoCurrencyCode} tokene na {cryptoCurrencyChain} Chainu i poslati u tvoj novčanik", "monerium-deposit.account-details-info-popup.bullet-point-2": "ŠALJI SAMO {fiatCurrencyCode} ({fiatCurrencySymbol}) na svoj račun", "monerium-deposit.account-details-info-popup.title": "Podaci o tvom računu", "monerium.check_order_status.sending": "<PERSON><PERSON><PERSON>", "monerium.not-eligible.cta": "Natrag", "monerium.not-eligible.subtitle": "Monerium ti ne može otvoriti račun. <PERSON><PERSON>, odaberi drugog pružatelja.", "monerium.not-eligible.title": "Pokušaj s drugim pružateljem", "monerium.setup-card.cancel": "Odustani", "monerium.setup-card.continue": "<PERSON><PERSON><PERSON>", "monerium.setup-card.create_account": "Otvori račun", "monerium.setup-card.login": "Prijavi se na Gnosis Pay", "monerium.setup-card.subtitle": "Otvori ili se prijavi na svoj Gnosis Pay račun kako bi omogućio trenutačne bankovne pologe.", "monerium.setup-card.subtitle_personal_account": "Otvori svoj osobni račun uz Gnosis Pay u nekoliko minuta:", "monerium.setup-card.title": "Omogući bankovne pologe", "moneriumDepositSuccess.goToWallet": "Idi u novčanik", "moneriumDepositSuccess.title": "{symbol} p<PERSON><PERSON><PERSON><PERSON>", "moneriumInfo.fees": "Naknade su 0%", "moneriumInfo.registration": "Monerium je ovlašten i reguliran kao Institucija za elektronički novac prema islandskom Zakonu o elektroničkom novcu br. 17/2013 <link>Saznaj više</link>", "moneriumInfo.selfCustody": "Digitalni novac koji primaš nalazi se u tvom privatnom novčaniku i nitko drugi neće imati kontrolu nad tvojom imovinom.", "moneriumWithdrawRejected.supportText": "Prijenos nije uspio. Pokušaj ponovno, a ako i dalje ne radi, <link>obrati se podršci.</link>", "moneriumWithdrawRejected.title": "Prijenos vraćen", "moneriumWithdrawRejected.tryAgain": "Pokušaj ponovno", "moneriumWithdrawSuccess.supportText": "<PERSON><PERSON><PERSON> proć<PERSON> 24 sata dok tvoj{br}primatelj ne primi sredstva", "moneriumWithdrawSuccess.title": "Poslano", "monerium_enable_banner.text": "Aktiviraj bankovne prijenose", "monerium_error_address_re_link_required.title": "Ponovno poveži novčanik s Moneriumom.", "monerium_error_duplicate_order.title": "<PERSON>vos<PERSON><PERSON>", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "Švicarski franak", "mt-pelerin-fork.options.chf.short": "Trenutno i besplatno uz Mt Pelerin", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Trenutno i besplatno uz Monerium", "mt-pelerin-fork.title": "<PERSON><PERSON>?", "mtPelerinProviderInfo.fees": "Plaćaš 0% naknada", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd povezan je sa SO-FIT-om, samoregulacijskim tijelom koje je priznalo Švicarsko nadzorno tijelo za financijsko tržište (FINMA) prema Zakonu o sprječavanju pranja novca. <link>Saznaj više</link>", "mtPelerinProviderInfo.selfCustody": "Digitalni novac koji primiš je u tvom privatnom vlasništvu i nitko drugi neće imati kontrolu nad tvojom imovinom", "network-fee-widget.title": "Naknade", "network.edit.verifying_rpc": "Provjera RPC-a", "network.editRpc.predefined_network_info.subtitle": "Poput VPN-a, <PERSON><PERSON> koristi RPC-jeve koji sprječavaju praćenje tvojih osobnih podataka.{br}{br}Zealovi zadani RPC-jevi su pouzdani i provjereni pružatelji usluga.", "network.editRpc.predefined_network_info.title": "Zeal RPC za privatnost", "network.filter.update_rpc_success": "RPC <PERSON><PERSON>", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "<PERSON><PERSON>", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "<PERSON><PERSON><PERSON>", "networks.filter.add_modal.chain_list.subtitle": "Dodaj bilo koju EVM mrežu", "networks.filter.add_modal.chain_list.title": "<PERSON><PERSON>", "networks.filter.add_modal.dapp_tip.subtitle": "U svojim omiljenim dApp aplikacijama, jednostavno se prebaci na EVM mrežu koju želiš koristiti i Zeal će te pitati želiš li je dodati u svoj novčanik.", "networks.filter.add_modal.dapp_tip.title": "Ili dodaj mrežu iz bilo koje dApp aplikacije", "networks.filter.add_networks.subtitle": "Podržane su sve EVM mreže", "networks.filter.add_networks.title": "<PERSON><PERSON><PERSON>", "networks.filter.add_test_networks.title": "<PERSON><PERSON><PERSON>", "networks.filter.tab.netwokrs": "Mreže", "networks.filter.testnets.title": "<PERSON><PERSON>", "nft.widget.emptystate": "Nema kolekcionarskih predmeta u novčaniku", "nft_collection.change_account_picture.subtitle": "<PERSON><PERSON><PERSON> profilnu sliku?", "nft_collection.change_account_picture.title": "<PERSON><PERSON><PERSON>raj profilnu sliku na NFT", "nfts.allNfts.pricingPopup.description": "Cijene se temelje na zadnjoj trgovini.", "nfts.allNfts.pricingPopup.title": "Cijene kolekcionarskih predmeta", "no-passkeys-found.modal.cta": "Zatvori", "no-passkeys-found.modal.subtitle": "Ne možemo otkriti nijedan Zeal pristupni ključ na ovom uređaju. Provjeri jesi li prijavljen/a na račun u oblaku koji si koristio/la za stvaranje svog Smart Walleta.", "no-passkeys-found.modal.title": "Nisu pronađeni pristupni ključevi", "notValidEmail.title": "Nevažeća e-mail adresa", "notValidPhone.title": "Ovo nije važeći broj telefona", "notification-settings.title": "Postavke obavijesti", "notification-settings.toggles.active-wallets": "Aktivni novčanici", "notification-settings.toggles.bank-transfers": "Bankovni prijenosi", "notification-settings.toggles.card-payments": "Plaćanja karticom", "notification-settings.toggles.readonly-wallets": "Novčanici samo za čitanje", "ntft.groupHeader.text": "Kolekcionarski predmeti", "on_ramp.crypto_completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "on_ramp.fiat_completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onboarding-widget.subtitle.card_created_from_order.left": "Visa kartica", "onboarding-widget.subtitle.card_created_from_order.right": "<PERSON><PERSON><PERSON><PERSON><PERSON> karticu", "onboarding-widget.subtitle.card_order_ready.left": "Fizička Visa kartica", "onboarding-widget.subtitle.default": "Bankovni prijenosi i Visa kartica", "onboarding-widget.title.card-order-in-progress": "Nastavi narudžbu kartice", "onboarding-widget.title.card_created_from_order": "Kartica je poslana", "onboarding-widget.title.kyc_approved": "Prijenosi i kartica spremni", "onboarding-widget.title.kyc_failed": "Kreiranje računa nije moguće", "onboarding-widget.title.kyc_not_started": "<PERSON><PERSON><PERSON>", "onboarding-widget.title.kyc_started_documents_requested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onboarding-widget.title.kyc_started_resubmission_requested": "Ponovi verifikaciju", "onboarding-widget.title.kyc_started_verification_in_progress": "Verifikacija identiteta u tijeku", "onboarding.loginOrCreateAccount.amountOfAssets": "$10 mlrd+ imovine", "onboarding.loginOrCreateAccount.cards.subtitle": "Dostupno samo u određenim regijama. Nastavkom prihvaćaš naše <Terms>Uvjete korištenja</Terms> i <PrivacyPolicy>Pravila o privatnosti</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Visa kartica s visokim{br}povratima i bez naknada", "onboarding.loginOrCreateAccount.createAccount": "Stvori račun", "onboarding.loginOrCreateAccount.earn.subtitle": "Povrati variraju; kapital je u opasnosti. Nastavkom prihvaćaš naše <Terms>Uvjete korištenja</Terms> i <PrivacyPolicy>Pravila o privatnosti</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "Zarada od {percent} godišnje{br}S povjerenjem od {currencySymbol}5 mlrd+", "onboarding.loginOrCreateAccount.earningPerYear": "Zarada {percent}{br}godiš<PERSON><PERSON>", "onboarding.loginOrCreateAccount.login": "<PERSON><PERSON><PERSON><PERSON>", "onboarding.loginOrCreateAccount.trading.subtitle": "Kapital je u opasnosti. Nastavkom prihvaćaš naše <Terms>Uvjete korištenja</Terms> i <PrivacyPolicy>Pravila o privatnosti</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Ulaži u sve,{br}od BTC-a do S&P-a", "onboarding.loginOrCreateAccount.trustedBy": "Digitalna novčana tržišta{br}S povjerenjem od {assets}", "onboarding.wallet_stories.close": "Zatvori", "onboarding.wallet_stories.previous": "Prethodno", "order-earn-deposit-bridge.deposit": "<PERSON><PERSON>", "order-earn-deposit-bridge.into": "U", "otpIncorrectMessage": "Kod za potvrdu je netočan", "passkey-creation-not-possible.modal.close": "Zatvori", "passkey-creation-not-possible.modal.subtitle": "Nismo mogli izraditi pristupni ključ za tvoj novčanik. Provjeri podržava li tvoj uređaj pristupne ključeve i pokušaj ponovno. <link>Javi se podršci</link> ako se problem nastavi.", "passkey-creation-not-possible.modal.title": "<PERSON>je mogu<PERSON>e izraditi pristupni ključ", "passkey-not-supported-in-mobile-browser.modal.cta": "<PERSON><PERSON><PERSON> Zeal", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Pametni novčanici ne rade na mobitelu.", "passkey-not-supported-in-mobile-browser.modal.title": "Preuzmi Zeal aplikaciju za nastavak", "passkey-recovery.recovering.deploy-signer.loading-text": "Provjera pristupnog ključa", "passkey-recovery.recovering.loading-text": "Oporavak novčanika", "passkey-recovery.recovering.signer-not-found.subtitle": "Nismo uspjeli povezati tvoj pristupni ključ s aktivnim novčanikom. Ako imaš sredstva u novčaniku, obrati se Zeal timu za podršku.", "passkey-recovery.recovering.signer-not-found.title": "Novčanik nije pronađen", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "Probajte s <PERSON>im k<PERSON>", "passkey-recovery.select-passkey.banner.subtitle": "Provjerite jeste li prijavljeni na pravi račun. Ključevi su vezani za račun.", "passkey-recovery.select-passkey.banner.title": "Ne vidiš pristupni ključ svog novčanika?", "passkey-recovery.select-passkey.continue": "Odaberi pristupni ključ", "passkey-recovery.select-passkey.subtitle": "Odaberi pristupni ključ povezan s tvojim novčanikom kako bi vratio/la pristup.", "passkey-recovery.select-passkey.title": "Odaberi pristupni ključ", "passkey-story_1.subtitle": "Sa Smart Walletom možeš plaćati mrežne naknade u većini tokena, tako da ne moraš brinuti o 'gasu'.", "passkey-story_1.title": "Zaboravi na 'gas' - plaćaj mrežne naknade u većini tokena", "passkey-story_2.subtitle": "Izgrađen na vodećim pametnim ugovorima tvrtke Safe koji osiguravaju više od 100 milijardi dolara u više od 20 milijuna novčanika.", "passkey-story_2.title": "Osigurano uz Safe", "passkey-story_3.subtitle": "Smart Wallets rade na glavnim mrežama kompatibilnim s Ethereumom. Provjeri podržane mreže prije slanja imovine.", "passkey-story_3.title": "Podržane su glavne EVM mreže", "password.add.header": "<PERSON><PERSON><PERSON> lo<PERSON>", "password.add.includeLowerAndUppercase": "Mala i velika slova", "password.add.includesNumberOrSpecialChar": "<PERSON><PERSON> broj ili simbol", "password.add.info.subtitle": "Ne šaljemo tvoju lozinku na naše poslužitelje niti je spremamo za tebe", "password.add.info.t_and_c": "Nastavkom prihvaćaš naše <Terms>Uvjete korištenja</Terms> i <PrivacyPolicy>Pravila o privatnosti</PrivacyPolicy>", "password.add.info.title": "Tvoja lozinka ostaje na ovom uređaju", "password.add.inputPlaceholder": "<PERSON><PERSON><PERSON> lo<PERSON>", "password.add.shouldContainsMinCharsCheck": "10+ z<PERSON><PERSON>", "password.add.subheader": "Koristit ćeš lozinku za otključavanje Zeala", "password.add.success.title": "Lozinka stvorena 🔥", "password.confirm.header": "Potvrdi lozinku", "password.confirm.passwordDidNotMatch": "Lozinke se moraju podudarati", "password.confirm.subheader": "Unesi lozinku još jednom", "password.create_pin.subtitle": "Ova zaporka zaključava aplikaciju Zeal", "password.create_pin.title": "Stvori svoju zaporku", "password.enter_pin.title": "Unesi zaporku", "password.incorrectPin": "Netočna zaporka", "password.pin_is_not_same": "Zaporka se ne podudara", "password.placeholder.enter": "<PERSON><PERSON> lozinku", "password.placeholder.reenter": "Ponovno unesi lozinku", "password.re_enter_pin.subtitle": "Ponovno unesi istu zaporku", "password.re_enter_pin.title": "Potvrdi zaporku", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "Na čekanju", "pending-send.details.pending": "Na čekanju", "pending-send.details.processing": "Obrada", "permit-info.modal.description": "Dozvole su zahtjevi koji, ako ih potpišeš, omogućuju aplikacijama premještanje tvojih tokena u tvoje ime, npr. za zamjenu.{br}Dozvole su slične Odobrenjima, ali te potpisivanje ne košta mrežnih naknada.", "permit-info.modal.title": "Što su dozvole?", "permit.edit-expiration": "Uredi {currency} istek", "permit.edit-limit": "Uredi {currency} limit potrošnje", "permit.edit-modal.expiresIn": "Istječe za…", "permit.expiration-warning": "{currency} upozorenje o isteku", "permit.expiration.info": "{currency} info o isteku", "permit.expiration.never": "Nikad", "permit.spend-limit.info": "{currency} info o limitu potrošnje", "permit.spend-limit.warning": "{currency} upozorenje o limitu potrošnje", "phoneNumber.title": "broj telefona", "physicalCardOrderFlow.cardOrdered": "Kartica naručena", "physicalCardOrderFlow.city": "Grad", "physicalCardOrderFlow.orderCard": "<PERSON><PERSON><PERSON><PERSON> karticu", "physicalCardOrderFlow.postcode": "Poštanski broj", "physicalCardOrderFlow.shippingAddress.subtitle": "Adresa na koju će kartica biti poslana", "physicalCardOrderFlow.shippingAddress.title": "<PERSON><PERSON><PERSON> za <PERSON>", "physicalCardOrderFlow.street": "Ulica", "placeholderDapps.1inch.description": "<PERSON><PERSON><PERSON><PERSON> putem najboljih ruta", "placeholderDapps.aave.description": "Posudi i pozajmi tokene", "placeholderDapps.bungee.description": "Poveži mreže putem najboljih ruta", "placeholderDapps.compound.description": "Posudi i pozajmi tokene", "placeholderDapps.cowswap.description": "Mijenjaj po najboljim tečajevima na Gnosisu", "placeholderDapps.gnosis-pay.description": "Upravljaj svojom Gnosis Pay karticom", "placeholderDapps.jumper.description": "Poveži mreže putem najboljih ruta", "placeholderDapps.lido.description": "Uloži ETH za više ETH-a", "placeholderDapps.monerium.description": "e-Novac i bankovni prijenosi", "placeholderDapps.odos.description": "<PERSON><PERSON><PERSON><PERSON> putem najboljih ruta", "placeholderDapps.stargate.description": "Poveži ili uloži za <14% APY", "placeholderDapps.uniswap.description": "Jedna od najpopularnijih mjenjačnica", "pleaseAllowNotifications.cardPayments": "Plaćanja karticom", "pleaseAllowNotifications.customiseInSettings": "Prilagodi u postavkama", "pleaseAllowNotifications.enable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pleaseAllowNotifications.forWalletActivity": "Za aktivnost novčanika", "pleaseAllowNotifications.title": "Primaj obavijesti novčanika", "pleaseAllowNotifications.whenReceivingAssets": "Prilikom primanja sredstava", "portfolio.quick-actions.add_funds": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.buy": "<PERSON><PERSON>", "portfolio.quick-actions.deposit": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.send": "Pošalji", "portfolio.view.lastRefreshed": "Osv<PERSON><PERSON><PERSON> {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Nadoplati svoj testni AVAX", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Idi na Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "Nadoplati svoj testni BNB", "portfolio.view.topupTestNet.BscTestnet.secondary": "Idi na Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Nadoplati svoj testni SepETH", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Idi na Sepolia Faucet", "portfolio.view.topupTestNet.FantomTestnet.primary": "Nadoplati svoj testni FTM", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Idi na Faucet", "privateKeyConfirmation.banner.subtitle": "Tko ima vaš privatni ključ, ima pristup novčaniku. Samo ga prevaranti traže.", "privateKeyConfirmation.banner.title": "<PERSON><PERSON><PERSON>je<PERSON> rizike", "privateKeyConfirmation.title": "NIKADA ne dijeli svoj privatni ključ ni s kim", "rating-request.not-now": "<PERSON>e sada", "rating-request.title": "Bi li preporučio/la Zeal?", "receive_funds.address-text": "Ovo je tvoja jedinstvena adresa novčanika. Možeš je sigurno dijeliti s drugima.", "receive_funds.copy_address": "<PERSON><PERSON><PERSON> nov<PERSON>", "receive_funds.network-warning.eoa.subtitle": "<link>Pogledaj popis standardnih mreža</link>. Imovina poslana na mrežama koje nisu EVM bit će izgubljena.", "receive_funds.network-warning.eoa.title": "Podržane su sve mreže temeljene na Ethereumu", "receive_funds.network-warning.scw.subtitle": "<link>Pogledaj podržane mreže</link>. Imovina poslana na drugim mrežama bit će izgubljena.", "receive_funds.network-warning.scw.title": "Važno: <PERSON><PERSON><PERSON> samo podržane mreže", "receive_funds.scan_qr_code": "Skeniraj QR kod", "receiving.in.days": "Primitak za {days}d", "receiving.this.week": "Primitak ovaj tjedan", "receiving.today": "Primitak danas", "reference.error.maximum_number_of_characters_exceeded": "<PERSON><PERSON><PERSON><PERSON>", "referral-code.placeholder": "Zalijepi pozivni link", "referral-code.subtitle": "Klikni ponovno na prijateljev link ili ga zalijepi dolje. Želimo biti sigurni da ćeš dobiti nagrade.", "referral-code.title": "Je li ti prijatelj poslao {bReward}?", "rekyc.verification_deadline.subtitle": "Dov<PERSON>ši verifikaciju unutar {daysUntil} dana za daljnje korištenje kartice.", "rekyc.verification_required.subtitle": "Dovrši verifikaciju za nastavak korištenja kartice.", "reminder.fund": "💸 Dodaj sredstva — počni odmah zarađivati 6%", "reminder.onboarding": "🏁 Dovrši postavljanje — zaradi 6% na svoje pologe", "remove-owner.confirmation.subtitle": "<PERSON><PERSON>, prom<PERSON>ne postavki traju 3 minute. <PERSON><PERSON> to vri<PERSON><PERSON>, kartica će biti privremeno zamrznuta i plaćanja neće biti moguća.", "remove-owner.confirmation.title": "Tvoja će kartica biti zamrznuta na 3 min dok se postavke ažuriraju", "restore-smart-wallet.wallet-recovered": "Novčanik je oporavljen", "rewardClaimCelebration.claimedTitle": "Nagrade su već preuzete", "rewardClaimCelebration.subtitle": "Za pozivanje prijatelja", "rewardClaimCelebration.title": "Zaradio si", "rewards-warning.subtitle": "Uklanjanjem ovog računa pauzirat će se pristup povezanim nagradama. Račun možeš vratiti u bilo kojem trenutku i preuzeti ih.", "rewards-warning.title": "Izgubit ćeš pristup svojim nagradama", "rewards.copiedInviteLink": "Pozivni link kopiran", "rewards.createAccount": "<PERSON><PERSON><PERSON> pozivni link", "rewards.header.subtitle": "Poslat ćemo {aReward} tebi i {bReward} tvom prijatelju, kada potro<PERSON>i {bSpendLimitReward}.", "rewards.header.title": "<PERSON><PERSON><PERSON> {amountA}{br}Daj {amountB}", "rewards.sendInvite": "Pošalji pozivnicu", "rewards.sendInviteTip": "Odaberi prijatelja i dat ćemo mu {bAmount}", "route.fees": "Naknade {fees}", "routesNotFound.description": "<PERSON><PERSON> zamjene za kombinaciju mreža {from} – {to} nije <PERSON>.", "routesNotFound.title": "<PERSON><PERSON> dostu<PERSON>ne rute za zamjenu", "rpc.OrderBuySignMessage.subtitle": "Putem Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Putem Swaps.IO", "rpc.addCustomNetwork.addNetwork": "<PERSON><PERSON><PERSON>", "rpc.addCustomNetwork.chainId": "ID lanca", "rpc.addCustomNetwork.nativeToken": "<PERSON>z<PERSON><PERSON> token", "rpc.addCustomNetwork.networkName": "<PERSON>v m<PERSON>ž<PERSON>", "rpc.addCustomNetwork.operationDescription": "Dopušta ovoj web stranici dodavanje mreže u tvoj novčanik. Zeal ne može provjeriti sigurnost prilagođenih mreža, pa se pobrini da razumiješ rizike.", "rpc.addCustomNetwork.rpcUrl": "RPC URL", "rpc.addCustomNetwork.subtitle": "<PERSON><PERSON><PERSON> {name}", "rpc.addCustomNetwork.title": "<PERSON><PERSON><PERSON>", "rpc.send_token.network_not_supported.subtitle": "Radimo na omogućavanju transakcija na ovoj mreži. Hvala na strpljenju 🙏", "rpc.send_token.network_not_supported.title": "Mreža uskoro stiže", "rpc.send_token.send_or_receive.settings": "Postavke", "rpc.sign.accept": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rpc.sign.cannot_parse_message.body": "Nismo mogli dekodirati ovu poruku. Prihvati ovaj zahtjev samo ako vjeruješ ovoj aplikaciji.{br}{br}Poruke se mogu koristiti za prijavu u aplikaciju, ali mogu i dati aplikacijama kontrolu nad tvojim tokenima.", "rpc.sign.cannot_parse_message.header": "Na<PERSON><PERSON> s oprezom", "rpc.sign.import_private_key": "Uvezi ključeve", "rpc.sign.subtitle": "Za {name}", "rpc.sign.title": "Potpiši", "safe-creation.success.title": "Novčanik stvoren", "safe-safety-checks-popup.title": "Sigurnosne provjere transakcije", "safetyChecksPopup.title": "Si<PERSON>rnosne provjere stranice", "scan_qr_code.description": "Skeniraj QR novčanika ili se poveži s aplikacijom", "scan_qr_code.show_qr_code": "Prikaži moj QR kod", "scan_qr_code.tryAgain": "Pokušaj ponovo", "scan_qr_code.unlockCamera": "Otključ<PERSON> ka<PERSON>", "screen-lock-missing.modal.close": "Zatvori", "screen-lock-missing.modal.subtitle": "Za pristupne ključeve potrebno je zaključavanje zaslona. Postavi ga i pokušaj ponovno.", "screen-lock-missing.modal.title": "Nema zaključavanja zaslona", "seedConfirmation.banner.subtitle": "Tko ima vašu tajnu frazu, ima pristup novčaniku. Samo je prevaranti traže.", "seedConfirmation.title": "NIKADA ne dijeli svoju tajnu frazu ni s kim", "select-active-owner.subtitle": "<PERSON><PERSON>š više novčanika povezanih s karticom. Odaberi jedan za povezivanje sa Zealom. Možeš promijeniti odabir bilo kada.", "select-active-owner.title": "Odaberi novčanik", "select-card.title": "<PERSON><PERSON><PERSON><PERSON> karticu", "select-crypto-currency-title": "Odaberi token", "select-token.title": "Odaberi token", "selectEarnAccount.chf.description.steps": "· Podiži sredstva 24/7, bez zaključavanja {br}· Kamata se obračunava svake sekunde {br}· Prekomjerno osigurani depoziti u <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} godišnje u CHF", "selectEarnAccount.eur.description.steps": "· Isplata sredstava 24/7, bez zaključavanja {br}· Kamata se obračunava svake sekunde {br}· Prekomjerno osigurani zajmovi uz <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} godišnje u EUR", "selectEarnAccount.subtitle": "<PERSON><PERSON><PERSON>š promijeniti bilo kada", "selectEarnAccount.title": "<PERSON><PERSON><PERSON><PERSON> valutu", "selectEarnAccount.usd.description.steps": "· Isplata sredstava 24/7, bez zaključavanja {br}· Kamata se obračunava svake sekunde {br}· Prekomjerno osigurani depoziti u <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} godišnje u USD", "selectEarnAccount.zero.description_general": "<PERSON><PERSON><PERSON> <PERSON>nu gotovinu bez zarade kamata", "selectEarnAccount.zero.title": "0 % godišnje", "selectRechargeThreshold.button.enterAmount": "Unesi iznos", "selectRechargeThreshold.button.setTo": "<PERSON><PERSON> na {amount}", "selectRechargeThreshold.description.line1": "Kada stanje na tvojoj kartici padne ispod {amount}, automatski se nadopunjuje na {amount} s tvog Earn računa.", "selectRechargeThreshold.description.line2": "Niži ciljani iznos zadržava više sredstava na tvom Earn računu (koji zarađuje 3 %). <PERSON><PERSON> možeš promijeniti bilo kada.", "selectRechargeThreshold.title": "<PERSON>avi ciljano stanje kartice", "select_currency_to_withdraw.select_token_to_withdraw": "Odaberi token za isplatu", "send-card-token.form.send": "Pošalji", "send-card-token.form.send-amount": "Iznos nadoplate", "send-card-token.form.title": "Dodaj s<PERSON>stva na karticu", "send-card-token.form.to-address": "Kartica", "send-safe-transaction.network-fee-widget.error": "Potrebno ti je {amount} ili odaberi drugi token", "send-safe-transaction.network-fee-widget.no-fee": "<PERSON>z naknade", "send-safe-transaction.network-fee-widget.title": "Naknade", "send-safe-transaction.network_fee_widget.title": "<PERSON><PERSON><PERSON><PERSON>", "send.banner.fees": "Potrebno ti je {amount} više {currency} za plaćanje naknada", "send.banner.toAddressNotSupportedNetwork.subtitle": "Novčanik primatelja ne podržava {network}. Promijeni na podržani token.", "send.banner.toAddressNotSupportedNetwork.title": "Mreža primatelja nije podržana", "send.banner.walletNotSupportedNetwork.subtitle": "Pametni novčanici ne mogu izvršavati transakcije na {network}. Promijeni na podržani token.", "send.banner.walletNotSupportedNetwork.title": "Mreža tokena nije podržana", "send.empty-portfolio.empty-state": "<PERSON><PERSON> pro<PERSON>", "send.empty-portfolio.header": "Tokeni", "send.titile": "Pošalji", "sendLimit.success.subtitle": "Tvoj dnevni limit potrošnje bit će ažuriran za 3 minute. Do tada možeš nastaviti trošiti unutar trenutnog limita.", "sendLimit.success.title": "<PERSON>va promjena će trajati 3 minute", "send_crypto.form.disconnected.cta.addFunds": "<PERSON><PERSON><PERSON>", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Prebaci na {network}", "send_crypto.form.disconnected.label": "Iznos za prijenos", "send_to.qr_code.description": "Skeniraj QR kod za slanje u novčanik", "send_to.qr_code.title": "Skeniraj QR kod", "send_to_card.header": "Pošalji na adresu kartice", "send_to_card.select_sender.add_wallet": "Dodaj novčanik", "send_to_card.select_sender.header": "Odaberi pošiljatelja", "send_to_card.select_sender.search.default_placeholder": "Pretraži adresu ili <PERSON>", "send_to_card.select_sender.show_card_address_button_description": "Prikaži adresu kartice", "send_token.form.select-address": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "send_token.form.send-amount": "Iznos za slanje", "send_token.form.title": "Pošalji", "setLimit.amount.error.zero_amount": "Nećeš moći izvršiti nijedno plaćanje", "setLimit.error.max_limit_reached": "Postavi na maks. limit {amount}", "setLimit.error.same_as_current_limit": "<PERSON><PERSON> kao trenutni limit", "setLimit.placeholder": "Trenutno: {amount}", "setLimit.submit": "Postavi limit", "setLimit.submit.error.amount_required": "Unesi iznos", "setLimit.subtitle": "Ovo je iznos koji možeš potrošiti dnevno svojom karticom.", "setLimit.title": "Postavi dnevni limit potrošnje", "settings.accounts": "<PERSON><PERSON><PERSON>", "settings.accountsSeeAll": "Prikaži sve", "settings.addAccount": "Dodaj novčanik", "settings.card": "Postavke kartice", "settings.connections": "Povezane aplikacije", "settings.currency": "<PERSON><PERSON><PERSON> valuta", "settings.default_currency_selector.title": "Valuta", "settings.discord": "Discord", "settings.experimentalMode": "Eksperimentalni način rada", "settings.experimentalMode.subtitle": "Isprobaj nove značajke", "settings.language": "<PERSON><PERSON><PERSON>", "settings.lockZeal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.notifications": "Obavijesti", "settings.open_expanded_view": "Otvori prošireni prikaz", "settings.privacyPolicy": "Pravila o privatnosti", "settings.settings": "Postavke", "settings.termsOfUse": "Uvjeti korištenja", "settings.twitter": "𝕏 / Twitter", "settings.version": "Verzija {version} okruženje: {env}", "setup-card.confirmation": "Zatraži virtualnu karticu", "setup-card.confirmation.subtitle": "Plaćaj online i dodaj u svoj {type} novčanik za beskontaktna plaćanja.", "setup-card.getCard": "<PERSON><PERSON><PERSON> karticu", "setup-card.order.physicalCard": "Fizička kartica", "setup-card.order.physicalCard.steps": "· Fizička VISA Gnosis Pay kartica {br}· Dostava traje do 3 tjedna {br}· Koristi za osobna plaćanja i na bankomatima. {br}· Dodaj u Apple/Google novčanik (samo za podržane zemlje", "setup-card.order.subtitle1": "<PERSON><PERSON><PERSON>š koristiti više kartica istovremeno", "setup-card.order.title": "Koju vrstu kartice?", "setup-card.order.virtualCard": "Virtualna kartica", "setup-card.order.virtual_card.steps": "· Digitalna VISA Gnosis Pay kartica {br}· Koristi odmah za online plaćanja {br}· Dodaj u Apple/Google novčanik (samo za podržane zemlje)", "setup-card.orderCard": "<PERSON><PERSON><PERSON><PERSON> karticu", "setup-card.virtual-card": "Zatraži virtualnu karticu", "setup.notifs.fakeAndroid.title": "Obavijesti o plaćanjima i dolaznim prijenosima", "setup.notifs.fakeIos.subtitle": "Zeal te može obavijestiti kada primiš gotovinu ili trošiš svojom Visa karticom. To možeš promijeniti kasnije.", "setup.notifs.fakeIos.title": "Obavijesti o plaćanjima i dolaznim prijenosima", "sign.PermitAllowanceItem.spendLimit": "Limit <PERSON>", "sign.ledger.subtitle": "Poslali smo zahtjev za transakciju na tvoj hardverski novčanik. Molimo, nastavi tamo.", "sign.ledger.title": "Potpiši na hardverskom novčaniku", "sign.passkey.subtitle": "Preglednik će zatražiti potpis pristupnim ključem. Molimo nastavi tamo.", "sign.passkey.title": "Odaberi pristupni ključ", "signal_aborted_for_uknown_reason.title": "Mrežni zahtjev je otkazan", "simulatedTransaction.BridgeTrx.info.title": "Most", "simulatedTransaction.CardTopUp.info.title": "Dodaj s<PERSON>stva na karticu", "simulatedTransaction.CardTopUpTrx.info.title": "<PERSON><PERSON><PERSON> gotovinu na karticu", "simulatedTransaction.NftCollectionApproval.approve": "Odobri NFT kolekciju", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "Do<PERSON>j na karticu", "simulatedTransaction.OrderEarnDepositBridge.title": "Uplati u Earn", "simulatedTransaction.P2PTransaction.info.title": "Pošalji", "simulatedTransaction.PermitSignMessage.title": "Dozvola", "simulatedTransaction.SingleNftApproval.approve": "Odobri NFT", "simulatedTransaction.UnknownSignMessage.title": "Potpiši", "simulatedTransaction.Withdrawal.info.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.approval.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.approve.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.p2p.info.account": "<PERSON>a", "simulatedTransaction.p2p.info.unlabelledAccount": "Novčanik bez oznake", "simulatedTransaction.unknown.info.receive": "<PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.info.send": "Pošalji", "simulatedTransaction.unknown.using": "<PERSON><PERSON><PERSON><PERSON><PERSON> {app}", "simulation.approval.modal.text": "<PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> do<PERSON> određenoj aplikaciji/pametnom ugovoru da koristi tvoje tokene ili NFT-ove u budućim transakcijama.", "simulation.approval.modal.title": "<PERSON>to su odobrenja?", "simulation.approval.spend-limit.label": "Limit <PERSON>", "simulation.approve.footer.for": "<PERSON>a", "simulation.approve.unlimited": "Neo<PERSON><PERSON><PERSON><PERSON>", "simulationNotAvailable.title": "Nepoznata radnja", "smart-wallet-activation-view.on": "Na", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password može blokirati pristup tvom novčaniku", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Koristi Apple ili Google za sigurno postavljanje Zeala", "smart-wallet.passkey-notice.title": "Izbjegavaj 1Password", "spend-limits.high.modal.text": "Postavi limit potrošnje blizu iznosa tokena koje ćeš stvarno koristiti s aplikacijom ili pametnim ugovorom. Visoki limiti su rizični i mogu olakšati prevarantima krađu tvojih tokena.", "spend-limits.high.modal.text_sign_message": "Limit potrošnje trebao bi biti blizu iznosa tokena koje ćeš stvarno koristiti s aplikacijom ili pametnim ugovorom. Visoki limiti su rizični i mogu prevarantima olakšati krađu tvojih tokena.", "spend-limits.high.modal.title": "Visok limit potrošnje", "spend-limits.modal.text": "Limit potrošnje je broj tokena koje aplikacija može koristiti u tvoje ime. Ovaj limit možeš promijeniti ili ukloniti bilo kada. <PERSON><PERSON>, drži limite potrošnje blizu iznosa tokena koje ćeš stvarno koristiti s aplikacijom.", "spend-limits.modal.title": "Što je limit potrošnje?", "spent-limit-info.modal.description": "Limit potrošnje određuje koliko tokena aplikacija može koristiti u tvoje ime. Ovaj limit možeš promijeniti ili ukloniti bilo kada. <PERSON><PERSON>, drži limite potrošnje blizu iznosa tokena koje ćeš stvarno koristiti s aplikacijom.", "spent-limit-info.modal.title": "Što je limit potrošnje?", "sswaps-io.transfer-provider": "Pružatelj usluge prijenosa", "storage.accountDetails.activateWallet": "Aktiviraj novčanik", "storage.accountDetails.changeWalletLabel": "Promijeni oznaku novčanika", "storage.accountDetails.deleteWallet": "Ukloni novčanik", "storage.accountDetails.setup_recovery_kit": "Komplet za oporavak", "storage.accountDetails.showPrivateKey": "Prikaži privatni ključ", "storage.accountDetails.showWalletAddress": "Prikaži adresu novčanika", "storage.accountDetails.smartBackup": "Sigurnosna kopija i oporavak", "storage.accountDetails.viewSsecretPhrase": "<PERSON><PERSON><PERSON><PERSON> tajnu frazu", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets?", "storage.manageAccounts.title": "Novčanici", "submit-userop.progress.text": "<PERSON><PERSON><PERSON>", "submit.error.amount_high": "<PERSON>znos je previsok", "submit.error.amount_hight": "<PERSON>znos je previsok", "submit.error.amount_low": "<PERSON><PERSON><PERSON> je prenizak", "submit.error.amount_required": "Unesi iznos", "submit.error.maximum_number_of_characters_exceeded": "<PERSON><PERSON><PERSON> por<PERSON>", "submit.error.not_enough_balance": "Nedovoljno sredstava", "submit.error.recipient_required": "Primatelj je obavezan", "submit.error.routes_not_found": "<PERSON><PERSON> ruta", "submitSafeTransaction.monitor.title": "Rezultat transakcije", "submitSafeTransaction.sign.title": "Rezultat transakcije", "submitSafeTransaction.state.sending": "<PERSON><PERSON><PERSON>", "submitSafeTransaction.state.sign": "<PERSON><PERSON><PERSON><PERSON>", "submitSafeTransaction.submittingToRelayer.title": "Rezultat transakcije", "submitTransaction.cancel": "Odustani", "submitTransaction.cancel.attemptingToStop": "Pokušaj zaustavljanja", "submitTransaction.cancel.failedToStop": "Zaustavljanje <PERSON>", "submitTransaction.cancel.stopped": "Zaustavljeno", "submitTransaction.cancel.title": "<PERSON><PERSON>", "submitTransaction.failed.banner.description": "Mreža je neočekivano otkazala ovu transakciju. Pokušaj ponovno ili nas kontaktiraj.", "submitTransaction.failed.banner.title": "Transakcija neuspješna", "submitTransaction.failed.execution_reverted.title": "Došlo je do pogreške u aplikaciji", "submitTransaction.failed.execution_reverted_without_message.title": "Došlo je do pogreške u aplikaciji", "submitTransaction.failed.out_of_gas.description": "Mreža je otkazala transakciju jer je potrošeno više mrežnih naknada od očekivanog", "submitTransaction.failed.out_of_gas.title": "Mrežna pog<PERSON>", "submitTransaction.sign.title": "Rezultat transakcije", "submitTransaction.speedUp": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue": "Dodano u red čekanja", "submitTransaction.state.addedToQueue.short": "U redu", "submitTransaction.state.cancelled": "Zaustavljeno", "submitTransaction.state.complete": "{currencyCode} dodano u Zeal", "submitTransaction.state.complete.subtitle": "Provjeri svoj Zeal portfelj", "submitTransaction.state.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.failed": "Neuspješno", "submitTransaction.state.includedInBlock": "Uključeno u blok", "submitTransaction.state.includedInBlock.short": "U bloku", "submitTransaction.state.replaced": "Zamijenjeno", "submitTransaction.state.sendingToNetwork": "Slanje na mrežu", "submitTransaction.stop": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.submit": "Potvrdi", "submitted-user-operation.state.bundled": "U redu čekanja", "submitted-user-operation.state.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.failed": "Neuspješno", "submitted-user-operation.state.pending": "Prosljeđivanje", "submitted-user-operation.state.rejected": "Odbijeno", "submittedTransaction.failed.title": "Transakcija neuspješna", "success_splash.card_activated": "Kartica aktivirana", "supportFork.give-feedback.title": "Pošalji povratne informacije", "supportFork.itercom.description": "Zeal odgovara na pitanja o pologu, <PERSON><PERSON><PERSON>, nagradama i svemu ostalom", "supportFork.itercom.title": "Pitanja o novčaniku", "supportFork.title": "Zatraži pomoć za", "supportFork.zendesk.subtitle": "Gnosis Pay odgovara na pitanja o plaćanju karticom, provjeri identiteta ili povratu sredstava", "supportFork.zendesk.title": "Plaćanje karticom i identitet", "supported-networks.ethereum.warning": "<PERSON><PERSON><PERSON> naknade", "supportedNetworks.networks": "Podržane mreže", "supportedNetworks.oneAddressForAllNetworks": "Jedna adresa za sve mreže", "supportedNetworks.receiveAnyAssets": "Primaj s podržanih mreža na istu adresu.", "swap.form.error.no_routes_found": "<PERSON><PERSON> prona<PERSON><PERSON> ruta", "swap.form.error.not_enough_balance": "Nedovoljno stanje", "swaps-io-details.bank.serviceProvider": "Pružate<PERSON>j <PERSON>", "swaps-io-details.details.processing": "Obrada", "swaps-io-details.pending": "Na čekanju", "swaps-io-details.rate": "<PERSON><PERSON><PERSON>", "swaps-io-details.serviceProvider": "Pružate<PERSON>j <PERSON>", "swaps-io-details.transaction.from.processing": "Započeta transakcija", "swaps-io-details.transaction.networkFees": "<PERSON><PERSON><PERSON><PERSON> naknade", "swaps-io-details.transaction.state.completed-transaction": "Završena transakcija", "swaps-io-details.transaction.state.started-transaction": "Započeta transakcija", "swaps-io-details.transaction.to.processing": "Završena transakcija", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Polog bi trebao uskoro biti završen. Kinetex još uvijek obrađuje tvoju transakciju.", "swapsIO.monitoring.awaitingLiqSend.title": "<PERSON><PERSON><PERSON>", "swapsIO.monitoring.awaitingRecive.title": "Prosljeđivanje", "swapsIO.monitoring.awaitingSend.title": "Na čekanju", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Tokeni su poslani Kinetexu, ali će uskoro biti vraćeni. Kinetex nije mogao dovršiti odredišnu transakciju.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Tokeni nisu preneseni zbog nepoznate pogreške. Molimo, pokušaj ponovno.", "swapsIO.monitoring.cancelledNoSlash.title": "Tokeni vraćeni", "swapsIO.monitoring.cancelledSlashed.subtitle": "Tokeni su vraćeni. Kinetex nije mogao dovršiti odredišnu transakciju.", "swapsIO.monitoring.cancelledSlashed.title": "Tokeni vraćeni", "swapsIO.monitoring.completed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "taker-metadata.earn": "Zaradi u digitalnom USD-u uz Sky", "taker-metadata.earn.aave": "Zaradi u digitalnom EUR-u uz Aave", "taker-metadata.earn.aave.cashout24": "<PERSON><PERSON><PERSON><PERSON> is<PERSON>, 24/7", "taker-metadata.earn.aave.trusted": "Povjerenje od 27 mlrd $, 2+ godine", "taker-metadata.earn.aave.yield": "Prinos se obračunava svake sekunde", "taker-metadata.earn.chf": "Zaradi u digitalnom CHF-u", "taker-metadata.earn.chf.cashout24": "<PERSON><PERSON><PERSON><PERSON> odmah, 24/7", "taker-metadata.earn.chf.trusted": "Povjereno 28 mil. Fr.", "taker-metadata.earn.chf.yield": "Prinos se obračunava svake sekunde", "taker-metadata.earn.usd.cashout24": "<PERSON><PERSON><PERSON><PERSON> is<PERSON>, 24/7", "taker-metadata.earn.usd.trusted": "Povjerenje od 10,7 mlrd $, 5+ godina", "taker-metadata.earn.usd.yield": "Prinos se obračunava svake sekunde", "test": "<PERSON><PERSON>", "to.titile": "<PERSON>a", "token.groupHeader.cashback": "Povrat novca", "token.groupHeader.title": "<PERSON><PERSON><PERSON>", "token.groupHeader.titleWithSum": "<PERSON><PERSON><PERSON> {sum}", "token.hidden_tokens.page.title": "Skriveni tokeni", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "<PERSON><PERSON><PERSON> token", "token.widget.cashback_empty": "<PERSON><PERSON> transakcija", "token.widget.emptyState": "Nema tokena u novčaniku", "tokens.cash": "Gotovina", "top-up-card-from-earn-view.approve.for": "<PERSON>a", "top-up-card-from-earn-view.approve.into": "U", "top-up-card-from-earn-view.swap.from": "Od", "top-up-card-from-earn-view.swap.to": "<PERSON>a", "top-up-card-from-earn-view.withdraw.to": "<PERSON>a", "top-up-card-from-earn.trx.title.approval": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn.trx.title.swap": "Do<PERSON>j na karticu", "top-up-card-from-earn.trx.title.withdrawal": "<PERSON><PERSON><PERSON><PERSON> s <PERSON>n", "topUpDapp.connectWallet": "Poveži novčanik", "topup-fee-breakdown.bungee-fee": "Naknada vanjskog pružatelja usluga", "topup-fee-breakdown.header": "Naknada za transakciju", "topup-fee-breakdown.network-fee": "<PERSON><PERSON><PERSON><PERSON>", "topup-fee-breakdown.total-fee": "<PERSON><PERSON><PERSON><PERSON> naknada", "topup.continue-in-wallet": "Nastavi u svom novčaniku", "topup.send.title": "Pošalji", "topup.submit-transaction.close": "Zatvori", "topup.submit-transaction.sent-to-wallet": "Po<PERSON><PERSON>ji {amount}", "topup.to": "<PERSON>a", "topup.transaction.complete.close": "Zatvori", "topup.transaction.complete.try-again": "Pokušaj ponovno", "transaction-request.nonce-too-low.modal.button-text": "Zatvori", "transaction-request.nonce-too-low.modal.text": "Transakcija s istim serijskim brojem (nonce) već je dovršena, stoga više ne možeš poslati ovu transakciju. To se može dogoditi ako izvršavaš transakcije jednu za drugom ili ako pokušavaš ubrzati ili otkazati transakciju koja je već dovršena.", "transaction-request.nonce-too-low.modal.title": "Transakcija s istim nonceom je dovršena", "transaction-request.replaced.modal.button-text": "Zatvori", "transaction-request.replaced.modal.text": "Ne možemo pratiti status ove transakcije. Ili je zamijenjena drugom transakcijom ili RPC čvor ima poteškoća.", "transaction-request.replaced.modal.title": "<PERSON><PERSON> moguće pronaći status transakcije", "transaction.activity.details.modal.close": "Zatvori", "transaction.cancel_popup.cancel": "Ne, pričekaj", "transaction.cancel_popup.confirm": "Da, zaustavi", "transaction.cancel_popup.description": "Za zaustavljanje moraš platiti novu mrežnu naknadu umjesto izvorne naknade od {oldFee}", "transaction.cancel_popup.description_without_original": "Za zaustavljanje moraš platiti novu mrežnu naknadu", "transaction.cancel_popup.not_supported.subtitle": "Zaustavljanje transakcija nije podržano na {network}", "transaction.cancel_popup.not_supported.title": "<PERSON><PERSON>", "transaction.cancel_popup.stopping_fee": "Mrežna naknada za zaustavljanje", "transaction.cancel_popup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>?", "transaction.in-progress": "U tijeku", "transaction.inProgress": "U tijeku", "transaction.speed_up_popup.cancel": "Ne, pričekaj", "transaction.speed_up_popup.confirm": "Da, ubrzaj", "transaction.speed_up_popup.description": "<PERSON><PERSON> <PERSON>, tre<PERSON><PERSON> platiti novu mrežnu naknadu umjesto izvorne naknade od {amount}", "transaction.speed_up_popup.description_without_original": "<PERSON><PERSON>, tre<PERSON>š platiti novu mrežnu naknadu", "transaction.speed_up_popup.seed_up_fee_title": "Mrežna naknada za ubrzanje", "transaction.speed_up_popup.title": "Ubrzati transakciju?", "transaction.speedup_popup.not_supported.subtitle": "Ubrzavanje transakcija nije podržano na {network}", "transaction.speedup_popup.not_supported.title": "<PERSON><PERSON>", "transaction.subTitle.failed": "Neuspješno", "transactionDetails.cashback.not-qualified": "<PERSON><PERSON>", "transactionDetails.cashback.paid": "{amount} isp<PERSON>ćeno", "transactionDetails.cashback.pending": "{amount} na čekanju", "transactionDetails.cashback.title": "Povrat novca", "transactionDetails.cashback.unknown": "Nepoznato", "transactionDetails.cashback_estimate": "Procjena povrata novca", "transactionDetails.category": "Kategorija", "transactionDetails.exchangeRate": "<PERSON><PERSON><PERSON>", "transactionDetails.location": "Lokacija", "transactionDetails.payment-approved": "Plaćanje o<PERSON>no", "transactionDetails.payment-declined": "Plaćanje odbijeno", "transactionDetails.payment-reversed": "Plać<PERSON><PERSON> s<PERSON>", "transactionDetails.recharge.amountSentFromEarn.title": "Iznos poslan s Earn računa", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "Nadopunjeno na karticu", "transactionDetails.recharge.rate.title": "<PERSON><PERSON><PERSON>", "transactionDetails.recharge.transactionId.title": "ID transakcije", "transactionDetails.refund": "<PERSON><PERSON><PERSON>", "transactionDetails.reversal": "Storniranje", "transactionDetails.transactionCurrency": "Valuta transakcije", "transactionDetails.transactionId": "ID transakcije", "transactionDetails.type": "Transakcija", "transactionRequestWidget.approve.subtitle": "Za {target}", "transactionRequestWidget.p2p.subtitle": "Za {target}", "transactionRequestWidget.unknown.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> {target}", "transactionSafetyChecksPopup.title": "Sigurnosne provjere transakcije", "transactions.main.activity.title": "Aktivnost", "transactions.page.hiddenActivity.title": "Skrivena aktivnost", "transactions.page.title": "Aktivnost", "transactions.viewTRXHistory.emptyState": "<PERSON><PERSON> transakcija", "transactions.viewTRXHistory.errorMessage": "Nismo uspjeli učitati tvoju povijest transakcija", "transactions.viewTRXHistory.hidden.emptyState": "<PERSON><PERSON> s<PERSON>", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Aktivnost nije podržana za testne mreže", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Aktivnost nije podržana za testne mreže{br}<link>Idi na block explorer</link>", "transfer_provider": "Pružatelj usluge prijenosa", "transfer_setup_with_different_wallet.subtitle": "Bankovni prijenosi postavljeni su s drugim novčanikom. Samo jedan novčanik može biti povezan za prijenose.", "transfer_setup_with_different_wallet.swtich_and_continue": "Promijeni i nastavi", "transfer_setup_with_different_wallet.title": "Promijeni novčanik", "tx-sent-to-wallet.button": "Zatvori", "tx-sent-to-wallet.subtitle": "<PERSON><PERSON><PERSON> u {wallet}", "unblockProviderInfo.fees": "Dobivaš najniže moguće naknade: 0% do 5 tisuća $ mjesečno i 0,2% iznad toga.", "unblockProviderInfo.registration": "Unblock je registriran i ovlašten od FNTT-a za VASP usluge te je registrirani MSB pružatelj kod US Fincena. <link>Saznaj više</link>", "unblockProviderInfo.selfCustody": "Digitalni novac koji primaš je u tvom privatnom vlasništvu i nitko drugi nema kontrolu nad tvojom imovinom.", "unblock_invalid_faster_payment_configuration.subtitle": "Bankovni račun koji si unio ne podržava europske SEPA prijenose ni UK Faster Payments. Molimo unesi drugi račun.", "unblock_invalid_faster_payment_configuration.title": "Potreban je drugi račun", "unknownTransaction.primaryText": "Transakcija karticom", "unsupportedCountry.subtitle": "Bankovni prijenosi još nisu dostupni u tvojoj zemlji.", "unsupportedCountry.title": "Nedostupno u {country}", "update-app-popup.subtitle": "Najnovije ažuriranje puno je ispravaka, značajki i više čarolije. Ažuriraj na najnoviju verziju i unaprijedi svoj Zeal.", "update-app-popup.title": "<PERSON><PERSON><PERSON><PERSON>", "update-app-popup.update-now": "<PERSON><PERSON><PERSON><PERSON> sada", "user_associated_with_other_merchant.subtitle": "Ovaj novčanik ne možeš koristiti za bankovne prijenose. Koristi drugi novčanik ili nam se javi na Discord za podršku i novosti.", "user_associated_with_other_merchant.title": "Novčanik se ne može koristiti", "user_associated_with_other_merchant.try_with_another_wallet": "Probaj drugi novčanik", "user_email_already_exists.subtitle": "Već si postavio bankovni prijenos s drugim novčanikom. Pokušaj ponovno s novčanikom koji si ranije koristio.", "user_email_already_exists.title": "Prijenosi postavljeni s drugim novčanikom", "user_email_already_exists.try_with_another_wallet": "Pokušaj drugi novčanik", "validation.invalid.iban": "Nevažeći IBAN", "validation.required": "Obavezno", "validation.required.first_name": "<PERSON>me je o<PERSON>no", "validation.required.iban": "IBAN je o<PERSON>", "validation.required.last_name": "Prezime je obavezno", "verify-passkey.cta": "Potvrdi pristupni ključ", "verify-passkey.subtitle": "Potvrdi da je tvoj pristupni ključ stvoren i ispravno osiguran.", "verify-passkey.title": "Potvrdi pristupni ključ", "view-cashback.cashback-next-cycle": "Stopa povrata novca za {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "Uplati za povrat novca", "view-cashback.pending": "{money} Na čekanju", "view-cashback.pending-rewards.not_paid": "Primitak za {days}d", "view-cashback.pending-rewards.paid": "Primljeno ovaj tjedan", "view-cashback.received-rewards": "Primljene nagrade", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Nepotvrđena p<PERSON>anja", "view-cashback.upcoming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {money}", "virtual-card-order.configure-safe.loading-text": "Izrada kartice", "virtual-card-order.create-order.loading-text": "Aktivacija kartice", "virtual-card-order.create-order.success-text": "Kartica aktivirana", "virtualCard.activateCard": "<PERSON><PERSON><PERSON><PERSON><PERSON> karticu", "walletDeleteConfirm.main_action": "Ukloni", "walletDeleteConfirm.subtitle": "<PERSON><PERSON> ga ponovno uvesti da bi vidio portfelj ili obavljao transakcije", "walletDeleteConfirm.title": "Ukloniti novčanik?", "walletSetting.header": "Postavke novčanika", "wallet_connect.connect.cancel": "Odustani", "wallet_connect.connect.connect_button": "<PERSON><PERSON><PERSON><PERSON>", "wallet_connect.connect.title": "Poveži se", "wallet_connect.connected.title": "<PERSON><PERSON><PERSON>", "wallet_connect_add_chain_missing.title": "Mreža nije podržana", "wallet_connect_proposal_expired.title": "Veza je istekla", "withdraw": "<PERSON><PERSON><PERSON><PERSON>", "withdraw.confirmation.close": "Odustani", "withdraw.confirmation.continue": "Potvrdi", "withdrawal_request.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withdrawal_request.pending": "Na čekanju", "zeal-dapp.connect-wallet.cta.primary.connecting": "Povezivanje...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Poveži", "zeal-dapp.connect-wallet.cta.secondary": "Odustani", "zeal-dapp.connect-wallet.title": "Poveži novčanik za nastavak", "zealSmartWalletInfo.gas": "Plaćaj 'gas' s mnogo tokena; koristi popularne ERC20 tokene na podržanim lancima za plaćanje mrežnih naknada, a ne samo izvorne tokene.", "zealSmartWalletInfo.recover": "Bez tajnih fraza; Oporavi novčanik pomoću biometrijskog pristupnog ključa iz svog upravitelja lozinki, iClouda ili Google računa.", "zealSmartWalletInfo.selfCustodial": "Potpuno privatan; Potpisi pristupnim ključem provjeravaju se na lancu (on-chain) kako bi se smanjile centralizirane ovisnosti.", "zealSmartWalletInfo.title": "O Zeal Smart Wallets", "zeal_a_rewards_already_claimed_error.title": "Nagrada je već preuzeta", "zwidget.minimizedDisconnected.label": "Zeal o<PERSON>n"}