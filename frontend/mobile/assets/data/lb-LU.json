{"Account.ListItem.details.label": "Detailer", "AddFromAddress.success": "Portmonni g<PERSON>äichert", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{<PERSON>g <PERSON>} one{{count} <PERSON><PERSON><PERSON>} other{{count} Portmonnien}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Geheime Saz {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "<PERSON><PERSON><PERSON> nei Portmonnie mat engem vun denge bestoende geheime Sätz", "AddFromExistingSecretPhrase.SelectPhrase.title": "Wiel e geheime Saz aus", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "<PERSON><PERSON><PERSON> geheime Saz ka vill Portmonnie sécheren. Wiel déi aus, déi s de benotze w<PERSON>ls.", "AddFromExistingSecretPhrase.WalletSelection.title": "Schnell e Portmonni bäisetzen", "AddFromExistingSecretPhrase.success": "Portmonnien op Zeal bäigesat", "AddFromHardwareWallet.subtitle": "Wiel däin Hardware-Portmonni fir dech mat Zeal ze verbannen", "AddFromHardwareWallet.title": "Hardware-Portmonni", "AddFromNewSecretPhrase.WalletSelection.subtitle": "<PERSON><PERSON> d'Portmonnien aus, déi s du wëlls importéieren", "AddFromNewSecretPhrase.WalletSelection.title": "Portmonnien importéieren", "AddFromNewSecretPhrase.accounts": "Portmonnien", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "E geheime Saz funktionéiert wéi e Schlësselrank fir Millioune Portmonnien, jidderee mat engem eegene private Schlëssel.{br}{br}Du kanns elo esou vill Portmonnie wéi s de brauchs importéieren oder spéider méi dobäisetzen.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Geheime Saz Portmonnien", "AddFromNewSecretPhrase.subtitle": "<PERSON><PERSON><PERSON> däi geheime Saz mat <PERSON>scherai<PERSON> getrennt an", "AddFromNewSecretPhrase.success_secret_phrase_added": "Geheime Saz dobäigesat 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Port<PERSON><PERSON><PERSON> zu Z<PERSON> dobäigesat", "AddFromNewSecretPhrase.wallets": "Portmonnien", "AddFromPrivateKey.subtitle": "G<PERSON><PERSON> d<PERSON>i private <PERSON><PERSON><PERSON><PERSON> an", "AddFromPrivateKey.success": "Private Schlëssel dobäigesat 🎉", "AddFromPrivateKey.title": "<PERSON><PERSON><PERSON> restau<PERSON>", "AddFromPrivateKey.typeOrPaste": "Hei tippen oder akoppéieren", "AddFromSecretPhrase.importWallets": "{count,plural,=0{<PERSON>g <PERSON>} one{Portmonni importen} other{{count} Portmonnien importen}}", "AddFromTrezor.AccountSelection.title": "Trezor-Portmonnien importéieren", "AddFromTrezor.hwWalletTip.subtitle": "En Hardware Portmonni huet Millioune vu Portmonnie mat verschiddenen Adressen. Du kanns elo esou vill importéiere wéi s de brauchs oder spéider méi dobäisetzen.", "AddFromTrezor.hwWalletTip.title": "Import vun Hardware Portmonnien", "AddFromTrezor.importAccounts": "{count,plural,=0{<PERSON><PERSON>} one{Importéier Port<PERSON>} other{Importéier {count} Portmonnien}}", "AddFromTrezor.success": "Port<PERSON><PERSON><PERSON> zu Z<PERSON> dobäigesat", "ApprovalSpenderTypeCheck.failed.subtitle": "Wahrscheinlech e Scam: d'Erlabnes sollt u Kontrakter goen", "ApprovalSpenderTypeCheck.failed.title": "D'Erlabnes geet un e Portmonni, net e Kontrakt", "ApprovalSpenderTypeCheck.passed.subtitle": "Normalerweis gëss du d'Erlabnes u Kontrakter", "ApprovalSpenderTypeCheck.passed.title": "D'Erlabnes geet un e Smart Contract", "BestReturns.subtitle": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> g<PERSON> dir dat bescht Resultat, mat alle K<PERSON> abegraff.", "BestReturnsPopup.title": "Bescht Resultater", "BlacklistCheck.Failed.subtitle": "Béisaarteg Meldunge vun <source></source>", "BlacklistCheck.Failed.title": "Site steet op enger Blacklist", "BlacklistCheck.Passed.subtitle": "<PERSON>g b<PERSON><PERSON><PERSON>g Meldunge vun <source></source>", "BlacklistCheck.Passed.title": "Site steet net op enger Blacklist", "BlacklistCheck.failed.statusButton.label": "Site gouf gemellt", "BridgeRoute.slippage": "Slippage {slippage}", "BridgeRoute.title": "<PERSON><PERSON><PERSON>-Ubidder", "CheckConfirmation.InProgress": "<PERSON><PERSON>tt verschafft...", "CheckConfirmation.success.splash": "Ofgeschloss", "ChooseImportOrCreateSecretPhrase.subtitle": "Importéier e geheime Saz oder erstell en neien", "ChooseImportOrCreateSecretPhrase.title": "Geheime Saz dobäisetzen", "ConfirmTransaction.Simuation.Skeleton.title": "Sécherheetskontrolle lafen...", "ConnectionSafetyCheckResult.passed": "Sécherheetscheck bestanen", "ContactGnosisPaysupport": "Kontaktéier Gnosis Pay", "CopyKeyButton.copied": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CopyKeyButton.copyYourKey": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON>", "CopyKeyButton.copyYourPhrase": "Deng Ph<PERSON> kopéieren", "DAppVerificationCheck.Failed.subtitle": "Site ass net gelëscht op <source></source>", "DAppVerificationCheck.Failed.title": "Site gouf net an App-Regëstere fonnt", "DAppVerificationCheck.Passed.subtitle": "Site ass gelëscht op <source></source>", "DAppVerificationCheck.Passed.title": "Site ass an App-Regësteren ze fannen", "DAppVerificationCheck.failed.statusButton.label": "Site net an App-Regëstere fonnt", "ERC20.tokens.emptyState": "<PERSON> hu keng Tokens fonnt", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Priority Fee", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "<PERSON><PERSON> {period}: t<PERSON><PERSON><PERSON> {from} an {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Basisgebühr: {baseFee} • Sécherheetspuffer: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Mir konnten déi aktuell Basisgebühr net ofruffen", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "<PERSON><PERSON>i héich wéi déi nächst Nonce. Bleift hänken", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "<PERSON><PERSON> kann net méi niddereg wéi déi aktuell Nonce gesat ginn", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "Mir konnten d'Priority Fee net berechnen", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Mir konnten déi aktuell Max. Gebühr net ofruffen", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "<PERSON><PERSON>", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "<PERSON><PERSON><PERSON> h<PERSON> b<PERSON>, bis d'Netzwierkskäschten erofginn", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "<PERSON><PERSON>", "EditFeeModal.Custom.gasLimit.title": "Gas-Limit {gasLimit}", "EditFeeModal.Custom.title": "Erweidert Astellungen", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "<PERSON><PERSON><PERSON> hänk<PERSON>, bis d'Basisgebühr erofgeet", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Niddereg Käschten. K<PERSON><PERSON> hänke bleiwen", "EditFeeModal.EditGasLimit.estimatedGas": "Geschate Gas: {estimated} • Sécherheetspuffer: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Manner wéi de geschate Limit. D'Transaktioun schléit feel", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Manner wéi de virgeschloene Limit. D'Transaktioun kéint feel schloen", "EditFeeModal.EditGasLimit.subtitle": "Lees de maximale Betrag u Gas fest, deen dës <PERSON> benotze soll. Deng <PERSON>, wanns du eng méi niddreg Limit festlees wéi néideg", "EditFeeModal.EditGasLimit.title": "Gas-Limit <PERSON>", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "<PERSON>er wéi de minimale Gaslimit: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "<PERSON><PERSON>i héich wéi déi nächst Nonce. Bleift hänken", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "Setz d'Nonce net méi déif wéi déi aktuell", "EditFeeModal.EditNonce.subtitle": "<PERSON>g <PERSON> ble<PERSON> hänk<PERSON>, wanns du eng aner wéi déi nächst Nonce agëss", "EditFeeModal.EditNonce.title": "<PERSON><PERSON>", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "Brauchs {amount} fir ofzes<PERSON><PERSON>n", "EditFeeModal.Header.Time.unknown": "Zäit onbekannt", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Max {fee}", "EditFeeModal.Header.fee.unknown": "Käsch<PERSON> onbekannt", "EditFeeModal.Header.subsequent_failed": "Schätzunge kéinten al sinn, lescht Aktualiséierung feelgeschloen", "EditFeeModal.Layout.Header.ariaLabel": "Aktuell Käschten", "EditFeeModal.MaxFee.subtitle": "D'Max-Kä<PERSON><PERSON> sinn dat Me<PERSON>, wat s du fir eng Transaktioun bezills, mee meeschtens bezills du déi virausgesote Käschten. <PERSON><PERSON><PERSON> extra P<PERSON><PERSON>, dass deng Transaktioun <PERSON>, och wann d'Netzwierk méi lues oder méi deier gëtt.", "EditFeeModal.MaxFee.title": "Max-Netzwierkskäschten", "EditFeeModal.SelectPreset.Time.unknown": "Zäit onbekannt", "EditFeeModal.SelectPreset.ariaLabel": "Käschte-Virauswiel treffen", "EditFeeModal.SelectPreset.fast": "<PERSON><PERSON><PERSON>", "EditFeeModal.SelectPreset.normal": "Normal", "EditFeeModal.SelectPreset.slow": "<PERSON><PERSON>", "EditFeeModal.ariaLabel": "Netzwierkskäschten änneren", "FailedSimulation.Confirmation.Item.subtitle": "<PERSON> haten en interne Feeler", "FailedSimulation.Confirmation.Item.title": "Transaktioun konnt net simuléiert ginn", "FailedSimulation.Confirmation.subtitle": "<PERSON> du sécher, dass du weiderfuere wëlls?", "FailedSimulation.Confirmation.title": "Du ënnerschreifs blann", "FailedSimulation.Title": "Simulat<PERSON>uns<PERSON><PERSON>r", "FailedSimulation.footer.subtitle": "<PERSON> haten en interne Feeler", "FailedSimulation.footer.title": "Transaktioun konnt net simuléiert ginn", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Du brauchs {amount} fir d'Transaktioun ofzeschécken", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Veraarbechtung kéint laang daueren", "FeeForecastWidget.networkFee": "Netzwierkskäschten", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Mir konnten d'Netzwierkskäschten net berechnen", "FeeForecastWidget.subsequentFailed.message": "Schätzunge kéinten al sinn, lescht Aktualiséierung ass feelgeschloen", "FeeForecastWidget.unknownDuration": "Onbekannt", "FeeForecastWidget.unknownFee": "Onbekannt", "GasCurrencySelector.balance": "Saldo: {balance}", "GasCurrencySelector.networkFee": "Netzwierkskäschten", "GasCurrencySelector.payNetworkFeesUsing": "Netzwierkskäschte bezuele mat", "GasCurrencySelector.removeDefaultGasToken.description": "Käschte vum gréisste Saldo bezuelen", "GasCurrencySelector.removeDefaultGasToken.title": "Automatesch Gestioun vun de Käschten", "GasCurrencySelector.save": "Späicheren", "GoogleDriveBackup.BeforeYouBegin.first_point": "<PERSON>n ech mäi <PERSON>-<PERSON><PERSON><PERSON> ve<PERSON>, verl<PERSON><PERSON>en ech mäi Verméige fir ëmmer", "GoogleDriveBackup.BeforeYouBegin.second_point": "Wann ech den Zougank zu mengem Google Drive verléieren oder meng Recovery-Datei änneren, verléieren ech mäi Verméige fir ëmmer", "GoogleDriveBackup.BeforeYouBegin.subtitle": "Lies an akzeptéier wgl. de folgende Punkt iwwer de private Portmonni:", "GoogleDriveBackup.BeforeYouBegin.third_point": "<PERSON>eal ka mir net hëllefen, m<PERSON><PERSON>-Passwuert oder mäin <PERSON> zu Google Drive ze restauréieren", "GoogleDriveBackup.BeforeYouBegin.title": "<PERSON><PERSON> <PERSON> de uf<PERSON>nks", "GoogleDriveBackup.loader.subtitle": "Bestäteg wgl. d'Ufro op Google Drive, fir deng Recovery-<PERSON><PERSON> er<PERSON>", "GoogleDriveBackup.loader.title": "Waarden op Bestätegung ...", "GoogleDriveBackup.success": "Backup gelongen 🎉", "MonitorOffRamp.overServiceTime": "Déi meescht Iwwerweisunge sinn innerhalb vun {estimated_time} ofgeschloss, mee heiansdo kënne se wéinst zousätzleche Kontrollen méi laang daueren. Dat ass normal an deng Sue si sécher, während dës Kontrollen duerchgefouert ginn.{br}{br}Wann d'Transaktioun net bannent {support_soft_deadline} ofgeschloss ass, w.e.g. {contact_support}", "MonitorOnRamp.contactSupport": "Support kontaktéieren", "MonitorOnRamp.from": "<PERSON><PERSON>", "MonitorOnRamp.fundsReceived": "<PERSON><PERSON> ukomm", "MonitorOnRamp.overServiceTime": "Déi meescht Iwwerweisunge si bannent {estimated_time} fäerd<PERSON>, mee heiansdo kann et duerch zousätzlech Kontrolle méi laang daueren. Dat ass normal an deng Sue si sécher, während dës Kontrolle gemaach ginn.{br}{br}<PERSON><PERSON> d'Transaktioun net bannent {support_soft_deadline} fäerdeg ass, w.e.g. {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Gëtt un däi Portmonni geschéckt", "MonitorOnRamp.to": "Un", "MonitorOnRamp.waitingForTransfer": "Waarden op deng Iwwerweisung", "NftCollectionCheck.failed.subtitle": "D'Kollektioun ass net verifizéiert op <source></source>", "NftCollectionCheck.failed.title": "D'Kollektioun ass net verifizéiert", "NftCollectionCheck.passed.subtitle": "D'Kollektioun ass verifizéiert op <source></source>", "NftCollectionCheck.passed.title": "D'Kollektioun ass verifiz<PERSON>iert", "NftCollectionInfo.entireCollection": "Ganz <PERSON>", "NoSigningKeyStore.createAccount": "<PERSON><PERSON> er<PERSON>", "NonceRangeError.biggerThanCurrent.message": "D'Transaktioun bleift hänken", "NonceRangeError.lessThanCurrent.message": "D'Transaktioun sch<PERSON> feel", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "<PERSON>'<PERSON><PERSON> ass méi héich wéi déi aktuell. Reduzéier se, fir datt se net hänke bleift.", "NonceRangeErrorPopup.biggerThanCurrent.title": "D'Transaktioun bleift hänken", "P2pReceiverTypeCheck.failed.subtitle": "Schécks du un déi richteg Adress?", "P2pReceiverTypeCheck.failed.title": "Den Empfänger ass e Smart Contract, kee Portmonni", "P2pReceiverTypeCheck.passed.subtitle": "Normalerweis schécks du Wäerter un aner Portmonnien", "P2pReceiverTypeCheck.passed.title": "Den Empfänger ass e Portmonni", "PasswordCheck.title": "<PERSON><PERSON><PERSON> aginn", "PasswordChecker.subtitle": "<PERSON><PERSON><PERSON> wgl. d<PERSON><PERSON>, fir ze confirm<PERSON>, dass du et bass", "PermitExpirationCheck.failed.subtitle": "Hal et kuerz an nëmmen esou laang wéi néideg", "PermitExpirationCheck.failed.title": "<PERSON>ang <PERSON>", "PermitExpirationCheck.passed.subtitle": "<PERSON><PERSON><PERSON> laang eng App deng Tokens benotzen däerf", "PermitExpirationCheck.passed.title": "Oflafzäit net ze laang", "PrivateKeyValidationError.moreThanMaximumWords": "<PERSON><PERSON> {count} <PERSON><PERSON><PERSON>", "PrivateKeyValidationError.notValidPrivateKey": "Dëst ass kee valabele private Sc<PERSON><PERSON><PERSON>", "PrivateKeyValidationError.secretPhraseIsInvalid": "Geheim Saz ass net valabel", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Wuert #{index} falsch geschriwwen oder net valabel", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} <PERSON><PERSON>} other{{count} <PERSON><PERSON><PERSON>}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Geheim Sätz a privat Schlëssele si verschlësselt a verloossen ni dësen Apparat.", "SecretPhraseReveal.header": "Geheime Saz opschreiwen", "SecretPhraseReveal.hint": "Deel däi Saz mat kengem. <PERSON><PERSON><PERSON> en sécher an offline", "SecretPhraseReveal.skip.subtitle": "Du kanns dat méi spéit maache<PERSON>, mee wanns de dësen Apparat verléiers, ier s de däi Saz opgeschriwwen hues, verléiers de all däi Verméigen op dësem Portmonni", "SecretPhraseReveal.skip.takeTheRisk": "Risk agoen", "SecretPhraseReveal.skip.title": "Saz opschreiwen iwwersprangen?", "SecretPhraseReveal.skip.writeDown": "Opschreiwen", "SecretPhraseReveal.skipForNow": "<PERSON><PERSON><PERSON>", "SecretPhraseReveal.subheader": "<PERSON>hreif e wgl. op a behaal en sécher offline. Duerno froe mir dech, en ze verifizéieren.", "SecretPhraseReveal.verify": "Bestätegen", "SelectCurrency.tokens": "Tokens", "SelectCurrency.tokens.emptyState": "<PERSON> hu keng Tokens fonnt", "SelectRoute.slippage": "Slippage {slippage}", "SelectRoutes.emptyState": "<PERSON> hu keng Route fir dë<PERSON> fonnt", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Port<PERSON><PERSON> verbannen", "SendERC20.labelAddress.inputPlaceholder": "Portmonni-Etikett", "SendERC20.labelAddress.subtitle": "<PERSON><PERSON><PERSON> d<PERSON> eng Eti<PERSON>, fir en spéider erëmzef<PERSON>n.", "SendERC20.labelAddress.title": "Dësem Portmonni eng Etikett ginn", "SendERC20.send_to": "<PERSON><PERSON><PERSON><PERSON>n un", "SendERC20.tokens": "Tokenen", "SendOrReceive.bankTransfer.primaryText": "Bankiwwerweisung", "SendOrReceive.bankTransfer.shortText": "<PERSON><PERSON><PERSON>, direkt On-Ramp an Off-Ramp", "SendOrReceive.bridge.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.bridge.shortText": "Tokenen tëscht Netzwierker iwwerdroen", "SendOrReceive.receive.primaryText": "Empfänken", "SendOrReceive.receive.shortText": "Tokenen oder Sammelstécker empfänken", "SendOrReceive.send.primaryText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SendOrReceive.send.shortText": "Tokenen un all Adress sch<PERSON>cken", "SendOrReceive.swap.primaryText": "Tauschen", "SendOrReceive.swap.shortText": "Tëscht Tokenen tauschen", "SendSafeTransaction.Confirm.loading": "<PERSON><PERSON>...", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "E Recovery-<PERSON><PERSON><PERSON> mat <PERSON><PERSON> ve<PERSON>", "SetupRecoveryKit.google.subtitle": "Synchronis<PERSON><PERSON><PERSON> {date}", "SetupRecoveryKit.google.title": "Google Drive Backup", "SetupRecoveryKit.subtitle": "Du brauchs op d'mannst ee <PERSON>, fir däi Kont ze restauréieren, wanns de Zeal desinstalléiers oder den Apparat wiessels", "SetupRecoveryKit.title": "<PERSON> <PERSON>", "SetupRecoveryKit.writeDown.subtitle": "Geheime Saz opschreiwen", "SetupRecoveryKit.writeDown.title": "<PERSON><PERSON>", "Sign.CheckSafeDeployment.activate": "Aktivéieren", "Sign.CheckSafeDeployment.subtitle": "Ier s du dech an enger App umells oder en Off-Chain-Message ënnerschreifs, muss du däin Apparat an dësem Netzwierk aktivéieren. <PERSON><PERSON><PERSON> g<PERSON>, nodeems du e Smart Wallet installéiert oder recuperéiert hues.", "Sign.CheckSafeDeployment.title": "Apparat an dësem Netzwierk aktivéieren", "Sign.Simuation.Skeleton.title": "<PERSON><PERSON>...", "SignMessageSafetyCheckResult.passed": "Sécherheetschecke bestanen", "SignMessageSafetyChecksPopup.title.permits": "Sécherheetschecke fir Permits", "SimulationFailedConfirmation.subtitle": "Mir hunn dës <PERSON>aktiou<PERSON> simuléiert an e <PERSON> fonnt, deen dozou fé<PERSON>t, dass se <PERSON>ch<PERSON>it. Du kanns dës Transaktioun of<PERSON>, mee si wäert warscheinlech feelen an du kéints deng Netzwierkskäschte verléieren.", "SimulationFailedConfirmation.title": "Transaktioun feelt warscheinlech", "SimulationNotSupported.Title": "Simulatioun net{br}ënnerstëtzt op{br}{network}", "SimulationNotSupported.footer.subtitle": "Du kanns dës <PERSON> trotzdem ofschécken", "SimulationNotSupported.footer.title": "Simulatioun net ënnerstëtzt", "SlippagePopup.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SlippagePopup.presetsHeader": "Swap-Slippage", "SlippagePopup.title": "Slippage-Astellungen", "SmartContractBlacklistCheck.failed.subtitle": "Béiswëlleg Meldunge vun <source></source>", "SmartContractBlacklistCheck.failed.title": "De Kontrakt ass op der schwaarzer Lëscht", "SmartContractBlacklistCheck.passed.subtitle": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> Meldunge vun <source></source>", "SmartContractBlacklistCheck.passed.title": "De Kontrakt ass net op der schwaarzer Lëscht", "SuspiciousCharactersCheck.Failed.subtitle": "<PERSON><PERSON><PERSON> ass eng gängeg Phishing-Taktik", "SuspiciousCharactersCheck.Failed.title": "Mir préiwe op gängeg Phishing-Musteren", "SuspiciousCharactersCheck.Passed.subtitle": "Mir préiwe op Phishing-V<PERSON>ich", "SuspiciousCharactersCheck.Passed.title": "<PERSON><PERSON> huet keng ongew<PERSON><PERSON><PERSON><PERSON> Zeechen", "SuspiciousCharactersCheck.failed.statusButton.label": "<PERSON><PERSON><PERSON><PERSON> huet onge<PERSON><PERSON><PERSON><PERSON><PERSON> Zeechen ", "TokenVerificationCheck.failed.subtitle": "Den Token ass net gelëscht op <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} ass net vu CoinGecko verifizéiert", "TokenVerificationCheck.passed.subtitle": "Den Token ass gelëscht op <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} ass vu CoinGecko verifizéiert", "TopupDapp.MonitorTransaction.success.splash": "Fäerdeg", "TransactionSafetyCheckResult.passed": "Sécherheetschecke bestanen", "TransactionSimulationCheck.failed.subtitle": "Feeler: {errorMessage}", "TransactionSimulationCheck.failed.title": "D'Transaktioun schléit warscheinlech feel", "TransactionSimulationCheck.passed.subtitle": "Simulatioun duerchgefouert mat <source></source>", "TransactionSimulationCheck.passed.title": "D'Virschau vun der Transaktioun war erfollegräich", "TrezorError.trezor_action_cancelled.action": "Zoumaachen", "TrezorError.trezor_action_cancelled.subtitle": "Du hues d'Transaktioun op dengem Hardware-Portmonni refuséiert", "TrezorError.trezor_device_used_elsewhere.action": "<PERSON><PERSON><PERSON> synchr<PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "<PERSON><PERSON>, dass all aner oppe <PERSON> zou sinn, a prob<PERSON><PERSON> nach eng <PERSON>, d<PERSON><PERSON> ze synchroniséieren", "TrezorError.trezor_method_cancelled.action": "<PERSON><PERSON><PERSON> synchr<PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "<PERSON><PERSON><PERSON><PERSON> dem Trezor w.e.g., Portmonnien op Zeal z'exportéieren", "TrezorError.trezor_permissions_not_granted.action": "<PERSON><PERSON><PERSON> synchr<PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "<PERSON><PERSON><PERSON> w.e.g. <PERSON>'Berechtegung, all Portmonnien ze gesinn", "TrezorError.trezor_pin_cancelled.action": "<PERSON><PERSON><PERSON> synchr<PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "Sessioun um Apparat ofgebrach", "TrezorError.trezor_popup_closed.action": "<PERSON><PERSON><PERSON> synchr<PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "<PERSON>or-<PERSON><PERSON> huet sech one<PERSON> z<PERSON>", "TrxLikelyToFail.lessThanEstimatedGas.message": "D'Transaktioun sch<PERSON> feel", "TrxLikelyToFail.lessThanMinimumGas.message": "D'Transaktioun sch<PERSON> feel", "TrxLikelyToFail.lessThanSuggestedGas.message": "Schléit warscheinlech feel", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "De Gas-Limit vun der Transaktioun ass ze niddreg. Erhé<PERSON>j de Gas-Limit op déi virgeschloe Limit, fir Feeler ze verhënneren.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "Transaktioun feelt warscheinlech", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "De Gas-Limit ass méi niddreg wéi de geschate Gas. Erhéij de Gas-Limit op déi virgeschloe Limit.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "Transaktioun feelt", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "Max-Basiskäschten si méi niddreg wéi déi aktuell Basiskäschten. Erhéij d'Max-Basiskäschten, fir ze verhënneren, dass d'Transaktioun hänke bleift.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "Transaktioun bleift hänken", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "D'Max-Käsch<PERSON> vun der Transaktioun sinn ze niddreg. Er<PERSON><PERSON><PERSON><PERSON> <PERSON>'<PERSON><PERSON>, fir ze verhënneren, dass d'Transaktioun hänke bleift.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "Transaktioun bleift hänken", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "D'Prioritéitskäschte si méi niddreg wéi recommandéiert. Erhéij d'Prioritéitskäschten, fir d'Transaktioun ze beschleunegen.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "Transaktioun kéint laang daueren", "UnsupportedMobileNetworkLayout.gotIt": "Verstanen!", "UnsupportedMobileNetworkLayout.subtitle": "Du kanns keng Transaktiounen oder Messagen am Netzwierk mat der ID {networkHexId} mat der mobiller Versioun vu Zeal maachen, nach net{br}{br}Wiessel op d'Browser-Erweiderung, fir an dësem Netzwierk Transaktiounen ze maachen, wärend mir dru schaffen, d'Ënnerstëtzung fir dëst Netzwierk bäizefügen 🚀", "UnsupportedMobileNetworkLayout.title": "Netzwierk gëtt fir déi mobil Versioun vu Zeal net ënnerstëtzt", "UnsupportedSafeNetworkLayout.subtitle": "Du kanns keng Transaktiounen maachen oder Messagen ënnerschreiwen op {network} mat engem Zeal Smart Wallet{br}{br}Wiessel op en ënnerstëtzt Netzwierk oder benotz e Legacy-Portmonni.", "UnsupportedSafeNetworkLayoutk.title": "Netzwierk gëtt fir Smart Wallet net ënnerstëtzt", "UserConfirmationPopup.goBack": "Ofbriechen", "UserConfirmationPopup.submit": "Weiderfueren", "ViewPrivateKey.header": "Private <PERSON><PERSON>", "ViewPrivateKey.hint": "Deel däi private <PERSON><PERSON><PERSON><PERSON> mat kengem. Be<PERSON><PERSON> en sécher an offline", "ViewPrivateKey.subheader.mobile": "Tipp fir däi private <PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewPrivateKey.subheader.web": "<PERSON><PERSON>, fir däi private <PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewPrivateKey.unblur.mobile": "<PERSON>ip<PERSON> fir unze<PERSON>sen", "ViewPrivateKey.unblur.web": "Hover fir unzeweisen", "ViewSecretPhrase.PasswordChecker.subtitle": "<PERSON><PERSON><PERSON> d<PERSON> an, fir d'Recovery-Datei ze verschlësselen. Du muss dech an Zukunft drun erënneren.", "ViewSecretPhrase.done": "Fäerdeg", "ViewSecretPhrase.header": "Geheime Saz", "ViewSecretPhrase.hint": "Deel däi Saz mat kengem. <PERSON><PERSON><PERSON> en sécher an offline", "ViewSecretPhrase.subheader.mobile": "Tipp fir däi geheime Saz unzeweisen", "ViewSecretPhrase.subheader.web": "<PERSON>ver, fir däi geheime Saz unzeweisen", "ViewSecretPhrase.unblur.mobile": "<PERSON>ip<PERSON> fir unze<PERSON>sen", "ViewSecretPhrase.unblur.web": "Hover fir unzeweisen", "account-details.monerium": "Iwwerweisunge gi mat Monerium gemaach, enger autoriséierter a reglementéierter EMI. <link>Méi gewuer ginn</link>", "account-details.unblock": "Iwwerweisunge lafen iwwer Unblock, en autoriséierten an ageschriwwenen Austausch- a Verwahrungsdéngschtleeschter. <link><PERSON><PERSON><PERSON> gewuer ginn</link>", "account-selector.empty-state": "<PERSON><PERSON> fonnt", "account-top-up.select-currency.title": "Tokens", "account.accounts_not_found": "Mir konnte keng Port<PERSON>nie fannen", "account.accounts_not_found_search_valid_address": "<PERSON> Portmonni ass net an denger L<PERSON>scht", "account.add.create_new_secret_phrase": "Geheime Saz erstellen", "account.add.create_new_secret_phrase.subtext": "En neie geheime Saz vun 12 Wierder", "account.add.fromRecoveryKit.fileNotFound": "Mir konnten däi Fichier net fannen", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "<PERSON><PERSON><PERSON><PERSON> nach eng Kéier", "account.add.fromRecoveryKit.fileNotFound.explanation": "<PERSON>ck wgl. no, ob s de dech mam richtege <PERSON> u<PERSON> hues, deen en <PERSON> Backup-<PERSON><PERSON><PERSON> huet", "account.add.fromRecoveryKit.fileNotValid": "Recovery-<PERSON><PERSON><PERSON> ass net valabel", "account.add.fromRecoveryKit.fileNotValid.explanation": "Mir hunn däi Fichier gepréift an et ass entweder net de richtegen Typ oder e gouf geännert", "account.add.import_secret_phrase": "Geheime Saz importéieren", "account.add.import_secret_phrase.subtext": "Erstallt op Zeal, Metamask oder aneren", "account.add.select_type.add_hardware_wallet": "Hardware Portmonni", "account.add.select_type.existing_smart_wallet": "Bestehende Smart Wallet", "account.add.select_type.private_key": "Private <PERSON><PERSON>", "account.add.select_type.seed_phrase": "Seed Phrase", "account.add.select_type.title": "Portmonni importéieren", "account.add.select_type.zeal_recovery_file": "Zeal <PERSON>", "account.add.success.title": "<PERSON><PERSON><PERSON> erstallt 🎉", "account.addLabel.header": "<PERSON><PERSON><PERSON> den<PERSON>m Portmonni en Numm", "account.addLabel.labelError.labelAlreadyExist": "Den Numm g<PERSON> et schonn. Probéier en aneren.", "account.addLabel.labelError.maxStringLengthExceeded": "Maximal Unzuel un Zeeche erreecht", "account.add_active_wallet.primary_text": "Portmonni do<PERSON>", "account.add_active_wallet.short_text": "<PERSON><PERSON><PERSON><PERSON>, verbannen oder importéieren", "account.add_from_ledger.success": "Portmonnien op Zeal bäigesat", "account.add_tracked_wallet.primary_text": "Read-only <PERSON><PERSON><PERSON>", "account.add_tracked_wallet.short_text": "Portfolio an Aktivitéit gesinn", "account.button.unlabelled-wallet": "Onbenannte Portmonni", "account.create_wallet": "<PERSON><PERSON><PERSON> er<PERSON>", "account.label.edit.title": "Numm vum Portmonni änneren", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON><PERSON> {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Recovery-<PERSON><PERSON><PERSON> ass net valabel", "account.recoveryKit.selectBackupFile.subtitle": "<PERSON>iel de Recovery-<PERSON><PERSON><PERSON>, deens de restauréiere wëlls", "account.recoveryKit.selectBackupFile.title": "Recovery-<PERSON><PERSON><PERSON>", "account.recoveryKit.success.recoveryFileFound": "Recovery-<PERSON><PERSON><PERSON> fonnt 🎉", "account.select_type_of_account.create_eoa.short": "Legacy-<PERSON><PERSON><PERSON> fir Experten", "account.select_type_of_account.create_eoa.title": "Seed-Phrase-<PERSON><PERSON><PERSON>", "account.select_type_of_account.create_safe_wallet.title": "Smart Wallet erstellen", "account.select_type_of_account.existing_smart_wallet": "Bestoende Smart Wallet", "account.select_type_of_account.hardware_wallet": "Hardware-Portmonni", "account.select_type_of_account.header": "Portmonni do<PERSON>", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Private Schlëssel / Seed Phrase", "account.select_type_of_account.read_only_wallet": "Read-only <PERSON><PERSON><PERSON>", "account.select_type_of_account.read_only_wallet.short": "Virschau fir all Portefeuille", "account.topup.title": "Suen op Zeal lueden", "account.view.error.refreshAssets": "Aktualiséieren", "account.widget.refresh": "Aktualiséieren", "account.widget.settings": "Astellungen", "accounts.view.copied-text": "Ko<PERSON><PERSON>iert {formattedAddress}", "accounts.view.copiedAddress": "Ko<PERSON><PERSON>iert {formattedAddress}", "action.accept": "Akzeptéieren", "action.accpet": "Akzeptéieren", "action.allow": "Erlaaben", "action.back": "<PERSON><PERSON><PERSON>", "action.cancel": "Ofbriechen", "action.card-activation.title": "<PERSON><PERSON> a<PERSON>", "action.claim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.close": "Zoumaachen", "action.complete-steps": "Ofschléissen", "action.confirm": "Bestätegen", "action.continue": "<PERSON><PERSON>", "action.copy-address-understand": "<PERSON> <PERSON> <PERSON><PERSON> k<PERSON>", "action.deposit": "<PERSON><PERSON><PERSON><PERSON>", "action.done": "Fäerdeg", "action.dontAllow": "Net erlaaben", "action.edit": "änner<PERSON>", "action.email-required": "E-Mail aginn", "action.enterPhoneNumber": "Telefonsnummer aginn", "action.expand": "Ausklappen", "action.fix": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.getStarted": "Lassleeën", "action.got_it": "Verstanen", "action.hide": "Verstoppen", "action.import": "Importéieren", "action.import-keys": "Schlësselen importéieren", "action.importKeys": "Schlësselen importéieren", "action.minimize": "Miniméieren", "action.next": "<PERSON><PERSON>", "action.ok": "OK", "action.reduceAmount": "Op max. reduzéieren", "action.refreshWebsite": "Websäit aktualiséieren", "action.remove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action.remove-account": "<PERSON><PERSON>", "action.requestCode": "Code ufueren", "action.resend_code": "Code nach eemol sch<PERSON>cken", "action.resend_code_with_time": "Code nees s<PERSON> {time}", "action.retry": "Nach eng <PERSON><PERSON> pro<PERSON>en", "action.reveal": "<PERSON><PERSON>", "action.save": "Späicheren", "action.save_changes": "Späicher RPC", "action.search": "<PERSON><PERSON>", "action.seeAll": "Alles weisen", "action.select": "Auswielen", "action.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.skip": "<PERSON><PERSON>", "action.submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.understood": "<PERSON>ch verstinn", "action.update": "Aktualiséier", "action.update-gnosis-pay-owner.complete": "Ofschléissen", "action.zeroAmount": "<PERSON><PERSON> aginn", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Sammlerstécker", "action_bar_title.tokens": "Tokens", "action_bar_title.transaction_request": "Transaktiounsufro", "activate-monerium.loading": "Däi perséinleche Kont gëtt ageriicht", "activate-monerium.success.title": "Monerium aktivéiert", "activate-physical-card-widget.subtitle": "D'Liwwerung ka bis zu 3 Wochen daueren", "activate-physical-card-widget.title": "<PERSON><PERSON><PERSON><PERSON> aktivéieren", "activate-smart-wallet.title": "Portmonni aktivéieren", "active_and_tracked_wallets.title": "Zeal iwwerhëlt all deng Käschten op {network}, sou dass du gratis Transaktioune maache kanns!", "activity.approval-amount.revoked": "Zréckgezunn", "activity.approval-amount.unlimited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.approval.approved_for": "Geneemegt fir", "activity.approval.approved_for_with_target": "Guttgeheescht {approvedTo}", "activity.approval.revoked_for": "Zréckgezunn fir", "activity.bank.serviceProvider": "Déngschtleeschter", "activity.bridge.serviceProvider": "Déngschtleeschter", "activity.cashback.period": "Cashback-Period", "activity.filter.card": "<PERSON><PERSON>", "activity.rate": "<PERSON><PERSON>", "activity.receive.receivedFrom": "<PERSON><PERSON><PERSON> vum", "activity.send.sendTo": "Geschéckt un", "activity.smartContract.unknown": "Onbekannte Kontrakt", "activity.smartContract.usingContract": "Benotzt Kontrakt", "activity.subtitle.pending_timer": "{timerString} Am <PERSON>aang", "activity.title.arbitrary_smart_contract_interaction": "{function} op {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Onbekannt Kontrakt-Interaktioun", "activity.title.bridge.from": "<PERSON><PERSON><PERSON> vun {token}", "activity.title.bridge.to": "<PERSON><PERSON><PERSON> op {token}", "activity.title.buy": "Kaaf {asset}", "activity.title.card_owners_updated": "Kaartebesëtzer aktualiséiert", "activity.title.card_spend_limit_updated": "Kaartelimit festgeluecht", "activity.title.cashback_deposit": "Alog op <PERSON>", "activity.title.cashback_reward": "Cashback kritt", "activity.title.cashback_withdraw": "Ofhiewe vum Cashback", "activity.title.claimed_reward": "<PERSON><PERSON><PERSON><PERSON> gefrot", "activity.title.deployed_smart_wallet_gnosis": "<PERSON><PERSON>", "activity.title.deposit_from_bank": "Alog vun der Bank", "activity.title.deposit_into_card": "<PERSON> a<PERSON>n", "activity.title.deposit_into_earn": "Alog op {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} op {smartContract}", "activity.title.from": "Vun {sender}", "activity.title.pendidng_areward_claim": "<PERSON>ounung ufroen", "activity.title.pendidng_breward_claim": "<PERSON>ounung ufroen", "activity.title.recharge_disabledh": "<PERSON><PERSON> oplueden desaktivéiert", "activity.title.recharge_set": "<PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>gel<PERSON>cht", "activity.title.recovered_smart_wallet_gnosis": "Installatioun neit <PERSON>", "activity.title.send_pending": "Un {receiver}", "activity.title.send_to_bank": "Un d'Bank", "activity.title.swap": "Ka<PERSON> {token}", "activity.title.to": "Un {receiver}", "activity.title.withdraw_from_card": "<PERSON><PERSON><PERSON><PERSON> vun der <PERSON>", "activity.title.withdraw_from_earn": "<PERSON><PERSON><PERSON><PERSON> vun {earn}", "activity.transaction.networkFees": "Netzwierkskäschten", "activity.transaction.state": "Transaktioun ofgeschloss", "activity.transaction.state.completed": "Ofgeschlossen Transaktioun", "activity.transaction.state.failed": "Transaktioun feelgeschloen", "add-account.section.import.header": "Importéieren", "add-another-card-owner": "En anere Kaartebesëtzer dobäisetzen", "add-another-card-owner.Recommended.footnote": "Setz däi Zeal Portmonni als zousätzleche Besëtzer vun denger Gnosis Pay Kaart dobäi", "add-another-card-owner.Recommended.primaryText": "Zeal bei Gnosis Pay dobäisetzen", "add-another-card-owner.recommended": "Recommandéiert", "add-owner.confirmation.subtitle": "Aus Sécherheetsgrënn daueren Ännerunge vun den Astellungen 3 Minutten. An där Zäit ass deng Ka<PERSON> temporär gespaart a keng Bezuelunge si méiglech.", "add-owner.confirmation.title": "<PERSON><PERSON> gëtt fir 3 Min. gespaart wärend d'Astellungen aktualiséiert ginn", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Portmonni ka net dobäigesat ginn, e g<PERSON>tt scho <PERSON>zt", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "<PERSON><PERSON><PERSON> probé<PERSON>en", "add.account.backup.decrypt.success": "<PERSON><PERSON><PERSON>au<PERSON>", "add.account.backup.password.passwordIncorrectMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> ass falsch", "add.account.backup.password.subtitle": "<PERSON><PERSON><PERSON>, dat s du benotzt hues fir däi Recovery Fichier ze verschlësselen.", "add.account.backup.password.title": "<PERSON><PERSON><PERSON> aginn", "add.account.google.login.subtitle": "Bestäteg wgl. d'Ufro op Google Drive fir däi Recovery Fichier ze synchroniséieren.", "add.account.google.login.title": "Waarden op d'Bestätegung...", "add.readonly.already_added": "Portmonni schonn derbäigesat", "add.readonly.continue": "<PERSON><PERSON>", "add.readonly.empty": "<PERSON><PERSON><PERSON> eng Adress oder ENS an", "addBankRecipient.title": "Bankdestinataire bäifügen", "add_funds.deposit_from_bank_account": "Vum Bankkonto abezuelen", "add_funds.from_another_wallet": "<PERSON>un engem anere Portmonni", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Mat Top-up dApp verbannen", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Verbann egal wéi ee Portmonni mat der Zeal Top-up dApp a schéck séier Fongen op däi Portmonni", "add_funds.from_crypto_wallet.header": "<PERSON>un engem anere Portmonni", "add_funds.from_crypto_wallet.header.show_wallet_address": "<PERSON><PERSON> uweisen", "add_funds.from_exchange.header": "<PERSON><PERSON> enger <PERSON>n", "add_funds.from_exchange.header.copy_wallet_address": "<PERSON><PERSON>-<PERSON><PERSON>", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance etc.", "add_funds.from_exchange.header.open_exchange": "Boursen-<PERSON>pp oder -Site opmaachen", "add_funds.from_exchange.header.selected_token": "<PERSON><PERSON><PERSON><PERSON> {token} un Zeal", "add_funds.from_exchange.header.selected_token.subtitle": "Op {network}", "add_funds.from_exchange.header.send_selected_token": "Ënnerstëtzten Token sch<PERSON>cken", "add_funds.from_exchange.header.send_selected_token.subtitle": "Ënnerstëtzten Token an Netzwierk auswielen", "add_funds.import_wallet": "Bestoende Krypto-Portmonni importéieren", "add_funds.title": "<PERSON><PERSON> däi <PERSON> op", "add_funds.transfer_from_exchange": "<PERSON><PERSON> enger Bourse iwwerweisen", "address.add.header": "Gesäi däi Portmonni op Zeal{br}am Nëmme-liese-Modus", "address.add.subheader": "<PERSON><PERSON><PERSON> deng <PERSON> oder ENS an, fir deng <PERSON>aben op allen EVM-Netzwierker op enger Plaz ze gesinn. Erstell oder importéier spéider méi Portmonnien.", "address_book.change_account.bank_transfers.header": "Bank-Destinatairen", "address_book.change_account.bank_transfers.primary": "Bank-Destinataire", "address_book.change_account.cta": "Portmonni verfollegen", "address_book.change_account.search_placeholder": "<PERSON><PERSON> b<PERSON> oder sichen", "address_book.change_account.tracked_header": "Read-only <PERSON><PERSON><PERSON><PERSON>", "address_book.change_account.wallets_header": "Aktiv Portmonnien", "app-association-check-failed.modal.cta": "Nach eng <PERSON><PERSON> pro<PERSON>en", "app-association-check-failed.modal.subtitle": "Probéier w.e.g. nach eng Kéier. Konnektivitéitsproblemer verursaache Retarden beim <PERSON> vun denge Passkeys. <PERSON><PERSON> <PERSON> bestoe bleift, start <PERSON>eal nei a probéier nach eng Kéier.", "app-association-check-failed.modal.subtitle.creation": "Probéier w.e.g. nach eng Kéier. Konnektivitéitsproblemer verursaache Retarden beim Erstelle vum Passkey. <PERSON><PERSON> de <PERSON> bestoe bleift, start <PERSON><PERSON> nei a probéier nach eng Kéier.", "app-association-check-failed.modal.title.creation": "<PERSON><PERSON><PERSON> konnt kee <PERSON> erstellen", "app-association-check-failed.modal.title.signing": "<PERSON><PERSON><PERSON> konnt d'Passkeys net lueden", "app.app_protocol_group.borrowed_tokens": "Geléinten Tokens", "app.app_protocol_group.claimable_amount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.health_rate": "Gesondheetsfaktor", "app.app_protocol_group.lending": "<PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.locked_tokens": "Gespaarten Tokens", "app.app_protocol_group.nfts": "Sammlerstécker", "app.app_protocol_group.reward_tokens": "Belounungs-Tokens", "app.app_protocol_group.supplied_tokens": "Ageluechten Tokens", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Vesting-Token", "app.appsGroupHeader.discoverMore": "Entdeck méi", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Sichen oder URL aginn", "app.error-banner.cory": "Feelerdonnéeë kop<PERSON>en", "app.error-banner.retry": "Nach eng <PERSON><PERSON> pro<PERSON>en", "app.list_item.rewards": "Belounungen {value}", "app.position_details.health_rate.description": "D'G<PERSON><PERSON><PERSON><PERSON> gëtt bere<PERSON>, and<PERSON><PERSON> de Betrag vun dengem Prêt duerch de Wäert vun denger Sécherheet gedeelt gëtt.", "app.position_details.health_rate.title": "Wat ass de <PERSON>eson<PERSON>äert?", "approval.edit-limit.label": "Ausgabelimit änneren", "approval.permit_info": "Permis-Informatioun", "approval.spend-limit.edit-modal.cancel": "Ofbriechen", "approval.spend-limit.edit-modal.limit-label": "Ausgabelimit", "approval.spend-limit.edit-modal.max-limit-error": "<PERSON>gepasst, héije Limit", "approval.spend-limit.edit-modal.revert": "Ännerungen zrécksetzen", "approval.spend-limit.edit-modal.set-to-unlimited": "Onlim<PERSON><PERSON>t astellen", "approval.spend-limit.edit-modal.submit": "Ännerunge späicheren", "approval.spend-limit.edit-modal.title": "Autorisatiounen änneren", "approval.spend_limit_info": "Wat ass en Ausgabelimit?", "approval.what_are_approvals": "Wat sinn Autorisatiounen?", "apps_list.page.emptyState": "Keng aktiv Appen", "backpace.removeLastDigit": "<PERSON><PERSON>", "backup-banner.backup_now": "Backup maachen", "backup-banner.risk_losing_funds": "Backup elo, fir kee Verloscht ze riskéieren", "backup-banner.title": "Portmonni net geséchert", "backupRecoverySmartWallet.noExportPrivateKeys": "Automatesche Backup: <PERSON><PERSON><PERSON> Smart Wallet gëtt als Passkey gespäichert - kee geheime Saz oder private <PERSON><PERSON><PERSON><PERSON> néideg.", "backupRecoverySmartWallet.safeContracts": "Multi-Schlëssel-Sécherheet: Zeal-Portmonnie lafen op Safe-Kontrakter, soudatt verschidden Apparater eng Transaktioun approuvéiere kënnen. Keng eenzeg Ausfallplaz.", "backupRecoverySmartWallet.security": "Verschidden Apparater: Du kanns däi Portmonni mat dem Passkey op verschiddenen Apparater benotzen. All Apparat kritt säin eegene private Sc<PERSON><PERSON><PERSON>.", "backupRecoverySmartWallet.showLocalPrivateKey": "Expert-Modus: Du kanns de private Schlëssel vun dësem Apparat exportéieren, en an engem anere Portmonni benotzen a dech verbannen op <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Private Schlëssel uweisen</Key>", "backupRecoverySmartWallet.storingKeys": "Cloud-synchroniséiert: <PERSON> g<PERSON>tt s<PERSON>cher an iCloud, am Google Password Manager oder an dengem Passwuert-Manager gespäichert.", "backupRecoverySmartWallet.title": "Smart Wallet Backup & Erhuelung", "balance-change.card.titile": "<PERSON><PERSON>", "balanceChange.pending": "<PERSON>", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Déngschtleeschter auswielen", "bank-transfer.change-deposit-receiver.subtitle": "Dëse Portmonni kritt all Bankalogen", "bank-transfer.change-deposit-receiver.title": "Empfänger-Portmonni festleeën", "bank-transfer.change-owner.subtitle": "<PERSON><PERSON><PERSON> ass fir d'Umeldung an d'Restauratioun vun dengem Bankiwwerweisungskont", "bank-transfer.change-owner.title": "Kontbesëtzer festleeën", "bank-transfer.configrm-change-deposit-receiver.subtitle": "All Bankalogen, déi s du un Z<PERSON> sch<PERSON>, ginn an dësem Portmonni empfaangen.", "bank-transfer.configrm-change-deposit-receiver.title": "Empfänger-Portmonni änneren", "bank-transfer.configrm-change-owner.subtitle": "Wëlls du de Kontbesëtzer sécher änneren? Dëse Portmonni gëtt fir d'Umeldung an d'Restauratioun vun dengem Bankiwwerweisungskont benotzt.", "bank-transfer.configrm-change-owner.title": "Kontbesëtzer änneren", "bank-transfer.deposit.widget.status.complete": "Fäerdeg", "bank-transfer.deposit.widget.status.funds_received": "<PERSON><PERSON> ukomm", "bank-transfer.deposit.widget.status.sending_to_wallet": "Gëtt un de Portmonni geschéckt", "bank-transfer.deposit.widget.status.transfer-on-hold": "Iwwerweisung op Waard", "bank-transfer.deposit.widget.status.transfer-received": "Gëtt un de Portmonni geschéckt", "bank-transfer.deposit.widget.subtitle": "{from} un {to}", "bank-transfer.deposit.widget.title": "Anzuelung", "bank-transfer.intro.bulletlist.point_1": "<PERSON><PERSON><PERSON> mat <PERSON>", "bank-transfer.intro.bulletlist.point_2": "Iwwerweis tëscht EUR/GBP a méi wéi 10 Tokens", "bank-transfer.intro.bulletlist.point_3": "0 % Käschte bis 5.000 $ pro Mount, duerno 0,2 %", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Gëtt un d'Bank geschéckt", "bank-transfer.withdrawal.widget.status.in-progress": "Iwwerweisung am Gaang", "bank-transfer.withdrawal.widget.status.on-hold": "Iwwerweisung suspendéiert", "bank-transfer.withdrawal.widget.status.success": "Ofgeschloss", "bank-transfer.withdrawal.widget.subtitle": "{from} un {to}", "bank-transfer.withdrawal.widget.title": "Ofhiewung", "bank-transfers.bank-account-actions.remove-this-account": "<PERSON><PERSON><PERSON>", "bank-transfers.bank-account-actions.switch-to-this-account": "Op dëse <PERSON> w<PERSON>en", "bank-transfers.deposit.fees-for-less-than-5k": "Käschte fir 5.000 $ oder manner", "bank-transfers.deposit.fees-for-more-than-5k": "Käschte fir méi wéi 5.000 $", "bank-transfers.set-receiving-bank.title": "Empfängerbank festleeën", "bank-transfers.settings.account_owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfers.settings.receiver_of_bank_deposits": "Empfänger vu Bankabezuelungen", "bank-transfers.settings.receiver_of_withdrawals": "Empfänger vun Ofhiewungen", "bank-transfers.settings.registered_email": "Registréiert E-Mail-Adress", "bank-transfers.settings.title": "Astellunge fir Bankiwwerweisungen", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} Kont", "bank-transfers.setup.bank-account": "Bankkonto", "bankTransfer.withdraw.max_loading": "Max.: {amount}", "bank_details_do_not_match.got_it": "Verstanen", "bank_details_do_not_match.subtitle": "De Sort Code an d'Kontosnummer stëmmen net iwwereneen. Kuck wgl. no, ob d'Detailer richteg agi sinn a probéier nach eng Kéier.", "bank_details_do_not_match.title": "Bankdetailer stëmmen net iwwereneen", "bank_tranfsers.select_country_of_residence.country_not_supported": "<PERSON><PERSON>, Bankiwwerweisunge ginn am Moment net an {country} ënnerstëtzt", "bank_transfer.deposit.bullet-point.open-your-bank-app": "<PERSON>ach deng Banken-App op", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Iwwerweis {fiatCurrencyCode} op däi Kont", "bank_transfer.deposit.header": "{fullName} seng perséinlech Kontodetailer&nbsp;", "bank_transfer.kyc_status_widget.subtitle": "Bankiwwerweisungen", "bank_transfer.kyc_status_widget.title": "Identitéit gëtt verifi<PERSON>", "bank_transfer.personal_details.date_of_birth": "Gebuertsdatum", "bank_transfer.personal_details.date_of_birth.invalid_format": "Den Datum ass ongëlteg", "bank_transfer.personal_details.date_of_birth.too_young": "<PERSON> muss mind<PERSON>ns 18 Joer al sinn", "bank_transfer.personal_details.first_name": "Virnumm", "bank_transfer.personal_details.last_name": "Familljennumm", "bank_transfer.personal_details.title": "<PERSON><PERSON>", "bank_transfer.reference.label": "Re<PERSON><PERSON>z (Optional)", "bank_transfer.reference_message": "Iwwer Zeal geschéckt", "bank_transfer.residence_details.address": "<PERSON><PERSON>", "bank_transfer.residence_details.city": "Stad", "bank_transfer.residence_details.country_of_residence": "Land vum Wunnsëtz", "bank_transfer.residence_details.country_placeholder": "Land", "bank_transfer.residence_details.postcode": "Postcode", "bank_transfer.residence_details.street": "Strooss", "bank_transfer.residence_details.your_residence": "<PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.continue": "<PERSON><PERSON>", "bank_transfers.choose-wallet.test": "Portmonni do<PERSON>", "bank_transfers.choose-wallet.warning.subtitle": "Du kanns nëmmen ee Portmonni gläichzäiteg verbannen. Du kanns de verbonnene Portmonni net méi änneren.", "bank_transfers.choose-wallet.warning.title": "Wiel däi Portmonni mat Suergfalt aus", "bank_transfers.choose_wallet.subtitle": "Wiel e Portmonni fir Bankiwwerweisungen. ", "bank_transfers.choose_wallet.title": "<PERSON><PERSON><PERSON>wi<PERSON>", "bank_transfers.continue": "<PERSON><PERSON>", "bank_transfers.currency_is_currently_not_supported": "<PERSON><PERSON>", "bank_transfers.deposit-header": "Abezuelung", "bank_transfers.deposit.account-name": "Kontosnumm", "bank_transfers.deposit.account-number-copied": "Kontosnummer kopéiert", "bank_transfers.deposit.amount-input": "Anzebezuelungsbetrag", "bank_transfers.deposit.amount-output": "Zilbetrag", "bank_transfers.deposit.amount-output.error": "Feeler", "bank_transfers.deposit.buttet-point.receive-crypto": "Empfaang {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "<PERSON><PERSON>", "bank_transfers.deposit.currency-not-supported.subtitle": "Bankiwwerweisunge vun {code} si bis op Weideres desaktivéiert.", "bank_transfers.deposit.currency-not-supported.title": "{code} Anzuelunge ginn am Moment net ënnerstëtzt", "bank_transfers.deposit.default-token.balance": "Saldo {amount}", "bank_transfers.deposit.deposit-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.enter_amount": "<PERSON><PERSON> aginn", "bank_transfers.deposit.iban-copied": "IBAN kopéiert", "bank_transfers.deposit.increase-amount": "Mindestbetrag ass {limit}", "bank_transfers.deposit.loading": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.max-limit-reached": "Betrag iwwerschreit de maximalen Iwwerweisungslimit", "bank_transfers.deposit.modal.kyc.button-text": "Ufänken", "bank_transfers.deposit.modal.kyc.text": "Fir deng Identitéit ze verifizéieren, brauche mir e puer perséinlech Detailer an Dokumenter. D'Areëche dauert normalerweis just e puer Minutten.", "bank_transfers.deposit.modal.kyc.title": "Verifizéier deng Identitéit fir d'Limitten ze erhéijen", "bank_transfers.deposit.reduce_amount": "Betrag reduzéieren", "bank_transfers.deposit.show-account.account-number": "Kontosnummer", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Sort Code", "bank_transfers.deposit.sort-code-copied": "Sort Code kopéiert", "bank_transfers.deposit.withdraw-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.failed_to_load_fee": "Onbekannt", "bank_transfers.fees": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.increase-amount": "Mindestbetrag ass {limit}", "bank_transfers.insufficient-funds": "Net genuch Saldo", "bank_transfers.select_country_of_residence.title": "Wou wunns du?", "bank_transfers.setup.cta": "Banktransferen ariichten", "bank_transfers.setup.enter-amount": "<PERSON><PERSON> aginn", "bank_transfers.source_of_funds.form.business_income": "Geschäftsakommes", "bank_transfers.source_of_funds.form.other": "<PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.pension": "<PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.salary": "<PERSON><PERSON>", "bank_transfers.source_of_funds.form.title": "Hierkonft vun denge Suen", "bank_transfers.source_of_funds_description.placeholder": "Hierkonft vun de Suen beschreiwen...", "bank_transfers.source_of_funds_description.title": "So eis méi iwwert d'Hierkonft vun denge Suen", "bank_transfers.withdraw-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.withdraw.amount-input": "Ofhiewungsbetrag", "bank_transfers.withdraw.max-limit-reached": "Betrag iwwerschreit de maximalen Iwwerweisungslimit", "bank_transfers.withdrawal.verify-id": "Betrag reduzéieren", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Maximumslimit iwwerschratt", "banner.above_maximum_limit.maximum_limit_per_deposit": "<PERSON>ë<PERSON> ass de maximale Betrag pro Depot", "banner.above_maximum_limit.subtitle": "Maximumslimit iwwerschratt", "banner.above_maximum_limit.title": "Reduzéier de Betrag op {amount} oder manner", "banner.above_maximum_limit.title.default": "Reduzéier de Betrag", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Minimumslimit net erreecht", "banner.below_minimum_limit.minimum_limit_for_token": "Dëst ass de Minimumsbetrag fir dësen <PERSON>", "banner.below_minimum_limit.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> op {amount} oder méi", "banner.below_minimum_limit.title.default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "breaard.in_porgress.info_popup.cta": "<PERSON><PERSON><PERSON> aus a verdéng {earn}", "breaard.in_porgress.info_popup.footnote": "Duerch d'Notzung vun Zeal an der Gnosis Pay Kaart stëmms du de Konditioune vun dëser Belounungscampagne zou.", "breaward.in_porgress.info_popup.bullet_point_1": "<PERSON><PERSON><PERSON> {remaining} bannent den nächsten {time} aus, fir dës <PERSON>ng ze kréien.", "breaward.in_porgress.info_popup.bullet_point_2": "Nëmme valabel Akeef mat Gnosis Pay ziele fir däi Gesamtbetrag.", "breaward.in_porgress.info_popup.bullet_point_3": "Nodeems du deng Belounung ugefuerdert hues, gëtt se op däi <PERSON>-<PERSON><PERSON> g<PERSON>.", "breaward.in_porgress.info_popup.header": "<PERSON><PERSON><PERSON><PERSON> {earn}, and<PERSON><PERSON> du {remaining}", "breward.celebration.for_spending": "Well s du mat denger Ka<PERSON> bezuelt hues", "breward.dc25-eligible-celebration.for_spending": "Du bass bei den éischten {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "Du wars net bei den éischten {limit} , déi ausginn hunn", "breward.expired_banner.earn_by_spending": "V<PERSON><PERSON>g {earn} andeems du {amount}", "breward.expired_banner.reward_expired": "{earn} -<PERSON>ounung ofgelaf", "breward.in_progress_banner.cta.title": "<PERSON><PERSON><PERSON>, verd<PERSON><PERSON> {earn}", "breward.ready_to_claim.error.try_again": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "breward.ready_to_claim.error_title": "Belounung konnt net ugefuerdert ginn", "breward.ready_to_claim.in_progress": "Belounung g<PERSON>tt ugefuerdert", "breward.ready_to_claim.youve_earned": "Du hues {earn} verd<PERSON><PERSON>!", "breward_already_claimed.title": "Belounung scho gefrot. Wanns du d'Belounung net krus, kontaktéier w.e.g. de <PERSON>.", "breward_cannotbe_claimed.title": "Belounung kann de Moment net gefrot ginn. Probéier w.e.g. méi spéit nach eng Kéier.", "bridge.best_return": "Route mam beschte Rendement", "bridge.best_serivce_time": "Schnellst Route", "bridge.check_status.complete": "Fäerdeg", "bridge.check_status.progress_text": "<PERSON><PERSON>ck vu(n) {from} op {to}", "bridge.remove_topup": "<PERSON><PERSON> e<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge.request_status.completed": "Ofgeschloss", "bridge.request_status.pending": "<PERSON>", "bridge.widget.completed": "Fäerdeg", "bridge.widget.currencies": "{from} op {to}", "bridge_rote.widget.title": "<PERSON><PERSON><PERSON>", "browse.discover_more_apps": "Entdeck méi <PERSON>", "browse.google_search_term": "Sich no \"{searchTerm}\"", "brward.celebration.you_earned": "<PERSON> hues verd<PERSON>", "brward.expired_banner.subtitle": "Méi Chance déi nächst Kéier", "brward.in_progress_banner.subtitle": "Leeft of an {expiredInFormatted}", "buy": "<PERSON><PERSON>", "buy.enter_amount": "<PERSON><PERSON> aginn", "buy.loading": "Lueden...", "buy.no_routes_found": "Keng <PERSON> fonnt", "buy.not_enough_balance": "Saldo net duer", "buy.select-currency.title": "Token au<PERSON>wielen", "buy.select-to-currency.title": "Tokene kafen", "buy_form.title": "Token kafen", "cancelled-card.create-card-button.primary": "<PERSON><PERSON>", "cancelled-card.switch-card-button.primary": "<PERSON><PERSON>", "cancelled-card.switch-card-button.short-text": "Du hues nach eng aner aktiv Ka<PERSON>", "card": "<PERSON><PERSON>", "card-add-cash.confirm-stage.banner.no-routes-found": "<PERSON><PERSON>, prob<PERSON>ier en aneren Token oder Montant", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Du brauchs {amount} méi {symbol} fir d'Käschten ze bezuelen", "card-add-cash.confirm-stage.banner.value-loss": "<PERSON> verléiers {loss} u <PERSON><PERSON><PERSON>", "card-add-cash.confirm-stage.banner.value-loss.revert": "Réckgängeg maachen", "card-add-cash.edit-stage.cta.cancel": "Ofbriechen", "card-add-cash.edit-stage.cta.continue": "<PERSON><PERSON>", "card-add-cash.edit-stage.cta.enter-amount": "<PERSON><PERSON> aginn", "card-add-cash.edit-stage.cta.reduce-to-max": "Maximal", "card-add-cash.edit-staget.banner.no-routes-found": "<PERSON><PERSON>, prob<PERSON>ier en aneren Token oder Montant", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Bestäteg w.e.g. op dengem Hardware-Wallet.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Um Hardware-Wallet ënnerschreiwen", "card-balance": "Saldo: {balance}", "card-cashback.status.title": "An de Cashback abezuelen", "card-copy-safe-address.copy_address": "<PERSON><PERSON>", "card-copy-safe-address.copy_address.done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card-copy-safe-address.warning.description": "<PERSON><PERSON><PERSON> kann nëmmen {cardAsset} op der Gnosis Chain kréien. Schéck keng Assets vun aneren Netzwierker op dës Adress. Si gi verluer.", "card-copy-safe-address.warning.header": "<PERSON><PERSON><PERSON><PERSON> nëmmen {cardAsset} op der Gnosis Chain", "card-marketing-card.center.subtitle": "Wiesselkäschten", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "Zënsen", "card-marketing-card.right.subtitle": "Umeldungskaddo", "card-marketing-card.title": "Europa seng VISA-<PERSON><PERSON> mat h<PERSON>", "card-marketing-tile.get-started": "Elo ufänken", "card-select-from-token-title": "Token fir d'Opluede wielen", "card-top-up.banner.subtitle.completed": "Ofgeschloss", "card-top-up.banner.subtitle.failed": "Feelgeschloen", "card-top-up.banner.subtitle.pending": "{timerString} Lafend", "card-top-up.banner.title": "Oplueden {amount}", "card-topup.select-token.emptyState": "<PERSON>g <PERSON> fonnt", "card.activate.card_number_not_valid": "Kaartennummer falsch. Iwwerpréif se w.e.g.", "card.activate.invalid_card_number": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "card.activation.activate_physical_card": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON>", "card.add-cash.amount-to-withdraw": "Opluedmontant", "card.add-from-earn-form.title": "Geld op d'Ka<PERSON> lueden", "card.add-from-earn-form.withdraw-to-card": "<PERSON><PERSON>", "card.add-from-earn.amount-to-withdraw": "Betrag fir op d'Kaart ofzehiewen", "card.add-from-earn.enter-amount": "<PERSON><PERSON> aginn", "card.add-from-earn.loading": "<PERSON><PERSON><PERSON>", "card.add-from-earn.max-label": "Saldo: {amount}", "card.add-from-earn.no-routes-found": "Keng <PERSON> fonnt", "card.add-from-earn.not-enough-balance": "Net genuch Saldo", "card.add-owner.queued": "Besëtzer-Ufro an der Waardeschléi", "card.add-to-wallet-flow.subtitle": "<PERSON><PERSON> mat dengem <PERSON>", "card.add-to-wallet.copy-card-number": "Kaartennummer hei drënner kopéieren", "card.add-to-wallet.title": "An de(n) {platformName} <PERSON>et dobäisetzen", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "ANNULÉIERT", "card.card-owner-not-found.disconnect-btn": "<PERSON><PERSON> vun <PERSON> trennen", "card.card-owner-not-found.subtitle": "Update <PERSON>tzer fir d'Kaart ze notzen.", "card.card-owner-not-found.title": "<PERSON><PERSON> nei verbannen", "card.card-owner-not-found.update-owner-btn": "Besëtzer aktualiséieren", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} an {date}", "card.cashback.widgetNoCashback.subtitle": "<PERSON>ach en <PERSON> fir ze verdéngen", "card.cashback.widgetNoCashback.title": "<PERSON>ritt bis zu {defaultPercentage} Cashback", "card.cashback.widgetcashbackValue.rewards": "{amount} ausstoend", "card.cashback.widgetcashbackValue.title": "{percentage} Cashback", "card.choose-wallet.connect_card": "<PERSON><PERSON>", "card.choose-wallet.create-new": "Neie Portmonni al<PERSON> Besëtzer dobäisetzen", "card.choose-wallet.import-another-wallet": "En anere Portmonni importéieren", "card.choose-wallet.import-current-owner": "Aktuelle Kaartebesëtzer importéieren", "card.choose-wallet.import-current-owner.sub-text": "Privat Schlësselen oder Seed-Phrase import<PERSON>, déi zu denger Gnosis Pay Ka<PERSON> gehéieren", "card.choose-wallet.title": "Portmonni fir d'Gestioun vun denger <PERSON> au<PERSON>wielen", "card.connectWalletToCardGuide": "Portmonni-<PERSON><PERSON>", "card.connectWalletToCardGuide.addGnosisPayOwner": "Gnosis Pay Besëtzer dobäisetzen", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Maach Gnosispay.com mat dengem anere Portmonni op{br}2. <PERSON><PERSON> op „Account“{br}3. <PERSON>lick op „Account details“{br}4. Klick op „Edit“, nieft „Account Owner“, an{br}5. <PERSON>lick op „Add address“{br}6. <PERSON><PERSON><PERSON>-Adress an a klick op „Save“", "card.connectWalletToCardGuide.header": "Verbann {account} mat der Gnosis <PERSON>", "card.connect_card.start": "Verbann Gnosis Pay Kaart", "card.copiedAddress": "Ko<PERSON><PERSON>iert {formattedAddress}", "card.disconnect-account.title": "Kont deconnectéieren", "card.hw-wallet-support-drop.add-owner-btn": "<PERSON><PERSON><PERSON> do<PERSON>", "card.hw-wallet-support-drop.disconnect-btn": "<PERSON><PERSON> vun <PERSON> trennen", "card.hw-wallet-support-drop.subtitle": "<PERSON><PERSON><PERSON> (keen HW) dobäisetzen.", "card.hw-wallet-support-drop.title": "HW-Wallets fir Kaart net méi ënnerstëtzt.", "card.kyc.continue": "<PERSON><PERSON> we<PERSON>", "card.list_item.title": "<PERSON><PERSON>", "card.onboarded.transactions.empty.description": "Deng Bezuelungsaktivitéit gëtt hei ugewisen", "card.onboarded.transactions.empty.title": "Aktivitéit", "card.order.continue": "Bestellung weiderfueren", "card.order.free_virtual_card": "<PERSON><PERSON><PERSON>", "card.order.start": "<PERSON><PERSON> gratis bestellen", "card.owner-not-imported.cancel": "Ofbriechen", "card.owner-not-imported.import": "Importéieren", "card.owner-not-imported.subtitle": "Fir dë<PERSON> z'autoriséieren, verbann den Owner-Wallet vun dengem Gnosis Pay Kont mat Zeal. Opgepasst: Dëst ass net dat selwecht wéi däin normale Gnosis Pay Wallet Login.", "card.owner-not-imported.title": "Gnosis Pay Kont-Owner do<PERSON><PERSON><PERSON>en", "card.page.order_free_physical_card": "<PERSON><PERSON><PERSON>ellen", "card.pin.change_pin_at_atm": "De PIN kann op ausgewielte Bankomaten geännert ginn", "card.pin.timeout": "<PERSON> Ecran gëtt zou an {seconds} Sek.", "card.quick-actions.add-assets": "<PERSON><PERSON><PERSON>", "card.quick-actions.add-cash": "<PERSON><PERSON><PERSON>", "card.quick-actions.details": "Detailer", "card.quick-actions.freeze": "Afréier<PERSON>", "card.quick-actions.freezing": "<PERSON><PERSON><PERSON>", "card.quick-actions.unfreeze": "Entspären", "card.quick-actions.unfreezing": "<PERSON><PERSON><PERSON>", "card.quick-actions.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "card.read-only-detected.create-new": "Neie Portmonni al<PERSON> Besëtzer dobäisetzen", "card.read-only-detected.import-current-owner": "Schlësselen importéiere fir {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Privat Schlësselen oder Seed-Phrase vum Portmonni importéieren {address}", "card.read-only-detected.title": "<PERSON><PERSON> op Read-only-Portmonni erkannt. Wielt e Portmonni fir d'Kaart ze verwalten", "card.remove-owner.queued": "Ewechhuele vum Besëtzer an der Waardeschläif", "card.settings.disconnect-from-zeal": "<PERSON><PERSON> trennen", "card.settings.edit-owners": "Kaartebesëtzer änneren", "card.settings.getCard": "<PERSON><PERSON> aner <PERSON> k<PERSON>", "card.settings.getCard.subtitle": "Virtuell oder phy<PERSON><PERSON>", "card.settings.notRecharging": "Gëtt net opgelueden", "card.settings.notifications.subtitle": "Bezuelnotifikatioune k<PERSON>", "card.settings.notifications.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.page.title": "Kaartenastellungen", "card.settings.select-card.cancelled-cards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.setAutoRecharge": "Automatesch Opluedung astellen", "card.settings.show-card-address": "<PERSON><PERSON><PERSON><PERSON> uweisen", "card.settings.spend-limit": "Ausgabelimit astellen", "card.settings.spend-limit-title": "Aktuell Dageslimit: {limit}", "card.settings.switch-active-card": "Aktiv Kaart w<PERSON>selen", "card.settings.switch-active-card-description": "<PERSON>kt<PERSON>: {card}", "card.settings.switch-card.card-item.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.frozen": "<PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.title": "Gnosis <PERSON>", "card.settings.switch-card.card-item.title.physical": "<PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.title.virtual": "<PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.title": "<PERSON><PERSON>", "card.settings.targetBalance": "<PERSON><PERSON><PERSON>: {threshold}", "card.settings.view-pin": "P<PERSON> uwei<PERSON>", "card.settings.view-pin-description": "Schütz däi PIN ëmmer", "card.title": "<PERSON><PERSON>", "card.transactions.header": "Kaartentransaktiounen", "card.transactions.see_all": "All Transaktioune weisen", "card.virtual": "VIRTUELL", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Cashback g<PERSON><PERSON>, <PERSON><PERSON> e ve<PERSON><PERSON> gouf, op deng <PERSON> g<PERSON>.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "Wat s de méi depos<PERSON>, wat s de méi bei all Aka<PERSON> verd<PERSON>.", "cardCashback.onboarding.title": "<PERSON><PERSON>t bis zu {percentage} Cashback", "cardCashbackWithdraw.amount": "Betrag ofhi<PERSON>en", "cardCashbackWithdraw.header": "Ofhiewen {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "<PERSON><PERSON><PERSON><PERSON> ass gespaart a konnt net aktivéiert ginn", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "Bis zu {percentage}", "cardWidget.startEarning": "Ufänke mat verdéngen", "cardWithdraw.amount": "Betrag fir opzehiewen", "cardWithdraw.header": "<PERSON><PERSON> der <PERSON> ophiewen", "cardWithdraw.selectWithdrawWallet.title": "<PERSON><PERSON> de Portmonni aus,{br} op deen opgehuewe gëtt", "cardWithdraw.success.cta": "Zoumaachen", "cardWithdraw.success.subtitle": "Aus Sécherheetsgrënn dauert all Ophiewung vun der Gnosis Pay Kaart 3 Minutten", "cardWithdraw.success.title": "Dës Ännerung dauert 3 Minutten", "card_top_up_trx.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card_top_up_trx.to": "Un", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "Oflaf<PERSON><PERSON>", "cards.card_number": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cards.choose-wallet.no-active-accounts": "Du hues keng aktiv Portmonnien", "cards.copied_card_number": "Ka<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Deeglech Limit iwwerschratt", "cards.transactions.decline_reason.incorrect_pin": "Falsche PIN", "cards.transactions.decline_reason.incorrect_security_code": "Falsche Sécherheetscode", "cards.transactions.decline_reason.invalid_amount": "Ongültege Betrag", "cards.transactions.decline_reason.low_balance": "<PERSON><PERSON><PERSON><PERSON>", "cards.transactions.decline_reason.other": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cards.transactions.decline_reason.pin_tries_exceeded": "PIN-Versich iwwerschratt", "cards.transactions.status.refund": "Remboursement", "cards.transactions.status.reversal": "Stornéierung", "cashback-deposit.trx.title": "Alog op <PERSON>", "cashback-estimate.text": "<PERSON><PERSON><PERSON> ass eng Schätzung a KENG garantéiert Ausbezuelung. All ëffentlech bekannt Cashback-<PERSON><PERSON><PERSON> ginn ugewannt, mee Gnosis Pay kann Transaktiounen no eegenem Ermoossen ausschléissen. E maximale Betrag vun {amount} pro Woch qualifizéiert sech fir Cashback, och wann d'Schätzung fir dës Transaktioun e méi héije Gesamtbetrag uginn géif.", "cashback-estimate.text.fallback": "<PERSON><PERSON><PERSON> ass eng Schätzung an keng garantéiert Ausbezuelung. All bekannt Cashback-<PERSON><PERSON><PERSON> ginn u<PERSON>wan<PERSON>, mee Gnosis Pay kann Transaktiounen no eegenem Gaddo ausschléissen.", "cashback-estimate.title": "Cashback-Sc<PERSON>ät<PERSON>ng", "cashback-onbarding-tersm.subtitle": "<PERSON>g <PERSON>saktiounsdate gi mat <PERSON><PERSON><PERSON><PERSON>, déi fir d'Verdeelung vu Cashback-Beloununge verantwortlech sinn. Wann s de op Akzeptéiere klick<PERSON>, akzeptéiers de d'Gnosis DAO Cashback <terms>Konditiounen</terms>", "cashback-onbarding-tersm.title": "Notzungsbedéngungen an Dateschutz", "cashback-tx-activity.retry": "<PERSON><PERSON>", "cashback-unconfirmed-payments-info.subtitle": "Bezuelunge qualifizéiere sech fir Cashback, soubal se mam <PERSON> ofgerechent sinn. Bis dohi gi se als net confirméiert Bezuelungen ugewisen. Net ofgerechent Bezuelunge qualifizéiere sech net fir Cashback.", "cashback-unconfirmed-payments-info.title": "Net confirméiert Kaartebezuelungen", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "Depot", "cashback.activity.title": "Rezent Aktivitéit", "cashback.activity.withdrawal": "Ofhiewung", "cashback.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.deposit.amount.label": "Depot-Betrag", "cashback.deposit.change": "{from} op {to}", "cashback.deposit.confirmation.subtitle": "Cashback-S<PERSON>z ginn eemol d'Woch aktualiséiert. Maach elo en Depot fir de Cashback vun nächster Woch ze erhéijen.", "cashback.deposit.confirmation.title": "<PERSON> verdéngs {percentage} ab dem {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "<PERSON><PERSON> Tokens an {currency} op der {network} Chain", "cashback.deposit.get.tokens.title": "<PERSON><PERSON><PERSON> {currency} -Tokens", "cashback.deposit.header": "Deposéier {currency}", "cashback.deposit.max_label": "Max: {amount}", "cashback.deposit.select-wallet.title": "<PERSON><PERSON> de Portmonni aus, vun deem s de deposéiere w<PERSON>ls", "cashback.deposit.yourcashback": "<PERSON><PERSON><PERSON>", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "<PERSON><PERSON> de Portmonni aus,{br}op deen s de ofhiewe wëlls", "cashback.transaction-details.network-label": "Netzwierk", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "<PERSON><PERSON>", "cashback.transaction-details.top-row.label-rewards": "Cashback-Period", "cashback.transaction-details.top-row.label-withdrawal": "Un", "cashback.transaction-details.transaction": "Transaktiouns-ID", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Cashback-<PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "cashback.withdraw.confirmation.cashback_reduction": "De Cashback fir dë<PERSON>, och dat wat s de scho verd<PERSON> hues, g<PERSON>tt reduzéiert vu(n) {before} op {after}", "cashback.withdraw.queued": "Ofhiewung an der Waardeschleef", "cashback.withdrawal.change": "{from} op {to}", "cashback.withdrawal.confirmation.subtitle": "Start d'Ofhiewung vu(n) {amount} mat enger Verzögerung vun 3 Minutten. Dëst reduzéiert däi Cashback op {after}.", "cashback.withdrawal.confirmation.title": "<PERSON><PERSON> geet erof, wanns de GNO ofhiefs", "cashback.withdrawal.delayTransaction.title": "Start d'GNO-Ofhiewung mat{br} enger Verzögerung vun 3 Minutten", "cashback.withdrawal.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "cashback.withdrawal.yourcashback": "<PERSON><PERSON><PERSON>", "celebration.aave": "<PERSON>", "celebration.cashback.subtitle": "Ausbezuelt an {code}", "celebration.cashback.subtitleGNO": "{amount} zu<PERSON><PERSON> verdéngt", "celebration.chf": "<PERSON>", "celebration.lido": "<PERSON>", "celebration.sky": "<PERSON> <PERSON>", "celebration.title": "Total Cashback", "celebration.well_done.title": "Gutt gemaach!", "change-withdrawal-account.add-new-account": "En anere Bankkont dobäisetzen", "change-withdrawal-account.item.shortText": "{currency} Kont", "check-confirmation.approve.footer.for": "Fir", "checkConfirmation.title": "Transaktiounsresultat", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin & Ether", "collateral.ethereum": "Ethereum", "collateral.other": "<PERSON><PERSON>", "collateral.rwa": "<PERSON><PERSON> Verméigenswäerter", "collateral.stablecoins": "Stablecoins (USD-gebonnen)", "collateral.us-t-bills": "US T-Bills", "confirm-bank-transfer-recipient.bullet-1": "<PERSON>g <PERSON> op digitalen EUR", "confirm-bank-transfer-recipient.bullet-2": "Depoten op {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Deel d'Detailer vun dengem Gnosis Pay Kont mat Monerium, engem autoriséierten a reglementéierten E-Geld-Institut. <link>Méi gewuer ginn</link>", "confirm-bank-transfer-recipient.bullet-4": "Akzeptéier d'Monerium <link>Notzungsbedéngungen</link>", "confirm-bank-transfer-recipient.title": "Kondi<PERSON>ounen <PERSON>", "confirm-change-withdrawal-account.cancel": "Ofbriechen", "confirm-change-withdrawal-account.confirm": "Bestätegen", "confirm-change-withdrawal-account.saving": "Späicheren", "confirm-change-withdrawal-account.subtitle": "All Ofhiewungen, déi s du vun Zeal aus sché<PERSON>, ginn op dësem Bankkont empfaangen.", "confirm-change-withdrawal-account.title": "Empfängerbank änneren", "confirm-ramove-withdrawal-account.title": "Bankkont läschen", "confirm-remove-withdrawal-account.subtitle": "<PERSON><PERSON><PERSON>-Detailer ginn aus Zeal geläscht. Du kanns se zu all <PERSON> erëm dobäisetzen.", "confirmTransaction.finalNetworkFee": "Netzwierkskäschten", "confirmTransaction.importKeys": "Schlësselen importéieren", "confirmTransaction.networkFee": "Netzwierkskäschten", "confirmation.title": "<PERSON><PERSON><PERSON><PERSON> {amount} un {recipient}", "conflicting-monerium-account.add-owner": "Als Gnosis Pay-Besëtzer bäifügen", "conflicting-monerium-account.create-wallet": "En neie Smart Wallet erstellen", "conflicting-monerium-account.disconnect-card": "<PERSON><PERSON> vun Zeal trennen an nees mam neie Besëtzer verbannen", "conflicting-monerium-account.header": "{wallet} mat engem anere Monerium-<PERSON><PERSON> verbonnen", "conflicting-monerium-account.subtitle": "Änner däi Gnosis Pay-Besëtzer-Wallet", "connection.diconnected.got_it": "Verstanen!", "connection.diconnected.page1.subtitle": "Zeal funktioné<PERSON><PERSON> i<PERSON>, wou Metamask funktionéiert. Verbann dech einfach wéi s du et mat Metamask géifs maachen.", "connection.diconnected.page1.title": "Wéi mat <PERSON>eal verbannen?", "connection.diconnected.page2.subtitle": "Du gesäis vill Optiounen. Zeal ass vläicht eng dovun. Wann Zeal net erschéngt ...", "connection.diconnected.page2.title": "Klick op 'Connect Wallet'", "connection.diconnected.page3.subtitle": "Mir froen eng Verbindung mat Zeal un. <PERSON><PERSON><PERSON> oder Injected soll och funktionéieren. Probéier et!", "connection.diconnected.page3.title": "<PERSON>iel <PERSON>", "connectionSafetyCheck.tag.caution": "Opgepasst", "connectionSafetyCheck.tag.danger": "Gefor", "connectionSafetyCheck.tag.passed": "<PERSON><PERSON><PERSON>", "connectionSafetyConfirmation.subtitle": "<PERSON> du sécher, dass s du weiderfuere wëlls?", "connectionSafetyConfirmation.title": "Dëse Site gesäit geféierlech aus", "connection_state.connect.cancel": "Ofbriechen", "connection_state.connect.changeToMetamask": "Op MetaMask 🦊 wiesselen", "connection_state.connect.changeToMetamask.label": "Op MetaMask wiesselen", "connection_state.connect.connect_button": "Verbannen", "connection_state.connect.expanded.connected": "<PERSON><PERSON>bonnen", "connection_state.connect.expanded.title": "Verbannen", "connection_state.connect.safetyChecksLoading": "Sécherheet vum Site gëtt gepréift", "connection_state.connect.safetyChecksLoadingError": "Sécherheetschecken konnten net ofgeschloss ginn", "connection_state.connected.expanded.disconnectButton": "Zeal trennen", "connection_state.connected.expanded.title": "<PERSON><PERSON>bonnen", "copied-diagnostics": "<PERSON><PERSON><PERSON>", "copy-diagnostics": "<PERSON><PERSON><PERSON>", "counterparty.component.add_recipient_primary_text": "Bankdestinataire bäifügen", "counterparty.country": "Land", "counterparty.countryTitle": "Land vum Destinataire", "counterparty.currency": "Wärung", "counterparty.delete.success.title": "<PERSON><PERSON><PERSON>gehol<PERSON>", "counterparty.edit.success.title": "Ännerunge gespäichert", "counterparty.errors.country_required": "Land obligatoresch", "counterparty.errors.first_name.invalid": "Virnumm muss méi laang sinn", "counterparty.errors.last_name.invalid": "Familljennumm muss méi laang sinn", "counterparty.first_name": "Virnumm", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Op d'Bank schécken", "countrySelector.noCountryFound": "Kee Land fonnt", "countrySelector.title": "Land auswielen", "create-passkey.cta": "<PERSON><PERSON> erstellen", "create-passkey.extension.cta": "<PERSON><PERSON>", "create-passkey.footnote": "Powered by", "create-passkey.mobile.cta": "Sécherheet astellen", "create-passkey.steps.enable-recovery": "Cloud-Erhuelung ariichten", "create-passkey.steps.setup-biometrics": "Biometresch Sécherheet aktivéieren", "create-passkey.subtitle": "Passkeys si méi sécher wéi Passwierder a ginn an der Cloud verschlësselt fir eng einfach Restauratioun.", "create-passkey.title": "<PERSON><PERSON>", "create-smart-wallet": "Smart Wallet erstellen", "create-userop.progress.text": "<PERSON><PERSON><PERSON>", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Weider op Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Op Gnosispay.com goen", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "Du hues deng Kaartebestellung schonn ugefaangen. <PERSON><PERSON><PERSON> op Gnosis Pay, fir se ofzeschléissen.", "create_recharge_preferences.card": "<PERSON><PERSON>", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "V<PERSON><PERSON><PERSON> {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "<PERSON>n halen", "create_recharge_preferences.link_accounts_title": "<PERSON><PERSON> verbannen", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "<PERSON><PERSON> gëtt NET automatesch no all Bezuelung opgelueden.", "create_recharge_preferences.not_configured_title": "Verdéngen & ausginn", "create_recharge_preferences.recharge_from_earn_accounts_description": "Deng <PERSON> gëtt no all Bezuelung automatesch vun dengem Earn-<PERSON>nt opgelueden.", "create_recharge_preferences.subtitle": "pro Joer", "creating-account.loading": "<PERSON><PERSON> g<PERSON> erstallt", "creating-gnosis-pay-account": "<PERSON><PERSON> g<PERSON> erstallt", "currencies.bridge.select_routes.emptyState": "<PERSON> hu keng Route fir dë<PERSON> fonnt", "currency.add_currency.add_token": "To<PERSON> dob<PERSON>tzen", "currency.add_currency.not_a_valid_address": "<PERSON><PERSON><PERSON> ass keng valabel <PERSON>-Adress", "currency.add_currency.token_decimals_feild": "Token-Dezimalen", "currency.add_currency.token_feild": "Token-<PERSON><PERSON>", "currency.add_currency.token_symbol_feild": "Token-Symbol", "currency.add_currency.update_token": "Token aktualiséieren", "currency.add_custom.remove_token.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.add_custom.remove_token.header": "<PERSON><PERSON> e<PERSON>n", "currency.add_custom.remove_token.subtitle": "<PERSON><PERSON>i Portmonni behält de Saldo vun dësem <PERSON>, mä en gëtt an denge Zeal-Portfolio-Salden verstoppt.", "currency.add_custom.token_removed": "Token ewechgeholl", "currency.add_custom.token_updated": "Token aktualiséiert", "currency.balance_label": "Saldo: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "Deng Bankiwwerweisung huet erfollegräich {fiat} un {crypto} iwwerwisen.", "currency.bankTransfer.deposit_status.finished.title": "Du hues {crypto}", "currency.bankTransfer.deposit_status.success": "An dengem Portmonni ukomm", "currency.bankTransfer.deposit_status.title": "Anzuelung", "currency.bankTransfer.off_ramp.check_bank_account": "<PERSON>ck op däi Bankkonto", "currency.bankTransfer.off_ramp.complete": "Ofgeschloss", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Gëtt un deng Bank geschéckt", "currency.bankTransfer.off_ramp.transferring_to_currency": "Iwwerweisung un {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "D'Sue sollten elo op dengem Bankkonto ukomm sinn.", "currency.bankTransfer.withdrawal_status.success": "Un deng Bank geschéckt", "currency.bankTransfer.withdrawal_status.title": "Ofhiewung", "currency.bank_transfer.create_unblock_user.email": "E-Mail-Adress", "currency.bank_transfer.create_unblock_user.email_invalid": "Ongülteg E-Mail-Adress", "currency.bank_transfer.create_unblock_user.email_missing": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name": "Virnumm", "currency.bank_transfer.create_unblock_user.first_name_missing": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Espace an - . , & ( ) ' sinn erlaabt.", "currency.bank_transfer.create_unblock_user.last_name": "Familljennumm", "currency.bank_transfer.create_unblock_user.last_name_missing": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Espace an - . , & ( ) ' sinn erlaabt.", "currency.bank_transfer.create_unblock_user.note": "<PERSON><PERSON> s du weiderfiers, akzeptéiers du dem Unblock (eisem Bankepartner) seng <terms>Ko<PERSON><PERSON>ounen</terms> an <policy>Dateschutzerklärung</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "<PERSON><PERSON><PERSON><PERSON> däin <PERSON>umm genee wéi op dengem Bankkont", "currency.bank_transfer.create_unblock_user.title": "Verbënn däi Bankkont", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Kontosnummer", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Land vun der Bank", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Gewënschte Wärung", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Sort Code", "currency.bank_transfer.create_unblock_withdraw_account.success": "<PERSON><PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.title": "Verbann däi Bankkonto", "currency.bank_transfer.residence-form.address-required": "<PERSON>b<PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.address-unsupported-char": "<PERSON>, <PERSON><PERSON><PERSON>, E<PERSON> an , ; {apostrophe} - \\\\ sinn erlaabt.", "currency.bank_transfer.residence-form.city-required": "<PERSON>b<PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.city-unsupported-char": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> an . , - & ( ) {apostrophe} sinn erlaabt.", "currency.bank_transfer.residence-form.postcode-invalid": "Ongëltege Postcode", "currency.bank_transfer.residence-form.postcode-required": "<PERSON>b<PERSON><PERSON><PERSON>", "currency.bank_transfer.validation.invalid.account_number": "Ongülteg Kontosnummer", "currency.bank_transfer.validation.invalid.iban": "Ongültegen IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Ongültege Sort Code", "currency.bridge.amount_label": "Betrag fir d'<PERSON>", "currency.bridge.best_returns.subtitle": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> g<PERSON> dir deen héchsten Ertrag, inklusiv alle Käschten.", "currency.bridge.best_returns_popup.title": "Beschte Rendement", "currency.bridge.bridge_from": "<PERSON><PERSON>", "currency.bridge.bridge_gas_fee_loading_failed": "Mir hate Problemer beim <PERSON> vun den Netzwierkskäschten", "currency.bridge.bridge_low_slippage": "Ganz niddrege Slippage. Probéier, en z'erhéijen", "currency.bridge.bridge_provider": "Iwwerweisungs-Ubidder", "currency.bridge.bridge_provider_loading_failed": "<PERSON> hate <PERSON>, d'U<PERSON>der ze lueden", "currency.bridge.bridge_settings": "Astellunge fir d'<PERSON>", "currency.bridge.bridge_status.subtitle": "Iwwer {name}", "currency.bridge.bridge_status.title": "<PERSON><PERSON><PERSON>", "currency.bridge.bridge_to": "Un", "currency.bridge.fastest_route_popup.subtitle": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> bitt dir déi schnellst Transaktiounsroute.", "currency.bridge.fastest_route_popup.title": "Schnellst Route", "currency.bridge.from": "<PERSON><PERSON>", "currency.bridge.success": "Fäerdeg", "currency.bridge.title": "<PERSON><PERSON><PERSON>", "currency.bridge.to": "Un", "currency.bridge.topup": "Oplueden {symbol}", "currency.bridge.withdrawal_status.title": "Ofhiewung", "currency.card.card_top_up_status.title": "Geld op d'Ka<PERSON> lueden", "currency.destination_amount": "Zilbetrag", "currency.hide_currency.confirm.subtitle": "Verstopp dësen Token aus dengem Portfolio. Du kanns en zu all Moment nees uweisen.", "currency.hide_currency.confirm.title": "Token verstoppen", "currency.hide_currency.success.title": "Token verstoppt", "currency.label": "<PERSON><PERSON><PERSON><PERSON> (Optional)", "currency.last_name": "Familljennumm", "currency.max_loading": "Max.:", "currency.swap.amount_to_swap": "Tauschbetrag", "currency.swap.best_return": "Route mam beschte Resultat", "currency.swap.destination_amount": "Zilbetrag", "currency.swap.header": "<PERSON><PERSON>", "currency.swap.max_label": "Saldo: {amount}", "currency.swap.provider.header": "Swap-Ubidder", "currency.swap.select_to_token": "Token au<PERSON>wielen", "currency.swap.swap_gas_fee_loading_failed": "Mir hate Problemer beim <PERSON> vun den Netzwierkskäschten", "currency.swap.swap_provider_loading_failed": "Mir hate Problemer be<PERSON> vun den Ubidder", "currency.swap.swap_settings": "Tausch-Astellungen", "currency.swap.swap_slippage_too_low": "Ganz niddrege Slippage. Probéier, en ze erhéijen.", "currency.swaps_io_native_token_swap.subtitle": "Iwwer Swaps.IO", "currency.swaps_io_native_token_swap.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.withdrawal.amount_from": "<PERSON><PERSON>", "currency.withdrawal.amount_to": "Un", "currencySelector.title": "Wärung auswielen", "dApp.wallet-does-not-support-chain.subtitle": "Däi Portmonni ënnerstëtzt anscheinend net {network}. <PERSON><PERSON><PERSON><PERSON>, dech mat engem anere Portmonni ze verbannen, oder <PERSON><PERSON><PERSON>.", "dApp.wallet-does-not-support-chain.title": "Netzwierk gëtt net ënnerstëtzt", "dapp.connection.manage.confirm.disconnect.all.cta": "All trennen", "dapp.connection.manage.confirm.disconnect.all.subtitle": "<PERSON> du s<PERSON>cher, dass du all Verbindungen trenne wëlls?", "dapp.connection.manage.confirm.disconnect.all.title": "All trennen", "dapp.connection.manage.connection_list.main.button.title": "<PERSON><PERSON><PERSON>", "dapp.connection.manage.connection_list.no_connections": "Du hues keng verbonnen A<PERSON>n", "dapp.connection.manage.connection_list.section.button.title": "All trennen", "dapp.connection.manage.connection_list.section.title": "Aktiv", "dapp.connection.manage.connection_list.title": "Verbindungen", "dapp.connection.manage.disconnect.success.title": "Appen getrennt", "dapp.metamask_mode.title": "MetaMask-Modus", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "Zënsen", "dc25-card-marketing-card.right.subtitle": "100 Persounen", "dc25-card-marketing-card.title": "<PERSON>é<PERSON> 100, déi 50 € ausginn, k<PERSON><PERSON> {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Du kanns dës Aktioun fir 3 Minutten net maachen. Aus Sécherheetsgrënn dauert d'Beaarbechtung vun all Kaartenastellungsännerung oder Ofhiewung 3 Minutten.", "delayQueueBusyBanner.processing-yout-action.title": "Deng Akt<PERSON> g<PERSON> ve<PERSON>, wann ech gelift waart", "delayQueueBusyWidget.cardFrozen": "<PERSON><PERSON> g<PERSON>", "delayQueueBusyWidget.processingAction": "Deng Aktioun g<PERSON> veraarbecht", "delayQueueFailedBanner.action-incomplete.get-support": "Support ufroen", "delayQueueFailedBanner.action-incomplete.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, eppes ass bei denger Ophiewung oder Astellungsännerung schifgaangen. Kontaktéier wgl. de Support op Discord.", "delayQueueFailedBanner.action-incomplete.title": "Aktioun net ofgeschloss", "delayQueueFailedWidget.actionIncomplete.title": "Kaartenaktioun net ofgeschloss", "delayQueueFailedWidget.cardFrozen.subtitle": "<PERSON><PERSON> g<PERSON>", "delayQueueFailedWidget.contactSupport": "Support kontaktéieren", "delay_queue_busy.subtitle": "Aus Sécherheetsgrënn daueren Ännerunge vun Astellungen oder Ophiewungen 3 Minutten. An där Zäit ass deng <PERSON> gespa<PERSON>.", "delay_queue_busy.title": "Deng Aktioun g<PERSON> veraarbecht", "delay_queue_failed.contact_support": "Kontakt", "delay_queue_failed.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, eppes ass bei denger Ophiewung oder Astellungsännerung schifgaangen. Kontaktéier wgl. de Support op Discord.", "delay_queue_failed.title": "Support kontaktéieren", "deploy-earn-form-smart-wallet.in-progress.title": "<PERSON><PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "disconnect-card-popup.cancel": "Ofbriechen", "disconnect-card-popup.disconnect": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.subtitle": "<PERSON><PERSON><PERSON> läscht deng Kaart aus der Zeal App. Däi Portmonni bleift an der Gnosis Pay App mat denger Kaart verbonnen. Du kanns deng Kaart zu all <PERSON> erëm verbannen.", "disconnect-card-popup.title": "<PERSON><PERSON>ren<PERSON>", "distance.long.days": "{count} Deeg", "distance.long.hours": "{count} <PERSON><PERSON><PERSON>", "distance.long.minutes": "{count} <PERSON><PERSON><PERSON>", "distance.long.months": "{count} <PERSON><PERSON><PERSON>", "distance.long.seconds": "{count} <PERSON><PERSON><PERSON>", "distance.long.years": "{count} <PERSON><PERSON>", "distance.short.days": "{count} d", "distance.short.hours": "{count} h", "distance.short.minutes": "{count} Min", "distance.short.months": "{count} M", "distance.short.seconds": "{count} Sek", "distance.short.years": "{count} J", "duration.short.days": "{count}d", "duration.short.hours": "{count}h", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "<PERSON><PERSON>", "earn-deposit-view.into": "An", "earn-deposit-view.to": "Un", "earn-deposit.swap.transfer-provider": "Iwwerweisungsanbidder", "earn-taker-investment-details.accrued-realtime": "An Echtzäit ugesammelt", "earn-taker-investment-details.asset-class": "Anlagklass", "earn-taker-investment-details.asset-coverage-ratio": "Verméigensdeckungsquot", "earn-taker-investment-details.asset-reserve": "Verméigensreserv", "earn-taker-investment-details.base_currency.label": "Basiswährung", "earn-taker-investment-details.chf.description": "Verdéng Zënsen op deng CHF andeems s du zCHF op Frankencoin deposéiers – en zouverlässegen digitale Geldmaart. Zënse ginn duerch iwwerséchert Prête mat geréngem Risiko op Frankencoin generéiert an an Echtzäit ausbezuelt. Deng Suen si sécher op engem Ënnerkont, deen nëmmen s du kontrolléiers.", "earn-taker-investment-details.chf.description.with_address_link": "Verdéng Zënsen op deng CHF andeems s du zCHF op Frankencoin deposéiers – en zouverlässegen digitale Geldmaart. Zënse ginn duerch iwwerséchert Prête mat geréngem Risiko op Frankencoin generéiert an an Echtzäit ausbezuelt. Deng Sue si sécher op engem Ënnerkont <link>(0x kopéieren)</link> , deen nëmmen s du kontrolléiers.", "earn-taker-investment-details.chf.label": "Digitalen Schwäizer Frang", "earn-taker-investment-details.collateral-composition": "Zesummesetzung vun de Sécherheeten", "earn-taker-investment-details.depositor-obligations": "Verflichtunge géintiwwer den Abéier", "earn-taker-investment-details.eure.description": "Verdéng Zënsen op deng Euroen andeems du EURe an Aave abezills – en zouverlässegen digitale Geldmaart. EURe ass e voll reglementéierte Euro-Stablecoin, de vu Monerium erausgi gëtt a 1:1 op ofgesécherte Konte garantéiert ass. Zënse ginn duerch risikoaarm, iwwerséchert Prêten op Aave generéiert an an Echtzäit ausbezuelt. Deng Sue bleiwen op engem séchere Sous-compte, deen nëmmen s du kontrolléiers.", "earn-taker-investment-details.eure.description.with_address_link": "Verdéng Zënsen op deng Euroen andeems du EURe an Aave abezills – en zouverlässegen digitale Geldmaart. EURe ass e voll reglementéierte Euro-Stablecoin, de vu Monerium erausgi gëtt a 1:1 op ofgesécherte Konte garantéiert ass. Zënse ginn duerch risikoaarm, iwwerséchert Prêten op Aave generéiert an an Echtzäit ausbezuelt. Deng Sue bleiwen op engem séchere Sous-compte, <link>(0x kopéieren)</link> deen nëmmen s du kontrolléiers.", "earn-taker-investment-details.eure.label": "Digitalen Euro (EURe)", "earn-taker-investment-details.faq": "FAQ", "earn-taker-investment-details.fixed-income": "Festverzënslech", "earn-taker-investment-details.issuer": "<PERSON><PERSON>gie<PERSON>", "earn-taker-investment-details.key-facts": "Wichteg Fakten", "earn-taker-investment-details.liquidity": "Liquiditéit", "earn-taker-investment-details.operator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.projected-yield": "Prognostizéiert Joresrendement", "earn-taker-investment-details.see-other-faq": "All aner FAQe weisen", "earn-taker-investment-details.see-realtime": "Echtzäit-<PERSON> weisen", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Rendement vun de leschten 12 Méint", "earn-taker-investment-details.total-collateral": "Total Sécherheeten", "earn-taker-investment-details.total-deposits": "$27.253.300.208", "earn-taker-investment-details.total-zchf-supply": "Total zCHF-Disponibilitéit", "earn-taker-investment-details.total_deposits": "Total Abezuelungen op Aave", "earn-taker-investment-details.usd.description": "Sky ass en digitale Geld<PERSON>art, dee stabil, an US-Dollar denominéiert Rendementer aus kuerzfristege US-Schatzbilljeeën an iwwersécherte Prête bitt – ouni Krypto-Volatilitéit, 24/7 Zougank zu de Suen an transparent, On-Chain-Ofsécherung.", "earn-taker-investment-details.usd.description.with_address_link": "Sky ass en digitale G<PERSON>, dee stabil, an US-Dollar denominéiert Rendementer aus kuerzfristege US-Schatzbilljeeën an iwwersécherte Prête bitt – ouni Krypto-Volatilitéit, 24/7 Zougank zu de Suen an transparent, On-Chain-Ofsécherung. D'Investissementer sinn op engem Sous-compte, <link>(0x kopéieren)</link> dee vun dir kontrolléiert gëtt.", "earn-taker-investment-details.usd.ftx-difference": "<PERSON><PERSON><PERSON> sech dat vun FT<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>i oder Luna?", "earn-taker-investment-details.usd.high-returns": "Wéi k<PERSON>nnen d'Rendementer esou héich sinn, besonnesch am Verglach mat traditionelle Banken?", "earn-taker-investment-details.usd.how-is-backed": "Wéi ass Sky USD ofgeséchert a wat geschitt mat menge <PERSON>, wann Zeal Faillite mécht?", "earn-taker-investment-details.usd.income-sources": "Akommesquellen 2024", "earn-taker-investment-details.usd.insurance": "Sinn meng Suen duerch eng Entitéit (wéi FDIC oder änleches) verséchert oder garantéiert?", "earn-taker-investment-details.usd.label": "Digitalen US-Dollar", "earn-taker-investment-details.usd.lose-principal": "Kann ech mäin Haaptkapital realistesch verléieren an ënner wéi engen Ëmstänn?", "earn-taker-investment-details.variable-rate": "<PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.withdraw-anytime": "Jidderzäit ofhiewen", "earn-taker-investment-details.yield": "Rendement", "earn-withdrawal-view.approve.for": "Fir", "earn-withdrawal-view.approve.into": "An", "earn-withdrawal-view.swap.into": "An", "earn-withdrawal-view.withdraw.to": "Un", "earn.add_another_asset.title": "Asset fir <PERSON><PERSON><PERSON>n", "earn.add_asset": "<PERSON><PERSON> do<PERSON>", "earn.asset_view.title": "Verdéngen", "earn.base-currency-popup.text": "D'Basiswährung ass d'Währung, an där deng Abezuelungen, däi Rendement an deng Transaktioune bewäert an opgezeechent ginn. Wanns du an enger anerer Währung abezills (z. B. EUR an USD), ginn deng Suen direkt unhand vun den aktuelle Wiesselcoursen an d'Basiswährung ëmgerechent. No der Ëmrechnung bleift däi Saldo an der Basiswährung stabil, mä bei zukünftegen Ofhiewunge kënnen erëm Währungsemrechnunge néideg sinn.", "earn.base-currency-popup.title": "Basiswährung", "earn.card-recharge.disabled.list-item.title": "Automatesch Oplueden desaktivéiert", "earn.card-recharge.enabled.list-item.title": "Automatesch Oplueden aktivéiert", "earn.choose_wallet_to_deposit.title": "<PERSON><PERSON><PERSON> vun", "earn.config.currency.eth": "Ethereum verdéngen", "earn.config.currency.on_chain_address_subtitle": "Onchain-<PERSON><PERSON>", "earn.config.currency.us_dollars": "Bankiwwerweisungen ariichten", "earn.configured_widget.current_apy.title": "Aktuellen APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} pro Joer", "earn.confirm.currency.cta": "<PERSON><PERSON><PERSON><PERSON>", "earn.currency.eth": "Ethereum verdéngen", "earn.deploy.status.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.deploy.status.title_with_taker": "<PERSON><PERSON><PERSON><PERSON> {title} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn.deposit.amount_to_deposit": "Betrag fir anzebezuelen", "earn.deposit.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn.deposit.enter_amount": "<PERSON><PERSON><PERSON> e <PERSON> an", "earn.deposit.no_routes_found": "Keng <PERSON> fonnt", "earn.deposit.not_enough_balance": "Net genuch Saldo", "earn.deposit.select-currency.title": "Token fir d'Abezuelung auswielen", "earn.deposit.select_account.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.desposit_form.title": "An Earn a<PERSON>n", "earn.earn_deposit.status.title": "<PERSON> den Earn-<PERSON><PERSON> a<PERSON>n", "earn.earn_deposit.trx.title": "Alog op Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "<PERSON><PERSON> <PERSON> zu all Moment of", "earn.earn_withdraw.status.title": "<PERSON><PERSON>n-<PERSON><PERSON>", "earn.earn_withdraw.trx.title.approval": "Ofhiewung guttgläichen", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Of<PERSON>ewen an {asset}", "earn.earn_withdraw.trx.title.withdrawal": "<PERSON><PERSON><PERSON><PERSON> vun Earn", "earn.recharge.cta": "Ännerunge späicheren", "earn.recharge.earn_not_configured.enable_some_account.error": "Kont aktivéieren", "earn.recharge.earn_not_configured.enter_amount.error": "<PERSON><PERSON> aginn", "earn.recharge.select_taker.header": "<PERSON><PERSON> oplueden an der Reiefolleg vun", "earn.recharge_card_tag.on": "un", "earn.recharge_card_tag.recharge": "<PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharge_not_configured": "Automatesch Oplueden", "earn.recharge_card_tag.recharge_off": "Oplueden aus", "earn.recharge_card_tag.recharged": "Opgelueden", "earn.recharge_card_tag.recharging": "<PERSON><PERSON> op", "earn.recharge_configured.disable.trx.title": "Auto-Oplueden desaktivéieren", "earn.recharge_configured.trx.disclaimer": "<PERSON><PERSON> ben<PERSON>t Cowswap. Tauxe kënne variéieren.", "earn.recharge_configured.trx.subtitle": "Automatesch Opluedung no all Bezuelung. {value}", "earn.recharge_configured.trx.title": "Auto-Oplueden astellen op {value}", "earn.recharge_configured.updated.trx.title": "Oplued-Astellunge späicheren", "earn.risk-banner.subtitle": "Dëst ass e private Produit ouni reglementaresche Schutz géint <PERSON>.", "earn.risk-banner.title": "<PERSON>'R<PERSON><PERSON> verstoen", "earn.set_recharge.status.title": "Auto-Oplueden astellen", "earn.setup_reacharge.input.disable.label": "Desaktivéieren", "earn.setup_reacharge.input.label": "<PERSON>il-<PERSON><PERSON> v<PERSON>", "earn.setup_reacharge_form.title": "Auto-Re<PERSON>rge hält deng{br}Kaart um selwechte Saldo", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Hal wstETH (Staked ETH) op der Gnosis Chain a léin iwwer Lido aus.", "earn.taker-bulletlist.point_1": "Verdéng {apyValue} pro Joer. D'Rendementer variéiere ma<PERSON>.", "earn.taker-bulletlist.point_3": "Zeal verlaangt keng <PERSON>.", "earn.taker-historical-returns": "<PERSON><PERSON>sch Rendement<PERSON>", "earn.taker-historical-returns.chf": "Wuesstem vum CHF an USD", "earn.taker-investment-tile.apy.perYear": "pro Joer", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Verdéngen duerch Aave op der Gnosis Chain", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Verdéngen mat Frankencoin op Gnosis Chain", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Verdéngen duerch Lido op der Gnosis Chain", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Verdéngen duerch Maker op der Gnosis Chain", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "<PERSON><PERSON> verdéngen", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH verdéngen", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "US-Staatsublidië (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Obschonn extrem seelen, ass et theoretesch méiglech. Deng Sue si duerch strikt Risikogestioun an héich Ofsécherung geschützt. De realistesche schlëmmste Fall wier eng nach ni virdru gesi<PERSON>, wéi zum Beispill wann e puer Stablecoins gläichzäiteg hir Ubindung verléieren – eppes, wat nach ni geschitt ass.", "earn.usd.can-I-lose-my-principal-popup.title": "Kann ech mäin Haaptkapital realistesch verléieren an ënner wéi engen Ëmstänn?", "earn.usd.ftx-difference-popup.text": "Sky ass grondsät<PERSON>ch anescht. Am Géigesaz zu FTX, <PERSON><PERSON><PERSON>, BlockFi oder Luna – déi staark op zentraliséiert Verwahrung, ontransparent Verméigensverwaltung a riskant gehebelt Positioune gesat hunn – notzt Sky USD transparent, iwwerpréiften, dezentraliséiert Smart Contracts a bitt voll On-Chain-Transparenz. Du behäls déi komplett Kontroll iwwert däi private Portmonni, wat d'Géigeparteirisiken, déi mat zentraliséierte Feeler verbonne sinn, däitlech reduzéiert.", "earn.usd.ftx-difference-popup.title": "<PERSON><PERSON><PERSON> sech dat vun FT<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>i oder Luna?", "earn.usd.high-returns-popup.text": "Sky USD generéiert Rendementer haaptsächlech duerch dezentraliséiert Fin<PERSON>ller (DeFi), dé<PERSON>-to-Peer-Pr<PERSON>ten an d'Bereetstelle vu Liquiditéit automatiséieren, an domat traditionell Bankkäschten an Intermediairen eliminéieren. <PERSON><PERSON><PERSON>, kombin<PERSON><PERSON><PERSON> mat robuste Risikokontrollen, erméiglecht däitlech méi héich Rendementer am Verglach mat traditionelle Banken.", "earn.usd.high-returns-popup.title": "Wéi k<PERSON>nnen d'Rendementer esou héich sinn, besonnesch am Verglach mat traditionelle Banken?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD ass voll ofgedeckt an iwwerséchert duerch eng Kombinatioun vun digitalen Anlagen a séchere Smart Contracts a Real-World-Assets wéi US-Schatzbilljeeën. D'Reserve kënnen an Echtzäit onchain iwwerpréift ginn, souguer aus Zeal eraus, wat Transparenz a Sécherheet bitt. Am onwarscheinleche Fall, datt <PERSON>h<PERSON>, bleiwen deng Anlagen onchain geséchert, voll ënner denger Kontroll an iwwer aner kompatibel Wallets zougänglech.", "earn.usd.how-is-sky-backed-popup.title": "Wéi ass Sky USD ofgeséchert a wat geschitt mat menge <PERSON>, wann Zeal Faillite mécht?", "earn.usd.insurance-popup.text": "Sky USD-Suen sinn net FDIC-verséchert oder duerch traditionell staatlech Garantië gedeckt, well et e Kont op Basis vun digitalen Anlagen ass, an net e konventionelle Bankkonto. Amplaz geréiert Sky d'ganz Risikoreduktioun duerch iwwerpréifte Smart Contracts a suergfälteg ausgewielte DeFi-Protokoller, fir sécherzestellen, datt d'Anlage sécher an transparent bleiwen.", "earn.usd.insurance-popup.title": "Sinn meng Suen duerch eng Entitéit (wéi FDIC oder änleches) verséchert oder garantéiert?", "earn.usd.lending-operations-popup.text": "Sky USD generéiert Rendement duerch d'Verléine vu Stablecoins iwwer dezentraliséiert Prêtmäert wéi Morpho a Spark. Deng Stablecoins gi u Prêtnehmer verl<PERSON>hen, déi däitlech méi Sécherheete – wéi ETH oder BTC – ha<PERSON>leeën, wéi de Wäert vun hirem Prêt. <PERSON><PERSON><PERSON>, Iwwersécherung genannt, g<PERSON><PERSON><PERSON><PERSON>, datt ëmmer genuch Sécherheete fir d'Ofdeckung vun de Prêten do sinn, wat de Risiko staark reduzéiert. Déi agesammelt Zënsen an heiansdo Liquidatiounskäschten, déi vun de Prêtnehmer bezuelt ginn, suerge fir zouverlä<PERSON>g, transparent a sécher Rendementer.", "earn.usd.lending-operations-popup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.usd.market-making-operations-popup.text": "Sky USD verdéngt z<PERSON>ätzlech Rendement duerch d'Participatioun un dezentraliséierten Echange-Plattformen (AMMs) wéi Curve oder Uniswap. Andeems Liquiditéit bereetgestallt gëtt – andeems deng Stablecoins a Pools gesat ginn, déi de Krypto-Handel erméiglechen – sammelt Sky USD d'Käschten, déi duerch den Handel entstinn. Dës Liquiditéitspools gi suergfälteg ausgewielt, fir d'Volatilitéit ze minimiséieren, haaptsächlech mat Stablecoin-zu-Stablecoin-Puer, fir Risike wéi den Impermanent Loss däitlech ze reduzéieren an deng Anlagen souwuel sécher wéi och zougänglech ze halen.", "earn.usd.market-making-operations-popup.title": "Market-Making-Operatiounen", "earn.usd.treasury-operations-popup.text": "Sky USD generéiert stabil a konstant Rendementer duerch strategesch Treasury-Investissementer. En Deel vun denge Stablecoin-Abezuelunge gëtt a sécher, risikoaarm Real-World-Assets investéiert – haaptsächlech kuerzfristeg Staatsanleihen an héichsécher Kredittinstrumenter. Dëse Prinzip, ähnlech wéi beim traditionelle Banking, suergt fir viraussiichtbar an zouverlässeg Rendementer. Deng Anlag bleift sécher, liquid an transparent geréiert.", "earn.usd.treasury-operations-popup.title": "Treasury-Operatiounen", "earn.view_earn.card_rechard_off": "Aus", "earn.view_earn.card_rechard_on": "Un", "earn.view_earn.card_recharge": "<PERSON><PERSON>", "earn.view_earn.total_balance_label": "Verd<PERSON>gs {percentage} pro Joer", "earn.view_earn.total_earnings_label": "Gesamtertrag", "earn.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "earn.withdraw.amount_to_withdraw": "Betrag fir opzehiewen", "earn.withdraw.enter_amount": "<PERSON><PERSON> aginn", "earn.withdraw.loading": "<PERSON><PERSON><PERSON>", "earn.withdraw.no_routes_found": "Keng <PERSON> fonnt", "earn.withdraw.not_enough_balance": "Net genuch Saldo", "earn.withdraw.select-currency.title": "Token au<PERSON>wielen", "earn.withdraw.select_to_token": "Token au<PERSON>wielen", "earn.withdraw.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "earn.withdraw_form.title": "<PERSON><PERSON>n op<PERSON>en", "earnings-view.earnings": "Gesamtverdéngschter", "edit-account-owners.add-owner.add-wallet": "Bes<PERSON>tz<PERSON> dobä<PERSON>tzen", "edit-account-owners.add-owner.add_wallet": "Bes<PERSON>tz<PERSON> dobä<PERSON>tzen", "edit-account-owners.add-owner.title": "Kaartebesëtzer dobäisetzen", "edit-account-owners.card-owners": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.external-wallet": "<PERSON><PERSON><PERSON>", "editBankRecipient.title": "<PERSON><PERSON><PERSON>", "editNetwork.addCustomRPC": "Personaliséierten RPC-K<PERSON>et bäifügen", "editNetwork.cannot_verify.subtitle": "De personaliséierten RPC-Knuet reagéiert net. Kuck d'URL no a probéier nach eemol.", "editNetwork.cannot_verify.title": "Mir kënnen den RPC-Knuet net verifizéieren", "editNetwork.cannot_verify.try_again": "Nach eemol", "editNetwork.customRPCNode": "Personaliséierten RPC-Knuet", "editNetwork.defaultRPC": "Standard-RPC", "editNetwork.networkRPC": "Netzwierk-RPC", "editNetwork.rpc_url.cannot_be_empty": "<PERSON><PERSON><PERSON><PERSON>", "editNetwork.rpc_url.not_a_valid_https_url": "Muss eng gëlteg HTTP(S) URL sinn", "editNetwork.safetyWarning.subtitle": "Zeal kann d'Pri<PERSON><PERSON><PERSON><PERSON><PERSON>, Zouverlässegkeet a Sécherheet vu personaliséierten RPCen net garantéieren. Bass du sécher, dass du e personaliséierte RPC-Knuet benotze wëlls?", "editNetwork.safetyWarning.title": "Personaliséiert RPCe kënnen onsécher sinn", "editNetwork.zealRPCNode": "Zeal RPC-K<PERSON><PERSON>", "editNetworkRpc.headerTitle": "Personaliséierte RPC-Knuet", "editNetworkRpc.rpcNodeUrl": "RPC-Knuet-URL", "editing-locked.modal.description": "Anescht wéi bei Geneemegungstransaktiounen erlabe Permisën et net, d'Ausgabelimit oder d'Oflafzäit z'änneren. Vergewësser dech, dass du enger dApp vertra<PERSON>, ier s du e Permis ofsch<PERSON>.", "editing-locked.modal.title": "Ännerunge gespaart", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Oplueden gëtt aktivéiert", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Oplueden aktivéiert", "enterCardnumber": "<PERSON><PERSON><PERSON><PERSON><PERSON> aginn", "error.connectivity_error.subtitle": "<PERSON>ck w.e.g. deng Internetverbindung no a probéier nach eng Kéier.", "error.connectivity_error.title": "<PERSON><PERSON>", "error.decrypt_incorrect_password.title": "<PERSON><PERSON><PERSON>", "error.encrypted_object_invalid_format.title": "Beschiedegt Donnéeën", "error.failed_to_fetch_google_auth_token.title": "Mir kru<PERSON>", "error.list.item.cta.action": "Nach eng <PERSON><PERSON> pro<PERSON>en", "error.trezor_action_cancelled.title": "Transaktioun refu<PERSON><PERSON><PERSON><PERSON>", "error.trezor_device_used_elsewhere.title": "Apparat g<PERSON>tt an enger anerer <PERSON><PERSON><PERSON><PERSON> ben<PERSON>t", "error.trezor_method_cancelled.title": "Konnt den Trezor net synchroniséieren", "error.trezor_permissions_not_granted.title": "Konnt den Trezor net synchroniséieren", "error.trezor_pin_cancelled.title": "Konnt den Trezor net synchroniséieren", "error.trezor_popup_closed.title": "Konnt den Trezor net synchroniséieren", "error.unblock_account_number_and_sort_code_mismatch": "Kontosnummer a Bankleitzuel stëmmen net iwwereneen", "error.unblock_can_not_change_details_after_kyc": "Ännerunge sinn nom KYC net méi méiglech.", "error.unblock_hard_kyc_failure": "Onerwaarte KYC-Status", "error.unblock_invalid_faster_payment_configuration.title": "Dës Bank ënnerstëtzt keng Faster Payments", "error.unblock_invalid_iban": "Ongëlteg IBAN", "error.unblock_session_expired.title": "Unblock-<PERSON><PERSON><PERSON><PERSON>gelaf", "error.unblock_user_with_address_already_exists.title": "<PERSON><PERSON> fir dës <PERSON> schonn", "error.unblock_user_with_such_email_already_exists.title": "<PERSON><PERSON><PERSON> mat dëser E-Mail existéiert schonn", "error.unknown_error.error_message": "Feelermeldung: ", "error.unknown_error.subtitle": "Pardon! Wanns du dréngend <PERSON><PERSON><PERSON><PERSON>, kontaktéier de Support an deel d'Detailer hei dr<PERSON><PERSON> mat.", "error.unknown_error.title": "Systemfeeler", "eth-cost-warning-modal.subtitle": "Smart Wallets funktionéieren op Ethereum, mee d'Käschte si ganz héich a mir recommandéiere STAARK, aner Netzwierker ze benotzen.", "eth-cost-warning-modal.title": "Vermeid Ethereum - Netzwierkskäschte sinn héich", "exchange.form.button.chain_unsupported": "Chain gëtt net ënnerstëtzt", "exchange.form.button.refreshing": "Gëtt aktualiséiert", "exchange.form.error.asset_not_supported.button": "En aneren <PERSON> au<PERSON>n", "exchange.form.error.asset_not_supported.description": "D'Bréck ënnerstëtzt dësen Asset net.", "exchange.form.error.asset_not_supported.title": "Asset net ënnerstëtzt", "exchange.form.error.bridge_quote_timeout.button": "En aneren <PERSON> au<PERSON>n", "exchange.form.error.bridge_quote_timeout.description": "<PERSON><PERSON><PERSON><PERSON> eng aner <PERSON>", "exchange.form.error.bridge_quote_timeout.title": "<PERSON><PERSON> fonnt", "exchange.form.error.different_receiver_not_supported.button": "Alternativen Empfänger ewechhuelen", "exchange.form.error.different_receiver_not_supported.description": "Dësen Exchange ënnerstëtzt d'Schécken un eng aner Adress net.", "exchange.form.error.different_receiver_not_supported.title": "D'Adress fir ze schécken an z'empfänke muss déi selwecht sinn", "exchange.form.error.insufficient_input_amount.button": "<PERSON><PERSON>", "exchange.form.error.insufficient_liquidity.button": "Betrag reduzéieren", "exchange.form.error.insufficient_liquidity.description": "D'Bréck huet net genuch Assets. Probéier e méi klenge Betrag.", "exchange.form.error.insufficient_liquidity.title": "Betrag ze héich", "exchange.form.error.max_amount_exceeded.button": "Betrag reduzéieren", "exchange.form.error.max_amount_exceeded.description": "De maximale Betrag gouf iwwerschratt.", "exchange.form.error.max_amount_exceeded.title": "Betrag ze héich", "exchange.form.error.min_amount_not_met.button": "<PERSON><PERSON>", "exchange.form.error.min_amount_not_met.description": "De Mindestbetrag fir den Echange vun dësem Token ass net erreecht.", "exchange.form.error.min_amount_not_met.description_with_amount": "<PERSON> Mindestbetrag fir den Echange ass {amount}.", "exchange.form.error.min_amount_not_met.title": "Betrag ze niddreg", "exchange.form.error.min_amount_not_met.title_increase": "<PERSON><PERSON>", "exchange.form.error.no_routes_found.button": "En aneren <PERSON> au<PERSON>n", "exchange.form.error.no_routes_found.description": "Et gëtt keng <PERSON>-Route fir dës <PERSON>/Netzwierk-Kombinatioun.", "exchange.form.error.no_routes_found.title": "<PERSON><PERSON> disponibel", "exchange.form.error.not_enough_balance.button": "Betrag reduzéieren", "exchange.form.error.not_enough_balance.description": "Du hues net genuch vun dësem Asset fir d'Transaktioun.", "exchange.form.error.not_enough_balance.title": "Saldo net duer", "exchange.form.error.slippage_passed_is_too_low.button": "Slippage erhéijen", "exchange.form.error.slippage_passed_is_too_low.description": "Den erlaabte Slippage ass ze niddreg fir d<PERSON>sen <PERSON>set.", "exchange.form.error.slippage_passed_is_too_low.title": "Slippage ze niddreg", "exchange.form.error.socket_internal_error.button": "<PERSON><PERSON><PERSON><PERSON> méi spéit nach eng Kéier", "exchange.form.error.socket_internal_error.description": "De Bridging-Partner h<PERSON><PERSON>. Probéier méi spéit nach eng Kéier.", "exchange.form.error.socket_internal_error.title": "Feeler beim <PERSON>-Partner", "exchange.form.error.stargatev2_requires_fee_in_native": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Setz {amount} bäi fir d'Transaktioun ofzeschléissen", "exchange.form.error.stargatev2_requires_fee_in_native.title": "<PERSON><PERSON><PERSON> méi {symbol}", "expiration-info.modal.description": "D'Oflafz<PERSON><PERSON> ass, wéi laang eng App deng To<PERSON> benotzen däerf. <PERSON><PERSON> d'<PERSON>äit eriwwer ass, verl<PERSON><PERSON>e si <PERSON> Zougank, bis du eppes anescht sees. Fir sécher ze ble<PERSON>wen, halt d'Oflafzäit kuerz.", "expiration-info.modal.title": "Wat ass d'Of<PERSON>f<PERSON>?", "expiration-time.high.modal.text": "Oflafzäite sollten kuerz sinn an dorop baséieren, wéi laang s du se wierklech brauchs. Laang Zäite si riskant a ginn Abzocker méi eng grous<PERSON>, deng To<PERSON> ze mëssbrauchen.", "expiration-time.high.modal.title": "<PERSON>ang <PERSON>", "failed.transaction.content": "Transaktioun feelt warscheinlech", "fee.unknown": "Onbekannt", "feedback-request.leave-message": "<PERSON> <PERSON>", "feedback-request.not-now": "Net elo", "feedback-request.title": "Merci! Wéi kënne mir Zeal verbesseren?", "float.input.period": "Dezimaltrennzeechen", "gnosis-activate-card.info-popup.subtitle": "Éischt Kéier: <PERSON><PERSON> as<PERSON> a PIN aginn.", "gnosis-activate-card.info-popup.title": "Éischt Bezuelung brauch Chip & PIN", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "<PERSON><PERSON><PERSON> deng <PERSON> an fir se z'aktivéieren.", "gnosis-activate-card.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gnosis-pay-re-kyc-widget.btn-text": "Verifizéieren", "gnosis-pay-re-kyc-widget.title.not-started": "Verifizéier deng Identitéit", "gnosis-pay.login.cta": "Bestoende Kont verbannen", "gnosis-pay.login.title": "Du hues schonn e Gnosis <PERSON>", "gnosis-signup.confirm.subtitle": "<PERSON>ck no enger E-Mail vu Gnosis Pay, si kéint an dengem Spam-Do<PERSON><PERSON> sinn.", "gnosis-signup.confirm.title": "Keng Verifizéierungs-E-Mail kritt?", "gnosis-signup.continue": "<PERSON><PERSON>", "gnosis-signup.dont_link_accounts": "Konte net verbannen", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "<PERSON><PERSON><PERSON> <EMAIL> an", "gnosis-signup.enter-email.title": "E-Mail aginn", "gnosis-signup.title": "Ech hu gelies an akzeptéiere Gnosis Pay seng <linkGnosisTNC>Allgemeng Konditiounen</linkGnosisTNC> <monovateTerms>d'Ka<PERSON>besëtzer-Konditiounen</monovateTerms> an <linkMonerium>dem Monerium seng Allgemeng Konditiounen</linkMonerium>.", "gnosis-signup.verify-email.title": "E-Mail verifizéieren", "gnosis.confirm.subtitle": "Kee Code kritt? <PERSON><PERSON><PERSON><PERSON><PERSON>, ob deng Telefonsnummer richteg ass", "gnosis.confirm.title": "Code geschéckt un {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Verifizéieren", "gnosisPayAccountStatus.success.title": "<PERSON><PERSON>", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay ass nach net an dengem Land verfügbar", "gnosisPayNoActiveCardsFound.title": "Keng aktiv Kaarten", "gnosis_pay_card_delay_relay_not_empty_error.title": "Deng Transaktioun konnt de Moment net veraarbecht ginn. Probéier w.e.g. méi spéit nach eng Kéier.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "<PERSON><PERSON> net méiglech", "gnosiskyc.modal.approved.activate-free-card": "<PERSON><PERSON><PERSON> aktiv<PERSON>", "gnosiskyc.modal.approved.button-text": "Vum Bankkonto abezuelen", "gnosiskyc.modal.approved.title": "Deng perséinlech Kontodetailer si fäerdeg.", "gnosiskyc.modal.failed.close": "Zoumaachen", "gnosiskyc.modal.failed.title": "<PERSON><PERSON>, eise Partner Gnosis Pay kann kee Kont fir dech erstellen", "gnosiskyc.modal.in-progress.title": "D'ID-Verifizéierung kann 24 Stonnen oder méi laang daueren. W.e.g. e bësse Gedold", "goToSettingsPopup.settings": "Astellungen", "goToSettingsPopup.title": "Aktiv<PERSON><PERSON> Notifika<PERSON> zu all Moment an dengen Astellungen.", "google_file.error.failed_to_fetch_auth_token.button_title": "Nach eng <PERSON><PERSON> pro<PERSON>en", "google_file.error.failed_to_fetch_auth_token.subtitle": "Fir eis d'Benotzung vun dengem Recovery File z'erlaben, g<PERSON>ff eis w.e.g. den Zougang op denger perséinlecher Cloud.", "google_file.error.failed_to_fetch_auth_token.title": "Mir kru<PERSON>", "hidden_tokens.widget.emptyState": "<PERSON>g verst<PERSON>", "how_to_connect_to_metamask.got_it": "OK, verstanen", "how_to_connect_to_metamask.story.subtitle": "<PERSON><PERSON><PERSON> zu all Moment einfach tëscht Zeal an anere Portmonnien.", "how_to_connect_to_metamask.story.title": "Zeal funktioné<PERSON>t niewent anere Portmonnien", "how_to_connect_to_metamask.why_switch": "Firwat tëscht Zeal an anere Portmonnie wiesselen?", "how_to_connect_to_metamask.why_switch.description": "Egal wéi e Portmonni s du wiels, du kriss ëmmer dem Zeal seng Sécherheetskontrollen, déi dech viru béisaartege Säiten an Transaktioune schützen.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "<PERSON>, et <PERSON> s<PERSON>, de <PERSON><PERSON> ze woen an en neie Portmonni ze benotzen. <PERSON><PERSON><PERSON> hu mir et ein<PERSON>ch g<PERSON>, Zeal niewent dengem existente Portmonni ze benotzen. <PERSON><PERSON><PERSON> zu all Moment.", "import-bank-transfer-owner.banner.title": "De Wallet fir Banktransferen huet geännert. Importéier en hei fir weiderzefueren.", "import-bank-transfer-owner.title": "Importéier d<PERSON><PERSON>, fir Bankiwwerweisungen op dësem Apparat ze notzen.", "import_gnosispay_wallet.add-another-card-owner.footnote": "Privat Schlëssel oder Seed-Phrase import<PERSON>, deen zu denger Gnosis <PERSON> geh<PERSON>t", "import_gnosispay_wallet.primaryText": "Gnosis Pay Portmonni importéieren", "injected-wallet": "Browser-<PERSON><PERSON><PERSON>", "intercom.getHelp": "<PERSON><PERSON><PERSON><PERSON>", "invalid_iban.got_it": "Verstanen", "invalid_iban.subtitle": "Den aginnen IBAN ass net gülteg. Kuck wgl. no, ob d'Detailer richteg sinn a probéier nach eng Kéier.", "invalid_iban.title": "Ongültegen IBAN", "keypad-0": "Tastaturknäppchen 0", "keypad-1": "Tastaturknäppchen 1", "keypad-2": "Tastaturknäppchen 2", "keypad-3": "Tastaturknäppchen 3", "keypad-4": "Tastaturknäppchen 4", "keypad-5": "Tastaturknäppchen 5", "keypad-6": "Tastaturknäppchen 6", "keypad-7": "Tastaturknäppchen 7", "keypad-8": "Tastaturknäppchen 8", "keypad-9": "Tastaturknäppchen 9", "keypad.biometric-button": "Biometresche Knäppche vum Zifferfeld", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Geheime Saz ofgeséchert 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "<PERSON><PERSON><PERSON> eng sécher offline Kopie vun dengem geheime Saz, fir däi Verméige spéider kënnen ze restauréieren", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "Probéier net d'Wuert ze roden", "keystore.write_secret_phrase.before_you_begin.first_point": "<PERSON><PERSON> ve<PERSON>, datt jiddereen mat mengem geheime Saz mäi Verméigen iwwerweise kann", "keystore.write_secret_phrase.before_you_begin.second_point": "<PERSON>ch si verantwort<PERSON>ch, mäi geheime Sa<PERSON> geheim a sécher ze halen", "keystore.write_secret_phrase.before_you_begin.subtitle": "Lies an akzeptéier wgl. déi folgend <PERSON>:", "keystore.write_secret_phrase.before_you_begin.third_point": "Ech sinn op enger privater P<PERSON>z ouni aner Leit oder Kameraen a menger Géigend", "keystore.write_secret_phrase.before_you_begin.title": "<PERSON><PERSON> <PERSON> de uf<PERSON>nks", "keystore.write_secret_phrase.secret_phrase_test.title": "Wat ass d'<PERSON>ert {count} an dengem geheime Saz?", "keystore.write_secret_phrase.test_ps.lets_do_it": "Maache mer dat", "keystore.write_secret_phrase.test_ps.subtitle": "Du brauchs däi geheime Saz, fir däi Kont op dësem oder aneren Apparater ze restauréieren. <PERSON><PERSON> eis testen, ob däi geheime Saz richteg opgeschriwwen ass.", "keystore.write_secret_phrase.test_ps.subtitle2": "Mir froen dech no {count} Wierder aus dengem Saz.", "keystore.write_secret_phrase.test_ps.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> testen", "kyc.modal.approved.button-text": "Bankiwwerweisung maachen", "kyc.modal.approved.subtitle": "Deng Verifizéierung ass ofgeschloss. Du kanns elo onbegrenzt Bankiwwerweisunge maachen.", "kyc.modal.approved.title": "Bankiwwerweisunge fräigeschalt", "kyc.modal.continue-with-partner.button-text": "<PERSON><PERSON>", "kyc.modal.continue-with-partner.subtitle": "Mir leeden dech elo un eise Partner weider, fir deng Dokumenter ze sammelen an d'Verifizéierung ofzeschléissen.", "kyc.modal.continue-with-partner.title": "Mat eisem Partner weiderfueren", "kyc.modal.failed.unblock.subtitle": "Unblock huet deng Identitéitsverifizéierung net approuvéiert a kann dir keng Bankiwwerweisungen ubidden.", "kyc.modal.failed.unblock.title": "Unblock-Ufro net approuvéiert", "kyc.modal.paused.button-text": "Detailer aktualiséieren", "kyc.modal.paused.subtitle": "Et gesäit aus, wéi wann e puer vun dengen Informatioune falsch wieren. Probéier wgl. nach eng Kéier a kontrolléier deng Detailer ier s du se ofschécks.", "kyc.modal.paused.title": "Deng Detailer schénge falsch ze sinn", "kyc.modal.pending.button-text": "Zoumaachen", "kyc.modal.pending.subtitle": "D'Verifizéierung dauert normalerweis manner wéi 10 Minutten, mee heiansdo kann et e bësse méi laang daueren.", "kyc.modal.pending.title": "Mir halen dech um Lafenden", "kyc.modal.required.cta": "Verifizéierung starten", "kyc.modal.required.subtitle": "Du hues d'Transaktiounslimit erreecht. Verifizéier wgl. deng Identitéit fir weiderzefueren. Dat dauert normalerweis nëmmen e puer Minutten a verlaangt perséinlech Detailer an Dokumenter.", "kyc.modal.required.title": "Identitéitsverifizéierung néideg", "kyc.submitted": "<PERSON><PERSON><PERSON>", "kyc.submitted_short": "<PERSON><PERSON><PERSON>", "kyc_status.completed_status": "Ofgeschloss", "kyc_status.failed_status": "Echec", "kyc_status.paused_status": "Iwwer<PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc_status.subtitle": "Bankiwwerweisungen", "kyc_status.subtitle.wrong_details": "<PERSON><PERSON><PERSON>", "kyc_status.subtitle_in_progress": "<PERSON>", "kyc_status.title": "Identitéit gëtt verifi<PERSON>", "label.close": "Zoumaachen", "label.saving": "Späichert...", "labels.this-month": "<PERSON><PERSON><PERSON>", "labels.today": "<PERSON><PERSON>", "labels.yesterday": "G<PERSON><PERSON><PERSON>", "language.selector.title": "<PERSON><PERSON><PERSON><PERSON>", "ledger.account_loaded.imported": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.add.success.title": "Ledger erfollegr<PERSON><PERSON> verbonnen 🎉", "ledger.connect.cta": "Ledger synchr<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.connect.step1": "Verbann de Ledger mat dengem Apparat", "ledger.connect.step2": "Maach d'Ethereum-App um Ledger op", "ledger.connect.step3": "Synchroniséier dann däi <PERSON>ger 👇", "ledger.connect.subtitle": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON> den<PERSON>-Portmonnien op Zeal z'importéieren", "ledger.connect.title": "<PERSON><PERSON><PERSON><PERSON> de Ledger mat <PERSON>", "ledger.error.ledger_is_locked.subtitle": "<PERSON><PERSON><PERSON><PERSON> den Ledger op an öffn d'Ethereum-App", "ledger.error.ledger_is_locked.title": "Ledger ass gespaart", "ledger.error.ledger_not_connected.action": "Ledger synchr<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.error.ledger_not_connected.subtitle": "Verbënn däin Hardware-Portmonni mat dengem Apparat an öffn d'Ethereum-App", "ledger.error.ledger_not_connected.title": "Ledger ass net verbonnen", "ledger.error.ledger_running_non_eth_app.title": "Ethereum-App ass net op", "ledger.error.user_trx_denied_by_user.action": "Zoumaachen", "ledger.error.user_trx_denied_by_user.subtitle": "Du hues d'Transaktioun op dengem Hardware-Portmonni refuséiert", "ledger.error.user_trx_denied_by_user.title": "Transaktioun refu<PERSON><PERSON><PERSON><PERSON>", "ledger.hd_path.bip44.subtitle": "z. B. <PERSON>, Trezor", "ledger.hd_path.bip44.title": "BIP44-Standard", "ledger.hd_path.ledger_live.subtitle": "Standard", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Legacy", "ledger.hd_path.phantom.subtitle": "z. B. Phantom", "ledger.select.hd_path.subtitle": "HD-Pied sinn d'A<PERSON> a <PERSON>, wéi Hardware Portmonnien hir Konte sortéieren. Dat ass ähnlech wéi en Index Säiten an engem Buch sortéiert.", "ledger.select.hd_path.title": "HD-<PERSON><PERSON>", "ledger.select_account.import_wallets_count": "{count,plural,=0{<PERSON><PERSON>} one{Importéier Port<PERSON>} other{Importéier {count} Portmonnien}}", "ledger.select_account.path_settings": "Pad-Astellungen", "ledger.select_account.subtitle": "Gesäis du net déi er<PERSON>arte Portmonnien? <PERSON><PERSON><PERSON><PERSON>, d'Pad-Astellungen z'änneren.", "ledger.select_account.subtitle.group_header": "Portmonnien", "ledger.select_account.title": "Ledger-Portmonnien importéieren", "legend.lending-operations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legend.market_making-operations": "Market-Making-Operatiounen", "legend.treasury-operations": "Treasury-Operatiounen", "link-existing-monerium-account-sign.button": "Zeal verbannen", "link-existing-monerium-account-sign.subtitle": "Du hues schonn e Monerium-Kont.", "link-existing-monerium-account-sign.title": "Verbann Zeal mat dengem Monerium-<PERSON>nt", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Du hues schonn e Monerium-Kont. Géi wgl. op d'Monerium-App fir alles fäerdeg ze maachen.", "link-existing-monerium-account.title": "Géi op Monerium fir däi Kont ze verbannen", "loading.pin": "PIN gëtt gelueden ...", "lockScreen.passwordIncorrectMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> ass falsch", "lockScreen.passwordRequiredMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lockScreen.unlock.header": "Opspären", "lockScreen.unlock.subheader": "<PERSON><PERSON><PERSON> däi <PERSON>wuert fir Zeal opzespären", "mainTabs.activity.label": "Aktivitéit", "mainTabs.browse.label": "Entdecken", "mainTabs.browse.title": "Entdecken", "mainTabs.card.label": "<PERSON><PERSON>", "mainTabs.portfolio.label": "Portfolio", "mainTabs.rewards.label": "Belounungen", "makeSpendable.cta": "Verfügbar maachen", "makeSpendable.holdAsCash": "Als Boergeld halen", "makeSpendable.shortText": "V<PERSON><PERSON>g {apy} pro Joer", "makeSpendable.title": "{amount} kritt", "merchantCategory.agriculture": "Landwirtschaft", "merchantCategory.alcohol": "Alkohol", "merchantCategory.antiques": "Antiquitéiten", "merchantCategory.appliances": "Haushaltsapparater", "merchantCategory.artGalleries": "Konschtgalerien", "merchantCategory.autoRepair": "Autosreparatur", "merchantCategory.autoRepairService": "Autosreparaturservice", "merchantCategory.beautyFitnessSpas": "Schéinheet, Fitness & Spa", "merchantCategory.beautyPersonalCare": "Schéinheet & Kierperfleeg", "merchantCategory.billiard": "<PERSON><PERSON>", "merchantCategory.books": "Bicher", "merchantCategory.bowling": "Bowling", "merchantCategory.businessProfessionalServices": "Geschäfts- & professionell Servicer", "merchantCategory.carRental": "Autoverloun", "merchantCategory.carWash": "Autoswäsch", "merchantCategory.cars": "Autoen", "merchantCategory.casino": "Casino", "merchantCategory.casinoGambling": "Casino & Glécksspill", "merchantCategory.cellular": "Handy", "merchantCategory.charity": "<PERSON><PERSON><PERSON>", "merchantCategory.childcare": "Kannerbetreiung", "merchantCategory.cigarette": "Zigaretten", "merchantCategory.cinema": "<PERSON><PERSON>", "merchantCategory.cinemaEvents": "Kino & Evenementer", "merchantCategory.cleaning": "<PERSON><PERSON>", "merchantCategory.cleaningMaintenance": "Botzen & Entretien", "merchantCategory.clothes": "<PERSON><PERSON><PERSON>", "merchantCategory.clothingServices": "Gezei-Servicer", "merchantCategory.communicationServices": "Kommunikatiounsservicer", "merchantCategory.construction": "Bau", "merchantCategory.cosmetics": "Kosmetik", "merchantCategory.craftsArtSupplies": "Bastel- & Konschtmaterial", "merchantCategory.datingServices": "<PERSON><PERSON>-Servicer", "merchantCategory.delivery": "Liwwerung", "merchantCategory.dentist": "Zänndokter", "merchantCategory.departmentStores": "Groussmagasinn", "merchantCategory.directMarketingSubscription": "Direktmarketing & Abonnement", "merchantCategory.discountStores": "Discountgeschäfter", "merchantCategory.drugs": "Medikamenter", "merchantCategory.dutyFree": "Duty Free", "merchantCategory.education": "Bildung", "merchantCategory.electricity": "Stroum", "merchantCategory.electronics": "Elektronik", "merchantCategory.emergencyServices": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.equipmentRental": "Locatioun vun Au<PERSON>rëschtung", "merchantCategory.evCharging": "Opluede vun E-Autoen", "merchantCategory.financialInstitutions": "Finanzinstituter", "merchantCategory.financialProfessionalServices": "Finanz- & professionell <PERSON>", "merchantCategory.finesPenalties": "Strofen & Geldstrofen", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "<PERSON><PERSON>", "merchantCategory.flowers": "<PERSON><PERSON><PERSON>", "merchantCategory.flowersGarden": "Blummen & Gaart", "merchantCategory.food": "<PERSON><PERSON><PERSON>", "merchantCategory.freight": "Fracht", "merchantCategory.fuel": "<PERSON><PERSON><PERSON>", "merchantCategory.funeralServices": "Begriefnesd<PERSON><PERSON>chter", "merchantCategory.furniture": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.games": "<PERSON><PERSON><PERSON>", "merchantCategory.gas": "Gas", "merchantCategory.generalMerchandiseRetail": "Allgemeng Wueren & Eenzelhandel", "merchantCategory.gifts": "Cadeauen", "merchantCategory.government": "Regierung", "merchantCategory.governmentServices": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.hardware": "Handwierksgeschir", "merchantCategory.healthMedicine": "Gesondheet & Medezin", "merchantCategory.homeImprovement": "Baumaart & Renovatioun", "merchantCategory.homeServices": "Déngschter fir doheem", "merchantCategory.hotel": "Hotel", "merchantCategory.housing": "<PERSON><PERSON><PERSON>", "merchantCategory.insurance": "Assurance", "merchantCategory.internet": "Internet", "merchantCategory.kids": "<PERSON><PERSON>", "merchantCategory.laundry": "<PERSON><PERSON><PERSON>", "merchantCategory.laundryCleaningServices": "Wäsch- & Botzservicer", "merchantCategory.legalGovernmentFees": "Juristesch & staatlech Käschten", "merchantCategory.luxuries": "Luxus", "merchantCategory.luxuriesCollectibles": "Luxusartikelen & Sammelstécker", "merchantCategory.magazines": "Zäitschrëften", "merchantCategory.magazinesNews": "Zäitschrëften & Noriichten", "merchantCategory.marketplaces": "<PERSON>artp<PERSON><PERSON>", "merchantCategory.media": "Medien", "merchantCategory.medicine": "Medezin", "merchantCategory.mobileHomes": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.moneyTransferCrypto": "Iwwerweisung & Krypto", "merchantCategory.musicRelated": "Muse<PERSON>", "merchantCategory.musicalInstruments": "Musikinstrumenter", "merchantCategory.optics": "Optik", "merchantCategory.organizationsClubs": "Organisatiounen & Veräiner", "merchantCategory.other": "<PERSON><PERSON><PERSON>", "merchantCategory.parking": "Parking", "merchantCategory.pawnShops": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.pets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.photoServicesSupplies": "Fotoservicer & -material", "merchantCategory.postalServices": "Postdéngschter", "merchantCategory.professionalServicesOther": "Profess<PERSON><PERSON> (Anerer)", "merchantCategory.publicTransport": "Ëffentlechen Transport", "merchantCategory.purchases": "Akeef", "merchantCategory.purchasesMiscServices": "Akeef & divers Déngschter", "merchantCategory.recreationServices": "Fräizäitdéngschter", "merchantCategory.religiousGoods": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.secondhandRetail": "Secondhand-<PERSON>", "merchantCategory.shoeHatRepair": "Reparatur vu Schong & Hitt", "merchantCategory.shoeRepair": "Schongreparatur", "merchantCategory.softwareApps": "Software & Appen", "merchantCategory.specializedRepairs": "Spezialiséiert Reparaturen", "merchantCategory.sport": "Sport", "merchantCategory.sportingGoods": "Sportsartikelen", "merchantCategory.sportingGoodsRecreation": "Sportsartikelen & Fräizäit", "merchantCategory.sportsClubsFields": "Sportveräiner & -terrainen", "merchantCategory.stationaryPrinting": "Schreifwueren & Dréckerei", "merchantCategory.stationery": "Schreifwueren", "merchantCategory.storage": "Lagerung", "merchantCategory.taxes": "Steieren", "merchantCategory.taxi": "Taxi", "merchantCategory.telecomEquipment": "Telekommunikatiouns-Ausrëschtung", "merchantCategory.telephony": "Telefonie", "merchantCategory.tobacco": "Tubak", "merchantCategory.tollRoad": "Ma<PERSON><PERSON><PERSON>", "merchantCategory.tourismAttractionsAmusement": "Tourismus, Attraktiounen & Spaass", "merchantCategory.towing": "Ofschleefdéngscht", "merchantCategory.toys": "Spillsaa<PERSON>", "merchantCategory.toysHobbies": "Spillsaachen & Hobbyen", "merchantCategory.trafficFine": "<PERSON><PERSON>f<PERSON><PERSON>", "merchantCategory.train": "<PERSON><PERSON>", "merchantCategory.travelAgency": "Reesagence", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, Radio & Streaming", "merchantCategory.utilities": "Niewekäschten", "merchantCategory.waterTransport": "Waassertransport", "merchantCategory.wholesaleClubs": "<PERSON><PERSON><PERSON><PERSON>", "metaMask.subtitle": "Aktivéier de MetaMask-Modus, fir all MetaMask-Verbindungen op Zeal ëmzeleeden. E Klick op MetaMask an dApps verbënnt dech dann mat <PERSON>.", "metaMask.title": "Kanns dech net mat <PERSON>eal verbannen?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Maach deng Bank-App op", "monerium-bank-deposit.buttet-point.receive-crypto": "Digital EUR kréien", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Schéck {fiatCurrencyCode} op däi Kont", "monerium-bank-deposit.deposit-account-country": "Land", "monerium-bank-deposit.header": "{fullName}— perséinle<PERSON> Kont", "monerium-bank-details.account-name": "Kontnumm", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT kopéiert", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT kopéiert", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN kopéiert", "monerium-bank-details.to-wallet": "Un de Portmonni", "monerium-bank-details.transfer-fee": "Iwwerweisungskäschten", "monerium-bank-transfer.enable-card.bullet-1": "Schléiss d'Identitéitsverifizéierung of", "monerium-bank-transfer.enable-card.bullet-2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>detail<PERSON>", "monerium-bank-transfer.enable-card.bullet-3": "Maach en Depot vun dengem Bankkonto", "monerium-card-delay-relay.success.cta": "Zoumaachen", "monerium-card-delay-relay.success.subtitle": "Aus Sécherheetsgrënn dauert et 3 Minutten, bis Ännerunge vun de Kaartenastellunge veraarbecht sinn.", "monerium-card-delay-relay.success.title": "Komm an 3 <PERSON><PERSON><PERSON> z<PERSON>, fir Monerium fäerdeg anzeriichten", "monerium-deposit.account-details-info-popup.bullet-point-1": "All {fiatCurrencyCode} , déi s du un dëse <PERSON>, ginn automatesch an {cryptoCurrencyCode} Tokens op der {cryptoCurrencyChain} Chain ëmgewandelt an un däi Portmonni geschéckt", "monerium-deposit.account-details-info-popup.bullet-point-2": "SCHÉCK NËMMEN {fiatCurrencyCode} ({fiatCurrencySymbol}) un däi Kont", "monerium-deposit.account-details-info-popup.title": "<PERSON><PERSON>", "monerium.check_order_status.sending": "<PERSON><PERSON><PERSON>", "monerium.not-eligible.cta": "<PERSON><PERSON><PERSON><PERSON>", "monerium.not-eligible.subtitle": "Monerium kann kee Kont fir dech opmaachen. Wiel wgl. en aneren Ubidder.", "monerium.not-eligible.title": "Probéier en aneren U<PERSON>der", "monerium.setup-card.cancel": "Ofbriechen", "monerium.setup-card.continue": "<PERSON><PERSON>", "monerium.setup-card.create_account": "<PERSON><PERSON> er<PERSON>", "monerium.setup-card.login": "Bei Gnosis <PERSON>ellen", "monerium.setup-card.subtitle": "Erstell e Gnosis Pay-Kont oder mell dech un, fir direkt Bankalogen z'aktivéieren.", "monerium.setup-card.subtitle_personal_account": "<PERSON><PERSON>t däi perséinleche Kont bei Gnosis Pay a Minutten:", "monerium.setup-card.title": "Bankalogen aktivéieren", "moneriumDepositSuccess.goToWallet": "Z<PERSON>", "moneriumDepositSuccess.title": "{symbol} kritt", "moneriumInfo.fees": "Du bezills 0 % Käschten", "moneriumInfo.registration": "Monerium ass als E-Geld-Institut ënner dem islännesche Gesetz iwwer elektronesch Suen Nr. 17/2013 autoriséiert a reglementéiert. <link><PERSON><PERSON>i gewuer ginn</link>", "moneriumInfo.selfCustody": "<PERSON><PERSON>i <PERSON>, d<PERSON><PERSON> <PERSON>, ginn a Selbstverwaltung gehalen a keen aneren huet Kontroll iwwer däin <PERSON>.", "moneriumWithdrawRejected.supportText": "Mir konnten deng Iwwerweisung net ofschléissen. Probéier wgl. nees. Wann et nach ëmmer net geet, <link>kontaktéier de Support.</link>", "moneriumWithdrawRejected.title": "Iwwerweisung réckgängeg gemaach", "moneriumWithdrawRejected.tryAgain": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "moneriumWithdrawSuccess.supportText": "Et kann 24 <PERSON><PERSON><PERSON>, bis d<PERSON>in{br}<PERSON><PERSON><PERSON> <PERSON>'<PERSON> kritt", "moneriumWithdrawSuccess.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monerium_enable_banner.text": "Bankiwwerweisungen elo aktivéieren", "monerium_error_address_re_link_required.title": "Port<PERSON><PERSON> muss nees mat Monerium verbonne ginn", "monerium_error_duplicate_order.title": "Duebel Commande", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "&lt; {limit}", "mt-pelerin-fork.options.chf.primary": "Schwäizer Frang", "mt-pelerin-fork.options.chf.short": "Direkt & gratis mat <PERSON> Pelerin", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Direkt & gratis mat Monerium", "mt-pelerin-fork.title": "Wat wëlls du deposéieren?", "mtPelerinProviderInfo.fees": "Du bezills 0 % Käschten", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd ass bei SO-FIT affiliéiert, engem Selbstreguléierungsorgan, dat vun der Schwäizer Finanzautoritéit (FINMA) am Kader vum Geldwäschgesetz unerkannt ass. <link>Méi gewuer ginn</link>", "mtPelerinProviderInfo.selfCustody": "Dat digit<PERSON><PERSON>, dat s du kris<PERSON>, g<PERSON>tt vu dir selwer verwalt a keen aneren huet Kontroll iwwer deng Wä<PERSON>er", "network-fee-widget.title": "<PERSON><PERSON><PERSON><PERSON>", "network.edit.verifying_rpc": "RPC gëtt veri<PERSON>", "network.editRpc.predefined_network_info.subtitle": "Wéi e VPN ben<PERSON>t <PERSON>, dé<PERSON>, dass deng perséinlech Date verfollegt ginn.{br}{br}Dem Zeal seng Standard-RPCe si zouverlässeg an an der Praxis getest RPC-Provider.", "network.editRpc.predefined_network_info.title": "Zeal Privatsphär-RPC", "network.filter.update_rpc_success": "RPC-K<PERSON>et gespäichert", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "All Netzwierker", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "Netzwierker bäifügen", "networks.filter.add_modal.chain_list.subtitle": "<PERSON>üg egal wéi eng EVM-Netzwierker bäi", "networks.filter.add_modal.chain_list.title": "<PERSON><PERSON><PERSON> op Chainlist", "networks.filter.add_modal.dapp_tip.subtitle": "An denge Liblings-<PERSON><PERSON><PERSON>, w<PERSON><PERSON> einfach op dat EVM-Netzwierk, dat s du benotze wëlls, an <PERSON><PERSON> freet dech, ob s du et an däi Portmonni bäifüge wëlls.", "networks.filter.add_modal.dapp_tip.title": "Oder füg en Netzwierk aus egal wéi enger dApp bäi", "networks.filter.add_networks.subtitle": "All EVM-Netzwierker ginn ënnerstëtzt", "networks.filter.add_networks.title": "Netzwierker bäifügen", "networks.filter.add_test_networks.title": "Testnets bäifügen", "networks.filter.tab.netwokrs": "Netzwierker", "networks.filter.testnets.title": "Testnets", "nft.widget.emptystate": "Keng <PERSON>mlerstécker am Portmonni", "nft_collection.change_account_picture.subtitle": "<PERSON> du s<PERSON>cher, dass du däi Profilbild aktualiséiere wëlls?", "nft_collection.change_account_picture.title": "Profilbild op NFT aktualiséieren", "nfts.allNfts.pricingPopup.description": "D'Präisser vun de Sammlerstécker baséieren op dem leschte gehandelte Präis.", "nfts.allNfts.pricingPopup.title": "Prä<PERSON><PERSON> v<PERSON> de Sammlerstécker", "no-passkeys-found.modal.cta": "Zoumaachen", "no-passkeys-found.modal.subtitle": "Mir kenne keng Zeal-Passkeys op dësem Apparat fannen. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> de<PERSON>, datt s du an de <PERSON>-<PERSON><PERSON> age<PERSON> bass, mat deem s du däi Smart Wallet erstallt hues.", "no-passkeys-found.modal.title": "Keng <PERSON>keys fonnt", "notValidEmail.title": "<PERSON>g valabel E-Mail-Adress", "notValidPhone.title": "Dëst ass keng gülteg Telefonsnummer", "notification-settings.title": "Notifikatiouns-Astellungen", "notification-settings.toggles.active-wallets": "Aktiv Portmonnien", "notification-settings.toggles.bank-transfers": "Bankiwwerweisungen", "notification-settings.toggles.card-payments": "Kaartebezuelungen", "notification-settings.toggles.readonly-wallets": "Read-only <PERSON><PERSON><PERSON><PERSON>", "ntft.groupHeader.text": "Sammlerstécker", "on_ramp.crypto_completed": "Fäerdeg", "on_ramp.fiat_completed": "Fäerdeg", "onboarding-widget.subtitle.card_created_from_order.left": "<PERSON>", "onboarding-widget.subtitle.card_created_from_order.right": "<PERSON><PERSON> a<PERSON>", "onboarding-widget.subtitle.card_order_ready.left": "Physesch <PERSON>", "onboarding-widget.subtitle.default": "Iwwerweisungen & Visa Kaart", "onboarding-widget.title.card-order-in-progress": "Kaartebestellung virufueren", "onboarding-widget.title.card_created_from_order": "<PERSON><PERSON><PERSON><PERSON> gouf ve<PERSON>", "onboarding-widget.title.kyc_approved": "Iwwerweisungen & Kaart prett", "onboarding-widget.title.kyc_failed": "<PERSON>nt net méiglech", "onboarding-widget.title.kyc_not_started": "Ariichtung virufueren", "onboarding-widget.title.kyc_started_documents_requested": "Verifizéierung ofschléissen", "onboarding-widget.title.kyc_started_resubmission_requested": "Verifizéierung widderhuelen", "onboarding-widget.title.kyc_started_verification_in_progress": "Identitéit gëtt verifi<PERSON>", "onboarding.loginOrCreateAccount.amountOfAssets": "$10bn+ u Verméigen", "onboarding.loginOrCreateAccount.cards.subtitle": "Nëmmen a bestëmmte Regiounen disponibel. Andeems du weiderfiers, akzeptéiers du eis <Terms>Konditiounen</Terms> & <PrivacyPolicy>Dateschutzerklärung</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Visa Kaart mat héije{br}Rendementer a keng Käschten", "onboarding.loginOrCreateAccount.createAccount": "<PERSON><PERSON> er<PERSON>", "onboarding.loginOrCreateAccount.earn.subtitle": "Rendementer variéieren; Kapital a Gefor. Andeems du weiderfiers, akzeptéiers du eis <Terms>Konditiounen</Terms> & <PrivacyPolicy>Dateschutzerklärung</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "Verdéng {percent} pro Joer{br}D'Vertraue vun {currencySymbol}5bn+", "onboarding.loginOrCreateAccount.earningPerYear": "Verdéng {percent}{br}pro Joer", "onboarding.loginOrCreateAccount.login": "Aloggen", "onboarding.loginOrCreateAccount.trading.subtitle": "Kapital a Gefor. Andeems du weiderfiers, akzeptéiers du eis <Terms>Konditiounen</Terms> & <PrivacyPolicy>Dateschutzerklärung</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Investéier an alles,{br}vu BTC bis S&P", "onboarding.loginOrCreateAccount.trustedBy": "Digital Geldmäert{br}D'Vertraue vun {assets}", "onboarding.wallet_stories.close": "Zoumaachen", "onboarding.wallet_stories.previous": "<PERSON><PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.deposit": "Depot", "order-earn-deposit-bridge.into": "An", "otpIncorrectMessage": "Confirmatiounscode ass falsch", "passkey-creation-not-possible.modal.close": "Zoumaachen", "passkey-creation-not-possible.modal.subtitle": "Mir konnten kee Passkey fir däi Portmonni erstellen. <PERSON><PERSON><PERSON>, dass däin Apparat Passkeys ë<PERSON>, a probéier nach eng Kéier. <link>Kontaktéier de Support</link> wann de Problem bestoe bleift.", "passkey-creation-not-possible.modal.title": "Passkey konnt net erstallt ginn", "passkey-not-supported-in-mobile-browser.modal.cta": "Zeal eroflueden", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Smart Wallets ginn a mobille Browseren net ënnerstëtzt.", "passkey-not-supported-in-mobile-browser.modal.title": "Zeal-App erofluede fir weiderzefueren", "passkey-recovery.recovering.deploy-signer.loading-text": "Passkey g<PERSON>tt veri<PERSON>", "passkey-recovery.recovering.loading-text": "Portmonni gëtt restauréiert", "passkey-recovery.recovering.signer-not-found.subtitle": "Mir konnten däi <PERSON> net mat engem aktive Portmonni verbannen. Wan<PERSON> s du Suen op dengem Portmonni hues, kontaktéier d'Zeal-Team fir Hëllef.", "passkey-recovery.recovering.signer-not-found.title": "<PERSON><PERSON> fonnt", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "<PERSON>ere Passkey probéieren", "passkey-recovery.select-passkey.banner.subtitle": "<PERSON><PERSON>, dass de mam richtege <PERSON> um Apparat ageloggt bass. <PERSON>keys si pro <PERSON>nt.", "passkey-recovery.select-passkey.banner.title": "Gesäis du de Passkey vun dengem Portmonni net?", "passkey-recovery.select-passkey.continue": "<PERSON><PERSON> auswi<PERSON>n", "passkey-recovery.select-passkey.subtitle": "<PERSON><PERSON> de <PERSON> aus, dee mat dengem Portmonni verbonnen ass, fir er<PERSON><PERSON> ze kré<PERSON>.", "passkey-recovery.select-passkey.title": "<PERSON><PERSON> auswi<PERSON>n", "passkey-story_1.subtitle": "Mat engem Smart Wallet kanns de Netzwierkskäschte mat de meeschten Token bezuelen a muss der keng Suergen iwwer Netzwierkskäschte maachen.", "passkey-story_1.title": "Vergiess d'Netzwierkskäschten - bezuel se mat de meeschten Token", "passkey-story_2.subtitle": "Baséiert op de branchenféierende Smart Contracts vu Safe, déi méi wéi 100 Mrd. $ a méi wéi 20 Millioune Portmonnie sécheren.", "passkey-story_2.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> due<PERSON>", "passkey-story_3.subtitle": "Smart Wallets funktionéieren op de grousse Ethereum-kompatibelen Netzwierker. Iwwerpréif déi ënnerstëtzt Netzwierker, ier s de <PERSON> sch<PERSON>.", "passkey-story_3.title": "Grouss EVM-Netzwierker ënnerstëtzt", "password.add.header": "<PERSON><PERSON><PERSON>", "password.add.includeLowerAndUppercase": "Kleng- a Groussbuschtawen", "password.add.includesNumberOrSpecialChar": "Eng Zuel oder e Symbol", "password.add.info.subtitle": "<PERSON> schécken däi <PERSON>wu<PERSON> net un eis Server<PERSON> a maache kee <PERSON>up fir dech", "password.add.info.t_and_c": "And<PERSON><PERSON> du weiderfiers, akzeptéiers du eis <Terms>Konditiounen</Terms> & <PrivacyPolicy>Dateschutzerklärung</PrivacyPolicy>", "password.add.info.title": "<PERSON><PERSON><PERSON> Passwuert bleift op dësem Apparat", "password.add.inputPlaceholder": "<PERSON><PERSON><PERSON>", "password.add.shouldContainsMinCharsCheck": "10+ <PERSON><PERSON><PERSON>", "password.add.subheader": "Du brauchs däi Passwuert fir Zeal opzespären", "password.add.success.title": "<PERSON><PERSON><PERSON> erstallt 🔥", "password.confirm.header": "<PERSON><PERSON><PERSON>", "password.confirm.passwordDidNotMatch": "D'Passwierder mussen iwwereneestëmmen", "password.confirm.subheader": "<PERSON><PERSON><PERSON> d<PERSON> nach eng Kéier an", "password.create_pin.subtitle": "Dëse Code späert d'Zeal-App", "password.create_pin.title": "<PERSON>rstell däi Code", "password.enter_pin.title": "Code aginn", "password.incorrectPin": "Falsche Code", "password.pin_is_not_same": "De Code stëmmt net iwwereneen", "password.placeholder.enter": "<PERSON><PERSON><PERSON> aginn", "password.placeholder.reenter": "<PERSON><PERSON><PERSON> nach eng Kéier aginn", "password.re_enter_pin.subtitle": "<PERSON><PERSON><PERSON> selwechte Code nach eng Kéier an", "password.re_enter_pin.title": "Code confirméieren", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "<PERSON>", "pending-send.details.pending": "<PERSON>", "pending-send.details.processing": "<PERSON><PERSON><PERSON>", "permit-info.modal.description": "Permits si <PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON>, wa se <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ginn, et <PERSON><PERSON><PERSON> er<PERSON>, deng <PERSON> a dengem Numm ze beweegen, zum Beispill fir en <PERSON>sch.{br}Permits si wé<PERSON>, awer kaschten dech keng Netzwierkskäschte fir d'Ënnerschrëft.", "permit-info.modal.title": "Wat si Permits?", "permit.edit-expiration": "Änner {currency} Oflafzäit", "permit.edit-limit": "Änner {currency} Ausgabelimit", "permit.edit-modal.expiresIn": "Leeft of an…", "permit.expiration-warning": "{currency} Oflafzäit-Warnung", "permit.expiration.info": "{currency} Oflaf-Info", "permit.expiration.never": "<PERSON>", "permit.spend-limit.info": "{currency} Ausgabelimit-Info", "permit.spend-limit.warning": "{currency} Ausgabelimit-Warnung", "phoneNumber.title": "Telefonsnummer", "physicalCardOrderFlow.cardOrdered": "<PERSON><PERSON>", "physicalCardOrderFlow.city": "Stad", "physicalCardOrderFlow.orderCard": "<PERSON><PERSON> bestellen", "physicalCardOrderFlow.postcode": "Postcode", "physicalCardOrderFlow.shippingAddress.subtitle": "<PERSON><PERSON><PERSON> deng <PERSON> g<PERSON> g<PERSON>tt", "physicalCardOrderFlow.shippingAddress.title": "Liwweradress", "physicalCardOrderFlow.street": "Strooss", "placeholderDapps.1inch.description": "Tausch iwwer déi bescht Routen", "placeholderDapps.aave.description": "Tokens léinen a verléinen", "placeholderDapps.bungee.description": "Netzwierker iwwer déi bescht Route brécken", "placeholderDapps.compound.description": "Tokens léinen a verléinen", "placeholderDapps.cowswap.description": "<PERSON><PERSON> zu de beschte Konditiounen op Gnosis", "placeholderDapps.gnosis-pay.description": "<PERSON><PERSON><PERSON><PERSON> deng G<PERSON>", "placeholderDapps.jumper.description": "Netzwierker iwwer déi bescht Route brécken", "placeholderDapps.lido.description": "Stake ETH fir méi ETH", "placeholderDapps.monerium.description": "eMoney an Iwwerweisungen", "placeholderDapps.odos.description": "Tausch iwwer déi bescht Routen", "placeholderDapps.stargate.description": "Bréck oder stake fir <14% APY", "placeholderDapps.uniswap.description": "Ee vun de beléiftsten Exchangen", "pleaseAllowNotifications.cardPayments": "Kaartebezuelungen", "pleaseAllowNotifications.customiseInSettings": "Personaliséier an den Astellungen", "pleaseAllowNotifications.enable": "Aktivéieren", "pleaseAllowNotifications.forWalletActivity": "Fir Portmonni-Aktivitéit", "pleaseAllowNotifications.title": "<PERSON><PERSON><PERSON>-Not<PERSON><PERSON><PERSON>", "pleaseAllowNotifications.whenReceivingAssets": "<PERSON><PERSON> du Fonge kriss", "portfolio.quick-actions.add_funds": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.buy": "<PERSON><PERSON>", "portfolio.quick-actions.deposit": "<PERSON><PERSON><PERSON><PERSON>", "portfolio.quick-actions.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portfolio.view.lastRefreshed": "Aktualiséiert viru {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "<PERSON><PERSON> deng <PERSON>net-AVAX op", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Géi bei de Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "<PERSON><PERSON> deng <PERSON>net-BNB op", "portfolio.view.topupTestNet.BscTestnet.secondary": "Géi bei de Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "<PERSON><PERSON> deng <PERSON>-SepETH op", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Géi bei de Sepolia Faucet", "portfolio.view.topupTestNet.FantomTestnet.primary": "<PERSON><PERSON> deng <PERSON>-FTM op", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Géi bei de Faucet", "privateKeyConfirmation.banner.subtitle": "G<PERSON>ff däi private <PERSON><PERSON><PERSON><PERSON> ni hier. Nëmme Scammer froen duerno.", "privateKeyConfirmation.banner.title": "Ech verstinn d'Risiken", "privateKeyConfirmation.title": "DEEL NI däi private Schlëssel mat iergendeppes", "rating-request.not-now": "Net elo", "rating-request.title": "<PERSON><PERSON><PERSON><PERSON> du Zeal weiderempfeelen?", "receive_funds.address-text": "<PERSON><PERSON><PERSON> ass deng eemoleg <PERSON>mon<PERSON>adress. Du kanns se ouni Bed<PERSON>ke mat aneren de<PERSON>.", "receive_funds.copy_address": "<PERSON><PERSON>", "receive_funds.network-warning.eoa.subtitle": "<link>Standard-Netzwierklëscht ukucken</link>. <PERSON><PERSON><PERSON>, déi op Net-EVM-Netzwierker geschéckt ginn, gi verluer.", "receive_funds.network-warning.eoa.title": "All Ethereum-baséiert Netzwierker ginn ënnerstëtzt", "receive_funds.network-warning.scw.subtitle": "<link>Ënnerstëtzt Netzwierker ukucken</link>. <PERSON><PERSON><PERSON>, déi op aneren Netzwierker geschéckt ginn, gi verluer.", "receive_funds.network-warning.scw.title": "Wichteg: <PERSON><PERSON><PERSON> nëmmen ënnerstëtzt Netzwierker", "receive_funds.scan_qr_code": "QR-Code scannen", "receiving.in.days": "<PERSON><PERSON><PERSON> kritt an {days}T", "receiving.this.week": "<PERSON><PERSON><PERSON> dë<PERSON> kritt", "receiving.today": "<PERSON><PERSON><PERSON> haut kritt", "reference.error.maximum_number_of_characters_exceeded": "Ze vill Zeechen", "referral-code.placeholder": "Invitatiounslink afügen", "referral-code.subtitle": "<PERSON>lick nach eng Kéier op de Link vun dengem Frënd oder setz en hei drënner an. <PERSON> wëlle s<PERSON>cher sinn, dass du deng Belounung kriss.", "referral-code.title": "Huet e <PERSON>nd dir {bReward} gesch<PERSON>ckt?", "rekyc.verification_deadline.subtitle": "<PERSON><PERSON><PERSON><PERSON> d'Verifizéierung bannent {daysUntil} Deeg of, fir deng <PERSON> weider ze notzen.", "rekyc.verification_required.subtitle": "<PERSON>hl<PERSON>iss d'Verifizéierung of, fir deng <PERSON> weider kënnen ze notzen.", "reminder.fund": "💸 Lued op — verdéng direkt 6 %", "reminder.onboarding": "🏁 Ariichtung ofschléissen — verdéng 6 % op deng Abezuelungen", "remove-owner.confirmation.subtitle": "Ännerungen dauere 3 Minutten. Den<PERSON> gëtt temporär g<PERSON><PERSON><PERSON>, Bezuelunge sinn net méiglech.", "remove-owner.confirmation.title": "<PERSON><PERSON> gëtt fir 3 Min gespaart wärend dem Update", "restore-smart-wallet.wallet-recovered": "<PERSON><PERSON><PERSON>au<PERSON>", "rewardClaimCelebration.claimedTitle": "Beloununge scho gefuerdert", "rewardClaimCelebration.subtitle": "Well s du Frënn invitéiert hues", "rewardClaimCelebration.title": "<PERSON> hues verd<PERSON>", "rewards-warning.subtitle": "<PERSON><PERSON> <PERSON> <PERSON> dë<PERSON>, g<PERSON><PERSON> den Zougank zu verbonnene Beloununge pauséiert. Du kanns de <PERSON>nt zu all Moment restauréieren, fir se ze k<PERSON>ien.", "rewards-warning.title": "Du verléiers den Zougank zu denge Belounungen", "rewards.copiedInviteLink": "Invitatiounslink kopéiert", "rewards.createAccount": "Invitéier-<PERSON> k<PERSON>", "rewards.header.subtitle": "<PERSON> schécken {aReward} un dech an {bReward} un däi <PERSON>, wann deen {bSpendLimitReward} ausgëtt.", "rewards.header.title": "<PERSON><PERSON><PERSON> {amountA}{br}<PERSON><PERSON><PERSON> {amountB}", "rewards.sendInvite": "Invitat<PERSON><PERSON> s<PERSON>n", "rewards.sendInviteTip": "<PERSON><PERSON> e <PERSON>ënd aus a mir ginn him {bAmount}", "route.fees": "<PERSON><PERSON><PERSON><PERSON> {fees}", "routesNotFound.description": "D'Tausch-Route fir d'Ko<PERSON>n {from}-{to} ass net disponibel.", "routesNotFound.title": "<PERSON><PERSON>-Route disponibel", "rpc.OrderBuySignMessage.subtitle": "Iwwer Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Iwwer Swaps.IO", "rpc.addCustomNetwork.addNetwork": "Netzw. bäif.", "rpc.addCustomNetwork.chainId": "Chain ID", "rpc.addCustomNetwork.nativeToken": "Native Token", "rpc.addCustomNetwork.networkName": "Numm vum Netzwierk", "rpc.addCustomNetwork.operationDescription": "<PERSON><PERSON><PERSON><PERSON> dë<PERSON> Web<PERSON>ä<PERSON>, en Netzwierk an dengem Portmonni bäizefügen. Zeal kann d'Sécherheet vu personaliséierten Netzwierker net préiwen, <PERSON><PERSON><PERSON><PERSON> dech, dass du d'Risike verstees.", "rpc.addCustomNetwork.rpcUrl": "RPC URL", "rpc.addCustomNetwork.subtitle": "Iwwer {name}", "rpc.addCustomNetwork.title": "Netzwierk bäifügen", "rpc.send_token.network_not_supported.subtitle": "<PERSON> schaffe drun, Transaktiounen op dësem Netzwierk z'aktivéieren. <PERSON><PERSON>i fir deng <PERSON> 🙏", "rpc.send_token.network_not_supported.title": "Netzwierk kënnt geschwënn", "rpc.send_token.send_or_receive.settings": "Astellungen", "rpc.sign.accept": "Akzeptéieren", "rpc.sign.cannot_parse_message.body": "Mir konnten dëse Message net dekodéieren. Akzeptéier dë<PERSON>, wanns du dëser A<PERSON> vertraus.{br}{br}Messagë kënne benotzt gi fir dech an enger <PERSON> un<PERSON>mell<PERSON>, mee kënnen Apps och Kontroll iwwer deng Token<PERSON> ginn.", "rpc.sign.cannot_parse_message.header": "Sief virsiichteg", "rpc.sign.import_private_key": "Schlësselen importéieren", "rpc.sign.subtitle": "Fir {name}", "rpc.sign.title": "Ënnerschreiwen", "safe-creation.success.title": "<PERSON><PERSON><PERSON> er<PERSON>lt", "safe-safety-checks-popup.title": "Sécherheetschecke fir Transaktiounen", "safetyChecksPopup.title": "Sécherheetschecke fir de Site", "scan_qr_code.description": "Portmonnis-QR scannen oder mat enger App verbannen", "scan_qr_code.show_qr_code": "Mäi QR-Code uweisen", "scan_qr_code.tryAgain": "Nach eng <PERSON><PERSON> pro<PERSON>en", "scan_qr_code.unlockCamera": "Kamera entspären", "screen-lock-missing.modal.close": "Zoumaachen", "screen-lock-missing.modal.subtitle": "<PERSON><PERSON><PERSON> Apparat brauch eng Bildschirmsspär fir Passkeys ze benotzen. Richt w.e.g. eng Bildschirmsspär an a probéier nach eng Kéier.", "screen-lock-missing.modal.title": "Bildschirmsspär feelt", "seedConfirmation.banner.subtitle": "<PERSON><PERSON><PERSON> däi geheime Saz ni hier. Nëmme Scammer froen duerno.", "seedConfirmation.title": "DEEL NI däi geheime Saz mat iergendeppes", "select-active-owner.subtitle": "Du hues méi Portmonnie mat denger Ka<PERSON> verbonnen. <PERSON><PERSON> een aus, fir dech mat Zeal ze verbannen. Du kanns jidderzäit wiesselen.", "select-active-owner.title": "<PERSON><PERSON><PERSON>wi<PERSON>", "select-card.title": "<PERSON><PERSON>", "select-crypto-currency-title": "Token au<PERSON>wielen", "select-token.title": "Token au<PERSON>wielen", "selectEarnAccount.chf.description.steps": "· Geld 24/7 of<PERSON><PERSON><PERSON>, ouni Spärzäit {br}· Zënse ginn all Sekonn ugesammelt {br}· Iwwerséchert Depositiounen an <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} pro Joer an CHF", "selectEarnAccount.eur.description.steps": "· Suen 24/7 <PERSON><PERSON><PERSON><PERSON>, k<PERSON> {br}· Zënsen kommen all Sekonn dobäi {br}· Iwwerdeckt Prête mat <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} pro Joer an EUR", "selectEarnAccount.subtitle": "Du kanns dat zu all <PERSON> änneren", "selectEarnAccount.title": "Wärung auswielen", "selectEarnAccount.usd.description.steps": "· Suen 24/7 <PERSON><PERSON><PERSON><PERSON>, k<PERSON> {br}· Zënsen kommen all Sekonn dobäi {br}· Iwwerdeckt Acompteen an <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} pro Joer an USD", "selectEarnAccount.zero.description_general": "Digital Sue halen ouni Zënsen ze verdéngen", "selectEarnAccount.zero.title": "0 % pro Joer", "selectRechargeThreshold.button.enterAmount": "<PERSON><PERSON> aginn", "selectRechargeThreshold.button.setTo": "Astellen op {amount}", "selectRechargeThreshold.description.line1": "<PERSON><PERSON> deng <PERSON> {amount} fält, gëtt se automatesch nees op {amount} vun dengem Earn-<PERSON><PERSON> op<PERSON>.", "selectRechargeThreshold.description.line2": "E méi niddregt <PERSON>, dass méi op dengem Earn-<PERSON>nt bleift (deen 3% bréngt). Du kanns dat zu all Moment änneren.", "selectRechargeThreshold.title": "<PERSON><PERSON><PERSON> fir <PERSON> f<PERSON>", "select_currency_to_withdraw.select_token_to_withdraw": "Token fir d'Ophiewen auswielen", "send-card-token.form.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "send-card-token.form.send-amount": "Opluedbetrag", "send-card-token.form.title": "Geld op d'Ka<PERSON> lueden", "send-card-token.form.to-address": "<PERSON><PERSON>", "send-safe-transaction.network-fee-widget.error": "Du brauchs {amount} oder wiel en aneren Token", "send-safe-transaction.network-fee-widget.no-fee": "<PERSON><PERSON>", "send-safe-transaction.network-fee-widget.title": "<PERSON><PERSON><PERSON><PERSON>", "send-safe-transaction.network_fee_widget.title": "Netzwierkskäschten", "send.banner.fees": "Du brauchs {amount} méi {currency} fir d'Käschten ze bezuelen", "send.banner.toAddressNotSupportedNetwork.subtitle": "De Portmonni vum Destinataire ënnerstëtzt {network}. Wiessel op en ënnerstëtzten Token.", "send.banner.toAddressNotSupportedNetwork.title": "Netzwierk gëtt vum Destinataire net ënnerstëtzt", "send.banner.walletNotSupportedNetwork.subtitle": "Smart Wallets kënne keng Transaktiounen op {network}. Wiessel op en ënnerstëtzten Token.", "send.banner.walletNotSupportedNetwork.title": "Token-Netzwierk gëtt net ënnerstëtzt", "send.empty-portfolio.empty-state": "<PERSON> hu keng Tokene fonnt", "send.empty-portfolio.header": "Tokenen", "send.titile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendLimit.success.subtitle": "<PERSON>g nei Limit ass an 3 Minutten aktiv.", "sendLimit.success.title": "Dës Ännerung dauert 3 Minutten", "send_crypto.form.disconnected.cta.addFunds": "<PERSON><PERSON> dob<PERSON>en", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Wiesselen op {network}", "send_crypto.form.disconnected.label": "Montant fir z'iwwerweisen", "send_to.qr_code.description": "Scan e QR-Code, fir un e Portmonni ze sch<PERSON>cken", "send_to.qr_code.title": "QR-Code scannen", "send_to_card.header": "<PERSON> d'Ka<PERSON>-<PERSON><PERSON>n", "send_to_card.select_sender.add_wallet": "Portmonni do<PERSON>", "send_to_card.select_sender.header": "Ofsender auswielen", "send_to_card.select_sender.search.default_placeholder": "<PERSON>ress oder <PERSON> sic<PERSON>", "send_to_card.select_sender.show_card_address_button_description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "send_token.form.select-address": "<PERSON><PERSON>", "send_token.form.send-amount": "<PERSON><PERSON>", "send_token.form.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setLimit.amount.error.zero_amount": "Du kanns keng Bezuelunge maachen", "setLimit.error.max_limit_reached": "Limit op max. setzen {amount}", "setLimit.error.same_as_current_limit": "Aktuell Limit", "setLimit.placeholder": "Aktuell: {amount}", "setLimit.submit": "Limit astellen", "setLimit.submit.error.amount_required": "<PERSON><PERSON> aginn", "setLimit.subtitle": "Maximum deen s du pro Dag ausgi kanns.", "setLimit.title": "Deeglecht Ausgabelimit astellen", "settings.accounts": "Konten", "settings.accountsSeeAll": "<PERSON><PERSON> u<PERSON>sen", "settings.addAccount": "Portmonni do<PERSON>", "settings.card": "Kaartenastellungen", "settings.connections": "App-Verbindungen", "settings.currency": "Standardwärung", "settings.default_currency_selector.title": "Wärung", "settings.discord": "Discord", "settings.experimentalMode": "Experimentelle Modus", "settings.experimentalMode.subtitle": "Test <PERSON><PERSON>", "settings.language": "<PERSON><PERSON><PERSON><PERSON>", "settings.lockZeal": "Zeal spären", "settings.notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.open_expanded_view": "Erweidert Usiicht opmaachen", "settings.privacyPolicy": "Dateschutzerklärung", "settings.settings": "Astellungen", "settings.termsOfUse": "Notzungsbedéngungen", "settings.twitter": "𝕏 / Twitter", "settings.version": "Versioun {version} env: {env}", "setup-card.confirmation": "<PERSON><PERSON><PERSON><PERSON>", "setup-card.confirmation.subtitle": "Maach Online-Bezuelungen a setz se an däi {type} Portmonni fir kontaktlos Bezuelungen.", "setup-card.getCard": "<PERSON><PERSON>", "setup-card.order.physicalCard": "<PERSON><PERSON><PERSON><PERSON>", "setup-card.order.physicalCard.steps": "· Eng physesch VISA Gnosis Pay {br}· Liwwerung ka bis zu 3 Wochen daueren {br}· Fir Bezuelungen op der Plaz an um Bancomat. {br}· Am Apple/Google Wallet dobäisetzen (nëmme fir ënnerstëtzt Länner", "setup-card.order.subtitle1": "Du kanns e puer Kaarten zur selwechter Zäit notzen", "setup-card.order.title": "Wéi eng <PERSON>?", "setup-card.order.virtualCard": "<PERSON><PERSON><PERSON><PERSON>", "setup-card.order.virtual_card.steps": "· Digital VISA Gnosis Pay {br}· Direkt fir Onlinebezuelungen notzen {br}· Am Apple/Google Wallet dobäisetzen (nëmme fir ënnerstëtzt Länner)", "setup-card.orderCard": "<PERSON><PERSON> bestellen", "setup-card.virtual-card": "<PERSON><PERSON><PERSON><PERSON>", "setup.notifs.fakeAndroid.title": "Notifikatioune fir Bezuelungen an erakommend Iwwerweisungen", "setup.notifs.fakeIos.subtitle": "Zeal kann dech informéieren, wanns de <PERSON>eld kriss oder mat denger Visa Kaart bezills. Du kanns dat méi spéit änneren.", "setup.notifs.fakeIos.title": "Notifikatioune fir Bezuelungen an erakommend Iwwerweisungen", "sign.PermitAllowanceItem.spendLimit": "Ausgabelimit", "sign.ledger.subtitle": "Ufro un Hardware-Wallet. Maach do weider.", "sign.ledger.title": "Hardware-Portmonni <PERSON>schreiwen", "sign.passkey.subtitle": "<PERSON><PERSON><PERSON>er sollt dech op<PERSON>, mam <PERSON> vun dësem Portmonni z'ënnerschreiwen. Maach w.e.g. do weider.", "sign.passkey.title": "<PERSON><PERSON> auswi<PERSON>n", "signal_aborted_for_uknown_reason.title": "Netzwierkufro ofgebrach", "simulatedTransaction.BridgeTrx.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.CardTopUp.info.title": "Geld op d'Ka<PERSON> lueden", "simulatedTransaction.CardTopUpTrx.info.title": "Suen op d'Ka<PERSON> lueden", "simulatedTransaction.NftCollectionApproval.approve": "NFT-Sammlung geneemegen", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "<PERSON> lueden", "simulatedTransaction.OrderEarnDepositBridge.title": "An Earn a<PERSON>n", "simulatedTransaction.P2PTransaction.info.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.PermitSignMessage.title": "<PERSON><PERSON>", "simulatedTransaction.SingleNftApproval.approve": "NFT geneemegen", "simulatedTransaction.UnknownSignMessage.title": "Ënnerschreiwen", "simulatedTransaction.Withdrawal.info.title": "Ofhiewung", "simulatedTransaction.approval.title": "Guttgläichen", "simulatedTransaction.approve.info.title": "Geneemegen", "simulatedTransaction.p2p.info.account": "Un", "simulatedTransaction.p2p.info.unlabelledAccount": "Onbenannte Portmonni", "simulatedTransaction.unknown.info.receive": "<PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.info.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.using": "Iwwer {app}", "simulation.approval.modal.text": "<PERSON>n s du eng Geneemegung gëss, kann eng App deng Tokens oder NFTs bei zukünftegen Transaktioune benotzen.", "simulation.approval.modal.title": "Wat si Geneemegungen?", "simulation.approval.spend-limit.label": "Ausgabelimit", "simulation.approve.footer.for": "Fir", "simulation.approve.unlimited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulationNotAvailable.title": "Onbekannt Aktioun", "smart-wallet-activation-view.on": "Op", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password ka den Zougank zu dengem Portmonni blockéieren", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "<PERSON>otz <PERSON> oder Google, fir Zeal sécher anzerii<PERSON>en", "smart-wallet.passkey-notice.title": "Vermeid 1Password", "spend-limits.high.modal.text": "<PERSON><PERSON> niddereg. H<PERSON>ich si riskant.", "spend-limits.high.modal.text_sign_message": "D'Ausgabelimit sollt no bei deem Betrag un Tokensen léien, deen s du tatsächlech mat enger App oder engem Smart Contract benotzt. Héich Limitte si riskant a kënnen et Abzo<PERSON> méi einfach maachen, deng <PERSON> ze klauen.", "spend-limits.high.modal.title": "Héijen Au<PERSON>gabel<PERSON>", "spend-limits.modal.text": "<PERSON>ite si, wat eng App fir dech ausgi kann.", "spend-limits.modal.title": "Wat ass en Ausgabelimit?", "spent-limit-info.modal.description": "D'Ausgabelimit ass d'<PERSON><PERSON> v<PERSON>, déi eng <PERSON> an dengem Numm benotzen däerf. Du kanns dës Limit zu all Moment änneren oder ewechhuelen. Fir sécher ze ble<PERSON>wen, halt d'Ausgabelimitte no bei deem Betrag un Tokensen, deen s du tatsächlech mat enger <PERSON> benotzt.", "spent-limit-info.modal.title": "Wat ass eng Ausgabelimit?", "sswaps-io.transfer-provider": "Iwwerweisungs-Ubidder", "storage.accountDetails.activateWallet": "Portmonni aktivéieren", "storage.accountDetails.changeWalletLabel": "Portmonnis-Etikett änneren", "storage.accountDetails.deleteWallet": "<PERSON><PERSON><PERSON>", "storage.accountDetails.setup_recovery_kit": "Recovery Kit", "storage.accountDetails.showPrivateKey": "Private <PERSON><PERSON><PERSON><PERSON>", "storage.accountDetails.showWalletAddress": "Portmonnisadress uweisen", "storage.accountDetails.smartBackup": "Backup & Erhuelung", "storage.accountDetails.viewSsecretPhrase": "Geheime Saz ukucken", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets?", "storage.manageAccounts.title": "Portmonnien", "submit-userop.progress.text": "Schécken...", "submit.error.amount_high": "<PERSON><PERSON> ze héich", "submit.error.amount_hight": "Betrag ze héich", "submit.error.amount_low": "<PERSON>ant ze niddreg", "submit.error.amount_required": "<PERSON><PERSON> aginn", "submit.error.maximum_number_of_characters_exceeded": "Message verkierzen", "submit.error.not_enough_balance": "Net genuch Saldo", "submit.error.recipient_required": "<PERSON><PERSON><PERSON> obliga<PERSON>", "submit.error.routes_not_found": "Keng <PERSON> fonnt", "submitSafeTransaction.monitor.title": "Resultat vun der Transaktioun", "submitSafeTransaction.sign.title": "Resultat vun der Transaktioun", "submitSafeTransaction.state.sending": "Schécken...", "submitSafeTransaction.state.sign": "<PERSON><PERSON><PERSON> erstallt...", "submitSafeTransaction.submittingToRelayer.title": "Resultat vun der Transaktioun", "submitTransaction.cancel": "Ofbriechen", "submitTransaction.cancel.attemptingToStop": "Gëtt versicht ze stoppen", "submitTransaction.cancel.failedToStop": "Konnt net gestoppt ginn", "submitTransaction.cancel.stopped": "Gestoppt", "submitTransaction.cancel.title": "Transaktiounsvisualisatioun", "submitTransaction.failed.banner.description": "Netzwierk huet se annuléiert. Probéier erneit oder kontaktéier eis.", "submitTransaction.failed.banner.title": "Transaktioun m<PERSON>longen", "submitTransaction.failed.execution_reverted.title": "D'App hat e Feeler", "submitTransaction.failed.execution_reverted_without_message.title": "D'App hat e Feeler", "submitTransaction.failed.out_of_gas.description": "Ofgebrach: mé<PERSON> h<PERSON><PERSON>.", "submitTransaction.failed.out_of_gas.title": "Netzwierksfeeler", "submitTransaction.sign.title": "Transaktiounsresultat", "submitTransaction.speedUp": "<PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue": "An d'Waardeschléi gesat", "submitTransaction.state.addedToQueue.short": "Waardeschléi", "submitTransaction.state.cancelled": "Gestoppt", "submitTransaction.state.complete": "{currencyCode} op <PERSON>eal bäigefüügt", "submitTransaction.state.complete.subtitle": "<PERSON><PERSON> an dengem <PERSON>-Portefeuille no", "submitTransaction.state.completed": "Ofgeschloss", "submitTransaction.state.failed": "Mësslongen", "submitTransaction.state.includedInBlock": "Am Block abegraff", "submitTransaction.state.includedInBlock.short": "Am Block", "submitTransaction.state.replaced": "Ersetzt", "submitTransaction.state.sendingToNetwork": "Gëtt un d'Netzwierk geschéckt", "submitTransaction.stop": "Stoppen", "submitTransaction.submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.bundled": "An der Waardeschléi", "submitted-user-operation.state.completed": "Ofgeschloss", "submitted-user-operation.state.failed": "Mësslongen", "submitted-user-operation.state.pending": "<PERSON><PERSON><PERSON>", "submitted-user-operation.state.rejected": "Ofgeleent", "submittedTransaction.failed.title": "Transaktioun m<PERSON>longen", "success_splash.card_activated": "<PERSON><PERSON>", "supportFork.give-feedback.title": "Feedback ginn", "supportFork.itercom.description": "Zeal hëlleft bei Abezuelen, <PERSON><PERSON><PERSON>, asw.", "supportFork.itercom.title": "Froen zum Portmonni", "supportFork.title": "<PERSON><PERSON><PERSON><PERSON> bei", "supportFork.zendesk.subtitle": "Gnosis Pay: Bezuelung, ID, Remboursement.", "supportFork.zendesk.title": "Kaartenzuelung an Identitéit", "supported-networks.ethereum.warning": "<PERSON><PERSON><PERSON>", "supportedNetworks.networks": "Ënnerstëtzten <PERSON><PERSON><PERSON><PERSON><PERSON>", "supportedNetworks.oneAddressForAllNetworks": "Eng <PERSON> fir all Netzwierker", "supportedNetworks.receiveAnyAssets": "Empfänk Assets vun ënnerstëtzten Netzwierker direkt an dengem Zeal-Portmonni mat der selwechter Adress.", "swap.form.error.no_routes_found": "Keng <PERSON> fonnt", "swap.form.error.not_enough_balance": "Net genuch Saldo", "swaps-io-details.bank.serviceProvider": "Déngschtleeschter", "swaps-io-details.details.processing": "<PERSON><PERSON><PERSON>", "swaps-io-details.pending": "<PERSON>", "swaps-io-details.rate": "<PERSON><PERSON>", "swaps-io-details.serviceProvider": "Déngschtleeschter", "swaps-io-details.transaction.from.processing": "Ugefaangen <PERSON>aktioun", "swaps-io-details.transaction.networkFees": "Netzwierkskäschten", "swaps-io-details.transaction.state.completed-transaction": "Ofgeschlossen Transaktioun", "swaps-io-details.transaction.state.started-transaction": "Ugefaangen <PERSON>aktioun", "swaps-io-details.transaction.to.processing": "Ofgeschlossen Transaktioun", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Den Depot sollt geschwë fäerdeg sinn. Kinetex ass nach am Gaang, deng Transaktioun ze verschaffen.", "swapsIO.monitoring.awaitingLiqSend.title": "Retar<PERSON><PERSON><PERSON><PERSON>", "swapsIO.monitoring.awaitingRecive.title": "Weiderleeden", "swapsIO.monitoring.awaitingSend.title": "An der Waardeschläif", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "D'Tokene goufen u Kinetex geschéckt, mä komme geschwënn zréck. Kinetex konnt d'Ziltransaktioun net ofschléissen.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "<PERSON><PERSON><PERSON>", "swapsIO.monitoring.cancelledNoSlash.subtitle": "D'Tokene goufen wéinst engem onbekannte Feeler net iwwerdroen. Probéier wgl. nach eng Kéier.", "swapsIO.monitoring.cancelledNoSlash.title": "<PERSON><PERSON><PERSON>", "swapsIO.monitoring.cancelledSlashed.subtitle": "D'Tokene si zréckkomm. Kinetex konnt d'Ziltransaktioun net ofschléissen.", "swapsIO.monitoring.cancelledSlashed.title": "<PERSON><PERSON><PERSON>", "swapsIO.monitoring.completed.title": "Ofgeschloss", "taker-metadata.earn": "Verdéng an digitalem USD mat Sky", "taker-metadata.earn.aave": "Verdéng an digitalem EUR mat Aave", "taker-metadata.earn.aave.cashout24": "<PERSON><PERSON><PERSON> ausbezuelen, 24/7", "taker-metadata.earn.aave.trusted": "<PERSON><PERSON><PERSON><PERSON> mat 27 Mrd. $, 2+ <PERSON><PERSON>", "taker-metadata.earn.aave.yield": "Rendement all Sekonn", "taker-metadata.earn.chf": "Verdéng an digitalen CHF", "taker-metadata.earn.chf.cashout24": "<PERSON><PERSON><PERSON> ausbezuelen, 24/7", "taker-metadata.earn.chf.trusted": "Fr. 28M uvertraut", "taker-metadata.earn.chf.yield": "Rendement gëtt all Sekonn ugesammelt", "taker-metadata.earn.usd.cashout24": "<PERSON><PERSON><PERSON> ausbezuelen, 24/7", "taker-metadata.earn.usd.trusted": "<PERSON><PERSON><PERSON><PERSON> mat 10,7 Mrd. $, 5+ <PERSON><PERSON>", "taker-metadata.earn.usd.yield": "Rendement all Sekonn", "test": "<PERSON><PERSON><PERSON><PERSON>", "to.titile": "Un", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "<PERSON>erméigen", "token.groupHeader.titleWithSum": "Verméigen {sum}", "token.hidden_tokens.page.title": "Verstoppten Tokens", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "To<PERSON> dob<PERSON>tzen", "token.widget.cashback_empty": "<PERSON>ch keng <PERSON>aktiou<PERSON>", "token.widget.emptyState": "Keng Tokens am Portmonni", "tokens.cash": "Cash", "top-up-card-from-earn-view.approve.for": "Fir", "top-up-card-from-earn-view.approve.into": "An", "top-up-card-from-earn-view.swap.from": "<PERSON><PERSON>", "top-up-card-from-earn-view.swap.to": "Un", "top-up-card-from-earn-view.withdraw.to": "Un", "top-up-card-from-earn.trx.title.approval": "<PERSON>sch guttgläichen", "top-up-card-from-earn.trx.title.swap": "<PERSON> d'Kaart dob<PERSON>tzen", "top-up-card-from-earn.trx.title.withdrawal": "<PERSON><PERSON><PERSON><PERSON> vun Earn", "topUpDapp.connectWallet": "Port<PERSON><PERSON> verbannen", "topup-fee-breakdown.bungee-fee": "Käschte vun externen Ubidder", "topup-fee-breakdown.header": "Transaktiounskäschten", "topup-fee-breakdown.network-fee": "Netzwierkskäschten", "topup-fee-breakdown.total-fee": "Gesamtkäschten", "topup.continue-in-wallet": "Maach an dengem Portmonni weider", "topup.send.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "topup.submit-transaction.close": "Zoumaachen", "topup.submit-transaction.sent-to-wallet": "<PERSON><PERSON><PERSON><PERSON> {amount}", "topup.to": "Un", "topup.transaction.complete.close": "Zoumaachen", "topup.transaction.complete.try-again": "<PERSON><PERSON>", "transaction-request.nonce-too-low.modal.button-text": "Zoumaachen", "transaction-request.nonce-too-low.modal.text": "Eng Transaktioun mat der selwechter Seriennummer (Nonce) ass scho f<PERSON>erd<PERSON>, do<PERSON>r kanns du dës Transaktioun net méi ofschécken. <PERSON>t ka gesché<PERSON>, wanns du Transaktioune kuerz noenee méchs oder wanns du probéiers, eng Transaktioun ze beschleunegen oder ze stoppen, déi schonn ofgeschloss ass.", "transaction-request.nonce-too-low.modal.title": "Transaktioun mat selwe<PERSON><PERSON> ass ofgeschloss", "transaction-request.replaced.modal.button-text": "Zoumaachen", "transaction-request.replaced.modal.text": "Mir kënnen de Status vun dëser Transaktioun net verfollegen. Entweeder gouf se duerch eng aner Transaktioun ersat oder den RPC-K<PERSON>et huet Problemer.", "transaction-request.replaced.modal.title": "Konnt den Transaktiounsstatus net fannen", "transaction.activity.details.modal.close": "Zoumaachen", "transaction.cancel_popup.cancel": "<PERSON><PERSON>, waart", "transaction.cancel_popup.confirm": "<PERSON>, stoppen", "transaction.cancel_popup.description": "Fir ze stoppen, muss du nei Netzwierkskäschte bezuelen amplaz vun den urspréngleche Käschte vun {oldFee}", "transaction.cancel_popup.description_without_original": "Stoppen erfuerdert nei Netzwierkskäschten.", "transaction.cancel_popup.not_supported.subtitle": "Stoppen ass net ënnerstëtzt op {network}", "transaction.cancel_popup.not_supported.title": "Net ënnerstëtzt", "transaction.cancel_popup.stopping_fee": "Netzwierkskäschte fir ze stoppen", "transaction.cancel_popup.title": "Transaktioun stoppen?", "transaction.in-progress": "<PERSON><PERSON><PERSON>", "transaction.inProgress": "<PERSON><PERSON><PERSON>", "transaction.speed_up_popup.cancel": "<PERSON><PERSON>, waart", "transaction.speed_up_popup.confirm": "<PERSON> mé<PERSON>", "transaction.speed_up_popup.description": "Fir ze besch<PERSON>gen, muss du eng nei Netzwierkskäscht bezuelen amplaz vun der ursprénglecher vun {amount}", "transaction.speed_up_popup.description_without_original": "Fir ze beschleunegen, bezuel eng nei Netzwierkskäscht.", "transaction.speed_up_popup.seed_up_fee_title": "Netzwierkskäscht fir ze beschleunegen", "transaction.speed_up_popup.title": "Transaktioun beschleunegen?", "transaction.speedup_popup.not_supported.subtitle": "Beschleunegung gëtt net ënnerstëtzt op {network}", "transaction.speedup_popup.not_supported.title": "Net ënnerstëtzt", "transaction.subTitle.failed": "Mësslongen", "transactionDetails.cashback.not-qualified": "Net qualifizéiert", "transactionDetails.cashback.paid": "{amount} bezuelt", "transactionDetails.cashback.pending": "{amount} ausstoend", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "Onbekannt", "transactionDetails.cashback_estimate": "Cashback-Sc<PERSON>ät<PERSON>ng", "transactionDetails.category": "<PERSON><PERSON><PERSON>", "transactionDetails.exchangeRate": "Wiesselcours", "transactionDetails.location": "<PERSON><PERSON><PERSON>", "transactionDetails.payment-approved": "Bezuelung akzeptéiert", "transactionDetails.payment-declined": "Bezuelung refuséiert", "transactionDetails.payment-reversed": "Bezuelung réckgängeg gemaach", "transactionDetails.recharge.amountSentFromEarn.title": "Betrag vum Earn-<PERSON><PERSON> g<PERSON>t", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "<PERSON> op<PERSON>", "transactionDetails.recharge.rate.title": "Cours", "transactionDetails.recharge.transactionId.title": "Transaktiouns-ID", "transactionDetails.refund": "Remboursement", "transactionDetails.reversal": "Réckbuchung", "transactionDetails.transactionCurrency": "Transaktiounswärung", "transactionDetails.transactionId": "Transaktiouns-ID", "transactionDetails.type": "Transaktioun", "transactionRequestWidget.approve.subtitle": "Fir {target}", "transactionRequestWidget.p2p.subtitle": "Un {target}", "transactionRequestWidget.unknown.subtitle": "Benotzt {target}", "transactionSafetyChecksPopup.title": "Sécherheetskontrolle vun der Transaktioun", "transactions.main.activity.title": "Aktivitéit", "transactions.page.hiddenActivity.title": "Verstoppt Aktivitéit", "transactions.page.title": "Aktivitéit", "transactions.viewTRXHistory.emptyState": "<PERSON>ch keng <PERSON>aktiou<PERSON>", "transactions.viewTRXHistory.errorMessage": "Konnten deng Transaktiounen net lueden.", "transactions.viewTRXHistory.hidden.emptyState": "<PERSON><PERSON> verst<PERSON>", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Aktivitéit fir Testnetzer net ënnerstëtzt.", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Aktivitéit fir Testnetzer net ënnerstëtzt.{br}<link>Zum Block Explorer</link>", "transfer_provider": "Iwwerweisungs-Ubidder", "transfer_setup_with_different_wallet.subtitle": "Bankiwwerweisunge si mat engem anere Portmonni ageriicht. Du kanns nëmmen ee Portmonni mat Iwwerweisunge verbannen.", "transfer_setup_with_different_wallet.swtich_and_continue": "Wiesselen a weiderfueren", "transfer_setup_with_different_wallet.title": "Port<PERSON><PERSON> w<PERSON>", "tx-sent-to-wallet.button": "Zoumaachen", "tx-sent-to-wallet.subtitle": "<PERSON>ach weider an {wallet}", "unblockProviderInfo.fees": "Du kriss déi niddregst méiglech Käschten: 0 % bis zu 5.000 $ pro Mount an 0,2 % doriwwer.", "unblockProviderInfo.registration": "Unblock ass vum FNTT registréiert an autoriséiert fir VASP-Austausch- a Verwahrdéngschter unzebidden, an ass en registréierten MSB-Ubidder bei der US Fincen. <link>Méi gewuer ginn</link>", "unblockProviderInfo.selfCustody": "Déi digital Suen, déi s du kriss, ginn an dengem private Portmonni verwalt a keen aneren huet Kontroll iwwer däi Verméigen.", "unblock_invalid_faster_payment_configuration.subtitle": "<PERSON>, deen s de uginn hues, ënnerstëtzt keng europäesch SEPA-Iwwerweisungen oder UK Faster Payments. Gëff wgl. en anere Kont un.", "unblock_invalid_faster_payment_configuration.title": "<PERSON><PERSON>", "unknownTransaction.primaryText": "Kaartentransaktioun", "unsupportedCountry.subtitle": "Bankiwwerweisunge sinn an dengem Land nach net verfügbar.", "unsupportedCountry.title": "Net verfügbar an {country}", "update-app-popup.subtitle": "De leschten Update huet <PERSON>, Features a méi. <PERSON><PERSON> den Update fir deng <PERSON>-Erfarung ze verbesseren.", "update-app-popup.title": "Zeal-Versioun aktualiséieren", "update-app-popup.update-now": "Elo aktualiséieren", "user_associated_with_other_merchant.subtitle": "Dëse Portmonni kann net fir Bankiwwerweisunge benotzt ginn. Benotz wgl. en anere Portmonni oder mellt dech op eisem Discord fir Hëllef an Updates.", "user_associated_with_other_merchant.title": "Portmonni kann net benotzt ginn", "user_associated_with_other_merchant.try_with_another_wallet": "<PERSON><PERSON><PERSON> probé<PERSON>en", "user_email_already_exists.subtitle": "Du hues scho Bankiwwerweisunge mat engem anere Portmonni ageriicht. Probéier wgl. nach eng Kéier mat dem Portmonni, deen s du virdru benotzt hues.", "user_email_already_exists.title": "Iwwerweisunge mat engem anere Portmonni ageriicht", "user_email_already_exists.try_with_another_wallet": "<PERSON><PERSON> pro<PERSON>", "validation.invalid.iban": "IBAN net valabel", "validation.required": "<PERSON><PERSON><PERSON><PERSON>", "validation.required.first_name": "Virnumm obligatoresch", "validation.required.iban": "IBAN obligatoresch", "validation.required.last_name": "Familljennumm obligatoresch", "verify-passkey.cta": "<PERSON>key verifiz<PERSON>en", "verify-passkey.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, datt däi <PERSON> erstallt a richteg ofgeséchert ass.", "verify-passkey.title": "<PERSON>key verifiz<PERSON>en", "view-cashback.cashback-next-cycle": "Cashback-<PERSON><PERSON> an {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "Maach en Depot fir Cashback ze kréien", "view-cashback.pending": "{money} Ausstoend", "view-cashback.pending-rewards.not_paid": "<PERSON><PERSON><PERSON> kritt an {days}T", "view-cashback.pending-rewards.paid": "<PERSON><PERSON><PERSON> kritt", "view-cashback.received-rewards": "<PERSON><PERSON><PERSON>", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Net confirméiert Bezuelungen", "view-cashback.upcoming": "Demnächst {money}", "virtual-card-order.configure-safe.loading-text": "<PERSON><PERSON> g<PERSON> er<PERSON>lt", "virtual-card-order.create-order.loading-text": "<PERSON><PERSON> g<PERSON> aktivé<PERSON>", "virtual-card-order.create-order.success-text": "<PERSON><PERSON>", "virtualCard.activateCard": "<PERSON><PERSON> a<PERSON>", "walletDeleteConfirm.main_action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walletDeleteConfirm.subtitle": "Du muss en erëm import<PERSON>, fir de Portfolio ze gesinn oder Transaktiounen ze maachen", "walletDeleteConfirm.title": "<PERSON><PERSON><PERSON> e<PERSON>?", "walletSetting.header": "Portmonnis-Astellungen", "wallet_connect.connect.cancel": "Ofbriechen", "wallet_connect.connect.connect_button": "Verbann Zeal", "wallet_connect.connect.title": "Verbannen", "wallet_connect.connected.title": "<PERSON><PERSON>bonnen", "wallet_connect_add_chain_missing.title": "Netzwierk net ënnerstëtzt", "wallet_connect_proposal_expired.title": "Verbindung ofgelaf", "withdraw": "<PERSON><PERSON><PERSON><PERSON>", "withdraw.confirmation.close": "Ofbriechen", "withdraw.confirmation.continue": "Bestätegen", "withdrawal_request.completed": "Ofgeschloss", "withdrawal_request.pending": "Ausstoend", "zeal-dapp.connect-wallet.cta.primary.connecting": "Verbannen...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Verbannen", "zeal-dapp.connect-wallet.cta.secondary": "Ofbriechen", "zeal-dapp.connect-wallet.title": "Verbann d<PERSON><PERSON>, fir weiderzefueren", "zealSmartWalletInfo.gas": "Bezuel Netzwierkskäschte mat ville verschiddenen Token; benotz beléiften ERC20-Token op ënnerstëtzte Chainen, fir Netzwierkskäschten ze bezuelen, net nëmmen déi natiiv <PERSON>", "zealSmartWalletInfo.recover": "<PERSON><PERSON> geh<PERSON> Sätz; Erhuelung iwwer biometresche Passkey aus dengem Passwuert-Manager, iCloud oder Google-Kont.", "zealSmartWalletInfo.selfCustodial": "Komplett privat Portmonni; Passkey-Signature ginn on-chain validéiert, fir zentral Ofhängegkeeten ze miniméieren.", "zealSmartWalletInfo.title": "Iwwer Zeal Smart Wallets", "zeal_a_rewards_already_claimed_error.title": "<PERSON>ounung scho <PERSON>efrot", "zwidget.minimizedDisconnected.label": "Zeal getrennt"}