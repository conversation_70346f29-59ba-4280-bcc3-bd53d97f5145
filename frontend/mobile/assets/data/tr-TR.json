{"Account.ListItem.details.label": "Ayrıntılar", "AddFromAddress.success": "Cüzdan Kaydedildi", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{Cüzdan yok} one{{count} Cüzdan} other{{count} Cüzdan}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Gizli İfade {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Mevcut Gizli İfadelerinden birini kullanarak yeni cüzdanlar oluştur", "AddFromExistingSecretPhrase.SelectPhrase.title": "Bir Gizli İfade Seç", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "Gizli İfaden birden çok cüzdanı yedekleyebilir. Kullanmak istediklerini seç.", "AddFromExistingSecretPhrase.WalletSelection.title": "Hızlıca cüzdan ekle", "AddFromExistingSecretPhrase.success": "Cüzdanlar Zeal'e eklendi", "AddFromHardwareWallet.subtitle": "Zeal'e bağlanmak için donanım cüzdanını seç", "AddFromHardwareWallet.title": "Donanım Cüzdanı", "AddFromNewSecretPhrase.WalletSelection.subtitle": "İçe aktarmak istediğin cüzdanları seç", "AddFromNewSecretPhrase.WalletSelection.title": "Cüzdanları içe aktar", "AddFromNewSecretPhrase.accounts": "Cüzdanlar", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "G<PERSON>li Söz Grubu cüzdanların için anah<PERSON>dır.{br}{br}Şimdi veya sonra cüzdan ekleyebilirsin.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Gizli Söz Grubu Cüzdanları", "AddFromNewSecretPhrase.subtitle": "Gizli Söz Grubunu boşluklarla ayırarak gir", "AddFromNewSecretPhrase.success_secret_phrase_added": "<PERSON><PERSON><PERSON> Söz Grubu eklendi 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Cüzdanlar Zeal'e eklendi", "AddFromNewSecretPhrase.wallets": "Cüzdanlar", "AddFromPrivateKey.subtitle": "Özel Anahtarını gir", "AddFromPrivateKey.success": "<PERSON><PERSON> anahtar eklendi 🎉", "AddFromPrivateKey.title": "Cüzdanı geri yükle", "AddFromPrivateKey.typeOrPaste": "Buraya yaz veya yapıştır", "AddFromSecretPhrase.importWallets": "{count,plural,=0{Cüzdan seçilmedi} one{Cüzdanı içe aktar} other{{count} cüzdanı içe aktar}}", "AddFromTrezor.AccountSelection.title": "Trezor cüzdanlarını içe aktar", "AddFromTrezor.hwWalletTip.subtitle": "Bir donanım cüzdanı milyonlarca adres tutar. İstediğin kadarını şimdi veya sonra ekleyebilirsin.", "AddFromTrezor.hwWalletTip.title": "Donanım Cüzdanlarından İçe Aktarma", "AddFromTrezor.importAccounts": "{count,plural,=0{Hiç cüzdan seçilmedi} one{Cüzdanı içe aktar} other{{count} cüzdanı içe aktar}}", "AddFromTrezor.success": "Cüzdanlar Zeal'e eklendi", "ApprovalSpenderTypeCheck.failed.subtitle": "Dolandırıcılık riski: ha<PERSON><PERSON> sözleşmedir", "ApprovalSpenderTypeCheck.failed.title": "<PERSON><PERSON><PERSON> ya<PERSON><PERSON><PERSON>, cüzdan", "ApprovalSpenderTypeCheck.passed.subtitle": "<PERSON><PERSON><PERSON> sözleşmeler için onay verirsin", "ApprovalSpenderTypeCheck.passed.title": "Harcama yapan bir akıllı sözleşme", "BestReturns.subtitle": "Bu sağlayıcı en yüksek getiriyi sunar.", "BestReturnsPopup.title": "En iyi getiri", "BlacklistCheck.Failed.subtitle": "Şu tarafça yapılan kötü amaçlı raporlar: <source></source>", "BlacklistCheck.Failed.title": "Site kara listeye alınmış", "BlacklistCheck.Passed.subtitle": "Şu tarafça yapılan kötü amaçlı rapor yok: <source></source>", "BlacklistCheck.Passed.title": "Site kara listede değil", "BlacklistCheck.failed.statusButton.label": "Site rapor edildi", "BridgeRoute.slippage": "Slippage {slippage}", "BridgeRoute.title": "Köprü sağlayıcısı", "CheckConfirmation.InProgress": "İşlem sürüyor...", "CheckConfirmation.success.splash": "Tamamlandı", "ChooseImportOrCreateSecretPhrase.subtitle": "Bir Gizli Söz Grubu içe aktar veya yeni bir tane oluştur", "ChooseImportOrCreateSecretPhrase.title": "<PERSON><PERSON><PERSON> Söz Grubu <PERSON>", "ConfirmTransaction.Simuation.Skeleton.title": "Güvenlik kontrolleri yapılıyor...", "ConnectionSafetyCheckResult.passed": "Güvenlik Kontrolü başarılı", "ContactGnosisPaysupport": "Gnosis Pay desteğine ulaş", "CopyKeyButton.copied": "Kopyalandı", "CopyKeyButton.copyYourKey": "Anahtarını kopyala", "CopyKeyButton.copyYourPhrase": "İfadeni kopyala", "DAppVerificationCheck.Failed.subtitle": "Site şurada listelenmiyor: <source></source>", "DAppVerificationCheck.Failed.title": "Site, uygulama kayıtlarında bulunamadı", "DAppVerificationCheck.Passed.subtitle": "Site şurada listeleniyor: <source></source>", "DAppVerificationCheck.Passed.title": "Site, uygulama kayıtlarında görünüyor", "DAppVerificationCheck.failed.statusButton.label": "Site, uygulama kayıtlarında bulunamadı", "ERC20.tokens.emptyState": "Token bulunamadı", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Öncelik Ücreti", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Son {period}: {from} ile {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Taban Ücret: {baseFee} • Güvenlik tamponu: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Mevcut Taban Ücret alınamadı", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Sonraki <PERSON>'tan yüksek. Sıkışacak", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "<PERSON><PERSON>, mevcut nonce'tan d<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "Öncelik Ücretini hesaplayamadık", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Mevcut Maksimum ücret alınamadı", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "Maks<PERSON>", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Ağ ücretleri düşene kadar takılı kalabilir", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "Maks<PERSON>", "EditFeeModal.Custom.gasLimit.title": "Gas Limiti {gasLimit}", "EditFeeModal.Custom.title": "Gelişmiş ayarlar", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Taban Ücret düşene kadar takılı kalacak", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Düşük ücret. Sıkışabilir", "EditFeeModal.EditGasLimit.estimatedGas": "Tah. gas: {estimated} • Güvenlik tamponu: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Tahmini limitten az. İşlem başarısız olacak", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Önerilen limitten az. İşlem başarısız olabilir", "EditFeeModal.EditGasLimit.subtitle": "İşlemin kullanacağı maksimum gas miktarını belirleyin. Gerekenden düşük bir limit ayarlarsanız işleminiz başarısız olur.", "EditFeeModal.EditGasLimit.title": "Gas limitini düzenle", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Minimum gas limitinden daha az: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "Sıradaki Nonce'tan yüksek. Takılı kalacak", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "<PERSON><PERSON>, mevcut nonce'tan d<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.EditNonce.subtitle": "Sonraki nonce dışında bir değer ayarlarsanız işleminiz takılı kalır", "EditFeeModal.EditNonce.title": "<PERSON><PERSON>'ı düzenle", "EditFeeModal.Header.NotEnoughBalance.errorMessage": " {amount} göndermek için gere<PERSON>", "EditFeeModal.Header.Time.unknown": "<PERSON><PERSON><PERSON>", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Maks. {fee}", "EditFeeModal.Header.fee.unknown": "Ücret bilinmiyor", "EditFeeModal.Header.subsequent_failed": "<PERSON><PERSON><PERSON><PERSON> es<PERSON>, son <PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "EditFeeModal.Layout.Header.ariaLabel": "Mevcut ücret", "EditFeeModal.MaxFee.subtitle": "<PERSON><PERSON><PERSON><PERSON>, bir iş<PERSON> için ödeyeceğiniz en yüksek tutardır, ancak genellikle tahmin edilen ücreti ödersiniz. Bu ek tampon, ağ yavaşlasa veya daha pahalı hale gelse bile işleminizin gerçekleşmesine yardımcı olur.", "EditFeeModal.MaxFee.title": "Maksimum Ağ Ücreti", "EditFeeModal.SelectPreset.Time.unknown": "<PERSON><PERSON><PERSON>", "EditFeeModal.SelectPreset.ariaLabel": "Ücret ön ayarını seç", "EditFeeModal.SelectPreset.fast": "Hızlı", "EditFeeModal.SelectPreset.normal": "Normal", "EditFeeModal.SelectPreset.slow": "Yavaş", "EditFeeModal.ariaLabel": "<PERSON><PERSON> ü<PERSON>i dü<PERSON>", "FailedSimulation.Confirmation.Item.subtitle": "<PERSON><PERSON><PERSON> bir hata <PERSON>", "FailedSimulation.Confirmation.Item.title": "İşlem simüle edilemedi", "FailedSimulation.Confirmation.subtitle": "<PERSON><PERSON> etmek istediğinizden emin misiniz?", "FailedSimulation.Confirmation.title": "Gözü kapalı imzalama yapıyorsunuz", "FailedSimulation.Title": "Simülasyon hatası", "FailedSimulation.footer.subtitle": "<PERSON><PERSON><PERSON> bir hata <PERSON>", "FailedSimulation.footer.title": "İşlem simüle edilemedi", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Gerekli: {amount} işlemi göndermek için", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "İşlenmesi uzun sürebilir", "FeeForecastWidget.networkFee": "<PERSON><PERSON>", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Ağ ücreti hesaplanamadı", "FeeForecastWidget.subsequentFailed.message": "<PERSON><PERSON><PERSON><PERSON> es<PERSON>, son <PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "FeeForecastWidget.unknownDuration": "Bilinmiyor", "FeeForecastWidget.unknownFee": "Bilinmiyor", "GasCurrencySelector.balance": "Bakiye: {balance}", "GasCurrencySelector.networkFee": "<PERSON><PERSON>", "GasCurrencySelector.payNetworkFeesUsing": "Ağ ücretlerini şununla öde", "GasCurrencySelector.removeDefaultGasToken.description": "Ücretleri en yüksek bakiyeden öde", "GasCurrencySelector.removeDefaultGasToken.title": "Otomatik ücret yönetimi", "GasCurrencySelector.save": "<PERSON><PERSON>", "GoogleDriveBackup.BeforeYouBegin.first_point": "Zeal şifremi unutursam varlıklarımı sonsuza dek kaybederim", "GoogleDriveBackup.BeforeYouBegin.second_point": "Google Drive eri<PERSON><PERSON><PERSON><PERSON> kay<PERSON>er veya Kurtarma Dosyamı değiştirirsem varlıklarımı sonsuza dek kaybederim", "GoogleDriveBackup.BeforeYouBegin.subtitle": "Lütfen özel cüzdanla ilgili aşağıdaki noktayı anlayıp kabul et:", "GoogleDriveBackup.BeforeYouBegin.third_point": "<PERSON><PERSON>, şifremi veya Google Drive erişimimi kurtarmama yardım edemez", "GoogleDriveBackup.BeforeYouBegin.title": "Başlamadan önce", "GoogleDriveBackup.loader.subtitle": "Lütfen Kurtarma Dosyanı yüklemek için Google Drive'daki isteği onayla", "GoogleDriveBackup.loader.title": "<PERSON>ay bekleniyor...", "GoogleDriveBackup.success": "Yedekleme başarılı 🎉", "MonitorOffRamp.overServiceTime": "<PERSON><PERSON><PERSON><PERSON> havale {estimated_time} içinde <PERSON>ma<PERSON>lanır, ancak bazen ek kontroller nedeniyle daha uzun sürebilir. Bu normaldir ve bu kontroller yapılırken fonlar güvendedir.{br}{br}İşlem {support_soft_deadline} içinde tamamlanmazsa, lütfen {contact_support}", "MonitorOnRamp.contactSupport": "Destekle iletişime geç", "MonitorOnRamp.from": "<PERSON><PERSON><PERSON>", "MonitorOnRamp.fundsReceived": "Fonlar alındı", "MonitorOnRamp.overServiceTime": "<PERSON><PERSON><PERSON><PERSON> havale {estimated_time} içinde tamamlanır, ancak bazen ek kontroller nedeniyle daha uzun sürebilir. Bu normal bir durumdur ve bu kontroller yapılırken fonların güvendedir.{br}{br}İşlem {support_soft_deadline} içinde tamamlanmazsa, lütfen {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Cüzdanına gö<PERSON>", "MonitorOnRamp.to": "<PERSON><PERSON><PERSON>", "MonitorOnRamp.waitingForTransfer": "Fonları havale etmen bekleniyor", "NftCollectionCheck.failed.subtitle": "Koleksiyon şurada doğrulanmamış: <source></source>", "NftCollectionCheck.failed.title": "Koleksiyon doğrulanmamış", "NftCollectionCheck.passed.subtitle": "Koleksiyon şurada doğrulanmış: <source></source>", "NftCollectionCheck.passed.title": "Koleksiyon doğrulanmış", "NftCollectionInfo.entireCollection": "<PERSON><PERSON><PERSON> k<PERSON>", "NoSigningKeyStore.createAccount": "<PERSON><PERSON><PERSON>", "NonceRangeError.biggerThanCurrent.message": "İşlem takılı kalacak", "NonceRangeError.lessThanCurrent.message": "İşlem başarısız olacak", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "Nonce çok yüksek. Takılmaması için düşür.", "NonceRangeErrorPopup.biggerThanCurrent.title": "İşlem takılı kalacak", "P2pReceiverTypeCheck.failed.subtitle": "Do<PERSON>ru adrese mi gönderiyorsun?", "P2pReceiverTypeCheck.failed.title": "Alıcı cüzdan değil, akıllı sözleşme", "P2pReceiverTypeCheck.passed.subtitle": "<PERSON><PERSON><PERSON>kları diğer cüzdanlara gönderirsin", "P2pReceiverTypeCheck.passed.title": "Alıcı bir cüzdan", "PasswordCheck.title": "<PERSON><PERSON><PERSON>i gir", "PasswordChecker.subtitle": "Sen olduğunu doğrulamak için lütfen şifreni gir", "PermitExpirationCheck.failed.subtitle": "<PERSON>ısa tut ve yalnızca ihtiyacın kadar onayla", "PermitExpirationCheck.failed.title": "Uzun geçerlilik süresi", "PermitExpirationCheck.passed.subtitle": "Bir uygulamanın token<PERSON>ini ne kadar kullanabileceği", "PermitExpirationCheck.passed.title": "Geçerlilik süresi çok uzun değil", "PrivateKeyValidationError.moreThanMaximumWords": "Maks. {count} kelime", "PrivateKeyValidationError.notValidPrivateKey": "Bu geçerli bir özel anahtar <PERSON>ğil", "PrivateKeyValidationError.secretPhraseIsInvalid": "Gizli ifade geçerli değil", "PrivateKeyValidationError.wordMisspelledOrInvalid": "#. kelime{index} hatalı yazılmış veya geçersiz", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} kelime} other{{count} kelime}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Gizli Söz Grupları ve özel anahtarlar şifrelenir ve bu cihazda kalır.", "SecretPhraseReveal.header": "Gizli İfadeni Not Al", "SecretPhraseReveal.hint": "İfadeni kimseyle paylaşma. Güvende ve çevrimdışı tut", "SecretPhraseReveal.skip.subtitle": "Bunu daha sonra yapabilirsin ama ifadeni not almadan bu cihazı kaybedersen bu cüzdana eklediğin tüm varlıkları kaybedersin", "SecretPhraseReveal.skip.takeTheRisk": "Risk alırım", "SecretPhraseReveal.skip.title": "İfadeni not almayı atla?", "SecretPhraseReveal.skip.writeDown": "Not Al", "SecretPhraseReveal.skipForNow": "<PERSON><PERSON> sonra", "SecretPhraseReveal.subheader": "Lütfen not alıp güvenli bir şekilde çevrimdışı sakla. Sonra doğrulamak için sana soracağız.", "SecretPhraseReveal.verify": "<PERSON><PERSON><PERSON><PERSON>", "SelectCurrency.tokens": "To<PERSON>'lar", "SelectCurrency.tokens.emptyState": "Token bulunamadı", "SelectRoute.slippage": "Slippage {slippage}", "SelectRoutes.emptyState": "Bu takas için rota bulunamadı", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Cüzdanı bağla", "SendERC20.labelAddress.inputPlaceholder": "Cüzdan etiketi", "SendERC20.labelAddress.subtitle": "Bu cüzdanı daha sonra bulabilmek için etiketle.", "SendERC20.labelAddress.title": "Bu cüzdanı etiketle", "SendERC20.send_to": "Alıcı", "SendERC20.tokens": "To<PERSON>'lar", "SendOrReceive.bankTransfer.primaryText": "<PERSON><PERSON>", "SendOrReceive.bankTransfer.shortText": "Ücretsiz, anında para yatırma ve çekme", "SendOrReceive.bridge.primaryText": "Köprü", "SendOrReceive.bridge.shortText": "Ağlar arasında token havalesi yap", "SendOrReceive.receive.primaryText": "Al", "SendOrReceive.receive.shortText": "Token veya koleksiyon ürünü al", "SendOrReceive.send.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.send.shortText": "<PERSON><PERSON><PERSON><PERSON>in adrese token gönder", "SendOrReceive.swap.primaryText": "<PERSON><PERSON>", "SendOrReceive.swap.shortText": "To<PERSON>'lar a<PERSON> takas yap", "SendSafeTransaction.Confirm.loading": "Güvenlik kontrolleri yapılıyor…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Kurtarma Dosyasını parolayla şifrele", "SetupRecoveryKit.google.subtitle": "Eşitlendi: {date}", "SetupRecoveryKit.google.title": "Google Drive yedeklemesi", "SetupRecoveryKit.subtitle": "Zeal'ı kaldırırsan veya cihaz değiştirirsen hesabını geri yüklemek için en az bir yola ihtiyacın olacak", "SetupRecoveryKit.title": "<PERSON><PERSON>", "SetupRecoveryKit.writeDown.subtitle": "Gizli İfadeni Not Al", "SetupRecoveryKit.writeDown.title": "<PERSON>", "Sign.CheckSafeDeployment.activate": "Etkinleştir", "Sign.CheckSafeDeployment.subtitle": "Bir uygulamaya giriş yapmadan veya zincir dışı bir mesaj imzalamadan önce cihazını bu ağda etkinleştirmen gerekir. Bu, bir akıllı cüzdan kurduktan veya kurtardıktan sonra olur.", "Sign.CheckSafeDeployment.title": "Cihazı bu ağda etkinleştir", "Sign.Simuation.Skeleton.title": "Güvenlik kontrolleri yapılıyor…", "SignMessageSafetyCheckResult.passed": "Güvenlik Kontrolleri Başarılı", "SignMessageSafetyChecksPopup.title.permits": "İzin güvenlik kontrolleri", "SimulationFailedConfirmation.subtitle": "Bu işlemi simüle ettik ve başarısız olmasına neden olacak bir sorun bulduk. Bu işlemi gönderebilirsiniz, ancak muhtemelen başarısız olacak ve ağ ücretinizi kaybedebilirsiniz.", "SimulationFailedConfirmation.title": "İşlemin başarısız olması muhtemel", "SimulationNotSupported.Title": "Simülasyon{br}desteklenmiyor{br}{network}", "SimulationNotSupported.footer.subtitle": "Bu işlemi yine de gönderebilirsiniz", "SimulationNotSupported.footer.title": "Simülasyon desteklenmiyor", "SlippagePopup.custom": "<PERSON><PERSON>", "SlippagePopup.presetsHeader": "<PERSON><PERSON> kayması", "SlippagePopup.title": "<PERSON><PERSON>", "SmartContractBlacklistCheck.failed.subtitle": "<PERSON><PERSON>un tarafından kötü amaçlı olarak raporlandı: <source></source>", "SmartContractBlacklistCheck.failed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> kara <PERSON>e", "SmartContractBlacklistCheck.passed.subtitle": "<PERSON><PERSON><PERSON> tarafından kötü amaçlı rapor yok: <source></source>", "SmartContractBlacklistCheck.passed.title": "S<PERSON>zleşme kara <PERSON>", "SuspiciousCharactersCheck.Failed.subtitle": "<PERSON><PERSON>, yaygın bir kimlik avı taktiğidir", "SuspiciousCharactersCheck.Failed.title": "Yaygın kimlik avı kalıplarını kontrol ediyoruz", "SuspiciousCharactersCheck.Passed.subtitle": "Kimlik avı girişimlerini kontrol ediyoruz", "SuspiciousCharactersCheck.Passed.title": "Adreste olağan dışı karakter yok", "SuspiciousCharactersCheck.failed.statusButton.label": "Adreste olağan dışı karakterler var ", "TokenVerificationCheck.failed.subtitle": "Token şurada listelenmiyor: <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} , CoinGecko tarafından doğrulanmamış", "TokenVerificationCheck.passed.subtitle": "Token şurada listeleniyor: <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} , CoinGecko tarafından doğrulanmış", "TopupDapp.MonitorTransaction.success.splash": "Tamamlandı", "TransactionSafetyCheckResult.passed": "Güvenlik Kontrolleri Başarılı", "TransactionSimulationCheck.failed.subtitle": "Hata: {errorMessage}", "TransactionSimulationCheck.failed.title": "İşlem büyük ihtimalle başarısız olacak", "TransactionSimulationCheck.passed.subtitle": "Simülasyon şununla yapıldı: <source></source>", "TransactionSimulationCheck.passed.title": "İşlem önizlemesi başarılı oldu", "TrezorError.trezor_action_cancelled.action": "Ka<PERSON><PERSON>", "TrezorError.trezor_action_cancelled.subtitle": "İşlemi donanım cüzdanında reddettin", "TrezorError.trezor_device_used_elsewhere.action": "Trezor'ı senkronize et", "TrezorError.trezor_device_used_elsewhere.subtitle": "<PERSON><PERSON><PERSON> tüm açık oturumları kapattığından emin ol ve Trezor'ını yeniden senkronize etmeyi dene", "TrezorError.trezor_method_cancelled.action": "Trezor'ı senkronize et", "TrezorError.trezor_method_cancelled.subtitle": "Trezor'ın cüzdanları Zeal'e aktarmasına izin verdiğinden emin ol", "TrezorError.trezor_permissions_not_granted.action": "Trezor'ı senkronize et", "TrezorError.trezor_permissions_not_granted.subtitle": "Lütfen Zeal'e tüm cüzdanları görme izni ver", "TrezorError.trezor_pin_cancelled.action": "Trezor'ı senkronize et", "TrezorError.trezor_pin_cancelled.subtitle": "Oturum cihazda iptal edildi", "TrezorError.trezor_popup_closed.action": "Trezor'ı senkronize et", "TrezorError.trezor_popup_closed.subtitle": "<PERSON><PERSON><PERSON> kutusu beklenmedik şekilde ka<PERSON>ı", "TrxLikelyToFail.lessThanEstimatedGas.message": "İşlem başarısız olacak", "TrxLikelyToFail.lessThanMinimumGas.message": "İşlem başarısız olacak", "TrxLikelyToFail.lessThanSuggestedGas.message": "Başarısız olabilir", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "İşlem Gas Limiti çok düşük. Başarısızlığı önlemek için Gas Limitini önerilen limite yükseltin.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "İşlemin başarısız olması muhtemel", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Gas Limiti, tahmini gas'tan daha dü<PERSON>. Gas Limitini önerilen limite yükseltin.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "İşlem başarısız olacak", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "Maks<PERSON><PERSON> Taban Ücret, mevcut taban ücretten daha düşük. İşlemin takılı kalmasını önlemek için Maksimum Taban Ücreti artırın.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "İşlem takılı kalacak", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "İşlem Maksimum Ücreti çok düşük. İşlemin takılı kalmasını önlemek için Maksimum Ücreti artırın.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "İşlem takılı kalacak", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Öncelik Ücreti, önerilenden daha düşük. İşlemi hızlandırmak için Öncelik Ücretini artırın.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "İş<PERSON>in ta<PERSON>lanması uzun sürebilir", "UnsupportedMobileNetworkLayout.gotIt": "<PERSON><PERSON><PERSON><PERSON>!", "UnsupportedMobileNetworkLayout.subtitle": "Henüz şu ağ kimliğiyle işlem yapamazsın: {networkHexId} Zeal mobil sürümüyle{br}{br}Bu ağda işlem yapmak için tarayıcı uzantısına geç. Biz de bu ağ için desteği eklemeye çalışıyoruz 🚀", "UnsupportedMobileNetworkLayout.title": "<PERSON><PERSON>, Zeal mobilde <PERSON>", "UnsupportedSafeNetworkLayout.subtitle": "İşlem yapamaz veya mesaj imzalayamazsın {network} üzerinde bir Zeal Akıllı Cüzdan ile{br}{br}Desteklenen bir ağa geç veya bir Eski cüzdan kullan.", "UnsupportedSafeNetworkLayoutk.title": "Ağ, Akıllı Cüzdan için desteklenmiyor", "UserConfirmationPopup.goBack": "İptal", "UserConfirmationPopup.submit": "<PERSON><PERSON>", "ViewPrivateKey.header": "<PERSON><PERSON>", "ViewPrivateKey.hint": "Özel anahtarını kimseyle paylaşma. Güvende ve çevrimdışı tut", "ViewPrivateKey.subheader.mobile": "Özel Anahtarını görmek için dokun", "ViewPrivateKey.subheader.web": "Özel Anahtarını görmek için üzerine gel", "ViewPrivateKey.unblur.mobile": "Görmek için dokun", "ViewPrivateKey.unblur.web": "Görmek için üzerine gel", "ViewSecretPhrase.PasswordChecker.subtitle": "Kurtarma Dosyasını şifrelemek için şifreni gir. Gelecekte hatırlaman gerekecek.", "ViewSecretPhrase.done": "<PERSON><PERSON>", "ViewSecretPhrase.header": "Gizli İ<PERSON>de", "ViewSecretPhrase.hint": "İfadeni kimseyle paylaşma. Güvende ve çevrimdışı tut", "ViewSecretPhrase.subheader.mobile": "Gizli İfadeni görmek için dokun", "ViewSecretPhrase.subheader.web": "Gizli İfadeni görmek için üzerine gel", "ViewSecretPhrase.unblur.mobile": "Görmek için dokun", "ViewSecretPhrase.unblur.web": "Görmek için üzerine gel", "account-details.monerium": "<PERSON><PERSON><PERSON>, yet<PERSON><PERSON> ve düzenlenmiş bir EMI olan Monerium kullanılarak yapılır. <link>Daha fazla bilgi</link>", "account-details.unblock": "<PERSON><PERSON><PERSON>, yet<PERSON><PERSON> ve tescilli bir borsa ve saklama hizmeti sağlayıcısı olan Unblock kullanılarak yapılır. <link>Daha fazla bilgi al</link>", "account-selector.empty-state": "Hiç cüzdan bulunamadı", "account-top-up.select-currency.title": "To<PERSON>'lar", "account.accounts_not_found": "<PERSON>ç cüzdan bulamadık", "account.accounts_not_found_search_valid_address": "Cüzdan listende <PERSON>", "account.add.create_new_secret_phrase": "Gizli Söz Grubu Oluştur", "account.add.create_new_secret_phrase.subtext": "Yeni 12 kelimelik gizli söz grubu", "account.add.fromRecoveryKit.fileNotFound": "Dosyanı bulamadık", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "<PERSON><PERSON><PERSON> dene", "account.add.fromRecoveryKit.fileNotFound.explanation": "Lütfen Zeal Yedekleme klasörünün bulunduğu doğru hesapta oturum açtığını kontrol et", "account.add.fromRecoveryKit.fileNotValid": "Kurtarma Dosyası geçerli değil", "account.add.fromRecoveryKit.fileNotValid.explanation": "Dosyanı kontrol ettik, ya yanlış türde ya da değiştirilmiş", "account.add.import_secret_phrase": "Gizli Söz Grubunu İçe Aktar", "account.add.import_secret_phrase.subtext": "<PERSON><PERSON>, Metamask veya diğerlerinde oluşturuldu", "account.add.select_type.add_hardware_wallet": "Donanım Cüzdanı", "account.add.select_type.existing_smart_wallet": "Mevcut Smart Wallet", "account.add.select_type.private_key": "<PERSON><PERSON>", "account.add.select_type.seed_phrase": "<PERSON>hum Söz Gru<PERSON>", "account.add.select_type.title": "Cüzdanı içe aktar", "account.add.select_type.zeal_recovery_file": "Zeal Kurtarma <PERSON>", "account.add.success.title": "<PERSON>ni cüzdan oluşturuldu 🎉", "account.addLabel.header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> isim ver", "account.addLabel.labelError.labelAlreadyExist": "<PERSON><PERSON> isim zaten var. Başka bir isim dene", "account.addLabel.labelError.maxStringLengthExceeded": "<PERSON><PERSON><PERSON>um karakter sayısına ula<PERSON>ıldı", "account.add_active_wallet.primary_text": "<PERSON><PERSON><PERSON><PERSON> ekle", "account.add_active_wallet.short_text": "Cüzdan Oluştur, Bağla veya İçe Aktar", "account.add_from_ledger.success": "Cüzdanlar Zeal'e eklendi", "account.add_tracked_wallet.primary_text": "Salt okunur cüzdan ekle", "account.add_tracked_wallet.short_text": "Portföyü ve etkinliği gör", "account.button.unlabelled-wallet": "Etiketsiz <PERSON>", "account.create_wallet": "Cüzdan Oluştur", "account.label.edit.title": "Cüzdan adını düzenle", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarihi {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Kurtarma Dosyası geçerli değil", "account.recoveryKit.selectBackupFile.subtitle": "Geri yüklemek istediğin Kurtarma Dosyasını seç", "account.recoveryKit.selectBackupFile.title": "Kurtarma Dosyası", "account.recoveryKit.success.recoveryFileFound": "Kurtarma Dosyası bulundu 🎉", "account.select_type_of_account.create_eoa.short": "Uzmanlar için eski tip cüzdan", "account.select_type_of_account.create_eoa.title": "Tohum Söz Grubu cüzdanı oluştur", "account.select_type_of_account.create_safe_wallet.title": "Smart Wallet oluştur", "account.select_type_of_account.existing_smart_wallet": "Mevcut Smart Wallet", "account.select_type_of_account.hardware_wallet": "Donanım cüzdanı", "account.select_type_of_account.header": "<PERSON><PERSON><PERSON><PERSON> ekle", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "<PERSON><PERSON> / <PERSON><PERSON>", "account.select_type_of_account.read_only_wallet": "Salt okunur cüzdan", "account.select_type_of_account.read_only_wallet.short": "Herhangi bir portföyü önizle", "account.topup.title": "Zeal'a para ekle", "account.view.error.refreshAssets": "<PERSON><PERSON><PERSON>", "account.widget.refresh": "<PERSON><PERSON><PERSON>", "account.widget.settings": "<PERSON><PERSON><PERSON>", "accounts.view.copied-text": "Kopyalandı {formattedAddress}", "accounts.view.copiedAddress": "Kopyalandı {formattedAddress}", "action.accept": "Kabul et", "action.accpet": "Kabul et", "action.allow": "<PERSON><PERSON> ver", "action.back": "<PERSON><PERSON>", "action.cancel": "İptal", "action.card-activation.title": "Kartı etkinleştir", "action.claim": "<PERSON><PERSON> et", "action.close": "Ka<PERSON><PERSON>", "action.complete-steps": "<PERSON><PERSON><PERSON>", "action.confirm": "<PERSON><PERSON><PERSON>", "action.continue": "<PERSON><PERSON> et", "action.copy-address-understand": "Tamam <PERSON> <PERSON><PERSON><PERSON>", "action.deposit": "<PERSON><PERSON><PERSON><PERSON>", "action.done": "<PERSON><PERSON>", "action.dontAllow": "<PERSON>zin Verme", "action.edit": "<PERSON><PERSON><PERSON><PERSON>", "action.email-required": "E-posta gir", "action.enterPhoneNumber": "Telefon numarası gir", "action.expand": "Genişlet", "action.fix": "<PERSON><PERSON><PERSON><PERSON>", "action.getStarted": "Başla", "action.got_it": "<PERSON><PERSON><PERSON><PERSON>", "action.hide": "<PERSON><PERSON><PERSON>", "action.import": "İçe Aktar", "action.import-keys": "Anahtarları içe aktar", "action.importKeys": "Anahtarları içe aktar", "action.minimize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.next": "<PERSON><PERSON><PERSON>", "action.ok": "<PERSON><PERSON>", "action.reduceAmount": "<PERSON><PERSON><PERSON><PERSON>", "action.refreshWebsite": "Web sitesini yenile", "action.remove": "Kaldır", "action.remove-account": "Hesabı kaldır", "action.requestCode": "Kod iste", "action.resend_code": "<PERSON><PERSON> yeniden gönder", "action.resend_code_with_time": "<PERSON><PERSON> yeniden gönder {time}", "action.retry": "<PERSON><PERSON><PERSON> dene", "action.reveal": "<PERSON><PERSON><PERSON>", "action.save": "<PERSON><PERSON>", "action.save_changes": "R<PERSON>", "action.search": "Ara", "action.seeAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gör", "action.select": "Seç", "action.send": "<PERSON><PERSON><PERSON>", "action.skip": "Atla", "action.submit": "<PERSON><PERSON><PERSON>", "action.understood": "Anlıyorum", "action.update": "<PERSON><PERSON><PERSON><PERSON>", "action.update-gnosis-pay-owner.complete": "<PERSON><PERSON><PERSON>", "action.zeroAmount": "Tutar gir", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Koleksi<PERSON>luk<PERSON>", "action_bar_title.tokens": "To<PERSON>'lar", "action_bar_title.transaction_request": "İşlem talebi", "activate-monerium.loading": "<PERSON><PERSON><PERSON><PERSON> kuru<PERSON>", "activate-monerium.success.title": "Monerium et<PERSON>di", "activate-physical-card-widget.subtitle": "Teslimat 3 hafta sürebilir", "activate-physical-card-widget.title": "Fiziksel kartı etkinleştir", "activate-smart-wallet.title": "Cüzdanı etkinleştir", "active_and_tracked_wallets.title": "Zeal, bu ağ üzerindeki tüm ücretlerini karşılıyor {network}, bu da ücretsiz işlem yapmanı sağlıyor!", "activity.approval-amount.revoked": "İptal edildi", "activity.approval-amount.unlimited": "Sınırsız", "activity.approval.approved_for": "<PERSON><PERSON><PERSON> i<PERSON>", "activity.approval.approved_for_with_target": "Onaylandı {approvedTo}", "activity.approval.revoked_for": "<PERSON><PERSON><PERSON> i<PERSON>in iptal edildi", "activity.bank.serviceProvider": "Hizmet sağlayıcı", "activity.bridge.serviceProvider": "Hizmet sağlayıcı", "activity.cashback.period": "<PERSON><PERSON><PERSON>", "activity.filter.card": "Kart", "activity.rate": "<PERSON><PERSON>", "activity.receive.receivedFrom": "<PERSON><PERSON><PERSON><PERSON>", "activity.send.sendTo": "Alıcı", "activity.smartContract.unknown": "Bilinmeyen sözleşme", "activity.smartContract.usingContract": "Kullanılan sözleşme", "activity.subtitle.pending_timer": "{timerString} Bekleniyor", "activity.title.arbitrary_smart_contract_interaction": "{function} üzerinde {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Bilinmeyen sözleşme etkileşimi", "activity.title.bridge.from": "<PERSON>ö<PERSON><PERSON><PERSON> gönder<PERSON> {token}", "activity.title.bridge.to": "Kö<PERSON><PERSON><PERSON> alımı {token}", "activity.title.buy": "Alım {asset}", "activity.title.card_owners_updated": "<PERSON><PERSON> sa<PERSON> g<PERSON>", "activity.title.card_spend_limit_updated": "Kart harcama limiti ayarlandı", "activity.title.cashback_deposit": "<PERSON><PERSON><PERSON> i<PERSON> yatı<PERSON>", "activity.title.cashback_reward": "Nakit iadesi ödülü", "activity.title.cashback_withdraw": "Nakit iadesinden çekim", "activity.title.claimed_reward": "<PERSON><PERSON><PERSON><PERSON> alındı", "activity.title.deployed_smart_wallet_gnosis": "<PERSON><PERSON><PERSON>", "activity.title.deposit_from_bank": "Bankadan para yatırma", "activity.title.deposit_into_card": "Karta Para Yatırma", "activity.title.deposit_into_earn": "Yatırma: {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} üzerinde {smartContract}", "activity.title.from": "G<PERSON>nderen: {sender}", "activity.title.pendidng_areward_claim": "<PERSON><PERSON><PERSON><PERSON> talep edili<PERSON>r", "activity.title.pendidng_breward_claim": "<PERSON><PERSON><PERSON><PERSON> talep edili<PERSON>r", "activity.title.recharge_disabledh": "Kart otomatik yükleme devre dışı", "activity.title.recharge_set": "Otomatik yükleme hedefi <PERSON>ı", "activity.title.recovered_smart_wallet_gnosis": "<PERSON><PERSON> cihaz kuru<PERSON>u", "activity.title.send_pending": "Alıcı: {receiver}", "activity.title.send_to_bank": "Bankaya", "activity.title.swap": "Alım {token}", "activity.title.to": "Alıcı: {receiver}", "activity.title.withdraw_from_card": "<PERSON><PERSON><PERSON>", "activity.title.withdraw_from_earn": "<PERSON><PERSON><PERSON>: {earn}", "activity.transaction.networkFees": "<PERSON><PERSON>", "activity.transaction.state": "İşlem Tamamlandı", "activity.transaction.state.completed": "Tamamlanan <PERSON>", "activity.transaction.state.failed": "Başarısız İşlem", "add-account.section.import.header": "İçe aktar", "add-another-card-owner": "Başka bir kart sahibi ekle", "add-another-card-owner.Recommended.footnote": "Zeal cüzdanını Gnosis Pay kartına ek sahip olarak ekle", "add-another-card-owner.Recommended.primaryText": "Zeal'i Gnosis Pay'e ekle", "add-another-card-owner.recommended": "Önerilen", "add-owner.confirmation.subtitle": "Güvenlik nedeniyle ayar değişikliklerinin işlenmesi 3 dakika sürer. Bu sırada kartın geçici olarak dondurulur ve ödeme yapılamaz.", "add-owner.confirmation.title": "Ayarlar güncellenirken kartın 3 dakika dondurulacak", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Cü<PERSON>n eklenemiyor, zaten kull<PERSON>ımda", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "Başka bir cüzdan dene", "add.account.backup.decrypt.success": "<PERSON>üzdan geri yü<PERSON>", "add.account.backup.password.passwordIncorrectMessage": "<PERSON><PERSON><PERSON>", "add.account.backup.password.subtitle": "Kurtarma Dosyası parolanı gir.", "add.account.backup.password.title": "Parolayı gir", "add.account.google.login.subtitle": "Dosyanı eşitlemek için Google'da onayla.", "add.account.google.login.title": "<PERSON>ay bekleniyor...", "add.readonly.already_added": "Cüzdan zaten eklenmiş", "add.readonly.continue": "<PERSON><PERSON> et", "add.readonly.empty": "Bir adres veya ENS gir", "addBankRecipient.title": "Banka alıcısı ekle", "add_funds.deposit_from_bank_account": "Banka hesabından para yatır", "add_funds.from_another_wallet": "Başka bir cüzdandan", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "<PERSON><PERSON>kleme dApp'ine bağlan", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Herhangi bir cüzdanı Zeal yükleme dApp'ine bağlayarak cüzdanına hızla para gönder", "add_funds.from_crypto_wallet.header": "Başka bir cüzdandan", "add_funds.from_crypto_wallet.header.show_wallet_address": "Cüzdan adresini göster", "add_funds.from_exchange.header": "<PERSON><PERSON><PERSON>", "add_funds.from_exchange.header.copy_wallet_address": "Zeal adresini k<PERSON>", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance vb.", "add_funds.from_exchange.header.open_exchange": "Borsa uygulamasını veya sitesini aç", "add_funds.from_exchange.header.selected_token": " {token} <PERSON><PERSON><PERSON><PERSON> g<PERSON>nder", "add_funds.from_exchange.header.selected_token.subtitle": "Ağ: {network}", "add_funds.from_exchange.header.send_selected_token": "Desteklenen token gönder", "add_funds.from_exchange.header.send_selected_token.subtitle": "Desteklenen token'ı ve ağı seç", "add_funds.import_wallet": "Mevcut kripto cüzdanını içe aktar", "add_funds.title": "<PERSON><PERSON><PERSON>ına para yatır", "add_funds.transfer_from_exchange": "<PERSON><PERSON><PERSON> ha<PERSON>", "address.add.header": "Cüzdanını Zeal'de gör{br}salt okunur modda", "address.add.subheader": "Varlıklarını tüm EVM ağlarında tek bir yerden görmek için adresini veya ENS'ni gir. Daha sonra daha fazla cüzdan oluşturabilir veya içe aktarabilirsin.", "address_book.change_account.bank_transfers.header": "Banka alıcıları", "address_book.change_account.bank_transfers.primary": "Banka alıcısı", "address_book.change_account.cta": "Cüzdanı izle", "address_book.change_account.search_placeholder": "<PERSON><PERSON> ekle veya ara", "address_book.change_account.tracked_header": "Salt okunur cüzdanlar", "address_book.change_account.wallets_header": "Aktif <PERSON>", "app-association-check-failed.modal.cta": "<PERSON><PERSON><PERSON> dene", "app-association-check-failed.modal.subtitle": "Lütfen tekrar dene. Bağlantı sorunları Passkey'lerini getirirken gecikmelere neden oluyor. Sorun devam ederse Zeal'i yeniden başlatıp bir kez daha dene.", "app-association-check-failed.modal.subtitle.creation": "Lütfen tekrar dene. Bağlantı sorunları Passkey oluşturulmasını geciktiriyor. <PERSON><PERSON> devam ederse Zeal'i yeniden başlatıp bir kez daha dene.", "app-association-check-failed.modal.title.creation": "Cihazın Passkey oluşturamadı", "app-association-check-failed.modal.title.signing": "Cihazın <PERSON>'<PERSON><PERSON>", "app.app_protocol_group.borrowed_tokens": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.claimable_amount": "Talep edilebilir tutar", "app.app_protocol_group.health_rate": "Sağlık oranı", "app.app_protocol_group.lending": "<PERSON><PERSON><PERSON>", "app.app_protocol_group.locked_tokens": "<PERSON><PERSON><PERSON>", "app.app_protocol_group.nfts": "Koleksi<PERSON>luk<PERSON>", "app.app_protocol_group.reward_tokens": "<PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.supplied_tokens": "<PERSON><PERSON><PERSON><PERSON> tokenler", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "<PERSON><PERSON><PERSON><PERSON>", "app.appsGroupHeader.discoverMore": "Daha fazlasını keşfet", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Ara veya URL gir", "app.error-banner.cory": "Hata verilerini k<PERSON>ala", "app.error-banner.retry": "<PERSON><PERSON><PERSON> dene", "app.list_item.rewards": "<PERSON><PERSON><PERSON><PERSON> {value}", "app.position_details.health_rate.description": "Sağ<PERSON><PERSON>k oranı, aldığın borcun teminatının değ<PERSON>ne bölünmesiyle he<PERSON>lanı<PERSON>.", "app.position_details.health_rate.title": "Sağlık oranı nedir?", "approval.edit-limit.label": "<PERSON><PERSON>ma limitini dü<PERSON>le", "approval.permit_info": "İzin bilgileri", "approval.spend-limit.edit-modal.cancel": "İptal", "approval.spend-limit.edit-modal.limit-label": "<PERSON><PERSON><PERSON> limiti", "approval.spend-limit.edit-modal.max-limit-error": "Uyarı, yüksek limit", "approval.spend-limit.edit-modal.revert": "Değişiklikleri geri al", "approval.spend-limit.edit-modal.set-to-unlimited": "Sınırsız olarak ayarla", "approval.spend-limit.edit-modal.submit": "<PERSON><PERSON>", "approval.spend-limit.edit-modal.title": "İzinleri düzenle", "approval.spend_limit_info": "Ha<PERSON>ma limiti nedir?", "approval.what_are_approvals": "<PERSON><PERSON><PERSON> nedir?", "apps_list.page.emptyState": "<PERSON>kt<PERSON>k", "backpace.removeLastDigit": "Son rakamı sil", "backup-banner.backup_now": "<PERSON><PERSON><PERSON>", "backup-banner.risk_losing_funds": "Fonlarını kaybetme riskine karşı hemen yedekle", "backup-banner.title": "Cüzdan yedeklenmedi", "backupRecoverySmartWallet.noExportPrivateKeys": "Otomatik yedekleme: Smart Wallet'ın bir geçiş anahtarı olarak kaydedilir; giz<PERSON> ifade veya özel anahtar gerekmez.", "backupRecoverySmartWallet.safeContracts": "Çok anahtarlı güvenlik: <PERSON><PERSON> cüzdanları Safe sözleşmeleriyle <PERSON>alışı<PERSON>, bu sayede bir işlemi birden fazla cihaz onaylayabilir. Tek bir hata noktası yoktur.", "backupRecoverySmartWallet.security": "Birden fazla cihaz: Cüzdanını Geçiş Anahtarı ile birden fazla cihazda kullanabilirsin. Her cihaz kendi özel anahtarını alır.", "backupRecoverySmartWallet.showLocalPrivateKey": "Uzman modu: <PERSON>u cihazın özel anahtarını dışa aktarabilir, başka bir cüzdanda kullanabilir ve <SafeGlobal>https://safe.global</SafeGlobal> üzerinden bağlanabilirsin. <Key>Özel anahtarı göster</Key>", "backupRecoverySmartWallet.storingKeys": "Bulutla senkronize: Geçiş anahtarı iCloud'da, Google Şifre Yöneticisi'nde veya şifre yöneticinde güvenli bir şekilde saklanır.", "backupRecoverySmartWallet.title": "Smart Wallet Yedeklem<PERSON> ve <PERSON>", "balance-change.card.titile": "Kart", "balanceChange.pending": "Beklemede", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Hizmet sağlayıcı seç", "bank-transfer.change-deposit-receiver.subtitle": "Tüm banka mevduatlarını bu cüzdan alacak", "bank-transfer.change-deposit-receiver.title": "Alıcı cüzdanı ayarla", "bank-transfer.change-owner.subtitle": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, banka havalesi hesabına giriş yapmak ve hesabını kurtarmak için kullanılır", "bank-transfer.change-owner.title": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Zeal'a gönderdiğin tüm banka mevduatları bu cüzdana yatırılacak.", "bank-transfer.configrm-change-deposit-receiver.title": "Alıcı cüzdanı değiştir", "bank-transfer.configrm-change-owner.subtitle": "Hesap sahi<PERSON>i değiştirmek istediğinden emin misin? <PERSON><PERSON>ü<PERSON>, banka havalesi hesabına giriş yapmak ve hesabını kurtarmak için kullanılır.", "bank-transfer.configrm-change-owner.title": "<PERSON><PERSON><PERSON>", "bank-transfer.deposit.widget.status.complete": "Tamamlandı", "bank-transfer.deposit.widget.status.funds_received": "Fonlar alındı", "bank-transfer.deposit.widget.status.sending_to_wallet": "Cüzdana gönderiliyor", "bank-transfer.deposit.widget.status.transfer-on-hold": "<PERSON><PERSON> be<PERSON>", "bank-transfer.deposit.widget.status.transfer-received": "Cüzdana gönderiliyor", "bank-transfer.deposit.widget.subtitle": "{from} → {to}", "bank-transfer.deposit.widget.title": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.intro.bulletlist.point_1": "Unblock ile kurulum", "bank-transfer.intro.bulletlist.point_2": "EUR/GBP ile 10'dan fazla token arasında havale", "bank-transfer.intro.bulletlist.point_3": "Aylık 5 bin do<PERSON>a kadar %0, sonrasında %0,2 ücret", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Bankaya gönderiliyor", "bank-transfer.withdrawal.widget.status.in-progress": "Havale yapılıyor", "bank-transfer.withdrawal.widget.status.on-hold": "<PERSON><PERSON> be<PERSON>", "bank-transfer.withdrawal.widget.status.success": "Tamamlandı", "bank-transfer.withdrawal.widget.subtitle": "{from} için {to}", "bank-transfer.withdrawal.widget.title": "Para Çekme", "bank-transfers.bank-account-actions.remove-this-account": "<PERSON>u hesabı kaldır", "bank-transfers.bank-account-actions.switch-to-this-account": "Bu hesaba geç", "bank-transfers.deposit.fees-for-less-than-5k": "5 bin dolar veya daha azı i<PERSON><PERSON>", "bank-transfers.deposit.fees-for-more-than-5k": "5 bin dolardan fazlası iç<PERSON>", "bank-transfers.set-receiving-bank.title": "Alıcı bankayı ayarla", "bank-transfers.settings.account_owner": "<PERSON><PERSON><PERSON>", "bank-transfers.settings.receiver_of_bank_deposits": "Banka mevduatları alıcısı", "bank-transfers.settings.receiver_of_withdrawals": "Para çekme alıcısı", "bank-transfers.settings.registered_email": "Kayıtlı E-posta", "bank-transfers.settings.title": "Banka havalesi a<PERSON>ı", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} Hesabı", "bank-transfers.setup.bank-account": "Banka hesabı", "bankTransfer.withdraw.max_loading": "Maks: {amount}", "bank_details_do_not_match.got_it": "<PERSON><PERSON><PERSON><PERSON>", "bank_details_do_not_match.subtitle": "Banka kodu ve hesap numarası eşleşmiyor. Lütfen bilgilerin doğru girildiğini iki kez kontrol edip tekrar dene.", "bank_details_do_not_match.title": "Banka bilgileri eşleşmiyor", "bank_tranfsers.select_country_of_residence.country_not_supported": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,  için banka havalesi {country} hen<PERSON>z desteklenmiyor.", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Banka uygulamanı aç", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "<PERSON><PERSON><PERSON> {fiatCurrencyCode} he<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.deposit.header": "{fullName}''in kişisel hesap bilgileri", "bank_transfer.kyc_status_widget.subtitle": "<PERSON><PERSON>i", "bank_transfer.kyc_status_widget.title": "Kimlik doğrulanıyor", "bank_transfer.personal_details.date_of_birth": "<PERSON><PERSON><PERSON> tarihi", "bank_transfer.personal_details.date_of_birth.invalid_format": "Geçersiz tarih", "bank_transfer.personal_details.date_of_birth.too_young": "En az 18 yaşında olmalısın", "bank_transfer.personal_details.first_name": "Ad", "bank_transfer.personal_details.last_name": "Soyadı", "bank_transfer.personal_details.title": "Bil<PERSON><PERSON><PERSON>", "bank_transfer.reference.label": "Referans (İsteğe bağlı)", "bank_transfer.reference_message": "<PERSON><PERSON>'dan g<PERSON>", "bank_transfer.residence_details.address": "Adresin", "bank_transfer.residence_details.city": "Şehir", "bank_transfer.residence_details.country_of_residence": "İkamet edilen <PERSON>", "bank_transfer.residence_details.country_placeholder": "<PERSON><PERSON><PERSON>", "bank_transfer.residence_details.postcode": "Posta kodu", "bank_transfer.residence_details.street": "Sokak", "bank_transfer.residence_details.your_residence": "İkametgahın", "bank_transfers.choose-wallet.continue": "<PERSON><PERSON>", "bank_transfers.choose-wallet.test": "<PERSON><PERSON><PERSON><PERSON> ekle", "bank_transfers.choose-wallet.warning.subtitle": "Aynı anda yalnızca bir cüzdan bağlayabilirsin. Bağlı cüzdanı değiştiremezsin.", "bank_transfers.choose-wallet.warning.title": "Cüzdanını akıllıca seç", "bank_transfers.choose_wallet.subtitle": "Banka hesabını bağlayacağın cüzdanı seç. ", "bank_transfers.choose_wallet.title": "Cüzdan seç", "bank_transfers.continue": "<PERSON><PERSON>", "bank_transfers.currency_is_currently_not_supported": "<PERSON><PERSON>", "bank_transfers.deposit-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.account-name": "<PERSON><PERSON><PERSON> adı", "bank_transfers.deposit.account-number-copied": "Hesap Numarası Kopyalandı", "bank_transfers.deposit.amount-input": "Yatırılacak tutar", "bank_transfers.deposit.amount-output": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.amount-output.error": "hata", "bank_transfers.deposit.buttet-point.receive-crypto": "Al {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "<PERSON><PERSON>", "bank_transfers.deposit.currency-not-supported.subtitle": "Banka mevduatları için {code} desteği bir sonraki duyuruya kadar kaldırılmıştır.", "bank_transfers.deposit.currency-not-supported.title": "{code} mevduatları şu anda desteklenmiyor", "bank_transfers.deposit.default-token.balance": "Bakiye {amount}", "bank_transfers.deposit.deposit-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.enter_amount": "Tutar G<PERSON>", "bank_transfers.deposit.iban-copied": "IBAN Kopyalandı", "bank_transfers.deposit.increase-amount": "Minimum havale tutarı {limit}", "bank_transfers.deposit.loading": "Yükleniyor", "bank_transfers.deposit.max-limit-reached": "<PERSON><PERSON>, maksimum havale limitini aşıyor", "bank_transfers.deposit.modal.kyc.button-text": "Başla", "bank_transfers.deposit.modal.kyc.text": "Kimliğini doğrulamak için bazı kişisel bilgilere ve belgelere ihtiyacımız olacak. Bu işlemi tamamlamak genellikle yalnızca birkaç dakika sürer.", "bank_transfers.deposit.modal.kyc.title": "Limitlerini artırmak için kimliğini doğrula", "bank_transfers.deposit.reduce_amount": "Tutarı azalt", "bank_transfers.deposit.show-account.account-number": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Banka kodu", "bank_transfers.deposit.sort-code-copied": "Banka Kodu Kopyalandı", "bank_transfers.deposit.withdraw-header": "Çek", "bank_transfers.failed_to_load_fee": "Bilinmiyor", "bank_transfers.fees": "<PERSON><PERSON><PERSON>", "bank_transfers.increase-amount": "Minimum havale tutarı {limit}", "bank_transfers.insufficient-funds": "<PERSON><PERSON><PERSON>", "bank_transfers.select_country_of_residence.title": "Nerede yaşıyorsun?", "bank_transfers.setup.cta": "<PERSON><PERSON> ha<PERSON> a<PERSON>", "bank_transfers.setup.enter-amount": "Tutar gir", "bank_transfers.source_of_funds.form.business_income": "<PERSON><PERSON>ri gelir", "bank_transfers.source_of_funds.form.other": "<PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.pension": "<PERSON><PERSON><PERSON> ma<PERSON>ı", "bank_transfers.source_of_funds.form.salary": "Maaş", "bank_transfers.source_of_funds.form.title": "<PERSON>on ka<PERSON>n", "bank_transfers.source_of_funds_description.placeholder": "Fon kaynağını açıkla...", "bank_transfers.source_of_funds_description.title": "Fon kaynağın hakkında daha fazla bilgi ver", "bank_transfers.withdraw-header": "Çek", "bank_transfers.withdraw.amount-input": "Çekilecek tutar", "bank_transfers.withdraw.max-limit-reached": "<PERSON><PERSON>, maksimum havale limitini aşıyor", "bank_transfers.withdrawal.verify-id": "Tutarı azalt", "banner.above_maximum_limit.maximum_input_limit_exceeded": "<PERSON><PERSON><PERSON>um giriş limiti aşıldı", "banner.above_maximum_limit.maximum_limit_per_deposit": "<PERSON><PERSON>, para yatırma başına maksimum limittir", "banner.above_maximum_limit.subtitle": "<PERSON><PERSON><PERSON>um giriş limiti aşıldı", "banner.above_maximum_limit.title": "Tu<PERSON><PERSON> şu de<PERSON> düşür: {amount} veya daha az", "banner.above_maximum_limit.title.default": "Tutarı düşür", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Minimum giriş limiti aşıldı", "banner.below_minimum_limit.minimum_limit_for_token": "Bu, bu token için minimum limittir", "banner.below_minimum_limit.title": "Tu<PERSON><PERSON> şu de<PERSON> yükselt: {amount} veya daha fazla", "banner.below_minimum_limit.title.default": "Tutarı artır", "breaard.in_porgress.info_popup.cta": "Kazanmak i<PERSON> harca {earn}", "breaard.in_porgress.info_popup.footnote": "Zeal ve Gnosis Pay kartını kullanarak bu ödül kampanyasının hüküm ve koşullarını kabul etmiş olursun.", "breaward.in_porgress.info_popup.bullet_point_1": " harca, {remaining} önümüzdeki  içinde {time} ve bu öd<PERSON>l<PERSON> talep et.", "breaward.in_porgress.info_popup.bullet_point_2": "Harcama tutarına yalnızca geçerli Gnosis Pay alışverişleri dahil edilir.", "breaward.in_porgress.info_popup.bullet_point_3": "Ödülünü talep ettikten sonra Zeal hesabına gönderilecektir.", "breaward.in_porgress.info_popup.header": " kazan, {earn}  harca<PERSON>ak {remaining}", "breward.celebration.for_spending": "<PERSON><PERSON><PERSON><PERSON><PERSON> harcama yaptı<PERSON>ı<PERSON> i<PERSON>", "breward.dc25-eligible-celebration.for_spending": "<PERSON>lk  kişiden birisin {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "<PERSON><PERSON>ma yapan ilk  kişi {limit} <PERSON><PERSON><PERSON><PERSON>", "breward.expired_banner.earn_by_spending": " kazan {earn}  harcayarak {amount}", "breward.expired_banner.reward_expired": "{earn}  ö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n süresi doldu", "breward.in_progress_banner.cta.title": " kazanmak i<PERSON> harca {earn}", "breward.ready_to_claim.error.try_again": "<PERSON><PERSON><PERSON> dene", "breward.ready_to_claim.error_title": "<PERSON><PERSON><PERSON><PERSON> edile<PERSON>i", "breward.ready_to_claim.in_progress": "<PERSON><PERSON><PERSON><PERSON> talep edili<PERSON>r", "breward.ready_to_claim.youve_earned": " kazandın {earn}!", "breward_already_claimed.title": "Ödül zaten alınmış. Ödül tokenini almadıysan lütfen destekle iletişime geç.", "breward_cannotbe_claimed.title": "<PERSON><PERSON><PERSON><PERSON>u anda alı<PERSON>. Lütfen daha sonra tekrar dene.", "bridge.best_return": "En iyi getiri rotası", "bridge.best_serivce_time": "En iyi hizmet süresi rotası", "bridge.check_status.complete": "Tamamlandı", "bridge.check_status.progress_text": "Köprüleniyor: {from} → {to}", "bridge.remove_topup": "Takviyeyi Kaldır", "bridge.request_status.completed": "Tamamlandı", "bridge.request_status.pending": "Beklemede", "bridge.widget.completed": "Tamamlandı", "bridge.widget.currencies": "{from} → {to}", "bridge_rote.widget.title": "Köprü", "browse.discover_more_apps": "Daha fazla Uygulama keşfet", "browse.google_search_term": "\"{searchTerm}\" ara", "brward.celebration.you_earned": "Kazandın", "brward.expired_banner.subtitle": "Bir dahaki sefere bol <PERSON>ans", "brward.in_progress_banner.subtitle": "<PERSON>a ermesine kalan süre: {expiredInFormatted}", "buy": "Satın Al", "buy.enter_amount": "Tutar G<PERSON>", "buy.loading": "Yükleniyor...", "buy.no_routes_found": "Rota bulunamadı", "buy.not_enough_balance": "Bakiye yet<PERSON>iz", "buy.select-currency.title": "Token seç", "buy.select-to-currency.title": "Token satın al", "buy_form.title": "Token satın al", "cancelled-card.create-card-button.primary": "Yeni sanal kart al", "cancelled-card.switch-card-button.primary": "<PERSON><PERSON>", "cancelled-card.switch-card-button.short-text": "Başka bir aktif kartın var", "card": "Kart", "card-add-cash.confirm-stage.banner.no-routes-found": "Rota bulunamadı, farklı bir token veya tutar dene", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Ücretleri ödemek için {amount} daha fazla {symbol} gerekiyor", "card-add-cash.confirm-stage.banner.value-loss": "<PERSON><PERSON><PERSON>n {loss} olacak", "card-add-cash.confirm-stage.banner.value-loss.revert": "<PERSON><PERSON>", "card-add-cash.edit-stage.cta.cancel": "İptal", "card-add-cash.edit-stage.cta.continue": "<PERSON><PERSON> et", "card-add-cash.edit-stage.cta.enter-amount": "Tutar gir", "card-add-cash.edit-stage.cta.reduce-to-max": "<PERSON><PERSON><PERSON> a<PERSON>", "card-add-cash.edit-staget.banner.no-routes-found": "Rota bulunamadı, farklı bir token veya tutar dene", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "İşlem talebini donanım cüzdanına gönderdik. Lütfen oradan devam et.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Donanım cüzdanında imzala", "card-balance": "Bakiye: {balance}", "card-cashback.status.title": "<PERSON><PERSON><PERSON>", "card-copy-safe-address.copy_address": "<PERSON><PERSON><PERSON>", "card-copy-safe-address.copy_address.done": "Kopyalandı", "card-copy-safe-address.warning.description": "Bu adres yalnızca {cardAsset} alabilir (Gnosis Chain üzerinde). Bu adrese başka ağlardan varlık gönderme. Varlıkların kaybolur.", "card-copy-safe-address.warning.header": "Yalnızca {cardAsset} g<PERSON><PERSON> (Gnosis Chain üzerinde)", "card-marketing-card.center.subtitle": "<PERSON><PERSON>", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "Faiz", "card-marketing-card.right.subtitle": "<PERSON><PERSON><PERSON>", "card-marketing-card.title": "Avrupa'nın yüksek faizli VISA kartı", "card-marketing-tile.get-started": "Başla", "card-select-from-token-title": "Kaynak token seç", "card-top-up.banner.subtitle.completed": "Tamamlandı", "card-top-up.banner.subtitle.failed": "Başarısız", "card-top-up.banner.subtitle.pending": "{timerString} <PERSON><PERSON><PERSON><PERSON>", "card-top-up.banner.title": "Yatırılıyor {amount}", "card-topup.select-token.emptyState": "Token bulunamadı", "card.activate.card_number_not_valid": "<PERSON>rt numarası geçerli değil. Kontrol edip tekrar dene.", "card.activate.invalid_card_number": "Geçersiz kart numarası.", "card.activation.activate_physical_card": "Fiziksel kartı aktive et", "card.add-cash.amount-to-withdraw": "<PERSON><PERSON><PERSON>me tutarı", "card.add-from-earn-form.title": "<PERSON>rta nakit ekle", "card.add-from-earn-form.withdraw-to-card": "<PERSON><PERSON> et", "card.add-from-earn.amount-to-withdraw": "<PERSON><PERSON> çekilecek tutar", "card.add-from-earn.enter-amount": "Tutar G<PERSON>", "card.add-from-earn.loading": "Yükleniyor", "card.add-from-earn.max-label": "Bakiye: {amount}", "card.add-from-earn.no-routes-found": "Rota bulunamadı", "card.add-from-earn.not-enough-balance": "<PERSON><PERSON><PERSON>", "card.add-owner.queued": "Sahip ekleme sıraya alı<PERSON>ı", "card.add-to-wallet-flow.subtitle": "Cüzdanından ödeme yap.", "card.add-to-wallet.copy-card-number": "Aşağıdaki Kart numarasını kopyala", "card.add-to-wallet.title": " {platformName} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "İPTAL EDİLDİ", "card.card-owner-not-found.disconnect-btn": "Kartı Zeal'dan a<PERSON>ır", "card.card-owner-not-found.subtitle": "Gnosis Pay kartını Zeal'da kullanmaya devam etmek için lütfen kart sahibini güncelleyerek yeniden bağla", "card.card-owner-not-found.title": "Kartı Yeniden Bağla", "card.card-owner-not-found.update-owner-btn": "<PERSON><PERSON>", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} içinde {date}", "card.cashback.widgetNoCashback.subtitle": "Kazanmaya başlamak için para yatır", "card.cashback.widgetNoCashback.title": "<PERSON><PERSON> orana kadar {defaultPercentage} Nakit <PERSON> kazan", "card.cashback.widgetcashbackValue.rewards": "{amount} beklemede", "card.cashback.widgetcashbackValue.title": "{percentage} Na<PERSON><PERSON>", "card.choose-wallet.connect_card": "Kartı bağla", "card.choose-wallet.create-new": "Sa<PERSON> olarak yeni bir cüzdan ekle", "card.choose-wallet.import-another-wallet": "Başka bir cüzdan içe aktar", "card.choose-wallet.import-current-owner": "Mevcut kart sahibini içe aktar", "card.choose-wallet.import-current-owner.sub-text": "Gnosis Pay kartına sahip olan özel anahtarları veya anahtar ifadeyi içe aktar", "card.choose-wallet.title": "Kartını yönetmek için cüzdan seç", "card.connectWalletToCardGuide": "Cüzdan adresini kopyala", "card.connectWalletToCardGuide.addGnosisPayOwner": "Gnosis <PERSON>", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. <PERSON><PERSON><PERSON>nınla Gnosispay.com'u aç{br}2. <PERSON><PERSON><PERSON><PERSON>”a tıkla{br}3. <PERSON><PERSON><PERSON><PERSON> detayları”na tıkla{br}4. <PERSON><PERSON><PERSON><PERSON>”nin yanı<PERSON> “<PERSON><PERSON><PERSON>”ye tıkla ve{br}5. <PERSON><PERSON><PERSON>”ye tıkla{br}6. <PERSON><PERSON> ad<PERSON><PERSON> yapıştır ve kaydet", "card.connectWalletToCardGuide.header": "Gnosis Pay Kartı {account} Bağlantı Kılavuzu", "card.connect_card.start": "Gnosis Pay Kartı Bağla", "card.copiedAddress": "Kopyalandı {formattedAddress}", "card.disconnect-account.title": "Hesap bağlantısını kes", "card.hw-wallet-support-drop.add-owner-btn": "Karta yeni sahip ekle", "card.hw-wallet-support-drop.disconnect-btn": "Kartı Zeal'dan a<PERSON>ır", "card.hw-wallet-support-drop.subtitle": "Gnosis Pay Kartını Zeal'da kullanmaya devam etmek için lütfen Kartına Donanım Cüzdanı olmayan başka bir sahip ekle.", "card.hw-wallet-support-drop.title": "Zeal artık Kart için Donanım Cüzdanlarını desteklemiyor", "card.kyc.continue": "<PERSON><PERSON><PERSON><PERSON> devam et", "card.list_item.title": "Kart", "card.onboarded.transactions.empty.description": "<PERSON><PERSON><PERSON> burada gö<PERSON>", "card.onboarded.transactions.empty.title": "E<PERSON>kin<PERSON>", "card.order.continue": "Kart sipari<PERSON> devam et", "card.order.free_virtual_card": "Ücretsiz Sanal Kart Al", "card.order.start": "Ücretsiz kart sipariş et", "card.owner-not-imported.cancel": "İptal", "card.owner-not-imported.import": "Cüzdan aktar", "card.owner-not-imported.subtitle": "Bu işlemi onaylamak için Gnosis Pay hesabının sahibi olan cüzdanı Zeal'a bağla. Not: Bu, normal Gnosis Pay cüzdanı oturum açma işleminden farklıdır.", "card.owner-not-imported.title": "Gnosis Pay hesap sahibini ekle", "card.page.order_free_physical_card": "Ücretsiz Fiziki Kart İste", "card.pin.change_pin_at_atm": "PIN, belirli ATM'lerde değiştirilebilir", "card.pin.timeout": "E<PERSON>n {seconds} saniye içinde kapanacak", "card.quick-actions.add-assets": "Para ekle", "card.quick-actions.add-cash": "Para ekle", "card.quick-actions.details": "Detaylar", "card.quick-actions.freeze": "<PERSON><PERSON><PERSON>", "card.quick-actions.freezing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.unfreeze": "Çöz", "card.quick-actions.unfreezing": "Çözülüyor", "card.quick-actions.withdraw": "Para çek", "card.read-only-detected.create-new": "Sa<PERSON> olarak yeni bir cüzdan ekle", "card.read-only-detected.import-current-owner": "<PERSON><PERSON><PERSON> için an<PERSON>ı içe aktar: {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Cüzdanın özel anahtarlarını veya anahtar ifadesini içe aktar {address}", "card.read-only-detected.title": "Salt okunur cüzdanda kart algılandı. Kartı yönetmek için cüzdan seç", "card.remove-owner.queued": "Sahip kaldırma sıraya alı<PERSON>ı", "card.settings.disconnect-from-zeal": "<PERSON><PERSON>'dan a<PERSON>ır", "card.settings.edit-owners": "<PERSON><PERSON>", "card.settings.getCard": "Başka bir Kart al", "card.settings.getCard.subtitle": "Sanal veya fiziksel kartlar", "card.settings.notRecharging": "Otomatik yükleme kapalı", "card.settings.notifications.subtitle": "Ödeme bild<PERSON>i al", "card.settings.notifications.title": "<PERSON><PERSON>", "card.settings.page.title": "<PERSON><PERSON>", "card.settings.select-card.cancelled-cards": "İptal edilen kartlar", "card.settings.setAutoRecharge": "Otomatik yüklemeyi ayarla", "card.settings.show-card-address": "<PERSON><PERSON> ad<PERSON>", "card.settings.spend-limit": "<PERSON><PERSON><PERSON> limitini a<PERSON>la", "card.settings.spend-limit-title": "Mevcut günlük limit: {limit}", "card.settings.switch-active-card": "Aktif <PERSON>ştir", "card.settings.switch-active-card-description": "Aktif kart: {card}", "card.settings.switch-card.card-item.cancelled": "İptal edildi", "card.settings.switch-card.card-item.frozen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.title": "Gnosis Pay Kartı", "card.settings.switch-card.card-item.title.physical": "<PERSON>ziks<PERSON>", "card.settings.switch-card.card-item.title.virtual": "<PERSON><PERSON>", "card.settings.switch-card.title": "<PERSON><PERSON> se<PERSON>", "card.settings.targetBalance": "<PERSON><PERSON><PERSON> b<PERSON>: {threshold}", "card.settings.view-pin": "PIN'i görüntüle", "card.settings.view-pin-description": "P<PERSON>'ini her zaman koru", "card.title": "Kart", "card.transactions.header": "<PERSON><PERSON>", "card.transactions.see_all": "<PERSON><PERSON><PERSON> işlemleri gör", "card.virtual": "SANAL", "cardCashback.onboarding.bullets.cashback_sent_weekly": "<PERSON><PERSON><PERSON>, kazanıldığı haftadan sonraki hafta başında kartına gönderilir.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "Ne kadar çok para yatırırsan her alışverişte o kadar çok kazanırsın.", "cardCashback.onboarding.title": "<PERSON><PERSON> orana kadar {percentage} Nakit <PERSON> kazan", "cardCashbackWithdraw.amount": "Çekilecek tutar", "cardCashbackWithdraw.header": "Çek: {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "<PERSON>rt engellendi ve etkinleştirilemedi", "cardWidget.cashback": "<PERSON><PERSON><PERSON>", "cardWidget.cashbackUpToDefaultPercentage": "En fazla {percentage}", "cardWidget.startEarning": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "cardWithdraw.amount": "Çekilecek tutar", "cardWithdraw.header": "Karttan para çek", "cardWithdraw.selectWithdrawWallet.title": "Para çekilecek cüzdanı{br} seç", "cardWithdraw.success.cta": "Ka<PERSON><PERSON>", "cardWithdraw.success.subtitle": "Güvenlik nedeniyle Gnosis Pay kartından yapılan tüm para çekme işlemlerinin işlenmesi 3 dakika sürer", "cardWithdraw.success.title": "Bu değişikliğin uygulanması 3 dakika sürecek", "card_top_up_trx.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card_top_up_trx.to": "Alıcı", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "<PERSON> kullanma tarihi", "cards.card_number": "<PERSON><PERSON> numa<PERSON>ı", "cards.choose-wallet.no-active-accounts": "<PERSON>kt<PERSON> yok", "cards.copied_card_number": "Kart numarası kopyalandı", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Günlük limiti aşıyor", "cards.transactions.decline_reason.incorrect_pin": "Yanlış PIN", "cards.transactions.decline_reason.incorrect_security_code": "Yanlış güvenlik kodu", "cards.transactions.decline_reason.invalid_amount": "Geçersiz tutar", "cards.transactions.decline_reason.low_balance": "Düşük bakiye", "cards.transactions.decline_reason.other": "Reddedildi", "cards.transactions.decline_reason.pin_tries_exceeded": "PIN deneme hakkı aşıldı", "cards.transactions.status.refund": "<PERSON><PERSON>", "cards.transactions.status.reversal": "<PERSON><PERSON> işlem", "cashback-deposit.trx.title": "<PERSON><PERSON><PERSON> i<PERSON> yatı<PERSON>", "cashback-estimate.text": "Bu bir tahmindir ve garantili bir ödeme DEĞİLDİR. Herkese açık tüm nakit iadesi kuralları uygulanır, ancak Gnosis Pay kendi takdirine bağlı olarak işlemleri hariç tutabilir. En fazla harcama tutarı olan {amount} haftalık tutar, bu işlem için tahmin daha yüksek bir toplam tutar gösterse bile Nakit İadesi için geçerlidir.", "cashback-estimate.text.fallback": "<PERSON>u tahmini bir tuta<PERSON>r, garanti edilen bir ödeme <PERSON>ğil<PERSON>. Herkese açık tüm nakit iadesi kuralları uygulanır, ancak Gnosis Pay takdir yetkisine dayanarak işlemleri hariç tutabilir.", "cashback-estimate.title": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "cashback-onbarding-tersm.subtitle": "Kart iş<PERSON> verilerin, Nakit İadesi ödüllerini dağıtmaktan sorumlu olan Karpatkey ile paylaşılacak. Kabul et'e tıklayarak Gnosis DAO Nakit İadesi <terms>Hüküm ve Koşulları'nı</terms>", "cashback-onbarding-tersm.title": "Kullanım Koşulları ve Gizlilik", "cashback-tx-activity.retry": "<PERSON><PERSON><PERSON> dene", "cashback-unconfirmed-payments-info.subtitle": "<PERSON><PERSON><PERSON><PERSON>, sat<PERSON><PERSON><PERSON>yla mutabakat sağlandığında Nakit İadesi için uygun olur. O zamana kadar onaylanmamış ödemeler olarak görünürler. Mutabakat sağlanmamış ödemeler nakit iadesi için uygun değildir.", "cashback-unconfirmed-payments-info.title": "Onaylanmamış kart ödemeleri", "cashback.activity.cashback": "<PERSON><PERSON><PERSON>", "cashback.activity.deposit": "Para Yatırma", "cashback.activity.title": "<PERSON><PERSON><PERSON>", "cashback.activity.withdrawal": "Para Çekme", "cashback.deposit": "Para Yatır", "cashback.deposit.amount.label": "Yatırılacak tutar", "cashback.deposit.change": "{from} -> {to}", "cashback.deposit.confirmation.subtitle": "Nakit iadesi oranları haftada bir güncellenir. Gelecek haftanın Nakit İadesi oranını artırmak için şimdi para yatır.", "cashback.deposit.confirmation.title": "Kazan<PERSON>ya ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oran {percentage} , ba<PERSON><PERSON><PERSON><PERSON> tarihi {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Token'larını şuna dönüştür: {currency} , şu ağda: {network} Ağı", "cashback.deposit.get.tokens.title": "Al: {currency} token'ı", "cashback.deposit.header": "Yatır: {currency}", "cashback.deposit.max_label": "Maks: {amount}", "cashback.deposit.select-wallet.title": "Para yatırmak için cüzdan seç", "cashback.deposit.yourcashback": "<PERSON><PERSON><PERSON>", "cashback.header": "<PERSON><PERSON><PERSON>", "cashback.selectWithdrawWallet.title": "<PERSON><PERSON><PERSON>{br} cüzdan seç", "cashback.transaction-details.network-label": "Ağ", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "<PERSON><PERSON><PERSON><PERSON>", "cashback.transaction-details.top-row.label-rewards": "<PERSON><PERSON><PERSON> d<PERSON>", "cashback.transaction-details.top-row.label-withdrawal": "Alıcı", "cashback.transaction-details.transaction": "İşlem Kimliği", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Nakit iadesi işlemleri", "cashback.withdraw": "Para Çek", "cashback.withdraw.confirmation.cashback_reduction": "<PERSON>u haftanın nakit <PERSON> (zaten kazandıkların dahil), şu a<PERSON><PERSON><PERSON> düş<PERSON>: {before} -> {after}", "cashback.withdraw.queued": "<PERSON><PERSON><PERSON> s<PERSON>", "cashback.withdrawal.change": "{from} -> {to}", "cashback.withdrawal.confirmation.subtitle": "<PERSON><PERSON> tutarda çekim talebi ba<PERSON>: {amount} , 3 dakikalık bir gecikmeyle. Bu, nakit iadeni şu orana düşürecek: {after}.", "cashback.withdrawal.confirmation.title": "GNO çekersen nakit iaden d<PERSON>", "cashback.withdrawal.delayTransaction.title": "GNO çekme i<PERSON> ba<PERSON>{br} (3 dakika gecik<PERSON>i)", "cashback.withdrawal.withdraw": "Para çek", "cashback.withdrawal.yourcashback": "<PERSON><PERSON><PERSON>", "celebration.aave": "<PERSON><PERSON> ile kazanı<PERSON>ı", "celebration.cashback.subtitle": "Ödendi: {code}", "celebration.cashback.subtitleGNO": "{amount} son <PERSON><PERSON><PERSON><PERSON>", "celebration.chf": "Frankencoin ile kazanıldı", "celebration.lido": "Lido ile kazanıldı", "celebration.sky": "Sky ile kazanıldı", "celebration.title": "Toplam Nakit İ<PERSON>i", "celebration.well_done.title": "<PERSON><PERSON>!", "change-withdrawal-account.add-new-account": "Başka bir banka hesabı ekle", "change-withdrawal-account.item.shortText": "{currency} Hesabı", "check-confirmation.approve.footer.for": "<PERSON><PERSON><PERSON>", "checkConfirmation.title": "İşlem sonucu", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin ve Ether", "collateral.ethereum": "Ethereum", "collateral.other": "<PERSON><PERSON><PERSON>", "collateral.rwa": "Gerçek Dünya Varlıkları", "collateral.stablecoins": "<PERSON><PERSON><PERSON> (USD'ye sabitli)", "collateral.us-t-bills": "ABD Hazine Bonoları", "confirm-bank-transfer-recipient.bullet-1": "Dijital EUR için ü<PERSON>t yok", "confirm-bank-transfer-recipient.bullet-2": "Para yatırma hede<PERSON>: {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Gnosis Pay hesap bil<PERSON><PERSON><PERSON>, yetkili ve denetime tabi bir E-Para Kuruluşu olan Monerium ile paylaş. <link>Daha fazla bilgi</link>", "confirm-bank-transfer-recipient.bullet-4": "Monerium <link>şartlarını onayla</link>", "confirm-bank-transfer-recipient.title": "Koşulları kabul et", "confirm-change-withdrawal-account.cancel": "İptal", "confirm-change-withdrawal-account.confirm": "<PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.saving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.subtitle": "Zeal'dan ya<PERSON>n tüm çekimler bu banka hesabına yatırılacak.", "confirm-change-withdrawal-account.title": "Alıcı bankayı değiştir", "confirm-ramove-withdrawal-account.title": "Banka hesabını kaldır", "confirm-remove-withdrawal-account.subtitle": "Bu banka hesabı bilgileri Zeal'dan kaldırılacak. İstediğin zaman tekrar ekleyebilirsin.", "confirmTransaction.finalNetworkFee": "<PERSON><PERSON>", "confirmTransaction.importKeys": "Anahtarları içe aktar", "confirmTransaction.networkFee": "<PERSON><PERSON>", "confirmation.title": " gönderiliyor {amount}  adlı alıcıya {recipient}", "conflicting-monerium-account.add-owner": "Gnosis Pay <PERSON> o<PERSON>ak ekle", "conflicting-monerium-account.create-wallet": "Yeni bir akıllı cüzdan oluştur", "conflicting-monerium-account.disconnect-card": "Kartın Zeal ile bağlantısını kes ve yeni sahiple yeniden bağla", "conflicting-monerium-account.header": "{wallet}  başka bir Monerium hesabına bağlı", "conflicting-monerium-account.subtitle": "Gnosis Pay sahip cüzdanını değiştir", "connection.diconnected.got_it": "<PERSON><PERSON><PERSON><PERSON>!", "connection.diconnected.page1.subtitle": "<PERSON><PERSON>, Metamask'la aynı şekilde çalışır.", "connection.diconnected.page1.title": "Zeal ile nasıl bağlanılır?", "connection.diconnected.page2.subtitle": "Birçok seçenek göreceksin. Zeal yoksa...", "connection.diconnected.page2.title": "Cüzdanı Bağla'ya tıkla", "connection.diconnected.page3.subtitle": "Zeal bağlantı isteği alacaksın. Dene!", "connection.diconnected.page3.title": "Metamask'i seç", "connectionSafetyCheck.tag.caution": "<PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.danger": "<PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.passed": "Geçti", "connectionSafetyConfirmation.subtitle": "<PERSON><PERSON> et<PERSON>k istediğine emin misin?", "connectionSafetyConfirmation.title": "Bu site tehlikeli görünüyor", "connection_state.connect.cancel": "İptal", "connection_state.connect.changeToMetamask": "MetaMask'e geç 🦊", "connection_state.connect.changeToMetamask.label": "MetaMask'e geç", "connection_state.connect.connect_button": "Bağlan", "connection_state.connect.expanded.connected": "Bağlandı", "connection_state.connect.expanded.title": "Bağlan", "connection_state.connect.safetyChecksLoading": "Site güvenliği kontrol ediliyor", "connection_state.connect.safetyChecksLoadingError": "Güvenlik kontrolleri tamamlanamadı", "connection_state.connected.expanded.disconnectButton": "Zeal bağlantısını kes", "connection_state.connected.expanded.title": "Bağlı", "copied-diagnostics": "Tanı bilgileri kopyalandı", "copy-diagnostics": "Tanı bilgilerini kopyala", "counterparty.component.add_recipient_primary_text": "Banka alıcısı ekle", "counterparty.country": "<PERSON><PERSON><PERSON>", "counterparty.countryTitle": "Alıcı<PERSON><PERSON><PERSON>", "counterparty.currency": "Para birimi", "counterparty.delete.success.title": "Kaldırıldı", "counterparty.edit.success.title": "Değişiklikler kaydedildi", "counterparty.errors.country_required": "<PERSON><PERSON><PERSON> gere<PERSON>", "counterparty.errors.first_name.invalid": "Ad daha uzun olmalı", "counterparty.errors.last_name.invalid": "Soyadı daha uzun olmalı", "counterparty.first_name": "Ad", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Bankaya gönder", "countrySelector.noCountryFound": "<PERSON>lk<PERSON> bulunamadı", "countrySelector.title": "<PERSON><PERSON><PERSON>", "create-passkey.cta": "Geçiş anahtarı oluştur", "create-passkey.extension.cta": "<PERSON><PERSON>", "create-passkey.footnote": "Sağlayan", "create-passkey.mobile.cta": "Güvenlik Kurulumu Başlat", "create-passkey.steps.enable-recovery": "Bulut kurtarmayı ayarla", "create-passkey.steps.setup-biometrics": "Biyometrik güvenliği etkinleştir", "create-passkey.subtitle": "Geçiş anahtarları şifrelerden daha güvenlidir ve kolay kurtarma için bulut depolamada şifrelenir.", "create-passkey.title": "Hesabı güvenceye al", "create-smart-wallet": "Smart Wallet oluştur", "create-userop.progress.text": "Oluşturuluyor", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Gnosis Pay'de devam et", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Gnosispay.com'a git", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "<PERSON>rt siparişini zaten başlattın. Tamamlamak için Gnosis Pay sitesine geri dön.", "create_recharge_preferences.card": "Kart", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "Kazan {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "Nakit tut", "create_recharge_preferences.link_accounts_title": "Hesapları bağla", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "<PERSON><PERSON><PERSON><PERSON>, her ödemeden sonra otomatik olarak YÜKLENMEZ.", "create_recharge_preferences.not_configured_title": "Kazan ve Harca", "create_recharge_preferences.recharge_from_earn_accounts_description": "<PERSON><PERSON><PERSON><PERSON>, her ödemeden sonra Kazan hesabından otomatik olarak yüklenir.", "create_recharge_preferences.subtitle": "yıllık", "creating-account.loading": "<PERSON><PERSON><PERSON>", "creating-gnosis-pay-account": "<PERSON><PERSON><PERSON>", "currencies.bridge.select_routes.emptyState": "<PERSON>u köprü için rota bula<PERSON>ık", "currency.add_currency.add_token": "<PERSON><PERSON> ekle", "currency.add_currency.not_a_valid_address": "Bu geçerli bir token adresi <PERSON>", "currency.add_currency.token_decimals_feild": "Token ondalık sayısı", "currency.add_currency.token_feild": "<PERSON><PERSON> adresi", "currency.add_currency.token_symbol_feild": "Token sembolü", "currency.add_currency.update_token": "Token'ı güncelle", "currency.add_custom.remove_token.cta": "Kaldır", "currency.add_custom.remove_token.header": "Token'ı kaldır", "currency.add_custom.remove_token.subtitle": "Cüzdanın bu token'daki bakiyeyi tutmaya devam edecek ancak token, Zeal portföy bakiyelerinden gizlenecek.", "currency.add_custom.token_removed": "Token kaldırıldı", "currency.add_custom.token_updated": "<PERSON><PERSON> g<PERSON>", "currency.balance_label": "Bakiye: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "Banka havalen ba<PERSON><PERSON><PERSON><PERSON> havale etti: {fiat} → {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "<PERSON><PERSON> kadar aldın: {crypto}", "currency.bankTransfer.deposit_status.success": "Cüzdanına ulaştı", "currency.bankTransfer.deposit_status.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.check_bank_account": "Banka hesabını kontrol et", "currency.bankTransfer.off_ramp.complete": "Tamamlandı", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "<PERSON><PERSON>", "currency.bankTransfer.off_ramp.transferring_to_currency": "<PERSON><PERSON> ediliyor: {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Fonlar şimdiye kadar banka hesabına ulaşmış olmalı.", "currency.bankTransfer.withdrawal_status.success": "<PERSON><PERSON>", "currency.bankTransfer.withdrawal_status.title": "Para Çekme", "currency.bank_transfer.create_unblock_user.email": "E-posta adresi", "currency.bank_transfer.create_unblock_user.email_invalid": "Geçersiz e-posta", "currency.bank_transfer.create_unblock_user.email_missing": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name": "Ad", "currency.bank_transfer.create_unblock_user.first_name_missing": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "<PERSON><PERSON><PERSON> ha<PERSON>, r<PERSON><PERSON>, b<PERSON><PERSON><PERSON>, - . , & ()'", "currency.bank_transfer.create_unblock_user.last_name": "Soyadı", "currency.bank_transfer.create_unblock_user.last_name_missing": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "<PERSON><PERSON><PERSON> ha<PERSON>, r<PERSON><PERSON>, b<PERSON><PERSON><PERSON>, - . , & ()'", "currency.bank_transfer.create_unblock_user.note": "Devam ederek bankacılık ortağımız Unblock'un <terms>Koşullarını</terms> ve <policy>Gizlilik Politikasını</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Adını banka hesabındakiyle bire bir aynı yaz", "currency.bank_transfer.create_unblock_user.title": "Banka hesabını bağla", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Bank<PERSON>ın <PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "<PERSON><PERSON><PERSON> edilen para birimi", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Banka kodu", "currency.bank_transfer.create_unblock_withdraw_account.success": "<PERSON><PERSON><PERSON> k<PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.title": "Banka hesabını bağla", "currency.bank_transfer.residence-form.address-required": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.address-unsupported-char": "<PERSON><PERSON><PERSON><PERSON><PERSON> harf, sayı, bo<PERSON>luk ve , ; {apostrophe} - \\\\ karakterlerine izin verilir.", "currency.bank_transfer.residence-form.city-required": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.city-unsupported-char": "<PERSON><PERSON><PERSON><PERSON><PERSON> harf, sayı, bo<PERSON><PERSON> ve . , - & ( ) {apostrophe} karakterlerine izin verilir.", "currency.bank_transfer.residence-form.postcode-invalid": "Geçersiz posta kodu", "currency.bank_transfer.residence-form.postcode-required": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.validation.invalid.account_number": "Geçersiz hesap numa<PERSON>ı", "currency.bank_transfer.validation.invalid.iban": "Geçersiz IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Geçersiz banka kodu", "currency.bridge.amount_label": "Köprülenecek tutar", "currency.bridge.best_returns.subtitle": "<PERSON><PERSON>, tüm ücretler dahil en yüksek miktarı verir.", "currency.bridge.best_returns_popup.title": "En iyi getiri", "currency.bridge.bridge_from": "<PERSON><PERSON><PERSON><PERSON>", "currency.bridge.bridge_gas_fee_loading_failed": "<PERSON>ğ ücreti yüklenirken sorun oluştu", "currency.bridge.bridge_low_slippage": "Slippage çok düşük. Artırmayı dene", "currency.bridge.bridge_provider": "Havale sağlayıcısı", "currency.bridge.bridge_provider_loading_failed": "Sağlayıcıları yüklerken sorun yaşadık", "currency.bridge.bridge_settings": "Köprü ayarları", "currency.bridge.bridge_status.subtitle": "Sağlayıcı: {name}", "currency.bridge.bridge_status.title": "Köprü", "currency.bridge.bridge_to": "Alıcı", "currency.bridge.fastest_route_popup.subtitle": "Bu sağlayıcı en hızlı işlem rotasını sunar.", "currency.bridge.fastest_route_popup.title": "En hızlı rota", "currency.bridge.from": "<PERSON><PERSON><PERSON>", "currency.bridge.success": "Tamamlandı", "currency.bridge.title": "Köprü", "currency.bridge.to": "<PERSON><PERSON><PERSON>", "currency.bridge.topup": "Takviye yap {symbol}", "currency.bridge.withdrawal_status.title": "<PERSON><PERSON><PERSON>", "currency.card.card_top_up_status.title": "<PERSON>rta nakit ekle", "currency.destination_amount": "<PERSON><PERSON><PERSON>", "currency.hide_currency.confirm.subtitle": "Bu token'ı portföyünden gizle. İstediğin zaman tekrar görünür ya<PERSON>in.", "currency.hide_currency.confirm.title": "Token'ı gizle", "currency.hide_currency.success.title": "<PERSON><PERSON> gizlendi", "currency.label": "Etiket (İsteğe bağlı)", "currency.last_name": "Soyadı", "currency.max_loading": "Maks:", "currency.swap.amount_to_swap": "Takas edilecek tutar", "currency.swap.best_return": "En iyi getiri rotası", "currency.swap.destination_amount": "<PERSON><PERSON><PERSON>", "currency.swap.header": "<PERSON><PERSON>", "currency.swap.max_label": "Bakiye: {amount}", "currency.swap.provider.header": "Takas sağlayıcı", "currency.swap.select_to_token": "Token seç", "currency.swap.swap_gas_fee_loading_failed": "<PERSON>ğ ücreti yüklenirken sorun oluştu", "currency.swap.swap_provider_loading_failed": "Sağlayıcılar yüklenirken sorun oluştu", "currency.swap.swap_settings": "<PERSON><PERSON>", "currency.swap.swap_slippage_too_low": "Slippage çok düşük. Artırmayı dene", "currency.swaps_io_native_token_swap.subtitle": "Swaps.IO kullanılıyor", "currency.swaps_io_native_token_swap.title": "<PERSON><PERSON><PERSON>", "currency.withdrawal.amount_from": "<PERSON><PERSON><PERSON><PERSON>", "currency.withdrawal.amount_to": "Alıcı", "currencySelector.title": "Para birimi seç", "dApp.wallet-does-not-support-chain.subtitle": "Cüzdanın desteklemiyor gibi görünüyor: {network}. Başka bir cüzdanla bağlan veya Zeal kullan.", "dApp.wallet-does-not-support-chain.title": "Desteklenmeyen Ağ", "dapp.connection.manage.confirm.disconnect.all.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kes", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Tüm bağlantıları kesmek istiyor musun?", "dapp.connection.manage.confirm.disconnect.all.title": "<PERSON><PERSON><PERSON> bağlantıları kes", "dapp.connection.manage.connection_list.main.button.title": "Bağlantıyı kes", "dapp.connection.manage.connection_list.no_connections": "Bağlı uygulaman yok", "dapp.connection.manage.connection_list.section.button.title": "<PERSON><PERSON><PERSON> bağlantıları kes", "dapp.connection.manage.connection_list.section.title": "Aktif", "dapp.connection.manage.connection_list.title": "Bağlantılar", "dapp.connection.manage.disconnect.success.title": "Uygulama bağlantıları kesildi", "dapp.metamask_mode.title": "MetaMask Modu", "dc25-card-marketing-card.center.subtitle": "<PERSON><PERSON><PERSON>", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "Faiz", "dc25-card-marketing-card.right.subtitle": "100 kişi", "dc25-card-marketing-card.title": "50€ harcayan ilk 100 kişi kazanacak {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Bu eylemi 3 dakika boyunca yapamayacaksın. Güvenlik nedenleriyle, kart ayarı değişiklikleri veya para çekme işlemlerinin işlenmesi 3 dakika sürer.", "delayQueueBusyBanner.processing-yout-action.title": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>, <PERSON><PERSON><PERSON><PERSON> bekle", "delayQueueBusyWidget.cardFrozen": "<PERSON><PERSON> don<PERSON>", "delayQueueBusyWidget.processingAction": "İş<PERSON>in gerçekleştiriliyor", "delayQueueFailedBanner.action-incomplete.get-support": "Destek al", "delayQueueFailedBanner.action-incomplete.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, para çekme veya ayar güncelleme işleminde bir sorun oluştu. Lütfen Discord'dan destek ekibiyle iletişime geç.", "delayQueueFailedBanner.action-incomplete.title": "İşlem tamamlanmadı", "delayQueueFailedWidget.actionIncomplete.title": "Kart işlemi ta<PERSON>", "delayQueueFailedWidget.cardFrozen.subtitle": "<PERSON><PERSON> don<PERSON>", "delayQueueFailedWidget.contactSupport": "Destekle iletişime geç", "delay_queue_busy.subtitle": "Gü<PERSON><PERSON> nedeniyle, kart ayarı değişikliklerinin veya para çekme işlemlerinin işlenmesi 3 dakika sürer. Bu süre boyunca kartın dondurulur.", "delay_queue_busy.title": "İşlemin işleniyor", "delay_queue_failed.contact_support": "Destek iste", "delay_queue_failed.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, para çekme veya ayar güncelleme işleminde bir sorun oluştu. Lütfen Discord'dan destek ekibiyle iletişime geç.", "delay_queue_failed.title": "<PERSON><PERSON><PERSON>", "deploy-earn-form-smart-wallet.in-progress.title": "<PERSON><PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "disconnect-card-popup.cancel": "İptal et", "disconnect-card-popup.disconnect": "Bağlantı kes", "disconnect-card-popup.subtitle": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, Kartını Zeal uygulamasından kaldırır. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Gnosis Pay uygulamasında kartına bağlı kalmaya devam eder. Kartını dilediğin zaman yeniden bağlayabilirsin.", "disconnect-card-popup.title": "Kart bağlantısını kes", "distance.long.days": "{count} gün", "distance.long.hours": "{count} saat", "distance.long.minutes": "{count} dakika", "distance.long.months": "{count} ay", "distance.long.seconds": "{count} saniye", "distance.long.years": "{count} yıl", "distance.short.days": "{count} g", "distance.short.hours": "{count} sa", "distance.short.minutes": "{count} dk", "distance.short.months": "{count} a", "distance.short.seconds": "{count} sn", "distance.short.years": "{count} y", "duration.short.days": "{count}g", "duration.short.hours": "{count}sa", "duration.short.minutes": "{count}dk", "duration.short.seconds": "{count}sn", "earn-deposit-view.deposit": "Yatırılan", "earn-deposit-view.into": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-deposit-view.to": "Alıcı", "earn-deposit.swap.transfer-provider": "Havale sağlayıcısı", "earn-taker-investment-details.accrued-realtime": "Gerçek zamanlı birikir", "earn-taker-investment-details.asset-class": "Varlık sınıfı", "earn-taker-investment-details.asset-coverage-ratio": "Varlık karşılama oranı", "earn-taker-investment-details.asset-reserve": "Varlık rezervi", "earn-taker-investment-details.base_currency.label": "Temel para birimi", "earn-taker-investment-details.chf.description": "zCHF'yi güvenilir bir dijital para piyasası olan Frankencoin'e yatırarak CHF üzerinden faiz kazan. <PERSON><PERSON>z, Frankencoin'de<PERSON> d<PERSON>, aş<PERSON>rı teminatlandırılmış kredilerden elde edilir ve anında ödenir. Paranız sadece senin kontrol ettiğin güvenli bir alt hesapta güvende kalır.", "earn-taker-investment-details.chf.description.with_address_link": "zCHF'yi güvenilir bir dijital para piyasası olan Frankencoin'e yatırarak CHF üzerinden faiz kazan. <PERSON><PERSON>z, Frankencoin'de<PERSON> d<PERSON>, aş<PERSON><PERSON>ı teminatlandırılmış kredilerden elde edilir ve anında ödenir. Paran güvenli bir alt hesapta güvendedir <link>(0x'i kopyala)</link> ve bu hesabı yalnızca sen kontrol edersin.", "earn-taker-investment-details.chf.label": "Dijital İsviçre Frangı", "earn-taker-investment-details.collateral-composition": "<PERSON><PERSON><PERSON> bi<PERSON>", "earn-taker-investment-details.depositor-obligations": "Mevduat yükümlülükleri", "earn-taker-investment-details.eure.description": "Güvenilir bir dijital para piyasası olan Aave'ye EURe yatırarak euro'ların üzerinden faiz kazan. EURe, Monerium tarafından çıkarılan ve korumalı hesaplarda 1:1 oranında desteklenen, ta<PERSON><PERSON> düzenlenmiş bir euro stabil koinidir. <PERSON><PERSON>z, Aave'de<PERSON> dü<PERSON>, aşırı teminatlandırılmış kredilerden elde edilir ve gerçek zamanlı olarak ödenir. Paranız, yalnızca senin kontrol ettiğin güvenli bir alt hesapta kalır.", "earn-taker-investment-details.eure.description.with_address_link": "Güvenilir bir dijital para piyasası olan Aave'ye EURe yatırarak euro'ların üzerinden faiz kazan. EURe, Monerium tarafından çıkarılan ve korumalı hesaplarda 1:1 oranında desteklenen, ta<PERSON><PERSON> düzenlenmiş bir euro stabil koinidir. <PERSON><PERSON>z, Aave'de<PERSON> dü<PERSON>li, aşırı teminatlandırılmış kredilerden elde edilir ve gerçek zamanlı olarak ödenir. Paran güvenli bir alt hesapta <link>(0x kopyala)</link> tutulur ve kontrolü sendedir.", "earn-taker-investment-details.eure.label": "Dijital Euro (EURe)", "earn-taker-investment-details.faq": "SSS", "earn-taker-investment-details.fixed-income": "Sabit gelir", "earn-taker-investment-details.issuer": "İhraççı", "earn-taker-investment-details.key-facts": "Önemli bilgiler", "earn-taker-investment-details.liquidity": "Likidite", "earn-taker-investment-details.operator": "Piyasa Operatörü", "earn-taker-investment-details.projected-yield": "<PERSON><PERSON><PERSON> yıllık getiri", "earn-taker-investment-details.see-other-faq": "<PERSON><PERSON><PERSON> tüm SSS'leri g<PERSON>", "earn-taker-investment-details.see-realtime": "Gerçek zamanlı verileri gör", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Son 12 aylık getiri", "earn-taker-investment-details.total-collateral": "Toplam Teminat", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "Toplam ZCHF arzı", "earn-taker-investment-details.total_deposits": "Toplam Aave mevduatları", "earn-taker-investment-details.usd.description": "Sky; kısa vadeli ABD Hazine Bonoları ve aşırı teminatlandırılmış borç verme işlemlerinden, ABD doları cinsinden istikrarlı getiriler sunan bir dijital para piyasasıdır. Kripto oynaklığı olmadan, 7/24 fona erişim ve şeffaf, zincir üstü destek sağlar.", "earn-taker-investment-details.usd.description.with_address_link": "Sky; kısa vadeli ABD Hazine Bonoları ve aşırı teminatlandırılmış borç verme işlemlerinden, ABD doları cinsinden istikrarlı getiriler sunan bir dijital para piyasasıdır. Kripto oynaklığı olmadan, 7/24 fona erişim ve şeffaf, zincir üstü destek sağlar. Yatırımlar bir alt hesapta <link>(0x kopyala)</link> tutulur ve kontrolü sendedir.", "earn-taker-investment-details.usd.ftx-difference": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> veya <PERSON>'dan farkı ne?", "earn-taker-investment-details.usd.high-returns": "<PERSON><PERSON><PERSON>, özellikle geleneksel bankalara kıyasla nasıl bu kadar yüksek olabilir?", "earn-taker-investment-details.usd.how-is-backed": "Sky USD nasıl destekleniyor ve Zeal iflas ederse parama ne olur?", "earn-taker-investment-details.usd.income-sources": "Gelir kaynakları 2024", "earn-taker-investment-details.usd.insurance": "Fonlarım herhangi bir kurum (FDIC veya benzeri) tarafından sigortalı mı veya garanti altında mı?", "earn-taker-investment-details.usd.label": "Dijital ABD Doları", "earn-taker-investment-details.usd.lose-principal": "Anaparamı kaybetmem gerçekçi bir risk mi ve hangi koşullar altında?", "earn-taker-investment-details.variable-rate": "Değişken oranlı borç verme", "earn-taker-investment-details.withdraw-anytime": "İstedi<PERSON>in z<PERSON>", "earn-taker-investment-details.yield": "Getiri", "earn-withdrawal-view.approve.for": "<PERSON><PERSON> hede<PERSON>", "earn-withdrawal-view.approve.into": "<PERSON><PERSON><PERSON>", "earn-withdrawal-view.swap.into": "<PERSON><PERSON><PERSON>", "earn-withdrawal-view.withdraw.to": "Alıcı", "earn.add_another_asset.title": "<PERSON><PERSON>lığı seç", "earn.add_asset": "Varlık ekle", "earn.asset_view.title": "Kazan", "earn.base-currency-popup.text": "Temel para birimi, me<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, getirilerinin ve işlemlerinin değerlenip kaydedildiği birimdir. Farklı bir para biriminde (örneğin USD hesabına EUR) para ya<PERSON><PERSON><PERSON><PERSON><PERSON>n, fonların anında güncel döviz kurları kullanılarak temel para birimine dönüştürülür. Dönüşümden sonra bakiyen temel para biriminde sabit kalır, ancak gelecekteki para çekme işlemleri tekrar kur dönüşümü gerektirebilir.", "earn.base-currency-popup.title": "Temel para birimi", "earn.card-recharge.disabled.list-item.title": "Otomatik yükleme kapalı", "earn.card-recharge.enabled.list-item.title": "Otomatik yükleme açık", "earn.choose_wallet_to_deposit.title": "<PERSON><PERSON><PERSON><PERSON>", "earn.config.currency.eth": "Ethereum Kazan", "earn.config.currency.on_chain_address_subtitle": "<PERSON><PERSON><PERSON>r ü<PERSON>ü <PERSON>", "earn.config.currency.us_dollars": "<PERSON><PERSON> ha<PERSON> a<PERSON>", "earn.configured_widget.current_apy.title": "Mevcut APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} Yıllık", "earn.confirm.currency.cta": "<PERSON><PERSON><PERSON><PERSON>", "earn.currency.eth": "Ethereum Kazan", "earn.deploy.status.title": "<PERSON><PERSON><PERSON><PERSON>", "earn.deploy.status.title_with_taker": "<PERSON><PERSON><PERSON><PERSON> {title} <PERSON><PERSON><PERSON>", "earn.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn.deposit.amount_to_deposit": "Yatırılacak tutar", "earn.deposit.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn.deposit.enter_amount": "Tutar G<PERSON>", "earn.deposit.no_routes_found": "Rota bulunamadı", "earn.deposit.not_enough_balance": "<PERSON><PERSON><PERSON>", "earn.deposit.select-currency.title": "Yatırılacak token'ı seç", "earn.deposit.select_account.title": "<PERSON><PERSON><PERSON>", "earn.desposit_form.title": "Kazan'a Yatır", "earn.earn_deposit.status.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.earn_deposit.trx.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> yatırma", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "İstediğin zaman para çek", "earn.earn_withdraw.status.title": "<PERSON><PERSON><PERSON>", "earn.earn_withdraw.trx.title.approval": "<PERSON><PERSON><PERSON>", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Şu varlığa çek: {asset}", "earn.earn_withdraw.trx.title.withdrawal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.recharge.cta": "Değişiklikleri kaydet", "earn.recharge.earn_not_configured.enable_some_account.error": "Hesabı etkinleştir", "earn.recharge.earn_not_configured.enter_amount.error": "Tutar gir", "earn.recharge.select_taker.header": "Kartı şuradan sırayla doldur", "earn.recharge_card_tag.on": "açık", "earn.recharge_card_tag.recharge": "<PERSON><PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharge_not_configured": "Otomatik Yükleme", "earn.recharge_card_tag.recharge_off": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>ı", "earn.recharge_card_tag.recharged": "Yüklendi", "earn.recharge_card_tag.recharging": "Yükleniyor", "earn.recharge_configured.disable.trx.title": "Otomatik Yüklemeyi Devre Dışı Bırak", "earn.recharge_configured.trx.disclaimer": "Kartını kullandı<PERSON>ı<PERSON>, ödemenle aynı tutarda Earn varlıkların kullanılarak bir alım yapmak için Cowswap açık artırması oluşturulur. Bu açık artırma süreci genellikle sana en iyi piyasa kurunu sunar ancak zincir üzerindeki kurun gerçek dünyadaki döviz kurlarından farklı olabileceğini unutma.", "earn.recharge_configured.trx.subtitle": "Her ödemeden sonra kart bakiyeni şu seviyede tutmak için Earn hesabından/hesaplarından otomatik olarak nakit eklenecektir: {value}", "earn.recharge_configured.trx.title": "Otomatik Yükleme He<PERSON>fi: {value}", "earn.recharge_configured.updated.trx.title": "Yükleme Ayarlarını Kaydet", "earn.risk-banner.subtitle": "<PERSON><PERSON>, kayı<PERSON>lara karşı yasal koruması olmayan özel bir üründür.", "earn.risk-banner.title": "<PERSON><PERSON><PERSON> anla", "earn.set_recharge.status.title": "Otomatik Yüklemeyi Ayarla", "earn.setup_reacharge.input.disable.label": "<PERSON><PERSON> dışı bırak", "earn.setup_reacharge.input.label": "<PERSON><PERSON><PERSON> kart b<PERSON>i", "earn.setup_reacharge_form.title": "Otomati<PERSON>,{br} kart bakiyeni sabit tutar", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Gnosis'te wstETH tut, <PERSON>do ile borç ver.", "earn.taker-bulletlist.point_1": "Yıllık {apyValue} kazan. <PERSON><PERSON><PERSON> de<PERSON>i<PERSON>dir.", "earn.taker-bulletlist.point_3": "Zeal ücret almaz.", "earn.taker-historical-returns": "Geçmiş <PERSON>ler", "earn.taker-historical-returns.chf": "CHF'nin <PERSON> ka<PERSON>şısındaki büyümesi", "earn.taker-investment-tile.apy.perYear": "yıllık", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Gnosis Chain'de Aave ile ka<PERSON>", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Gnosis Chain'de Frankencoin ile kazanma", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Gnosis Chain'de Lido ile ka<PERSON>ç", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Gnosis Chain'de Maker ile ka<PERSON>", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "<PERSON>th Kazanç", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH Kazanç", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "ABD Hazineleri (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Çok nadir de olsa, teorik olarak mümkün. Fonların katı risk yönetimi ve yüksek teminatlandırma ile korunur. Gerçekçi en kötü senaryo, birden fazla stabil koinin aynı anda sabitini kaybetmesi gibi daha önce hiç yaşanmamış, emsalsiz piyasa koşullarını içerir.", "earn.usd.can-I-lose-my-principal-popup.title": "Anaparamı kaybetmem gerçekçi bir risk mi ve hangi koşullar altında?", "earn.usd.ftx-difference-popup.text": "Sky temelden farklıdır. Büyük ölçüde merkezi saklamaya, şeffaf o<PERSON>yan varlık yönetimine ve riskli kaldıraçlı pozisyonlara dayanan FTX, <PERSON><PERSON><PERSON>, BlockFi veya Luna'nın <PERSON><PERSON>, Sky USD şeffaf, <PERSON><PERSON><PERSON>miş, merkeziyetsiz akıllı sözleşmeler kullanır ve tam zincir üstü şeffaflığı korur. Tamamen özel cüzdan kontrolünü elinde tutarsın, bu da merkezi başarısızlıklarla ilişkili karşı taraf risklerini önemli ölçüde azaltır.", "earn.usd.ftx-difference-popup.title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> veya <PERSON>'dan farkı ne?", "earn.usd.high-returns-popup.text": "Sky USD, getirilerini öncelikle eşler arası borç vermeyi ve likidite sağlamayı otomatikleştiren, geleneksel bankacılık masraflarını ve aracıları ortadan kaldıran merkeziyetsiz finans (DeFi) protokolleri aracılığıyla üretir. Bu verimlilikler, sağlam risk kontrolleriyle birleştiğinde, geleneksel bankalara kıyasla önemli ölçüde daha yüksek getirilere olanak tanır.", "earn.usd.high-returns-popup.title": "<PERSON><PERSON><PERSON>, özellikle geleneksel bankalara kıyasla nasıl bu kadar yüksek olabilir?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD, güvenli akıllı sözleşmelerde tutulan dijital varlıklar ve ABD Hazine Bonoları gibi gerçek dünya varlıklarının bir kombinasyonu ile tam olarak desteklenir ve aşırı teminatlandırılmıştır. <PERSON><PERSON><PERSON><PERSON>, şeffaflık ve güvenlik sağlayarak Zeal içinden bile zincir üstünde gerçek zamanlı olarak denetlenebilir. Zeal'in kapanması gibi düşük bir olasılıkta, varlıkların zincir üstünde güvende, tamamen senin kontrolünde kalır ve diğer uyumlu cüzdanlar aracılığıyla erişilebilir olur.", "earn.usd.how-is-sky-backed-popup.title": "Sky USD nasıl destekleniyor ve Zeal iflas ederse parama ne olur?", "earn.usd.insurance-popup.text": "Sky USD fonları FDIC sigortalı değildir veya geleneksel devlet garantileriyle desteklenmez çünkü bu geleneksel bir banka hesabı değil, dijital varlık tabanlı bir hesaptır. Bunun yerine Sky, tüm risk azaltımını denetlenmiş akıllı sözleşmeler ve özenle seçilmiş DeFi protokolleri aracılığıyla yöneterek varlıkların güvende ve şeffaf kalmasını sağlar.", "earn.usd.insurance-popup.title": "Fonlarım herhangi bir kurum (FDIC veya benzeri) tarafından sigortalı mı veya garanti altında mı?", "earn.usd.lending-operations-popup.text": "Sky USD, Morpho ve Spark gibi merkeziyetsiz borç verme piyasaları aracılığıyla stabil koinleri borç vererek getiri sağlar. <PERSON><PERSON><PERSON> koin<PERSON>, kredilerinin değerinden önemli ölçüde daha fazla teminat (ETH veya BTC gibi) yatıran borçlulara verilir. Aşırı teminatlandırma olarak adlandırılan bu yaklaşım, kredileri karş<PERSON> için her zaman yeterli teminat olmasını sağlar ve riski büyük ölçüde azaltır. Borçlular tarafından ödenen toplanan faiz ve ara sıra alınan tasfiye ücretleri, güvenilir, şeffaf ve güvenli getiriler sağlar.", "earn.usd.lending-operations-popup.title": "<PERSON><PERSON><PERSON>i", "earn.usd.market-making-operations-popup.text": "Sky USD, Curve veya Uniswap gibi merkeziyetsiz b<PERSON> (AMM'ler) katılarak ek getiri elde eder. Likidite sağ<PERSON>arak, yani stabil koinlerini kripto ticaretini kolaylaştıran havuzlara yerleştirerek, Sky USD alım satımlardan elde edilen ücretleri toplar. Bu likidite havuzları, oynaklığı en aza indirmek için dikkatle seçilir ve varlıklarını hem güvende hem de erişilebilir tutarak kalıcı kayıp gibi riskleri önemli ölçüde azaltmak için öncelikle stabil koin-stabil koin çiftleri kullanılır.", "earn.usd.market-making-operations-popup.title": "Piyasa Yapıcılık İşlemleri", "earn.usd.treasury-operations-popup.text": "Sky USD, stratejik hazine yatırımları aracılığıyla istikrarlı ve tutarlı bir getiri sağlar. Stabil koin mevduatlarının bir kısmı, <PERSON><PERSON><PERSON><PERSON>, düşük riskli gerçek dünya varlıklarına (öncelikle kısa vadeli devlet tahvilleri ve yüksek güvenlikli kredi araçları) tahsis edilir. Geleneksel bankacılığa benzer bu yaklaşım, öngörülebilir ve güvenilir bir getiri sağlar. Varlıkların güvende, likit ve şeffaf bir şekilde yönetilir.", "earn.usd.treasury-operations-popup.title": "Hazine İşlemleri", "earn.view_earn.card_rechard_off": "<PERSON><PERSON><PERSON>", "earn.view_earn.card_rechard_on": "Açık", "earn.view_earn.card_recharge": "<PERSON><PERSON>", "earn.view_earn.total_balance_label": "Yılda {percentage} kazanılıyor", "earn.view_earn.total_earnings_label": "Toplam kazanç", "earn.withdraw": "Çek", "earn.withdraw.amount_to_withdraw": "Çekilecek tutar", "earn.withdraw.enter_amount": "Tutar G<PERSON>", "earn.withdraw.loading": "Yükleniyor", "earn.withdraw.no_routes_found": "Rota bulunamadı", "earn.withdraw.not_enough_balance": "<PERSON><PERSON><PERSON>", "earn.withdraw.select-currency.title": "Token seç", "earn.withdraw.select_to_token": "Token seç", "earn.withdraw.withdraw": "Çek", "earn.withdraw_form.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earnings-view.earnings": "Toplam Kazanç", "edit-account-owners.add-owner.add-wallet": "<PERSON><PERSON> e<PERSON>", "edit-account-owners.add-owner.add_wallet": "<PERSON>ü<PERSON><PERSON>", "edit-account-owners.add-owner.title": "<PERSON><PERSON> <PERSON>", "edit-account-owners.card-owners": "<PERSON><PERSON>", "edit-account-owners.external-wallet": "<PERSON><PERSON>", "editBankRecipient.title": "Alıcıyı düzenle", "editNetwork.addCustomRPC": "Özel RPC düğümü ekle", "editNetwork.cannot_verify.subtitle": "Özel RPC düğümü düzgün yanıt vermiyor. URL'yi kontrol edip tekrar dene.", "editNetwork.cannot_verify.title": "RPC Düğümünü doğrulayamıyoruz", "editNetwork.cannot_verify.try_again": "<PERSON><PERSON><PERSON> dene", "editNetwork.customRPCNode": "Özel RPC düğümü", "editNetwork.defaultRPC": "Varsayılan RPC", "editNetwork.networkRPC": "<PERSON>ğ RPC'si", "editNetwork.rpc_url.cannot_be_empty": "<PERSON><PERSON><PERSON><PERSON>", "editNetwork.rpc_url.not_a_valid_https_url": "Geçerli bir HTTP(S) URL'si olmalı", "editNetwork.safetyWarning.subtitle": "<PERSON>zel RPC'ler gü<PERSON>z olabil<PERSON>. <PERSON>in misin?", "editNetwork.safetyWarning.title": "Özel RPC'ler gü<PERSON><PERSON>", "editNetwork.zealRPCNode": "Zeal RPC Düğümü", "editNetworkRpc.headerTitle": "Özel RPC Düğümü", "editNetworkRpc.rpcNodeUrl": "RPC Düğümü URL'si", "editing-locked.modal.description": "<PERSON><PERSON>, <PERSON>zinler Harcama Limiti veya Son Kullanma Süresini düzenlemenize izin vermez. Bir İzin göndermeden önce dApp'e güvendiğinizden emin olun.", "editing-locked.modal.title": "Düzenleme kilitli", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Dold<PERSON>ma etkinleştiriliyor", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Dold<PERSON><PERSON>", "enterCardnumber": "Kart numarasını gir", "error.connectivity_error.subtitle": "Lütfen internet bağlantını kontrol edip tekrar dene.", "error.connectivity_error.title": "İnternet bağlantısı yok", "error.decrypt_incorrect_password.title": "Yanlış şifre", "error.encrypted_object_invalid_format.title": "Bozuk veri", "error.failed_to_fetch_google_auth_token.title": "<PERSON><PERSON><PERSON><PERSON>", "error.list.item.cta.action": "<PERSON><PERSON><PERSON> dene", "error.trezor_action_cancelled.title": "İşlem reddedildi", "error.trezor_device_used_elsewhere.title": "Cihaz başka bir oturumda kullanılıyor", "error.trezor_method_cancelled.title": "<PERSON><PERSON>or senkron<PERSON> edilemedi", "error.trezor_permissions_not_granted.title": "<PERSON><PERSON>or senkron<PERSON> edilemedi", "error.trezor_pin_cancelled.title": "<PERSON><PERSON>or senkron<PERSON> edilemedi", "error.trezor_popup_closed.title": "<PERSON><PERSON>or senkron<PERSON> edilemedi", "error.unblock_account_number_and_sort_code_mismatch": "<PERSON><PERSON><PERSON> numa<PERSON> ve şube kodu uyuşmuyor", "error.unblock_can_not_change_details_after_kyc": "KYC'den sonra bilgilerini <PERSON>", "error.unblock_hard_kyc_failure": "Beklenmeyen KYC durumu", "error.unblock_invalid_faster_payment_configuration.title": "Bu banka Hızlı Ödemeleri desteklemiyor", "error.unblock_invalid_iban": "Geçersiz IBAN", "error.unblock_session_expired.title": "<PERSON>block oturumu sona erdi", "error.unblock_user_with_address_already_exists.title": "<PERSON>u adres i<PERSON>in zaten bir hesap var", "error.unblock_user_with_such_email_already_exists.title": "Bu e-postaya sahip bir kullanıcı zaten var", "error.unknown_error.error_message": "Hata mesajı: ", "error.unknown_error.subtitle": "Üzgünüz! Acil yardıma ihtiyacın olursa lütfen destekle iletişime geç ve aşağıdaki bilgileri paylaş.", "error.unknown_error.title": "Sistem hatası", "eth-cost-warning-modal.subtitle": "Akıllı cüzdanlar Ethereum'da çalışır, ancak ücretler çok yüksektir. Diğer ağları kullanmanı ŞİDDETLE tavsiye ederiz.", "eth-cost-warning-modal.title": "Ethereum'dan ka<PERSON>ı<PERSON> - ağ ücretleri yüksek", "exchange.form.button.chain_unsupported": "<PERSON>ğ desteklenmiyor", "exchange.form.button.refreshing": "Yenileniyor", "exchange.form.error.asset_not_supported.button": "Başka bir varlık seç", "exchange.form.error.asset_not_supported.description": "Köprü bu varlığı desteklemiyor.", "exchange.form.error.asset_not_supported.title": "Varlık desteklenmiyor", "exchange.form.error.bridge_quote_timeout.button": "Başka bir varlık seç", "exchange.form.error.bridge_quote_timeout.description": "Başka bir token çifti dene", "exchange.form.error.bridge_quote_timeout.title": "Takas bulunamadı", "exchange.form.error.different_receiver_not_supported.button": "Alternatif alıcıyı kaldır", "exchange.form.error.different_receiver_not_supported.description": "<PERSON>u borsa başka bir adrese göndermeyi desteklemiyor.", "exchange.form.error.different_receiver_not_supported.title": "Gönderme ve alma adresi aynı olmalı", "exchange.form.error.insufficient_input_amount.button": "Tutarı artır", "exchange.form.error.insufficient_liquidity.button": "Tutarı azalt", "exchange.form.error.insufficient_liquidity.description": "Köprüde yet<PERSON>li varlık yok. Daha küçük bir tutar dene.", "exchange.form.error.insufficient_liquidity.title": "Tutar çok yüksek", "exchange.form.error.max_amount_exceeded.button": "Tutarı azalt", "exchange.form.error.max_amount_exceeded.description": "<PERSON><PERSON><PERSON><PERSON> tutar aşıldı.", "exchange.form.error.max_amount_exceeded.title": "Tutar çok yüksek", "exchange.form.error.min_amount_not_met.button": "Tutarı artır", "exchange.form.error.min_amount_not_met.description": "Bu token için minimum takas tutarına ulaşılmadı.", "exchange.form.error.min_amount_not_met.description_with_amount": "Minimum takas tutarı {amount}.", "exchange.form.error.min_amount_not_met.title": "Tutar çok düşük", "exchange.form.error.min_amount_not_met.title_increase": "Tutarı artır", "exchange.form.error.no_routes_found.button": "Başka bir varlık seç", "exchange.form.error.no_routes_found.description": "Bu token/ağ çifti için takas rotası mevcut değil.", "exchange.form.error.no_routes_found.title": "<PERSON><PERSON> mevcut de<PERSON>", "exchange.form.error.not_enough_balance.button": "Tutarı azalt", "exchange.form.error.not_enough_balance.description": "Bu işlem için yet<PERSON> varlığın yok.", "exchange.form.error.not_enough_balance.title": "Bakiye yet<PERSON>iz", "exchange.form.error.slippage_passed_is_too_low.button": "Slippage'ı artır", "exchange.form.error.slippage_passed_is_too_low.description": "Bu varlık için slippage çok düşük.", "exchange.form.error.slippage_passed_is_too_low.title": "Slippage çok düşük", "exchange.form.error.socket_internal_error.button": "<PERSON>ha sonra tekrar dene", "exchange.form.error.socket_internal_error.description": "Köprü ortağı sorun yaşıyor. <PERSON><PERSON> sonra tekrar dene.", "exchange.form.error.socket_internal_error.title": "Köprü ortağında hata", "exchange.form.error.stargatev2_requires_fee_in_native": "Ekle {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "İşlemi ta<PERSON>lamak için {amount} ekle", "exchange.form.error.stargatev2_requires_fee_in_native.title": "<PERSON><PERSON> fazla {symbol}", "expiration-info.modal.description": "Son kullanma süresi, bir <PERSON>y<PERSON>lamanın tokenlerinizi ne kadar süre kullanabileceğini belirtir. <PERSON><PERSON><PERSON>, siz aksini belirtmedikçe erişimlerini kaybederler. Güvende kalmak için son kullanma süresini kısa tutun.", "expiration-info.modal.title": "Son kullanma süresi nedir?", "expiration-time.high.modal.text": "Son kullanma süreleri kısa ve gerçekten ihtiyacın olan süreye göre olmalı. Uzun süreler risklidir ve dolandırıcılara tokenlarını kötüye kullanmaları için daha fazla fırsat verir.", "expiration-time.high.modal.title": "<PERSON><PERSON><PERSON> son kull<PERSON><PERSON> s<PERSON>i", "failed.transaction.content": "İş<PERSON>in baş<PERSON><PERSON><PERSON><PERSON>z o<PERSON>ılığı yüksek", "fee.unknown": "Bilinmiyor", "feedback-request.leave-message": "<PERSON><PERSON>", "feedback-request.not-now": "<PERSON><PERSON><PERSON>", "feedback-request.title": "Teşekkürler! Zeal'ı nasıl iyileştirebiliriz?", "float.input.period": "Ondalık ayırıcı", "gnosis-activate-card.info-popup.subtitle": "İlk işleminde kartını takıp PIN'ini girmelisin. Sonrasında temassız ödemeler çalışacaktır.", "gnosis-activate-card.info-popup.title": "İlk ödeme için Çip ve PIN gerekli", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "Etkinleştirmek için kart numaranı gir.", "gnosis-activate-card.title": "<PERSON><PERSON> numa<PERSON>ı", "gnosis-pay-re-kyc-widget.btn-text": "<PERSON><PERSON><PERSON><PERSON>", "gnosis-pay-re-kyc-widget.title.not-started": "Kimliğini doğrula", "gnosis-pay.login.cta": "Mevcut hesabı bağla", "gnosis-pay.login.title": "Zaten bir Gnosis Pay hesabın var", "gnosis-signup.confirm.subtitle": "Gnosis Pay'den gelen e-postayı kontrol et, spam klasöründe olabilir.", "gnosis-signup.confirm.title": "Doğrulama e-postası almadın mı?", "gnosis-signup.continue": "<PERSON><PERSON> et", "gnosis-signup.dont_link_accounts": "Hesapları bağlama", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "E-posta adresini gir", "gnosis-signup.enter-email.title": "E-posta gir", "gnosis-signup.title": "Gnosis Pay'in <linkGnosisTNC>Kullanım Koşulları</linkGnosisTNC> <monovateTerms><PERSON><PERSON></monovateTerms> ve <linkMonerium>Monerium Şartları</linkMonerium> koşullarını okudum ve kabul ediyorum.", "gnosis-signup.verify-email.title": "E-postayı doğrula", "gnosis.confirm.subtitle": "Kodu almadın mı? Telefon numaranın doğru olduğunu kontrol et", "gnosis.confirm.title": "<PERSON>d <PERSON>u numaraya <PERSON>: {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "<PERSON><PERSON><PERSON><PERSON>", "gnosisPayAccountStatus.success.title": "Kart içe aktarıldı", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay henüz ülkende mevcut değil", "gnosisPayNoActiveCardsFound.title": "Aktif kart yok", "gnosis_pay_card_delay_relay_not_empty_error.title": "İşlemin <PERSON> anda gerçekleştirilemedi. Lütfen daha sonra tekrar dene.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "<PERSON><PERSON> mü<PERSON><PERSON><PERSON><PERSON>", "gnosiskyc.modal.approved.activate-free-card": "Ücretsiz kartı aktifle", "gnosiskyc.modal.approved.button-text": "Bankadan para yatır", "gnosiskyc.modal.approved.title": "<PERSON><PERSON><PERSON><PERSON> hesap bil<PERSON><PERSON><PERSON> o<PERSON>", "gnosiskyc.modal.failed.close": "Ka<PERSON><PERSON>", "gnosiskyc.modal.failed.title": "Üzgünüz, iş ortağımız Gnosis Pay senin için bir hesap oluşturamıyor", "gnosiskyc.modal.in-progress.title": "Kimlik doğrulaması 24 saat veya daha uzun sürebilir. Lütfen sabırlı ol", "goToSettingsPopup.settings": "<PERSON><PERSON><PERSON>", "goToSettingsPopup.title": "Bildirimleri istediğin zaman cihaz ayarlarından etkinleştirebilirsin", "google_file.error.failed_to_fetch_auth_token.button_title": "<PERSON><PERSON><PERSON> dene", "google_file.error.failed_to_fetch_auth_token.subtitle": "Kurtarma Dosyanı kullanmamıza izin vermek için lütfen kişisel bulutunda erişim izni ver.", "google_file.error.failed_to_fetch_auth_token.title": "<PERSON><PERSON><PERSON><PERSON>", "hidden_tokens.widget.emptyState": "Gizli token yok", "how_to_connect_to_metamask.got_it": "Tamam, anladım", "how_to_connect_to_metamask.story.subtitle": "Cüzdanlar arasında kolayca geçiş yap.", "how_to_connect_to_metamask.story.title": "Zeal diğer cüzdanlarla birlikte çalışır", "how_to_connect_to_metamask.why_switch": "Neden cüzdanlar arasında geçiş yapmalı?", "how_to_connect_to_metamask.why_switch.description": "Zeal'ın güvenlik kontrolleri seni korur.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "Mevcut cüzdanınla Zeal'ı kolayca kullan.", "import-bank-transfer-owner.banner.title": "Banka havaleleri için değişen cüzdanınızı içe aktarın.", "import-bank-transfer-owner.title": "Bu cihazda banka havalelerini kullanmak için cüzdanı içe aktar", "import_gnosispay_wallet.add-another-card-owner.footnote": "Gnosis Pay kartına sahip özel anahtarları veya anahtar ifadeyi içe aktar", "import_gnosispay_wallet.primaryText": "Gnosis Pay cüzdanını içe aktar", "injected-wallet": "Tarayıcı cüzdanı", "intercom.getHelp": "<PERSON><PERSON><PERSON> al", "invalid_iban.got_it": "<PERSON><PERSON><PERSON><PERSON>", "invalid_iban.subtitle": "Girilen IBAN geçerli değil. Lütfen bilgilerin doğru girildiğini iki kez kontrol edip tekrar dene.", "invalid_iban.title": "Geçersiz IBAN", "keypad-0": "Tuş takımı tuşu 0", "keypad-1": "Tuş takımı tuşu 1", "keypad-2": "Tuş takımı tuşu 2", "keypad-3": "Tuş takımı tuşu 3", "keypad-4": "Tuş takımı tuşu 4", "keypad-5": "Tuş takımı tuşu 5", "keypad-6": "Tuş takımı tuşu 6", "keypad-7": "Tuş takımı tuşu 7", "keypad-8": "Tuş takımı tuşu 8", "keypad-9": "Tuş takımı tuşu 9", "keypad.biometric-button": "Tuş takımı biyometrik düğmesi", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Gizli İfade güvende 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "İfadeyi görüntüle", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Varlıklarını daha sonra kurtarabilmek için Gizli İfadeni güvenli bir şekilde <PERSON>evrimdışı sakla", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>in <PERSON>", "keystore.write_secret_phrase.before_you_begin.first_point": "Gizli İfademe sahip olan herkesin varlıklarımı havale edebileceğini anlıyorum", "keystore.write_secret_phrase.before_you_begin.second_point": "<PERSON><PERSON><PERSON> İfademi gizli ve güvende tutmaktan ben sorumluyum", "keystore.write_secret_phrase.before_you_begin.subtitle": "Lütfen aşağıdaki maddeleri okuyup kabul et:", "keystore.write_secret_phrase.before_you_begin.third_point": "Etrafımda kimsenin veya kameranın olmadığı gizli bir yer<PERSON>yim", "keystore.write_secret_phrase.before_you_begin.title": "Başlamadan önce", "keystore.write_secret_phrase.secret_phrase_test.title": "<PERSON><PERSON><PERSON> {count} . kelime hang<PERSON>?", "keystore.write_secret_phrase.test_ps.lets_do_it": "<PERSON><PERSON>", "keystore.write_secret_phrase.test_ps.subtitle": "Bu veya başka cihazlarda hesabını geri yüklemek için Gizli İfaden'e ihtiyacın olacak. Gizli İfadeni doğru yazdığını test edelim.", "keystore.write_secret_phrase.test_ps.subtitle2": "<PERSON><PERSON><PERSON> {count} kelimeyi soracağız.", "keystore.write_secret_phrase.test_ps.title": "Hesap <PERSON> Et", "kyc.modal.approved.button-text": "<PERSON><PERSON> ha<PERSON> yap", "kyc.modal.approved.subtitle": "Doğrulaman tamamlandı. Artık sınırsız banka havalesi yapabilirsin.", "kyc.modal.approved.title": "Banka havaleleri açıldı", "kyc.modal.continue-with-partner.button-text": "<PERSON><PERSON> et", "kyc.modal.continue-with-partner.subtitle": "Belgelerini almak ve doğrulama başvurusunu tamamlamak için seni şimdi ortağımıza yönlendireceğiz.", "kyc.modal.continue-with-partner.title": "Ortağımızla devam et", "kyc.modal.failed.unblock.subtitle": "Unblock kimlik doğrulamanı onaylamadı ve sana banka havalesi hizmeti sunamıyor", "kyc.modal.failed.unblock.title": "Unblock ba<PERSON><PERSON><PERSON><PERSON>", "kyc.modal.paused.button-text": "Bilgileri güncelle", "kyc.modal.paused.subtitle": "Görünüşe göre bazı bilgilerin yanlış. Lütfen tekrar dene ve göndermeden önce bilgilerini iki kez kontrol et.", "kyc.modal.paused.title": "Bilgilerin yanlış görünüyor", "kyc.modal.pending.button-text": "Ka<PERSON><PERSON>", "kyc.modal.pending.subtitle": "Doğrulama normalde 10 dakikadan az sürer ama bazen biraz daha uzun sürebilir.", "kyc.modal.pending.title": "Seni bilgilendireceğiz", "kyc.modal.required.cta": "Doğrulamayı başlat", "kyc.modal.required.subtitle": "İşlem limitine ulaştın. Devam etmek için lütfen kimliğini doğrula. Bu işlem genellikle birkaç dakika sürer ve bazı kişisel bilgiler ile belgeler gerektirir.", "kyc.modal.required.title": "Kimlik doğrulaması gerekli", "kyc.submitted": "Başvuru Gönderildi", "kyc.submitted_short": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc_status.completed_status": "Tamamlandı", "kyc_status.failed_status": "Başarısız", "kyc_status.paused_status": "İncelemede", "kyc_status.subtitle": "<PERSON><PERSON>i", "kyc_status.subtitle.wrong_details": "Yanlış bilgiler", "kyc_status.subtitle_in_progress": "<PERSON><PERSON> ediyor", "kyc_status.title": "Kimlik doğrulanıyor", "label.close": "Ka<PERSON><PERSON>", "label.saving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labels.this-month": "<PERSON>u ay", "labels.today": "<PERSON><PERSON><PERSON><PERSON>", "labels.yesterday": "<PERSON><PERSON><PERSON>", "language.selector.title": "Dil", "ledger.account_loaded.imported": "İçe aktarıldı", "ledger.add.success.title": "Ledger başarıyla bağlandı 🎉", "ledger.connect.cta": "Ledger'ı Eşitle", "ledger.connect.step1": "Ledger'ı cihazına bağla", "ledger.connect.step2": "Ledger'da Ethereum uygulamasını aç", "ledger.connect.step3": "Sonra Ledger'ını eşitle 👇", "ledger.connect.subtitle": "Ledger cüzdanlarını Zeal'e aktarmak için bu adımları izle", "ledger.connect.title": "Ledger'ı Zeal'e Bağla", "ledger.error.ledger_is_locked.subtitle": "Ledger'ın kilidini aç ve Ethereum uygulamasını aç", "ledger.error.ledger_is_locked.title": "<PERSON><PERSON> kilitli", "ledger.error.ledger_not_connected.action": "Ledger'ı senkronize et", "ledger.error.ledger_not_connected.subtitle": "Donanım cüzdanını cihazına bağla ve Ethereum uygulamasını aç", "ledger.error.ledger_not_connected.title": "Ledger bağ<PERSON><PERSON> değil", "ledger.error.ledger_running_non_eth_app.title": "Ethereum uygulaması açık değil", "ledger.error.user_trx_denied_by_user.action": "Ka<PERSON><PERSON>", "ledger.error.user_trx_denied_by_user.subtitle": "İşlemi donanım cüzdanında reddettin", "ledger.error.user_trx_denied_by_user.title": "İşlem reddedildi", "ledger.hd_path.bip44.subtitle": "örn. Metamask, Trezor", "ledger.hd_path.bip44.title": "BIP44 Standardı", "ledger.hd_path.ledger_live.subtitle": "Varsayılan", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "<PERSON><PERSON>", "ledger.hd_path.phantom.subtitle": "örn. Phantom", "ledger.select.hd_path.subtitle": "HD yolları, don<PERSON><PERSON>m cüzdanlarının hesaplarını sıralama yöntemidir. Kitaptaki dizin gibidir.", "ledger.select.hd_path.title": "HD Yolunu Seç", "ledger.select_account.import_wallets_count": "{count,plural,=0{Hiç cüzdan seçilmedi} one{Cüzdanı içe aktar} other{{count} cüzdanı içe aktar}}", "ledger.select_account.path_settings": "<PERSON><PERSON>", "ledger.select_account.subtitle": "Cüzdanın yok mu? Yol ayarlarını değiştir.", "ledger.select_account.subtitle.group_header": "Cüzdanlar", "ledger.select_account.title": "Ledger cüzdanlarını içe aktar", "legend.lending-operations": "<PERSON><PERSON>ç Verme İşlemleri", "legend.market_making-operations": "Piyasa Yapıcılık İşlemleri", "legend.treasury-operations": "Hazine İşlemleri", "link-existing-monerium-account-sign.button": "Zeal'ı bağla", "link-existing-monerium-account-sign.subtitle": "Zaten bir Monerium hesabın var.", "link-existing-monerium-account-sign.title": "Mevcut Monerium hesabına Zeal'ı bağla", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Zaten bir Monerium hesabın var. Ku<PERSON>lumu tama<PERSON>lamak için lütfen Monerium uygulamasına git.", "link-existing-monerium-account.title": "Hesabını bağlamak için Monerium'a git", "loading.pin": "PIN yükleniyor...", "lockScreen.passwordIncorrectMessage": "<PERSON><PERSON>re <PERSON>ı<PERSON>", "lockScreen.passwordRequiredMessage": "<PERSON><PERSON><PERSON>", "lockScreen.unlock.header": "<PERSON><PERSON><PERSON>", "lockScreen.unlock.subheader": "Zeal'ın kilidini açmak için şifreni kullan", "mainTabs.activity.label": "Aktivite", "mainTabs.browse.label": "Göz At", "mainTabs.browse.title": "Göz At", "mainTabs.card.label": "Kart", "mainTabs.portfolio.label": "Portföy", "mainTabs.rewards.label": "<PERSON><PERSON><PERSON><PERSON>", "makeSpendable.cta": "Harcanabil<PERSON> yap", "makeSpendable.holdAsCash": "<PERSON><PERSON>t olarak tut", "makeSpendable.shortText": "Yıllık {apy} kazan", "makeSpendable.title": "{amount} alındı", "merchantCategory.agriculture": "Tarım", "merchantCategory.alcohol": "Alkol", "merchantCategory.antiques": "Antikalar", "merchantCategory.appliances": "<PERSON><PERSON>", "merchantCategory.artGalleries": "Sanat Galerileri", "merchantCategory.autoRepair": "<PERSON><PERSON>", "merchantCategory.autoRepairService": "<PERSON><PERSON>", "merchantCategory.beautyFitnessSpas": "Gü<PERSON>lik, Fitness ve Spa", "merchantCategory.beautyPersonalCare": "Güzellik ve Kişisel Bakım", "merchantCategory.billiard": "<PERSON><PERSON><PERSON>", "merchantCategory.books": "Kitaplar", "merchantCategory.bowling": "Bowling", "merchantCategory.businessProfessionalServices": "İş ve Profesyonel Hizmetler", "merchantCategory.carRental": "<PERSON><PERSON>", "merchantCategory.carWash": "<PERSON><PERSON>", "merchantCategory.cars": "<PERSON><PERSON><PERSON>", "merchantCategory.casino": "<PERSON><PERSON>", "merchantCategory.casinoGambling": "<PERSON>hane ve Şans Oyunları", "merchantCategory.cellular": "Mobil", "merchantCategory.charity": "Bağış", "merchantCategory.childcare": "Çocuk Bakımı", "merchantCategory.cigarette": "Sigara", "merchantCategory.cinema": "<PERSON><PERSON>", "merchantCategory.cinemaEvents": "Sin<PERSON> ve <PERSON>likler", "merchantCategory.cleaning": "Temizlik", "merchantCategory.cleaningMaintenance": "Temizlik ve Bakım", "merchantCategory.clothes": "G<PERSON><PERSON><PERSON>", "merchantCategory.clothingServices": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.communicationServices": "İletişim <PERSON>", "merchantCategory.construction": "İnşaat", "merchantCategory.cosmetics": "Kozmetik", "merchantCategory.craftsArtSupplies": "El Sanatları ve Sanat Malzemeleri", "merchantCategory.datingServices": "<PERSON><PERSON><PERSON>rt <PERSON>", "merchantCategory.delivery": "Teslimat", "merchantCategory.dentist": "<PERSON><PERSON>", "merchantCategory.departmentStores": "Çok Katlı Mağazalar", "merchantCategory.directMarketingSubscription": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.discountStores": "İndirim Mağazaları", "merchantCategory.drugs": "İlaçlar", "merchantCategory.dutyFree": "Duty Free", "merchantCategory.education": "Eğitim", "merchantCategory.electricity": "Elektrik", "merchantCategory.electronics": "Elektronik", "merchantCategory.emergencyServices": "Acil Durum Hizmetleri", "merchantCategory.equipmentRental": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.evCharging": "Elektrikli Araç Şarjı", "merchantCategory.financialInstitutions": "Finans Ku<PERSON>ları", "merchantCategory.financialProfessionalServices": "Finansal ve Profesyonel <PERSON>", "merchantCategory.finesPenalties": "Para Cezaları ve Yaptırımlar", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "Uçuşlar", "merchantCategory.flowers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.flowersGarden": "Çiçek ve Bahçe", "merchantCategory.food": "Yiyecek", "merchantCategory.freight": "Nakliye", "merchantCategory.fuel": "Yakıt", "merchantCategory.funeralServices": "<PERSON><PERSON>ze <PERSON>", "merchantCategory.furniture": "Mobilya", "merchantCategory.games": "Oyunlar", "merchantCategory.gas": "Benzin", "merchantCategory.generalMerchandiseRetail": "<PERSON><PERSON> ve Perakende", "merchantCategory.gifts": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.government": "<PERSON><PERSON><PERSON>", "merchantCategory.governmentServices": "Devlet Hizmetleri", "merchantCategory.hardware": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.healthMedicine": "Sağlık ve İlaç", "merchantCategory.homeImprovement": "<PERSON><PERSON>", "merchantCategory.homeServices": "<PERSON><PERSON>", "merchantCategory.hotel": "Otel", "merchantCategory.housing": "<PERSON><PERSON>", "merchantCategory.insurance": "Sigorta", "merchantCategory.internet": "İnternet", "merchantCategory.kids": "Çocuk", "merchantCategory.laundry": "Çamaşırhane", "merchantCategory.laundryCleaningServices": "Çamaşırhane ve Temizlik Hizmetleri", "merchantCategory.legalGovernmentFees": "<PERSON><PERSON> ve <PERSON><PERSON>", "merchantCategory.luxuries": "Lüks Tüketim", "merchantCategory.luxuriesCollectibles": "Lüks Tüketim ve Koleksiyon Ürünleri", "merchantCategory.magazines": "<PERSON><PERSON><PERSON>", "merchantCategory.magazinesNews": "Dergiler ve Haberler", "merchantCategory.marketplaces": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.media": "<PERSON><PERSON><PERSON>", "merchantCategory.medicine": "Sağlık", "merchantCategory.mobileHomes": "<PERSON><PERSON>", "merchantCategory.moneyTransferCrypto": "Para Transferi ve Kripto", "merchantCategory.musicRelated": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.musicalInstruments": "Müzik Aletleri", "merchantCategory.optics": "Optik", "merchantCategory.organizationsClubs": "Organizasyonlar <PERSON>", "merchantCategory.other": "<PERSON><PERSON><PERSON>", "merchantCategory.parking": "Otopark", "merchantCategory.pawnShops": "<PERSON><PERSON><PERSON>", "merchantCategory.pets": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.photoServicesSupplies": "Fotoğraf <PERSON>i ve Malzemeleri", "merchantCategory.postalServices": "Posta Hizmetleri", "merchantCategory.professionalServicesOther": "Profes<PERSON><PERSON> (Diğer)", "merchantCategory.publicTransport": "Toplu Taşıma", "merchantCategory.purchases": "Alışveriş", "merchantCategory.purchasesMiscServices": "Alışveriş ve Çeşitli Hizmetler", "merchantCategory.recreationServices": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.religiousGoods": "<PERSON><PERSON>", "merchantCategory.secondhandRetail": "İkinci El Perakende", "merchantCategory.shoeHatRepair": "Ayakkabı ve Şapka Tamiri", "merchantCategory.shoeRepair": "Ayakkabı Tamiri", "merchantCategory.softwareApps": "Ya<PERSON><PERSON><PERSON><PERSON>m ve Uygulamalar", "merchantCategory.specializedRepairs": "Özel Tamirat Hizmetleri", "merchantCategory.sport": "Spor", "merchantCategory.sportingGoods": "Spor Malzemeleri", "merchantCategory.sportingGoodsRecreation": "Spor Malzemeleri ve Eğlence", "merchantCategory.sportsClubsFields": "Spor Kulüpleri ve Sahaları", "merchantCategory.stationaryPrinting": "Kırtasiye ve Baskı", "merchantCategory.stationery": "Kırtasiye", "merchantCategory.storage": "<PERSON><PERSON><PERSON>", "merchantCategory.taxes": "<PERSON>er<PERSON><PERSON>", "merchantCategory.taxi": "<PERSON><PERSON><PERSON>", "merchantCategory.telecomEquipment": "Telekom Ekipmanı", "merchantCategory.telephony": "Telekomünikasyon", "merchantCategory.tobacco": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.tollRoad": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.tourismAttractionsAmusement": "<PERSON><PERSON><PERSON>, Gezilecek Yerler ve Eğlence", "merchantCategory.towing": "Çekici <PERSON>", "merchantCategory.toys": "Oyuncaklar", "merchantCategory.toysHobbies": "Oyuncaklar ve Hobiler", "merchantCategory.trafficFine": "Trafik <PERSON>zası", "merchantCategory.train": "<PERSON>ren", "merchantCategory.travelAgency": "<PERSON><PERSON><PERSON>", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, Radyo ve Yayın Akışı", "merchantCategory.utilities": "<PERSON><PERSON>", "merchantCategory.waterTransport": "<PERSON><PERSON>", "merchantCategory.wholesaleClubs": "Toptan Satış Kulüpleri", "metaMask.subtitle": "MetaMask bağlantılarını Zeal'a yönlendirir.", "metaMask.title": "Zeal ile ba<PERSON>lanamıyor musun?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Banka uygulamanı aç", "monerium-bank-deposit.buttet-point.receive-crypto": "Dijital EUR al", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": " g<PERSON>nder {fiatCurrencyCode} hesab<PERSON>na", "monerium-bank-deposit.deposit-account-country": "<PERSON><PERSON><PERSON>", "monerium-bank-deposit.header": "{fullName}  k<PERSON><PERSON><PERSON><PERSON> hesabı", "monerium-bank-details.account-name": "<PERSON><PERSON><PERSON> adı", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT Kopyalandı", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT kopyalandı", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN kopyalandı", "monerium-bank-details.to-wallet": "Cüzdana", "monerium-bank-details.transfer-fee": "<PERSON><PERSON>", "monerium-bank-transfer.enable-card.bullet-1": "Kimlik doğrulamasını tamamla", "monerium-bank-transfer.enable-card.bullet-2": "<PERSON><PERSON><PERSON><PERSON> hesap bil<PERSON> al", "monerium-bank-transfer.enable-card.bullet-3": "Banka hesabından para yatır", "monerium-card-delay-relay.success.cta": "Ka<PERSON><PERSON>", "monerium-card-delay-relay.success.subtitle": "Gü<PERSON>lik nedeniyle, kart ayarlarındaki değişikliklerin işlenmesi 3 dakika sürer.", "monerium-card-delay-relay.success.title": "Monerium kurulumuna devam etmek için 3 dakika sonra tekrar gel", "monerium-deposit.account-details-info-popup.bullet-point-1": "Bu hesaba gönder<PERSON>ğin {fiatCurrencyCode} tutarı otomatik olarak {cryptoCurrencyCode} token'larına dönüştürülür ve {cryptoCurrencyChain} ağından cüzdanına gönderilir", "monerium-deposit.account-details-info-popup.bullet-point-2": "Hesabına SADECE {fiatCurrencyCode} ({fiatCurrencySymbol}) gönder", "monerium-deposit.account-details-info-popup.title": "<PERSON><PERSON>p bilgilerin", "monerium.check_order_status.sending": "G<PERSON><PERSON><PERSON><PERSON><PERSON>", "monerium.not-eligible.cta": "<PERSON><PERSON>", "monerium.not-eligible.subtitle": "Monerium senin için bir hesap açamıyor. Lütfen alternatif bir sağlayıcı seç.", "monerium.not-eligible.title": "Farklı bir sağlayıcı dene", "monerium.setup-card.cancel": "İptal", "monerium.setup-card.continue": "<PERSON><PERSON>", "monerium.setup-card.create_account": "<PERSON><PERSON><PERSON>", "monerium.setup-card.login": "Gnosis Pay'e giriş yap", "monerium.setup-card.subtitle": "Anında banka mevduatlarını etkinleştirmek için Gnosis Pay hesabını oluştur veya hesabına giriş yap.", "monerium.setup-card.subtitle_personal_account": "Gnosis Pay ile kişisel hesabını dakikalar içinde oluştur:", "monerium.setup-card.title": "Banka mevduatlarını etkinleştir", "moneriumDepositSuccess.goToWallet": "Cüzdana git", "moneriumDepositSuccess.title": "{symbol}  alındı", "moneriumInfo.fees": "İşlem ücreti %0", "moneriumInfo.registration": "Monerium, 17/2013 sayılı İzlanda Elektronik Para Yasası uyarınca bir Elektronik Para Kuruluşu olarak yetkilendirilmiş ve denetime tabidir. <link>Daha fazla bilgi</link>", "moneriumInfo.selfCustody": "Aldığın dijital nakit özel cüzdanında saklanır ve varlığın üzerinde senden başka kimsenin kontrolü olmaz.", "moneriumWithdrawRejected.supportText": "Havalen ta<PERSON>mlanamadı. Lütfen tekrar dene, yine de olmazsa <link>destekle iletişime geç.</link>", "moneriumWithdrawRejected.title": "<PERSON><PERSON> geri alındı", "moneriumWithdrawRejected.tryAgain": "<PERSON><PERSON><PERSON> dene", "moneriumWithdrawSuccess.supportText": "Alıcının parayı alması{br} 24 saati bulabilir", "moneriumWithdrawSuccess.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monerium_enable_banner.text": "Banka havalelerini şimdi etkinleştir", "monerium_error_address_re_link_required.title": "Cüzdanın Monerium'a yeniden bağlanması gerekiyor", "monerium_error_duplicate_order.title": "<PERSON><PERSON><PERSON><PERSON> emir", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "İsviçre Frangı", "mt-pelerin-fork.options.chf.short": "Mt Pelerin ile Anında ve Ücretsiz", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Monerium ile Anında ve Ücretsiz", "mt-pelerin-fork.title": "Ne yatırmak istersin?", "mtPelerinProviderInfo.fees": "%0 ücret ödersin", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd, Kara Paranın Aklanmasının Önlenmesi Yasası kapsamında İsviçre Finans Otoritesi (FINMA) tarafından tanınan bir öz denetim organı olan SO-FIT'e bağlıdır. <link>Daha fazla bilgi al</link>", "mtPelerinProviderInfo.selfCustody": "Aldığın dijital nakit senin kontrolündedir ve varlıkların üzerinde senden başka kimsenin kontrolü olmaz.", "network-fee-widget.title": "<PERSON><PERSON><PERSON>", "network.edit.verifying_rpc": "RPC doğrulanıyor", "network.editRpc.predefined_network_info.subtitle": "Tıpkı bir VPN gibi, <PERSON><PERSON> kişisel verilerinin takip edilmesini engelleyen RPC'ler kullanır.{br}{br}Zeal'ın Varsayılan RPC'leri, güven<PERSON>r ve zorlu koşullarda test edilmiş RPC sağlayıcılarıdır.", "network.editRpc.predefined_network_info.title": "Zeal gizlilik RPC'si", "network.filter.update_rpc_success": "RPC Düğümü kaydedildi", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "<PERSON><PERSON><PERSON>", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "<PERSON><PERSON>", "networks.filter.add_modal.chain_list.subtitle": "İstediğin EVM ağını ekle", "networks.filter.add_modal.chain_list.title": "Chainlist'e git", "networks.filter.add_modal.dapp_tip.subtitle": "Favori <PERSON>, kullanmak istediğin EVM ağına geçiş yapman yeterli. Zeal, ağı cüzdanına eklemek isteyip istemediğini soracaktır.", "networks.filter.add_modal.dapp_tip.title": "<PERSON><PERSON>a herhangi bir dApp'ten ağ ekle", "networks.filter.add_networks.subtitle": "Tüm EVM ağları desteklenir", "networks.filter.add_networks.title": "<PERSON><PERSON>", "networks.filter.add_test_networks.title": "Test ağları ekle", "networks.filter.tab.netwokrs": "<PERSON><PERSON><PERSON>", "networks.filter.testnets.title": "Test Ağları", "nft.widget.emptystate": "Cüzdanda koleksiyonluk yok", "nft_collection.change_account_picture.subtitle": "Profil resmini güncellemek istiyor musun?", "nft_collection.change_account_picture.title": "Profil Resmini NFT Olarak <PERSON>", "nfts.allNfts.pricingPopup.description": "<PERSON><PERSON><PERSON><PERSON>, en son <PERSON><PERSON><PERSON> fi<PERSON><PERSON><PERSON>.", "nfts.allNfts.pricingPopup.title": "Koleksiyonluk Fiyatlandırması", "no-passkeys-found.modal.cta": "Ka<PERSON><PERSON>", "no-passkeys-found.modal.subtitle": "Bu cihazda Zeal geçiş anahtarı bulunamadı. Cüzdanını oluşturduğun bulut hesabına giriş yaptığından emin ol.", "no-passkeys-found.modal.title": "Geçiş anahtarı bulunamadı", "notValidEmail.title": "Geçerli bir e-posta adresi değil", "notValidPhone.title": "Bu geçerli bir telefon numarası değil", "notification-settings.title": "<PERSON><PERSON><PERSON><PERSON>", "notification-settings.toggles.active-wallets": "Aktif <PERSON>", "notification-settings.toggles.bank-transfers": "<PERSON><PERSON>i", "notification-settings.toggles.card-payments": "<PERSON><PERSON>", "notification-settings.toggles.readonly-wallets": "<PERSON><PERSON><PERSON>ü<PERSON>", "ntft.groupHeader.text": "Koleksi<PERSON>luk<PERSON>", "on_ramp.crypto_completed": "Tamamlandı", "on_ramp.fiat_completed": "Tamamlandı", "onboarding-widget.subtitle.card_created_from_order.left": "Visa kartı", "onboarding-widget.subtitle.card_created_from_order.right": "Kartı etkinleştir", "onboarding-widget.subtitle.card_order_ready.left": "Fiziksel Visa kartı", "onboarding-widget.subtitle.default": "Banka havaleleri ve Visa Kart", "onboarding-widget.title.card-order-in-progress": "Kart sipari<PERSON> devam et", "onboarding-widget.title.card_created_from_order": "Ka<PERSON>ın <PERSON>", "onboarding-widget.title.kyc_approved": "<PERSON><PERSON><PERSON> ve Kart hazır", "onboarding-widget.title.kyc_failed": "<PERSON><PERSON><PERSON>", "onboarding-widget.title.kyc_not_started": "<PERSON><PERSON><PERSON><PERSON> devam et", "onboarding-widget.title.kyc_started_documents_requested": "Doğrulamayı tamamla", "onboarding-widget.title.kyc_started_resubmission_requested": "Doğrulamayı yeniden dene", "onboarding-widget.title.kyc_started_verification_in_progress": "Kimlik doğrulanıyor", "onboarding.loginOrCreateAccount.amountOfAssets": "10 milyar do<PERSON>an fazla <PERSON>lık", "onboarding.loginOrCreateAccount.cards.subtitle": "Yalnızca belirli bölgelerde mevcuttur. Devam ederek şunları kabul edersin: <Terms>Şartlar</Terms> ve <PrivacyPolicy>Gizlilik Politikası</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Yüksek getirili ve ücretsiz{br}Visa kart", "onboarding.loginOrCreateAccount.createAccount": "<PERSON><PERSON><PERSON>", "onboarding.loginOrCreateAccount.earn.subtitle": "<PERSON><PERSON><PERSON> değişebilir; anapara risk altındadır. Devam ederek şunları kabul edersin: <Terms>Şartlar</Terms> ve <PrivacyPolicy>Gizlilik Politikası</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "Yıllık {percent} kazanç{br}Bizi tercih edenler: {currencySymbol}5 milyar dolar+", "onboarding.loginOrCreateAccount.earningPerYear": "Yıllık {percent}{br}kazanç", "onboarding.loginOrCreateAccount.login": "<PERSON><PERSON><PERSON>", "onboarding.loginOrCreateAccount.trading.subtitle": "Anapara risk altındadır. Devam ederek şunları kabul edersin: <Terms>Şartlar</Terms> ve <PrivacyPolicy>Gizlilik Politikası</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Her şeye yatırım yap,{br}BTC'den S&P'ye", "onboarding.loginOrCreateAccount.trustedBy": "Dijital para piyasaları{br}Bizi tercih edenler: {assets}", "onboarding.wallet_stories.close": "Ka<PERSON><PERSON>", "onboarding.wallet_stories.previous": "<PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.deposit": "Yatırılan", "order-earn-deposit-bridge.into": "<PERSON><PERSON><PERSON>", "otpIncorrectMessage": "Doğrulama kodu yanlış", "passkey-creation-not-possible.modal.close": "Ka<PERSON><PERSON>", "passkey-creation-not-possible.modal.subtitle": "Cüzdanın için bir Passkey oluşturamadık. Lütfen cihazının Passkey'leri desteklediğinden emin ol ve tekrar dene. <link>Destekle iletişime geç</link> sorun devam ederse.", "passkey-creation-not-possible.modal.title": "Passkey oluşturulamıyor", "passkey-not-supported-in-mobile-browser.modal.cta": "Zeal'i indir", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Akıllı cüzdanlar mobil tarayıcılarda desteklenmez.", "passkey-not-supported-in-mobile-browser.modal.title": "Devam etmek için Zeal uygulamasını indir", "passkey-recovery.recovering.deploy-signer.loading-text": "Geçiş anahtarı doğrulanıyor", "passkey-recovery.recovering.loading-text": "Cüzdan kurtarılıyor", "passkey-recovery.recovering.signer-not-found.subtitle": "Geçiş anahtarın bir cüzdana bağlanamadı. Cüzdanında para varsa destek için Zeal ekibine ulaş.", "passkey-recovery.recovering.signer-not-found.title": "Cüzdan bulunamadı", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "Başka geçiş anahtarı dene", "passkey-recovery.select-passkey.banner.subtitle": "Cihazında doğru hesapta oturum açtığından emin ol. Geçiş anahtarları hesaba özeldir.", "passkey-recovery.select-passkey.banner.title": "Cüzdanının geçiş anahtarını göremiyor musun?", "passkey-recovery.select-passkey.continue": "Geçiş anahtarı seç", "passkey-recovery.select-passkey.subtitle": "Cüzdanına bağlı geçiş anahtarını seç.", "passkey-recovery.select-passkey.title": "Geçiş Anahtarı Seç", "passkey-story_1.subtitle": "Smart Wallet ile ağ ücretlerini çoğu token ile ödeyebilirsin ve ağ ücretleri için endişelenmene gerek kalmaz.", "passkey-story_1.title": "<PERSON>ğ ücretlerini unut - çoğu token ile öde", "passkey-story_2.subtitle": "20 milyondan fazla cüzdanda 100 milyar dolardan fazlasını güvence altına alan, Safe'in sektör lideri akıllı sözleşmeleri üzerine inşa edilmiştir.", "passkey-story_2.title": "Safe ile güvende", "passkey-story_3.subtitle": "Smart Wallet'lar başlıca Ethereum uyumlu ağlarda çalışır. Varlık göndermeden önce desteklenen ağları kontrol et.", "passkey-story_3.title": "Başlıca EVM ağları desteklenir", "password.add.header": "<PERSON><PERSON><PERSON>", "password.add.includeLowerAndUppercase": "Küçük ve büyük harf", "password.add.includesNumberOrSpecialChar": "Bir sayı veya sembol", "password.add.info.subtitle": "Şifreni sunucularımıza göndermeyiz veya senin için <PERSON>", "password.add.info.t_and_c": "Devam ederek şunları kabul edersin: <Terms>Şartlar</Terms> ve <PrivacyPolicy>Gizlilik Politikası</PrivacyPolicy>", "password.add.info.title": "<PERSON><PERSON><PERSON> bu cihazda kalır", "password.add.inputPlaceholder": "<PERSON><PERSON><PERSON>", "password.add.shouldContainsMinCharsCheck": "10+ karakter", "password.add.subheader": "Zeal'ın kilidini açmak için şifreni kullanacaksın", "password.add.success.title": "<PERSON><PERSON><PERSON> 🔥", "password.confirm.header": "<PERSON><PERSON><PERSON><PERSON>", "password.confirm.passwordDidNotMatch": "<PERSON><PERSON><PERSON><PERSON>ş<PERSON>şmeli", "password.confirm.subheader": "Şifreni bir kez daha gir", "password.create_pin.subtitle": "Bu parola Zeal uygulamasını kilitler", "password.create_pin.title": "Parolanı oluştur", "password.enter_pin.title": "Parolayı gir", "password.incorrectPin": "Yanlış parola", "password.pin_is_not_same": "<PERSON><PERSON><PERSON>ş<PERSON>", "password.placeholder.enter": "<PERSON><PERSON><PERSON>i gir", "password.placeholder.reenter": "<PERSON><PERSON><PERSON>i tekrar gir", "password.re_enter_pin.subtitle": "Aynı parolayı tekrar gir", "password.re_enter_pin.title": "Parolayı onayla", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "Bekleniyor", "pending-send.details.pending": "Bekleniyor", "pending-send.details.processing": "İşleniyor", "permit-info.modal.description": "<PERSON><PERSON><PERSON>, imzalandı<PERSON><PERSON> takdirde uygulamaların senin adına tokenlerini taşımasına olanak tanıyan taleplerdir, örne<PERSON><PERSON> takas yapmak için.{br}<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> benzer ancak imzalamak için herhangi bir ağ ücreti ödemezsin.", "permit-info.modal.title": "<PERSON><PERSON><PERSON> nedir?", "permit.edit-expiration": "Düzenle {currency} son kull<PERSON><PERSON> s<PERSON>i", "permit.edit-limit": "Düzenle: {currency} harcama limiti", "permit.edit-modal.expiresIn": "Sona erme s<PERSON>…", "permit.expiration-warning": "{currency} son kull<PERSON><PERSON> ta<PERSON>ı", "permit.expiration.info": "{currency} sona erme bilgisi", "permit.expiration.never": "<PERSON><PERSON>", "permit.spend-limit.info": "{currency} harcama limiti bilgisi", "permit.spend-limit.warning": "{currency} harcama limiti uyarısı", "phoneNumber.title": "telefon numarası", "physicalCardOrderFlow.cardOrdered": "<PERSON><PERSON> sip<PERSON> edildi", "physicalCardOrderFlow.city": "Şehir", "physicalCardOrderFlow.orderCard": "Ka<PERSON> Sipariş Et", "physicalCardOrderFlow.postcode": "Posta kodu", "physicalCardOrderFlow.shippingAddress.subtitle": "Kartının gö<PERSON> yer", "physicalCardOrderFlow.shippingAddress.title": "Teslimat adresi", "physicalCardOrderFlow.street": "Sokak", "placeholderDapps.1inch.description": "En iyi rotaları kullanarak takas yap", "placeholderDapps.aave.description": "Token ö<PERSON>ü<PERSON>ç ver ve al", "placeholderDapps.bungee.description": "En iyi rotalarla ağlar arası köprü kur", "placeholderDapps.compound.description": "Token ö<PERSON>ü<PERSON>ç ver ve al", "placeholderDapps.cowswap.description": "Gnosis'te en iyi oran<PERSON>la takas yap", "placeholderDapps.gnosis-pay.description": "Gnosis Pay kartını yönet", "placeholderDapps.jumper.description": "En iyi rotalarla ağlar arası köprü kur", "placeholderDapps.lido.description": "Daha fazla ETH için ETH stake et", "placeholderDapps.monerium.description": "e-Para ve Banka ha<PERSON>i", "placeholderDapps.odos.description": "En iyi rotaları kullanarak takas yap", "placeholderDapps.stargate.description": "Köprü kur veya <%14 APY için <PERSON>ake et", "placeholderDapps.uniswap.description": "En popüler takas platformlarından biri", "pleaseAllowNotifications.cardPayments": "<PERSON><PERSON>", "pleaseAllowNotifications.customiseInSettings": "<PERSON><PERSON><PERSON><PERSON>", "pleaseAllowNotifications.enable": "Etkinleştir", "pleaseAllowNotifications.forWalletActivity": "Cüzdan etkinliği için", "pleaseAllowNotifications.title": "Cüzdan bildirimleri al", "pleaseAllowNotifications.whenReceivingAssets": "Varlık alırken", "portfolio.quick-actions.add_funds": "<PERSON><PERSON>", "portfolio.quick-actions.buy": "Al", "portfolio.quick-actions.deposit": "<PERSON><PERSON><PERSON><PERSON>", "portfolio.quick-actions.send": "<PERSON><PERSON><PERSON>", "portfolio.view.lastRefreshed": "<PERSON><PERSON><PERSON><PERSON> {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Testnet AVAX bakiyeni yükle", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Faucet'e git", "portfolio.view.topupTestNet.BscTestnet.primary": "Testnet BNB bakiyeni y<PERSON>", "portfolio.view.topupTestNet.BscTestnet.secondary": "Faucet'e git", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Testnet SepETH bakiyeni yü<PERSON>", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Sepolia Faucet'e git", "portfolio.view.topupTestNet.FantomTestnet.primary": "Testnet FTM bakiyeni yükle", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Faucet'e git", "privateKeyConfirmation.banner.subtitle": "Özel Anahtarınızı bilen herkes fonlarınıza erişebilir. Bunu yalnızca dolandırıcılar ister.", "privateKeyConfirmation.banner.title": "Riskleri anlıyorum", "privateKeyConfirmation.title": "Özel Anahtarını ASLA kimseyle paylaşma", "rating-request.not-now": "<PERSON><PERSON><PERSON>", "rating-request.title": "Zeal'ı tavsiye eder misin?", "receive_funds.address-text": "Bu senin benzersiz cüzdan adresindir. Güvenle başkalarıyla paylaşabilirsin.", "receive_funds.copy_address": "Cüzdan adresini kopyala", "receive_funds.network-warning.eoa.subtitle": "<link>Standart ağ listesini gö<PERSON>ü<PERSON>ü<PERSON></link>. EVM olmayan ağlarda gönderilen varlıklar kaybolur.", "receive_funds.network-warning.eoa.title": "Tüm Ethereum tabanlı ağlar desteklenir", "receive_funds.network-warning.scw.subtitle": "<link>Desteklenen ağları görüntüle</link>. Diğer ağlarda gönderilen varlıklar kaybolur.", "receive_funds.network-warning.scw.title": "Önemli: Yalnızca desteklenen ağları kullan", "receive_funds.scan_qr_code": "QR kod tara", "receiving.in.days": "Alınacak: {days}g", "receiving.this.week": "Bu hafta alınacak", "receiving.today": "<PERSON>ug<PERSON>n <PERSON>", "reference.error.maximum_number_of_characters_exceeded": "Çok fazla karakter", "referral-code.placeholder": "<PERSON>t bağlantısını yapıştır", "referral-code.subtitle": "Arkadaşının bağlantısına tekrar tıkla veya bağlantıyı aşağıya yapıştır. Ödüllerini aldığından emin olmak istiyoruz.", "referral-code.title": "Bir arkadaşın sana  mı gönderdi {bReward}?", "rekyc.verification_deadline.subtitle": "Doğrulamayı {daysUntil} gün içinde tamamlayarak kartını kullanmaya devam et.", "rekyc.verification_required.subtitle": "Kartını kullanmaya devam etmek için doğrulamayı tamamla.", "reminder.fund": "💸 Fon ekle — anında %6 kazanmaya başla", "reminder.onboarding": "🏁 Kurulumu tamamla — mevduatlarında %6 kazan", "remove-owner.confirmation.subtitle": "Güvenlik nedeniyle ayar değişikliklerinin işlenmesi 3 dakika sürer. Bu sürede kartın geçici olarak dondurulur ve ödeme yapılamaz.", "remove-owner.confirmation.title": "Ayarlar güncellenirken kartın 3 dakika dondurulacak", "restore-smart-wallet.wallet-recovered": "Cüzdan kurtarıldı", "rewardClaimCelebration.claimedTitle": "<PERSON><PERSON><PERSON><PERSON> zaten talep edilmiş", "rewardClaimCelebration.subtitle": "Arkadaşlarını davet ettiğin için", "rewardClaimCelebration.title": "Kazandın", "rewards-warning.subtitle": "Bu hesabı kaldırmak, bağlantılı ödüllere erişimi duraklatır. Ödülleri almak için hesabı istediğin zaman geri yükleyebilirsin.", "rewards-warning.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rewards.copiedInviteLink": "Davet bağlantısı kopyalandı", "rewards.createAccount": "<PERSON><PERSON> k<PERSON>", "rewards.header.subtitle": "<PERSON><PERSON> {aReward} ve arkadaşına {bReward} g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eğ<PERSON>  harcarsa {bSpendLimitReward}.", "rewards.header.title": " Kazan, {amountA}{br}  Kazandır {amountB}", "rewards.sendInvite": "<PERSON><PERSON>", "rewards.sendInviteTip": "Bir arkadaşını seç, ona  verelim {bAmount}", "route.fees": "<PERSON><PERSON> {fees}", "routesNotFound.description": "<PERSON><PERSON> rotası, {from}-{to} ağ kombinasyonu için mevcut değil.", "routesNotFound.title": "Takas rotası mevcut değil", "rpc.OrderBuySignMessage.subtitle": "Swaps.IO kullanılıyor", "rpc.OrderCardTopupSignMessage.subtitle": "Swaps.IO kullanılıyor", "rpc.addCustomNetwork.addNetwork": "<PERSON><PERSON>", "rpc.addCustomNetwork.chainId": "<PERSON><PERSON><PERSON><PERSON>", "rpc.addCustomNetwork.nativeToken": "<PERSON><PERSON>", "rpc.addCustomNetwork.networkName": "<PERSON><PERSON> adı", "rpc.addCustomNetwork.operationDescription": "Bu web sitesinin cüzdanınıza bir ağ eklemesine izin verir. Zeal, özel ağların güvenliğini kontrol edemez, riskleri anladığınızdan emin olun.", "rpc.addCustomNetwork.rpcUrl": "RPC URL'si", "rpc.addCustomNetwork.subtitle": "Kullanan: {name}", "rpc.addCustomNetwork.title": "<PERSON><PERSON>", "rpc.send_token.network_not_supported.subtitle": "Bu ağda işlemleri etkinleştirmek için çalışıyoruz. Sabrınız için teşekkürler 🙏", "rpc.send_token.network_not_supported.title": "Ağ yakında eklenecek", "rpc.send_token.send_or_receive.settings": "<PERSON><PERSON><PERSON>", "rpc.sign.accept": "Kabul et", "rpc.sign.cannot_parse_message.body": "Bu mesajın kodunu çözemedik. Yalnızca bu uygulamaya güveniyorsanız bu isteği kabul edin.{br}{br}Mesajlar bir uygulamada oturum açmak için kullanılabilir, ancak uygulamalara tokenleriniz üzerinde kontrol de verebilir.", "rpc.sign.cannot_parse_message.header": "<PERSON><PERSON><PERSON><PERSON> devam edin", "rpc.sign.import_private_key": "Anahtarları içe aktar", "rpc.sign.subtitle": "<PERSON><PERSON><PERSON>: {name}", "rpc.sign.title": "İmzala", "safe-creation.success.title": "Cüzdan oluşturuldu", "safe-safety-checks-popup.title": "İşlem Güvenlik Kontrolleri", "safetyChecksPopup.title": "Site Güvenlik Kontrolleri", "scan_qr_code.description": "Cüzdan QR'ını tara veya bir Uygulamaya bağlan", "scan_qr_code.show_qr_code": "QR kodumu göster", "scan_qr_code.tryAgain": "<PERSON><PERSON><PERSON> dene", "scan_qr_code.unlockCamera": "Kameranın kilidini aç", "screen-lock-missing.modal.close": "Ka<PERSON><PERSON>", "screen-lock-missing.modal.subtitle": "Cihazın Passkey kullanmak için bir ekran kilidi gerektiriyor. Lütfen bir ekran kilidi ayarla ve tekrar dene.", "screen-lock-missing.modal.title": "Ekran kilidi eksik", "seedConfirmation.banner.subtitle": "Gizli İfadenizi bilen herkes fonlarınıza erişebilir. Bunu yalnızca dolandırıcılar ister.", "seedConfirmation.title": "Gizli İfaden'i ASLA kimseyle paylaşma", "select-active-owner.subtitle": "Kartına bağlı birden fazla cüzdanın var. Zeal'e bağlanmak için birini seç. İstediğin zaman değiştirebilirsin.", "select-active-owner.title": "Cüzdan seç", "select-card.title": "<PERSON><PERSON> se<PERSON>", "select-crypto-currency-title": "Token seç", "select-token.title": "Token seç", "selectEarnAccount.chf.description.steps": "· 7/24 para çekme, kilitlenme yok {br}· Her saniye faiz işler {br}· Aşırı korumalı mevduatlar: <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} yıllık CHF cinsinden", "selectEarnAccount.eur.description.steps": "· 7/24 para <PERSON><PERSON>, kilit yok {br}· <PERSON><PERSON>z her saniye işler {br}· Aşırı teminatlı krediler, kaynak: <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} yıllık EUR", "selectEarnAccount.subtitle": "İstediğin zaman değiştirebilirsin", "selectEarnAccount.title": "Para Birimi <PERSON>", "selectEarnAccount.usd.description.steps": "· 7/24 para <PERSON><PERSON>, kilit yok {br}· <PERSON><PERSON>z her saniye işler {br}· Aşırı teminatlı mevduatlar, kaynak: <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} yıllık USD", "selectEarnAccount.zero.description_general": "<PERSON><PERSON>z kazanmadan dijital nakit tut", "selectEarnAccount.zero.title": "%0 yıllık", "selectRechargeThreshold.button.enterAmount": "Tutar gir", "selectRechargeThreshold.button.setTo": "Şuna ayarla: {amount}", "selectRechargeThreshold.description.line1": "Kartın şu tutarın altına düştüğünde, {amount} otomatik olarak şu tutara geri yüklenir {amount} Kazan hesabından.", "selectRechargeThreshold.description.line2": "Daha düşük bir hedef, Kazan hesabında daha fazla para tutmanı sağlar (%3 kazandırır). Bunu istediğin zaman değiştirebilirsin.", "selectRechargeThreshold.title": "<PERSON><PERSON> hede<PERSON> b<PERSON><PERSON>", "select_currency_to_withdraw.select_token_to_withdraw": "Çekilecek token'ı seç", "send-card-token.form.send": "<PERSON><PERSON><PERSON>", "send-card-token.form.send-amount": "<PERSON><PERSON><PERSON>me tutarı", "send-card-token.form.title": "<PERSON>rta nakit ekle", "send-card-token.form.to-address": "Kart", "send-safe-transaction.network-fee-widget.error": "Şuna ihtiyacın var: {amount} veya başka bir token seç", "send-safe-transaction.network-fee-widget.no-fee": "Ücret yok", "send-safe-transaction.network-fee-widget.title": "<PERSON><PERSON><PERSON>", "send-safe-transaction.network_fee_widget.title": "<PERSON><PERSON>", "send.banner.fees": "İhtiyacın var: {amount} daha fazla {currency} ücretleri ödemek için", "send.banner.toAddressNotSupportedNetwork.subtitle": "Alıcının cüzdanı desteklemiyor: {network}. Desteklenen bir token'a geç.", "send.banner.toAddressNotSupportedNetwork.title": "Alıcı ağı desteklemiyor", "send.banner.walletNotSupportedNetwork.subtitle": "Akıllı cüzdanlar şu ağda işlem yapamaz: {network}. Desteklenen bir token'a geç.", "send.banner.walletNotSupportedNetwork.title": "Token ağı desteklenmiyor", "send.empty-portfolio.empty-state": "Hiç <PERSON> bulamad<PERSON>k", "send.empty-portfolio.header": "To<PERSON>'lar", "send.titile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendLimit.success.subtitle": "Günlük harcama limitin 3 dakika içinde güncellenecek. O zamana kadar mevcut limitinle harcamaya devam edebilirsin.", "sendLimit.success.title": "Bu değişiklik 3 dakika sürecek", "send_crypto.form.disconnected.cta.addFunds": "Para ekle", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Şu ağa geç: {network}", "send_crypto.form.disconnected.label": "Havale edilecek tutar", "send_to.qr_code.description": "Bir cüzdana göndermek için QR kodunu tara", "send_to.qr_code.title": "QR kodu tara", "send_to_card.header": "Kart adresine gönder", "send_to_card.select_sender.add_wallet": "<PERSON><PERSON><PERSON><PERSON> ekle", "send_to_card.select_sender.header": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "send_to_card.select_sender.search.default_placeholder": "Adres veya ENS ara", "send_to_card.select_sender.show_card_address_button_description": "<PERSON><PERSON> ad<PERSON>", "send_token.form.select-address": "<PERSON><PERSON>", "send_token.form.send-amount": "Gönderilecek tutar", "send_token.form.title": "<PERSON><PERSON><PERSON>", "setLimit.amount.error.zero_amount": "Hiçbir ödeme yapamazsın", "setLimit.error.max_limit_reached": "<PERSON><PERSON> ma<PERSON> {amount}", "setLimit.error.same_as_current_limit": "Mevcut limitle aynı", "setLimit.placeholder": "Mevcut: {amount}", "setLimit.submit": "<PERSON><PERSON>", "setLimit.submit.error.amount_required": "Tutar G<PERSON>", "setLimit.subtitle": "<PERSON><PERSON>, kartınla günde harcayabileceğin tutardır.", "setLimit.title": "Günlük harcama limiti a<PERSON>la", "settings.accounts": "<PERSON><PERSON><PERSON><PERSON>", "settings.accountsSeeAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gör", "settings.addAccount": "<PERSON><PERSON><PERSON><PERSON> ekle", "settings.card": "<PERSON><PERSON>", "settings.connections": "Uygulama Bağlantıları", "settings.currency": "Varsayılan Para Birimi", "settings.default_currency_selector.title": "Para Birimi", "settings.discord": "Discord", "settings.experimentalMode": "Deneysel mod", "settings.experimentalMode.subtitle": "Yeni özel<PERSON> test et", "settings.language": "Dil", "settings.lockZeal": "Zeal'<PERSON>", "settings.notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.open_expanded_view": "Genişletilmiş görünümü aç", "settings.privacyPolicy": "Gizlilik Politikası", "settings.settings": "<PERSON><PERSON><PERSON>", "settings.termsOfUse": "Kullanım Koşulları", "settings.twitter": "𝕏 / Twitter", "settings.version": "<PERSON><PERSON><PERSON><PERSON><PERSON> {version} ortam: {env}", "setup-card.confirmation": "Sanal Kart Al", "setup-card.confirmation.subtitle": "İnternetten ödeme yap ve temassız ödemeler için {type} cüzdanına ekle.", "setup-card.getCard": "<PERSON>rt al", "setup-card.order.physicalCard": "Fiziksel kart", "setup-card.order.physicalCard.steps": "· Fiziksel bir VISA Gnosis Pay {br}· Sana gönderilmesi 3 haftaya kadar sürer {br}· Yüz yüze ödemelerde ve ATM'lerde kullan. {br}· Apple/Google cüzdanına ekle (yalnızca desteklenen ülkelerde)", "setup-card.order.subtitle1": "<PERSON><PERSON><PERSON> anda birden fazla kart kullanabilirsin", "setup-card.order.title": "Ne tür bir kart?", "setup-card.order.virtualCard": "Sanal kart", "setup-card.order.virtual_card.steps": "· Dijital VISA Gnosis Pay {br}· Çevrimiçi ödemeler için anında kullan {br}· Apple/Google cüzdanına ekle (yalnızca desteklenen ülkelerde)", "setup-card.orderCard": "<PERSON><PERSON> et", "setup-card.virtual-card": "Sanal Kart Al", "setup.notifs.fakeAndroid.title": "Ödemeler ve gelen havaleler i<PERSON><PERSON> bi<PERSON>", "setup.notifs.fakeIos.subtitle": "Zeal, nakit aldığında veya Visa kartınla harcama yaptığında seni uyarabilir. <PERSON><PERSON>u daha sonra değiştirebilirsin.", "setup.notifs.fakeIos.title": "Ödemeler ve gelen havaleler i<PERSON><PERSON> bi<PERSON>", "sign.PermitAllowanceItem.spendLimit": "<PERSON><PERSON><PERSON> limiti", "sign.ledger.subtitle": "İşlem talebini donanım cüzdanına gönderdik. Lütfen oradan devam et.", "sign.ledger.title": "Donanım cüzdanını imzala", "sign.passkey.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu cüzdanla ilişkili Passkey ile imza atmanı istemelidir. Lütfen oradan devam et.", "sign.passkey.title": "<PERSON>key seç", "signal_aborted_for_uknown_reason.title": "<PERSON><PERSON> is<PERSON>ği iptal edildi", "simulatedTransaction.BridgeTrx.info.title": "Köprü", "simulatedTransaction.CardTopUp.info.title": "<PERSON>rta nakit ekle", "simulatedTransaction.CardTopUpTrx.info.title": "<PERSON>rta nakit ekle", "simulatedTransaction.NftCollectionApproval.approve": "NFT koleksiyonunu onayla", "simulatedTransaction.OrderBuySignMessage.title": "Satın Al", "simulatedTransaction.OrderCardTopupSignMessage.title": "<PERSON><PERSON> e<PERSON>", "simulatedTransaction.OrderEarnDepositBridge.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.P2PTransaction.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.PermitSignMessage.title": "İzin", "simulatedTransaction.SingleNftApproval.approve": "N<PERSON>'yi on<PERSON>la", "simulatedTransaction.UnknownSignMessage.title": "İmzala", "simulatedTransaction.Withdrawal.info.title": "Para Çekme", "simulatedTransaction.approval.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.approve.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.p2p.info.account": "Alıcı", "simulatedTransaction.p2p.info.unlabelledAccount": "Etiketsiz <PERSON>", "simulatedTransaction.unknown.info.receive": "Alınacak", "simulatedTransaction.unknown.info.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.using": "Uygulama: {app}", "simulation.approval.modal.text": "Bir onayı kabul et<PERSON>ğ<PERSON>e, belirli bir uygulamanın/akıll<PERSON> sözleşmenin gelecekteki işlemlerde token'larını veya NFT'lerini kullanmasına izin vermiş olursun.", "simulation.approval.modal.title": "<PERSON><PERSON><PERSON> nedir?", "simulation.approval.spend-limit.label": "<PERSON><PERSON><PERSON> limiti", "simulation.approve.footer.for": "<PERSON><PERSON><PERSON>", "simulation.approve.unlimited": "Sınırsız", "simulationNotAvailable.title": "Bilinmeyen E<PERSON>m", "smart-wallet-activation-view.on": "Ağ", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password cüzdanına erişimi en<PERSON>r", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Zeal'ı güvenle kurmak için Apple veya Google kullan", "smart-wallet.passkey-notice.title": "1Password'den kaçın", "spend-limits.high.modal.text": "Bir uygulama veya akıllı sözleşme ile gerçekten kullanacağın token miktarına yakın bir harcama limiti belirle. Yüksek limitler risklidir ve dolandırıcıların token'larını çalmasını kolaylaştırabilir.", "spend-limits.high.modal.text_sign_message": "<PERSON><PERSON><PERSON> limit<PERSON>, bir uygulama veya akıllı sözleşmeyle gerçekten kullanacağın token miktarına yakın olmalı. Yüksek limitler risklidir ve dolandırıcıların tokenlarını çalmasını kolaylaştırabilir.", "spend-limits.high.modal.title": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON> limiti", "spend-limits.modal.text": "<PERSON><PERSON><PERSON> limiti, bir uygulamanın senin adına ne kadar token kullanabileceğidir. Bu limiti istediğin zaman değiştirebilir veya kaldırabilirsin. Güvende kalmak için harcama limitlerini bir uygulamayla gerçekten kullanacağın token miktarına yakın tut.", "spend-limits.modal.title": "Ha<PERSON>ma limiti nedir?", "spent-limit-info.modal.description": "<PERSON><PERSON><PERSON> limiti, bir uygulamanın senin adına ne kadar token kullanabileceğidir. Bu limiti istediğin zaman değiştirebilir veya kaldırabilirsin. Güvende kalmak için harcama limitlerini bir uygulamayla gerçekten kullanacağın token miktarına yakın tut.", "spent-limit-info.modal.title": "Ha<PERSON>ma limiti nedir?", "sswaps-io.transfer-provider": "Havale sağlayıcısı", "storage.accountDetails.activateWallet": "Cüzdanı etkinleştir", "storage.accountDetails.changeWalletLabel": "Cüzdan Etiketini değiştir", "storage.accountDetails.deleteWallet": "Cüzdanı kaldır", "storage.accountDetails.setup_recovery_kit": "<PERSON><PERSON>", "storage.accountDetails.showPrivateKey": "Özel Anahtarı Göster", "storage.accountDetails.showWalletAddress": "Cüzdan adresini göster", "storage.accountDetails.smartBackup": "<PERSON><PERSON><PERSON><PERSON>", "storage.accountDetails.viewSsecretPhrase": "Gizli İfadeyi Görüntüle", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallet'lar?", "storage.manageAccounts.title": "Cüzdanlar", "submit-userop.progress.text": "G<PERSON><PERSON><PERSON><PERSON><PERSON>", "submit.error.amount_high": "Tutar çok yüksek", "submit.error.amount_hight": "Tutar çok yüksek", "submit.error.amount_low": "Tutar çok düşük", "submit.error.amount_required": "Tutar G<PERSON>", "submit.error.maximum_number_of_characters_exceeded": "<PERSON><PERSON>", "submit.error.not_enough_balance": "<PERSON><PERSON><PERSON>", "submit.error.recipient_required": "Alıcı gerekli", "submit.error.routes_not_found": "Rota bulunamadı", "submitSafeTransaction.monitor.title": "İşlem sonucu", "submitSafeTransaction.sign.title": "İşlem sonucu", "submitSafeTransaction.state.sending": "G<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitSafeTransaction.state.sign": "Oluşturuluyor", "submitSafeTransaction.submittingToRelayer.title": "İşlem sonucu", "submitTransaction.cancel": "İptal", "submitTransaction.cancel.attemptingToStop": "Du<PERSON><PERSON><PERSON> deneniyor", "submitTransaction.cancel.failedToStop": "Durdurulamadı", "submitTransaction.cancel.stopped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.cancel.title": "İşlem önizlemesi", "submitTransaction.failed.banner.description": "Ağ bu işlemi beklenmedik bir şekilde iptal etti. Tekrar dene veya bize ulaş.", "submitTransaction.failed.banner.title": "İşlem başarısız oldu", "submitTransaction.failed.execution_reverted.title": "Uygulamada bir hata <PERSON>", "submitTransaction.failed.execution_reverted_without_message.title": "Uygulamada bir hata <PERSON>", "submitTransaction.failed.out_of_gas.description": "Yüksek ağ ücreti işlemi iptal etti.", "submitTransaction.failed.out_of_gas.title": "<PERSON><PERSON>ı", "submitTransaction.sign.title": "İşlem sonucu", "submitTransaction.speedUp": "Hızlandır", "submitTransaction.state.addedToQueue": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue.short": "<PERSON><PERSON><PERSON>", "submitTransaction.state.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.complete": "{currencyCode} Zeal'a eklendi", "submitTransaction.state.complete.subtitle": "Zeal portföyünü kontrol et", "submitTransaction.state.completed": "Tamamlandı", "submitTransaction.state.failed": "Başarısız", "submitTransaction.state.includedInBlock": "<PERSON><PERSON><PERSON><PERSON> da<PERSON> edil<PERSON>", "submitTransaction.state.includedInBlock.short": "Blokta", "submitTransaction.state.replaced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.sendingToNetwork": "<PERSON><PERSON><PERSON> gö<PERSON>", "submitTransaction.stop": "<PERSON><PERSON><PERSON>", "submitTransaction.submit": "<PERSON><PERSON><PERSON>", "submitted-user-operation.state.bundled": "<PERSON><PERSON><PERSON> be<PERSON>r", "submitted-user-operation.state.completed": "Tamamlandı", "submitted-user-operation.state.failed": "Başarısız", "submitted-user-operation.state.pending": "İletiliyor", "submitted-user-operation.state.rejected": "Reddedildi", "submittedTransaction.failed.title": "İşlem başarısız oldu", "success_splash.card_activated": "<PERSON><PERSON>", "supportFork.give-feedback.title": "<PERSON><PERSON> bildirim ver", "supportFork.itercom.description": "Zeal; para ya<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ve diğer tüm konularla ilgili soruları yanıtlar", "supportFork.itercom.title": "Cüzdan soruları", "supportFork.title": "<PERSON><PERSON><PERSON> kon<PERSON>u", "supportFork.zendesk.subtitle": "Gnosis Pay; kartlı ödemeler, kimlik kontrolleri ve iadelerle ilgili soruları yanıtlar", "supportFork.zendesk.title": "Kartlı ödemeler ve kimlik", "supported-networks.ethereum.warning": "Yüksek ücretler", "supportedNetworks.networks": "Desteklenen ağlar", "supportedNetworks.oneAddressForAllNetworks": "<PERSON><PERSON><PERSON> ağlar için tek adres", "supportedNetworks.receiveAnyAssets": "Tek adresle tüm ağlardan varlık al", "swap.form.error.no_routes_found": "Rota bulunamadı", "swap.form.error.not_enough_balance": "<PERSON><PERSON><PERSON>", "swaps-io-details.bank.serviceProvider": "Hizmet sağlayıcı", "swaps-io-details.details.processing": "İşleniyor", "swaps-io-details.pending": "Beklemede", "swaps-io-details.rate": "<PERSON><PERSON>", "swaps-io-details.serviceProvider": "Hizmet sağlayıcı", "swaps-io-details.transaction.from.processing": "İşlem başlatıldı", "swaps-io-details.transaction.networkFees": "<PERSON><PERSON>", "swaps-io-details.transaction.state.completed-transaction": "Tamamlanan <PERSON>", "swaps-io-details.transaction.state.started-transaction": "Başlatılan İşlem", "swaps-io-details.transaction.to.processing": "Tamamlanan <PERSON>", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Yatırma işlemi yakında tamamlanmalı. Kinetex işlemini hâlâ işliyor.", "swapsIO.monitoring.awaitingLiqSend.title": "<PERSON><PERSON><PERSON><PERSON>", "swapsIO.monitoring.awaitingRecive.title": "Aktarılıyor", "swapsIO.monitoring.awaitingSend.title": "<PERSON><PERSON><PERSON>", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Token'lar Kinetex'e gönderildi ancak yakında iade edilecek. Kinetex hedef işlemi tamamlayamadı.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "To<PERSON>'lar iade ediliyor", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Bilinmeyen bir hata nedeniyle token'lar havale edilmedi. Lütfen tekrar dene.", "swapsIO.monitoring.cancelledNoSlash.title": "To<PERSON>'lar iade edildi", "swapsIO.monitoring.cancelledSlashed.subtitle": "Token'lar iade edildi. Kinetex hedef işlemi tamamlayamadı.", "swapsIO.monitoring.cancelledSlashed.title": "To<PERSON>'lar iade edildi", "swapsIO.monitoring.completed.title": "Tamamlandı", "taker-metadata.earn": "Sky ile dijital USD kazan", "taker-metadata.earn.aave": "Aave ile dijital EUR kazan", "taker-metadata.earn.aave.cashout24": "<PERSON><PERSON><PERSON>, 7/24", "taker-metadata.earn.aave.trusted": "2+ yı<PERSON><PERSON>r 27 milyar $ ile güvenilir", "taker-metadata.earn.aave.yield": "<PERSON><PERSON> her saniye birikir", "taker-metadata.earn.chf": "Dijital CHF olarak kazan", "taker-metadata.earn.chf.cashout24": "7/24 anı<PERSON> nak<PERSON> çev<PERSON>", "taker-metadata.earn.chf.trusted": "Güvenilir: 28 <PERSON><PERSON><PERSON> Fr.", "taker-metadata.earn.chf.yield": "<PERSON><PERSON> her saniye birikir", "taker-metadata.earn.usd.cashout24": "<PERSON><PERSON><PERSON>, 7/24", "taker-metadata.earn.usd.trusted": "5+ yıldır 10,7 milyar $ ile güvenilir", "taker-metadata.earn.usd.yield": "<PERSON><PERSON> her saniye birikir", "test": "<PERSON><PERSON><PERSON><PERSON>", "to.titile": "Alınacak", "token.groupHeader.cashback": "<PERSON><PERSON><PERSON>", "token.groupHeader.title": "Varlıklar", "token.groupHeader.titleWithSum": "Varlıklar {sum}", "token.hidden_tokens.page.title": "<PERSON><PERSON><PERSON> token'lar", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "<PERSON><PERSON> ekle", "token.widget.cashback_empty": "Henüz işlem yok", "token.widget.emptyState": "Cüzdanda token yok", "tokens.cash": "Nakit", "top-up-card-from-earn-view.approve.for": "<PERSON><PERSON> hede<PERSON>", "top-up-card-from-earn-view.approve.into": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.swap.from": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.swap.to": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.withdraw.to": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn.trx.title.approval": "Takas<PERSON> onayla", "top-up-card-from-earn.trx.title.swap": "<PERSON><PERSON> e<PERSON>", "top-up-card-from-earn.trx.title.withdrawal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "topUpDapp.connectWallet": "Cüzdanı bağla", "topup-fee-breakdown.bungee-fee": "Harici sağlayıcı ücreti", "topup-fee-breakdown.header": "İşlem ücreti", "topup-fee-breakdown.network-fee": "<PERSON><PERSON>", "topup-fee-breakdown.total-fee": "Toplam ücret", "topup.continue-in-wallet": "Cüzdanında devam et", "topup.send.title": "<PERSON><PERSON><PERSON>", "topup.submit-transaction.close": "Ka<PERSON><PERSON>", "topup.submit-transaction.sent-to-wallet": "<PERSON><PERSON><PERSON> {amount}", "topup.to": "Alıcı", "topup.transaction.complete.close": "Ka<PERSON><PERSON>", "topup.transaction.complete.try-again": "<PERSON><PERSON><PERSON> dene", "transaction-request.nonce-too-low.modal.button-text": "Ka<PERSON><PERSON>", "transaction-request.nonce-too-low.modal.text": "Aynı seri numarasına (nonce) sahip bir işlem zaten tamamlandığı için bu işlemi artık gönderemezsin. Bu durum, art arda işlem yaptığında veya zaten tamamlanmış bir işlemi hızlandırmaya ya da iptal etmeye çalıştığında meydana gelebilir.", "transaction-request.nonce-too-low.modal.title": "Aynı nonce'a sahip işlem tamamlandı", "transaction-request.replaced.modal.button-text": "Ka<PERSON><PERSON>", "transaction-request.replaced.modal.text": "Bu işlemin durumunu takip edemiyoruz. İşlem başka bir işlemle değiştirilmiş veya RPC düğümünde sorunlar olabilir.", "transaction-request.replaced.modal.title": "İşlem durumu bulunamadı", "transaction.activity.details.modal.close": "Ka<PERSON><PERSON>", "transaction.cancel_popup.cancel": "Hay<PERSON><PERSON>, bekle", "transaction.cancel_popup.confirm": "<PERSON><PERSON>, durd<PERSON>", "transaction.cancel_popup.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, orijinal ücret yerine yeni bir ağ ücreti ödemen gerekir. Orijinal ücret: {oldFee}", "transaction.cancel_popup.description_without_original": "Durdurmak için yeni bir ağ ücreti ödemen gerekir", "transaction.cancel_popup.not_supported.subtitle": "İşlemleri durdurma özelliği şu ağda desteklenmiyor: {network}", "transaction.cancel_popup.not_supported.title": "Desteklenmiyor", "transaction.cancel_popup.stopping_fee": "<PERSON><PERSON> durdurma ücreti", "transaction.cancel_popup.title": "İşlem durdurulsun mu?", "transaction.in-progress": "İşlemde", "transaction.inProgress": "İşlemde", "transaction.speed_up_popup.cancel": "Hay<PERSON><PERSON>, bekle", "transaction.speed_up_popup.confirm": "Hızlandır", "transaction.speed_up_popup.description": "Eski ücret yerine yeni bir ağ ücreti öde. {amount}", "transaction.speed_up_popup.description_without_original": "Hızlandırmak için yeni bir ağ ücreti öde.", "transaction.speed_up_popup.seed_up_fee_title": "<PERSON><PERSON> <PERSON>ız<PERSON>ırma ücreti", "transaction.speed_up_popup.title": "İşlem hızlandırılsın mı?", "transaction.speedup_popup.not_supported.subtitle": "İşlem hızlandırma şu ağda desteklenmiyor: {network}", "transaction.speedup_popup.not_supported.title": "Desteklenmiyor", "transaction.subTitle.failed": "Başarısız", "transactionDetails.cashback.not-qualified": "Koşullar sağlanmadı", "transactionDetails.cashback.paid": "{amount} ödendi", "transactionDetails.cashback.pending": "{amount} beklemede", "transactionDetails.cashback.title": "<PERSON><PERSON><PERSON>", "transactionDetails.cashback.unknown": "Bilinmiyor", "transactionDetails.cashback_estimate": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "transactionDetails.category": "<PERSON><PERSON><PERSON>", "transactionDetails.exchangeRate": "Döviz kuru", "transactionDetails.location": "<PERSON><PERSON>", "transactionDetails.payment-approved": "Ödeme <PERSON>ı", "transactionDetails.payment-declined": "<PERSON><PERSON><PERSON>", "transactionDetails.payment-reversed": "Ödeme İptal Edildi", "transactionDetails.recharge.amountSentFromEarn.title": "Kazan hesabından gönderilen tutar", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "<PERSON><PERSON>", "transactionDetails.recharge.rate.title": "<PERSON><PERSON>", "transactionDetails.recharge.transactionId.title": "İşlem Kimliği", "transactionDetails.refund": "İade", "transactionDetails.reversal": "İptal", "transactionDetails.transactionCurrency": "İşlem para birimi", "transactionDetails.transactionId": "İşlem Kimliği", "transactionDetails.type": "İşlem", "transactionRequestWidget.approve.subtitle": "<PERSON><PERSON><PERSON>: {target}", "transactionRequestWidget.p2p.subtitle": "Alıcı: {target}", "transactionRequestWidget.unknown.subtitle": "Kullanılan: {target}", "transactionSafetyChecksPopup.title": "İşlem Güvenlik Kontrolleri", "transactions.main.activity.title": "Aktivite", "transactions.page.hiddenActivity.title": "Gizli aktivite", "transactions.page.title": "Aktivite", "transactions.viewTRXHistory.emptyState": "Henüz işlem yok", "transactions.viewTRXHistory.errorMessage": "İşlem geçmişini yükleyemedik.", "transactions.viewTRXHistory.hidden.emptyState": "Gizli işlem yok", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Test ağları için aktivite desteklenmiyor", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Test ağları için aktivite desteklenmiyor{br}<link>Blok gezginine git</link>", "transfer_provider": "Havale sağlayıcısı", "transfer_setup_with_different_wallet.subtitle": "Banka havaleleri farklı bir cüzdanla kurulmuş. Havaleler için sadece bir cüzdan bağlayabilirsin.", "transfer_setup_with_different_wallet.swtich_and_continue": "Değiştir ve devam et", "transfer_setup_with_different_wallet.title": "Cüzdan değiştir", "tx-sent-to-wallet.button": "Ka<PERSON><PERSON>", "tx-sent-to-wallet.subtitle": "Şurada devam et: {wallet}", "unblockProviderInfo.fees": "Aylık 5 bin $'a kadar %0, üzerindeki tutarlar için %0,2 olmak üzere mümkün olan en düşük ücretleri alırsın.", "unblockProviderInfo.registration": "Unblock, VASP takas ve saklama hizmetleri sağlamak üzere FNTT tarafından tescil edilmiş ve yetkilendirilmiştir ve US Fincen'e kayıtlı bir MSB sağlayıcısıdır. <link>Daha fazla bilgi</link>", "unblockProviderInfo.selfCustody": "Aldığın dijital nakit özel cüzdanında saklanır ve varlıkların üzerinde senden başka kimsenin kontrolü olmaz.", "unblock_invalid_faster_payment_configuration.subtitle": "Belirttiğin banka hesabı Avrupa SEPA havalelerini veya Birleşik Krallık Hızlı Ödemelerini desteklemiyor. Lütfen başka bir hesap belirt", "unblock_invalid_faster_payment_configuration.title": "Farklı bir hesap gere<PERSON>", "unknownTransaction.primaryText": "<PERSON><PERSON>lem<PERSON>", "unsupportedCountry.subtitle": "Banka havaleleri henüz ülkende mevcut değil.", "unsupportedCountry.title": "<PERSON><PERSON> mevcut değil: {country}", "update-app-popup.subtitle": "Son günce<PERSON><PERSON>, özellikler ve daha fazla si<PERSON>le dolu. En son sürüme güncelle ve Zeal deneyimini bir üst seviyeye taşı.", "update-app-popup.title": "Zeal s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "update-app-popup.update-now": "<PERSON><PERSON>", "user_associated_with_other_merchant.subtitle": "Bu cüzdan banka havaleleri için kullanılamaz. Lütfen başka bir cüzdan kullan veya destek ve güncellemeler için Discord'dan bize bildir.", "user_associated_with_other_merchant.title": "Cüzdan kullanılamaz", "user_associated_with_other_merchant.try_with_another_wallet": "Başka bir cüzdanla dene", "user_email_already_exists.subtitle": "Banka havalesini başka bir cüzdanla zaten kurmuşsun. Lütfen daha önce kullandığın cüzdanla tekrar dene.", "user_email_already_exists.title": "Havaleler farklı bir cüzdanla kurulmuş", "user_email_already_exists.try_with_another_wallet": "Başka bir cüzdanla dene", "validation.invalid.iban": "Geçersiz IBAN", "validation.required": "<PERSON><PERSON><PERSON><PERSON>", "validation.required.first_name": "Ad gere<PERSON>", "validation.required.iban": "IBAN gerekli", "validation.required.last_name": "Soyadı gerekli", "verify-passkey.cta": "Geçiş anahtarını doğrula", "verify-passkey.subtitle": "Geçiş anahtarının oluşturulduğunu ve düzgün bir şekilde güvenceye alındığını doğrula.", "verify-passkey.title": "Geçiş anahtarını doğrula", "view-cashback.cashback-next-cycle": " <PERSON><PERSON>i nakit iade oranı {time}", "view-cashback.no-cashback": "%0", "view-cashback.no-cashback.subtitle": "Na<PERSON>t iadesi için para yatır", "view-cashback.pending": "{money} <PERSON><PERSON><PERSON><PERSON>", "view-cashback.pending-rewards.not_paid": "Alınacak: {days}g", "view-cashback.pending-rewards.paid": "Bu hafta alındı", "view-cashback.received-rewards": "<PERSON><PERSON><PERSON>", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Onaylanmamış ödemeler", "view-cashback.upcoming": "<PERSON><PERSON><PERSON><PERSON> {money}", "virtual-card-order.configure-safe.loading-text": "<PERSON><PERSON> o<PERSON>ulu<PERSON>", "virtual-card-order.create-order.loading-text": "<PERSON><PERSON><PERSON>", "virtual-card-order.create-order.success-text": "<PERSON><PERSON>", "virtualCard.activateCard": "Kartı etkinleştir", "walletDeleteConfirm.main_action": "Kaldır", "walletDeleteConfirm.subtitle": "Portföyü görüntülemek veya işlem yapmak için tekrar içe aktarman gerekecek", "walletDeleteConfirm.title": "<PERSON>üzdan kaldırılsın mı?", "walletSetting.header": "Cüzdan Ayarları", "wallet_connect.connect.cancel": "İptal", "wallet_connect.connect.connect_button": "Zeal Bağlan", "wallet_connect.connect.title": "Bağlan", "wallet_connect.connected.title": "Bağlandı", "wallet_connect_add_chain_missing.title": "<PERSON>ğ desteklenmiyor", "wallet_connect_proposal_expired.title": "Bağlantının süresi doldu", "withdraw": "Çek", "withdraw.confirmation.close": "İptal", "withdraw.confirmation.continue": "<PERSON><PERSON><PERSON>", "withdrawal_request.completed": "Tamamlandı", "withdrawal_request.pending": "Beklemede", "zeal-dapp.connect-wallet.cta.primary.connecting": "Bağlanılıyor...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Bağlan", "zeal-dapp.connect-wallet.cta.secondary": "İptal", "zeal-dapp.connect-wallet.title": "Devam etmek için c<PERSON>nını bağla", "zealSmartWalletInfo.gas": "Ağ ücretlerini birçok token ile öde; ağ ücretleri için sadece yerel token'lar<PERSON> de<PERSON><PERSON>, desteklenen zincirlerdeki popüler ERC20 token'lar<PERSON><PERSON><PERSON> kullan.", "zealSmartWalletInfo.recover": "G<PERSON>li İfade Yok; <PERSON><PERSON><PERSON>, iCloud veya Google hesabından biyometrik geçiş anahtarı kullanarak kurtar.", "zealSmartWalletInfo.selfCustodial": "Tamamen özel cüzdan; Merkezi bağımlılıkları en aza indirmek için Geçiş Anahtarı imzaları zincir üzerinde doğrulanır.", "zealSmartWalletInfo.title": "Zeal smart wallet'lar hakkında", "zeal_a_rewards_already_claimed_error.title": "Öd<PERSON>l zaten alınmış", "zwidget.minimizedDisconnected.label": "Zeal Bağlı Değil"}