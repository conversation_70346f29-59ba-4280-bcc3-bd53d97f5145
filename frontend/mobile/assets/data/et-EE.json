{"Account.ListItem.details.label": "<PERSON>ail<PERSON>", "AddFromAddress.success": "<PERSON><PERSON><PERSON>", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{<PERSON><PERSON><PERSON> pole} one{{count} rahakott} other{{count} rahakotti}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Salafraas {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Loo uusi rahakotte ühest oma olemasolevast salafraasist", "AddFromExistingSecretPhrase.SelectPhrase.title": "<PERSON><PERSON>", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "<PERSON>u salafraas saab varundada mitu rahakotti. <PERSON><PERSON> need, mida soovid kasutada.", "AddFromExistingSecretPhrase.WalletSelection.title": "<PERSON> kii<PERSON> r<PERSON>", "AddFromExistingSecretPhrase.success": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "AddFromHardwareWallet.subtitle": "Vali oma rii<PERSON><PERSON><PERSON><PERSON>, et Zealiga ühenduda", "AddFromHardwareWallet.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.WalletSelection.subtitle": "<PERSON>i rah<PERSON>otid, mida soovid importida", "AddFromNewSecretPhrase.WalletSelection.title": "<PERSON><PERSON><PERSON> rahakotid", "AddFromNewSecretPhrase.accounts": "<PERSON><PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "Salaf<PERSON>as on nagu võtmehoidja miljonitele rahakottidele, millest igaühel on unikaalne privaatvõti.{br}{br}Saad importida nii palju rahakotte kui soovid, kas kohe või hilje<PERSON>.", "AddFromNewSecretPhrase.secretPhraseTip.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> rah<PERSON>", "AddFromNewSecretPhrase.subtitle": "Sisesta oma sala<PERSON> tühikutega eraldatult", "AddFromNewSecretPhrase.success_secret_phrase_added": "Salafraas lisatud 🎉", "AddFromNewSecretPhrase.success_wallets_added": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON>", "AddFromNewSecretPhrase.wallets": "<PERSON><PERSON><PERSON><PERSON>", "AddFromPrivateKey.subtitle": "Sisesta oma privaatvõti", "AddFromPrivateKey.success": "Privaatvõti lisatud 🎉", "AddFromPrivateKey.title": "<PERSON><PERSON><PERSON>", "AddFromPrivateKey.typeOrPaste": "<PERSON><PERSON><PERSON><PERSON> või k<PERSON> siia", "AddFromSecretPhrase.importWallets": "{count,plural,=0{<PERSON><PERSON><PERSON> pole valitud} one{Impordi rahakott} other{Impordi {count} rahakotti}}", "AddFromTrezor.AccountSelection.title": "<PERSON><PERSON><PERSON> r<PERSON>", "AddFromTrezor.hwWalletTip.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rahakott sisaldab miljon<PERSON>d erinevate aadressidega rahakotte. Saad importida nii palju rahakotte kui soovid, kas kohe või hilje<PERSON>.", "AddFromTrezor.hwWalletTip.title": "Importimine rii<PERSON>varalisest rahakotist", "AddFromTrezor.importAccounts": "{count,plural,=0{<PERSON><PERSON><PERSON> pole valitud} one{Impordi rahakott} other{Impordi {count} rahakotti}}", "AddFromTrezor.success": "<PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON>", "ApprovalSpenderTypeCheck.failed.subtitle": "Tõenäoliselt pettus: kulutaja peaks olema leping", "ApprovalSpenderTypeCheck.failed.title": "<PERSON><PERSON><PERSON> on r<PERSON><PERSON><PERSON>, mitte leping", "ApprovalSpenderTypeCheck.passed.subtitle": "Taval<PERSON><PERSON> annad varadele loa lepingutele", "ApprovalSpenderTypeCheck.passed.title": "<PERSON><PERSON><PERSON> on nutileping", "BestReturns.subtitle": "See v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> annab sulle su<PERSON><PERSON> v<PERSON>, arvestad<PERSON> kõ<PERSON> ta<PERSON>.", "BestReturnsPopup.title": "<PERSON><PERSON>", "BlacklistCheck.Failed.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> teated teenuselt <source></source>", "BlacklistCheck.Failed.title": "Sait on mustas nimeki<PERSON>", "BlacklistCheck.Passed.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> teateid pole teenuselt <source></source>", "BlacklistCheck.Passed.title": "Sait ei ole mustas ni<PERSON>", "BlacklistCheck.failed.statusButton.label": "Sait on märg<PERSON><PERSON> oh<PERSON>s", "BridgeRoute.slippage": "Slippage {slippage}", "BridgeRoute.title": "Bridge'i pakkuja", "CheckConfirmation.InProgress": "Teostamisel...", "CheckConfirmation.success.splash": "Val<PERSON>", "ChooseImportOrCreateSecretPhrase.subtitle": "I<PERSON>rdi sala<PERSON>as või loo uus", "ChooseImportOrCreateSecretPhrase.title": "<PERSON>", "ConfirmTransaction.Simuation.Skeleton.title": "Teen turvakontrolli...", "ConnectionSafetyCheckResult.passed": "Ohutuskontroll läbitud", "ContactGnosisPaysupport": "Gnosis Pay tugi", "CopyKeyButton.copied": "Kopeeritud", "CopyKeyButton.copyYourKey": "<PERSON><PERSON><PERSON> v<PERSON>", "CopyKeyButton.copyYourPhrase": "<PERSON><PERSON><PERSON> o<PERSON> f<PERSON>as", "DAppVerificationCheck.Failed.subtitle": "Sait ei ole loetletud registris <source></source>", "DAppVerificationCheck.Failed.title": "Saiti ei leitud rakenduste registritest", "DAppVerificationCheck.Passed.subtitle": "Sait on loetletud registris <source></source>", "DAppVerificationCheck.Passed.title": "Sait on rakenduste registrites", "DAppVerificationCheck.failed.statusButton.label": "Saiti ei leitud rakenduste registritest", "ERC20.tokens.emptyState": "Me ei leidnud <PERSON>d", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Prioriteeditasu", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Viimati {period}: vah<PERSON><PERSON><PERSON> {from} kuni {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Põhitasu: {baseFee} • Turvapuhver: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Praegust p<PERSON> ei <PERSON> hankida", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "<PERSON><PERSON><PERSON><PERSON> kui järg<PERSON>. <PERSON><PERSON><PERSON><PERSON> kinni", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "Nonce ei saa olla madalam kui praegune", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "Prioriteeditasu arvutamine ebaõnnestus", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "<PERSON>rae<PERSON><PERSON> maksim<PERSON><PERSON> ei <PERSON> hankida", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "<PERSON><PERSON><PERSON> kin<PERSON> j<PERSON>, kuni võrgutasud lange<PERSON>d", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.Custom.gasLimit.title": "Gasi limiit {gasLimit}", "EditFeeModal.Custom.title": "<PERSON><PERSON><PERSON><PERSON> seaded", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "<PERSON><PERSON><PERSON><PERSON>, kuni p<PERSON><PERSON><PERSON> langeb", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Madal tasu. <PERSON><PERSON><PERSON> kinni j<PERSON>", "EditFeeModal.EditGasLimit.estimatedGas": "Hinnanguline gas: {estimated} • Turvapuhver: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "<PERSON><PERSON><PERSON> kui hinnanguline limiit. <PERSON><PERSON> e<PERSON>", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Vä<PERSON> kui soovitatud limiit. Te<PERSON> võib ebaõnnestuda", "EditFeeModal.EditGasLimit.subtitle": "<PERSON><PERSON><PERSON><PERSON> maksima<PERSON>ne gasi kogus, mida see tehing kasutab. <PERSON><PERSON> ebaõnnestub, kui limiit on vajalikust väiksem.", "EditFeeModal.EditGasLimit.title": "Muuda gasi limiiti", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Vähem kui minimaalne võrgutasu limiit: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "<PERSON><PERSON><PERSON><PERSON> kui järg<PERSON>. <PERSON><PERSON><PERSON><PERSON> kinni", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "Noncet ei saa määrata praegusest madalamaks", "EditFeeModal.EditNonce.subtitle": "<PERSON><PERSON> j<PERSON><PERSON><PERSON> kin<PERSON>, kui sa ei määra järg<PERSON>i", "EditFeeModal.EditNonce.title": "<PERSON><PERSON>i", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "<PERSON><PERSON> on {amount} saatmiseks", "EditFeeModal.Header.Time.unknown": "Aeg teadmata", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Maks<PERSON>alne {fee}", "EditFeeModal.Header.fee.unknown": "<PERSON><PERSON> teadmata", "EditFeeModal.Header.subsequent_failed": "<PERSON><PERSON><PERSON><PERSON> võ<PERSON><PERSON> o<PERSON>, vii<PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "EditFeeModal.Layout.Header.ariaLabel": "<PERSON><PERSON><PERSON><PERSON> tasu", "EditFeeModal.MaxFee.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> tasu on suurim summa, mida tehingu eest maksad, kuid tavaliselt maksad prognoositud tasu. See lisapuhver aitab tehingul läbi minna, isegi kui võrk aeglustub või muutub kallimaks.", "EditFeeModal.MaxFee.title": "Maksimaalne võrgutasu", "EditFeeModal.SelectPreset.Time.unknown": "Aeg teadmata", "EditFeeModal.SelectPreset.ariaLabel": "<PERSON>i tasu eelseade", "EditFeeModal.SelectPreset.fast": "<PERSON><PERSON>", "EditFeeModal.SelectPreset.normal": "Tavaline", "EditFeeModal.SelectPreset.slow": "Aeglane", "EditFeeModal.ariaLabel": "Muuda võrgutasu", "FailedSimulation.Confirmation.Item.subtitle": "Tekkis sisemine viga", "FailedSimulation.Confirmation.Item.title": "<PERSON><PERSON><PERSON> simu<PERSON><PERSON><PERSON> e<PERSON>", "FailedSimulation.Confirmation.subtitle": "Oled kindel, et soovid jätkata?", "FailedSimulation.Confirmation.title": "Sa allkirjastad pimesi", "FailedSimulation.Title": "Simulatsiooni viga", "FailedSimulation.footer.subtitle": "Tekkis sisemine viga", "FailedSimulation.footer.title": "<PERSON><PERSON><PERSON> simu<PERSON><PERSON><PERSON> e<PERSON>", "FeeForecastWidget.NotEnoughBalance.errorMessage": "<PERSON><PERSON> on {amount} te<PERSON>u esitamiseks", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Töötlemine võib kaua aega võtta", "FeeForecastWidget.networkFee": "Võrgutasu", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Võrgutasu ei õnnestunud arvu<PERSON>a", "FeeForecastWidget.subsequentFailed.message": "<PERSON><PERSON><PERSON><PERSON> võ<PERSON><PERSON>, vii<PERSON><PERSON> v<PERSON>rsk<PERSON><PERSON> e<PERSON>", "FeeForecastWidget.unknownDuration": "Teadmata", "FeeForecastWidget.unknownFee": "Teadmata", "GasCurrencySelector.balance": "Saldo: {balance}", "GasCurrencySelector.networkFee": "Võrgutasu", "GasCurrencySelector.payNetworkFeesUsing": "Võrgutasude maksmine", "GasCurrencySelector.removeDefaultGasToken.description": "<PERSON><PERSON>a tasud su<PERSON> saldo<PERSON>", "GasCurrencySelector.removeDefaultGasToken.title": "Automaatne tasude haldamine", "GasCurrencySelector.save": "Salvesta", "GoogleDriveBackup.BeforeYouBegin.first_point": "<PERSON>i un<PERSON>an oma <PERSON>, kaotan oma varad i<PERSON>s", "GoogleDriveBackup.BeforeYouBegin.second_point": "Kui kaotan juurdepääsu oma Google Drive'ile või muudan taastefaili, kaotan oma varad igaveseks", "GoogleDriveBackup.BeforeYouBegin.subtitle": "<PERSON><PERSON><PERSON> mõista ja nõustu järgmise punktiga privaatse rahakoti kohta:", "GoogleDriveBackup.BeforeYouBegin.third_point": "Zeal ei saa aidata mul taastada minu Zeali parooli ega juurdepääsu Google Drive'ile", "GoogleDriveBackup.BeforeYouBegin.title": "<PERSON><PERSON>", "GoogleDriveBackup.loader.subtitle": "Kinnita Google Drive'is taastefaili üleslaadimise taotlus", "GoogleDriveBackup.loader.title": "Ootan kinnitust...", "GoogleDriveBackup.success": "Varundamine õnnestus 🎉", "MonitorOffRamp.overServiceTime": "Enamik <PERSON> te<PERSON> {estimated_time} jook<PERSON>, kuid mõnikord võib see täiendavate kontrollide tõttu kauem aega võtta. See on normaalne ja sinu raha on kontrollide tegemise ajal turvaliselt hoitud.{br}{br}Kui tehing ei lõpe {support_soft_deadline} jooksul, siis palun {contact_support}", "MonitorOnRamp.contactSupport": "Võta ühend<PERSON> toega", "MonitorOnRamp.from": "<PERSON><PERSON>", "MonitorOnRamp.fundsReceived": "<PERSON><PERSON>", "MonitorOnRamp.overServiceTime": "Enamik <PERSON> te<PERSON> {estimated_time} jook<PERSON>, kuid vahel võib lisakontrollide tõttu kauem aega minna. See on tavapärane ja sinu raha on kontrolli ajal turvaliselt hoitud.{br}{br}<PERSON>i tehing ei lõ<PERSON> {support_soft_deadline} jook<PERSON>, palun {contact_support}", "MonitorOnRamp.sendingToYourWallet": "<PERSON><PERSON>n sinu rahakotti", "MonitorOnRamp.to": "<PERSON><PERSON>", "MonitorOnRamp.waitingForTransfer": "Ootan sinu ülekannet", "NftCollectionCheck.failed.subtitle": "Kollektsioon ei ole kinnitatud asuk<PERSON> <source></source>", "NftCollectionCheck.failed.title": "Kollektsioon ei ole kinnitatud", "NftCollectionCheck.passed.subtitle": "Kollektsioon on kinnitatud asukohas <source></source>", "NftCollectionCheck.passed.title": "Kollektsioon on kinnitatud", "NftCollectionInfo.entireCollection": "<PERSON><PERSON> k<PERSON>", "NoSigningKeyStore.createAccount": "Loo konto", "NonceRangeError.biggerThanCurrent.message": "<PERSON><PERSON> j<PERSON><PERSON> kinni", "NonceRangeError.lessThanCurrent.message": "<PERSON><PERSON>", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "<PERSON>ce on praegusest Nonce'ist kõrgem. Vähenda Non<PERSON>'i, et vältida tehingu kinnijäämist.", "NonceRangeErrorPopup.biggerThanCurrent.title": "<PERSON><PERSON> j<PERSON><PERSON> kinni", "P2pReceiverTypeCheck.failed.subtitle": "Kas saadad <PERSON> aadressile?", "P2pReceiverTypeCheck.failed.title": "<PERSON><PERSON> on nutileping, mitte rahakott", "P2pReceiverTypeCheck.passed.subtitle": "<PERSON><PERSON><PERSON><PERSON> saadad varasid teistele r<PERSON>", "P2pReceiverTypeCheck.passed.title": "<PERSON><PERSON> on rah<PERSON><PERSON>", "PasswordCheck.title": "<PERSON><PERSON><PERSON> parool", "PasswordChecker.subtitle": "<PERSON><PERSON><PERSON> si<PERSON>ta oma parool, et sa<PERSON><PERSON> veenduda, et see oled sina", "PermitExpirationCheck.failed.subtitle": "<PERSON><PERSON> lü<PERSON>ke ja ainult nii kaua, kui vaja", "PermitExpirationCheck.failed.title": "Pikk aegumistähtaeg", "PermitExpirationCheck.passed.subtitle": "<PERSON>i kaua rakendus sinu tokeneid kasutada saab", "PermitExpirationCheck.passed.title": "Aegumistähtaeg pole liiga pikk", "PrivateKeyValidationError.moreThanMaximumWords": "Max {count} s<PERSON>na", "PrivateKeyValidationError.notValidPrivateKey": "See ei ole kehtiv privaatvõti", "PrivateKeyValidationError.secretPhraseIsInvalid": "Salafraas pole kehtiv", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Sõna #{index} on valesti kirjutatud või vigane", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} sõna} other{{count} sõna}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Salafraasid ja privaatvõtmed krüpteeritakse ja need ei lahku kunagi sellest seadmest", "SecretPhraseReveal.header": "<PERSON><PERSON><PERSON><PERSON>", "SecretPhraseReveal.hint": "Ära jaga oma fraasi kellegagi. Hoia seda turvaliselt ja võrguväliselt", "SecretPhraseReveal.skip.subtitle": "<PERSON>ad seda ka hiljem teha, aga kui kaotad seadme enne fra<PERSON>, kaotad k<PERSON>ik varad selles rahakotis.", "SecretPhraseReveal.skip.takeTheRisk": "<PERSON><PERSON><PERSON>", "SecretPhraseReveal.skip.title": "<PERSON><PERSON><PERSON> fraasi <PERSON> k<PERSON>?", "SecretPhraseReveal.skip.writeDown": "<PERSON><PERSON><PERSON><PERSON>", "SecretPhraseReveal.skipForNow": "<PERSON><PERSON><PERSON> v<PERSON>", "SecretPhraseReveal.subheader": "<PERSON><PERSON><PERSON> kirjuta see <PERSON>les ja hoia turvaliselt võrguväliselt. Seejärel palume sul seda kinnitada.", "SecretPhraseReveal.verify": "<PERSON><PERSON><PERSON>", "SelectCurrency.tokens": "<PERSON><PERSON><PERSON>", "SelectCurrency.tokens.emptyState": "Me ei leidnud <PERSON>d", "SelectRoute.slippage": "Libisemine {slippage}", "SelectRoutes.emptyState": "<PERSON><PERSON> v<PERSON>e jaoks ei leitud mars<PERSON>ute", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "<PERSON><PERSON><PERSON>", "SendERC20.labelAddress.inputPlaceholder": "Rahakoti silt", "SendERC20.labelAddress.subtitle": "Lisa rahakotile silt, et see hiljem üles leida.", "SendERC20.labelAddress.title": "Sildista see rah<PERSON><PERSON>", "SendERC20.send_to": "<PERSON><PERSON>", "SendERC20.tokens": "<PERSON><PERSON><PERSON>", "SendOrReceive.bankTransfer.primaryText": "Panga<PERSON><PERSON><PERSON><PERSON>", "SendOrReceive.bankTransfer.shortText": "<PERSON><PERSON><PERSON> ja kohene sisse- ja v<PERSON><PERSON>e", "SendOrReceive.bridge.primaryText": "Bridge", "SendOrReceive.bridge.shortText": "Tokenite ülekanne võrkude vahel", "SendOrReceive.receive.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.receive.shortText": "Tokenite ja kogutavate esemete vastuvõtt", "SendOrReceive.send.primaryText": "Saada", "SendOrReceive.send.shortText": "<PERSON><PERSON>d mis tahes aadressile", "SendOrReceive.swap.primaryText": "Vaheta", "SendOrReceive.swap.shortText": "Vaheta tokenite vahel", "SendSafeTransaction.Confirm.loading": "Teen ohutuskontrolli...", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Krüpteeri taastefail parooliga", "SetupRecoveryKit.google.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "SetupRecoveryKit.google.title": "Google Drive'i varukoopia", "SetupRecoveryKit.subtitle": "Sul on vaja vähemalt üht viisi oma konto taastamiseks, kui e<PERSON>ldad Zeali või vahetad seadet", "SetupRecoveryKit.title": "Seadista taastekomplekt", "SetupRecoveryKit.writeDown.subtitle": "<PERSON><PERSON><PERSON><PERSON>", "SetupRecoveryKit.writeDown.title": "Kä<PERSON>tsi varundamine", "Sign.CheckSafeDeployment.activate": "Aktiveeri", "Sign.CheckSafeDeployment.subtitle": "Enne kui saad rakendusse sisse logida või ahelavälist sõnumit allkirjastada, pead oma seadme selles võrgus aktiveerima. See toim<PERSON> pärast nutika rahakoti installimist võ<PERSON> taastamist.", "Sign.CheckSafeDeployment.title": "Aktiveeri seade selles võrgus", "Sign.Simuation.Skeleton.title": "Teen ohutuskontrolli...", "SignMessageSafetyCheckResult.passed": "Ohutuskontroll läbitud", "SignMessageSafetyChecksPopup.title.permits": "Permit'i ohutuskontroll", "SimulationFailedConfirmation.subtitle": "Simuleerisime seda tehingut ja leidsime vea, mis põhjustaks selle ebaõnnestumise. Võid tehingu esitada, kuid see tõenäoliselt ebaõnnestub ja võid kaotada võrgutasu.", "SimulationFailedConfirmation.title": "<PERSON><PERSON> tõenäoliselt ebaõnnestub", "SimulationNotSupported.Title": "Simulatsioon pole{br}<PERSON><PERSON><PERSON> v<PERSON>{br}{network}", "SimulationNotSupported.footer.subtitle": "<PERSON>ad selle tehingu siiski esitada", "SimulationNotSupported.footer.title": "Si<PERSON><PERSON><PERSON>ine pole toetatud", "SlippagePopup.custom": "<PERSON><PERSON><PERSON><PERSON>", "SlippagePopup.presetsHeader": "Vahetuse libisemine", "SlippagePopup.title": "Libisemise seaded", "SmartContractBlacklistCheck.failed.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> teated allikalt <source></source>", "SmartContractBlacklistCheck.failed.title": "Leping on mustas nime<PERSON>", "SmartContractBlacklistCheck.passed.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> teateid pole allikalt <source></source>", "SmartContractBlacklistCheck.passed.title": "Leping ei ole mustas ni<PERSON>", "SuspiciousCharactersCheck.Failed.subtitle": "See on le<PERSON><PERSON> andmepüügi taktika", "SuspiciousCharactersCheck.Failed.title": "Kont<PERSON><PERSON> le<PERSON>ud and<PERSON><PERSON><PERSON><PERSON><PERSON> must<PERSON>id", "SuspiciousCharactersCheck.Passed.subtitle": "Kontrollime andmepüügi kats<PERSON>d", "SuspiciousCharactersCheck.Passed.title": "<PERSON><PERSON><PERSON><PERSON> pole ebatavalisi märke", "SuspiciousCharactersCheck.failed.statusButton.label": "<PERSON><PERSON><PERSON><PERSON> on ebatavalisi märke ", "TokenVerificationCheck.failed.subtitle": "Token pole loetletud asukoh<PERSON> <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} ei ole CoinGecko poolt kinnitatud", "TokenVerificationCheck.passed.subtitle": "Token on loetlet<PERSON> asukohas <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} on CoinGecko poolt kinnitatud", "TopupDapp.MonitorTransaction.success.splash": "Val<PERSON>", "TransactionSafetyCheckResult.passed": "Ohutuskontroll läbitud", "TransactionSimulationCheck.failed.subtitle": "Viga: {errorMessage}", "TransactionSimulationCheck.failed.title": "<PERSON><PERSON> tõenäoliselt ebaõnnestub", "TransactionSimulationCheck.passed.subtitle": "Simulatsioon tehtud kasutades <source></source>", "TransactionSimulationCheck.passed.title": "<PERSON><PERSON><PERSON> e<PERSON> oli edukas", "TrezorError.trezor_action_cancelled.action": "Sulge", "TrezorError.trezor_action_cancelled.subtitle": "Lükkasid te<PERSON>u oma rii<PERSON>varalises rahakotis tagasi.", "TrezorError.trezor_device_used_elsewhere.action": "<PERSON>ünk<PERSON><PERSON> Trezor", "TrezorError.trezor_device_used_elsewhere.subtitle": "<PERSON><PERSON> kindlasti kõik muud avatud se<PERSON>id ja proovi Trezorit uuesti sünkroonida.", "TrezorError.trezor_method_cancelled.action": "<PERSON>ünk<PERSON><PERSON> Trezor", "TrezorError.trezor_method_cancelled.subtitle": "<PERSON><PERSON><PERSON>, et lubad Trezoril rahakotid Zeali eksportida.", "TrezorError.trezor_permissions_not_granted.action": "<PERSON>ünk<PERSON><PERSON> Trezor", "TrezorError.trezor_permissions_not_granted.subtitle": "<PERSON><PERSON><PERSON> anna <PERSON> luba n<PERSON>ha k<PERSON> rah<PERSON>.", "TrezorError.trezor_pin_cancelled.action": "<PERSON>ünk<PERSON><PERSON> Trezor", "TrezorError.trezor_pin_cancelled.subtitle": "<PERSON><PERSON><PERSON> t<PERSON>i seadmes", "TrezorError.trezor_popup_closed.action": "<PERSON>ünk<PERSON><PERSON> Trezor", "TrezorError.trezor_popup_closed.subtitle": "<PERSON><PERSON><PERSON> dialoog sulgus ootamatult", "TrxLikelyToFail.lessThanEstimatedGas.message": "<PERSON><PERSON>", "TrxLikelyToFail.lessThanMinimumGas.message": "<PERSON><PERSON>", "TrxLikelyToFail.lessThanSuggestedGas.message": "Tõenäoliselt ebaõnnestub", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "Tehingu gasi limiit on liiga madal. Ebaõnnestumise vältimiseks tõsta gasi limiit soovitatud tasemele.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "<PERSON><PERSON> tõenäoliselt ebaõnnestub", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Gasi limiit on hinnangulisest madalam. Tõsta gasi limiit soovitatud tasemele.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "<PERSON><PERSON>", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "Maks<PERSON><PERSON>ne baastasu on praegusest baastasust madalam. Tehingu kinnijäämise vältimiseks tõsta maksimaalset baastasu.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "<PERSON><PERSON> j<PERSON><PERSON> kinni", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "<PERSON><PERSON><PERSON> maksimaalne tasu on liiga madal. Te<PERSON>u kinnijäämise vältimiseks tõsta maksimaalset tasu.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "<PERSON><PERSON> j<PERSON><PERSON> kinni", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Prioriteeditasu on soovitatust madalam. Tehingu kiirendamiseks tõsta prioriteeditasu.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "Tehingu lõpuleviimine võib kaua aega võtta", "UnsupportedMobileNetworkLayout.gotIt": "Sain aru!", "UnsupportedMobileNetworkLayout.subtitle": "Sa ei saa teha tehinguid ega allkirjastada sõnumeid võrgus ID-ga {networkHexId} Zeali mobiiliversiooniga veel{br}{br}Mine üle brauserilaiendusele, et saaksid selles võrgus tehinguid teha, sel ajal kui me töötame usinalt selle võrgu toe lisamise kallal 🚀", "UnsupportedMobileNetworkLayout.title": "Võrk ei ole Zeali mobiiliversioonis toe<PERSON>ud", "UnsupportedSafeNetworkLayout.subtitle": "Sa ei saa teha tehinguid ega allkirjastada sõnumeid võrgus {network} Zeali nutika rahakotiga.{br}{br}Vaheta toetatud võrku või kasuta Legacy rahakotti.", "UnsupportedSafeNetworkLayoutk.title": "Võrk ei ole nutika rahakoti jaoks toetatud", "UserConfirmationPopup.goBack": "<PERSON><PERSON><PERSON><PERSON>", "UserConfirmationPopup.submit": "<PERSON><PERSON><PERSON>", "ViewPrivateKey.header": "Privaatvõti", "ViewPrivateKey.hint": "Ära jaga oma privaatvõtit kellegagi. Hoia seda turvaliselt ja võrguväliselt", "ViewPrivateKey.subheader.mobile": "<PERSON><PERSON><PERSON><PERSON>, et näha oma privaatvõtit", "ViewPrivateKey.subheader.web": "<PERSON><PERSON><PERSON><PERSON><PERSON>, et näha oma privaatvõtit", "ViewPrivateKey.unblur.mobile": "Puuduta nägemiseks", "ViewPrivateKey.unblur.web": "Hõljuta nägemiseks", "ViewSecretPhrase.PasswordChecker.subtitle": "<PERSON><PERSON><PERSON> oma parool, et krüpteerida taastefail. Pead selle tulevikus meeles pidama.", "ViewSecretPhrase.done": "Val<PERSON>", "ViewSecretPhrase.header": "<PERSON><PERSON><PERSON> f<PERSON>as", "ViewSecretPhrase.hint": "Ära jaga oma fraasi kellegagi. Hoia seda turvaliselt ja võrguväliselt", "ViewSecretPhrase.subheader.mobile": "<PERSON><PERSON><PERSON><PERSON>, et näha oma salajast fraasi", "ViewSecretPhrase.subheader.web": "<PERSON><PERSON><PERSON><PERSON><PERSON>, et näha oma salajast fraasi", "ViewSecretPhrase.unblur.mobile": "Puuduta nägemiseks", "ViewSecretPhrase.unblur.web": "Hõljuta nägemiseks", "account-details.monerium": "Ülekan<PERSON><PERSON> teeb Monerium, reguleeritud EMI. <link><PERSON><PERSON> lisaks</link>", "account-details.unblock": "Ülekandeid teostatakse Unblocki kaudu, mis on volitatud ja registreeritud vahetus- ja hoiustamisteenuse pakkuja. <link>Loe lisaks</link>", "account-selector.empty-state": "<PERSON><PERSON><PERSON> ei leitud", "account-top-up.select-currency.title": "<PERSON><PERSON><PERSON>", "account.accounts_not_found": "Me ei leidnud <PERSON>ht<PERSON> rah<PERSON>i", "account.accounts_not_found_search_valid_address": "Seda rahakotti pole sinu nimekirjas", "account.add.create_new_secret_phrase": "<PERSON><PERSON> sa<PERSON>", "account.add.create_new_secret_phrase.subtext": "Uus 12-<PERSON><PERSON><PERSON><PERSON>", "account.add.fromRecoveryKit.fileNotFound": "Me ei leidnud sinu faili", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "<PERSON>ovi uuesti", "account.add.fromRecoveryKit.fileNotFound.explanation": "<PERSON><PERSON><PERSON><PERSON>, et oled sisse loginud <PERSON> k<PERSON>, kus on Zeal Backup kaust", "account.add.fromRecoveryKit.fileNotValid": "Taastefail pole kehtiv", "account.add.fromRecoveryKit.fileNotValid.explanation": "Kontrollisime sinu faili ja see on vale tüüpi või seda on muudetud", "account.add.import_secret_phrase": "<PERSON><PERSON><PERSON>", "account.add.import_secret_phrase.subtext": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> mujal", "account.add.select_type.add_hardware_wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "account.add.select_type.existing_smart_wallet": "Olemasolev Smart Wallet", "account.add.select_type.private_key": "Privaatvõti", "account.add.select_type.seed_phrase": "<PERSON><PERSON><PERSON><PERSON>", "account.add.select_type.title": "<PERSON><PERSON><PERSON> r<PERSON>", "account.add.select_type.zeal_recovery_file": "<PERSON><PERSON><PERSON>", "account.add.success.title": "<PERSON><PERSON> rahakott loodud 🎉", "account.addLabel.header": "<PERSON><PERSON><PERSON> o<PERSON> r<PERSON>", "account.addLabel.labelError.labelAlreadyExist": "<PERSON><PERSON> on juba kasutusel. <PERSON><PERSON>i teist nime", "account.addLabel.labelError.maxStringLengthExceeded": "Märkide limiit on täis", "account.add_active_wallet.primary_text": "<PERSON>", "account.add_active_wallet.short_text": "<PERSON><PERSON>, <PERSON><PERSON>a või impordi rahakott", "account.add_from_ledger.success": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "account.add_tracked_wallet.primary_text": "<PERSON> v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "account.add_tracked_wallet.short_text": "<PERSON><PERSON>ta port<PERSON>i ja tegevusi", "account.button.unlabelled-wallet": "<PERSON><PERSON><PERSON>", "account.create_wallet": "<PERSON><PERSON> rah<PERSON><PERSON>", "account.label.edit.title": "<PERSON><PERSON> rahakoti nime", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON> {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Taastefail pole kehtiv", "account.recoveryKit.selectBackupFile.subtitle": "<PERSON><PERSON> ta<PERSON>, mida soovid taastada", "account.recoveryKit.selectBackupFile.title": "Taastefail", "account.recoveryKit.success.recoveryFileFound": "<PERSON>ast<PERSON><PERSON> leitud 🎉", "account.select_type_of_account.create_eoa.short": "Pärandrahakott ekspertidele", "account.select_type_of_account.create_eoa.title": "<PERSON><PERSON> sala<PERSON><PERSON><PERSON> rah<PERSON>", "account.select_type_of_account.create_safe_wallet.title": "Loo Smart Wallet", "account.select_type_of_account.existing_smart_wallet": "Olemasolev Smart Wallet", "account.select_type_of_account.hardware_wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "account.select_type_of_account.header": "<PERSON>", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Privaatvõti / Seemnefraas", "account.select_type_of_account.read_only_wallet": "<PERSON>ult vaatam<PERSON><PERSON> rah<PERSON>", "account.select_type_of_account.read_only_wallet.short": "Vaata mis tahes portfelli", "account.topup.title": "<PERSON> r<PERSON>", "account.view.error.refreshAssets": "Värskenda", "account.widget.refresh": "Värskenda", "account.widget.settings": "Seaded", "accounts.view.copied-text": "Kopeeritud {formattedAddress}", "accounts.view.copiedAddress": "Kopeeritud {formattedAddress}", "action.accept": "Nõustu", "action.accpet": "Nõustu", "action.allow": "Luba", "action.back": "Tagasi", "action.cancel": "<PERSON><PERSON><PERSON><PERSON>", "action.card-activation.title": "<PERSON><PERSON><PERSON><PERSON> kaart", "action.claim": "Lunasta", "action.close": "Sule", "action.complete-steps": "<PERSON><PERSON><PERSON> sa<PERSON>", "action.confirm": "<PERSON><PERSON><PERSON>", "action.continue": "Jätka", "action.copy-address-understand": "OK - <PERSON><PERSON><PERSON> aadress", "action.deposit": "<PERSON><PERSON>", "action.done": "Val<PERSON>", "action.dontAllow": "<PERSON><PERSON> luba", "action.edit": "muuda", "action.email-required": "Sisesta e-post", "action.enterPhoneNumber": "<PERSON><PERSON>ta telefoninumber", "action.expand": "<PERSON><PERSON><PERSON>", "action.fix": "<PERSON><PERSON>", "action.getStarted": "<PERSON><PERSON><PERSON>", "action.got_it": "<PERSON><PERSON> aru", "action.hide": "<PERSON><PERSON><PERSON>", "action.import": "<PERSON><PERSON><PERSON>", "action.import-keys": "Impordi võtmed", "action.importKeys": "Impordi võtmed", "action.minimize": "<PERSON><PERSON><PERSON>", "action.next": "<PERSON><PERSON>", "action.ok": "OK", "action.reduceAmount": "<PERSON><PERSON><PERSON><PERSON> maksim<PERSON>", "action.refreshWebsite": "Värskenda ve<PERSON>", "action.remove": "<PERSON><PERSON><PERSON>", "action.remove-account": "<PERSON><PERSON><PERSON> k<PERSON>", "action.requestCode": "<PERSON><PERSON><PERSON> kood", "action.resend_code": "<PERSON><PERSON> kood u<PERSON>i", "action.resend_code_with_time": "<PERSON><PERSON> kood u<PERSON>i {time}", "action.retry": "<PERSON>ovi uuesti", "action.reveal": "<PERSON><PERSON>", "action.save": "Salvesta", "action.save_changes": "Salvesta RPC", "action.search": "<PERSON><PERSON><PERSON>", "action.seeAll": "Vaata kõiki", "action.select": "Vali", "action.send": "Saada", "action.skip": "<PERSON><PERSON><PERSON> v<PERSON>", "action.submit": "Esi<PERSON>", "action.understood": "<PERSON><PERSON> aru", "action.update": "<PERSON><PERSON><PERSON>", "action.update-gnosis-pay-owner.complete": "Vii lõpuni", "action.zeroAmount": "<PERSON><PERSON><PERSON> summa", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Kogutavad", "action_bar_title.tokens": "<PERSON><PERSON><PERSON>", "action_bar_title.transaction_request": "<PERSON><PERSON><PERSON> taotlus", "activate-monerium.loading": "Sinu isikliku konto seadistamine", "activate-monerium.success.title": "Monerium aktiveeritud", "activate-physical-card-widget.subtitle": "Kohaletoimetamine võib võtta 3 nädalat", "activate-physical-card-widget.title": "<PERSON>ktiveeri f<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "activate-smart-wallet.title": "<PERSON><PERSON><PERSON><PERSON>", "active_and_tracked_wallets.title": "Zeal katab kõik sinu tasud võrgus {network}, mis võimaldab sul teha tasuta tehinguid!", "activity.approval-amount.revoked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.approval-amount.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "activity.approval.approved_for": "<PERSON><PERSON> kii<PERSON>", "activity.approval.approved_for_with_target": "Kinnitatud {approvedTo}", "activity.approval.revoked_for": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.bank.serviceProvider": "Teenusepak<PERSON><PERSON>", "activity.bridge.serviceProvider": "Teenusepak<PERSON><PERSON>", "activity.cashback.period": "<PERSON><PERSON><PERSON> per<PERSON>", "activity.filter.card": "<PERSON><PERSON>", "activity.rate": "<PERSON><PERSON><PERSON>", "activity.receive.receivedFrom": "<PERSON><PERSON><PERSON>", "activity.send.sendTo": "<PERSON><PERSON>", "activity.smartContract.unknown": "<PERSON><PERSON><PERSON><PERSON> leping", "activity.smartContract.usingContract": "<PERSON><PERSON><PERSON><PERSON> leping", "activity.subtitle.pending_timer": "{timerString} Ootel", "activity.title.arbitrary_smart_contract_interaction": "{function} lepingus {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Tundmatu lepingu interaktsioon", "activity.title.bridge.from": "Bridge võrgust {token}", "activity.title.bridge.to": "Bridge võrku {token}", "activity.title.buy": "Osta {asset}", "activity.title.card_owners_updated": "<PERSON><PERSON><PERSON> o<PERSON> u<PERSON>", "activity.title.card_spend_limit_updated": "Kaardi kululim<PERSON>t seatud", "activity.title.cashback_deposit": "<PERSON>ssema<PERSON><PERSON>", "activity.title.cashback_reward": "Cashback preemia", "activity.title.cashback_withdraw": "Väljamakse Cashbackist", "activity.title.claimed_reward": "<PERSON><PERSON><PERSON><PERSON>", "activity.title.deployed_smart_wallet_gnosis": "<PERSON><PERSON> lo<PERSON>", "activity.title.deposit_from_bank": "Sissemakse pan<PERSON>t", "activity.title.deposit_into_card": "Sissemakse kaardile", "activity.title.deposit_into_earn": "<PERSON>ssemakse kontole {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} lepingus {smartContract}", "activity.title.from": "<PERSON><PERSON><PERSON> {sender}", "activity.title.pendidng_areward_claim": "<PERSON>emia kät<PERSON>", "activity.title.pendidng_breward_claim": "<PERSON>emia kät<PERSON>", "activity.title.recharge_disabledh": "<PERSON><PERSON><PERSON> la<PERSON> peat<PERSON>ud", "activity.title.recharge_set": "Laadimise sihtsumma seatud", "activity.title.recovered_smart_wallet_gnosis": "<PERSON><PERSON> seadme installimine", "activity.title.send_pending": "<PERSON><PERSON> {receiver}", "activity.title.send_to_bank": "<PERSON><PERSON>", "activity.title.swap": "Osta {token}", "activity.title.to": "<PERSON><PERSON> {receiver}", "activity.title.withdraw_from_card": "Väljamakse kaardilt", "activity.title.withdraw_from_earn": "Väljamakse kontolt {earn}", "activity.transaction.networkFees": "Võrgutasud", "activity.transaction.state": "<PERSON><PERSON>", "activity.transaction.state.completed": "<PERSON><PERSON><PERSON> te<PERSON>", "activity.transaction.state.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "add-account.section.import.header": "Import", "add-another-card-owner": "<PERSON> ka<PERSON>ile te<PERSON> o<PERSON>k", "add-another-card-owner.Recommended.footnote": "Lisa <PERSON> Gnosis Pay kaardi omanikuks", "add-another-card-owner.Recommended.primaryText": "<PERSON>eal Gnosis Pay'le", "add-another-card-owner.recommended": "<PERSON><PERSON><PERSON><PERSON>", "add-owner.confirmation.subtitle": "Turvalisuse huvides võtab seadete muutmine aega 3 minutit. <PERSON><PERSON> aja jook<PERSON> on su kaart aju<PERSON>lt külmutatud ja maksed ei ole võ<PERSON>.", "add-owner.confirmation.title": "<PERSON><PERSON> ka<PERSON> on seadete uuendamise ajal 3 minutit külmutatud", "add-readonly-signer-if-not-exist.error.already_in_use.title": "<PERSON><PERSON><PERSON> on juba kasutusel", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "<PERSON><PERSON>i te<PERSON> rah<PERSON><PERSON>i", "add.account.backup.decrypt.success": "<PERSON><PERSON><PERSON>", "add.account.backup.password.passwordIncorrectMessage": "Salasõna on vale", "add.account.backup.password.subtitle": "<PERSON><PERSON><PERSON>, mida ka<PERSON><PERSON><PERSON> taastefaili krüpteerimiseks", "add.account.backup.password.title": "<PERSON><PERSON><PERSON>", "add.account.google.login.subtitle": "Kinnita Google Drive'is pä<PERSON>, et sünkroonida oma taastefail", "add.account.google.login.title": "Ootan kinnitust...", "add.readonly.already_added": "<PERSON><PERSON><PERSON> on juba lisatud", "add.readonly.continue": "Jätka", "add.readonly.empty": "Sisesta aadress või ENS", "addBankRecipient.title": "<PERSON>", "add_funds.deposit_from_bank_account": "<PERSON><PERSON> sisse<PERSON><PERSON><PERSON> pangakontolt", "add_funds.from_another_wallet": "<PERSON><PERSON><PERSON> rahak<PERSON>", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Ühendu täiendamise dAppiga", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Ühenda dAppiga ja saada raha oma rahakotti.", "add_funds.from_crypto_wallet.header": "<PERSON><PERSON><PERSON> rahak<PERSON>", "add_funds.from_crypto_wallet.header.show_wallet_address": "<PERSON><PERSON><PERSON> oma rahakoti aadressi", "add_funds.from_exchange.header": "Saada vahetusplatvormilt", "add_funds.from_exchange.header.copy_wallet_address": "<PERSON><PERSON><PERSON> a<PERSON>ress", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance jne", "add_funds.from_exchange.header.open_exchange": "Ava börsi rakendus või veeb<PERSON>ht", "add_funds.from_exchange.header.selected_token": "<PERSON><PERSON> {token} <PERSON><PERSON><PERSON>", "add_funds.from_exchange.header.selected_token.subtitle": "Võrgus {network}", "add_funds.from_exchange.header.send_selected_token": "<PERSON><PERSON> token", "add_funds.from_exchange.header.send_selected_token.subtitle": "<PERSON>i toetatud token ja võrk", "add_funds.import_wallet": "Impordi olemasolev krüptorahakott", "add_funds.title": "Täienda oma kontot", "add_funds.transfer_from_exchange": "<PERSON><PERSON> ü<PERSON>kan<PERSON> vahetusplatvormilt", "address.add.header": "<PERSON><PERSON><PERSON> o<PERSON><PERSON>{br}ainult vaatam<PERSON> re<PERSON>mis", "address.add.subheader": "Sisesta aadress või ENS, et näha oma varasid kõigis EVM-võrkudes ühes kohas. Loo või impordi hiljem rohkem rahakotte.", "address_book.change_account.bank_transfers.header": "<PERSON><PERSON> saajad", "address_book.change_account.bank_transfers.primary": "Panga saaja", "address_book.change_account.cta": "<PERSON><PERSON><PERSON><PERSON>", "address_book.change_account.search_placeholder": "<PERSON> või otsi a<PERSON>i", "address_book.change_account.tracked_header": "Vaatamisõigusega rahakotid", "address_book.change_account.wallets_header": "<PERSON><PERSON><PERSON><PERSON><PERSON> rahakotid", "app-association-check-failed.modal.cta": "<PERSON>ovi uuesti", "app-association-check-failed.modal.subtitle": "<PERSON><PERSON><PERSON> proovi uuesti. Ühendusprobleemid põhjustavad sinu pääsuvõtmete hankimisel viivitusi. Kui probleem püsi<PERSON>, taaskäivita Zeal ja proovi veel kord.", "app-association-check-failed.modal.subtitle.creation": "<PERSON><PERSON>n proovi uuesti. Ühendusprobleemid põhjustavad pääsuvõtme loomisel viivitusi. Kui probleem püsib, taaskäivita Zeal ja proovi veel kord.", "app-association-check-failed.modal.title.creation": "Sinu seadmel ei õ<PERSON>ud pääsuvõtit luua", "app-association-check-failed.modal.title.signing": "Sinu seadmel ei õ<PERSON>ud pääsuvõtmeid laadida", "app.app_protocol_group.borrowed_tokens": "<PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.claimable_amount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> summa", "app.app_protocol_group.health_rate": "<PERSON><PERSON><PERSON>", "app.app_protocol_group.lending": "Laenamine", "app.app_protocol_group.locked_tokens": "Lukustatud tokenid", "app.app_protocol_group.nfts": "<PERSON><PERSON><PERSON><PERSON><PERSON> esemed", "app.app_protocol_group.reward_tokens": "Preemiatokenid", "app.app_protocol_group.supplied_tokens": "<PERSON><PERSON><PERSON>", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Vesting token", "app.appsGroupHeader.discoverMore": "Avasta rohkem", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Otsi või sisesta URL", "app.error-banner.cory": "<PERSON><PERSON><PERSON> veaan<PERSON>", "app.error-banner.retry": "<PERSON>ovi uuesti", "app.list_item.rewards": "Preemiad {value}", "app.position_details.health_rate.description": "Tervisenä<PERSON><PERSON>, jagades sinu laenusumma tagatise väärtusega.", "app.position_details.health_rate.title": "Mis on tervisenäitaja?", "approval.edit-limit.label": "<PERSON><PERSON> k<PERSON>", "approval.permit_info": "Loa teave", "approval.spend-limit.edit-modal.cancel": "<PERSON><PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.limit-label": "Ku<PERSON><PERSON>iit", "approval.spend-limit.edit-modal.max-limit-error": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> limiit", "approval.spend-limit.edit-modal.revert": "<PERSON><PERSON><PERSON><PERSON> muuda<PERSON>", "approval.spend-limit.edit-modal.set-to-unlimited": "Määra p<PERSON>ramatuks", "approval.spend-limit.edit-modal.submit": "<PERSON><PERSON><PERSON> muudatus<PERSON>", "approval.spend-limit.edit-modal.title": "<PERSON><PERSON> lubasid", "approval.spend_limit_info": "Mis on kululimiit?", "approval.what_are_approvals": "Mis on kinnitused?", "apps_list.page.emptyState": "Aktii<PERSON><PERSON><PERSON> rake<PERSON><PERSON>i pole", "backpace.removeLastDigit": "<PERSON><PERSON><PERSON> viimane number", "backup-banner.backup_now": "Varunda", "backup-banner.risk_losing_funds": "Varunda kohe või riskid raha kaota<PERSON>ga", "backup-banner.title": "<PERSON><PERSON><PERSON>", "backupRecoverySmartWallet.noExportPrivateKeys": "Automaatne varundus: Sinu Smart Wallet salvestatakse pääsuvõtmena – salafraasi või privaatvõtit pole vaja.", "backupRecoverySmartWallet.safeContracts": "Mitme võtmega turvalisus: <PERSON><PERSON><PERSON> rahakotid kasuta<PERSON>d Safe'i lepinguid, nii et tehingu saab heaks kiita mitu seadet. Puudub üksik tõrkepunkt.", "backupRecoverySmartWallet.security": "Mitu seadet: Pääsuvõtmega saad oma rahakotti kasutada mitmes seadmes. Iga seade saab oma privaatvõtme.", "backupRecoverySmartWallet.showLocalPrivateKey": "Ekspertrežiim: saad eksportida selle seadme privaatvõtme, kasutada seda teises rahakotis ja ühenduda aadressil <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Näita privaatvõtit</Key>", "backupRecoverySmartWallet.storingKeys": "Pilvega sünkroonitud: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on turvaliselt salvestatud iCloudi, Google'i paroolihaldurisse või sinu paroolihaldurisse.", "backupRecoverySmartWallet.title": "Smart Walleti varundamine ja taastamine", "balance-change.card.titile": "<PERSON><PERSON>", "balanceChange.pending": "Ootel", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "<PERSON><PERSON>", "bank-transfer.change-deposit-receiver.subtitle": "See rah<PERSON>ott võtab vastu kõik panga sissemaksed", "bank-transfer.change-deposit-receiver.title": "<PERSON><PERSON><PERSON><PERSON>ttev rahakott", "bank-transfer.change-owner.subtitle": "Selle rahakotiga logid sisse ja taastad oma pangaülekande konto.", "bank-transfer.change-owner.title": "<PERSON><PERSON><PERSON><PERSON> konto omanik", "bank-transfer.configrm-change-deposit-receiver.subtitle": "<PERSON><PERSON><PERSON> sa<PERSON>tud panga sissemaksed laekuvad sellesse rah<PERSON>.", "bank-transfer.configrm-change-deposit-receiver.title": "Vaheta vastuvõttev rahakott", "bank-transfer.configrm-change-owner.subtitle": "Kas soovid kindlasti konto omanikku vahetada? Selle rahakotiga logid sisse ja taastad oma pangaülekande konto.", "bank-transfer.configrm-change-owner.title": "Vaheta konto omanik", "bank-transfer.deposit.widget.status.complete": "<PERSON><PERSON><PERSON>", "bank-transfer.deposit.widget.status.funds_received": "<PERSON><PERSON>", "bank-transfer.deposit.widget.status.sending_to_wallet": "<PERSON><PERSON><PERSON>", "bank-transfer.deposit.widget.status.transfer-on-hold": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>l", "bank-transfer.deposit.widget.status.transfer-received": "<PERSON><PERSON><PERSON>", "bank-transfer.deposit.widget.subtitle": "{from} → {to}", "bank-transfer.deposit.widget.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfer.intro.bulletlist.point_1": "Seadista Unblocki kaudu", "bank-transfer.intro.bulletlist.point_2": "<PERSON><PERSON> üle EUR/GBP ja enam kui 10 tokeni vahel", "bank-transfer.intro.bulletlist.point_3": "0% tasu kuni $5k kuus, seejärel 0,2%", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "<PERSON><PERSON>n panka", "bank-transfer.withdrawal.widget.status.in-progress": "<PERSON>", "bank-transfer.withdrawal.widget.status.on-hold": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>l", "bank-transfer.withdrawal.widget.status.success": "Val<PERSON>", "bank-transfer.withdrawal.widget.subtitle": "{from} saad: {to}", "bank-transfer.withdrawal.widget.title": "Väljamakse", "bank-transfers.bank-account-actions.remove-this-account": "<PERSON><PERSON>lda see konto", "bank-transfers.bank-account-actions.switch-to-this-account": "Vaheta sellele kontole", "bank-transfers.deposit.fees-for-less-than-5k": "Tasud kuni $5k tehingutele", "bank-transfers.deposit.fees-for-more-than-5k": "Tasud üle $5k tehingutele", "bank-transfers.set-receiving-bank.title": "<PERSON><PERSON><PERSON><PERSON>õttev pank", "bank-transfers.settings.account_owner": "<PERSON><PERSON>", "bank-transfers.settings.receiver_of_bank_deposits": "Panga sissema<PERSON>ete saaja", "bank-transfers.settings.receiver_of_withdrawals": "Väljamaksete saaja", "bank-transfers.settings.registered_email": "Registreeritud e-post", "bank-transfers.settings.title": "Pangaülekande seaded", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} konto", "bank-transfers.setup.bank-account": "Pangakonto", "bankTransfer.withdraw.max_loading": "Max: {amount}", "bank_details_do_not_match.got_it": "<PERSON><PERSON> aru", "bank_details_do_not_match.subtitle": "Sorteerimiskood ja kontonumber ei klapi. <PERSON><PERSON><PERSON> kont<PERSON> and<PERSON> ja proovi uuesti.", "bank_details_do_not_match.title": "Pangaandmed ei klapi", "bank_tranfsers.select_country_of_residence.country_not_supported": "<PERSON><PERSON><PERSON><PERSON>, pangaülekandeid ei toetata riigis {country} veel", "bank_transfer.deposit.bullet-point.open-your-bank-app": "<PERSON> oma <PERSON>", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Saada {fiatCurrencyCode} oma kontole", "bank_transfer.deposit.header": "{fullName} isiklikud konto&nbsp;andmed", "bank_transfer.kyc_status_widget.subtitle": "Pangaülekanded", "bank_transfer.kyc_status_widget.title": "<PERSON><PERSON><PERSON> tu<PERSON>", "bank_transfer.personal_details.date_of_birth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfer.personal_details.date_of_birth.invalid_format": "<PERSON><PERSON><PERSON><PERSON> on vigane", "bank_transfer.personal_details.date_of_birth.too_young": "Pead olema vähemalt 18-a<PERSON><PERSON>", "bank_transfer.personal_details.first_name": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.personal_details.last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfer.personal_details.title": "<PERSON><PERSON> and<PERSON>", "bank_transfer.reference.label": "<PERSON><PERSON><PERSON><PERSON> (valikuline)", "bank_transfer.reference_message": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.residence_details.address": "<PERSON><PERSON> aadress", "bank_transfer.residence_details.city": "<PERSON><PERSON>", "bank_transfer.residence_details.country_of_residence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfer.residence_details.country_placeholder": "Riik", "bank_transfer.residence_details.postcode": "Postiindeks", "bank_transfer.residence_details.street": "Tänav", "bank_transfer.residence_details.your_residence": "<PERSON><PERSON>", "bank_transfers.choose-wallet.continue": "Jätka", "bank_transfers.choose-wallet.test": "<PERSON>", "bank_transfers.choose-wallet.warning.subtitle": "<PERSON><PERSON> korraga siduda ainult ühe rahakoti. <PERSON><PERSON>ud rahakotti ei saa hiljem vahetada.", "bank_transfers.choose-wallet.warning.title": "Vali rah<PERSON> hooli<PERSON>t", "bank_transfers.choose_wallet.subtitle": "Vali rahakott pangaga sidumiseks. ", "bank_transfers.choose_wallet.title": "<PERSON><PERSON>", "bank_transfers.continue": "Jätka", "bank_transfers.currency_is_currently_not_supported": "Jätka", "bank_transfers.deposit-header": "Sissemakse", "bank_transfers.deposit.account-name": "Konto omaniku nimi", "bank_transfers.deposit.account-number-copied": "Kontonumber kopeeritud", "bank_transfers.deposit.amount-input": "<PERSON><PERSON><PERSON><PERSON><PERSON> summa", "bank_transfers.deposit.amount-output": "<PERSON><PERSON><PERSON> summa", "bank_transfers.deposit.amount-output.error": "viga", "bank_transfers.deposit.buttet-point.receive-crypto": "Saa kätte {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "Jätka", "bank_transfers.deposit.currency-not-supported.subtitle": "<PERSON><PERSON><PERSON><PERSON>ed valuutas {code} on ajutiselt peatatud.", "bank_transfers.deposit.currency-not-supported.title": "{code} si<PERSON><PERSON><PERSON><PERSON><PERSON> hetkel ei toetata", "bank_transfers.deposit.default-token.balance": "Saldo {amount}", "bank_transfers.deposit.deposit-header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.enter_amount": "<PERSON><PERSON><PERSON> summa", "bank_transfers.deposit.iban-copied": "IBAN kopeeritud", "bank_transfers.deposit.increase-amount": "<PERSON><PERSON><PERSON><PERSON> on {limit}", "bank_transfers.deposit.loading": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.max-limit-reached": "Summa ületab ülekande limiidi", "bank_transfers.deposit.modal.kyc.button-text": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.modal.kyc.text": "Isiku tuvastam<PERSON>ks vajame sinu isikuandmeid ja dokumente. Esitamine võtab tavaliselt vaid paar minutit.", "bank_transfers.deposit.modal.kyc.title": "<PERSON><PERSON><PERSON> oma isik limiitide tõstmiseks", "bank_transfers.deposit.reduce_amount": "<PERSON><PERSON><PERSON><PERSON> summat", "bank_transfers.deposit.show-account.account-number": "Ko<PERSON>umber", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Sorteerimis<PERSON><PERSON>", "bank_transfers.deposit.sort-code-copied": "Sorteerimiskood kopeeritud", "bank_transfers.deposit.withdraw-header": "Väljamakse", "bank_transfers.failed_to_load_fee": "Teadmata", "bank_transfers.fees": "Ta<PERSON><PERSON>", "bank_transfers.increase-amount": "<PERSON><PERSON><PERSON><PERSON> on {limit}", "bank_transfers.insufficient-funds": "Pole piisa<PERSON><PERSON> vahendeid", "bank_transfers.select_country_of_residence.title": "Kus sa elad?", "bank_transfers.setup.cta": "Seadista pangaülekanded", "bank_transfers.setup.enter-amount": "<PERSON><PERSON><PERSON> summa", "bank_transfers.source_of_funds.form.business_income": "Äritulu", "bank_transfers.source_of_funds.form.other": "<PERSON><PERSON>", "bank_transfers.source_of_funds.form.pension": "Pension", "bank_transfers.source_of_funds.form.salary": "Palk", "bank_transfers.source_of_funds.form.title": "<PERSON>u vahendite allikas", "bank_transfers.source_of_funds_description.placeholder": "<PERSON><PERSON><PERSON><PERSON> vahendite allikat...", "bank_transfers.source_of_funds_description.title": "R<PERSON><PERSON><PERSON> meile oma vahendite allikast lähemalt", "bank_transfers.withdraw-header": "Väljamakse", "bank_transfers.withdraw.amount-input": "Väljamakstav summa", "bank_transfers.withdraw.max-limit-reached": "Summa ületab ülekande limiidi", "bank_transfers.withdrawal.verify-id": "<PERSON><PERSON><PERSON><PERSON> summat", "banner.above_maximum_limit.maximum_input_limit_exceeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> on ületatud", "banner.above_maximum_limit.maximum_limit_per_deposit": "See on deposiidi maksimaalne limiit", "banner.above_maximum_limit.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> on ületatud", "banner.above_maximum_limit.title": "V<PERSON><PERSON>a summat {amount} v<PERSON>i vä<PERSON>", "banner.above_maximum_limit.title.default": "<PERSON><PERSON><PERSON><PERSON> summat", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Minimaalne si<PERSON>t on ületatud", "banner.below_minimum_limit.minimum_limit_for_token": "See on selle tokeni miinimumlimiit", "banner.below_minimum_limit.title": "<PERSON><PERSON><PERSON> summat {amount} v<PERSON><PERSON> rohke<PERSON>ks", "banner.below_minimum_limit.title.default": "<PERSON><PERSON><PERSON> summat", "breaard.in_porgress.info_popup.cta": "<PERSON><PERSON><PERSON> ja teeni {earn}", "breaard.in_porgress.info_popup.footnote": "Ka<PERSON>tades Zeali ja Gnosis Pay kaarti, nõustud selle preemiakampaania tingimustega.", "breaward.in_porgress.info_popup.bullet_point_1": "<PERSON><PERSON><PERSON> {remaining} j<PERSON>rg<PERSON> {time} jook<PERSON>, et see preemia lunastada.", "breaward.in_porgress.info_popup.bullet_point_2": "Arvesse lähevad vaid Gnosis Pay ostud.", "breaward.in_porgress.info_popup.bullet_point_3": "Lunastatud preemia jõuab su Zeali kontole.", "breaward.in_porgress.info_popup.header": "<PERSON><PERSON> {earn}, kulutades {remaining}", "breward.celebration.for_spending": "Kaardiga kulutamise eest", "breward.dc25-eligible-celebration.for_spending": "Oled esimeste {limit} seas!", "breward.dc25-non-eligible-celebration.for_spending": "Sa ei olnud esimeste {limit} kulutaja seas", "breward.expired_banner.earn_by_spending": "Teeni {earn} kulutades {amount}", "breward.expired_banner.reward_expired": "{earn} preemia aegus", "breward.in_progress_banner.cta.title": "<PERSON><PERSON><PERSON> ja teeni {earn}", "breward.ready_to_claim.error.try_again": "<PERSON>ovi uuesti", "breward.ready_to_claim.error_title": "Preemia lunastamine eba<PERSON>nnestus", "breward.ready_to_claim.in_progress": "Preemia lunastamine", "breward.ready_to_claim.youve_earned": "Oled teeninud {earn}!", "breward_already_claimed.title": "Preemia on juba välja võetud. Kui sa preemiamärki ei saanud, võta palun ühendust toega.", "breward_cannotbe_claimed.title": "Preemiat ei saa hetkel välja võtta. <PERSON><PERSON><PERSON> proovi hiljem uuesti.", "bridge.best_return": "Parima tootlusega marsruut", "bridge.best_serivce_time": "<PERSON><PERSON><PERSON>", "bridge.check_status.complete": "Lõpetatud", "bridge.check_status.progress_text": "Bridge'in {from} -> {to}", "bridge.remove_topup": "<PERSON><PERSON><PERSON> lisamine", "bridge.request_status.completed": "Lõpetatud", "bridge.request_status.pending": "Ootel", "bridge.widget.completed": "Lõpetatud", "bridge.widget.currencies": "{from} -> {to}", "bridge_rote.widget.title": "Bridge", "browse.discover_more_apps": "Avasta rohkem rakendusi", "browse.google_search_term": "O<PERSON>i \"{searchTerm}\"", "brward.celebration.you_earned": "<PERSON><PERSON> teen<PERSON>", "brward.expired_banner.subtitle": "<PERSON>ärg<PERSON><PERSON> korral läheb paremini", "brward.in_progress_banner.subtitle": "Aegub {expiredInFormatted}", "buy": "<PERSON><PERSON>", "buy.enter_amount": "<PERSON><PERSON><PERSON> summa", "buy.loading": "<PERSON><PERSON><PERSON>...", "buy.no_routes_found": "Marsruute ei leitud", "buy.not_enough_balance": "Saldo ei ole piisav", "buy.select-currency.title": "Vali token", "buy.select-to-currency.title": "<PERSON><PERSON>", "buy_form.title": "Osta token", "cancelled-card.create-card-button.primary": "Hang<PERSON> uus virtuaalkaart", "cancelled-card.switch-card-button.primary": "<PERSON><PERSON><PERSON> ka<PERSON>i", "cancelled-card.switch-card-button.short-text": "Sul on veel üks aktiivne kaart", "card": "<PERSON><PERSON>", "card-add-cash.confirm-stage.banner.no-routes-found": "Sobivat viisi ei leitud. Proovi teist tokenit või summat", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Sul on vaja {amount} rohkem {symbol} tasude maksmiseks", "card-add-cash.confirm-stage.banner.value-loss": "Kaotad {loss} väärtusest", "card-add-cash.confirm-stage.banner.value-loss.revert": "<PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.cancel": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.continue": "Jätka", "card-add-cash.edit-stage.cta.enter-amount": "<PERSON> summa", "card-add-cash.edit-stage.cta.reduce-to-max": "<PERSON><PERSON><PERSON> max", "card-add-cash.edit-staget.banner.no-routes-found": "Sobivat viisi ei leitud. Proovi teist tokenit või summat", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "<PERSON><PERSON> ootab riist<PERSON>alises rahakotis.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "<PERSON><PERSON><PERSON> r<PERSON><PERSON> rahakotis", "card-balance": "Saldo: {balance}", "card-cashback.status.title": "Sissemakse cashback'i", "card-copy-safe-address.copy_address": "<PERSON><PERSON><PERSON> a<PERSON>", "card-copy-safe-address.copy_address.done": "Kopeeritud", "card-copy-safe-address.warning.description": "<PERSON><PERSON><PERSON> aadressile saab saata ainult {cardAsset} Gnosis Chain võrgus. Ära saada sellele aadressile varasid teistest võrkudest. Need lähevad kaotsi.", "card-copy-safe-address.warning.header": "<PERSON>ada ainult {cardAsset} Gnosis Chain võrgus", "card-marketing-card.center.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "Intress", "card-marketing-card.right.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "card-marketing-card.title": "Euroopa kõrge intressiga VISA kaart", "card-marketing-tile.get-started": "<PERSON><PERSON><PERSON>", "card-select-from-token-title": "Vali token laadimiseks", "card-top-up.banner.subtitle.completed": "<PERSON><PERSON><PERSON>", "card-top-up.banner.subtitle.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card-top-up.banner.subtitle.pending": "{timerString} Ootel", "card-top-up.banner.title": "Lisan kaardile {amount}", "card-topup.select-token.emptyState": "Tokeneid ei leitud", "card.activate.card_number_not_valid": "<PERSON><PERSON>e kaardinumber. Kontrolli ja proovi uuesti.", "card.activate.invalid_card_number": "<PERSON><PERSON><PERSON> kaardinumber.", "card.activation.activate_physical_card": "<PERSON>ktiveeri f<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "card.add-cash.amount-to-withdraw": "Lisa<PERSON>v summa", "card.add-from-earn-form.title": "<PERSON> kaardile raha", "card.add-from-earn-form.withdraw-to-card": "Jätka", "card.add-from-earn.amount-to-withdraw": "<PERSON>mma ka<PERSON>ile kand<PERSON>", "card.add-from-earn.enter-amount": "<PERSON><PERSON><PERSON> summa", "card.add-from-earn.loading": "<PERSON><PERSON><PERSON>", "card.add-from-earn.max-label": "Saldo: {amount}", "card.add-from-earn.no-routes-found": "Teekondi ei leitud", "card.add-from-earn.not-enough-balance": "Saldost ei piisa", "card.add-owner.queued": "<PERSON>iku lisa<PERSON>", "card.add-to-wallet-flow.subtitle": "<PERSON><PERSON> ma<PERSON><PERSON>d oma rahakot<PERSON>", "card.add-to-wallet.copy-card-number": "<PERSON><PERSON><PERSON> all<PERSON> kaard<PERSON>ber", "card.add-to-wallet.title": "<PERSON> {<PERSON>Name} Walletisse", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "TÜHISTATUD", "card.card-owner-not-found.disconnect-btn": "<PERSON><PERSON><PERSON> kaart lahti", "card.card-owner-not-found.subtitle": "<PERSON><PERSON><PERSON> ka<PERSON>ks uuenda kaardi omanikku.", "card.card-owner-not-found.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "card.card-owner-not-found.update-owner-btn": "<PERSON><PERSON>da kaardi omani<PERSON>ku", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} {date}", "card.cashback.widgetNoCashback.subtitle": "<PERSON><PERSON> sisse<PERSON>kse ja hakka teenima", "card.cashback.widgetNoCashback.title": "<PERSON><PERSON> kuni {defaultPercentage} cashbacki", "card.cashback.widgetcashbackValue.rewards": "{amount} ootel", "card.cashback.widgetcashbackValue.title": "{percentage} cashback", "card.choose-wallet.connect_card": "<PERSON><PERSON><PERSON> ka<PERSON>", "card.choose-wallet.create-new": "<PERSON> uus rahak<PERSON> omani<PERSON>ks", "card.choose-wallet.import-another-wallet": "<PERSON><PERSON>rdi te<PERSON> r<PERSON>", "card.choose-wallet.import-current-owner": "I<PERSON>rdi kaardi omaniku rahakott", "card.choose-wallet.import-current-owner.sub-text": "Impordi kaardi omaniku võtmed või fraas", "card.choose-wallet.title": "<PERSON><PERSON> rahak<PERSON> kaardi halda<PERSON>", "card.connectWalletToCardGuide": "<PERSON><PERSON><PERSON> rah<PERSON>oti aadress", "card.connectWalletToCardGuide.addGnosisPayOwner": "<PERSON> omanik", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Ava Gnosispay.com oma teise rahakotiga{br}2. <PERSON><PERSON><PERSON><PERSON><PERSON> „Account“{br}3. <PERSON><PERSON><PERSON><PERSON><PERSON> „Account details“{br}4. <PERSON><PERSON><PERSON><PERSON><PERSON> „Edit“, vali<PERSON> „Account Owner“ kõrval ja{br}5. <PERSON><PERSON><PERSON><PERSON><PERSON> „Add address“{br}6. <PERSON><PERSON><PERSON> oma Zeali aadress ja klõpsa „Save“", "card.connectWalletToCardGuide.header": "Ühenda {account} Gnosis Pay kaardiga", "card.connect_card.start": "<PERSON><PERSON>a G<PERSON> kaart", "card.copiedAddress": "Kopeeritud {formattedAddress}", "card.disconnect-account.title": "<PERSON><PERSON><PERSON> konto lahti", "card.hw-wallet-support-drop.add-owner-btn": "<PERSON> ka<PERSON> u<PERSON> o<PERSON>k", "card.hw-wallet-support-drop.disconnect-btn": "<PERSON><PERSON><PERSON> kaart lahti", "card.hw-wallet-support-drop.subtitle": "Jätkamiseks lisa ka<PERSON>ile o<PERSON> konto, mis pole riist<PERSON><PERSON><PERSON> r<PERSON>.", "card.hw-wallet-support-drop.title": "Zeal ei toeta enam kaardi jaoks riistvaralisi rahakotte", "card.kyc.continue": "Jätka seadistamist", "card.list_item.title": "<PERSON><PERSON>", "card.onboarded.transactions.empty.description": "<PERSON>u maksete tegevus kuvat<PERSON>e siin", "card.onboarded.transactions.empty.title": "Tegevused", "card.order.continue": "<PERSON><PERSON><PERSON> kaardi tellimist", "card.order.free_virtual_card": "<PERSON>a tasuta virtua<PERSON><PERSON>t", "card.order.start": "<PERSON><PERSON> ka<PERSON> tasuta", "card.owner-not-imported.cancel": "<PERSON><PERSON><PERSON><PERSON>", "card.owner-not-imported.import": "<PERSON><PERSON><PERSON>", "card.owner-not-imported.subtitle": "Te<PERSON>u kinnitamiseks ühenda oma Gnosis Pay konto omaniku rahakott Zealiga. Tähelepanu: see on eraldiseisev sinu tavapärasest Gnosis Pay rahakoti sisselogimisest.", "card.owner-not-imported.title": "<PERSON> Pay konto omanik", "card.page.order_free_physical_card": "<PERSON><PERSON> tasuta p<PERSON><PERSON> kaart", "card.pin.change_pin_at_atm": "PIN-koodi saab muuta valitud ATM-ides", "card.pin.timeout": "<PERSON><PERSON><PERSON> sulgub {seconds} sek pärast", "card.quick-actions.add-assets": "<PERSON> raha", "card.quick-actions.add-cash": "<PERSON> raha", "card.quick-actions.details": "<PERSON>ail<PERSON>", "card.quick-actions.freeze": "Sule", "card.quick-actions.freezing": "Sulgen", "card.quick-actions.unfreeze": "Ava", "card.quick-actions.unfreezing": "Avan", "card.quick-actions.withdraw": "Välja", "card.read-only-detected.create-new": "<PERSON> uus rahak<PERSON> omani<PERSON>ks", "card.read-only-detected.import-current-owner": "Impordi rahakoti võtmed {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Impordi rahakoti võ<PERSON>med või fraas {address}", "card.read-only-detected.title": "<PERSON><PERSON><PERSON> on vaja te<PERSON> rahak<PERSON>i", "card.remove-owner.queued": "Omaniku eemaldamine ootel", "card.settings.disconnect-from-zeal": "<PERSON><PERSON><PERSON> Zeal'ist lahti", "card.settings.edit-owners": "<PERSON><PERSON> kaardi o<PERSON>ke", "card.settings.getCard": "<PERSON><PERSON> uus kaart", "card.settings.getCard.subtitle": "Virtuaalne või fü<PERSON><PERSON><PERSON> kaart", "card.settings.notRecharging": "Automaatne lisamine väljas", "card.settings.notifications.subtitle": "Saa maks<PERSON>ati<PERSON>", "card.settings.notifications.title": "<PERSON><PERSON><PERSON> teated", "card.settings.page.title": "<PERSON><PERSON><PERSON> seaded", "card.settings.select-card.cancelled-cards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.setAutoRecharge": "Määra automaatne lisamine", "card.settings.show-card-address": "<PERSON><PERSON><PERSON> kaardi a<PERSON>i", "card.settings.spend-limit": "Määra kululim<PERSON>t", "card.settings.spend-limit-title": "Praegune päevalimiit: {limit}", "card.settings.switch-active-card": "Vaheta aktiiv<PERSON> kaarti", "card.settings.switch-active-card-description": "Aktii<PERSON><PERSON> kaart: {card}", "card.settings.switch-card.card-item.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.frozen": "Külmutatud", "card.settings.switch-card.card-item.title": "Gnosis Pay kaart", "card.settings.switch-card.card-item.title.physical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.title.virtual": "<PERSON>irt<PERSON><PERSON><PERSON> kaart", "card.settings.switch-card.title": "<PERSON><PERSON> ka<PERSON>", "card.settings.targetBalance": "Sihtsaldo: {threshold}", "card.settings.view-pin": "<PERSON><PERSON><PERSON>koodi", "card.settings.view-pin-description": "<PERSON><PERSON><PERSON> alati oma PIN-koodi", "card.title": "<PERSON><PERSON>", "card.transactions.header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.transactions.see_all": "Vaata kõiki te<PERSON>", "card.virtual": "VIRTUAALNE", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Cashback saadetakse sinu kaardile teenimisele järgneva nädala alguses.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "<PERSON><PERSON> rohkem sisse maksad, seda rohkem teenid iga ostuga.", "cardCashback.onboarding.title": "<PERSON><PERSON> kuni {percentage} cashbacki", "cardCashbackWithdraw.amount": "Väljamakse summa", "cardCashbackWithdraw.header": "Väljamakse {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "<PERSON><PERSON> on blokeeritud ja seda ei saanud aktiveerida", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "<PERSON><PERSON> {percentage}", "cardWidget.startEarning": "<PERSON><PERSON><PERSON> teenimist", "cardWithdraw.amount": "Väljavõetav summa", "cardWithdraw.header": "Võta kaardilt välja", "cardWithdraw.selectWithdrawWallet.title": "<PERSON><PERSON>, kuhu{br}raha kanda", "cardWithdraw.success.cta": "Sulge", "cardWithdraw.success.subtitle": "Turvalisuse huvides võtavad kõik Gnosis Pay kaardilt väljamaksed aega 3 minutit", "cardWithdraw.success.title": "See muudatus võtab aega 3 minutit", "card_top_up_trx.send": "Sa<PERSON><PERSON>", "card_top_up_trx.to": "<PERSON><PERSON>", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cards.card_number": "<PERSON><PERSON><PERSON> number", "cards.choose-wallet.no-active-accounts": "Sul pole aktiivseid rahak<PERSON>", "cards.copied_card_number": "Kaardi number kopeeritud", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Päevalimiit ületatud", "cards.transactions.decline_reason.incorrect_pin": "Vale PIN-kood", "cards.transactions.decline_reason.incorrect_security_code": "Vale turvakood", "cards.transactions.decline_reason.invalid_amount": "Vale summa", "cards.transactions.decline_reason.low_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "cards.transactions.decline_reason.other": "Tagasi lü<PERSON>", "cards.transactions.decline_reason.pin_tries_exceeded": "PIN-koodi katsete arv ületatud", "cards.transactions.status.refund": "Tagasimakse", "cards.transactions.status.reversal": "Tühistamine", "cashback-deposit.trx.title": "<PERSON>ssema<PERSON><PERSON>", "cashback-estimate.text": "See on prognoos, MITTE garanteeritud väljamaksed. Rakendatakse kõiki avalikke cashbacki reegleid, kuid Gnosis Pay võib tehinguid oma äranägemise järgi välistada. Maksimaalne kulu {amount} nädalas kvalifitseerub cashbacki saamiseks isegi siis, kui selle tehingu prognoos näitaks suuremat kogusummat.", "cashback-estimate.text.fallback": "See on hinnanguline, MITTE garanteeritud cashback. Rakendatud on kõiki avalikult teadaolevaid cashback'i reegleid, kuid Gnosis Pay võib tehinguid oma äranägemisel välistada.", "cashback-estimate.title": "Cashback'i hinnang", "cashback-onbarding-tersm.subtitle": "<PERSON><PERSON> and<PERSON><PERSON> jaga<PERSON><PERSON><PERSON>, kes vastutab cashbacki preemiate jagamise eest. Nõustudes kinnitad, et aktsepteerid Gnosis DAO Cashback <terms>kasutustingimusi</terms>", "cashback-onbarding-tersm.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ja privaatsus", "cashback-tx-activity.retry": "<PERSON>ovi uuesti", "cashback-unconfirmed-payments-info.subtitle": "Maksed kvalifitseeruvad cashbackile, kui need on kaupmehega tasaarveldatud. <PERSON>i kuvatakse neid kui kinnitamata makseid. Tasaarveldamata maksed ei kvalifitseeru cashbackile.", "cashback-unconfirmed-payments-info.title": "<PERSON><PERSON><PERSON><PERSON>", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "Sissemakse", "cashback.activity.title": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "cashback.activity.withdrawal": "Väljamakse", "cashback.deposit": "Sissemakse", "cashback.deposit.amount.label": "Sissemakse summa", "cashback.deposit.change": "{from} -> {to}", "cashback.deposit.confirmation.subtitle": "Cashbacki määrad uuenevad kord nädalas. <PERSON><PERSON> si<PERSON> kohe, et suurendada järgmise nädala cashbacki.", "cashback.deposit.confirmation.title": "<PERSON>kka<PERSON> {percentage} alates {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Vaheta tokeneid {currency} vastu {network} Chain võrgus", "cashback.deposit.get.tokens.title": "Hangi {currency} tokeneid", "cashback.deposit.header": "<PERSON><PERSON> si<PERSON> {currency}", "cashback.deposit.max_label": "Max: {amount}", "cashback.deposit.select-wallet.title": "<PERSON><PERSON>, kust sissema<PERSON>e teha", "cashback.deposit.yourcashback": "Sinu cashback", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "<PERSON><PERSON>, {br}kuhu v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> teha", "cashback.transaction-details.network-label": "Võrk", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "<PERSON><PERSON><PERSON>", "cashback.transaction-details.top-row.label-rewards": "<PERSON><PERSON><PERSON> per<PERSON>", "cashback.transaction-details.top-row.label-withdrawal": "<PERSON><PERSON><PERSON>", "cashback.transaction-details.transaction": "Tehingu ID", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "<PERSON><PERSON><PERSON>", "cashback.withdraw": "Väljamakse", "cashback.withdraw.confirmation.cashback_reduction": "<PERSON><PERSON> cashback, kaasa arvatud juba <PERSON>, v<PERSON><PERSON><PERSON> v<PERSON>ärtuselt {before} väärtuse<PERSON> {after}", "cashback.withdraw.queued": "Väljamakse ootejärjekorras", "cashback.withdrawal.change": "{from} -> {to}", "cashback.withdrawal.confirmation.subtitle": "<PERSON>usta väljamakset summas {amount} 3-minutilise viivitusega. See vähendab sinu cashbacki tasemele {after}.", "cashback.withdrawal.confirmation.title": "GNO väljamaksel cashback väheneb", "cashback.withdrawal.delayTransaction.title": "Alusta GNO väljamakset{br} 3-minutilise viivitusega", "cashback.withdrawal.withdraw": "Võta välja", "cashback.withdrawal.yourcashback": "Sinu cashback", "celebration.aave": "<PERSON><PERSON><PERSON>", "celebration.cashback.subtitle": "Makstud {code}", "celebration.cashback.subtitleGNO": "{amount} viimati teenitud", "celebration.chf": "<PERSON><PERSON><PERSON>", "celebration.lido": "<PERSON><PERSON><PERSON>", "celebration.sky": "<PERSON><PERSON><PERSON>a", "celebration.title": "Cashback kokku", "celebration.well_done.title": "Hästi tehtud!", "change-withdrawal-account.add-new-account": "<PERSON> u<PERSON> pan<PERSON>", "change-withdrawal-account.item.shortText": "{currency} konto", "check-confirmation.approve.footer.for": "Jaoks", "checkConfirmation.title": "<PERSON><PERSON><PERSON> tulemus", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin ja Ether", "collateral.ethereum": "Ethereum", "collateral.other": "<PERSON><PERSON>", "collateral.rwa": "<PERSON><PERSON><PERSON><PERSON><PERSON> varad", "collateral.stablecoins": "Stabiilsusmündid (USD-ga seotud)", "collateral.us-t-bills": "USA riigivõlakirjad", "confirm-bank-transfer-recipient.bullet-1": "Digitaalsetel eurodel tasud puuduvad", "confirm-bank-transfer-recipient.bullet-2": "Sissemaksed kontole {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Jaga Gnosis Pay konto and<PERSON><PERSON>, kes on volitatud ja reguleeritud e-raha asutus. <link>Loe lisaks</link>", "confirm-bank-transfer-recipient.bullet-4": "<PERSON><PERSON>ust<PERSON> <link>ka<PERSON>tustingimustega</link>", "confirm-bank-transfer-recipient.title": "<PERSON><PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.cancel": "<PERSON><PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.confirm": "<PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.saving": "Salvestan", "confirm-change-withdrawal-account.subtitle": "Kõik Zeal'ist saadetud väljamaksed laekuvad sellele pangakontole.", "confirm-change-withdrawal-account.title": "Vaheta vastuvõttev pank", "confirm-ramove-withdrawal-account.title": "<PERSON><PERSON><PERSON>", "confirm-remove-withdrawal-account.subtitle": "<PERSON><PERSON> and<PERSON> e<PERSON> Zeal'ist. <PERSON><PERSON> selle igal ajal uuesti lisada.", "confirmTransaction.finalNetworkFee": "Võrgutasu", "confirmTransaction.importKeys": "Impordi võtmed", "confirmTransaction.networkFee": "Võrgutasu", "confirmation.title": "<PERSON><PERSON> {amount} sa<PERSON><PERSON> {recipient}", "conflicting-monerium-account.add-owner": "<PERSON> Pay omanikuks", "conflicting-monerium-account.create-wallet": "Loo uus Smart Wallet", "conflicting-monerium-account.disconnect-card": "<PERSON><PERSON><PERSON> kaart ja ühenda uue o<PERSON>ga.", "conflicting-monerium-account.header": "{wallet} on se<PERSON>ud teise Moneriumi kontoga", "conflicting-monerium-account.subtitle": "Vaheta oma Gnosis Pay omaniku rahakott", "connection.diconnected.got_it": "Sain aru!", "connection.diconnected.page1.subtitle": "Zeal töötab <PERSON>, kus töötab MetaMask. Lihtsalt ühenda nagu MetaMaskiga.", "connection.diconnected.page1.title": "Kuidas Zealiga ühendada?", "connection.diconnected.page2.subtitle": "<PERSON><PERSON><PERSON> palju valiku<PERSON>. <PERSON><PERSON> võib olla üks neist. Kui Zeali ei ilmu...", "connection.diconnected.page2.title": "Klõpsa Ühend<PERSON> r<PERSON>", "connection.diconnected.page3.subtitle": "Pakume ühendust Zealiga. Ka brauser või Injected peaksid töötama. Proovi järele!", "connection.diconnected.page3.title": "Vali MetaMask", "connectionSafetyCheck.tag.caution": "Ettevaatust", "connectionSafetyCheck.tag.danger": "<PERSON>tl<PERSON>", "connectionSafetyCheck.tag.passed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectionSafetyConfirmation.subtitle": "Oled kindel, et soovid jätkata?", "connectionSafetyConfirmation.title": "See sait tundub ohtlik", "connection_state.connect.cancel": "<PERSON><PERSON><PERSON><PERSON>", "connection_state.connect.changeToMetamask": "Vaheta MetaMaskile 🦊", "connection_state.connect.changeToMetamask.label": "Vaheta MetaMaskile", "connection_state.connect.connect_button": "<PERSON><PERSON><PERSON>", "connection_state.connect.expanded.connected": "Ü<PERSON><PERSON><PERSON>", "connection_state.connect.expanded.title": "<PERSON><PERSON><PERSON>", "connection_state.connect.safetyChecksLoading": "<PERSON><PERSON> kont<PERSON>", "connection_state.connect.safetyChecksLoadingError": "Ohutuskontrolli ei õnnestunud lõpule viia", "connection_state.connected.expanded.disconnectButton": "<PERSON><PERSON><PERSON> lahti", "connection_state.connected.expanded.title": "Ü<PERSON><PERSON><PERSON>", "copied-diagnostics": "Diagnostika kopeeritud", "copy-diagnostics": "Kopeeri diagnostika", "counterparty.component.add_recipient_primary_text": "<PERSON>", "counterparty.country": "Riik", "counterparty.countryTitle": "Saaja riik", "counterparty.currency": "<PERSON><PERSON><PERSON>", "counterparty.delete.success.title": "Eemaldatud", "counterparty.edit.success.title": "<PERSON><PERSON><PERSON><PERSON> salves<PERSON>", "counterparty.errors.country_required": "Riik on nõutud", "counterparty.errors.first_name.invalid": "<PERSON><PERSON><PERSON><PERSON> peab olema pikem", "counterparty.errors.last_name.invalid": "Perek<PERSON>nan<PERSON><PERSON> peab olema pikem", "counterparty.first_name": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "<PERSON><PERSON> panka", "countrySelector.noCountryFound": "Riiki ei leitud", "countrySelector.title": "Vali riik", "create-passkey.cta": "Loo pääsuvõti", "create-passkey.extension.cta": "Jätka", "create-passkey.footnote": "<PERSON><PERSON><PERSON>", "create-passkey.mobile.cta": "<PERSON><PERSON><PERSON>", "create-passkey.steps.enable-recovery": "Seadista pilvetaaste", "create-passkey.steps.setup-biometrics": "Luba biomeetriline turvalisus", "create-passkey.subtitle": "Pää<PERSON><PERSON><PERSON><PERSON><PERSON> on turvalisemad kui paroolid ja pilves krüpt<PERSON>ud, et neid oleks lihtne taastada.", "create-passkey.title": "<PERSON><PERSON> oma konto", "create-smart-wallet": "Loo Smart Wallet", "create-userop.progress.text": "<PERSON><PERSON><PERSON>", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Jätka Gnosis Pay's", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Mine Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>i tellimine Gnosis Pay's.", "create_recharge_preferences.card": "<PERSON><PERSON>", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "<PERSON><PERSON> {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "<PERSON><PERSON> raha", "create_recharge_preferences.link_accounts_title": "<PERSON><PERSON> kont<PERSON>", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "Sinu kaarti EI laeta automaatselt pärast igat makset.", "create_recharge_preferences.not_configured_title": "<PERSON>i ja kuluta", "create_recharge_preferences.recharge_from_earn_accounts_description": "<PERSON>u kaart laeb end automaatselt pärast igat makset sinu Earn-kontolt.", "create_recharge_preferences.subtitle": "aastas", "creating-account.loading": "Konto loomine", "creating-gnosis-pay-account": "Loon kontot", "currencies.bridge.select_routes.emptyState": "Selle bridge'i jaoks marsruute ei leitud", "currency.add_currency.add_token": "<PERSON> token", "currency.add_currency.not_a_valid_address": "See ei ole kehtiv tokeni aadress", "currency.add_currency.token_decimals_feild": "Tokeni k<PERSON>", "currency.add_currency.token_feild": "<PERSON><PERSON><PERSON> aadress", "currency.add_currency.token_symbol_feild": "Tokeni sümbol", "currency.add_currency.update_token": "Uuenda tokenit", "currency.add_custom.remove_token.cta": "<PERSON><PERSON><PERSON>", "currency.add_custom.remove_token.header": "<PERSON><PERSON><PERSON> token", "currency.add_custom.remove_token.subtitle": "<PERSON><PERSON>, kuid saldo j<PERSON><PERSON>b alles.", "currency.add_custom.token_removed": "<PERSON><PERSON> e<PERSON>d", "currency.add_custom.token_updated": "<PERSON><PERSON> u<PERSON>d", "currency.balance_label": "Saldo: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "<PERSON><PERSON> pangaülekandega on edukalt kantud {fiat} , millest sai {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "<PERSON>d kätte saanud {crypto}", "currency.bankTransfer.deposit_status.success": "<PERSON><PERSON><PERSON>", "currency.bankTransfer.deposit_status.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.check_bank_account": "Kontrolli oma pangakontot", "currency.bankTransfer.off_ramp.complete": "Val<PERSON>", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Saadan sinu panka", "currency.bankTransfer.off_ramp.transferring_to_currency": "<PERSON><PERSON><PERSON> valuuta<PERSON> {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Raha peaks olema nüüdseks sinu pangakontole jõudnud.", "currency.bankTransfer.withdrawal_status.success": "Saadetud sinu panka", "currency.bankTransfer.withdrawal_status.title": "Väljamakse", "currency.bank_transfer.create_unblock_user.email": "E-posti aadress", "currency.bank_transfer.create_unblock_user.email_invalid": "Vigane e-post", "currency.bank_transfer.create_unblock_user.email_missing": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_missing": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "<PERSON><PERSON><PERSON> on ainult tähed, numbrid, tü<PERSON>kud ja - . , & ( ) '.", "currency.bank_transfer.create_unblock_user.last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_missing": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "<PERSON><PERSON><PERSON> on ainult tähed, numbrid, tü<PERSON>kud ja - . , & ( ) '.", "currency.bank_transfer.create_unblock_user.note": "Jätkates nõustud <PERSON>'i (meie panganduspartner) <terms>Tingimustega</terms> ja <policy>Privaatsuspoliitikaga</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "<PERSON><PERSON><PERSON><PERSON> oma nimi tä<PERSON>lt nii, nagu see on sinu pangakontol", "currency.bank_transfer.create_unblock_user.title": "<PERSON>o oma pangakonto", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Ko<PERSON>umber", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "<PERSON><PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Eelistatud valuuta", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Sorteerimis<PERSON><PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.success": "<PERSON><PERSON>dis<PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.title": "<PERSON>o oma pangakonto", "currency.bank_transfer.residence-form.address-required": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.address-unsupported-char": "Ainult tähed, numbrid, tühikud ja , ; {apostrophe} - \\\\ on lubatud.", "currency.bank_transfer.residence-form.city-required": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.city-unsupported-char": "Ainult tähed, numbrid, t<PERSON><PERSON>kud ja . , - & ( ) {apostrophe} on lubatud.", "currency.bank_transfer.residence-form.postcode-invalid": "Vigane postiindeks", "currency.bank_transfer.residence-form.postcode-required": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.validation.invalid.account_number": "<PERSON><PERSON>e kontonumber", "currency.bank_transfer.validation.invalid.iban": "Vigane IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Vigane sorteerimiskood", "currency.bridge.amount_label": "Bridge'itav summa", "currency.bridge.best_returns.subtitle": "<PERSON><PERSON><PERSON>, arvestades kõiki tasusid.", "currency.bridge.best_returns_popup.title": "<PERSON><PERSON>", "currency.bridge.bridge_from": "<PERSON><PERSON>", "currency.bridge.bridge_gas_fee_loading_failed": "Võrgutasu laadimisel esines viga", "currency.bridge.bridge_low_slippage": "Väga madal slippage. <PERSON><PERSON><PERSON>", "currency.bridge.bridge_provider": "Ülekande p<PERSON>", "currency.bridge.bridge_provider_loading_failed": "Pakkujate laadimisel esines probleeme", "currency.bridge.bridge_settings": "Bridge'i seaded", "currency.bridge.bridge_status.subtitle": "<PERSON><PERSON><PERSON><PERSON> {name}", "currency.bridge.bridge_status.title": "Bridge", "currency.bridge.bridge_to": "<PERSON><PERSON>", "currency.bridge.fastest_route_popup.subtitle": "See pakkuja pakub kiire<PERSON>t ma<PERSON>ru<PERSON>.", "currency.bridge.fastest_route_popup.title": "<PERSON><PERSON><PERSON>", "currency.bridge.from": "<PERSON><PERSON>", "currency.bridge.success": "Lõpetatud", "currency.bridge.title": "Bridge", "currency.bridge.to": "<PERSON><PERSON>", "currency.bridge.topup": "<PERSON> {symbol}", "currency.bridge.withdrawal_status.title": "Väljamakse", "currency.card.card_top_up_status.title": "<PERSON> kaardile raha", "currency.destination_amount": "<PERSON><PERSON><PERSON><PERSON> summa", "currency.hide_currency.confirm.subtitle": "Peida token portfellist. <PERSON><PERSON> selle taastada.", "currency.hide_currency.confirm.title": "Peida token", "currency.hide_currency.success.title": "<PERSON><PERSON> p<PERSON>", "currency.label": "<PERSON><PERSON><PERSON> (valikuline)", "currency.last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.max_loading": "Max:", "currency.swap.amount_to_swap": "Vahetatav summa", "currency.swap.best_return": "Parima tootlusega marsruut", "currency.swap.destination_amount": "<PERSON><PERSON><PERSON><PERSON> summa", "currency.swap.header": "Vaheta", "currency.swap.max_label": "Saldo: {amount}", "currency.swap.provider.header": "Vahetuse pak<PERSON>", "currency.swap.select_to_token": "Vali token", "currency.swap.swap_gas_fee_loading_failed": "Võrgutasu laadimisel esines viga", "currency.swap.swap_provider_loading_failed": "Pakkujate laadimisel esines viga", "currency.swap.swap_settings": "<PERSON><PERSON><PERSON><PERSON> seaded", "currency.swap.swap_slippage_too_low": "Väga madal libisemine. Proovi seda suurendada.", "currency.swaps_io_native_token_swap.subtitle": "Kasutades Swaps.IO", "currency.swaps_io_native_token_swap.title": "Saatmine", "currency.withdrawal.amount_from": "<PERSON><PERSON>", "currency.withdrawal.amount_to": "<PERSON><PERSON>", "currencySelector.title": "Vali valuuta", "dApp.wallet-does-not-support-chain.subtitle": "<PERSON>u rahakott ei paista toetavat {network}. Proovi teist rahakotti või kasuta <PERSON>.", "dApp.wallet-does-not-support-chain.title": "Mittetoetatud võrk", "dapp.connection.manage.confirm.disconnect.all.cta": "<PERSON><PERSON><PERSON>", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Oled kindel, et soovid kõik ühendused lahti ühendada?", "dapp.connection.manage.confirm.disconnect.all.title": "Ühenda kõik lahti", "dapp.connection.manage.connection_list.main.button.title": "<PERSON><PERSON><PERSON> lahti", "dapp.connection.manage.connection_list.no_connections": "<PERSON> <PERSON> ühend<PERSON> rakend<PERSON>i", "dapp.connection.manage.connection_list.section.button.title": "Ühenda kõik lahti", "dapp.connection.manage.connection_list.section.title": "Aktiivsed", "dapp.connection.manage.connection_list.title": "<PERSON><PERSON><PERSON>", "dapp.connection.manage.disconnect.success.title": "Raken<PERSON>ed lahti ühendatud", "dapp.metamask_mode.title": "MetaMaski režiim", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "Intress", "dc25-card-marketing-card.right.subtitle": "100 inimesele", "dc25-card-marketing-card.title": "Esimesed 100, kes kulutavad 50 €, teenivad {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Sa ei saa seda toimingut 3 minuti jooksul teha. Turvakaalutlustel võtab kaardi seadete muutmine või väljamaksete tegemine aega 3 minutit.", "delayQueueBusyBanner.processing-yout-action.title": "Töötleme sinu toimingut, palun oota", "delayQueueBusyWidget.cardFrozen": "<PERSON><PERSON>", "delayQueueBusyWidget.processingAction": "Sinu tegevust töödeldakse", "delayQueueFailedBanner.action-incomplete.get-support": "<PERSON><PERSON><PERSON> abi", "delayQueueFailedBanner.action-incomplete.subtitle": "Vabandust, sinu väljamakse või seadete uuendamisega tekkis viga. Palun võta ühendust klienditoega Discordis.", "delayQueueFailedBanner.action-incomplete.title": "Toiming j<PERSON><PERSON>", "delayQueueFailedWidget.actionIncomplete.title": "<PERSON><PERSON><PERSON> te<PERSON>", "delayQueueFailedWidget.cardFrozen.subtitle": "<PERSON><PERSON>", "delayQueueFailedWidget.contactSupport": "Võta ühend<PERSON> toega", "delay_queue_busy.subtitle": "Turvakaalutlustel võtab kaardi seadete muutmine või väljamaksete tegemine aega 3 minutit. <PERSON><PERSON> aja jooksul on sinu kaart külmutatud.", "delay_queue_busy.title": "Sinu tegevust töödeldakse", "delay_queue_failed.contact_support": "<PERSON><PERSON><PERSON> abi", "delay_queue_failed.subtitle": "Vabandust, sinu väljamakse või seadete uuendamisega tekkis viga. Palun võta ühendust klienditoega Discordis.", "delay_queue_failed.title": "Võta ühendust klienditoega", "deploy-earn-form-smart-wallet.in-progress.title": "Valmistan <PERSON><PERSON><PERSON>", "deposit": "Sissemakse", "disconnect-card-popup.cancel": "<PERSON><PERSON><PERSON><PERSON>", "disconnect-card-popup.disconnect": "<PERSON><PERSON><PERSON> lahti", "disconnect-card-popup.subtitle": "See eemaldab sinu kaardi Zeali rakendusest. Sinu rahakott jääb Gnosis Pay rakenduses kaardiga ühendatuks. <PERSON>ad oma kaardi igal ajal uuesti ühendada.", "disconnect-card-popup.title": "<PERSON><PERSON><PERSON> kaart lahti", "distance.long.days": "{count} p<PERSON><PERSON>", "distance.long.hours": "{count} tundi", "distance.long.minutes": "{count} minutit", "distance.long.months": "{count} kuud", "distance.long.seconds": "{count} sekundit", "distance.long.years": "{count} aastat", "distance.short.days": "{count} p", "distance.short.hours": "{count} t", "distance.short.minutes": "{count} min", "distance.short.months": "{count} k", "distance.short.seconds": "{count} s", "distance.short.years": "{count} a", "duration.short.days": "{count}p", "duration.short.hours": "{count}t", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "Sissemakse", "earn-deposit-view.into": "Sihtkoht", "earn-deposit-view.to": "<PERSON><PERSON>", "earn-deposit.swap.transfer-provider": "Ülekande p<PERSON>", "earn-taker-investment-details.accrued-realtime": "<PERSON><PERSON><PERSON>", "earn-taker-investment-details.asset-class": "Varaklass", "earn-taker-investment-details.asset-coverage-ratio": "Varade kattekordaja", "earn-taker-investment-details.asset-reserve": "Varade reserv", "earn-taker-investment-details.base_currency.label": "Baasvaluuta", "earn-taker-investment-details.chf.description": "Teeni intressi oma CHF-id<PERSON>t, hoiustades zCHF-i Frankencoini – usaldusväärsesse digitaalsesse rahaturgu. Intressi teenitakse Frankencoini madala riskiga, ületagatud laenudelt ja makstakse välja reaalajas. <PERSON>u raha on turvaliselt eraldi al<PERSON>, mida kontrollid ainult sina.", "earn-taker-investment-details.chf.description.with_address_link": "Teeni intressi oma CHF-id<PERSON>t, hoiustades zCHF-i Frankencoini – usaldusväärsesse digitaalsesse rahaturgu. Intressi teenitakse Frankencoini madala riskiga, ületagatud laenudelt ja makstakse välja reaalajas. Sinu raha on turvaliselt eraldi alamkontol <link>(kopeeri 0x)</link> , mida kontrollid ainult sina.", "earn-taker-investment-details.chf.label": "Digitaalne Šveitsi frank", "earn-taker-investment-details.collateral-composition": "<PERSON>ati<PERSON> k<PERSON>", "earn-taker-investment-details.depositor-obligations": "<PERSON><PERSON><PERSON><PERSON><PERSON> koh<PERSON>", "earn-taker-investment-details.eure.description": "Teeni oma eurodelt intressi, hoiustades EURe usaldusväärsele digitaalsele rahaturule Aave. EURe on täielikult reguleeritud euro stabiilsusmünt, mille on välja andnud Monerium ja mis on 1:1 tagatud kaitstud kontodel. Intressi teenitakse Aave madala riskiga, ületagatud laenudelt ja makstakse välja reaalajas. Sinu vahendid on turvalisel alamkontol, mida kontrollid ainult sina.", "earn-taker-investment-details.eure.description.with_address_link": "Teeni oma eurodelt intressi, hoiustades EURe usaldusväärsele digitaalsele rahaturule Aave. EURe on täielikult reguleeritud euro stabiilsusmünt, mille on välja andnud Monerium ja mis on 1:1 tagatud kaitstud kontodel. Intressi teenitakse Aave madala riskiga, ületagatud laenudelt ja makstakse välja reaalajas. Sinu vahendid on turvalisel alamkontol <link>(kopeeri 0x)</link> , mida kontrollid ainult sina.", "earn-taker-investment-details.eure.label": "Digitaalne euro (EURe)", "earn-taker-investment-details.faq": "KKK", "earn-taker-investment-details.fixed-income": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.issuer": "Väljaandja", "earn-taker-investment-details.key-facts": "Põhifaktid", "earn-taker-investment-details.liquidity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.operator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.projected-yield": "Prognoositav aastatootlus", "earn-taker-investment-details.see-other-faq": "Vaata kõiki KKK-sid", "earn-taker-investment-details.see-realtime": "<PERSON><PERSON><PERSON> and<PERSON><PERSON>", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Viimase 12 kuu tootlus", "earn-taker-investment-details.total-collateral": "Tagatise koguväärtus", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "ZCHF-i kogupakkumine", "earn-taker-investment-details.total_deposits": "<PERSON>ave hoiused kokku", "earn-taker-investment-details.usd.description": "Sky on digitaalne rahaturg, mis pak<PERSON> stabii<PERSON>, USA dollarites nomineeritud tootlust lühiajalistelt USA riigivõlakirjadelt ja ületagatud laenudelt – ilma krüptovaluuta volatiilsuseta, 24/7 ligipääsuga vahenditele ja läbi<PERSON>, ahelasisese tagatisega.", "earn-taker-investment-details.usd.description.with_address_link": "Sky on digitaalne rahaturg, mis pak<PERSON> stabii<PERSON>et, USA dollarites nomineeritud tootlust lühiajalistelt USA riigivõlakirjadelt ja ületagatud laenudelt – ilma krüptovaluuta volatiilsuseta, 24/7 ligipääsuga vahenditele ja lä<PERSON>, ahelasisese tagatisega. Investeeringud on alamkontol <link>(kopeeri 0x)</link> , mida kontrollid sina.", "earn-taker-investment-details.usd.ftx-difference": "Milles see erineb FTX-ist, Celsiusest, BlockFi-st või Lunast?", "earn-taker-investment-details.usd.high-returns": "<PERSON><PERSON> saab tootlus olla nii kõ<PERSON>, eriti võ<PERSON>des tavapankadega?", "earn-taker-investment-details.usd.how-is-backed": "Kuidas on Sky USD tagatud ja mis saab mu rahast Zeali pan<PERSON>roti korral?", "earn-taker-investment-details.usd.income-sources": "Tuluallikad 2024", "earn-taker-investment-details.usd.insurance": "<PERSON><PERSON> mu vahendid on kindlustatud või tagatud (nt FDIC poolt)?", "earn-taker-investment-details.usd.label": "Digitaalne USA dollar", "earn-taker-investment-details.usd.lose-principal": "Kas ma võin re<PERSON>selt oma põhiosa kaotada ja mis tingimustel?", "earn-taker-investment-details.variable-rate": "Muutuva intressimääraga laenamine", "earn-taker-investment-details.withdraw-anytime": "Väljamakse igal ajal", "earn-taker-investment-details.yield": "<PERSON><PERSON><PERSON>", "earn-withdrawal-view.approve.for": "Mille jaoks", "earn-withdrawal-view.approve.into": "Sihtkoht", "earn-withdrawal-view.swap.into": "Sihtkoht", "earn-withdrawal-view.withdraw.to": "<PERSON><PERSON>", "earn.add_another_asset.title": "<PERSON><PERSON> teen<PERSON> vara", "earn.add_asset": "<PERSON> vara", "earn.asset_view.title": "<PERSON><PERSON>", "earn.base-currency-popup.text": "Baasvaluuta on see, kuidas sinu sissemakseid, tootlust ja tehinguid väärtustatakse ja registreeritakse. Kui teed sissemakse muus valuutas (nt EUR-i USD-sse), konverteeritakse sinu vahendid kohe kehtivate vahetuskursside alusel baasvaluutasse. Pärast konverteerimist j<PERSON><PERSON><PERSON> sinu saldo baasvaluutas stabiilseks, kuid tulevased väljamaksed võivad taas kaasa tuua valuuta konverteerimise.", "earn.base-currency-popup.title": "Baasvaluuta", "earn.card-recharge.disabled.list-item.title": "Automaatne laadimine väljas", "earn.card-recharge.enabled.list-item.title": "Automaatne laadimine sees", "earn.choose_wallet_to_deposit.title": "Sissemakse kontolt", "earn.config.currency.eth": "<PERSON><PERSON>", "earn.config.currency.on_chain_address_subtitle": "<PERSON><PERSON><PERSON><PERSON> aadress", "earn.config.currency.us_dollars": "Seadista pangaülekanded", "earn.configured_widget.current_apy.title": "Praegune APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} A<PERSON><PERSON>", "earn.confirm.currency.cta": "Sissemakse", "earn.currency.eth": "<PERSON><PERSON>", "earn.deploy.status.title": "<PERSON>o E<PERSON>n konto", "earn.deploy.status.title_with_taker": "Loo {title} Earn konto", "earn.deposit": "Sissemakse", "earn.deposit.amount_to_deposit": "Sissemakstav summa", "earn.deposit.deposit": "Sissemakse", "earn.deposit.enter_amount": "<PERSON><PERSON><PERSON> summa", "earn.deposit.no_routes_found": "Marsruute ei leitud", "earn.deposit.not_enough_balance": "Saldost ei piisa", "earn.deposit.select-currency.title": "Vali token sissemakseks", "earn.deposit.select_account.title": "Vali Earn konto", "earn.desposit_form.title": "<PERSON>ssemaks<PERSON>programmi", "earn.earn_deposit.status.title": "Sissemakse Earn kontole", "earn.earn_deposit.trx.title": "Sissemakse Earn kontole", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "<PERSON><PERSON><PERSON> raha v<PERSON><PERSON>ja igal ajal", "earn.earn_withdraw.status.title": "Väljamakse Earn kontolt", "earn.earn_withdraw.trx.title.approval": "<PERSON><PERSON><PERSON>", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Väljamakse varasse {asset}", "earn.earn_withdraw.trx.title.withdrawal": "Väljamakse Earn kontolt", "earn.recharge.cta": "<PERSON><PERSON><PERSON> muudatus<PERSON>", "earn.recharge.earn_not_configured.enable_some_account.error": "Luba konto", "earn.recharge.earn_not_configured.enter_amount.error": "<PERSON><PERSON><PERSON> summa", "earn.recharge.select_taker.header": "<PERSON>e kaarti järjekorras kontodelt", "earn.recharge_card_tag.on": "sees", "earn.recharge_card_tag.recharge": "Laadimine", "earn.recharge_card_tag.recharge_not_configured": "Automaatne laadimine", "earn.recharge_card_tag.recharge_off": "Laadimine väljas", "earn.recharge_card_tag.recharged": "<PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharging": "Laadimine...", "earn.recharge_configured.disable.trx.title": "Lülita automaatne laadimine välja", "earn.recharge_configured.trx.disclaimer": "<PERSON><PERSON>i kasutades luuakse Cowswapi oksjon, et osta sinu maksega sama summa eest vara sinu Earn kontolt. See oksjoniprotsess tagab tavaliselt parima turuhinna, kuid pea meeles, et ahelas olev kurss võib erineda reaalsetest vahetuskurssidest.", "earn.recharge_configured.trx.subtitle": "Pärast igat makset lisatakse sinu Earn kontolt (kontodelt) automaatselt raha, et hoida kaardi saldot summal {value}", "earn.recharge_configured.trx.title": "Määra automaatne laadimine summale {value}", "earn.recharge_configured.updated.trx.title": "Salvesta laadimise seaded", "earn.risk-banner.subtitle": "See on priva<PERSON>ne toode, millel puudub regulatiivne kaitse kahjude vastu.", "earn.risk-banner.title": "Mõista riske", "earn.set_recharge.status.title": "Määra automaatne laadimine", "earn.setup_reacharge.input.disable.label": "<PERSON><PERSON>", "earn.setup_reacharge.input.label": "<PERSON><PERSON><PERSON>", "earn.setup_reacharge_form.title": "Automaatne laadimine hoiab sinu {br} kaardi saldo samal tasemel", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Hoia wstETH-d (Staked ETH) Gnosis Chainis ja laena <PERSON>do kaudu.", "earn.taker-bulletlist.point_1": "<PERSON><PERSON> {apy<PERSON><PERSON>ue} aastas. <PERSON><PERSON><PERSON> s<PERSON> turust.", "earn.taker-bulletlist.point_3": "Zeal ei võta ta<PERSON>.", "earn.taker-historical-returns": "<PERSON><PERSON><PERSON><PERSON><PERSON>us", "earn.taker-historical-returns.chf": "CHF-i väärtuse kasv USD suhtes", "earn.taker-investment-tile.apy.perYear": "aastas", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "Sissemakse", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "<PERSON><PERSON><PERSON> kaudu <PERSON>", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Frankencoiniga teenimine <PERSON>", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "<PERSON><PERSON><PERSON> ka<PERSON>u <PERSON>", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "<PERSON><PERSON><PERSON> <PERSON><PERSON> kaudu <PERSON>", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "Eth earn", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH Earn", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "USA võlakirjad (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "<PERSON><PERSON> see on äärmiselt haruldane, on see teoreetiliselt võimalik. Sinu vahendeid kaitsevad range riskijuhtimine ja kõrge tagatise määr. Kõige realistlikum must stsenaarium hõlmaks enneolematuid turutingimusi, näiteks mitme stabiilsusmündi samaaegset seotuse kaotamist – midagi, mida pole kunagi varem juhtunud.", "earn.usd.can-I-lose-my-principal-popup.title": "Kas ma võin re<PERSON>selt oma põhiosa kaotada ja mis tingimustel?", "earn.usd.ftx-difference-popup.text": "Sky on põhimõtteliselt erinev. Erinevalt FTX-ist, Celsiusest, BlockFi-st võ<PERSON>, mis tuginesid tugevalt tsentraliseeritud hoidmisele, läbipaistmatule varahaldusele ja riskantsetele võimendatud positsioonidele, kasutab Sky USD läbipaistvaid, auditeeritud ja detsentraliseeritud nutilepinguid ning tagab täieliku ahelasisese läbipaistvuse. Säilitad täieliku kontrolli oma privaatse rahakoti üle, vähendades oluliselt vastaspoole riske, mis on seotud tsentraliseeritud süsteemide ebaõnnestumisega.", "earn.usd.ftx-difference-popup.title": "Milles see erineb FTX-ist, Celsiusest, BlockFi-st või Lunast?", "earn.usd.high-returns-popup.text": "Sky USD teenib tootlust peamiselt detsentraliseeritud rahanduse (DeFi) protokollide kaudu, mis automatiseerivad otse-laenamist ja likviidsuse pakkumist, eemaldades traditsioonilise panganduse üldkulud ja vahendajad. See tõhusus koos tugevate riskikontrollidega võimaldab oluliselt kõrgemat tootlust võrreldes tavapankadega.", "earn.usd.high-returns-popup.title": "<PERSON><PERSON> saab tootlus olla nii kõ<PERSON>, eriti võ<PERSON>des tavapankadega?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD on täielikult tagatud ja ületagatistatud kombinatsiooniga digitaalsetest varadest, mida hoitakse turvalistes nutilepingutes, ja re<PERSON>maailma varadest, nagu USA riigivõlakirjad. Reserve saab auditeerida reaalajas ahelasiseselt isegi Zeali kaudu, mis tagab läbipaistvuse ja turvalisuse. Ebatõenäolisel juhul, kui <PERSON> tegevuse lõpetab, jäävad sinu varad ahelasiseselt turvatuks, täielikult sinu kontrolli alla ja kättesaadavaks teiste ühilduvate rahakottide kaudu.", "earn.usd.how-is-sky-backed-popup.title": "Kuidas on Sky USD tagatud ja mis saab mu rahast Zeali pan<PERSON>roti korral?", "earn.usd.insurance-popup.text": "Sky USD vahendid ei ole FDIC-kindlustatud ega tagatud traditsiooniliste riiklike garantiidega, kuna tegemist on digitaalse varapõhise kontoga, mitte tavalise pangakontoga. Se<PERSON> asemel maandab Sky kõiki riske auditeeritud nutilepingute ja hoolikalt kontrollitud DeFi-protokollide kaudu, tagades varade turvalisuse ja läbipaistvuse.", "earn.usd.insurance-popup.title": "<PERSON><PERSON> mu vahendid on kindlustatud või tagatud (nt FDIC poolt)?", "earn.usd.lending-operations-popup.text": "Sky USD teenib tootl<PERSON>, laena<PERSON> stabiilsus<PERSON><PERSON>nte detsentraliseeritud laenuturgude, nagu Morp<PERSON> ja <PERSON>, kaudu. Sinu stabiilsusmündid laenatakse välja laenuvõtjatele, kes deponeerivad oluliselt rohkem tagatist – näiteks ETH-d või BTC-d – kui nende laenu väärtus. See lähenemine, mida nimetatakse ületagatistamiseks, tagab, et laenude katteks on alati piisavalt tagatist, vähendades oluliselt riski. Laenuvõtjate makstud intressid ja aeg-ajalt likvideerimistasud tagavad usaldusväärse, läbipaistva ja turvalise tulu.", "earn.usd.lending-operations-popup.title": "La<PERSON>utegevu<PERSON>", "earn.usd.market-making-operations-popup.text": "Sky USD teenib lisa<PERSON>, o<PERSON><PERSON><PERSON> detsentraliseeri<PERSON><PERSON> (AMM-id), nagu Curve või Uniswap. Pakkudes likviidsust – paigutades oma stabiilsusmündid krüptokauplemist võimaldavatesse kogumitesse – kogub Sky USD tehingutest teenitud tasusid. Need likviidsuskogumid on hoolikalt valitud volatiilsuse minimeerimiseks, kasutades peamiselt stabiilsusmündi-stabiilsusmündi paare, et oluliselt vähendada riske, nagu ajutine kaotus, hoides sinu varad turvaliselt ja kättesaadavana.", "earn.usd.market-making-operations-popup.title": "Turutegemise operatsioonid", "earn.usd.treasury-operations-popup.text": "Sky USD teenib stabiilset ja järjepidevat tootlust strateegiliste riigikassa investeeringute kaudu. Osa sinu stabiilsusmündi hoiustest paigutatakse turvalistesse, madala riskiga reaalmaailma varadesse – peamiselt lühiajalistesse riigivõlakirjadesse ja üliturvalistesse krediidiinstrumentidesse. See traditsioonilisele pangandusele sarnane lähenemine tagab prognoositava ja usaldusväärse tootluse. Sinu varad jäävad turvaliseks, likviidseks ja läbipaistvalt hallatuks.", "earn.usd.treasury-operations-popup.title": "Riigikassa operatsioonid", "earn.view_earn.card_rechard_off": "<PERSON><PERSON><PERSON><PERSON>", "earn.view_earn.card_rechard_on": "Sees", "earn.view_earn.card_recharge": "<PERSON><PERSON><PERSON>", "earn.view_earn.total_balance_label": "Teenid {percentage} aastas", "earn.view_earn.total_earnings_label": "<PERSON><PERSON><PERSON> kokku", "earn.withdraw": "Võta välja", "earn.withdraw.amount_to_withdraw": "Väljavõetav summa", "earn.withdraw.enter_amount": "<PERSON><PERSON><PERSON> summa", "earn.withdraw.loading": "<PERSON><PERSON><PERSON>", "earn.withdraw.no_routes_found": "Teekondi ei leitud", "earn.withdraw.not_enough_balance": "Saldost ei piisa", "earn.withdraw.select-currency.title": "Vali token", "earn.withdraw.select_to_token": "Vali token", "earn.withdraw.withdraw": "Võta välja", "earn.withdraw_form.title": "Võta välja Earnist", "earnings-view.earnings": "<PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.add-wallet": "<PERSON>", "edit-account-owners.add-owner.add_wallet": "<PERSON>", "edit-account-owners.add-owner.title": "<PERSON>", "edit-account-owners.card-owners": "Kaardiomanikud", "edit-account-owners.external-wallet": "<PERSON><PERSON><PERSON>", "editBankRecipient.title": "<PERSON><PERSON> sa<PERSON>t", "editNetwork.addCustomRPC": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editNetwork.cannot_verify.subtitle": "Kohandatud RPC-s<PERSON><PERSON> ei vasta korralikult. Kontrolli URL-i ja proovi uuesti.", "editNetwork.cannot_verify.title": "RPC-sõlme ei õnnestu kontrollida", "editNetwork.cannot_verify.try_again": "<PERSON>ovi taas", "editNetwork.customRPCNode": "Kohandatud RPC-sõlm", "editNetwork.defaultRPC": "Vaikimisi RPC", "editNetwork.networkRPC": "Võrgu RPC", "editNetwork.rpc_url.cannot_be_empty": "<PERSON><PERSON><PERSON><PERSON>", "editNetwork.rpc_url.not_a_valid_https_url": "Peab olema kehtiv HTTP(S) URL", "editNetwork.safetyWarning.subtitle": "Zeal ei taga kohandatud RPC-de turvalisust. <PERSON><PERSON> oled kindel, et soovid jätkata?", "editNetwork.safetyWarning.title": "Kohandatud RPC võib olla ebaturvaline.", "editNetwork.zealRPCNode": "Zeali RPC-sõlm", "editNetworkRpc.headerTitle": "Kohandatud RPC sõlm", "editNetworkRpc.rpcNodeUrl": "RPC sõlme URL", "editing-locked.modal.description": "Erinevalt kinnitustehingutest ei luba load sul kululimiiti või aegumisaega muuta. Enne loa esitamist veendu, et usaldad dAppi.", "editing-locked.modal.title": "Muutmine luku<PERSON>tud", "enable-recharge-for-smart-wallet.enabling-recharge.title": "<PERSON><PERSON>", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Laadimine lubatud", "enterCardnumber": "<PERSON><PERSON><PERSON> kaardi number", "error.connectivity_error.subtitle": "<PERSON><PERSON><PERSON> kontrolli oma internetiühendust ja proovi uuesti.", "error.connectivity_error.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> puudub", "error.decrypt_incorrect_password.title": "Vale parool", "error.encrypted_object_invalid_format.title": "<PERSON><PERSON><PERSON><PERSON> and<PERSON>", "error.failed_to_fetch_google_auth_token.title": "Me ei saanud juurdepääsu", "error.list.item.cta.action": "<PERSON>ovi uuesti", "error.trezor_action_cancelled.title": "<PERSON><PERSON> lükati tagasi", "error.trezor_device_used_elsewhere.title": "<PERSON><PERSON> ka<PERSON>e teises sessi<PERSON>is", "error.trezor_method_cancelled.title": "<PERSON><PERSON><PERSON>", "error.trezor_permissions_not_granted.title": "<PERSON><PERSON><PERSON>", "error.trezor_pin_cancelled.title": "<PERSON><PERSON><PERSON>", "error.trezor_popup_closed.title": "<PERSON><PERSON><PERSON>", "error.unblock_account_number_and_sort_code_mismatch": "Ko<PERSON><PERSON>ber ja sortimiskood ei kattu", "error.unblock_can_not_change_details_after_kyc": "P<PERSON><PERSON>t isikutuvastust ei saa and<PERSON>d muuta", "error.unblock_hard_kyc_failure": "Ootamatu KYC olek", "error.unblock_invalid_faster_payment_configuration.title": "See pank ei toeta kiiremaid makseid", "error.unblock_invalid_iban": "Vigane IBAN", "error.unblock_session_expired.title": "<PERSON><PERSON>i sessioon aegus", "error.unblock_user_with_address_already_exists.title": "Aadress<PERSON> on konto juba seadistatud", "error.unblock_user_with_such_email_already_exists.title": "Sellise e-postiga kasutaja on juba olemas", "error.unknown_error.error_message": "Veateade: ", "error.unknown_error.subtitle": "Vabandust! Kui vajad kiiret abi, võta palun ühendust toega ja jaga allolevaid üksikasju.", "error.unknown_error.title": "Süsteemiviga", "eth-cost-warning-modal.subtitle": "Nutikad rahakotid töötavad Ethereumis, kuid tasud on väga kõrged ja me soovitame TUNGIVALT kasutada teisi võrke.", "eth-cost-warning-modal.title": "Väldi Ethereumi - võrgutasud on kõrged", "exchange.form.button.chain_unsupported": "Võrk ei ole toe<PERSON>ud", "exchange.form.button.refreshing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exchange.form.error.asset_not_supported.button": "<PERSON>i teine vara", "exchange.form.error.asset_not_supported.description": "Bridge ei toeta selle vara üle viimist.", "exchange.form.error.asset_not_supported.title": "Vara ei toetata", "exchange.form.error.bridge_quote_timeout.button": "<PERSON>i teine vara", "exchange.form.error.bridge_quote_timeout.description": "<PERSON>ovi teist tokenite paari", "exchange.form.error.bridge_quote_timeout.title": "Vahetust ei leitud", "exchange.form.error.different_receiver_not_supported.button": "<PERSON><PERSON>lda alternatiivne saaja", "exchange.form.error.different_receiver_not_supported.description": "See vahetus ei toeta teisele aadressile saatmist.", "exchange.form.error.different_receiver_not_supported.title": "<PERSON><PERSON><PERSON> ja saaja aadress peavad olema samad", "exchange.form.error.insufficient_input_amount.button": "<PERSON><PERSON><PERSON> summat", "exchange.form.error.insufficient_liquidity.button": "<PERSON><PERSON><PERSON><PERSON> summat", "exchange.form.error.insufficient_liquidity.description": "<PERSON><PERSON><PERSON> varasid. Proovi väiksemat summat.", "exchange.form.error.insufficient_liquidity.title": "Summa on liiga suur", "exchange.form.error.max_amount_exceeded.button": "<PERSON><PERSON><PERSON><PERSON> summat", "exchange.form.error.max_amount_exceeded.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> summa on ületatud.", "exchange.form.error.max_amount_exceeded.title": "Summa on liiga suur", "exchange.form.error.min_amount_not_met.button": "<PERSON><PERSON><PERSON> summat", "exchange.form.error.min_amount_not_met.description": "Selle tokeni minimaalne vahetussumma ei ole tä<PERSON>tud.", "exchange.form.error.min_amount_not_met.description_with_amount": "Minima<PERSON>ne vahetussumma on {amount}.", "exchange.form.error.min_amount_not_met.title": "Summa on liiga väike", "exchange.form.error.min_amount_not_met.title_increase": "<PERSON><PERSON><PERSON> summat", "exchange.form.error.no_routes_found.button": "<PERSON>i teine vara", "exchange.form.error.no_routes_found.description": "<PERSON><PERSON><PERSON>/võrgu paarile ma<PERSON> pole.", "exchange.form.error.no_routes_found.title": "<PERSON><PERSON><PERSON> pole saadaval", "exchange.form.error.not_enough_balance.button": "<PERSON><PERSON><PERSON><PERSON> summat", "exchange.form.error.not_enough_balance.description": "<PERSON><PERSON><PERSON><PERSON> pole piisa<PERSON>t seda vara.", "exchange.form.error.not_enough_balance.title": "Saldo ei ole piisav", "exchange.form.error.slippage_passed_is_too_low.button": "<PERSON><PERSON><PERSON>", "exchange.form.error.slippage_passed_is_too_low.description": "<PERSON><PERSON> vara jaoks on slippage liiga madal.", "exchange.form.error.slippage_passed_is_too_low.title": "Slippage liiga madal", "exchange.form.error.socket_internal_error.button": "<PERSON><PERSON><PERSON> hi<PERSON>", "exchange.form.error.socket_internal_error.description": "Bridge'i partneril on probleeme. Proovi hiljem uuesti.", "exchange.form.error.socket_internal_error.title": "Viga Bridge'i partneri ju<PERSON>", "exchange.form.error.stargatev2_requires_fee_in_native": "<PERSON> {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Lisa {amount} te<PERSON><PERSON>", "exchange.form.error.stargatev2_requires_fee_in_native.title": "<PERSON><PERSON><PERSON> rohkem {symbol}", "expiration-info.modal.description": "Aeg<PERSON><PERSON><PERSON>, kui kaua rakendus sinu tokeneid kasutada saab. <PERSON>i aeg saab täis, kaotavad nad juurdepääsu, kuni sa uuesti loa annad. Turvalisuse huvides hoia aegumisaeg lühike.", "expiration-info.modal.title": "Mis on aegumisaeg?", "expiration-time.high.modal.text": "Aegumistähtajad peaksid olema lühikesed ja vastama sellele, kui kaua luba tegelikult vajad. Pikad tähtajad on riskantsed ja annavad kelmidele rohkem võimalusi sinu tokeneid kuritarvitada.", "expiration-time.high.modal.title": "Pikk aegumistähtaeg", "failed.transaction.content": "<PERSON><PERSON> tõenäoliselt ebaõnnestub", "fee.unknown": "Teadmata", "feedback-request.leave-message": "<PERSON><PERSON><PERSON> teade", "feedback-request.not-now": "<PERSON><PERSON> p<PERSON>gu", "feedback-request.title": "Aitäh! Kuidas saaksime <PERSON>i paremaks muuta?", "float.input.period": "Komakoht", "gnosis-activate-card.info-popup.subtitle": "Esimese te<PERSON>u jaoks sisesta kaart ja PIN-kood. Pärast seda toimivad viipemaksed.", "gnosis-activate-card.info-popup.title": "<PERSON><PERSON><PERSON><PERSON> makse vajab kiipi ja PIN-koodi", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "Aktiveerimiseks sisesta oma kaardi number.", "gnosis-activate-card.title": "<PERSON><PERSON><PERSON> number", "gnosis-pay-re-kyc-widget.btn-text": "<PERSON><PERSON><PERSON>", "gnosis-pay-re-kyc-widget.title.not-started": "<PERSON><PERSON><PERSON> o<PERSON> isik", "gnosis-pay.login.cta": "Ühenda o<PERSON>olev konto", "gnosis-pay.login.title": "Sul on Gnosis Pay konto juba olemas", "gnosis-signup.confirm.subtitle": "Gnosis Pay e-kiri võib olla rämpspostis.", "gnosis-signup.confirm.title": "Ei saanud kin<PERSON>?", "gnosis-signup.continue": "Jätka", "gnosis-signup.dont_link_accounts": "<PERSON><PERSON> seo kontosid", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "Si<PERSON>ta <EMAIL>", "gnosis-signup.enter-email.title": "Sisesta e-post", "gnosis-signup.title": "<PERSON><PERSON> ja nõustun Gnosis Pay <linkGnosisTNC>tingimustega</linkGnosisTNC> <monovateTerms>kaardiomaniku tingimustega</monovateTerms> ja <linkMonerium>Moneriumi T&C-ga</linkMonerium>.", "gnosis-signup.verify-email.title": "Kinnita e-post", "gnosis.confirm.subtitle": "Koodi ei tulnud? Kontrolli oma numbrit.", "gnosis.confirm.title": "<PERSON><PERSON> {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "<PERSON><PERSON><PERSON>", "gnosisPayAccountStatus.success.title": "<PERSON><PERSON> imporditud", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay ei ole sinu riigis veel saadaval", "gnosisPayNoActiveCardsFound.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> pole", "gnosis_pay_card_delay_relay_not_empty_error.title": "<PERSON>u tehingut ei saanud hetkel töödel<PERSON>. <PERSON><PERSON><PERSON> proovi hiljem uuesti.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "<PERSON><PERSON> võimalik", "gnosiskyc.modal.approved.activate-free-card": "<PERSON><PERSON><PERSON><PERSON> tasuta kaart", "gnosiskyc.modal.approved.button-text": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>t", "gnosiskyc.modal.approved.title": "<PERSON><PERSON> is<PERSON>u konto andmed on loodud", "gnosiskyc.modal.failed.close": "Sulge", "gnosiskyc.modal.failed.title": "<PERSON><PERSON><PERSON><PERSON>, meie partner <PERSON><PERSON> ei saa sulle kontot luua", "gnosiskyc.modal.in-progress.title": "Isikutuvastus võib võtta 24 tundi või kauem. <PERSON><PERSON><PERSON> ole kanna<PERSON>.", "goToSettingsPopup.settings": "Seaded", "goToSettingsPopup.title": "<PERSON><PERSON> teavitused seadme seadetes igal ajal sisse l<PERSON>ada", "google_file.error.failed_to_fetch_auth_token.button_title": "<PERSON>ovi uuesti", "google_file.error.failed_to_fetch_auth_token.subtitle": "<PERSON>t saaksime kasutada sinu taastefaili, anna palun juurdep<PERSON><PERSON>s oma isiklikus pilves.", "google_file.error.failed_to_fetch_auth_token.title": "Me ei saanud juurdepääsu", "hidden_tokens.widget.emptyState": "<PERSON><PERSON><PERSON><PERSON>", "how_to_connect_to_metamask.got_it": "OK, sain aru", "how_to_connect_to_metamask.story.subtitle": "Vaheta Zeali ja teiste rahak<PERSON>ide vahel igal ajal.", "how_to_connect_to_metamask.story.title": "Zeal tö<PERSON>tab koos teiste rahakottidega", "how_to_connect_to_metamask.why_switch": "<PERSON><PERSON> vahetada <PERSON>i ja teiste rahak<PERSON> vahel?", "how_to_connect_to_metamask.why_switch.description": "Olenemata sellest, millise rahakoti valid, ka<PERSON>evad sind alati Zeali turvakontrollid pahatahtlike saitide ja tehingute eest.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "Teame, et uue rahakoti kasu<PERSON>tt on raske. Seepärast tegime Zeali kasutamise sinu olemasoleva rahakoti kõrval lihtsaks. Vaheta igal ajal.", "import-bank-transfer-owner.banner.title": "Pangaülekannete rahakott on muutunud. Jätkamiseks impordi rahakott.", "import-bank-transfer-owner.title": "Impordi rahakott pangaülekannete kasutamiseks", "import_gnosispay_wallet.add-another-card-owner.footnote": "Impordi kaardi omaniku võtmed või fraas", "import_gnosispay_wallet.primaryText": "Impordi Gnosis Pay rahakott", "injected-wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "intercom.getHelp": "<PERSON><PERSON><PERSON> abi", "invalid_iban.got_it": "<PERSON><PERSON> aru", "invalid_iban.subtitle": "Sisestatud IBAN ei ole kehtiv. <PERSON><PERSON><PERSON> k<PERSON> and<PERSON> ja proovi uuesti.", "invalid_iban.title": "Vigane IBAN", "keypad-0": "Klahv 0", "keypad-1": "Klahv 1", "keypad-2": "Klahv 2", "keypad-3": "Klahv 3", "keypad-4": "Klahv 4", "keypad-5": "Klahv 5", "keypad-6": "Klahv 6", "keypad-7": "Klahv 7", "keypad-8": "Klahv 8", "keypad-9": "Klahv 9", "keypad.biometric-button": "Klaviatuuri biomeetriline nupp", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "<PERSON><PERSON><PERSON> fraas on turvatud 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "<PERSON><PERSON><PERSON> fraasi", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Hoia oma salajasest fraasist turvalist võrguv<PERSON><PERSON> koop<PERSON>, et saaksid oma varad hiljem taastada", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "Ära proovi sõna ära arvata", "keystore.write_secret_phrase.before_you_begin.first_point": "Mõistan, et <PERSON><PERSON><PERSON><PERSON>, kellel on minu salajane fraas, saab minu varasid üle kanda", "keystore.write_secret_phrase.before_you_begin.second_point": "Vastutan oma salajase fraasi salajas ja turvalisena hoidmise eest", "keystore.write_secret_phrase.before_you_begin.subtitle": "<PERSON><PERSON><PERSON> loe läbi ja nõustu järgmiste punktidega:", "keystore.write_secret_phrase.before_you_begin.third_point": "Olen pri<PERSON> kohas, kus <PERSON> teisi inimesi ega kaameraid", "keystore.write_secret_phrase.before_you_begin.title": "<PERSON><PERSON>", "keystore.write_secret_phrase.secret_phrase_test.title": "Mis on sõna nr {count} sinu salajases fraasis?", "keystore.write_secret_phrase.test_ps.lets_do_it": "<PERSON><PERSON><PERSON>", "keystore.write_secret_phrase.test_ps.subtitle": "Sul on vaja oma salajast fraasi, et taastada oma konto selles või teistes seadmetes. <PERSON><PERSON><PERSON>ime, kas sinu salajane fraas on õigesti üles kirjutatud.", "keystore.write_secret_phrase.test_ps.subtitle2": "<PERSON><PERSON><PERSON><PERSON> sinult {count} s<PERSON>na sinu fraasist.", "keystore.write_secret_phrase.test_ps.title": "<PERSON><PERSON> taastamise testimine", "kyc.modal.approved.button-text": "<PERSON><PERSON>", "kyc.modal.approved.subtitle": "<PERSON><PERSON> isik on kinnitatud. <PERSON><PERSON> nüüd teha pii<PERSON>ta pan<PERSON>ü<PERSON>de<PERSON>.", "kyc.modal.approved.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> avatud", "kyc.modal.continue-with-partner.button-text": "Jätka", "kyc.modal.continue-with-partner.subtitle": "Suuname sind nüüd oma partneri juur<PERSON>, et koguda sinu dokumendid ja viia lõpule tuvastamise taotlus.", "kyc.modal.continue-with-partner.title": "Jätka meie partneriga", "kyc.modal.failed.unblock.subtitle": "Unblock ei kinnitanud sinu isikut ja ei saa sulle pangaülekande teenust pakkuda", "kyc.modal.failed.unblock.title": "Unblock'i taotlust ei kinnitatud", "kyc.modal.paused.button-text": "<PERSON><PERSON><PERSON> and<PERSON><PERSON>", "kyc.modal.paused.subtitle": "<PERSON><PERSON><PERSON>, et osa sinu andmetest on valed. <PERSON><PERSON>n proovi uuesti ja kontrolli oma andmed enne esitamist üle.", "kyc.modal.paused.title": "<PERSON><PERSON> and<PERSON> t<PERSON> valed", "kyc.modal.pending.button-text": "Sulge", "kyc.modal.pending.subtitle": "Tavaliselt võtab kontroll aega alla 10 minuti, kuid mõnikord võib see kauem kesta.", "kyc.modal.pending.title": "<PERSON><PERSON>e sind kursis", "kyc.modal.required.cta": "<PERSON><PERSON><PERSON> kin<PERSON>", "kyc.modal.required.subtitle": "Oled jõ<PERSON><PERSON>d <PERSON>. Jätkamiseks palun kinnita oma isik. See võtab tavaliselt paar minutit ning nõuab isikuandmeid ja dokumente.", "kyc.modal.required.title": "<PERSON><PERSON><PERSON> isiku tu<PERSON>", "kyc.submitted": "<PERSON><PERSON><PERSON> es<PERSON>d", "kyc.submitted_short": "<PERSON><PERSON><PERSON><PERSON>", "kyc_status.completed_status": "Val<PERSON>", "kyc_status.failed_status": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc_status.paused_status": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc_status.subtitle": "Pangaülekanded", "kyc_status.subtitle.wrong_details": "Valed andmed", "kyc_status.subtitle_in_progress": "<PERSON><PERSON><PERSON><PERSON>", "kyc_status.title": "<PERSON><PERSON><PERSON> tu<PERSON>", "label.close": "Sulge", "label.saving": "Salvestan...", "labels.this-month": "See kuu", "labels.today": "<PERSON><PERSON><PERSON>", "labels.yesterday": "<PERSON><PERSON>", "language.selector.title": "<PERSON><PERSON>", "ledger.account_loaded.imported": "Imporditud", "ledger.add.success.title": "Ledger on edukalt ühendatud 🎉", "ledger.connect.cta": "Sünkrooni Ledger", "ledger.connect.step1": "<PERSON><PERSON>a Ledger oma seadmega", "ledger.connect.step2": "Ava Ledger<PERSON> rakendus", "ledger.connect.step3": "<PERSON><PERSON><PERSON><PERSON> oma Ledger 👇", "ledger.connect.subtitle": "<PERSON><PERSON><PERSON><PERSON>, et importida oma Ledgeri rahakotid <PERSON>i", "ledger.connect.title": "Ühenda Ledger Zealiga", "ledger.error.ledger_is_locked.subtitle": "Ava Ledger ja E<PERSON><PERSON>i rakendus", "ledger.error.ledger_is_locked.title": "Ledger on lukus", "ledger.error.ledger_not_connected.action": "Sünkrooni Ledger", "ledger.error.ledger_not_connected.subtitle": "Ühenda oma riist<PERSON><PERSON><PERSON> rahakott seadmega ja ava Ethereumi rakendus.", "ledger.error.ledger_not_connected.title": "Ledger pole ühendatud", "ledger.error.ledger_running_non_eth_app.title": "Ethereumi rakendus pole avatud", "ledger.error.user_trx_denied_by_user.action": "Sulge", "ledger.error.user_trx_denied_by_user.subtitle": "Lükkasid te<PERSON>u oma rii<PERSON>varalises rahakotis tagasi.", "ledger.error.user_trx_denied_by_user.title": "<PERSON><PERSON> lükati tagasi", "ledger.hd_path.bip44.subtitle": "nt Metamask, Trezor", "ledger.hd_path.bip44.title": "BIP44 standard", "ledger.hd_path.ledger_live.subtitle": "<PERSON><PERSON><PERSON><PERSON>", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Legacy", "ledger.hd_path.phantom.subtitle": "nt Phantom", "ledger.select.hd_path.subtitle": "HD-rajad on viis, kuidas riistvaralised rahakotid oma kontosid sorteerivad. See sarna<PERSON><PERSON> sisukor<PERSON>e raamatus.", "ledger.select.hd_path.title": "Vali HD-rada", "ledger.select_account.import_wallets_count": "{count,plural,=0{<PERSON><PERSON><PERSON> pole valitud} one{Impordi rahakott} other{Impordi {count} rahakotti}}", "ledger.select_account.path_settings": "<PERSON> seaded", "ledger.select_account.subtitle": "Ei näe oodatud rahakotte? Proovi muuta raja seadeid", "ledger.select_account.subtitle.group_header": "<PERSON><PERSON><PERSON><PERSON>", "ledger.select_account.title": "Impordi Ledgeri rahakotid", "legend.lending-operations": "La<PERSON>utegevu<PERSON>", "legend.market_making-operations": "Turutegemise operatsioonid", "legend.treasury-operations": "Riigikassa operatsioonid", "link-existing-monerium-account-sign.button": "Seo Zealiga", "link-existing-monerium-account-sign.subtitle": "Sul on juba Moneriumi konto olemas.", "link-existing-monerium-account-sign.title": "<PERSON><PERSON> oma Moneriumi kontoga.", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Sul on juba Moneriumi konto. Seadistamise lõpetamiseks mine Moneriumi rakendusse.", "link-existing-monerium-account.title": "Konto sidumiseks mine Moneriumi", "loading.pin": "<PERSON><PERSON><PERSON>kood<PERSON>...", "lockScreen.passwordIncorrectMessage": "Vale parool", "lockScreen.passwordRequiredMessage": "Parool on nõutav", "lockScreen.unlock.header": "Ava", "lockScreen.unlock.subheader": "<PERSON><PERSON><PERSON> a<PERSON> oma par<PERSON>i", "mainTabs.activity.label": "Tegevused", "mainTabs.browse.label": "<PERSON><PERSON>", "mainTabs.browse.title": "<PERSON><PERSON>", "mainTabs.card.label": "<PERSON><PERSON>", "mainTabs.portfolio.label": "Portfell", "mainTabs.rewards.label": "Auhinnad", "makeSpendable.cta": "<PERSON><PERSON><PERSON>", "makeSpendable.holdAsCash": "<PERSON><PERSON> alles", "makeSpendable.shortText": "Teenid {apy} aastas", "makeSpendable.title": "{amount} laekunud", "merchantCategory.agriculture": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.alcohol": "Alkohol", "merchantCategory.antiques": "<PERSON><PERSON>", "merchantCategory.appliances": "Kodumasinad", "merchantCategory.artGalleries": "Kunstigaleriid", "merchantCategory.autoRepair": "Autoremont", "merchantCategory.autoRepairService": "Autoremonditeenus", "merchantCategory.beautyFitnessSpas": "<PERSON><PERSON>, fitness ja spaad", "merchantCategory.beautyPersonalCare": "<PERSON><PERSON> ja isik<PERSON> ho<PERSON>us", "merchantCategory.billiard": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.books": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.bowling": "Bowling", "merchantCategory.businessProfessionalServices": "<PERSON><PERSON>- ja professionaalsed teenused", "merchantCategory.carRental": "Autorent", "merchantCategory.carWash": "Autopesula", "merchantCategory.cars": "Autod", "merchantCategory.casino": "<PERSON><PERSON><PERSON>", "merchantCategory.casinoGambling": "<PERSON><PERSON><PERSON> ja has<PERSON>", "merchantCategory.cellular": "Mobiilside", "merchantCategory.charity": "Heategevus", "merchantCategory.childcare": "Lastehoid", "merchantCategory.cigarette": "Sigaretid", "merchantCategory.cinema": "<PERSON><PERSON>", "merchantCategory.cinemaEvents": "<PERSON><PERSON> ja s<PERSON><PERSON><PERSON>", "merchantCategory.cleaning": "<PERSON><PERSON><PERSON>", "merchantCategory.cleaningMaintenance": "<PERSON><PERSON><PERSON> ja hooldus", "merchantCategory.clothes": "Riided", "merchantCategory.clothingServices": "Rõivasteenused", "merchantCategory.communicationServices": "Sideteenused", "merchantCategory.construction": "<PERSON><PERSON><PERSON>", "merchantCategory.cosmetics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.craftsArtSupplies": "Käsitöö- ja kunsti<PERSON>bed", "merchantCategory.datingServices": "Tutvumisteenused", "merchantCategory.delivery": "Kohaletoimetamine", "merchantCategory.dentist": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.departmentStores": "Kaubamajad", "merchantCategory.directMarketingSubscription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ja tellimused", "merchantCategory.discountStores": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.drugs": "<PERSON><PERSON><PERSON>", "merchantCategory.dutyFree": "Duty Free", "merchantCategory.education": "<PERSON><PERSON>", "merchantCategory.electricity": "Elekter", "merchantCategory.electronics": "Elektroonika", "merchantCategory.emergencyServices": "Hädaabiteenused", "merchantCategory.equipmentRental": "Seadmete rent", "merchantCategory.evCharging": "Elektriauto <PERSON>", "merchantCategory.financialInstitutions": "Finantsasutused", "merchantCategory.financialProfessionalServices": "Finants- ja kutseteen<PERSON>", "merchantCategory.finesPenalties": "<PERSON><PERSON><PERSON> ja viivised", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "<PERSON><PERSON><PERSON>", "merchantCategory.flowers": "Lilled", "merchantCategory.flowersGarden": "<PERSON>d ja aed", "merchantCategory.food": "Toit", "merchantCategory.freight": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.fuel": "<PERSON><PERSON><PERSON>", "merchantCategory.funeralServices": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.furniture": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.games": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.gas": "<PERSON><PERSON><PERSON>", "merchantCategory.generalMerchandiseRetail": "Üldkaup ja jae<PERSON><PERSON>", "merchantCategory.gifts": "Kingitused", "merchantCategory.government": "<PERSON><PERSON><PERSON>", "merchantCategory.governmentServices": "Riigiteenused", "merchantCategory.hardware": "<PERSON><PERSON><PERSON>- ja maja<PERSON><PERSON>", "merchantCategory.healthMedicine": "<PERSON><PERSON><PERSON> ja meditsiin", "merchantCategory.homeImprovement": "<PERSON><PERSON> remont", "merchantCategory.homeServices": "<PERSON><PERSON><PERSON>", "merchantCategory.hotel": "Hotell", "merchantCategory.housing": "<PERSON><PERSON><PERSON>", "merchantCategory.insurance": "<PERSON><PERSON><PERSON>", "merchantCategory.internet": "Internet", "merchantCategory.kids": "Lapsed", "merchantCategory.laundry": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.laundryCleaningServices": "<PERSON><PERSON><PERSON><PERSON> ja pu<PERSON>", "merchantCategory.legalGovernmentFees": "Õigus- ja r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.luxuries": "Luksuskaubad", "merchantCategory.luxuriesCollectibles": "Luksuskaubad ja kogumisobjektid", "merchantCategory.magazines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.magazinesNews": "<PERSON><PERSON><PERSON><PERSON><PERSON> ja u<PERSON>", "merchantCategory.marketplaces": "Turuplatsid", "merchantCategory.media": "Meedia", "merchantCategory.medicine": "Me<PERSON><PERSON>in", "merchantCategory.mobileHomes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.moneyTransferCrypto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ja krüpto", "merchantCategory.musicRelated": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.musicalInstruments": "Muusikariistad", "merchantCategory.optics": "Optika", "merchantCategory.organizationsClubs": "Organisatsioonid ja klubid", "merchantCategory.other": "<PERSON><PERSON>", "merchantCategory.parking": "<PERSON><PERSON><PERSON>", "merchantCategory.pawnShops": "Pandimajad", "merchantCategory.pets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.photoServicesSupplies": "Fototeenused ja -tarbed", "merchantCategory.postalServices": "Postiteenused", "merchantCategory.professionalServicesOther": "Kutseteenused (muu)", "merchantCategory.publicTransport": "Ühistransport", "merchantCategory.purchases": "<PERSON><PERSON><PERSON>", "merchantCategory.purchasesMiscServices": "<PERSON><PERSON><PERSON> ja mit<PERSON><PERSON><PERSON> teenused", "merchantCategory.recreationServices": "Meelelahutusteenused", "merchantCategory.religiousGoods": "Reli<PERSON><PERSON><PERSON> kaubad", "merchantCategory.secondhandRetail": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "merchantCategory.shoeHatRepair": "Jalatsite ja peakatete parandus", "merchantCategory.shoeRepair": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.softwareApps": "<PERSON><PERSON><PERSON><PERSON> ja rakendused", "merchantCategory.specializedRepairs": "Eriremont", "merchantCategory.sport": "Sport", "merchantCategory.sportingGoods": "Spordikaubad", "merchantCategory.sportingGoodsRecreation": "Spordikaubad ja vaba aeg", "merchantCategory.sportsClubsFields": "Spordiklubid ja -väljakud", "merchantCategory.stationaryPrinting": "<PERSON><PERSON><PERSON><PERSON><PERSON> ja tr<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.stationery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.storage": "Hoiustamine", "merchantCategory.taxes": "<PERSON><PERSON><PERSON>", "merchantCategory.taxi": "<PERSON><PERSON><PERSON>", "merchantCategory.telecomEquipment": "Telekomiseadmed", "merchantCategory.telephony": "Telefoniteenused", "merchantCategory.tobacco": "<PERSON><PERSON><PERSON>", "merchantCategory.tollRoad": "<PERSON><PERSON><PERSON> tee", "merchantCategory.tourismAttractionsAmusement": "<PERSON><PERSON>, vaatamisväärsused ja meele<PERSON>us", "merchantCategory.towing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.toys": "Mänguasjad", "merchantCategory.toysHobbies": "M<PERSON>ngua<PERSON><PERSON><PERSON> ja hobid", "merchantCategory.trafficFine": "Liiklustrahv", "merchantCategory.train": "<PERSON><PERSON>", "merchantCategory.travelAgency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, raadio ja voogedastus", "merchantCategory.utilities": "Kommunaalteenused", "merchantCategory.waterTransport": "Veetransport", "merchantCategory.wholesaleClubs": "Hulgimüügiklubid", "metaMask.subtitle": "Luba MetaMaski režiim, et suunata kõik MetaMaski ühendused Zeali. DAppides MetaMaskile klõpsamine ühendab sind siis Zealiga.", "metaMask.title": "Ei saa Zealiga ühendada?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "<PERSON> oma <PERSON>", "monerium-bank-deposit.buttet-point.receive-crypto": "Võta vastu digitaalseid eurosid", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Saada {fiatCurrencyCode} oma kontole", "monerium-bank-deposit.deposit-account-country": "Riik", "monerium-bank-deposit.header": "{fullName} is<PERSON><PERSON> konto", "monerium-bank-details.account-name": "Konto omaniku nimi", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT kopeeritud", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT kopeeritud", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN kopeeritud", "monerium-bank-details.to-wallet": "<PERSON><PERSON><PERSON><PERSON>", "monerium-bank-details.transfer-fee": "Ülekandetasu", "monerium-bank-transfer.enable-card.bullet-1": "Vii lõpuni isiku tuvastamine", "monerium-bank-transfer.enable-card.bullet-2": "<PERSON><PERSON> is<PERSON><PERSON> konto and<PERSON>", "monerium-bank-transfer.enable-card.bullet-3": "<PERSON><PERSON> sisse<PERSON><PERSON><PERSON> pangakontolt", "monerium-card-delay-relay.success.cta": "Sulge", "monerium-card-delay-relay.success.subtitle": "Turvakaalutlustel võtab kaardi seadete muutmine aega 3 minutit.", "monerium-card-delay-relay.success.title": "Jätka Moneriumi seadistust 3 min pärast.", "monerium-deposit.account-details-info-popup.bullet-point-1": "Iga {fiatCurrencyCode} , mille sa sellele kontole saadad, konverteeritakse automaatselt {cryptoCurrencyCode} tokeniteks {cryptoCurrencyChain} ahelas ja saadetakse sinu rahakotti", "monerium-deposit.account-details-info-popup.bullet-point-2": "SAADA AINULT {fiatCurrencyCode} ({fiatCurrencySymbol}) oma kontole", "monerium-deposit.account-details-info-popup.title": "<PERSON><PERSON> konto and<PERSON>", "monerium.check_order_status.sending": "Saatmisel", "monerium.not-eligible.cta": "Tagasi", "monerium.not-eligible.subtitle": "Monerium ei saa sulle kontot avada. <PERSON>lu<PERSON> vali teine <PERSON>.", "monerium.not-eligible.title": "Proovi te<PERSON>pa<PERSON>", "monerium.setup-card.cancel": "<PERSON><PERSON><PERSON><PERSON>", "monerium.setup-card.continue": "Jätka", "monerium.setup-card.create_account": "Loo konto", "monerium.setup-card.login": "<PERSON><PERSON> sisse Gnosis Pay'sse", "monerium.setup-card.subtitle": "Loo Gnosis Pay konto või logi sisse, et võimaldada kohesed panga sissemaksed.", "monerium.setup-card.subtitle_personal_account": "Saa oma isiklik Gnosis Pay konto minutitega:", "monerium.setup-card.title": "Luba panga sissemaksed", "moneriumDepositSuccess.goToWallet": "Mine rahakotti", "moneriumDepositSuccess.title": "{symbol} laek<PERSON>ud", "moneriumInfo.fees": "Tasud 0%", "moneriumInfo.registration": "Monerium on volitatud ja reguleeritud e-raha asutusena vastavalt Islandi e-raha seadusele nr 17/2013. <link>Loe lisaks</link>", "moneriumInfo.selfCustody": "<PERSON><PERSON><PERSON> digit<PERSON> raha on sinu privaatses rahakotis ja kellelgi teisel pole sinu vara üle kontrolli.", "moneriumWithdrawRejected.supportText": "Me ei saanud sinu ülekannet lõpule viia. Proovi uuesti ja kui see ikka ei <PERSON>, siis <link>kontakteeru toega.</link>", "moneriumWithdrawRejected.title": "<PERSON><PERSON><PERSON><PERSON>", "moneriumWithdrawRejected.tryAgain": "<PERSON>ovi uuesti", "moneriumWithdrawSuccess.supportText": "<PERSON><PERSON> saajani jõuab raha{br}kuni 24 tunni jooksul.", "moneriumWithdrawSuccess.title": "Saadetud", "monerium_enable_banner.text": "Aktiveeri pan<PERSON>ü<PERSON> kohe", "monerium_error_address_re_link_required.title": "<PERSON><PERSON><PERSON> tuleb Moneriumiga uuesti siduda", "monerium_error_duplicate_order.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "Šveitsi frank", "mt-pelerin-fork.options.chf.short": "<PERSON><PERSON><PERSON> ja tasuta Mt Peleriniga", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "<PERSON><PERSON><PERSON> ja tasuta <PERSON>", "mt-pelerin-fork.title": "Mida soovid hoiustada?", "mtPelerinProviderInfo.fees": "Maksad 0% tasusid", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd on seotud SO-FITiga, mis on Šveitsi Finantsinspektsiooni (FINMA) poolt tunnustatud isereguleeruv organ vastavalt rahapesu tõkestamise seadusele. <link>Loe lisaks</link>", "mtPelerinProviderInfo.selfCustody": "<PERSON><PERSON><PERSON> digit<PERSON> raha on sinu privaatses rahakotis ja kellelgi teisel pole sinu varade üle kontrolli.", "network-fee-widget.title": "Ta<PERSON><PERSON>", "network.edit.verifying_rpc": "RPC kontrollimine", "network.editRpc.predefined_network_info.subtitle": "<PERSON><PERSON>, ka<PERSON><PERSON><PERSON>-sid, mis takistavad sinu isikuandmete jälgimist.{br}{br}<PERSON><PERSON><PERSON> vaikimisi RPC-d on usaldusväärsed ja töökindlad pakkujad.", "network.editRpc.predefined_network_info.title": "Zeali privaatne RPC", "network.filter.update_rpc_success": "RPC s<PERSON><PERSON>", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "Kõik võrgud", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "<PERSON>", "networks.filter.add_modal.chain_list.subtitle": "Lisa mis tahes EVM-võrke", "networks.filter.add_modal.chain_list.title": "Mine Chainlisti", "networks.filter.add_modal.dapp_tip.subtitle": "Lihtsalt vaheta oma lemmik-dAppis soovitud EVM-võrgule ja <PERSON> k<PERSON>, kas soovid selle oma rahakotti lisada.", "networks.filter.add_modal.dapp_tip.title": "<PERSON><PERSON><PERSON> lisa võrk mis tahes dAppist", "networks.filter.add_networks.subtitle": "Kõik EVM-võrgud on toetatud", "networks.filter.add_networks.title": "<PERSON>", "networks.filter.add_test_networks.title": "<PERSON>", "networks.filter.tab.netwokrs": "<PERSON><PERSON><PERSON><PERSON>", "networks.filter.testnets.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nft.widget.emptystate": "<PERSON><PERSON><PERSON><PERSON> <PERSON> kogutavaid esemeid", "nft_collection.change_account_picture.subtitle": "Kas soovid profiilipilti uuendada?", "nft_collection.change_account_picture.title": "Uuenda profiilipilt NFT-ks", "nfts.allNfts.pricingPopup.description": "Hinnad põhinevad viimasel tehing<PERSON>.", "nfts.allNfts.pricingPopup.title": "Kogutavate hinnastamine", "no-passkeys-found.modal.cta": "Sulge", "no-passkeys-found.modal.subtitle": "Me ei tuvastanud selles seadmes ühtegi Zeali pääsukoodi. Veendu, et oled sisse loginud pilvekontole, millega lõid oma <PERSON>eti.", "no-passkeys-found.modal.title": "Pääsukoode ei leitud", "notValidEmail.title": "See ei ole korrektne e-posti aadress", "notValidPhone.title": "See ei ole kehtiv telefoninumber", "notification-settings.title": "Teavituste seaded", "notification-settings.toggles.active-wallets": "<PERSON><PERSON><PERSON><PERSON><PERSON> rahakotid", "notification-settings.toggles.bank-transfers": "Pangaülekanded", "notification-settings.toggles.card-payments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notification-settings.toggles.readonly-wallets": "<PERSON>ult vaatamiseks rahakotid", "ntft.groupHeader.text": "Kogutavad", "on_ramp.crypto_completed": "<PERSON><PERSON><PERSON>", "on_ramp.fiat_completed": "<PERSON><PERSON><PERSON>", "onboarding-widget.subtitle.card_created_from_order.left": "<PERSON> kaart", "onboarding-widget.subtitle.card_created_from_order.right": "<PERSON><PERSON><PERSON><PERSON> kaart", "onboarding-widget.subtitle.card_order_ready.left": "Füüsiline Visa kaart", "onboarding-widget.subtitle.default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ja <PERSON> kaart", "onboarding-widget.title.card-order-in-progress": "<PERSON><PERSON><PERSON> kaardi tellimist", "onboarding-widget.title.card_created_from_order": "<PERSON><PERSON> on teele pandud", "onboarding-widget.title.kyc_approved": "<PERSON><PERSON><PERSON><PERSON> ja kaart on valmis", "onboarding-widget.title.kyc_failed": "<PERSON>nto loomine <PERSON>", "onboarding-widget.title.kyc_not_started": "Jätka seadistamist", "onboarding-widget.title.kyc_started_documents_requested": "Lõ<PERSON>a kinni<PERSON>", "onboarding-widget.title.kyc_started_resubmission_requested": "Proovi uuesti kinnitada", "onboarding-widget.title.kyc_started_verification_in_progress": "Isikuandmete kontroll", "onboarding.loginOrCreateAccount.amountOfAssets": "Varasid $10+ miljardi väärtuses", "onboarding.loginOrCreateAccount.cards.subtitle": "Saadaval ainult teatud piirkondades. Jätkates nõustud meie <Terms>Tingimused</Terms> ja <PrivacyPolicy>Privaatsuspoliitika</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "<PERSON> kaart, millel on kõrged{br}tootlused ja puuduvad tasud", "onboarding.loginOrCreateAccount.createAccount": "Loo konto", "onboarding.loginOrCreateAccount.earn.subtitle": "<PERSON><PERSON><PERSON> on varieeruv; kapital on ohus. Jätkates nõustud meie <Terms>Tingimused</Terms> ja <PrivacyPolicy>Privaatsuspoliitika</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "<PERSON>i {percent} aastas{br}Usaldab {currencySymbol}5+ miljar<PERSON>", "onboarding.loginOrCreateAccount.earningPerYear": "Teeni {percent}{br}aastas", "onboarding.loginOrCreateAccount.login": "<PERSON><PERSON> sisse", "onboarding.loginOrCreateAccount.trading.subtitle": "Kapital on ohus. Jätkates nõustud meie <Terms>Tingimused</Terms> ja <PrivacyPolicy>Privaatsuspoliitika</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "<PERSON><PERSON><PERSON><PERSON>,{br}BTC-st S&P-ni", "onboarding.loginOrCreateAccount.trustedBy": "Digitaalsed rahaturud{br}Usaldab {assets}", "onboarding.wallet_stories.close": "Sule", "onboarding.wallet_stories.previous": "<PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.deposit": "Sissemakse", "order-earn-deposit-bridge.into": "Sihtkoht", "otpIncorrectMessage": "Kinnituskood on vale", "passkey-creation-not-possible.modal.close": "Sulge", "passkey-creation-not-possible.modal.subtitle": "Me ei saanud sinu rahakotile pääsuvõtit luua. Veendu, et su seade toetab pääsuvõtmeid ja proovi uuesti. <link>Võta ühendust toega</link> , kui probleem püsib.", "passkey-creation-not-possible.modal.title": "Pääsuvõtit ei saa luua", "passkey-not-supported-in-mobile-browser.modal.cta": "Laadi Z<PERSON> alla", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Nutikaid rahakotte ei toetata mobiilibrauserites.", "passkey-not-supported-in-mobile-browser.modal.title": "Jätkamiseks laadi alla Zeali rakendus", "passkey-recovery.recovering.deploy-signer.loading-text": "Pääsukoodi kontrollimine", "passkey-recovery.recovering.loading-text": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "passkey-recovery.recovering.signer-not-found.subtitle": "Me ei saanud sinu pääsukoodi aktiivse rahakotiga siduda. Kui sul on rahakotis vahendeid, võta abi saamiseks ühendust Zeali meeskonnaga.", "passkey-recovery.recovering.signer-not-found.title": "Rahakott<PERSON> ei leitud", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "Proovi teist pääsukoodi", "passkey-recovery.select-passkey.banner.subtitle": "<PERSON><PERSON><PERSON>, et oled oma seadmes sisse loginud õigele kontole. Pääsukoodid on kontospetsiifilised.", "passkey-recovery.select-passkey.banner.title": "Ei näe oma rahakoti pääsukoodi?", "passkey-recovery.select-passkey.continue": "Vali pääsukood", "passkey-recovery.select-passkey.subtitle": "Juurdepääsu taastamiseks vali oma rahakotiga seotud pääsukood.", "passkey-recovery.select-passkey.title": "Vali pääsukood", "passkey-story_1.subtitle": "Smart Walletiga saad maksta võrgutasusid enamikus tokenites ega pea muretsema võrgutasu pärast.", "passkey-story_1.title": "Unusta võrgutasu – maksa enamikus tokenites", "passkey-story_2.subtitle": "Põhineb Safe'i tipptasemel nutilepingutel, mis kaitsevad üle 100 miljardi dollari väärtuses vara enam kui 20 miljonis rahakotis.", "passkey-story_2.title": "Turvatud Safe'iga", "passkey-story_3.subtitle": "Smart Walletid töötavad suuremates Ethereumiga ühilduvates võrkudes. Enne varade saatmist kontrolli toetatud võrke.", "passkey-story_3.title": "Toetatud on suured EVM-võrgud", "password.add.header": "Loo parool", "password.add.includeLowerAndUppercase": "<PERSON><PERSON><PERSON><PERSON> ja su<PERSON> tähed", "password.add.includesNumberOrSpecialChar": "Üks number või sümbol", "password.add.info.subtitle": "Me ei saada sinu parooli oma serveritesse ega varunda seda sinu eest", "password.add.info.t_and_c": "Jätkates nõustud meie <Terms>Tingimused</Terms> ja <PrivacyPolicy>Privaatsuspoliitika</PrivacyPolicy>", "password.add.info.title": "<PERSON>u parool j<PERSON><PERSON>b sellesse seadmesse", "password.add.inputPlaceholder": "Loo parool", "password.add.shouldContainsMinCharsCheck": "10+ t<PERSON><PERSON><PERSON><PERSON><PERSON>", "password.add.subheader": "<PERSON><PERSON><PERSON> oma parooli <PERSON> a<PERSON>", "password.add.success.title": "Parool loodud 🔥", "password.confirm.header": "<PERSON><PERSON><PERSON>", "password.confirm.passwordDidNotMatch": "Paroolid peavad <PERSON>", "password.confirm.subheader": "<PERSON><PERSON><PERSON> oma parool veel kord", "password.create_pin.subtitle": "See pää<PERSON><PERSON>od lukustab Zeali rakenduse", "password.create_pin.title": "Loo oma p<PERSON>od", "password.enter_pin.title": "Sisesta pääsukood", "password.incorrectPin": "Vale pääsukood", "password.pin_is_not_same": "Pääsukood ei ühti", "password.placeholder.enter": "<PERSON><PERSON><PERSON> parool", "password.placeholder.reenter": "<PERSON><PERSON><PERSON> parool u<PERSON>i", "password.re_enter_pin.subtitle": "Sisesta sama pääsukood uuesti", "password.re_enter_pin.title": "<PERSON><PERSON><PERSON>", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "Ootel", "pending-send.details.pending": "Ootel", "pending-send.details.processing": "Töötlemisel", "permit-info.modal.description": "Permit on taotlus, mis allkirjastamisel lubab rakendusel sinu nimel tokeneid liigutada, näiteks vahetustehingu tegemiseks.{br}Permit'id on sarnased kinnitustele (Approvals), kuid nende allkirjastamine on võrgutasuta.", "permit-info.modal.title": "Mis on Permit'id?", "permit.edit-expiration": "<PERSON>uda {currency} aegumist", "permit.edit-limit": "Muuda {currency} kululimiiti", "permit.edit-modal.expiresIn": "<PERSON><PERSON><PERSON>…", "permit.expiration-warning": "{currency} aegumise hoiatus", "permit.expiration.info": "{currency} aegumise info", "permit.expiration.never": "<PERSON><PERSON> k<PERSON>", "permit.spend-limit.info": "{currency} kululimiidi info", "permit.spend-limit.warning": "{currency} kululimiidi hoiatus", "phoneNumber.title": "telefoninumber", "physicalCardOrderFlow.cardOrdered": "<PERSON><PERSON>", "physicalCardOrderFlow.city": "<PERSON><PERSON>", "physicalCardOrderFlow.orderCard": "<PERSON><PERSON>", "physicalCardOrderFlow.postcode": "Postiindeks", "physicalCardOrderFlow.shippingAddress.subtitle": "<PERSON><PERSON><PERSON>, kuhu kaart sa<PERSON>e", "physicalCardOrderFlow.shippingAddress.title": "<PERSON><PERSON><PERSON><PERSON>", "physicalCardOrderFlow.street": "Tänav", "placeholderDapps.1inch.description": "<PERSON><PERSON><PERSON> parimaid teid pidi", "placeholderDapps.aave.description": "<PERSON><PERSON> ja laenuta <PERSON>", "placeholderDapps.bungee.description": "<PERSON><PERSON><PERSON>i parimaid teid pidi", "placeholderDapps.compound.description": "<PERSON><PERSON> ja laenuta <PERSON>", "placeholderDapps.cowswap.description": "Vaheta parima kursiga Gnosis võrgus", "placeholderDapps.gnosis-pay.description": "<PERSON><PERSON> oma Gnosis Pay kaarti", "placeholderDapps.jumper.description": "<PERSON><PERSON><PERSON>i parimaid teid pidi", "placeholderDapps.lido.description": "Panusta ETH-d, et teenida rohkem ETH-d", "placeholderDapps.monerium.description": "e-raha ja panga<PERSON>", "placeholderDapps.odos.description": "<PERSON><PERSON><PERSON> parimaid teid pidi", "placeholderDapps.stargate.description": "Kasuta Bridge'i või panusta <14% APY", "placeholderDapps.uniswap.description": "Üks populaarsemaid vahetusplatvorme", "pleaseAllowNotifications.cardPayments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pleaseAllowNotifications.customiseInSettings": "<PERSON><PERSON><PERSON> sea<PERSON>", "pleaseAllowNotifications.enable": "Luba", "pleaseAllowNotifications.forWalletActivity": "Rahakot<PERSON> tegevuste kohta", "pleaseAllowNotifications.title": "Saa rahakoti tea<PERSON>i", "pleaseAllowNotifications.whenReceivingAssets": "Varade laekumi<PERSON>", "portfolio.quick-actions.add_funds": "<PERSON> raha", "portfolio.quick-actions.buy": "<PERSON><PERSON>", "portfolio.quick-actions.deposit": "Deposiit", "portfolio.quick-actions.send": "Saada", "portfolio.view.lastRefreshed": "Värskendatud {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Täienda oma testvõrgu AVAX-i", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Mine Faucetisse", "portfolio.view.topupTestNet.BscTestnet.primary": "Täienda oma testvõrgu BNB-d", "portfolio.view.topupTestNet.BscTestnet.secondary": "Mine Faucetisse", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Täienda oma testvõrgu SepETH-i", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Mine Sepolia Faucetisse", "portfolio.view.topupTestNet.FantomTestnet.primary": "Täienda oma testvõrgu FTM-i", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Mine Faucetisse", "privateKeyConfirmation.banner.subtitle": "Privaatvõti annab ligi<PERSON>ä<PERSON> su rahale. V<PERSON> petturid küsivad seda.", "privateKeyConfirmation.banner.title": "Mõistan riske", "privateKeyConfirmation.title": "ÄRA KUNAGI JAGA oma privaatvõtit kellegagi", "rating-request.not-now": "<PERSON><PERSON> p<PERSON>gu", "rating-request.title": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>?", "receive_funds.address-text": "See on sinu unikaalne rahakoti aadress. <PERSON><PERSON><PERSON> seda turvaliselt teistega jagada.", "receive_funds.copy_address": "<PERSON><PERSON><PERSON> rah<PERSON>oti aadress", "receive_funds.network-warning.eoa.subtitle": "<link>Vaata standardvõrke</link>. Mitte-EVM võrkudes saadetud varad lähevad kaotsi.", "receive_funds.network-warning.eoa.title": "Kõik Ethereumil põhinevad võrgud on toetatud", "receive_funds.network-warning.scw.subtitle": "<link><PERSON><PERSON><PERSON> võrk<PERSON></link>. Teistes võrkudes saadetud varad lähevad ka<PERSON>i.", "receive_funds.network-warning.scw.title": "Tähtis: ka<PERSON>ta ainult toetatud võrke", "receive_funds.scan_qr_code": "Skaneeri QR-kood", "receiving.in.days": "<PERSON><PERSON><PERSON> {days}p p<PERSON><PERSON><PERSON>", "receiving.this.week": "<PERSON><PERSON><PERSON> sel nädalal", "receiving.today": "<PERSON><PERSON><PERSON> t<PERSON>na", "reference.error.maximum_number_of_characters_exceeded": "Liiga palju märke", "referral-code.placeholder": "Kleebi kutse link", "referral-code.subtitle": "Klõpsa uuesti sõbra lingil või kleebi link allpool. <PERSON><PERSON> olla kindlad, et saad oma preemiad.", "referral-code.title": "<PERSON><PERSON> s<PERSON> sa<PERSON> sulle {bReward}?", "rekyc.verification_deadline.subtitle": "<PERSON><PERSON><PERSON> oma isik {daysUntil} p<PERSON><PERSON> jook<PERSON>, et kaarti edasi kasutada.", "rekyc.verification_required.subtitle": "<PERSON><PERSON><PERSON> ka<PERSON>tam<PERSON>ks kinnita oma isik.", "reminder.fund": "💸 <PERSON> raha — hakka kohe teenima 6%", "reminder.onboarding": "🏁 Lõpeta seadistamine — teeni oma sissemaksetelt 6%", "remove-owner.confirmation.subtitle": "<PERSON>det<PERSON> muutmisel on kaart 3 min suletud.", "remove-owner.confirmation.title": "<PERSON><PERSON> on uuenduste ajaks 3 min suletud", "restore-smart-wallet.wallet-recovered": "<PERSON><PERSON><PERSON>", "rewardClaimCelebration.claimedTitle": "<PERSON>emiad on juba lunastatud", "rewardClaimCelebration.subtitle": "Sõprade kutsumise eest", "rewardClaimCelebration.title": "<PERSON><PERSON> teen<PERSON>", "rewards-warning.subtitle": "Selle konto eemaldamine peatab juurdepääsu seotud preemiatele. Saad konto igal ajal taastada, et need kätte saada.", "rewards-warning.title": "Kaotad juurdepääsu oma preemiatele", "rewards.copiedInviteLink": "Kutse link kopeeritud", "rewards.createAccount": "Kopeeri kutse link", "rewards.header.subtitle": "Saadame {aReward} sulle ja {bReward} su s<PERSON><PERSON><PERSON>, kui ta kulutab {bSpendLimitReward}.", "rewards.header.title": "Saa {amountA}{br}Anna {amountB}", "rewards.sendInvite": "<PERSON>ada kutse", "rewards.sendInviteTip": "<PERSON><PERSON> s<PERSON>ber ja anname talle {bAmount}", "route.fees": "Tasud {fees}", "routesNotFound.description": "<PERSON>ahetusmarsruut {from}-{to} võrgukombinatsioonile pole saadaval.", "routesNotFound.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pole saadaval", "rpc.OrderBuySignMessage.subtitle": "Kasutades Swaps.IO-d", "rpc.OrderCardTopupSignMessage.subtitle": "Kasutades Swaps.IO-d", "rpc.addCustomNetwork.addNetwork": "<PERSON>", "rpc.addCustomNetwork.chainId": "Ahela ID", "rpc.addCustomNetwork.nativeToken": "Põhitoken", "rpc.addCustomNetwork.networkName": "<PERSON><PERSON><PERSON><PERSON> nimi", "rpc.addCustomNetwork.operationDescription": "Lubab sellel veebisaidil sinu rahakotti võrgu lisada. Zeal ei saa kohandatud võrkude turvalisust kontrollida, seega veendu, et mõistad riske.", "rpc.addCustomNetwork.rpcUrl": "RPC URL", "rpc.addCustomNetwork.subtitle": "<PERSON><PERSON><PERSON><PERSON> {name}", "rpc.addCustomNetwork.title": "<PERSON>", "rpc.send_token.network_not_supported.subtitle": "Töötame selle nimel, et tehingud selles võrgus lubada. Täname kannatlikkuse eest 🙏", "rpc.send_token.network_not_supported.title": "Võrk on peagi tulekul", "rpc.send_token.send_or_receive.settings": "Seaded", "rpc.sign.accept": "Nõustu", "rpc.sign.cannot_parse_message.body": "Selle sõnumi dekodeerimine ebaõnnestus. Nõustu päringuga ainult siis, kui usaldad seda rakendust.{br}{br}Sõnumeid saab kasutada rakendusse sisselogimiseks, kuid need võivad anda rakendustele ka kontrolli sinu tokenite üle.", "rpc.sign.cannot_parse_message.header": "<PERSON>", "rpc.sign.import_private_key": "Impordi võtmed", "rpc.sign.subtitle": "<PERSON><PERSON><PERSON><PERSON> {name}", "rpc.sign.title": "Allkirjasta", "safe-creation.success.title": "<PERSON><PERSON><PERSON> lood<PERSON>", "safe-safety-checks-popup.title": "<PERSON><PERSON><PERSON>ontroll", "safetyChecksPopup.title": "<PERSON><PERSON>", "scan_qr_code.description": "Skaneeri rahakoti QR-kood või ühendu rakendusega", "scan_qr_code.show_qr_code": "<PERSON>äita minu QR-koodi", "scan_qr_code.tryAgain": "<PERSON>ovi uuesti", "scan_qr_code.unlockCamera": "<PERSON><PERSON> ka<PERSON>ra", "screen-lock-missing.modal.close": "Sulge", "screen-lock-missing.modal.subtitle": "Sinu seade nõuab pääsuvõtmete kasutamiseks ekraanilukku. Palun seadista ekraanilukk ja proovi uuesti.", "screen-lock-missing.modal.title": "Ekraanilukk puudub", "seedConfirmation.banner.subtitle": "Salafraas annab ligi<PERSON>ä<PERSON> su rahale. Vaid petturid küsivad seda.", "seedConfirmation.title": "ÄRA KUNAGI JAGA oma salajast fraasi kellegagi", "select-active-owner.subtitle": "Vali aktiivne rahakott. <PERSON><PERSON> hiljem muuta.", "select-active-owner.title": "<PERSON><PERSON>", "select-card.title": "<PERSON><PERSON> ka<PERSON>", "select-crypto-currency-title": "Vali token", "select-token.title": "Vali token", "selectEarnAccount.chf.description.steps": "· Väljamaksed 24/7, lukustusperioodita {br}· Intress koguneb iga sekund {br}· Ületagatud hoiused <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} aastas CHF-is", "selectEarnAccount.eur.description.steps": "· <PERSON><PERSON> 24/7, lukustusperioodideta {br}· Intress koguneb iga sekund {br}· Ületagatud laenud <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} aastas eurodes", "selectEarnAccount.subtitle": "<PERSON><PERSON> igal ajal muuta", "selectEarnAccount.title": "Vali valuuta", "selectEarnAccount.usd.description.steps": "· <PERSON>ha <PERSON> 24/7, lukustusperioodideta {br}· Intress koguneb iga sekund {br}· Ületagatud hoiused <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} aastas dollarites", "selectEarnAccount.zero.description_general": "Hoia digitaalset raha ilma intressi teenimata", "selectEarnAccount.zero.title": "0% aastas", "selectRechargeThreshold.button.enterAmount": "<PERSON><PERSON><PERSON> summa", "selectRechargeThreshold.button.setTo": "<PERSON><PERSON><PERSON><PERSON> {amount}", "selectRechargeThreshold.description.line1": "Kui su kaardi saldo langeb alla {amount}, laeta<PERSON>e see automaatselt tagasi summani {amount} sinu Earn-kontolt.", "selectRechargeThreshold.description.line2": "Madalam sihtsumma hoiab rohkem raha sinu Earn-kontol (teenides 3%). <PERSON><PERSON> seda igal ajal muuta.", "selectRechargeThreshold.title": "<PERSON><PERSON><PERSON><PERSON> kaardi si<PERSON>", "select_currency_to_withdraw.select_token_to_withdraw": "Vali väljavõetav token", "send-card-token.form.send": "Saada", "send-card-token.form.send-amount": "Laetav summa", "send-card-token.form.title": "<PERSON> kaardile raha", "send-card-token.form.to-address": "<PERSON><PERSON>", "send-safe-transaction.network-fee-widget.error": "Sul on vaja {amount} või vali mõni muu token", "send-safe-transaction.network-fee-widget.no-fee": "<PERSON><PERSON><PERSON><PERSON>", "send-safe-transaction.network-fee-widget.title": "Ta<PERSON><PERSON>", "send-safe-transaction.network_fee_widget.title": "Võrgutasu", "send.banner.fees": "Sul on vaja {amount} rohkem {currency} tasude maksmiseks", "send.banner.toAddressNotSupportedNetwork.subtitle": "<PERSON><PERSON> rahak<PERSON> ei toeta {network}. Vaheta toetatud tokeni vastu.", "send.banner.toAddressNotSupportedNetwork.title": "Saaja ei toeta seda võrku", "send.banner.walletNotSupportedNetwork.subtitle": "Smart Walletid ei saa teha tehinguid {network}. Vaheta toetatud tokeni vastu.", "send.banner.walletNotSupportedNetwork.title": "Tokeni võrk pole toetatud", "send.empty-portfolio.empty-state": "Me ei leidnud <PERSON>d", "send.empty-portfolio.header": "<PERSON><PERSON><PERSON>", "send.titile": "Saatmine", "sendLimit.success.subtitle": "Sinu päevalimiit uueneb 3 minuti jooksul. <PERSON><PERSON> kehtib praegune limiit.", "sendLimit.success.title": "<PERSON>udatus jõustub 3 minuti p<PERSON>rast", "send_crypto.form.disconnected.cta.addFunds": "<PERSON> raha", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Vaheta võrgule {network}", "send_crypto.form.disconnected.label": "Ülekantav summa", "send_to.qr_code.description": "<PERSON><PERSON><PERSON><PERSON>k<PERSON>, et rahakotti saata", "send_to.qr_code.title": "Skaneeri QR-kood", "send_to_card.header": "<PERSON><PERSON> ka<PERSON>i aadressile", "send_to_card.select_sender.add_wallet": "<PERSON>", "send_to_card.select_sender.header": "<PERSON><PERSON> sa<PERSON>ja", "send_to_card.select_sender.search.default_placeholder": "Otsi aadressi või ENS nime", "send_to_card.select_sender.show_card_address_button_description": "<PERSON><PERSON><PERSON> kaardi a<PERSON>i", "send_token.form.select-address": "<PERSON><PERSON> aadress", "send_token.form.send-amount": "Saadetav summa", "send_token.form.title": "Saada", "setLimit.amount.error.zero_amount": "Sa ei saa ühtegi makset teha", "setLimit.error.max_limit_reached": "<PERSON><PERSON><PERSON><PERSON> maksim<PERSON>t {amount}", "setLimit.error.same_as_current_limit": "<PERSON><PERSON>, mis praegune limiit", "setLimit.placeholder": "Praegune: {amount}", "setLimit.submit": "Määra limiit", "setLimit.submit.error.amount_required": "<PERSON><PERSON><PERSON> summa", "setLimit.subtitle": "See on summa, mida saad oma kaardiga päevas kulutada.", "setLimit.title": "Määra päeva kululimiit", "settings.accounts": "<PERSON><PERSON><PERSON>", "settings.accountsSeeAll": "Vaata kõiki", "settings.addAccount": "<PERSON>", "settings.card": "<PERSON><PERSON><PERSON> seaded", "settings.connections": "Rakenduste ühendused", "settings.currency": "Vaikimisi valuuta", "settings.default_currency_selector.title": "<PERSON><PERSON><PERSON>", "settings.discord": "Discord", "settings.experimentalMode": "Eksperimentaalrežiim", "settings.experimentalMode.subtitle": "Testi uusi funkts<PERSON>one", "settings.language": "<PERSON><PERSON>", "settings.lockZeal": "Lukusta Zeal", "settings.notifications": "Teated", "settings.open_expanded_view": "Ava laiendatud vaade", "settings.privacyPolicy": "Privaatsuspoliitika", "settings.settings": "Seaded", "settings.termsOfUse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.twitter": "𝕏 / Twitter", "settings.version": "Versioon {version} kesk: {env}", "setup-card.confirmation": "<PERSON><PERSON> virtuaalkaart", "setup-card.confirmation.subtitle": "Tee makseid internetis ja lisa oma {type} rahakotti viipemaksete jaoks.", "setup-card.getCard": "<PERSON><PERSON>", "setup-card.order.physicalCard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setup-card.order.physicalCard.steps": "· Füüsiline Gnosis Pay VISA kaart {br}· Kohaletoimetamine kuni 3 nädalat {br}· Kasuta maksmiseks poes ja sularahaautomaatides. {br}· Lisa Apple/Google Walletisse (ainult toetatud riikides)", "setup-card.order.subtitle1": "<PERSON><PERSON> korraga kasutada mitut kaarti", "setup-card.order.title": "Mis tüüpi kaarti soovid?", "setup-card.order.virtualCard": "<PERSON>irt<PERSON><PERSON><PERSON> kaart", "setup-card.order.virtual_card.steps": "· Digitaalne Gnosis Pay VISA kaart {br}· <PERSON><PERSON>ta kohe veebimakseteks {br}· Lisa Apple/Google Walletisse (ainult toetatud riikides)", "setup-card.orderCard": "<PERSON><PERSON>", "setup-card.virtual-card": "<PERSON><PERSON> virtuaalkaart", "setup.notifs.fakeAndroid.title": "Teadete saamine maksete ja sissetulevate ülekannete kohta", "setup.notifs.fakeIos.subtitle": "Zeal saab sind teavitada, kui saad raha või kulutad oma Visa kaardiga. Saad seda hiljem muuta.", "setup.notifs.fakeIos.title": "Teadete saamine maksete ja sissetulevate ülekannete kohta", "sign.PermitAllowanceItem.spendLimit": "Ku<PERSON><PERSON>iit", "sign.ledger.subtitle": "Saatsime tehingu sinu riistvaralisse rahakotti. <PERSON><PERSON><PERSON> j<PERSON> sealt.", "sign.ledger.title": "<PERSON><PERSON><PERSON> r<PERSON><PERSON> rahakotis", "sign.passkey.subtitle": "Sinu brauser peaks paluma sul allkirjastada selle rahakotiga seotud pääsuvõtmega. <PERSON>lun j<PERSON> seal.", "sign.passkey.title": "Vali pääsuvõti", "signal_aborted_for_uknown_reason.title": "Võrgupäring tühistati", "simulatedTransaction.BridgeTrx.info.title": "Bridge", "simulatedTransaction.CardTopUp.info.title": "<PERSON> kaardile raha", "simulatedTransaction.CardTopUpTrx.info.title": "<PERSON> kaardile raha", "simulatedTransaction.NftCollectionApproval.approve": "Kinnita NFT kollektsioon", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "<PERSON>", "simulatedTransaction.OrderEarnDepositBridge.title": "Sissemakse Earni", "simulatedTransaction.P2PTransaction.info.title": "Saatmine", "simulatedTransaction.PermitSignMessage.title": "Luba", "simulatedTransaction.SingleNftApproval.approve": "Kinnita N<PERSON>", "simulatedTransaction.UnknownSignMessage.title": "Allkirjasta", "simulatedTransaction.Withdrawal.info.title": "Väljamakse", "simulatedTransaction.approval.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.approve.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.p2p.info.account": "<PERSON><PERSON>", "simulatedTransaction.p2p.info.unlabelledAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.info.receive": "<PERSON><PERSON>", "simulatedTransaction.unknown.info.send": "Sa<PERSON><PERSON>", "simulatedTransaction.unknown.using": "<PERSON><PERSON><PERSON><PERSON> {app}", "simulation.approval.modal.text": "Kinnitusega annad konk<PERSON>e rakendusele või nutilepingule loa kasutada sinu tokeneid või NFT-sid tulevastes tehingutes.", "simulation.approval.modal.title": "Mis on kinnitused?", "simulation.approval.spend-limit.label": "Ku<PERSON><PERSON>iit", "simulation.approve.footer.for": "<PERSON><PERSON>", "simulation.approve.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "simulationNotAvailable.title": "<PERSON><PERSON><PERSON><PERSON> toiming", "smart-wallet-activation-view.on": "Võrgus", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password võib blokeerida juurdepääsu sinu rahakotile", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "<PERSON><PERSON><PERSON> turvaliseks seadistamiseks Apple'it või Google'it", "smart-wallet.passkey-notice.title": "Väldi 1Passwordi", "spend-limits.high.modal.text": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>, mis on lähedane tokenite summale, mida rakenduse või nutilepinguga tegelikult kasutad. Kõrged limiidid on riskantsed ja võivad teha kelmidel sinu tokenite varastamise lihtsamaks.", "spend-limits.high.modal.text_sign_message": "Kululimiit peaks olema lähedane tokenite hulgale, mida rakenduse või nutilepinguga tegelikult kasutad. Suured limiidid on riskantsed ja võivad teha kelmidel sinu tokenite varastamise lihtsamaks.", "spend-limits.high.modal.title": "<PERSON><PERSON><PERSON> k<PERSON>", "spend-limits.modal.text": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON><PERSON>, kui palju <PERSON>d saab rakendus sinu nimel kasutada. Saad seda limiiti igal ajal muuta või eemaldada. Turvalisuse huvides hoia kululimiidid lähedal summale, mida rakendusega tegelikult kasutad.", "spend-limits.modal.title": "Mis on kululimiit?", "spent-limit-info.modal.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON><PERSON>, kui palju <PERSON>d saab rakendus sinu nimel kasutada. Saad seda limiiti igal ajal muuta või eemaldada. Turvalisuse huvides hoia kululimiidid lähedal sellele tokenite hulgale, mida rakendusega tegelikult kasutad.", "spent-limit-info.modal.title": "Mis on kululimiit?", "sswaps-io.transfer-provider": "Ülekande p<PERSON>", "storage.accountDetails.activateWallet": "<PERSON><PERSON><PERSON><PERSON>", "storage.accountDetails.changeWalletLabel": "<PERSON><PERSON> rahakoti nime", "storage.accountDetails.deleteWallet": "<PERSON><PERSON><PERSON>", "storage.accountDetails.setup_recovery_kit": "Taastekomplekt", "storage.accountDetails.showPrivateKey": "Näita privaatvõtit", "storage.accountDetails.showWalletAddress": "<PERSON><PERSON><PERSON> rah<PERSON>oti aadressi", "storage.accountDetails.smartBackup": "Varundamine ja taastamine", "storage.accountDetails.viewSsecretPhrase": "<PERSON><PERSON><PERSON>", "storage.accountDetails.zealSmartWallets": "Zeal Smart Walletid?", "storage.manageAccounts.title": "<PERSON><PERSON><PERSON><PERSON>", "submit-userop.progress.text": "Saatmisel", "submit.error.amount_high": "Summa liiga suur", "submit.error.amount_hight": "Summa on liiga suur", "submit.error.amount_low": "Summa liiga väike", "submit.error.amount_required": "<PERSON><PERSON><PERSON> summa", "submit.error.maximum_number_of_characters_exceeded": "Lühenda sõnumit", "submit.error.not_enough_balance": "Saldost ei piisa", "submit.error.recipient_required": "<PERSON><PERSON> on nõutud", "submit.error.routes_not_found": "Teekonda ei leitud", "submitSafeTransaction.monitor.title": "<PERSON><PERSON><PERSON> tulemus", "submitSafeTransaction.sign.title": "<PERSON><PERSON><PERSON> tulemus", "submitSafeTransaction.state.sending": "Saatmisel", "submitSafeTransaction.state.sign": "<PERSON><PERSON><PERSON>", "submitSafeTransaction.submittingToRelayer.title": "<PERSON><PERSON><PERSON> tulemus", "submitTransaction.cancel": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.cancel.attemptingToStop": "Proovime peatada", "submitTransaction.cancel.failedToStop": "Peatamine e<PERSON>", "submitTransaction.cancel.stopped": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.cancel.title": "<PERSON><PERSON><PERSON> e<PERSON>", "submitTransaction.failed.banner.description": "Võrk tühistas selle tehingu ootamatult. Proovi uuesti või võta meiega ühendust.", "submitTransaction.failed.banner.title": "<PERSON><PERSON>", "submitTransaction.failed.execution_reverted.title": "Rakenduses ilmnes viga", "submitTransaction.failed.execution_reverted_without_message.title": "Rakenduses ilmnes viga", "submitTransaction.failed.out_of_gas.description": "Võrk tühistas tehingu, kuna võrgutasu oli oodatust suurem.", "submitTransaction.failed.out_of_gas.title": "Võrguviga", "submitTransaction.sign.title": "<PERSON><PERSON><PERSON> tulemus", "submitTransaction.speedUp": "<PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue": "<PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue.short": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.cancelled": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.complete": "{currencyCode} l<PERSON><PERSON>", "submitTransaction.state.complete.subtitle": "<PERSON><PERSON><PERSON> o<PERSON>", "submitTransaction.state.completed": "Lõpetatud", "submitTransaction.state.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.includedInBlock": "Plokki lisatud", "submitTransaction.state.includedInBlock.short": "<PERSON><PERSON><PERSON>", "submitTransaction.state.replaced": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.sendingToNetwork": "Saadan võrku", "submitTransaction.stop": "<PERSON><PERSON><PERSON>", "submitTransaction.submit": "Esi<PERSON>", "submitted-user-operation.state.bundled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.completed": "Lõpetatud", "submitted-user-operation.state.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.pending": "Edastamisel", "submitted-user-operation.state.rejected": "Tagasi lü<PERSON>", "submittedTransaction.failed.title": "<PERSON><PERSON>", "success_splash.card_activated": "<PERSON><PERSON> on aktiveeritud", "supportFork.give-feedback.title": "<PERSON>", "supportFork.itercom.description": "<PERSON>eal aitab depo<PERSON>, <PERSON><PERSON><PERSON>, pre<PERSON>te ja muude k<PERSON><PERSON>.", "supportFork.itercom.title": "<PERSON><PERSON><PERSON><PERSON>", "supportFork.title": "<PERSON><PERSON> tee<PERSON>l", "supportFork.zendesk.subtitle": "Gnosis Pay tegeleb ka<PERSON>, is<PERSON><PERSON><PERSON>vas<PERSON>e ja tagasima<PERSON>etega.", "supportFork.zendesk.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ja is<PERSON><PERSON>", "supported-networks.ethereum.warning": "<PERSON><PERSON><PERSON><PERSON>d", "supportedNetworks.networks": "Toetatud võrgud", "supportedNetworks.oneAddressForAllNetworks": "Üks aadress kõikide võrkude jaoks", "supportedNetworks.receiveAnyAssets": "<PERSON><PERSON><PERSON> vastu varasid toetatud võrkudest otse oma Zeali rahakotti sama aadressiga.", "swap.form.error.no_routes_found": "Marsruute ei leitud", "swap.form.error.not_enough_balance": "Saldost ei piisa", "swaps-io-details.bank.serviceProvider": "Teenusepak<PERSON><PERSON>", "swaps-io-details.details.processing": "Töötlemisel", "swaps-io-details.pending": "Ootel", "swaps-io-details.rate": "<PERSON><PERSON><PERSON>", "swaps-io-details.serviceProvider": "Teenusepak<PERSON><PERSON>", "swaps-io-details.transaction.from.processing": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "swaps-io-details.transaction.networkFees": "Võrgutasud", "swaps-io-details.transaction.state.completed-transaction": "<PERSON><PERSON><PERSON> te<PERSON>", "swaps-io-details.transaction.state.started-transaction": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "swaps-io-details.transaction.to.processing": "<PERSON><PERSON><PERSON> te<PERSON>", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Sissemakse lõpeb peagi. Kinetex töötleb.", "swapsIO.monitoring.awaitingLiqSend.title": "<PERSON><PERSON><PERSON><PERSON>", "swapsIO.monitoring.awaitingRecive.title": "Edastamine", "swapsIO.monitoring.awaitingSend.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Tokenid tagastatakse. <PERSON><PERSON>.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Tokenite tagastamine", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Viga ülekandel. Proovi uuesti.", "swapsIO.monitoring.cancelledNoSlash.title": "<PERSON><PERSON><PERSON> tag<PERSON>", "swapsIO.monitoring.cancelledSlashed.subtitle": "<PERSON><PERSON><PERSON> on tagastatud. <PERSON><PERSON> e<PERSON>õnnestus.", "swapsIO.monitoring.cancelledSlashed.title": "<PERSON><PERSON><PERSON> tag<PERSON>", "swapsIO.monitoring.completed.title": "Lõpetatud", "taker-metadata.earn": "Teeni digitaalseid USD-sid Sky'ga", "taker-metadata.earn.aave": "Teeni digitaalseid EUR-e Aave'iga", "taker-metadata.earn.aave.cashout24": "Väljamaks<PERSON> kohe, 24/7", "taker-metadata.earn.aave.trusted": "Usaldatud 27 miljardit dollarit, 2+ aastat", "taker-metadata.earn.aave.yield": "<PERSON><PERSON><PERSON> koguneb iga sekund", "taker-metadata.earn.chf": "Teeni digitaalses CHF-is", "taker-metadata.earn.chf.cashout24": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kohe, 24/7", "taker-metadata.earn.chf.trusted": "Usaldatud 28M Fr. väärtuses", "taker-metadata.earn.chf.yield": "Intress kog<PERSON>b iga sekund", "taker-metadata.earn.usd.cashout24": "Väljamaks<PERSON> kohe, 24/7", "taker-metadata.earn.usd.trusted": "Usaldatud 10,7 miljardit dollarit, 5+ aastat", "taker-metadata.earn.usd.yield": "<PERSON><PERSON><PERSON> koguneb iga sekund", "test": "Sissemakse", "to.titile": "<PERSON><PERSON>", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "<PERSON><PERSON><PERSON>", "token.groupHeader.titleWithSum": "Varad {sum}", "token.hidden_tokens.page.title": "<PERSON><PERSON><PERSON><PERSON>", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "<PERSON> token", "token.widget.cashback_empty": "Tehinguid veel pole", "token.widget.emptyState": "<PERSON><PERSON><PERSON><PERSON> pole <PERSON><PERSON>d", "tokens.cash": "<PERSON><PERSON>", "top-up-card-from-earn-view.approve.for": "Mille jaoks", "top-up-card-from-earn-view.approve.into": "Sihtkoht", "top-up-card-from-earn-view.swap.from": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.swap.to": "Sihtkoht", "top-up-card-from-earn-view.withdraw.to": "<PERSON><PERSON>", "top-up-card-from-earn.trx.title.approval": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn.trx.title.swap": "<PERSON>", "top-up-card-from-earn.trx.title.withdrawal": "Väljamakse Earn kontolt", "topUpDapp.connectWallet": "<PERSON><PERSON><PERSON>", "topup-fee-breakdown.bungee-fee": "<PERSON><PERSON><PERSON><PERSON> tasu", "topup-fee-breakdown.header": "<PERSON><PERSON><PERSON><PERSON>", "topup-fee-breakdown.network-fee": "Võrgutasu", "topup-fee-breakdown.total-fee": "<PERSON><PERSON> kokku", "topup.continue-in-wallet": "<PERSON><PERSON><PERSON> o<PERSON> rahak<PERSON>", "topup.send.title": "Saada", "topup.submit-transaction.close": "Sulge", "topup.submit-transaction.sent-to-wallet": "Saada {amount}", "topup.to": "<PERSON><PERSON>", "topup.transaction.complete.close": "Sulge", "topup.transaction.complete.try-again": "<PERSON>ovi uuesti", "transaction-request.nonce-too-low.modal.button-text": "Sulge", "transaction-request.nonce-too-low.modal.text": "<PERSON>a seerian<PERSON>briga (nonce) tehing on juba lõpetatud, seega ei saa seda tehingut enam esitada. See võib juhtuda, kui teed tehinguid järjestikku või üritad kiirendada või tühistada juba lõpetatud tehingut.", "transaction-request.nonce-too-low.modal.title": "<PERSON><PERSON> non<PERSON>'<PERSON>ga te<PERSON> on lõpetatud", "transaction-request.replaced.modal.button-text": "Sulge", "transaction-request.replaced.modal.text": "Me ei saa selle tehingu olekut jälgida. See on kas asendatud teise tehinguga või esineb RPC-s<PERSON><PERSON>es probleeme.", "transaction-request.replaced.modal.title": "<PERSON><PERSON><PERSON> olekut ei leitud", "transaction.activity.details.modal.close": "Sule", "transaction.cancel_popup.cancel": "Ei, oota", "transaction.cancel_popup.confirm": "Jah, peata", "transaction.cancel_popup.description": "Peatamiseks tuleb maksta uus võrgutasu algse asemel, mis oli {oldFee}", "transaction.cancel_popup.description_without_original": "Peatamiseks tuleb tasuda uus võrgutasu", "transaction.cancel_popup.not_supported.subtitle": "Tehingute peatamine pole toetatud võrgus {network}", "transaction.cancel_popup.not_supported.title": "<PERSON><PERSON> ole <PERSON>", "transaction.cancel_popup.stopping_fee": "Peatamise võrgutasu", "transaction.cancel_popup.title": "Kas peatada tehing?", "transaction.in-progress": "Töötlemisel", "transaction.inProgress": "Töötlemisel", "transaction.speed_up_popup.cancel": "Ei, oota", "transaction.speed_up_popup.confirm": "<PERSON>ah kii<PERSON>da", "transaction.speed_up_popup.description": "Kiirendamiseks pead maksma uue võrgutasu esialgse tasu asemel {amount}", "transaction.speed_up_popup.description_without_original": "Kiirendamiseks pead maksma uue võrgutasu", "transaction.speed_up_popup.seed_up_fee_title": "Kiirendamise võrgutasu", "transaction.speed_up_popup.title": "Kiirendada tehingut?", "transaction.speedup_popup.not_supported.subtitle": "Te<PERSON><PERSON> kiirendamine pole toetatud võrgus {network}", "transaction.speedup_popup.not_supported.title": "<PERSON><PERSON> ole <PERSON>", "transaction.subTitle.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionDetails.cashback.not-qualified": "<PERSON><PERSON>", "transactionDetails.cashback.paid": "{amount} makstud", "transactionDetails.cashback.pending": "{amount} ootel", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "Teadmata", "transactionDetails.cashback_estimate": "Cashback'i hinnang", "transactionDetails.category": "Kategooria", "transactionDetails.exchangeRate": "Vahetusku<PERSON>s", "transactionDetails.location": "<PERSON><PERSON><PERSON><PERSON>", "transactionDetails.payment-approved": "<PERSON><PERSON><PERSON> kin<PERSON>", "transactionDetails.payment-declined": "<PERSON><PERSON><PERSON> keeldutud", "transactionDetails.payment-reversed": "<PERSON><PERSON><PERSON>", "transactionDetails.recharge.amountSentFromEarn.title": "Earn-kontolt saadetud summa", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "<PERSON><PERSON><PERSON> la<PERSON>", "transactionDetails.recharge.rate.title": "<PERSON><PERSON><PERSON>", "transactionDetails.recharge.transactionId.title": "Tehingu ID", "transactionDetails.refund": "Tagasimakse", "transactionDetails.reversal": "Tühistamine", "transactionDetails.transactionCurrency": "<PERSON><PERSON><PERSON> valuuta", "transactionDetails.transactionId": "Tehingu ID", "transactionDetails.type": "Te<PERSON>", "transactionRequestWidget.approve.subtitle": "Rakendusele {target}", "transactionRequestWidget.p2p.subtitle": "<PERSON><PERSON> {target}", "transactionRequestWidget.unknown.subtitle": "<PERSON><PERSON><PERSON><PERSON> {target}", "transactionSafetyChecksPopup.title": "<PERSON><PERSON><PERSON>", "transactions.main.activity.title": "Tegevused", "transactions.page.hiddenActivity.title": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "transactions.page.title": "Tegevused", "transactions.viewTRXHistory.emptyState": "Tehinguid veel pole", "transactions.viewTRXHistory.errorMessage": "Tehingute ajaloo la<PERSON> e<PERSON>", "transactions.viewTRXHistory.hidden.emptyState": "<PERSON><PERSON><PERSON><PERSON> te<PERSON> pole", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Testvõrkude tegevusi ei toetata", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Testvõrkude tegevusi ei toetata{br}<link><PERSON><PERSON><PERSON></link>", "transfer_provider": "Ülekande p<PERSON>", "transfer_setup_with_different_wallet.subtitle": "Pangaülekanded on seadistatud teise rahakotiga. Ülekannetega saab siduda ainult ühe rahakoti.", "transfer_setup_with_different_wallet.swtich_and_continue": "Vaheta ja j<PERSON>ka", "transfer_setup_with_different_wallet.title": "<PERSON><PERSON><PERSON> rah<PERSON>", "tx-sent-to-wallet.button": "Sulge", "tx-sent-to-wallet.subtitle": "<PERSON><PERSON><PERSON> {wallet}", "unblockProviderInfo.fees": "Saad madalaimad võimalikud tasud: 0% kuni $5k kuus ja 0.2% sellest üle.", "unblockProviderInfo.registration": "Unblock on FNTT poolt registreeritud ja volitatud osutama VASP-i vahetus- ja hoiustamisteenuseid ning on registreeritud MSB-teenuse pakkuja USA Fincenis. <link>Loe lisaks</link>", "unblockProviderInfo.selfCustody": "Sinu saadud digitaalne raha on sinu enda privaatses rahakotis ja kellelgi teisel pole sinu vara üle kontrolli", "unblock_invalid_faster_payment_configuration.subtitle": "Sinu sisestatud pangakonto ei toeta Euroopa SEPA makseid ega UK Faster Payments süsteemi. <PERSON>lun sisesta teine konto.", "unblock_invalid_faster_payment_configuration.title": "<PERSON><PERSON><PERSON> on teine konto", "unknownTransaction.primaryText": "<PERSON><PERSON><PERSON><PERSON>", "unsupportedCountry.subtitle": "Pangaü<PERSON>kanded pole sinu riigis veel saadaval.", "unsupportedCountry.title": "Ei ole saadaval riigis {country}", "update-app-popup.subtitle": "<PERSON>us<PERSON> versioon on täis parandusi, uusi funkts<PERSON> ja muud maagiat. Uuenda ja vii oma Zeal uuele tasemele.", "update-app-popup.title": "Uuenda Zeali vers<PERSON>", "update-app-popup.update-now": "<PERSON><PERSON><PERSON> kohe", "user_associated_with_other_merchant.subtitle": "Seda rahakotti ei saa pangaülekanneteks kasutada. <PERSON><PERSON>n kasuta teist rahakotti või pöördu abi ja info saamiseks meie Discordi kanalisse.", "user_associated_with_other_merchant.title": "<PERSON><PERSON><PERSON>i ei saa kasutada", "user_associated_with_other_merchant.try_with_another_wallet": "Proovi teise rahakotiga", "user_email_already_exists.subtitle": "Oled pan<PERSON>ü<PERSON>kanded juba seadistanud teise rahakotiga. Palun proovi uuesti varem kasutatud rahakotiga.", "user_email_already_exists.title": "Ülekanded on seadistatud teise rahakotiga", "user_email_already_exists.try_with_another_wallet": "Proovi teise rahakotiga", "validation.invalid.iban": "Vigane IBAN", "validation.required": "<PERSON><PERSON><PERSON><PERSON>", "validation.required.first_name": "<PERSON><PERSON><PERSON><PERSON> on nõutud", "validation.required.iban": "IBAN on nõutud", "validation.required.last_name": "Perekonnanimi on nõutud", "verify-passkey.cta": "<PERSON><PERSON><PERSON>", "verify-passkey.subtitle": "<PERSON><PERSON><PERSON>, et sinu pää<PERSON><PERSON>õ<PERSON> on loodud ja korralikult turvatud.", "verify-passkey.title": "<PERSON><PERSON><PERSON>", "view-cashback.cashback-next-cycle": "<PERSON><PERSON><PERSON> m<PERSON> {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "<PERSON><PERSON>, et saada cashbacki", "view-cashback.pending": "{money} ootel", "view-cashback.pending-rewards.not_paid": "<PERSON><PERSON><PERSON> {days}p p<PERSON><PERSON><PERSON>", "view-cashback.pending-rewards.paid": "<PERSON><PERSON><PERSON> sel nädalal", "view-cashback.received-rewards": "<PERSON><PERSON><PERSON><PERSON> preemiad", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "<PERSON><PERSON><PERSON><PERSON> maksed", "view-cashback.upcoming": "<PERSON><PERSON><PERSON><PERSON> {money}", "virtual-card-order.configure-safe.loading-text": "<PERSON><PERSON> kaarti", "virtual-card-order.create-order.loading-text": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>i", "virtual-card-order.create-order.success-text": "<PERSON>art aktiveeritud", "virtualCard.activateCard": "<PERSON><PERSON><PERSON><PERSON> kaart", "walletDeleteConfirm.main_action": "<PERSON><PERSON><PERSON>", "walletDeleteConfirm.subtitle": "Portfelli vaatamiseks või tehingute tegemiseks pead uuesti importima", "walletDeleteConfirm.title": "<PERSON><PERSON><PERSON><PERSON> rahakott?", "walletSetting.header": "<PERSON><PERSON><PERSON><PERSON> seaded", "wallet_connect.connect.cancel": "<PERSON><PERSON><PERSON><PERSON>", "wallet_connect.connect.connect_button": "<PERSON><PERSON><PERSON>", "wallet_connect.connect.title": "<PERSON><PERSON><PERSON>", "wallet_connect.connected.title": "Ü<PERSON><PERSON><PERSON>", "wallet_connect_add_chain_missing.title": "Võrku ei toetata", "wallet_connect_proposal_expired.title": "<PERSON><PERSON>us aegus", "withdraw": "Väljamakse", "withdraw.confirmation.close": "<PERSON><PERSON><PERSON><PERSON>", "withdraw.confirmation.continue": "<PERSON><PERSON><PERSON>", "withdrawal_request.completed": "Lõpetatud", "withdrawal_request.pending": "Ootel", "zeal-dapp.connect-wallet.cta.primary.connecting": "<PERSON><PERSON><PERSON>...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "<PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.cta.secondary": "<PERSON><PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.title": "Jätkamiseks ühenda r<PERSON>", "zealSmartWalletInfo.gas": "Maksa võrgutasu mitme tokeniga. Kasuta võrgutasude maksmiseks toetatud kettides populaarseid ERC20 tokeneid, mitte ainult võrgu põhitokenit.", "zealSmartWalletInfo.recover": "Salafraase pole vaja; taasta biomeetrilise pääsuvõtmega oma parool<PERSON>aldurist, iCloudist või Google'i kontolt.", "zealSmartWalletInfo.selfCustodial": "Täielikult privaatne r<PERSON>; pääsuvõtme allkirju valideeritakse ahela<PERSON>, et minimeerida keskseid sõltuvusi.", "zealSmartWalletInfo.title": "<PERSON>ve Z<PERSON>i Smart Walletite kohta", "zeal_a_rewards_already_claimed_error.title": "Preemia on juba välja võetud", "zwidget.minimizedDisconnected.label": "Zeal lahti ühendatud"}