{"Account.ListItem.details.label": "<PERSON><PERSON><PERSON>", "AddFromAddress.success": "Port<PERSON><PERSON><PERSON>", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{<PERSON><PERSON><PERSON> port<PERSON>} one{{count} portafoglio} other{{count} portafogli}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Frase segreta {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Crea nuovi portafogli da una delle tue frasi segrete esistenti", "AddFromExistingSecretPhrase.SelectPhrase.title": "<PERSON><PERSON>li una frase segreta", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "La tua frase segreta può fare il backup di molti portafogli. Scegli quelli che vuoi usare.", "AddFromExistingSecretPhrase.WalletSelection.title": "Aggiungi rapidamente un portafoglio", "AddFromExistingSecretPhrase.success": "Portafogli aggiunti a Zeal", "AddFromHardwareWallet.subtitle": "Seleziona il tuo hardware wallet da connettere a Zeal", "AddFromHardwareWallet.title": "Hardware Wallet", "AddFromNewSecretPhrase.WalletSelection.subtitle": "Seleziona i portafogli che vuoi importare", "AddFromNewSecretPhrase.WalletSelection.title": "Importa port<PERSON>ogli", "AddFromNewSecretPhrase.accounts": "Port<PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "Una frase segreta funziona come un portachiavi per milioni di portafogli, ognuno con una chiave privata unica.{br}{br}Puoi importare tutti i portafogli che vuoi ora, o aggiungerne altri in seguito.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Port<PERSON><PERSON><PERSON> con frase segreta", "AddFromNewSecretPhrase.subtitle": "Inserisci la tua frase segreta con le parole separate da spazi", "AddFromNewSecretPhrase.success_secret_phrase_added": "Frase segreta aggiunta 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Portafogli aggiunti a Zeal", "AddFromNewSecretPhrase.wallets": "Port<PERSON><PERSON><PERSON>", "AddFromPrivateKey.subtitle": "Inserisci la tua chiave privata", "AddFromPrivateKey.success": "Chiave privata aggiunta 🎉", "AddFromPrivateKey.title": "<PERSON><PERSON><PERSON><PERSON>", "AddFromPrivateKey.typeOrPaste": "<PERSON><PERSON>vi o incolla qui", "AddFromSecretPhrase.importWallets": "{count,plural,=0{Nessun wallet selezionato} one{Importa wallet} other{Importa {count} wallet}}", "AddFromTrezor.AccountSelection.title": "Importa portafogli Trezor", "AddFromTrezor.hwWalletTip.subtitle": "Un portafoglio hardware contiene milioni di portafogli con indirizzi diversi. Puoi importare tutti i portafogli che vuoi ora, o aggiungerne altri in seguito.", "AddFromTrezor.hwWalletTip.title": "Importazione da portafogli hardware", "AddFromTrezor.importAccounts": "{count,plural,=0{Nessun wallet selezionato} one{Importa wallet} other{Importa {count} wallet}}", "AddFromTrezor.success": "Portafogli aggiunti a Zeal", "ApprovalSpenderTypeCheck.failed.subtitle": "Probabile truffa: deve essere un contratto", "ApprovalSpenderTypeCheck.failed.title": "Chi spende è un wallet, non un contratto", "ApprovalSpenderTypeCheck.passed.subtitle": "Di solito approvi asset per contratti", "ApprovalSpenderTypeCheck.passed.title": "Chi spende è uno smart contract", "BestReturns.subtitle": "Questo fornitore di swap offre il rendimento più alto, commissioni incluse.", "BestReturnsPopup.title": "<PERSON><PERSON><PERSON>", "BlacklistCheck.Failed.subtitle": "Segnalazioni malevole da <source></source>", "BlacklistCheck.Failed.title": "<PERSON><PERSON> in blacklist", "BlacklistCheck.Passed.subtitle": "Nessuna segnalazione malevola da <source></source>", "BlacklistCheck.Passed.title": "<PERSON>o non in blacklist", "BlacklistCheck.failed.statusButton.label": "<PERSON><PERSON> se<PERSON>", "BridgeRoute.slippage": "Slippage {slippage}", "BridgeRoute.title": "Provider <PERSON>", "CheckConfirmation.InProgress": "In corso...", "CheckConfirmation.success.splash": "Completato", "ChooseImportOrCreateSecretPhrase.subtitle": "Importa una frase segreta o creane una nuova", "ChooseImportOrCreateSecretPhrase.title": "Aggiungi frase segreta", "ConfirmTransaction.Simuation.Skeleton.title": "Eseguo controlli di sicurezza…", "ConnectionSafetyCheckResult.passed": "Controllo di sicurezza superato", "ContactGnosisPaysupport": "Contatta supporto Gnosis", "CopyKeyButton.copied": "Copiato", "CopyKeyButton.copyYourKey": "Copia la tua chiave", "CopyKeyButton.copyYourPhrase": "Copia la tua frase", "DAppVerificationCheck.Failed.subtitle": "Il sito non è elencato su <source></source>", "DAppVerificationCheck.Failed.title": "Sito non trovato nei registri delle app", "DAppVerificationCheck.Passed.subtitle": "Il sito è elencato su <source></source>", "DAppVerificationCheck.Passed.title": "Il sito è presente nei registri delle app", "DAppVerificationCheck.failed.statusButton.label": "Sito non trovato nei registri delle app", "ERC20.tokens.emptyState": "<PERSON><PERSON><PERSON> token trovato", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Commissione di priorità", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Ultimi {period}: tra {from} e {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Commissione di base: {baseFee} • Margine di sicurezza: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Impossibile caricare la commissione di base", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Superiore al Nonce successivo. Si bloccherà", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "Il Nonce non può essere inferiore a quello attuale", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "Non siamo riusciti a calcolare la commissione di priorità", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Impossibile caricare la commissione massima", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "Commissione massima", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Rischio di blocco con commissioni basse", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "Commissione di base massima", "EditFeeModal.Custom.gasLimit.title": "Limite gas {gasLimit}", "EditFeeModal.Custom.title": "Impostazioni avanzate", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Bloccata finché la commissione base cala", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Commissione bassa. <PERSON><PERSON><PERSON> b<PERSON>", "EditFeeModal.EditGasLimit.estimatedGas": "Gas stimato: {estimated} • Margine di sicurezza: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "<PERSON>ite basso, la transazione fallirà", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Sotto il limite suggerito, potrebbe fallire", "EditFeeModal.EditGasLimit.subtitle": "Imposta la quantità massima di gas che vuoi usare per questa transazione. La transazione fallirà se imposti un limite inferiore al necessario", "EditFeeModal.EditGasLimit.title": "Modifica limite gas", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Inferiore al limite minimo di gas: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "<PERSON><PERSON> troppo alto, si bloc<PERSON>", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "Nonce non può essere inferiore all'attuale", "EditFeeModal.EditNonce.subtitle": "La tua transazione si bloccherà se non imposti il nonce successivo", "EditFeeModal.EditNonce.title": "Modifica nonce", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "Servono {amount} per inviare", "EditFeeModal.Header.Time.unknown": "Tempo <PERSON>", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Max {fee}", "EditFeeModal.Header.fee.unknown": "Commissione sconosciuta", "EditFeeModal.Header.subsequent_failed": "Le stime potrebbero non essere aggiornate, ultimo aggiornamento fallito", "EditFeeModal.Layout.Header.ariaLabel": "Commissione attuale", "EditFeeModal.MaxFee.subtitle": "La commissione massima è l'importo più alto che pagherai per una transazione, ma di solito paghi la commissione prevista. Questo margine extra aiuta la transazione ad andare a buon fine, anche se la rete rallenta o diventa più costosa.", "EditFeeModal.MaxFee.title": "Commissione di rete massima", "EditFeeModal.SelectPreset.Time.unknown": "Tempo <PERSON>", "EditFeeModal.SelectPreset.ariaLabel": "Seleziona preimpostazione commissione", "EditFeeModal.SelectPreset.fast": "Veloce", "EditFeeModal.SelectPreset.normal": "Normale", "EditFeeModal.SelectPreset.slow": "<PERSON><PERSON>", "EditFeeModal.ariaLabel": "Modifica commissione di rete", "FailedSimulation.Confirmation.Item.subtitle": "Si è verificato un errore interno", "FailedSimulation.Confirmation.Item.title": "Impossibile simulare la transazione", "FailedSimulation.Confirmation.subtitle": "Vuoi continuare?", "FailedSimulation.Confirmation.title": "Stai firmando alla cieca", "FailedSimulation.Title": "Errore di simulazione", "FailedSimulation.footer.subtitle": "Si è verificato un errore interno", "FailedSimulation.footer.title": "Impossibile simulare la transazione", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Servono {amount} per inviare la transazione", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "L'elaborazione potrebbe richiedere tempo", "FeeForecastWidget.networkFee": "Commissione di rete", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Impossibile calcolare la commissione", "FeeForecastWidget.subsequentFailed.message": "Stime obsolete, aggiornamento fallito", "FeeForecastWidget.unknownDuration": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FeeForecastWidget.unknownFee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "GasCurrencySelector.balance": "Saldo: {balance}", "GasCurrencySelector.networkFee": "Commissione di rete", "GasCurrencySelector.payNetworkFeesUsing": "Paga le commissioni di rete con", "GasCurrencySelector.removeDefaultGasToken.description": "Paga le commissioni dal saldo più alto", "GasCurrencySelector.removeDefaultGasToken.title": "Gestione automatica delle commissioni", "GasCurrencySelector.save": "<PERSON><PERSON>", "GoogleDriveBackup.BeforeYouBegin.first_point": "Se dimentico la mia password Zeal, perderò i miei fondi per sempre", "GoogleDriveBackup.BeforeYouBegin.second_point": "Se perdo l'accesso a Google Drive o modifico il File di recupero, perderò i miei fondi per sempre", "GoogleDriveBackup.BeforeYouBegin.subtitle": "Leggi e accetta quanto segue sul tuo portafoglio privato:", "GoogleDriveBackup.BeforeYouBegin.third_point": "Zeal non può aiutarmi a recuperare la password di Zeal o l'accesso a Google Drive", "GoogleDriveBackup.BeforeYouBegin.title": "Prima di iniziare", "GoogleDriveBackup.loader.subtitle": "Approva la richiesta su Google Drive per caricare il tuo File di recupero", "GoogleDriveBackup.loader.title": "In attesa di approvazione...", "GoogleDriveBackup.success": "Backup riuscito 🎉", "MonitorOffRamp.overServiceTime": "La maggior parte dei trasferimenti viene completata entro {estimated_time}, ma a volte possono richiedere più tempo a causa di controlli aggiuntivi. È normale e i fondi sono al sicuro durante queste verifiche.{br}{br}Se la transazione non viene completata entro {support_soft_deadline}, per favore {contact_support}", "MonitorOnRamp.contactSupport": "Contatta il supporto", "MonitorOnRamp.from": "Da", "MonitorOnRamp.fundsReceived": "Fondi rice<PERSON>ti", "MonitorOnRamp.overServiceTime": "La maggior parte dei trasferimenti è completata entro {estimated_time}, ma a volte possono richiedere più tempo per verifiche aggiuntive. È normale e i tuoi fondi sono al sicuro.{br}{br}Se la transazione non si completa entro {support_soft_deadline}, per favore {contact_support}", "MonitorOnRamp.sendingToYourWallet": "<PERSON><PERSON> al tuo portafoglio", "MonitorOnRamp.to": "A", "MonitorOnRamp.waitingForTransfer": "In attesa del tuo trasferimento fondi", "NftCollectionCheck.failed.subtitle": "La collezione non è verificata su <source></source>", "NftCollectionCheck.failed.title": "Collezione non verificata", "NftCollectionCheck.passed.subtitle": "La collezione è verificata su <source></source>", "NftCollectionCheck.passed.title": "Collezione verificata", "NftCollectionInfo.entireCollection": "Intera collezione", "NoSigningKeyStore.createAccount": "Crea account", "NonceRangeError.biggerThanCurrent.message": "La transazione si bloccherà", "NonceRangeError.lessThanCurrent.message": "La transazione fallirà", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "<PERSON>ce alto. <PERSON><PERSON><PERSON> per evitare blocchi.", "NonceRangeErrorPopup.biggerThanCurrent.title": "La transazione si bloccherà", "P2pReceiverTypeCheck.failed.subtitle": "Stai inviando all'indirizzo corretto?", "P2pReceiverTypeCheck.failed.title": "Destinatario è un contratto, non un wallet", "P2pReceiverTypeCheck.passed.subtitle": "In genere si inviano asset ad altri wallet", "P2pReceiverTypeCheck.passed.title": "Il destinatario è un wallet", "PasswordCheck.title": "Inser<PERSON>ci password", "PasswordChecker.subtitle": "Inserisci la password per verificare la tua identità", "PermitExpirationCheck.failed.subtitle": "Tienila breve e per il tempo necessario", "PermitExpirationCheck.failed.title": "Scadenza troppo lunga", "PermitExpirationCheck.passed.subtitle": "Per quanto un'app può usare i tuoi token", "PermitExpirationCheck.passed.title": "Scadenza non troppo lunga", "PrivateKeyValidationError.moreThanMaximumWords": "Max {count} parole", "PrivateKeyValidationError.notValidPrivateKey": "Questa non è una chiave privata valida", "PrivateKeyValidationError.secretPhraseIsInvalid": "La frase segreta non è valida", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Parola #{index} errata o non valida", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} parola} other{{count} parole}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Le frasi segrete e le chiavi private sono crittografate e non lasciano mai questo dispositivo", "SecretPhraseReveal.header": "Sc<PERSON>vi la Frase segreta", "SecretPhraseReveal.hint": "Non condividere la tua frase con nessuno. Conservala offline e al sicuro", "SecretPhraseReveal.skip.subtitle": "<PERSON><PERSON>i farlo più tardi, ma se perdi questo dispositivo prima di scrivere la frase, perderai tutti i fondi aggiunti a questo portafoglio", "SecretPhraseReveal.skip.takeTheRisk": "Salta", "SecretPhraseReveal.skip.title": "Saltare la scrittura della frase?", "SecretPhraseReveal.skip.writeDown": "<PERSON><PERSON><PERSON>", "SecretPhraseReveal.skipForNow": "<PERSON><PERSON> tardi", "SecretPhraseReveal.subheader": "Scrivila e conservala offline al sicuro. In seguito ti chiederemo di verificarla.", "SecretPhraseReveal.verify": "Verifica", "SelectCurrency.tokens": "Token", "SelectCurrency.tokens.emptyState": "<PERSON><PERSON><PERSON> token trovato", "SelectRoute.slippage": "Slippage {slippage}", "SelectRoutes.emptyState": "<PERSON><PERSON><PERSON> percorso trovato per questo swap", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "<PERSON><PERSON><PERSON>", "SendERC20.labelAddress.inputPlaceholder": "Etichetta portafoglio", "SendERC20.labelAddress.subtitle": "Assegna un'etichetta a questo portafoglio per trovarlo più facilmente.", "SendERC20.labelAddress.title": "Etichetta questo portaf<PERSON>lio", "SendERC20.send_to": "Invia a", "SendERC20.tokens": "Token", "SendOrReceive.bankTransfer.primaryText": "Bonifico ban<PERSON>", "SendOrReceive.bankTransfer.shortText": "On-ramp e off-ramp gratuiti e istantanei", "SendOrReceive.bridge.primaryText": "Bridge", "SendOrReceive.bridge.shortText": "Trasfer<PERSON><PERSON> <PERSON> tra reti", "SendOrReceive.receive.primaryText": "<PERSON><PERSON>", "SendOrReceive.receive.shortText": "Ricevi token o collezionabili", "SendOrReceive.send.primaryText": "Invia", "SendOrReceive.send.shortText": "Invia token a qualsiasi indirizzo", "SendOrReceive.swap.primaryText": "Scambia", "SendOrReceive.swap.shortText": "Scambia tra token", "SendSafeTransaction.Confirm.loading": "Eseguo i controlli di sicurezza…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Cripta un file di ripristino con password", "SetupRecoveryKit.google.subtitle": "Sin<PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "SetupRecoveryKit.google.title": "Backup su Google Drive", "SetupRecoveryKit.subtitle": "Ti servirà almeno un metodo per ripristinare il tuo conto se disinstalli Zeal o cambi dispositivo", "SetupRecoveryKit.title": "Configura il Kit di recupero", "SetupRecoveryKit.writeDown.subtitle": "Sc<PERSON>vi la Frase segreta", "SetupRecoveryKit.writeDown.title": "Backup manuale", "Sign.CheckSafeDeployment.activate": "<PERSON><PERSON><PERSON>", "Sign.CheckSafeDeployment.subtitle": "Prima di accedere a un'app o firmare un messaggio off-chain, devi attivare il tuo dispositivo su questa rete. Questo avviene dopo aver installato o recuperato uno Smart Wallet.", "Sign.CheckSafeDeployment.title": "Attiva dispositivo su questa rete", "Sign.Simuation.Skeleton.title": "Eseguo i controlli di sicurezza…", "SignMessageSafetyCheckResult.passed": "Controlli di sicurezza superati", "SignMessageSafetyChecksPopup.title.permits": "Controlli di sicurezza dei permessi", "SimulationFailedConfirmation.subtitle": "Abbiamo simulato questa transazione e trovato un problema che la farebbe fallire. Puoi inviarla, ma probabilmente fallirà e potresti perdere la commissione di rete.", "SimulationFailedConfirmation.title": "Probabile fallimento della transazione", "SimulationNotSupported.Title": "Simulazione non{br}supportata su{br}{network}", "SimulationNotSupported.footer.subtitle": "Puoi comunque inviare questa transazione", "SimulationNotSupported.footer.title": "Simulazione non supportata", "SlippagePopup.custom": "<PERSON><PERSON><PERSON><PERSON>", "SlippagePopup.presetsHeader": "Slippage dello swap", "SlippagePopup.title": "Impostazioni slippage", "SmartContractBlacklistCheck.failed.subtitle": "Segnalazioni malevole da parte di <source></source>", "SmartContractBlacklistCheck.failed.title": "<PERSON><PERSON><PERSON> in blacklist", "SmartContractBlacklistCheck.passed.subtitle": "Nessuna segnalazione malevola da parte di <source></source>", "SmartContractBlacklistCheck.passed.title": "<PERSON><PERSON><PERSON> non in blacklist", "SuspiciousCharactersCheck.Failed.subtitle": "È una tattica di phishing comune", "SuspiciousCharactersCheck.Failed.title": "Verifichiamo i pattern di phishing comuni", "SuspiciousCharactersCheck.Passed.subtitle": "Verifichiamo i tentativi di phishing", "SuspiciousCharactersCheck.Passed.title": "L'indirizzo non ha caratteri insoliti", "SuspiciousCharactersCheck.failed.statusButton.label": "L'indirizzo ha caratteri insoliti ", "TokenVerificationCheck.failed.subtitle": "Il token non è elencato su <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} non è verificato da CoinGecko", "TokenVerificationCheck.passed.subtitle": "Il token è elencato su <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} è verificato da CoinGecko", "TopupDapp.MonitorTransaction.success.splash": "Completato", "TransactionSafetyCheckResult.passed": "Controlli di sicurezza superati", "TransactionSimulationCheck.failed.subtitle": "Errore: {errorMessage}", "TransactionSimulationCheck.failed.title": "È probabile che la transazione fallisca", "TransactionSimulationCheck.passed.subtitle": "Simulazione eseguita con <source></source>", "TransactionSimulationCheck.passed.title": "L'anteprima della transazione è riuscita", "TrezorError.trezor_action_cancelled.action": "<PERSON><PERSON>", "TrezorError.trezor_action_cancelled.subtitle": "Hai rifiutato la transazione sul tuo hardware wallet", "TrezorError.trezor_device_used_elsewhere.action": "Sincronizza Trezor", "TrezorError.trezor_device_used_elsewhere.subtitle": "Assicurati di chiudere tutte le altre sessioni aperte e riprova a sincronizzare il tuo Trezor", "TrezorError.trezor_method_cancelled.action": "Sincronizza Trezor", "TrezorError.trezor_method_cancelled.subtitle": "Assicurati di consentire a Trezor di esportare i portafogli su Zeal", "TrezorError.trezor_permissions_not_granted.action": "Sincronizza Trezor", "TrezorError.trezor_permissions_not_granted.subtitle": "Concedi a Z<PERSON> i permessi per visualizzare tutti i portafogli", "TrezorError.trezor_pin_cancelled.action": "Sincronizza Trezor", "TrezorError.trezor_pin_cancelled.subtitle": "Sessione annullata sul dispositivo", "TrezorError.trezor_popup_closed.action": "Sincronizza Trezor", "TrezorError.trezor_popup_closed.subtitle": "La finestra di dialogo di Trezor si è chiusa inaspettatamente", "TrxLikelyToFail.lessThanEstimatedGas.message": "La transazione fallirà", "TrxLikelyToFail.lessThanMinimumGas.message": "La transazione fallirà", "TrxLikelyToFail.lessThanSuggestedGas.message": "Probabilmente fallirà", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "Il limite gas della transazione è troppo basso. Aumentalo al limite consigliato per evitare il fallimento.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "È probabile che la transazione fallisca", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Il limite gas è inferiore a quello stimato. Aumentalo al limite consigliato.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "La transazione fallirà", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "La commissione di base massima è inferiore a quella attuale. Aumentala per evitare che la transazione si blocchi.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "La transazione si bloccherà", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "La commissione massima della transazione è troppo bassa. Aumentala per evitare che la transazione si blocchi.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "La transazione si bloccherà", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "La commissione di priorità è inferiore a quella consigliata. Aumentala per velocizzare la transazione.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "La transazione potrebbe richiedere tempo", "UnsupportedMobileNetworkLayout.gotIt": "Capito!", "UnsupportedMobileNetworkLayout.subtitle": "Non puoi ancora effettuare transazioni o firmare messaggi sulla rete con ID {networkHexId} con la versione mobile di Zeal{br}{br}Usa l'estensione per browser per operare su questa rete, mentre lavoriamo per aggiungere il supporto 🚀", "UnsupportedMobileNetworkLayout.title": "Rete non supportata dalla versione mobile di Zeal", "UnsupportedSafeNetworkLayout.subtitle": "Non puoi effettuare transazioni o firmare messaggi su {network} con uno Zeal Smart Wallet{br}{br}Passa a una rete supportata o usa un portafoglio Legacy.", "UnsupportedSafeNetworkLayoutk.title": "Rete non supportata per Smart Wallet", "UserConfirmationPopup.goBack": "<PERSON><PERSON><PERSON>", "UserConfirmationPopup.submit": "Continua", "ViewPrivateKey.header": "Chiave privata", "ViewPrivateKey.hint": "Non condividere la tua chiave privata con nessuno. Conservala offline e al sicuro", "ViewPrivateKey.subheader.mobile": "Tocca per mostrare la tua Chiave privata", "ViewPrivateKey.subheader.web": "Passa il mouse per mostrare la tua Chiave privata", "ViewPrivateKey.unblur.mobile": "Tocca per mostrare", "ViewPrivateKey.unblur.web": "Passa il mouse per mostrare", "ViewSecretPhrase.PasswordChecker.subtitle": "Inserisci la password per crittografare il File di recupero. Dovrai ricordarla in futuro.", "ViewSecretPhrase.done": "<PERSON><PERSON>", "ViewSecretPhrase.header": "Frase segreta", "ViewSecretPhrase.hint": "Non condividere la tua frase con nessuno. Conservala offline e al sicuro", "ViewSecretPhrase.subheader.mobile": "Tocca per mostrare la tua Frase segreta", "ViewSecretPhrase.subheader.web": "Passa il mouse per mostrare la tua Frase segreta", "ViewSecretPhrase.unblur.mobile": "Tocca per mostrare", "ViewSecretPhrase.unblur.web": "Passa il mouse per mostrare", "account-details.monerium": "Trasferimenti con Monerium, IME autorizzato. <link>Scopri di più</link>", "account-details.unblock": "I trasferimenti vengono eseguiti tramite Unblock, un fornitore di servizi di cambio e custodia autorizzato e registrato. <link>Scopri di più</link>", "account-selector.empty-state": "<PERSON><PERSON><PERSON> t<PERSON>", "account-top-up.select-currency.title": "Token", "account.accounts_not_found": "Non abbiamo trovato nessun portafoglio", "account.accounts_not_found_search_valid_address": "Il portafoglio non è nella tua lista", "account.add.create_new_secret_phrase": "Crea una frase segreta", "account.add.create_new_secret_phrase.subtext": "Una nuova frase segreta di 12 parole", "account.add.fromRecoveryKit.fileNotFound": "Non siamo riusciti a trovare il tuo file", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "<PERSON><PERSON><PERSON><PERSON>", "account.add.fromRecoveryKit.fileNotFound.explanation": "Assicurati di usare l'account Google con la cartella di backup Zeal", "account.add.fromRecoveryKit.fileNotValid": "Il file di ripristino non è valido", "account.add.fromRecoveryKit.fileNotValid.explanation": "Il file non è del tipo corretto oppure è stato modificato", "account.add.import_secret_phrase": "Importa frase segreta", "account.add.import_secret_phrase.subtext": "<PERSON><PERSON><PERSON>, Metamask o altri", "account.add.select_type.add_hardware_wallet": "Portafoglio hardware", "account.add.select_type.existing_smart_wallet": "Smart Wallet esistente", "account.add.select_type.private_key": "Chiave privata", "account.add.select_type.seed_phrase": "Frase segreta", "account.add.select_type.title": "Importa portafoglio", "account.add.select_type.zeal_recovery_file": "File di ripristino <PERSON>", "account.add.success.title": "Nuovo portafoglio creato 🎉", "account.addLabel.header": "Dai un nome al tuo portafoglio", "account.addLabel.labelError.labelAlreadyExist": "Nome già in uso. Prova un altro nome", "account.addLabel.labelError.maxStringLengthExceeded": "Numero massimo di caratteri raggiunto", "account.add_active_wallet.primary_text": "Aggiungi portafoglio", "account.add_active_wallet.short_text": "<PERSON><PERSON>, collega o importa portafoglio", "account.add_from_ledger.success": "Portafogli aggiunti a Zeal", "account.add_tracked_wallet.primary_text": "Aggiungi portafoglio di sola lettura", "account.add_tracked_wallet.short_text": "Visualizza portafoglio e attività", "account.button.unlabelled-wallet": "Portafoglio senza etichetta", "account.create_wallet": "<PERSON><PERSON>", "account.label.edit.title": "Modifica nome portafoglio", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON> il {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Il file di ripristino non è valido", "account.recoveryKit.selectBackupFile.subtitle": "Seleziona il file di ripristino da usare", "account.recoveryKit.selectBackupFile.title": "File di ripristino", "account.recoveryKit.success.recoveryFileFound": "File di ripristino trovato 🎉", "account.select_type_of_account.create_eoa.short": "Portafoglio tradizionale per esperti", "account.select_type_of_account.create_eoa.title": "<PERSON><PERSON> port<PERSON>lio con frase segreta", "account.select_type_of_account.create_safe_wallet.title": "<PERSON><PERSON>", "account.select_type_of_account.existing_smart_wallet": "Smart Wallet esistente", "account.select_type_of_account.hardware_wallet": "Hardware wallet", "account.select_type_of_account.header": "Aggiungi portafoglio", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Chiave privata / Frase seed", "account.select_type_of_account.read_only_wallet": "Portafoglio di sola lettura", "account.select_type_of_account.read_only_wallet.short": "Visualizza qualsiasi portafoglio", "account.topup.title": "Aggiungi fondi a Zeal", "account.view.error.refreshAssets": "Aggiorna", "account.widget.refresh": "Aggiorna", "account.widget.settings": "Impostazioni", "accounts.view.copied-text": "Copiato {formattedAddress}", "accounts.view.copiedAddress": "Copiato {formattedAddress}", "action.accept": "Accetta", "action.accpet": "Accetta", "action.allow": "<PERSON><PERSON><PERSON>", "action.back": "Indietro", "action.cancel": "<PERSON><PERSON><PERSON>", "action.card-activation.title": "Attiva carta", "action.claim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.close": "<PERSON><PERSON>", "action.complete-steps": "Completa", "action.confirm": "Conferma", "action.continue": "Continua", "action.copy-address-understand": "Ok - Copia indirizzo", "action.deposit": "Deposita", "action.done": "<PERSON><PERSON>", "action.dontAllow": "Non consentire", "action.edit": "modifica", "action.email-required": "Inserisci email", "action.enterPhoneNumber": "Inserisci telefono", "action.expand": "Espandi", "action.fix": "<PERSON><PERSON><PERSON><PERSON>", "action.getStarted": "Inizia", "action.got_it": "Ho capito", "action.hide": "Nascondi", "action.import": "Importa", "action.import-keys": "<PERSON><PERSON><PERSON> chiavi", "action.importKeys": "<PERSON><PERSON><PERSON> chiavi", "action.minimize": "<PERSON><PERSON><PERSON>", "action.next": "<PERSON><PERSON>", "action.ok": "OK", "action.reduceAmount": "<PERSON><PERSON><PERSON><PERSON> al massimo", "action.refreshWebsite": "Aggiorna il sito", "action.remove": "<PERSON><PERSON><PERSON><PERSON>", "action.remove-account": "Rimuovi account", "action.requestCode": "<PERSON><PERSON> codice", "action.resend_code": "<PERSON><PERSON>via codice", "action.resend_code_with_time": "Reinvia codice {time}", "action.retry": "<PERSON><PERSON><PERSON><PERSON>", "action.reveal": "Mostra", "action.save": "<PERSON><PERSON>", "action.save_changes": "Salva RPC", "action.search": "Cerca", "action.seeAll": "<PERSON><PERSON>i tutto", "action.select": "Seleziona", "action.send": "Invia", "action.skip": "Salta", "action.submit": "Invia", "action.understood": "Ho capito", "action.update": "Aggiorna", "action.update-gnosis-pay-owner.complete": "<PERSON><PERSON>", "action.zeroAmount": "Inserisci importo", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "<PERSON><PERSON><PERSON> da collezione", "action_bar_title.tokens": "Token", "action_bar_title.transaction_request": "Richiesta di transazione", "activate-monerium.loading": "Configurazione del tuo conto personale", "activate-monerium.success.title": "Monerium attivato", "activate-physical-card-widget.subtitle": "La consegna può richiedere 3 settimane", "activate-physical-card-widget.title": "Attiva la carta fisica", "activate-smart-wallet.title": "Attiva portafoglio", "active_and_tracked_wallets.title": "Zeal copre tutte le tue commissioni su {network}, così puoi fare transazioni gratis!", "activity.approval-amount.revoked": "Revocata", "activity.approval-amount.unlimited": "Illimitato", "activity.approval.approved_for": "Approvato per", "activity.approval.approved_for_with_target": "Approvato {approvedTo}", "activity.approval.revoked_for": "Revocato per", "activity.bank.serviceProvider": "Fornitore del servizio", "activity.bridge.serviceProvider": "Fornitore del servizio", "activity.cashback.period": "Periodo di cashback", "activity.filter.card": "Carta", "activity.rate": "Tasso di cambio", "activity.receive.receivedFrom": "Rice<PERSON><PERSON> da", "activity.send.sendTo": "Inviato a", "activity.smartContract.unknown": "<PERSON><PERSON><PERSON> s<PERSON>", "activity.smartContract.usingContract": "<PERSON><PERSON><PERSON><PERSON> contratto", "activity.subtitle.pending_timer": "{timerString} In sospeso", "activity.title.arbitrary_smart_contract_interaction": "{function} su {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Interazione con contratto sconos<PERSON>uto", "activity.title.bridge.from": "Bridge da {token}", "activity.title.bridge.to": "Bridge a {token}", "activity.title.buy": "Acquisto {asset}", "activity.title.card_owners_updated": "Proprietari carta aggiornati", "activity.title.card_spend_limit_updated": "Limite di spesa carta impostato", "activity.title.cashback_deposit": "Deposito su <PERSON>back", "activity.title.cashback_reward": "Cashback ricevuto", "activity.title.cashback_withdraw": "Prelievo da Cashback", "activity.title.claimed_reward": "Ricompens<PERSON> riscattata", "activity.title.deployed_smart_wallet_gnosis": "Account creato", "activity.title.deposit_from_bank": "Deposito da banca", "activity.title.deposit_into_card": "Deposito su carta", "activity.title.deposit_into_earn": "Deposito su {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} su {smartContract}", "activity.title.from": "Da {sender}", "activity.title.pendidng_areward_claim": "Riscossione premio", "activity.title.pendidng_breward_claim": "Riscossione premio", "activity.title.recharge_disabledh": "Ricarica carta disattivata", "activity.title.recharge_set": "Obiettivo ricarica impostato", "activity.title.recovered_smart_wallet_gnosis": "Installazione nuovo dispositivo", "activity.title.send_pending": "A {receiver}", "activity.title.send_to_bank": "Alla banca", "activity.title.swap": "Ac<PERSON><PERSON> {token}", "activity.title.to": "A {receiver}", "activity.title.withdraw_from_card": "Prelievo da carta", "activity.title.withdraw_from_earn": "Prelievo da {earn}", "activity.transaction.networkFees": "Commissioni di rete", "activity.transaction.state": "Transazione completata", "activity.transaction.state.completed": "Transazione completata", "activity.transaction.state.failed": "Transazione fallita", "add-account.section.import.header": "Importa", "add-another-card-owner": "Aggiungi un altro titolare alla carta", "add-another-card-owner.Recommended.footnote": "Aggiungi Zeal alla tua carta Gnosis Pay", "add-another-card-owner.Recommended.primaryText": "<PERSON><PERSON><PERSON><PERSON><PERSON> Zeal a Gnosis Pay", "add-another-card-owner.recommended": "Consigliato", "add-owner.confirmation.subtitle": "<PERSON>, le modifiche alle impostazioni richiedono 3 minuti. Durante questo tempo, la tua carta sarà temporaneamente bloccata e i pagamenti non saranno possibili.", "add-owner.confirmation.title": "La tua carta sarà bloccata per 3 min durante l'aggiornamento", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Wallet già in uso, impossibile aggiungerlo", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "Prova un altro wallet", "add.account.backup.decrypt.success": "<PERSON><PERSON><PERSON><PERSON>", "add.account.backup.password.passwordIncorrectMessage": "La password non è corretta", "add.account.backup.password.subtitle": "Inserisci la password che hai usato per crittografare il tuo file di ripristino", "add.account.backup.password.title": "Inser<PERSON>ci password", "add.account.google.login.subtitle": "Approva la richiesta su Google Drive per sincronizzare il tuo file di ripristino", "add.account.google.login.title": "In attesa di approvazione...", "add.readonly.already_added": "Portafoglio già aggiunto", "add.readonly.continue": "Continua", "add.readonly.empty": "Inserisci un indirizzo o ENS", "addBankRecipient.title": "Aggiungi beneficiario", "add_funds.deposit_from_bank_account": "Deposita da conto bancario", "add_funds.from_another_wallet": "Da un altro portafoglio", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Connettiti alla dApp di ricarica", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "<PERSON><PERSON>i qualsiasi portafoglio alla dApp di ricarica Zeal e invia fondi rapidamente al tuo portafoglio", "add_funds.from_crypto_wallet.header": "Da un altro portafoglio", "add_funds.from_crypto_wallet.header.show_wallet_address": "Mostra l'indirizzo del tuo portafoglio", "add_funds.from_exchange.header": "Invia da un exchange", "add_funds.from_exchange.header.copy_wallet_address": "Copia il tuo indirizzo Z<PERSON>", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance, ecc.", "add_funds.from_exchange.header.open_exchange": "Apri l'app o il sito dell'exchange", "add_funds.from_exchange.header.selected_token": "Invia {token} a Zeal", "add_funds.from_exchange.header.selected_token.subtitle": "Su {network}", "add_funds.from_exchange.header.send_selected_token": "Invia un token supportato", "add_funds.from_exchange.header.send_selected_token.subtitle": "Seleziona token e rete supportati", "add_funds.import_wallet": "Importa un portafoglio crypto esistente", "add_funds.title": "Ricarica il tuo account", "add_funds.transfer_from_exchange": "<PERSON><PERSON><PERSON><PERSON><PERSON> da exchange", "address.add.header": "Visualizza il tuo portafoglio in Zeal{br}in modalità di sola lettura", "address.add.subheader": "Inserisci il tuo indirizzo o ENS per vedere i tuoi asset su tutte le reti EVM in un unico posto. Potrai creare o importare altri portafogli in seguito.", "address_book.change_account.bank_transfers.header": "<PERSON><PERSON><PERSON><PERSON> ban<PERSON>i", "address_book.change_account.bank_transfers.primary": "Destinatario bancario", "address_book.change_account.cta": "Traccia portafoglio", "address_book.change_account.search_placeholder": "Aggiungi o cerca indirizzo", "address_book.change_account.tracked_header": "Portafogli di sola lettura", "address_book.change_account.wallets_header": "Portafogli attivi", "app-association-check-failed.modal.cta": "<PERSON><PERSON><PERSON><PERSON>", "app-association-check-failed.modal.subtitle": "Riprova. Problemi di connettività causano ritardi nel recupero delle tue Passkey. Se il problema persiste, riavvia Z<PERSON> e riprova.", "app-association-check-failed.modal.subtitle.creation": "Riprova. Problemi di connettività stanno causando ritardi nella creazione della Passkey. Se il problema persiste, riavvia Z<PERSON> e riprova.", "app-association-check-failed.modal.title.creation": "Il tuo dispositivo non è riuscito a creare una passkey", "app-association-check-failed.modal.title.signing": "Il tuo dispositivo non è riuscito a caricare le passkey", "app.app_protocol_group.borrowed_tokens": "Token in prestito", "app.app_protocol_group.claimable_amount": "Importo riscattabile", "app.app_protocol_group.health_rate": "Tasso di salute", "app.app_protocol_group.lending": "<PERSON><PERSON><PERSON>", "app.app_protocol_group.locked_tokens": "<PERSON><PERSON> bloc<PERSON>i", "app.app_protocol_group.nfts": "Collezionabili", "app.app_protocol_group.reward_tokens": "Token premio", "app.app_protocol_group.supplied_tokens": "Token forniti", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Token in maturazione", "app.appsGroupHeader.discoverMore": "Scopri di più", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Cerca o inserisci URL", "app.error-banner.cory": "<PERSON><PERSON> dati errore", "app.error-banner.retry": "<PERSON><PERSON><PERSON><PERSON>", "app.list_item.rewards": "Premi {value}", "app.position_details.health_rate.description": "L'indice di salute è calcolato dividendo l'importo del tuo prestito per il valore della tua garanzia.", "app.position_details.health_rate.title": "Cos'è l'indice di salute?", "approval.edit-limit.label": "Modifica limite di spesa", "approval.permit_info": "Informazioni sul permesso", "approval.spend-limit.edit-modal.cancel": "<PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.limit-label": "Limite di spesa", "approval.spend-limit.edit-modal.max-limit-error": "Attenzione, limite elevato", "approval.spend-limit.edit-modal.revert": "<PERSON><PERSON><PERSON> modific<PERSON>", "approval.spend-limit.edit-modal.set-to-unlimited": "Imposta su Illimitato", "approval.spend-limit.edit-modal.submit": "Salva modifiche", "approval.spend-limit.edit-modal.title": "Modifica autorizzazioni", "approval.spend_limit_info": "Cos'è il limite di spesa?", "approval.what_are_approvals": "Cosa sono le approvazioni?", "apps_list.page.emptyState": "Nessuna app attiva", "backpace.removeLastDigit": "R<PERSON>uovi l'ultima cifra", "backup-banner.backup_now": "Fai il backup", "backup-banner.risk_losing_funds": "Fai il backup ora o rischi di perdere i fondi", "backup-banner.title": "<PERSON>et senza backup", "backupRecoverySmartWallet.noExportPrivateKeys": "Backup automatico: il tuo Smart Wallet è salvato come passkey, non servono frasi segrete o chiavi private.", "backupRecoverySmartWallet.safeContracts": "Sicurezza multi-chiave: i portafogli Zeal usano contratti Safe, così più dispositivi possono approvare una transazione. Nessun singolo punto di vulnerabilità.", "backupRecoverySmartWallet.security": "Dispositivi multipli: puoi usare il tuo portafoglio su più dispositivi con la passkey. Ogni dispositivo ottiene la sua chiave privata.", "backupRecoverySmartWallet.showLocalPrivateKey": "Modalità esperto: puoi esportare la chiave privata di questo dispositivo, usarla in un altro portafoglio e connetterti su <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Mostra la chiave privata</Key>", "backupRecoverySmartWallet.storingKeys": "Sincronizzazione cloud: la passkey è salvata in modo sicuro su iCloud, Google Password Manager o il tuo gestore di password.", "backupRecoverySmartWallet.title": "Backup e recupero Smart Wallet", "balance-change.card.titile": "Carta", "balanceChange.pending": "In attesa", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Seleziona fornitore del servizio", "bank-transfer.change-deposit-receiver.subtitle": "Questo portafoglio riceverà i depositi.", "bank-transfer.change-deposit-receiver.title": "Imposta portafoglio ricevente", "bank-transfer.change-owner.subtitle": "Usalo per accedere e recuperare l'account.", "bank-transfer.change-owner.title": "Imposta proprietario account", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Tutti i depositi arriveranno qui.", "bank-transfer.configrm-change-deposit-receiver.title": "Cambia portafoglio <PERSON>vente", "bank-transfer.configrm-change-owner.subtitle": "Serve per l'accesso e recupero.", "bank-transfer.configrm-change-owner.title": "Cambia proprietario account", "bank-transfer.deposit.widget.status.complete": "Completato", "bank-transfer.deposit.widget.status.funds_received": "Fondi rice<PERSON>ti", "bank-transfer.deposit.widget.status.sending_to_wallet": "<PERSON><PERSON> al portafoglio", "bank-transfer.deposit.widget.status.transfer-on-hold": "Trasferimento in sospeso", "bank-transfer.deposit.widget.status.transfer-received": "<PERSON><PERSON> al portafoglio", "bank-transfer.deposit.widget.subtitle": "{from} a {to}", "bank-transfer.deposit.widget.title": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.intro.bulletlist.point_1": "Configurazione con Unblock", "bank-transfer.intro.bulletlist.point_2": "Trasferisci tra EUR/GBP e più di 10 token", "bank-transfer.intro.bulletlist.point_3": "0% di commissioni fino a 5k $ mensili, 0,2% dopo", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Invio alla banca", "bank-transfer.withdrawal.widget.status.in-progress": "Trasferimento in corso", "bank-transfer.withdrawal.widget.status.on-hold": "Trasferimento in sospeso", "bank-transfer.withdrawal.widget.status.success": "Completato", "bank-transfer.withdrawal.widget.subtitle": "{from} a {to}", "bank-transfer.withdrawal.widget.title": "Prelievo", "bank-transfers.bank-account-actions.remove-this-account": "Rimuovi questo account", "bank-transfers.bank-account-actions.switch-to-this-account": "Passa a questo account", "bank-transfers.deposit.fees-for-less-than-5k": "Commissioni per 5k $ o meno", "bank-transfers.deposit.fees-for-more-than-5k": "Commissioni per più di 5k $", "bank-transfers.set-receiving-bank.title": "Imposta banca ricevente", "bank-transfers.settings.account_owner": "<PERSON><PERSON><PERSON> conto", "bank-transfers.settings.receiver_of_bank_deposits": "Destinatario dei depositi bancari", "bank-transfers.settings.receiver_of_withdrawals": "Destinatario dei prelievi", "bank-transfers.settings.registered_email": "Email registrata", "bank-transfers.settings.title": "Impostazioni bonifico bancario", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} Conto", "bank-transfers.setup.bank-account": "<PERSON><PERSON> bancario", "bankTransfer.withdraw.max_loading": "Max: {amount}", "bank_details_do_not_match.got_it": "Ho capito", "bank_details_do_not_match.subtitle": "Il sort code e il numero di conto non corrispondono. Ricontrolla che i dati siano corretti e riprova.", "bank_details_do_not_match.title": "I dati bancari non corrispondono", "bank_tranfsers.select_country_of_residence.country_not_supported": "I bonifici non sono supportati in {country} ancora", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Apri la tua app bancaria", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Invia {fiatCurrencyCode} al tuo conto", "bank_transfer.deposit.header": "{fullName}Dati del conto&nbsp;personale", "bank_transfer.kyc_status_widget.subtitle": "Bon<PERSON>i bancari", "bank_transfer.kyc_status_widget.title": "Verifica dell'identità", "bank_transfer.personal_details.date_of_birth": "Data di nascita", "bank_transfer.personal_details.date_of_birth.invalid_format": "Data non valida", "bank_transfer.personal_details.date_of_birth.too_young": "Devi avere almeno 18 anni", "bank_transfer.personal_details.first_name": "Nome", "bank_transfer.personal_details.last_name": "Cognome", "bank_transfer.personal_details.title": "I tuoi dati", "bank_transfer.reference.label": "Causale (Opzionale)", "bank_transfer.reference_message": "Inviato da Zeal", "bank_transfer.residence_details.address": "Il tuo indirizzo", "bank_transfer.residence_details.city": "Città", "bank_transfer.residence_details.country_of_residence": "Paese di residenza", "bank_transfer.residence_details.country_placeholder": "<PERSON><PERSON>", "bank_transfer.residence_details.postcode": "Codice postale", "bank_transfer.residence_details.street": "Via", "bank_transfer.residence_details.your_residence": "La tua residenza", "bank_transfers.choose-wallet.continue": "Continua", "bank_transfers.choose-wallet.test": "Aggiungi portafoglio", "bank_transfers.choose-wallet.warning.subtitle": "Puoi collegare un solo portafoglio alla volta. Non potrai cambiare il portafoglio collegato.", "bank_transfers.choose-wallet.warning.title": "<PERSON><PERSON><PERSON> il tuo portafoglio con attenzione", "bank_transfers.choose_wallet.subtitle": "<PERSON><PERSON>li il wallet per i tuoi bonifici. ", "bank_transfers.choose_wallet.title": "Scegli portafoglio", "bank_transfers.continue": "Continua", "bank_transfers.currency_is_currently_not_supported": "Continua", "bank_transfers.deposit-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.account-name": "Intestatario del conto", "bank_transfers.deposit.account-number-copied": "Numero di conto copiato", "bank_transfers.deposit.amount-input": "Importo da depositare", "bank_transfers.deposit.amount-output": "Importo di destinazione", "bank_transfers.deposit.amount-output.error": "errore", "bank_transfers.deposit.buttet-point.receive-crypto": "Ricevi {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "Continua", "bank_transfers.deposit.currency-not-supported.subtitle": "I depositi bancari da {code} sono stati disabilitati fino a nuovo avviso.", "bank_transfers.deposit.currency-not-supported.title": "{code} depositi al momento non supportati", "bank_transfers.deposit.default-token.balance": "Saldo {amount}", "bank_transfers.deposit.deposit-header": "Deposita", "bank_transfers.deposit.enter_amount": "Inserisci l'importo", "bank_transfers.deposit.iban-copied": "IBAN copiato", "bank_transfers.deposit.increase-amount": "Il trasferimento minimo è {limit}", "bank_transfers.deposit.loading": "Caricamento", "bank_transfers.deposit.max-limit-reached": "L'importo supera il limite massimo", "bank_transfers.deposit.modal.kyc.button-text": "Inizia", "bank_transfers.deposit.modal.kyc.text": "Per verificare la tua identità, avremo bisogno di alcuni dati personali e documenti. La procedura richiede in genere solo un paio di minuti.", "bank_transfers.deposit.modal.kyc.title": "Verifica la tua identità per aumentare i limiti", "bank_transfers.deposit.reduce_amount": "Riduci l'importo", "bank_transfers.deposit.show-account.account-number": "Numero di conto", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Sort code", "bank_transfers.deposit.sort-code-copied": "Sort code copiato", "bank_transfers.deposit.withdraw-header": "<PERSON><PERSON><PERSON>", "bank_transfers.failed_to_load_fee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.fees": "Commissioni", "bank_transfers.increase-amount": "Il trasferimento minimo è {limit}", "bank_transfers.insufficient-funds": "Fondi insufficienti", "bank_transfers.select_country_of_residence.title": "Dove vivi?", "bank_transfers.setup.cta": "Imposta bonifici", "bank_transfers.setup.enter-amount": "Inserisci importo", "bank_transfers.source_of_funds.form.business_income": "Reddito d'impresa", "bank_transfers.source_of_funds.form.other": "Altro", "bank_transfers.source_of_funds.form.pension": "Pensione", "bank_transfers.source_of_funds.form.salary": "Stipendio", "bank_transfers.source_of_funds.form.title": "La tua origine dei fondi", "bank_transfers.source_of_funds_description.placeholder": "Descrivi l'origine dei fondi...", "bank_transfers.source_of_funds_description.title": "Dicci di più sulla tua origine dei fondi", "bank_transfers.withdraw-header": "<PERSON><PERSON><PERSON>", "bank_transfers.withdraw.amount-input": "<PERSON><PERSON><PERSON><PERSON> da prelevare", "bank_transfers.withdraw.max-limit-reached": "L'importo supera il limite massimo", "bank_transfers.withdrawal.verify-id": "Riduci l'importo", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Limite massimo di inserimento superato", "banner.above_maximum_limit.maximum_limit_per_deposit": "Questo è il limite massimo per deposito", "banner.above_maximum_limit.subtitle": "Limite massimo di inserimento superato", "banner.above_maximum_limit.title": "Riduci l'importo a {amount} o meno", "banner.above_maximum_limit.title.default": "Riduci l'importo", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Limite minimo non raggiunto", "banner.below_minimum_limit.minimum_limit_for_token": "Questo è il limite minimo per questo token", "banner.below_minimum_limit.title": "Aumenta l'importo a {amount} o più", "banner.below_minimum_limit.title.default": "Aumenta l'importo", "breaard.in_porgress.info_popup.cta": "Spendi per guadagnare {earn}", "breaard.in_porgress.info_popup.footnote": "Usando <PERSON> e Gnosis Pay accetti i T&C.", "breaward.in_porgress.info_popup.bullet_point_1": "<PERSON><PERSON>di {remaining} entro i prossimi {time} per riscattare questo premio.", "breaward.in_porgress.info_popup.bullet_point_2": "<PERSON><PERSON> solo acquisti Gnosis Pay validi.", "breaward.in_porgress.info_popup.bullet_point_3": "Riscattato il premio, lo ricevi su Zeal.", "breaward.in_porgress.info_popup.header": "G<PERSON><PERSON>na {earn}, spendendo {remaining}", "breward.celebration.for_spending": "Per aver speso con la tua carta", "breward.dc25-eligible-celebration.for_spending": "Sei tra i primi {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "Non eri tra i primi {limit} a spendere", "breward.expired_banner.earn_by_spending": "Guadagna {earn} spendendo {amount}", "breward.expired_banner.reward_expired": "{earn} premio scaduto", "breward.in_progress_banner.cta.title": "Spendi per guadagnare {earn}", "breward.ready_to_claim.error.try_again": "<PERSON><PERSON><PERSON><PERSON>", "breward.ready_to_claim.error_title": "Riscatto del premio non riuscito", "breward.ready_to_claim.in_progress": "Riscatto del premio in corso", "breward.ready_to_claim.youve_earned": "Hai guadagnato {earn}!", "breward_already_claimed.title": "Ricompensa già riscattata. Se non hai ricevuto il token, contatta l'assistenza.", "breward_cannotbe_claimed.title": "Impossibile riscattare la ricompensa. Riprova più tardi.", "bridge.best_return": "Percorso con rendimento migliore", "bridge.best_serivce_time": "Percorso più veloce", "bridge.check_status.complete": "Completato", "bridge.check_status.progress_text": "Trasferendo {from} a {to}", "bridge.remove_topup": "Rimuovi ricarica", "bridge.request_status.completed": "Completato", "bridge.request_status.pending": "In attesa", "bridge.widget.completed": "Completato", "bridge.widget.currencies": "{from} a {to}", "bridge_rote.widget.title": "Bridge", "browse.discover_more_apps": "Scopri altre app", "browse.google_search_term": "Cerca \"{searchTerm}\"", "brward.celebration.you_earned": "Hai guadagnato", "brward.expired_banner.subtitle": "Più fortuna la prossima volta", "brward.in_progress_banner.subtitle": "Scade tra {expiredInFormatted}", "buy": "Acquista", "buy.enter_amount": "Inserisci l'importo", "buy.loading": "Caricamento...", "buy.no_routes_found": "<PERSON><PERSON><PERSON> per<PERSON>so trovato", "buy.not_enough_balance": "<PERSON><PERSON>", "buy.select-currency.title": "Seleziona token", "buy.select-to-currency.title": "Acquista token", "buy_form.title": "Acquista token", "cancelled-card.create-card-button.primary": "<PERSON>di una nuova carta virtuale", "cancelled-card.switch-card-button.primary": "Cambia carta", "cancelled-card.switch-card-button.short-text": "Hai un'altra carta attiva", "card": "Carta", "card-add-cash.confirm-stage.banner.no-routes-found": "<PERSON><PERSON><PERSON> per<PERSON>, prova un altro token o importo", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Ti serve {amount} in più {symbol} per pagare le commissioni", "card-add-cash.confirm-stage.banner.value-loss": "Perderai {loss} di valore", "card-add-cash.confirm-stage.banner.value-loss.revert": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.cancel": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.continue": "Continua", "card-add-cash.edit-stage.cta.enter-amount": "<PERSON><PERSON>a cifra", "card-add-cash.edit-stage.cta.reduce-to-max": "<PERSON><PERSON><PERSON><PERSON> a max", "card-add-cash.edit-staget.banner.no-routes-found": "<PERSON><PERSON><PERSON> per<PERSON>, prova un altro token o importo", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Richiesta inviata al wallet. Continua lì.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Firma con hardware wallet", "card-balance": "Saldo: {balance}", "card-cashback.status.title": "Deposito nel cashback", "card-copy-safe-address.copy_address": "Copia indirizzo", "card-copy-safe-address.copy_address.done": "Copiato", "card-copy-safe-address.warning.description": "<PERSON>o indirizzo può ricevere solo {cardAsset} su Gnosis Chain. Non inviare asset su altre reti a questo indirizzo. Andranno persi.", "card-copy-safe-address.warning.header": "Invia solo {cardAsset} su Gnosis Chain", "card-marketing-card.center.subtitle": "Commissioni FX", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "<PERSON><PERSON><PERSON>", "card-marketing-card.right.subtitle": "Regalo di benvenuto", "card-marketing-card.title": "La carta VISA europea ad alto interesse", "card-marketing-tile.get-started": "Inizia ora", "card-select-from-token-title": "Seleziona token di origine", "card-top-up.banner.subtitle.completed": "Completato", "card-top-up.banner.subtitle.failed": "Fallito", "card-top-up.banner.subtitle.pending": "{timerString} In attesa", "card-top-up.banner.title": "Deposito in corso {amount}", "card-topup.select-token.emptyState": "<PERSON><PERSON><PERSON> token trovato", "card.activate.card_number_not_valid": "Numero carta non valido. Controlla e riprova.", "card.activate.invalid_card_number": "Numero di carta non valido.", "card.activation.activate_physical_card": "Attiva carta fisica", "card.add-cash.amount-to-withdraw": "Importo ricarica", "card.add-from-earn-form.title": "Aggiungi fondi alla carta", "card.add-from-earn-form.withdraw-to-card": "Continua", "card.add-from-earn.amount-to-withdraw": "Importo da prelevare sulla carta", "card.add-from-earn.enter-amount": "Inserisci importo", "card.add-from-earn.loading": "Caricamento...", "card.add-from-earn.max-label": "Saldo: {amount}", "card.add-from-earn.no-routes-found": "<PERSON><PERSON><PERSON> percorso disponibile", "card.add-from-earn.not-enough-balance": "<PERSON>do non sufficiente", "card.add-owner.queued": "Aggiunta titolare in coda", "card.add-to-wallet-flow.subtitle": "Paga direttamente dal tuo wallet.", "card.add-to-wallet.copy-card-number": "Copia il numero della carta qui sotto", "card.add-to-wallet.title": "Aggiungi a {platformName} Wallet", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "ANNULLATA", "card.card-owner-not-found.disconnect-btn": "Scollega carta da Zeal", "card.card-owner-not-found.subtitle": "Aggiorna il proprietario per ricollegarla.", "card.card-owner-not-found.title": "Ricollega la carta", "card.card-owner-not-found.update-owner-btn": "Aggiorna proprietario", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} tra {date}", "card.cashback.widgetNoCashback.subtitle": "Deposita per iniziare a guadagnare", "card.cashback.widgetNoCashback.title": "Ottieni fino al {defaultPercentage} di cashback", "card.cashback.widgetcashbackValue.rewards": "{amount} in sospeso", "card.cashback.widgetcashbackValue.title": "{percentage} di cashback", "card.choose-wallet.connect_card": "Collega carta", "card.choose-wallet.create-new": "Aggiungi un nuovo wallet come titolare", "card.choose-wallet.import-another-wallet": "Importa un altro portafoglio", "card.choose-wallet.import-current-owner": "Importa l'attuale titolare della carta", "card.choose-wallet.import-current-owner.sub-text": "Importa chiavi/seed phrase del titolare", "card.choose-wallet.title": "Seleziona wallet per gestire la carta", "card.connectWalletToCardGuide": "Copia l'indirizzo del portafoglio", "card.connectWalletToCardGuide.addGnosisPayOwner": "Aggiungi titolare Gnosis Pay", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Apri Gnosispay.com con il tuo altro portafoglio{br}2. <PERSON><PERSON><PERSON> su \"Account\"{br}3. <PERSON><PERSON><PERSON> su \"Dettagli account\"{br}4. <PERSON><PERSON><PERSON> su \"Modifica\", accanto a \"Titolare account\", e{br}5. Clicca su \"Aggiungi indirizzo\"{br}6. Incolla il tuo indirizzo Zeal e clicca su Salva", "card.connectWalletToCardGuide.header": "Collega {account} alla carta Gnosis Pay", "card.connect_card.start": "Collega Gnosis Pay Card", "card.copiedAddress": "Copiato {formattedAddress}", "card.disconnect-account.title": "Scollega account", "card.hw-wallet-support-drop.add-owner-btn": "Aggiungi proprietario", "card.hw-wallet-support-drop.disconnect-btn": "Scollega carta da Zeal", "card.hw-wallet-support-drop.subtitle": "Aggiungi un proprietario non hardware wallet.", "card.hw-wallet-support-drop.title": "Zeal non supporta più hardware wallet.", "card.kyc.continue": "Continua la verifica", "card.list_item.title": "Carta", "card.onboarded.transactions.empty.description": "La cronologia dei tuoi pagamenti apparirà qui", "card.onboarded.transactions.empty.title": "Attività", "card.order.continue": "Continua ordine carta", "card.order.free_virtual_card": "<PERSON><PERSON> <PERSON>e gratis", "card.order.start": "Ordina la carta gratis", "card.owner-not-imported.cancel": "<PERSON><PERSON><PERSON>", "card.owner-not-imported.import": "Importa", "card.owner-not-imported.subtitle": "Per autorizzare questa transazione, collega a Zeal il wallet proprietario del tuo account Gnosis Pay. Nota: questa operazione è separata dal tuo solito accesso al wallet Gnosis Pay.", "card.owner-not-imported.title": "Aggiungi proprietario account Gnosis Pay", "card.page.order_free_physical_card": "Ordina carta gratuita", "card.pin.change_pin_at_atm": "Puoi cambiare il PIN presso alcuni ATM", "card.pin.timeout": "La schermata si chiuderà tra {seconds} sec", "card.quick-actions.add-assets": "Ricarica", "card.quick-actions.add-cash": "Ricarica", "card.quick-actions.details": "<PERSON><PERSON><PERSON>", "card.quick-actions.freeze": "Blocca", "card.quick-actions.freezing": "Blocco in corso", "card.quick-actions.unfreeze": "S<PERSON><PERSON>ca", "card.quick-actions.unfreezing": "Sblocco in corso", "card.quick-actions.withdraw": "<PERSON><PERSON><PERSON>", "card.read-only-detected.create-new": "Aggiungi un nuovo wallet come titolare", "card.read-only-detected.import-current-owner": "<PERSON><PERSON>rta chiavi per {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Importa chiavi private o seed phrase del wallet {address}", "card.read-only-detected.title": "Gestisci questa carta con un altro wallet", "card.remove-owner.queued": "Rimozione titolare in coda", "card.settings.disconnect-from-zeal": "<PERSON><PERSON><PERSON><PERSON> da Zeal", "card.settings.edit-owners": "Modifica proprietari carta", "card.settings.getCard": "<PERSON><PERSON> un'altra carta", "card.settings.getCard.subtitle": "Carte virtuali o fisiche", "card.settings.notRecharging": "Nessuna ricarica automatica", "card.settings.notifications.subtitle": "Ricevi notifiche di pagamento", "card.settings.notifications.title": "Notifiche carta", "card.settings.page.title": "Impostazioni carta", "card.settings.select-card.cancelled-cards": "Carte annullate", "card.settings.setAutoRecharge": "Imposta ricarica automatica", "card.settings.show-card-address": "Mostra indirizzo carta", "card.settings.spend-limit": "Imposta limite di spesa", "card.settings.spend-limit-title": "Limite giornal<PERSON>o attuale: {limit}", "card.settings.switch-active-card": "Cambia carta attiva", "card.settings.switch-active-card-description": "Carta attiva: {card}", "card.settings.switch-card.card-item.cancelled": "<PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.frozen": "<PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.title": "Carta Gnosis Pay", "card.settings.switch-card.card-item.title.physical": "Carta fisica", "card.settings.switch-card.card-item.title.virtual": "Carta virtuale", "card.settings.switch-card.title": "Seleziona carta", "card.settings.targetBalance": "Saldo target: {threshold}", "card.settings.view-pin": "Mostra PIN", "card.settings.view-pin-description": "Proteggi sempre il tuo PIN", "card.title": "Carta", "card.transactions.header": "Transazioni della carta", "card.transactions.see_all": "Vedi tutte le transazioni", "card.virtual": "VIRTUALE", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Il cashback viene inviato sulla tua carta all'inizio della settimana successiva a quella in cui è stato maturato.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "<PERSON><PERSON> depositi, più guadagni su ogni acquisto.", "cardCashback.onboarding.title": "O<PERSON>eni fino al {percentage} di cashback", "cardCashbackWithdraw.amount": "<PERSON><PERSON><PERSON><PERSON> da prelevare", "cardCashbackWithdraw.header": "<PERSON><PERSON>a {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "<PERSON>ta bloccata, impossibile attivarla.", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "Fino a {percentage}", "cardWidget.startEarning": "Inizia a guadagnare", "cardWithdraw.amount": "<PERSON><PERSON><PERSON><PERSON> da prelevare", "cardWithdraw.header": "<PERSON><PERSON><PERSON> dalla carta", "cardWithdraw.selectWithdrawWallet.title": "Scegli il portafoglio su cui{br}prelevare", "cardWithdraw.success.cta": "<PERSON><PERSON>", "cardWithdraw.success.subtitle": "<PERSON> sic<PERSON>, tutti i prelievi dalla carta Gnosis Pay richiedono 3 minuti per essere elaborati", "cardWithdraw.success.title": "Questa modifica richiederà 3 minuti", "card_top_up_trx.send": "Invio", "card_top_up_trx.to": "A", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "Data di scadenza", "cards.card_number": "Numero di carta", "cards.choose-wallet.no-active-accounts": "Non hai portafogli attivi", "cards.copied_card_number": "Numero di carta copiato", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Supera il limite giornaliero", "cards.transactions.decline_reason.incorrect_pin": "PIN errato", "cards.transactions.decline_reason.incorrect_security_code": "Codice di sicurezza errato", "cards.transactions.decline_reason.invalid_amount": "Importo non valido", "cards.transactions.decline_reason.low_balance": "<PERSON><PERSON>", "cards.transactions.decline_reason.other": "Rifiu<PERSON>", "cards.transactions.decline_reason.pin_tries_exceeded": "Tentativi PIN superati", "cards.transactions.status.refund": "<PERSON><PERSON><PERSON><PERSON>", "cards.transactions.status.reversal": "Storno", "cashback-deposit.trx.title": "Deposita su <PERSON>", "cashback-estimate.text": "Questa è una stima e NON un pagamento garantito. Vengono applicate tutte le regole di cashback note pubblicamente, ma Gnosis Pay può escludere transazioni a sua discrezione. Una spesa massima di {amount} a settimana dà diritto al cashback anche se la stima per questa transazione indicasse un importo totale superiore.", "cashback-estimate.text.fallback": "Questa è una stima e NON un importo garantito. Si applicano tutte le regole di cashback pubblicamente note, ma Gnosis Pay può escludere transazioni a sua discrezione.", "cashback-estimate.title": "Stima del cashback", "cashback-onbarding-tersm.subtitle": "I dati delle transazioni della tua carta saranno condivisi con <PERSON><PERSON><PERSON><PERSON>, responsabile della distribuzione dei premi cashback. Cliccando su 'Accetta', acconsenti ai <terms>Termini d'uso</terms>", "cashback-onbarding-tersm.title": "Termini d'uso e Privacy", "cashback-tx-activity.retry": "<PERSON><PERSON><PERSON><PERSON>", "cashback-unconfirmed-payments-info.subtitle": "I pagamenti danno diritto al cashback una volta saldati con l'esercente. Fino a quel momento, appaiono come pagamenti non confermati. I pagamenti non saldati non danno diritto al cashback.", "cashback-unconfirmed-payments-info.title": "Pagamenti con carta non confermati", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "<PERSON><PERSON><PERSON><PERSON>", "cashback.activity.title": "Attività recenti", "cashback.activity.withdrawal": "Prelievo", "cashback.deposit": "Deposita", "cashback.deposit.amount.label": "Importo da depositare", "cashback.deposit.change": "{from} a {to}", "cashback.deposit.confirmation.subtitle": "Le percentuali di cashback si aggiornano una volta a settimana. Deposita ora per aumentare il cashback della prossima settimana.", "cashback.deposit.confirmation.title": "Inizierai a guadagnare il {percentage} dal {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Scambia token in {currency} su {network} Chain", "cashback.deposit.get.tokens.title": "Ottieni {currency} token", "cashback.deposit.header": "Deposita {currency}", "cashback.deposit.max_label": "Max: {amount}", "cashback.deposit.select-wallet.title": "Scegli da quale wallet depositare", "cashback.deposit.yourcashback": "Il tuo cashback", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "<PERSON><PERSON>li il wallet su cui{br}prelevare", "cashback.transaction-details.network-label": "Rete", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "Da", "cashback.transaction-details.top-row.label-rewards": "Periodo del cashback", "cashback.transaction-details.top-row.label-withdrawal": "A", "cashback.transaction-details.transaction": "ID transazione", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Transazioni di cashback", "cashback.withdraw": "<PERSON><PERSON><PERSON>", "cashback.withdraw.confirmation.cashback_reduction": "Il cashback di questa settimana, incluso quello già maturato, si ridurrà da {before} a {after}", "cashback.withdraw.queued": "Prelievo in coda", "cashback.withdrawal.change": "{from} a {to}", "cashback.withdrawal.confirmation.subtitle": "Avvia il prelievo di {amount} con un ritardo di 3 minuti. Questo ridurrà il tuo cashback al {after}.", "cashback.withdrawal.confirmation.title": "Il cashback diminuirà se prelevi GNO", "cashback.withdrawal.delayTransaction.title": "Avvia prelievo GNO con{br} un ritardo di 3 minuti", "cashback.withdrawal.withdraw": "<PERSON><PERSON><PERSON>", "cashback.withdrawal.yourcashback": "Il tuo cashback", "celebration.aave": "Guadagnato con Aave", "celebration.cashback.subtitle": "<PERSON><PERSON><PERSON> in {code}", "celebration.cashback.subtitleGNO": "{amount} appena guadagnati", "celebration.chf": "<PERSON><PERSON><PERSON> con Frankencoin", "celebration.lido": "Guadagnato con Lido", "celebration.sky": "Guadagnato con Sky", "celebration.title": "Cashback totale", "celebration.well_done.title": "Ottimo lavoro!", "change-withdrawal-account.add-new-account": "Aggiungi un altro conto", "change-withdrawal-account.item.shortText": "{currency} Conto", "check-confirmation.approve.footer.for": "Per", "checkConfirmation.title": "Risultato della transazione", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin & Ether", "collateral.ethereum": "Ethereum", "collateral.other": "Altro", "collateral.rwa": "Asset del mondo reale", "collateral.stablecoins": "Stablecoin (ancorate a USD)", "collateral.us-t-bills": "Buoni del Tesoro USA", "confirm-bank-transfer-recipient.bullet-1": "Nessuna commissione su EUR digitali", "confirm-bank-transfer-recipient.bullet-2": "Depositi su {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Condividi i dettagli dell'account Gnosis Pay con Monerium, un istituto di moneta elettronica (EMI) autorizzato e regolamentato. <link>Scopri di più</link>", "confirm-bank-transfer-recipient.bullet-4": "Accetta i <link>termini di servizio</link>", "confirm-bank-transfer-recipient.title": "Accetta i termini", "confirm-change-withdrawal-account.cancel": "<PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.confirm": "Conferma", "confirm-change-withdrawal-account.saving": "Salvataggio", "confirm-change-withdrawal-account.subtitle": "Qui riceverai tutti i prelievi da Zeal.", "confirm-change-withdrawal-account.title": "Cambia banca ricevente", "confirm-ramove-withdrawal-account.title": "<PERSON><PERSON><PERSON><PERSON> conto bancario", "confirm-remove-withdrawal-account.subtitle": "Dati del conto rimossi. Puoi riaggiungerlo.", "confirmTransaction.finalNetworkFee": "Commissione di rete", "confirmTransaction.importKeys": "<PERSON><PERSON><PERSON> chiavi", "confirmTransaction.networkFee": "Commissione di rete", "confirmation.title": "Invia {amount} a {recipient}", "conflicting-monerium-account.add-owner": "Aggiungi come proprietario Gnosis Pay", "conflicting-monerium-account.create-wallet": "Crea un nuovo Smart Wallet", "conflicting-monerium-account.disconnect-card": "Scollega carta e ricollega con nuovo owner", "conflicting-monerium-account.header": "{wallet} collegato a un altro account Monerium", "conflicting-monerium-account.subtitle": "Cambia wallet proprietario Gnosis Pay", "connection.diconnected.got_it": "Capito!", "connection.diconnected.page1.subtitle": "Zeal è compatibile con MetaMask. Connettiti come fai di solito.", "connection.diconnected.page1.title": "Come connettersi con Zeal?", "connection.diconnected.page2.subtitle": "Vedrai diverse opzioni. Se Zeal non compare...", "connection.diconnected.page2.title": "<PERSON><PERSON><PERSON> port<PERSON>", "connection.diconnected.page3.subtitle": "Proporremo una connessione con Zeal. <PERSON><PERSON> o Injected funzionano. Prova!", "connection.diconnected.page3.title": "<PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.caution": "Attenzione", "connectionSafetyCheck.tag.danger": "<PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.passed": "Superato", "connectionSafetyConfirmation.subtitle": "Vuoi davvero continuare?", "connectionSafetyConfirmation.title": "Questo sito sembra pericoloso", "connection_state.connect.cancel": "<PERSON><PERSON><PERSON>", "connection_state.connect.changeToMetamask": "Passa a MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Passa a MetaMask", "connection_state.connect.connect_button": "<PERSON><PERSON><PERSON>", "connection_state.connect.expanded.connected": "<PERSON><PERSON><PERSON>", "connection_state.connect.expanded.title": "<PERSON><PERSON><PERSON>", "connection_state.connect.safetyChecksLoading": "Verifica della sicurezza del sito", "connection_state.connect.safetyChecksLoadingError": "Impossibile completare i controlli di sicurezza", "connection_state.connected.expanded.disconnectButton": "<PERSON><PERSON><PERSON>", "connection_state.connected.expanded.title": "<PERSON><PERSON><PERSON>", "copied-diagnostics": "Dati diagnostici copiati", "copy-diagnostics": "Copia da<PERSON>i", "counterparty.component.add_recipient_primary_text": "Aggiungi beneficiario", "counterparty.country": "<PERSON><PERSON>", "counterparty.countryTitle": "Paese del beneficiario", "counterparty.currency": "Valuta", "counterparty.delete.success.title": "<PERSON><PERSON><PERSON>", "counterparty.edit.success.title": "Modifiche salvate", "counterparty.errors.country_required": "Paese obbligatorio", "counterparty.errors.first_name.invalid": "Il nome deve essere più lungo", "counterparty.errors.last_name.invalid": "Il cognome deve essere più lungo", "counterparty.first_name": "Nome", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Invia alla banca", "countrySelector.noCountryFound": "<PERSON><PERSON><PERSON> paese trovato", "countrySelector.title": "<PERSON><PERSON><PERSON> il paese", "create-passkey.cta": "<PERSON><PERSON> passkey", "create-passkey.extension.cta": "Continua", "create-passkey.footnote": "Basato su", "create-passkey.mobile.cta": "Avvia configurazione", "create-passkey.steps.enable-recovery": "Imposta recupero cloud", "create-passkey.steps.setup-biometrics": "Abilita sicurezza biometrica", "create-passkey.subtitle": "Le passkey sono più sicure delle password e vengono crittografate nel cloud per un facile recupero.", "create-passkey.title": "Proteggi il conto", "create-smart-wallet": "<PERSON><PERSON>", "create-userop.progress.text": "Creazione in corso", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Continua su Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Vai su Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "Ordine carta già iniziato. Completalo sul sito di Gnosis Pay.", "create_recharge_preferences.card": "Carta", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "Guadagna {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "Tieni i fondi", "create_recharge_preferences.link_accounts_title": "Collega account", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "La tua carta NON si ricaricherà automaticamente dopo ogni pagamento.", "create_recharge_preferences.not_configured_title": "Guadagna e spendi", "create_recharge_preferences.recharge_from_earn_accounts_description": "La tua carta si ricarica automaticamente dopo ogni pagamento dal tuo account Earn.", "create_recharge_preferences.subtitle": "all'anno", "creating-account.loading": "Creazione conto in corso", "creating-gnosis-pay-account": "Creazione account in corso", "currencies.bridge.select_routes.emptyState": "<PERSON><PERSON><PERSON> percorso trovato per questo bridge", "currency.add_currency.add_token": "Aggiungi token", "currency.add_currency.not_a_valid_address": "Questo non è un indirizzo token valido", "currency.add_currency.token_decimals_feild": "Decimali del token", "currency.add_currency.token_feild": "Indirizzo del token", "currency.add_currency.token_symbol_feild": "Simbolo del token", "currency.add_currency.update_token": "Aggiorna token", "currency.add_custom.remove_token.cta": "<PERSON><PERSON><PERSON><PERSON>", "currency.add_custom.remove_token.header": "Rimuovi token", "currency.add_custom.remove_token.subtitle": "Il tuo portafoglio manterrà il saldo di questo token, ma sarà nascosto dai saldi del tuo portfolio Zeal.", "currency.add_custom.token_removed": "<PERSON><PERSON> rimosso", "currency.add_custom.token_updated": "Token a<PERSON>to", "currency.balance_label": "Saldo: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "Il tuo trasferimento bancario ha trasferito con successo {fiat} in {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "<PERSON> ricevuto {crypto}", "currency.bankTransfer.deposit_status.success": "Ricevuti nel tuo portaf<PERSON>lio", "currency.bankTransfer.deposit_status.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.check_bank_account": "Controlla il tuo conto bancario", "currency.bankTransfer.off_ramp.complete": "Completato", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Invio alla tua banca", "currency.bankTransfer.off_ramp.transferring_to_currency": "Trasferimento a {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "I fondi dovrebbero essere già arrivati sul tuo conto bancario.", "currency.bankTransfer.withdrawal_status.success": "Inviati alla tua banca", "currency.bankTransfer.withdrawal_status.title": "Prelievo", "currency.bank_transfer.create_unblock_user.email": "Indirizzo email", "currency.bank_transfer.create_unblock_user.email_invalid": "Email non valida", "currency.bank_transfer.create_unblock_user.email_missing": "Obbligatorio", "currency.bank_transfer.create_unblock_user.first_name": "Nome", "currency.bank_transfer.create_unblock_user.first_name_missing": "Obbligatorio", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "Solo lettere, numeri, spazi, e -.,&()'", "currency.bank_transfer.create_unblock_user.last_name": "Cognome", "currency.bank_transfer.create_unblock_user.last_name_missing": "Obbligatorio", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "Solo lettere, numeri, spazi, e -.,&()'", "currency.bank_transfer.create_unblock_user.note": "Con<PERSON>uando, accetti i <terms>Termini di Unblock</terms> e l' <policy>Informativa sulla Privacy</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Scrivi il nome come sul tuo conto.", "currency.bank_transfer.create_unblock_user.title": "Collega il tuo conto bancario", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Numero di conto", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Paese della banca", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Valuta preferita", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Sort code", "currency.bank_transfer.create_unblock_withdraw_account.success": "<PERSON>to impostato", "currency.bank_transfer.create_unblock_withdraw_account.title": "Collega il tuo conto bancario", "currency.bank_transfer.residence-form.address-required": "Obbligatorio", "currency.bank_transfer.residence-form.address-unsupported-char": "Solo lettere, numeri, spazi e , ; {apostrophe} - \\\\ consentiti.", "currency.bank_transfer.residence-form.city-required": "Obbligatorio", "currency.bank_transfer.residence-form.city-unsupported-char": "Solo lettere, numeri, spazi e . , - & ( ) {apostrophe} consentiti.", "currency.bank_transfer.residence-form.postcode-invalid": "Codice postale non valido", "currency.bank_transfer.residence-form.postcode-required": "Obbligatorio", "currency.bank_transfer.validation.invalid.account_number": "Numero di conto non valido", "currency.bank_transfer.validation.invalid.iban": "IBAN non valido", "currency.bank_transfer.validation.invalid.sort_code": "Sort code non valido", "currency.bridge.amount_label": "Importo da trasferire con Bridge", "currency.bridge.best_returns.subtitle": "Questo provider di bridge ti darà l'importo finale più alto, incluse tutte le commissioni.", "currency.bridge.best_returns_popup.title": "Rendi<PERSON> migliore", "currency.bridge.bridge_from": "Da", "currency.bridge.bridge_gas_fee_loading_failed": "Problemi nel caricare la commissione di rete", "currency.bridge.bridge_low_slippage": "Slippage molto basso. Prova ad aumentarlo", "currency.bridge.bridge_provider": "Fornitore del trasferimento", "currency.bridge.bridge_provider_loading_failed": "Abbiamo avuto problemi a caricare i provider", "currency.bridge.bridge_settings": "Impostazioni Bridge", "currency.bridge.bridge_status.subtitle": "Tramite {name}", "currency.bridge.bridge_status.title": "Bridge", "currency.bridge.bridge_to": "A", "currency.bridge.fastest_route_popup.subtitle": "Questo provider di bridge ti offre il percorso di transazione più veloce.", "currency.bridge.fastest_route_popup.title": "Percorso più veloce", "currency.bridge.from": "Da", "currency.bridge.success": "Completato", "currency.bridge.title": "Bridge", "currency.bridge.to": "A", "currency.bridge.topup": "Ricarica {symbol}", "currency.bridge.withdrawal_status.title": "Prelievo", "currency.card.card_top_up_status.title": "Aggiungi fondi alla carta", "currency.destination_amount": "Importo di destinazione", "currency.hide_currency.confirm.subtitle": "Nascondi questo token dal tuo portfolio. Potrai mostrarlo di nuovo in qualsiasi momento.", "currency.hide_currency.confirm.title": "Nascondi token", "currency.hide_currency.success.title": "Token nascosto", "currency.label": "Etichetta (Opzionale)", "currency.last_name": "Cognome", "currency.max_loading": "Max:", "currency.swap.amount_to_swap": "Importo da scam<PERSON>re", "currency.swap.best_return": "Percorso con rendimento migliore", "currency.swap.destination_amount": "Importo di destinazione", "currency.swap.header": "Scambia", "currency.swap.max_label": "Saldo: {amount}", "currency.swap.provider.header": "Fornitore di swap", "currency.swap.select_to_token": "Seleziona token", "currency.swap.swap_gas_fee_loading_failed": "Problemi nel caricare la commissione di rete", "currency.swap.swap_provider_loading_failed": "Problemi nel caricare i provider", "currency.swap.swap_settings": "Impostazioni di scambio", "currency.swap.swap_slippage_too_low": "Slippage molto basso. Prova ad aumentarlo", "currency.swaps_io_native_token_swap.subtitle": "Tramite Swaps.IO", "currency.swaps_io_native_token_swap.title": "Invia", "currency.withdrawal.amount_from": "Da", "currency.withdrawal.amount_to": "A", "currencySelector.title": "Scegli la valuta", "dApp.wallet-does-not-support-chain.subtitle": "Il tuo portafoglio sembra non supportare {network}. Prova a connetterti con un altro portafoglio o usa Zeal.", "dApp.wallet-does-not-support-chain.title": "Rete non supportata", "dapp.connection.manage.confirm.disconnect.all.cta": "<PERSON><PERSON> tutte", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Vuoi davvero disconnettere tutte le connessioni?", "dapp.connection.manage.confirm.disconnect.all.title": "Disconnetti tutte", "dapp.connection.manage.connection_list.main.button.title": "<PERSON><PERSON><PERSON>", "dapp.connection.manage.connection_list.no_connections": "Non hai app connesse", "dapp.connection.manage.connection_list.section.button.title": "Disconnetti tutte", "dapp.connection.manage.connection_list.section.title": "Attive", "dapp.connection.manage.connection_list.title": "<PERSON><PERSON><PERSON><PERSON>", "dapp.connection.manage.disconnect.success.title": "App disconnesse", "dapp.metamask_mode.title": "Modalità MetaMask", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "<PERSON><PERSON><PERSON>", "dc25-card-marketing-card.right.subtitle": "100 persone", "dc25-card-marketing-card.title": "I primi 100 che spenderanno 50€ otterranno {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Non potrai eseguire questa azione per 3 minuti. Per motivi di sicurezza, l'elaborazione di qualsiasi modifica alle impostazioni della carta o dei prelievi richiede 3 minuti.", "delayQueueBusyBanner.processing-yout-action.title": "Elaborazione della tua azione in corso, attendi", "delayQueueBusyWidget.cardFrozen": "<PERSON>ta bloccata", "delayQueueBusyWidget.processingAction": "Elaborazione in corso", "delayQueueFailedBanner.action-incomplete.get-support": "<PERSON><PERSON>", "delayQueueFailedBanner.action-incomplete.subtitle": "Errore. Contatta l'assistenza su Discord.", "delayQueueFailedBanner.action-incomplete.title": "Azione non completata", "delayQueueFailedWidget.actionIncomplete.title": "Azione sulla carta non completata", "delayQueueFailedWidget.cardFrozen.subtitle": "<PERSON>ta bloccata", "delayQueueFailedWidget.contactSupport": "Contatta il supporto", "delay_queue_busy.subtitle": "<PERSON> sic<PERSON>, la carta è bloccata 3 min.", "delay_queue_busy.title": "La tua azione è in fase di elaborazione", "delay_queue_failed.contact_support": "Chiedi a<PERSON>to", "delay_queue_failed.subtitle": "Errore. Contatta l'assistenza su Discord.", "delay_queue_failed.title": "Contatta l'assistenza", "deploy-earn-form-smart-wallet.in-progress.title": "Preparo <PERSON>", "deposit": "Deposita", "disconnect-card-popup.cancel": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.disconnect": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.subtitle": "Rimuovi la carta da Zeal (non Gnosis Pay).", "disconnect-card-popup.title": "Disconnetti carta", "distance.long.days": "{count} gior<PERSON>", "distance.long.hours": "{count} ore", "distance.long.minutes": "{count} minuti", "distance.long.months": "{count} mesi", "distance.long.seconds": "{count} secondi", "distance.long.years": "{count} anni", "distance.short.days": "{count} g", "distance.short.hours": "{count} h", "distance.short.minutes": "{count} min", "distance.short.months": "{count} m", "distance.short.seconds": "{count} sec", "distance.short.years": "{count} a", "duration.short.days": "{count}g", "duration.short.hours": "{count}h", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn-deposit-view.into": "In", "earn-deposit-view.to": "A", "earn-deposit.swap.transfer-provider": "Fornitore del trasferimento", "earn-taker-investment-details.accrued-realtime": "<PERSON><PERSON><PERSON> in tempo reale", "earn-taker-investment-details.asset-class": "Classe di asset", "earn-taker-investment-details.asset-coverage-ratio": "Rapporto di copertura degli asset", "earn-taker-investment-details.asset-reserve": "Riserva di asset", "earn-taker-investment-details.base_currency.label": "Valuta di base", "earn-taker-investment-details.chf.description": "Guadagna interessi sui tuoi CHF depositando zCHF in Frankencoin, un mercato monetario digitale di fiducia. Gli interessi sono generati da prestiti a basso rischio e super-garantiti su Frankencoin e pagati in tempo reale. I tuoi fondi restano al sicuro in un sub-account protetto che solo tu controlli.", "earn-taker-investment-details.chf.description.with_address_link": "Guadagna interessi sui tuoi CHF depositando zCHF in Frankencoin, un mercato monetario digitale di fiducia. Gli interessi sono generati da prestiti a basso rischio e super-garantiti su Frankencoin e pagati in tempo reale. I tuoi fondi restano al sicuro in un sub-account protetto <link>(copia 0x)</link> che solo tu controlli.", "earn-taker-investment-details.chf.label": "Franco svizzero digitale", "earn-taker-investment-details.collateral-composition": "Composizione del collaterale", "earn-taker-investment-details.depositor-obligations": "Obbligazioni verso i depositanti", "earn-taker-investment-details.eure.description": "Guadagna interessi sui tuoi euro depositando EURe in Aave, un mercato monetario digitale affidabile. EURe è una stablecoin in euro completamente regolamentata, emessa da Monerium e garantita 1:1 su conti protetti. Gli interessi sono generati da prestiti a basso rischio e sovracollateralizzati su Aave, e vengono pagati in tempo reale. I tuoi fondi restano in un sottoconto sicuro che solo tu controlli.", "earn-taker-investment-details.eure.description.with_address_link": "Guadagna interessi sui tuoi euro depositando EURe in Aave, un mercato monetario digitale affidabile. EURe è una stablecoin in euro completamente regolamentata, emessa da Monerium e garantita 1:1 su conti protetti. Gli interessi sono generati da prestiti a basso rischio e sovracollateralizzati su Aave, e vengono pagati in tempo reale. I tuoi fondi restano in un sottoconto sicuro <link>(copia 0x)</link> che solo tu controlli.", "earn-taker-investment-details.eure.label": "Euro digitale (EURe)", "earn-taker-investment-details.faq": "FAQ", "earn-taker-investment-details.fixed-income": "<PERSON><PERSON><PERSON> fisso", "earn-taker-investment-details.issuer": "<PERSON><PERSON><PERSON>", "earn-taker-investment-details.key-facts": "Informazioni chiave", "earn-taker-investment-details.liquidity": "Liquidità", "earn-taker-investment-details.operator": "Operatore di mercato", "earn-taker-investment-details.projected-yield": "Rendimento annuo previsto", "earn-taker-investment-details.see-other-faq": "Vedi tutte le altre FAQ", "earn-taker-investment-details.see-realtime": "Vedi i dati in tempo reale", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Rendimento degli ultimi 12 mesi", "earn-taker-investment-details.total-collateral": "Garanzia totale", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "ZCHF totali in circolazione", "earn-taker-investment-details.total_deposits": "Depositi totali su Aave", "earn-taker-investment-details.usd.description": "Sky è un mercato monetario digitale che offre rendimenti stabili denominati in dollari USA, derivanti da Titoli di Stato americani a breve scadenza e prestiti sovracollateralizzati, senza la volatilità delle crypto, con accesso ai fondi 24/7 e garanzie on-chain trasparenti.", "earn-taker-investment-details.usd.description.with_address_link": "Sky è un mercato monetario digitale che offre rendimenti stabili denominati in dollari USA, derivanti da Titoli di Stato americani a breve scadenza e prestiti sovracollateralizzati, senza la volatilità delle crypto, con accesso ai fondi 24/7 e garanzie on-chain trasparenti. Gli investimenti sono in un sottoconto <link>(copia 0x)</link> controllato da te.", "earn-taker-investment-details.usd.ftx-difference": "In cosa si differenzia da FTX, <PERSON><PERSON><PERSON>, BlockFi o Luna?", "earn-taker-investment-details.usd.high-returns": "Come possono i rendimenti essere così alti, specie rispetto alle banche tradizionali?", "earn-taker-investment-details.usd.how-is-backed": "Quali garanzie ha Sky USD e cosa succede ai miei soldi se Zeal fallisce?", "earn-taker-investment-details.usd.income-sources": "Fonti di reddito 2024", "earn-taker-investment-details.usd.insurance": "I miei fondi sono assicurati o garantiti da qualche ente (come il FITD o simili)?", "earn-taker-investment-details.usd.label": "Dollaro USA digitale", "earn-taker-investment-details.usd.lose-principal": "Posso davvero perdere il mio capitale e in quali circostanze?", "earn-taker-investment-details.variable-rate": "Prestito a tasso variabile", "earn-taker-investment-details.withdraw-anytime": "<PERSON><PERSON><PERSON> in qualsiasi momento", "earn-taker-investment-details.yield": "Rendimento", "earn-withdrawal-view.approve.for": "Per", "earn-withdrawal-view.approve.into": "In", "earn-withdrawal-view.swap.into": "In", "earn-withdrawal-view.withdraw.to": "A", "earn.add_another_asset.title": "Seleziona asset di guadagno", "earn.add_asset": "Aggiungi asset", "earn.asset_view.title": "Guadagna", "earn.base-currency-popup.text": "La valuta di base è il riferimento con cui vengono valutati e registrati i tuoi depositi, rendimenti e transazioni. Se depositi in una valuta diversa (ad esempio, EUR in un conto USD), i tuoi fondi vengono convertiti immediatamente nella valuta di base al tasso di cambio corrente. <PERSON><PERSON> la conversione, il tuo saldo rimane stabile nella valuta di base, ma i prelievi futuri potrebbero richiedere nuove conversioni di valuta.", "earn.base-currency-popup.title": "Valuta di base", "earn.card-recharge.disabled.list-item.title": "Ricarica automatica disattivata", "earn.card-recharge.enabled.list-item.title": "Ricarica automatica attivata", "earn.choose_wallet_to_deposit.title": "Deposita da", "earn.config.currency.eth": "Guadagna Ethereum", "earn.config.currency.on_chain_address_subtitle": "Indirizzo on-chain", "earn.config.currency.us_dollars": "Imposta trasferimenti bancari", "earn.configured_widget.current_apy.title": "APY attuale", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} Annuali", "earn.confirm.currency.cta": "Deposita", "earn.currency.eth": "Guadagna Ethereum", "earn.deploy.status.title": "Crea account Earn", "earn.deploy.status.title_with_taker": "Crea {title} conto Earn", "earn.deposit": "Deposita", "earn.deposit.amount_to_deposit": "Importo da depositare", "earn.deposit.deposit": "Deposita", "earn.deposit.enter_amount": "Inserisci l'importo", "earn.deposit.no_routes_found": "<PERSON><PERSON><PERSON> per<PERSON>so trovato", "earn.deposit.not_enough_balance": "<PERSON><PERSON>", "earn.deposit.select-currency.title": "Seleziona token da depositare", "earn.deposit.select_account.title": "Seleziona conto Earn", "earn.desposit_form.title": "Deposita in Guadagna", "earn.earn_deposit.status.title": "Deposita in Earn", "earn.earn_deposit.trx.title": "Deposita in Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "<PERSON><PERSON><PERSON> i fondi in qualsiasi momento", "earn.earn_withdraw.status.title": "<PERSON><PERSON><PERSON> dal conto Earn", "earn.earn_withdraw.trx.title.approval": "Approva prelievo", "earn.earn_withdraw.trx.title.withdraw_into_asset": "<PERSON><PERSON><PERSON> in {asset}", "earn.earn_withdraw.trx.title.withdrawal": "Prelievo da Earn", "earn.recharge.cta": "Salva modifiche", "earn.recharge.earn_not_configured.enable_some_account.error": "Abilita account", "earn.recharge.earn_not_configured.enter_amount.error": "Inserisci importo", "earn.recharge.select_taker.header": "Ricarica la carta in ordine da", "earn.recharge_card_tag.on": "ON", "earn.recharge_card_tag.recharge": "Ricarica", "earn.recharge_card_tag.recharge_not_configured": "Ricarica automatica", "earn.recharge_card_tag.recharge_off": "Ricarica disattivata", "earn.recharge_card_tag.recharged": "Ricaricato", "earn.recharge_card_tag.recharging": "In ricarica", "earn.recharge_configured.disable.trx.title": "Disattiva Ricarica Automatica", "earn.recharge_configured.trx.disclaimer": "Quando usi la tua carta, viene creata un'asta Cowswap per acquistare lo stesso importo del tuo pagamento usando i tuoi asset Earn. Questo processo di asta ti garantisce solitamente il miglior tasso di mercato, ma tieni presente che il tasso onchain potrebbe differire dai tassi di cambio reali.", "earn.recharge_configured.trx.subtitle": "<PERSON><PERSON> og<PERSON> paga<PERSON>, verranno aggiunti automaticamente fondi dai tuoi account Earn per mantenere il saldo della carta a {value}", "earn.recharge_configured.trx.title": "Imposta Ricarica Automatica a {value}", "earn.recharge_configured.updated.trx.title": "Salva Impostazioni Ricarica", "earn.risk-banner.subtitle": "Questo è un prodotto con auto-custodia senza protezione normativa contro le perdite.", "earn.risk-banner.title": "Comprendi i rischi", "earn.set_recharge.status.title": "Imposta ricarica automatica", "earn.setup_reacharge.input.disable.label": "Disabilita", "earn.setup_reacharge.input.label": "<PERSON>do target della carta", "earn.setup_reacharge_form.title": "La Ricarica Automatica mantiene la tua {br} carta con lo stesso saldo", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Conserva wstETH (Staked ETH) su Gnosis Chain e presta tramite Lido.", "earn.taker-bulletlist.point_1": "Guadagna il {apyValue} all'anno. I rendimenti variano con il mercato.", "earn.taker-bulletlist.point_3": "Zeal non applica commissioni.", "earn.taker-historical-returns": "Rendimenti storici", "earn.taker-historical-returns.chf": "Crescita da CHF a USD", "earn.taker-investment-tile.apy.perYear": "all'anno", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "Deposita", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Guadagno da Aave su Gnosis Chain", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Guadagno da Frankencoin su Gnosis Chain", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Guadagno da Lido su Gnosis Chain", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Guadagno da Maker su Gnosis Chain", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "Guadagno Eth", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "Guadagno ETH", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "Titoli di Stato USA (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Anche se estremamente raro, in teoria è possibile. I tuoi fondi sono protetti da una gestione del rischio rigorosa e da un'elevata collateralizzazione. Lo scenario peggiore realistico comporterebbe condizioni di mercato senza precedenti, come la perdita simultanea dell'ancoraggio da parte di più stablecoin, un evento mai accaduto prima.", "earn.usd.can-I-lose-my-principal-popup.title": "Posso davvero perdere il mio capitale e in quali circostanze?", "earn.usd.ftx-difference-popup.text": "Sky è fondamentalmente diverso. A differenza di FTX, <PERSON><PERSON><PERSON>, BlockFi o Luna, che si basavano su custodia centralizzata, gestione opaca degli asset e posizioni a leva rischiose, Sky USD utilizza smart contract decentralizzati, trasparenti e verificati, mantenendo piena trasparenza on-chain. Tu mantieni il pieno controllo con un portafoglio privato, riducendo notevolmente i rischi di controparte associati ai fallimenti centralizzati.", "earn.usd.ftx-difference-popup.title": "In cosa si differenzia da FTX, <PERSON><PERSON><PERSON>, BlockFi o Luna?", "earn.usd.high-returns-popup.text": "Sky USD genera rendimenti principalmente attraverso protocolli di finanza decentralizzata (DeFi), che automatizzano prestiti peer-to-peer e fornitura di liquidità, eliminando i costi e gli intermediari bancari tradizionali. Questa efficienza, unita a solidi controlli del rischio, consente rendimenti notevolmente più alti rispetto alle banche tradizionali.", "earn.usd.high-returns-popup.title": "Come possono i rendimenti essere così alti, specie rispetto alle banche tradizionali?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD è interamente garantito e sovracollateralizzato da una combinazione di asset digitali custoditi in smart contract sicuri e asset del mondo reale come i Titoli di Stato USA. Le riserve possono essere verificate in tempo reale on-chain, anche dall'interno di Zeal, offrendo trasparenza e sicurezza. Nell'improbabile caso in cui Zeal cessasse l'attività, i tuoi asset rimarrebbero al sicuro on-chain, sotto il tuo pieno controllo e accessibili tramite altri portafogli compatibili.", "earn.usd.how-is-sky-backed-popup.title": "Quali garanzie ha Sky USD e cosa succede ai miei soldi se Zeal fallisce?", "earn.usd.insurance-popup.text": "I fondi in Sky USD non sono assicurati dal FITD né coperti da garanzie statali tradizionali, perché si tratta di un conto basato su asset digitali, non di un conto bancario convenzionale. Sky gestisce la mitigazione del rischio tramite smart contract verificati e protocolli DeFi attentamente selezionati, garantendo che gli asset rimangano sicuri e trasparenti.", "earn.usd.insurance-popup.title": "I miei fondi sono assicurati o garantiti da qualche ente (come il FITD o simili)?", "earn.usd.lending-operations-popup.text": "Sky USD genera rendimento prestando stablecoin attraverso mercati di prestito decentralizzati come Morpho e Spark. Le tue stablecoin vengono prestate a mutuatari che depositano un collaterale (come ETH o BTC) di valore significativamente superiore a quello del prestito. Questo approccio, chiamato sovracollateralizzazione, assicura che ci sia sempre abbastanza garanzia per coprire i prestiti, riducendo notevolmente il rischio. Gli interessi raccolti e le occasionali commissioni di liquidazione pagate dai mutuatari forniscono rendimenti affidabili, trasparenti e sicuri.", "earn.usd.lending-operations-popup.title": "Operazioni di prestito", "earn.usd.market-making-operations-popup.text": "Sky USD ottiene un rendimento aggiuntivo partecipando a exchange decentralizzati (AMM) come Curve o Uniswap. Fornendo liquidità, cioè depositando le tue stablecoin in pool che facilitano gli scambi di crypto, Sky USD incassa le commissioni generate dalle transazioni. Questi pool di liquidità sono selezionati con cura per minimizzare la volatilità, utilizzando principalmente coppie stablecoin-stablecoin per ridurre significativamente rischi come l'impermanent loss, mantenendo i tuoi asset sicuri e accessibili.", "earn.usd.market-making-operations-popup.title": "Operazioni di Market Making", "earn.usd.treasury-operations-popup.text": "Sky USD genera un rendimento stabile e costante attraverso investimenti di tesoreria strategici. Parte dei tuoi depositi in stablecoin è allocata in asset del mondo reale sicuri e a basso rischio, principalmente titoli di stato a breve termine e strumenti di credito altamente sicuri. <PERSON>o approccio, simile a quello bancario tradizionale, assicura un rendimento prevedibile e affidabile. I tuoi asset rimangono sicuri, liquidi e gestiti in modo trasparente.", "earn.usd.treasury-operations-popup.title": "Operazioni di tesoreria", "earn.view_earn.card_rechard_off": "OFF", "earn.view_earn.card_rechard_on": "ON", "earn.view_earn.card_recharge": "Ricarica carta", "earn.view_earn.total_balance_label": "<PERSON><PERSON><PERSON><PERSON> il {percentage} all'anno", "earn.view_earn.total_earnings_label": "Guadag<PERSON> totali", "earn.withdraw": "<PERSON><PERSON><PERSON>", "earn.withdraw.amount_to_withdraw": "<PERSON><PERSON><PERSON><PERSON> da prelevare", "earn.withdraw.enter_amount": "Inserisci importo", "earn.withdraw.loading": "Caricamento", "earn.withdraw.no_routes_found": "<PERSON><PERSON><PERSON> per<PERSON>so trovato", "earn.withdraw.not_enough_balance": "<PERSON><PERSON>", "earn.withdraw.select-currency.title": "Seleziona token", "earn.withdraw.select_to_token": "Seleziona token", "earn.withdraw.withdraw": "<PERSON><PERSON><PERSON>", "earn.withdraw_form.title": "<PERSON><PERSON><PERSON>", "earnings-view.earnings": "Guadag<PERSON> totali", "edit-account-owners.add-owner.add-wallet": "Aggiungi titolare", "edit-account-owners.add-owner.add_wallet": "Aggiungi wallet", "edit-account-owners.add-owner.title": "Aggiungi titolare della carta", "edit-account-owners.card-owners": "Titolari della carta", "edit-account-owners.external-wallet": "<PERSON>et esterno", "editBankRecipient.title": "Modifica beneficiario", "editNetwork.addCustomRPC": "Aggiungi nodo RPC personalizzato", "editNetwork.cannot_verify.subtitle": "Il nodo RPC personalizzato non risponde correttamente. Controlla l'URL e riprova.", "editNetwork.cannot_verify.title": "Impossibile verificare il nodo RPC", "editNetwork.cannot_verify.try_again": "<PERSON><PERSON><PERSON><PERSON>", "editNetwork.customRPCNode": "Nodo RPC personalizzato", "editNetwork.defaultRPC": "RPC predefinito", "editNetwork.networkRPC": "RPC della rete", "editNetwork.rpc_url.cannot_be_empty": "Obbligatorio", "editNetwork.rpc_url.not_a_valid_https_url": "Deve essere un URL HTTP(S) valido", "editNetwork.safetyWarning.subtitle": "Zeal non può garantire la privacy, l'affidabilità e la sicurezza degli RPC personalizzati. Vuoi davvero usare un nodo RPC personalizzato?", "editNetwork.safetyWarning.title": "Gli RPC personalizzati possono essere insicuri", "editNetwork.zealRPCNode": "Nodo RPC di Zeal", "editNetworkRpc.headerTitle": "Nodo RPC personalizzato", "editNetworkRpc.rpcNodeUrl": "URL del nodo RPC", "editing-locked.modal.description": "A differenza delle Approvazioni, i Permessi non ti lasciano modificare Limite di Spesa o Scadenza. Assicurati di fidarti della dApp prima di inviarne uno.", "editing-locked.modal.title": "Modifica bloccata", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Abilito la ricarica", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Ricarica abilitata", "enterCardnumber": "Inserisci numero carta", "error.connectivity_error.subtitle": "Controlla la tua connessione internet e riprova.", "error.connectivity_error.title": "Nessuna connessione internet", "error.decrypt_incorrect_password.title": "Password errata", "error.encrypted_object_invalid_format.title": "<PERSON><PERSON>", "error.failed_to_fetch_google_auth_token.title": "Accesso non riuscito", "error.list.item.cta.action": "<PERSON><PERSON><PERSON><PERSON>", "error.trezor_action_cancelled.title": "Transazione rifiutata", "error.trezor_device_used_elsewhere.title": "Dispositivo in uso in un'altra sessione", "error.trezor_method_cancelled.title": "Impossibile <PERSON>croniz<PERSON>", "error.trezor_permissions_not_granted.title": "Impossibile <PERSON>croniz<PERSON>", "error.trezor_pin_cancelled.title": "Impossibile <PERSON>croniz<PERSON>", "error.trezor_popup_closed.title": "Impossibile <PERSON>croniz<PERSON>", "error.unblock_account_number_and_sort_code_mismatch": "Numero di conto e sort code non corrispondono", "error.unblock_can_not_change_details_after_kyc": "Non puoi modificare i dati dopo il KYC", "error.unblock_hard_kyc_failure": "Stato KYC imprevisto", "error.unblock_invalid_faster_payment_configuration.title": "Questa banca non supporta i Faster Payments", "error.unblock_invalid_iban": "IBAN non valido", "error.unblock_session_expired.title": "Sessione Unblock scaduta", "error.unblock_user_with_address_already_exists.title": "Conto già configurato per questo indirizzo", "error.unblock_user_with_such_email_already_exists.title": "Esiste già un utente con questa email", "error.unknown_error.error_message": "Messaggio di errore: ", "error.unknown_error.subtitle": "Spiacenti! Se hai bisogno di aiuto urgente, contatta l'assistenza e condividi i dettagli qui sotto.", "error.unknown_error.title": "Errore di sistema", "eth-cost-warning-modal.subtitle": "Gli Smart Wallet funzionano su Ethereum, ma le commissioni sono molto alte. Ti consigliamo VIVAMENTE di usare altre reti.", "eth-cost-warning-modal.title": "Evita Ethereum: le commissioni di rete sono alte", "exchange.form.button.chain_unsupported": "Rete non supportata", "exchange.form.button.refreshing": "Aggiornamento in corso", "exchange.form.error.asset_not_supported.button": "Seleziona un altro asset", "exchange.form.error.asset_not_supported.description": "Il Bridge non supporta questo asset.", "exchange.form.error.asset_not_supported.title": "Asset non supportato", "exchange.form.error.bridge_quote_timeout.button": "Seleziona un altro asset", "exchange.form.error.bridge_quote_timeout.description": "Prova un'altra coppia di token", "exchange.form.error.bridge_quote_timeout.title": "<PERSON><PERSON><PERSON> cambio trovato", "exchange.form.error.different_receiver_not_supported.button": "Rimuovi destinatario alternativo", "exchange.form.error.different_receiver_not_supported.description": "Questo exchange non supporta l'invio a un altro indirizzo.", "exchange.form.error.different_receiver_not_supported.title": "Indirizzo di invio e ricezione devono coincidere", "exchange.form.error.insufficient_input_amount.button": "Aumenta importo", "exchange.form.error.insufficient_liquidity.button": "Riduci importo", "exchange.form.error.insufficient_liquidity.description": "Il bridge non ha abbastanza liquidità. Prova con un importo inferiore.", "exchange.form.error.insufficient_liquidity.title": "Importo troppo alto", "exchange.form.error.max_amount_exceeded.button": "Riduci importo", "exchange.form.error.max_amount_exceeded.description": "L'importo massimo è stato superato.", "exchange.form.error.max_amount_exceeded.title": "Importo troppo alto", "exchange.form.error.min_amount_not_met.button": "Aumenta importo", "exchange.form.error.min_amount_not_met.description": "L'importo minimo di scambio per questo token non è stato raggiunto.", "exchange.form.error.min_amount_not_met.description_with_amount": "L'importo minimo di scambio è {amount}.", "exchange.form.error.min_amount_not_met.title": "Importo troppo basso", "exchange.form.error.min_amount_not_met.title_increase": "Aumenta l'importo", "exchange.form.error.no_routes_found.button": "Seleziona un altro asset", "exchange.form.error.no_routes_found.description": "Nessun percorso di scambio disponibile per questa combinazione di token/rete.", "exchange.form.error.no_routes_found.title": "Nessun cambio disponibile", "exchange.form.error.not_enough_balance.button": "Riduci importo", "exchange.form.error.not_enough_balance.description": "Non hai abbastanza di questo asset per la transazione.", "exchange.form.error.not_enough_balance.title": "<PERSON><PERSON>", "exchange.form.error.slippage_passed_is_too_low.button": "Aumenta slippage", "exchange.form.error.slippage_passed_is_too_low.description": "Lo slippage consentito è troppo basso per questo asset.", "exchange.form.error.slippage_passed_is_too_low.title": "Slippage troppo basso", "exchange.form.error.socket_internal_error.button": "R<PERSON>rova più tardi", "exchange.form.error.socket_internal_error.description": "Il partner per il Bridge sta riscontrando problemi. Riprova più tardi.", "exchange.form.error.socket_internal_error.title": "Errore del partner per il Bridge", "exchange.form.error.stargatev2_requires_fee_in_native": "Aggiungi {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Aggiungi {amount} per completare la transazione", "exchange.form.error.stargatev2_requires_fee_in_native.title": "<PERSON><PERSON><PERSON> pi<PERSON> {symbol}", "expiration-info.modal.description": "La scadenza indica per quanto tempo un'app può usare i tuoi token. Allo scadere, perde l'accesso finché non la rinnovi. Per sicure<PERSON>, imposta una scadenza breve.", "expiration-info.modal.title": "Cos'è la data di scadenza?", "expiration-time.high.modal.text": "Le scadenze dovrebbero essere brevi e basate sul tempo necessario. Scadenze lunghe sono rischiose e danno ai truffatori più possibilità di usare impropriamente i tuoi token.", "expiration-time.high.modal.title": "Scadenza lunga", "failed.transaction.content": "Probabile fallimento della transazione", "fee.unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "feedback-request.leave-message": "Lascia un messaggio", "feedback-request.not-now": "Non ora", "feedback-request.title": "Grazie! Come possiamo migliorare Zeal?", "float.input.period": "Separatore decimale", "gnosis-activate-card.info-popup.subtitle": "Prima usa Chip e PIN, poi contactless.", "gnosis-activate-card.info-popup.title": "Primo pagamento con Chip e PIN", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "Inserisci il numero della carta per attivarla.", "gnosis-activate-card.title": "Numero di carta", "gnosis-pay-re-kyc-widget.btn-text": "Verifica", "gnosis-pay-re-kyc-widget.title.not-started": "Verifica la tua identità", "gnosis-pay.login.cta": "Collega account esistente", "gnosis-pay.login.title": "Hai già un account Gnosis Pay", "gnosis-signup.confirm.subtitle": "Cerca un'email da Gnosis Pay, potrebbe essere finita nello spam.", "gnosis-signup.confirm.title": "Non hai ricevuto l'email di verifica?", "gnosis-signup.continue": "Continua", "gnosis-signup.dont_link_accounts": "Non collegare gli account", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "Inserisci <EMAIL>", "gnosis-signup.enter-email.title": "Inserisci email", "gnosis-signup.title": "Ho letto e accetto i <linkGnosisTNC>Termini e Condizioni</linkGnosisTNC> <monovateTerms>Termini del Titolare della Carta</monovateTerms> e i <linkMonerium>Termini Monerium</linkMonerium>.", "gnosis-signup.verify-email.title": "Verifica email", "gnosis.confirm.subtitle": "Nessun codice? Verifica il tuo numero.", "gnosis.confirm.title": "Codice inviato a {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Verifica", "gnosisPayAccountStatus.success.title": "Carta importata", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay non è ancora disponibile nel tuo paese", "gnosisPayNoActiveCardsFound.title": "Nessuna carta attiva", "gnosis_pay_card_delay_relay_not_empty_error.title": "Impossibile elaborare la transazione. Riprova più tardi.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "Carta non disponibile", "gnosiskyc.modal.approved.activate-free-card": "Attiva la carta gratuita", "gnosiskyc.modal.approved.button-text": "Deposita da conto", "gnosiskyc.modal.approved.title": "Il tuo account personale è stato creato.", "gnosiskyc.modal.failed.close": "<PERSON><PERSON>", "gnosiskyc.modal.failed.title": "Gnosis Pay non può creare un account per te.", "gnosiskyc.modal.in-progress.title": "Verifica ID richiede fino a 24 ore o più.", "goToSettingsPopup.settings": "Impostazioni", "goToSettingsPopup.title": "Puoi abilitare le notifiche nelle impostazioni del dispositivo in qualsiasi momento", "google_file.error.failed_to_fetch_auth_token.button_title": "<PERSON><PERSON><PERSON><PERSON>", "google_file.error.failed_to_fetch_auth_token.subtitle": "Per consentirci di usare il tuo file di recupero, concedi l'accesso sul tuo cloud personale.", "google_file.error.failed_to_fetch_auth_token.title": "Accesso non riuscito", "hidden_tokens.widget.emptyState": "Nessun token nascosto", "how_to_connect_to_metamask.got_it": "OK, capito", "how_to_connect_to_metamask.story.subtitle": "Passa facilmente da Zeal ad altri portafogli in qualsiasi momento.", "how_to_connect_to_metamask.story.title": "Zeal funziona insieme ad altri portafogli", "how_to_connect_to_metamask.why_switch": "Perché passare da Zeal ad altri portafogli?", "how_to_connect_to_metamask.why_switch.description": "Qualsiasi portafoglio tu scelga, i Controlli di Sicurezza Zeal ti proteggono sempre da siti e transazioni dannose.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "Sappiamo che è difficile passare a un nuovo portafoglio. Per questo, puoi usare Zeal insieme al tuo portafoglio attuale. Cambia quando vuoi.", "import-bank-transfer-owner.banner.title": "Il wallet per i bonifici è cambiato. Importalo per continuare.", "import-bank-transfer-owner.title": "Importa portafoglio per usare i trasferimenti su questo dispositivo", "import_gnosispay_wallet.add-another-card-owner.footnote": "Importa chiavi/seed phrase del titolare", "import_gnosispay_wallet.primaryText": "Importa wallet Gnosis Pay", "injected-wallet": "Portafoglio del browser", "intercom.getHelp": "<PERSON><PERSON>", "invalid_iban.got_it": "Ho capito", "invalid_iban.subtitle": "L'IBAN inserito non è valido. Ricontrolla che i dati siano corretti e riprova.", "invalid_iban.title": "IBAN non valido", "keypad-0": "Tasto 0", "keypad-1": "Tasto 1", "keypad-2": "Tasto 2", "keypad-3": "Tasto 3", "keypad-4": "Tasto 4", "keypad-5": "Tasto 5", "keypad-6": "Tasto 6", "keypad-7": "Tasto 7", "keypad-8": "Tasto 8", "keypad-9": "Tasto 9", "keypad.biometric-button": "Pulsante biometrico del tastierino", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Frase segreta al sicuro 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "Vedi la frase", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Conserva una copia offline sicura della tua Frase segreta per recuperare i tuoi fondi in futuro", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "Non provare a indovinare la parola", "keystore.write_secret_phrase.before_you_begin.first_point": "Comprendo che chiunque abbia la mia Frase segreta può trasferire i miei fondi", "keystore.write_secret_phrase.before_you_begin.second_point": "Sono responsabile di mantenere la mia Frase segreta al sicuro e riservata", "keystore.write_secret_phrase.before_you_begin.subtitle": "Leggi e accetta i seguenti punti:", "keystore.write_secret_phrase.before_you_begin.third_point": "Sono in un luogo privato, senza altre persone o telecamere intorno a me", "keystore.write_secret_phrase.before_you_begin.title": "Prima di iniziare", "keystore.write_secret_phrase.secret_phrase_test.title": "Qual è la parola numero {count} nella tua Frase segreta?", "keystore.write_secret_phrase.test_ps.lets_do_it": "Iniziamo", "keystore.write_secret_phrase.test_ps.subtitle": "Ti servirà la tua Frase segreta per ripristinare il conto su questo o altri dispositivi. Verifichiamo che sia scritta correttamente.", "keystore.write_secret_phrase.test_ps.subtitle2": "Ti chiederemo {count} parole della tua frase.", "keystore.write_secret_phrase.test_ps.title": "Test di recupero del conto", "kyc.modal.approved.button-text": "Fai bonifico", "kyc.modal.approved.subtitle": "Verifica completa. Bonifici illimitati.", "kyc.modal.approved.title": "Bonifici s<PERSON>i", "kyc.modal.continue-with-partner.button-text": "Continua", "kyc.modal.continue-with-partner.subtitle": "Ora ti reindirizzeremo al nostro partner per raccogliere la documentazione e completare la richiesta di verifica.", "kyc.modal.continue-with-partner.title": "Continua con il nostro partner", "kyc.modal.failed.unblock.subtitle": "Unblock non ha approvato la tua verifica.", "kyc.modal.failed.unblock.title": "Richiesta Unblock non approvata", "kyc.modal.paused.button-text": "Aggiorna dati", "kyc.modal.paused.subtitle": "Dati errati. Controlla e riprova.", "kyc.modal.paused.title": "I tuoi dati sembrano errati", "kyc.modal.pending.button-text": "<PERSON><PERSON>", "kyc.modal.pending.subtitle": "Verifica in meno di 10 min, a volte di più.", "kyc.modal.pending.title": "Ti terremo aggiornato", "kyc.modal.required.cta": "Inizia verifica", "kyc.modal.required.subtitle": "Limite raggiunto. Verifica la tua identità.", "kyc.modal.required.title": "Verifica dell'identità richiesta", "kyc.submitted": "Richiesta inviata", "kyc.submitted_short": "Inviata", "kyc_status.completed_status": "Completata", "kyc_status.failed_status": "Non riuscita", "kyc_status.paused_status": "In revisione", "kyc_status.subtitle": "Bon<PERSON>i bancari", "kyc_status.subtitle.wrong_details": "<PERSON><PERSON> errati", "kyc_status.subtitle_in_progress": "In corso", "kyc_status.title": "Verifica dell'identità", "label.close": "<PERSON><PERSON>", "label.saving": "Salvo...", "labels.this-month": "<PERSON>o mese", "labels.today": "<PERSON><PERSON><PERSON>", "labels.yesterday": "<PERSON><PERSON>", "language.selector.title": "<PERSON><PERSON>", "ledger.account_loaded.imported": "<PERSON><PERSON>rta<PERSON>", "ledger.add.success.title": "Ledger connesso con successo 🎉", "ledger.connect.cta": "Sincronizza Ledger", "ledger.connect.step1": "<PERSON><PERSON><PERSON> al tuo dispositivo", "ledger.connect.step2": "Apri l'app Ethereum su Ledger", "ledger.connect.step3": "Poi sincronizza il tuo Ledger 👇", "ledger.connect.subtitle": "Segui questi passaggi per importare i tuoi portafogli Ledger su Zeal", "ledger.connect.title": "<PERSON><PERSON><PERSON> a Zeal", "ledger.error.ledger_is_locked.subtitle": "Sblocca il Ledger e apri l'app Ethereum", "ledger.error.ledger_is_locked.title": "Ledger bloccato", "ledger.error.ledger_not_connected.action": "Sincronizza Ledger", "ledger.error.ledger_not_connected.subtitle": "Collega il tuo hardware wallet al dispositivo e apri l'app Ethereum", "ledger.error.ledger_not_connected.title": "Ledger non collegato", "ledger.error.ledger_running_non_eth_app.title": "App Ethereum non aperta", "ledger.error.user_trx_denied_by_user.action": "<PERSON><PERSON>", "ledger.error.user_trx_denied_by_user.subtitle": "Hai rifiutato la transazione sul tuo hardware wallet", "ledger.error.user_trx_denied_by_user.title": "Transazione rifiutata", "ledger.hd_path.bip44.subtitle": "<PERSON><PERSON>, Trezor", "ledger.hd_path.bip44.title": "Standard BIP44", "ledger.hd_path.ledger_live.subtitle": "Predefinito", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Legacy", "ledger.hd_path.phantom.subtitle": "es. Phantom", "ledger.select.hd_path.subtitle": "I percorsi HD sono il modo in cui i portafogli hardware ordinano i loro account. È simile a come un indice ordina le pagine di un libro.", "ledger.select.hd_path.title": "Seleziona percorso HD", "ledger.select_account.import_wallets_count": "{count,plural,=0{Nessun wallet selezionato} one{Importa wallet} other{Importa {count} wallet}}", "ledger.select_account.path_settings": "Impostazioni percorso", "ledger.select_account.subtitle": "Non vedi i portafogli che ti aspetti? Prova a cambiare le impostazioni del percorso", "ledger.select_account.subtitle.group_header": "Port<PERSON><PERSON><PERSON>", "ledger.select_account.title": "Importa portafogli Ledger", "legend.lending-operations": "Operazioni di prestito", "legend.market_making-operations": "Operazioni di Market Making", "legend.treasury-operations": "Operazioni di tesoreria", "link-existing-monerium-account-sign.button": "<PERSON><PERSON><PERSON>", "link-existing-monerium-account-sign.subtitle": "Hai già un account Monerium.", "link-existing-monerium-account-sign.title": "Collega Zeal al tuo account Monerium", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Hai già un account. Completa nell'app.", "link-existing-monerium-account.title": "Vai su Monerium e collega l'account", "loading.pin": "Caricamento PIN...", "lockScreen.passwordIncorrectMessage": "Password errata", "lockScreen.passwordRequiredMessage": "Password richiesta", "lockScreen.unlock.header": "S<PERSON><PERSON>ca", "lockScreen.unlock.subheader": "Usa la tua password per sbloccare Zeal", "mainTabs.activity.label": "Attività", "mainTabs.browse.label": "Esplora", "mainTabs.browse.title": "Esplora", "mainTabs.card.label": "Carta", "mainTabs.portfolio.label": "Portafoglio", "mainTabs.rewards.label": "<PERSON><PERSON>", "makeSpendable.cta": "<PERSON><PERSON>", "makeSpendable.holdAsCash": "Conserva i fondi", "makeSpendable.shortText": "G<PERSON><PERSON><PERSON><PERSON> {apy} all'anno", "makeSpendable.title": "{amount} ricevuti", "merchantCategory.agriculture": "Agricoltura", "merchantCategory.alcohol": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.antiques": "Antiquariato", "merchantCategory.appliances": "Elettrodomestici", "merchantCategory.artGalleries": "Gallerie d'arte", "merchantCategory.autoRepair": "Riparazione auto", "merchantCategory.autoRepairService": "Servizio riparazione auto", "merchantCategory.beautyFitnessSpas": "Bellezza, Fitness e Spa", "merchantCategory.beautyPersonalCare": "Bellezza e cura personale", "merchantCategory.billiard": "<PERSON><PERSON><PERSON>", "merchantCategory.books": "Libri", "merchantCategory.bowling": "Bowling", "merchantCategory.businessProfessionalServices": "Servizi professionali e aziendali", "merchantCategory.carRental": "Noleggio auto", "merchantCategory.carWash": "Autolavaggio", "merchantCategory.cars": "Auto", "merchantCategory.casino": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.casinoGambling": "Casinò e gioco d'azzardo", "merchantCategory.cellular": "Telefonia mobile", "merchantCategory.charity": "Beneficenza", "merchantCategory.childcare": "Assistenza all'infanzia", "merchantCategory.cigarette": "<PERSON><PERSON><PERSON>", "merchantCategory.cinema": "Cinema", "merchantCategory.cinemaEvents": "Cinema ed eventi", "merchantCategory.cleaning": "Pulizia", "merchantCategory.cleaningMaintenance": "Pulizia e manutenzione", "merchantCategory.clothes": "Abbigliamento", "merchantCategory.clothingServices": "Servizi di abbigliamento", "merchantCategory.communicationServices": "Servizi di comunicazione", "merchantCategory.construction": "Edilizia", "merchantCategory.cosmetics": "Cosmetici", "merchantCategory.craftsArtSupplies": "Artigianato e articoli per l'arte", "merchantCategory.datingServices": "Servizi di incontri", "merchantCategory.delivery": "Consegna", "merchantCategory.dentist": "<PERSON><PERSON>", "merchantCategory.departmentStores": "<PERSON><PERSON>", "merchantCategory.directMarketingSubscription": "Marketing diretto e abbonamenti", "merchantCategory.discountStores": "Discount", "merchantCategory.drugs": "<PERSON><PERSON><PERSON>", "merchantCategory.dutyFree": "Duty Free", "merchantCategory.education": "Istruzione", "merchantCategory.electricity": "Elettricità", "merchantCategory.electronics": "Elettronica", "merchantCategory.emergencyServices": "Servizi di emergenza", "merchantCategory.equipmentRental": "Noleggio attrezza<PERSON>", "merchantCategory.evCharging": "Ricarica veicoli elettrici", "merchantCategory.financialInstitutions": "Istituti finanziari", "merchantCategory.financialProfessionalServices": "Servizi finanziari e professionali", "merchantCategory.finesPenalties": "Multe e sanzioni", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "Voli", "merchantCategory.flowers": "<PERSON><PERSON>", "merchantCategory.flowersGarden": "Fiori e giardinaggio", "merchantCategory.food": "Cibo", "merchantCategory.freight": "Trasporto merci", "merchantCategory.fuel": "Carburante", "merchantCategory.funeralServices": "<PERSON><PERSON><PERSON>", "merchantCategory.furniture": "Mobili", "merchantCategory.games": "Giochi", "merchantCategory.gas": "Benzina", "merchantCategory.generalMerchandiseRetail": "Vendita al dettaglio e merci varie", "merchantCategory.gifts": "<PERSON><PERSON>", "merchantCategory.government": "<PERSON><PERSON> pubblici", "merchantCategory.governmentServices": "Servizi governativi", "merchantCategory.hardware": "Ferramenta", "merchantCategory.healthMedicine": "Salute e medicina", "merchantCategory.homeImprovement": "Bricolage", "merchantCategory.homeServices": "Servizi per la casa", "merchantCategory.hotel": "Hotel", "merchantCategory.housing": "Abitazione", "merchantCategory.insurance": "Assicurazione", "merchantCategory.internet": "Internet", "merchantCategory.kids": "Bambini", "merchantCategory.laundry": "Lavanderia", "merchantCategory.laundryCleaningServices": "Lavanderia e pulizie", "merchantCategory.legalGovernmentFees": "Spese legali e governative", "merchantCategory.luxuries": "Beni di lusso", "merchantCategory.luxuriesCollectibles": "Lusso e collezionismo", "merchantCategory.magazines": "Riviste", "merchantCategory.magazinesNews": "Riviste e notizie", "merchantCategory.marketplaces": "Marketplace", "merchantCategory.media": "Media", "merchantCategory.medicine": "Medicina", "merchantCategory.mobileHomes": "Case mobili", "merchantCategory.moneyTransferCrypto": "Trasferimento di denaro e cripto", "merchantCategory.musicRelated": "Musica e correlati", "merchantCategory.musicalInstruments": "Strumenti musicali", "merchantCategory.optics": "Ottica", "merchantCategory.organizationsClubs": "Organizzazioni e club", "merchantCategory.other": "Altro", "merchantCategory.parking": "Parcheggio", "merchantCategory.pawnShops": "Banchi dei pegni", "merchantCategory.pets": "Animali domestici", "merchantCategory.photoServicesSupplies": "Servizi e forniture fotografiche", "merchantCategory.postalServices": "Servizi postali", "merchantCategory.professionalServicesOther": "Servizi professionali (Altro)", "merchantCategory.publicTransport": "Trasporto pubblico", "merchantCategory.purchases": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.purchasesMiscServices": "Acquisti e servizi vari", "merchantCategory.recreationServices": "<PERSON><PERSON><PERSON>", "merchantCategory.religiousGoods": "Articoli religiosi", "merchantCategory.secondhandRetail": "Vendita di seconda mano", "merchantCategory.shoeHatRepair": "Riparazione scarpe e cappelli", "merchantCategory.shoeRepair": "Riparazione scarpe", "merchantCategory.softwareApps": "Software e app", "merchantCategory.specializedRepairs": "Riparazioni specializzate", "merchantCategory.sport": "Sport", "merchantCategory.sportingGoods": "Articoli sportivi", "merchantCategory.sportingGoodsRecreation": "Articoli sportivi e ricreazione", "merchantCategory.sportsClubsFields": "Club e campi sportivi", "merchantCategory.stationaryPrinting": "Cartoleria e stampa", "merchantCategory.stationery": "Cancelleria", "merchantCategory.storage": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.taxes": "Tasse", "merchantCategory.taxi": "Taxi", "merchantCategory.telecomEquipment": "Apparecchiature di telecomunicazione", "merchantCategory.telephony": "Telefonia", "merchantCategory.tobacco": "Ta<PERSON><PERSON>", "merchantCategory.tollRoad": "Pedaggio", "merchantCategory.tourismAttractionsAmusement": "Turismo, attrazioni e divertimento", "merchantCategory.towing": "<PERSON><PERSON>", "merchantCategory.toys": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.toysHobbies": "Giocattoli e hobby", "merchantCategory.trafficFine": "Multa stradale", "merchantCategory.train": "Treno", "merchantCategory.travelAgency": "Agenzia di viaggi", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, radio e streaming", "merchantCategory.utilities": "<PERSON><PERSON><PERSON>", "merchantCategory.waterTransport": "Trasporto via acqua", "merchantCategory.wholesaleClubs": "Club all'ingrosso", "metaMask.subtitle": "Attiva la Modalità MetaMask per usare Zeal al posto di MetaMask. Cliccando su MetaMask nelle dApp, ti connetterai a Zeal.", "metaMask.title": "Non riesci a connetterti con Zeal?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Apri la tua app bancaria", "monerium-bank-deposit.buttet-point.receive-crypto": "Ricevi EUR digitali", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Invia {fiatCurrencyCode} al tuo conto", "monerium-bank-deposit.deposit-account-country": "<PERSON><PERSON>", "monerium-bank-deposit.header": "{fullName}Conto personale", "monerium-bank-details.account-name": "Nome del conto", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT copiato", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT copiato", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN copiato", "monerium-bank-details.to-wallet": "Al portafoglio", "monerium-bank-details.transfer-fee": "Commissione di trasferimento", "monerium-bank-transfer.enable-card.bullet-1": "Completa la verifica dell'identità", "monerium-bank-transfer.enable-card.bullet-2": "O<PERSON><PERSON> i dati del tuo conto personale", "monerium-bank-transfer.enable-card.bullet-3": "Deposita dal tuo conto bancario", "monerium-card-delay-relay.success.cta": "<PERSON><PERSON>", "monerium-card-delay-relay.success.subtitle": "<PERSON>, le modifiche richiedono 3 min.", "monerium-card-delay-relay.success.title": "Torna tra 3 min per il setup di Monerium.", "monerium-deposit.account-details-info-popup.bullet-point-1": "<PERSON>tti gli {fiatCurrencyCode} che invii a questo conto diventano {cryptoCurrencyCode} token su {cryptoCurrencyChain} Chain e inviati al tuo portafoglio", "monerium-deposit.account-details-info-popup.bullet-point-2": "INVIA SOLO {fiatCurrencyCode} ({fiatCurrencySymbol}) al tuo conto", "monerium-deposit.account-details-info-popup.title": "Dettagli del tuo conto", "monerium.check_order_status.sending": "Invio in corso", "monerium.not-eligible.cta": "Indietro", "monerium.not-eligible.subtitle": "Monerium non può aprirti un conto.", "monerium.not-eligible.title": "Prova un altro fornitore", "monerium.setup-card.cancel": "<PERSON><PERSON><PERSON>", "monerium.setup-card.continue": "Continua", "monerium.setup-card.create_account": "Crea account", "monerium.setup-card.login": "Accedi a Gnosis Pay", "monerium.setup-card.subtitle": "Crea o accedi al tuo account Gnosis Pay.", "monerium.setup-card.subtitle_personal_account": "Ottieni il tuo conto personale con Gnosis Pay in pochi minuti:", "monerium.setup-card.title": "Abilita depositi bancari", "moneriumDepositSuccess.goToWallet": "Vai al portafoglio", "moneriumDepositSuccess.title": "{symbol} <PERSON><PERSON><PERSON>", "moneriumInfo.fees": "Commissioni allo 0%", "moneriumInfo.registration": "Monerium è autorizzato e regolamentato come Istituto di Moneta Elettronica ai sensi della legge islandese sulla moneta elettronica n. 17/2013 <link>Scopri di più</link>", "moneriumInfo.selfCustody": "Il denaro digitale che ricevi è custodito da te e nessun altro avrà il controllo sui tuoi fondi", "moneriumWithdrawRejected.supportText": "Trasferimento fallito. Riprova e se non va <link>contatta il supporto.</link>", "moneriumWithdrawRejected.title": "Trasferimento stornato", "moneriumWithdrawRejected.tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "moneriumWithdrawSuccess.supportText": "Potrebbero volerci 24 ore prima che il tuo{br}beneficiario rice<PERSON> i fondi", "moneriumWithdrawSuccess.title": "Inviato", "monerium_enable_banner.text": "Attiva ora i bonifici bancari", "monerium_error_address_re_link_required.title": "Il portafoglio deve essere ricollegato a Monerium", "monerium_error_duplicate_order.title": "Ordine duplicato", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "&lt; {limit}", "mt-pelerin-fork.options.chf.primary": "<PERSON> s<PERSON>", "mt-pelerin-fork.options.chf.short": "Istantaneo e gratuito con Mt Pelerin", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Istantaneo e gratuito con Monerium", "mt-pelerin-fork.title": "Cosa vuoi depositare?", "mtPelerinProviderInfo.fees": "<PERSON><PERSON><PERSON> lo 0% di commissioni", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd è affiliata a SO-FIT, un organismo di autodisciplina riconosciuto dall'Autorità federale di vigilanza sui mercati finanziari (FINMA) ai sensi della Legge sul riciclaggio di denaro. <link>Scopri di più</link>", "mtPelerinProviderInfo.selfCustody": "Il denaro digitale che ricevi è in un portafoglio privato e nessun altro avrà il controllo sui tuoi asset.", "network-fee-widget.title": "Commissioni", "network.edit.verifying_rpc": "Verifica RPC in corso", "network.editRpc.predefined_network_info.subtitle": "Come una VPN, Zeal usa RPC che impediscono il tracciamento dei tuoi dati personali.{br}{br}Gli RPC predefiniti di Zeal sono provider RPC affidabili e testati sul campo.", "network.editRpc.predefined_network_info.title": "RPC per la privacy di Zeal", "network.filter.update_rpc_success": "Nodo RPC salvato", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "<PERSON><PERSON> le reti", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "Aggiungi reti", "networks.filter.add_modal.chain_list.subtitle": "Aggiungi qualsiasi rete EVM", "networks.filter.add_modal.chain_list.title": "<PERSON><PERSON> a Chainlist", "networks.filter.add_modal.dapp_tip.subtitle": "Nelle tue dApp preferite, passa alla rete EVM che vuoi usare e Zeal ti chiederà se vuoi aggiungerla al tuo portafoglio.", "networks.filter.add_modal.dapp_tip.title": "Oppure aggiungi una rete da qualsiasi dApp", "networks.filter.add_networks.subtitle": "Tutte le reti EVM sono supportate", "networks.filter.add_networks.title": "Aggiungi reti", "networks.filter.add_test_networks.title": "Aggiungi testnet", "networks.filter.tab.netwokrs": "<PERSON><PERSON>", "networks.filter.testnets.title": "Testnet", "nft.widget.emptystate": "<PERSON><PERSON><PERSON> og<PERSON> da collezione nel portafoglio", "nft_collection.change_account_picture.subtitle": "Vuoi davvero aggiornare la tua immagine del profilo?", "nft_collection.change_account_picture.title": "Aggiorna immagine profilo con NFT", "nfts.allNfts.pricingPopup.description": "I prezzi degli oggetti da collezione si basano sul prezzo dell'ultima transazione.", "nfts.allNfts.pricingPopup.title": "<PERSON>zzi degli oggetti da collezione", "no-passkeys-found.modal.cta": "<PERSON><PERSON>", "no-passkeys-found.modal.subtitle": "Non riusciamo a rilevare passkey di Zeal su questo dispositivo. Assicurati di aver effettuato l'accesso all'account cloud usato per creare il tuo Smart Wallet.", "no-passkeys-found.modal.title": "Nessuna passkey trovata", "notValidEmail.title": "Indirizzo email non valido", "notValidPhone.title": "Numero di telefono non valido", "notification-settings.title": "Impostazioni notifiche", "notification-settings.toggles.active-wallets": "Portafogli attivi", "notification-settings.toggles.bank-transfers": "Trasferimenti bancari", "notification-settings.toggles.card-payments": "Pagamenti con carta", "notification-settings.toggles.readonly-wallets": "Portafogli in sola lettura", "ntft.groupHeader.text": "<PERSON><PERSON><PERSON> da collezione", "on_ramp.crypto_completed": "Completato", "on_ramp.fiat_completed": "Completato", "onboarding-widget.subtitle.card_created_from_order.left": "Carta Visa", "onboarding-widget.subtitle.card_created_from_order.right": "Attiva carta", "onboarding-widget.subtitle.card_order_ready.left": "Carta Visa fisica", "onboarding-widget.subtitle.default": "Bonifici e carta Visa", "onboarding-widget.title.card-order-in-progress": "Continua l'ordine della carta", "onboarding-widget.title.card_created_from_order": "La carta è stata spedita", "onboarding-widget.title.kyc_approved": "Bonifici e carta pronti", "onboarding-widget.title.kyc_failed": "Impossibile creare l'account", "onboarding-widget.title.kyc_not_started": "Continua la configurazione", "onboarding-widget.title.kyc_started_documents_requested": "Completa la verifica", "onboarding-widget.title.kyc_started_resubmission_requested": "Riprova la verifica", "onboarding-widget.title.kyc_started_verification_in_progress": "Verifica identità in corso", "onboarding.loginOrCreateAccount.amountOfAssets": "Più di 10 mld $ di fondi", "onboarding.loginOrCreateAccount.cards.subtitle": "Disponibile solo in alcune aree geografiche. Continuando accetti i nostri <Terms>Termini</Terms> e l' <PrivacyPolicy>Informativa sulla privacy</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Carta Visa con alti{br}rendimenti e zero commissioni", "onboarding.loginOrCreateAccount.createAccount": "<PERSON><PERSON> conto", "onboarding.loginOrCreateAccount.earn.subtitle": "I rendimenti variano; capitale a rischio. Continuando accetti i nostri <Terms>Termini</Terms> e l' <PrivacyPolicy>Informativa sulla privacy</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "Guadagna il {percent} all'anno{br}<PERSON><PERSON><PERSON> da oltre {currencySymbol}5 mld+", "onboarding.loginOrCreateAccount.earningPerYear": "G<PERSON>agna il {percent}{br} all'anno", "onboarding.loginOrCreateAccount.login": "Accedi", "onboarding.loginOrCreateAccount.trading.subtitle": "Capitale a rischio. Continuando accetti i nostri <Terms>Termini</Terms> e l' <PrivacyPolicy>Informativa sulla privacy</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "<PERSON><PERSON><PERSON> in tutto,{br}da BTC a S&P", "onboarding.loginOrCreateAccount.trustedBy": "Mercati monetari digitali{br}Con oltre {assets}", "onboarding.wallet_stories.close": "<PERSON><PERSON>", "onboarding.wallet_stories.previous": "Precedente", "order-earn-deposit-bridge.deposit": "<PERSON><PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.into": "In", "otpIncorrectMessage": "Il codice di conferma non è corretto", "passkey-creation-not-possible.modal.close": "<PERSON><PERSON>", "passkey-creation-not-possible.modal.subtitle": "Non siamo riusciti a creare una passkey per il tuo portafoglio. Assicurati che il tuo dispositivo supporti le passkey e riprova. <link>Contatta l'assistenza</link> se il problema persiste.", "passkey-creation-not-possible.modal.title": "Impossibile creare passkey", "passkey-not-supported-in-mobile-browser.modal.cta": "Scarica <PERSON>", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Gli Smart Wallet non sono supportati sui browser mobili.", "passkey-not-supported-in-mobile-browser.modal.title": "Scarica l'app Zeal per continuare", "passkey-recovery.recovering.deploy-signer.loading-text": "Verifica passkey in corso", "passkey-recovery.recovering.loading-text": "<PERSON><PERSON><PERSON> port<PERSON> in corso", "passkey-recovery.recovering.signer-not-found.subtitle": "Non siamo riusciti a collegare la tua passkey a un portafoglio attivo. Se hai fondi nel portafoglio, contatta il team di Zeal per ricevere assistenza.", "passkey-recovery.recovering.signer-not-found.title": "<PERSON><PERSON><PERSON> t<PERSON>", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "<PERSON>va un'altra passkey", "passkey-recovery.select-passkey.banner.subtitle": "Accedi all'account corretto sul tuo dispositivo. Le passkey sono specifiche per account.", "passkey-recovery.select-passkey.banner.title": "Non vedi la passkey del tuo portafoglio?", "passkey-recovery.select-passkey.continue": "Seleziona passkey", "passkey-recovery.select-passkey.subtitle": "Seleziona la passkey collegata al tuo portafoglio per riottenere l'accesso.", "passkey-recovery.select-passkey.title": "Seleziona passkey", "passkey-story_1.subtitle": "Con uno Smart Wallet puoi pagare le commissioni di rete con la maggior parte dei token, senza doverti preoccupare del gas.", "passkey-story_1.title": "Dimentica il gas: paga le commissioni di rete con la maggior parte dei token", "passkey-story_2.subtitle": "Basato sugli smart contract leader di settore di Safe, che proteggono oltre 100 miliardi di dollari in più di 20 milioni di portafogli.", "passkey-story_2.title": "<PERSON><PERSON><PERSON>", "passkey-story_3.subtitle": "Gli Smart Wallet funzionano sulle principali reti compatibili con Ethereum. Controlla le reti supportate prima di inviare asset.", "passkey-story_3.title": "Supporto per le principali reti EVM", "password.add.header": "Crea password", "password.add.includeLowerAndUppercase": "Lettere minuscole e maiuscole", "password.add.includesNumberOrSpecialChar": "Un numero o un simbolo", "password.add.info.subtitle": "Non inviamo la tua password ai nostri server né ne eseguiamo il backup per te", "password.add.info.t_and_c": "Continuando accetti i nostri <Terms>Termini</Terms> e l' <PrivacyPolicy>Informativa sulla privacy</PrivacyPolicy>", "password.add.info.title": "La tua password rimane su questo dispositivo", "password.add.inputPlaceholder": "Crea password", "password.add.shouldContainsMinCharsCheck": "10+ caratteri", "password.add.subheader": "Userai la password per sbloccare Zeal", "password.add.success.title": "Password creata 🔥", "password.confirm.header": "Conferma password", "password.confirm.passwordDidNotMatch": "Le <PERSON> devono corrispondere", "password.confirm.subheader": "Inserisci di nuovo la tua password", "password.create_pin.subtitle": "Questo codice blocca l'app Zeal", "password.create_pin.title": "Crea il tuo codice", "password.enter_pin.title": "<PERSON><PERSON><PERSON><PERSON> codice", "password.incorrectPin": "Codice errato", "password.pin_is_not_same": "Il codice non corrisponde", "password.placeholder.enter": "Inser<PERSON>ci password", "password.placeholder.reenter": "Reinserisci la password", "password.re_enter_pin.subtitle": "Inserisci di nuovo lo stesso codice", "password.re_enter_pin.title": "Conferma codice", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "In sospeso", "pending-send.details.pending": "In sospeso", "pending-send.details.processing": "In elaborazione", "permit-info.modal.description": "I permessi sono richieste che, se firmate, consentono alle app di spostare i tuoi token per tuo conto, ad esempio per effettuare uno swap.{br}I permessi sono simili alle approvazioni, ma non hanno commissioni di rete per la firma.", "permit-info.modal.title": "Cosa sono i permessi?", "permit.edit-expiration": "Modifica {currency} scadenza", "permit.edit-limit": "Modifica {currency} limite di spesa", "permit.edit-modal.expiresIn": "Scade tra...", "permit.expiration-warning": "{currency} avviso di scadenza", "permit.expiration.info": "{currency} info scadenza", "permit.expiration.never": "<PERSON>", "permit.spend-limit.info": "{currency} info limite di spesa", "permit.spend-limit.warning": "{currency} avviso limite di spesa", "phoneNumber.title": "numero di telefono", "physicalCardOrderFlow.cardOrdered": "Carta ordinata", "physicalCardOrderFlow.city": "Città", "physicalCardOrderFlow.orderCard": "Ordina carta", "physicalCardOrderFlow.postcode": "CAP", "physicalCardOrderFlow.shippingAddress.subtitle": "Dove verrà spedita la tua carta", "physicalCardOrderFlow.shippingAddress.title": "Indirizzo di spedizione", "physicalCardOrderFlow.street": "Via", "placeholderDapps.1inch.description": "Scambia usando i percorsi migliori", "placeholderDapps.aave.description": "Presta e prendi in prestito token", "placeholderDapps.bungee.description": "Bridge tra reti con i percorsi migliori", "placeholderDapps.compound.description": "Presta e prendi in prestito token", "placeholderDapps.cowswap.description": "Scambia alle migliori tariffe su Gnosis", "placeholderDapps.gnosis-pay.description": "Gestisci la tua carta Gnosis Pay", "placeholderDapps.jumper.description": "Bridge tra reti con i percorsi migliori", "placeholderDapps.lido.description": "Metti in stake ETH per ottenere più ETH", "placeholderDapps.monerium.description": "eMoney e bonifici bancari", "placeholderDapps.odos.description": "Scambia usando i percorsi migliori", "placeholderDapps.stargate.description": "Bridge o metti in stake per <14% APY", "placeholderDapps.uniswap.description": "Uno degli exchange più popolari", "pleaseAllowNotifications.cardPayments": "Pagamenti con carta", "pleaseAllowNotifications.customiseInSettings": "Personalizza nelle impostazioni", "pleaseAllowNotifications.enable": "Abilita", "pleaseAllowNotifications.forWalletActivity": "Per l'attività del portafoglio", "pleaseAllowNotifications.title": "Ricevi notifiche dal portafoglio", "pleaseAllowNotifications.whenReceivingAssets": "<PERSON>uando rice<PERSON> fondi", "portfolio.quick-actions.add_funds": "Deposita", "portfolio.quick-actions.buy": "Compra", "portfolio.quick-actions.deposit": "Deposita", "portfolio.quick-actions.send": "Invia", "portfolio.view.lastRefreshed": "Aggiornato {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Ricarica i tuoi AVAX di testnet", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Vai al Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "Ricarica i tuoi BNB di testnet", "portfolio.view.topupTestNet.BscTestnet.secondary": "Vai al Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Ricarica i tuoi SepETH di testnet", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Vai al Faucet di Sepolia", "portfolio.view.topupTestNet.FantomTestnet.primary": "Ricarica i tuoi FTM di testnet", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Vai al Faucet", "privateKeyConfirmation.banner.subtitle": "Chi ha la tua chiave privata ha accesso a portafoglio e fondi. Solo i truffatori la chiedono.", "privateKeyConfirmation.banner.title": "Comprendo i rischi", "privateKeyConfirmation.title": "NON CONDIVIDERE MAI la tua Chiave privata con nessuno", "rating-request.not-now": "Non ora", "rating-request.title": "Consigli<PERSON><PERSON><PERSON>?", "receive_funds.address-text": "Questo è l'indirizzo unico del tuo portafoglio. Puoi condividerlo con altri in sicurezza.", "receive_funds.copy_address": "Copia indirizzo", "receive_funds.network-warning.eoa.subtitle": "<link>Vedi reti standard</link>. Gli asset inviati su reti non EVM andranno persi.", "receive_funds.network-warning.eoa.title": "Tutte le reti basate su Ethereum sono supportate", "receive_funds.network-warning.scw.subtitle": "<link>Reti supportate</link>. Gli asset inviati su altre reti andranno persi.", "receive_funds.network-warning.scw.title": "Importante: usa solo le reti supportate", "receive_funds.scan_qr_code": "Scansiona un codice QR", "receiving.in.days": "In arrivo tra {days}g", "receiving.this.week": "In arrivo questa settimana", "receiving.today": "In arrivo oggi", "reference.error.maximum_number_of_characters_exceeded": "<PERSON><PERSON><PERSON>", "referral-code.placeholder": "Incolla link d'invito", "referral-code.subtitle": "Incolla il link per avere i tuoi premi.", "referral-code.title": "Un amico ti ha inviato {bReward}?", "rekyc.verification_deadline.subtitle": "Completa la verifica entro {daysUntil} giorni per continuare a usare la tua carta.", "rekyc.verification_required.subtitle": "Completa la verifica per usare la carta.", "reminder.fund": "💸 Aggiungi fondi — inizia subito a guadagnare il 6%", "reminder.onboarding": "🏁 Completa la configurazione — guadagna il 6% sui tuoi depositi", "remove-owner.confirmation.subtitle": "<PERSON>, l'aggiornamento delle impostazioni richiede 3 minuti. Durante questo tempo la tua carta sarà temporaneamente bloccata e i pagamenti non saranno possibili.", "remove-owner.confirmation.title": "La carta sarà bloccata per 3 minuti", "restore-smart-wallet.wallet-recovered": "Portafoglio recuperato", "rewardClaimCelebration.claimedTitle": "Premi già riscattati", "rewardClaimCelebration.subtitle": "Per aver invitato amici", "rewardClaimCelebration.title": "Hai guadagnato", "rewards-warning.subtitle": "Rimuovendo questo account, l'accesso a eventuali ricompense collegate sarà sospeso. Puoi ripristinarlo in qualsiasi momento per riscattarle.", "rewards-warning.title": "Perderai l'accesso alle tue ricompense", "rewards.copiedInviteLink": "Link d'invito copiato", "rewards.createAccount": "Copia link d'invito", "rewards.header.subtitle": "Invieremo {aReward} a te e {bReward} al tuo amico, quando spenderà {bSpendLimitReward}.", "rewards.header.title": "<PERSON><PERSON> {amountA}{br}Regala {amountB}", "rewards.sendInvite": "Invia invito", "rewards.sendInviteTip": "Scegli un amico e gli daremo {bAmount}", "route.fees": "Commissioni {fees}", "routesNotFound.description": "Il percorso di scambio per la combinazione di reti {from}-{to} non è disponibile.", "routesNotFound.title": "<PERSON><PERSON><PERSON> percorso di scambio disponibile", "rpc.OrderBuySignMessage.subtitle": "Tramite Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Tramite Swaps.IO", "rpc.addCustomNetwork.addNetwork": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rpc.addCustomNetwork.chainId": "ID catena", "rpc.addCustomNetwork.nativeToken": "Token nativo", "rpc.addCustomNetwork.networkName": "Nome rete", "rpc.addCustomNetwork.operationDescription": "Questo sito può aggiungere una rete al tuo portafoglio. Zeal non può verificare la sicurezza delle reti personalizzate. Assicurati di capire i rischi.", "rpc.addCustomNetwork.rpcUrl": "URL RPC", "rpc.addCustomNetwork.subtitle": "Tramite {name}", "rpc.addCustomNetwork.title": "Aggiungi rete", "rpc.send_token.network_not_supported.subtitle": "Stiamo lavorando per abilitare le transazioni su questa rete. Grazie per la pazienza 🙏", "rpc.send_token.network_not_supported.title": "Rete in arrivo", "rpc.send_token.send_or_receive.settings": "Impostazioni", "rpc.sign.accept": "Accetta", "rpc.sign.cannot_parse_message.body": "Impossibile decodificare questo messaggio. Accetta questa richiesta solo se ti fidi di questa app.{br}{br}I messaggi possono essere usati per accedere a un'app, ma possono anche dare alle app il controllo dei tuoi token.", "rpc.sign.cannot_parse_message.header": "Procedi con cautela", "rpc.sign.import_private_key": "<PERSON><PERSON><PERSON> chiavi", "rpc.sign.subtitle": "Per {name}", "rpc.sign.title": "Firma", "safe-creation.success.title": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "safe-safety-checks-popup.title": "Controlli di sicurezza della transazione", "safetyChecksPopup.title": "Controlli di sicurezza del sito", "scan_qr_code.description": "Scansiona un QR del portafoglio o connettiti a un'app", "scan_qr_code.show_qr_code": "Mostra il mio codice QR", "scan_qr_code.tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "scan_qr_code.unlockCamera": "Sblocca fotocamera", "screen-lock-missing.modal.close": "<PERSON><PERSON>", "screen-lock-missing.modal.subtitle": "Il tuo dispositivo richiede un blocco schermo per usare le passkey. Impostalo e riprova.", "screen-lock-missing.modal.title": "Blocco schermo mancante", "seedConfirmation.banner.subtitle": "Chi ha la tua Frase segreta ha accesso a portafoglio e fondi. Solo i truffatori la chiedono.", "seedConfirmation.title": "NON CONDIVIDERE MAI la tua Frase segreta con nessuno", "select-active-owner.subtitle": "Hai più wallet collegati alla tua carta. Selezionane uno da connettere a Zeal. Puoi cambiarlo quando vuoi.", "select-active-owner.title": "Seleziona wallet", "select-card.title": "Seleziona carta", "select-crypto-currency-title": "Seleziona token", "select-token.title": "Seleziona token", "selectEarnAccount.chf.description.steps": "· Preleva fondi 24/7, senza vincoli {br}· Gli interessi maturano ogni secondo {br}· Depositi super-garantiti in <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} all'anno in CHF", "selectEarnAccount.eur.description.steps": "· Preleva fondi 24/7, senza vincoli {br}· Gli interessi maturano ogni secondo {br}· Prestiti iper-garantiti con <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} all'anno in EUR", "selectEarnAccount.subtitle": "Puoi cambiare quando vuoi", "selectEarnAccount.title": "Seleziona valuta", "selectEarnAccount.usd.description.steps": "· Preleva fondi 24/7, senza vincoli {br}· Gli interessi maturano ogni secondo {br}· Depositi iper-garantiti in <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} all'anno in USD", "selectEarnAccount.zero.description_general": "Tieni fondi digitali senza guadagnare interessi", "selectEarnAccount.zero.title": "0% all'anno", "selectRechargeThreshold.button.enterAmount": "Inserisci importo", "selectRechargeThreshold.button.setTo": "Imposta su {amount}", "selectRechargeThreshold.description.line1": "Quando il saldo della tua carta scende sotto {amount}, si ricarica automaticamente fino a {amount} dal tuo account Earn.", "selectRechargeThreshold.description.line2": "Un obiettivo più basso mantiene più fondi nel tuo account Earn (guadagnando il 3%). Puoi cambiarlo quando vuoi.", "selectRechargeThreshold.title": "Imposta saldo target della carta", "select_currency_to_withdraw.select_token_to_withdraw": "Seleziona il token da prelevare", "send-card-token.form.send": "Invia", "send-card-token.form.send-amount": "Importo della ricarica", "send-card-token.form.title": "Aggiungi fondi alla carta", "send-card-token.form.to-address": "Carta", "send-safe-transaction.network-fee-widget.error": "Ti serve {amount} o scegli un altro token", "send-safe-transaction.network-fee-widget.no-fee": "Nessuna commissione", "send-safe-transaction.network-fee-widget.title": "Commissioni", "send-safe-transaction.network_fee_widget.title": "Commissione di rete", "send.banner.fees": "<PERSON><PERSON><PERSON> {amount} {currency} per pagare le commissioni", "send.banner.toAddressNotSupportedNetwork.subtitle": "Il portafoglio del destinatario non supporta {network}. Cambia con un token supportato.", "send.banner.toAddressNotSupportedNetwork.title": "Rete non supportata dal destinatario", "send.banner.walletNotSupportedNetwork.subtitle": "Gli Smart Wallet non possono effettuare transazioni su {network}. Cambia con un token supportato.", "send.banner.walletNotSupportedNetwork.title": "Rete del token non supportata", "send.empty-portfolio.empty-state": "<PERSON><PERSON><PERSON> token trovato", "send.empty-portfolio.header": "Token", "send.titile": "Invio", "sendLimit.success.subtitle": "Nuovo limite attivo in 3 minuti.", "sendLimit.success.title": "La modifica richiederà 3 minuti", "send_crypto.form.disconnected.cta.addFunds": "Aggiungi fondi", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Passa a {network}", "send_crypto.form.disconnected.label": "Importo da trasferire", "send_to.qr_code.description": "Scansiona un codice QR per inviare a un portafoglio", "send_to.qr_code.title": "Scansiona codice QR", "send_to_card.header": "Invia all'indirizzo della carta", "send_to_card.select_sender.add_wallet": "Aggiungi wallet", "send_to_card.select_sender.header": "Seleziona mittente", "send_to_card.select_sender.search.default_placeholder": "Cerca indirizzo o ENS", "send_to_card.select_sender.show_card_address_button_description": "Mostra indirizzo della carta", "send_token.form.select-address": "Seleziona indirizzo", "send_token.form.send-amount": "Importo di invio", "send_token.form.title": "Invia", "setLimit.amount.error.zero_amount": "Non potrai effettuare pagamenti", "setLimit.error.max_limit_reached": "Imposta limite max {amount}", "setLimit.error.same_as_current_limit": "Limite attuale invariato", "setLimit.placeholder": "Attuale: {amount}", "setLimit.submit": "Imposta limite", "setLimit.submit.error.amount_required": "Inserisci importo", "setLimit.subtitle": "L'importo che puoi spendere al giorno.", "setLimit.title": "Imposta limite di spesa giornaliero", "settings.accounts": "Account", "settings.accountsSeeAll": "<PERSON><PERSON> tutto", "settings.addAccount": "Aggiungi portafoglio", "settings.card": "Impostazioni carta", "settings.connections": "Connessioni app", "settings.currency": "Valuta predefinita", "settings.default_currency_selector.title": "Valuta", "settings.discord": "Discord", "settings.experimentalMode": "Modalità sperimentale", "settings.experimentalMode.subtitle": "Prova le nuove funzionalità", "settings.language": "<PERSON><PERSON>", "settings.lockZeal": "Blocca Zeal", "settings.notifications": "Notifiche", "settings.open_expanded_view": "Apri la vista espansa", "settings.privacyPolicy": "Informativa sulla privacy", "settings.settings": "Impostazioni", "settings.termsOfUse": "Termini di utilizzo", "settings.twitter": "𝕏 / Twitter", "settings.version": "Versione {version} amb: {env}", "setup-card.confirmation": "Richiedi carta virtuale", "setup-card.confirmation.subtitle": "Paga online e aggiungila al tuo {type} wallet per pagamenti contactless.", "setup-card.getCard": "<PERSON><PERSON> carta", "setup-card.order.physicalCard": "Carta fisica", "setup-card.order.physicalCard.steps": "· Una carta fisica VISA Gnosis Pay {br}· Spedizione in massimo 3 settimane {br}· Usala per pagamenti fisici e prelievi ATM {br}· Aggiungila a wallet Apple/Google (solo paesi supportati)", "setup-card.order.subtitle1": "Puoi usare più carte contemporaneamente", "setup-card.order.title": "Che tipo di carta?", "setup-card.order.virtualCard": "Carta virtuale", "setup-card.order.virtual_card.steps": "· Una carta digitale VISA Gnosis Pay {br}· Usala subito per pagamenti online {br}· Aggiungila a wallet Apple/Google (solo paesi supportati)", "setup-card.orderCard": "Ordina carta", "setup-card.virtual-card": "Ottieni carta virtuale", "setup.notifs.fakeAndroid.title": "Notifiche per pagamenti e trasferimenti in entrata", "setup.notifs.fakeIos.subtitle": "Zeal può avvisarti quando ricevi denaro o spendi con la tua carta Visa. Potrai modificare questa opzione in seguito.", "setup.notifs.fakeIos.title": "Notifiche per pagamenti e trasferimenti in entrata", "sign.PermitAllowanceItem.spendLimit": "Limite di spesa", "sign.ledger.subtitle": "Prosegui sul tuo portafoglio hardware.", "sign.ledger.title": "Firma con portafoglio hardware", "sign.passkey.subtitle": "Il browser dovrebbe chiederti di firmare con la passkey associata a questo portafoglio. Prosegui lì.", "sign.passkey.title": "Seleziona passkey", "signal_aborted_for_uknown_reason.title": "Richiesta di rete annullata", "simulatedTransaction.BridgeTrx.info.title": "Bridge", "simulatedTransaction.CardTopUp.info.title": "Aggiungi fondi alla carta", "simulatedTransaction.CardTopUpTrx.info.title": "Aggiungi fondi alla carta", "simulatedTransaction.NftCollectionApproval.approve": "Approva la collezione di NFT", "simulatedTransaction.OrderBuySignMessage.title": "Acquista", "simulatedTransaction.OrderCardTopupSignMessage.title": "Aggiungi alla carta", "simulatedTransaction.OrderEarnDepositBridge.title": "Deposita in Earn", "simulatedTransaction.P2PTransaction.info.title": "Invia", "simulatedTransaction.PermitSignMessage.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.SingleNftApproval.approve": "Approva NFT", "simulatedTransaction.UnknownSignMessage.title": "Firma", "simulatedTransaction.Withdrawal.info.title": "Prelievo", "simulatedTransaction.approval.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.approve.info.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.p2p.info.account": "A", "simulatedTransaction.p2p.info.unlabelledAccount": "Portafoglio senza etichetta", "simulatedTransaction.unknown.info.receive": "Ricezione", "simulatedTransaction.unknown.info.send": "Invio", "simulatedTransaction.unknown.using": "Tramite {app}", "simulation.approval.modal.text": "Accettando un'approvazione, autorizzi un'app o uno smart contract a usare i tuoi token o NFT in transazioni future.", "simulation.approval.modal.title": "Cosa sono le approvazioni?", "simulation.approval.spend-limit.label": "Limite di spesa", "simulation.approve.footer.for": "Per", "simulation.approve.unlimited": "Illimitato", "simulationNotAvailable.title": "Azione sconosciuta", "smart-wallet-activation-view.on": "Su", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password potrebbe bloccare l'accesso al tuo portafoglio", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Usa Apple o Google per configurare Zeal in sicurezza", "smart-wallet.passkey-notice.title": "Evita 1Password", "spend-limits.high.modal.text": "Imposta un limite di spesa vicino alla quantità di token che utilizzerai effettivamente con un'app o uno smart contract. Limiti elevati sono rischiosi e possono rendere più facile per i truffatori rubare i tuoi token.", "spend-limits.high.modal.text_sign_message": "Il limite di spesa dovrebbe essere vicino alla quantità di token che userai con un'app o uno smart contract. Limiti alti sono rischiosi e possono facilitare il furto dei tuoi token da parte dei truffatori.", "spend-limits.high.modal.title": "Limite di spesa elevato", "spend-limits.modal.text": "Il limite di spesa indica quanti token un'app può usare per tuo conto. Puoi modificare o rimuovere questo limite in qualsiasi momento. Per sicurezza, mantieni i limiti di spesa vicini alla quantità di token che userai effettivamente con un'app.", "spend-limits.modal.title": "Cos'è il limite di spesa?", "spent-limit-info.modal.description": "Il limite di spesa è la quantità di token che un'app può usare per tuo conto. Puoi modificare o rimuovere questo limite in qualsiasi momento. Per sicure<PERSON>, mantieni i limiti di spesa vicini alla quantità di token che userai effettivamente con un'app.", "spent-limit-info.modal.title": "Cos'è il limite di spesa?", "sswaps-io.transfer-provider": "Provider di trasferimento", "storage.accountDetails.activateWallet": "Attiva portafoglio", "storage.accountDetails.changeWalletLabel": "Modifica etichetta portafoglio", "storage.accountDetails.deleteWallet": "<PERSON><PERSON><PERSON><PERSON>af<PERSON>lio", "storage.accountDetails.setup_recovery_kit": "<PERSON> recupero", "storage.accountDetails.showPrivateKey": "Mostra chiave privata", "storage.accountDetails.showWalletAddress": "Mostra indirizzo portafoglio", "storage.accountDetails.smartBackup": "Backup e recupero", "storage.accountDetails.viewSsecretPhrase": "Visualizza frase segreta", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets?", "storage.manageAccounts.title": "Port<PERSON><PERSON><PERSON>", "submit-userop.progress.text": "Invio in corso", "submit.error.amount_high": "Importo troppo alto", "submit.error.amount_hight": "Importo troppo alto", "submit.error.amount_low": "Importo troppo basso", "submit.error.amount_required": "Inserisci l'importo", "submit.error.maximum_number_of_characters_exceeded": "Riduci i caratteri del messaggio", "submit.error.not_enough_balance": "<PERSON><PERSON>", "submit.error.recipient_required": "Beneficiario obbligatorio", "submit.error.routes_not_found": "<PERSON><PERSON><PERSON> percorso disponibile", "submitSafeTransaction.monitor.title": "Risultato della transazione", "submitSafeTransaction.sign.title": "Risultato della transazione", "submitSafeTransaction.state.sending": "Invio in corso", "submitSafeTransaction.state.sign": "Creazione in corso", "submitSafeTransaction.submittingToRelayer.title": "Risultato della transazione", "submitTransaction.cancel": "<PERSON><PERSON><PERSON>", "submitTransaction.cancel.attemptingToStop": "Tentativo di arresto in corso", "submitTransaction.cancel.failedToStop": "Arresto non riuscito", "submitTransaction.cancel.stopped": "Arrestata", "submitTransaction.cancel.title": "Anteprima transazione", "submitTransaction.failed.banner.description": "La rete ha annullato questa transazione. Riprova o contattaci.", "submitTransaction.failed.banner.title": "Transazione non riuscita", "submitTransaction.failed.execution_reverted.title": "L'app ha riscontrato un errore", "submitTransaction.failed.execution_reverted_without_message.title": "L'app ha riscontrato un errore", "submitTransaction.failed.out_of_gas.description": "Usate più commissioni del previsto.", "submitTransaction.failed.out_of_gas.title": "Errore di rete", "submitTransaction.sign.title": "Risultato della transazione", "submitTransaction.speedUp": "Accelera", "submitTransaction.state.addedToQueue": "Aggiunta alla coda", "submitTransaction.state.addedToQueue.short": "In coda", "submitTransaction.state.cancelled": "Arrestata", "submitTransaction.state.complete": "{currencyCode} aggiunto a Zeal", "submitTransaction.state.complete.subtitle": "Controlla il tuo portafoglio <PERSON>", "submitTransaction.state.completed": "Completata", "submitTransaction.state.failed": "Non riuscita", "submitTransaction.state.includedInBlock": "Inclusa nel blocco", "submitTransaction.state.includedInBlock.short": "Nel blocco", "submitTransaction.state.replaced": "Sostituita", "submitTransaction.state.sendingToNetwork": "Invio alla rete in corso", "submitTransaction.stop": "<PERSON><PERSON><PERSON>", "submitTransaction.submit": "Invia", "submitted-user-operation.state.bundled": "In coda", "submitted-user-operation.state.completed": "Completata", "submitted-user-operation.state.failed": "Non riuscita", "submitted-user-operation.state.pending": "Inoltro", "submitted-user-operation.state.rejected": "Rifiu<PERSON>", "submittedTransaction.failed.title": "Transazione non riuscita", "success_splash.card_activated": "Carta attivata", "supportFork.give-feedback.title": "<PERSON>cia un feedback", "supportFork.itercom.description": "<PERSON>, <PERSON><PERSON><PERSON>, premi: contatta Zeal.", "supportFork.itercom.title": "Domande sul portafoglio", "supportFork.title": "<PERSON><PERSON> assistenza per", "supportFork.zendesk.subtitle": "<PERSON> paga<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>: Gnosis Pay.", "supportFork.zendesk.title": "Pagamenti con carta e identità", "supported-networks.ethereum.warning": "Commissioni elevate", "supportedNetworks.networks": "Reti supportate", "supportedNetworks.oneAddressForAllNetworks": "Un unico indirizzo per tutte le reti", "supportedNetworks.receiveAnyAssets": "Ricevi asset da più reti su un indirizzo.", "swap.form.error.no_routes_found": "<PERSON><PERSON><PERSON> per<PERSON>so trovato", "swap.form.error.not_enough_balance": "<PERSON><PERSON>", "swaps-io-details.bank.serviceProvider": "Fornitore del servizio", "swaps-io-details.details.processing": "In elaborazione", "swaps-io-details.pending": "In attesa", "swaps-io-details.rate": "Tasso di cambio", "swaps-io-details.serviceProvider": "Fornitore del servizio", "swaps-io-details.transaction.from.processing": "Transazione avviata", "swaps-io-details.transaction.networkFees": "Commissioni di rete", "swaps-io-details.transaction.state.completed-transaction": "Transazione completata", "swaps-io-details.transaction.state.started-transaction": "Transazione avviata", "swaps-io-details.transaction.to.processing": "Transazione completata", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Il deposito dovrebbe completarsi a breve. Kinetex sta ancora elaborando la tua transazione.", "swapsIO.monitoring.awaitingLiqSend.title": "In ritardo", "swapsIO.monitoring.awaitingRecive.title": "Inoltro in corso", "swapsIO.monitoring.awaitingSend.title": "In coda", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "I token sono stati inviati a Kinetex, ma saranno restituiti a breve. Kinetex non ha potuto completare la transazione di destinazione.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Restituzione token in corso", "swapsIO.monitoring.cancelledNoSlash.subtitle": "I token non sono stati trasferiti a causa di un errore sconosciuto. Riprova.", "swapsIO.monitoring.cancelledNoSlash.title": "Token restituiti", "swapsIO.monitoring.cancelledSlashed.subtitle": "I token sono stati restituiti. Kinetex non ha potuto completare la transazione di destinazione.", "swapsIO.monitoring.cancelledSlashed.title": "Token restituiti", "swapsIO.monitoring.completed.title": "Completato", "taker-metadata.earn": "<PERSON><PERSON><PERSON><PERSON> in USD digitali con Sky", "taker-metadata.earn.aave": "<PERSON><PERSON><PERSON><PERSON> in EUR digitali con Aave", "taker-metadata.earn.aave.cashout24": "Incassa subito, 24/7", "taker-metadata.earn.aave.trusted": "Gestisce $27B da più di 2 anni", "taker-metadata.earn.aave.yield": "Il rendimento matura ogni secondo", "taker-metadata.earn.chf": "<PERSON><PERSON><PERSON><PERSON> in CHF digitali", "taker-metadata.earn.chf.cashout24": "Incassa subito, 24/7", "taker-metadata.earn.chf.trusted": "Con 28 mln di Fr. affidati", "taker-metadata.earn.chf.yield": "Il rendimento matura ogni secondo", "taker-metadata.earn.usd.cashout24": "Incassa subito, 24/7", "taker-metadata.earn.usd.trusted": "Gestisce $10.7B da più di 5 anni", "taker-metadata.earn.usd.yield": "Il rendimento matura ogni secondo", "test": "Deposita", "to.titile": "A", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "<PERSON><PERSON>", "token.groupHeader.titleWithSum": "Asset {sum}", "token.hidden_tokens.page.title": "Token nascosti", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "Aggiungi token", "token.widget.cashback_empty": "Ancora nessuna transazione", "token.widget.emptyState": "Nessun token nel port<PERSON>lio", "tokens.cash": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.approve.for": "Per", "top-up-card-from-earn-view.approve.into": "In", "top-up-card-from-earn-view.swap.from": "Da", "top-up-card-from-earn-view.swap.to": "A", "top-up-card-from-earn-view.withdraw.to": "A", "top-up-card-from-earn.trx.title.approval": "<PERSON><PERSON><PERSON><PERSON> scambio", "top-up-card-from-earn.trx.title.swap": "Aggiungi alla carta", "top-up-card-from-earn.trx.title.withdrawal": "Prelievo da Earn", "topUpDapp.connectWallet": "<PERSON><PERSON><PERSON>", "topup-fee-breakdown.bungee-fee": "Commissione fornitore esterno", "topup-fee-breakdown.header": "Commissione di transazione", "topup-fee-breakdown.network-fee": "Commissione di rete", "topup-fee-breakdown.total-fee": "Commissione totale", "topup.continue-in-wallet": "Continua nel tuo portafoglio", "topup.send.title": "Invia", "topup.submit-transaction.close": "<PERSON><PERSON>", "topup.submit-transaction.sent-to-wallet": "Invia {amount}", "topup.to": "A", "topup.transaction.complete.close": "<PERSON><PERSON>", "topup.transaction.complete.try-again": "<PERSON><PERSON><PERSON><PERSON>", "transaction-request.nonce-too-low.modal.button-text": "<PERSON><PERSON>", "transaction-request.nonce-too-low.modal.text": "Una transazione con lo stesso numero di serie (nonce) è già stata completata, quindi non puoi più inviare questa. Può succedere se effettui transazioni ravvicinate o se provi ad accelerare o annullare una transazione già completata.", "transaction-request.nonce-too-low.modal.title": "Transazione con stesso nonce completata", "transaction-request.replaced.modal.button-text": "<PERSON><PERSON>", "transaction-request.replaced.modal.text": "Non riusciamo a tracciare lo stato di questa transazione. Potrebbe essere stata sostituita da un'altra o il nodo RPC potrebbe avere problemi.", "transaction-request.replaced.modal.title": "Impossibile trovare stato transazione", "transaction.activity.details.modal.close": "<PERSON><PERSON>", "transaction.cancel_popup.cancel": "No, attendi", "transaction.cancel_popup.confirm": "Sì, arresta", "transaction.cancel_popup.description": "<PERSON>, devi pagare una nuova commissione di rete al posto di quella originale di {oldFee}", "transaction.cancel_popup.description_without_original": "Per fermarla, paga una nuova commissione.", "transaction.cancel_popup.not_supported.subtitle": "Arresto transazioni non supportato su {network}", "transaction.cancel_popup.not_supported.title": "Non supportato", "transaction.cancel_popup.stopping_fee": "Commissione di rete per l'arresto", "transaction.cancel_popup.title": "Arrestare la transazione?", "transaction.in-progress": "In corso", "transaction.inProgress": "In corso", "transaction.speed_up_popup.cancel": "No, attendi", "transaction.speed_up_popup.confirm": "Sì, accelera", "transaction.speed_up_popup.description": "Per velocizzare, devi pagare una nuova commissione di rete invece di quella originale di {amount}", "transaction.speed_up_popup.description_without_original": "Per velocizzare, devi pagare una nuova commissione di rete", "transaction.speed_up_popup.seed_up_fee_title": "Commissione per velocizzare", "transaction.speed_up_popup.title": "Velocizzare la transazione?", "transaction.speedup_popup.not_supported.subtitle": "Non puoi velocizzare le transazioni su {network}", "transaction.speedup_popup.not_supported.title": "Non supportato", "transaction.subTitle.failed": "<PERSON><PERSON>", "transactionDetails.cashback.not-qualified": "Non idoneo", "transactionDetails.cashback.paid": "{amount} pagato", "transactionDetails.cashback.pending": "{amount} in attesa", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionDetails.cashback_estimate": "Stima del cashback", "transactionDetails.category": "Categoria", "transactionDetails.exchangeRate": "Tasso di cambio", "transactionDetails.location": "<PERSON><PERSON>", "transactionDetails.payment-approved": "Pagamento approvato", "transactionDetails.payment-declined": "Pagamento rifiutato", "transactionDetails.payment-reversed": "Pagamento stornato", "transactionDetails.recharge.amountSentFromEarn.title": "Importo inviato da Earn", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "Ricaricato sulla carta", "transactionDetails.recharge.rate.title": "Tasso", "transactionDetails.recharge.transactionId.title": "ID transazione", "transactionDetails.refund": "<PERSON><PERSON><PERSON><PERSON>", "transactionDetails.reversal": "Storno", "transactionDetails.transactionCurrency": "Valuta della transazione", "transactionDetails.transactionId": "ID transazione", "transactionDetails.type": "Transazione", "transactionRequestWidget.approve.subtitle": "Per {target}", "transactionRequestWidget.p2p.subtitle": "A {target}", "transactionRequestWidget.unknown.subtitle": "Tramite {target}", "transactionSafetyChecksPopup.title": "Controlli di sicurezza transazione", "transactions.main.activity.title": "Attività", "transactions.page.hiddenActivity.title": "Attività nascosta", "transactions.page.title": "Attività", "transactions.viewTRXHistory.emptyState": "Ancora nessuna transazione", "transactions.viewTRXHistory.errorMessage": "Non siamo riusciti a caricare la cronologia delle tue transazioni", "transactions.viewTRXHistory.hidden.emptyState": "Nessuna transazione nascosta", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Attività non supportata per le testnet", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Attività non supportata per le testnet{br}<link>Vai su explorer</link>", "transfer_provider": "Ser<PERSON><PERSON> di trasferimento", "transfer_setup_with_different_wallet.subtitle": "Bonifici attivi su un altro portafoglio.", "transfer_setup_with_different_wallet.swtich_and_continue": "Cambia e continua", "transfer_setup_with_different_wallet.title": "Cambia portafoglio", "tx-sent-to-wallet.button": "<PERSON><PERSON>", "tx-sent-to-wallet.subtitle": "Continua su {wallet}", "unblockProviderInfo.fees": "Commissioni: 0% fino a $5k/mese, poi 0,2%.", "unblockProviderInfo.registration": "Unblock è autorizzata da FNTT e US Fincen. <link>Scopri di più</link>", "unblockProviderInfo.selfCustody": "I fondi digitali ricevuti sono solo tuoi.", "unblock_invalid_faster_payment_configuration.subtitle": "Il conto bancario fornito non supporta i trasferimenti SEPA europei o i Faster Payments del Regno Unito. Fornisci un altro conto.", "unblock_invalid_faster_payment_configuration.title": "È richiesto un altro conto", "unknownTransaction.primaryText": "Transazione con carta", "unsupportedCountry.subtitle": "I trasferimenti bancari non sono ancora disponibili nel tuo paese.", "unsupportedCountry.title": "Non disponibile in {country}", "update-app-popup.subtitle": "L'ultimo aggiornamento è pieno di correzioni, funzionalità e altre magie. Aggiorna all'ultima versione e migliora la tua esperienza con Zeal.", "update-app-popup.title": "Aggiorna la versione di Zeal", "update-app-popup.update-now": "Aggiorna ora", "user_associated_with_other_merchant.subtitle": "Questo portafoglio non può essere usato per i trasferimenti. Usa un altro portafoglio o scrivici su Discord per supporto.", "user_associated_with_other_merchant.title": "Portafoglio non utilizzabile", "user_associated_with_other_merchant.try_with_another_wallet": "Usa un altro wallet", "user_email_already_exists.subtitle": "Bonifici attivi su un altro portafoglio.", "user_email_already_exists.title": "Bonifici su un altro portafoglio", "user_email_already_exists.try_with_another_wallet": "Usa un altro portafoglio", "validation.invalid.iban": "IBAN non valido", "validation.required": "Obbligatorio", "validation.required.first_name": "Nome obbligatorio", "validation.required.iban": "IBAN obbligatorio", "validation.required.last_name": "Cognome obbligatorio", "verify-passkey.cta": "Verifica passkey", "verify-passkey.subtitle": "Verifica che la tua passkey sia stata creata e protetta correttamente.", "verify-passkey.title": "Verifica passkey", "view-cashback.cashback-next-cycle": "Tasso di cashback tra {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "Deposita per ottenere il cashback", "view-cashback.pending": "{money} In attesa", "view-cashback.pending-rewards.not_paid": "In arrivo tra {days}g", "view-cashback.pending-rewards.paid": "Ricevuto questa settimana", "view-cashback.received-rewards": "<PERSON><PERSON>", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Pagamenti non confermati", "view-cashback.upcoming": "In arrivo {money}", "virtual-card-order.configure-safe.loading-text": "Creazione carta", "virtual-card-order.create-order.loading-text": "Attivazione carta", "virtual-card-order.create-order.success-text": "Carta attivata", "virtualCard.activateCard": "Attiva la carta", "walletDeleteConfirm.main_action": "<PERSON><PERSON><PERSON><PERSON>", "walletDeleteConfirm.subtitle": "Dovrai importarlo di nuovo per vedere il portafoglio o fare transazioni", "walletDeleteConfirm.title": "Rimuovere il portafoglio?", "walletSetting.header": "Impostazioni portafoglio", "wallet_connect.connect.cancel": "<PERSON><PERSON><PERSON>", "wallet_connect.connect.connect_button": "<PERSON><PERSON><PERSON>", "wallet_connect.connect.title": "<PERSON><PERSON><PERSON>", "wallet_connect.connected.title": "<PERSON><PERSON><PERSON>", "wallet_connect_add_chain_missing.title": "Rete non supportata", "wallet_connect_proposal_expired.title": "Connessione scaduta", "withdraw": "<PERSON><PERSON><PERSON>", "withdraw.confirmation.close": "<PERSON><PERSON><PERSON>", "withdraw.confirmation.continue": "Conferma", "withdrawal_request.completed": "Completato", "withdrawal_request.pending": "In attesa", "zeal-dapp.connect-wallet.cta.primary.connecting": "Connessione...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "<PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.cta.secondary": "<PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.title": "<PERSON><PERSON>i il portafoglio per continuare", "zealSmartWalletInfo.gas": "Paga il gas con molti token; usa i popolari token ERC20 sulle chain supportate per le commissioni di rete, non solo i token nativi.", "zealSmartWalletInfo.recover": "Nessuna frase segreta; recupera l'accesso con la passkey biometrica dal tuo gestore di password, iCloud o account Google.", "zealSmartWalletInfo.selfCustodial": "Completamente privato; le firme con passkey sono validate on-chain per ridurre al minimo le dipendenze centrali.", "zealSmartWalletInfo.title": "Informazioni sugli Smart Wallet Zeal", "zeal_a_rewards_already_claimed_error.title": "Ricompensa già riscattata", "zwidget.minimizedDisconnected.label": "Zeal disconnesso"}