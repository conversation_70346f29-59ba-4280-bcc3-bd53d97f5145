{"Account.ListItem.details.label": "<PERSON><PERSON><PERSON>", "AddFromAddress.success": "<PERSON><PERSON><PERSON>", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{M'hemmx kartieri} one{{count} Kartiera} other{{count} Kartieri}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Fra<PERSON>i Sigrieta {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Oħloq kartieri ġodda minn waħda mill-Frażijiet Sigrieti eżistenti tiegħek", "AddFromExistingSecretPhrase.SelectPhrase.title": "Agħżel Frażi Sigrieta", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "Il-Frażi Sigrieta tiegħek tista' tagħmel backup ta' ħafna kartieri. Agħżel dawk li trid tuża.", "AddFromExistingSecretPhrase.WalletSelection.title": "Żid kartiera malajr", "AddFromExistingSecretPhrase.success": "Ka<PERSON>ieri miżjuda f'Zeal", "AddFromHardwareWallet.subtitle": "Agħżel il-hardware wallet tiegħek biex tikkonnettja ma' Zeal", "AddFromHardwareWallet.title": "Hardware Wallet", "AddFromNewSecretPhrase.WalletSelection.subtitle": "Agħ<PERSON>el il-kartieri li trid timporta", "AddFromNewSecretPhrase.WalletSelection.title": "<PERSON><PERSON><PERSON> kartieri", "AddFromNewSecretPhrase.accounts": "<PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "Frażi Sigrieta taġixxi bħal keychain għal miljuni ta' kartieri, kull waħda b'ċavetta privata unika.{br}{br}Tista' timporta kemm trid kartieri issa jew iżżid aktar aktar tard.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Kartieri bi Frażi Sigrieta", "AddFromNewSecretPhrase.subtitle": "Daħħal il-Frażi Sigrieta tiegħek mifruqa bi spazji", "AddFromNewSecretPhrase.success_secret_phrase_added": "Frażi Sigrieta miżjuda 🎉", "AddFromNewSecretPhrase.success_wallets_added": "<PERSON><PERSON><PERSON> ma<PERSON>", "AddFromNewSecretPhrase.wallets": "<PERSON><PERSON><PERSON>", "AddFromPrivateKey.subtitle": "Daħħal iċ-Ċavetta Privata tiegħek", "AddFromPrivateKey.success": "Ċavetta privata miżjuda 🎉", "AddFromPrivateKey.title": "Irrestawra l-kartiera", "AddFromPrivateKey.typeOrPaste": "It<PERSON>j<PERSON><PERSON> jew wa<PERSON><PERSON><PERSON> hawn", "AddFromSecretPhrase.importWallets": "{count,plural,=0{M'hemmx kartieri magħżula} one{Importa kartiera} other{Importa {count} kartieri}}", "AddFromTrezor.AccountSelection.title": "Importa kartieri ta' Trezor", "AddFromTrezor.hwWalletTip.subtitle": "Hardware wallet iżżomm miljuni ta' kartieri b'indirizzi differenti. Tista' timporta kemm trid kartieri issa jew iżżid aktar aktar tard.", "AddFromTrezor.hwWalletTip.title": "Importazzjoni minn Hardware Wallets", "AddFromTrezor.importAccounts": "{count,plural,=0{L-ebda kartiera magħżula} one{Importa kartiera} other{Importa {count} kartieri}}", "AddFromTrezor.success": "<PERSON><PERSON><PERSON> ma<PERSON>", "ApprovalSpenderTypeCheck.failed.subtitle": "X'aktarx scam: min jonfoq għandu jkun kuntratt", "ApprovalSpenderTypeCheck.failed.title": "<PERSON> jonfoq hu wallet, mhux kuntratt", "ApprovalSpenderTypeCheck.passed.subtitle": "Normalment, l-approvazzjoni tingħata lill-kuntratti", "ApprovalSpenderTypeCheck.passed.title": "Min jonfoq hu smart contract", "BestReturns.subtitle": "Dan il-provider tal-iswap jagħtik l-ogħla redditu, inkluż it-tariffi kollha.", "BestReturnsPopup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> redditu", "BlacklistCheck.Failed.subtitle": "Rapporti malizzjużi minn <source></source>", "BlacklistCheck.Failed.title": "Is-sit jinsab fuq blacklist", "BlacklistCheck.Passed.subtitle": "Ebda rapport malizzjuż minn <source></source>", "BlacklistCheck.Passed.title": "Is-sit mhuwiex fuq blacklist", "BlacklistCheck.failed.statusButton.label": "<PERSON>-<PERSON> <PERSON><PERSON>", "BridgeRoute.slippage": "Slippage {slippage}", "BridgeRoute.title": "Provider tal-<PERSON>", "CheckConfirmation.InProgress": "Għaddejja...", "CheckConfirmation.success.splash": "<PERSON><PERSON><PERSON><PERSON>", "ChooseImportOrCreateSecretPhrase.subtitle": "Importa Frażi Sigrieta jew o<PERSON><PERSON><PERSON> waħda ġdida", "ChooseImportOrCreateSecretPhrase.title": "<PERSON><PERSON> Sigrie<PERSON>", "ConfirmTransaction.Simuation.Skeleton.title": "Qed nagħmlu l-kontrolli tas-sigurtà…", "ConnectionSafetyCheckResult.passed": "Verifika tas-sigurtà għaddiet", "ContactGnosisPaysupport": "Ikkuntattja Gnosis Pay", "CopyKeyButton.copied": "Ikkupjat", "CopyKeyButton.copyYourKey": "Ikkopja ċ-ċavetta tiegħek", "CopyKeyButton.copyYourPhrase": "Ikkopja l-frażi tiegħek", "DAppVerificationCheck.Failed.subtitle": "Is-sit mhuwiex elenkat fuq <source></source>", "DAppVerificationCheck.Failed.title": "Is-sit ma nstabx fir-re<PERSON><PERSON><PERSON> tal-apps", "DAppVerificationCheck.Passed.subtitle": "Is-sit hu elenkat fuq <source></source>", "DAppVerificationCheck.Passed.title": "Is-sit jidher fir-re<PERSON><PERSON><PERSON> tal-apps", "DAppVerificationCheck.failed.statusButton.label": "Is-sit ma nstabx fir-re<PERSON><PERSON><PERSON> tal-apps", "ERC20.tokens.emptyState": "Ma sibna l-ebda tokens", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "<PERSON>riffa <PERSON>rijoritar<PERSON>", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "L-aħħar {period}: bejn {from} u {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "<PERSON><PERSON><PERSON>: {baseFee} • Buffer ta' sigurtà: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Ma rnexxilniex niksbu t-Tariffa Bażi attwali", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "<PERSON><PERSON> og<PERSON>la min-li jmiss. Se teħel.", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "In-<PERSON><PERSON> ma jistax ikun inqas mill-attwali.", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "Ma stajniex nikka<PERSON>w it-<PERSON>riffa Prijoritarja", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Ma rnexxilniex niksbu t-Tariffa Massima attwali", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "<PERSON><PERSON><PERSON>", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Ji<PERSON>' je<PERSON><PERSON> sakemm jonqsu t-tariffi tan-netwerk", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "Tariffa Bażi Mass<PERSON>", "EditFeeModal.Custom.gasLimit.title": "Limitu tal-Gas {gasLimit}", "EditFeeModal.Custom.title": "<PERSON><PERSON><PERSON> a<PERSON>", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Se jeħel sakemm tonqos it-<PERSON><PERSON><PERSON>", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Tariffa baxxa. T<PERSON>' teħel.", "EditFeeModal.EditGasLimit.estimatedGas": "Gas stmat: {estimated} • Buffer ta' sigurtà: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Inqas mil-limitu stmat. It-tranżazzjoni se tfalli", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Inqas mil-limitu s<PERSON>ġġerit. It-tranżazzjoni tista' tfalli", "EditFeeModal.EditGasLimit.subtitle": "Issettja l-ammont massimu ta' gas li tixtieq tuża għal din it-tranżazzjoni. It-tranżazzjoni tiegħek se tfalli jekk tissettja limitu aktar baxx milli hemm bżonn", "EditFeeModal.EditGasLimit.title": "Editja l-limitu tal-gas", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Inqas mil-limitu minimu tat-tariffa: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "In-nonce ogħla minn dak li jmiss. Se jeħel", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "Ma tistax tissettja nonce iktar baxx min-nonce attwali", "EditFeeModal.EditNonce.subtitle": "It-tranżazzjoni tiegħek se teħel jekk tissettja nonce differenti minn dak li jmiss", "EditFeeModal.EditNonce.title": "<PERSON><PERSON> n-nonce", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>n {amount} biex tissottometti", "EditFeeModal.Header.Time.unknown": "<PERSON><PERSON>", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Mass. {fee}", "EditFeeModal.Header.fee.unknown": "Tariffa mhux magħrufa", "EditFeeModal.Header.subsequent_failed": "L-<PERSON><PERSON><PERSON> jistg<PERSON><PERSON>, l-a<PERSON><PERSON><PERSON> refresh falla", "EditFeeModal.Layout.Header.ariaLabel": "<PERSON><PERSON><PERSON> attwali", "EditFeeModal.MaxFee.subtitle": "Il-Max fee hija l-massimu li tħallas għal tranżazzjoni, imma s-soltu tħallas it-tariffa mbassra. Dan il-buffer żejjed jgħin biex it-tranżazzjoni tiegħek tgħaddi, anke jekk in-netwerk inaqqas jew isir aktar għali.", "EditFeeModal.MaxFee.title": "<PERSON><PERSON><PERSON> tan-Netwerk", "EditFeeModal.SelectPreset.Time.unknown": "<PERSON><PERSON>", "EditFeeModal.SelectPreset.ariaLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> preset tat-tariffa", "EditFeeModal.SelectPreset.fast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.SelectPreset.normal": "Normali", "EditFeeModal.SelectPreset.slow": "Bil-mod", "EditFeeModal.ariaLabel": "Editja t-tariffa tan-netwerk", "FailedSimulation.Confirmation.Item.subtitle": "Kellna żball intern", "FailedSimulation.Confirmation.Item.title": "Ma stajniex nissimulaw it-tranżazzjoni", "FailedSimulation.Confirmation.subtitle": "<PERSON><PERSON>r li trid tkompli?", "FailedSimulation.Confirmation.title": "Qed tiffirma bl-addoċċ", "FailedSimulation.Title": "Żball fis-simulazzjoni", "FailedSimulation.footer.subtitle": "Kellna żball intern", "FailedSimulation.footer.title": "Ma stajniex nissimulaw it-tranżazzjoni", "FeeForecastWidget.NotEnoughBalance.errorMessage": "<PERSON><PERSON><PERSON><PERSON> bżonn {amount} biex tissottometti t-tranżazzjoni", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Jista' jdum biex jiġi pproċessat", "FeeForecastWidget.networkFee": "Tariffa tan-netwerk", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Ma stajniex nikkalkulaw it-tariffa tan-netwerk", "FeeForecastWidget.subsequentFailed.message": "L-is<PERSON><PERSON> jist<PERSON><PERSON><PERSON>, l-<PERSON>ġġorn<PERSON> falla", "FeeForecastWidget.unknownDuration": "<PERSON><PERSON><PERSON> ma<PERSON>", "FeeForecastWidget.unknownFee": "<PERSON><PERSON><PERSON> ma<PERSON>", "GasCurrencySelector.balance": "Bilanċ: {balance}", "GasCurrencySelector.networkFee": "Tariffa tan-netwerk", "GasCurrencySelector.payNetworkFeesUsing": "Ħallas it-tariffi tan-netwerk bi", "GasCurrencySelector.removeDefaultGasToken.description": "Ħallas it-tariffi mill-akbar bilanċ", "GasCurrencySelector.removeDefaultGasToken.title": "Immaniġġjar awtomatiku tat-tariffi", "GasCurrencySelector.save": "Issejvja", "GoogleDriveBackup.BeforeYouBegin.first_point": "Jekk ninsa l-password ta' <PERSON>eal, nitle<PERSON> l-assi tiegħi għal dejjem", "GoogleDriveBackup.BeforeYouBegin.second_point": "Jekk nitlef l-aċċess għall-Google Drive jew nimmodifika r-Recovery File, nitlef l-assi tiegħi għal dejjem", "GoogleDriveBackup.BeforeYouBegin.subtitle": "Jekk jogħġbok ifhem u aċċetta l-punti li ġejjin dwar il-kustodja personali:", "GoogleDriveBackup.BeforeYouBegin.third_point": "Zeal ma jistax jgħinni nirkupra l-password ta' Zeal jew l-aċċess tiegħi għall-Google Drive", "GoogleDriveBackup.BeforeYouBegin.title": "<PERSON><PERSON><PERSON> tibda", "GoogleDriveBackup.loader.subtitle": "Jekk jogħġbok approva t-talba fuq Google Drive biex ittella' r-Recovery File tiegħek", "GoogleDriveBackup.loader.title": "Nistennew l-approvazzjoni...", "GoogleDriveBackup.success": "Backup sar b'suċċess 🎉", "MonitorOffRamp.overServiceTime": "Il-biċċa l-kbira tat-trasferimenti jitlestew fi żmien {estimated_time}, imma kultant jistgħu jdumu aktar minħabba verifiki addizzjonali. Dan hu normali u l-fondi huma siguri waqt li jkunu qed isiru dawn il-verifiki.{br}{br}Jekk it-tranżazzjoni ma titlestiex fi żmien {support_soft_deadline}, jekk jog<PERSON> {contact_support}", "MonitorOnRamp.contactSupport": "Ikkuntattja lis-support", "MonitorOnRamp.from": "<PERSON>n", "MonitorOnRamp.fundsReceived": "Fondi rċevuti", "MonitorOnRamp.overServiceTime": "Ħafna mit-trasferimenti jitlestew fi żmien {estimated_time}, imma kultant jistgħu jdumu aktar minħabba verifiki addizzjonali. Dan hu normali u l-fondi huma siguri.{br}{br}Jekk it-tranżazzjoni ma titlestiex fi żmien {support_soft_deadline}, jekk jog<PERSON> {contact_support}", "MonitorOnRamp.sendingToYourWallet": "<PERSON><PERSON> jintbagħat lill-kartiera tiegħek", "MonitorOnRamp.to": "<PERSON>", "MonitorOnRamp.waitingForTransfer": "Nistennewk tittrasferixxi l-fondi", "NftCollectionCheck.failed.subtitle": "Il-kollezzjoni mhix verifikata fuq <source></source>", "NftCollectionCheck.failed.title": "Il-kollezzjoni mhix verifikata", "NftCollectionCheck.passed.subtitle": "Il-kollezzjoni hija vverifikata fuq <source></source>", "NftCollectionCheck.passed.title": "Il-kollezzjoni hija vverifikata", "NftCollectionInfo.entireCollection": "Il-kollezzjoni kollha", "NoSigningKeyStore.createAccount": "<PERSON><PERSON><PERSON><PERSON>", "NonceRangeError.biggerThanCurrent.message": "It-tranżazzjoni se teħel", "NonceRangeError.lessThanCurrent.message": "It-tranżazzjoni se tfalli", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "In-nonce huwa ogħla min-nonce attwali. Naqqas in-nonce biex it-tranżazzjoni ma teħilx.", "NonceRangeErrorPopup.biggerThanCurrent.title": "It-tranżazzjoni se teħel", "P2pReceiverTypeCheck.failed.subtitle": "Qed tibgħat lill-indirizz it-tajjeb?", "P2pReceiverTypeCheck.failed.title": "Id-destinatarju huwa smart contract, mhux wallet", "P2pReceiverTypeCheck.passed.subtitle": "Normalment, l-assi jintbagħtu lil wallets oħra", "P2pReceiverTypeCheck.passed.title": "Id-des<PERSON><PERSON><PERSON> huwa <PERSON>", "PasswordCheck.title": "<PERSON><PERSON><PERSON><PERSON> il-password", "PasswordChecker.subtitle": "Daħħal il-password biex nivverifikaw.", "PermitExpirationCheck.failed.subtitle": "Żomm il-<PERSON>in qasir u għal kemm għandek bżonn biss", "PermitExpirationCheck.failed.title": "Żmien twil ta' skadenza", "PermitExpirationCheck.passed.subtitle": "Kemm tista' tuża t-tokens tiegħek app", "PermitExpirationCheck.passed.title": "<PERSON>ż-ż<PERSON>n tal-iskadenza mhuwiex twil wisq", "PrivateKeyValidationError.moreThanMaximumWords": "<PERSON>. {count} kel<PERSON>t", "PrivateKeyValidationError.notValidPrivateKey": "Din mhijiex ċavetta privata valida", "PrivateKeyValidationError.secretPhraseIsInvalid": "Il-frażi sigrieta mhijiex valida", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Ke<PERSON> #{index} mikt<PERSON> ħ<PERSON>ż<PERSON> jew <PERSON>a", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} kelma} other{{count} kelmiet}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Il-Frażijiet Sigrieti u ċ-ċwievet privati huma kkodifikati u qatt ma jħallu dan l-apparat", "SecretPhraseReveal.header": "<PERSON><PERSON><PERSON> is-Secret Phrase", "SecretPhraseReveal.hint": "Taqsamx il-frażi tiegħek ma' ħadd. Żommha f'post sigur u offline", "SecretPhraseReveal.skip.subtitle": "T<PERSON>' tagħ<PERSON> dan aktar tard, imma jekk titlef dan l-apparat qabel tikteb il-frażi, titlef l-assi kollha li daħħalt f'din il-kartiera", "SecretPhraseReveal.skip.takeTheRisk": "Nirriskja", "SecretPhraseReveal.skip.title": "<PERSON>q<PERSON>ż il-kitba tal-frażi?", "SecretPhraseReveal.skip.writeDown": "<PERSON><PERSON><PERSON>", "SecretPhraseReveal.skipForNow": "<PERSON><PERSON><PERSON> tard", "SecretPhraseReveal.subheader": "Jekk jogħġbok iktibha u żommha f'post sigur u offline. Imbagħad nitolbuk tivverifikaha.", "SecretPhraseReveal.verify": "Ivverifika", "SelectCurrency.tokens": "Tokens", "SelectCurrency.tokens.emptyState": "Ma sibna l-ebda tokens", "SelectRoute.slippage": "Slippage {slippage}", "SelectRoutes.emptyState": "Ma sibna l-ebda rotta għal dan l-iswap", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Qabbad il-kartiera", "SendERC20.labelAddress.inputPlaceholder": "Isem tal-kartiera", "SendERC20.labelAddress.subtitle": "Semmi l-kartiera biex issibha aktar tard.", "SendERC20.labelAddress.title": "<PERSON><PERSON><PERSON> din il-kartiera", "SendERC20.send_to": "Ibgħat lil", "SendERC20.tokens": "Tokens", "SendOrReceive.bankTransfer.primaryText": "Trasferiment Bankarju", "SendOrReceive.bankTransfer.shortText": "On-ramp u off-ramp b'xejn u immedjat", "SendOrReceive.bridge.primaryText": "<PERSON>", "SendOrReceive.bridge.shortText": "Ittrasferixxi tokens bejn in-networks", "SendOrReceive.receive.primaryText": "Irċievi", "SendOrReceive.receive.shortText": "Irċievi tokens jew collectibles", "SendOrReceive.send.primaryText": "Ibgħat", "SendOrReceive.send.shortText": "Ibgħat tokens lil kwalunkwe indirizz", "SendOrReceive.swap.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.swap.shortText": "Agħmel swap bejn it-tokens", "SendSafeTransaction.Confirm.loading": "Qed nagħmlu l-verifiki tas-sigurtà…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Ikkripta Recovery File b'password", "SetupRecoveryKit.google.subtitle": "Sinkronizzat {date}", "SetupRecoveryKit.google.title": "Backup fuq Google Drive", "SetupRecoveryKit.subtitle": "<PERSON><PERSON> tal-inqas mod wieħed kif tirrestawra l-kont tiegħek jekk tneħħi Zeal jew tibdel l-apparat", "SetupRecoveryKit.title": "Stabbilixxi Recovery Kit", "SetupRecoveryKit.writeDown.subtitle": "<PERSON><PERSON><PERSON> is-Secret Phrase", "SetupRecoveryKit.writeDown.title": "<PERSON>up manwali", "Sign.CheckSafeDeployment.activate": "<PERSON><PERSON><PERSON>", "Sign.CheckSafeDeployment.subtitle": "Qabel ma tkun tista' tidħol f'app jew tiffirma messaġġ off-chain, trid tattiva t-tagħmir tiegħek fuq dan in-netwerk. <PERSON> iseħħ wara li tkun installajt jew irkuprajt Smart Wallet.", "Sign.CheckSafeDeployment.title": "Attiva t-tagħmir fuq dan in-netwerk", "Sign.Simuation.Skeleton.title": "Qed nagħmlu l-verifiki tas-sigurtà…", "SignMessageSafetyCheckResult.passed": "Il-Verifiki tas-Sigurtà Għaddew", "SignMessageSafetyChecksPopup.title.permits": "Verifiki tas-sigurtà tal-permessi", "SimulationFailedConfirmation.subtitle": "Aħna ssimulajna din it-tranżazzjoni u sibna problema li tikkawżalha tfalli. Tista' tissottometti din it-tranżazzjoni, iżda x'aktarx se tfalli u tista' titlef it-tariffa tan-netwerk tiegħek.", "SimulationFailedConfirmation.title": "It-tranżazzjoni x'aktarx tfalli", "SimulationNotSupported.Title": "Simulazzjoni mhix{br}appoġġjata fuq{br}{network}", "SimulationNotSupported.footer.subtitle": "Xorta tista' tissottometti din it-tranżazzjoni", "SimulationNotSupported.footer.title": "Is-simulazzjoni mhix appoġġjata", "SlippagePopup.custom": "Personalizzat", "SlippagePopup.presetsHeader": "Slippage tal-iswap", "SlippagePopup.title": "Settings tas-slippage", "SmartContractBlacklistCheck.failed.subtitle": "Rapporti malizzjużi minn <source></source>", "SmartContractBlacklistCheck.failed.title": "<PERSON>-k<PERSON>ratt jinsab fil-lista s-sewda", "SmartContractBlacklistCheck.passed.subtitle": "Ebda rapport malizzjuż minn <source></source>", "SmartContractBlacklistCheck.passed.title": "Il-k<PERSON><PERSON>t mhuwiex fil-lista s-sewda", "SuspiciousCharactersCheck.Failed.subtitle": "Din hi tattika komuni tal-phishing", "SuspiciousCharactersCheck.Failed.title": "Niċċekkjaw għal mudelli komuni tal-phishing", "SuspiciousCharactersCheck.Passed.subtitle": "Niċċekkjaw għal attentati ta' phishing", "SuspiciousCharactersCheck.Passed.title": "L-indirizz m'għandux karattri mhux tas-soltu", "SuspiciousCharactersCheck.failed.statusButton.label": "L-indirizz fih karattri strambi ", "TokenVerificationCheck.failed.subtitle": "It-token mhux elenkat fuq <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} mhux verifikat minn CoinGecko", "TokenVerificationCheck.passed.subtitle": "It-token huwa elenkat fuq <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} huwa vverifikat minn CoinGecko", "TopupDapp.MonitorTransaction.success.splash": "Les<PERSON>", "TransactionSafetyCheckResult.passed": "Il-Verifiki tas-Sigurtà Għaddew", "TransactionSimulationCheck.failed.subtitle": "Żball: {errorMessage}", "TransactionSimulationCheck.failed.title": "It-tranżazzjoni x'aktarx li tfalli", "TransactionSimulationCheck.passed.subtitle": "Simulazzjoni magħmula permezz ta' <source></source>", "TransactionSimulationCheck.passed.title": "Il-preview tat-tranżazzjoni rnexxiet", "TrezorError.trezor_action_cancelled.action": "Agħlaq", "TrezorError.trezor_action_cancelled.subtitle": "Inti rrifjutajt it-tranżazzjoni fuq il-hardware wallet tiegħek", "TrezorError.trezor_device_used_elsewhere.action": "Issinkronizza t-Trezor", "TrezorError.trezor_device_used_elsewhere.subtitle": "<PERSON>n <PERSON><PERSON><PERSON> li tagħlaq is-sessjonijiet miftuħa l-oħra kollha u erġa' pprova ssinkronizza t-T<PERSON><PERSON> tie<PERSON>ek", "TrezorError.trezor_method_cancelled.action": "Issinkronizza t-Trezor", "TrezorError.trezor_method_cancelled.subtitle": "<PERSON>n <PERSON><PERSON> li tippermetti lil Trezor jesporta l-kartieri lejn <PERSON>", "TrezorError.trezor_permissions_not_granted.action": "Issinkronizza t-Trezor", "TrezorError.trezor_permissions_not_granted.subtitle": "Jekk jogħġbok agħti permess lil Zeal biex jara l-kartieri kollha", "TrezorError.trezor_pin_cancelled.action": "Issinkronizza t-Trezor", "TrezorError.trezor_pin_cancelled.subtitle": "Sessjoni kkanċellata fuq it-tagħmir", "TrezorError.trezor_popup_closed.action": "Issinkronizza t-Trezor", "TrezorError.trezor_popup_closed.subtitle": "Id-<PERSON><PERSON><PERSON><PERSON> tat-<PERSON><PERSON><PERSON> għalaq għall-għarrieda", "TrxLikelyToFail.lessThanEstimatedGas.message": "It-tranżazzjoni se tfalli", "TrxLikelyToFail.lessThanMinimumGas.message": "It-tranżazzjoni se tfalli", "TrxLikelyToFail.lessThanSuggestedGas.message": "X'aktarx li tfalli", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "Il-Limitu tal-Gas tat-tranżazzjoni huwa baxx wisq. Għolli l-Limitu tal-Gas sal-limitu ssuġġerit biex tevita li tfalli.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "It-tranżazzjoni x'aktarx tfalli", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Il-Limitu tal-Gas huwa aktar baxx mill-gas stmat. Għolli l-Limitu tal-Gas sal-limitu ssuġġerit.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "It-tranżazzjoni se tfalli", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "Il-Max Base Fee hija aktar baxxa mill-base fee attwali. Għolli l-Max Base Fee biex tevita li t-tranżazzjoni teħel.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "It-tranżazzjoni se teħel", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "Il-Max <PERSON>e tat-tranżazzjoni hija baxxa wisq. Għolli l-Max Fee biex tevita li t-tranżazzjoni teħel.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "It-tranżazzjoni se teħel", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Il-Priority Fee hija aktar baxxa minn dik rakkomandata. Għolli l-Priority Fee biex tħaffef it-tranżazzjoni.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "It-tranżazzjoni tista' ddum biex titlesta", "UnsupportedMobileNetworkLayout.gotIt": "Fhimt!", "UnsupportedMobileNetworkLayout.subtitle": "Ma tistax tagħmel tranżazzjonijiet jew tiffirma messaġġi fuq in-netwerk bl-id {networkHexId} bil-verżjoni mobbli ta' Zeal għalissa{br}{br}Aqleb għall-estensjoni tal-browser biex tkun tista' tagħmel tranżazzjonijiet fuq dan in-netwerk, waqt li aħna qed naħdmu qatigħ biex inżidu l-appoġġ għalih 🚀", "UnsupportedMobileNetworkLayout.title": "Netwerk mhux appoġġjat fuq il-mobile.", "UnsupportedSafeNetworkLayout.subtitle": "Ma tistax tagħmel tranżazzjonijiet jew tiffirma messaġġi fuq {network} bi Zeal Smart Wallet{br}{br}Aqleb għal netwerk appoġġjat jew uża Legacy wallet.", "UnsupportedSafeNetworkLayoutk.title": "In-netwerk mhuwiex appoġġjat għal Smart Wallet", "UserConfirmationPopup.goBack": "Ikkanċella", "UserConfirmationPopup.submit": "Ibgħat xorta", "ViewPrivateKey.header": "Ċavetta Privata", "ViewPrivateKey.hint": "Taqsamx iċ-ċavetta privata tiegħek ma' ħadd. Żommha f'post sigur u offline", "ViewPrivateKey.subheader.mobile": "Agħfas biex tara ċ-Ċavetta Privata tiegħek", "ViewPrivateKey.subheader.web": "Poġġ<PERSON> l-cursor fuqha biex tara ċ-Ċavetta Privata tiegħek", "ViewPrivateKey.unblur.mobile": "Agħfas biex tara", "ViewPrivateKey.unblur.web": "Poġġi l-cursor biex tara", "ViewSecretPhrase.PasswordChecker.subtitle": "Daħħal il-password tiegħek biex tikkripta r-Recovery File. Ikollok bżonn tiftakarha fil-futur.", "ViewSecretPhrase.done": "Les<PERSON>", "ViewSecretPhrase.header": "Secret Phrase", "ViewSecretPhrase.hint": "Taqsamx il-frażi tiegħek ma' ħadd. Żommha f'post sigur u offline", "ViewSecretPhrase.subheader.mobile": "Agħ<PERSON>s biex tara s-Secret Phrase tiegħek", "ViewSecretPhrase.subheader.web": "Poġġi l-cursor fuqha biex tara s-Secret Phrase tiegħek", "ViewSecretPhrase.unblur.mobile": "Agħfas biex tara", "ViewSecretPhrase.unblur.web": "Poġġi l-cursor biex tara", "account-details.monerium": "Trasferimenti via Monerium, EMI regolata. <link>Tgħallem aktar</link>", "account-details.unblock": "It-trasferimenti jsiru permezz ta' Unblock, servizz awtorizzat u reġistrat għall-kambju u l-kustodja. <link>Sir af aktar</link>", "account-selector.empty-state": "Ma nstabet l-ebda kartiera", "account-top-up.select-currency.title": "Tokens", "account.accounts_not_found": "Ma stajna nsibu l-ebda kartiera", "account.accounts_not_found_search_valid_address": "Il-kartiera mhix fil-lista tiegħek", "account.add.create_new_secret_phrase": "Oħloq Frażi Sigrieta", "account.add.create_new_secret_phrase.subtext": "Fr<PERSON><PERSON><PERSON> sigrieta ġ<PERSON>da ta' 12-il kelma", "account.add.fromRecoveryKit.fileNotFound": "Ma stajniex insibu l-fajl tiegħek", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "account.add.fromRecoveryKit.fileNotFound.explanation": "Iċċekkja li dħalt fil-kont tajjeb bil-folder ta' Zeal Backup", "account.add.fromRecoveryKit.fileNotValid": "Ir-Recovery File mhuwiex validu", "account.add.fromRecoveryKit.fileNotValid.explanation": "Il-fajl mhux it-tip tajjeb jew <PERSON>ie mibdul.", "account.add.import_secret_phrase": "Importa Frażi Sigrieta", "account.add.import_secret_phrase.subtext": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, jew <PERSON><PERSON><PERSON><PERSON>", "account.add.select_type.add_hardware_wallet": "Hardware Wallet", "account.add.select_type.existing_smart_wallet": "Smart Wallet Eżistenti", "account.add.select_type.private_key": "Ċavetta Privata", "account.add.select_type.seed_phrase": "Seed Phrase", "account.add.select_type.title": "Importa kartiera", "account.add.select_type.zeal_recovery_file": "Fajl ta' Rkupru ta' Zeal", "account.add.success.title": "Kartiera ġdida maħluqa 🎉", "account.addLabel.header": "<PERSON><PERSON><PERSON> l-kartiera tiegħek", "account.addLabel.labelError.labelAlreadyExist": "L-isem diġà jeżisti. Ipprova isem ieħor", "account.addLabel.labelError.maxStringLengthExceeded": "Inqabeż l-għadd massimu ta' karattri", "account.add_active_wallet.primary_text": "Żid kartiera", "account.add_active_wallet.short_text": "<PERSON><PERSON><PERSON><PERSON>, Ikkonnettja jew Importa Kartiera", "account.add_from_ledger.success": "Ka<PERSON>ieri miżjuda f'Zeal", "account.add_tracked_wallet.primary_text": "<PERSON><PERSON> kart<PERSON> read-only", "account.add_tracked_wallet.short_text": "Ara l-portafoll u l-attività", "account.button.unlabelled-wallet": "Kart<PERSON> mingħajr isem", "account.create_wallet": "Oħloq Kartiera", "account.label.edit.title": "<PERSON><PERSON> l-isem tal-kartiera", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON><PERSON> {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Ir-Recovery File mhuwiex validu", "account.recoveryKit.selectBackupFile.subtitle": "Agħżel ir-Recovery File li trid tirrestawra", "account.recoveryKit.selectBackupFile.title": "Recovery File", "account.recoveryKit.success.recoveryFileFound": "Instab ir-Recovery File 🎉", "account.select_type_of_account.create_eoa.short": "Kartiera tradizzjonali għall-esperti", "account.select_type_of_account.create_eoa.title": "Oħloq kartiera b'Seed-phrase", "account.select_type_of_account.create_safe_wallet.title": "Oħloq Smart wallet", "account.select_type_of_account.existing_smart_wallet": "Smart Wallet eżistenti", "account.select_type_of_account.hardware_wallet": "Hardware wallet", "account.select_type_of_account.header": "Żid kartiera", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Ċavetta privata / Seed phrase", "account.select_type_of_account.read_only_wallet": "Kartiera għall-wiri biss", "account.select_type_of_account.read_only_wallet.short": "Ara kwalunkwe portafoll", "account.topup.title": "<PERSON><PERSON> fondi ma' <PERSON>", "account.view.error.refreshAssets": "Aġġorna", "account.widget.refresh": "Aġġorna", "account.widget.settings": "Settings", "accounts.view.copied-text": "Ikkupjat {formattedAddress}", "accounts.view.copiedAddress": "Ikkupjat {formattedAddress}", "action.accept": "Aċċetta", "action.accpet": "Aċċetta", "action.allow": "<PERSON>all<PERSON>", "action.back": "<PERSON><PERSON>", "action.cancel": "Ikkanċella", "action.card-activation.title": "Attiva l-kard", "action.claim": "Itlob", "action.close": "Agħlaq", "action.complete-steps": "Imla l-passi", "action.confirm": "Ikkonferma", "action.continue": "<PERSON><PERSON><PERSON>", "action.copy-address-understand": "Ok - Ikkopja l-indirizz", "action.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action.done": "Les<PERSON>", "action.dontAllow": "Tħallix", "action.edit": "<PERSON><PERSON>", "action.email-required": "Daħħal l-email", "action.enterPhoneNumber": "Daħħal numru tat-telefon", "action.expand": "<PERSON><PERSON><PERSON>", "action.fix": "Irranġa", "action.getStarted": "<PERSON><PERSON><PERSON>", "action.got_it": "Fhimt", "action.hide": "<PERSON><PERSON><PERSON>", "action.import": "Importa", "action.import-keys": "Importa ċ-ċwievet", "action.importKeys": "Importa ċ-Ċwievet", "action.minimize": "Minimize", "action.next": "<PERSON><PERSON><PERSON>", "action.ok": "OK", "action.reduceAmount": "<PERSON><PERSON><PERSON><PERSON> għall-massimu", "action.refreshWebsite": "Aġġorna l-websajt", "action.remove": "Neħħi", "action.remove-account": "Neħħi l-kont", "action.requestCode": "Itlob il-kodiċi", "action.resend_code": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>bgħat il-kodiċi", "action.resend_code_with_time": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>bgħat il-kodiċi {time}", "action.retry": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "action.reveal": "<PERSON><PERSON>", "action.save": "Issejvja", "action.save_changes": "Salva l-RPC", "action.search": "Fittex", "action.seeAll": "<PERSON> kollox", "action.select": "A<PERSON><PERSON><PERSON><PERSON>", "action.send": "Ibgħat", "action.skip": "Aqbeż", "action.submit": "Ibgħat", "action.understood": "<PERSON><PERSON><PERSON>", "action.update": "Aġġorna", "action.update-gnosis-pay-owner.complete": "Imla l-passi", "action.zeroAmount": "Daħħal l-ammont", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Oġġetti tal-kollezzjoni", "action_bar_title.tokens": "Tokens", "action_bar_title.transaction_request": "Talba għal tranżazzjoni", "activate-monerium.loading": "Qed inħejju l-kont personali tiegħek", "activate-monerium.success.title": "Monerium attivat", "activate-physical-card-widget.subtitle": "Il-kunsinna tista' tieħu sa 3 ġimgħat", "activate-physical-card-widget.title": "Attiva l-kard fiżika", "activate-smart-wallet.title": "Attiva l-kartiera", "active_and_tracked_wallets.title": "Zeal iħallaslek it-tariffi kollha fuq {network}, biex tkun tista' tagħmel tranżazzjonijiet b'xejn!", "activity.approval-amount.revoked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.approval-amount.unlimited": "<PERSON><PERSON> <PERSON>u", "activity.approval.approved_for": "Approvat għal", "activity.approval.approved_for_with_target": "Approvat {approvedTo}", "activity.approval.revoked_for": "Revokat għal", "activity.bank.serviceProvider": "Fornitur tas-servizz", "activity.bridge.serviceProvider": "Fornitur tas-servizz", "activity.cashback.period": "<PERSON><PERSON><PERSON> tal-flus lura", "activity.filter.card": "<PERSON><PERSON>", "activity.rate": "<PERSON><PERSON>", "activity.receive.receivedFrom": "Riċevut mingħand", "activity.send.sendTo": "<PERSON><PERSON><PERSON><PERSON><PERSON> lil", "activity.smartContract.unknown": "<PERSON><PERSON><PERSON><PERSON> mhux magħruf", "activity.smartContract.usingContract": "Bl-u<PERSON><PERSON> tal-kuntratt", "activity.subtitle.pending_timer": "{timerString} Għaddejja", "activity.title.arbitrary_smart_contract_interaction": "{function} fuq {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Interazzjoni ma' kuntratt mhux magħruf", "activity.title.bridge.from": "<PERSON> minn {token}", "activity.title.bridge.to": "<PERSON> lejn {token}", "activity.title.buy": "Ix<PERSON> {asset}", "activity.title.card_owners_updated": "<PERSON><PERSON> tal-kard a<PERSON>", "activity.title.card_spend_limit_updated": "<PERSON><PERSON><PERSON> tal-infiq tal-kard is<PERSON>t", "activity.title.cashback_deposit": "Depożitu għal Flus Lura", "activity.title.cashback_reward": "Premju ta' Flus <PERSON>", "activity.title.cashback_withdraw": "<PERSON><PERSON><PERSON><PERSON> minn <PERSON><PERSON>", "activity.title.claimed_reward": "<PERSON><PERSON><PERSON>", "activity.title.deployed_smart_wallet_gnosis": "<PERSON><PERSON>", "activity.title.deposit_from_bank": "Depożitu mill-Bank", "activity.title.deposit_into_card": "Depożitu fil-Ka<PERSON>", "activity.title.deposit_into_earn": "<PERSON><PERSON><PERSON><PERSON><PERSON> fi {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} fuq {smartContract}", "activity.title.from": "Minn {sender}", "activity.title.pendidng_areward_claim": "<PERSON><PERSON> titlob il-<PERSON>ju", "activity.title.pendidng_breward_claim": "<PERSON><PERSON> titlob il-<PERSON>ju", "activity.title.recharge_disabledh": "Recharge tal-kard diżatt<PERSON>t", "activity.title.recharge_set": "Mira tar-recharge issettjata", "activity.title.recovered_smart_wallet_gnosis": "Installazzjoni ta' apparat ġdid", "activity.title.send_pending": "<PERSON><PERSON> {receiver}", "activity.title.send_to_bank": "Lejn il-Bank", "activity.title.swap": "Ix<PERSON> {token}", "activity.title.to": "<PERSON><PERSON> {receiver}", "activity.title.withdraw_from_card": "Irtirar mill-Kard", "activity.title.withdraw_from_earn": "<PERSON><PERSON><PERSON><PERSON> minn {earn}", "activity.transaction.networkFees": "Tariffi tan-netwerk", "activity.transaction.state": "Tranżazzjoni kompluta", "activity.transaction.state.completed": "Tranżazzjoni kompluta", "activity.transaction.state.failed": "Tranżazzjoni falluta", "add-account.section.import.header": "Importa", "add-another-card-owner": "<PERSON><PERSON> sid ie<PERSON>or tal-kard", "add-another-card-owner.Recommended.footnote": "<PERSON><PERSON> il-wallet Zeal bħala sid ieħor tal-kard Gnosis Pay", "add-another-card-owner.Recommended.primaryText": "<PERSON><PERSON>eal ma' Gnosis Pay", "add-another-card-owner.recommended": "Rakkomandat", "add-owner.confirmation.subtitle": "Bħala miżura ta' sigurtà, it-tibdil jieħu 3 minuti. <PERSON><PERSON> dan <PERSON><PERSON><PERSON>, il-kard tiegħek tkun iffriżata u ma tkunx tista' tagħmel pagamenti.", "add-owner.confirmation.title": "Il-kard tiegħek se tkun iffriżata għal 3 minuti sakemm isir it-tibdil.", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Il-wallet ma tistax tiżdied, di<PERSON><PERSON> qed tintuża", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "Ipprova kartiera oħra", "add.account.backup.decrypt.success": "Kartiera r<PERSON>aw<PERSON>", "add.account.backup.password.passwordIncorrectMessage": "Il-password mhix korretta", "add.account.backup.password.subtitle": "Daħħal il-password li użajt biex tikkodifika l-Fajl ta' Rkupru tiegħek", "add.account.backup.password.title": "<PERSON><PERSON><PERSON><PERSON> il-password", "add.account.google.login.subtitle": "Approva t-talba fuq Google Drive biex tissinkronizza l-Fajl ta' Rkupru", "add.account.google.login.title": "Nistennew l-approvazzjoni...", "add.readonly.already_added": "Il-kartiera diġà miżjuda", "add.readonly.continue": "<PERSON><PERSON><PERSON>", "add.readonly.empty": "Daħħal indirizz jew E<PERSON>", "addBankRecipient.title": "Żid riċevitur bankarju", "add_funds.deposit_from_bank_account": "Iddepożita minn kont bankarju", "add_funds.from_another_wallet": "Minn kartiera oħra", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "<PERSON><PERSON><PERSON><PERSON><PERSON> mat-topup dApp", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Ikkonnettja kwalunkwe kartiera maz-Zeal topup dApp u ibgħat il-fondi malajr fil-kartiera tiegħek", "add_funds.from_crypto_wallet.header": "Minn kartiera oħra", "add_funds.from_crypto_wallet.header.show_wallet_address": "Uri l-indirizz tal-kartiera tiegħek", "add_funds.from_exchange.header": "Ibgħat minn exchange", "add_funds.from_exchange.header.copy_wallet_address": "Ikkopja l-indirizz ta' Zeal tiegħek", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance eċċ", "add_funds.from_exchange.header.open_exchange": "Iftaħ l-app jew is-sit tal-exchange", "add_funds.from_exchange.header.selected_token": "Ibgħat {token} lil <PERSON>", "add_funds.from_exchange.header.selected_token.subtitle": "Fuq {network}", "add_funds.from_exchange.header.send_selected_token": "Ibgħat token appoġġjat", "add_funds.from_exchange.header.send_selected_token.subtitle": "Agħżel token u network appoġġjat", "add_funds.import_wallet": "Importa kartiera tal-kripto eż<PERSON>enti", "add_funds.title": "Iffinanzja l-kont tiegħek", "add_funds.transfer_from_exchange": "Ittrasferixxi minn exchange", "address.add.header": "Ara l-kartiera tiegħek f'Zeal{br}bil-modalità read-only", "address.add.subheader": "Daħħal l-indirizz jew l-ENS tiegħek biex tara l-assi tiegħek fuq in-networks EVM kollha f'post wieħed. Oħloq jew importa aktar kartieri aktar tard.", "address_book.change_account.bank_transfers.header": "Destinatarji tal-bank", "address_book.change_account.bank_transfers.primary": "Destinatarju tal-bank", "address_book.change_account.cta": "Segwi l-kartiera", "address_book.change_account.search_placeholder": "<PERSON><PERSON> jew fittex indirizz", "address_book.change_account.tracked_header": "<PERSON><PERSON><PERSON> read-only", "address_book.change_account.wallets_header": "<PERSON><PERSON><PERSON> at<PERSON>", "app-association-check-failed.modal.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "app-association-check-failed.modal.subtitle": "Erġa' pprova. Jista' jkun hemm dewmien biex jittellgħu l-Passkeys minħabba problemi ta' konnettività. Jekk il-problema tippers<PERSON>i, er<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Z<PERSON> u erġa' pprova.", "app-association-check-failed.modal.subtitle.creation": "Erġa' pprova. Jista' jkun hemm dewmien fil-ħolqien tal-Passkey minħabba problemi ta' konnettività. Jekk il-problema tippers<PERSON>i, er<PERSON><PERSON>' <PERSON><PERSON><PERSON> Z<PERSON> u erġa' pprova.", "app-association-check-failed.modal.title.creation": "It-<PERSON><PERSON><PERSON> tiegħek ma rnexxilux joħloq passkey", "app-association-check-failed.modal.title.signing": "It-<PERSON><PERSON><PERSON> tiegħ<PERSON> ma rnexxilux itella' l-passkeys", "app.app_protocol_group.borrowed_tokens": "Tokens mislufin", "app.app_protocol_group.claimable_amount": "Ammont li tista' titlob", "app.app_protocol_group.health_rate": "Rata tas-saħħa", "app.app_protocol_group.lending": "Self", "app.app_protocol_group.locked_tokens": "Tokens imsakkrin", "app.app_protocol_group.nfts": "Oġġetti tal-kollezzjoni", "app.app_protocol_group.reward_tokens": "Tokens ta' premju", "app.app_protocol_group.supplied_tokens": "To<PERSON>s ipprovduti", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Vesting token", "app.appsGroupHeader.discoverMore": "Skopri aktar", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Fittex jew daħħal URL", "app.error-banner.cory": "Ikkopja d-data tal-iżball", "app.error-banner.retry": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "app.list_item.rewards": "Premjijiet {value}", "app.position_details.health_rate.description": "Is-self tiegħek diviż bil-kollateral.", "app.position_details.health_rate.title": "X'inhi l-health rate?", "approval.edit-limit.label": "<PERSON><PERSON> l-limitu tal-infiq", "approval.permit_info": "Informazzjoni dwar il-Permit", "approval.spend-limit.edit-modal.cancel": "Ikkanċella", "approval.spend-limit.edit-modal.limit-label": "<PERSON><PERSON><PERSON> tal-infiq", "approval.spend-limit.edit-modal.max-limit-error": "<PERSON><PERSON><PERSON><PERSON>, limitu għoli", "approval.spend-limit.edit-modal.revert": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "approval.spend-limit.edit-modal.set-to-unlimited": "Issettja għal Bla Limitu", "approval.spend-limit.edit-modal.submit": "Issejvja", "approval.spend-limit.edit-modal.title": "Editja l-permessi", "approval.spend_limit_info": "<PERSON>'inhu l-limitu tal-infiq?", "approval.what_are_approvals": "X'inhuma l-Approvazzjonijiet?", "apps_list.page.emptyState": "M'hemmx apps attivi", "backpace.removeLastDigit": "Neħħi l-aħħar ċifra", "backup-banner.backup_now": "<PERSON><PERSON><PERSON><PERSON> backup", "backup-banner.risk_losing_funds": "Agħmel backup issa jew tirris<PERSON><PERSON> li <PERSON>f il-fondi", "backup-banner.title": "Il-kartiera m'għandhiex backup", "backupRecoverySmartWallet.noExportPrivateKeys": "Backup awtomatiku: L-iSmart Wallet tiegħek tiġi ssejvjata bħala passkey - m'hemmx bżonn ta' frażi sigrieta jew ċavetta privata.", "backupRecoverySmartWallet.safeContracts": "Sigurtà b'ħafna ċwievet: Il-kartieri ta' Zeal jaħdmu fuq kuntratti Safe, għalhekk diversi apparati jistgħu japprovaw tranżazzjoni. M'hemm l-ebda punt wieħed ta' falliment.", "backupRecoverySmartWallet.security": "Apparati multipli: Tista' tuża l-kartiera tiegħek fuq diversi apparati bil-Passkey. Kull apparat ikollu ċ-ċavetta privata tiegħu.", "backupRecoverySmartWallet.showLocalPrivateKey": "Modalità Espert: T<PERSON>' tesporta ċ-ċavetta privata ta' dan l-apparat, tużaha f'kartiera oħra u tikkonnettja fuq <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Uri ċ-ċavetta privata</Key>", "backupRecoverySmartWallet.storingKeys": "Sinkronizzat mal-Cloud: Il-pass<PERSON> tin<PERSON><PERSON><PERSON> b'mod sigur fl-iCloud, fil-Google Password Manager, jew fil-password manager tie<PERSON><PERSON><PERSON>.", "backupRecoverySmartWallet.title": "Backup u Rkupru tal-iSmart Wallet", "balance-change.card.titile": "<PERSON><PERSON>", "balanceChange.pending": "Pendent<PERSON>", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Agħżel fornitur tas-servizz", "bank-transfer.change-deposit-receiver.subtitle": "<PERSON><PERSON> depożitu se jidħol f'din il-kartiera", "bank-transfer.change-deposit-receiver.title": "Agħżel il-kartiera għad-depożiti", "bank-transfer.change-owner.subtitle": "Kartiera għal-login u l-irkupru tal-kont", "bank-transfer.change-owner.title": "Issettja s-sid tal-kont", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Id-depożiti kollha jidħlu f'din il-kartiera.", "bank-transfer.configrm-change-deposit-receiver.title": "<PERSON><PERSON><PERSON> il-kartiera tad-depożiti", "bank-transfer.configrm-change-owner.subtitle": "<PERSON><PERSON><PERSON>? Din hi l-kartiera tal-irkupru.", "bank-transfer.configrm-change-owner.title": "<PERSON><PERSON><PERSON> is-sid tal-kont", "bank-transfer.deposit.widget.status.complete": "Les<PERSON>", "bank-transfer.deposit.widget.status.funds_received": "Fondi rċevuti", "bank-transfer.deposit.widget.status.sending_to_wallet": "<PERSON><PERSON> jintbagħat lill-kartiera", "bank-transfer.deposit.widget.status.transfer-on-hold": "Trasferiment imwaqqaf", "bank-transfer.deposit.widget.status.transfer-received": "<PERSON><PERSON> jintbagħat lill-kartiera", "bank-transfer.deposit.widget.subtitle": "{from} għal {to}", "bank-transfer.deposit.widget.title": "Depożitu", "bank-transfer.intro.bulletlist.point_1": "Stabbilixxi b'Unblock", "bank-transfer.intro.bulletlist.point_2": "Trasferiment bejn EUR/GBP u aktar minn 10 tokens", "bank-transfer.intro.bulletlist.point_3": "0% tariffi sa $5k fix-xahar, 0.2% wara", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "<PERSON>ed jintbag<PERSON>tu lill-bank", "bank-transfer.withdrawal.widget.status.in-progress": "Trasferiment għaddej", "bank-transfer.withdrawal.widget.status.on-hold": "Trasferiment imwaqqaf", "bank-transfer.withdrawal.widget.status.success": "Les<PERSON>", "bank-transfer.withdrawal.widget.subtitle": "{from} għal {to}", "bank-transfer.withdrawal.widget.title": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfers.bank-account-actions.remove-this-account": "<PERSON>eħ<PERSON><PERSON> dan il-kont", "bank-transfers.bank-account-actions.switch-to-this-account": "<PERSON><PERSON><PERSON><PERSON> għal dan il-kont", "bank-transfers.deposit.fees-for-less-than-5k": "Tariffi għal $5k jew inqas", "bank-transfers.deposit.fees-for-more-than-5k": "Tariffi għal aktar minn $5k", "bank-transfers.set-receiving-bank.title": "Issettja l-bank li jirċievi", "bank-transfers.settings.account_owner": "Sid <PERSON>kont", "bank-transfers.settings.receiver_of_bank_deposits": "Riċevitur ta' depożiti bankarji", "bank-transfers.settings.receiver_of_withdrawals": "Riċevitur tal-irtirar", "bank-transfers.settings.registered_email": "<PERSON>ail <PERSON>", "bank-transfers.settings.title": "Settings tat-trasferimenti bankarji", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} Kont", "bank-transfers.setup.bank-account": "Kont bankarju", "bankTransfer.withdraw.max_loading": "Mass.: {amount}", "bank_details_do_not_match.got_it": "Fhimt", "bank_details_do_not_match.subtitle": "Is-sort code u n-numru tal-kont ma jaqblux. Iċċekkjahom u erġa' pprova.", "bank_details_do_not_match.title": "Id-dettalji tal-bank ma jaqblux", "bank_tranfsers.select_country_of_residence.country_not_supported": "Jiddispjaċina, m'hemmx trasferimenti fi {country} għalissa", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Iftaħ l-app tal-bank tiegħek", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Ibgħat {fiatCurrencyCode} lill-kont tiegħek", "bank_transfer.deposit.header": "{fullName}: detta<PERSON>ji tal-kont <PERSON>i", "bank_transfer.kyc_status_widget.subtitle": "Trasferimenti bankarji", "bank_transfer.kyc_status_widget.title": "Qed nivverifikaw l-identità", "bank_transfer.personal_details.date_of_birth": "Data tat-twelid", "bank_transfer.personal_details.date_of_birth.invalid_format": "Id-data mhix valida", "bank_transfer.personal_details.date_of_birth.too_young": "<PERSON><PERSON> minn tal-inqas 18-il sena", "bank_transfer.personal_details.first_name": "<PERSON><PERSON>", "bank_transfer.personal_details.last_name": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.personal_details.title": "<PERSON>d-<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.reference.label": "Referenza (Mhux obbligatorja)", "bank_transfer.reference_message": "<PERSON><PERSON><PERSON><PERSON><PERSON> minn <PERSON>", "bank_transfer.residence_details.address": "L-indirizz tiegħek", "bank_transfer.residence_details.city": "Belt", "bank_transfer.residence_details.country_of_residence": "Pajjiż ta' residenza", "bank_transfer.residence_details.country_placeholder": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.residence_details.postcode": "Kodiċi postali", "bank_transfer.residence_details.street": "Triq", "bank_transfer.residence_details.your_residence": "Ir-<PERSON><PERSON> tiegħek", "bank_transfers.choose-wallet.continue": "<PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.test": "Żid kartiera", "bank_transfers.choose-wallet.warning.subtitle": "Tista' torbot kartiera waħda biss. Wara ma tkunx tista' tibdelha.", "bank_transfers.choose-wallet.warning.title": "Agħżel il-kartiera bil-għaqal", "bank_transfers.choose_wallet.subtitle": "Agħżel kartiera biex torbotha mal-bank. ", "bank_transfers.choose_wallet.title": "Agħżel kartiera", "bank_transfers.continue": "<PERSON><PERSON><PERSON>", "bank_transfers.currency_is_currently_not_supported": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit-header": "Depożitu", "bank_transfers.deposit.account-name": "<PERSON><PERSON> tal-kont", "bank_transfers.deposit.account-number-copied": "Numru tal-kont <PERSON>", "bank_transfers.deposit.amount-input": "Ammont li se tiddepożita", "bank_transfers.deposit.amount-output": "Ammont tad-destinazzjoni", "bank_transfers.deposit.amount-output.error": "żball", "bank_transfers.deposit.buttet-point.receive-crypto": "Irċievi {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.currency-not-supported.subtitle": "Id-depożiti bankarji minn {code} twaqqfu sa avviż ieħor.", "bank_transfers.deposit.currency-not-supported.title": "{code} depożiti mhumiex disponibbli bħalissa", "bank_transfers.deposit.default-token.balance": "Bilanċ {amount}", "bank_transfers.deposit.deposit-header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.enter_amount": "Daħħal l-Ammont", "bank_transfers.deposit.iban-copied": "IBAN ikkupjat", "bank_transfers.deposit.increase-amount": "L-inqas trasferiment hu {limit}", "bank_transfers.deposit.loading": "<PERSON><PERSON>", "bank_transfers.deposit.max-limit-reached": "L-ammont jaqbeż il-limitu massimu", "bank_transfers.deposit.modal.kyc.button-text": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.modal.kyc.text": "Biex nivverifikaw l-identità tiegħek, neħ<PERSON>ġu xi dettalji personali u dokumenti. Is-sottomissjoni normalment tieħu ftit minuti biss.", "bank_transfers.deposit.modal.kyc.title": "Ivverifika l-identità tiegħek biex iżżid il-limiti", "bank_transfers.deposit.reduce_amount": "Naqqas l-ammont", "bank_transfers.deposit.show-account.account-number": "<PERSON><PERSON><PERSON> tal-kont", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Sort code", "bank_transfers.deposit.sort-code-copied": "Sort code ikkupjat", "bank_transfers.deposit.withdraw-header": "Ġbid", "bank_transfers.failed_to_load_fee": "<PERSON><PERSON><PERSON> ma<PERSON>", "bank_transfers.fees": "<PERSON><PERSON><PERSON>", "bank_transfers.increase-amount": "L-inqas trasferiment hu {limit}", "bank_transfers.insufficient-funds": "Fondi insuffiċjenti", "bank_transfers.select_country_of_residence.title": "<PERSON>jn to<PERSON>?", "bank_transfers.setup.cta": "Attiva t-trasferimenti", "bank_transfers.setup.enter-amount": "Daħħal ammont", "bank_transfers.source_of_funds.form.business_income": "Dħul min-negozju", "bank_transfers.source_of_funds.form.other": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.pension": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.salary": "Salarju", "bank_transfers.source_of_funds.form.title": "Is-sors tal-fondi tieg<PERSON>", "bank_transfers.source_of_funds_description.placeholder": "Idd<PERSON><PERSON><PERSON><PERSON> s-sors tal-fondi...", "bank_transfers.source_of_funds_description.title": "Għidilna aktar dwar is-sors tal-fondi tiegħek", "bank_transfers.withdraw-header": "Ġbid", "bank_transfers.withdraw.amount-input": "Ammont li se tiġbed", "bank_transfers.withdraw.max-limit-reached": "L-ammont jaqbeż il-limitu massimu", "bank_transfers.withdrawal.verify-id": "Naqqas l-ammont", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Qbiżt il-limitu massimu tal-input", "banner.above_maximum_limit.maximum_limit_per_deposit": "Dan hu l-limitu massimu għal kull depożitu", "banner.above_maximum_limit.subtitle": "Qbiżt il-limitu massimu tal-input", "banner.above_maximum_limit.title": "Naqqas l-ammont għal {amount} jew inqas", "banner.above_maximum_limit.title.default": "Naqqas l-ammont", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Ma lħaqtx il-limitu minimu", "banner.below_minimum_limit.minimum_limit_for_token": "Dan hu l-limitu minimu għal din it-token", "banner.below_minimum_limit.title": "<PERSON><PERSON> l-ammont għal {amount} jew aktar", "banner.below_minimum_limit.title.default": "Żid l-ammont", "breaard.in_porgress.info_popup.cta": "Onfoq biex taqla' {earn}", "breaard.in_porgress.info_popup.footnote": "<PERSON><PERSON> tu<PERSON> u l-kard Gnosis Pay, taqbel mat-termini ta' din il-kampanja.", "breaward.in_porgress.info_popup.bullet_point_1": "Onfoq {remaining} fi <PERSON><PERSON>n {time} biex tikseb dan ir-rigal.", "breaward.in_porgress.info_popup.bullet_point_2": "<PERSON><PERSON>u biss b'Gnosis Pay jgħodd għall-ammont li tonfoq.", "breaward.in_porgress.info_popup.bullet_point_3": "<PERSON>a li tikseb ir-rig<PERSON> tieg<PERSON>, dan jint<PERSON><PERSON> fil-kont <PERSON> tiegħek.", "breaward.in_porgress.info_popup.header": "A<PERSON><PERSON>' {earn}, billi tonfoq {remaining}", "breward.celebration.for_spending": "Talli nfaqt bil-kard tiegħek", "breward.dc25-eligible-celebration.for_spending": "Int fost l-ewwel {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "Ma kontx fost l-ewwel {limit} li nfaqaw", "breward.expired_banner.earn_by_spending": "Aqla' {earn} billi tonfoq {amount}", "breward.expired_banner.reward_expired": "{earn} rigal skada", "breward.in_progress_banner.cta.title": "Onfoq biex taqla' {earn}", "breward.ready_to_claim.error.try_again": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "breward.ready_to_claim.error_title": "Ma stajniex nagħtuk ir-rigal", "breward.ready_to_claim.in_progress": "<PERSON>ed niksbu r-rigal", "breward.ready_to_claim.youve_earned": "A<PERSON><PERSON>'jt {earn}!", "breward_already_claimed.title": "Il-premju diġà nġabar. Jekk ma rċevejtx it-token tal-premju, jekk jogħġbok ikkuntattja lis-support.", "breward_cannotbe_claimed.title": "Il-premju ma jistax jinġabar bħalissa. Jekk jogħġbok erġa' pprova aktar tard.", "bridge.best_return": "<PERSON>-<PERSON><PERSON><PERSON> rotta għall-qligħ", "bridge.best_serivce_time": "L-aktar rotta mgħaġġla", "bridge.check_status.complete": "Les<PERSON>", "bridge.check_status.progress_text": "Qed isir il-pont ta' {from} għal {to}", "bridge.remove_topup": "Neħħi t-Top-up", "bridge.request_status.completed": "Les<PERSON>", "bridge.request_status.pending": "Pendent<PERSON>", "bridge.widget.completed": "Les<PERSON>", "bridge.widget.currencies": "{from} għal {to}", "bridge_rote.widget.title": "<PERSON>", "browse.discover_more_apps": "Skopri aktar Apps", "browse.google_search_term": "Fittex \"{searchTerm}\"", "brward.celebration.you_earned": "Aqla'jt", "brward.expired_banner.subtitle": "<PERSON><PERSON><PERSON> i<PERSON><PERSON>, aħjar", "brward.in_progress_banner.subtitle": "Jiskadi fi {expiredInFormatted}", "buy": "<PERSON><PERSON><PERSON>", "buy.enter_amount": "Daħħal Ammont", "buy.loading": "<PERSON><PERSON> ji<PERSON>...", "buy.no_routes_found": "Ma nstabet l-ebda rotta", "buy.not_enough_balance": "M'hemmx biżżejjed bilanċ", "buy.select-currency.title": "Agħ<PERSON>el token", "buy.select-to-currency.title": "Ixtri tokens", "buy_form.title": "Ixtri <PERSON>", "cancelled-card.create-card-button.primary": "<PERSON>dna kard virtwali ġ<PERSON>da", "cancelled-card.switch-card-button.primary": "<PERSON><PERSON><PERSON><PERSON> kard", "cancelled-card.switch-card-button.short-text": "Għ<PERSON><PERSON> kard oħra attiva", "card": "<PERSON><PERSON>", "card-add-cash.confirm-stage.banner.no-routes-found": "<PERSON>'hemmx rotot, ipprova token jew ammont differenti", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>onn {amount} i<PERSON><PERSON><PERSON> {symbol} biex tħallas it-tariffi", "card-add-cash.confirm-stage.banner.value-loss": "Se titlef {loss} f'valur", "card-add-cash.confirm-stage.banner.value-loss.revert": "<PERSON><PERSON> lura", "card-add-cash.edit-stage.cta.cancel": "Ikkanċella", "card-add-cash.edit-stage.cta.continue": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.enter-amount": "<PERSON><PERSON><PERSON><PERSON> somma", "card-add-cash.edit-stage.cta.reduce-to-max": "<PERSON><PERSON><PERSON> massimu", "card-add-cash.edit-staget.banner.no-routes-found": "<PERSON>'hemmx rotot, ipprova token jew ammont differenti", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Bgħatna t-talba lill-ħardwer wallet tiegħek. Kompli minn hemm.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Iffirma bil-ħardwer wallet", "card-balance": "Bilanċ: {balance}", "card-cashback.status.title": "Iddepożita fil-Flus <PERSON>ra", "card-copy-safe-address.copy_address": "Ikkopja l-indirizz", "card-copy-safe-address.copy_address.done": "Ikkupjat", "card-copy-safe-address.warning.description": "Dan l-indirizz jista' jirċievi biss {cardAsset} fuq Gnosis Chain. Tibgħatx assi minn networks oħra għal dan l-indirizz. Dawn jintilfu.", "card-copy-safe-address.warning.header": "Ibgħat biss {cardAsset} fuq Gnosis Chain", "card-marketing-card.center.subtitle": "Tariffi tal-FX", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "Imgħax", "card-marketing-card.right.subtitle": "Rigal tar-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "card-marketing-card.title": "Il-kard tal-VISA b'imgħax għoli fl-Ewropa", "card-marketing-tile.get-started": "<PERSON><PERSON><PERSON>", "card-select-from-token-title": "Ag<PERSON><PERSON><PERSON> it-token li se tuża", "card-top-up.banner.subtitle.completed": "Les<PERSON>", "card-top-up.banner.subtitle.failed": "Ma rnexxiex", "card-top-up.banner.subtitle.pending": "{timerString} Għaddejja", "card-top-up.banner.title": "Qed tidde<PERSON> {amount}", "card-topup.select-token.emptyState": "Ma sibna l-ebda tokens", "card.activate.card_number_not_valid": "<PERSON><PERSON><PERSON> tal-kard <PERSON>u. Erġa' pprova.", "card.activate.invalid_card_number": "<PERSON><PERSON><PERSON> tal-kard <PERSON>u.", "card.activation.activate_physical_card": "Attiva l-kard fiżika", "card.add-cash.amount-to-withdraw": "Ammont tat-top-up", "card.add-from-earn-form.title": "<PERSON><PERSON> flus mal-Kard", "card.add-from-earn-form.withdraw-to-card": "<PERSON><PERSON><PERSON>", "card.add-from-earn.amount-to-withdraw": "Ammont li trid tiġbed lejn il-Kard", "card.add-from-earn.enter-amount": "Daħħal Ammont", "card.add-from-earn.loading": "<PERSON><PERSON>", "card.add-from-earn.max-label": "Bilanċ: {amount}", "card.add-from-earn.no-routes-found": "Ma nstabet l-ebda rotta", "card.add-from-earn.not-enough-balance": "M'hemmx biżżejjed bilanċ", "card.add-owner.queued": "<PERSON><PERSON><PERSON>' sid pendenti", "card.add-to-wallet-flow.subtitle": "Agħmel pagamenti mill-kartiera tiegħek", "card.add-to-wallet.copy-card-number": "Ikkopja n-numru tal-kard hawn taħt", "card.add-to-wallet.title": "<PERSON><PERSON> ma' {platformName} Wallet", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "IKKANĊELLATA", "card.card-owner-not-found.disconnect-btn": "<PERSON><PERSON><PERSON><PERSON> kard minn <PERSON>", "card.card-owner-not-found.subtitle": "Aġġorna s-sid biex terġa' tqabbad il-kard.", "card.card-owner-not-found.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> il-kard", "card.card-owner-not-found.update-owner-btn": "Aġġorna s-sid tal-kard", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} fi {date}", "card.cashback.widgetNoCashback.subtitle": "Iddepożita biex tibda taqla'", "card.cashback.widgetNoCashback.title": "Aqla' sa {defaultPercentage} Flus lura", "card.cashback.widgetcashbackValue.rewards": "{amount} pendenti", "card.cashback.widgetcashbackValue.title": "{percentage} Flus lura", "card.choose-wallet.connect_card": "Ikkonnettja l-kard", "card.choose-wallet.create-new": "<PERSON><PERSON> <PERSON> b<PERSON>ala sid", "card.choose-wallet.import-another-wallet": "Importa kartiera oħra", "card.choose-wallet.import-current-owner": "Importa s-sid attwali tal-kard", "card.choose-wallet.import-current-owner.sub-text": "Importa ċ-ċwievet privati jew is-seed phrase tal-kard Gnosis Pay tiegħek", "card.choose-wallet.title": "Agħżel wallet biex timmaniġġja l-kard", "card.connectWalletToCardGuide": "Ikkopja l-indirizz tal-kartiera", "card.connectWalletToCardGuide.addGnosisPayOwner": "<PERSON>id Sid ta' Gnosis Pay", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Iftaħ Gnosispay.com bil-kartiera l-oħra tiegħek{br}2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> “Account”{br}3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> “Account details”{br}4. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> “Edit”, <PERSON><PERSON><PERSON> “Account Owner”, u{br}5. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> “Add address”{br}6. Pejstja l-indirizz ta' Zeal tiegħek u kklikkja 'save'", "card.connectWalletToCardGuide.header": "<PERSON>k<PERSON><PERSON><PERSON> {account} mal-Gnosis Pay Card", "card.connect_card.start": "Ikkon<PERSON><PERSON>", "card.copiedAddress": "Ikkupjat {formattedAddress}", "card.disconnect-account.title": "Skonnettja l-kont", "card.hw-wallet-support-drop.add-owner-btn": "<PERSON><PERSON> sid <PERSON>l<PERSON>kard", "card.hw-wallet-support-drop.disconnect-btn": "<PERSON><PERSON><PERSON><PERSON> kard minn <PERSON>", "card.hw-wallet-support-drop.subtitle": "<PERSON><PERSON> sid <PERSON>d li mhuwiex ħardwer wallet.", "card.hw-wallet-support-drop.title": "M'għadux appoġġ għal ħardwer wallets.", "card.kyc.continue": "Kompli s-setup", "card.list_item.title": "<PERSON><PERSON>", "card.onboarded.transactions.empty.description": "L-attività tal-pagamenti tiegħek se tidher hawn", "card.onboarded.transactions.empty.title": "Attività", "card.order.continue": "Kompli ordna l-kard", "card.order.free_virtual_card": "<PERSON><PERSON> virt<PERSON>i b'<PERSON><PERSON>n", "card.order.start": "<PERSON><PERSON><PERSON> kard b'<PERSON><PERSON>n", "card.owner-not-imported.cancel": "Ikkanċella", "card.owner-not-imported.import": "Importa", "card.owner-not-imported.subtitle": "Biex tawtorizza din it-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, qab<PERSON> l-owner wallet tal-kont tiegħek ta' Gnosis Pay ma' Zeal. Nota: Din hija separata mis-soltu sign-in tal-wallet tiegħek ta' Gnosis Pay.", "card.owner-not-imported.title": "Żid l-owner tal-kont Gnosis Pay", "card.page.order_free_physical_card": "Ordna kard fiżika b'xejn", "card.pin.change_pin_at_atm": "Il-PIN tista' tibdlu minn ATMs magħżula", "card.pin.timeout": "L-iskrin se jagħlaq fi {seconds} sek", "card.quick-actions.add-assets": "<PERSON>id flus", "card.quick-actions.add-cash": "<PERSON>id flus", "card.quick-actions.details": "<PERSON><PERSON><PERSON>", "card.quick-actions.freeze": "Iffriża", "card.quick-actions.freezing": "<PERSON>ed tiġi f<PERSON>", "card.quick-actions.unfreeze": "Sblokka", "card.quick-actions.unfreezing": "<PERSON><PERSON> ti<PERSON> sblukkata", "card.quick-actions.withdraw": "<PERSON><PERSON><PERSON>", "card.read-only-detected.create-new": "<PERSON><PERSON> <PERSON> b<PERSON>ala sid", "card.read-only-detected.import-current-owner": "Importa ċ-ċwievet għal {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Importa ċ-ċwievet privati jew is-seed phrase tal-wallet {address}", "card.read-only-detected.title": "Instabet kard fuq wallet read-only. Agħżel wallet biex timmaniġġja l-kard", "card.remove-owner.queued": "It-tneħ<PERSON>ija tas-sid fil-kju", "card.settings.disconnect-from-zeal": "<PERSON><PERSON><PERSON><PERSON> minn <PERSON>", "card.settings.edit-owners": "Biddel is-sidien tal-kard", "card.settings.getCard": "<PERSON><PERSON><PERSON> ka<PERSON>", "card.settings.getCard.subtitle": "<PERSON><PERSON> virtwali jew fiżiċi", "card.settings.notRecharging": "<PERSON><PERSON><PERSON> qed tirriċarja", "card.settings.notifications.subtitle": "Irċievi notifiki tal-pagamenti", "card.settings.notifications.title": "Notifiki tal-kard", "card.settings.page.title": "Setting<PERSON> tal-Kard", "card.settings.select-card.cancelled-cards": "<PERSON><PERSON> imħassra", "card.settings.setAutoRecharge": "Issettja auto-recharge", "card.settings.show-card-address": "Uri l-indirizz tal-kard", "card.settings.spend-limit": "Issettja l-limitu tan-nefqa", "card.settings.spend-limit-title": "<PERSON>itu attwali ta' kuljum: {limit}", "card.settings.switch-active-card": "<PERSON><PERSON><PERSON> il-kard attiva", "card.settings.switch-active-card-description": "Kard attiva: {card}", "card.settings.switch-card.card-item.cancelled": "Imħassra", "card.settings.switch-card.card-item.frozen": "Iffriżata", "card.settings.switch-card.card-item.title": "Gnosis Pay Card", "card.settings.switch-card.card-item.title.physical": "<PERSON><PERSON>", "card.settings.switch-card.card-item.title.virtual": "<PERSON><PERSON> virt<PERSON>i", "card.settings.switch-card.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> kard", "card.settings.targetBalance": "Bilanċ fil-mira: {threshold}", "card.settings.view-pin": "Ara l-PIN", "card.settings.view-pin-description": "De<PERSON><PERSON>m ipproteġi l-PIN tiegħek", "card.title": "<PERSON><PERSON>", "card.transactions.header": "Tranżazzjonijiet tal-kard", "card.transactions.see_all": "Ara t-tranżazzjonijiet kollha", "card.virtual": "VIRTWALI", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Il-flus lura jintbagħtu fuq il-kard tiegħek fil-bidu tal-ġimgħa ta' wara li tkun qlajthom.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "<PERSON>ktar ma t<PERSON>, iktar taqla' fuq kull xirja.", "cardCashback.onboarding.title": "<PERSON><PERSON><PERSON><PERSON> <PERSON> {percentage} Flus lura", "cardCashbackWithdraw.amount": "Ammont tal-irtirar", "cardCashbackWithdraw.header": "Irtira {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "Il<PERSON>kard imblukkata u ma setgħetx tiġi attivata", "cardWidget.cashback": "Flus lura", "cardWidget.cashbackUpToDefaultPercentage": "Sa {percentage}", "cardWidget.startEarning": "<PERSON><PERSON><PERSON> a<PERSON>'", "cardWithdraw.amount": "Ammont li se tiġbed", "cardWithdraw.header": "Iġbed mill-kard", "cardWithdraw.selectWithdrawWallet.title": "Agħżel kartiera biex{br}tiġ<PERSON> fiha", "cardWithdraw.success.cta": "Agħlaq", "cardWithdraw.success.subtitle": "<PERSON>ħ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, l-irtirar jieħu 3 minuti biex jitlesta", "cardWithdraw.success.title": "Dan it-tibdil se jieħu 3 minuti", "card_top_up_trx.send": "Se tibgħat", "card_top_up_trx.to": "<PERSON><PERSON>", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "Data tal-iskadenza", "cards.card_number": "<PERSON><PERSON><PERSON> tal-kard", "cards.choose-wallet.no-active-accounts": "M'għandekx kartieri attivi", "cards.copied_card_number": "<PERSON><PERSON><PERSON> tal-kard i<PERSON>", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Qbiżt il-limitu ta' kuljum", "cards.transactions.decline_reason.incorrect_pin": "PIN mhux korrett", "cards.transactions.decline_reason.incorrect_security_code": "Kodiċi tas-sigurtà mhux korrett", "cards.transactions.decline_reason.invalid_amount": "Ammont invalidu", "cards.transactions.decline_reason.low_balance": "Bilanċ baxx", "cards.transactions.decline_reason.other": "Miċħud", "cards.transactions.decline_reason.pin_tries_exceeded": "Inqabżu l-provi tal-PIN", "cards.transactions.status.refund": "Rifużjoni", "cards.transactions.status.reversal": "Treġġigħ lura", "cashback-deposit.trx.title": "Iddepożita fi Flus <PERSON>", "cashback-estimate.text": "Din hi stima u MHUX ħlas garantit. Ir-regol<PERSON> pubbliċi kollha tal-flus lura jiġu applikati, imma Gnosis Pay jista' jeskludi tranżazzjonijiet kif jidhirlu. Nefqa massima ta' {amount} fil-ġimgħa tikkwalifika għal flus lura anke jekk l-istima għal din it-tranżazzjoni tindika ammont totali ogħla.", "cashback-estimate.text.fallback": "Din hija stima u mhux ħlas garantit. Ir-regoli pubbliċi kollha tal-flus lura jiġu applikati, iżda Gnosis Pay jista' jeskludi tranżazzjonijiet skont id-diskrezzjoni tiegħu.", "cashback-estimate.title": "Stima tal-flus lura", "cashback-onbarding-tersm.subtitle": "Id-data tat-tranżazzjonijiet tal-Kard tiegħek se tinqasam ma' <PERSON>, li huma responsabbli għat-tqassim tal-premjijiet ta' Flus Lura. Billi tikk<PERSON>ja 'aċċetta' tkun qed taqbel mat- <terms>Termini u Kundizzjonijiet</terms>", "cashback-onbarding-tersm.title": "<PERSON><PERSON><PERSON> tal-u<PERSON><PERSON> u <PERSON>zza", "cashback-tx-activity.retry": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "cashback-unconfirmed-payments-info.subtitle": "Il-pagamenti jikkwalifikaw għal Flus Lura meta jiġu saldati man-negozjant. Sadanittant jidhru bħala pagamenti mhux ikkonfermati. Pagamenti mhux saldati ma jikkwalifikawx għal flus lura.", "cashback-unconfirmed-payments-info.title": "Pagamenti tal-kard mhux ikkonfermati", "cashback.activity.cashback": "Flus lura", "cashback.activity.deposit": "Depożitu", "cashback.activity.title": "Attività riċenti", "cashback.activity.withdrawal": "<PERSON><PERSON><PERSON><PERSON>", "cashback.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.deposit.amount.label": "Ammont tad-depożitu", "cashback.deposit.change": "{from} għal {to}", "cashback.deposit.confirmation.subtitle": "Ir-rati tal-Flus Lura jaġġornaw darba fil-ġimgħa. Iddepożita issa biex iżżid il-Flus Lura tal-ġimgħa d-dieħla.", "cashback.deposit.confirmation.title": "Se tibda taqla' {percentage} fi {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Biddel it-tokens f' {currency} fuq {network} Chain", "cashback.deposit.get.tokens.title": "Ikseb {currency} tokens", "cashback.deposit.header": "Iddepożita {currency}", "cashback.deposit.max_label": "Mass: {amount}", "cashback.deposit.select-wallet.title": "Agħżel wallet minn fejn se tiddepożita", "cashback.deposit.yourcashback": "Il-Flus Lura Tiegħek", "cashback.header": "Flus lura", "cashback.selectWithdrawWallet.title": "Agħżel wallet fejn{br}se tirtira", "cashback.transaction-details.network-label": "Network", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "<PERSON>n", "cashback.transaction-details.top-row.label-rewards": "<PERSON><PERSON><PERSON> tal-flus lura", "cashback.transaction-details.top-row.label-withdrawal": "Għal", "cashback.transaction-details.transaction": "ID tat-tranżazzjoni", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Tranżazzjonijiet tal-flus lura", "cashback.withdraw": "<PERSON><PERSON><PERSON>", "cashback.withdraw.confirmation.cashback_reduction": "Il-Flus Lura għal din il-ġ<PERSON>ħa, <PERSON><PERSON><PERSON> dak li di<PERSON> qlajt, se jonqos minn {before} għal {after}", "cashback.withdraw.queued": "<PERSON><PERSON><PERSON><PERSON> fil-kju", "cashback.withdrawal.change": "{from} għal {to}", "cashback.withdrawal.confirmation.subtitle": "<PERSON><PERSON><PERSON> l-irtirar ta' {amount} b'dewmien ta' 3 minuti. <PERSON> se jnaqqas il-flus lura tiegħek għal {after}.", "cashback.withdrawal.confirmation.title": "Il-Flus Lura jonqsu jekk tirtira GNO", "cashback.withdrawal.delayTransaction.title": "<PERSON><PERSON><PERSON> l-irtirar ta' GNO bi{br} ddewmien ta' 3 minuti", "cashback.withdrawal.withdraw": "<PERSON><PERSON><PERSON>", "cashback.withdrawal.yourcashback": "Il-Flus Lura Tiegħek", "celebration.aave": "<PERSON><PERSON><PERSON><PERSON>", "celebration.cashback.subtitle": "Mogħtija fi {code}", "celebration.cashback.subtitleGNO": "{amount} maq<PERSON>ħa l-aħħar", "celebration.chf": "Q<PERSON><PERSON>ħ minn <PERSON>", "celebration.lido": "<PERSON><PERSON><PERSON><PERSON> Li<PERSON>", "celebration.sky": "Q<PERSON>għ ma' Sky", "celebration.title": "Flus Lura Totali", "celebration.well_done.title": "Prosit!", "change-withdrawal-account.add-new-account": "Żid kont bankarju ieħor", "change-withdrawal-account.item.shortText": "{currency} Kont", "check-confirmation.approve.footer.for": "Għal", "checkConfirmation.title": "Riżultat tat-tranżazzjoni", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin & Ether", "collateral.ethereum": "Ethereum", "collateral.other": "<PERSON><PERSON><PERSON><PERSON>", "collateral.rwa": "Assi Reali", "collateral.stablecoins": "Stablecoins (marbuta mal-USD)", "collateral.us-t-bills": "US T-Bills", "confirm-bank-transfer-recipient.bullet-1": "Ebda tariffi fuq EUR diġitali", "confirm-bank-transfer-recipient.bullet-2": "Depożiti lil {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Aqsam id-dettalji tal-kont Gnosis Pay ma' Monerium, EMI awtorizzata u regolata. <link>Sir af aktar</link>", "confirm-bank-transfer-recipient.bullet-4": "Aċċetta Monerium <link>termini tas-servizz</link>", "confirm-bank-transfer-recipient.title": "Aċċetta t-termini", "confirm-change-withdrawal-account.cancel": "Ikkanċella", "confirm-change-withdrawal-account.confirm": "Ikkonferma", "confirm-change-withdrawal-account.saving": "<PERSON><PERSON> is<PERSON>", "confirm-change-withdrawal-account.subtitle": "<PERSON><PERSON> i<PERSON><PERSON>r minn <PERSON> jidħol f'dan il-kont", "confirm-change-withdrawal-account.title": "Ibdel il-bank li jirċievi", "confirm-ramove-withdrawal-account.title": "Neħħi l-kont bankarju", "confirm-remove-withdrawal-account.subtitle": "Il-kont se jitneħħa. Tista' żżidu lura.", "confirmTransaction.finalNetworkFee": "Tariffa tan-netwerk", "confirmTransaction.importKeys": "Importa ċ-ċwievet", "confirmTransaction.networkFee": "Tariffa tan-netwerk", "confirmation.title": "Ibgħat {amount} lil {recipient}", "conflicting-monerium-account.add-owner": "<PERSON><PERSON> b<PERSON>ala Sid ta' Gnosis Pay", "conflicting-monerium-account.create-wallet": "Oħloq smart wallet ġdida", "conflicting-monerium-account.disconnect-card": "Skonnettja l-kard minn <PERSON> u erġa' kkonnettja mas-sid <PERSON>", "conflicting-monerium-account.header": "{wallet} marbut ma' kont ieħor ta' Monerium", "conflicting-monerium-account.subtitle": "Biddel il-wallet tiegħek bħala sid ta' Gnosis Pay", "connection.diconnected.got_it": "Fhimt!", "connection.diconnected.page1.subtitle": "Zeal ja<PERSON><PERSON>m bħal Metamask. Ikkonnettja.", "connection.diconnected.page1.title": "<PERSON><PERSON> t<PERSON>?", "connection.diconnected.page2.subtitle": "Tara ħafna għażliet. Jekk ma tarax Zeal...", "connection.diconnected.page2.title": "Agħfas 'Connect Wallet'", "connection.diconnected.page3.subtitle": "<PERSON><PERSON>na nqabbduk ma' Zeal. Ipprova!", "connection.diconnected.page3.title": "Agħ<PERSON>el <PERSON>", "connectionSafetyCheck.tag.caution": "<PERSON><PERSON>z<PERSON><PERSON>", "connectionSafetyCheck.tag.danger": "<PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.passed": "Għadda", "connectionSafetyConfirmation.subtitle": "<PERSON><PERSON>r li trid tkompli?", "connectionSafetyConfirmation.title": "Dan is-sit jidh<PERSON>", "connection_state.connect.cancel": "Ikkanċella", "connection_state.connect.changeToMetamask": "Aqleb għal MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Aq<PERSON>b għal MetaMask", "connection_state.connect.connect_button": "Qabbad", "connection_state.connect.expanded.connected": "<PERSON><PERSON><PERSON>", "connection_state.connect.expanded.title": "Ikkonnettja", "connection_state.connect.safetyChecksLoading": "Qed niċċekkjaw is-sigurtà tas-sit", "connection_state.connect.safetyChecksLoadingError": "Ma stajniex nagħmlu l-verifiki tas-sigurtà", "connection_state.connected.expanded.disconnectButton": "<PERSON><PERSON><PERSON><PERSON>", "connection_state.connected.expanded.title": "<PERSON><PERSON><PERSON>", "copied-diagnostics": "Id-dijanjostika ġiet i<PERSON>kupjata", "copy-diagnostics": "Ikkopja d-dijanjostika", "counterparty.component.add_recipient_primary_text": "Żid riċevitur bankarju", "counterparty.country": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.countryTitle": "Pajjiż tar-riċevitur", "counterparty.currency": "<PERSON><PERSON><PERSON>", "counterparty.delete.success.title": "<PERSON><PERSON><PERSON>ħħ<PERSON>", "counterparty.edit.success.title": "<PERSON><PERSON><PERSON><PERSON> salvati", "counterparty.errors.country_required": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.errors.first_name.invalid": "L-isem għandu jkun itwal", "counterparty.errors.last_name.invalid": "Il-kunjom għandu jkun itwal", "counterparty.first_name": "<PERSON><PERSON>", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Ibgħat lill-bank", "countrySelector.noCountryFound": "Ma nstab l-ebda paj<PERSON>ż", "countrySelector.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create-passkey.cta": "Oħloq passkey", "create-passkey.extension.cta": "<PERSON><PERSON><PERSON>", "create-passkey.footnote": "Bis-saħħa ta'", "create-passkey.mobile.cta": "Ibda s-Setup tas-Sigurtà", "create-passkey.steps.enable-recovery": "Stabbilixxi l-irkupru mill-cloud", "create-passkey.steps.setup-biometrics": "Attiva s-sigurtà bijometrika", "create-passkey.subtitle": "Il-passkeys huma aktar siguri mill-passwords, u jkunu kriptati fil-cloud biex tirkuprahom faċilment.", "create-passkey.title": "Assigura l-kont", "create-smart-wallet": "Oħloq Smart wallet", "create-userop.progress.text": "<PERSON><PERSON>", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Kompli f'Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Mur fuq Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "Bdejt ordni tal-kard. Mur fuq Gnosis Pay biex tlestiha.", "create_recharge_preferences.card": "<PERSON><PERSON>", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "Aqla' {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "Żomm il-flus", "create_recharge_preferences.link_accounts_title": "Ikkonnettja l-kontijiet", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "Il<PERSON><PERSON>rd tiegħek MHUX se tiċċarġja awtomatikament wara kull pagament.", "create_recharge_preferences.not_configured_title": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "create_recharge_preferences.recharge_from_earn_accounts_description": "Il-kard tiegħek tiċċarġja awtomatikament wara kull pagament mill-kont Earn tiegħek.", "create_recharge_preferences.subtitle": "fis-sena", "creating-account.loading": "<PERSON><PERSON> ji<PERSON><PERSON><PERSON> il-kont", "creating-gnosis-pay-account": "<PERSON><PERSON> ji<PERSON><PERSON><PERSON> il-kont", "currencies.bridge.select_routes.emptyState": "Ma sibna l-ebda rotta għal dan il-Pont", "currency.add_currency.add_token": "Żid token", "currency.add_currency.not_a_valid_address": "Dan mhuwiex indirizz validu għat-token", "currency.add_currency.token_decimals_feild": "Deċimali tat-token", "currency.add_currency.token_feild": "Indirizz tat-token", "currency.add_currency.token_symbol_feild": "Simbolu tat-token", "currency.add_currency.update_token": "Aġġorna t-token", "currency.add_custom.remove_token.cta": "Neħħi token", "currency.add_custom.remove_token.header": "Neħħi token", "currency.add_custom.remove_token.subtitle": "Il-kartiera tiegħek se żżomm il-bilanċ ta' din it-token, imma se tinħeba mill-portfolio tiegħek ta' Zeal.", "currency.add_custom.token_removed": "<PERSON><PERSON> mne<PERSON>ħi", "currency.add_custom.token_updated": "Token aġġornat", "currency.balance_label": "Bilanċ: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "It-trasferiment bankarju tiegħek ittrasferixxa b'suċċess {fiat} għal {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "Irċevejt {crypto}", "currency.bankTransfer.deposit_status.success": "Irċevuti fil-kartiera tiegħek", "currency.bankTransfer.deposit_status.title": "Depożitu", "currency.bankTransfer.off_ramp.check_bank_account": "Iċċekkja l-kont bankarju tiegħek", "currency.bankTransfer.off_ramp.complete": "Les<PERSON>", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "<PERSON><PERSON> jintbag<PERSON>tu lill-bank tiegħek", "currency.bankTransfer.off_ramp.transferring_to_currency": "<PERSON><PERSON> jint<PERSON> għal {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Il-fondi messhom waslu fil-kont bankarju tiegħek sa issa.", "currency.bankTransfer.withdrawal_status.success": "Mibgħuta lill-bank tiegħek", "currency.bankTransfer.withdrawal_status.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.email": "Indirizz tal-email", "currency.bank_transfer.create_unblock_user.email_invalid": "<PERSON><PERSON> invalida", "currency.bank_transfer.create_unblock_user.email_missing": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name": "<PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_missing": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "<PERSON><PERSON>a biss ittri, numri u - . , & ( ) '", "currency.bank_transfer.create_unblock_user.last_name": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_missing": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "<PERSON><PERSON>a biss ittri, numri u - . , & ( ) '", "currency.bank_transfer.create_unblock_user.note": "Billi tkompli taċċetta t- <terms>Termini</terms> u l- <policy>Politika tal-Privatezza</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Ikteb ismek kif inhu fil-kont tal-bank", "currency.bank_transfer.create_unblock_user.title": "Irbot il-kont bankarju tiegħek", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "<PERSON><PERSON><PERSON> tal-kont", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Pajjiż tal-bank", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Valuta preferuta", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Sort code", "currency.bank_transfer.create_unblock_withdraw_account.success": "Il-kont ġie stabbilit", "currency.bank_transfer.create_unblock_withdraw_account.title": "Orbot il-kont bankarju tiegħek", "currency.bank_transfer.residence-form.address-required": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.address-unsupported-char": "<PERSON>ża biss ittri, numri, spazji, u , ; {apostrophe} - \\\\ .", "currency.bank_transfer.residence-form.city-required": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.city-unsupported-char": "<PERSON>ża biss ittri, numri, spazji, u . , - & ( ) {apostrophe} .", "currency.bank_transfer.residence-form.postcode-invalid": "Kodiċi postali invalidu", "currency.bank_transfer.residence-form.postcode-required": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.validation.invalid.account_number": "<PERSON><PERSON><PERSON> tal-kont <PERSON>u", "currency.bank_transfer.validation.invalid.iban": "IBAN invalidu", "currency.bank_transfer.validation.invalid.sort_code": "Sort code invalidu", "currency.bridge.amount_label": "Ammont għall-Pont", "currency.bridge.best_returns.subtitle": "Dan il-provider tal-Pont se jagħtik l-ogħla qligħ, inklużi t-tariffi kollha.", "currency.bridge.best_returns_popup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> qligħ", "currency.bridge.bridge_from": "<PERSON>n", "currency.bridge.bridge_gas_fee_loading_failed": "Kellna problemi biex intellgħu t-tariffa tan-netwerk", "currency.bridge.bridge_low_slippage": "Slippage baxx ħafna. <PERSON><PERSON>rov<PERSON>", "currency.bridge.bridge_provider": "Fornitur tat-trasferiment", "currency.bridge.bridge_provider_loading_failed": "Kellna problemi biex nitellgħu l-fornituri", "currency.bridge.bridge_settings": "Setting<PERSON> tal-Pont", "currency.bridge.bridge_status.subtitle": "<PERSON><PERSON> tu<PERSON> {name}", "currency.bridge.bridge_status.title": "<PERSON>", "currency.bridge.bridge_to": "<PERSON><PERSON>", "currency.bridge.fastest_route_popup.subtitle": "Dan il-provider tal-Pont se jagħtik l-aktar rotta ta' tranżazzjoni mgħaġġla.", "currency.bridge.fastest_route_popup.title": "L-aktar rotta mgħaġġla", "currency.bridge.from": "<PERSON>n", "currency.bridge.success": "Les<PERSON>", "currency.bridge.title": "<PERSON>", "currency.bridge.to": "Għal", "currency.bridge.topup": "Agħmel top-up ta' {symbol}", "currency.bridge.withdrawal_status.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.card.card_top_up_status.title": "<PERSON><PERSON> il-flus mal-kard", "currency.destination_amount": "Ammont tad-destinazzjoni", "currency.hide_currency.confirm.subtitle": "<PERSON><PERSON><PERSON> din it-token mill-portfolio tiegħek. Tista' terġa' turieha meta trid.", "currency.hide_currency.confirm.title": "Aħbi t-token", "currency.hide_currency.success.title": "<PERSON><PERSON> mo<PERSON>bi", "currency.label": "Tikketta (Mhux obbligatorja)", "currency.last_name": "<PERSON><PERSON><PERSON><PERSON>", "currency.max_loading": "Mass.:", "currency.swap.amount_to_swap": "Ammont għas-swap", "currency.swap.best_return": "L-a<PERSON><PERSON> rotta ta' redditu", "currency.swap.destination_amount": "Ammont tad-destinazzjoni", "currency.swap.header": "<PERSON><PERSON><PERSON>", "currency.swap.max_label": "Bilanċ: {amount}", "currency.swap.provider.header": "Provider tal-iswap", "currency.swap.select_to_token": "Agħ<PERSON>el token", "currency.swap.swap_gas_fee_loading_failed": "Kellna problemi biex intellgħu t-tariffa tan-netwerk", "currency.swap.swap_provider_loading_failed": "Kellna problemi biex intellgħu l-providers", "currency.swap.swap_settings": "Settings tas-swap", "currency.swap.swap_slippage_too_low": "Slippage baxx ħafna. Ipprova żidu.", "currency.swaps_io_native_token_swap.subtitle": "Qed tuża Swaps.IO", "currency.swaps_io_native_token_swap.title": "Ibgħat", "currency.withdrawal.amount_from": "<PERSON>n", "currency.withdrawal.amount_to": "<PERSON>", "currencySelector.title": "Agħżel munita", "dApp.wallet-does-not-support-chain.subtitle": "Il-kartiera tiegħek jidher li ma tappoġġjax {network}. <PERSON><PERSON>rova qabbad ma' kartiera differenti, jew u<PERSON><PERSON> minflo<PERSON>.", "dApp.wallet-does-not-support-chain.title": "Network mhux appoġġjat", "dapp.connection.manage.confirm.disconnect.all.cta": "Neħħi kollha", "dapp.connection.manage.confirm.disconnect.all.subtitle": "<PERSON><PERSON><PERSON> li tiskon<PERSON> koll<PERSON>?", "dapp.connection.manage.confirm.disconnect.all.title": "Skonnett<PERSON> koll<PERSON>", "dapp.connection.manage.connection_list.main.button.title": "<PERSON><PERSON><PERSON><PERSON>", "dapp.connection.manage.connection_list.no_connections": "M'għandekx apps konnessi", "dapp.connection.manage.connection_list.section.button.title": "Skonnett<PERSON> koll<PERSON>", "dapp.connection.manage.connection_list.section.title": "<PERSON><PERSON><PERSON>", "dapp.connection.manage.connection_list.title": "Konnessjonijiet", "dapp.connection.manage.disconnect.success.title": "<PERSON><PERSON>", "dapp.metamask_mode.title": "Modalità MetaMask", "dc25-card-marketing-card.center.subtitle": "Flus lura", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "Imgħax", "dc25-card-marketing-card.right.subtitle": "100 persuna", "dc25-card-marketing-card.title": "L-ewwel 100 li jonfqu €50 jaqilgħu {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Mhux se tkun tista' tagħmel din l-azzjoni għal 3 min. Għal raġunijiet ta' sigurtà, kwalunkwe bidla fis-settings tal-kard jew irtirar jieħdu 3 minuti biex jiġu proċessati.", "delayQueueBusyBanner.processing-yout-action.title": "Qed nipproċessaw l-a<PERSON><PERSON><PERSON>, jekk jogħġ<PERSON>k stenna", "delayQueueBusyWidget.cardFrozen": "<PERSON><PERSON><PERSON><PERSON>", "delayQueueBusyWidget.processingAction": "Qed nipproċessaw l-azz<PERSON>i tiegħek", "delayQueueFailedBanner.action-incomplete.get-support": "<PERSON><PERSON><PERSON>", "delayQueueFailedBanner.action-incomplete.subtitle": "<PERSON><PERSON><PERSON><PERSON>, xi ħaġa marret ħa<PERSON>in bl-i<PERSON><PERSON>r jew l-aġġornament tas-settings tiegħek. Jekk jogħġbok, ikkuntatt<PERSON> s-support fuq Discord.", "delayQueueFailedBanner.action-incomplete.title": "Azzjoni mhux kompluta", "delayQueueFailedWidget.actionIncomplete.title": "<PERSON><PERSON><PERSON><PERSON> fuq il-kard mhux kompluta", "delayQueueFailedWidget.cardFrozen.subtitle": "<PERSON><PERSON><PERSON><PERSON>", "delayQueueFailedWidget.contactSupport": "Ikkuntattja lis-sapport", "delay_queue_busy.subtitle": "Għal raġunijiet ta' sigurt<PERSON>, kull bidla fis-settings tal-kard jew irtirar jieħu 3 minuti biex jiġi pproċessat. Matul dan i<PERSON>, il-kard tiegħek tkun iffriżata.", "delay_queue_busy.title": "L-azzjoni tiegħek qed tiġi pproċessata", "delay_queue_failed.contact_support": "Ikkuntattja", "delay_queue_failed.subtitle": "<PERSON><PERSON><PERSON><PERSON>, xi ħaġa marret ħa<PERSON>in bl-i<PERSON><PERSON>r jew l-aġġornament tas-settings tiegħek. Jekk jogħġbok, ikkuntatt<PERSON> s-support fuq Discord.", "delay_queue_failed.title": "Ikkuntattja s-support", "deploy-earn-form-smart-wallet.in-progress.title": "Inħejju l-Earn", "deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disconnect-card-popup.cancel": "Ikkanċella", "disconnect-card-popup.disconnect": "<PERSON><PERSON><PERSON><PERSON>", "disconnect-card-popup.subtitle": "<PERSON> ineħ<PERSON>i l-Kard tiegħek mill-app ta' Zeal. Il-kartiera tiegħek tibqa' konnessa mal-kard tiegħek fl-app ta' Gnosis Pay. Tista' terġa' tikkonnettja l-Kard tiegħek meta trid.", "disconnect-card-popup.title": "Skonnettja l-kard", "distance.long.days": "{count} ijiem", "distance.long.hours": "{count} sigħat", "distance.long.minutes": "{count} minuti", "distance.long.months": "{count} xhur", "distance.long.seconds": "{count} sekondi", "distance.long.years": "{count} snin", "distance.short.days": "{count} j", "distance.short.hours": "{count} s", "distance.short.minutes": "{count} min", "distance.short.months": "{count} x", "distance.short.seconds": "{count} sek", "distance.short.years": "{count} sn", "duration.short.days": "{count}j", "duration.short.hours": "{count}s", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}sek", "earn-deposit-view.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-deposit-view.into": "Fi", "earn-deposit-view.to": "<PERSON><PERSON>", "earn-deposit.swap.transfer-provider": "Fornitur tat-trasferiment", "earn-taker-investment-details.accrued-realtime": "Jin<PERSON><PERSON>' f'ħin reali", "earn-taker-investment-details.asset-class": "Klassi tal-assi", "earn-taker-investment-details.asset-coverage-ratio": "Proporzjon tal-kopertura tal-assi", "earn-taker-investment-details.asset-reserve": "Riżerva tal-assi", "earn-taker-investment-details.base_currency.label": "Valuta bażi", "earn-taker-investment-details.chf.description": "A<PERSON><PERSON>' imgħax fuq is-CHF tiegħek billi tiddepożita zCHF fi Frankencoin, suq tal-flus diġitali affidabbli. L-imgħax jiġi minn self b'riskju baxx u b'kollateral żejjed fuq <PERSON>, u jitħallas f'ħin reali. Il-fondi tiegħek jibqgħu protetti f'sub-account sigur li tikkontrolla int biss.", "earn-taker-investment-details.chf.description.with_address_link": "A<PERSON><PERSON>' imgħax fuq is-CHF tiegħek billi tiddepożita zCHF fi Frankencoin, suq tal-flus diġitali affidabbli. L-imgħax jiġi minn self b'riskju baxx u b'kollateral żejjed fuq <PERSON>, u jitħallas f'ħin reali. Il-fondi tiegħek jibqgħu protetti f'sub-account sigur <link>(ikkopja 0x)</link> li tikkontrolla int biss.", "earn-taker-investment-details.chf.label": "<PERSON>", "earn-taker-investment-details.collateral-composition": "Kompożizzjoni tal-kollateral", "earn-taker-investment-details.depositor-obligations": "Obbligi tad-depożitant", "earn-taker-investment-details.eure.description": "Aqla' mgħax fuq l-ewro tiegħek billi tiddepożita EURe f'Aave - suq tal-flus diġitali affidabbli. L-EURe hija stablecoin tal-ewro regolata għalkollox, maħruġa minn Monerium u sostnuta 1:1 f'kontijiet protetti. L-imgħax jiġi minn self b'riskju baxx u b'kollateral żejjed fuq Aave u jitħallas f'ħin reali. Il-fondi tiegħek jibqgħu f'sottokont sigur li tikkontrolla int biss.", "earn-taker-investment-details.eure.description.with_address_link": "Aqla' mgħax fuq l-ewro tiegħek billi tiddepożita EURe f'Aave - suq tal-flus diġitali affidabbli. L-EURe hija stablecoin tal-ewro regolata għalkollox, maħruġa minn Monerium u sostnuta 1:1 f'kontijiet protetti. L-imgħax jiġi minn self b'riskju baxx u b'kollateral żejjed fuq Aave u jitħallas f'ħin reali. Il-fondi tiegħek jibqgħu f'sottokont sigur <link>(ikkopja 0x)</link> li tikkontrolla int biss.", "earn-taker-investment-details.eure.label": "<PERSON><PERSON><PERSON> (EURe)", "earn-taker-investment-details.faq": "FAQ", "earn-taker-investment-details.fixed-income": "Introjtu fiss", "earn-taker-investment-details.issuer": "Emittent", "earn-taker-investment-details.key-facts": "<PERSON><PERSON>", "earn-taker-investment-details.liquidity": "Likwidità", "earn-taker-investment-details.operator": "Operatur tas-Suq", "earn-taker-investment-details.projected-yield": "Rendiment annwali pro<PERSON>t", "earn-taker-investment-details.see-other-faq": "Ara l-FAQs l-oħra kollha", "earn-taker-investment-details.see-realtime": "<PERSON> d-dejta f'ħin reali", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Rendiment tal-aħħar 12-il xahar", "earn-taker-investment-details.total-collateral": "Kollateral Totali", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "Provvista Totali ta' ZCHF", "earn-taker-investment-details.total_deposits": "Depożiti totali f'Aave", "earn-taker-investment-details.usd.description": "Sky huwa suq tal-flus diġitali li joffri rendimenti stabbli f'dollari Amerikani minn U.S. Treasuries għal żmien qasir u self b'kollateral żejjed—mingħajr volatilità tal-kripto, b'aċċess għall-fondi 24/7, u b'sostenn trasparenti on-chain.", "earn-taker-investment-details.usd.description.with_address_link": "Sky huwa suq tal-flus diġitali li joffri rendimenti stabbli f'dollari Amerikani minn U.S. Treasuries għal żmien qasir u self b'kollateral żejjed—mingħajr volatilità tal-kripto, b'aċċess għall-fondi 24/7, u b'sostenn trasparenti on-chain. L-investimenti huma f'sottokont <link>(ikkopja 0x)</link> ikkontrollat minnek.", "earn-taker-investment-details.usd.ftx-difference": "<PERSON> b'hiex huwa differenti minn <PERSON>T<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, jew <PERSON>?", "earn-taker-investment-details.usd.high-returns": "Ir-rendimenti kif jistgħu j<PERSON>nu <PERSON> għoljin, speċjalment meta mqabbla mal-banek tradizzjonali?", "earn-taker-investment-details.usd.how-is-backed": "Sky USD kif inhu sostnut, u x'ji<PERSON>ri minn flusi jekk <PERSON> j<PERSON>?", "earn-taker-investment-details.usd.income-sources": "Sorsi ta' introjtu 2024", "earn-taker-investment-details.usd.insurance": "Il-fondi tiegħi huma assigurati jew garantiti minn xi entità (bħall-FDIC jew simili)?", "earn-taker-investment-details.usd.label": "Dollaru Amerikan Diġitali", "earn-taker-investment-details.usd.lose-principal": "Realistikament nista' nitlef il-kapital tiegħi, u f'liema ċirkostanzi?", "earn-taker-investment-details.variable-rate": "Self b'rata var<PERSON><PERSON>", "earn-taker-investment-details.withdraw-anytime": "<PERSON><PERSON><PERSON> f'kull ħin", "earn-taker-investment-details.yield": "Rendiment", "earn-withdrawal-view.approve.for": "Għal", "earn-withdrawal-view.approve.into": "Fi", "earn-withdrawal-view.swap.into": "Fi", "earn-withdrawal-view.withdraw.to": "<PERSON><PERSON>", "earn.add_another_asset.title": "Agħżel asset għall-qligħ", "earn.add_asset": "<PERSON><PERSON> assi", "earn.asset_view.title": "<PERSON><PERSON><PERSON><PERSON>", "earn.base-currency-popup.text": "Il-valuta bażi hija kif id-depożiti, ir-rendi<PERSON>, u t-transazzjonijiet tiegħek jiġu vvalutati u rreġistrati. Jekk tiddepożita f'valuta differenti (bħal EUR f'USD), il-fondi tiegħek jiġu kkonvertiti immedjatament fil-valuta bażi bir-rati tal-kambju attwali. Wara l-konverżjoni, il-bilanċ tiegħek jibqa' stabbli fil-valuta bażi, iżda meta tiġbed il-flus fil-futur jista' jkun hemm bżonn ta' konverżjonijiet oħra.", "earn.base-currency-popup.title": "Valuta bażi", "earn.card-recharge.disabled.list-item.title": "Auto-recharge diżattivat", "earn.card-recharge.enabled.list-item.title": "Auto-recharge attivat", "earn.choose_wallet_to_deposit.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> minn", "earn.config.currency.eth": "Aqla' Ethereum", "earn.config.currency.on_chain_address_subtitle": "Indirizz onchain", "earn.config.currency.us_dollars": "Stabbilixxi t-trasferimenti bankarji", "earn.configured_widget.current_apy.title": "APY attwali", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} Ann<PERSON><PERSON>", "earn.confirm.currency.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.currency.eth": "Aqla' Ethereum", "earn.deploy.status.title": "Oħloq kont Earn", "earn.deploy.status.title_with_taker": "O<PERSON><PERSON><PERSON> {title} kont <PERSON>arn", "earn.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.deposit.amount_to_deposit": "Ammont għad-depożitu", "earn.deposit.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.deposit.enter_amount": "Daħħal Ammont", "earn.deposit.no_routes_found": "<PERSON> nstabu l-ebda rotot", "earn.deposit.not_enough_balance": "M'għandekx biżżejjed bilanċ", "earn.deposit.select-currency.title": "Agħżel token biex tiddepożita", "earn.deposit.select_account.title": "Agħżel kont Earn", "earn.desposit_form.title": "Iddepożita f'Earn", "earn.earn_deposit.status.title": "Iddepożita f'Earn", "earn.earn_deposit.trx.title": "Iddepożita f'Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "<PERSON><PERSON><PERSON> l-fondi f'kull ħin", "earn.earn_withdraw.status.title": "Iġbed mill-kont Earn", "earn.earn_withdraw.trx.title.approval": "Approva l-irtirar", "earn.earn_withdraw.trx.title.withdraw_into_asset": "<PERSON><PERSON><PERSON> għ<PERSON> {asset}", "earn.earn_withdraw.trx.title.withdrawal": "<PERSON><PERSON><PERSON> minn <PERSON>n", "earn.recharge.cta": "Issejvja t-tibdil", "earn.recharge.earn_not_configured.enable_some_account.error": "Attiva l-kont", "earn.recharge.earn_not_configured.enter_amount.error": "Daħħal l-ammont", "earn.recharge.select_taker.header": "Irriċarġja l-kard f'din l-ordni minn", "earn.recharge_card_tag.on": "mixgħul", "earn.recharge_card_tag.recharge": "Top-up", "earn.recharge_card_tag.recharge_not_configured": "Top-up <PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharge_off": "Top-up mitfi", "earn.recharge_card_tag.recharged": "Top-up sar", "earn.recharge_card_tag.recharging": "Qed isir it-top-up", "earn.recharge_configured.disable.trx.title": "Iddiżattiva l-Auto-Recharge", "earn.recharge_configured.trx.disclaimer": "Meta tħallas bil-kard, aħna nużaw l-assi tal-Earn tiegħek biex nixtru lura l-istess ammont. Dan il-proċess normalment jagħtik l-a<PERSON><PERSON> rata, imma r-rata onlajn tista' tkun differenti mir-rati tal-kambju reali.", "earn.recharge_configured.trx.subtitle": "<PERSON>a kull pagament, il-flus se jiżdiedu awtomatikament mill-kont(ijiet) Earn tiegħek biex iżommu l-bilanċ tal-kard tiegħek fuq {value}", "earn.recharge_configured.trx.title": "Issettja l-Auto-Recharge għal {value}", "earn.recharge_configured.updated.trx.title": "Issejvja s-Settings tar-Recharge", "earn.risk-banner.subtitle": "Dan huwa prodott ta' kartiera privata mingħajr protezzjoni regolatorja kontra t-telf.", "earn.risk-banner.title": "<PERSON><PERSON> ir-<PERSON><PERSON>", "earn.set_recharge.status.title": "Issettja l-Auto-Recharge", "earn.setup_reacharge.input.disable.label": "Iddiżattiva", "earn.setup_reacharge.input.label": "Bilanċ fil-mira tal-kard", "earn.setup_reacharge_form.title": "L-Auto-Recharge iżomm il-kard tiegħek{br} bl-istess bilanċ", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Żomm wstETH fuq Gnosis, selfu b'<PERSON><PERSON>.", "earn.taker-bulletlist.point_1": "Aqla' {apyValue} fis-sena. Ir-redditu j<PERSON>ja mas-suq.", "earn.taker-bulletlist.point_3": "Zeal ma jimponi l-ebda tariffa.", "earn.taker-historical-returns": "Redditu Storiku", "earn.taker-historical-returns.chf": "Tkabbir tas-CHF għal USD", "earn.taker-investment-tile.apy.perYear": "fis-sena", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "<PERSON><PERSON><PERSON><PERSON> minn <PERSON>ave fuq Gnosis Chain", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Qligħ minn <PERSON>en<PERSON>in fuq Gnosis Chain", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Qligħ minn Lido fuq Gnosis Chain", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "<PERSON><PERSON><PERSON><PERSON> minn Maker fuq Gnosis Chain", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "Q<PERSON>g<PERSON>", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "Qligħ ETH", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "US Treasuries (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Għalkemm rari ħafna, teoretikament huwa possibbli. Il-fondi tiegħek huma protetti minn ġestjoni stretta tar-riskju u kollateralizzazzjoni għolja. L-agħar xenarju realistiku jkun jinvolvi kundizzjonijiet tas-suq bla preċedent, bħal li diversi stablecoins jitilfu l-valur tagħhom fl-istess ħin—xi ħaġa li qatt ma ġrat qabel.", "earn.usd.can-I-lose-my-principal-popup.title": "Realistikament nista' nitlef il-kapital tiegħi, u f'liema ċirkostanzi?", "earn.usd.ftx-difference-popup.text": "Sky huwa fundamentalment differenti. B'differenza minn FTX, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, jew <PERSON>—li kienu jiddependu ħafna fuq kura ċentralizzata, ġ<PERSON>joni tal-assi mhux trasparenti, u pożizzjonijiet riskjużi—Sky USD juża smart contracts deċentralizzati, verifikati u trasparenti, u jżomm trasparenza sħiħa on-chain. Inti żżomm kontroll privat sħiħ, u b'hekk tnaqqas b'mod sinifikanti r-riskji assoċjati ma' fallimenti ċentralizzati.", "earn.usd.ftx-difference-popup.title": "<PERSON> b'hiex huwa differenti minn <PERSON>T<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, jew <PERSON>?", "earn.usd.high-returns-popup.text": "Sky USD jiġġenera rendimenti primarjament permezz ta' protokolli ta' finanzi deċentralizzati (DeFi), li awtomatizzaw is-self peer-to-peer u l-provvista ta' likwidità, u b'hekk ineħħu l-ispejjeż u l-intermedjarji bankarji tradizzjonali. <PERSON> l-effiċjenza, flimkien ma' kontrolli robusti tar-riskju, tippermetti rendimenti ferm ogħla meta mqabbla mal-banek tradizzjonali.", "earn.usd.high-returns-popup.title": "Ir-rendimenti kif jistgħu j<PERSON>nu <PERSON> għoljin, speċjalment meta mqabbla mal-banek tradizzjonali?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD huwa sostnut u kkollateralizzat b'mod sħiħ b'kombinazzjoni ta' assi diġitali miżmuma fi smart contracts siguri u assi tad-dinja reali bħall-US Treasuries. Ir-riżervi jistgħu jiġu verifikati f'ħin reali onchain anke minn ġew<PERSON> Zeal, u dan jipprovdi trasparenza u sigurtà. Fil-każ improbabbli li Zeal jagħlaq, l-assi tiegħek jibqgħu siguri on-chain, taħt il-kontroll sħiħ tiegħek, u aċċessibbli permezz ta' kartieri oħra kompatibbli.", "earn.usd.how-is-sky-backed-popup.title": "Sky USD kif inhu sostnut, u x'ji<PERSON>ri minn flusi jekk <PERSON> j<PERSON>?", "earn.usd.insurance-popup.text": "Il-fondi ta' Sky USD mhumiex assigurati mill-FDIC jew sostnuti minn garanziji governattivi tradizzjonali għax huwa kont ibbażat fuq assi diġitali, mhux kont bankarju konvenzjonali. <PERSON><PERSON><PERSON><PERSON>, Sky jimmaniġġja t-tnaqqis tar-riskju kollu permezz ta' smart contracts verifikati u protokolli DeFi magħżula bir-reqqa, biex jiżgura li l-assi jibqgħu siguri u trasparenti.", "earn.usd.insurance-popup.title": "Il-fondi tiegħi huma assigurati jew garantiti minn xi entità (bħall-FDIC jew simili)?", "earn.usd.lending-operations-popup.text": "Sky USD jiġġenera rendiment billi jsellef stablecoins permezz ta' swieq tas-self deċentralizzati bħal Morpho u Spark. L-istablecoins tiegħek jissellfu lil min jidħol għad-dejn u jiddepożita kollateral sinifikament akbar—bħal ETH jew BTC—mill-valur tas-self. <PERSON> il<PERSON>, imsej<PERSON><PERSON> overcollateralization, jiżgura li dejjem ikun hemm biżżejjed kollateral biex ikopri s-self, u b'hekk inaqqas ir-riskju ħafna. L-imgħax miġbur u t-tariffi okkażjonali tal-likwidazzjoni mħallsa minn min jissellef jipprovdu rendimenti affidabbli, trasparenti u siguri.", "earn.usd.lending-operations-popup.title": "Operazzjonijiet ta' self", "earn.usd.market-making-operations-popup.text": "Sky USD jaqla' rendiment addizzjonali billi jipparteċipa fi skambji deċentralizzati (AMMs) bħal Curve jew Uniswap. Billi jipprovdi likwidità—ipoġġi l-istablecoins tiegħek f'pools li jiffaċilitaw il-kummerċ tal-kripto—Sky USD jaqbad it-tariffi ġġenerati mill-kummerċ. Dawn il-pools tal-likwidità jintgħażlu bir-reqqa biex jimminimizzaw il-volatilità, primarjament bl-użu ta' pari stablecoin-ma-stablecoin biex jitnaqqsu b'mod sinifikanti riskji bħat-telf impermanenti, u b'hekk l-assi tiegħek jinżammu kemm siguri kif ukoll aċċessibbli.", "earn.usd.market-making-operations-popup.title": "Operazzjonijiet ta' Market Making", "earn.usd.treasury-operations-popup.text": "Sky USD jiġġenera rendiment stabbli u konsistenti permezz ta' investimenti strateġiċi tat-teżor. Parti mid-depożiti tiegħek ta' stablecoin tiġi allokata f'assi siguri u b'riskju baxx fid-dinja reali—primarjament bonds tal-gvern għal żmien qasir u strumenti ta' kreditu siguri ħafna. <PERSON>met<PERSON>, simili għall-ban<PERSON> trad<PERSON>, jiżgura rendiment prevedibbli u affidabbli. L-assi tiegħek jibqgħu siguri, l<PERSON><PERSON><PERSON>, u ġestiti b'mod trasparenti.", "earn.usd.treasury-operations-popup.title": "Operazzjonijiet tat-Teżor", "earn.view_earn.card_rechard_off": "<PERSON><PERSON><PERSON>", "earn.view_earn.card_rechard_on": "Mixgħul", "earn.view_earn.card_recharge": "Top-up tal-Kard", "earn.view_earn.total_balance_label": "Qed taqla' {percentage} fis-sena", "earn.view_earn.total_earnings_label": "Qligħ totali", "earn.withdraw": "<PERSON><PERSON><PERSON>", "earn.withdraw.amount_to_withdraw": "Ammont li trid tiġbed", "earn.withdraw.enter_amount": "Daħħal Ammont", "earn.withdraw.loading": "<PERSON><PERSON>", "earn.withdraw.no_routes_found": "<PERSON> nstabu l-ebda rotot", "earn.withdraw.not_enough_balance": "M'hemmx biżżejjed bilanċ", "earn.withdraw.select-currency.title": "Agħ<PERSON>el token", "earn.withdraw.select_to_token": "Agħ<PERSON>el token", "earn.withdraw.withdraw": "<PERSON><PERSON><PERSON>", "earn.withdraw_form.title": "<PERSON><PERSON><PERSON> minn <PERSON>n", "earnings-view.earnings": "Qligħ Totali", "edit-account-owners.add-owner.add-wallet": "<PERSON><PERSON> sid", "edit-account-owners.add-owner.add_wallet": "<PERSON><PERSON>", "edit-account-owners.add-owner.title": "<PERSON><PERSON>l-<PERSON>", "edit-account-owners.card-owners": "<PERSON><PERSON> tal-kard", "edit-account-owners.external-wallet": "Wallet estern", "editBankRecipient.title": "<PERSON><PERSON> r-riċevitur", "editNetwork.addCustomRPC": "Żid custom RPC node", "editNetwork.cannot_verify.subtitle": "L-RPC ma jaħdimx. Iċċekkja l-URL u pprova.", "editNetwork.cannot_verify.title": "Ma nistax nivverifika l-RPC Node", "editNetwork.cannot_verify.try_again": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "editNetwork.customRPCNode": "Custom RPC node", "editNetwork.defaultRPC": "Default RPC", "editNetwork.networkRPC": "Network RPC", "editNetwork.rpc_url.cannot_be_empty": "<PERSON><PERSON><PERSON>", "editNetwork.rpc_url.not_a_valid_https_url": "Irid ikun URL HTTP(S) validu", "editNetwork.safetyWarning.subtitle": "Zeal ma jistax jiggarantixxi l-privatezza, l-affidabbiltà u s-sigurtà ta' RPCs apposta. Inti żgur li trid tuża node RPC apposta?", "editNetwork.safetyWarning.title": "L-RPCs apposta jistgħu ma jkunux siguri", "editNetwork.zealRPCNode": "Zeal RPC Node", "editNetworkRpc.headerTitle": "Node RPC Apposta", "editNetworkRpc.rpcNodeUrl": "URL tan-Node RPC", "editing-locked.modal.description": "B'differenza għat-tranżazzjonijiet ta' Approvazzjoni, il-Permits ma jħallukx teditja l-Limitu tal-Infiq jew il-<PERSON><PERSON> ta' Skadenza. Kun ċert li tafda dApp qabel tissottometti Permit.", "editing-locked.modal.title": "<PERSON><PERSON><PERSON><PERSON>", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Nattivaw ir-recharge", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Recharge attivat", "enterCardnumber": "<PERSON><PERSON><PERSON><PERSON> in-numru tal-kard", "error.connectivity_error.subtitle": "Jekk jogħġbok iċċekkja l-konnessjoni tal-internet tiegħek u erġa' pprova.", "error.connectivity_error.title": "M'hemmx konnessjoni tal-internet", "error.decrypt_incorrect_password.title": "Password ħażina", "error.encrypted_object_invalid_format.title": "<PERSON> korrotta", "error.failed_to_fetch_google_auth_token.title": "Ma rnexxilniex niksbu aċċess", "error.list.item.cta.action": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "error.trezor_action_cancelled.title": "Tranżazzjoni miċħuda", "error.trezor_device_used_elsewhere.title": "It-<PERSON><PERSON><PERSON> qed jintuża f'sessjoni oħra", "error.trezor_method_cancelled.title": "Ma stajniex nissinkronizzaw it-<PERSON><PERSON><PERSON>", "error.trezor_permissions_not_granted.title": "Ma stajniex nissinkronizzaw it-<PERSON><PERSON><PERSON>", "error.trezor_pin_cancelled.title": "Ma stajniex nissinkronizzaw it-<PERSON><PERSON><PERSON>", "error.trezor_popup_closed.title": "Ma stajniex nissinkronizzaw it-<PERSON><PERSON><PERSON>", "error.unblock_account_number_and_sort_code_mismatch": "In-numru tal-kont u s-sort code ma jaqblux", "error.unblock_can_not_change_details_after_kyc": "Ma tistax tibdel id-dettalji wara l-KYC", "error.unblock_hard_kyc_failure": "Stat tal-KYC mhux mistenni", "error.unblock_invalid_faster_payment_configuration.title": "Dan il-bank ma jappoġġjax Faster Payments", "error.unblock_invalid_iban": "IBAN invalidu", "error.unblock_session_expired.title": "<PERSON>-<PERSON><PERSON><PERSON><PERSON> ta' Unblock skadiet", "error.unblock_user_with_address_already_exists.title": "Diġà hemm kont imwaqqaf għal dan l-indirizz", "error.unblock_user_with_such_email_already_exists.title": "Diġà jeżisti utent b'dan l-email", "error.unknown_error.error_message": "Messaġġ tal-iżball: ", "error.unknown_error.subtitle": "Skużana! Jekk teħtieġ għajnuna urġenti, ikkuntattja lis-support u aqsam id-dettalji t'hawn taħt.", "error.unknown_error.title": "Żball fis-sistema", "eth-cost-warning-modal.subtitle": "L-iSmart Wallets jaħdmu fuq Ethereum, iżda t-tariffi huma għoljin ħafna u NIRRAKKOMANDAW B'SAĦĦA li tuża netwerks oħra minflok.", "eth-cost-warning-modal.title": "<PERSON><PERSON>ta <PERSON>ther<PERSON> - it-tariffi tan-netwerk huma għoljin", "exchange.form.button.chain_unsupported": "Netwerk mhux appoġġjat", "exchange.form.button.refreshing": "<PERSON><PERSON>", "exchange.form.error.asset_not_supported.button": "Agħżel asset ieħor", "exchange.form.error.asset_not_supported.description": "Il-Pont ma jappoġġjax il-bridging ta' dan l-asset.", "exchange.form.error.asset_not_supported.title": "Asset mhux appoġġ<PERSON>t", "exchange.form.error.bridge_quote_timeout.button": "Agħżel asset ieħor", "exchange.form.error.bridge_quote_timeout.description": "Ipprova par tokens ieħor", "exchange.form.error.bridge_quote_timeout.title": "Ma nstab l-ebda exchange", "exchange.form.error.different_receiver_not_supported.button": "Neħħi r-riċevitur alternattiv", "exchange.form.error.different_receiver_not_supported.description": "Dan l-exchange ma jappoġġjax li tibgħat f'indirizz ieħor.", "exchange.form.error.different_receiver_not_supported.title": "L-indirizz tal-bgħit u r-riċeviment irid ikun l-istess.", "exchange.form.error.insufficient_input_amount.button": "Żid l-ammont", "exchange.form.error.insufficient_liquidity.button": "Naqqas l-ammont", "exchange.form.error.insufficient_liquidity.description": "Il-Pont m'għandux biżżejjed assi. Ipprova ammont iżgħar.", "exchange.form.error.insufficient_liquidity.title": "Ammont għoli wisq", "exchange.form.error.max_amount_exceeded.button": "Naqqas l-ammont", "exchange.form.error.max_amount_exceeded.description": "L-ammont massimu nqa<PERSON>ż.", "exchange.form.error.max_amount_exceeded.title": "Ammont għoli wisq", "exchange.form.error.min_amount_not_met.button": "Żid l-ammont", "exchange.form.error.min_amount_not_met.description": "L-ammont minimu ta' exchange għal dan it-token ma ntlaħaqx.", "exchange.form.error.min_amount_not_met.description_with_amount": "L-ammont minimu għall-exchange hu {amount}.", "exchange.form.error.min_amount_not_met.title": "Ammont baxx wisq", "exchange.form.error.min_amount_not_met.title_increase": "Żid l-ammont", "exchange.form.error.no_routes_found.button": "Agħżel asset ieħor", "exchange.form.error.no_routes_found.description": "M'hemm l-ebda rotta ta' exchange disponibbli għal din il-kombinazzjoni ta' token/network.", "exchange.form.error.no_routes_found.title": "L-ebda exchange disponibbli", "exchange.form.error.not_enough_balance.button": "Naqqas l-ammont", "exchange.form.error.not_enough_balance.description": "M'għandekx biżżejjed minn dan l-asset għat-tranżazzjoni.", "exchange.form.error.not_enough_balance.title": "M'hemmx biżżejjed bilanċ", "exchange.form.error.slippage_passed_is_too_low.button": "Żid l-islippage", "exchange.form.error.slippage_passed_is_too_low.description": "Is-slippage permess huwa baxx wisq għal dan l-asset.", "exchange.form.error.slippage_passed_is_too_low.title": "Slippage baxx wisq", "exchange.form.error.socket_internal_error.button": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>a aktar tard", "exchange.form.error.socket_internal_error.description": "Il-partner tal-Pont għandu xi problemi. Erġ<PERSON>' pprova aktar tard.", "exchange.form.error.socket_internal_error.title": "<PERSON><PERSON> mal-partner tal-<PERSON>", "exchange.form.error.stargatev2_requires_fee_in_native": "Żid {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Żid {amount} biex tlesti t-tranżazzjoni", "exchange.form.error.stargatev2_requires_fee_in_native.title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> iktar {symbol}", "expiration-info.modal.description": "Il-ħin ta' skadenza huwa kemm app tista' tuża t-tokens tiegħek. <PERSON>a jiskadi l-ħin, ji<PERSON><PERSON> l-aċċess sakemm tgħidilhom int. Biex tib<PERSON> sigu<PERSON>, żomm il-ħin ta' skadenza qasir.", "expiration-info.modal.title": "<PERSON>'inhu l-ħin ta' skadenza?", "expiration-time.high.modal.text": "Il-ħin tal-iskadenza għandu jkun qasir. <PERSON>in twil hu <PERSON><PERSON> għax jagħti ċans lill-iscammers biex jużawlek it-tokens ħa<PERSON><PERSON>.", "expiration-time.high.modal.title": "<PERSON>in twil ta' skadenza", "failed.transaction.content": "Tranżazzjoni probabbli li tfalli", "fee.unknown": "<PERSON><PERSON><PERSON> ma<PERSON>", "feedback-request.leave-message": "Ħalli <PERSON>", "feedback-request.not-now": "<PERSON><PERSON><PERSON> issa", "feedback-request.title": "Grazzi! Ki<PERSON> nistgħu ntejbu Zeal?", "float.input.period": "Separatur deċimali", "gnosis-activate-card.info-popup.subtitle": "Għall-<PERSON><PERSON><PERSON><PERSON> pagament, uża l-kard u l-PIN.", "gnosis-activate-card.info-popup.title": "L-ewwel pagament jeħtieġ Chip u PIN", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "<PERSON><PERSON><PERSON><PERSON> in-numru tal-kard biex tattivaha.", "gnosis-activate-card.title": "<PERSON><PERSON><PERSON> tal-kard", "gnosis-pay-re-kyc-widget.btn-text": "Ivverifika", "gnosis-pay-re-kyc-widget.title.not-started": "Ivverifika l-identità tiegħek", "gnosis-pay.login.cta": "Irbot kont eżistenti", "gnosis-pay.login.title": "Diġà għandek kont Gnosis Pay", "gnosis-signup.confirm.subtitle": "Fittex email mingħand Gnosis Pay, tista' tkun fl-ispam.", "gnosis-signup.confirm.title": "Ma rċevejtx l-email tal-verifika?", "gnosis-signup.continue": "<PERSON><PERSON><PERSON>", "gnosis-signup.dont_link_accounts": "Tikkonnettjax kontijiet", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "<PERSON><PERSON><PERSON><PERSON> <EMAIL>", "gnosis-signup.enter-email.title": "Daħħal l-email", "gnosis-signup.title": "Qrajt u naqbel mat- <linkGnosisTNC>Termini u Kund.</linkGnosisTNC> <monovateTerms>Termini għad-Detentur tal-Kard</monovateTerms> u <linkMonerium>T&Cs ta' Monerium</linkMonerium>.", "gnosis-signup.verify-email.title": "Ivverifika l-email", "gnosis.confirm.subtitle": "Ma rċevejtx kodiċi? Ara jekk in-numru hux tajjeb", "gnosis.confirm.title": "Intbagħat kodiċi lil {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Ivverifika", "gnosisPayAccountStatus.success.title": "Kard importata", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay għadu mhux disponibbli f'pajjiżek", "gnosisPayNoActiveCardsFound.title": "M'hemmx kards attivi", "gnosis_pay_card_delay_relay_not_empty_error.title": "It-tranżazzjoni tiegħek ma setgħetx tiġi pproċessata bħalissa. Erġa' pprova aktar tard.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "<PERSON><PERSON> m<PERSON>x possibbli", "gnosiskyc.modal.approved.activate-free-card": "At<PERSON>va l-kard b'xejn", "gnosiskyc.modal.approved.button-text": "Iddepożita minn bank", "gnosiskyc.modal.approved.title": "Id-dettalji tal-kont personali tiegħek inħolqu", "gnosiskyc.modal.failed.close": "Agħlaq", "gnosiskyc.modal.failed.title": "<PERSON><PERSON><PERSON><PERSON>, is-sie<PERSON><PERSON> tagħna Gnosis Pay ma jistax joħloq kont għalik", "gnosiskyc.modal.in-progress.title": "Il-verifika tal-ID tista' tieħu 24 siegħa jew aktar. Jekk jogħġbok, ħu paċenzja.", "goToSettingsPopup.settings": "Settings", "goToSettingsPopup.title": "Attiva n-notifiki mis-settings tat-tagħmir.", "google_file.error.failed_to_fetch_auth_token.button_title": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "google_file.error.failed_to_fetch_auth_token.subtitle": "Biex inkunu nistgħu nużaw ir-Recovery File tiegħek, agħtina aċċess għall-cloud personali tiegħek.", "google_file.error.failed_to_fetch_auth_token.title": "Ma rnexxilniex niksbu aċċess", "hidden_tokens.widget.emptyState": "L-ebda tokens moħbija", "how_to_connect_to_metamask.got_it": "OK, fhimt", "how_to_connect_to_metamask.story.subtitle": "Aqleb faċilment bejn <PERSON> u kartieri.", "how_to_connect_to_metamask.story.title": "Zeal jaħdem ma' kartieri oħra", "how_to_connect_to_metamask.why_switch": "Għaliex taqleb bejn <PERSON> u kartieri oħra?", "how_to_connect_to_metamask.why_switch.description": "Is-Safety Checks ta' Zeal jipprote<PERSON><PERSON>.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "Faċli tuża <PERSON>eal ma' kartieri oħra.", "import-bank-transfer-owner.banner.title": "Il-kartiera għat-trasferimenti nbidlet. Importa l-kartiera tiegħek biex tkompli tużahom.", "import-bank-transfer-owner.title": "Importa l-kartiera għat-trasferimenti.", "import_gnosispay_wallet.add-another-card-owner.footnote": "Importa ċ-ċavetta privata jew is-seed phrase tal-kard Gnosis Pay tiegħek", "import_gnosispay_wallet.primaryText": "Importa l-wallet Gnosis Pay", "injected-wallet": "Kartiera tal-browser", "intercom.getHelp": "Ikseb l<PERSON>ħ<PERSON>nu<PERSON>", "invalid_iban.got_it": "Fhimt", "invalid_iban.subtitle": "L-IBAN li daħħalt mhuwiex validu. Iċċekkja d-dettalji u erġa' pprova.", "invalid_iban.title": "IBAN invalidu", "keypad-0": "Tast 0 tat-tastiera", "keypad-1": "Tast 1 tat-tastiera", "keypad-2": "Tast 2 tat-tastiera", "keypad-3": "Tast 3 tat-tastiera", "keypad-4": "Tast 4 tat-tastiera", "keypad-5": "Tast 5 tat-tastiera", "keypad-6": "Tast 6 tat-tastiera", "keypad-7": "Tast 7 tat-tastiera", "keypad-8": "Tast 8 tat-tastiera", "keypad-9": "Tast 9 tat-tastiera", "keypad.biometric-button": "Buttuna bijometrika tat-tastiera", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Secret Phrase assigurata 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "Ara l-frażi", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Żomm kopja sigura u offline tas-Secret Phrase tiegħek biex tkun tista' tirkupra l-assi tiegħek aktar tard", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "Tippruvax taqta' l-kelma", "keystore.write_secret_phrase.before_you_begin.first_point": "<PERSON><PERSON><PERSON> li kull min għandu s-Secret Phrase tiegħi jista' jittrasferixxi l-assi tiegħi", "keystore.write_secret_phrase.before_you_begin.second_point": "<PERSON><PERSON> responsab<PERSON><PERSON> li nżomm is-Secret Phrase tiegħi sigrieta u f'post sigur", "keystore.write_secret_phrase.before_you_begin.subtitle": "Jekk jogħġbok aqra u aċċetta l-punti li ġejjin:", "keystore.write_secret_phrase.before_you_begin.third_point": "<PERSON><PERSON><PERSON> f'post privat, ming<PERSON><PERSON><PERSON> nies jew kameras madwari", "keystore.write_secret_phrase.before_you_begin.title": "<PERSON><PERSON><PERSON> tibda", "keystore.write_secret_phrase.secret_phrase_test.title": "<PERSON><PERSON><PERSON><PERSON> l-kelma numru {count} fis-Secret Phrase tiegħek?", "keystore.write_secret_phrase.test_ps.lets_do_it": "<PERSON><PERSON><PERSON>", "keystore.write_secret_phrase.test_ps.subtitle": "Se j<PERSON><PERSON>k b<PERSON>onn is-Secret Phrase tiegħek biex tirrestawra l-kont tiegħek fuq dan l-apparat jew apparat ieħor. E<PERSON>ja nittestjaw li s-Secret Phrase tiegħek hija miktuba sew.", "keystore.write_secret_phrase.test_ps.subtitle2": "Se nsaqsuk għal {count} kelmiet mill-frażi tiegħek.", "keystore.write_secret_phrase.test_ps.title": "Ittestja l-Irkupru tal-Kont", "kyc.modal.approved.button-text": "Agħmel trasferiment", "kyc.modal.approved.subtitle": "Verifika lesta. Trasferimenti bla limitu.", "kyc.modal.approved.title": "Trasferimenti bankarji attivati", "kyc.modal.continue-with-partner.button-text": "<PERSON><PERSON><PERSON>", "kyc.modal.continue-with-partner.subtitle": "<PERSON><PERSON> se ngħadduk għand il-partner tagħna biex jiġbor id-dokumenti tiegħek u jlesti l-applikazzjoni tal-verifika.", "kyc.modal.continue-with-partner.title": "<PERSON><PERSON><PERSON> mal-partner <PERSON><PERSON><PERSON>", "kyc.modal.failed.unblock.subtitle": "Unblock ma approvakx għal trasferimenti.", "kyc.modal.failed.unblock.title": "Applikazzjoni ta' Unblock mhux approvata", "kyc.modal.paused.button-text": "Aġġorna d-dettalji", "kyc.modal.paused.subtitle": "Żball fid-dettalji. Erġa' pprova.", "kyc.modal.paused.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc.modal.pending.button-text": "Agħlaq", "kyc.modal.pending.subtitle": "Verifika tieħu madwar 10 min.", "kyc.modal.pending.title": "Inżommuk aġġornat", "kyc.modal.required.cta": "Ibda l-verifika", "kyc.modal.required.subtitle": "Lħaqt il-limitu. Ivverifika l-identità.", "kyc.modal.required.title": "Verifika tal-identità meħtieġa", "kyc.submitted": "Applikazzjoni Mibgħuta", "kyc.submitted_short": "Sottomessa", "kyc_status.completed_status": "Les<PERSON>", "kyc_status.failed_status": "Falliet", "kyc_status.paused_status": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc_status.subtitle": "Trasferimenti bankarji", "kyc_status.subtitle.wrong_details": "<PERSON><PERSON><PERSON>", "kyc_status.subtitle_in_progress": "Għaddejja", "kyc_status.title": "Qed nivverifikaw l-identità", "label.close": "Agħlaq", "label.saving": "Issej<PERSON><PERSON>...", "labels.this-month": "<PERSON> ix-xahar", "labels.today": "<PERSON><PERSON>", "labels.yesterday": "Ilbieraħ", "language.selector.title": "Ling<PERSON>", "ledger.account_loaded.imported": "Importat", "ledger.add.success.title": "Ledger <PERSON><PERSON><PERSON><PERSON> b'suċċess 🎉", "ledger.connect.cta": "Sinkronizza l-Ledger", "ledger.connect.step1": "Ikkonnettja l-Ledger mal-apparat tiegħ<PERSON>", "ledger.connect.step2": "Iftaħ l-app tal-Ethereum fuq il-Ledger", "ledger.connect.step3": "Imbagħad sinkronizza l-Ledger tiegħek 👇", "ledger.connect.subtitle": "Seg<PERSON> <PERSON> il-passi biex timporta l-kartieri tal-Ledger tiegħek f'Zeal", "ledger.connect.title": "Ikkonnettja l-Ledger ma' Zeal", "ledger.error.ledger_is_locked.subtitle": "Iftaħ il-Ledger u l-app tal-Ethereum", "ledger.error.ledger_is_locked.title": "<PERSON><PERSON><PERSON><PERSON> huwa msakkar", "ledger.error.ledger_not_connected.action": "Issinkronizza l-Ledger", "ledger.error.ledger_not_connected.subtitle": "Ikkonnettja l-hardware wallet mat-tagħmir tiegħek u iftaħ l-app tal-Ethereum", "ledger.error.ledger_not_connected.title": "Il-Ledger mhuwiex ikkonnettjat", "ledger.error.ledger_running_non_eth_app.title": "L-app tal-Ethereum mhix miftuħa", "ledger.error.user_trx_denied_by_user.action": "Agħlaq", "ledger.error.user_trx_denied_by_user.subtitle": "Inti rrifjutajt it-tranżazzjoni fuq il-hardware wallet tiegħek", "ledger.error.user_trx_denied_by_user.title": "Tranżazzjoni miċħuda", "ledger.hd_path.bip44.subtitle": "<PERSON><PERSON><PERSON>, Trezor", "ledger.hd_path.bip44.title": "Standard BIP44", "ledger.hd_path.ledger_live.subtitle": "<PERSON><PERSON><PERSON>", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Legacy", "ledger.hd_path.phantom.subtitle": "eż. <PERSON>", "ledger.select.hd_path.subtitle": "L-HD paths huma l-mod kif il-hardware wallets jorganizzaw il-kontijiet. Dan jixbah kif indiċi jorganizza l-paġni fi ktieb.", "ledger.select.hd_path.title": "Agħżel l-HD Path", "ledger.select_account.import_wallets_count": "{count,plural,=0{L-ebda kartiera magħżula} one{Importa kartiera} other{Importa {count} kartieri}}", "ledger.select_account.path_settings": "Settings tal-Path", "ledger.select_account.subtitle": "<PERSON>hux qed tara l-kartieri li tistenna? <PERSON><PERSON><PERSON><PERSON> biddel is-settings tal-path", "ledger.select_account.subtitle.group_header": "<PERSON><PERSON><PERSON>", "ledger.select_account.title": "Importa kartieri ta' Ledger", "legend.lending-operations": "Operazzjonijiet ta' Self", "legend.market_making-operations": "Operazzjonijiet ta' Market Making", "legend.treasury-operations": "Operazzjonijiet tat-Teżor", "link-existing-monerium-account-sign.button": "<PERSON><PERSON><PERSON> ma' Zeal", "link-existing-monerium-account-sign.subtitle": "Diġà għandek kont Monerium.", "link-existing-monerium-account-sign.title": "Irbot Zeal mal-kont Monerium eżistenti tiegħek", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Diġà għandek kont Monerium. Jekk jogħġbok mur fl-app Monerium biex tlesti.", "link-existing-monerium-account.title": "Mur fuq Monerium biex torbot il-kont tiegħek", "loading.pin": "<PERSON><PERSON> jitgħabba l-PIN...", "lockScreen.passwordIncorrectMessage": "Il-password mhix korretta", "lockScreen.passwordRequiredMessage": "Password meħtieġa", "lockScreen.unlock.header": "Iftaħ", "lockScreen.unlock.subheader": "Uża l-password tiegħek biex tiftaħ Zeal", "mainTabs.activity.label": "Attività", "mainTabs.browse.label": "Browse", "mainTabs.browse.title": "Browse", "mainTabs.card.label": "<PERSON><PERSON>", "mainTabs.portfolio.label": "Portafoll", "mainTabs.rewards.label": "Pre<PERSON><PERSON><PERSON><PERSON>", "makeSpendable.cta": "Ikkonverti biex tuża", "makeSpendable.holdAsCash": "Żommhom bħala flus", "makeSpendable.shortText": "Qed taqla' {apy} fis-sena", "makeSpendable.title": "{amount} irċevuti", "merchantCategory.agriculture": "Agrikoltura", "merchantCategory.alcohol": "Alkoħol", "merchantCategory.antiques": "Antikitjiet", "merchantCategory.appliances": "Apparat", "merchantCategory.artGalleries": "Galleriji tal-Arti", "merchantCategory.autoRepair": "<PERSON><PERSON><PERSON><PERSON> tal<PERSON>", "merchantCategory.autoRepairService": "Servizz ta' Tiswija ta<PERSON>-<PERSON>", "merchantCategory.beautyFitnessSpas": "S<PERSON>ħ<PERSON>, Fitness u Spas", "merchantCategory.beautyPersonalCare": "Sbuħija u Kura Personali", "merchantCategory.billiard": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.books": "Kotba", "merchantCategory.bowling": "Bowling", "merchantCategory.businessProfessionalServices": "<PERSON><PERSON><PERSON> tan-Negozju u Professjonali", "merchantCategory.carRental": "<PERSON><PERSON>", "merchantCategory.carWash": "<PERSON><PERSON><PERSON>", "merchantCategory.cars": "<PERSON><PERSON><PERSON>", "merchantCategory.casino": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.casinoGambling": "Każinò u Logħob tal-Ażżard", "merchantCategory.cellular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.charity": "<PERSON><PERSON><PERSON>", "merchantCategory.childcare": "Kura tat-T<PERSON>l", "merchantCategory.cigarette": "<PERSON><PERSON><PERSON>", "merchantCategory.cinema": "Ċinema", "merchantCategory.cinemaEvents": "Ċinema u Avvenimenti", "merchantCategory.cleaning": "<PERSON><PERSON><PERSON>", "merchantCategory.cleaningMaintenance": "Tindif u Manutenzjoni", "merchantCategory.clothes": "Ħwejjeġ", "merchantCategory.clothingServices": "<PERSON><PERSON>zzi relatati mal-Ħwejjeġ", "merchantCategory.communicationServices": "Servizzi ta' Komunikazzjoni", "merchantCategory.construction": "Kostruzzjoni", "merchantCategory.cosmetics": "Kożmetiċi", "merchantCategory.craftsArtSupplies": "Snajja' u Provvisti tal-Arti", "merchantCategory.datingServices": "<PERSON><PERSON><PERSON> tad-Dating", "merchantCategory.delivery": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.dentist": "Dentist", "merchantCategory.departmentStores": "Department Stores", "merchantCategory.directMarketingSubscription": "Direct Marketing u Abbonament", "merchantCategory.discountStores": "Ħwienet bi skont", "merchantCategory.drugs": "Mediċini", "merchantCategory.dutyFree": "Duty Free", "merchantCategory.education": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.electricity": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.electronics": "Elettronika", "merchantCategory.emergencyServices": "Ser<PERSON>zzi ta' Emerġenza", "merchantCategory.equipmentRental": "<PERSON><PERSON>", "merchantCategory.evCharging": "Iċċarġjar ta' Vetturi Elettriċi", "merchantCategory.financialInstitutions": "Istituzzjonijiet Finanzjarji", "merchantCategory.financialProfessionalServices": "<PERSON><PERSON><PERSON>z<PERSON> u Professjonali", "merchantCategory.finesPenalties": "Multi u Penali", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.flowers": "<PERSON><PERSON><PERSON>", "merchantCategory.flowersGarden": "Fjuri u Ġnien", "merchantCategory.food": "<PERSON><PERSON>", "merchantCategory.freight": "Merkanzija", "merchantCategory.fuel": "Fju<PERSON><PERSON>", "merchantCategory.funeralServices": "<PERSON><PERSON><PERSON>", "merchantCategory.furniture": "Għamara", "merchantCategory.games": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.gas": "Petrol", "merchantCategory.generalMerchandiseRetail": "Bejgħ u Merkanzija Ġenerali", "merchantCategory.gifts": "<PERSON><PERSON>", "merchantCategory.government": "Gvern", "merchantCategory.governmentServices": "<PERSON><PERSON><PERSON>", "merchantCategory.hardware": "Hardware", "merchantCategory.healthMedicine": "<PERSON><PERSON><PERSON><PERSON> u Mediċina", "merchantCategory.homeImprovement": "Titjib fid-Dar", "merchantCategory.homeServices": "<PERSON><PERSON><PERSON> għad-Dar", "merchantCategory.hotel": "<PERSON><PERSON><PERSON>", "merchantCategory.housing": "Djar", "merchantCategory.insurance": "Assigurazzjoni", "merchantCategory.internet": "Internet", "merchantCategory.kids": "Tfal", "merchantCategory.laundry": "<PERSON><PERSON><PERSON>", "merchantCategory.laundryCleaningServices": "<PERSON><PERSON><PERSON> <PERSON>' Laundry u Tindif", "merchantCategory.legalGovernmentFees": "Tariffi Legali u tal-Gvern", "merchantCategory.luxuries": "Lussu", "merchantCategory.luxuriesCollectibles": "Lussu u Oġġetti tal-Kollezzjoni", "merchantCategory.magazines": "Rivisti", "merchantCategory.magazinesNews": "Rivisti u Aħbarijiet", "merchantCategory.marketplaces": "Swieq", "merchantCategory.media": "Midja", "merchantCategory.medicine": "Mediċina", "merchantCategory.mobileHomes": "Mobile Homes", "merchantCategory.moneyTransferCrypto": "Trasferiment ta' Flus u Crypto", "merchantCategory.musicRelated": "Relatat mal-Mużika", "merchantCategory.musicalInstruments": "Strumenti Mużikali", "merchantCategory.optics": "Ottika", "merchantCategory.organizationsClubs": "Organizzazzjonijiet u Klabbs", "merchantCategory.other": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.parking": "Parkeġġ", "merchantCategory.pawnShops": "Ħwienet tal-Prenda", "merchantCategory.pets": "<PERSON><PERSON><PERSON> Domestiċi", "merchantCategory.photoServicesSupplies": "Servizzi u Provvisti tar-Ritratti", "merchantCategory.postalServices": "<PERSON><PERSON><PERSON>i", "merchantCategory.professionalServicesOther": "<PERSON><PERSON><PERSON> (Oħrajn)", "merchantCategory.publicTransport": "Trasport Pubbliku", "merchantCategory.purchases": "<PERSON><PERSON>", "merchantCategory.purchasesMiscServices": "<PERSON><PERSON> <PERSON>", "merchantCategory.recreationServices": "Servizzi ta' Rikreazzjoni", "merchantCategory.religiousGoods": "Oġġ<PERSON>", "merchantCategory.secondhandRetail": "Bejgħ Secondhand", "merchantCategory.shoeHatRepair": "Tiswija ta' Żraben u Kpiepel", "merchantCategory.shoeRepair": "Tiswija taż-Żraben", "merchantCategory.softwareApps": "Software u Apps", "merchantCategory.specializedRepairs": "Tiswijiet Speċjalizzati", "merchantCategory.sport": "Sport", "merchantCategory.sportingGoods": "Oġġetti Sportivi", "merchantCategory.sportingGoodsRecreation": "Oġġetti Sportivi u Rikreazzjoni", "merchantCategory.sportsClubsFields": "Klabbs u Grawnds tal-Isport", "merchantCategory.stationaryPrinting": "Stazzjonarju u Stampar", "merchantCategory.stationery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.storage": "Ħażna", "merchantCategory.taxes": "Taxxi", "merchantCategory.taxi": "Taxi", "merchantCategory.telecomEquipment": "Tagħmir tat-Telekomunikazzjoni", "merchantCategory.telephony": "Telefonija", "merchantCategory.tobacco": "Tabakk", "merchantCategory.tollRoad": "<PERSON><PERSON>", "merchantCategory.tourismAttractionsAmusement": "<PERSON><PERSON><PERSON><PERSON>, Attrazzjonijiet u Divertiment", "merchantCategory.towing": "Servizz ta' Rmonk", "merchantCategory.toys": "Ġugarelli", "merchantCategory.toysHobbies": "Ġugarelli u Passatempi", "merchantCategory.trafficFine": "Multa tat-Traffiku", "merchantCategory.train": "Ferrovija", "merchantCategory.travelAgency": "Aġenzija tal-Ivvjaġġar", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, Radju u Streaming", "merchantCategory.utilities": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.waterTransport": "Trasport fuq l-Ilma", "merchantCategory.wholesaleClubs": "Wholesale Clubs", "metaMask.subtitle": "Attiva l-Modalità MetaMask biex tuża Zeal.", "metaMask.title": "Ma tistax tikkon<PERSON>ja ma' Z<PERSON>?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Iftaħ l-app tal-bank tiegħek", "monerium-bank-deposit.buttet-point.receive-crypto": "Irċievi EUR diġitali", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Ibgħat {fiatCurrencyCode} fil-kont tiegħek", "monerium-bank-deposit.deposit-account-country": "<PERSON><PERSON><PERSON><PERSON>", "monerium-bank-deposit.header": "{fullName} kont personali", "monerium-bank-details.account-name": "<PERSON><PERSON>", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT ikkupjat", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT ikkupjat", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN ikkupjat", "monerium-bank-details.to-wallet": "Lill-kartiera", "monerium-bank-details.transfer-fee": "Tariffa tat-trasferiment", "monerium-bank-transfer.enable-card.bullet-1": "Imla l-verifika tal-identità", "monerium-bank-transfer.enable-card.bullet-2": "<PERSON><PERSON><PERSON>de<PERSON><PERSON><PERSON> tal-kont <PERSON>i", "monerium-bank-transfer.enable-card.bullet-3": "Iddepożita mill-kont bankarju", "monerium-card-delay-relay.success.cta": "Agħlaq", "monerium-card-delay-relay.success.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bid<PERSON>t fil-kard jieħdu 3 minuti.", "monerium-card-delay-relay.success.title": "Erġa' l<PERSON> fi 3min biex tkompli s-setup ta' Monerium", "monerium-deposit.account-details-info-popup.bullet-point-1": "Kull {fiatCurrencyCode} li tibgħat f'dan il-kont jinbidel awtomatikament f' {cryptoCurrencyCode} tokens fuq {cryptoCurrencyChain} Chain u jintbagħat fil-kartiera tiegħek", "monerium-deposit.account-details-info-popup.bullet-point-2": "IBGĦAT BISS {fiatCurrencyCode} ({fiatCurrencySymbol}) fil-kont tiegħek", "monerium-deposit.account-details-info-popup.title": "<PERSON><PERSON><PERSON> tal-kont tiegħek", "monerium.check_order_status.sending": "<PERSON><PERSON> ji<PERSON>", "monerium.not-eligible.cta": "<PERSON><PERSON>", "monerium.not-eligible.subtitle": "Monerium ma jistax jiftaħlek kont.", "monerium.not-eligible.title": "<PERSON><PERSON><PERSON><PERSON> fornitur differenti", "monerium.setup-card.cancel": "Ikkanċella", "monerium.setup-card.continue": "<PERSON><PERSON><PERSON>", "monerium.setup-card.create_account": "<PERSON><PERSON><PERSON><PERSON>", "monerium.setup-card.login": "Idħol f'Gnosis Pay", "monerium.setup-card.subtitle": "Idħol f'Gnosis Pay għal depożiti.", "monerium.setup-card.subtitle_personal_account": "<PERSON><PERSON><PERSON>kont personali tiegħek ma' Gnosis Pay fi ftit minuti:", "monerium.setup-card.title": "Attiva d-depożiti bankarji", "moneriumDepositSuccess.goToWallet": "Mur fil-kartiera", "moneriumDepositSuccess.title": "{symbol} irċevut", "moneriumInfo.fees": "Ikollok 0% tariffi", "moneriumInfo.registration": "Monerium hija awtorizzata u regolata bħala Istituzzjoni tal-Flus Elettroniċi taħt l-Att Iżlandiż dwar il-Flus Elettroniċi Nru 17/2013 <link>Sir af aktar</link>", "moneriumInfo.selfCustody": "Il-flus diġitali li tirċievi huma fil-kustodja tiegħek stess u ħadd ħliefek ma jkollu kontroll fuq l-assi tiegħek", "moneriumWithdrawRejected.supportText": "Ma stajniex inlestu t-trasferiment tiegħek. Jekk jogħġbok erġa' pprova u jekk xorta ma jaħdimx, <link>ikkuntattja l-għajnuna.</link>", "moneriumWithdrawRejected.title": "Trasferiment imreġġa' lura", "moneriumWithdrawRejected.tryAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "moneriumWithdrawSuccess.supportText": "<PERSON><PERSON>' jieħu 24 siegħa biex{br}ir-riċevitur jirċievi l-fondi", "moneriumWithdrawSuccess.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monerium_enable_banner.text": "Attiva t-trasferimenti bankarji issa", "monerium_error_address_re_link_required.title": "Il-kartiera teħtieġ li terġa' tiġi kkonnettjata ma' Monerium", "monerium_error_duplicate_order.title": "Ordni duplikata", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "<PERSON>", "mt-pelerin-fork.options.chf.short": "Immedjat u b'xejn ma' Mt Pelerin", "mt-pelerin-fork.options.euro.primary": "<PERSON><PERSON><PERSON>", "mt-pelerin-fork.options.euro.short": "Immedjat u b'xejn ma' Monerium", "mt-pelerin-fork.title": "<PERSON>'t<PERSON><PERSON><PERSON> tid<PERSON>?", "mtPelerinProviderInfo.fees": "Tħallas 0% tariffi", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd hija affiljata ma' SO-FIT, korp awtoregolatorju rikonoxxut mill-Awtorità Finanzjarja Svizzera (FINMA) taħt l-Att Kontra l-Ħasil tal-Flus. <link>Skopri aktar</link>", "mtPelerinProviderInfo.selfCustody": "Il-flus diġitali li tirċievi huma f'kartiera privata u ħadd ħliefek ma jkollu kontroll fuq l-assi tiegħek.", "network-fee-widget.title": "<PERSON><PERSON><PERSON>", "network.edit.verifying_rpc": "Qed nivverifikaw l-RPC", "network.editRpc.predefined_network_info.subtitle": "Bħal VPN, Zeal juża RPCs li ma jħallux id-data personali tiegħek tiġi ttraċċjata.{br}{br}L-RPCs default ta' Zeal huma minn fornituri affidabbli u ttestjati sew.", "network.editRpc.predefined_network_info.title": "Zeal privacy RPC", "network.filter.update_rpc_success": "Node RPC issejvjat", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "In-Networks Kollha", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "Żid networks", "networks.filter.add_modal.chain_list.subtitle": "Żid kwalunkwe network EVM", "networks.filter.add_modal.chain_list.title": "<PERSON><PERSON> fuq Chainlist", "networks.filter.add_modal.dapp_tip.subtitle": "Fid-dApps favoriti tiegħek, aqleb għan-network EVM li trid tuża u Zeal jistaqsik jekk tridx iżżidu mal-kartiera tiegħek.", "networks.filter.add_modal.dapp_tip.title": "Jew żid network minn kwalunkwe dApp", "networks.filter.add_networks.subtitle": "In-networks EVM kollha huma appoġġjati", "networks.filter.add_networks.title": "Żid networks", "networks.filter.add_test_networks.title": "<PERSON><PERSON> testnets", "networks.filter.tab.netwokrs": "Networks", "networks.filter.testnets.title": "Testnets", "nft.widget.emptystate": "M'hemmx oġġetti tal-kollezzjoni fil-kartiera", "nft_collection.change_account_picture.subtitle": "Inti żgur li trid taġġorna l-istampa tal-profil tiegħek?", "nft_collection.change_account_picture.title": "Aġġorna l-istampa tal-profil għal NFT", "nfts.allNfts.pricingPopup.description": "Il-prezzijiet tal-oġġetti tal-kollezzjoni huma bbażati fuq l-aħħar prezz innegozjat.", "nfts.allNfts.pricingPopup.title": "<PERSON><PERSON><PERSON><PERSON> tal-Oġġetti tal-Ko<PERSON><PERSON><PERSON>i", "no-passkeys-found.modal.cta": "Agħlaq", "no-passkeys-found.modal.subtitle": "Ma stajna nsibu l-ebda passkey ta' Zeal fuq dan l-apparat. Kun <PERSON>gur li dħalt fil-kont tal-cloud li użajt biex toħloq l-ismart wallet tiegħek.", "no-passkeys-found.modal.title": "Ma nstab l-ebda passkey", "notValidEmail.title": "Indirizz tal-email mhux validu", "notValidPhone.title": "Dan mhuwiex numru tat-telefon validu", "notification-settings.title": "Settings tan-notifiki", "notification-settings.toggles.active-wallets": "<PERSON><PERSON><PERSON> at<PERSON>", "notification-settings.toggles.bank-transfers": "Trasferimenti bankarji", "notification-settings.toggles.card-payments": "Pagamenti bil-kard", "notification-settings.toggles.readonly-wallets": "<PERSON><PERSON><PERSON> read-only", "ntft.groupHeader.text": "Oġġetti tal-kollezzjoni", "on_ramp.crypto_completed": "Les<PERSON>", "on_ramp.fiat_completed": "Les<PERSON>", "onboarding-widget.subtitle.card_created_from_order.left": "Kard tal-Visa", "onboarding-widget.subtitle.card_created_from_order.right": "Attiva l-kard", "onboarding-widget.subtitle.card_order_ready.left": "Kard fiżika tal-Visa", "onboarding-widget.subtitle.default": "Trasferimenti bankarji u Kard tal-Visa", "onboarding-widget.title.card-order-in-progress": "Kompli l-ordni tal-kard", "onboarding-widget.title.card_created_from_order": "Il<PERSON><PERSON><PERSON> intbagħ<PERSON>t", "onboarding-widget.title.kyc_approved": "Trasferimenti u Kard lesti", "onboarding-widget.title.kyc_failed": "Il-kont mhux possibbli", "onboarding-widget.title.kyc_not_started": "Kompli s-setup", "onboarding-widget.title.kyc_started_documents_requested": "Imla l-verifika", "onboarding-widget.title.kyc_started_resubmission_requested": "Erġa' pprova l-verifika", "onboarding-widget.title.kyc_started_verification_in_progress": "Qed nivverifikaw l-identità", "onboarding.loginOrCreateAccount.amountOfAssets": "$10bn+ f'assi", "onboarding.loginOrCreateAccount.cards.subtitle": "Disponibbli biss f'ċerti reġjuni. <PERSON><PERSON>, taċċetta t- <Terms>Termini tagħna</Terms> u l- <PrivacyPolicy>Politika tal-Privatezza</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Kard tal-Visa bi dħul {br}għoli u mingħajr tariffi", "onboarding.loginOrCreateAccount.createAccount": "<PERSON><PERSON><PERSON><PERSON>", "onboarding.loginOrCreateAccount.earn.subtitle": "Id-d<PERSON><PERSON> ivarja; kapital f'riskju. <PERSON><PERSON>, taċċetta t- <Terms>Termini tagħna</Terms> u l- <PrivacyPolicy>Politika tal-Privatezza</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "Qligħ ta' {percent} fis-sena{br}Fdat minn {currencySymbol}5bn+", "onboarding.loginOrCreateAccount.earningPerYear": "Qligħ ta' {percent}{br}fis-sena", "onboarding.loginOrCreateAccount.login": "<PERSON><PERSON>ħol", "onboarding.loginOrCreateAccount.trading.subtitle": "Kapital f'riskju. <PERSON><PERSON>, taċċetta t- <Terms>Termini tagħna</Terms> u l- <PrivacyPolicy>Politika tal-Privatezza</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "<PERSON><PERSON><PERSON> f'kollox, {br}minn BTC sa S&P", "onboarding.loginOrCreateAccount.trustedBy": "<PERSON>wi<PERSON><PERSON> tal-flus di<PERSON>{br}Fdat minn {assets}", "onboarding.wallet_stories.close": "Agħlaq", "onboarding.wallet_stories.previous": "<PERSON><PERSON> q<PERSON>l", "order-earn-deposit-bridge.deposit": "Depożitu", "order-earn-deposit-bridge.into": "F'", "otpIncorrectMessage": "Il-kodiċi ta' konferma mhuwiex korrett", "passkey-creation-not-possible.modal.close": "Agħlaq", "passkey-creation-not-possible.modal.subtitle": "Ma stajniex noħolqu passkey għall-kartiera tiegħek. Jekk jogħġbok kun żgur li t-tagħmir tiegħek jappoġġja l-passkeys u erġa' pprova. <link>Ikkuntattja lis-support</link> jekk il-problema tippersisti.", "passkey-creation-not-possible.modal.title": "Impossibbli toħloq passkey", "passkey-not-supported-in-mobile-browser.modal.cta": "<PERSON><PERSON><PERSON><PERSON>", "passkey-not-supported-in-mobile-browser.modal.subtitle": "L-iSmart Wallets mhumiex appoġġjati fuq il-browsers tal-mowbajl.", "passkey-not-supported-in-mobile-browser.modal.title": "Niżżel l-app ta' Zeal biex tkompli", "passkey-recovery.recovering.deploy-signer.loading-text": "<PERSON><PERSON> nivver<PERSON><PERSON>w il-passkey", "passkey-recovery.recovering.loading-text": "<PERSON><PERSON> nirk<PERSON> il-kartiera", "passkey-recovery.recovering.signer-not-found.subtitle": "Ma stajniex nikkonnettjaw il-passkey tiegħek ma' kartiera attiva. Jekk għandek fondi, ikkuntattja lit-tim ta' Zeal għall-għajnuna.", "passkey-recovery.recovering.signer-not-found.title": "Ma nstabet l-ebda kartiera", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "<PERSON><PERSON>rova passkey oħra", "passkey-recovery.select-passkey.banner.subtitle": "Kun <PERSON>gur li dħalt fil-kont it-tajjeb fuq l-apparat tiegħek. Il-passkeys huma speċifiċi għall-kont.", "passkey-recovery.select-passkey.banner.title": "<PERSON>hux qed tara l-passkey tal-kartiera tiegħek?", "passkey-recovery.select-passkey.continue": "Ag<PERSON><PERSON><PERSON> passkey", "passkey-recovery.select-passkey.subtitle": "Agħ<PERSON>el il-passkey marbuta mal-kartiera tiegħek biex terġa' tikseb l-aċċess.", "passkey-recovery.select-passkey.title": "Ag<PERSON><PERSON><PERSON>", "passkey-story_1.subtitle": "B'Smart Wallet tista' tħallas it-tariffa tan-netwerk fil-biċċa l-kbira tat-tokens, u m'għandekx għalfejn tinkwieta dwarha.", "passkey-story_1.title": "Insa l-gas - ħallas it-tariffa tan-netwerk b'ħafna tokens", "passkey-story_2.subtitle": "Mibni fuq il-kuntratti intelliġenti ta' Safe, li j<PERSON><PERSON><PERSON>w aktar minn $100 biljun f'aktar minn 20 miljun kartiera.", "passkey-story_2.title": "<PERSON><PERSON><PERSON><PERSON> minn <PERSON>", "passkey-story_3.subtitle": "L-iSmart Wallets jaħdmu fuq in-networks ewlenin kompatibbli mal-Ethereum. Iċċekkja n-networks appoġġjati qabel ma tibgħat l-assi.", "passkey-story_3.title": "Networks ewlenin tal-EVM appoġġjati", "password.add.header": "<PERSON><PERSON><PERSON><PERSON> password", "password.add.includeLowerAndUppercase": "<PERSON><PERSON> u kbar", "password.add.includesNumberOrSpecialChar": "<PERSON><PERSON><PERSON> jew simbolu wieħed", "password.add.info.subtitle": "<PERSON>bag<PERSON>tux il-password tiegħek lis-servers tagħna jew nag<PERSON><PERSON><PERSON><PERSON> backup.", "password.add.info.t_and_c": "<PERSON><PERSON>, taċċetta t- <Terms>Termini tagħna</Terms> u l- <PrivacyPolicy>Politika tal-Privatezza</PrivacyPolicy>", "password.add.info.title": "Il-password tiegħek tibqa' fuq dan it-tagħmir.", "password.add.inputPlaceholder": "<PERSON><PERSON><PERSON><PERSON> password", "password.add.shouldContainsMinCharsCheck": "10+ karat<PERSON>", "password.add.subheader": "Se tuża l-password tiegħek biex tiftaħ Zeal", "password.add.success.title": "Password maħluqa 🔥", "password.confirm.header": "Ikkonferma l-password", "password.confirm.passwordDidNotMatch": "Il-passwords i<PERSON><PERSON>", "password.confirm.subheader": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> il-password tiegħek", "password.create_pin.subtitle": "Dan il-passcode jsakkar l-app ta' Zeal", "password.create_pin.title": "Oħloq il-passcode tiegħek", "password.enter_pin.title": "Daħħal il-passcode", "password.incorrectPin": "Passcode mhux korrett", "password.pin_is_not_same": "Il-passcode ma jaqbilx", "password.placeholder.enter": "<PERSON><PERSON><PERSON><PERSON> il-password", "password.placeholder.reenter": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> il-password", "password.re_enter_pin.subtitle": "Erġ<PERSON><PERSON> <PERSON>ħħal l-istess passcode", "password.re_enter_pin.title": "Ikkonferma l-passcode", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "Għaddejja", "pending-send.details.pending": "Għaddejja", "pending-send.details.processing": "Qed tiġi pproċessata", "permit-info.modal.description": "Il-permessi huma talbiet li, jek<PERSON> jiġu ffi<PERSON>ti, i<PERSON><PERSON><PERSON> lill-apps iċaqalqu t-tokens tiegħek f'ismek, pereżempju biex tagħmel swap.{br}Il-permessi huma simili għall-Approvazzjonijiet imma ma jiswew ebda tariffa tan-netwerk biex tiffirmahom.", "permit-info.modal.title": "<PERSON>'inhum<PERSON> l-Permessi?", "permit.edit-expiration": "Editja {currency} l-iskadenza", "permit.edit-limit": "Immodifika {currency} l-limitu tal-infiq", "permit.edit-modal.expiresIn": "Jiskadi fi…", "permit.expiration-warning": "{currency} twissija ta' skadenza", "permit.expiration.info": "{currency} informazzjoni dwar l-iskadenza", "permit.expiration.never": "<PERSON><PERSON>", "permit.spend-limit.info": "{currency} informazzjoni dwar il-limitu tal-infiq", "permit.spend-limit.warning": "{currency} twissija dwar il-limitu tal-infiq", "phoneNumber.title": "numru tat-telefon", "physicalCardOrderFlow.cardOrdered": "Kard ordnata", "physicalCardOrderFlow.city": "Belt", "physicalCardOrderFlow.orderCard": "Ordna l-Kard", "physicalCardOrderFlow.postcode": "Kodiċi Postali", "physicalCardOrderFlow.shippingAddress.subtitle": "Fejn se tintbagħat il-kard tiegħek", "physicalCardOrderFlow.shippingAddress.title": "Indirizz tal-kunsinna", "physicalCardOrderFlow.street": "Triq", "placeholderDapps.1inch.description": "<PERSON><PERSON><PERSON><PERSON> bl-a<PERSON><PERSON> rotot", "placeholderDapps.aave.description": "Sellef u silif tokens", "placeholderDapps.bungee.description": "Uża Pont bejn in-networks bl-aħ<PERSON> rotot", "placeholderDapps.compound.description": "Sellef u silif tokens", "placeholderDapps.cowswap.description": "Skambja bl-a<PERSON><PERSON> rati fuq Gnosis", "placeholderDapps.gnosis-pay.description": "Immaniġġja l-kard Gnosis Pay tiegħek", "placeholderDapps.jumper.description": "Uża Pont bejn in-networks bl-aħ<PERSON> rotot", "placeholderDapps.lido.description": "Agħmel stake ta' ETH għal aktar ETH", "placeholderDapps.monerium.description": "eMoney u Trasferimenti bankarji", "placeholderDapps.odos.description": "<PERSON><PERSON><PERSON><PERSON> bl-a<PERSON><PERSON> rotot", "placeholderDapps.stargate.description": "Uża Pont jew agħmel Stake għal <14% APY", "placeholderDapps.uniswap.description": "Wieħed mill-aktar exchanges popolari", "pleaseAllowNotifications.cardPayments": "Pagamenti bil-kard", "pleaseAllowNotifications.customiseInSettings": "Personalizza mis-settings", "pleaseAllowNotifications.enable": "<PERSON><PERSON><PERSON>", "pleaseAllowNotifications.forWalletActivity": "Għall-attività tal-kartiera", "pleaseAllowNotifications.title": "Irċievi notifiki tal-kartiera", "pleaseAllowNotifications.whenReceivingAssets": "Meta tirċievi l-assi", "portfolio.quick-actions.add_funds": "<PERSON><PERSON>", "portfolio.quick-actions.buy": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.deposit": "<PERSON><PERSON><PERSON><PERSON>", "portfolio.quick-actions.send": "Ibgħat", "portfolio.view.lastRefreshed": "Aġġornat {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Agħmel top up tal-AVAX tat-testnet tiegħek", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "<PERSON>r fil-Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "Agħmel top up tal-BNB tat-testnet tiegħek", "portfolio.view.topupTestNet.BscTestnet.secondary": "<PERSON>r fil-Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "A<PERSON><PERSON><PERSON> top up tas-SepETH tat-testnet tiegħek", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Mur fis-Sepolia Faucet", "portfolio.view.topupTestNet.FantomTestnet.primary": "Agħmel top up tal-FTM tat-testnet tiegħek", "portfolio.view.topupTestNet.FantomTestnet.secondary": "<PERSON>r fil-Faucet", "privateKeyConfirmation.banner.subtitle": "Min għandu ċ-Ċavetta Privata tiegħek għandu aċċess għall-fondi. L-iscammers biss jitolbuha.", "privateKeyConfirmation.banner.title": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "privateKeyConfirmation.title": "QATT taqsam iċ-Ċavetta Privata tiegħek ma' ħadd", "rating-request.not-now": "<PERSON><PERSON><PERSON> issa", "rating-request.title": "T<PERSON>rak<PERSON>manda lil Zeal?", "receive_funds.address-text": "Dan huwa l-indirizz uniku tal-kartiera tiegħek. Tista' taqsmu ma' oħrajn mingħajr biża'.", "receive_funds.copy_address": "Ikkopja l-indirizz", "receive_funds.network-warning.eoa.subtitle": "<link><PERSON> l-lista</link>. L-assi mibgħuta fuq networks mhux EVM jintilfu.", "receive_funds.network-warning.eoa.title": "In-networks kollha bbażati fuq l-Ethereum huma appoġġjati", "receive_funds.network-warning.scw.subtitle": "<link><PERSON></link>. L-assi mibgħuta fuq networks oħra jintilfu.", "receive_funds.network-warning.scw.title": "Importanti: <PERSON><PERSON><PERSON> biss in-networks appoġġjati", "receive_funds.scan_qr_code": "Skennja QR code", "receiving.in.days": "Tirċievi fi {days}j", "receiving.this.week": "Tirċievi din il-ġimgħa", "receiving.today": "Tirċievi llum", "reference.error.maximum_number_of_characters_exceeded": "<PERSON><PERSON><PERSON>", "referral-code.placeholder": "Waħħal il-link tal-istedina", "referral-code.subtitle": "Ikklikkja l-link ta' <PERSON><PERSON><PERSON> mill-<PERSON><PERSON><PERSON>, jew waħ<PERSON><PERSON> il-link hawn taħt. Irridu niżguraw li tieħu r-rigali tiegħek.", "referral-code.title": "Bagħ<PERSON><PERSON><PERSON> ħabi<PERSON> {bReward}?", "rekyc.verification_deadline.subtitle": "<PERSON>ti l-verifika fi żmien {daysUntil} ġranet biex tibqa' tuża l-kard tiegħek.", "rekyc.verification_required.subtitle": "Lesti l-verifika biex tkompli tuża l-kard tiegħek.", "reminder.fund": "💸 <PERSON>id fondi — aqla' 6% minnufih", "reminder.onboarding": "🏁 Lesti s-setup — aqla' 6% fuq id-depożiti.", "remove-owner.confirmation.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, il-<PERSON><PERSON>t jieħdu 3 minuti. <PERSON><PERSON><PERSON>, il-kard tiġi ffriż<PERSON>.", "remove-owner.confirmation.title": "<PERSON>rd iffriżata għal 3 min waqt l-update", "restore-smart-wallet.wallet-recovered": "Kartiera rkuprata", "rewardClaimCelebration.claimedTitle": "<PERSON><PERSON> miksuba", "rewardClaimCelebration.subtitle": "<PERSON><PERSON> sted<PERSON>", "rewardClaimCelebration.title": "Aqla'jt", "rewards-warning.subtitle": "Jekk tneħħi dan il-kont, tieqaf taċċessa kwalunkwe premju marbut miegħu. Tista' tirrestawra l-kont fi kwalunkwe ħin biex tiġborhom.", "rewards-warning.title": "Se titlef l-aċċess għall-premjijiet tiegħek", "rewards.copiedInviteLink": "Link tal-is<PERSON><PERSON> k<PERSON>t", "rewards.createAccount": "Ikkopja l-link", "rewards.header.subtitle": "Nibagħtu {aReward} lilek u {bReward} lil <PERSON>, meta jonfoq {bSpendLimitReward}.", "rewards.header.title": "<PERSON><PERSON><PERSON> {amountA}{br}Agħti {amountB}", "rewards.sendInvite": "Ibgħat stedina", "rewards.sendInviteTip": "Agħ<PERSON><PERSON> ħabib u nagħtuh {bAmount}", "route.fees": "Tariffi {fees}", "routesNotFound.description": "Ir-rotta tal-exchange għall-kombinazzjoni ta' netwerks {from}-{to} mhijiex disponibbli.", "routesNotFound.title": "L-ebda rotta ta' exchange disponibbli", "rpc.OrderBuySignMessage.subtitle": "Permezz ta' Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Permezz ta' Swaps.IO", "rpc.addCustomNetwork.addNetwork": "Żid network", "rpc.addCustomNetwork.chainId": "Chain ID", "rpc.addCustomNetwork.nativeToken": "Token nattiv", "rpc.addCustomNetwork.networkName": "<PERSON><PERSON>-netwerk", "rpc.addCustomNetwork.operationDescription": "Jippermetti lil din il-websajt iżżid network mal-kartiera tiegħek. Zeal ma jistax jiċċekkja s-sigurtà tan-networks custom, kun ċert li tifhem ir-riskji.", "rpc.addCustomNetwork.rpcUrl": "RPC URL", "rpc.addCustomNetwork.subtitle": "Bl-użu ta' {name}", "rpc.addCustomNetwork.title": "Żid network", "rpc.send_token.network_not_supported.subtitle": "Qed naħdmu biex nippermettu tranżazzjonijiet fuq dan in-netwerk. Grazzi tal-paċenzja tiegħek 🙏", "rpc.send_token.network_not_supported.title": "Network dalwaqt disponibbli", "rpc.send_token.send_or_receive.settings": "Settings", "rpc.sign.accept": "Aċċetta", "rpc.sign.cannot_parse_message.body": "Ma stajniex niddekodifikaw dan il-messaġ<PERSON>. Aċċetta din it-talba biss jekk tafda lil din l-app.{br}{br}Il-messaġġi jistgħu jintużaw biex tidħol f'app, iżda jistgħu wkoll jagħtu lill-apps kontroll fuq it-tokens tiegħek.", "rpc.sign.cannot_parse_message.header": "Ipproċedi b'kawtela", "rpc.sign.import_private_key": "Importa ċ-ċwievet", "rpc.sign.subtitle": "<PERSON><PERSON><PERSON> {name}", "rpc.sign.title": "Iffirma", "safe-creation.success.title": "Kartiera maħluqa", "safe-safety-checks-popup.title": "Verifiki tas-Sigurtà tat-Tranżazzjoni", "safetyChecksPopup.title": "Verifiki tas-Sigurtà tas-Sit", "scan_qr_code.description": "Skennja QR tal-kartiera jew ikkonnettja <PERSON>", "scan_qr_code.show_qr_code": "Uri l-QR code tiegħi", "scan_qr_code.tryAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "scan_qr_code.unlockCamera": "If<PERSON>ħ il-ka<PERSON>a", "screen-lock-missing.modal.close": "Agħlaq", "screen-lock-missing.modal.subtitle": "It-<PERSON><PERSON><PERSON> tieg<PERSON> je<PERSON>ġ screen lock biex tuża l-passkeys. Jekk jogħġbok waqqaf screen lock u erġa' pprova.", "screen-lock-missing.modal.title": "M'hemmx screen lock", "seedConfirmation.banner.subtitle": "Min għandu s-Secret Phrase tiegħek għandu aċċess għall-fondi. L-iscammers biss jitolbuha.", "seedConfirmation.title": "QATT taqsam is-Secret Phrase tie<PERSON><PERSON><PERSON>", "select-active-owner.subtitle": "Agħżel wallet biex tikkonnettja ma' Zeal.", "select-active-owner.title": "Agħ<PERSON>el wallet", "select-card.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> kard", "select-crypto-currency-title": "Agħ<PERSON>el token", "select-token.title": "Agħ<PERSON>el token", "selectEarnAccount.chf.description.steps": "· Iġ<PERSON>fondi 24/7, ming<PERSON><PERSON><PERSON> ebda restrizzjoni {br}· L-imgħax jiżdied kull sekonda {br}· Depożiti super-protetti fi <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} fis-sena f'CHF", "selectEarnAccount.eur.description.steps": "· Iġ<PERSON><PERSON> 24/7, min<PERSON><PERSON><PERSON><PERSON> {br}· L-imgħax jiżdied kull sekonda {br}· Self b'protezzjoni żejda ma' <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} fis-sena f'EUR", "selectEarnAccount.subtitle": "Tista' tibdel f'kull ħin", "selectEarnAccount.title": "Agħżel il-Munita", "selectEarnAccount.usd.description.steps": "· Iġ<PERSON>fond<PERSON> 24/7, min<PERSON><PERSON><PERSON><PERSON> im<PERSON> {br}· L-imgħax jiżdied kull sekonda {br}· Depożiti b'protezzjoni żejda fi <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} fis-sena f'USD", "selectEarnAccount.zero.description_general": "Żomm il-flus diġitali mingħajr ma taqla' mgħax", "selectEarnAccount.zero.title": "0% fis-sena", "selectRechargeThreshold.button.enterAmount": "Daħħal l-ammont", "selectRechargeThreshold.button.setTo": "Issettja għal {amount}", "selectRechargeThreshold.description.line1": "Meta l-kard tiegħek tinżel taħt {amount}, tiċċarġja awtomatikament lura għal {amount} mill-kont <PERSON>arn tiegħek.", "selectRechargeThreshold.description.line2": "Mira aktar baxxa żżomm aktar flus fil-kont Earn tie<PERSON> (fejn taqla' 3%). Tista' tibdel dan f'kull ħin.", "selectRechargeThreshold.title": "Issettja l-bilanċ fil-mira tal-kard", "select_currency_to_withdraw.select_token_to_withdraw": "Agħżel token biex tiġbed", "send-card-token.form.send": "Ibgħat", "send-card-token.form.send-amount": "Ammont tat-top-up", "send-card-token.form.title": "<PERSON><PERSON> flus mal-Kard", "send-card-token.form.to-address": "<PERSON><PERSON>", "send-safe-transaction.network-fee-widget.error": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>n {amount} jew ag<PERSON><PERSON><PERSON> token ieħor", "send-safe-transaction.network-fee-widget.no-fee": "Ebda tariffa", "send-safe-transaction.network-fee-widget.title": "<PERSON><PERSON><PERSON>", "send-safe-transaction.network_fee_widget.title": "Tariffa tan-netwerk", "send.banner.fees": "<PERSON><PERSON><PERSON><PERSON> bżonn {amount} aktar {currency} biex tħallas it-tariffi", "send.banner.toAddressNotSupportedNetwork.subtitle": "Il-kartiera tad-destinatarju ma tappoġġjax {network}. Ibdel għal token appoġġjat.", "send.banner.toAddressNotSupportedNetwork.title": "Network mhux appoġġjat għad-destinatarju", "send.banner.walletNotSupportedNetwork.subtitle": "Il-kartieri Smart ma jistgħux jagħmlu tranżazzjonijiet fuq {network}. Ibdel għal token appoġġjat.", "send.banner.walletNotSupportedNetwork.title": "Network tat-token mhux appoġġjat", "send.empty-portfolio.empty-state": "Ma sibna l-ebda tokens", "send.empty-portfolio.header": "Tokens", "send.titile": "Se tibgħat", "sendLimit.success.subtitle": "Il<PERSON><PERSON><PERSON> l-<PERSON><PERSON>d jidħol fis-seħħ fi 3 minuti.", "sendLimit.success.title": "Din il-bidla tieħu 3 minuti", "send_crypto.form.disconnected.cta.addFunds": "<PERSON><PERSON>fond<PERSON>", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "<PERSON><PERSON><PERSON><PERSON> għal {network}", "send_crypto.form.disconnected.label": "Ammont għat-trasferiment", "send_to.qr_code.description": "Skenja QR code biex tibgħat lil kartiera", "send_to.qr_code.title": "Skenja QR code", "send_to_card.header": "Ibgħat lill-indirizz tal-Kard", "send_to_card.select_sender.add_wallet": "<PERSON><PERSON> wallet", "send_to_card.select_sender.header": "Ag<PERSON><PERSON><PERSON> min se jibgħat", "send_to_card.select_sender.search.default_placeholder": "Fittex indirizz jew ENS", "send_to_card.select_sender.show_card_address_button_description": "Uri l-indirizz tal-kard", "send_token.form.select-address": "Agħżel indirizz", "send_token.form.send-amount": "Ammont li se jintbagħat", "send_token.form.title": "Ibgħat", "setLimit.amount.error.zero_amount": "Mhux se tkun tista' tagħmel pagamenti", "setLimit.error.max_limit_reached": "Issettja għall-massimu {amount}", "setLimit.error.same_as_current_limit": "L-istess limitu", "setLimit.placeholder": "Attwali: {amount}", "setLimit.submit": "Issettja l-Limitu", "setLimit.submit.error.amount_required": "Daħħal Ammont", "setLimit.subtitle": "L-ammont li tista' tonfoq kuljum.", "setLimit.title": "Issettja l-limitu tan-nefqa ta' kuljum", "settings.accounts": "<PERSON><PERSON><PERSON><PERSON>", "settings.accountsSeeAll": "<PERSON> kollha", "settings.addAccount": "Żid kartiera", "settings.card": "Setting<PERSON> tal-Kard", "settings.connections": "Konnessjonijiet tal-App", "settings.currency": "Munita Predefinita", "settings.default_currency_selector.title": "<PERSON><PERSON><PERSON>", "settings.discord": "Discord", "settings.experimentalMode": "Modalità sperimentali", "settings.experimentalMode.subtitle": "Ittestja funzjonijiet ġodda", "settings.language": "Ling<PERSON>", "settings.lockZeal": "Illokkja <PERSON>", "settings.notifications": "Notifiki", "settings.open_expanded_view": "Iftaħ fil-veduta estiża", "settings.privacyPolicy": "Politika tal-Privatezza", "settings.settings": "Settings", "settings.termsOfUse": "Termini tal-Użu", "settings.twitter": "𝕏 / Twitter", "settings.version": "<PERSON><PERSON><PERSON><PERSON><PERSON> {version} ambjent: {env}", "setup-card.confirmation": "<PERSON><PERSON><PERSON>", "setup-card.confirmation.subtitle": "Agħmel pagamenti online u żid mal- {type} wallet tiegħek għal pagamenti contactless.", "setup-card.getCard": "<PERSON><PERSON><PERSON>", "setup-card.order.physicalCard": "<PERSON><PERSON>", "setup-card.order.physicalCard.steps": "· Kard fiżika VISA Gnosis Pay {br}· Tista' tieħu sa 3 ġimgħat biex tasal {br}· Użaha għal pagamenti fil-ħwienet u fl-ATMs. {br}· Żidha mal-wallet (fejn appoġġjat)", "setup-card.order.subtitle1": "Tista' tuża diversi kards fl-istess ħin", "setup-card.order.title": "X'tip ta' kard?", "setup-card.order.virtualCard": "<PERSON><PERSON> virt<PERSON>i", "setup-card.order.virtual_card.steps": "· Kard diġitali VISA Gnosis Pay {br}· Użaha minnufih għal pagamenti online {br}· Żidha mal-wallet (fejn appoġġjat)", "setup-card.orderCard": "Ordna l-kard", "setup-card.virtual-card": "<PERSON><PERSON><PERSON>", "setup.notifs.fakeAndroid.title": "Notifiki għall-ħlasijiet u trasferimenti li deħlin", "setup.notifs.fakeIos.subtitle": "Zeal jista' j<PERSON><PERSON><PERSON> meta tirċievi l-flus, jew tonfoq bil-kard tal-Visa tiegħek. Tista' tbiddel dan aktar tard.", "setup.notifs.fakeIos.title": "Notifiki għall-ħlasijiet u trasferimenti li deħlin", "sign.PermitAllowanceItem.spendLimit": "<PERSON><PERSON><PERSON> tal-infiq", "sign.ledger.subtitle": "Kompli fuq il-hardware wallet.", "sign.ledger.title": "Iffirma bil-hardware wallet", "sign.passkey.subtitle": "Il-browser tiegħek għandu jistaqsik biex tiffirma bil-passkey assoċjata ma' din il-kartiera. Jekk jogħġbok kompli hemmhekk.", "sign.passkey.title": "Ag<PERSON><PERSON><PERSON> passkey", "signal_aborted_for_uknown_reason.title": "Talba tan-netwerk imħassra", "simulatedTransaction.BridgeTrx.info.title": "<PERSON>", "simulatedTransaction.CardTopUp.info.title": "<PERSON><PERSON> il-flus mal-kard", "simulatedTransaction.CardTopUpTrx.info.title": "<PERSON>id flus mal-kard", "simulatedTransaction.NftCollectionApproval.approve": "Approva l-kollezzjoni NFT", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "<PERSON><PERSON> mal-kard", "simulatedTransaction.OrderEarnDepositBridge.title": "Iddepożita f'Earn", "simulatedTransaction.P2PTransaction.info.title": "Ibgħat", "simulatedTransaction.PermitSignMessage.title": "Permit", "simulatedTransaction.SingleNftApproval.approve": "Approva l-NFT", "simulatedTransaction.UnknownSignMessage.title": "Iffirma", "simulatedTransaction.Withdrawal.info.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.approval.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.approve.info.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.p2p.info.account": "<PERSON><PERSON>", "simulatedTransaction.p2p.info.unlabelledAccount": "Kart<PERSON> bla isem", "simulatedTransaction.unknown.info.receive": "Se tirċievi", "simulatedTransaction.unknown.info.send": "Se tibgħat", "simulatedTransaction.unknown.using": "<PERSON><PERSON> tu<PERSON> {app}", "simulation.approval.modal.text": "Meta taċċetta approvazz<PERSON>i, tkun qed tagħti permess lil app jew kuntratt speċifiku biex juża t-tokens jew l-NFTs tiegħek fi tranżazzjonijiet futuri.", "simulation.approval.modal.title": "X'inhuma l-Approvazzjonijiet?", "simulation.approval.spend-limit.label": "<PERSON><PERSON><PERSON> tal-infiq", "simulation.approve.footer.for": "Għal", "simulation.approve.unlimited": "<PERSON><PERSON> <PERSON>u", "simulationNotAvailable.title": "Azzjoni Mhux Magħrufa", "smart-wallet-activation-view.on": "Fuq", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password jista' jimblokka l-aċċess għall-kartiera tiegħek", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Uża Apple jew Google biex tistabbilixxi Zeal b'mod sigur", "smart-wallet.passkey-notice.title": "Evita 1Password", "spend-limits.high.modal.text": "Issettja limitu tal-infiq qrib l-ammont ta' tokens li se tuża ma' app jew kuntratt. Limiti għoljin huma riskjużi u jistgħu jagħmluha aktar faċli għall-iscammers biex jisirqulek it-tokens.", "spend-limits.high.modal.text_sign_message": "Il-limitu tal-infiq għandu jkun qrib l-ammont ta' tokens li se tuża ma' app jew smart contract. Limiti għoljin huma riskjużi u jistgħu jagħmluha aktar faċli għall-iscammers biex jisirqulek it-tokens.", "spend-limits.high.modal.title": "Limitu tal-infiq għoli", "spend-limits.modal.text": "Il-limitu tal-infiq huwa kemm tokens tista' tuża app f'ismek. Tista' tibdel jew tneħħi dan il-limitu meta trid. Biex tib<PERSON>' sigur, żomm il-limiti qrib l-ammont ta' tokens li se tuża.", "spend-limits.modal.title": "<PERSON>'inhu l-limitu tal-infiq?", "spent-limit-info.modal.description": "Il-limitu tal-infiq hu kemm tokens tista' tuża app f'ismek. Tista' tibdel jew tneħħi dan il-limitu meta trid. Biex tib<PERSON>' sigur, żomm il-limiti tal-infiq qrib l-ammont ta' tokens li se tuża ma' app.", "spent-limit-info.modal.title": "<PERSON>'inhu l-limitu tal-infiq?", "sswaps-io.transfer-provider": "Provider tat-trasferiment", "storage.accountDetails.activateWallet": "Attiva l-kartiera", "storage.accountDetails.changeWalletLabel": "Biddel l-isem tal-kartiera", "storage.accountDetails.deleteWallet": "Neħħi l-kartiera", "storage.accountDetails.setup_recovery_kit": "<PERSON>-<PERSON>", "storage.accountDetails.showPrivateKey": "Uri ċ-Ċavetta Privata", "storage.accountDetails.showWalletAddress": "Uri l-indirizz tal-kartiera", "storage.accountDetails.smartBackup": "Backup u Rkupru", "storage.accountDetails.viewSsecretPhrase": "Ara l-Frażi Sigrieta", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets?", "storage.manageAccounts.title": "<PERSON><PERSON><PERSON>", "submit-userop.progress.text": "<PERSON><PERSON> ji<PERSON>", "submit.error.amount_high": "Ammont għoli wisq", "submit.error.amount_hight": "Ammont għoli wisq", "submit.error.amount_low": "Ammont baxx wisq", "submit.error.amount_required": "Daħħal Ammont", "submit.error.maximum_number_of_characters_exceeded": "<PERSON><PERSON><PERSON><PERSON>", "submit.error.not_enough_balance": "M'hemmx biżżejjed bilanċ", "submit.error.recipient_required": "Riċevitur me<PERSON><PERSON>", "submit.error.routes_not_found": "Ma nstabet l-ebda rotta", "submitSafeTransaction.monitor.title": "Riżultat tat-tranżazzjoni", "submitSafeTransaction.sign.title": "Riżultat tat-tranżazzjoni", "submitSafeTransaction.state.sending": "<PERSON><PERSON> ji<PERSON>", "submitSafeTransaction.state.sign": "<PERSON><PERSON>", "submitSafeTransaction.submittingToRelayer.title": "Riżultat tat-tranżazzjoni", "submitTransaction.cancel": "Ikkanċella", "submitTransaction.cancel.attemptingToStop": "<PERSON><PERSON> ni<PERSON><PERSON>w inwa<PERSON><PERSON>ha", "submitTransaction.cancel.failedToStop": "Ma rnexxilniex inwaqqfuha", "submitTransaction.cancel.stopped": "Imwaqqfa", "submitTransaction.cancel.title": "Previżjoni tat-tranżazzjoni", "submitTransaction.failed.banner.description": "In-netwerk ħassar din it-tranżazzjoni bla mistenni. Erġa' pprova jew ikkuntattjana.", "submitTransaction.failed.banner.title": "It-tranżazzjoni falliet", "submitTransaction.failed.execution_reverted.title": "Kien hemm żball fl-app", "submitTransaction.failed.execution_reverted_without_message.title": "Kien hemm żball fl-app", "submitTransaction.failed.out_of_gas.description": "In-netwerk ħassar it-tranżazzjoni għax użat aktar tariffi tan-netwerk milli mistenni", "submitTransaction.failed.out_of_gas.title": "Żball fin-netwerk", "submitTransaction.sign.title": "Riżultat tat-tranżazzjoni", "submitTransaction.speedUp": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue": "Miżjuda fil-kju", "submitTransaction.state.addedToQueue.short": "Fil-kju", "submitTransaction.state.cancelled": "Imwaqqfa", "submitTransaction.state.complete": "{currencyCode} mi<PERSON><PERSON><PERSON>", "submitTransaction.state.complete.subtitle": "Iċċekkja l-portafoll ta' Zeal tiegħek", "submitTransaction.state.completed": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.failed": "Falliet", "submitTransaction.state.includedInBlock": "Inkluża fi blokka", "submitTransaction.state.includedInBlock.short": "<PERSON> blokka", "submitTransaction.state.replaced": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.sendingToNetwork": "<PERSON><PERSON> tintbagħat lin-netwerk", "submitTransaction.stop": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.submit": "Ibgħat", "submitted-user-operation.state.bundled": "Fil-kju", "submitted-user-operation.state.completed": "<PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.failed": "Falliet", "submitted-user-operation.state.pending": "<PERSON><PERSON> tint<PERSON>at", "submitted-user-operation.state.rejected": "Rifjutata", "submittedTransaction.failed.title": "It-tranżazzjoni falliet", "success_splash.card_activated": "<PERSON><PERSON> attivata", "supportFork.give-feedback.title": "Agħti feedback", "supportFork.itercom.description": "Zeal j<PERSON><PERSON><PERSON> f'mist<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, jew <PERSON><PERSON><PERSON>.", "supportFork.itercom.title": "Mistoqsijiet dwar il-kartiera", "supportFork.title": "<PERSON><PERSON><PERSON> dwar", "supportFork.zendesk.subtitle": "Gnosis Pay jgħin f'mistoqsijiet dwar ħlas<PERSON>et bil-kard, verifika tal-identità, jew rifu<PERSON>.", "supportFork.zendesk.title": "Ħlasijiet bil-kard u identità", "supported-networks.ethereum.warning": "Tariffi għoljin", "supportedNetworks.networks": "Networks appoġġjati", "supportedNetworks.oneAddressForAllNetworks": "Indirizz wieħed għan-networks kollha", "supportedNetworks.receiveAnyAssets": "Irċievi assi min-networks kollha fl-istess indirizz.", "swap.form.error.no_routes_found": "<PERSON> nstabu l-ebda rotot", "swap.form.error.not_enough_balance": "M'hemmx biżżejjed bilanċ", "swaps-io-details.bank.serviceProvider": "Fornitur tas-servizz", "swaps-io-details.details.processing": "Qed tiġi pproċessata", "swaps-io-details.pending": "Pendent<PERSON>", "swaps-io-details.rate": "<PERSON><PERSON>", "swaps-io-details.serviceProvider": "Fornitur tas-servizz", "swaps-io-details.transaction.from.processing": "Tranżazzjoni mibdija", "swaps-io-details.transaction.networkFees": "Tariffi tan-netwerk", "swaps-io-details.transaction.state.completed-transaction": "Tranżazzjoni kompluta", "swaps-io-details.transaction.state.started-transaction": "Tranżazzjoni mibdija", "swaps-io-details.transaction.to.processing": "Tranżazzjoni kompluta", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Id-depożitu għandu jitlesta dalwaqt. Kinetex għadu qed jipproċessa t-tranżazzjoni tiegħek.", "swapsIO.monitoring.awaitingLiqSend.title": "<PERSON><PERSON><PERSON><PERSON>", "swapsIO.monitoring.awaitingRecive.title": "Relaying", "swapsIO.monitoring.awaitingSend.title": "Fil-kju", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "It-tokens intbagħtu lil Kinetex, iżda se jintbagħtu lura dalwaqt. Kinetex ma setax ilesti t-tranżazzjoni tad-destinazzjoni.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Qed jintbag<PERSON>tu lura t-tokens", "swapsIO.monitoring.cancelledNoSlash.subtitle": "It-tokens ma ġewx trasferiti minħabba żball mhux magħruf. Jekk jogħġbok erġa' pprova.", "swapsIO.monitoring.cancelledNoSlash.title": "Tokens mibgħuta lura", "swapsIO.monitoring.cancelledSlashed.subtitle": "It-tokens intbagħtu lura. Kinetex ma setax ilesti t-tranżazzjoni tad-destinazzjoni.", "swapsIO.monitoring.cancelledSlashed.title": "Tokens mibgħuta lura", "swapsIO.monitoring.completed.title": "Les<PERSON>", "taker-metadata.earn": "Aqla' f'USD diġitali ma' Sky", "taker-metadata.earn.aave": "Aqla' f'EUR diġitali ma' Aave", "taker-metadata.earn.aave.cashout24": "<PERSON><PERSON><PERSON> istantanja<PERSON>, 24/7", "taker-metadata.earn.aave.trusted": "Fdat b'$27B, 2+ snin", "taker-metadata.earn.aave.yield": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jin<PERSON><PERSON> kull sekonda", "taker-metadata.earn.chf": "Aqla' f'CHF diġitali", "taker-metadata.earn.chf.cashout24": "<PERSON><PERSON><PERSON> flu<PERSON>k immedjatament, 24/7", "taker-metadata.earn.chf.trusted": "Fdat bi Fr. 28M", "taker-metadata.earn.chf.yield": "<PERSON>r-rendi<PERSON> ji<PERSON><PERSON>d kull sekonda", "taker-metadata.earn.usd.cashout24": "<PERSON><PERSON><PERSON> istantanja<PERSON>, 24/7", "taker-metadata.earn.usd.trusted": "Fdat b'$10.7B, 5+ snin", "taker-metadata.earn.usd.yield": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jin<PERSON><PERSON> kull sekonda", "test": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "to.titile": "Se tirċievi", "token.groupHeader.cashback": "Flus lura", "token.groupHeader.title": "<PERSON><PERSON>", "token.groupHeader.titleWithSum": "Assi {sum}", "token.hidden_tokens.page.title": "Tokens moħbija", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "Żid token", "token.widget.cashback_empty": "Għad m'hemmx tranżazzjonijiet", "token.widget.emptyState": "L-ebda token fil-kartiera", "tokens.cash": "Flus", "top-up-card-from-earn-view.approve.for": "Għal", "top-up-card-from-earn-view.approve.into": "Fi", "top-up-card-from-earn-view.swap.from": "<PERSON>n", "top-up-card-from-earn-view.swap.to": "<PERSON><PERSON>", "top-up-card-from-earn-view.withdraw.to": "<PERSON><PERSON>", "top-up-card-from-earn.trx.title.approval": "Approva s-swap", "top-up-card-from-earn.trx.title.swap": "<PERSON><PERSON> mal-kard", "top-up-card-from-earn.trx.title.withdrawal": "<PERSON><PERSON><PERSON> minn <PERSON>n", "topUpDapp.connectWallet": "Qabbad il-kartiera", "topup-fee-breakdown.bungee-fee": "Tariffa tal-fornitur estern", "topup-fee-breakdown.header": "Tariffa tat-tranżazzjoni", "topup-fee-breakdown.network-fee": "Tariffa tan-netwerk", "topup-fee-breakdown.total-fee": "Tariffa totali", "topup.continue-in-wallet": "Kompli fil-kartiera tiegħek", "topup.send.title": "Ibgħat", "topup.submit-transaction.close": "Agħlaq", "topup.submit-transaction.sent-to-wallet": "Ibgħat {amount}", "topup.to": "<PERSON>", "topup.transaction.complete.close": "Agħlaq", "topup.transaction.complete.try-again": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "transaction-request.nonce-too-low.modal.button-text": "Agħlaq", "transaction-request.nonce-too-low.modal.text": "Diġà tlestiet tranżazzjoni bl-istess numru tas-serje (nonce), għalhekk ma tistax tibgħat din it-tranżazzjoni. Dan jista' jiġ<PERSON> jekk tagħmel tranżazzjonijiet wara xulxin jew jekk tipprova tħaffef jew tħassar tranżazzjoni li diġà tlestiet.", "transaction-request.nonce-too-low.modal.title": "Tranżazzjoni bl-istess nonce tlestiet", "transaction-request.replaced.modal.button-text": "Agħlaq", "transaction-request.replaced.modal.text": "Ma nistgħux insegwu l-istatus ta' din it-tranżazzjoni. Jew ġiet mibdula bi tranżazzjoni oħra jew l-RPC node għandu l-problemi.", "transaction-request.replaced.modal.title": "L-istatus tat-tranżazzjoni ma nstabx", "transaction.activity.details.modal.close": "Agħlaq", "transaction.cancel_popup.cancel": "Le, stenna", "transaction.cancel_popup.confirm": "<PERSON><PERSON>, waqqaf", "transaction.cancel_popup.description": "<PERSON><PERSON><PERSON> twa<PERSON>, trid tħallas tariffa tan-netwerk ġdida minflok it-tariffa oriġinali ta' {oldFee}", "transaction.cancel_popup.description_without_original": "<PERSON><PERSON><PERSON> twa<PERSON>, trid tħallas tariffa tan-netwerk ġdida", "transaction.cancel_popup.not_supported.subtitle": "It-twaqqif tat-tranżazzjonijiet mhuwiex appoġġjat fuq {network}", "transaction.cancel_popup.not_supported.title": "Mhux appoġġ<PERSON>t", "transaction.cancel_popup.stopping_fee": "Tariffa tan-netwerk għat-twaqqif", "transaction.cancel_popup.title": "Twaqqaf it-tranżazzjoni?", "transaction.in-progress": "Għaddejja", "transaction.inProgress": "Għaddejja", "transaction.speed_up_popup.cancel": "Le, stenna", "transaction.speed_up_popup.confirm": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "transaction.speed_up_popup.description": "<PERSON>ie<PERSON> tħ<PERSON><PERSON><PERSON>, trid tħallas tariffa tan-netwerk ġdida minflok it-tariffa oriġinali ta' {amount}", "transaction.speed_up_popup.description_without_original": "<PERSON>ie<PERSON> tħ<PERSON><PERSON><PERSON>, trid tħallas tariffa tan-netwerk ġdida", "transaction.speed_up_popup.seed_up_fee_title": "Tariffa biex tħaffef in-netwerk", "transaction.speed_up_popup.title": "Tħ<PERSON><PERSON><PERSON> it-tranżazzjoni?", "transaction.speedup_popup.not_supported.subtitle": "It-tħ<PERSON><PERSON><PERSON> tat-tranżazzjonijiet mhuwiex appoġġjat fuq {network}", "transaction.speedup_popup.not_supported.title": "Mhux appoġġ<PERSON>t", "transaction.subTitle.failed": "Falliet", "transactionDetails.cashback.not-qualified": "<PERSON><PERSON><PERSON>", "transactionDetails.cashback.paid": "{amount} imħallas", "transactionDetails.cashback.pending": "{amount} pendenti", "transactionDetails.cashback.title": "Flus lura", "transactionDetails.cashback.unknown": "<PERSON><PERSON><PERSON> ma<PERSON>", "transactionDetails.cashback_estimate": "Stima tal-flus lura", "transactionDetails.category": "Kategorija", "transactionDetails.exchangeRate": "<PERSON>a tal-kamb<PERSON>", "transactionDetails.location": "Post", "transactionDetails.payment-approved": "Pagament Approvat", "transactionDetails.payment-declined": "Pagament Miċħud", "transactionDetails.payment-reversed": "Pagament Imreġġ<PERSON>' Lu<PERSON>", "transactionDetails.recharge.amountSentFromEarn.title": "Ammont mibgħut minn Earn", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "Iċċarġjat lill-kard", "transactionDetails.recharge.rate.title": "<PERSON><PERSON>", "transactionDetails.recharge.transactionId.title": "ID tat-tranżazzjoni", "transactionDetails.refund": "Rifużjoni", "transactionDetails.reversal": "Treġġigħ lura", "transactionDetails.transactionCurrency": "Munita tat-tranżazzjoni", "transactionDetails.transactionId": "ID tat-tranżazzjoni", "transactionDetails.type": "Tranżazzjoni", "transactionRequestWidget.approve.subtitle": "<PERSON><PERSON><PERSON> {target}", "transactionRequestWidget.p2p.subtitle": "Lil {target}", "transactionRequestWidget.unknown.subtitle": "Bl-użu ta' {target}", "transactionSafetyChecksPopup.title": "Kontrolli tas-Sigurtà tat-Tranżazzjoni", "transactions.main.activity.title": "Attività", "transactions.page.hiddenActivity.title": "Attività moħbija", "transactions.page.title": "Attività", "transactions.viewTRXHistory.emptyState": "Għad m'hemmx tranżazzjonijiet", "transactions.viewTRXHistory.errorMessage": "Ma rnexxilniex intellgħu l-istorja tat-tranżazzjonijiet tiegħek", "transactions.viewTRXHistory.hidden.emptyState": "M'hemmx tranżazzjonijiet moħbija", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Attività mhux appoġġjata għat-testnets", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Attività mhux appoġġjata għat-testnets{br}<link>Mur fil-block explorer</link>", "transfer_provider": "Fornitur tat-trasferiment", "transfer_setup_with_different_wallet.subtitle": "Trasferimenti diġà ma' kartiera oħra.", "transfer_setup_with_different_wallet.swtich_and_continue": "<PERSON><PERSON><PERSON><PERSON> u kompli", "transfer_setup_with_different_wallet.title": "Aqleb il-kartiera", "tx-sent-to-wallet.button": "Agħlaq", "tx-sent-to-wallet.subtitle": "Kompli fi {wallet}", "unblockProviderInfo.fees": "Tariffi: 0% sa $5k/xahar, 0.2% 'il fuq.", "unblockProviderInfo.registration": "Unblock hu rreġistrat u awtorizzat. <link>Sir af aktar</link>", "unblockProviderInfo.selfCustody": "Il-flus diġitali huma fil-kontroll tiegħek.", "unblock_invalid_faster_payment_configuration.subtitle": "Il-kont bankarju li tajt ma jappoġġjax trasferimenti SEPA Ewropej jew UK Faster Payments. Jekk jogħġbok uża kont ieħor.", "unblock_invalid_faster_payment_configuration.title": "Hu <PERSON> kont differenti", "unknownTransaction.primaryText": "Tranżazzjoni bil-kard", "unsupportedCountry.subtitle": "It-trasferimenti bankarji għadhom mhumiex disponibbli f'pajjiżek.", "unsupportedCountry.title": "Mhux disponibbli fi {country}", "update-app-popup.subtitle": "L-aħħar aġġornament fih soluzzjonijiet, funzjonijiet ġodda u aktar. Aġġorna għall-aħħar verżjoni u tejjeb l-esperjenza tiegħek fuq Zeal.", "update-app-popup.title": "Aġġorna l-verżjoni ta' Zeal", "update-app-popup.update-now": "Aġġorna Issa", "user_associated_with_other_merchant.subtitle": "Din il-kartiera ma tistax tintuża għat-trasferimenti. Uża kartiera oħra jew irrapporta fuq id-Discord tagħna.", "user_associated_with_other_merchant.title": "Il-kartiera ma tistax tintuża", "user_associated_with_other_merchant.try_with_another_wallet": "Ipprova b'kartiera oħra", "user_email_already_exists.subtitle": "Użajt kartiera oħra. Erġa' pprova biha.", "user_email_already_exists.title": "Trasferimenti kkonfigurati b'kartiera oħra", "user_email_already_exists.try_with_another_wallet": "Ipprova b'kartiera oħra", "validation.invalid.iban": "IBAN invalidu", "validation.required": "<PERSON><PERSON><PERSON>", "validation.required.first_name": "<PERSON><PERSON>", "validation.required.iban": "IBAN <PERSON>", "validation.required.last_name": "<PERSON><PERSON><PERSON><PERSON>", "verify-passkey.cta": "Ivverifika l-passkey", "verify-passkey.subtitle": "Ivverifika li l-passkey tiegħek inħolqot u hija assigurata sew.", "verify-passkey.title": "Ivverifika l-passkey", "view-cashback.cashback-next-cycle": "Rata tal-flus lura fi {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "Iddepożita biex tikseb flus lura", "view-cashback.pending": "{money} Pen<PERSON>i", "view-cashback.pending-rewards.not_paid": "Tirċievi fi {days}j", "view-cashback.pending-rewards.paid": "Irċevejt din il-ġimgħa", "view-cashback.received-rewards": "Premjijiet li rċevejt", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Pagamenti mhux ikkonfermati", "view-cashback.upcoming": "<PERSON> jmiss {money}", "virtual-card-order.configure-safe.loading-text": "<PERSON><PERSON> il-kard", "virtual-card-order.create-order.loading-text": "<PERSON>ed tiġi attivata l-kard", "virtual-card-order.create-order.success-text": "<PERSON><PERSON> attivata", "virtualCard.activateCard": "Attiva l-Kard", "walletDeleteConfirm.main_action": "Neħħi", "walletDeleteConfirm.subtitle": "Ikollok terġa' timportaha biex tara l-portafoll jew tagħmel tranżazzjonijiet", "walletDeleteConfirm.title": "Tneħħi l-kartiera?", "walletSetting.header": "Settings tal-Kartiera", "wallet_connect.connect.cancel": "Ikkanċella", "wallet_connect.connect.connect_button": "<PERSON><PERSON><PERSON> Zeal", "wallet_connect.connect.title": "Ikkonnettja", "wallet_connect.connected.title": "<PERSON><PERSON><PERSON>", "wallet_connect_add_chain_missing.title": "Netwerk mhux appoġġjat", "wallet_connect_proposal_expired.title": "Konnessjoni skadiet", "withdraw": "<PERSON><PERSON><PERSON>", "withdraw.confirmation.close": "Ikkanċella", "withdraw.confirmation.continue": "Ikkonferma", "withdrawal_request.completed": "Les<PERSON>", "withdrawal_request.pending": "Pendent<PERSON>", "zeal-dapp.connect-wallet.cta.primary.connecting": "Qed nik<PERSON>...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Qabbad", "zeal-dapp.connect-wallet.cta.secondary": "Ikkanċella", "zeal-dapp.connect-wallet.title": "Qabbad il-kartiera biex tkompli", "zealSmartWalletInfo.gas": "Ħallas it-tariffa tan-netwerk b'ħafna tokens; uża tokens ERC20 popolari fuq networks appoġġjati, mhux biss tokens nattivi.", "zealSmartWalletInfo.recover": "M'hemmx Frażijiet Sigrieti; Irkupra billi tuża passkey bijometrika mill-password manager, l-iCloud jew il-kont tal-Google tiegħek.", "zealSmartWalletInfo.selfCustodial": "Kartiera kompletament privata; L-iffirmar bil-passkey jiġi vvalidat on-chain biex jitnaqqsu d-dipendenzi ċentrali.", "zealSmartWalletInfo.title": "Dwar l-iSmart Wallets ta' Zeal", "zeal_a_rewards_already_claimed_error.title": "Premju diġà nġabar", "zwidget.minimizedDisconnected.label": "<PERSON><PERSON>"}