{"Account.ListItem.details.label": "Details", "AddFromAddress.success": "<PERSON>et g<PERSON><PERSON>", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{Keine Wallets} one{{count} Wallet} other{{count} Wallets}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Geheime Phrase {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "<PERSON><PERSON><PERSON> neue Wallets aus einer deiner bestehenden geheimen Phrasen", "AddFromExistingSecretPhrase.SelectPhrase.title": "<PERSON><PERSON>hle eine geheime Phrase", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "Deine geheime Phrase kann viele Wallets sichern. W<PERSON>hle die aus, die du verwenden möchtest.", "AddFromExistingSecretPhrase.WalletSelection.title": "<PERSON>et schnell hinzufügen", "AddFromExistingSecretPhrase.success": "<PERSON><PERSON> zu Zeal hinzugefügt", "AddFromHardwareWallet.subtitle": "W<PERSON>hle deine Hardware-<PERSON><PERSON> aus, um sie mit Zeal zu verbinden", "AddFromHardwareWallet.title": "Hardware-Wallet", "AddFromNewSecretPhrase.WalletSelection.subtitle": "<PERSON><PERSON>hle die Wallets aus, die du importieren möchtest", "AddFromNewSecretPhrase.WalletSelection.title": "Wallets importieren", "AddFromNewSecretPhrase.accounts": "Wallets", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "Eine geheime Wiederherstellungsphrase funktioniert wie ein Schlüsselbund für Millionen von Wallets, jedes mit einem einzigartigen Privatschlüssel.{br}{br}Du kannst jetzt so viele Wallets importieren, wie du benötigst, oder später weitere hinzufügen.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Wallets mit geheimer Wiederherstellungsphrase", "AddFromNewSecretPhrase.subtitle": "Gib deine geheime Wiederherstellungsphrase mit Leerzeichen getrennt ein", "AddFromNewSecretPhrase.success_secret_phrase_added": "Geheime Wiederherstellungsphrase hinzugefügt 🎉", "AddFromNewSecretPhrase.success_wallets_added": "<PERSON><PERSON> zu Zeal hinzugefügt", "AddFromNewSecretPhrase.wallets": "Wallets", "AddFromPrivateKey.subtitle": "Gib deinen Privatschlüssel ein", "AddFromPrivateKey.success": "Privatschlüssel hinzugefügt 🎉", "AddFromPrivateKey.title": "<PERSON><PERSON> wied<PERSON><PERSON><PERSON>len", "AddFromPrivateKey.typeOrPaste": "<PERSON>er tippen oder ein<PERSON>ügen", "AddFromSecretPhrase.importWallets": "{count,plural,=0{Keine Wallets ausgewählt} one{Wallet importieren} other{{count} Wallets importieren}}", "AddFromTrezor.AccountSelection.title": "Trezor-Wallets importieren", "AddFromTrezor.hwWalletTip.subtitle": "Ein Hardware-Wallet enthält Millionen von Wallets mit unterschiedlichen Adressen. Du kannst jetzt so viele Wallets importieren, wie du benötigst, oder später weitere hinzufügen.", "AddFromTrezor.hwWalletTip.title": "<PERSON><PERSON><PERSON> von Hardware-Wallets", "AddFromTrezor.importAccounts": "{count,plural,=0{Keine Wallets ausgewählt} one{Wallet importieren} other{{count} Wallets importieren}}", "AddFromTrezor.success": "<PERSON><PERSON> zu Zeal hinzugefügt", "ApprovalSpenderTypeCheck.failed.subtitle": "Wahrscheinlich Betrug: Ausgabenberechtigte sollten Smart Contracts sein", "ApprovalSpenderTypeCheck.failed.title": "Ausgabenberechtigter ist ein Wallet, kein Smart Contract", "ApprovalSpenderTypeCheck.passed.subtitle": "Normalerweise genehmigst du Assets für Smart Contracts", "ApprovalSpenderTypeCheck.passed.title": "Ausgabenberechtigter ist ein Smart Contract", "BestReturns.subtitle": "<PERSON>ser Swap-<PERSON><PERSON><PERSON> liefert dir das beste Ergebnis, inklusive aller Gebühren.", "BestReturnsPopup.title": "Beste Ergebnisse", "BlacklistCheck.Failed.subtitle": "Böswillige Meldungen von <source></source>", "BlacklistCheck.Failed.title": "Seite steht auf der Blacklist", "BlacklistCheck.Passed.subtitle": "<PERSON><PERSON> b<PERSON>swilligen Meldungen von <source></source>", "BlacklistCheck.Passed.title": "Seite steht nicht auf der Blacklist", "BlacklistCheck.failed.statusButton.label": "Seite wurde gemeldet", "BridgeRoute.slippage": "Slippage {slippage}", "BridgeRoute.title": "Bridge-Anbieter", "CheckConfirmation.InProgress": "In Bearbeitung ...", "CheckConfirmation.success.splash": "Abgeschlossen", "ChooseImportOrCreateSecretPhrase.subtitle": "Importiere eine geheime Wiederherstellungsphrase oder erstelle eine neue", "ChooseImportOrCreateSecretPhrase.title": "Geheime Wiederherstellungsphrase hinzufügen", "ConfirmTransaction.Simuation.Skeleton.title": "Sicherheitschecks werden ausgeführt …", "ConnectionSafetyCheckResult.passed": "Sicherheitsprüfung bestanden", "ContactGnosisPaysupport": "Gnosis Pay kontaktieren", "CopyKeyButton.copied": "<PERSON><PERSON><PERSON>", "CopyKeyButton.copyYourKey": "<PERSON>inen Schlüssel kopieren", "CopyKeyButton.copyYourPhrase": "Deine Phrase kopieren", "DAppVerificationCheck.Failed.subtitle": "Seite ist nicht gelistet auf <source></source>", "DAppVerificationCheck.Failed.title": "Seite in App-Verzeichnissen nicht gefunden", "DAppVerificationCheck.Passed.subtitle": "Seite ist gelistet auf <source></source>", "DAppVerificationCheck.Passed.title": "Seite erscheint in App-Verzeichnissen", "DAppVerificationCheck.failed.statusButton.label": "Seite in App-Verzeichnissen nicht gefunden", "ERC20.tokens.emptyState": "<PERSON><PERSON> gefunden", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Prioritätsgebühr", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Letzte {period}: zwi<PERSON> {from} und {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Grundgebühr: {baseFee} • Sicherheitspuffer: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Wir konnten die aktuelle Grundgebühr nicht abrufen", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Höher als die nächste Nonce. Wird fehlschlagen", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "Nonce kann nicht niedriger als die aktuelle Nonce sein", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "Wir konnten die Prioritätsgebühr nicht berechnen", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Wir konnten die aktuelle max. Gebühr nicht abrufen", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "<PERSON><PERSON>", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Könnte blockiert bleiben, bis die Netzwerkgebühren sinken", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "<PERSON><PERSON>", "EditFeeModal.Custom.gasLimit.title": "Gas-Limit {gasLimit}", "EditFeeModal.Custom.title": "Erweiterte Einstellungen", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Wird block<PERSON>t, bis die Grundgebühr sinkt", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Niedrige Gebühr. Könnte fehlschlagen", "EditFeeModal.EditGasLimit.estimatedGas": "Gesch. Gas: {estimated} • Sicherheitspuffer: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Weniger als geschätztes Limit. Transaktion wird fehlschlagen", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Weniger als empfohlenes Limit. Transaktion könnte fehlschlagen", "EditFeeModal.EditGasLimit.subtitle": "Lege den maximalen Gas-Betrag fest, den diese Transaktion verwenden soll. Deine Transaktion schlägt fehl, wenn du ein niedrigeres Limit als nötig festlegst", "EditFeeModal.EditGasLimit.title": "Gas-Limit bearbeiten", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "<PERSON><PERSON> als das Mindest-Gaslimit: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "Höher als die nächste Nonce. Wird blockiert", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "Nonce kann nicht niedriger als die aktuelle Nonce sein", "EditFeeModal.EditNonce.subtitle": "Deine Transaktion wird blockiert, wenn du eine andere als die nächste Nonce festlegst", "EditFeeModal.EditNonce.title": "<PERSON><PERSON> bear<PERSON>ten", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "<PERSON><PERSON><PERSON><PERSON> {amount} zum Senden", "EditFeeModal.Header.Time.unknown": "Zeit unbekannt", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Max. {fee}", "EditFeeModal.Header.fee.unknown": "Gebühr unbekannt", "EditFeeModal.Header.subsequent_failed": "Schätzungen könnten veraltet sein, letzte Aktualisierung fehlgeschlagen", "EditFeeModal.Layout.Header.ariaLabel": "Aktuelle Gebühr", "EditFeeModal.MaxFee.subtitle": "Die Max. Gebühr ist der Höchstbetrag, den du für eine Transaktion zahlst, aber normalerweise zahlst du die prognostizierte Gebühr. Dieser zusätzliche Puffer stellt sicher, dass deine Transaktion durchgeführt wird, auch wenn das Netzwerk langsamer oder teurer wird.", "EditFeeModal.MaxFee.title": "Maximale Netzwerkgebühr", "EditFeeModal.SelectPreset.Time.unknown": "Zeit unbekannt", "EditFeeModal.SelectPreset.ariaLabel": "Gebührenvoreinstellung wählen", "EditFeeModal.SelectPreset.fast": "<PERSON><PERSON><PERSON>", "EditFeeModal.SelectPreset.normal": "Normal", "EditFeeModal.SelectPreset.slow": "Langsam", "EditFeeModal.ariaLabel": "Netzwerkgebühr bearbeiten", "FailedSimulation.Confirmation.Item.subtitle": "Es ist ein interner Fehler aufgetreten", "FailedSimulation.Confirmation.Item.title": "Transaktion konnte nicht simuliert werden", "FailedSimulation.Confirmation.subtitle": "Möchtest du wirklich fortfahren?", "FailedSimulation.Confirmation.title": "<PERSON> blind", "FailedSimulation.Title": "Simu<PERSON><PERSON><PERSON><PERSON>", "FailedSimulation.footer.subtitle": "Es ist ein interner Fehler aufgetreten", "FailedSimulation.footer.title": "Transaktion konnte nicht simuliert werden", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Ben<PERSON><PERSON><PERSON> {amount} zur Übermittlung der Transaktion", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Verarbeitung könnte lange dauern", "FeeForecastWidget.networkFee": "Netzwerkgebühr", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Wir konnten die Netzwerkgebühr nicht berechnen", "FeeForecastWidget.subsequentFailed.message": "Schätzungen könnten veraltet sein, letzte Aktualisierung fehlgeschlagen", "FeeForecastWidget.unknownDuration": "Unbekannt", "FeeForecastWidget.unknownFee": "Unbekannt", "GasCurrencySelector.balance": "Saldo: {balance}", "GasCurrencySelector.networkFee": "Netzwerkgebühr", "GasCurrencySelector.payNetworkFeesUsing": "Netzwerkgebühren zahlen mit", "GasCurrencySelector.removeDefaultGasToken.description": "Gebühren vom größten Saldo zahlen", "GasCurrencySelector.removeDefaultGasToken.title": "Automatische Gebührenzahlung", "GasCurrencySelector.save": "Speichern", "GoogleDriveBackup.BeforeYouBegin.first_point": "Wenn ich mein Zeal-Passwort vergesse, verliere ich mein Guthaben für immer.", "GoogleDriveBackup.BeforeYouBegin.second_point": "Wenn ich den Zugriff auf mein Google Drive verliere oder meine Wiederherstellungsdatei ändere, verliere ich mein Guthaben für immer.", "GoogleDriveBackup.BeforeYouBegin.subtitle": "Bitte lies und akzeptiere den folgenden Punkt zur privaten Wallet:", "GoogleDriveBackup.BeforeYouBegin.third_point": "Zeal kann mir nicht helfen, mein <PERSON>-Passwort oder meinen Zugriff auf Google Drive wiederherzustellen.", "GoogleDriveBackup.BeforeYouBegin.title": "<PERSON><PERSON>", "GoogleDriveBackup.loader.subtitle": "Bitte genehmige die Anfrage in Google Drive, um deine Wiederherstellungsdatei hochzuladen.", "GoogleDriveBackup.loader.title": "Warte auf Genehmigung ...", "GoogleDriveBackup.success": "Backup erfolgreich 🎉", "MonitorOffRamp.overServiceTime": "Die meisten Überweisungen sind innerhalb von {estimated_time} abgeschlossen, können aber manchmal aufgrund zusätzlicher Prüfungen länger dauern. Das ist normal und dein Geld ist während dieser Prüfungen sicher.{br}{br}Wenn die Transaktion nicht innerhalb von {support_soft_deadline} abgeschlossen ist, {contact_support}", "MonitorOnRamp.contactSupport": "Support kontaktieren", "MonitorOnRamp.from": "<PERSON>", "MonitorOnRamp.fundsReceived": "Zahlung eingegangen", "MonitorOnRamp.overServiceTime": "Die meisten Überweisungen werden innerhalb von {estimated_time} abgeschlossen, können aber aufgrund zusätzlicher Prüfungen manchmal länger dauern. Das ist normal und dein Geld ist während dieser Prüfungen sicher.{br}{br}Wenn die Transaktion nicht innerhalb von {support_soft_deadline} abgeschlossen wird, bitte {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Wird an deine Wallet gesendet", "MonitorOnRamp.to": "An", "MonitorOnRamp.waitingForTransfer": "Warte auf deine Überweisung", "NftCollectionCheck.failed.subtitle": "Kollektion ist nicht verifiziert auf <source></source>", "NftCollectionCheck.failed.title": "Kollektion ist nicht verifiziert", "NftCollectionCheck.passed.subtitle": "Kollektion ist verifiziert auf <source></source>", "NftCollectionCheck.passed.title": "Kollektion ist verifiziert", "NftCollectionInfo.entireCollection": "<PERSON><PERSON><PERSON>", "NoSigningKeyStore.createAccount": "<PERSON><PERSON> er<PERSON>", "NonceRangeError.biggerThanCurrent.message": "Transaktion wird blockiert", "NonceRangeError.lessThanCurrent.message": "Transaktion wird fehlschlagen", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "Die Nonce ist höher als die aktuelle Nonce. Verringere die Nonce, damit die Transaktion nicht blockiert wird.", "NonceRangeErrorPopup.biggerThanCurrent.title": "Transaktion wird blockiert", "P2pReceiverTypeCheck.failed.subtitle": "Sendest du an die richtige Adresse?", "P2pReceiverTypeCheck.failed.title": "Empfänger ist Smart Contract, kein Wallet", "P2pReceiverTypeCheck.passed.subtitle": "Normalerweise sendest du Assets an andere Wallets", "P2pReceiverTypeCheck.passed.title": "Empfänger ist ein Wallet", "PasswordCheck.title": "Passwort eingeben", "PasswordChecker.subtitle": "Bitte gib dein Passwort ein, um zu bestätigen, dass du es bist", "PermitExpirationCheck.failed.subtitle": "Halte die Dauer kurz und nur so lang wie nötig", "PermitExpirationCheck.failed.title": "<PERSON>", "PermitExpirationCheck.passed.subtitle": "Wie lange eine App deine Token verwenden kann", "PermitExpirationCheck.passed.title": "Laufzeit nicht zu lang", "PrivateKeyValidationError.moreThanMaximumWords": "<PERSON><PERSON> {count} <PERSON><PERSON><PERSON>", "PrivateKeyValidationError.notValidPrivateKey": "Dies ist kein gültiger Privatschlüssel", "PrivateKeyValidationError.secretPhraseIsInvalid": "Die geheime Phrase ist ungültig", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Wort #{index} ist falsch geschrieben oder ungültig", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} Wort} other{{count} Wörter}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Geheime Wiederherstellungsphrasen und Privatschlüssel sind verschlüsselt und verlassen dieses Gerät niemals", "SecretPhraseReveal.header": "Geheime Phrase aufschreiben", "SecretPhraseReveal.hint": "<PERSON><PERSON> deine Phrase mit niemandem. Bewahre sie sicher und offline auf.", "SecretPhraseReveal.skip.subtitle": "Du kannst das später tun. Aber wenn du dieses Gerät verlierst, bevor du deine Phrase aufgeschrieben hast, verlierst du dein gesamtes Guthaben in dieser Wallet.", "SecretPhraseReveal.skip.takeTheRisk": "Riskieren", "SecretPhraseReveal.skip.title": "Aufschreiben überspringen?", "SecretPhraseReveal.skip.writeDown": "Aufschreiben", "SecretPhraseReveal.skipForNow": "Überspringen", "SecretPhraseReveal.subheader": "Bitte schreib sie auf und bewahre sie sicher offline auf. Wir werden dich dann bitten, sie zu bestätigen.", "SecretPhraseReveal.verify": "Bestätigen", "SelectCurrency.tokens": "Token", "SelectCurrency.tokens.emptyState": "<PERSON><PERSON> gefunden", "SelectRoute.slippage": "Slippage {slippage}", "SelectRoutes.emptyState": "Wir haben keine Routen für diesen Swap gefunden", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Wallet verbinden", "SendERC20.labelAddress.inputPlaceholder": "Wallet-Bezeichnung", "SendERC20.labelAddress.subtitle": "<PERSON><PERSON> dieser Wall<PERSON> eine Bezeichnung, damit du sie später wiederfindest.", "SendERC20.labelAddress.title": "<PERSON><PERSON> be<PERSON>", "SendERC20.send_to": "Senden an", "SendERC20.tokens": "Token", "SendOrReceive.bankTransfer.primaryText": "Banküberweisung", "SendOrReceive.bankTransfer.shortText": "Kostenloses, sofortiges On- und Off-Ramping", "SendOrReceive.bridge.primaryText": "Bridge", "SendOrReceive.bridge.shortText": "Überweise Tokens zwischen Netzwerken", "SendOrReceive.receive.primaryText": "Empfangen", "SendOrReceive.receive.shortText": "Empfange Tokens oder Sammlerstücke", "SendOrReceive.send.primaryText": "Senden", "SendOrReceive.send.shortText": "Sende Tokens an eine beliebige Adresse", "SendOrReceive.swap.primaryText": "Tauschen", "SendOrReceive.swap.shortText": "Tausche zwischen Tokens", "SendSafeTransaction.Confirm.loading": "Sicherheitsprüfungen laufen…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Wiederherstellungsdatei mit Passwort verschlüsseln", "SetupRecoveryKit.google.subtitle": "Synchronisiert {date}", "SetupRecoveryKit.google.title": "Google Drive-Backup", "SetupRecoveryKit.subtitle": "Du benötigst mindestens eine Methode zur Wiederherstellung deines Kontos, falls du Zeal deinstallierst oder das Gerät wechselst.", "SetupRecoveryKit.title": "Wiederherstellungs-<PERSON>", "SetupRecoveryKit.writeDown.subtitle": "Geheime Phrase aufschreiben", "SetupRecoveryKit.writeDown.title": "<PERSON><PERSON>", "Sign.CheckSafeDeployment.activate": "Aktivieren", "Sign.CheckSafeDeployment.subtitle": "Bevor du dich bei einer App anmelden oder eine Off-Chain-Nachricht signieren kannst, musst du dein Gerät in diesem Netzwerk aktivieren. Dies ges<PERSON>, nachdem du ein Smart Wallet installiert oder wiederhergestellt hast.", "Sign.CheckSafeDeployment.title": "Gerät in diesem Netzwerk aktivieren", "Sign.Simuation.Skeleton.title": "Sicherheitsprüfungen laufen…", "SignMessageSafetyCheckResult.passed": "Sicherheitsprüfungen bestanden", "SignMessageSafetyChecksPopup.title.permits": "Sicherheitsprüfungen für Permit", "SimulationFailedConfirmation.subtitle": "Wir haben diese Transaktion simuliert und ein Problem gefunden, das zum Fehlschlagen führen würde. Du kannst die Transaktion absenden, aber sie wird wahrscheinlich fehlschlagen und du könntest deine Netzwerkgebühr verlieren.", "SimulationFailedConfirmation.title": "Transaktion schlägt wahrsche<PERSON>lich fehl", "SimulationNotSupported.Title": "Simulation nicht{br}unterstützt auf{br}{network}", "SimulationNotSupported.footer.subtitle": "Du kannst diese Transaktion trotzdem senden", "SimulationNotSupported.footer.title": "Simulation nicht unterstützt", "SlippagePopup.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SlippagePopup.presetsHeader": "Swap-Slippage", "SlippagePopup.title": "Slippage-Einstellungen", "SmartContractBlacklistCheck.failed.subtitle": "Schadensmeldungen von <source></source>", "SmartContractBlacklistCheck.failed.title": "Smart Contract ist auf der Blacklist", "SmartContractBlacklistCheck.passed.subtitle": "<PERSON><PERSON> von <source></source>", "SmartContractBlacklistCheck.passed.title": "Smart Contract ist nicht auf der Blacklist", "SuspiciousCharactersCheck.Failed.subtitle": "Dies ist eine gängige Phishing-Taktik", "SuspiciousCharactersCheck.Failed.title": "Wir prüfen auf gängige Phishing-Muster", "SuspiciousCharactersCheck.Passed.subtitle": "Wir prüfen auf Phishing-Versuche", "SuspiciousCharactersCheck.Passed.title": "Adresse hat keine ungewöhnlichen Zeichen", "SuspiciousCharactersCheck.failed.statusButton.label": "Adresse enthält ungewöhnliche Zeichen ", "TokenVerificationCheck.failed.subtitle": "Token ist nicht gelistet auf <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} ist nicht von CoinGecko verifiziert", "TokenVerificationCheck.passed.subtitle": "Token ist gelistet auf <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} ist von CoinGecko verifiziert", "TopupDapp.MonitorTransaction.success.splash": "Abgeschlossen", "TransactionSafetyCheckResult.passed": "Sicherheitsprüfungen bestanden", "TransactionSimulationCheck.failed.subtitle": "<PERSON><PERSON>: {errorMessage}", "TransactionSimulationCheck.failed.title": "Transaktion wird wahrscheinlich fehlschlagen", "TransactionSimulationCheck.passed.subtitle": "Simulation durchgeführt mit <source></source>", "TransactionSimulationCheck.passed.title": "Transaktionsvorschau war erfolgreich", "TrezorError.trezor_action_cancelled.action": "Schließen", "TrezorError.trezor_action_cancelled.subtitle": "Du hast die Transaktion auf deinem Hardware-Wallet abgelehnt.", "TrezorError.trezor_device_used_elsewhere.action": "<PERSON><PERSON><PERSON> synchroni<PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "<PERSON>hl<PERSON>ße alle anderen offenen Sitzungen und versuche erneut, deinen Trezor zu synchronisieren.", "TrezorError.trezor_method_cancelled.action": "<PERSON><PERSON><PERSON> synchroni<PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "<PERSON><PERSON><PERSON><PERSON> Trezor, Wallets nach Zeal zu exportieren.", "TrezorError.trezor_permissions_not_granted.action": "<PERSON><PERSON><PERSON> synchroni<PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "<PERSON>te erteile Zeal die Berechtigung, alle Wallets anzuzeigen.", "TrezorError.trezor_pin_cancelled.action": "<PERSON><PERSON><PERSON> synchroni<PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "Sitzung auf dem Gerät abgebrochen", "TrezorError.trezor_popup_closed.action": "<PERSON><PERSON><PERSON> synchroni<PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "Der Trezor-<PERSON><PERSON> wurde unerwartet geschlossen", "TrxLikelyToFail.lessThanEstimatedGas.message": "Transaktion wird fehlschlagen", "TrxLikelyToFail.lessThanMinimumGas.message": "Transaktion wird fehlschlagen", "TrxLikelyToFail.lessThanSuggestedGas.message": "Wird wahr<PERSON>lich fehlschlagen", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "Das Gas-Limit der Transaktion ist zu niedrig. Erhöhe das Gas-Limit auf das vorgeschlagene Limit, um ein Fehlschlagen zu verhindern.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "Transaktion schlägt wahrsche<PERSON>lich fehl", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Das Gas-Limit ist niedriger als das geschätzte Gas. Erhöhe das Gas-Limit auf das vorgeschlagene Limit.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "Transaktion wird fehlschlagen", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "Die max. Basisgebühr ist niedriger als die aktuelle Basisgebühr. Erhöhe die max. Basisgebühr, um ein Blockieren der Transaktion zu verhindern.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "Transaktion wird blockiert", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "Die max. Gebühr der Transaktion ist zu niedrig. Erhöhe die max. Gebühr, um ein Blockieren der Transaktion zu verhindern.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "Transaktion wird blockiert", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Die Prioritätsgebühr ist niedriger als empfohlen. Erhöhe die Prioritätsgebühr, um die Transaktion zu beschleunigen.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "Transaktion könnte lange dauern", "UnsupportedMobileNetworkLayout.gotIt": "Verstanden!", "UnsupportedMobileNetworkLayout.subtitle": "Du kannst auf dem Netzwerk mit der ID {networkHexId} noch keine Transaktionen durchführen oder Nachrichten mit der mobilen Version von Zeal signieren{br}{br}Wechsle zur Browser-Erweiterung, um Transaktionen in diesem Netzwerk durchzuführen, während wir mit Hochdruck an der Unterstützung für dieses Netzwerk arbeiten 🚀", "UnsupportedMobileNetworkLayout.title": "Netzwerk wird von der mobilen Version von Z<PERSON> nicht unterstützt", "UnsupportedSafeNetworkLayout.subtitle": "Du kannst keine Transaktionen durchführen oder Nachrichten auf {network} mit einem Zeal Smart Wallet signieren.{br}{br}<PERSON><PERSON><PERSON> zu einem unterstützten Netzwerk oder verwende ein Legacy Wallet.", "UnsupportedSafeNetworkLayoutk.title": "Netzwerk wird für Smart Wallet nicht unterstützt", "UserConfirmationPopup.goBack": "Abbrechen", "UserConfirmationPopup.submit": "Ignorieren", "ViewPrivateKey.header": "Private<PERSON><PERSON><PERSON>", "ViewPrivateKey.hint": "<PERSON><PERSON> deinen privaten Schlüssel mit niemandem. Bewahre ihn sicher und offline auf.", "ViewPrivateKey.subheader.mobile": "<PERSON><PERSON><PERSON>, um deinen privaten Schlüssel anzuzeigen", "ViewPrivateKey.subheader.web": "Zum Anzeigen deines privaten Schlüssels darüberfahren", "ViewPrivateKey.unblur.mobile": "Zum Anzeigen tippen", "ViewPrivateKey.unblur.web": "Zum Anzeigen darüberfahren", "ViewSecretPhrase.PasswordChecker.subtitle": "Gib dein Passwort ein, um die Wiederherstellungsdatei zu verschlüsseln. Du musst es dir für die Zukunft merken.", "ViewSecretPhrase.done": "<PERSON><PERSON><PERSON>", "ViewSecretPhrase.header": "Geheime Phrase", "ViewSecretPhrase.hint": "<PERSON><PERSON> deine Phrase mit niemandem. Bewahre sie sicher und offline auf.", "ViewSecretPhrase.subheader.mobile": "<PERSON><PERSON><PERSON>, um deine geheime Phrase anzuzeigen", "ViewSecretPhrase.subheader.web": "Zum Anzeigen deiner geheimen Phrase darüberfahren", "ViewSecretPhrase.unblur.mobile": "Zum Anzeigen tippen", "ViewSecretPhrase.unblur.web": "Zum Anzeigen darüberfahren", "account-details.monerium": "Überweisungen erfolgen über Monerium, ein autorisiertes und reguliertes EMI. <link>Mehr erfahren</link>", "account-details.unblock": "Überweisungen werden über Unblock abgewickelt, einem autorisierten und registrierten Anbieter von Tausch- und Verwahrdiensten. <link><PERSON><PERSON> erfahren</link>", "account-selector.empty-state": "Keine Wallets gefunden", "account-top-up.select-currency.title": "Token", "account.accounts_not_found": "Keine Wallets gefunden", "account.accounts_not_found_search_valid_address": "Wallet ist nicht in deiner Liste", "account.add.create_new_secret_phrase": "Geheime Wiederherstellungsphrase erstellen", "account.add.create_new_secret_phrase.subtext": "Eine neue 12-<PERSON><PERSON>-Wiederherstellungsphrase", "account.add.fromRecoveryKit.fileNotFound": "Wir konnten deine Datei nicht finden", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "<PERSON><PERSON><PERSON> versuchen", "account.add.fromRecoveryKit.fileNotFound.explanation": "<PERSON><PERSON>ü<PERSON>, ob du mit dem richtigen Konto angemeldet bist, das einen Zeal-Backup-Ordner hat", "account.add.fromRecoveryKit.fileNotValid": "Wiederherstellungsdatei ist ungültig", "account.add.fromRecoveryKit.fileNotValid.explanation": "Wir haben deine Datei geprüft. Entweder ist es der falsche Typ oder sie wurde geändert", "account.add.import_secret_phrase": "Geheime Wiederherstellungsphrase importieren", "account.add.import_secret_phrase.subtext": "<PERSON><PERSON><PERSON><PERSON> mit Zeal, Metamask oder anderen", "account.add.select_type.add_hardware_wallet": "Hardware-Wallet", "account.add.select_type.existing_smart_wallet": "Bestehendes Smart Wallet", "account.add.select_type.private_key": "Privatschlüssel", "account.add.select_type.seed_phrase": "Wiederherstellungsphrase", "account.add.select_type.title": "Wallet importieren", "account.add.select_type.zeal_recovery_file": "Zeal-Wiederherstellungsdatei", "account.add.success.title": "Neue Wallet erstellt 🎉", "account.addLabel.header": "<PERSON><PERSON>", "account.addLabel.labelError.labelAlreadyExist": "Name bereits vergeben. Versuch einen anderen.", "account.addLabel.labelError.maxStringLengthExceeded": "Maximale Zeichenzahl erreicht", "account.add_active_wallet.primary_text": "<PERSON><PERSON> hinzufügen", "account.add_active_wallet.short_text": "<PERSON><PERSON> er<PERSON>, verbinden oder importieren", "account.add_from_ledger.success": "<PERSON><PERSON> zu Zeal hinzugefügt", "account.add_tracked_wallet.primary_text": "Wallet mit Lesezugriff hinzufügen", "account.add_tracked_wallet.short_text": "Portfolio und Aktivitäten einsehen", "account.button.unlabelled-wallet": "Unbenannte Wallet", "account.create_wallet": "<PERSON><PERSON> erstellen", "account.label.edit.title": "Wallet-<PERSON><PERSON> bearbeiten", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON><PERSON> {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Wiederherstellungsdatei ist ungültig", "account.recoveryKit.selectBackupFile.subtitle": "Wähle die Wiederherstellungsdatei aus, die du wiederherstellen möchtest", "account.recoveryKit.selectBackupFile.title": "Wiederherstellungsdatei", "account.recoveryKit.success.recoveryFileFound": "Wiederherstellungsdatei gefunden 🎉", "account.select_type_of_account.create_eoa.short": "Klassische Wallet für Experten", "account.select_type_of_account.create_eoa.title": "Wallet mit Wiederherstellungsphrase erstellen", "account.select_type_of_account.create_safe_wallet.title": "Smart Wallet erstellen", "account.select_type_of_account.existing_smart_wallet": "Bestehende Smart Wallet", "account.select_type_of_account.hardware_wallet": "Hardware Wallet", "account.select_type_of_account.header": "<PERSON><PERSON> hinzufügen", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Privatschlüssel / Seed-Phrase", "account.select_type_of_account.read_only_wallet": "Read-only-<PERSON><PERSON>", "account.select_type_of_account.read_only_wallet.short": "<PERSON><PERSON>", "account.topup.title": "<PERSON><PERSON><PERSON><PERSON> zu <PERSON> hinzufügen", "account.view.error.refreshAssets": "Aktualisieren", "account.widget.refresh": "Aktualisieren", "account.widget.settings": "Einstellungen", "accounts.view.copied-text": "Ko<PERSON>rt {formattedAddress}", "accounts.view.copiedAddress": "Ko<PERSON>rt {formattedAddress}", "action.accept": "Akzeptieren", "action.accpet": "Akzeptieren", "action.allow": "<PERSON><PERSON><PERSON>", "action.back": "Zurück", "action.cancel": "Abbrechen", "action.card-activation.title": "Karte aktivieren", "action.claim": "An<PERSON>ern", "action.close": "Schließen", "action.complete-steps": "Abschließen", "action.confirm": "Bestätigen", "action.continue": "<PERSON><PERSON>", "action.copy-address-understand": "OK – <PERSON><PERSON><PERSON> k<PERSON>en", "action.deposit": "Ein<PERSON><PERSON><PERSON>", "action.done": "<PERSON><PERSON><PERSON>", "action.dontAllow": "<PERSON>cht zu<PERSON>en", "action.edit": "bearbeiten", "action.email-required": "E-Mail eingeben", "action.enterPhoneNumber": "Telefonnummer eingeben", "action.expand": "<PERSON><PERSON><PERSON><PERSON>", "action.fix": "Korrigieren", "action.getStarted": "Loslegen", "action.got_it": "Verstanden", "action.hide": "Ausblenden", "action.import": "Importieren", "action.import-keys": "Schlüssel importieren", "action.importKeys": "Schlüssel importieren", "action.minimize": "Minimieren", "action.next": "<PERSON><PERSON>", "action.ok": "OK", "action.reduceAmount": "Auf Maximum reduzieren", "action.refreshWebsite": "Website aktualisieren", "action.remove": "Entfernen", "action.remove-account": "<PERSON><PERSON> entfernen", "action.requestCode": "Code anfordern", "action.resend_code": "Code erneut senden", "action.resend_code_with_time": "Code erneut senden {time}", "action.retry": "<PERSON><PERSON><PERSON> versuchen", "action.reveal": "Anzeigen", "action.save": "Speichern", "action.save_changes": "<PERSON><PERSON>", "action.search": "<PERSON><PERSON>", "action.seeAll": "<PERSON>e ansehen", "action.select": "Auswählen", "action.send": "Senden", "action.skip": "Überspringen", "action.submit": "Senden", "action.understood": "<PERSON>ch verstehe", "action.update": "Updaten", "action.update-gnosis-pay-owner.complete": "Abschließen", "action.zeroAmount": "<PERSON><PERSON> e<PERSON>ben", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Sammlerstücke", "action_bar_title.tokens": "Tokens", "action_bar_title.transaction_request": "Transaktionsanfrage", "activate-monerium.loading": "<PERSON><PERSON> per<PERSON>önliches Konto wird eingerichtet", "activate-monerium.success.title": "Monerium aktiviert", "activate-physical-card-widget.subtitle": "Lieferung kann bis zu 3 <PERSON>ochen dauern", "activate-physical-card-widget.title": "Physische Karte aktivieren", "activate-smart-wallet.title": "Wallet aktivieren", "active_and_tracked_wallets.title": "Zeal übernimmt alle deine Gebühren auf {network}, sodass du kostenlos Transaktionen durchführen kannst!", "activity.approval-amount.revoked": "Widerrufen", "activity.approval-amount.unlimited": "Unbegrenzt", "activity.approval.approved_for": "<PERSON><PERSON><PERSON><PERSON>", "activity.approval.approved_for_with_target": "Gene<PERSON><PERSON>t {approvedTo}", "activity.approval.revoked_for": "Widerrufen für", "activity.bank.serviceProvider": "Dienstanbieter", "activity.bridge.serviceProvider": "Dienstanbieter", "activity.cashback.period": "Cashback-<PERSON><PERSON><PERSON><PERSON>", "activity.filter.card": "<PERSON><PERSON>", "activity.rate": "<PERSON><PERSON>", "activity.receive.receivedFrom": "<PERSON><PERSON><PERSON><PERSON> von", "activity.send.sendTo": "Gesendet an", "activity.smartContract.unknown": "Unbekannter Vertrag", "activity.smartContract.usingContract": "Genutzter Vertrag", "activity.subtitle.pending_timer": "{timerString} Ausstehend", "activity.title.arbitrary_smart_contract_interaction": "{function} auf {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Unbekannte Vertragsinteraktion", "activity.title.bridge.from": "<PERSON> von {token}", "activity.title.bridge.to": "Bridge zu {token}", "activity.title.buy": "<PERSON><PERSON> {asset}", "activity.title.card_owners_updated": "<PERSON><PERSON><PERSON><PERSON><PERSON> aktual<PERSON>", "activity.title.card_spend_limit_updated": "Ausgabenlimit für Karte festgelegt", "activity.title.cashback_deposit": "Einzahlung auf Cashback", "activity.title.cashback_reward": "Cashback-<PERSON><PERSON><PERSON><PERSON>", "activity.title.cashback_withdraw": "Auszahl<PERSON> von <PERSON>", "activity.title.claimed_reward": "<PERSON><PERSON><PERSON><PERSON>", "activity.title.deployed_smart_wallet_gnosis": "<PERSON><PERSON> erste<PERSON>t", "activity.title.deposit_from_bank": "Einzahlung von Bank", "activity.title.deposit_into_card": "Einzahlung auf Karte", "activity.title.deposit_into_earn": "Einzahlung in {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} auf {smartContract}", "activity.title.from": "<PERSON> {sender}", "activity.title.pendidng_areward_claim": "<PERSON><PERSON>nung wird beansp<PERSON>cht", "activity.title.pendidng_breward_claim": "<PERSON><PERSON>nung wird beansp<PERSON>cht", "activity.title.recharge_disabledh": "Kartenaufladung deaktiviert", "activity.title.recharge_set": "Aufladeziel festgelegt", "activity.title.recovered_smart_wallet_gnosis": "Installation auf neuem Gerät", "activity.title.send_pending": "An {receiver}", "activity.title.send_to_bank": "An Bank", "activity.title.swap": "<PERSON><PERSON> {token}", "activity.title.to": "An {receiver}", "activity.title.withdraw_from_card": "Auszahlung von Ka<PERSON>", "activity.title.withdraw_from_earn": "Auszahlung von {earn}", "activity.transaction.networkFees": "Netzwerkgebühren", "activity.transaction.state": "Abgeschlossene Transaktion", "activity.transaction.state.completed": "Abgeschlossene Transaktion", "activity.transaction.state.failed": "Fehlgeschlagene Transaktion", "add-account.section.import.header": "Importieren", "add-another-card-owner": "<PERSON><PERSON><PERSON>er hinzufü<PERSON>", "add-another-card-owner.Recommended.footnote": "Zeal Wallet als Inhaber hinzufügen", "add-another-card-owner.Recommended.primaryText": "Zeal zu Gnosis Pay hinzufügen", "add-another-card-owner.recommended": "<PERSON><PERSON><PERSON><PERSON>", "add-owner.confirmation.subtitle": "Aus Sicherheitsgründen dauert die Verarbeitung von Einstellungsänderungen 3 Minuten. Währenddessen ist deine Karte vorübergehend gesperrt und Zahlungen sind nicht möglich.", "add-owner.confirmation.title": "<PERSON><PERSON> wird für 3 Min. ges<PERSON>, während die Einstellungen aktualisiert werden", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Wallet wird bereits verwendet", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "Andere Wallet wählen", "add.account.backup.decrypt.success": "Wallet wiederhergestellt", "add.account.backup.password.passwordIncorrectMessage": "Passwort ist falsch", "add.account.backup.password.subtitle": "Bitte gib das Passwort ein, das du zur Verschlüsselung deiner Wiederherstellungsdatei verwendet hast", "add.account.backup.password.title": "Passwort eingeben", "add.account.google.login.subtitle": "Bitte genehmige die Anfrage in Google Drive, um deine Wiederherstellungsdatei zu synchronisieren", "add.account.google.login.title": "Warte auf Genehmigung...", "add.readonly.already_added": "Wallet bereits hinzugefügt", "add.readonly.continue": "<PERSON><PERSON>", "add.readonly.empty": "Ad<PERSON>e oder ENS e<PERSON>ben", "addBankRecipient.title": "Bankempfänger hinzufügen", "add_funds.deposit_from_bank_account": "Vom Bankkonto einzahlen", "add_funds.from_another_wallet": "<PERSON> einer anderen Wallet", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Mit Top-up-dApp verbinden", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Verbinde eine beliebige Wallet mit der Zeal Top-up-dApp und sende schnell Geld an deine Wallet", "add_funds.from_crypto_wallet.header": "<PERSON> einer anderen Wallet", "add_funds.from_crypto_wallet.header.show_wallet_address": "<PERSON><PERSON>-<PERSON><PERSON><PERSON> anzeigen", "add_funds.from_exchange.header": "<PERSON> einer Börse senden", "add_funds.from_exchange.header.copy_wallet_address": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance etc.", "add_funds.from_exchange.header.open_exchange": "Börsen-App oder -Website öffnen", "add_funds.from_exchange.header.selected_token": "Sende {token} an Zeal", "add_funds.from_exchange.header.selected_token.subtitle": "Auf {network}", "add_funds.from_exchange.header.send_selected_token": "Unterstützten Token senden", "add_funds.from_exchange.header.send_selected_token.subtitle": "Unterstützten Token & Netzwerk auswählen", "add_funds.import_wallet": "Bestehende Krypto-Wallet importieren", "add_funds.title": "<PERSON><PERSON> aufladen", "add_funds.transfer_from_exchange": "<PERSON>ber<PERSON><PERSON><PERSON> von <PERSON>", "address.add.header": "Sieh deine Wallet in Zeal{br}im schreibgeschützten Modus", "address.add.subheader": "Gib deine Adresse oder ENS ein, um deine Vermögenswerte auf allen EVM-Netzwerken an einem Ort zu sehen. Erstelle oder importiere später weitere Wallets.", "address_book.change_account.bank_transfers.header": "Bankempfänger", "address_book.change_account.bank_transfers.primary": "Bankempfänger", "address_book.change_account.cta": "Wallet verfolgen", "address_book.change_account.search_placeholder": "<PERSON><PERSON><PERSON> hinzufügen oder suchen", "address_book.change_account.tracked_header": "Wallets mit Lesezugriff", "address_book.change_account.wallets_header": "Aktive Wallets", "app-association-check-failed.modal.cta": "<PERSON><PERSON><PERSON> versuchen", "app-association-check-failed.modal.subtitle": "Bitte versuche es erneut. Verbindungsprobleme verursachen Verzögerungen beim Abrufen deiner Passkeys. Wenn das Problem weiterhin besteht, starte Zeal neu und versuche es noch einmal.", "app-association-check-failed.modal.subtitle.creation": "Bitte versuche es erneut. Verbindungsprobleme verursachen Verzögerungen bei der Erstellung des Passkeys. Wenn das Problem weiterhin besteht, starte Zeal neu und versuche es noch einmal.", "app-association-check-failed.modal.title.creation": "<PERSON><PERSON> Gerät konnte keinen Passkey erstellen", "app-association-check-failed.modal.title.signing": "De<PERSON> Gerät konnte die Passkeys nicht laden", "app.app_protocol_group.borrowed_tokens": "<PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.claimable_amount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.health_rate": "Health Rate", "app.app_protocol_group.lending": "Lending", "app.app_protocol_group.locked_tokens": "Gesperrte Token", "app.app_protocol_group.nfts": "Sammlerstücke", "app.app_protocol_group.reward_tokens": "Belohnungs-Token", "app.app_protocol_group.supplied_tokens": "Bereitgestellte Token", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Vesting-Token", "app.appsGroupHeader.discoverMore": "<PERSON><PERSON> en<PERSON>cken", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Suchen oder URL eingeben", "app.error-banner.cory": "Fehlerdaten kopieren", "app.error-banner.retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.list_item.rewards": "Belohnungen {value}", "app.position_details.health_rate.description": "Der Health-Faktor wird berechnet, indem der Betrag deines Kredits durch den Wert deiner Sicherheiten geteilt wird.", "app.position_details.health_rate.title": "Was ist der Health-Faktor?", "approval.edit-limit.label": "Ausgabenlimit bearbeiten", "approval.permit_info": "Permit-Informationen", "approval.spend-limit.edit-modal.cancel": "Abbrechen", "approval.spend-limit.edit-modal.limit-label": "Ausgabenlimit", "approval.spend-limit.edit-modal.max-limit-error": "Warnung, hohes Limit", "approval.spend-limit.edit-modal.revert": "Änderungen verwerfen", "approval.spend-limit.edit-modal.set-to-unlimited": "<PERSON><PERSON> unbegrenzt setzen", "approval.spend-limit.edit-modal.submit": "Änderungen speichern", "approval.spend-limit.edit-modal.title": "Berechtigungen bearbeiten", "approval.spend_limit_info": "Was ist ein Ausgabenlimit?", "approval.what_are_approvals": "Was sind Genehmigungen?", "apps_list.page.emptyState": "<PERSON>ine aktiven Apps", "backpace.removeLastDigit": "<PERSON><PERSON><PERSON> Ziffer entfernen", "backup-banner.backup_now": "Jetzt sichern", "backup-banner.risk_losing_funds": "<PERSON>zt sichern oder den Verlust von Guthaben riskieren", "backup-banner.title": "<PERSON>et nicht gesichert", "backupRecoverySmartWallet.noExportPrivateKeys": "Automatische Sicherung: <PERSON><PERSON> wird als Passkey gespeichert – keine Seed-Phrase oder Privatschlüssel erforderlich.", "backupRecoverySmartWallet.safeContracts": "Multi-Key-Sicherheit: Zeal-Wallets laufen auf Safe-Contracts, sodass mehrere Geräte eine Transaktion genehmigen können. Kein Single Point of Failure.", "backupRecoverySmartWallet.security": "Mehrere Geräte: Du kannst deine Wallet mit dem Passkey auf mehreren Geräten verwenden. Jedes Gerät erhält seinen eigenen Privatschlüssel.", "backupRecoverySmartWallet.showLocalPrivateKey": "Expertenmodus: Du kannst den Privatschlüssel dieses Geräts exportieren, ihn in einer anderen Wallet verwenden und dich auf <SafeGlobal>https://safe.global</SafeGlobal> verbinden. <Key>Privatschlüssel anzeigen</Key>", "backupRecoverySmartWallet.storingKeys": "Cloud-synchronisiert: Der Passkey wird sicher in iCloud, dem Google Passwortmanager oder deinem Passwortmanager gespeichert.", "backupRecoverySmartWallet.title": "Smart Wallet Sicherung & Wiederherstellung", "balance-change.card.titile": "<PERSON><PERSON>", "balanceChange.pending": "<PERSON><PERSON><PERSON><PERSON>", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Dienstanbieter auswählen", "bank-transfer.change-deposit-receiver.subtitle": "Diese Wallet empfängt alle Bankeinzahlungen", "bank-transfer.change-deposit-receiver.title": "Empfangende Wallet festlegen", "bank-transfer.change-owner.subtitle": "Diese Wallet dient zur Anmeldung und Wiederherstellung deines Überweisungskontos.", "bank-transfer.change-owner.title": "<PERSON><PERSON>inhaber festlegen", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Alle Bankeinzahlungen an Zeal werden von dieser Wallet empfangen.", "bank-transfer.configrm-change-deposit-receiver.title": "Empfangende Wallet ändern", "bank-transfer.configrm-change-owner.subtitle": "Möchtest du den Kontoinhaber wirklich ändern? Diese Wallet dient zur Anmeldung und Wiederherstellung deines Überweisungskontos.", "bank-transfer.configrm-change-owner.title": "Ko<PERSON>inhaber ä<PERSON>n", "bank-transfer.deposit.widget.status.complete": "Abgeschlossen", "bank-transfer.deposit.widget.status.funds_received": "Zahlung eingegangen", "bank-transfer.deposit.widget.status.sending_to_wallet": "Wird an <PERSON>et gesendet", "bank-transfer.deposit.widget.status.transfer-on-hold": "Überweisung angehalten", "bank-transfer.deposit.widget.status.transfer-received": "Wird an <PERSON>et gesendet", "bank-transfer.deposit.widget.subtitle": "{from} zu {to}", "bank-transfer.deposit.widget.title": "Einzahlung", "bank-transfer.intro.bulletlist.point_1": "Einrichtung mit Unblock", "bank-transfer.intro.bulletlist.point_2": "Überweisungen zwischen EUR/GBP und mehr als 10 Token", "bank-transfer.intro.bulletlist.point_3": "0 % Gebühren bis 5.000 $ monatlich, danach 0,2 %", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Wird an Bank gesendet", "bank-transfer.withdrawal.widget.status.in-progress": "Überweisung wird ausgeführt", "bank-transfer.withdrawal.widget.status.on-hold": "Überweisung angehalten", "bank-transfer.withdrawal.widget.status.success": "Abgeschlossen", "bank-transfer.withdrawal.widget.subtitle": "{from} an {to}", "bank-transfer.withdrawal.widget.title": "Auszahlung", "bank-transfers.bank-account-actions.remove-this-account": "<PERSON><PERSON>tfer<PERSON>", "bank-transfers.bank-account-actions.switch-to-this-account": "<PERSON><PERSON> diesem <PERSON> wechseln", "bank-transfers.deposit.fees-for-less-than-5k": "Gebühren für 5.000 $ oder weniger", "bank-transfers.deposit.fees-for-more-than-5k": "Gebühren für mehr als 5.000 $", "bank-transfers.set-receiving-bank.title": "Empfängerbank festlegen", "bank-transfers.settings.account_owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfers.settings.receiver_of_bank_deposits": "<PERSON><PERSON><PERSON><PERSON> <PERSON> Bankeinzahlungen", "bank-transfers.settings.receiver_of_withdrawals": "<PERSON><PERSON><PERSON><PERSON> von Au<PERSON>", "bank-transfers.settings.registered_email": "Registrierte E-Mail", "bank-transfers.settings.title": "Einstellungen für Banküberweisungen", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} -Konto", "bank-transfers.setup.bank-account": "Bankkonto", "bankTransfer.withdraw.max_loading": "Max.: {amount}", "bank_details_do_not_match.got_it": "Verstanden", "bank_details_do_not_match.subtitle": "Bankleitzahl und Kontonummer stimmen nicht überein. Bitte überprüfe die Angaben und versuche es erneut.", "bank_details_do_not_match.title": "Bankverbindung stimmt nicht", "bank_tranfsers.select_country_of_residence.country_not_supported": "Leider werden Banküberweisungen in {country} noch nicht unterstützt", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Öffne deine Banking-App", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Sende {fiatCurrencyCode} an dein Konto", "bank_transfer.deposit.header": "{fullName}s persönliche&nbsp;Kontodetails", "bank_transfer.kyc_status_widget.subtitle": "Banküberweisungen", "bank_transfer.kyc_status_widget.title": "Identitätsprüfung", "bank_transfer.personal_details.date_of_birth": "Geburtsdatum", "bank_transfer.personal_details.date_of_birth.invalid_format": "Ungültiges Datum", "bank_transfer.personal_details.date_of_birth.too_young": "Du musst mindestens 18 Jahre alt sein", "bank_transfer.personal_details.first_name": "<PERSON><PERSON><PERSON>", "bank_transfer.personal_details.last_name": "Nachname", "bank_transfer.personal_details.title": "<PERSON><PERSON>", "bank_transfer.reference.label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Optional)", "bank_transfer.reference_message": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.residence_details.address": "<PERSON><PERSON>", "bank_transfer.residence_details.city": "Stadt", "bank_transfer.residence_details.country_of_residence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfer.residence_details.country_placeholder": "Land", "bank_transfer.residence_details.postcode": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.residence_details.street": "Straße", "bank_transfer.residence_details.your_residence": "<PERSON><PERSON>", "bank_transfers.choose-wallet.continue": "<PERSON><PERSON>", "bank_transfers.choose-wallet.test": "<PERSON><PERSON> hinzufügen", "bank_transfers.choose-wallet.warning.subtitle": "Du kannst nur eine Wallet gleichzeitig verknüpfen. Die verknüpfte Wallet kann nicht geändert werden.", "bank_transfers.choose-wallet.warning.title": "<PERSON><PERSON>hle deine Wallet mit Bedacht", "bank_transfers.choose_wallet.subtitle": "<PERSON><PERSON>hle ein Wallet für Banküberweisungen. ", "bank_transfers.choose_wallet.title": "<PERSON>et auswählen", "bank_transfers.continue": "<PERSON><PERSON>", "bank_transfers.currency_is_currently_not_supported": "<PERSON><PERSON>", "bank_transfers.deposit-header": "Einzahlung", "bank_transfers.deposit.account-name": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.account-number-copied": "<PERSON><PERSON><PERSON><PERSON> kop<PERSON>t", "bank_transfers.deposit.amount-input": "Einzuzahlender Betrag", "bank_transfers.deposit.amount-output": "Zielbetrag", "bank_transfers.deposit.amount-output.error": "<PERSON><PERSON>", "bank_transfers.deposit.buttet-point.receive-crypto": "Erhalte {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "<PERSON><PERSON>", "bank_transfers.deposit.currency-not-supported.subtitle": "Bankeinzahlungen in {code} wurden bis auf Weiteres deaktiviert.", "bank_transfers.deposit.currency-not-supported.title": "{code} Einzahlungen derzeit nicht unterstützt", "bank_transfers.deposit.default-token.balance": "Saldo {amount}", "bank_transfers.deposit.deposit-header": "Ein<PERSON><PERSON><PERSON>", "bank_transfers.deposit.enter_amount": "<PERSON><PERSON> e<PERSON>ben", "bank_transfers.deposit.iban-copied": "IBAN kopiert", "bank_transfers.deposit.increase-amount": "Mindestüberweisung ist {limit}", "bank_transfers.deposit.loading": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.max-limit-reached": "Betrag überschreitet maximales Überweisungslimit", "bank_transfers.deposit.modal.kyc.button-text": "<PERSON> geht's", "bank_transfers.deposit.modal.kyc.text": "Zur Überprüfung deiner Identität benötigen wir einige persönliche Daten und Dokumente. Das Einreichen dauert in der Regel nur wenige Minuten.", "bank_transfers.deposit.modal.kyc.title": "Verifiziere deine Identität, um Limits zu erhöhen", "bank_transfers.deposit.reduce_amount": "Betrag reduzieren", "bank_transfers.deposit.show-account.account-number": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Bankleitzahl (Sort Code)", "bank_transfers.deposit.sort-code-copied": "Bankleitzahl (Sort Code) kopiert", "bank_transfers.deposit.withdraw-header": "Au<PERSON>ahl<PERSON>", "bank_transfers.failed_to_load_fee": "Unbekannt", "bank_transfers.fees": "Gebühren", "bank_transfers.increase-amount": "Mindestüberweisung ist {limit}", "bank_transfers.insufficient-funds": "<PERSON><PERSON><PERSON><PERSON> reicht nicht aus", "bank_transfers.select_country_of_residence.title": "Wo wohnst du?", "bank_transfers.setup.cta": "Überweisungen einrichten", "bank_transfers.setup.enter-amount": "<PERSON><PERSON> e<PERSON>ben", "bank_transfers.source_of_funds.form.business_income": "Geschäftseinkommen", "bank_transfers.source_of_funds.form.other": "Sonstiges", "bank_transfers.source_of_funds.form.pension": "Rente", "bank_transfers.source_of_funds.form.salary": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.title": "Herkunft deiner Mittel", "bank_transfers.source_of_funds_description.placeholder": "Herkunft der Mittel beschreiben ...", "bank_transfers.source_of_funds_description.title": "<PERSON><PERSON><PERSON><PERSON> uns mehr über die Herkunft deiner Mittel", "bank_transfers.withdraw-header": "Au<PERSON>ahl<PERSON>", "bank_transfers.withdraw.amount-input": "Auszuzahlender Betrag", "bank_transfers.withdraw.max-limit-reached": "Betrag überschreitet maximales Überweisungslimit", "bank_transfers.withdrawal.verify-id": "Betrag reduzieren", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Maximales Eingabelimit überschritten", "banner.above_maximum_limit.maximum_limit_per_deposit": "Dies ist das maximale Limit pro Einzahlung", "banner.above_maximum_limit.subtitle": "Maximales Eingabelimit überschritten", "banner.above_maximum_limit.title": "Reduziere den Betrag auf {amount} oder weniger", "banner.above_maximum_limit.title.default": "Reduziere den Betrag", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Mindesteingabelimit unterschritten", "banner.below_minimum_limit.minimum_limit_for_token": "Dies ist das Mindestlimit für diesen Token", "banner.below_minimum_limit.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> den Betrag auf {amount} oder mehr", "banner.below_minimum_limit.title.default": "Erhöhe den Betrag", "breaard.in_porgress.info_popup.cta": "Ausgeben und verdienen {earn}", "breaard.in_porgress.info_popup.footnote": "Durch die Nutzung von Zeal und der Gnosis Pay Karte stimmst du den Teilnahmebedingungen dieser Prämienaktion zu.", "breaward.in_porgress.info_popup.bullet_point_1": "Gib {remaining} innerhalb der nächsten {time} aus, um diese Prämie einzufordern.", "breaward.in_porgress.info_popup.bullet_point_2": "Nur gültige Käufe mit Gnosis Pay zählen.", "breaward.in_porgress.info_popup.bullet_point_3": "Nachdem du die Prämie angefordert hast, wird sie deinem Z<PERSON>-Konto gutgeschrieben.", "breaward.in_porgress.info_popup.header": "<PERSON><PERSON> {earn}, indem du {remaining}", "breward.celebration.for_spending": "<PERSON><PERSON><PERSON> Ausgaben mit deiner Karte", "breward.dc25-eligible-celebration.for_spending": "Du gehörst zu den ersten {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "Du warst nicht unter den ersten {limit} , die ausgegeben haben", "breward.expired_banner.earn_by_spending": "<PERSON><PERSON> {earn} , indem du {amount}", "breward.expired_banner.reward_expired": "{earn} Prämie abgelaufen", "breward.in_progress_banner.cta.title": "Ausgeben und verdienen {earn}", "breward.ready_to_claim.error.try_again": "<PERSON><PERSON><PERSON> versuchen", "breward.ready_to_claim.error_title": "Anfordern der Prämie fehlgeschlagen", "breward.ready_to_claim.in_progress": "<PERSON><PERSON><PERSON><PERSON> wird an<PERSON>", "breward.ready_to_claim.youve_earned": "Du hast {earn} verdient!", "breward_already_claimed.title": "Prämie bereits eingefordert. Wenn du den Prämien-Token nicht erhalten hast, wende dich bitte an den Support.", "breward_cannotbe_claimed.title": "Prämie kann derzeit nicht eingefordert werden. Bitte versuche es später erneut.", "bridge.best_return": "Route mit bester Rendite", "bridge.best_serivce_time": "Schnellste Route", "bridge.check_status.complete": "Abgeschlossen", "bridge.check_status.progress_text": "Bridging von {from} nach {to}", "bridge.remove_topup": "Aufladung entfernen", "bridge.request_status.completed": "Abgeschlossen", "bridge.request_status.pending": "<PERSON><PERSON><PERSON><PERSON>", "bridge.widget.completed": "Abgeschlossen", "bridge.widget.currencies": "{from} nach {to}", "bridge_rote.widget.title": "Bridge", "browse.discover_more_apps": "Weitere Apps entdecken", "browse.google_search_term": "<PERSON><PERSON> „{searchTerm}“", "brward.celebration.you_earned": "Du hast verdient", "brward.expired_banner.subtitle": "Viel Glück beim nächsten Mal", "brward.in_progress_banner.subtitle": "Läuft ab in {expiredInFormatted}", "buy": "<PERSON><PERSON><PERSON>", "buy.enter_amount": "<PERSON><PERSON> e<PERSON>ben", "buy.loading": "Lädt ...", "buy.no_routes_found": "Keine Routen gefunden", "buy.not_enough_balance": "<PERSON>do nicht ausreichend", "buy.select-currency.title": "Token auswählen", "buy.select-to-currency.title": "<PERSON><PERSON> kaufen", "buy_form.title": "<PERSON><PERSON> kaufen", "cancelled-card.create-card-button.primary": "<PERSON>eue virtuelle <PERSON>n", "cancelled-card.switch-card-button.primary": "<PERSON><PERSON> we<PERSON>n", "cancelled-card.switch-card-button.short-text": "Du hast eine weitere aktive Karte", "card": "<PERSON><PERSON>", "card-add-cash.confirm-stage.banner.no-routes-found": "Keine Routen gefunden. Versuche es mit einem anderen Token oder Betrag.", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Du brauchst {amount} mehr {symbol} für die Gebühren", "card-add-cash.confirm-stage.banner.value-loss": "<PERSON> verlierst {loss} an Wert", "card-add-cash.confirm-stage.banner.value-loss.revert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.cancel": "Abbrechen", "card-add-cash.edit-stage.cta.continue": "<PERSON><PERSON>", "card-add-cash.edit-stage.cta.enter-amount": "Eingeben", "card-add-cash.edit-stage.cta.reduce-to-max": "<PERSON><PERSON>", "card-add-cash.edit-staget.banner.no-routes-found": "Keine Routen gefunden. Versuche es mit einem anderen Token oder Betrag.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Wir haben die Transaktion an dein Hardware Wallet gesendet. Bitte fahre dort fort.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Auf Hardware Wallet bestätigen", "card-balance": "Saldo: {balance}", "card-cashback.status.title": "Einzahlung in Cashback", "card-copy-safe-address.copy_address": "<PERSON><PERSON><PERSON> k<PERSON>", "card-copy-safe-address.copy_address.done": "<PERSON><PERSON><PERSON>", "card-copy-safe-address.warning.description": "Diese Adresse kann nur {cardAsset} auf Gnosis Chain empfangen. Sende keine Assets von anderen Netzwerken an diese Adresse. Sie gehen sonst verloren.", "card-copy-safe-address.warning.header": "Sende nur {cardAsset} auf Gnosis Chain", "card-marketing-card.center.subtitle": "FX-Gebühren", "card-marketing-card.center.title": "0 %", "card-marketing-card.left.subtitle": "<PERSON><PERSON><PERSON>", "card-marketing-card.right.subtitle": "Anmeldegeschenk", "card-marketing-card.title": "Europas hochverzinste VISA-Karte", "card-marketing-tile.get-started": "Jetzt starten", "card-select-from-token-title": "Token zum Aufladen auswählen", "card-top-up.banner.subtitle.completed": "Abgeschlossen", "card-top-up.banner.subtitle.failed": "Fehlgeschlagen", "card-top-up.banner.subtitle.pending": "{timerString} Ausstehend", "card-top-up.banner.title": "Einzahlung {amount}", "card-topup.select-token.emptyState": "<PERSON><PERSON> gefunden", "card.activate.card_number_not_valid": "Kartennummer ungültig. Bitte prüfen und erneut versuchen.", "card.activate.invalid_card_number": "Ungültige Kartennummer.", "card.activation.activate_physical_card": "Phys. Karte aktivieren", "card.add-cash.amount-to-withdraw": "Aufladebetrag", "card.add-from-earn-form.title": "Geld zur Karte hinzufügen", "card.add-from-earn-form.withdraw-to-card": "<PERSON><PERSON>", "card.add-from-earn.amount-to-withdraw": "Auszahlungsbetrag für Karte", "card.add-from-earn.enter-amount": "<PERSON><PERSON> e<PERSON>ben", "card.add-from-earn.loading": "Wird geladen", "card.add-from-earn.max-label": "Saldo: {amount}", "card.add-from-earn.no-routes-found": "Keine Routen gefunden", "card.add-from-earn.not-enough-balance": "<PERSON>do nicht ausreichend", "card.add-owner.queued": "Besitzer hinzufügen in Warteschlange", "card.add-to-wallet-flow.subtitle": "Direkt aus deinem Wallet bezahlen", "card.add-to-wallet.copy-card-number": "Kartennummer unten kopieren", "card.add-to-wallet.title": "<PERSON><PERSON> {platformName} <PERSON>et hinzufügen", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "STORNIERT", "card.card-owner-not-found.disconnect-btn": "<PERSON><PERSON> trennen", "card.card-owner-not-found.subtitle": "Um deine Gnosis Pay Karte weiter in Zeal zu nutzen, aktualisiere bitte den Karteninhaber, um sie neu zu verbinden.", "card.card-owner-not-found.title": "Karte erneut verbinden", "card.card-owner-not-found.update-owner-btn": "<PERSON>haber aktualisieren", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} in {date}", "card.cashback.widgetNoCashback.subtitle": "<PERSON>ahle ein, um Prämien zu erhalten", "card.cashback.widgetNoCashback.title": "<PERSON><PERSON><PERSON><PERSON> bis zu {defaultPercentage} Cashback", "card.cashback.widgetcashbackValue.rewards": "{amount} ausstehend", "card.cashback.widgetcashbackValue.title": "{percentage} Cashback", "card.choose-wallet.connect_card": "Karte verbinden", "card.choose-wallet.create-new": "Neue Wallet als Inhaber hinzufügen", "card.choose-wallet.import-another-wallet": "Anderes Wallet importieren", "card.choose-wallet.import-current-owner": "Aktuellen Karteninhaber importieren", "card.choose-wallet.import-current-owner.sub-text": "Schlüssel für Gnosis Pay Karte importieren", "card.choose-wallet.title": "Wallet zur Kartenverwaltung auswählen", "card.connectWalletToCardGuide": "Wallet-<PERSON><PERSON><PERSON> k<PERSON>", "card.connectWalletToCardGuide.addGnosisPayOwner": "Gnosis Pay-Besitzer hinzufügen", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Öffne Gnosispay.com mit deiner anderen Wallet{br}2. <PERSON><PERSON><PERSON> auf „Konto“{br}3. <PERSON><PERSON><PERSON> auf „Kontodetails“{br}4. <PERSON><PERSON><PERSON> auf „Bearbeiten“ neben „Kontoinhaber“ und{br}5. <PERSON><PERSON><PERSON> auf „Adresse hinzufügen“{br}6. Füge deine Zeal-Adresse ein und klicke auf Speichern", "card.connectWalletToCardGuide.header": "Verbinde {account} mit Gnosis Pay Card", "card.connect_card.start": "Gnosis Pay Card verbinden", "card.copiedAddress": "Ko<PERSON>rt {formattedAddress}", "card.disconnect-account.title": "<PERSON><PERSON> trennen", "card.hw-wallet-support-drop.add-owner-btn": "Neuen Inhaber hinzufügen", "card.hw-wallet-support-drop.disconnect-btn": "<PERSON><PERSON> trennen", "card.hw-wallet-support-drop.subtitle": "Um deine Gnosis Pay Karte in Zeal weiterhin zu nutzen, füge bitte einen weiteren Inhaber hinzu, der kein Hardware Wallet ist.", "card.hw-wallet-support-drop.title": "Zeal unterstützt keine Hardware Wallets für Karten mehr.", "card.kyc.continue": "Einrichtung fortsetzen", "card.list_item.title": "<PERSON><PERSON>", "card.onboarded.transactions.empty.description": "<PERSON><PERSON> Zahlungsaktivitäten werden hier angezeigt", "card.onboarded.transactions.empty.title": "Aktivität", "card.order.continue": "Bestellung fortsetzen", "card.order.free_virtual_card": "<PERSON><PERSON><PERSON><PERSON>n", "card.order.start": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bestellen", "card.owner-not-imported.cancel": "Abbrechen", "card.owner-not-imported.import": "Importieren", "card.owner-not-imported.subtitle": "Um diese Transaktion zu autorisieren, verknüpfe das Owner-Wallet deines Gnosis Pay-Kontos mit Zeal. Hinweis: Dies ist ein anderer Vorgang als deine übliche Anmeldung bei Gnosis Pay.", "card.owner-not-imported.title": "Inhaber des Gnosis Pay-Kontos hinzufügen", "card.page.order_free_physical_card": "Physische Karte holen", "card.pin.change_pin_at_atm": "PIN-Änderung an ausgewählten Geldautomaten", "card.pin.timeout": "Der Bildschirm schließt sich in {seconds} Sek.", "card.quick-actions.add-assets": "Ein<PERSON><PERSON><PERSON>", "card.quick-actions.add-cash": "Ein<PERSON><PERSON><PERSON>", "card.quick-actions.details": "Details", "card.quick-actions.freeze": "<PERSON><PERSON><PERSON>", "card.quick-actions.freezing": "Wird ges<PERSON>rt", "card.quick-actions.unfreeze": "Entsperren", "card.quick-actions.unfreezing": "Wird ents<PERSON>rt", "card.quick-actions.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "card.read-only-detected.create-new": "Neue Wallet als Inhaber hinzufügen", "card.read-only-detected.import-current-owner": "Schlüssel importieren für {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Schlüssel für Wallet importieren {address}", "card.read-only-detected.title": "Read-only-Wallet: <PERSON><PERSON> ver<PERSON><PERSON>", "card.remove-owner.queued": "Entfernung des Inhabers vorgemerkt", "card.settings.disconnect-from-zeal": "<PERSON> trennen", "card.settings.edit-owners": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.getCard": "<PERSON><PERSON><PERSON>", "card.settings.getCard.subtitle": "Virtuelle oder physische Karten", "card.settings.notRecharging": "Automatisches Aufladen deaktiviert", "card.settings.notifications.subtitle": "Zahlungsbenachrichtigungen erhalten", "card.settings.notifications.title": "Karten-Benachrichtigungen", "card.settings.page.title": "Karteneinstellungen", "card.settings.select-card.cancelled-cards": "Gekündigte <PERSON>", "card.settings.setAutoRecharge": "Automatisches Aufladen einrichten", "card.settings.show-card-address": "Kartenadresse anzeigen", "card.settings.spend-limit": "Ausgabenlimit festlegen", "card.settings.spend-limit-title": "Aktuelles Tageslimit: {limit}", "card.settings.switch-active-card": "Aktive Karte wechseln", "card.settings.switch-active-card-description": "Aktive Karte: {card}", "card.settings.switch-card.card-item.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.frozen": "<PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.title": "Gnosis Pay Card", "card.settings.switch-card.card-item.title.physical": "Physische Karte", "card.settings.switch-card.card-item.title.virtual": "<PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.title": "<PERSON><PERSON> auswählen", "card.settings.targetBalance": "Zielguthaben: {threshold}", "card.settings.view-pin": "PIN anzeigen", "card.settings.view-pin-description": "Schütze deine PIN immer", "card.title": "<PERSON><PERSON>", "card.transactions.header": "Kartentransaktionen", "card.transactions.see_all": "Alle Transaktionen ansehen", "card.virtual": "VIRTUELL", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Cashback wird zu Beginn der Woche, nachdem du es verdient hast, an deine Karte gesendet.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "Je mehr du e<PERSON>, desto mehr verdi<PERSON>t du bei jedem Einkauf.", "cardCashback.onboarding.title": "<PERSON><PERSON><PERSON><PERSON> bis zu {percentage} Cashback", "cardCashbackWithdraw.amount": "Auszahlungsbetrag", "cardCashbackWithdraw.header": "Auszahlung {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "Karte ist gesperrt und konnte nicht aktiviert werden", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "Bis zu {percentage}", "cardWidget.startEarning": "Jetzt verdienen", "cardWithdraw.amount": "Abhebungsbetrag", "cardWithdraw.header": "<PERSON> abheben", "cardWithdraw.selectWithdrawWallet.title": "<PERSON><PERSON><PERSON><PERSON> Wallet {br}für die Auszahlung", "cardWithdraw.success.cta": "Schließen", "cardWithdraw.success.subtitle": "Aus Sicherheitsgründen dauern alle Abhebungen von der Gnosis Pay Card 3 Minuten.", "cardWithdraw.success.title": "Diese Änderung dauert 3 Minuten", "card_top_up_trx.send": "Senden", "card_top_up_trx.to": "An", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "Gültig bis", "cards.card_number": "Kartennummer", "cards.choose-wallet.no-active-accounts": "Du hast keine aktiven Wallets", "cards.copied_card_number": "Kartennummer kopiert", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Tageslimit überschritten", "cards.transactions.decline_reason.incorrect_pin": "Falsche PIN", "cards.transactions.decline_reason.incorrect_security_code": "Falscher Sicherheitscode", "cards.transactions.decline_reason.invalid_amount": "Ungültiger Betrag", "cards.transactions.decline_reason.low_balance": "<PERSON><PERSON><PERSON>", "cards.transactions.decline_reason.other": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cards.transactions.decline_reason.pin_tries_exceeded": "PIN-Versuche überschritten", "cards.transactions.status.refund": "Rückerstattung", "cards.transactions.status.reversal": "Stornierung", "cashback-deposit.trx.title": "In Cashback e<PERSON><PERSON><PERSON>en", "cashback-estimate.text": "Dies ist eine Schätzung und KEINE garantierte Auszahlung. Es werden alle öffentlich bekannten Cashback-Regeln angewendet, aber Gnosis Pay kann Transaktionen nach eigenem Ermessen ausschließen. Ein maximaler Umsatz von {amount} pro Woche qualifiziert sich für Cashback, auch wenn die Schätzung für diese Transaktion einen höheren Gesamtbetrag ergeben würde.", "cashback-estimate.text.fallback": "Dies ist eine Schätzung und keine garantierte Auszahlung. Es werden alle öffentlich bekannten Cashback-Regeln angewendet, aber Gnosis Pay kann Transaktionen nach eigenem Ermessen ausschließen.", "cashback-estimate.title": "Cashback-Sc<PERSON>ät<PERSON>ng", "cashback-onbarding-tersm.subtitle": "<PERSON><PERSON>aktionsdaten werden mit Karpatkey geteilt, die für die Ausschüttung der Cashback-Prämien verantwortlich sind. Indem du auf „Akzeptieren“ klickst, akzeptierst du die Gnosis DAO Cashback <terms>Nutzungsbedingungen</terms>", "cashback-onbarding-tersm.title": "Nutzungsbedingungen und Datenschutz", "cashback-tx-activity.retry": "<PERSON><PERSON><PERSON> versuchen", "cashback-unconfirmed-payments-info.subtitle": "Zahlungen qualifizieren sich für Cashback, sobald sie mit dem Händler abgerechnet sind. Bis dahin werden sie als unbestätigte Zahlungen angezeigt. Nicht abgerechnete Zahlungen qualifizieren sich nicht für Cashback.", "cashback-unconfirmed-payments-info.title": "Unbestätigte Kartenzahlungen", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "Einzahlung", "cashback.activity.title": "Letzte Aktivitäten", "cashback.activity.withdrawal": "Auszahlung", "cashback.deposit": "Ein<PERSON><PERSON><PERSON>", "cashback.deposit.amount.label": "Einzahlungsbetrag", "cashback.deposit.change": "{from} auf {to}", "cashback.deposit.confirmation.subtitle": "Cashback-Raten werden einmal pro Woche aktualisiert. Zahle jetzt ein, um das Cashback der nächsten Woche zu erhöhen.", "cashback.deposit.confirmation.title": "<PERSON> verdienst {percentage} ab dem {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Tausche Token in {currency} auf der {network} Chain", "cashback.deposit.get.tokens.title": "Erhalte {currency} -Token", "cashback.deposit.header": "Einzahlung {currency}", "cashback.deposit.max_label": "Max.: {amount}", "cashback.deposit.select-wallet.title": "Wallet für die Einzahlung auswählen", "cashback.deposit.yourcashback": "<PERSON><PERSON>", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "<PERSON>et auswählen{br}zum Auszahlen", "cashback.transaction-details.network-label": "Netzwerk", "cashback.transaction-details.reward-period": "{start} – {end}", "cashback.transaction-details.top-row.label-deposit": "<PERSON>", "cashback.transaction-details.top-row.label-rewards": "Cashback-<PERSON><PERSON><PERSON><PERSON>", "cashback.transaction-details.top-row.label-withdrawal": "An", "cashback.transaction-details.transaction": "Transaktions-ID", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Cashback-Transaktionen", "cashback.withdraw": "Au<PERSON>ahl<PERSON>", "cashback.withdraw.confirmation.cashback_reduction": "Das Cashback für diese Woche, einsch<PERSON>ßlich deiner bereits verdienten Beträge, wird von {before} auf {after}", "cashback.withdraw.queued": "Auszahlung in Warteschlange", "cashback.withdrawal.change": "{from} auf {to}", "cashback.withdrawal.confirmation.subtitle": "Auszahlung von {amount} mit 3 Minuten Verzögerung starten. Dies reduziert dein Cashback auf {after}.", "cashback.withdrawal.confirmation.title": "<PERSON><PERSON> sinkt, wenn du GNO auszahlst", "cashback.withdrawal.delayTransaction.title": "GNO-Auszahlung starten mit{br} 3 Minuten Verzögerung", "cashback.withdrawal.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "cashback.withdrawal.yourcashback": "<PERSON><PERSON>", "celebration.aave": "<PERSON><PERSON> <PERSON> verdient", "celebration.cashback.subtitle": "Ausgezahlt in {code}", "celebration.cashback.subtitleGNO": "{amount} zuletzt verdient", "celebration.chf": "<PERSON><PERSON> verdient", "celebration.lido": "<PERSON><PERSON> ve<PERSON>", "celebration.sky": "Mit <PERSON> verdient", "celebration.title": "Cashback gesamt", "celebration.well_done.title": "Gut gemacht!", "change-withdrawal-account.add-new-account": "Weiteres Bankkonto hinzufügen", "change-withdrawal-account.item.shortText": "{currency} -Konto", "check-confirmation.approve.footer.for": "<PERSON><PERSON><PERSON>", "checkConfirmation.title": "Transaktionsergebnis", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin & Ether", "collateral.ethereum": "Ethereum", "collateral.other": "Sonstige", "collateral.rwa": "Reale Vermögenswerte", "collateral.stablecoins": "Stablecoins (USD-gebunden)", "collateral.us-t-bills": "US-Schatzwechsel", "confirm-bank-transfer-recipient.bullet-1": "<PERSON><PERSON> Gebühren auf digitale EUR", "confirm-bank-transfer-recipient.bullet-2": "Einzahlungen an {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Teile die Kontodaten deines Gnosis Pay-Kontos mit Monerium, einem autorisierten und regulierten E-Geld-Institut. <link><PERSON><PERSON> erfahren</link>", "confirm-bank-transfer-recipient.bullet-4": "Akzeptiere Moneriums <link>Nutzungsbedingungen</link>", "confirm-bank-transfer-recipient.title": "Bedingungen akzeptieren", "confirm-change-withdrawal-account.cancel": "Abbrechen", "confirm-change-withdrawal-account.confirm": "Bestätigen", "confirm-change-withdrawal-account.saving": "Speichern", "confirm-change-withdrawal-account.subtitle": "Alle Auszahlungen von Zeal werden auf diesem Bankkonto empfangen.", "confirm-change-withdrawal-account.title": "Empfängerbank ändern", "confirm-ramove-withdrawal-account.title": "Bankkonto entfernen", "confirm-remove-withdrawal-account.subtitle": "Diese Bankkontodaten werden aus Zeal entfernt. Du kannst sie jederzeit wieder hinzufügen.", "confirmTransaction.finalNetworkFee": "Netzwerkgebühr", "confirmTransaction.importKeys": "Schlüssel importieren", "confirmTransaction.networkFee": "Netzwerkgebühr", "confirmation.title": "Sende {amount} an {recipient}", "conflicting-monerium-account.add-owner": "Als Gnosis Pay-Inhaber hinzufügen", "conflicting-monerium-account.create-wallet": "Neues Smart Wallet erstellen", "conflicting-monerium-account.disconnect-card": "<PERSON><PERSON> von <PERSON> trennen und mit neuem Inhaber verbinden", "conflicting-monerium-account.header": "{wallet} mit einem anderen Monerium-Konto verknüpft", "conflicting-monerium-account.subtitle": "Ändere dein Gnosis Pay-Inhaber-Wallet", "connection.diconnected.got_it": "Verstanden!", "connection.diconnected.page1.subtitle": "Zeal funktioniert überall, wo MetaMask funktioniert. Verbinde dich einfach wie mit MetaMask.", "connection.diconnected.page1.title": "Wie verbinde ich mich mit Z<PERSON>?", "connection.diconnected.page2.subtitle": "Du wirst viele Optionen sehen. Zeal könnte eine davon sein. <PERSON>n <PERSON>eal nicht erscheint …", "connection.diconnected.page2.title": "<PERSON><PERSON><PERSON> auf „Wallet verbinden“", "connection.diconnected.page3.subtitle": "Wir fordern eine Verbindung mit Zeal an. <PERSON><PERSON><PERSON> oder Injected sollte auch funktionieren. <PERSON>bier's aus!", "connection.diconnected.page3.title": "<PERSON>ähle MetaMask", "connectionSafetyCheck.tag.caution": "V<PERSON>icht", "connectionSafetyCheck.tag.danger": "<PERSON><PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.passed": "<PERSON><PERSON><PERSON>", "connectionSafetyConfirmation.subtitle": "Möchtest du wirklich fortfahren?", "connectionSafetyConfirmation.title": "Diese Seite scheint gefährlich", "connection_state.connect.cancel": "Abbrechen", "connection_state.connect.changeToMetamask": "<PERSON>u MetaM<PERSON> wechseln 🦊", "connection_state.connect.changeToMetamask.label": "Zu <PERSON>a<PERSON> wechseln", "connection_state.connect.connect_button": "Verbinden", "connection_state.connect.expanded.connected": "Verbunden", "connection_state.connect.expanded.title": "Verbinden", "connection_state.connect.safetyChecksLoading": "Sicherheit der Seite wird geprüft", "connection_state.connect.safetyChecksLoadingError": "Sicherheitsprüfungen fehlgeschlagen", "connection_state.connected.expanded.disconnectButton": "Zeal trennen", "connection_state.connected.expanded.title": "Verbunden", "copied-diagnostics": "Diagnose-<PERSON><PERSON>", "copy-diagnostics": "Diagnose-<PERSON><PERSON> k<PERSON>", "counterparty.component.add_recipient_primary_text": "Bankempfänger hinzufügen", "counterparty.country": "Land", "counterparty.countryTitle": "Land des Empfängers", "counterparty.currency": "Währung", "counterparty.delete.success.title": "Entfernt", "counterparty.edit.success.title": "Änderungen gespeichert", "counterparty.errors.country_required": "Land erforderlich", "counterparty.errors.first_name.invalid": "<PERSON><PERSON>ame sollte länger sein", "counterparty.errors.last_name.invalid": "Nachname sollte länger sein", "counterparty.first_name": "<PERSON><PERSON><PERSON>", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "An Bank senden", "countrySelector.noCountryFound": "Kein Land gefunden", "countrySelector.title": "Land auswählen", "create-passkey.cta": "<PERSON><PERSON> erstellen", "create-passkey.extension.cta": "<PERSON><PERSON>", "create-passkey.footnote": "Powered by", "create-passkey.mobile.cta": "Sicherheit einrichten", "create-passkey.steps.enable-recovery": "Cloud-Wiederherstellung einrichten", "create-passkey.steps.setup-biometrics": "Biometrische Sicherheit aktivieren", "create-passkey.subtitle": "Passkeys sind sicherer als Passwörter und werden für eine einfache Wiederherstellung verschlüsselt in der Cloud gespeichert.", "create-passkey.title": "<PERSON><PERSON> a<PERSON>iche<PERSON>", "create-smart-wallet": "Smart Wallet erstellen", "create-userop.progress.text": "<PERSON>ird erste<PERSON>t", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "<PERSON><PERSON> in Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Zu Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "Bestellung auf Gnosis Pay abschließen.", "create_recharge_preferences.card": "<PERSON><PERSON>", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "Verdiene {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "Geld halten", "create_recharge_preferences.link_accounts_title": "Konten verknüpfen", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "<PERSON><PERSON> wird nach jeder Zahlung NICHT automatisch aufgeladen.", "create_recharge_preferences.not_configured_title": "Verdienen & Ausgeben", "create_recharge_preferences.recharge_from_earn_accounts_description": "<PERSON><PERSON> wird nach jeder Zahlung automatisch von deinem Earn-Konto aufgeladen.", "create_recharge_preferences.subtitle": "pro Jahr", "creating-account.loading": "<PERSON><PERSON> wird erstellt", "creating-gnosis-pay-account": "<PERSON><PERSON> wird erstellt", "currencies.bridge.select_routes.emptyState": "Wir haben keine Routen für diese Bridge gefunden", "currency.add_currency.add_token": "Token hinzufügen", "currency.add_currency.not_a_valid_address": "Dies ist keine gültige Token-Adresse", "currency.add_currency.token_decimals_feild": "Token-Dezimalen", "currency.add_currency.token_feild": "Token-<PERSON><PERSON><PERSON>", "currency.add_currency.token_symbol_feild": "Token-Symbol", "currency.add_currency.update_token": "Token aktualisieren", "currency.add_custom.remove_token.cta": "Entfernen", "currency.add_custom.remove_token.header": "Token entfernen", "currency.add_custom.remove_token.subtitle": "Dein <PERSON>et behält den Saldo dieses Tokens, aber er wird in deinem Zeal-Portfolio ausgeblendet.", "currency.add_custom.token_removed": "Token entfernt", "currency.add_custom.token_updated": "Token aktualisiert", "currency.balance_label": "Saldo: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "<PERSON><PERSON> Banküberweisung hat erfolgreich {fiat} in {crypto} umgewandelt.", "currency.bankTransfer.deposit_status.finished.title": "Eingang: {crypto}", "currency.bankTransfer.deposit_status.success": "In deiner Wallet erhalten", "currency.bankTransfer.deposit_status.title": "Einzahlung", "currency.bankTransfer.off_ramp.check_bank_account": "Prüfe dein Bankkonto", "currency.bankTransfer.off_ramp.complete": "Abgeschlossen", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Wird an deine Bank gesendet", "currency.bankTransfer.off_ramp.transferring_to_currency": "Wird um<PERSON><PERSON><PERSON> in {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Das Geld sollte inzwischen auf deinem Bankkonto eingegangen sein.", "currency.bankTransfer.withdrawal_status.success": "An deine Bank gesendet", "currency.bankTransfer.withdrawal_status.title": "Auszahlung", "currency.bank_transfer.create_unblock_user.email": "E-Mail-Adresse", "currency.bank_transfer.create_unblock_user.email_invalid": "Ungültige E-Mail-Adresse", "currency.bank_transfer.create_unblock_user.email_missing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_missing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> und - . , & ( ) ' sind erlaubt.", "currency.bank_transfer.create_unblock_user.last_name": "Nachname", "currency.bank_transfer.create_unblock_user.last_name_missing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> und - . , & ( ) ' sind erlaubt.", "currency.bank_transfer.create_unblock_user.note": "Indem du fortfährst, akzeptierst du die von Unblock (unserem Banking-Partner) <terms>Nutzungsbedingungen</terms> und die <policy>Datenschutzrichtlinie</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Gib deinen Namen genau wie auf deinem Bankkonto an", "currency.bank_transfer.create_unblock_user.title": "Verknüpfe dein Bankkonto", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Land der Bank", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Bevorzugte Währung", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Bankleitzahl (Sort Code)", "currency.bank_transfer.create_unblock_withdraw_account.success": "<PERSON><PERSON>ich<PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.title": "Verknüpfe dein Bankkonto", "currency.bank_transfer.residence-form.address-required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.address-unsupported-char": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> , ; {apostrophe} - \\\\ sind erlaubt.", "currency.bank_transfer.residence-form.city-required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.city-unsupported-char": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> und . , - & ( ) {apostrophe} sind erlaubt.", "currency.bank_transfer.residence-form.postcode-invalid": "Ungültige Postleitzahl", "currency.bank_transfer.residence-form.postcode-required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.validation.invalid.account_number": "Ungültige Kontonummer", "currency.bank_transfer.validation.invalid.iban": "Ungültige IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Ungültige Bankleitzahl (Sort Code)", "currency.bridge.amount_label": "Betrag für die Bridge", "currency.bridge.best_returns.subtitle": "Dieser Bridge-<PERSON><PERSON><PERSON> bietet dir den höchsten Ertrag, inklusive aller Gebühren.", "currency.bridge.best_returns_popup.title": "Beste Rendite", "currency.bridge.bridge_from": "<PERSON>", "currency.bridge.bridge_gas_fee_loading_failed": "Wir hatten Probleme beim Laden der Netzwerkgebühr.", "currency.bridge.bridge_low_slippage": "<PERSON><PERSON> geringe Slippage. Versuche, sie zu erh<PERSON>hen.", "currency.bridge.bridge_provider": "Überweisungsanbieter", "currency.bridge.bridge_provider_loading_failed": "<PERSON><PERSON> Anbieter gab es Probleme", "currency.bridge.bridge_settings": "Bridge-Einstellungen", "currency.bridge.bridge_status.subtitle": "<PERSON><PERSON> {name}", "currency.bridge.bridge_status.title": "Bridge", "currency.bridge.bridge_to": "An", "currency.bridge.fastest_route_popup.subtitle": "Dieser Bridge-<PERSON><PERSON><PERSON> bietet dir die schnellste Transaktionsroute.", "currency.bridge.fastest_route_popup.title": "Schnellste Route", "currency.bridge.from": "<PERSON>", "currency.bridge.success": "Abgeschlossen", "currency.bridge.title": "Bridge", "currency.bridge.to": "An", "currency.bridge.topup": "Aufladen {symbol}", "currency.bridge.withdrawal_status.title": "Auszahlung", "currency.card.card_top_up_status.title": "<PERSON><PERSON> auf Ka<PERSON>", "currency.destination_amount": "Zielbetrag", "currency.hide_currency.confirm.subtitle": "Blende diesen Token in deinem Portfolio aus. Du kannst ihn jederzeit wieder einblenden.", "currency.hide_currency.confirm.title": "Token ausblenden", "currency.hide_currency.success.title": "Token ausgeblendet", "currency.label": "<PERSON><PERSON><PERSON><PERSON>ng (Optional)", "currency.last_name": "Nachname", "currency.max_loading": "Max.:", "currency.swap.amount_to_swap": "Betrag zum Tauschen", "currency.swap.best_return": "Route mit bestem E<PERSON>", "currency.swap.destination_amount": "Zielbetrag", "currency.swap.header": "Tauschen", "currency.swap.max_label": "Saldo: {amount}", "currency.swap.provider.header": "Swap-Anbieter", "currency.swap.select_to_token": "Token auswählen", "currency.swap.swap_gas_fee_loading_failed": "Wir hatten Probleme beim Laden der Netzwerkgebühr.", "currency.swap.swap_provider_loading_failed": "Wir hatten Probleme beim Laden der Anbieter.", "currency.swap.swap_settings": "Tausch-Einstellungen", "currency.swap.swap_slippage_too_low": "<PERSON><PERSON> geringe Slippage. Versuche, sie zu erh<PERSON>hen.", "currency.swaps_io_native_token_swap.subtitle": "Über Swaps.IO", "currency.swaps_io_native_token_swap.title": "Senden", "currency.withdrawal.amount_from": "<PERSON>", "currency.withdrawal.amount_to": "An", "currencySelector.title": "Währung auswählen", "dApp.wallet-does-not-support-chain.subtitle": "De<PERSON> scheint {network} nicht zu unterstützen. Verbinde dich mit einer anderen Wallet oder nutze stattdessen Zeal.", "dApp.wallet-does-not-support-chain.title": "Nicht unterstütztes Netzwerk", "dapp.connection.manage.confirm.disconnect.all.cta": "Alle trennen", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Bist du sicher, dass du alle Verbindungen trennen möchtest?", "dapp.connection.manage.confirm.disconnect.all.title": "Alle trennen", "dapp.connection.manage.connection_list.main.button.title": "<PERSON><PERSON><PERSON>", "dapp.connection.manage.connection_list.no_connections": "Du hast keine verbundenen Apps", "dapp.connection.manage.connection_list.section.button.title": "Alle trennen", "dapp.connection.manage.connection_list.section.title": "Aktiv", "dapp.connection.manage.connection_list.title": "Verbindungen", "dapp.connection.manage.disconnect.success.title": "Apps getrennt", "dapp.metamask_mode.title": "MetaMask-Modus", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4 %", "dc25-card-marketing-card.left.subtitle": "<PERSON><PERSON><PERSON>", "dc25-card-marketing-card.right.subtitle": "100 Personen", "dc25-card-marketing-card.title": "Die ersten 100, die 50 € ausgeben, erhalten {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Du kannst diese Aktion für 3 Min. nicht ausführen. Aus Sicherheitsgründen dauert die Bearbeitung von Änderungen der Karteneinstellungen oder Abhebungen 3 Minuten.", "delayQueueBusyBanner.processing-yout-action.title": "Deine Aktion wird verarbeitet, bitte warten", "delayQueueBusyWidget.cardFrozen": "<PERSON><PERSON> g<PERSON>", "delayQueueBusyWidget.processingAction": "Deine Aktion wird verarbeitet", "delayQueueFailedBanner.action-incomplete.get-support": "Support erhalten", "delayQueueFailedBanner.action-incomplete.subtitle": "Leider ist bei deiner Abhebung oder der Aktualisierung deiner Einstellungen ein Fehler aufgetreten. Bitte kontaktiere den Support auf Discord.", "delayQueueFailedBanner.action-incomplete.title": "Aktion unvollständig", "delayQueueFailedWidget.actionIncomplete.title": "Kartenaktion unvollständig", "delayQueueFailedWidget.cardFrozen.subtitle": "<PERSON><PERSON> g<PERSON>", "delayQueueFailedWidget.contactSupport": "Support kontaktieren", "delay_queue_busy.subtitle": "Aus Sicherheitsgründen dauert die Bearbeitung von Änderungen der Karteneinstellungen oder Abhebungen 3 Minuten. <PERSON><PERSON><PERSON><PERSON> dieser Zeit ist deine Karte gesperrt.", "delay_queue_busy.title": "Deine Aktion wird bearbeitet", "delay_queue_failed.contact_support": "Support", "delay_queue_failed.subtitle": "Leider ist bei deiner Abhebung oder der Aktualisierung deiner Einstellungen ein Fehler aufgetreten. Bitte kontaktiere den Support auf Discord.", "delay_queue_failed.title": "Support kontaktieren", "deploy-earn-form-smart-wallet.in-progress.title": "<PERSON>arn wird vorbereitet", "deposit": "Ein<PERSON><PERSON><PERSON>", "disconnect-card-popup.cancel": "Abbrechen", "disconnect-card-popup.disconnect": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.subtitle": "<PERSON><PERSON><PERSON> wird deine Karte aus der Zeal-App entfernt. <PERSON><PERSON> bleibt in der Gnosis-Pay-App mit deiner Karte verbunden. Du kannst deine Karte jederzeit wieder verbinden.", "disconnect-card-popup.title": "<PERSON><PERSON> trennen", "distance.long.days": "{count} <PERSON><PERSON>", "distance.long.hours": "{count} <PERSON><PERSON><PERSON>", "distance.long.minutes": "{count} Minuten", "distance.long.months": "{count} <PERSON><PERSON>", "distance.long.seconds": "{count} <PERSON><PERSON><PERSON>", "distance.long.years": "{count} J<PERSON>re", "distance.short.days": "{count} T", "distance.short.hours": "{count} h", "distance.short.minutes": "{count} min", "distance.short.months": "{count} M", "distance.short.seconds": "{count} s", "distance.short.years": "{count} J", "duration.short.days": "{count}T", "duration.short.hours": "{count}h", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "Einzahlung", "earn-deposit-view.into": "In", "earn-deposit-view.to": "An", "earn-deposit.swap.transfer-provider": "Anbieter für Überweisung", "earn-taker-investment-details.accrued-realtime": "Zuwachs in Echtzeit", "earn-taker-investment-details.asset-class": "Anlageklasse", "earn-taker-investment-details.asset-coverage-ratio": "Deckungsgrad der Vermögenswerte", "earn-taker-investment-details.asset-reserve": "Vermögensreserve", "earn-taker-investment-details.base_currency.label": "Basiswährung", "earn-taker-investment-details.chf.description": "Verdiene Zinsen auf deine CHF, indem du zCHF bei Frankencoin – einem vertrauenswürdigen digitalen Geldmarkt – einzahlst. Die Zinsen werden aus risikoarmen, überbesicherten Krediten auf Frankencoin generiert und in Echtzeit ausgezahlt. Dein Geld ist auf einem sicheren Unterkonto geschützt, das nur du kontrollierst.", "earn-taker-investment-details.chf.description.with_address_link": "Verdiene Zinsen auf deine CHF, indem du zCHF bei Frankencoin – einem vertrauenswürdigen digitalen Geldmarkt – einzahlst. Die Zinsen werden aus risikoarmen, überbesicherten Krediten auf Frankencoin generiert und in Echtzeit ausgezahlt. Dein Geld ist auf einem sicheren Unterkonto geschützt <link>(0x kopieren)</link> , das nur du kontrollierst.", "earn-taker-investment-details.chf.label": "Digitaler Schweizer Franken", "earn-taker-investment-details.collateral-composition": "Zusammensetzung der Sicherheiten", "earn-taker-investment-details.depositor-obligations": "Verbindlichkeiten gegenüber Einlegern", "earn-taker-investment-details.eure.description": "Verdiene Zinsen auf deine Euro, indem du EURe bei Aave einzahlst – einem vertrauenswürdigen digitalen Geldmarkt. EURe ist ein vollständig regulierter Euro-Stablecoin, der von Monerium ausgegeben und 1:1 auf gesicherten Konten gedeckt wird. Die Zinsen werden aus risikoarmen, überbesicherten Krediten auf Aave generiert und in Echtzeit ausgezahlt. Dein G<PERSON> bleibt auf einem sicheren Unterkonto, das nur du kontrollierst.", "earn-taker-investment-details.eure.description.with_address_link": "Verdiene Zinsen auf deine Euro, indem du EURe bei Aave einzahlst – einem vertrauenswürdigen digitalen Geldmarkt. EURe ist ein vollständig regulierter Euro-Stablecoin, der von Monerium ausgegeben und 1:1 auf gesicherten Konten gedeckt wird. Die Zinsen werden aus risikoarmen, überbesicherten Krediten auf Aave generiert und in Echtzeit ausgezahlt. Dein G<PERSON> bleibt auf einem sicheren Unterkonto <link>(0x kopieren)</link> , das nur du kontrollierst.", "earn-taker-investment-details.eure.label": "Digitaler Euro (EURe)", "earn-taker-investment-details.faq": "FAQ", "earn-taker-investment-details.fixed-income": "Festverzinslich", "earn-taker-investment-details.issuer": "Emittent", "earn-taker-investment-details.key-facts": "Wichtige Fakten", "earn-taker-investment-details.liquidity": "Liquidität", "earn-taker-investment-details.operator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.projected-yield": "Voraussichtliche Jahresrendite", "earn-taker-investment-details.see-other-faq": "Alle weiteren FAQs ansehen", "earn-taker-investment-details.see-realtime": "Echtzeitdaten an<PERSON>hen", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Rendite der letzten 12 Monate", "earn-taker-investment-details.total-collateral": "Gesamtsicherheit", "earn-taker-investment-details.total-deposits": "27.253.300.208 $", "earn-taker-investment-details.total-zchf-supply": "Gesamtangebot ZCHF", "earn-taker-investment-details.total_deposits": "Aave-Einlagen gesamt", "earn-taker-investment-details.usd.description": "Sky ist ein digitaler Geldmarkt, der stabile, US-Dollar-denominierte Renditen aus kurzfristigen US-Staatsanleihen und überbesicherten Krediten bietet – ohne Krypto-Volatilität, mit 24/7-Zugriff auf dein Geld und transparenter On-Chain-Deckung.", "earn-taker-investment-details.usd.description.with_address_link": "Sky ist ein digitaler Geldmarkt, der stabile, US-Dollar-denominierte Renditen aus kurzfristigen US-Staatsanleihen und überbesicherten Krediten bietet – ohne Krypto-Volatilität, mit 24/7-Zugriff auf dein Geld und transparenter On-Chain-Deckung. Deine Anlagen befinden sich auf einem Unterkonto <link>(0x kopieren)</link> , das von dir kontrolliert wird.", "earn-taker-investment-details.usd.ftx-difference": "<PERSON><PERSON><PERSON> besteht der Unterschied zu FTX, <PERSON><PERSON><PERSON>, Block<PERSON>i oder Luna?", "earn-taker-investment-details.usd.high-returns": "Wie können die Renditen so hoch sein, insbesondere im Vergleich zu traditionellen Banken?", "earn-taker-investment-details.usd.how-is-backed": "Wie ist Sky USD gedeckt und was passiert mit meinem Geld, wenn Zeal insolvent wird?", "earn-taker-investment-details.usd.income-sources": "Einnahmequellen 2024", "earn-taker-investment-details.usd.insurance": "Ist mein Geld durch eine Einrichtung (wie FDIC oder ähnliche) versichert oder garantiert?", "earn-taker-investment-details.usd.label": "Digitaler US-Dollar", "earn-taker-investment-details.usd.lose-principal": "Kann ich mein Kapital realistischerweise verlieren und unter welchen Umständen?", "earn-taker-investment-details.variable-rate": "Variable Verzinsung", "earn-taker-investment-details.withdraw-anytime": "Jederzeit auszahlen", "earn-taker-investment-details.yield": "Rendite", "earn-withdrawal-view.approve.for": "<PERSON><PERSON><PERSON>", "earn-withdrawal-view.approve.into": "In", "earn-withdrawal-view.swap.into": "In", "earn-withdrawal-view.withdraw.to": "An", "earn.add_another_asset.title": "Asset zum Verdi<PERSON>n auswählen", "earn.add_asset": "<PERSON><PERSON>", "earn.asset_view.title": "<PERSON><PERSON><PERSON>", "earn.base-currency-popup.text": "Die Basiswährung ist die Währung, in der deine Einlagen, Renditen und Transaktionen bewertet und erfasst werden. Wenn du in einer anderen Währung einzahlst (z. B. EUR in USD), wird dein Geld sofort zum aktuellen Wechselkurs in die Basiswährung umgerechnet. Nach der Umrechnung bleibt dein Saldo in der Basiswährung stabil, aber bei zukünftigen Auszahlungen kann es erneut zu Währungsumrechnungen kommen.", "earn.base-currency-popup.title": "Basiswährung", "earn.card-recharge.disabled.list-item.title": "Automatisches Aufladen deaktiviert", "earn.card-recharge.enabled.list-item.title": "Automatisches Aufladen aktiviert", "earn.choose_wallet_to_deposit.title": "<PERSON><PERSON><PERSON><PERSON> von", "earn.config.currency.eth": "Ethereum verdienen", "earn.config.currency.on_chain_address_subtitle": "Onchain-<PERSON><PERSON><PERSON>", "earn.config.currency.us_dollars": "Banküberweisungen einrichten", "earn.configured_widget.current_apy.title": "Aktueller APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} <PERSON><PERSON><PERSON><PERSON>", "earn.confirm.currency.cta": "Ein<PERSON><PERSON><PERSON>", "earn.currency.eth": "Ethereum verdienen", "earn.deploy.status.title": "Earn-<PERSON><PERSON>", "earn.deploy.status.title_with_taker": "<PERSON><PERSON><PERSON> {title} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.deposit": "Ein<PERSON><PERSON><PERSON>", "earn.deposit.amount_to_deposit": "Einzahlungsbetrag", "earn.deposit.deposit": "Ein<PERSON><PERSON><PERSON>", "earn.deposit.enter_amount": "<PERSON><PERSON> e<PERSON>ben", "earn.deposit.no_routes_found": "Keine Routen gefunden", "earn.deposit.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON> nicht ausreichend", "earn.deposit.select-currency.title": "Token zum Einzahlen auswählen", "earn.deposit.select_account.title": "Earn-<PERSON><PERSON> auswählen", "earn.desposit_form.title": "In „Verdienen“ e<PERSON>zahlen", "earn.earn_deposit.status.title": "Einzahlung in Earn", "earn.earn_deposit.trx.title": "In Earn ein<PERSON>en", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "Geld jederzeit abheben", "earn.earn_withdraw.status.title": "Auszahlung vom Earn-Konto", "earn.earn_withdraw.trx.title.approval": "Auszahlung genehmigen", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Auszahlung in {asset}", "earn.earn_withdraw.trx.title.withdrawal": "Auszahlung von <PERSON>n", "earn.recharge.cta": "Änderungen speichern", "earn.recharge.earn_not_configured.enable_some_account.error": "Konto aktivieren", "earn.recharge.earn_not_configured.enter_amount.error": "<PERSON><PERSON> e<PERSON>ben", "earn.recharge.select_taker.header": "Karte aufladen in Reihenfolge von", "earn.recharge_card_tag.on": "an", "earn.recharge_card_tag.recharge": "Aufladen", "earn.recharge_card_tag.recharge_not_configured": "Autom. Aufladen", "earn.recharge_card_tag.recharge_off": "Aufladen aus", "earn.recharge_card_tag.recharged": "Aufgeladen", "earn.recharge_card_tag.recharging": "<PERSON><PERSON><PERSON> auf", "earn.recharge_configured.disable.trx.title": "Automatisches Aufladen deaktivieren", "earn.recharge_configured.trx.disclaimer": "Wenn du deine Ka<PERSON> nutzt, wird eine Cowswap-Auktion erstellt, um mit deinem Earn-Guthaben den gleichen Betrag wie deine Zahlung zu kaufen. Dieses Auktionsverfahren sichert dir in der Regel den besten Marktkurs. <PERSON><PERSON>, dass der On-Chain-Kurs von realen Wechselkursen abweichen kann.", "earn.recharge_configured.trx.subtitle": "Nach jeder Zahlung wird dein Kartensaldo automatisch von deinen Earn-Konten aufgefüllt, um ihn bei {value}", "earn.recharge_configured.trx.title": "Automatisches Aufladen festlegen auf {value}", "earn.recharge_configured.updated.trx.title": "Aufladeeinstellungen speichern", "earn.risk-banner.subtitle": "Dies ist ein privates Produkt ohne regulatorischen Schutz vor Verlusten.", "earn.risk-banner.title": "Verstehe die Risiken", "earn.set_recharge.status.title": "Automatisches Aufladen einrichten", "earn.setup_reacharge.input.disable.label": "Deaktivieren", "earn.setup_reacharge.input.label": "Ziel-Saldo <PERSON>", "earn.setup_reacharge_form.title": "Auto-Recharge hält deine{br}Karte auf dem gleichen Saldo", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Halte wstETH (Staked ETH) auf der Gnosis Chain und verleihe über Lido.", "earn.taker-bulletlist.point_1": "Verdiene {apyValue} j<PERSON><PERSON><PERSON>. Die Renditen schwanken mit dem Markt.", "earn.taker-bulletlist.point_3": "Zeal erhebt keine Gebühren.", "earn.taker-historical-returns": "Historische Renditen", "earn.taker-historical-returns.chf": "Wertentwicklung von CHF zu <PERSON>", "earn.taker-investment-tile.apy.perYear": "pro Jahr", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "Ein<PERSON><PERSON><PERSON>", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Verdienen durch Aave auf der Gnosis Chain", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Verdienen mit Frankencoin auf der Gnosis Chain", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Verdienen durch Lido auf der Gnosis Chain", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Verdienen durch Maker auf der Gnosis Chain", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "<PERSON><PERSON> verdienen", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH Earn", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "US-Staatsanleihen (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Obwohl extrem selten, ist es theoretisch möglich. Dein Geld ist durch striktes Risikomanagement und hohe Besicherung geschützt. Das realistische Worst-Case-Szenario wären beispiellose Marktbedingungen, wie z. B. der gleichzeitige Verlust der Anbindung mehrerer Stablecoins – etwas, das noch nie zuvor passiert ist.", "earn.usd.can-I-lose-my-principal-popup.title": "Kann ich mein Kapital realistischerweise verlieren und unter welchen Umständen?", "earn.usd.ftx-difference-popup.text": "Sky ist fundamental anders. <PERSON><PERSON>T<PERSON>, <PERSON><PERSON><PERSON>, Block<PERSON>i oder Luna, die stark auf zentralisierte Verwahrung, intransparente Vermögensverwaltung und riskante Hebelpositionen setzten, nutzt Sky USD transparente, geprüfte, dezentrale Smart Contracts und wahrt volle On-Chain-Transparenz. Du behältst die vollständige Kontrolle über deine private Wallet, was das Gegenparteirisiko bei zentralisierten Ausfällen erheblich reduziert.", "earn.usd.ftx-difference-popup.title": "<PERSON><PERSON><PERSON> besteht der Unterschied zu FTX, <PERSON><PERSON><PERSON>, Block<PERSON>i oder Luna?", "earn.usd.high-returns-popup.text": "Sky USD erzielt Renditen hauptsächlich durch dezentrale Finanzprotokolle (DeFi), die Peer-to-Peer-Kredite und die Bereitstellung von Liquidität automatisieren und dabei traditionelle Bankgebühren und Vermittler eliminieren. <PERSON><PERSON>, kombiniert mit robusten Risikokontrollen, ermöglicht deutlich höhere Renditen als bei traditionellen Banken.", "earn.usd.high-returns-popup.title": "Wie können die Renditen so hoch sein, insbesondere im Vergleich zu traditionellen Banken?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD ist vollständig gedeckt und überbesichert durch eine Kombination aus digitalen Vermögenswerten in sicheren Smart Contracts und realen Vermögenswerten wie US-Staatsanleihen. Die Reserven können in Echtzeit onchain geprüft werden, sogar direkt in Zeal, was Transparenz und Sicherheit bietet. Im unwahrscheinlichen Fall einer Einstellung von Zeal bleiben deine Vermögenswerte onchain gesichert, unter deiner vollen Kontrolle und über andere kompatible Wallets zugänglich.", "earn.usd.how-is-sky-backed-popup.title": "Wie ist Sky USD gedeckt und was passiert mit meinem Geld, wenn Zeal insolvent wird?", "earn.usd.insurance-popup.text": "Sky USD-Guthaben sind nicht FDIC-versichert oder durch traditionelle staatliche Garantien abgesichert, da es sich um ein digitales, vermögenswertbasiertes Konto und nicht um ein herkömmliches Bankkonto handelt. Stattdessen steuert Sky die Risikominderung durch geprüfte Smart Contracts und sorgfältig ausgewählte DeFi-Protokolle, um sicherzustellen, dass die Vermögenswerte sicher und transparent bleiben.", "earn.usd.insurance-popup.title": "Ist mein Geld durch eine Einrichtung (wie FDIC oder ähnliche) versichert oder garantiert?", "earn.usd.lending-operations-popup.text": "Sky USD erzielt Renditen, indem Stablecoins über dezentrale Kreditmärkte wie Morpho und Spark verliehen werden. Deine Stablecoins werden an Kreditnehmer verliehen, die deutlich mehr Sicherheiten – wie ETH oder BTC – hinterlegen, als der Wert ihres Kredits beträgt. <PERSON><PERSON>, genannt Überbesicherung, stellt sicher, dass immer genügend Sicherheiten zur Deckung der Kredite vorhanden sind, was das Risiko erheblich reduziert. Die eingenommenen Zinsen und gelegentlichen Liquidationsgebühren der Kreditnehmer sorgen für verlässliche, transparente und sichere Erträge.", "earn.usd.lending-operations-popup.title": "Kreditgeschäfte", "earn.usd.market-making-operations-popup.text": "Sky USD erzielt zusätzliche Renditen durch die Teilnahme an dezen<PERSON><PERSON> Börsen (AMMs) wie Curve oder Uniswap. Durch die Bereitstellung von Liquidität – das Einbringen deiner Stablecoins in Pools, die den Krypto-Handel ermöglichen – erhält Sky USD Gebühren aus den Handelsaktivitäten. Diese Liquiditätspools werden sorgfältig ausgewählt, um die Volatilität zu minimieren, wobei hauptsächlich Stablecoin-zu-Stablecoin-Paare verwendet werden, um Risiken wie unbeständige Verluste (Impermanent Loss) deutlich zu reduzieren und dein Vermögen sicher und zugänglich zu halten.", "earn.usd.market-making-operations-popup.title": "Market-Making-Geschäfte", "earn.usd.treasury-operations-popup.text": "Sky USD erzielt stabile, beständige Renditen durch strategische Treasury-Anlagen. Ein Teil deiner Stablecoin-Einlagen wird in sichere, risikoarme reale Vermögenswerte investiert – hauptsächlich kurzfristige Staatsanleihen und hochsichere Kreditinstrumente. <PERSON>ser Ansatz, ähnlich dem traditionellen Bankwesen, gewährleistet eine vorhersehbare und verlässliche Rendite. Dein Vermögen bleibt sicher, liquide und wird transparent verwaltet.", "earn.usd.treasury-operations-popup.title": "Treasury-Geschäfte", "earn.view_earn.card_rechard_off": "Aus", "earn.view_earn.card_rechard_on": "An", "earn.view_earn.card_recharge": "Kartenaufladung", "earn.view_earn.total_balance_label": "<PERSON><PERSON><PERSON> von {percentage} pro Jahr", "earn.view_earn.total_earnings_label": "Gesamtertrag", "earn.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "earn.withdraw.amount_to_withdraw": "Abzuhebender Betrag", "earn.withdraw.enter_amount": "<PERSON><PERSON> e<PERSON>ben", "earn.withdraw.loading": "<PERSON><PERSON><PERSON>", "earn.withdraw.no_routes_found": "Keine Routen gefunden", "earn.withdraw.not_enough_balance": "<PERSON>do nicht ausreichend", "earn.withdraw.select-currency.title": "Token auswählen", "earn.withdraw.select_to_token": "Token auswählen", "earn.withdraw.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "earn.withdraw_form.title": "<PERSON> a<PERSON>ben", "earnings-view.earnings": "Gesamte Einnahmen", "edit-account-owners.add-owner.add-wallet": "<PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.add_wallet": "<PERSON><PERSON> hinzufügen", "edit-account-owners.add-owner.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.card-owners": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.external-wallet": "<PERSON><PERSON><PERSON>", "editBankRecipient.title": "Empfänger bearbeiten", "editNetwork.addCustomRPC": "Benutzerdefinierten RPC-Knoten hinzufügen", "editNetwork.cannot_verify.subtitle": "Der benutzerdefinierte RPC-Knoten antwortet nicht. Prüfe die URL und versuche es erneut.", "editNetwork.cannot_verify.title": "Wir können den RPC-Knoten nicht verifizieren", "editNetwork.cannot_verify.try_again": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editNetwork.customRPCNode": "Benutzerdefinierter RPC-Knoten", "editNetwork.defaultRPC": "Standard-RPC", "editNetwork.networkRPC": "Netzwerk-RPC", "editNetwork.rpc_url.cannot_be_empty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editNetwork.rpc_url.not_a_valid_https_url": "Muss eine gültige HTTP(S)-URL sein", "editNetwork.safetyWarning.subtitle": "Zeal kann bei benutzerdefinierten RPCs <PERSON>, Zuverlässigkeit und Sicherheit nicht garantieren. <PERSON><PERSON> du sicher, dass du einen benutzerdefinierten RPC-Knoten nutzen möchtest?", "editNetwork.safetyWarning.title": "Benutzerdefinierte RPCs können unsicher sein", "editNetwork.zealRPCNode": "Zeal RPC-Knoten", "editNetworkRpc.headerTitle": "Benutzerdefinierter RPC-Knoten", "editNetworkRpc.rpcNodeUrl": "RPC-Knoten-URL", "editing-locked.modal.description": "Anders als bei Approval-Transaktionen kannst du bei Permits das Ausgabelimit oder die Ablaufzeit nicht bearbeiten. Vertraue einer dApp, bevor du ein Permit absendest.", "editing-locked.modal.title": "Bearbeitung gesperrt", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Aufladen wird aktiviert", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Aufladen aktiviert", "enterCardnumber": "Kartennummer eingeben", "error.connectivity_error.subtitle": "Bitte überprüfe deine Internetverbindung und versuche es erneut.", "error.connectivity_error.title": "<PERSON>ine <PERSON>bindung", "error.decrypt_incorrect_password.title": "Falsches Passwort", "error.encrypted_object_invalid_format.title": "Beschädigte Daten", "error.failed_to_fetch_google_auth_token.title": "Wir konnten keinen Zugriff erhalten", "error.list.item.cta.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error.trezor_action_cancelled.title": "Transaktion abgelehnt", "error.trezor_device_used_elsewhere.title": "Ger<PERSON> wird in einer anderen Sitzung verwendet", "error.trezor_method_cancelled.title": "<PERSON><PERSON><PERSON> konnte nicht synchronisiert werden", "error.trezor_permissions_not_granted.title": "<PERSON><PERSON><PERSON> konnte nicht synchronisiert werden", "error.trezor_pin_cancelled.title": "<PERSON><PERSON><PERSON> konnte nicht synchronisiert werden", "error.trezor_popup_closed.title": "<PERSON><PERSON><PERSON> konnte nicht synchronisiert werden", "error.unblock_account_number_and_sort_code_mismatch": "Kontonummer und Bankleitzahl stimmen nicht überein", "error.unblock_can_not_change_details_after_kyc": "Daten können nach KYC nicht geändert werden", "error.unblock_hard_kyc_failure": "Unerwarteter KYC-Status", "error.unblock_invalid_faster_payment_configuration.title": "Diese Bank unterstützt keine Faster Payments", "error.unblock_invalid_iban": "Ungültige IBAN", "error.unblock_session_expired.title": "Unblock-Sitzung abgelaufen", "error.unblock_user_with_address_already_exists.title": "Konto für Adresse bereits eingerichtet", "error.unblock_user_with_such_email_already_exists.title": "Benutzer mit dieser E-Mail-Adresse existiert bereits", "error.unknown_error.error_message": "Fehlermeldung: ", "error.unknown_error.subtitle": "Entschuldigung! Wenn du dringend Hilf<PERSON>, kontaktiere bitte den Support und teile die folgenden Details mit.", "error.unknown_error.title": "<PERSON><PERSON><PERSON>", "eth-cost-warning-modal.subtitle": "Smart Wallets funktionieren auf Ethereum, aber die Gebühren sind sehr hoch. Wir empfehlen DRINGEND, stattdessen andere Netzwerke zu verwenden.", "eth-cost-warning-modal.title": "Ethereum meiden – hohe Netzwerkgebühren", "exchange.form.button.chain_unsupported": "Chain nicht unterstützt", "exchange.form.button.refreshing": "<PERSON><PERSON><PERSON> a<PERSON><PERSON>", "exchange.form.error.asset_not_supported.button": "Anderen As<PERSON> w<PERSON>hlen", "exchange.form.error.asset_not_supported.description": "Die Bridge unterstützt diesen Vermögenswert nicht.", "exchange.form.error.asset_not_supported.title": "Vermögenswert nicht unterstützt", "exchange.form.error.bridge_quote_timeout.button": "Anderen As<PERSON> w<PERSON>hlen", "exchange.form.error.bridge_quote_timeout.description": "<PERSON>ers<PERSON> ein andere<PERSON>-<PERSON>", "exchange.form.error.bridge_quote_timeout.title": "<PERSON><PERSON> gefunden", "exchange.form.error.different_receiver_not_supported.button": "Anderen Empfänger entfernen", "exchange.form.error.different_receiver_not_supported.description": "<PERSON><PERSON> Bö<PERSON> unterstützt das Senden an eine andere Adresse nicht.", "exchange.form.error.different_receiver_not_supported.title": "Sende- und Empfangsadresse müssen identisch sein", "exchange.form.error.insufficient_input_amount.button": "<PERSON><PERSON>", "exchange.form.error.insufficient_liquidity.button": "<PERSON><PERSON> verring<PERSON>", "exchange.form.error.insufficient_liquidity.description": "Die Bridge hat nicht genügend Vermögenswerte. Versuche einen kleineren Betrag.", "exchange.form.error.insufficient_liquidity.title": "<PERSON><PERSON> zu hoch", "exchange.form.error.max_amount_exceeded.button": "<PERSON><PERSON> verring<PERSON>", "exchange.form.error.max_amount_exceeded.description": "Der Höchstbetrag wurde überschritten.", "exchange.form.error.max_amount_exceeded.title": "<PERSON><PERSON> zu hoch", "exchange.form.error.min_amount_not_met.button": "<PERSON><PERSON>", "exchange.form.error.min_amount_not_met.description": "Der Mindesttauschbetrag für diesen Token wurde nicht erreicht.", "exchange.form.error.min_amount_not_met.description_with_amount": "Der Mindesttauschbetrag ist {amount}.", "exchange.form.error.min_amount_not_met.title": "<PERSON><PERSON> zu nied<PERSON>", "exchange.form.error.min_amount_not_met.title_increase": "<PERSON><PERSON>", "exchange.form.error.no_routes_found.button": "Anderen As<PERSON> w<PERSON>hlen", "exchange.form.error.no_routes_found.description": "<PERSON><PERSON><PERSON> diese Token-/Netzwerk-Kombination ist keine Tauschroute verfügbar.", "exchange.form.error.no_routes_found.title": "<PERSON><PERSON> ve<PERSON>", "exchange.form.error.not_enough_balance.button": "<PERSON><PERSON> verring<PERSON>", "exchange.form.error.not_enough_balance.description": "Du hast nicht genug von diesem Vermögenswert für die Transaktion.", "exchange.form.error.not_enough_balance.title": "<PERSON>do nicht ausreichend", "exchange.form.error.slippage_passed_is_too_low.button": "Slippage erhöhen", "exchange.form.error.slippage_passed_is_too_low.description": "Die erlaubte Slippage ist für diesen Vermögenswert zu niedrig.", "exchange.form.error.slippage_passed_is_too_low.title": "Slip<PERSON> zu niedrig", "exchange.form.error.socket_internal_error.button": "Später erneut versuchen", "exchange.form.error.socket_internal_error.description": "Der Bridge-Partner hat derzeit Probleme. Versuche es später erneut.", "exchange.form.error.socket_internal_error.title": "<PERSON><PERSON> beim Bridge-Partner", "exchange.form.error.stargatev2_requires_fee_in_native": "Hinzufügen {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Füge {amount} hinzu, um die Transaktion abzuschließen", "exchange.form.error.stargatev2_requires_fee_in_native.title": "Mehr {symbol}", "expiration-info.modal.description": "Die Ablaufzeit gibt an, wie lange eine App deine Token verwenden kann. Nach Ablauf der Zeit verliert sie den Zugriff, bis du ihn erneut erteilst. Halte die Ablaufzeit kurz, um sicher zu bleiben.", "expiration-info.modal.title": "Was ist die Ablaufzeit?", "expiration-time.high.modal.text": "Ablaufzeiten sollten kurz sein und sich danach richten, wie lange du sie wirklich brauchst. Lange Zeiten sind riskant und geben Betrügern mehr Chancen, deine To<PERSON> zu missbrauchen.", "expiration-time.high.modal.title": "Lange Ablaufzeit", "failed.transaction.content": "Transaktion schlägt wahrsche<PERSON>lich fehl", "fee.unknown": "Unbekannt", "feedback-request.leave-message": "Nachricht hinterlassen", "feedback-request.not-now": "<PERSON><PERSON>t nicht", "feedback-request.title": "Danke! Wie können wir Zeal verbessern?", "float.input.period": "Dezimaltrennzeichen", "gnosis-activate-card.info-popup.subtitle": "Für deine erste Transaktion musst du die Karte einstecken und deine PIN eingeben. Danach funktionieren kontaktlose Zahlungen.", "gnosis-activate-card.info-popup.title": "Erste Zahlung erfordert Chip & PIN", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "Gib deine Kartennummer ein, um sie zu aktivieren.", "gnosis-activate-card.title": "Kartennummer", "gnosis-pay-re-kyc-widget.btn-text": "Verifizieren", "gnosis-pay-re-kyc-widget.title.not-started": "Identität verifizieren", "gnosis-pay.login.cta": "Konto verbinden", "gnosis-pay.login.title": "Du hast bereits ein Gnosis Pay Konto", "gnosis-signup.confirm.subtitle": "E-Mail von Gnosis Pay im Spam-Ordner?", "gnosis-signup.confirm.title": "<PERSON><PERSON>igungs-E-Mail erhalten?", "gnosis-signup.continue": "<PERSON><PERSON>", "gnosis-signup.dont_link_accounts": "Konten nicht verknüpfen", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "<EMAIL> eingeben", "gnosis-signup.enter-email.title": "E-Mail eingeben", "gnosis-signup.title": "Ich stimme den <linkGnosisTNC>AGB von Gnosis Pay</linkGnosisTNC> <monovateTerms>Karteninhaberbedingungen</monovateTerms> und den <linkMonerium>AGB von Monerium</linkMonerium> zu.", "gnosis-signup.verify-email.title": "E-Mail bestätigen", "gnosis.confirm.subtitle": "Kein Code? Prüfe deine Telefonnummer.", "gnosis.confirm.title": "Code gesendet an {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Verifizieren", "gnosisPayAccountStatus.success.title": "Karte importiert", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay ist in deinem Land noch nicht verfügbar", "gnosisPayNoActiveCardsFound.title": "<PERSON><PERSON> aktive<PERSON>", "gnosis_pay_card_delay_relay_not_empty_error.title": "Deine Transaktion konnte jetzt nicht verarbeitet werden. Bitte versuche es später erneut.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "<PERSON>rte nicht möglich", "gnosiskyc.modal.approved.activate-free-card": "Gratis-Karte aktivieren", "gnosiskyc.modal.approved.button-text": "Vom Bankkonto einzahlen", "gnosiskyc.modal.approved.title": "<PERSON><PERSON> per<PERSON>önlichen Kontodaten wurden erstellt", "gnosiskyc.modal.failed.close": "Schließen", "gnosiskyc.modal.failed.title": "<PERSON><PERSON> kann unser Partner Gnosis Pay kein Konto für dich erstellen", "gnosiskyc.modal.in-progress.title": "Die ID-Verifizierung kann 24 Stunden oder länger dauern. Bitte habe etwas Geduld", "goToSettingsPopup.settings": "Einstell.", "goToSettingsPopup.title": "Benachrichtigungen jederzeit in deinen Geräteeinstellungen aktivieren", "google_file.error.failed_to_fetch_auth_token.button_title": "<PERSON><PERSON><PERSON> versuchen", "google_file.error.failed_to_fetch_auth_token.subtitle": "Um uns die Nutzung deiner Wiederherstellungsdatei zu ermöglichen, gewähre bitte Zugriff auf deine persönliche Cloud.", "google_file.error.failed_to_fetch_auth_token.title": "Wir konnten keinen Zugriff erhalten", "hidden_tokens.widget.emptyState": "<PERSON><PERSON> verst<PERSON>", "how_to_connect_to_metamask.got_it": "OK, verstanden", "how_to_connect_to_metamask.story.subtitle": "Wechsle jederzeit einfach zwischen Zeal und anderen Wallets.", "how_to_connect_to_metamask.story.title": "Zeal funktion<PERSON>t neben anderen Wallets", "how_to_connect_to_metamask.why_switch": "Warum zwischen Zeal und anderen Wallets wechseln?", "how_to_connect_to_metamask.why_switch.description": "<PERSON><PERSON>, welches Wallet du wählst, die Sicherheits-Checks von <PERSON> schützen dich immer vor bösartigen Websites und Transaktionen.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "Wir wissen, dass der Umstieg auf ein neues Wallet schwerfällt. Deshalb haben wir es einfach gemacht, Zeal neben deinem bestehenden Wallet zu nutzen. Wechsle jederzeit.", "import-bank-transfer-owner.banner.title": "Wallet geändert. Importieren, um Banktransfers zu nutzen.", "import-bank-transfer-owner.title": "Wallet importieren, um Banküberweisungen auf diesem Gerät zu nutzen", "import_gnosispay_wallet.add-another-card-owner.footnote": "Schlüssel für Gnosis Pay Karte importieren", "import_gnosispay_wallet.primaryText": "Gnosis Pay Wallet importieren", "injected-wallet": "Browser-<PERSON><PERSON>", "intercom.getHelp": "<PERSON><PERSON><PERSON> erhalten", "invalid_iban.got_it": "Verstanden", "invalid_iban.subtitle": "Die eingegebene IBAN ist ungültig. Bitte überprüfe die Angaben und versuche es erneut.", "invalid_iban.title": "Ungültige IBAN", "keypad-0": "Tastenfeld Taste 0", "keypad-1": "Tastenfeld Taste 1", "keypad-2": "Tastenfeld Taste 2", "keypad-3": "Tastenfeld Taste 3", "keypad-4": "Tastenfeld Taste 4", "keypad-5": "Tastenfeld Taste 5", "keypad-6": "Tastenfeld Taste 6", "keypad-7": "Tastenfeld Taste 7", "keypad-8": "Tastenfeld Taste 8", "keypad-9": "Tastenfeld Taste 9", "keypad.biometric-button": "Biometrie-Schaltfläche des Tastenfelds", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Geheime Phrase gesichert 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "Phrase an<PERSON>hen", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Bewahre eine sichere Offline-Kopie deiner geheimen Phrase auf, damit du dein G<PERSON>aben später wiederherstellen kannst.", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "<PERSON><PERSON><PERSON> nicht, das Wort zu erraten", "keystore.write_secret_phrase.before_you_begin.first_point": "<PERSON>ch verstehe, dass jeder mit meiner geheimen Phrase mein Guthaben überweisen kann.", "keystore.write_secret_phrase.before_you_begin.second_point": "<PERSON><PERSON> bin da<PERSON><PERSON><PERSON>, meine geheime Phrase geheim und sicher aufzubewahren.", "keystore.write_secret_phrase.before_you_begin.subtitle": "Bitte lies und akzeptiere die folgenden Punkte:", "keystore.write_secret_phrase.before_you_begin.third_point": "Ich befinde mich an einem privaten Ort ohne andere Personen oder Kameras in meiner Nähe.", "keystore.write_secret_phrase.before_you_begin.title": "<PERSON><PERSON>", "keystore.write_secret_phrase.secret_phrase_test.title": "<PERSON><PERSON> ist Wort {count} in deiner geheimen Phrase?", "keystore.write_secret_phrase.test_ps.lets_do_it": "<PERSON> geht's", "keystore.write_secret_phrase.test_ps.subtitle": "Du benötigst deine geheime Phrase, um dein Konto auf diesem oder anderen Geräten wiederherzustellen. Las<PERSON> uns testen, ob du deine geheime Phrase richtig notiert hast.", "keystore.write_secret_phrase.test_ps.subtitle2": "Wir werden dich nach {count} Wörtern deiner Phrase fragen.", "keystore.write_secret_phrase.test_ps.title": "Kontowiederherstellung testen", "kyc.modal.approved.button-text": "Banküberweisung tätigen", "kyc.modal.approved.subtitle": "Deine Verifizierung ist abgeschlossen. Du kannst jetzt unbegrenzt Banküberweisungen tätigen.", "kyc.modal.approved.title": "Banküberweisungen freigeschaltet", "kyc.modal.continue-with-partner.button-text": "<PERSON><PERSON>", "kyc.modal.continue-with-partner.subtitle": "Wir leiten dich nun an unseren Partner weiter, um deine Unterlagen zu erfassen und den Verifizierungsantrag abzuschließen.", "kyc.modal.continue-with-partner.title": "Bei unserem Partner fortfahren", "kyc.modal.failed.unblock.subtitle": "Unblock hat deine Identitätsprüfung nicht genehmigt und kann dir keine Banküberweisungen anbieten.", "kyc.modal.failed.unblock.title": "Unblock-<PERSON><PERSON><PERSON> nicht genehmigt", "kyc.modal.paused.button-text": "Daten aktualisieren", "kyc.modal.paused.subtitle": "Einige deiner Angaben scheinen falsch zu sein. Bitte versuche es erneut und überprüfe deine Daten vor dem Absenden.", "kyc.modal.paused.title": "<PERSON><PERSON> scheinen falsch zu sein", "kyc.modal.pending.button-text": "Schließen", "kyc.modal.pending.subtitle": "Die Verifizierung dauert normalerweise weniger als 10 Minuten, kann aber manchmal etwas länger dauern.", "kyc.modal.pending.title": "Wir halten dich auf dem Laufenden", "kyc.modal.required.cta": "Verifizierung starten", "kyc.modal.required.subtitle": "Du hast das Transaktionslimit erreicht. Bitte verifiziere deine Identität, um fortzufahren. Dies dauert nur wenige Minuten und erfordert einige persönliche Daten und Dokumente.", "kyc.modal.required.title": "Identitätsverifizierung erforderlich", "kyc.submitted": "<PERSON><PERSON><PERSON>", "kyc.submitted_short": "Eingereicht", "kyc_status.completed_status": "Abgeschlossen", "kyc_status.failed_status": "Fehlgeschlagen", "kyc_status.paused_status": "Überprüfung", "kyc_status.subtitle": "Banküberweisungen", "kyc_status.subtitle.wrong_details": "Falsche Angaben", "kyc_status.subtitle_in_progress": "In Bearbeitung", "kyc_status.title": "Identitätsprüfung", "label.close": "Schließen", "label.saving": "Speichern...", "labels.this-month": "<PERSON><PERSON>", "labels.today": "<PERSON><PERSON>", "labels.yesterday": "Gestern", "language.selector.title": "<PERSON><PERSON><PERSON>", "ledger.account_loaded.imported": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.add.success.title": "Ledger erfolgreich verbunden 🎉", "ledger.connect.cta": "Ledger synchronisieren", "ledger.connect.step1": "Ledger mit deinem Gerät verbinden", "ledger.connect.step2": "Öffne die Ethereum-App auf dem Ledger", "ledger.connect.step3": "Dann synchronisiere deinen Ledger 👇", "ledger.connect.subtitle": "<PERSON><PERSON><PERSON> diese Schritte, um deine Ledger-Wallets in Zeal zu importieren", "ledger.connect.title": "Ledger mit Zeal verbinden", "ledger.error.ledger_is_locked.subtitle": "Ledger entsperren und die Ethereum-App ö<PERSON>nen", "ledger.error.ledger_is_locked.title": "Ledger ist gesperrt", "ledger.error.ledger_not_connected.action": "Ledger synchronisieren", "ledger.error.ledger_not_connected.subtitle": "Verbinde dein Hardware-Wallet mit deinem Gerät und öffne die Ethereum-App.", "ledger.error.ledger_not_connected.title": "Ledger ist nicht verbunden", "ledger.error.ledger_running_non_eth_app.title": "Ethereum-A<PERSON> nicht geöffnet", "ledger.error.user_trx_denied_by_user.action": "Schließen", "ledger.error.user_trx_denied_by_user.subtitle": "Du hast die Transaktion auf deinem Hardware-Wallet abgelehnt.", "ledger.error.user_trx_denied_by_user.title": "Transaktion abgelehnt", "ledger.hd_path.bip44.subtitle": "z. B. <PERSON>, Trezor", "ledger.hd_path.bip44.title": "BIP44-Standard", "ledger.hd_path.ledger_live.subtitle": "Standard", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Legacy", "ledger.hd_path.phantom.subtitle": "z. B. Phantom", "ledger.select.hd_path.subtitle": "HD-Pfade sind die Methode, mit der Hardware-Wallets ihre Konten sortieren. Ähnlich wie ein Index Seiten in einem Buch ordnet.", "ledger.select.hd_path.title": "HD-Pfad auswählen", "ledger.select_account.import_wallets_count": "{count,plural,=0{Keine Wallets ausgewählt} one{Wallet importieren} other{{count} Wallets importieren}}", "ledger.select_account.path_settings": "Pfad-Einstellungen", "ledger.select_account.subtitle": "Siehst du die erwarteten Wallets nicht? Versuch, die Pfad-Einstellungen zu ändern", "ledger.select_account.subtitle.group_header": "Wallets", "ledger.select_account.title": "Ledger-Wallets importieren", "legend.lending-operations": "Kreditgeschäfte", "legend.market_making-operations": "Market-Making-Geschäfte", "legend.treasury-operations": "Treasury-Geschäfte", "link-existing-monerium-account-sign.button": "Zeal verknüpfen", "link-existing-monerium-account-sign.subtitle": "Du hast bereits ein Monerium-Konto.", "link-existing-monerium-account-sign.title": "Verknüpfe Zeal mit deinem Monerium-Konto.", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Du hast bereits ein Monerium-Konto. Öffne die Monerium-App, um die Einrichtung abzuschließen.", "link-existing-monerium-account.title": "<PERSON><PERSON><PERSON> zu Monerium, um dein Konto zu verknüpfen", "loading.pin": "PIN wird geladen ...", "lockScreen.passwordIncorrectMessage": "Passwort ist falsch", "lockScreen.passwordRequiredMessage": "Passwort er<PERSON>", "lockScreen.unlock.header": "Entsperren", "lockScreen.unlock.subheader": "Nutze dein Passwort, um Zeal zu entsperren", "mainTabs.activity.label": "Aktivität", "mainTabs.browse.label": "Entdecken", "mainTabs.browse.title": "Entdecken", "mainTabs.card.label": "<PERSON><PERSON>", "mainTabs.portfolio.label": "Portfolio", "mainTabs.rewards.label": "Prämien", "makeSpendable.cta": "Aus<PERSON><PERSON><PERSON> machen", "makeSpendable.holdAsCash": "<PERSON><PERSON> be<PERSON>en", "makeSpendable.shortText": "Verdiene {apy} pro Jahr", "makeSpendable.title": "{amount} erhalten", "merchantCategory.agriculture": "Landwirtschaft", "merchantCategory.alcohol": "Alkohol", "merchantCategory.antiques": "Antiquitäten", "merchantCategory.appliances": "Haushaltsgeräte", "merchantCategory.artGalleries": "Kunstgalerien", "merchantCategory.autoRepair": "Autoreparatur", "merchantCategory.autoRepairService": "Autoreparaturservice", "merchantCategory.beautyFitnessSpas": "Schönheit, Fitness & Spas", "merchantCategory.beautyPersonalCare": "Schönheit & Körperpflege", "merchantCategory.billiard": "<PERSON><PERSON>", "merchantCategory.books": "<PERSON><PERSON><PERSON>", "merchantCategory.bowling": "Bowling", "merchantCategory.businessProfessionalServices": "Geschäfts- & professionelle Dienstleistungen", "merchantCategory.carRental": "Autovermietung", "merchantCategory.carWash": "Autowäsche", "merchantCategory.cars": "Autos", "merchantCategory.casino": "Casino", "merchantCategory.casinoGambling": "Casino & Glücksspiel", "merchantCategory.cellular": "Mobilfunk", "merchantCategory.charity": "Wohltätigkeit", "merchantCategory.childcare": "Kinderbetreuung", "merchantCategory.cigarette": "Zigarette", "merchantCategory.cinema": "<PERSON><PERSON>", "merchantCategory.cinemaEvents": "Kino & Events", "merchantCategory.cleaning": "Reinigung", "merchantCategory.cleaningMaintenance": "Reinigung & Wartung", "merchantCategory.clothes": "Kleidung", "merchantCategory.clothingServices": "Bekleidungsdienste", "merchantCategory.communicationServices": "Kommunikationsdienste", "merchantCategory.construction": "Bau", "merchantCategory.cosmetics": "Kosmetik", "merchantCategory.craftsArtSupplies": "Bastel- & Künstlerbedarf", "merchantCategory.datingServices": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.delivery": "Lieferung", "merchantCategory.dentist": "Zahnarzt", "merchantCategory.departmentStores": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.directMarketingSubscription": "Direktmarketing & Abonnement", "merchantCategory.discountStores": "Discounter", "merchantCategory.drugs": "Medikamente", "merchantCategory.dutyFree": "Duty-free", "merchantCategory.education": "Bildung", "merchantCategory.electricity": "<PERSON><PERSON>", "merchantCategory.electronics": "Elektronik", "merchantCategory.emergencyServices": "Notdienste", "merchantCategory.equipmentRental": "Ausrüstungsverleih", "merchantCategory.evCharging": "<PERSON><PERSON> von E-Fahrzeugen", "merchantCategory.financialInstitutions": "Finanzinstitute", "merchantCategory.financialProfessionalServices": "Finanz- & professionelle <PERSON>", "merchantCategory.finesPenalties": "Bußgelder & Strafen", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "Flüge", "merchantCategory.flowers": "Blumen", "merchantCategory.flowersGarden": "Blumen & Garten", "merchantCategory.food": "Essen", "merchantCategory.freight": "Fracht", "merchantCategory.fuel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.funeralServices": "Bestattungsdienste", "merchantCategory.furniture": "<PERSON><PERSON><PERSON>", "merchantCategory.games": "<PERSON><PERSON><PERSON>", "merchantCategory.gas": "Benzin", "merchantCategory.generalMerchandiseRetail": "Allgemeine Handelswaren & Einzelhandel", "merchantCategory.gifts": "Geschenke", "merchantCategory.government": "Behörden", "merchantCategory.governmentServices": "Staatliche Dienstleistungen", "merchantCategory.hardware": "Baumarkt", "merchantCategory.healthMedicine": "Gesundheit & Medizin", "merchantCategory.homeImprovement": "Heimwerkerbedarf", "merchantCategory.homeServices": "Dienstleistungen für zu Hause", "merchantCategory.hotel": "Hotel", "merchantCategory.housing": "<PERSON><PERSON><PERSON>", "merchantCategory.insurance": "Versicherung", "merchantCategory.internet": "Internet", "merchantCategory.kids": "Kinder", "merchantCategory.laundry": "Wäscherei", "merchantCategory.laundryCleaningServices": "Wäscherei & Reinigungsdienste", "merchantCategory.legalGovernmentFees": "Rechts- & Verwaltungsgebühren", "merchantCategory.luxuries": "Luxusartikel", "merchantCategory.luxuriesCollectibles": "Luxusartikel & Sammlerstücke", "merchantCategory.magazines": "Zeitschriften", "merchantCategory.magazinesNews": "Zeitschriften & Nachrichten", "merchantCategory.marketplaces": "Marktplätze", "merchantCategory.media": "Medien", "merchantCategory.medicine": "Medizin", "merchantCategory.mobileHomes": "Wohnmobile", "merchantCategory.moneyTransferCrypto": "Geldtransfer & Krypto", "merchantCategory.musicRelated": "Mu<PERSON>", "merchantCategory.musicalInstruments": "Musikinstrumente", "merchantCategory.optics": "Optik", "merchantCategory.organizationsClubs": "Organisationen & Vereine", "merchantCategory.other": "Sonstiges", "merchantCategory.parking": "Parken", "merchantCategory.pawnShops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.pets": "Haustiere", "merchantCategory.photoServicesSupplies": "Fotodienste & -zubehör", "merchantCategory.postalServices": "Postdienste", "merchantCategory.professionalServicesOther": "Professionelle Dienstleistungen (Sonstige)", "merchantCategory.publicTransport": "Öffentlicher Nahverkehr", "merchantCategory.purchases": "Einkäufe", "merchantCategory.purchasesMiscServices": "Einkäufe & sonstige Dienstleistungen", "merchantCategory.recreationServices": "Freizeitdienstleistungen", "merchantCategory.religiousGoods": "Religiöse Artikel", "merchantCategory.secondhandRetail": "Secondhand-<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.shoeHatRepair": "Schuh- & Hutreparatur", "merchantCategory.shoeRepair": "Schuhreparatur", "merchantCategory.softwareApps": "Software & Apps", "merchantCategory.specializedRepairs": "Spezialisierte Reparaturen", "merchantCategory.sport": "Sport", "merchantCategory.sportingGoods": "Sportartikel", "merchantCategory.sportingGoodsRecreation": "Sportartikel & Freizeit", "merchantCategory.sportsClubsFields": "Sportvereine & -plätze", "merchantCategory.stationaryPrinting": "Schreibwaren & Druckereien", "merchantCategory.stationery": "Schr<PERSON>b<PERSON>n", "merchantCategory.storage": "Lagerung", "merchantCategory.taxes": "Steuern", "merchantCategory.taxi": "Taxi", "merchantCategory.telecomEquipment": "Telekommunikationsausrüstung", "merchantCategory.telephony": "Telefonie", "merchantCategory.tobacco": "Tabakwaren", "merchantCategory.tollRoad": "Mautstraße", "merchantCategory.tourismAttractionsAmusement": "Tourismus, Attraktionen & Unterhaltung", "merchantCategory.towing": "Abschleppdienste", "merchantCategory.toys": "Spielzeug", "merchantCategory.toysHobbies": "Spielzeug & Hobbys", "merchantCategory.trafficFine": "<PERSON><PERSON><PERSON>gel<PERSON>", "merchantCategory.train": "<PERSON>ug", "merchantCategory.travelAgency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.tv": "Fernseh<PERSON>", "merchantCategory.tvRadioStreaming": "TV, Radio & Streaming", "merchantCategory.utilities": "Versorgungsunternehmen", "merchantCategory.waterTransport": "Wassertransport", "merchantCategory.wholesaleClubs": "Großhandelsclubs", "metaMask.subtitle": "Aktiviere den MetaMask-Modus, um alle MetaMask-Verbindungen zu Zeal umzuleiten. Ein Klick auf MetaMask in dApps stellt dann eine Verbindung zu Zeal her.", "metaMask.title": "Verbindung mit Zeal nicht möglich?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Öffne deine Banking-App", "monerium-bank-deposit.buttet-point.receive-crypto": "Erhalte digitale EUR", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Sende {fiatCurrencyCode} auf dein Konto", "monerium-bank-deposit.deposit-account-country": "Land", "monerium-bank-deposit.header": "{fullName}s per<PERSON><PERSON><PERSON><PERSON><PERSON>", "monerium-bank-details.account-name": "<PERSON><PERSON><PERSON>", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT kopiert", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT kopiert", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN kopiert", "monerium-bank-details.to-wallet": "An Wallet", "monerium-bank-details.transfer-fee": "Überweisungsgebühr", "monerium-bank-transfer.enable-card.bullet-1": "Identitätsprüfung abschließen", "monerium-bank-transfer.enable-card.bullet-2": "Persönliche Kontodaten erhalten", "monerium-bank-transfer.enable-card.bullet-3": "Vom Bankkonto einzahlen", "monerium-card-delay-relay.success.cta": "Schließen", "monerium-card-delay-relay.success.subtitle": "Aus Sicherheitsgründen dauert die Bearbeitung von Karten-Einstellungen 3 Minuten.", "monerium-card-delay-relay.success.title": "In 3 Min. mit Monerium-<PERSON><PERSON> fortfahren", "monerium-deposit.account-details-info-popup.bullet-point-1": "Jeder {fiatCurrencyCode} , den du an dieses Konto sendest, wird automatisch in {cryptoCurrencyCode} -Token in der {cryptoCurrencyChain} -Chain umgewandelt und an deine Wallet gesendet", "monerium-deposit.account-details-info-popup.bullet-point-2": "SENDE NUR {fiatCurrencyCode} ({fiatCurrencySymbol}) an dein Konto", "monerium-deposit.account-details-info-popup.title": "<PERSON><PERSON>", "monerium.check_order_status.sending": "Wird gesendet", "monerium.not-eligible.cta": "Zurück", "monerium.not-eligible.subtitle": "Monerium kann kein Konto für dich eröffnen. Bitte wähle einen anderen Anbieter.", "monerium.not-eligible.title": "Anderen Anbieter wählen", "monerium.setup-card.cancel": "Abbrechen", "monerium.setup-card.continue": "<PERSON><PERSON>", "monerium.setup-card.create_account": "<PERSON><PERSON> er<PERSON>", "monerium.setup-card.login": "Bei Gnosis Pay anmelden", "monerium.setup-card.subtitle": "<PERSON><PERSON><PERSON> ein Gnosis Pay-Konto oder melde dich an, um sofortige Bankeinzahlungen zu aktivieren.", "monerium.setup-card.subtitle_personal_account": "Erhalte dein persönliches Konto bei Gnosis Pay in wenigen Minuten:", "monerium.setup-card.title": "Bankeinzahlungen aktivieren", "moneriumDepositSuccess.goToWallet": "Zum Wallet", "moneriumDepositSuccess.title": "{symbol} erhalten", "moneriumInfo.fees": "Du zahlst 0 % Gebühren", "moneriumInfo.registration": "Monerium ist als E-Geld-Institut gemäß dem isländischen E-Geld-Gesetz Nr. 17/2013 zugelassen und reguliert. <link>Mehr erfahren</link>", "moneriumInfo.selfCustody": "Das digitale Bargeld, das du erh<PERSON>t, ist selbstverwahrt, und niemand sonst hat die Kontrolle über dein Guthaben", "moneriumWithdrawRejected.supportText": "Wir konnten deine Überweisung nicht abschließen. Bitte versuche es erneut. Wenn es weiterhin nicht funktioniert, dann <link>kontaktiere den Support.</link>", "moneriumWithdrawRejected.title": "Überweisung rückgängig gemacht", "moneriumWithdrawRejected.tryAgain": "<PERSON><PERSON><PERSON> versuchen", "moneriumWithdrawSuccess.supportText": "Es kann bis zu 24 Stunden dauern, bis dein{br}<PERSON><PERSON><PERSON><PERSON> das Geld erhält", "moneriumWithdrawSuccess.title": "Gesendet", "monerium_enable_banner.text": "Banküberweisungen jetzt aktivieren", "monerium_error_address_re_link_required.title": "<PERSON>et muss erneut mit Monerium verknüpft werden", "monerium_error_duplicate_order.title": "Doppelte Order", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "Schweizer Franken", "mt-pelerin-fork.options.chf.short": "Sofort & kostenlos mit Mt Pelerin", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Sofort & kostenlos mit Monerium", "mt-pelerin-fork.title": "Was möchtest du ein<PERSON>hlen?", "mtPelerinProviderInfo.fees": "Du zahlst 0 % Gebühren", "mtPelerinProviderInfo.registration": "Die Mt Pelerin Group Ltd ist der SO-FIT angeschlossen, einer Selbstregulierungsorganisation, die von der Eidgenössischen Finanzmarktaufsicht (FINMA) gemäss dem Geldwäschereigesetz anerkannt ist. <link>Mehr erfahren</link>", "mtPelerinProviderInfo.selfCustody": "Das digitale Geld, das du erh<PERSON>t, verwahrst du selbst und niemand sonst hat die Kontrolle über deine Vermögenswerte.", "network-fee-widget.title": "Gebühren", "network.edit.verifying_rpc": "RPC wird überprüft", "network.editRpc.predefined_network_info.subtitle": "<PERSON><PERSON><PERSON> wie ein VPN verwendet Zeal RPCs, die das Tracking deiner persönlichen Daten verhindern.{br}{br}Die Standard-RPCs von Z<PERSON> sind zuverlässige, praxiserprobte RPC-Anbieter.", "network.editRpc.predefined_network_info.title": "Zeal-Datenschutz-RPC", "network.filter.update_rpc_success": "RPC-K<PERSON>n g<PERSON>", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "Alle Netzwerke", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "Netzwerke hinzufügen", "networks.filter.add_modal.chain_list.subtitle": "Beliebige EVM-Netzwerke hinzufügen", "networks.filter.add_modal.chain_list.title": "Zu Chainlist gehen", "networks.filter.add_modal.dapp_tip.subtitle": "<PERSON><PERSON>le in deinen bevorzugten dApps einfach zu dem EVM-Netzwerk, das du nutzen möchtest, und Zeal wird dich fragen, ob du es zu deinem Wallet hinzufügen willst.", "networks.filter.add_modal.dapp_tip.title": "Oder füge ein Netzwerk von jeder dApp hinzu", "networks.filter.add_networks.subtitle": "Alle EVM-Netzwerke werden unterstützt", "networks.filter.add_networks.title": "Netzwerke hinzufügen", "networks.filter.add_test_networks.title": "Testnetze hinzufügen", "networks.filter.tab.netwokrs": "Netzwerke", "networks.filter.testnets.title": "Testnetze", "nft.widget.emptystate": "<PERSON><PERSON> Sammlerstücke in der Wallet", "nft_collection.change_account_picture.subtitle": "B<PERSON> du sicher, dass du dein Profilbild aktualisieren möchtest?", "nft_collection.change_account_picture.title": "Profilbild auf NFT aktualisieren", "nfts.allNfts.pricingPopup.description": "Die Preise für Sammlerstücke basieren auf dem zuletzt gehandelten Preis.", "nfts.allNfts.pricingPopup.title": "Preise für Sammlerstücke", "no-passkeys-found.modal.cta": "Schließen", "no-passkeys-found.modal.subtitle": "Wir können keine Z<PERSON>-Passkeys auf diesem Gerät finden. <PERSON><PERSON> sic<PERSON>, dass du mit dem Cloud-Konto angemeldet bist, mit dem du dein Smart Wallet erstellt hast.", "no-passkeys-found.modal.title": "<PERSON><PERSON>keys gefunden", "notValidEmail.title": "<PERSON><PERSON> gültige E-Mail-Adresse", "notValidPhone.title": "Dies ist keine gültige Telefonnummer", "notification-settings.title": "Benachrichtigungseinstellungen", "notification-settings.toggles.active-wallets": "Aktive Wallets", "notification-settings.toggles.bank-transfers": "Banküberweisungen", "notification-settings.toggles.card-payments": "Kartenzahlungen", "notification-settings.toggles.readonly-wallets": "Wallets mit Leseberechtigung", "ntft.groupHeader.text": "Sammlerstücke", "on_ramp.crypto_completed": "Abgeschlossen", "on_ramp.fiat_completed": "Abgeschlossen", "onboarding-widget.subtitle.card_created_from_order.left": "Visa-Karte", "onboarding-widget.subtitle.card_created_from_order.right": "Karte aktivieren", "onboarding-widget.subtitle.card_order_ready.left": "Physische Visa-Karte", "onboarding-widget.subtitle.default": "Banküberweisungen & Visa-Karte", "onboarding-widget.title.card-order-in-progress": "Kartenbestellung fortsetzen", "onboarding-widget.title.card_created_from_order": "<PERSON>rte wurde versandt", "onboarding-widget.title.kyc_approved": "Überweisungen & Karte bereit", "onboarding-widget.title.kyc_failed": "Konto nicht möglich", "onboarding-widget.title.kyc_not_started": "Einrichtung fortsetzen", "onboarding-widget.title.kyc_started_documents_requested": "Verifizierung abschließen", "onboarding-widget.title.kyc_started_resubmission_requested": "Verifizierung wiederholen", "onboarding-widget.title.kyc_started_verification_in_progress": "Identität wird verifiziert", "onboarding.loginOrCreateAccount.amountOfAssets": "Vermögen von über 10 Mrd. USD", "onboarding.loginOrCreateAccount.cards.subtitle": "Nur in bestimmten Regionen verfügbar. Indem du fortfährst, akzeptierst du unsere <Terms>AGB</Terms> & <PrivacyPolicy>Datenschutzrichtlinie</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Visa-Karte mit hohen {br}Renditen und ohne Gebühren", "onboarding.loginOrCreateAccount.createAccount": "<PERSON><PERSON> er<PERSON>", "onboarding.loginOrCreateAccount.earn.subtitle": "Renditen variieren; Kapital ist Risiken ausgesetzt. Indem du fortfährst, akzeptierst du unsere <Terms>AGB</Terms> & <PrivacyPolicy>Datenschutzrichtlinie</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> von {percent} pro Jahr{br}<PERSON><PERSON><PERSON> von {currencySymbol}über 5 Mrd.", "onboarding.loginOrCreateAccount.earningPerYear": "<PERSON><PERSON><PERSON><PERSON><PERSON> von {percent}{br} pro Jahr", "onboarding.loginOrCreateAccount.login": "Anmelden", "onboarding.loginOrCreateAccount.trading.subtitle": "Kapital ist Risiken ausgesetzt. Indem du fortfährst, akzeptierst du unsere <Terms>AGB</Terms> & <PrivacyPolicy>Datenschutzrichtlinie</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Investiere in alles, {br}von BTC bis S&P", "onboarding.loginOrCreateAccount.trustedBy": "Digitale Geldmärkte{br}<PERSON><PERSON><PERSON> {assets}", "onboarding.wallet_stories.close": "Schließen", "onboarding.wallet_stories.previous": "Zurück", "order-earn-deposit-bridge.deposit": "Einzahlung", "order-earn-deposit-bridge.into": "In", "otpIncorrectMessage": "Bestätigungscode ist falsch", "passkey-creation-not-possible.modal.close": "Schließen", "passkey-creation-not-possible.modal.subtitle": "Wir konnten keinen Passkey für dein Wallet erstellen. <PERSON>te stelle sicher, dass dein Gerät Passkeys unterstützt, und versuche es erneut. <link>Frage den Support</link> , wenn das Problem weiterhin besteht.", "passkey-creation-not-possible.modal.title": "Passkey konnte nicht erstellt werden", "passkey-not-supported-in-mobile-browser.modal.cta": "Zeal herunterladen", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Smart Wallets werden in mobilen Browsern nicht unterstützt.", "passkey-not-supported-in-mobile-browser.modal.title": "Lade die Zeal-A<PERSON> herunter, um fortzufahren", "passkey-recovery.recovering.deploy-signer.loading-text": "Passkey wird verifiziert", "passkey-recovery.recovering.loading-text": "Wallet wird wiederhergestellt", "passkey-recovery.recovering.signer-not-found.subtitle": "Wir konnten deinen Passkey nicht mit einem aktiven Wallet verknüpfen. Wenn du Guthaben in deinem Wallet hast, wende dich an den Zeal-Support.", "passkey-recovery.recovering.signer-not-found.title": "<PERSON><PERSON> gefunden", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "Anderen Passkey versuchen", "passkey-recovery.select-passkey.banner.subtitle": "Prüfe dein Gerätekonto. Passkeys sind kontospezifisch.", "passkey-recovery.select-passkey.banner.title": "Siehst du den Passkey deines Wallets nicht?", "passkey-recovery.select-passkey.continue": "Passkey auswählen", "passkey-recovery.select-passkey.subtitle": "<PERSON><PERSON><PERSON><PERSON> den Passkey aus, der mit deinem Wallet verknüpft ist, um wieder Zugriff zu erhalten.", "passkey-recovery.select-passkey.title": "Passkey auswählen", "passkey-story_1.subtitle": "Mit einer Smart Wallet kannst du Netzwerkgebühren in den meisten Token bezahlen und musst dir keine Sorgen um die richtige Währung dafür machen.", "passkey-story_1.title": "Netzwerkgebühren? Zahl sie einfach in den meisten Token", "passkey-story_2.subtitle": "Basiert auf den branchenführenden Smart Contracts von Safe, die mehr als 100 Milliarden US-Dollar in über 20 Millionen Wallets sichern.", "passkey-story_2.title": "<PERSON><PERSON><PERSON><PERSON> durch <PERSON>", "passkey-story_3.subtitle": "Smart Wallets funktionieren in den großen Ethereum-kompatiblen Netzwerken. Prüfe die unterstützten Netzwerke, bevor du Vermögenswerte sendest.", "passkey-story_3.title": "Große EVM-Netzwerke unterstützt", "password.add.header": "Passwort erstellen", "password.add.includeLowerAndUppercase": "Klein- und Großbuchstaben", "password.add.includesNumberOrSpecialChar": "Eine Zahl oder ein Symbol", "password.add.info.subtitle": "Wir senden dein Passwort nicht an unsere Server und sichern es nicht für dich", "password.add.info.t_and_c": "Indem du fortfährst, akzeptierst du unsere <Terms>AGB</Terms> & <PrivacyPolicy>Datenschutzrichtlinie</PrivacyPolicy>", "password.add.info.title": "Dein Passwort bleibt auf diesem Gerät", "password.add.inputPlaceholder": "Passwort erstellen", "password.add.shouldContainsMinCharsCheck": "10+ <PERSON><PERSON><PERSON>", "password.add.subheader": "Du wirst dein Passwort verwenden, um Zeal zu entsperren", "password.add.success.title": "Passwort erstellt 🔥", "password.confirm.header": "Passwort bestätigen", "password.confirm.passwordDidNotMatch": "Passwörter müssen übereinstimmen", "password.confirm.subheader": "Gib dein Passwort noch einmal ein", "password.create_pin.subtitle": "Dieser Code sperrt die Zeal-App", "password.create_pin.title": "<PERSON><PERSON><PERSON> dei<PERSON>", "password.enter_pin.title": "Code eingeben", "password.incorrectPin": "Falscher Code", "password.pin_is_not_same": "Code stimmt nicht überein", "password.placeholder.enter": "Passwort eingeben", "password.placeholder.reenter": "Passwort erneut eingeben", "password.re_enter_pin.subtitle": "Gib denselben Code erneut ein", "password.re_enter_pin.title": "Code bestätigen", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "<PERSON><PERSON><PERSON><PERSON>", "pending-send.details.pending": "<PERSON><PERSON><PERSON><PERSON>", "pending-send.details.processing": "In Bearbeitung", "permit-info.modal.description": "Permits sind Anfragen, die nach deiner Signatur Apps erlauben, deine To<PERSON> zu bewegen, z. B. für einen Swap.{br}Permits <PERSON>hneln Freigaben, aber ihre Signatur kostet dich keine Netzwerkgebühren.", "permit-info.modal.title": "Was sind Permits?", "permit.edit-expiration": "Bearbeite {currency} Ablaufzeit", "permit.edit-limit": "Bearbeite {currency} Ausgabenlimit", "permit.edit-modal.expiresIn": "Lä<PERSON>t ab in…", "permit.expiration-warning": "{currency} Ablaufzeit-Warnung", "permit.expiration.info": "{currency} Ablaufinformationen", "permit.expiration.never": "<PERSON><PERSON>", "permit.spend-limit.info": "{currency} Ausgabenlimit-Info", "permit.spend-limit.warning": "{currency} Ausgabenlimit-Warnung", "phoneNumber.title": "Telefonnummer", "physicalCardOrderFlow.cardOrdered": "<PERSON><PERSON> best<PERSON>", "physicalCardOrderFlow.city": "Stadt", "physicalCardOrderFlow.orderCard": "<PERSON><PERSON> bestellen", "physicalCardOrderFlow.postcode": "<PERSON><PERSON><PERSON><PERSON>", "physicalCardOrderFlow.shippingAddress.subtitle": "<PERSON><PERSON><PERSON> wird deine <PERSON> geschi<PERSON>t", "physicalCardOrderFlow.shippingAddress.title": "Lieferadresse", "physicalCardOrderFlow.street": "Straße", "placeholderDapps.1inch.description": "Tausche über die besten Routen", "placeholderDapps.aave.description": "Token verleihen und leihen", "placeholderDapps.bungee.description": "Netzwerke über die besten Routen bridgen", "placeholderDapps.compound.description": "Token verleihen und leihen", "placeholderDapps.cowswap.description": "<PERSON><PERSON> zu besten Raten auf Gnosis", "placeholderDapps.gnosis-pay.description": "Verwalte deine Gnosis Pay Card", "placeholderDapps.jumper.description": "Netzwerke über die besten Routen bridgen", "placeholderDapps.lido.description": "Stake ETH für mehr ETH", "placeholderDapps.monerium.description": "E-Geld und Banküberweisungen", "placeholderDapps.odos.description": "Tausche über die besten Routen", "placeholderDapps.stargate.description": "Bridge oder stake für <14% APY", "placeholderDapps.uniswap.description": "Eine der beliebtesten Börsen", "pleaseAllowNotifications.cardPayments": "Kartenzahlungen", "pleaseAllowNotifications.customiseInSettings": "In den Einstellungen anpassen", "pleaseAllowNotifications.enable": "Aktivieren", "pleaseAllowNotifications.forWalletActivity": "F<PERSON>r <PERSON>-Aktivitäten", "pleaseAllowNotifications.title": "Wallet-Benachrichtigungen erhalten", "pleaseAllowNotifications.whenReceivingAssets": "<PERSON><PERSON> <PERSON><PERSON>", "portfolio.quick-actions.add_funds": "Aufladen", "portfolio.quick-actions.buy": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.deposit": "Ein<PERSON><PERSON><PERSON>", "portfolio.quick-actions.send": "Senden", "portfolio.view.lastRefreshed": "Aktualisiert {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Lade dein Testnetz-AVAX auf", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Zum Faucet gehen", "portfolio.view.topupTestNet.BscTestnet.primary": "Lade dein Testnetz-BNB auf", "portfolio.view.topupTestNet.BscTestnet.secondary": "Zum Faucet gehen", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Lade dein Testnetz-SepETH auf", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Zum Sepolia Faucet gehen", "portfolio.view.topupTestNet.FantomTestnet.primary": "Lade dein Testnetz-FTM auf", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Zum Faucet gehen", "privateKeyConfirmation.banner.subtitle": "<PERSON>er mit deinem privaten Schlüssel hat Zugriff auf deine Wallet. Nur Betrüger fragen danach.", "privateKeyConfirmation.banner.title": "Ich verstehe die Risiken", "privateKeyConfirmation.title": "TEILE DEINEN PRIVATEN SCHLÜSSEL NIEMALS MIT ANDEREN", "rating-request.not-now": "<PERSON><PERSON>t nicht", "rating-request.title": "<PERSON><PERSON><PERSON><PERSON> du Zeal weiterempfehlen?", "receive_funds.address-text": "Das ist deine einzigartige Wallet-Adresse. Du kannst sie sicher mit anderen teilen.", "receive_funds.copy_address": "Wallet-<PERSON><PERSON><PERSON> k<PERSON>", "receive_funds.network-warning.eoa.subtitle": "<link>Standard-Netzwerkliste ansehen</link>. Vermögenswerte, die über nicht-EVM-Netzwerke gesendet werden, gehen verloren.", "receive_funds.network-warning.eoa.title": "Alle Ethereum-basierten Netzwerke werden unterstützt", "receive_funds.network-warning.scw.subtitle": "<link>Unterstützte Netzwerke ansehen</link>. Vermögenswerte, die über andere Netzwerke gesendet werden, gehen verloren.", "receive_funds.network-warning.scw.title": "Wichtig: <PERSON><PERSON> unterstützte Netzwerke verwenden", "receive_funds.scan_qr_code": "QR-Code scannen", "receiving.in.days": "Erhalt in {days} T", "receiving.this.week": "<PERSON><PERSON><PERSON> diese Woche", "receiving.today": "<PERSON><PERSON><PERSON> heute", "reference.error.maximum_number_of_characters_exceeded": "<PERSON>u viele Zeichen", "referral-code.placeholder": "Einladungslink einfügen", "referral-code.subtitle": "<PERSON>licke erneut auf den Link deines Freundes oder füge ihn unten ein. Wir wollen sicherstellen, dass du deine Prämien erhältst.", "referral-code.title": "Hat ein Freund dir {bReward} geschickt?", "rekyc.verification_deadline.subtitle": "Verifiziere dich in {daysUntil} Tagen, um deine Karte weiter zu nutzen.", "rekyc.verification_required.subtitle": "<PERSON><PERSON><PERSON>ng verifizieren.", "reminder.fund": "💸 Guthaben hinz<PERSON>gen — sofort 6 % verdienen", "reminder.onboarding": "🏁 Einrichtung abschließen — 6 % auf deine Einlagen verdienen", "remove-owner.confirmation.subtitle": "Sicherheits-Update: <PERSON><PERSON> 3 <PERSON><PERSON> g<PERSON>", "remove-owner.confirmation.title": "<PERSON>rte wird für 3 Min. gesperrt", "restore-smart-wallet.wallet-recovered": "Wallet wiederhergestellt", "rewardClaimCelebration.claimedTitle": "Prämien bereits angefordert", "rewardClaimCelebration.subtitle": "<PERSON><PERSON><PERSON> das Einladen von Freunden", "rewardClaimCelebration.title": "Du hast verdient", "rewards-warning.subtitle": "Wenn du dieses Konto entfernst, wird der Zugriff auf verknüpfte Prämien pausiert. Du kannst das Konto jederzeit wiederherstellen, um sie einzufordern.", "rewards-warning.title": "Du verlierst den Zugriff auf deine Prämien", "rewards.copiedInviteLink": "Einladungslink kopiert", "rewards.createAccount": "Einladungslink kopieren", "rewards.header.subtitle": "Wir senden {aR<PERSON>ard} an dich und {bReward} an deinen <PERSON>nd, wenn er {bSpendLimitReward} ausgibt.", "rewards.header.title": "<PERSON><PERSON><PERSON><PERSON> {amountA}{br}Gib {amountB}", "rewards.sendInvite": "Einladung senden", "rewards.sendInviteTip": "W<PERSON>hle einen Freund aus und wir geben ihm {bAmount}", "route.fees": "Gebühren {fees}", "routesNotFound.description": "Die Tauschroute für die {from}-{to} Netzwerk-Kombination ist nicht verfügbar.", "routesNotFound.title": "<PERSON>ine Tausch<PERSON>e verfügbar", "rpc.OrderBuySignMessage.subtitle": "Über Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Über Swaps.IO", "rpc.addCustomNetwork.addNetwork": "Hinzufügen", "rpc.addCustomNetwork.chainId": "Chain ID", "rpc.addCustomNetwork.nativeToken": "Nativer <PERSON>", "rpc.addCustomNetwork.networkName": "Netzwerkname", "rpc.addCustomNetwork.operationDescription": "Erl<PERSON><PERSON> dieser Website, ein Netzwerk zu deiner Wallet hinzuzufügen. Zeal kann die Sicherheit von benutzerdefinierten Netzwerken nicht überprüfen. <PERSON><PERSON> sic<PERSON>, dass du die Risiken verstehst.", "rpc.addCustomNetwork.rpcUrl": "RPC-URL", "rpc.addCustomNetwork.subtitle": "<PERSON><PERSON> {name}", "rpc.addCustomNetwork.title": "Netzwerk hinzufügen", "rpc.send_token.network_not_supported.subtitle": "Wir arbeiten daran, Transaktionen in diesem Netzwerk zu ermöglichen. Danke für deine Geduld 🙏", "rpc.send_token.network_not_supported.title": "Netzwerk bald verfügbar", "rpc.send_token.send_or_receive.settings": "Einstellungen", "rpc.sign.accept": "Akzeptieren", "rpc.sign.cannot_parse_message.body": "Wir konnten diese Nachricht nicht entschlüsseln. Akzeptiere diese Anfrage nur, wenn du dieser App vertraust.{br}{br}Nachrichten können verwendet werden, um dich bei einer A<PERSON> anzumelden, können Apps aber auch die Kontrolle über deine Token geben.", "rpc.sign.cannot_parse_message.header": "Mit Vorsicht fortfahren", "rpc.sign.import_private_key": "Schlüssel importieren", "rpc.sign.subtitle": "<PERSON><PERSON><PERSON> {name}", "rpc.sign.title": "Signieren", "safe-creation.success.title": "<PERSON><PERSON> erste<PERSON>t", "safe-safety-checks-popup.title": "Sicherheitsprüfungen der Transaktion", "safetyChecksPopup.title": "Sicherheitsprüfungen der Seite", "scan_qr_code.description": "Wallet-QR scannen oder mit einer App verbinden", "scan_qr_code.show_qr_code": "Meinen QR-Code anzeigen", "scan_qr_code.tryAgain": "<PERSON><PERSON><PERSON> versuchen", "scan_qr_code.unlockCamera": "<PERSON><PERSON><PERSON> f<PERSON>", "screen-lock-missing.modal.close": "Schließen", "screen-lock-missing.modal.subtitle": "Dein Gerät benötigt eine Bildschirmsperre, um Passkeys zu verwenden. Bitte richte eine Bildschirmsperre ein und versuche es erneut.", "screen-lock-missing.modal.title": "Bildschirmsperre fehlt", "seedConfirmation.banner.subtitle": "<PERSON><PERSON> mit deiner geheimen Phrase hat Zugriff auf deine Wallet. Nur Betrüger fragen danach.", "seedConfirmation.title": "TEILE DEINE GEHEIME PHRASE NIEMALS MIT ANDEREN", "select-active-owner.subtitle": "Wähle Wallet für Zeal (wechselbar).", "select-active-owner.title": "<PERSON>et auswählen", "select-card.title": "<PERSON><PERSON> auswählen", "select-crypto-currency-title": "Token auswählen", "select-token.title": "Token auswählen", "selectEarnAccount.chf.description.steps": "· Geld rund um die Uhr abheben, keine Sperrfristen {br}· Zinsgutschrift im Sekundentakt {br}· Überbesicherte Einlagen bei <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} pro Jahr in CHF", "selectEarnAccount.eur.description.steps": "· Geld rund um die Uhr abheben, keine Sperrfristen {br}· Zinsen fallen sekundengenau an {br}· Überbesicherte Kredite mit <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} pro Jahr in EUR", "selectEarnAccount.subtitle": "Du kannst es jederzeit ändern", "selectEarnAccount.title": "Währung auswählen", "selectEarnAccount.usd.description.steps": "· Geld rund um die Uhr abheben, keine Sperrfristen {br}· Zinsen fallen sekundengenau an {br}· Überbesicherte Einlagen in <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} pro Jahr in USD", "selectEarnAccount.zero.description_general": "Digitales Geld halten, ohne Zinsen zu verdienen", "selectEarnAccount.zero.title": "0 % pro Jahr", "selectRechargeThreshold.button.enterAmount": "<PERSON><PERSON> e<PERSON>ben", "selectRechargeThreshold.button.setTo": "Festlegen auf {amount}", "selectRechargeThreshold.description.line1": "<PERSON>n deine Ka<PERSON> unter {amount} f<PERSON><PERSON><PERSON>, wird sie automatisch wieder auf {amount} von deinem Earn-Ko<PERSON> aufgeladen.", "selectRechargeThreshold.description.line2": "Ein niedrigeres Ziel hält mehr in deinem Earn-Konto (verdient 3 %). <PERSON> kannst dies jederzeit ändern.", "selectRechargeThreshold.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> festlegen", "select_currency_to_withdraw.select_token_to_withdraw": "Token zur Abhebung auswählen", "send-card-token.form.send": "Senden", "send-card-token.form.send-amount": "Aufladebetrag", "send-card-token.form.title": "Geld zur Karte hinzufügen", "send-card-token.form.to-address": "<PERSON><PERSON>", "send-safe-transaction.network-fee-widget.error": "<PERSON>tigst {amount} oder wähle einen anderen <PERSON>ken", "send-safe-transaction.network-fee-widget.no-fee": "<PERSON><PERSON>", "send-safe-transaction.network-fee-widget.title": "Gebühren", "send-safe-transaction.network_fee_widget.title": "Netzwerkgebühr", "send.banner.fees": "<PERSON> benötigst {amount} mehr {currency} für die Gebühren", "send.banner.toAddressNotSupportedNetwork.subtitle": "Die Wallet des Empfängers unterstützt {network} nicht. We<PERSON>le zu einem unterstützten Token.", "send.banner.toAddressNotSupportedNetwork.title": "Netzwerk vom Empfänger nicht unterstützt", "send.banner.walletNotSupportedNetwork.subtitle": "Smart Wallets tätigen keine Transaktionen auf {network}. <PERSON><PERSON>le zu einem unterstützten Token.", "send.banner.walletNotSupportedNetwork.title": "Token-Netzwerk nicht unterstützt", "send.empty-portfolio.empty-state": "<PERSON><PERSON> gefunden", "send.empty-portfolio.header": "Token", "send.titile": "Senden", "sendLimit.success.subtitle": "<PERSON><PERSON> Tages<PERSON>it wird in 3 Minuten aktualisiert. Bis dahin gilt dein aktuelles Limit.", "sendLimit.success.title": "Diese Änderung dauert 3 Minuten", "send_crypto.form.disconnected.cta.addFunds": "<PERSON><PERSON> e<PERSON>en", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "<PERSON><PERSON><PERSON><PERSON> zu {network}", "send_crypto.form.disconnected.label": "Zu überweisender Betrag", "send_to.qr_code.description": "<PERSON><PERSON> einen QR-Code, um an eine Wallet zu senden", "send_to.qr_code.title": "QR-Code scannen", "send_to_card.header": "An Kartenadresse senden", "send_to_card.select_sender.add_wallet": "<PERSON><PERSON> hinzufügen", "send_to_card.select_sender.header": "Absender auswählen", "send_to_card.select_sender.search.default_placeholder": "Ad<PERSON>e oder ENS suchen", "send_to_card.select_sender.show_card_address_button_description": "Kartenadresse anzeigen", "send_token.form.select-address": "<PERSON><PERSON><PERSON> au<PERSON>wählen", "send_token.form.send-amount": "<PERSON><PERSON> senden", "send_token.form.title": "Senden", "setLimit.amount.error.zero_amount": "Du kannst dann keine Zahlungen mehr tätigen.", "setLimit.error.max_limit_reached": "Limit auf <PERSON> setzen {amount}", "setLimit.error.same_as_current_limit": "Entspricht akt. Limit", "setLimit.placeholder": "Aktuell: {amount}", "setLimit.submit": "<PERSON>it festlegen", "setLimit.submit.error.amount_required": "<PERSON><PERSON> e<PERSON>ben", "setLimit.subtitle": "Das ist der Betrag, den du täglich mit deiner Karte ausgeben kannst.", "setLimit.title": "Tägliches Ausgabenlimit festlegen", "settings.accounts": "Konten", "settings.accountsSeeAll": "Alle anzeigen", "settings.addAccount": "<PERSON><PERSON> hinzufügen", "settings.card": "Karteneinstellungen", "settings.connections": "App-Verbindungen", "settings.currency": "Standardwährung", "settings.default_currency_selector.title": "Währung", "settings.discord": "Discord", "settings.experimentalMode": "Experimenteller Modus", "settings.experimentalMode.subtitle": "Neue Funktionen testen", "settings.language": "<PERSON><PERSON><PERSON>", "settings.lockZeal": "Zeal sperren", "settings.notifications": "Benachrichtigungen", "settings.open_expanded_view": "Erweiterte Ansicht öffnen", "settings.privacyPolicy": "Datenschutzrichtlinie", "settings.settings": "Einstellungen", "settings.termsOfUse": "Nutzungsbedingungen", "settings.twitter": "𝕏 / Twitter", "settings.version": "Version {version} Env: {env}", "setup-card.confirmation": "Virtuelle Karte erhalten", "setup-card.confirmation.subtitle": "Online bezahlen und zu deiner {type} -Wallet für kontaktloses Bezahlen hinzufügen.", "setup-card.getCard": "<PERSON><PERSON>en", "setup-card.order.physicalCard": "Physische Karte", "setup-card.order.physicalCard.steps": "· Eine physische VISA Gnosis Pay {br}· V<PERSON>and dauert bis zu 3 Wochen {br}· <PERSON><PERSON>r Zahlungen vor Ort und an Geldautomaten. {br}· Zu Apple/Google Wallet hinzufügen (nur in unterstützten Ländern", "setup-card.order.subtitle1": "Du kannst mehrere Karten gleichzeitig nutzen", "setup-card.order.title": "Welch<PERSON>?", "setup-card.order.virtualCard": "<PERSON><PERSON><PERSON><PERSON>", "setup-card.order.virtual_card.steps": "· Digitale VISA Gnosis Pay {br}· Sofort für Online-Zahlungen nutzbar {br}· Zu Apple/Google Wallet hinzufügen (nur in unterstützten Ländern)", "setup-card.orderCard": "<PERSON><PERSON> bestellen", "setup-card.virtual-card": "Virtuelle Karte erhalten", "setup.notifs.fakeAndroid.title": "Benachrichtigungen für Zahlungen und eingehende Überweisungen", "setup.notifs.fakeIos.subtitle": "Zeal kann dich benachrich<PERSON>gen, wenn du Geld erhältst oder mit deiner Visa-Karte bezahlst. <PERSON> kannst dies später ändern.", "setup.notifs.fakeIos.title": "Benachrichtigungen für Zahlungen und eingehende Überweisungen", "sign.PermitAllowanceItem.spendLimit": "Ausgabenlimit", "sign.ledger.subtitle": "Wir haben die Transaktionsanfrage an deine Hardware-Wallet gesendet. Bitte fahre dort fort.", "sign.ledger.title": "<PERSON>f Hardware-<PERSON><PERSON> signieren", "sign.passkey.subtitle": "<PERSON><PERSON>er sollte dich auffordern, mit dem mit diesem Wallet verknüpften Passkey zu signieren. Bitte fahre dort fort.", "sign.passkey.title": "Passkey auswählen", "signal_aborted_for_uknown_reason.title": "Netzwerkanfrage abgebrochen", "simulatedTransaction.BridgeTrx.info.title": "Bridge", "simulatedTransaction.CardTopUp.info.title": "<PERSON><PERSON> auf Ka<PERSON>", "simulatedTransaction.CardTopUpTrx.info.title": "<PERSON><PERSON> auf Ka<PERSON>", "simulatedTransaction.NftCollectionApproval.approve": "NFT-<PERSON><PERSON><PERSON>n", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "Zur Karte hinzufügen", "simulatedTransaction.OrderEarnDepositBridge.title": "In Earn ein<PERSON>en", "simulatedTransaction.P2PTransaction.info.title": "Senden", "simulatedTransaction.PermitSignMessage.title": "Permit", "simulatedTransaction.SingleNftApproval.approve": "NFT genehmigen", "simulatedTransaction.UnknownSignMessage.title": "Signieren", "simulatedTransaction.Withdrawal.info.title": "Auszahlung", "simulatedTransaction.approval.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.approve.info.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.p2p.info.account": "An", "simulatedTransaction.p2p.info.unlabelledAccount": "Unbenanntes Wallet", "simulatedTransaction.unknown.info.receive": "Empfangen", "simulatedTransaction.unknown.info.send": "Senden", "simulatedTransaction.unknown.using": "Über {app}", "simulation.approval.modal.text": "Eine Genehmigung erlaubt einer App oder einem Smart Contract, deine Token oder NFTs für zukünftige Transaktionen zu verwenden.", "simulation.approval.modal.title": "Was sind Genehmigungen?", "simulation.approval.spend-limit.label": "Ausgabenlimit", "simulation.approve.footer.for": "<PERSON><PERSON><PERSON>", "simulation.approve.unlimited": "Unbegrenzt", "simulationNotAvailable.title": "Unbekannte Aktion", "smart-wallet-activation-view.on": "<PERSON><PERSON>", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password kann den Zugriff auf deine Wallet blockieren", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Nutze Apple oder Google, um Zeal sicher einzu<PERSON>ten", "smart-wallet.passkey-notice.title": "1Password vermeiden", "spend-limits.high.modal.text": "Setze ein Ausgabenlimit, das dem tatsächlichen Token-Bedarf der App oder des Smart Contracts entspricht. Hohe Limits sind riskant und machen es Betrügern leichter, deine To<PERSON> zu stehlen.", "spend-limits.high.modal.text_sign_message": "Das Ausgabenlimit sollte nahe an der Menge der Token liegen, die du tatsächlich mit einer App oder einem Smart Contract verwendest. Hohe Limits sind riskant und können es Betrügern erleichtern, deine To<PERSON> zu stehlen.", "spend-limits.high.modal.title": "Hohes Ausgabenlimit", "spend-limits.modal.text": "Das Ausgabenlimit legt fest, wie viele Token eine App in deinem Namen verwenden darf. Du kannst dieses Limit jederzeit ändern oder entfernen. Halte Ausgabenlimits zur Sicherheit nahe an dem Betrag, den du tatsächlich mit einer App nutzt.", "spend-limits.modal.title": "Was ist ein Ausgabenlimit?", "spent-limit-info.modal.description": "Das Ausgabenlimit legt fest, wie viele Token eine App in deinem Namen verwenden darf. Du kannst dieses Limit jederzeit ändern oder entfernen. Um sicher zu bleiben, halte die Ausgabenlimits nahe an der Menge der Token, die du tatsächlich mit einer App verwendest.", "spent-limit-info.modal.title": "Was ist ein Ausgabenlimit?", "sswaps-io.transfer-provider": "Transferanbieter", "storage.accountDetails.activateWallet": "Wallet aktivieren", "storage.accountDetails.changeWalletLabel": "Wallet-Bezeichnung ändern", "storage.accountDetails.deleteWallet": "<PERSON><PERSON> ent<PERSON>nen", "storage.accountDetails.setup_recovery_kit": "Recovery Kit", "storage.accountDetails.showPrivateKey": "Privatschlüssel anzeigen", "storage.accountDetails.showWalletAddress": "Wallet-<PERSON><PERSON><PERSON> anzeigen", "storage.accountDetails.smartBackup": "Sicherung & Wiederherstellung", "storage.accountDetails.viewSsecretPhrase": "Geheime Phrase anzeigen", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets?", "storage.manageAccounts.title": "Wallets", "submit-userop.progress.text": "Wird gesendet", "submit.error.amount_high": "<PERSON><PERSON> zu hoch", "submit.error.amount_hight": "<PERSON><PERSON> zu hoch", "submit.error.amount_low": "<PERSON><PERSON> zu nied<PERSON>", "submit.error.amount_required": "<PERSON><PERSON> e<PERSON>ben", "submit.error.maximum_number_of_characters_exceeded": "Nachricht kürzen", "submit.error.not_enough_balance": "<PERSON>do nicht ausreichend", "submit.error.recipient_required": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "submit.error.routes_not_found": "Routen nicht gefunden", "submitSafeTransaction.monitor.title": "Transaktionsergebnis", "submitSafeTransaction.sign.title": "Transaktionsergebnis", "submitSafeTransaction.state.sending": "Wird gesendet", "submitSafeTransaction.state.sign": "<PERSON>ird erste<PERSON>t", "submitSafeTransaction.submittingToRelayer.title": "Transaktionsergebnis", "submitTransaction.cancel": "Abbrechen", "submitTransaction.cancel.attemptingToStop": "Stopp wird versucht", "submitTransaction.cancel.failedToStop": "Stoppen fehlgeschlagen", "submitTransaction.cancel.stopped": "Gestoppt", "submitTransaction.cancel.title": "Transaktionsvorschau", "submitTransaction.failed.banner.description": "Das Netzwerk hat diese Transaktion unerwartet abgebrochen. Versuche es erneut oder kontaktiere uns.", "submitTransaction.failed.banner.title": "Transaktion fehlgeschlagen", "submitTransaction.failed.execution_reverted.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.failed.execution_reverted_without_message.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.failed.out_of_gas.description": "Das Netzwerk hat die Transaktion abgebrochen, weil mehr Netzwerkgebühren als erwartet verbraucht wurden.", "submitTransaction.failed.out_of_gas.title": "Netzwerkfehler", "submitTransaction.sign.title": "Transaktionsergebnis", "submitTransaction.speedUp": "<PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue": "Zur Warteschlange hinzugefügt", "submitTransaction.state.addedToQueue.short": "In Warteschlange", "submitTransaction.state.cancelled": "Gestoppt", "submitTransaction.state.complete": "{currencyCode} zu <PERSON><PERSON> hinz<PERSON>fügt", "submitTransaction.state.complete.subtitle": "Überprüfe dein Zeal-Portfolio", "submitTransaction.state.completed": "Abgeschlossen", "submitTransaction.state.failed": "Fehlgeschlagen", "submitTransaction.state.includedInBlock": "In Block aufgenommen", "submitTransaction.state.includedInBlock.short": "In Block", "submitTransaction.state.replaced": "Ersetzt", "submitTransaction.state.sendingToNetwork": "Wird an Netzwerk gesendet", "submitTransaction.stop": "Stoppen", "submitTransaction.submit": "Senden", "submitted-user-operation.state.bundled": "In Warteschlange", "submitted-user-operation.state.completed": "Abgeschlossen", "submitted-user-operation.state.failed": "Fehlgeschlagen", "submitted-user-operation.state.pending": "Wird weitergeleitet", "submitted-user-operation.state.rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submittedTransaction.failed.title": "Transaktion fehlgeschlagen", "success_splash.card_activated": "Karte aktiviert", "supportFork.give-feedback.title": "Feedback geben", "supportFork.itercom.description": "Zeal <PERSON><PERSON><PERSON><PERSON>t Fragen zu Einzahlungen, Earn, Prämien und allem anderen.", "supportFork.itercom.title": "Fragen zur Wallet", "supportFork.title": "Hilfe bei", "supportFork.zendesk.subtitle": "Gnosis Pay beantwortet Fragen zu Kartenzahlungen, Identitätsprüfungen oder Rückerstattungen.", "supportFork.zendesk.title": "Kartenzahlungen & Identität", "supported-networks.ethereum.warning": "Hohe Gebühren", "supportedNetworks.networks": "Unterstützte Netzwerke", "supportedNetworks.oneAddressForAllNetworks": "Eine Adresse für alle Netzwerke", "supportedNetworks.receiveAnyAssets": "Empfange Assets von unterstützten Netzwerken mit nur einer Adresse.", "swap.form.error.no_routes_found": "Keine Routen gefunden", "swap.form.error.not_enough_balance": "<PERSON>do nicht ausreichend", "swaps-io-details.bank.serviceProvider": "Dienstanbieter", "swaps-io-details.details.processing": "In Bearbeitung", "swaps-io-details.pending": "<PERSON><PERSON><PERSON><PERSON>", "swaps-io-details.rate": "<PERSON><PERSON>", "swaps-io-details.serviceProvider": "Dienstanbieter", "swaps-io-details.transaction.from.processing": "Transaktion gestartet", "swaps-io-details.transaction.networkFees": "Netzwerkgebühren", "swaps-io-details.transaction.state.completed-transaction": "Abgeschlossene Transaktion", "swaps-io-details.transaction.state.started-transaction": "Transaktion gestartet", "swaps-io-details.transaction.to.processing": "Abgeschlossene Transaktion", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Einzahlung sollte bald abgeschlossen sein. Kinetex verarbeitet deine Transaktion noch.", "swapsIO.monitoring.awaitingLiqSend.title": "Verzögert", "swapsIO.monitoring.awaitingRecive.title": "Weiterleitung", "swapsIO.monitoring.awaitingSend.title": "In Warteschlange", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Tokens wurden an Kinetex gesendet, werden aber bald zurückgegeben. Kinetex konnte die Zieltransaktion nicht abschließen.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Tokens werden zurückgegeben", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Tokens wurden aufgrund eines unbekannten Fehlers nicht überwiesen. Bitte versuche es erneut.", "swapsIO.monitoring.cancelledNoSlash.title": "Tokens zurückgegeben", "swapsIO.monitoring.cancelledSlashed.subtitle": "Tokens wurden zurückgegeben. Kinetex konnte die Zieltransaktion nicht abschließen.", "swapsIO.monitoring.cancelledSlashed.title": "Tokens zurückgegeben", "swapsIO.monitoring.completed.title": "Abgeschlossen", "taker-metadata.earn": "Verdiene digitale USD mit Sky", "taker-metadata.earn.aave": "Verdiene digitale EUR mit Aave", "taker-metadata.earn.aave.cashout24": "<PERSON><PERSON>, rund um die Uhr", "taker-metadata.earn.aave.trusted": "Vertrauenswürdig mit 27 Mrd. $, 2+ Jahre", "taker-metadata.earn.aave.yield": "Rendite wächst jede Sekunde", "taker-metadata.earn.chf": "In digitalen CHF verdienen", "taker-metadata.earn.chf.cashout24": "<PERSON><PERSON>, rund um die Uhr", "taker-metadata.earn.chf.trusted": "Vertrauenswürdig mit 28 Mio. CHF", "taker-metadata.earn.chf.yield": "Ertrag im Sekundentakt", "taker-metadata.earn.usd.cashout24": "<PERSON><PERSON>, rund um die Uhr", "taker-metadata.earn.usd.trusted": "Vertrauenswürdig mit 10,7 Mrd. $, 5+ Jahre", "taker-metadata.earn.usd.yield": "Rendite wächst jede Sekunde", "test": "Ein<PERSON><PERSON><PERSON>", "to.titile": "An", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "Vermögenswerte", "token.groupHeader.titleWithSum": "Vermögenswerte {sum}", "token.hidden_tokens.page.title": "Versteckte Tokens", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "Token hinzufügen", "token.widget.cashback_empty": "Noch keine Transaktionen", "token.widget.emptyState": "<PERSON><PERSON> in der Wallet", "tokens.cash": "<PERSON><PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.approve.for": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.approve.into": "In", "top-up-card-from-earn-view.swap.from": "<PERSON>", "top-up-card-from-earn-view.swap.to": "An", "top-up-card-from-earn-view.withdraw.to": "An", "top-up-card-from-earn.trx.title.approval": "<PERSON><PERSON><PERSON> gene<PERSON>n", "top-up-card-from-earn.trx.title.swap": "Zur Karte hinzufügen", "top-up-card-from-earn.trx.title.withdrawal": "Auszahlung von <PERSON>n", "topUpDapp.connectWallet": "Wallet verbinden", "topup-fee-breakdown.bungee-fee": "Externe Anbietergebühr", "topup-fee-breakdown.header": "Transaktionsgebühr", "topup-fee-breakdown.network-fee": "Netzwerkgebühr", "topup-fee-breakdown.total-fee": "Gesamtgebühr", "topup.continue-in-wallet": "In deiner Wallet fortfahren", "topup.send.title": "Senden", "topup.submit-transaction.close": "Schließen", "topup.submit-transaction.sent-to-wallet": "Senden {amount}", "topup.to": "An", "topup.transaction.complete.close": "Schließen", "topup.transaction.complete.try-again": "<PERSON><PERSON><PERSON> versuchen", "transaction-request.nonce-too-low.modal.button-text": "Schließen", "transaction-request.nonce-too-low.modal.text": "Eine Transaktion mit der gleichen Seriennummer (Nonce) wurde bereits abgeschlossen, daher kannst du diese Transaktion nicht mehr senden. Das kann passieren, wenn du Transaktionen kurz hintereinander durchführst oder versuchst, eine bereits abgeschlossene Transaktion zu beschleunigen oder abzubrechen.", "transaction-request.nonce-too-low.modal.title": "Transaktion mit gleicher Nonce wurde abgeschlossen", "transaction-request.replaced.modal.button-text": "Schließen", "transaction-request.replaced.modal.text": "Wir können den Status dieser Transaktion nicht verfolgen. Sie wurde entweder durch eine andere Transaktion ersetzt oder der RPC-Knoten hat Probleme.", "transaction-request.replaced.modal.title": "Transaktionsstatus nicht gefunden", "transaction.activity.details.modal.close": "Schließen", "transaction.cancel_popup.cancel": "<PERSON><PERSON>, warten", "transaction.cancel_popup.confirm": "Ja, stoppen", "transaction.cancel_popup.description": "Zum Stoppen zahlst du eine neue Netzwerkgebühr anstelle der ursprünglichen Gebühr von {oldFee}", "transaction.cancel_popup.description_without_original": "Zum Stoppen musst du eine neue Netzwerkgebühr zahlen", "transaction.cancel_popup.not_supported.subtitle": "Das Stoppen von Transaktionen wird auf nicht unterstützt {network}", "transaction.cancel_popup.not_supported.title": "<PERSON>cht unterstützt", "transaction.cancel_popup.stopping_fee": "Netzwerkgebühr zum Stoppen", "transaction.cancel_popup.title": "Transaktion stoppen?", "transaction.in-progress": "In Bearbeitung", "transaction.inProgress": "In Bearbeitung", "transaction.speed_up_popup.cancel": "<PERSON><PERSON>, warten", "transaction.speed_up_popup.confirm": "Ja, erhöhen", "transaction.speed_up_popup.description": "Zum Beschleunigen zahlst du eine neue Netzwerkgebühr statt der ursprünglichen Gebühr von {amount}", "transaction.speed_up_popup.description_without_original": "Zum Beschleunigen ist eine neue Netzwerkgebühr fällig.", "transaction.speed_up_popup.seed_up_fee_title": "Netzwerkgebühr zur Beschleunigung", "transaction.speed_up_popup.title": "Transaktion beschleunigen?", "transaction.speedup_popup.not_supported.subtitle": "Beschleunigen wird auf nicht unterstützt {network}", "transaction.speedup_popup.not_supported.title": "<PERSON>cht unterstützt", "transaction.subTitle.failed": "Fehlgeschlagen", "transactionDetails.cashback.not-qualified": "<PERSON><PERSON> qualifiziert", "transactionDetails.cashback.paid": "{amount} ausgezahlt", "transactionDetails.cashback.pending": "{amount} ausstehend", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "Unbekannt", "transactionDetails.cashback_estimate": "Cashback-Sc<PERSON>ät<PERSON>ng", "transactionDetails.category": "<PERSON><PERSON><PERSON>", "transactionDetails.exchangeRate": "Wechselkurs", "transactionDetails.location": "<PERSON><PERSON>", "transactionDetails.payment-approved": "Zahlung genehmigt", "transactionDetails.payment-declined": "Zahlung abgelehnt", "transactionDetails.payment-reversed": "Zahlung storniert", "transactionDetails.recharge.amountSentFromEarn.title": "<PERSON>n gesendeter Betrag", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "<PERSON><PERSON> aufgeladen", "transactionDetails.recharge.rate.title": "<PERSON><PERSON>", "transactionDetails.recharge.transactionId.title": "Transaktions-ID", "transactionDetails.refund": "Rückerstattung", "transactionDetails.reversal": "Stornierung", "transactionDetails.transactionCurrency": "Transaktionswährung", "transactionDetails.transactionId": "Transaktions-ID", "transactionDetails.type": "Transaktion", "transactionRequestWidget.approve.subtitle": "<PERSON><PERSON><PERSON> {target}", "transactionRequestWidget.p2p.subtitle": "An {target}", "transactionRequestWidget.unknown.subtitle": "Über {target}", "transactionSafetyChecksPopup.title": "Sicherheitschecks für Transaktionen", "transactions.main.activity.title": "Aktivität", "transactions.page.hiddenActivity.title": "Verborgene Aktivität", "transactions.page.title": "Aktivität", "transactions.viewTRXHistory.emptyState": "Noch keine Transaktionen", "transactions.viewTRXHistory.errorMessage": "Wir konnten deinen Transaktionsverlauf nicht laden", "transactions.viewTRXHistory.hidden.emptyState": "Keine verborgenen Transaktionen", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Aktivität für Testnetze nicht unterstützt", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Aktivität für Testnetze nicht unterstützt{br}<link>Zum Block Explorer</link>", "transfer_provider": "Überweisungsanbieter", "transfer_setup_with_different_wallet.subtitle": "Banküberweisungen sind mit einer anderen Wallet verknüpft. Du kannst nur eine Wallet für Überweisungen verwenden.", "transfer_setup_with_different_wallet.swtich_and_continue": "Wechseln und fortfahren", "transfer_setup_with_different_wallet.title": "<PERSON>et wechseln", "tx-sent-to-wallet.button": "Schließen", "tx-sent-to-wallet.subtitle": "Fahre fort in {wallet}", "unblockProviderInfo.fees": "Du erhältst die niedrigsten Gebühren: 0 % bis 5.000 $ pro Monat und 0,2 % darüber.", "unblockProviderInfo.registration": "Unblock ist bei der FNTT registriert und zugelassen, um VASP-Tausch- und Depotdienste anzubieten, und ist ein registrierter MSB-Anbieter bei der US-Fincen. <link>Mehr erfahren</link>", "unblockProviderInfo.selfCustody": "Das digitale Geld, das du erhä<PERSON>t, ist selbstverwahrt und niemand sonst hat die Kontrolle über dein Vermögen.", "unblock_invalid_faster_payment_configuration.subtitle": "Das von dir angegebene Bankkonto unterstützt keine europäischen SEPA-Überweisungen oder UK Faster Payments. Bitte gib ein anderes Konto an.", "unblock_invalid_faster_payment_configuration.title": "<PERSON><PERSON><PERSON>", "unknownTransaction.primaryText": "Kartentransaktion", "unsupportedCountry.subtitle": "Banküberweisungen sind in deinem Land noch nicht verfügbar.", "unsupportedCountry.title": "Nicht verfügbar in {country}", "update-app-popup.subtitle": "Das neueste Update enthält Fehlerbehebungen, neue Funktionen und mehr. Aktualisiere auf die neueste Version, um dein Zeal zu verbessern.", "update-app-popup.title": "Zeal-Version aktualisieren", "update-app-popup.update-now": "Jetzt aktualisieren", "user_associated_with_other_merchant.subtitle": "Diese Wallet kann nicht für Banküberweisungen verwendet werden. <PERSON>te nutze eine andere Wallet oder melde dich für Support und Updates auf unserem Discord.", "user_associated_with_other_merchant.title": "<PERSON>et kann nicht verwendet werden", "user_associated_with_other_merchant.try_with_another_wallet": "Andere Wallet versuchen", "user_email_already_exists.subtitle": "Du hast Banküberweisungen bereits mit einer anderen Wallet eingerichtet. Bitte versuche es erneut mit der zuvor verwendeten Wallet.", "user_email_already_exists.title": "Überweisungen mit anderer Wallet eingerichtet", "user_email_already_exists.try_with_another_wallet": "Andere Wallet versuchen", "validation.invalid.iban": "Ungültige IBAN", "validation.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validation.required.first_name": "<PERSON><PERSON><PERSON>", "validation.required.iban": "IBAN <PERSON>", "validation.required.last_name": "Nachname er<PERSON>", "verify-passkey.cta": "<PERSON>key bestätigen", "verify-passkey.subtitle": "Bestätige, dass dein Passkey erstellt und ordnungsgemäß gesichert ist.", "verify-passkey.title": "<PERSON>key bestätigen", "view-cashback.cashback-next-cycle": "Cashback-Rate in {time}", "view-cashback.no-cashback": "0 %", "view-cashback.no-cashback.subtitle": "<PERSON><PERSON><PERSON> ein, um Cashback zu erhalten", "view-cashback.pending": "{money} <PERSON><PERSON><PERSON><PERSON>", "view-cashback.pending-rewards.not_paid": "Erhalt in {days} T", "view-cashback.pending-rewards.paid": "<PERSON><PERSON> erhalten", "view-cashback.received-rewards": "Erhaltene Prämien", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Unbestätigte Zahlungen", "view-cashback.upcoming": "An<PERSON><PERSON> {money}", "virtual-card-order.configure-safe.loading-text": "<PERSON>rte wird erstellt", "virtual-card-order.create-order.loading-text": "Karte wird aktiviert", "virtual-card-order.create-order.success-text": "Karte aktiviert", "virtualCard.activateCard": "Karte aktivieren", "walletDeleteConfirm.main_action": "Entfernen", "walletDeleteConfirm.subtitle": "Du musst sie erneut importieren, um das Portfolio anzuzeigen oder Transaktionen durchzuführen", "walletDeleteConfirm.title": "Wallet entfernen?", "walletSetting.header": "Wallet-Einstellungen", "wallet_connect.connect.cancel": "Abbrechen", "wallet_connect.connect.connect_button": "Verbinden", "wallet_connect.connect.title": "Verbinden", "wallet_connect.connected.title": "Verbunden", "wallet_connect_add_chain_missing.title": "Netzwerk nicht unterstützt", "wallet_connect_proposal_expired.title": "Verbindung abgelaufen", "withdraw": "Au<PERSON>ahl<PERSON>", "withdraw.confirmation.close": "Abbrechen", "withdraw.confirmation.continue": "Bestätigen", "withdrawal_request.completed": "Abgeschlossen", "withdrawal_request.pending": "<PERSON><PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.cta.primary.connecting": "Wird verbunden...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Verbinden", "zeal-dapp.connect-wallet.cta.secondary": "Abbrechen", "zeal-dapp.connect-wallet.title": "Wallet verbinden, um fortzufahren", "zealSmartWalletInfo.gas": "Bezahle Netzwerkgebühren mit vielen Token; nutze beliebte ERC20-Token auf unterstützten Chains, um für Netzwerkgebühren zu bezahlen, nicht nur native <PERSON>ken", "zealSmartWalletInfo.recover": "<PERSON><PERSON>; Wiederherstellung über biometrischen Passkey aus deinem Passwortmanager, iCloud oder Google-Konto.", "zealSmartWalletInfo.selfCustodial": "Vollständig private Wallet; Passkey-Signaturen werden on-chain validiert, um zentrale Abhängigkeiten zu minimieren.", "zealSmartWalletInfo.title": "Über Zeal Smart Wallets", "zeal_a_rewards_already_claimed_error.title": "Prämie bereits eingefordert", "zwidget.minimizedDisconnected.label": "Zeal getrennt"}