{"Account.ListItem.details.label": "<PERSON><PERSON><PERSON>", "AddFromAddress.success": "Carteira guardada", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{<PERSON><PERSON><PERSON><PERSON> carteira} one{{count} carteira} other{{count} carteiras}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Frase secreta {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Cria novas carteiras a partir de uma das tuas frases secretas existentes", "AddFromExistingSecretPhrase.SelectPhrase.title": "Escolhe uma frase secreta", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "A tua frase secreta pode fazer cópias de segurança de muitas carteiras. Escolhe as que queres usar.", "AddFromExistingSecretPhrase.WalletSelection.title": "Adicionar uma carteira rapidamente", "AddFromExistingSecretPhrase.success": "Carteiras adicionadas à Zeal", "AddFromHardwareWallet.subtitle": "Seleciona a tua hardware wallet para ligares à Zeal", "AddFromHardwareWallet.title": "Hardware Wallet", "AddFromNewSecretPhrase.WalletSelection.subtitle": "<PERSON><PERSON><PERSON>a as carteiras que queres importar", "AddFromNewSecretPhrase.WalletSelection.title": "Importar carteiras", "AddFromNewSecretPhrase.accounts": "Carteiras", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "Uma Frase Secreta funciona como um porta-chaves para milhões de carteiras, cada uma com a sua chave privada única.{br}{br}Podes importar as carteiras que precisares agora ou adicionar mais tarde.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Carteiras com Frase Secreta", "AddFromNewSecretPhrase.subtitle": "Introduz a tua Frase Secreta separada por espaços", "AddFromNewSecretPhrase.success_secret_phrase_added": "Frase Secreta adicionada 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Carteiras adicionadas à Zeal", "AddFromNewSecretPhrase.wallets": "Carteiras", "AddFromPrivateKey.subtitle": "Introduz a tua Chave Privada", "AddFromPrivateKey.success": "Chave privada adicionada 🎉", "AddFromPrivateKey.title": "<PERSON><PERSON><PERSON>", "AddFromPrivateKey.typeOrPaste": "Escreve ou cola aqui", "AddFromSecretPhrase.importWallets": "{count,plural,=0{Nenhuma selecionada} one{Importar carteira} other{Importar {count} carteiras}}", "AddFromTrezor.AccountSelection.title": "Importar carteiras Trezor", "AddFromTrezor.hwWalletTip.subtitle": "Uma carteira de hardware contém milhões de carteiras com endereços diferentes. Podes importar as carteiras que precisares agora ou adicionar mais tarde.", "AddFromTrezor.hwWalletTip.title": "Importar de carteiras de hardware", "AddFromTrezor.importAccounts": "{count,plural,=0{Nenhuma selecionada} one{Importar conta} other{Importar {count} contas}}", "AddFromTrezor.success": "Carteiras adicionadas à Zeal", "ApprovalSpenderTypeCheck.failed.subtitle": "Fraude: quem gasta deve ser um contrato", "ApprovalSpenderTypeCheck.failed.title": "O recetor é uma carteira, não um contrato", "ApprovalSpenderTypeCheck.passed.subtitle": "G<PERSON>mente, aprovas ativos para contratos", "ApprovalSpenderTypeCheck.passed.title": "O gastador é um contrato inteligente", "BestReturns.subtitle": "Este fornecedor de swap dá-te o maior retorno, taxas incluídas.", "BestReturnsPopup.title": "<PERSON><PERSON><PERSON> retornos", "BlacklistCheck.Failed.subtitle": "Denúncias maliciosas por <source></source>", "BlacklistCheck.Failed.title": "O site está na lista negra.", "BlacklistCheck.Passed.subtitle": "Sem denúncias maliciosas por <source></source>", "BlacklistCheck.Passed.title": "O site não está na lista negra.", "BlacklistCheck.failed.statusButton.label": "O site foi denunciado.", "BridgeRoute.slippage": "Slippage {slippage}", "BridgeRoute.title": "<PERSON><PERSON><PERSON><PERSON>", "CheckConfirmation.InProgress": "Em curso...", "CheckConfirmation.success.splash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ChooseImportOrCreateSecretPhrase.subtitle": "Importa uma Frase Secreta ou cria uma nova", "ChooseImportOrCreateSecretPhrase.title": "<PERSON><PERSON><PERSON><PERSON>", "ConfirmTransaction.Simuation.Skeleton.title": "A realizar verificações de segurança…", "ConnectionSafetyCheckResult.passed": "Verificação de segurança aprovada", "ContactGnosisPaysupport": "Contactar Gnosis Pay", "CopyKeyButton.copied": "Copiado", "CopyKeyButton.copyYourKey": "Copiar a tua chave", "CopyKeyButton.copyYourPhrase": "Copia a tua frase", "DAppVerificationCheck.Failed.subtitle": "O site não está listado em <source></source>", "DAppVerificationCheck.Failed.title": "Site não encontrado nos registos de apps.", "DAppVerificationCheck.Passed.subtitle": "O site está listado em <source></source>", "DAppVerificationCheck.Passed.title": "O site consta nos registos de apps.", "DAppVerificationCheck.failed.statusButton.label": "Site não encontrado nos registos de apps.", "ERC20.tokens.emptyState": "Não encontrámos tokens", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Taxa de prioridade", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Últimos {period}: entre {from} e {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Taxa base: {baseFee} • Margem de segurança: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Não foi possível obter a Taxa Base atual", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Superior ao próximo Nonce. Irá bloquear", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "Nonce não pode ser inferior ao atual.", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "Não foi possível calcular a taxa de prioridade", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Não foi possível obter a Taxa Máxima atual", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "Taxa máxima", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Pode bloquear até as taxas de rede baixarem", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "Taxa Base Máxima", "EditFeeModal.Custom.gasLimit.title": "Limite de gas {gasLimit}", "EditFeeModal.Custom.title": "Definições avançadas", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Bloqueará até a Taxa Base diminuir", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Taxa baixa. Pode bloquear", "EditFeeModal.EditGasLimit.estimatedGas": "Gas est.: {estimated} • Margem de segurança: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Abaixo do estimado. A transação falhará.", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Abaixo do sugerido. A transação pode falhar.", "EditFeeModal.EditGasLimit.subtitle": "Define a quantidade máxima de gas para esta transação. Se o limite for inferior ao necessário, a transação falhará", "EditFeeModal.EditGasLimit.title": "Editar limite de gas", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Inferior ao limite mínimo de <PERSON>: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "Nonce superior ao próximo. Irá bloquear.", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "<PERSON>ão podes usar um nonce inferior ao atual", "EditFeeModal.EditNonce.subtitle": "A tua transação ficará bloqueada se definier um valor diferente do próximo nonce", "EditFeeModal.EditNonce.title": "<PERSON><PERSON> nonce", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "Precisas de {amount} para submeter", "EditFeeModal.Header.Time.unknown": "Tempo desconhecido", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Máx. {fee}", "EditFeeModal.Header.fee.unknown": "Taxa desconhecida", "EditFeeModal.Header.subsequent_failed": "As estimativas podem estar desatualizadas, a última atualização falhou", "EditFeeModal.Layout.Header.ariaLabel": "Taxa atual", "EditFeeModal.MaxFee.subtitle": "A taxa máxima é o valor máximo que pagarás por uma transação, mas geralmente pagarás a taxa prevista. Esta margem extra ajuda a que a tua transação seja concluída, mesmo que a rede abrande ou fique mais cara.", "EditFeeModal.MaxFee.title": "Taxa máxima de rede", "EditFeeModal.SelectPreset.Time.unknown": "Tempo desconhecido", "EditFeeModal.SelectPreset.ariaLabel": "Selecionar predefinição de taxa", "EditFeeModal.SelectPreset.fast": "<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.SelectPreset.normal": "Normal", "EditFeeModal.SelectPreset.slow": "<PERSON><PERSON>", "EditFeeModal.ariaLabel": "Editar taxa de rede", "FailedSimulation.Confirmation.Item.subtitle": "Ocorreu um erro interno", "FailedSimulation.Confirmation.Item.title": "Não foi possível simular a transação", "FailedSimulation.Confirmation.subtitle": "Tens a certeza de que queres continuar?", "FailedSimulation.Confirmation.title": "Estás a assinar às cegas", "FailedSimulation.Title": "Erro de simulação", "FailedSimulation.footer.subtitle": "Ocorreu um erro interno", "FailedSimulation.footer.title": "Não foi possível simular a transação", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Precisas de {amount} para submeter a transação", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Pode demorar a processar", "FeeForecastWidget.networkFee": "Taxa de rede", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Não foi possível calcular a taxa de rede", "FeeForecastWidget.subsequentFailed.message": "Estimativas antigas, a atualização falhou.", "FeeForecastWidget.unknownDuration": "Desconhecido", "FeeForecastWidget.unknownFee": "Desconhecida", "GasCurrencySelector.balance": "Saldo: {balance}", "GasCurrencySelector.networkFee": "Taxa de rede", "GasCurrencySelector.payNetworkFeesUsing": "Pagar taxas de rede com", "GasCurrencySelector.removeDefaultGasToken.description": "Pagar taxas a partir do maior saldo", "GasCurrencySelector.removeDefaultGasToken.title": "Gestão automática de taxas", "GasCurrencySelector.save": "Guardar", "GoogleDriveBackup.BeforeYouBegin.first_point": "Se me esquecer da minha palavra-passe da Zeal, perderei os meus ativos para sempre", "GoogleDriveBackup.BeforeYouBegin.second_point": "Se perder o acesso ao meu Google Drive ou modificar o meu Ficheiro de Recuperação, perderei os meus ativos para sempre", "GoogleDriveBackup.BeforeYouBegin.subtitle": "Por favor, compreende e aceita o seguinte ponto sobre a tua carteira privada:", "GoogleDriveBackup.BeforeYouBegin.third_point": "A Zeal não me pode ajudar a recuperar a minha palavra-passe da Zeal ou o meu acesso ao Google Drive", "GoogleDriveBackup.BeforeYouBegin.title": "<PERSON><PERSON> começa<PERSON>", "GoogleDriveBackup.loader.subtitle": "Por favor, aprova o pedido no Google Drive para carregar o teu Ficheiro de Recuperação", "GoogleDriveBackup.loader.title": "A aguardar aprovação...", "GoogleDriveBackup.success": "Cópia de segurança bem-sucedida 🎉", "MonitorOffRamp.overServiceTime": "A maioria das transferências é concluída em {estimated_time}, mas por vezes podem demorar mais tempo devido a verificações adicionais. Isto é normal e os fundos estão seguros enquanto estas verificações são feitas.{br}{br}Se a transação não for concluída em {support_soft_deadline}, por favor {contact_support}", "MonitorOnRamp.contactSupport": "Contactar o suporte", "MonitorOnRamp.from": "De", "MonitorOnRamp.fundsReceived": "Fundos recebidos", "MonitorOnRamp.overServiceTime": "A maioria das transferências é concluída em {estimated_time}, mas por vezes podem demorar mais tempo devido a verificações adicionais. Isto é normal e os fundos estão seguros enquanto estas verificações são feitas.{br}{br}Se a transação não for concluída em {support_soft_deadline}, por favor {contact_support}", "MonitorOnRamp.sendingToYourWallet": "A enviar para a tua carteira", "MonitorOnRamp.to": "Para", "MonitorOnRamp.waitingForTransfer": "A aguardar que transfiras os fundos", "NftCollectionCheck.failed.subtitle": "A coleção não é verificada em <source></source>", "NftCollectionCheck.failed.title": "A coleção não é verificada", "NftCollectionCheck.passed.subtitle": "A coleção é verificada em <source></source>", "NftCollectionCheck.passed.title": "A coleção é verificada", "NftCollectionInfo.entireCollection": "Coleção inteira", "NoSigningKeyStore.createAccount": "<PERSON><PERSON><PERSON>", "NonceRangeError.biggerThanCurrent.message": "A transação ficará bloqueada", "NonceRangeError.lessThanCurrent.message": "A transação falhará", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "Nonce alto demais. Reduz para evitar bloqueio.", "NonceRangeErrorPopup.biggerThanCurrent.title": "A transação ficará bloqueada", "P2pReceiverTypeCheck.failed.subtitle": "Estás a enviar para o endereço correto?", "P2pReceiverTypeCheck.failed.title": "O recetor é um contrato, não uma carteira", "P2pReceiverTypeCheck.passed.subtitle": "Normalmente envias ativos para carteiras.", "P2pReceiverTypeCheck.passed.title": "O recetor é uma carteira", "PasswordCheck.title": "Introduzir palavra-passe", "PasswordChecker.subtitle": "Introduz a tua palavra-passe para confirmar que és tu", "PermitExpirationCheck.failed.subtitle": "Mantém a duração curta e necessária", "PermitExpirationCheck.failed.title": "Prazo de validade longo", "PermitExpirationCheck.passed.subtitle": "Duração de uso dos teus tokens pela app", "PermitExpirationCheck.passed.title": "Prazo de validade não muito longo", "PrivateKeyValidationError.moreThanMaximumWords": "Máx. {count} palavras", "PrivateKeyValidationError.notValidPrivateKey": "Esta não é uma chave privada válida", "PrivateKeyValidationError.secretPhraseIsInvalid": "A frase secreta não é válida", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Palavra n.º {index} com erro ortográfico ou inválida", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} palavra} other{{count} palavras}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "As Frases Secretas e as chaves privadas são encriptadas e nunca saem deste dispositivo", "SecretPhraseReveal.header": "Anota a Frase Secreta", "SecretPhraseReveal.hint": "Não partilhes a tua frase com ninguém. Guarda-a em segurança e offline", "SecretPhraseReveal.skip.subtitle": "<PERSON><PERSON>a possas fazer isto mais tarde, se perderes este dispositivo antes de anotares a tua frase, perderás todos os ativos que adicionaste a esta carteira", "SecretPhraseReveal.skip.takeTheRisk": "Corro risco", "SecretPhraseReveal.skip.title": "Ignorar anotação da frase?", "SecretPhraseReveal.skip.writeDown": "<PERSON><PERSON><PERSON>", "SecretPhraseReveal.skipForNow": "<PERSON><PERSON><PERSON>", "SecretPhraseReveal.subheader": "Por favor, anota-a e guarda-a em segurança offline. <PERSON><PERSON><PERSON>, vamos pedir-te que a verifiques.", "SecretPhraseReveal.verify": "Verificar", "SelectCurrency.tokens": "Tokens", "SelectCurrency.tokens.emptyState": "Não encontrámos tokens", "SelectRoute.slippage": "Slippage {slippage}", "SelectRoutes.emptyState": "Não encontrámos rotas para este swap", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "<PERSON>r <PERSON><PERSON>", "SendERC20.labelAddress.inputPlaceholder": "Etiqueta da carteira", "SendERC20.labelAddress.subtitle": "Etiqueta a carteira para a encontrares depois.", "SendERC20.labelAddress.title": "Etiquetar esta carteira", "SendERC20.send_to": "Enviar para", "SendERC20.tokens": "Tokens", "SendOrReceive.bankTransfer.primaryText": "Transferência bancária", "SendOrReceive.bankTransfer.shortText": "Rampa de entrada e saída grátis e instantânea", "SendOrReceive.bridge.primaryText": "Bridge", "SendOrReceive.bridge.shortText": "Transferir tokens entre redes", "SendOrReceive.receive.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.receive.shortText": "Receber tokens ou colecionáveis", "SendOrReceive.send.primaryText": "Enviar", "SendOrReceive.send.shortText": "Enviar tokens para qualquer endereço", "SendOrReceive.swap.primaryText": "Trocar", "SendOrReceive.swap.shortText": "Trocar entre tokens", "SendSafeTransaction.Confirm.loading": "A efetuar verificações de segurança…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Cifrar um ficheiro de recuperação com palavra-passe.", "SetupRecoveryKit.google.subtitle": "Sincronizado {date}", "SetupRecoveryKit.google.title": "Cópia de segurança do Google Drive", "SetupRecoveryKit.subtitle": "Vais precisar de pelo menos uma forma de restaurar a tua conta se desinstalares a Zeal ou trocares de dispositivo", "SetupRecoveryKit.title": "Configurar Kit de Recuperação", "SetupRecoveryKit.writeDown.subtitle": "<PERSON><PERSON><PERSON>", "SetupRecoveryKit.writeDown.title": "Cópia de segurança manual", "Sign.CheckSafeDeployment.activate": "Ativar", "Sign.CheckSafeDeployment.subtitle": "Antes de poderes iniciar sessão numa app ou assinar uma mensagem off-chain, precisas de ativar o teu dispositivo nesta rede. Isto acontece depois de instalares ou recuperares uma Smart Wallet.", "Sign.CheckSafeDeployment.title": "Ativar dispositivo nesta rede", "Sign.Simuation.Skeleton.title": "A efetuar verificações de segurança…", "SignMessageSafetyCheckResult.passed": "Verificações de Segurança Aprovadas", "SignMessageSafetyChecksPopup.title.permits": "Verificações de segurança da Permissão", "SimulationFailedConfirmation.subtitle": "Simulámos esta transação e encontrámos um problema que a fará falhar. Podes submeter esta transação, mas é provável que falhe e que percas a taxa de rede.", "SimulationFailedConfirmation.title": "É provável que a transação falhe", "SimulationNotSupported.Title": "Simulação não{br}suportada em{br}{network}", "SimulationNotSupported.footer.subtitle": "Ainda podes submeter esta transação", "SimulationNotSupported.footer.title": "Simulação não suportada", "SlippagePopup.custom": "Personalizado", "SlippagePopup.presetsHeader": "Slippage da troca", "SlippagePopup.title": "Definições de slippage", "SmartContractBlacklistCheck.failed.subtitle": "Denúncias maliciosas por <source></source>", "SmartContractBlacklistCheck.failed.title": "O contrato está na lista negra", "SmartContractBlacklistCheck.passed.subtitle": "Sem denúncias maliciosas por <source></source>", "SmartContractBlacklistCheck.passed.title": "O contrato não está na lista negra", "SuspiciousCharactersCheck.Failed.subtitle": "Esta é uma tática de phishing comum.", "SuspiciousCharactersCheck.Failed.title": "Verificamos padrões de phishing comuns.", "SuspiciousCharactersCheck.Passed.subtitle": "Verificamos tentativas de phishing.", "SuspiciousCharactersCheck.Passed.title": "O endereço não tem caracteres invulgares.", "SuspiciousCharactersCheck.failed.statusButton.label": "Endereço com caracteres invulgares. ", "TokenVerificationCheck.failed.subtitle": "O token não está listado em <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} não é verificado pela CoinGecko", "TokenVerificationCheck.passed.subtitle": "O token está listado em <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} é verificado pela CoinGecko", "TopupDapp.MonitorTransaction.success.splash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TransactionSafetyCheckResult.passed": "Verificações de Segurança Aprovadas", "TransactionSimulationCheck.failed.subtitle": "Erro: {errorMessage}", "TransactionSimulationCheck.failed.title": "A transação provavelmente falhará", "TransactionSimulationCheck.passed.subtitle": "Simulação feita com <source></source>", "TransactionSimulationCheck.passed.title": "Pré-visualização da transação bem-sucedida", "TrezorError.trezor_action_cancelled.action": "<PERSON><PERSON><PERSON>", "TrezorError.trezor_action_cancelled.subtitle": "Rejeitaste a transação na tua carteira de hardware", "TrezorError.trezor_device_used_elsewhere.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "Certifica-te que fechas todas as outras sessões abertas e tenta sincronizar a tua Trezor novamente", "TrezorError.trezor_method_cancelled.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "Certifica-te que permites que a Trezor exporte carteiras para a Zeal", "TrezorError.trezor_permissions_not_granted.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "Por favor, dá permissões à Zeal para ver todas as carteiras", "TrezorError.trezor_pin_cancelled.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "Sessão cancelada no dispositivo", "TrezorError.trezor_popup_closed.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "A caixa de diálogo da Trezor fechou inesperadamente", "TrxLikelyToFail.lessThanEstimatedGas.message": "A transação falhará", "TrxLikelyToFail.lessThanMinimumGas.message": "A transação falhará", "TrxLikelyToFail.lessThanSuggestedGas.message": "Provavelmente falhará", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "O limite de gas da transação é demasiado baixo. Aumenta o limite de gas para o limite sugerido para evitar falhas.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "É provável que a transação falhe", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "O limite de gas é inferior ao gas estimado. Aumenta o limite de gas para o limite sugerido.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "A transação falhará", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "A taxa base máxima é inferior à taxa base atual. Aumenta a taxa base máxima para evitar que a transação fique bloqueada.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "A transação ficará bloqueada", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "A taxa máxima da transação é demasiado baixa. Aumenta a taxa máxima para evitar que a transação fique bloqueada.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "A transação ficará bloqueada", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "A taxa de prioridade é inferior à recomendada. Aumenta a taxa de prioridade para acelerar a transação.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "A transação pode demorar a ser concluída", "UnsupportedMobileNetworkLayout.gotIt": "<PERSON><PERSON><PERSON>", "UnsupportedMobileNetworkLayout.subtitle": "Não podes fazer transações ou assinar mensagens na rede com o id {networkHexId} com a versão móvel da Zeal ainda{br}{br}Muda para a extensão do browser para poderes transacionar nesta rede, enquanto trabalhamos para adicionar suporte para esta rede 🚀", "UnsupportedMobileNetworkLayout.title": "Rede não suportada na versão móvel da Zeal", "UnsupportedSafeNetworkLayout.subtitle": "Não podes fazer transações nem assinar mensagens em {network} com uma Zeal Smart Wallet{br}{br}Muda para uma rede suportada ou usa uma carteira Legacy.", "UnsupportedSafeNetworkLayoutk.title": "Rede não suportada para Smart Wallet", "UserConfirmationPopup.goBack": "<PERSON><PERSON><PERSON>", "UserConfirmationPopup.submit": "Submeter", "ViewPrivateKey.header": "<PERSON>ve <PERSON>", "ViewPrivateKey.hint": "Não partilhes a tua chave privada com ninguém. Guarda-a em segurança e offline", "ViewPrivateKey.subheader.mobile": "Toca para revelar a tua Chave Privada", "ViewPrivateKey.subheader.web": "Passa o rato por cima para revelar a tua Chave Privada", "ViewPrivateKey.unblur.mobile": "Toca para revelar", "ViewPrivateKey.unblur.web": "Passa o rato para revelar", "ViewSecretPhrase.PasswordChecker.subtitle": "Introduz a tua palavra-passe para encriptar o Ficheiro de Recuperação. Terás de te lembrar dela no futuro.", "ViewSecretPhrase.done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewSecretPhrase.header": "Frase Secreta", "ViewSecretPhrase.hint": "Não partilhes a tua frase com ninguém. Guarda-a em segurança e offline", "ViewSecretPhrase.subheader.mobile": "Toca para revelar a tua Frase Secreta", "ViewSecretPhrase.subheader.web": "Passa o rato por cima para revelar a tua Frase Secreta", "ViewSecretPhrase.unblur.mobile": "Toca para revelar", "ViewSecretPhrase.unblur.web": "Passa o rato para revelar", "account-details.monerium": "Usa-se Monerium, uma IME autorizada. <link><PERSON><PERSON> mais</link>", "account-details.unblock": "As transferências são feitas através da Unblock, um prestador de serviços de câmbio e custódia autorizado e registado. <link><PERSON><PERSON> mais</link>", "account-selector.empty-state": "<PERSON><PERSON><PERSON><PERSON> carteira encontrada", "account-top-up.select-currency.title": "Tokens", "account.accounts_not_found": "Não encontrámos nenhuma carteira", "account.accounts_not_found_search_valid_address": "A carteira não está na tua lista", "account.add.create_new_secret_phrase": "<PERSON><PERSON><PERSON>", "account.add.create_new_secret_phrase.subtext": "Uma nova frase secreta de 12 palavras", "account.add.fromRecoveryKit.fileNotFound": "Não foi possível encontrar o teu ficheiro", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "Tentar novamente", "account.add.fromRecoveryKit.fileNotFound.explanation": "Verifica se estás na conta correta, com a pasta de Backup da Zeal.", "account.add.fromRecoveryKit.fileNotValid": "O ficheiro de recuperação não é válido", "account.add.fromRecoveryKit.fileNotValid.explanation": "Verificámos o teu ficheiro. Ou não é do tipo certo ou foi modificado.", "account.add.import_secret_phrase": "Importar Frase Secreta", "account.add.import_secret_phrase.subtext": "<PERSON><PERSON><PERSON> na <PERSON>, Metamask ou outras", "account.add.select_type.add_hardware_wallet": "Carteira de hardware", "account.add.select_type.existing_smart_wallet": "Smart Wallet existente", "account.add.select_type.private_key": "<PERSON>ve <PERSON>", "account.add.select_type.seed_phrase": "Frase de Recuperação", "account.add.select_type.title": "Importar carteira", "account.add.select_type.zeal_recovery_file": "Ficheiro de Recuperação Zeal", "account.add.success.title": "Nova carteira criada 🎉", "account.addLabel.header": "Dá um nome à tua carteira", "account.addLabel.labelError.labelAlreadyExist": "O nome já existe. Tenta outro nome", "account.addLabel.labelError.maxStringLengthExceeded": "Número máximo de caracteres atingido", "account.add_active_wallet.primary_text": "<PERSON><PERSON><PERSON><PERSON>", "account.add_active_wallet.short_text": "<PERSON><PERSON><PERSON>, ligar ou importar carteira", "account.add_from_ledger.success": "Carteiras adicionadas à Zeal", "account.add_tracked_wallet.primary_text": "Adicionar carteira só de leitura", "account.add_tracked_wallet.short_text": "Ver portefólio e atividade", "account.button.unlabelled-wallet": "Carteira sem nome", "account.create_wallet": "<PERSON><PERSON><PERSON>", "account.label.edit.title": "Editar nome da carteira", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON> em {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "O ficheiro de recuperação não é válido", "account.recoveryKit.selectBackupFile.subtitle": "Seleciona o ficheiro de recuperação a restaurar.", "account.recoveryKit.selectBackupFile.title": "Ficheiro de recuperação", "account.recoveryKit.success.recoveryFileFound": "Ficheiro de recuperação encontrado 🎉", "account.select_type_of_account.create_eoa.short": "Carteira tradicional para especialistas", "account.select_type_of_account.create_eoa.title": "Criar carteira com Frase de Recuperação", "account.select_type_of_account.create_safe_wallet.title": "Criar Smart wallet", "account.select_type_of_account.existing_smart_wallet": "Smart Wallet existente", "account.select_type_of_account.hardware_wallet": "Carteira de hardware", "account.select_type_of_account.header": "<PERSON><PERSON><PERSON><PERSON>", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "<PERSON>ve privada / Frase semente", "account.select_type_of_account.read_only_wallet": "Carteira só de leitura", "account.select_type_of_account.read_only_wallet.short": "Pré-visualiza qualquer portfólio", "account.topup.title": "Adicionar fundos ao Zeal", "account.view.error.refreshAssets": "<PERSON><PERSON><PERSON><PERSON>", "account.widget.refresh": "<PERSON><PERSON><PERSON><PERSON>", "account.widget.settings": "Definições", "accounts.view.copied-text": "Copiado {formattedAddress}", "accounts.view.copiedAddress": "Copiado {formattedAddress}", "action.accept": "Aceitar", "action.accpet": "Aceitar", "action.allow": "<PERSON><PERSON><PERSON>", "action.back": "Voltar", "action.cancel": "<PERSON><PERSON><PERSON>", "action.card-activation.title": "Ativar cart<PERSON>", "action.claim": "Reivindicar", "action.close": "<PERSON><PERSON><PERSON>", "action.complete-steps": "Concluir", "action.confirm": "Confirmar", "action.continue": "<PERSON><PERSON><PERSON><PERSON>", "action.copy-address-understand": "<PERSON> - <PERSON><PERSON><PERSON>", "action.deposit": "Depositar", "action.done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.dontAllow": "Não permitir", "action.edit": "editar", "action.email-required": "Introduzir e-mail", "action.enterPhoneNumber": "Inserir n.º de telemóvel", "action.expand": "Expandir", "action.fix": "<PERSON><PERSON><PERSON><PERSON>", "action.getStarted": "<PERSON><PERSON><PERSON>", "action.got_it": "Entendido", "action.hide": "Ocultar", "action.import": "Importar", "action.import-keys": "Importar chaves", "action.importKeys": "Importar chaves", "action.minimize": "<PERSON><PERSON><PERSON>", "action.next": "Se<PERSON><PERSON>", "action.ok": "OK", "action.reduceAmount": "Reduzir ao máximo", "action.refreshWebsite": "Atualizar site", "action.remove": "Remover", "action.remove-account": "Remover conta", "action.requestCode": "<PERSON>ed<PERSON> c<PERSON>", "action.resend_code": "Reenviar código", "action.resend_code_with_time": "<PERSON><PERSON><PERSON><PERSON> có<PERSON> {time}", "action.retry": "Tentar novamente", "action.reveal": "<PERSON><PERSON><PERSON>", "action.save": "Guardar", "action.save_changes": "Guardar RPC", "action.search": "<PERSON><PERSON><PERSON><PERSON>", "action.seeAll": "Ver tudo", "action.select": "Selecionar", "action.send": "Enviar", "action.skip": "<PERSON><PERSON><PERSON>", "action.submit": "Enviar", "action.understood": "<PERSON><PERSON><PERSON><PERSON>", "action.update": "<PERSON><PERSON><PERSON><PERSON>", "action.update-gnosis-pay-owner.complete": "Concluir", "action.zeroAmount": "Introduzir valor", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Colecionáveis", "action_bar_title.tokens": "Tokens", "action_bar_title.transaction_request": "Pedido de transação", "activate-monerium.loading": "A configurar a tua conta pessoal", "activate-monerium.success.title": "Monerium ativado", "activate-physical-card-widget.subtitle": "A entrega pode demorar 3 semanas", "activate-physical-card-widget.title": "Ativar cartão físico", "activate-smart-wallet.title": "Ativar carteira", "active_and_tracked_wallets.title": "A Zeal cobre todas as tuas taxas em {network}, permitindo-te fazer transações gratuitamente.", "activity.approval-amount.revoked": "Revogada", "activity.approval-amount.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "activity.approval.approved_for": "Aprovado para", "activity.approval.approved_for_with_target": "Aprovado {approvedTo}", "activity.approval.revoked_for": "Revogado para", "activity.bank.serviceProvider": "Prestador de serviços", "activity.bridge.serviceProvider": "Prestador de serviços", "activity.cashback.period": "Período de cashback", "activity.filter.card": "Cartão", "activity.rate": "Taxa", "activity.receive.receivedFrom": "Recebido de", "activity.send.sendTo": "Enviado para", "activity.smartContract.unknown": "Contrato desconhecido", "activity.smartContract.usingContract": "A utilizar contrato", "activity.subtitle.pending_timer": "{timerString} Pendente", "activity.title.arbitrary_smart_contract_interaction": "{function} em {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Interação com contrato desconhecido", "activity.title.bridge.from": "Bridge de {token}", "activity.title.bridge.to": "Bridge para {token}", "activity.title.buy": "Compra de {asset}", "activity.title.card_owners_updated": "Titulares do cartão atualizados", "activity.title.card_spend_limit_updated": "Limite de gastos do cartão definido", "activity.title.cashback_deposit": "<PERSON><PERSON><PERSON><PERSON> em <PERSON>", "activity.title.cashback_reward": "Cashback recebido", "activity.title.cashback_withdraw": "Levantamento de Cashback", "activity.title.claimed_reward": "Recompensa resgatada", "activity.title.deployed_smart_wallet_gnosis": "<PERSON>ta criada", "activity.title.deposit_from_bank": "Depósito do banco", "activity.title.deposit_into_card": "Depósito no cartão", "activity.title.deposit_into_earn": "<PERSON><PERSON><PERSON><PERSON> em {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} em {smartContract}", "activity.title.from": "De {sender}", "activity.title.pendidng_areward_claim": "A reivindicar recompensa", "activity.title.pendidng_breward_claim": "A reivindicar recompensa", "activity.title.recharge_disabledh": "Recarga do cartão desativada", "activity.title.recharge_set": "Objetivo de recarga definido", "activity.title.recovered_smart_wallet_gnosis": "Instalação de novo dispositivo", "activity.title.send_pending": "Para {receiver}", "activity.title.send_to_bank": "Para o banco", "activity.title.swap": "Compra de {token}", "activity.title.to": "Para {receiver}", "activity.title.withdraw_from_card": "Levantamento do cartão", "activity.title.withdraw_from_earn": "Levantamento de {earn}", "activity.transaction.networkFees": "Taxas de rede", "activity.transaction.state": "Transação concluída", "activity.transaction.state.completed": "Transferência concluída", "activity.transaction.state.failed": "Transferência falhada", "add-account.section.import.header": "Importar", "add-another-card-owner": "Adicionar outro titular do cartão", "add-another-card-owner.Recommended.footnote": "Adiciona a Zeal como titular do teu cartão", "add-another-card-owner.Recommended.primaryText": "Adicionar <PERSON> ao Gnosis Pay", "add-another-card-owner.recommended": "Recomendado", "add-owner.confirmation.subtitle": "Por segurança, as alterações de definições demoram 3 minutos a ser processadas, durante os quais o teu cartão será temporariamente bloqueado e não será possível efetuar pagamentos.", "add-owner.confirmation.title": "O teu cartão será bloqueado durante 3 min enquanto as definições são atualizadas", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Não é possível adicionar a carteira, já está a ser usada", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "Tentar outra carteira", "add.account.backup.decrypt.success": "<PERSON><PERSON>ira restaurada", "add.account.backup.password.passwordIncorrectMessage": "A palavra-passe está incorreta", "add.account.backup.password.subtitle": "Introduz a palavra-passe que usaste para encriptar o teu Ficheiro de Recuperação", "add.account.backup.password.title": "Introduzir palavra-passe", "add.account.google.login.subtitle": "Aprova o pedido no Google Drive para sincronizar o teu Ficheiro de Recuperação", "add.account.google.login.title": "A aguardar aprovação...", "add.readonly.already_added": "Carteira já adicionada", "add.readonly.continue": "<PERSON><PERSON><PERSON><PERSON>", "add.readonly.empty": "Introduz um endereço ou ENS", "addBankRecipient.title": "<PERSON><PERSON><PERSON><PERSON>", "add_funds.deposit_from_bank_account": "Depositar da conta bancária", "add_funds.from_another_wallet": "De outra carteira", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Ligar à dApp de carregamento", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Liga qualquer carteira à dApp de carregamento da Zeal para enviares fundos rapidamente.", "add_funds.from_crypto_wallet.header": "De outra carteira", "add_funds.from_crypto_wallet.header.show_wallet_address": "Mostrar o endereço da tua carteira", "add_funds.from_exchange.header": "Enviar de uma exchange", "add_funds.from_exchange.header.copy_wallet_address": "Copiar o teu endereço Zeal", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance, etc.", "add_funds.from_exchange.header.open_exchange": "Abrir a app ou site da exchange", "add_funds.from_exchange.header.selected_token": "Enviar {token} para a Zeal", "add_funds.from_exchange.header.selected_token.subtitle": "Em {network}", "add_funds.from_exchange.header.send_selected_token": "Enviar token suportado", "add_funds.from_exchange.header.send_selected_token.subtitle": "Selecionar token e rede suportados", "add_funds.import_wallet": "Importar carteira de cripto existente", "add_funds.title": "Financiar a tua conta", "add_funds.transfer_from_exchange": "Transferir de uma exchange", "address.add.header": "Vê a tua carteira na Zeal{br}em modo de só de leitura", "address.add.subheader": "Introduz o teu endereço ou ENS para veres os teus ativos em todas as redes EVM num só local. Cria ou importa mais carteiras mais tarde.", "address_book.change_account.bank_transfers.header": "Destinatários bancários", "address_book.change_account.bank_transfers.primary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "address_book.change_account.cta": "Acompanhar carteira", "address_book.change_account.search_placeholder": "Adicionar ou procurar endereço", "address_book.change_account.tracked_header": "Carteiras só de leitura", "address_book.change_account.wallets_header": "Carteiras ativas", "app-association-check-failed.modal.cta": "Tentar novamente", "app-association-check-failed.modal.subtitle": "Tenta novamente. Problemas de conetividade estão a causar atrasos ao obter as tuas Passkeys. Se o problema persistir, reinicia a Zeal e tenta mais uma vez.", "app-association-check-failed.modal.subtitle.creation": "Tenta novamente. Problemas de conetividade estão a causar atrasos na criação da Passkey. Se o problema persistir, reinicia a Zeal e tenta mais uma vez.", "app-association-check-failed.modal.title.creation": "O teu dispositivo não conseguiu criar uma passkey", "app-association-check-failed.modal.title.signing": "O teu dispositivo não conseguiu carregar as passkeys", "app.app_protocol_group.borrowed_tokens": "Tokens pedidos de empréstimo", "app.app_protocol_group.claimable_amount": "<PERSON><PERSON> re<PERSON>", "app.app_protocol_group.health_rate": "Taxa de saúde", "app.app_protocol_group.lending": "Emprést<PERSON>", "app.app_protocol_group.locked_tokens": "Tokens bloqueados", "app.app_protocol_group.nfts": "Colecionáveis", "app.app_protocol_group.reward_tokens": "Tokens de recompensa", "app.app_protocol_group.supplied_tokens": "Tokens fornecidos", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Token em aquisição", "app.appsGroupHeader.discoverMore": "<PERSON><PERSON><PERSON><PERSON> mais", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Pesquisar ou inserir URL", "app.error-banner.cory": "Copiar dados do erro", "app.error-banner.retry": "Tentar novamente", "app.list_item.rewards": "Recompensas {value}", "app.position_details.health_rate.description": "A saúde é calculada dividindo o montante do teu empréstimo pelo valor da tua garantia.", "app.position_details.health_rate.title": "O que é a taxa de saúde?", "approval.edit-limit.label": "Editar limite de gastos", "approval.permit_info": "Informações da permissão", "approval.spend-limit.edit-modal.cancel": "<PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.limit-label": "Limite de gastos", "approval.spend-limit.edit-modal.max-limit-error": "Aviso, limite elevado", "approval.spend-limit.edit-modal.revert": "Reverter alteraç<PERSON>es", "approval.spend-limit.edit-modal.set-to-unlimited": "Definir como ilimitado", "approval.spend-limit.edit-modal.submit": "Guardar alterações", "approval.spend-limit.edit-modal.title": "<PERSON><PERSON>", "approval.spend_limit_info": "O que é o limite de gastos?", "approval.what_are_approvals": "O que são aprovações?", "apps_list.page.emptyState": "Nenhuma app ativa", "backpace.removeLastDigit": "Remover o último dígito", "backup-banner.backup_now": "Fazer backup", "backup-banner.risk_losing_funds": "Faz backup agora ou arrisca-te a perder os fundos", "backup-banner.title": "<PERSON><PERSON>ira sem backup", "backupRecoverySmartWallet.noExportPrivateKeys": "Backup automático: a tua Smart Wallet é guardada como uma passkey, não precisas de frase secreta nem de chaves privadas.", "backupRecoverySmartWallet.safeContracts": "Segurança multi-chave: as carteiras Zeal usam contratos Safe, para que vários dispositivos possam aprovar uma transação. Sem ponto único de falha.", "backupRecoverySmartWallet.security": "Vários dispositivos: podes usar a tua carteira em vários dispositivos com a Passkey. Cada dispositivo tem a sua própria chave privada.", "backupRecoverySmartWallet.showLocalPrivateKey": "Modo especialista: podes exportar a chave privada deste dispositivo, usá-la noutra carteira e ligar-te em <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Mostrar chave privada</Key>", "backupRecoverySmartWallet.storingKeys": "Sincronizado na cloud: a passkey é guardada em segurança no iCloud, no Gestor de Palavras-passe do Google ou no teu gestor de palavras-passe.", "backupRecoverySmartWallet.title": "Backup e recuperação da Smart Wallet", "balance-change.card.titile": "Cartão", "balanceChange.pending": "Pendente", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Selecionar prestador de serviços", "bank-transfer.change-deposit-receiver.subtitle": "Esta carteira receberá todos os depósitos bancários", "bank-transfer.change-deposit-receiver.title": "Definir carteira de receção", "bank-transfer.change-owner.subtitle": "Esta carteira é usada para iniciar sessão e recuperar a tua conta de transferência bancária", "bank-transfer.change-owner.title": "Definir proprietário da conta", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Todos os depósitos bancários que enviares para a Zeal serão recebidos por esta carteira.", "bank-transfer.configrm-change-deposit-receiver.title": "Alterar carteira de receção", "bank-transfer.configrm-change-owner.subtitle": "Tens a certeza de que queres alterar o proprietário da conta? Esta carteira é usada para iniciar sessão e recuperar a tua conta de transferência bancária.", "bank-transfer.configrm-change-owner.title": "Alterar proprietá<PERSON> da conta", "bank-transfer.deposit.widget.status.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfer.deposit.widget.status.funds_received": "Fundos recebidos", "bank-transfer.deposit.widget.status.sending_to_wallet": "A enviar para a carteira", "bank-transfer.deposit.widget.status.transfer-on-hold": "Transferência em espera", "bank-transfer.deposit.widget.status.transfer-received": "A enviar para a carteira", "bank-transfer.deposit.widget.subtitle": "{from} para {to}", "bank-transfer.deposit.widget.title": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.intro.bulletlist.point_1": "Configurar com Unblock", "bank-transfer.intro.bulletlist.point_2": "Transferir entre EUR/GBP e mais de 10 tokens", "bank-transfer.intro.bulletlist.point_3": "0% de comissões até 5 mil $ mensais, 0,2% depois disso", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "A enviar para o banco", "bank-transfer.withdrawal.widget.status.in-progress": "A realizar a transferência", "bank-transfer.withdrawal.widget.status.on-hold": "Transferência em espera", "bank-transfer.withdrawal.widget.status.success": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfer.withdrawal.widget.subtitle": "{from} para {to}", "bank-transfer.withdrawal.widget.title": "Levantamento", "bank-transfers.bank-account-actions.remove-this-account": "Remover esta conta", "bank-transfers.bank-account-actions.switch-to-this-account": "Mudar para esta conta", "bank-transfers.deposit.fees-for-less-than-5k": "Comissões para 5 mil $ ou menos", "bank-transfers.deposit.fees-for-more-than-5k": "Comissões para mais de 5 mil $", "bank-transfers.set-receiving-bank.title": "Definir banco de receção", "bank-transfers.settings.account_owner": "Titular da conta", "bank-transfers.settings.receiver_of_bank_deposits": "Recetor de depósitos bancários", "bank-transfers.settings.receiver_of_withdrawals": "Re<PERSON>tor de levant<PERSON>os", "bank-transfers.settings.registered_email": "E-mail registado", "bank-transfers.settings.title": "Definições de transferência bancária", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} Conta", "bank-transfers.setup.bank-account": "Conta bancária", "bankTransfer.withdraw.max_loading": "Máx.: {amount}", "bank_details_do_not_match.got_it": "<PERSON><PERSON><PERSON>", "bank_details_do_not_match.subtitle": "O sort code e o número de conta não correspondem. Verifica se os dados foram introduzidos corretamente e tenta novamente.", "bank_details_do_not_match.title": "Os dados bancários não correspondem", "bank_tranfsers.select_country_of_residence.country_not_supported": "<PERSON><PERSON><PERSON><PERSON>, mas as transferências bancárias não são suportadas em {country} ainda", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Abre a tua aplicação bancária", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Envia {fiatCurrencyCode} para a tua conta", "bank_transfer.deposit.header": "{fullName}<PERSON><PERSON> da conta pessoal", "bank_transfer.kyc_status_widget.subtitle": "Transferências bancárias", "bank_transfer.kyc_status_widget.title": "A verificar a identidade", "bank_transfer.personal_details.date_of_birth": "Data de nascimento", "bank_transfer.personal_details.date_of_birth.invalid_format": "Data inválida", "bank_transfer.personal_details.date_of_birth.too_young": "Tens de ter pelo menos 18 anos", "bank_transfer.personal_details.first_name": "Nome próprio", "bank_transfer.personal_details.last_name": "Apelido", "bank_transfer.personal_details.title": "Os teus dados", "bank_transfer.reference.label": "Referência (Opcional)", "bank_transfer.reference_message": "Enviado do Zeal", "bank_transfer.residence_details.address": "A tua morada", "bank_transfer.residence_details.city": "Cidade", "bank_transfer.residence_details.country_of_residence": "País de residência", "bank_transfer.residence_details.country_placeholder": "<PERSON><PERSON>", "bank_transfer.residence_details.postcode": "Código postal", "bank_transfer.residence_details.street": "<PERSON><PERSON>", "bank_transfer.residence_details.your_residence": "A tua residência", "bank_transfers.choose-wallet.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.test": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.warning.subtitle": "Só podes associar uma carteira de cada vez. Não poderás alterar a carteira associada.", "bank_transfers.choose-wallet.warning.title": "Escolhe a tua carteira com atenção", "bank_transfers.choose_wallet.subtitle": "Escolhe a carteira para ligar à conta. ", "bank_transfers.choose_wallet.title": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.currency_is_currently_not_supported": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.account-name": "Nome da conta", "bank_transfers.deposit.account-number-copied": "Número de conta copiado", "bank_transfers.deposit.amount-input": "Montante a depositar", "bank_transfers.deposit.amount-output": "Montante de destino", "bank_transfers.deposit.amount-output.error": "erro", "bank_transfers.deposit.buttet-point.receive-crypto": "Recebe {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.currency-not-supported.subtitle": "Os depósitos bancários de {code} foram desativados até novo aviso.", "bank_transfers.deposit.currency-not-supported.title": "{code} depósitos não suportados de momento", "bank_transfers.deposit.default-token.balance": "Saldo {amount}", "bank_transfers.deposit.deposit-header": "Depositar", "bank_transfers.deposit.enter_amount": "<PERSON><PERSON><PERSON><PERSON><PERSON> montante", "bank_transfers.deposit.iban-copied": "IBAN copiado", "bank_transfers.deposit.increase-amount": "A transferência mínima é {limit}", "bank_transfers.deposit.loading": "A carregar", "bank_transfers.deposit.max-limit-reached": "O montante excede o limite máximo de transferência", "bank_transfers.deposit.modal.kyc.button-text": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.modal.kyc.text": "Para verificar a tua identidade, precisamos de alguns dados pessoais e documentos. O envio demora apenas alguns minutos.", "bank_transfers.deposit.modal.kyc.title": "Verifica a tua identidade para aumentar os limites", "bank_transfers.deposit.reduce_amount": "<PERSON><PERSON><PERSON>tante", "bank_transfers.deposit.show-account.account-number": "Número de conta", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Sort code", "bank_transfers.deposit.sort-code-copied": "Sort code copiado", "bank_transfers.deposit.withdraw-header": "Levantar", "bank_transfers.failed_to_load_fee": "Desconhecido", "bank_transfers.fees": "Comissões", "bank_transfers.increase-amount": "A transferência mínima é {limit}", "bank_transfers.insufficient-funds": "Fundos insuficientes", "bank_transfers.select_country_of_residence.title": "Onde vives?", "bank_transfers.setup.cta": "Ativar transferências", "bank_transfers.setup.enter-amount": "<PERSON><PERSON><PERSON><PERSON><PERSON> montante", "bank_transfers.source_of_funds.form.business_income": "Rendimentos de negócio", "bank_transfers.source_of_funds.form.other": "Outro", "bank_transfers.source_of_funds.form.pension": "Pensão", "bank_transfers.source_of_funds.form.salary": "<PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.title": "A tua origem de fundos", "bank_transfers.source_of_funds_description.placeholder": "Descreve a origem dos fundos...", "bank_transfers.source_of_funds_description.title": "Diz-nos mais sobre a tua origem de fundos", "bank_transfers.withdraw-header": "Levantar", "bank_transfers.withdraw.amount-input": "Montante a levantar", "bank_transfers.withdraw.max-limit-reached": "O montante excede o limite máximo de transferência", "bank_transfers.withdrawal.verify-id": "<PERSON><PERSON><PERSON>tante", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Limite máximo de entrada excedido", "banner.above_maximum_limit.maximum_limit_per_deposit": "Este é o limite máximo por depósito", "banner.above_maximum_limit.subtitle": "Limite máximo de entrada excedido", "banner.above_maximum_limit.title": "Reduz o valor para {amount} ou menos", "banner.above_maximum_limit.title.default": "Reduz o valor", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Limite m<PERSON><PERSON> de entrada não atingido", "banner.below_minimum_limit.minimum_limit_for_token": "Este é o limite mínimo para este token", "banner.below_minimum_limit.title": "Aumenta o valor para {amount} ou mais", "banner.below_minimum_limit.title.default": "Aumenta o valor", "breaard.in_porgress.info_popup.cta": "Gasta para ganhar {earn}", "breaard.in_porgress.info_popup.footnote": "Ao usar o Zeal e Gnosis Pay, aceitas os T&C.", "breaward.in_porgress.info_popup.bullet_point_1": "Gasta {remaining} nos próximos {time} para reclamar esta recompensa.", "breaward.in_porgress.info_popup.bullet_point_2": "Apenas compras com Gnosis Pay são válidas.", "breaward.in_porgress.info_popup.bullet_point_3": "<PERSON><PERSON><PERSON> reclamar, vai para a tua conta Zeal.", "breaward.in_porgress.info_popup.header": "<PERSON><PERSON><PERSON> {earn}, ao gastar {remaining}", "breward.celebration.for_spending": "Por usar o teu cartão", "breward.dc25-eligible-celebration.for_spending": "Est<PERSON> entre os primeiros {limit}.", "breward.dc25-non-eligible-celebration.for_spending": "Não estiveste entre os primeiros {limit} a gastar", "breward.expired_banner.earn_by_spending": "Ganha {earn} ao gastar {amount}", "breward.expired_banner.reward_expired": "{earn} recompensa expirou", "breward.in_progress_banner.cta.title": "Gasta para ganhar {earn}", "breward.ready_to_claim.error.try_again": "Tentar de novo", "breward.ready_to_claim.error_title": "Falha ao reclamar recompensa", "breward.ready_to_claim.in_progress": "A reclamar recompensa", "breward.ready_to_claim.youve_earned": "<PERSON><PERSON><PERSON><PERSON><PERSON> {earn}.", "breward_already_claimed.title": "Recompensa já reclamada. Se não recebeste o token de recompensa, contacta o suporte", "breward_cannotbe_claimed.title": "A recompensa não pode ser reclamada agora. Tenta novamente mais tarde", "bridge.best_return": "Rota com melhor retorno", "bridge.best_serivce_time": "Rota com melhor tempo de serviço", "bridge.check_status.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge.check_status.progress_text": "A fazer Bridge de {from} para {to}", "bridge.remove_topup": "Remover carregamento", "bridge.request_status.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge.request_status.pending": "Pendente", "bridge.widget.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge.widget.currencies": "{from} para {to}", "bridge_rote.widget.title": "Bridge", "browse.discover_more_apps": "Descobrir mais apps", "browse.google_search_term": "Pesquisar \"{searchTerm}\"", "brward.celebration.you_earned": "Ganháste", "brward.expired_banner.subtitle": "Mais sorte para a próxima", "brward.in_progress_banner.subtitle": "Expira em {expiredInFormatted}", "buy": "<PERSON><PERSON><PERSON>", "buy.enter_amount": "Introduz o valor", "buy.loading": "A carregar...", "buy.no_routes_found": "Nenhuma rota encontrada", "buy.not_enough_balance": "<PERSON><PERSON> insuficiente", "buy.select-currency.title": "Selecionar token", "buy.select-to-currency.title": "Comprar tokens", "buy_form.title": "Comprar token", "cancelled-card.create-card-button.primary": "Obter novo cartão virtual", "cancelled-card.switch-card-button.primary": "T<PERSON>car de <PERSON>", "cancelled-card.switch-card-button.short-text": "Tens outro cartão ativo", "card": "Cartão", "card-add-cash.confirm-stage.banner.no-routes-found": "<PERSON><PERSON><PERSON><PERSON> rota, tenta com um token ou valor diferente", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Precisas de {amount} mais {symbol} para pagar as taxas", "card-add-cash.confirm-stage.banner.value-loss": "Vais perder {loss} de valor", "card-add-cash.confirm-stage.banner.value-loss.revert": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.cancel": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.continue": "<PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.enter-amount": "Digite valor", "card-add-cash.edit-stage.cta.reduce-to-max": "<PERSON><PERSON>", "card-add-cash.edit-staget.banner.no-routes-found": "<PERSON><PERSON><PERSON><PERSON> rota, tenta com um token ou valor diferente", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Pedido na tua hardware wallet. Continua lá.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Assinar na hardware wallet", "card-balance": "Saldo: {balance}", "card-cashback.status.title": "<PERSON><PERSON><PERSON><PERSON> em <PERSON>", "card-copy-safe-address.copy_address": "<PERSON><PERSON><PERSON>", "card-copy-safe-address.copy_address.done": "Copiado", "card-copy-safe-address.warning.description": "Este endereço apenas pode receber {cardAsset} na Gnosis Chain. Não envies ativos de outras redes para este endereço. Serão perdidos.", "card-copy-safe-address.warning.header": "Apenas enviar {cardAsset} na Gnosis Chain", "card-marketing-card.center.subtitle": "Taxas de Câmbio", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "<PERSON><PERSON>", "card-marketing-card.right.subtitle": "Oferta de adesão", "card-marketing-card.title": "O cartão VISA com juros altos da Europa", "card-marketing-tile.get-started": "<PERSON><PERSON><PERSON>", "card-select-from-token-title": "Selecionar token de origem", "card-top-up.banner.subtitle.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card-top-up.banner.subtitle.failed": "Fal<PERSON>", "card-top-up.banner.subtitle.pending": "{timerString} Pendente", "card-top-up.banner.title": "A depositar {amount}", "card-topup.select-token.emptyState": "Não encontrámos tokens", "card.activate.card_number_not_valid": "Cartão inválido. Verifica e tenta de novo.", "card.activate.invalid_card_number": "Número de cartão inválido.", "card.activation.activate_physical_card": "Ativar cartão físico", "card.add-cash.amount-to-withdraw": "Valor a carregar", "card.add-from-earn-form.title": "Adicionar <PERSON> ao Cartão", "card.add-from-earn-form.withdraw-to-card": "<PERSON><PERSON><PERSON><PERSON>", "card.add-from-earn.amount-to-withdraw": "Montante a levantar para o Cartão", "card.add-from-earn.enter-amount": "<PERSON><PERSON><PERSON><PERSON><PERSON> montante", "card.add-from-earn.loading": "A carregar", "card.add-from-earn.max-label": "Saldo: {amount}", "card.add-from-earn.no-routes-found": "Nenhuma rota encontrada", "card.add-from-earn.not-enough-balance": "<PERSON><PERSON> insuficiente", "card.add-owner.queued": "Adicionar proprietário em fila", "card.add-to-wallet-flow.subtitle": "Faz pagamentos a partir da tua carteira.", "card.add-to-wallet.copy-card-number": "Copia o número do cartão abaixo", "card.add-to-wallet.title": "Adicionar <PERSON> {platformName} Wallet", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "CANCELADO", "card.card-owner-not-found.disconnect-btn": "Desligar cartão do Zeal", "card.card-owner-not-found.subtitle": "Para continuares a usar o teu cartão Gnosis Pay no Zeal, atualiza o titular para o reconectar.", "card.card-owner-not-found.title": "Reconectar cartão", "card.card-owner-not-found.update-owner-btn": "Atualizar titular", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} em {date}", "card.cashback.widgetNoCashback.subtitle": "Deposita para começar a ganhar", "card.cashback.widgetNoCashback.title": "<PERSON><PERSON><PERSON> até {defaultPercentage} de Cashback", "card.cashback.widgetcashbackValue.rewards": "{amount} pendente", "card.cashback.widgetcashbackValue.title": "{percentage} de Cashback", "card.choose-wallet.connect_card": "Ligar <PERSON>", "card.choose-wallet.create-new": "Adicionar nova carteira como titular", "card.choose-wallet.import-another-wallet": "Importar outra carteira", "card.choose-wallet.import-current-owner": "Importar titular atual do cartão", "card.choose-wallet.import-current-owner.sub-text": "<PERSON><PERSON><PERSON> as chaves do titular do teu cartão", "card.choose-wallet.title": "Seleciona a carteira para gerir o teu cartão", "card.connectWalletToCardGuide": "<PERSON><PERSON><PERSON> endere<PERSON> da carteira", "card.connectWalletToCardGuide.addGnosisPayOwner": "Adicionar proprietário do Gnosis Pay", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Abre Gnosispay.com com a tua outra carteira{br}2. Clica em \"Conta\"{br}3. Clica em \"Detalhes da conta\"{br}4. Clica em \"Editar\", junto a \"Proprietário da conta\", e{br}5. Clica em \"Adicionar endereço\"{br}6. Cola o teu endereço Zeal e clica em guardar", "card.connectWalletToCardGuide.header": "Ligar {account} ao cart<PERSON> Gnosis Pay", "card.connect_card.start": "Ligar cartão existente", "card.copiedAddress": "Copiado {formattedAddress}", "card.disconnect-account.title": "Des<PERSON>r conta", "card.hw-wallet-support-drop.add-owner-btn": "Adicionar novo titular", "card.hw-wallet-support-drop.disconnect-btn": "Desligar cartão do Zeal", "card.hw-wallet-support-drop.subtitle": "Para continuares a usar o teu cartão Gnosis Pay no Zeal, adiciona outro titular que não seja uma Hardware Wallet.", "card.hw-wallet-support-drop.title": "O Zeal já não suporta hardware wallets para o cartão.", "card.kyc.continue": "Contin<PERSON>r configura<PERSON>", "card.list_item.title": "Cartão", "card.onboarded.transactions.empty.description": "A tua atividade de pagamentos aparecerá aqui", "card.onboarded.transactions.empty.title": "Atividade", "card.order.continue": "Con<PERSON>uar pedido", "card.order.free_virtual_card": "Pedir <PERSON>r<PERSON>", "card.order.start": "Pedir <PERSON> gr<PERSON>", "card.owner-not-imported.cancel": "<PERSON><PERSON><PERSON>", "card.owner-not-imported.import": "Importar", "card.owner-not-imported.subtitle": "Para autorizar esta transação, associa a carteira proprietária da tua conta Gnosis Pay ao Zeal. Nota: Isto é diferente do teu início de sessão habitual da carteira Gnosis Pay.", "card.owner-not-imported.title": "Adicionar proprietário da conta Gnosis Pay", "card.page.order_free_physical_card": "Encomendar <PERSON> gr<PERSON>", "card.pin.change_pin_at_atm": "O PIN pode ser alterado em caixas multibanco selecionadas", "card.pin.timeout": "O ecrã fechará em {seconds} seg", "card.quick-actions.add-assets": "<PERSON><PERSON><PERSON>", "card.quick-actions.add-cash": "<PERSON><PERSON><PERSON>", "card.quick-actions.details": "<PERSON><PERSON><PERSON>", "card.quick-actions.freeze": "Bloquear", "card.quick-actions.freezing": "A bloquear", "card.quick-actions.unfreeze": "Desb<PERSON>que<PERSON>", "card.quick-actions.unfreezing": "A desbloquear", "card.quick-actions.withdraw": "Levantar", "card.read-only-detected.create-new": "Adicionar nova carteira como titular", "card.read-only-detected.import-current-owner": "Importar chaves para {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Importar chaves privadas ou frase de recuperação da carteira {address}", "card.read-only-detected.title": "Cartão em carteira só de leitura. Seleciona uma carteira para o gerir.", "card.remove-owner.queued": "Remoção de titular em fila", "card.settings.disconnect-from-zeal": "Desligar do Zeal", "card.settings.edit-owners": "Alterar titulares do cartão", "card.settings.getCard": "Obter outro cartão", "card.settings.getCard.subtitle": "Cartões virtuais ou físicos", "card.settings.notRecharging": "Sem recarregamento", "card.settings.notifications.subtitle": "Receber notificações de pagamento", "card.settings.notifications.title": "Notificações do cartão", "card.settings.page.title": "Definições do Cartão", "card.settings.select-card.cancelled-cards": "Cartões cancelados", "card.settings.setAutoRecharge": "Definir recarregamento automático", "card.settings.show-card-address": "Mostrar endereço do cartão", "card.settings.spend-limit": "Definir limite de gastos", "card.settings.spend-limit-title": "Limite <PERSON><PERSON><PERSON> atual: {limit}", "card.settings.switch-active-card": "Mudar de cartão ativo", "card.settings.switch-active-card-description": "Cartão ativo: {card}", "card.settings.switch-card.card-item.cancelled": "Cancelado", "card.settings.switch-card.card-item.frozen": "<PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.title": "Cartão Gnosis Pay", "card.settings.switch-card.card-item.title.physical": "Cartão físico", "card.settings.switch-card.card-item.title.virtual": "Cartão virtual", "card.settings.switch-card.title": "Selecionar cartão", "card.settings.targetBalance": "Saldo alvo: {threshold}", "card.settings.view-pin": "Ver PIN", "card.settings.view-pin-description": "Protege sempre o teu PIN", "card.title": "Cartão", "card.transactions.header": "Transações do cartão", "card.transactions.see_all": "<PERSON><PERSON> to<PERSON> as transaçõ<PERSON>", "card.virtual": "VIRTUAL", "cardCashback.onboarding.bullets.cashback_sent_weekly": "O cashback é enviado para o teu cartão no início da semana seguinte a ter sido ganho.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "Quanto mais depositares, mais ganhas em cada compra.", "cardCashback.onboarding.title": "<PERSON><PERSON><PERSON> até {percentage} de Cashback", "cardCashbackWithdraw.amount": "Montante a levantar", "cardCashbackWithdraw.header": "Levantar {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "O cartão está bloqueado e não foi ativado", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "Até {percentage}", "cardWidget.startEarning": "Começar a ganhar", "cardWithdraw.amount": "Montante a levantar", "cardWithdraw.header": "Levantar do cartão", "cardWithdraw.selectWithdrawWallet.title": "Escolhe a carteira{br}para onde levantar", "cardWithdraw.success.cta": "<PERSON><PERSON><PERSON>", "cardWithdraw.success.subtitle": "Por seguran<PERSON>, todos os levantamentos do cartão Gnosis Pay demoram 3 minutos a ser processados", "cardWithdraw.success.title": "Esta alteração demorará 3 minutos", "card_top_up_trx.send": "Enviar", "card_top_up_trx.to": "Para", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "Data de validade", "cards.card_number": "Número do cartão", "cards.choose-wallet.no-active-accounts": "Não tens nenhuma carteira ativa", "cards.copied_card_number": "Número do cartão copiado", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Excede o limite diário", "cards.transactions.decline_reason.incorrect_pin": "PIN incorreto", "cards.transactions.decline_reason.incorrect_security_code": "Código de segurança incorreto", "cards.transactions.decline_reason.invalid_amount": "<PERSON><PERSON>", "cards.transactions.decline_reason.low_balance": "<PERSON><PERSON> insuficiente", "cards.transactions.decline_reason.other": "Recusado", "cards.transactions.decline_reason.pin_tries_exceeded": "Tentativas de PIN excedidas", "cards.transactions.status.refund": "Reembolso", "cards.transactions.status.reversal": "<PERSON><PERSON><PERSON>", "cashback-deposit.trx.title": "Depositar em Cashback", "cashback-estimate.text": "Esta é uma estimativa e não um pagamento garantido. <PERSON>das as regras de cashback publicamente conhecidas são aplicadas, mas a Gnosis Pay pode excluir transações ao seu critério. Um gasto máximo de {amount} por semana qualifica-se para Cashback, mesmo que a estimativa para esta transação indique um valor total superior.", "cashback-estimate.text.fallback": "Esta é uma estimativa e não um pagamento garantido. Aplicam-se todas as regras de cashback conhecidas publicamente, mas a Gnosis Pay pode excluir transações ao seu critério.", "cashback-estimate.title": "Estimativa de cashback", "cashback-onbarding-tersm.subtitle": "Os dados das transações do teu Cartão serão partilhados com a Karpatkey, que é responsável pela distribuição das recompensas de Cashback. Ao clicar em aceitar, concordas com os <terms>Termos e Condições</terms>", "cashback-onbarding-tersm.title": "Termos de utilização e Privacidade", "cashback-tx-activity.retry": "Tentar novamente", "cashback-unconfirmed-payments-info.subtitle": "Os pagamentos qualificam-se para Cashback quando são liquidados com o comerciante. Até lá, são apresentados como pagamentos não confirmados. Os pagamentos não liquidados não se qualificam para cashback.", "cashback-unconfirmed-payments-info.title": "Pagamentos com cartão não confirmados", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "<PERSON><PERSON><PERSON><PERSON>", "cashback.activity.title": "Atividade recente", "cashback.activity.withdrawal": "Levantamento", "cashback.deposit": "Depositar", "cashback.deposit.amount.label": "Montante a depositar", "cashback.deposit.change": "{from} para {to}", "cashback.deposit.confirmation.subtitle": "As taxas de cashback são atualizadas uma vez por semana. Deposita agora para aumentar o Cashback da próxima semana.", "cashback.deposit.confirmation.title": "<PERSON><PERSON><PERSON><PERSON> a ganhar {percentage} em {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Troca tokens por {currency} na {network} Chain", "cashback.deposit.get.tokens.title": "Obter {currency} tokens", "cashback.deposit.header": "Depositar {currency}", "cashback.deposit.max_label": "Máx.: {amount}", "cashback.deposit.select-wallet.title": "Escolhe a carteira de onde depositar", "cashback.deposit.yourcashback": "O teu <PERSON>", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "Escolhe a carteira para{br}levantar", "cashback.transaction-details.network-label": "Rede", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "De", "cashback.transaction-details.top-row.label-rewards": "Período de cashback", "cashback.transaction-details.top-row.label-withdrawal": "Para", "cashback.transaction-details.transaction": "ID da transação", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Transações de cashback", "cashback.withdraw": "Levantar", "cashback.withdraw.confirmation.cashback_reduction": "O cashback desta semana, incluindo o que já gan<PERSON>, será reduzido de {before} para {after}", "cashback.withdraw.queued": "Levantamento em fila", "cashback.withdrawal.change": "{from} para {to}", "cashback.withdrawal.confirmation.subtitle": "Iniciar levantamento de {amount} com um atraso de 3 minutos. Isto irá reduzir o teu cashback para {after}.", "cashback.withdrawal.confirmation.title": "O cashback diminuirá se levantares GNO", "cashback.withdrawal.delayTransaction.title": "Iniciar le<PERSON><PERSON><PERSON> de GNO com{br} um atraso de 3 minutos", "cashback.withdrawal.withdraw": "Levantar", "cashback.withdrawal.yourcashback": "O teu <PERSON>", "celebration.aave": "Ganhos com Aave", "celebration.cashback.subtitle": "Entregue em {code}", "celebration.cashback.subtitleGNO": "{amount} gan<PERSON> da última vez", "celebration.chf": "Ganhos com Frankencoin", "celebration.lido": "Ganhos com Lido", "celebration.sky": "Ganhos com Sky", "celebration.title": "Cashback Total", "celebration.well_done.title": "<PERSON><PERSON> bem.", "change-withdrawal-account.add-new-account": "Adicionar outra conta bancária", "change-withdrawal-account.item.shortText": "{currency} Conta", "check-confirmation.approve.footer.for": "Para", "checkConfirmation.title": "Resultado da transação", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin e Ether", "collateral.ethereum": "Ethereum", "collateral.other": "Outros", "collateral.rwa": "Ativos do Mundo Real", "collateral.stablecoins": "Stablecoins (indexadas ao USD)", "collateral.us-t-bills": "T-Bills dos EUA", "confirm-bank-transfer-recipient.bullet-1": "Sem taxas em EUR digital", "confirm-bank-transfer-recipient.bullet-2": "<PERSON><PERSON><PERSON><PERSON><PERSON> para {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Partilhar os detalhes da conta Gnosis Pay com a Monerium, uma IME autorizada e regulada. <link><PERSON><PERSON> mais</link>", "confirm-bank-transfer-recipient.bullet-4": "Aceitar os <link>termos de serviço</link>", "confirm-bank-transfer-recipient.title": "Aceitar termos", "confirm-change-withdrawal-account.cancel": "<PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.confirm": "Confirmar", "confirm-change-withdrawal-account.saving": "A guardar", "confirm-change-withdrawal-account.subtitle": "Todos os levantamentos que enviares da Zeal serão recebidos por esta conta bancária.", "confirm-change-withdrawal-account.title": "Alterar banco de receção", "confirm-ramove-withdrawal-account.title": "Remover conta bancária", "confirm-remove-withdrawal-account.subtitle": "<PERSON><PERSON> detalhes desta conta bancária serão removidos da Zeal. Podes adicioná-la novamente a qualquer momento.", "confirmTransaction.finalNetworkFee": "Taxa de rede", "confirmTransaction.importKeys": "Importar chaves", "confirmTransaction.networkFee": "Taxa de rede", "confirmation.title": "Enviar {amount} para {recipient}", "conflicting-monerium-account.add-owner": "Adicionar como titular Gnosis Pay", "conflicting-monerium-account.create-wallet": "Criar uma nova smart wallet", "conflicting-monerium-account.disconnect-card": "Desligar cartão e reconectar com titular.", "conflicting-monerium-account.header": "{wallet} associada a outra conta Monerium", "conflicting-monerium-account.subtitle": "Muda a tua carteira titular Gnosis Pay", "connection.diconnected.got_it": "<PERSON><PERSON><PERSON>", "connection.diconnected.page1.subtitle": "A Zeal funciona onde a MetaMask funciona. Conecta-te como farias com a MetaMask.", "connection.diconnected.page1.title": "Como conectar com a Zeal?", "connection.diconnected.page2.subtitle": "Vais ver muitas opções. A Zeal pode ser uma delas. Se a Zeal não aparecer...", "connection.diconnected.page2.title": "Clica em Conectar Carteira", "connection.diconnected.page3.subtitle": "Vamos pedir uma conexão com a Zeal. <PERSON><PERSON><PERSON> ou Injected também devem funcionar. Experimenta.", "connection.diconnected.page3.title": "Escolhe <PERSON>M<PERSON>", "connectionSafetyCheck.tag.caution": "Cuidado", "connectionSafetyCheck.tag.danger": "Perigo", "connectionSafetyCheck.tag.passed": "<PERSON><PERSON><PERSON>", "connectionSafetyConfirmation.subtitle": "Tens a certeza de que queres continuar?", "connectionSafetyConfirmation.title": "Este site parece perigoso.", "connection_state.connect.cancel": "<PERSON><PERSON><PERSON>", "connection_state.connect.changeToMetamask": "Mudar para MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Mudar para MetaMask", "connection_state.connect.connect_button": "Conectar", "connection_state.connect.expanded.connected": "Conectado", "connection_state.connect.expanded.title": "Conectar", "connection_state.connect.safetyChecksLoading": "A verificar a segurança do site", "connection_state.connect.safetyChecksLoadingError": "Não foi possível concluir as verificações de segurança.", "connection_state.connected.expanded.disconnectButton": "Desconectar Zeal", "connection_state.connected.expanded.title": "Conectado", "copied-diagnostics": "Diagnósticos copiados", "copy-diagnostics": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.component.add_recipient_primary_text": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.country": "<PERSON><PERSON>", "counterparty.countryTitle": "País do destinatário", "counterparty.currency": "<PERSON><PERSON>", "counterparty.delete.success.title": "Removido", "counterparty.edit.success.title": "Alterações guardadas", "counterparty.errors.country_required": "<PERSON><PERSON>", "counterparty.errors.first_name.invalid": "O nome próprio deve ser mais longo", "counterparty.errors.last_name.invalid": "O apelido deve ser mais longo", "counterparty.first_name": "Nome próprio", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Enviar para o banco", "countrySelector.noCountryFound": "Nenhum país encontrado", "countrySelector.title": "E<PERSON><PERSON><PERSON> pa<PERSON>", "create-passkey.cta": "<PERSON><PERSON><PERSON> passkey", "create-passkey.extension.cta": "<PERSON><PERSON><PERSON><PERSON>", "create-passkey.footnote": "Desenvolvido por", "create-passkey.mobile.cta": "Configurar <PERSON>gu<PERSON>", "create-passkey.steps.enable-recovery": "Configurar recuperação na cloud", "create-passkey.steps.setup-biometrics": "Ativar segurança biométrica", "create-passkey.subtitle": "As passkeys são mais seguras do que as palavras-passe e são encriptadas no armazenamento na nuvem para uma recuperação fácil.", "create-passkey.title": "Proteger conta", "create-smart-wallet": "Criar Smart Wallet", "create-userop.progress.text": "A criar", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Continuar em Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Ir para Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "Já iniciaste o pedido do teu cartão. Volta ao site da Gnosis Pay para o concluir.", "create_recharge_preferences.card": "Cartão", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "Ganhar {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "<PERSON><PERSON>", "create_recharge_preferences.link_accounts_title": "Ligar contas", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "O teu cartão NÃO será recarregado automaticamente após cada pagamento.", "create_recharge_preferences.not_configured_title": "Ganhar e Gastar", "create_recharge_preferences.recharge_from_earn_accounts_description": "O teu cartão é recarregado automaticamente após cada pagamento a partir da tua conta Earn.", "create_recharge_preferences.subtitle": "por ano", "creating-account.loading": "A criar conta", "creating-gnosis-pay-account": "A criar conta", "currencies.bridge.select_routes.emptyState": "Não encontrámos rotas para esta Bridge", "currency.add_currency.add_token": "Adicionar token", "currency.add_currency.not_a_valid_address": "Este não é um endereço de token válido", "currency.add_currency.token_decimals_feild": "Decimais do token", "currency.add_currency.token_feild": "Endereço do token", "currency.add_currency.token_symbol_feild": "Símbolo do token", "currency.add_currency.update_token": "Atualizar token", "currency.add_custom.remove_token.cta": "Remover", "currency.add_custom.remove_token.header": "Remover token", "currency.add_custom.remove_token.subtitle": "O token será ocultado do teu portefólio, mas o saldo permanece na tua carteira.", "currency.add_custom.token_removed": "Token removido", "currency.add_custom.token_updated": "Token atualizado", "currency.balance_label": "Saldo: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "A tua transferência bancária transferiu com sucesso {fiat} para {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "Recebeste {crypto}", "currency.bankTransfer.deposit_status.success": "Recebido na tua carteira", "currency.bankTransfer.deposit_status.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.check_bank_account": "Verifica a tua conta bancária", "currency.bankTransfer.off_ramp.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "A enviar para o teu banco", "currency.bankTransfer.off_ramp.transferring_to_currency": "A transferir para {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Os fundos já devem ter chegado à tua conta bancária.", "currency.bankTransfer.withdrawal_status.success": "Enviado para o teu banco", "currency.bankTransfer.withdrawal_status.title": "Levantamento", "currency.bank_transfer.create_unblock_user.email": "Endereço de email", "currency.bank_transfer.create_unblock_user.email_invalid": "<PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.email_missing": "Obrigatório", "currency.bank_transfer.create_unblock_user.first_name": "Nome próprio", "currency.bank_transfer.create_unblock_user.first_name_missing": "Obrigatório", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "Apenas são permitidas letras, números, espaços e - . , & ( ) '.", "currency.bank_transfer.create_unblock_user.last_name": "Apelido", "currency.bank_transfer.create_unblock_user.last_name_missing": "Obrigatório", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "Apenas são permitidas letras, números, espaços e - . , & ( ) '.", "currency.bank_transfer.create_unblock_user.note": "Ao continuares, aceitas do Unblock (o nosso parceiro bancário) os <terms>Termos</terms> e a <policy>Política de Privacidade</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Escreve o teu nome exatamente como está na tua conta bancária", "currency.bank_transfer.create_unblock_user.title": "Associar conta bancária", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Número de conta", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "País do banco", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Moeda preferencial", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Sort code", "currency.bank_transfer.create_unblock_withdraw_account.success": "Conta configurada", "currency.bank_transfer.create_unblock_withdraw_account.title": "Associa a tua conta bancária", "currency.bank_transfer.residence-form.address-required": "Obrigatório", "currency.bank_transfer.residence-form.address-unsupported-char": "Apenas letras, números, espaços e , ; {apostrophe} - \\\\ permitidos.", "currency.bank_transfer.residence-form.city-required": "Obrigatório", "currency.bank_transfer.residence-form.city-unsupported-char": "Apenas letras, números, espaços e . , - & ( ) {apostrophe} permitidos.", "currency.bank_transfer.residence-form.postcode-invalid": "Código postal inválido", "currency.bank_transfer.residence-form.postcode-required": "Obrigatório", "currency.bank_transfer.validation.invalid.account_number": "Número de conta inválido", "currency.bank_transfer.validation.invalid.iban": "IBAN inválido", "currency.bank_transfer.validation.invalid.sort_code": "Sort code inválido", "currency.bridge.amount_label": "Montante para a Bridge", "currency.bridge.best_returns.subtitle": "<PERSON>ste fornecedor de Bridge dá-te o maior valor final, <PERSON>s incluídas.", "currency.bridge.best_returns_popup.title": "<PERSON><PERSON><PERSON> retornos", "currency.bridge.bridge_from": "De", "currency.bridge.bridge_gas_fee_loading_failed": "Tivemos problemas a carregar a taxa de rede", "currency.bridge.bridge_low_slippage": "Slippage muito baixo. Tenta aumentá-lo", "currency.bridge.bridge_provider": "Fornecedor da transferência", "currency.bridge.bridge_provider_loading_failed": "Tivemos problemas a carregar os fornecedores", "currency.bridge.bridge_settings": "Definições da <PERSON>", "currency.bridge.bridge_status.subtitle": "A usar {name}", "currency.bridge.bridge_status.title": "Bridge", "currency.bridge.bridge_to": "Para", "currency.bridge.fastest_route_popup.subtitle": "Este fornecedor de Bridge oferece-te a rota de transação mais rápida.", "currency.bridge.fastest_route_popup.title": "Rota mais rápida", "currency.bridge.from": "De", "currency.bridge.success": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bridge.title": "Bridge", "currency.bridge.to": "Para", "currency.bridge.topup": "<PERSON>eg<PERSON> {symbol}", "currency.bridge.withdrawal_status.title": "Levantamento", "currency.card.card_top_up_status.title": "Adicionar <PERSON> ao cartão", "currency.destination_amount": "Valor de destino", "currency.hide_currency.confirm.subtitle": "Oculta este token do teu portefólio. Podes voltar a mostrá-lo quando quiseres.", "currency.hide_currency.confirm.title": "Ocultar token", "currency.hide_currency.success.title": "Token oculto", "currency.label": "Etiqueta (Opcional)", "currency.last_name": "Apelido", "currency.max_loading": "Máx.:", "currency.swap.amount_to_swap": "Valor a trocar", "currency.swap.best_return": "Rota com melhor retorno", "currency.swap.destination_amount": "Valor de destino", "currency.swap.header": "Trocar", "currency.swap.max_label": "Saldo: {amount}", "currency.swap.provider.header": "Fornecedor de swap", "currency.swap.select_to_token": "Selecionar token", "currency.swap.swap_gas_fee_loading_failed": "Tivemos problemas a carregar a taxa de rede", "currency.swap.swap_provider_loading_failed": "Tivemos problemas a carregar os fornecedores", "currency.swap.swap_settings": "Definições de troca", "currency.swap.swap_slippage_too_low": "Slippage muito baixo. Tenta aumentá-lo", "currency.swaps_io_native_token_swap.subtitle": "A usar Swaps.IO", "currency.swaps_io_native_token_swap.title": "Enviar", "currency.withdrawal.amount_from": "De", "currency.withdrawal.amount_to": "Para", "currencySelector.title": "<PERSON><PERSON><PERSON><PERSON> moeda", "dApp.wallet-does-not-support-chain.subtitle": "A tua carteira parece não suportar {network}. Tenta ligar com outra carteira ou usa Zeal.", "dApp.wallet-does-not-support-chain.title": "Rede não suportada", "dapp.connection.manage.confirm.disconnect.all.cta": "<PERSON>r de tudo", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Tens a certeza que queres desconectar todas as ligações?", "dapp.connection.manage.confirm.disconnect.all.title": "Desconectar tudo", "dapp.connection.manage.connection_list.main.button.title": "Desconectar", "dapp.connection.manage.connection_list.no_connections": "Não tens aplicações conectadas", "dapp.connection.manage.connection_list.section.button.title": "Desconectar tudo", "dapp.connection.manage.connection_list.section.title": "Ativas", "dapp.connection.manage.connection_list.title": "Ligações", "dapp.connection.manage.disconnect.success.title": "Aplicações desconectadas", "dapp.metamask_mode.title": "<PERSON><PERSON>", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "<PERSON><PERSON>", "dc25-card-marketing-card.right.subtitle": "100 pessoas", "dc25-card-marketing-card.title": "<PERSON>s primeiros 100 a gastar 50 € ganham {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Não poderás realizar esta ação durante 3 min. Por razões de segurança, quaisquer alterações às definições do cartão ou levantamentos demoram 3 minutos a ser processados.", "delayQueueBusyBanner.processing-yout-action.title": "A processar a tua ação, aguarda por favor", "delayQueueBusyWidget.cardFrozen": "Cartão bloqueado", "delayQueueBusyWidget.processingAction": "A processar a tua ação", "delayQueueFailedBanner.action-incomplete.get-support": "Obter suporte", "delayQueueFailedBanner.action-incomplete.subtitle": "<PERSON><PERSON><PERSON><PERSON>, algo correu mal com o teu levantamento ou atualização de definições. Por favor, contacta o suporte no Discord.", "delayQueueFailedBanner.action-incomplete.title": "Ação incompleta", "delayQueueFailedWidget.actionIncomplete.title": "Ação do cartão incompleta", "delayQueueFailedWidget.cardFrozen.subtitle": "Cartão bloqueado", "delayQueueFailedWidget.contactSupport": "Contactar o suporte", "delay_queue_busy.subtitle": "Por razões de segurança, as alterações às definições do cartão ou levantamentos demoram 3 minutos a ser processados. Durante este período, o teu cartão fica congelado.", "delay_queue_busy.title": "A tua ação está a ser processada", "delay_queue_failed.contact_support": "Contactar", "delay_queue_failed.subtitle": "<PERSON><PERSON><PERSON><PERSON>, algo correu mal com o teu levantamento ou atualização de definições. Por favor, contacta o suporte no Discord.", "delay_queue_failed.title": "Contactar suporte", "deploy-earn-form-smart-wallet.in-progress.title": "A preparar o Earn", "deposit": "Depositar", "disconnect-card-popup.cancel": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.disconnect": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.subtitle": "Isto irá remover o teu Cartão da app Zeal. A tua carteira continuará ligada ao teu cartão na app Gnosis Pay. Podes voltar a ligar o teu Cartão a qualquer momento.", "disconnect-card-popup.title": "<PERSON><PERSON><PERSON>", "distance.long.days": "{count} dias", "distance.long.hours": "{count} horas", "distance.long.minutes": "{count} minutos", "distance.long.months": "{count} meses", "distance.long.seconds": "{count} segundos", "distance.long.years": "{count} anos", "distance.short.days": "{count} d", "distance.short.hours": "{count} h", "distance.short.minutes": "{count} min", "distance.short.months": "{count} m", "distance.short.seconds": "{count} seg", "distance.short.years": "{count} a", "duration.short.days": "{count}d", "duration.short.hours": "{count}h", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn-deposit-view.into": "Para", "earn-deposit-view.to": "Para", "earn-deposit.swap.transfer-provider": "Fornecedor de transferência", "earn-taker-investment-details.accrued-realtime": "Acumulado em tempo real", "earn-taker-investment-details.asset-class": "Classe de ativos", "earn-taker-investment-details.asset-coverage-ratio": "Rácio de cobertura de ativos", "earn-taker-investment-details.asset-reserve": "Reserva de ativos", "earn-taker-investment-details.base_currency.label": "Moeda de base", "earn-taker-investment-details.chf.description": "Ganha juros no teu CHF ao depositar zCHF na Frankencoin - um mercado monetário digital de confiança. Os juros são gerados por empréstimos de baixo risco e com excesso de garantias na Frankencoin e pagos em tempo real. Os teus fundos ficam seguros numa subconta protegida que só tu controlas.", "earn-taker-investment-details.chf.description.with_address_link": "Ganha juros no teu CHF ao depositar zCHF na Frankencoin - um mercado monetário digital de confiança. Os juros são gerados por empréstimos de baixo risco e com excesso de garantias na Frankencoin e pagos em tempo real. Os teus fundos ficam seguros numa subconta protegida <link>(copiar 0x)</link> que só tu controlas.", "earn-taker-investment-details.chf.label": "Franco Suíço Digital", "earn-taker-investment-details.collateral-composition": "Composição da garantia", "earn-taker-investment-details.depositor-obligations": "Obrigações dos depositantes", "earn-taker-investment-details.eure.description": "Ganha juros nos teus euros ao depositar EURe na Aave, um mercado monetário digital de confiança. A EURe é uma stablecoin em euros totalmente regulada, emitida pela Monerium e com uma garantia de 1:1 em contas protegidas. Os juros são gerados a partir de empréstimos de baixo risco e com excesso de garantias na Aave, sendo pagos em tempo real. Os teus fundos permanecem numa subconta segura que só tu controlas.", "earn-taker-investment-details.eure.description.with_address_link": "Ganha juros nos teus euros ao depositar EURe na Aave, um mercado monetário digital de confiança. A EURe é uma stablecoin em euros totalmente regulada, emitida pela Monerium e com uma garantia de 1:1 em contas protegidas. Os juros são gerados a partir de empréstimos de baixo risco e com excesso de garantias na Aave, sendo pagos em tempo real. Os teus fundos permanecem numa subconta segura <link>(copiar 0x)</link> que só tu controlas.", "earn-taker-investment-details.eure.label": "Euro Digital (EURe)", "earn-taker-investment-details.faq": "FAQ", "earn-taker-investment-details.fixed-income": "Rendimento fixo", "earn-taker-investment-details.issuer": "Emissor", "earn-taker-investment-details.key-facts": "Factos principais", "earn-taker-investment-details.liquidity": "Liquidez", "earn-taker-investment-details.operator": "Operador de mercado", "earn-taker-investment-details.projected-yield": "Rendimento anual projetado", "earn-taker-investment-details.see-other-faq": "<PERSON><PERSON> as outras FAQ", "earn-taker-investment-details.see-realtime": "Ver dados em tempo real", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Rendimento dos últimos 12 meses", "earn-taker-investment-details.total-collateral": "Garantia Total", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "Fornecimento total de ZCHF", "earn-taker-investment-details.total_deposits": "Total de depósitos Aave", "earn-taker-investment-details.usd.description": "A Sky é um mercado monetário digital que oferece rendimentos estáveis denominados em dólares americanos, provenientes de T-Bills dos EUA de curta duração e de empréstimos com excesso de garantias — sem a volatilidade das criptomoedas, com acesso aos fundos 24/7 e com uma garantia transparente na blockchain.", "earn-taker-investment-details.usd.description.with_address_link": "A Sky é um mercado monetário digital que oferece rendimentos estáveis denominados em dólares americanos, provenientes de T-Bills dos EUA de curta duração e de empréstimos com excesso de garantias — sem a volatilidade das criptomoedas, com acesso aos fundos 24/7 e com uma garantia transparente na blockchain. Os investimentos estão numa subconta <link>(copiar 0x)</link> controlada por ti.", "earn-taker-investment-details.usd.ftx-difference": "Qual é a diferença em relação à FTX, <PERSON><PERSON><PERSON>, <PERSON><PERSON>i ou Luna?", "earn-taker-investment-details.usd.high-returns": "Como é que os retornos podem ser tão altos, especialmente em comparação com os bancos tradicionais?", "earn-taker-investment-details.usd.how-is-backed": "Como é garantido o Sky USD e o que acontece ao meu dinheiro se a Zeal falir?", "earn-taker-investment-details.usd.income-sources": "Fontes de rendimento 2024", "earn-taker-investment-details.usd.insurance": "Os meus fundos estão seguros ou garantidos por alguma entidade (como o FDIC ou semelhante)?", "earn-taker-investment-details.usd.label": "Dólar Americano Digital", "earn-taker-investment-details.usd.lose-principal": "Posso realmente perder o meu capital e em que circunstâncias?", "earn-taker-investment-details.variable-rate": "Empréstimo com taxa variável", "earn-taker-investment-details.withdraw-anytime": "Levantar a qualquer momento", "earn-taker-investment-details.yield": "Rendimento", "earn-withdrawal-view.approve.for": "Para", "earn-withdrawal-view.approve.into": "Para", "earn-withdrawal-view.swap.into": "Para", "earn-withdrawal-view.withdraw.to": "Para", "earn.add_another_asset.title": "Seleciona o ativo para Earn", "earn.add_asset": "Adicionar ativo", "earn.asset_view.title": "<PERSON><PERSON><PERSON>", "earn.base-currency-popup.text": "A moeda de base é a forma como os teus depósitos, rendimentos e transações são avaliados e registados. Se depositares numa moeda diferente (como EUR em USD), os teus fundos são imediatamente convertidos para a moeda de base utilizando as taxas de câmbio atuais. Após a conversão, o teu saldo permanece estável na moeda de base, mas futuros levantamentos podem implicar novamente conversões de moeda.", "earn.base-currency-popup.title": "Moeda de base", "earn.card-recharge.disabled.list-item.title": "Recarga automática desativada", "earn.card-recharge.enabled.list-item.title": "Recarga automática ativada", "earn.choose_wallet_to_deposit.title": "Depositar de", "earn.config.currency.eth": "Ganha Ethereum", "earn.config.currency.on_chain_address_subtitle": "Endereço na blockchain", "earn.config.currency.us_dollars": "Configurar transferências bancárias", "earn.configured_widget.current_apy.title": "APY atual", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} Anual", "earn.confirm.currency.cta": "Depositar", "earn.currency.eth": "Earn <PERSON>", "earn.deploy.status.title": "<PERSON><PERSON><PERSON> conta <PERSON>n", "earn.deploy.status.title_with_taker": "Criar {title} conta Earn", "earn.deposit": "Depositar", "earn.deposit.amount_to_deposit": "Montante a depositar", "earn.deposit.deposit": "Depositar", "earn.deposit.enter_amount": "Introduz o montante", "earn.deposit.no_routes_found": "Nenhuma rota encontrada", "earn.deposit.not_enough_balance": "<PERSON><PERSON> insuficiente", "earn.deposit.select-currency.title": "Seleciona o token a depositar", "earn.deposit.select_account.title": "Selecionar conta Earn", "earn.desposit_form.title": "Depositar em Earn", "earn.earn_deposit.status.title": "Depósito em Earn", "earn.earn_deposit.trx.title": "Depositar em Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "Levanta fundos a qualquer momento", "earn.earn_withdraw.status.title": "Levantar da conta Earn", "earn.earn_withdraw.trx.title.approval": "<PERSON><PERSON><PERSON>", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Levantar para {asset}", "earn.earn_withdraw.trx.title.withdrawal": "<PERSON><PERSON> de <PERSON>n", "earn.recharge.cta": "Guardar alterações", "earn.recharge.earn_not_configured.enable_some_account.error": "Ativa a conta", "earn.recharge.earn_not_configured.enter_amount.error": "Insere um montante", "earn.recharge.select_taker.header": "Carrega o cartão por ordem de", "earn.recharge_card_tag.on": "ativa", "earn.recharge_card_tag.recharge": "Recarga", "earn.recharge_card_tag.recharge_not_configured": "Recarga automática", "earn.recharge_card_tag.recharge_off": "Recarga desligada", "earn.recharge_card_tag.recharged": "<PERSON><PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharging": "A recarregar", "earn.recharge_configured.disable.trx.title": "Desativar Recarga Automática", "earn.recharge_configured.trx.disclaimer": "Quando usas o teu cartão, é criado um leilão Cowswap para comprar o mesmo valor do teu pagamento usando os teus ativos Earn. Este processo de leilão normalmente garante-te a melhor taxa de mercado, mas tem em atenção que a taxa onchain pode ser diferente das taxas de câmbio do mundo real.", "earn.recharge_configured.trx.subtitle": "Após cada pagamento, será adicionado dinheiro automaticamente da(s) tua(s) conta(s) Earn para manter o saldo do teu cartão em {value}", "earn.recharge_configured.trx.title": "Definir Recarga Automática para {value}", "earn.recharge_configured.updated.trx.title": "Guardar Definições de Recarga", "earn.risk-banner.subtitle": "Este é um produto de auto-custódia sem proteção regulamentar contra perdas.", "earn.risk-banner.title": "Comp<PERSON><PERSON> os riscos", "earn.set_recharge.status.title": "Definir recarregamento automático", "earn.setup_reacharge.input.disable.label": "Desativar", "earn.setup_reacharge.input.label": "Saldo alvo do cartão", "earn.setup_reacharge_form.title": "O Carregamento Automático mantém o teu{br}cartão com o mesmo saldo", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Mantém wstETH (ETH em stake) na Gnosis Chain e empresta via Lido.", "earn.taker-bulletlist.point_1": "Ganha {apyValue} anualmente. Os retornos variam com o mercado.", "earn.taker-bulletlist.point_3": "A Zeal não cobra taxas.", "earn.taker-historical-returns": "Re<PERSON><PERSON> his<PERSON>", "earn.taker-historical-returns.chf": "Crescimento de CHF para USD", "earn.taker-investment-tile.apy.perYear": "por ano", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "Depositar", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "A ganhar com Aave na Gnosis Chain", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Ganhos com Frankencoin na Gnosis Chain", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "A ganhar com Lido na Gnosis Chain", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "A ganhar com Maker na Gnosis Chain", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "Eth earn", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH Earn", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "Títulos do Tesouro dos EUA (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Embora extremamente raro, é teoricamente possível. Os teus fundos estão protegidos por uma gestão de risco rigorosa e por uma elevada colateralização. O pior cenário realista implicaria condições de mercado sem precedentes, como várias stablecoins a perderem a sua indexação em simultâneo — algo que nunca aconteceu.", "earn.usd.can-I-lose-my-principal-popup.title": "Posso realmente perder o meu capital e em que circunstâncias?", "earn.usd.ftx-difference-popup.text": "A Sky é fundamentalmente diferente. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> FTX, <PERSON><PERSON><PERSON>, BlockFi ou Luna — que dependiam fortemente de custódia centralizada, gestão de ativos opaca e posições alavancadas de risco — o Sky USD utiliza contratos inteligentes descentralizados, auditados e transparentes, e mantém total transparência na blockchain. Tu manténs o controlo total e privado, reduzindo significativamente os riscos de contraparte associados a falhas centralizadas.", "earn.usd.ftx-difference-popup.title": "Qual é a diferença em relação à FTX, <PERSON><PERSON><PERSON>, <PERSON><PERSON>i ou Luna?", "earn.usd.high-returns-popup.text": "O Sky USD gera rendimentos principalmente através de protocolos de finanças descentralizadas (DeFi), que automatizam empréstimos peer-to-peer e o fornecimento de liquidez, eliminando os custos operacionais e intermediários da banca tradicional. Estas eficiências, combinadas com controlos de risco robustos, permitem retornos significativamente mais elevados em comparação com os bancos tradicionais.", "earn.usd.high-returns-popup.title": "Como é que os retornos podem ser tão altos, especialmente em comparação com os bancos tradicionais?", "earn.usd.how-is-sky-backed-popup.text": "O Sky USD é totalmente garantido e com excesso de garantias por uma combinação de ativos digitais mantidos em contratos inteligentes seguros e ativos do mundo real, como T-Bills dos EUA. As reservas podem ser auditadas em tempo real na blockchain, mesmo dentro da Zeal, proporcionando transparência e segurança. No caso improvável de a Zeal encerrar, os teus ativos permanecem seguros na blockchain, totalmente sob o teu controlo e acessíveis através de outras carteiras compatíveis.", "earn.usd.how-is-sky-backed-popup.title": "Como é garantido o Sky USD e o que acontece ao meu dinheiro se a Zeal falir?", "earn.usd.insurance-popup.text": "Os fundos do Sky USD não têm seguro FDIC nem são apoiados por garantias governamentais tradicionais, porque é uma conta baseada em ativos digitais, não uma conta bancária convencional. Em vez disso, a Sky gere toda a mitigação de riscos através de contratos inteligentes auditados e protocolos DeFi cuidadosamente selecionados, garantindo que os ativos permanecem seguros e transparentes.", "earn.usd.insurance-popup.title": "Os meus fundos estão seguros ou garantidos por alguma entidade (como o FDIC ou semelhante)?", "earn.usd.lending-operations-popup.text": "O Sky USD gera rendimento ao emprestar stablecoins através de mercados de empréstimos descentralizados como Morpho e Spark. As tuas stablecoins são emprestadas a mutuários que depositam garantias significativamente maiores — como ETH ou BTC — do que o valor do empréstimo. Esta abordagem, chamada de excesso de garantias, garante que há sempre garantias suficientes para cobrir os empréstimos, reduzindo consideravelmente o risco. Os juros cobrados e as taxas de liquidação ocasionais pagas pelos mutuários proporcionam retornos fiáveis, transparentes e seguros.", "earn.usd.lending-operations-popup.title": "Operações de empréstimo", "earn.usd.market-making-operations-popup.text": "O Sky USD obtém rendimento adicional ao participar em exchanges descentralizadas (AMMs) como a Curve ou a Uniswap. Ao fornecer liquidez — colocando as tuas stablecoins em pools que facilitam a negociação de criptomoedas — o Sky USD captura taxas geradas a partir das negociações. Estas pools de liquidez são selecionadas cuidadosamente para minimizar a volatilidade, utilizando principalmente pares de stablecoin para stablecoin para reduzir significativamente riscos como a perda impermanente, mantendo os teus ativos seguros e acessíveis.", "earn.usd.market-making-operations-popup.title": "Operações de Market Making", "earn.usd.treasury-operations-popup.text": "O Sky USD gera rendimento estável e consistente através de investimentos estratégicos de tesouraria. Parte dos teus depósitos em stablecoins é alocada a ativos do mundo real seguros e de baixo risco — principalmente títulos do governo de curto prazo e instrumentos de crédito de alta segurança. Esta abordagem, semelhante à banca tradicional, garante um rendimento previsível e fiável. Os teus ativos permanecem seguros, líquidos e geridos de forma transparente.", "earn.usd.treasury-operations-popup.title": "Operações de tesouraria", "earn.view_earn.card_rechard_off": "Des<PERSON><PERSON>", "earn.view_earn.card_rechard_on": "Ligado", "earn.view_earn.card_recharge": "Recarga do Cartão", "earn.view_earn.total_balance_label": "A render {percentage} por ano", "earn.view_earn.total_earnings_label": "<PERSON><PERSON><PERSON> to<PERSON>", "earn.withdraw": "Levantar", "earn.withdraw.amount_to_withdraw": "Montante a levantar", "earn.withdraw.enter_amount": "<PERSON><PERSON><PERSON><PERSON><PERSON> montante", "earn.withdraw.loading": "A carregar", "earn.withdraw.no_routes_found": "Nenhuma rota encontrada", "earn.withdraw.not_enough_balance": "<PERSON><PERSON> insuficiente", "earn.withdraw.select-currency.title": "Selecionar token", "earn.withdraw.select_to_token": "Selecionar token", "earn.withdraw.withdraw": "Levantar", "earn.withdraw_form.title": "<PERSON><PERSON> de <PERSON>n", "earnings-view.earnings": "<PERSON><PERSON><PERSON> to<PERSON>", "edit-account-owners.add-owner.add-wallet": "Ad<PERSON>onar <PERSON>", "edit-account-owners.add-owner.add_wallet": "<PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.title": "Adicionar Titular do Cartão", "edit-account-owners.card-owners": "Titulares do cartão", "edit-account-owners.external-wallet": "Carteira externa", "editBankRecipient.title": "<PERSON><PERSON>", "editNetwork.addCustomRPC": "Adicionar nó RPC personalizado", "editNetwork.cannot_verify.subtitle": "O nó RPC não responde. Verifica o URL.", "editNetwork.cannot_verify.title": "Não conseguimos verificar o nó RPC", "editNetwork.cannot_verify.try_again": "<PERSON><PERSON>r", "editNetwork.customRPCNode": "Nó RPC personalizado", "editNetwork.defaultRPC": "RPC predefinido", "editNetwork.networkRPC": "RPC da rede", "editNetwork.rpc_url.cannot_be_empty": "Obrigatório", "editNetwork.rpc_url.not_a_valid_https_url": "Tem de ser um URL HTTP(S) válido", "editNetwork.safetyWarning.subtitle": "A Zeal não pode garantir a privacidade, fiabilidade e segurança de RPCs personalizados. Tens a certeza de que queres usar um nó RPC personalizado?", "editNetwork.safetyWarning.title": "RPCs personalizados podem não ser seguros", "editNetwork.zealRPCNode": "Nó RPC Zeal", "editNetworkRpc.headerTitle": "Nó RPC personalizado", "editNetworkRpc.rpcNodeUrl": "URL do nó RPC", "editing-locked.modal.description": "Ao contrário das transações de aprovação, as permissões não permitem editar o limite de gastos ou o prazo de validade. Certifica-te de que confias na dApp antes de submeter uma permissão.", "editing-locked.modal.title": "Edição bloqueada", "enable-recharge-for-smart-wallet.enabling-recharge.title": "A ativar o recarregamento", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Recarregamento ativado", "enterCardnumber": "Inserir número do cartão", "error.connectivity_error.subtitle": "Verifica a tua ligação à internet e tenta novamente.", "error.connectivity_error.title": "Sem ligação à internet", "error.decrypt_incorrect_password.title": "Palavra-passe incorreta", "error.encrypted_object_invalid_format.title": "Dados corrompidos", "error.failed_to_fetch_google_auth_token.title": "Não conseguimos obter acesso", "error.list.item.cta.action": "Tentar novamente", "error.trezor_action_cancelled.title": "Transação rejeitada", "error.trezor_device_used_elsewhere.title": "O dispositivo está a ser usado noutra sessão", "error.trezor_method_cancelled.title": "Não foi possível sincronizar a Trezor", "error.trezor_permissions_not_granted.title": "Não foi possível sincronizar a Trezor", "error.trezor_pin_cancelled.title": "Não foi possível sincronizar a Trezor", "error.trezor_popup_closed.title": "Não foi possível sincronizar a Trezor", "error.unblock_account_number_and_sort_code_mismatch": "Número de conta e sort code não correspondem", "error.unblock_can_not_change_details_after_kyc": "Não podes alterar os dados após o KYC", "error.unblock_hard_kyc_failure": "Estado de KYC inesperado", "error.unblock_invalid_faster_payment_configuration.title": "Este banco não suporta Faster Payments", "error.unblock_invalid_iban": "IBAN inválido", "error.unblock_session_expired.title": "Sessão Unblock expirou", "error.unblock_user_with_address_already_exists.title": "Conta já configurada para o endereço", "error.unblock_user_with_such_email_already_exists.title": "Utilizador com este e-mail já existe", "error.unknown_error.error_message": "Mensagem de erro: ", "error.unknown_error.subtitle": "Lamentamos. Se precisares de ajuda urgente, contacta o suporte e partilha os detalhes abaixo.", "error.unknown_error.title": "Erro de sistema", "eth-cost-warning-modal.subtitle": "As Smart Wallets funcionam na Ethereum, mas as taxas são muito altas e recomendamos VIVAMENTE o uso de outras redes.", "eth-cost-warning-modal.title": "Evita a Ethereum - as taxas de rede são altas", "exchange.form.button.chain_unsupported": "Rede não suportada", "exchange.form.button.refreshing": "A atualizar", "exchange.form.error.asset_not_supported.button": "Seleciona outro ativo", "exchange.form.error.asset_not_supported.description": "A Bridge não suporta este ativo.", "exchange.form.error.asset_not_supported.title": "Ativo não suportado", "exchange.form.error.bridge_quote_timeout.button": "Seleciona outro ativo", "exchange.form.error.bridge_quote_timeout.description": "Tenta outro par de tokens", "exchange.form.error.bridge_quote_timeout.title": "Nenhuma troca encontrada", "exchange.form.error.different_receiver_not_supported.button": "Remove o destinatário alternativo", "exchange.form.error.different_receiver_not_supported.description": "Esta bolsa não suporta o envio para outro endereço.", "exchange.form.error.different_receiver_not_supported.title": "O endereço de envio e receção tem de ser o mesmo", "exchange.form.error.insufficient_input_amount.button": "Aumenta o montante", "exchange.form.error.insufficient_liquidity.button": "Reduz o montante", "exchange.form.error.insufficient_liquidity.description": "A Bridge não tem ativos suficientes. Tenta um valor menor.", "exchange.form.error.insufficient_liquidity.title": "Valor muito alto", "exchange.form.error.max_amount_exceeded.button": "Reduz o montante", "exchange.form.error.max_amount_exceeded.description": "O montante máximo foi excedido.", "exchange.form.error.max_amount_exceeded.title": "<PERSON>ante demasiado elevado", "exchange.form.error.min_amount_not_met.button": "Aumenta o montante", "exchange.form.error.min_amount_not_met.description": "O montante mínimo de troca para este token não foi atingido.", "exchange.form.error.min_amount_not_met.description_with_amount": "O montante mínimo de troca <PERSON> {amount}.", "exchange.form.error.min_amount_not_met.title": "Montante demasiado baixo", "exchange.form.error.min_amount_not_met.title_increase": "Aumenta o montante", "exchange.form.error.no_routes_found.button": "Seleciona outro ativo", "exchange.form.error.no_routes_found.description": "Não há rota de troca disponível para esta combinação de token/rede.", "exchange.form.error.no_routes_found.title": "Nenhuma troca disponível", "exchange.form.error.not_enough_balance.button": "Reduz o montante", "exchange.form.error.not_enough_balance.description": "Não tens saldo suficiente deste ativo para a transação.", "exchange.form.error.not_enough_balance.title": "<PERSON><PERSON> insuficiente", "exchange.form.error.slippage_passed_is_too_low.button": "Aumenta a derrapagem", "exchange.form.error.slippage_passed_is_too_low.description": "O slippage permitido é muito baixo para este ativo.", "exchange.form.error.slippage_passed_is_too_low.title": "Slippage muito baixo", "exchange.form.error.socket_internal_error.button": "Tenta novamente mais tarde", "exchange.form.error.socket_internal_error.description": "O parceiro de Bridge está com problemas. Tenta novamente mais tarde.", "exchange.form.error.socket_internal_error.title": "Erro no parceiro de Bridge", "exchange.form.error.stargatev2_requires_fee_in_native": "Adicionar {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Adiciona {amount} para concluir a transação", "exchange.form.error.stargatev2_requires_fee_in_native.title": "Precisas de mais {symbol}", "expiration-info.modal.description": "O prazo de validade é o tempo durante o qual uma aplicação pode utilizar os teus tokens. Esgotado o tempo, perdem o acesso até que digas o contrário. Para te manteres seguro, define um prazo de validade curto.", "expiration-info.modal.title": "O que é o prazo de validade?", "expiration-time.high.modal.text": "Os prazos de validade devem ser curtos e baseados no tempo que realmente precisas. Prazos longos são arriscados, dando aos burlões mais hipóteses de usarem indevidamente os teus tokens.", "expiration-time.high.modal.title": "Prazo de validade longo", "failed.transaction.content": "É provável que a transação falhe", "fee.unknown": "Desconhecido", "feedback-request.leave-message": "Deixar uma mensagem", "feedback-request.not-now": "<PERSON><PERSON><PERSON> n<PERSON>", "feedback-request.title": "Obrigado! Como podemos melhorar o Zeal?", "float.input.period": "Separador decimal", "gnosis-activate-card.info-popup.subtitle": "Na primeira transação, deves inserir o cartão e o PIN. Depois, os pagamentos contactless funcionarão.", "gnosis-activate-card.info-popup.title": "Primeiro pagamento requer Chip e PIN", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "Insere o número do teu cartão para o ativar.", "gnosis-activate-card.title": "Número do cartão", "gnosis-pay-re-kyc-widget.btn-text": "Verificar", "gnosis-pay-re-kyc-widget.title.not-started": "Verifica a tua identidade", "gnosis-pay.login.cta": "Ligar conta existente", "gnosis-pay.login.title": "<PERSON><PERSON> tens uma conta Gnosis Pay", "gnosis-signup.confirm.subtitle": "Procura um e-mail da Gnosis Pay, pode estar na tua pasta de spam.", "gnosis-signup.confirm.title": "Não recebeste o e-mail de verificação?", "gnosis-signup.continue": "<PERSON><PERSON><PERSON><PERSON>", "gnosis-signup.dont_link_accounts": "Não ligar contas", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "Introduz <EMAIL>", "gnosis-signup.enter-email.title": "Introduzir e-mail", "gnosis-signup.title": "Li e concordo com os <linkGnosisTNC>T&C da Gnosis Pay</linkGnosisTNC> <monovateTerms>Termos do Titular do Cartão</monovateTerms> e os <linkMonerium>T&C da Monerium</linkMonerium>.", "gnosis-signup.verify-email.title": "Verificar e-mail", "gnosis.confirm.subtitle": "Não recebeste o código? Verifica se o teu número de telemóvel está correto", "gnosis.confirm.title": "Código enviado para {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Verificar", "gnosisPayAccountStatus.success.title": "Cartão importado", "gnosisPayIsNotAvailableInThisCountry.title": "A GnosisPay ainda não está disponível no teu país", "gnosisPayNoActiveCardsFound.title": "Sem cartões ativos", "gnosis_pay_card_delay_relay_not_empty_error.title": "A tua transação não pôde ser processada agora. Tenta novamente mais tarde", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "Cartão não disponível", "gnosiskyc.modal.approved.activate-free-card": "Ativar cartão gratuito", "gnosiskyc.modal.approved.button-text": "Depositar da conta", "gnosiskyc.modal.approved.title": "Os teus dados de conta foram criados", "gnosiskyc.modal.failed.close": "<PERSON><PERSON><PERSON>", "gnosiskyc.modal.failed.title": "Gnosis Pay não pode criar a tua conta.", "gnosiskyc.modal.in-progress.title": "Verificação de ID pode levar 24h. Aguarda.", "goToSettingsPopup.settings": "Definições", "goToSettingsPopup.title": "Ativa as notificações nas definições.", "google_file.error.failed_to_fetch_auth_token.button_title": "Tentar novamente", "google_file.error.failed_to_fetch_auth_token.subtitle": "Para nos permitires usar o teu Ficheiro de Recuperação, concede acesso na tua nuvem pessoal.", "google_file.error.failed_to_fetch_auth_token.title": "Não conseguimos obter acesso", "hidden_tokens.widget.emptyState": "Nenhum token oculto", "how_to_connect_to_metamask.got_it": "OK, percebi", "how_to_connect_to_metamask.story.subtitle": "Alterna facilmente entre a Zeal e outras carteiras a qualquer momento.", "how_to_connect_to_metamask.story.title": "A Zeal funciona com outras carteiras", "how_to_connect_to_metamask.why_switch": "Porquê alternar entre a Zeal e outras carteiras?", "how_to_connect_to_metamask.why_switch.description": "Independentemente da carteira que escolheres, terás sempre as Verificações de Segurança da Zeal a proteger-te de sites e transações maliciosas.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "Sabemos que é difícil dar o passo e começar a usar uma carteira nova. Por isso, facilitámos o uso da Zeal com a tua carteira atual. Alterna quando quiseres.", "import-bank-transfer-owner.banner.title": "Importa a nova carteira para continuares com as transferências.", "import-bank-transfer-owner.title": "Importar carteira para usar transferências bancárias neste dispositivo", "import_gnosispay_wallet.add-another-card-owner.footnote": "Importa a chave/frase do titular do cartão", "import_gnosispay_wallet.primaryText": "Importar carteira Gnosis Pay", "injected-wallet": "Carteira do navegador", "intercom.getHelp": "Obter ajuda", "invalid_iban.got_it": "<PERSON><PERSON><PERSON>", "invalid_iban.subtitle": "O IBAN introduzido não é válido. Verifica se os dados foram introduzidos corretamente e tenta novamente.", "invalid_iban.title": "IBAN inválido", "keypad-0": "Tecla do teclado 0", "keypad-1": "Tecla do teclado 1", "keypad-2": "Tecla do teclado 2", "keypad-3": "Tecla do teclado 3", "keypad-4": "Tecla do teclado 4", "keypad-5": "Tecla do teclado 5", "keypad-6": "Tecla do teclado 6", "keypad-7": "Tecla do teclado 7", "keypad-8": "Tecla do teclado 8", "keypad-9": "Tecla do teclado 9", "keypad.biometric-button": "Botão biométrico do teclado", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Frase Secreta protegida 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "Ver frase", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Guarda uma cópia offline segura da tua Frase Secreta para poderes recuperar os teus ativos mais tarde", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "Não tentes adivinhar a palavra", "keystore.write_secret_phrase.before_you_begin.first_point": "Comp<PERSON><PERSON> que qualquer pessoa com a minha Frase Secreta pode transferir os meus ativos", "keystore.write_secret_phrase.before_you_begin.second_point": "Sou responsável por manter a minha Frase Secreta secreta e segura", "keystore.write_secret_phrase.before_you_begin.subtitle": "Por favor, lê e aceita os seguintes pontos:", "keystore.write_secret_phrase.before_you_begin.third_point": "Estou num local privado, sem pessoas ou câmaras à minha volta", "keystore.write_secret_phrase.before_you_begin.title": "<PERSON><PERSON> começa<PERSON>", "keystore.write_secret_phrase.secret_phrase_test.title": "Qual é a palavra {count} da tua Frase Secreta?", "keystore.write_secret_phrase.test_ps.lets_do_it": "Vamos a isso", "keystore.write_secret_phrase.test_ps.subtitle": "Vais precisar da tua Frase Secreta para restaurar a tua conta neste ou noutros dispositivos. Vamos verificar se a tua Frase Secreta está escrita corretamente.", "keystore.write_secret_phrase.test_ps.subtitle2": "<PERSON><PERSON>s pedir-te {count} palavras da tua frase.", "keystore.write_secret_phrase.test_ps.title": "Testar Recuperação de Conta", "kyc.modal.approved.button-text": "Fazer transferência", "kyc.modal.approved.subtitle": "A tua verificação está concluída. Agora podes fazer transferências bancárias ilimitadas.", "kyc.modal.approved.title": "Transferências bancárias desbloqueadas", "kyc.modal.continue-with-partner.button-text": "<PERSON><PERSON><PERSON><PERSON>", "kyc.modal.continue-with-partner.subtitle": "Vamos agora reencaminhar-te para o nosso parceiro para recolher a tua documentação e concluir o pedido de verificação.", "kyc.modal.continue-with-partner.title": "Continuar com o nosso parceiro", "kyc.modal.failed.unblock.subtitle": "O Unblock não aprovou a tua verificação de identidade e não te pode fornecer serviços de transferência bancária", "kyc.modal.failed.unblock.title": "Pedido ao Unblock não aprovado", "kyc.modal.paused.button-text": "<PERSON><PERSON><PERSON><PERSON>", "kyc.modal.paused.subtitle": "Parece que algumas das tuas informações estão incorretas. Tenta novamente e verifica os teus detalhes antes de submeter.", "kyc.modal.paused.title": "Os teus detalhes parecem incorretos", "kyc.modal.pending.button-text": "<PERSON><PERSON><PERSON>", "kyc.modal.pending.subtitle": "A verificação demora normalmente menos de 10 minutos, mas por vezes pode demorar um pouco mais.", "kyc.modal.pending.title": "<PERSON><PERSON>-te-emos atualiza<PERSON>", "kyc.modal.required.cta": "Iniciar verificação", "kyc.modal.required.subtitle": "Atingiste o limite de transações. Verifica a tua identidade para continuares. Isto demora apenas alguns minutos e requer alguns dados pessoais e documentação.", "kyc.modal.required.title": "Verificação de identidade necessária", "kyc.submitted": "Candidatura submetida", "kyc.submitted_short": "Enviado", "kyc_status.completed_status": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc_status.failed_status": "Fal<PERSON>", "kyc_status.paused_status": "Em revisão", "kyc_status.subtitle": "Transferências bancárias", "kyc_status.subtitle.wrong_details": "Dados incorretos", "kyc_status.subtitle_in_progress": "Em curso", "kyc_status.title": "A verificar a identidade", "label.close": "<PERSON><PERSON><PERSON>", "label.saving": "A guardar...", "labels.this-month": "<PERSON><PERSON> mês", "labels.today": "Hoje", "labels.yesterday": "Ontem", "language.selector.title": "Idioma", "ledger.account_loaded.imported": "Importada", "ledger.add.success.title": "Ledger ligada com sucesso 🎉", "ledger.connect.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.connect.step1": "Liga a Ledger ao teu dispositivo", "ledger.connect.step2": "Abre a app Ethereum na Ledger", "ledger.connect.step3": "<PERSON><PERSON><PERSON>, sincroniza o teu Ledger 👇", "ledger.connect.subtitle": "Segue estes passos para importares as tuas carteiras Ledger para a Zeal", "ledger.connect.title": "Ligar a Ledger à Zeal", "ledger.error.ledger_is_locked.subtitle": "Desbloqueia a Ledger e abre a app Ethereum", "ledger.error.ledger_is_locked.title": "Ledger está bloqueada", "ledger.error.ledger_not_connected.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.error.ledger_not_connected.subtitle": "Liga a tua carteira de hardware ao teu dispositivo e abre a app Ethereum", "ledger.error.ledger_not_connected.title": "Ledger não está ligada", "ledger.error.ledger_running_non_eth_app.title": "App Ethereum não está aberta", "ledger.error.user_trx_denied_by_user.action": "<PERSON><PERSON><PERSON>", "ledger.error.user_trx_denied_by_user.subtitle": "Rejeitaste a transação na tua carteira de hardware", "ledger.error.user_trx_denied_by_user.title": "Transação rejeitada", "ledger.hd_path.bip44.subtitle": "ex: Metamask, Trezor", "ledger.hd_path.bip44.title": "Padrão BIP44", "ledger.hd_path.ledger_live.subtitle": "Padrão", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "<PERSON><PERSON>", "ledger.hd_path.phantom.subtitle": "ex: Phantom", "ledger.select.hd_path.subtitle": "Os caminhos HD organizam as contas da tua carteira de hardware, como um índice num livro.", "ledger.select.hd_path.title": "Selecionar Caminho HD", "ledger.select_account.import_wallets_count": "{count,plural,=0{Nenhuma selecionada} one{Importar conta} other{Importar {count} contas}}", "ledger.select_account.path_settings": "Definições de caminho", "ledger.select_account.subtitle": "<PERSON><PERSON> as carteiras que esperavas? <PERSON><PERSON> mudar as definições de caminho", "ledger.select_account.subtitle.group_header": "Carteiras", "ledger.select_account.title": "Importar carteiras Ledger", "legend.lending-operations": "Operações de empréstimo", "legend.market_making-operations": "Operações de Market Making", "legend.treasury-operations": "Operações de tesouraria", "link-existing-monerium-account-sign.button": "Ligar Zeal", "link-existing-monerium-account-sign.subtitle": "Já tens uma conta Monerium.", "link-existing-monerium-account-sign.title": "Liga o Zeal à tua conta Monerium.", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Já tens conta Monerium. Conclui na app.", "link-existing-monerium-account.title": "Vai ao Monerium para ligar a tua conta", "loading.pin": "A carregar PIN...", "lockScreen.passwordIncorrectMessage": "Palavra-passe incorreta", "lockScreen.passwordRequiredMessage": "Palavra-passe necessária", "lockScreen.unlock.header": "Desb<PERSON>que<PERSON>", "lockScreen.unlock.subheader": "Usa a tua palavra-passe para desbloquear a Zeal", "mainTabs.activity.label": "Atividade", "mainTabs.browse.label": "Explorar", "mainTabs.browse.title": "Explorar", "mainTabs.card.label": "Cartão", "mainTabs.portfolio.label": "Portfólio", "mainTabs.rewards.label": "Recompensas", "makeSpendable.cta": "Aplicar fundos", "makeSpendable.holdAsCash": "<PERSON><PERSON> como din<PERSON><PERSON>", "makeSpendable.shortText": "A render {apy} por ano", "makeSpendable.title": "{amount} recebidos", "merchantCategory.agriculture": "Agricultura", "merchantCategory.alcohol": "Álcool", "merchantCategory.antiques": "Antiguidades", "merchantCategory.appliances": "Eletrodomésticos", "merchantCategory.artGalleries": "Galerias de arte", "merchantCategory.autoRepair": "Reparação automóvel", "merchantCategory.autoRepairService": "Serviço de reparação automóvel", "merchantCategory.beautyFitnessSpas": "Beleza, fitness e spas", "merchantCategory.beautyPersonalCare": "Beleza e cuidados pessoais", "merchantCategory.billiard": "Bilhar", "merchantCategory.books": "<PERSON><PERSON>", "merchantCategory.bowling": "Bowling", "merchantCategory.businessProfessionalServices": "Serviços empresariais e profissionais", "merchantCategory.carRental": "<PERSON><PERSON><PERSON> de <PERSON>", "merchantCategory.carWash": "<PERSON><PERSON><PERSON>", "merchantCategory.cars": "<PERSON><PERSON>", "merchantCategory.casino": "Casino", "merchantCategory.casinoGambling": "Casino e jogos de azar", "merchantCategory.cellular": "Telemóvel", "merchantCategory.charity": "Caridade", "merchantCategory.childcare": "Cuidados infantis", "merchantCategory.cigarette": "Tabaco", "merchantCategory.cinema": "Cinema", "merchantCategory.cinemaEvents": "Cinema e eventos", "merchantCategory.cleaning": "Limpeza", "merchantCategory.cleaningMaintenance": "Limpeza e manutenção", "merchantCategory.clothes": "<PERSON><PERSON><PERSON>", "merchantCategory.clothingServices": "Serviços de vestuário", "merchantCategory.communicationServices": "Serviços de comunicação", "merchantCategory.construction": "Construção", "merchantCategory.cosmetics": "Cosmé<PERSON><PERSON>", "merchantCategory.craftsArtSupplies": "Artesanato e material de arte", "merchantCategory.datingServices": "Serviços de encontros", "merchantCategory.delivery": "Entregas", "merchantCategory.dentist": "<PERSON><PERSON>", "merchantCategory.departmentStores": "Grandes armazéns", "merchantCategory.directMarketingSubscription": "Marketing direto e subscrição", "merchantCategory.discountStores": "Lojas de desconto", "merchantCategory.drugs": "Farmácia", "merchantCategory.dutyFree": "Duty Free", "merchantCategory.education": "Educação", "merchantCategory.electricity": "Eletricidade", "merchantCategory.electronics": "Eletrónica", "merchantCategory.emergencyServices": "Serviços de emergência", "merchantCategory.equipmentRental": "Aluguer de equipamento", "merchantCategory.evCharging": "Carregamento de VE", "merchantCategory.financialInstitutions": "Instituições financeiras", "merchantCategory.financialProfessionalServices": "Serviços financeiros e profissionais", "merchantCategory.finesPenalties": "Multas e penalizações", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "<PERSON><PERSON>", "merchantCategory.flowers": "Flores", "merchantCategory.flowersGarden": "Flores e jardim", "merchantCategory.food": "<PERSON><PERSON><PERSON>", "merchantCategory.freight": "Frete", "merchantCategory.fuel": "Combustível", "merchantCategory.funeralServices": "Serviços funerários", "merchantCategory.furniture": "Mobiliá<PERSON>", "merchantCategory.games": "Jogos", "merchantCategory.gas": "Combustível", "merchantCategory.generalMerchandiseRetail": "Mercadoria geral e retalho", "merchantCategory.gifts": "Presentes", "merchantCategory.government": "Governo", "merchantCategory.governmentServices": "Serviços governamentais", "merchantCategory.hardware": "<PERSON><PERSON><PERSON>", "merchantCategory.healthMedicine": "Saúde e medicina", "merchantCategory.homeImprovement": "Bricolage", "merchantCategory.homeServices": "Serviços ao domicílio", "merchantCategory.hotel": "Hotel", "merchantCategory.housing": "Habitação", "merchantCategory.insurance": "<PERSON><PERSON><PERSON>", "merchantCategory.internet": "Internet", "merchantCategory.kids": "Crian<PERSON><PERSON>", "merchantCategory.laundry": "Lavandaria", "merchantCategory.laundryCleaningServices": "Lavandaria e serviços de limpeza", "merchantCategory.legalGovernmentFees": "Taxas legais e governamentais", "merchantCategory.luxuries": "Luxo", "merchantCategory.luxuriesCollectibles": "Luxos e colecionáveis", "merchantCategory.magazines": "Revistas", "merchantCategory.magazinesNews": "Revistas e notícias", "merchantCategory.marketplaces": "Mercados", "merchantCategory.media": "Media", "merchantCategory.medicine": "Medicina", "merchantCategory.mobileHomes": "Casas móveis", "merchantCategory.moneyTransferCrypto": "Transferência de dinheiro e cripto", "merchantCategory.musicRelated": "Relacionado com música", "merchantCategory.musicalInstruments": "Instrumentos musicais", "merchantCategory.optics": "Ótica", "merchantCategory.organizationsClubs": "Organizações e clubes", "merchantCategory.other": "Outros", "merchantCategory.parking": "Estacionamento", "merchantCategory.pawnShops": "Lojas de penhores", "merchantCategory.pets": "Animais de estimação", "merchantCategory.photoServicesSupplies": "Serviços e materiais de fotografia", "merchantCategory.postalServices": "Serviços postais", "merchantCategory.professionalServicesOther": "Serviços profissionais (outros)", "merchantCategory.publicTransport": "Transportes públicos", "merchantCategory.purchases": "Compras", "merchantCategory.purchasesMiscServices": "Compras e serviços diversos", "merchantCategory.recreationServices": "Serviços de recreação", "merchantCategory.religiousGoods": "<PERSON><PERSON><PERSON> religiosos", "merchantCategory.secondhandRetail": "Retalho em segunda mão", "merchantCategory.shoeHatRepair": "Reparação de sapatos e chapéus", "merchantCategory.shoeRepair": "Reparação de sapatos", "merchantCategory.softwareApps": "Software e apps", "merchantCategory.specializedRepairs": "Reparações especializadas", "merchantCategory.sport": "Desporto", "merchantCategory.sportingGoods": "Artigos de desporto", "merchantCategory.sportingGoodsRecreation": "Artigos desportivos e recreação", "merchantCategory.sportsClubsFields": "Clubes e campos desportivos", "merchantCategory.stationaryPrinting": "Papelaria e impressão", "merchantCategory.stationery": "Papelaria", "merchantCategory.storage": "Armazenamento", "merchantCategory.taxes": "Impostos", "merchantCategory.taxi": "Táxi", "merchantCategory.telecomEquipment": "Equipamento de telecomunicações", "merchantCategory.telephony": "Telefonia", "merchantCategory.tobacco": "Tabaco", "merchantCategory.tollRoad": "Portagem", "merchantCategory.tourismAttractionsAmusement": "Turismo, atrações e diversão", "merchantCategory.towing": "Reboque", "merchantCategory.toys": "Brinquedos", "merchantCategory.toysHobbies": "Brinquedos e passatempos", "merchantCategory.trafficFine": "Multa de trânsito", "merchantCategory.train": "Comboio", "merchantCategory.travelAgency": "Agência de viagens", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, rádio e streaming", "merchantCategory.utilities": "Serviços públicos", "merchantCategory.waterTransport": "Transporte aquático", "merchantCategory.wholesaleClubs": "Clubes grossistas", "metaMask.subtitle": "Ativa o Modo MetaMask para redirecionar todas as ligações MetaMask para a Zeal. Clicar em MetaMask nas dApps irá conectar à Zeal.", "metaMask.title": "<PERSON>ão consegues conectar com a Zeal?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Abre a tua app do banco", "monerium-bank-deposit.buttet-point.receive-crypto": "Recebe EUR digital", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Envia {fiatCurrencyCode} para a tua conta", "monerium-bank-deposit.deposit-account-country": "<PERSON><PERSON>", "monerium-bank-deposit.header": "{fullName} conta pessoal", "monerium-bank-details.account-name": "Nome da conta", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT copiado", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT copiado", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN copiado", "monerium-bank-details.to-wallet": "Para a carteira", "monerium-bank-details.transfer-fee": "Custo da transferência", "monerium-bank-transfer.enable-card.bullet-1": "Concluir a verificação de identidade", "monerium-bank-transfer.enable-card.bullet-2": "Obter detalhes da conta pessoal", "monerium-bank-transfer.enable-card.bullet-3": "Depositar da conta bancária", "monerium-card-delay-relay.success.cta": "<PERSON><PERSON><PERSON>", "monerium-card-delay-relay.success.subtitle": "<PERSON><PERSON>, alterações demoram 3 min.", "monerium-card-delay-relay.success.title": "Volta em 3 min para continuar.", "monerium-deposit.account-details-info-popup.bullet-point-1": "Qualquer {fiatCurrencyCode} que enviares para esta conta será automaticamente convertido em {cryptoCurrencyCode} tokens na {cryptoCurrencyChain} Chain e enviado para a tua carteira", "monerium-deposit.account-details-info-popup.bullet-point-2": "ENVIA APENAS {fiatCurrencyCode} ({fiatCurrencySymbol}) para a tua conta", "monerium-deposit.account-details-info-popup.title": "Os teus dados da conta", "monerium.check_order_status.sending": "A enviar", "monerium.not-eligible.cta": "Voltar", "monerium.not-eligible.subtitle": "A Monerium não te consegue abrir uma conta. Seleciona um fornecedor alternativo.", "monerium.not-eligible.title": "Tenta outro fornecedor", "monerium.setup-card.cancel": "<PERSON><PERSON><PERSON>", "monerium.setup-card.continue": "<PERSON><PERSON><PERSON><PERSON>", "monerium.setup-card.create_account": "C<PERSON><PERSON> conta", "monerium.setup-card.login": "Entrar na Gnosis Pay", "monerium.setup-card.subtitle": "Cria ou inicia sessão na tua conta Gnosis Pay para ativar depósitos bancários instantâneos.", "monerium.setup-card.subtitle_personal_account": "Obtém a tua conta pessoal com a Gnosis Pay em minutos:", "monerium.setup-card.title": "Ativar depósitos bancários", "moneriumDepositSuccess.goToWallet": "<PERSON>r para a carteira", "moneriumDepositSuccess.title": "{symbol} recebido", "moneriumInfo.fees": "Tens 0% de taxas", "moneriumInfo.registration": "A Monerium é autorizada e regulada como Instituição de Moeda Eletrónica ao abrigo da Lei Islandesa de Moeda Eletrónica N.º 17/2013 <link><PERSON>ber mais</link>", "moneriumInfo.selfCustody": "O dinheiro digital que recebes é teu e só tu tens controlo sobre ele. Mais ninguém terá acesso ao teu ativo.", "moneriumWithdrawRejected.supportText": "Transferência falhou. Tenta de novo ou <link>contacta o apoio.</link>", "moneriumWithdrawRejected.title": "Transferência revertida", "moneriumWithdrawRejected.tryAgain": "Tentar de novo", "moneriumWithdrawSuccess.supportText": "Pode demorar 24h até o teu{br}destinatário receber os fundos", "moneriumWithdrawSuccess.title": "Enviado", "monerium_enable_banner.text": "Ativar transferências bancárias", "monerium_error_address_re_link_required.title": "A carteira precisa de ser novamente associada à Monerium", "monerium_error_duplicate_order.title": "Ordem duplicada", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "Franco Suíço", "mt-pelerin-fork.options.chf.short": "Instantâneo e gratuito com Mt Pelerin", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Instantâneo e gratuito com Monerium", "mt-pelerin-fork.title": "O que queres depositar?", "mtPelerinProviderInfo.fees": "Pagas 0% de taxas", "mtPelerinProviderInfo.registration": "A Mt Pelerin Group Ltd é afiliada da SO-FIT, um organismo de autorregulação reconhecido pela Autoridade Supervisora do Mercado Financeiro Suíço (FINMA) ao abrigo da Lei de Combate ao Branqueamento de Capitais. <link>Saber mais</link>", "mtPelerinProviderInfo.selfCustody": "O dinheiro digital que recebes está na tua carteira privada e mais ninguém terá controlo sobre os teus ativos", "network-fee-widget.title": "Taxas", "network.edit.verifying_rpc": "A verificar RPC", "network.editRpc.predefined_network_info.subtitle": "Tal como uma VPN, a Zeal usa RPCs que impedem o rastreio dos teus dados pessoais.{br}{br}Os RPCs predefinidos da Zeal são fornecedores de RPC fiáveis e testados.", "network.editRpc.predefined_network_info.title": "RPC de privacidade da Zeal", "network.filter.update_rpc_success": "Nó RPC guardado", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "<PERSON><PERSON> as redes", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "Adicionar redes", "networks.filter.add_modal.chain_list.subtitle": "Adiciona qualquer rede EVM", "networks.filter.add_modal.chain_list.title": "<PERSON>r para a Chainlist", "networks.filter.add_modal.dapp_tip.subtitle": "Nas tuas dApps favoritas, basta mudares para a rede EVM que queres usar e a Zeal perguntará se a queres adicionar à tua carteira.", "networks.filter.add_modal.dapp_tip.title": "Ou adiciona uma rede a partir de qualquer dApp", "networks.filter.add_networks.subtitle": "<PERSON>das as redes EVM são suportadas", "networks.filter.add_networks.title": "Adicionar redes", "networks.filter.add_test_networks.title": "<PERSON><PERSON><PERSON>r testnets", "networks.filter.tab.netwokrs": "Redes", "networks.filter.testnets.title": "Testnets", "nft.widget.emptystate": "Nenhum colecionável na carteira", "nft_collection.change_account_picture.subtitle": "Tens a certeza de que queres atualizar a tua foto de perfil?", "nft_collection.change_account_picture.title": "Atualizar foto de perfil para NFT", "nfts.allNfts.pricingPopup.description": "Os preços dos colecionáveis baseiam-se no preço da transação mais recente.", "nfts.allNfts.pricingPopup.title": "Preços dos colecionáveis", "no-passkeys-found.modal.cta": "<PERSON><PERSON><PERSON>", "no-passkeys-found.modal.subtitle": "Não detetámos passkeys da Zeal neste dispositivo. Inicia sessão na conta da nuvem que usaste para criar a tua smart wallet.", "no-passkeys-found.modal.title": "<PERSON><PERSON><PERSON><PERSON> passkey encontrada", "notValidEmail.title": "Endereço de e-mail inválido", "notValidPhone.title": "Este não é um número de telemóvel válido", "notification-settings.title": "Definições de notificação", "notification-settings.toggles.active-wallets": "Carteiras ativas", "notification-settings.toggles.bank-transfers": "Transferências bancárias", "notification-settings.toggles.card-payments": "Pagamentos com cartão", "notification-settings.toggles.readonly-wallets": "Carteiras só de leitura", "ntft.groupHeader.text": "Colecionáveis", "on_ramp.crypto_completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "on_ramp.fiat_completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onboarding-widget.subtitle.card_created_from_order.left": "Cartão Visa", "onboarding-widget.subtitle.card_created_from_order.right": "Ativar cart<PERSON>", "onboarding-widget.subtitle.card_order_ready.left": "Cartão Visa físico", "onboarding-widget.subtitle.default": "Transferências bancárias e cartão Visa", "onboarding-widget.title.card-order-in-progress": "Continuar encomenda do cartão", "onboarding-widget.title.card_created_from_order": "O cartão foi enviado", "onboarding-widget.title.kyc_approved": "Transferências e cartão prontos", "onboarding-widget.title.kyc_failed": "Não é possível criar conta", "onboarding-widget.title.kyc_not_started": "Contin<PERSON>r configura<PERSON>", "onboarding-widget.title.kyc_started_documents_requested": "Concluir verificação", "onboarding-widget.title.kyc_started_resubmission_requested": "Repetir verificação", "onboarding-widget.title.kyc_started_verification_in_progress": "A verificar identidade", "onboarding.loginOrCreateAccount.amountOfAssets": "+10 mil M em ativos", "onboarding.loginOrCreateAccount.cards.subtitle": "Disponível apenas em algumas regiões. Ao continuares, aceitas os nossos <Terms>Termos</Terms> e a nossa <PrivacyPolicy>Política de Privacidade</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Cartão Visa com elevados{br}retornos e sem taxas", "onboarding.loginOrCreateAccount.createAccount": "<PERSON><PERSON><PERSON>", "onboarding.loginOrCreateAccount.earn.subtitle": "Os retornos variam; capital em risco. Ao continuares, aceitas os nossos <Terms>Termos</Terms> e a nossa <PrivacyPolicy>Política de Privacidade</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "Ganhos de {percent} por ano{br}Com a confiança de {currencySymbol}+5 mil M", "onboarding.loginOrCreateAccount.earningPerYear": "G<PERSON><PERSON> de {percent}{br}por ano", "onboarding.loginOrCreateAccount.login": "<PERSON><PERSON><PERSON>", "onboarding.loginOrCreateAccount.trading.subtitle": "Capital em risco. Ao continuares, aceitas os nossos <Terms>Termos</Terms> e a nossa <PrivacyPolicy>Política de Privacidade</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Investe em tudo,{br}de BTC a S&P", "onboarding.loginOrCreateAccount.trustedBy": "Mercados monetários digitais{br}Com a confiança de {assets}", "onboarding.wallet_stories.close": "<PERSON><PERSON><PERSON>", "onboarding.wallet_stories.previous": "Anterior", "order-earn-deposit-bridge.deposit": "<PERSON><PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.into": "Para", "otpIncorrectMessage": "O código de confirmação está incorreto", "passkey-creation-not-possible.modal.close": "<PERSON><PERSON><PERSON>", "passkey-creation-not-possible.modal.subtitle": "Não conseguimos criar uma passkey para a tua carteira. Certifica-te que o teu dispositivo suporta passkeys e tenta novamente. <link>Contactar suporte</link> se o problema persistir.", "passkey-creation-not-possible.modal.title": "Não é possível criar passkey", "passkey-not-supported-in-mobile-browser.modal.cta": "<PERSON><PERSON><PERSON><PERSON>", "passkey-not-supported-in-mobile-browser.modal.subtitle": "As Smart Wallets não são suportadas em navegadores móveis.", "passkey-not-supported-in-mobile-browser.modal.title": "Descarrega a app Zeal para continuar", "passkey-recovery.recovering.deploy-signer.loading-text": "A verificar passkey", "passkey-recovery.recovering.loading-text": "A recuperar carteira", "passkey-recovery.recovering.signer-not-found.subtitle": "Não conseguimos associar a tua passkey a uma carteira ativa. Se tiveres fundos na tua carteira, contacta a equipa da Zeal para obteres ajuda.", "passkey-recovery.recovering.signer-not-found.title": "<PERSON><PERSON><PERSON><PERSON> carteira encontrada", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "Tentar com outra passkey", "passkey-recovery.select-passkey.banner.subtitle": "Verifica se estás na conta certa do dispositivo. As passkeys são específicas da conta.", "passkey-recovery.select-passkey.banner.title": "<PERSON>ão vês a passkey da tua carteira?", "passkey-recovery.select-passkey.continue": "Selecionar passkey", "passkey-recovery.select-passkey.subtitle": "Seleciona a passkey associada à tua carteira para recuperares o acesso.", "passkey-recovery.select-passkey.title": "Selecionar Passkey", "passkey-story_1.subtitle": "Com uma Smart Wallet, podes pagar as taxas de rede com a maioria dos tokens, sem te preocupares com isso.", "passkey-story_1.title": "Paga as taxas de rede com a maioria dos tokens", "passkey-story_2.subtitle": "Baseada nos smart contracts líderes da Safe, que protegem mais de 100 mil milhões de dólares em mais de 20 milhões de carteiras.", "passkey-story_2.title": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "passkey-story_3.subtitle": "As Smart Wallets funcionam nas principais redes compatíveis com Ethereum. Verifica as redes suportadas antes de enviares ativos.", "passkey-story_3.title": "Suporte para as principais redes EVM", "password.add.header": "<PERSON><PERSON>r palavra-passe", "password.add.includeLowerAndUppercase": "Letras maiúsculas e minúsculas", "password.add.includesNumberOrSpecialChar": "Um número ou símbolo", "password.add.info.subtitle": "Não enviamos a tua palavra-passe para os nossos servidores nem fazemos backup por ti", "password.add.info.t_and_c": "Ao continuares, aceitas os nossos <Terms>Termos</Terms> e a nossa <PrivacyPolicy>Política de Privacidade</PrivacyPolicy>", "password.add.info.title": "A tua palavra-passe fica neste dispositivo", "password.add.inputPlaceholder": "<PERSON><PERSON>r palavra-passe", "password.add.shouldContainsMinCharsCheck": "Mais de 10 caracteres", "password.add.subheader": "Vais usar a tua palavra-passe para desbloquear a Zeal", "password.add.success.title": "Palavra-passe criada 🔥", "password.confirm.header": "Confirmar palavra-passe", "password.confirm.passwordDidNotMatch": "As palavras-passe têm de coincidir", "password.confirm.subheader": "Introduz a tua palavra-passe mais uma vez", "password.create_pin.subtitle": "Este código de acesso bloqueia a app Zeal", "password.create_pin.title": "Cria o teu código de acesso", "password.enter_pin.title": "Introduzir código de acesso", "password.incorrectPin": "Código de acesso incorreto", "password.pin_is_not_same": "O código de acesso não coincide", "password.placeholder.enter": "Introduzir palavra-passe", "password.placeholder.reenter": "Reintroduzir palavra-passe", "password.re_enter_pin.subtitle": "Introduz o mesmo código de acesso novamente", "password.re_enter_pin.title": "Confirmar <PERSON><PERSON><PERSON>", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "Pendente", "pending-send.details.pending": "Pendente", "pending-send.details.processing": "A processar", "permit-info.modal.description": "As Permissões são pedidos que, se assinados, permitem que as apps movimentem os teus tokens em teu nome, por exemplo, para efetuar uma troca.{br}As Permissões são semelhantes às Aprovações, mas a sua assinatura não te custa taxas de rede.", "permit-info.modal.title": "O que são as Permissõ<PERSON>?", "permit.edit-expiration": "Editar {currency} validade", "permit.edit-limit": "Editar {currency} o limite de gastos", "permit.edit-modal.expiresIn": "<PERSON><PERSON>ra em…", "permit.expiration-warning": "{currency} aviso de validade", "permit.expiration.info": "{currency} informação sobre a validade", "permit.expiration.never": "Nunca", "permit.spend-limit.info": "{currency} informação sobre o limite de gastos", "permit.spend-limit.warning": "{currency} aviso de limite de gastos", "phoneNumber.title": "número de telemóvel", "physicalCardOrderFlow.cardOrdered": "Cartão pedido", "physicalCardOrderFlow.city": "Cidade", "physicalCardOrderFlow.orderCard": "<PERSON><PERSON><PERSON>", "physicalCardOrderFlow.postcode": "Código postal", "physicalCardOrderFlow.shippingAddress.subtitle": "Para onde o teu cartão será enviado", "physicalCardOrderFlow.shippingAddress.title": "Morada de envio", "physicalCardOrderFlow.street": "<PERSON><PERSON>", "placeholderDapps.1inch.description": "Troca pelas melhores rotas", "placeholderDapps.aave.description": "Empresta e pede tokens emprestados", "placeholderDapps.bungee.description": "Faz bridge de redes pelas melhores rotas", "placeholderDapps.compound.description": "Empresta e pede tokens emprestados", "placeholderDapps.cowswap.description": "Troca com as melhores taxas na Gnosis", "placeholderDapps.gnosis-pay.description": "Gere o teu cartão Gnosis Pay", "placeholderDapps.jumper.description": "Faz bridge de redes pelas melhores rotas", "placeholderDapps.lido.description": "Faz stake de ETH para mais ETH", "placeholderDapps.monerium.description": "eMoney e transferências bancárias", "placeholderDapps.odos.description": "Troca pelas melhores rotas", "placeholderDapps.stargate.description": "Faz bridge ou stake para <14% APY", "placeholderDapps.uniswap.description": "Uma das corretoras mais populares", "pleaseAllowNotifications.cardPayments": "Pagamentos com cartão", "pleaseAllowNotifications.customiseInSettings": "Personalizar nas definições", "pleaseAllowNotifications.enable": "Ativar", "pleaseAllowNotifications.forWalletActivity": "Para atividade da carteira", "pleaseAllowNotifications.title": "Recebe notificações da carteira", "pleaseAllowNotifications.whenReceivingAssets": "Ao receberes ativos", "portfolio.quick-actions.add_funds": "<PERSON><PERSON><PERSON><PERSON>", "portfolio.quick-actions.buy": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.deposit": "Depositar", "portfolio.quick-actions.send": "Enviar", "portfolio.view.lastRefreshed": "Atualizado {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Carrega o teu AVAX de testnet", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Ir para a Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "Carrega o teu BNB de testnet", "portfolio.view.topupTestNet.BscTestnet.secondary": "Ir para a Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Carrega o teu SepETH de testnet", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Ir para a Sepolia Faucet", "portfolio.view.topupTestNet.FantomTestnet.primary": "Carrega o teu FTM de testnet", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Ir para a Faucet", "privateKeyConfirmation.banner.subtitle": "Quem tem a sua Chave Privada acede à sua carteira e fundos. Só burlões a pedem.", "privateKeyConfirmation.banner.title": "<PERSON><PERSON><PERSON><PERSON> os riscos", "privateKeyConfirmation.title": "NUNCA PARTILHES a tua Chave Privada com ninguém", "rating-request.not-now": "<PERSON><PERSON><PERSON> n<PERSON>", "rating-request.title": "Recomendarias o Zeal?", "receive_funds.address-text": "Este é o teu endereço de carteira exclusivo. Podes partilhá-lo em segurança.", "receive_funds.copy_address": "<PERSON><PERSON>r morada carteira", "receive_funds.network-warning.eoa.subtitle": "<link>Ver lista de redes padrão</link>. Os ativos enviados em redes não-EVM serão perdidos.", "receive_funds.network-warning.eoa.title": "<PERSON><PERSON> as redes baseadas em Ethereum são suportadas", "receive_funds.network-warning.scw.subtitle": "<link>Ver redes suportadas</link>. Os ativos enviados noutras redes serão perdidos.", "receive_funds.network-warning.scw.title": "Importante: usa apenas redes suportadas", "receive_funds.scan_qr_code": "Ler um código QR", "receiving.in.days": "A receber em {days}d", "receiving.this.week": "A receber esta semana", "receiving.today": "A receber hoje", "reference.error.maximum_number_of_characters_exceeded": "<PERSON><PERSON><PERSON><PERSON> caracteres", "referral-code.placeholder": "Cola o link de convite", "referral-code.subtitle": "Clica no link ou cola-o abaixo.", "referral-code.title": "Um amigo enviou-te {bReward}?", "rekyc.verification_deadline.subtitle": "Conclui a verificação em {daysUntil} dias para continuares a usar o teu cartão.", "rekyc.verification_required.subtitle": "Conclui a verificação para continuares a usar o teu cartão.", "reminder.fund": "💸 Adiciona fundos e ganha 6%.", "reminder.onboarding": "🏁 Conclui a configuração e ganha 6%.", "remove-owner.confirmation.subtitle": "Por seguran<PERSON>, as alterações demoram 3 minutos a processar. Durante este tempo, o teu cartão será bloqueado e não poderás fazer pagamentos.", "remove-owner.confirmation.title": "Cartão bloqueado por 3 min para atualizar definições", "restore-smart-wallet.wallet-recovered": "Carteira recuperada", "rewardClaimCelebration.claimedTitle": "Recompensas já reclamadas", "rewardClaimCelebration.subtitle": "Por convidar amigos", "rewardClaimCelebration.title": "Ganháste", "rewards-warning.subtitle": "Remover esta conta irá pausar o acesso a quaisquer recompensas associadas. Podes restaurar a conta a qualquer momento para as reivindicares.", "rewards-warning.title": "Vais perder o acesso às tuas recompensas", "rewards.copiedInviteLink": "Link de convite copiado", "rewards.createAccount": "Copiar link de convite", "rewards.header.subtitle": "Enviamos {aReward} a ti e {bReward} ao teu amigo, quando ele gastar {bSpendLimitReward}.", "rewards.header.title": "Recebe {amountA}{br}Dá {amountB}", "rewards.sendInvite": "Enviar convite", "rewards.sendInviteTip": "Escolhe um amigo e nós damos-lhe {bAmount}", "route.fees": "Taxas {fees}", "routesNotFound.description": "A rota de troca para a combinação de redes {from}-{to} não está disponível.", "routesNotFound.title": "Nenhuma rota de troca disponível", "rpc.OrderBuySignMessage.subtitle": "A usar Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "A usar Swaps.IO", "rpc.addCustomNetwork.addNetwork": "<PERSON><PERSON><PERSON><PERSON>", "rpc.addCustomNetwork.chainId": "ID da cadeia", "rpc.addCustomNetwork.nativeToken": "Token nativo", "rpc.addCustomNetwork.networkName": "Nome da rede", "rpc.addCustomNetwork.operationDescription": "Permite que este site adicione uma rede à tua carteira. A Zeal não pode verificar a segurança de redes personalizadas, certifica-te de que compreendes os riscos.", "rpc.addCustomNetwork.rpcUrl": "URL do RPC", "rpc.addCustomNetwork.subtitle": "A usar {name}", "rpc.addCustomNetwork.title": "Adicionar rede", "rpc.send_token.network_not_supported.subtitle": "Estamos a trabalhar para permitir transações nesta rede. <PERSON><PERSON><PERSON> pela tua paciência 🙏", "rpc.send_token.network_not_supported.title": "Rede disponível em breve", "rpc.send_token.send_or_receive.settings": "Definições", "rpc.sign.accept": "Aceitar", "rpc.sign.cannot_parse_message.body": "Não foi possível descodificar esta mensagem. Aceita este pedido apenas se confiares nesta aplicação.{br}{br}As mensagens podem ser usadas para iniciar sessão numa aplicação, mas também podem dar o controlo dos teus tokens a aplicações.", "rpc.sign.cannot_parse_message.header": "Avançar com cuidado", "rpc.sign.import_private_key": "Importar chaves", "rpc.sign.subtitle": "Para {name}", "rpc.sign.title": "<PERSON><PERSON><PERSON>", "safe-creation.success.title": "<PERSON><PERSON>ira <PERSON>riad<PERSON>", "safe-safety-checks-popup.title": "Verificações de Segurança da Transação", "safetyChecksPopup.title": "Verificações de Segurança do Site", "scan_qr_code.description": "Lê o QR da carteira ou liga-te a uma App", "scan_qr_code.show_qr_code": "Mostrar o meu código QR", "scan_qr_code.tryAgain": "Tentar novamente", "scan_qr_code.unlockCamera": "Desbloquear câmara", "screen-lock-missing.modal.close": "<PERSON><PERSON><PERSON>", "screen-lock-missing.modal.subtitle": "O teu dispositivo requer um bloqueio de ecrã para usar passkeys. Configura um bloqueio de ecrã e tenta novamente.", "screen-lock-missing.modal.title": "Falta bloqueio de ecrã", "seedConfirmation.banner.subtitle": "Quem tem a sua Frase Secreta acede à sua carteira e fundos. Só burlões a pedem.", "seedConfirmation.title": "NUNCA PARTILHES a tua Frase Secreta com ninguém", "select-active-owner.subtitle": "Tens várias carteiras ligadas ao teu cartão. Seleciona uma para ligar à Zeal. Podes trocar a qualquer momento.", "select-active-owner.title": "Selecionar carteira", "select-card.title": "Selecionar cartão", "select-crypto-currency-title": "Selecionar token", "select-token.title": "Selecionar token", "selectEarnAccount.chf.description.steps": "· Levanta fundos 24/7, sem períodos de bloqueio {br}· Os juros acumulam a cada segundo {br}· Depósitos com proteção extra em <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} ao ano em CHF", "selectEarnAccount.eur.description.steps": "· Levanta fundos 24/7, sem bloqueios {br}· Os juros acumulam a cada segundo {br}· Empréstimos com garantia reforçada com <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} por ano em EUR", "selectEarnAccount.subtitle": "Podes alterar a qualquer momento", "selectEarnAccount.title": "Selecionar moeda", "selectEarnAccount.usd.description.steps": "· Levanta fundos 24/7, sem bloqueios {br}· Os juros acumulam a cada segundo {br}· Depósitos com garantia reforçada em <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} por ano em USD", "selectEarnAccount.zero.description_general": "Mantém dinheiro digital sem ganhar juros", "selectEarnAccount.zero.title": "0% por ano", "selectRechargeThreshold.button.enterAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON> montante", "selectRechargeThreshold.button.setTo": "Definir para {amount}", "selectRechargeThreshold.description.line1": "Quando o saldo do teu cartão descer abaixo de {amount}, é automaticamente recarregado para {amount} a partir da tua conta Earn.", "selectRechargeThreshold.description.line2": "Um objetivo mais baixo mantém mais fundos na tua conta Earn (a render 3%). Podes alterar isto a qualquer momento.", "selectRechargeThreshold.title": "Definir saldo alvo do cartão", "select_currency_to_withdraw.select_token_to_withdraw": "Selecionar token a levantar", "send-card-token.form.send": "Enviar", "send-card-token.form.send-amount": "Montante de <PERSON>", "send-card-token.form.title": "Adicionar <PERSON> ao Cartão", "send-card-token.form.to-address": "Cartão", "send-safe-transaction.network-fee-widget.error": "Precisas de {amount} ou escolhe outro token", "send-safe-transaction.network-fee-widget.no-fee": "Sem taxas", "send-safe-transaction.network-fee-widget.title": "Taxas", "send-safe-transaction.network_fee_widget.title": "Taxa de rede", "send.banner.fees": "Precisas de {amount} mais {currency} para pagar as taxas", "send.banner.toAddressNotSupportedNetwork.subtitle": "A carteira do destinatário não suporta {network}. Muda para um token suportado.", "send.banner.toAddressNotSupportedNetwork.title": "Rede não suportada pelo destinatário", "send.banner.walletNotSupportedNetwork.subtitle": "As Smart Wallets não podem fazer transações em {network}. Muda para um token suportado.", "send.banner.walletNotSupportedNetwork.title": "Rede do token não suportada", "send.empty-portfolio.empty-state": "Não encontrámos tokens", "send.empty-portfolio.header": "Tokens", "send.titile": "Enviar", "sendLimit.success.subtitle": "O teu limite de gastos diário será atualizado em 3 minutos. Até lá, podes continuar a gastar dentro do teu limite atual.", "sendLimit.success.title": "Esta alteração demorará 3 minutos", "send_crypto.form.disconnected.cta.addFunds": "Adicionar fundos", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "<PERSON><PERSON> para {network}", "send_crypto.form.disconnected.label": "Montante a transferir", "send_to.qr_code.description": "Lê um código QR para enviar para uma carteira", "send_to.qr_code.title": "<PERSON><PERSON> c<PERSON><PERSON>", "send_to_card.header": "Enviar para o endereço do Cartão", "send_to_card.select_sender.add_wallet": "<PERSON><PERSON><PERSON><PERSON>", "send_to_card.select_sender.header": "Selecionar remetente", "send_to_card.select_sender.search.default_placeholder": "<PERSON><PERSON><PERSON> ou ENS", "send_to_card.select_sender.show_card_address_button_description": "Mostrar endereço do cartão", "send_token.form.select-address": "Selecionar endereço", "send_token.form.send-amount": "Montante a enviar", "send_token.form.title": "Enviar", "setLimit.amount.error.zero_amount": "Não poderás fazer pagamentos", "setLimit.error.max_limit_reached": "Definir limite máximo {amount}", "setLimit.error.same_as_current_limit": "Igual ao limite atual", "setLimit.placeholder": "Atual: {amount}", "setLimit.submit": "Definir Limite", "setLimit.submit.error.amount_required": "Inser<PERSON>", "setLimit.subtitle": "Valor diário que podes gastar com o cartão.", "setLimit.title": "Definir limite de gastos diário", "settings.accounts": "<PERSON><PERSON>", "settings.accountsSeeAll": "Ver tudo", "settings.addAccount": "<PERSON><PERSON><PERSON><PERSON>", "settings.card": "Definições do cartão", "settings.connections": "Ligações de apps", "settings.currency": "Moeda predefinida", "settings.default_currency_selector.title": "<PERSON><PERSON>", "settings.discord": "Discord", "settings.experimentalMode": "Modo experimental", "settings.experimentalMode.subtitle": "Testa novas funcionalidades", "settings.language": "Idioma", "settings.lockZeal": "Bloquear Zeal", "settings.notifications": "Notificações", "settings.open_expanded_view": "Abrir vista expandida", "settings.privacyPolicy": "Política de Privacidade", "settings.settings": "Definições", "settings.termsOfUse": "Termos de Utilização", "settings.twitter": "𝕏 / Twitter", "settings.version": "Vers<PERSON> {version} amb: {env}", "setup-card.confirmation": "Obter Cartão Virtual", "setup-card.confirmation.subtitle": "Faz pagamentos online e adiciona à tua carteira {type} para pagamentos contactless.", "setup-card.getCard": "Obter cart<PERSON>", "setup-card.order.physicalCard": "Cartão físico", "setup-card.order.physicalCard.steps": "· Um VISA Gnosis Pay físico {br}· Demora até 3 semanas a chegar a ti {br}· Para pagamentos presenciais e em multibancos. {br}· Adicionar à carteira Apple/Google (apenas em países suportados", "setup-card.order.subtitle1": "Podes usar vários cartões ao mesmo tempo", "setup-card.order.title": "Que tipo de cartão?", "setup-card.order.virtualCard": "Cartão virtual", "setup-card.order.virtual_card.steps": "· Um VISA Gnosis Pay digital {br}· Para usar já em pagamentos online {br}· Adicionar à carteira Apple/Google (apenas em países suportados)", "setup-card.orderCard": "<PERSON><PERSON><PERSON>", "setup-card.virtual-card": "Obter Cartão Virtual", "setup.notifs.fakeAndroid.title": "Notificações de pagamentos e transferências recebidas", "setup.notifs.fakeIos.subtitle": "A Zeal pode alertar-te quando receberes dinheiro ou gastares com o teu cartão Visa. Podes alterar isto mais tarde.", "setup.notifs.fakeIos.title": "Notificações de pagamentos e transferências recebidas", "sign.PermitAllowanceItem.spendLimit": "Limite de gastos", "sign.ledger.subtitle": "Enviámos o pedido de transação para a tua carteira de hardware. Continua lá.", "sign.ledger.title": "Assinar na carteira de hardware", "sign.passkey.subtitle": "O teu navegador deve pedir-te para assinares com a passkey associada a esta carteira. Por favor, continua lá.", "sign.passkey.title": "Selecionar passkey", "signal_aborted_for_uknown_reason.title": "Pedido de rede cancelado", "simulatedTransaction.BridgeTrx.info.title": "Bridge", "simulatedTransaction.CardTopUp.info.title": "Adicionar <PERSON> ao cartão", "simulatedTransaction.CardTopUpTrx.info.title": "Adicionar <PERSON> ao cartão", "simulatedTransaction.NftCollectionApproval.approve": "Aprovar coleção de NFT", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "Adicionar ao cart<PERSON>", "simulatedTransaction.OrderEarnDepositBridge.title": "Depositar em Earn", "simulatedTransaction.P2PTransaction.info.title": "Enviar", "simulatedTransaction.PermitSignMessage.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.SingleNftApproval.approve": "Aprovar NFT", "simulatedTransaction.UnknownSignMessage.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.Withdrawal.info.title": "Levantamento", "simulatedTransaction.approval.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.approve.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.p2p.info.account": "Para", "simulatedTransaction.p2p.info.unlabelledAccount": "Carteira sem nome", "simulatedTransaction.unknown.info.receive": "<PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.info.send": "Enviar", "simulatedTransaction.unknown.using": "A usar {app}", "simulation.approval.modal.text": "Permite a uma app usar os teus tokens.", "simulation.approval.modal.title": "O que são aprovações?", "simulation.approval.spend-limit.label": "Limite de gastos", "simulation.approve.footer.for": "Para", "simulation.approve.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "simulationNotAvailable.title": "Ação desconhecida", "smart-wallet-activation-view.on": "Em", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "O 1Password pode bloquear o acesso à tua carteira", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Usa a Apple ou o Google para configurar a Zeal em segurança", "smart-wallet.passkey-notice.title": "Evitar o 1Password", "spend-limits.high.modal.text": "Define um limite de gastos próximo do valor de tokens que vais realmente usar com uma app ou contrato inteligente. Limites elevados são arriscados e podem facilitar o roubo dos teus tokens por burlões.", "spend-limits.high.modal.text_sign_message": "O limite de gastos deve ser próximo da quantidade de tokens que vais realmente usar com uma app ou contrato inteligente. Limites altos são arriscados e podem facilitar o roubo dos teus tokens por burlões.", "spend-limits.high.modal.title": "Limite de gastos elevado", "spend-limits.modal.text": "O limite de gastos é a quantidade de tokens que uma app pode usar em teu nome. Podes alterar ou remover este limite a qualquer momento. Para te manteres seguro, mantém os limites de gastos próximos do valor de tokens que vais realmente usar com uma app.", "spend-limits.modal.title": "O que é o limite de gastos?", "spent-limit-info.modal.description": "O limite de gastos define quantos tokens uma app pode usar em teu nome. Podes alterar ou remover este limite a qualquer momento. Para segurança, mantém os limites de gastos próximos da quantidade de tokens que vais realmente usar com uma app.", "spent-limit-info.modal.title": "O que é o limite de gastos?", "sswaps-io.transfer-provider": "Fornecedor de transferência", "storage.accountDetails.activateWallet": "Ativar carteira", "storage.accountDetails.changeWalletLabel": "Alterar etiqueta da carteira", "storage.accountDetails.deleteWallet": "Remover carteira", "storage.accountDetails.setup_recovery_kit": "Kit de recuperação", "storage.accountDetails.showPrivateKey": "Mostrar chave privada", "storage.accountDetails.showWalletAddress": "Mostrar endereço da carteira", "storage.accountDetails.smartBackup": "Backup e recuperação", "storage.accountDetails.viewSsecretPhrase": "Ver frase secreta", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets?", "storage.manageAccounts.title": "Carteiras", "submit-userop.progress.text": "A enviar", "submit.error.amount_high": "Montante demasiado alto", "submit.error.amount_hight": "Montante demasiado alto", "submit.error.amount_low": "Montante demasiado baixo", "submit.error.amount_required": "<PERSON><PERSON><PERSON><PERSON><PERSON> montante", "submit.error.maximum_number_of_characters_exceeded": "Reduz os caracteres da mensagem", "submit.error.not_enough_balance": "<PERSON><PERSON> insuficiente", "submit.error.recipient_required": "Destina<PERSON><PERSON><PERSON> o<PERSON>", "submit.error.routes_not_found": "Nenhuma rota encontrada", "submitSafeTransaction.monitor.title": "Resultado da transação", "submitSafeTransaction.sign.title": "Resultado da transação", "submitSafeTransaction.state.sending": "A enviar", "submitSafeTransaction.state.sign": "A criar", "submitSafeTransaction.submittingToRelayer.title": "Resultado da transação", "submitTransaction.cancel": "<PERSON><PERSON><PERSON>", "submitTransaction.cancel.attemptingToStop": "A tentar parar", "submitTransaction.cancel.failedToStop": "<PERSON>alha ao parar", "submitTransaction.cancel.stopped": "Parada", "submitTransaction.cancel.title": "Pré-visualização da transação", "submitTransaction.failed.banner.description": "A rede cancelou esta transação inesperadamente. Tenta novamente ou contacta-nos.", "submitTransaction.failed.banner.title": "Transação falhou", "submitTransaction.failed.execution_reverted.title": "A app teve um erro", "submitTransaction.failed.execution_reverted_without_message.title": "A app teve um erro", "submitTransaction.failed.out_of_gas.description": "A rede cancelou a transação porque usou mais taxas de rede do que o esperado", "submitTransaction.failed.out_of_gas.title": "Erro de rede", "submitTransaction.sign.title": "Resultado da transação", "submitTransaction.speedUp": "<PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue": "Adicionada à fila", "submitTransaction.state.addedToQueue.short": "Em fila", "submitTransaction.state.cancelled": "Parada", "submitTransaction.state.complete": "{currencyCode} adicionados ao Zeal", "submitTransaction.state.complete.subtitle": "Verifica o teu portfólio <PERSON>", "submitTransaction.state.completed": "Conclu<PERSON><PERSON>", "submitTransaction.state.failed": "Fal<PERSON>", "submitTransaction.state.includedInBlock": "Incluída no bloco", "submitTransaction.state.includedInBlock.short": "No bloco", "submitTransaction.state.replaced": "Substituída", "submitTransaction.state.sendingToNetwork": "A enviar para a rede", "submitTransaction.stop": "<PERSON><PERSON>", "submitTransaction.submit": "Enviar", "submitted-user-operation.state.bundled": "Em fila", "submitted-user-operation.state.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.failed": "Fal<PERSON>", "submitted-user-operation.state.pending": "A retransmitir", "submitted-user-operation.state.rejected": "<PERSON><PERSON><PERSON><PERSON>", "submittedTransaction.failed.title": "Transação falhou", "success_splash.card_activated": "Cartão ativado", "supportFork.give-feedback.title": "Dar feedback", "supportFork.itercom.description": "O Zeal trata de questões sobre dep<PERSON><PERSON>s, <PERSON><PERSON><PERSON>, recompensas ou qualquer outra coisa", "supportFork.itercom.title": "<PERSON>õ<PERSON> da carteira", "supportFork.title": "Obter ajuda com", "supportFork.zendesk.subtitle": "A Gnosis Pay trata de questões sobre pagamentos com cartão, verificações de identidade ou reembolsos", "supportFork.zendesk.title": "Pagamentos e identidade", "supported-networks.ethereum.warning": "Taxas elevadas", "supportedNetworks.networks": "Redes suportadas", "supportedNetworks.oneAddressForAllNetworks": "Um endereço para todas as redes", "supportedNetworks.receiveAnyAssets": "Recebe ativos de redes suportadas diretamente na tua carteira Zeal com o mesmo endereço.", "swap.form.error.no_routes_found": "Nenhuma rota encontrada", "swap.form.error.not_enough_balance": "<PERSON><PERSON> insuficiente", "swaps-io-details.bank.serviceProvider": "Prestador de serviços", "swaps-io-details.details.processing": "A processar", "swaps-io-details.pending": "Pendente", "swaps-io-details.rate": "Taxa", "swaps-io-details.serviceProvider": "Prestador de serviços", "swaps-io-details.transaction.from.processing": "Transferência iniciada", "swaps-io-details.transaction.networkFees": "Taxas de rede", "swaps-io-details.transaction.state.completed-transaction": "Transferência concluída", "swaps-io-details.transaction.state.started-transaction": "Transferência iniciada", "swaps-io-details.transaction.to.processing": "Transferência concluída", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "O depósito deve ser concluído em breve. A Kinetex ainda está a processar a tua transação.", "swapsIO.monitoring.awaitingLiqSend.title": "<PERSON><PERSON><PERSON>", "swapsIO.monitoring.awaitingRecive.title": "A retransmitir", "swapsIO.monitoring.awaitingSend.title": "Em fila", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Os tokens foram enviados para a Kinetex, mas serão devolvidos em breve. A Kinetex não conseguiu concluir a transação de destino.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "A devolver tokens", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Os tokens não foram transferidos devido a um erro desconhecido. Tenta novamente.", "swapsIO.monitoring.cancelledNoSlash.title": "Tokens devolvidos", "swapsIO.monitoring.cancelledSlashed.subtitle": "Os tokens foram devolvidos. A Kinetex não conseguiu concluir a transação de destino.", "swapsIO.monitoring.cancelledSlashed.title": "Tokens devolvidos", "swapsIO.monitoring.completed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "taker-metadata.earn": "Ganha em USD digital com a Sky", "taker-metadata.earn.aave": "Ganha em EUR digital com a Aave", "taker-metadata.earn.aave.cashout24": "Levanta instantaneamente, 24/7", "taker-metadata.earn.aave.trusted": "Confiança de 27 mil M$, +2 anos", "taker-metadata.earn.aave.yield": "Rendimento acumulado a cada segundo", "taker-metadata.earn.chf": "Ganha em CHF digital", "taker-metadata.earn.chf.cashout24": "Levanta fundos instantaneamente, 24/7", "taker-metadata.earn.chf.trusted": "Com a confiança de 28 M de Fr.", "taker-metadata.earn.chf.yield": "Rendimento acumula a cada segundo", "taker-metadata.earn.usd.cashout24": "Levanta instantaneamente, 24/7", "taker-metadata.earn.usd.trusted": "Confiança de 10,7 mil M$, +5 anos", "taker-metadata.earn.usd.yield": "Rendimento acumulado a cada segundo", "test": "Depositar", "to.titile": "Para", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "Ativos", "token.groupHeader.titleWithSum": "Ativos {sum}", "token.hidden_tokens.page.title": "Tokens ocultos", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "Adicionar token", "token.widget.cashback_empty": "Ainda sem transações", "token.widget.emptyState": "Nenhum token na carteira", "tokens.cash": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.approve.for": "Para", "top-up-card-from-earn-view.approve.into": "Para", "top-up-card-from-earn-view.swap.from": "De", "top-up-card-from-earn-view.swap.to": "Para", "top-up-card-from-earn-view.withdraw.to": "Para", "top-up-card-from-earn.trx.title.approval": "<PERSON><PERSON><PERSON> troca", "top-up-card-from-earn.trx.title.swap": "Adicionar ao cart<PERSON>", "top-up-card-from-earn.trx.title.withdrawal": "<PERSON><PERSON> de <PERSON>n", "topUpDapp.connectWallet": "<PERSON>r <PERSON><PERSON>", "topup-fee-breakdown.bungee-fee": "Taxa do provedor externo", "topup-fee-breakdown.header": "Taxa de transação", "topup-fee-breakdown.network-fee": "Taxa de rede", "topup-fee-breakdown.total-fee": "Taxa total", "topup.continue-in-wallet": "Continua na tua carteira", "topup.send.title": "Enviar", "topup.submit-transaction.close": "<PERSON><PERSON><PERSON>", "topup.submit-transaction.sent-to-wallet": "Enviar {amount}", "topup.to": "Para", "topup.transaction.complete.close": "<PERSON><PERSON><PERSON>", "topup.transaction.complete.try-again": "Tentar de novo", "transaction-request.nonce-too-low.modal.button-text": "<PERSON><PERSON><PERSON>", "transaction-request.nonce-too-low.modal.text": "Uma transação com o mesmo número de série (nonce) já foi concluída, pelo que já não podes enviar esta transação. Isto pode acontecer se fizeres transações muito próximas ou se tentares acelerar ou cancelar uma transação que já foi concluída.", "transaction-request.nonce-too-low.modal.title": "Transação com o mesmo nonce foi concluída", "transaction-request.replaced.modal.button-text": "<PERSON><PERSON><PERSON>", "transaction-request.replaced.modal.text": "Não conseguimos acompanhar o estado desta transação. Ou foi substituída por outra transação ou o nó RPC está com problemas.", "transaction-request.replaced.modal.title": "Não foi possível encontrar o estado da transação", "transaction.activity.details.modal.close": "<PERSON><PERSON><PERSON>", "transaction.cancel_popup.cancel": "Não, esperar", "transaction.cancel_popup.confirm": "<PERSON>m, parar", "transaction.cancel_popup.description": "Para parar, tens de pagar uma nova taxa de rede em vez da taxa original de {oldFee}", "transaction.cancel_popup.description_without_original": "Para parar, tens de pagar uma nova taxa de rede", "transaction.cancel_popup.not_supported.subtitle": "Parar transações não é suportado em {network}", "transaction.cancel_popup.not_supported.title": "Não suportado", "transaction.cancel_popup.stopping_fee": "Taxa de rede para parar", "transaction.cancel_popup.title": "Parar transação?", "transaction.in-progress": "Em andamento", "transaction.inProgress": "Em andamento", "transaction.speed_up_popup.cancel": "Não, esperar", "transaction.speed_up_popup.confirm": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "transaction.speed_up_popup.description": "Paga uma nova taxa em vez da original de {amount}", "transaction.speed_up_popup.description_without_original": "Para acelerar, paga uma nova taxa de rede", "transaction.speed_up_popup.seed_up_fee_title": "Taxa de aceleração da rede", "transaction.speed_up_popup.title": "Acelerar transferência?", "transaction.speedup_popup.not_supported.subtitle": "Aceleração não suportada em {network}", "transaction.speedup_popup.not_supported.title": "Não suportado", "transaction.subTitle.failed": "<PERSON><PERSON><PERSON><PERSON>", "transactionDetails.cashback.not-qualified": "Não elegível", "transactionDetails.cashback.paid": "{amount} pago", "transactionDetails.cashback.pending": "{amount} pendente", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "Desconhecido", "transactionDetails.cashback_estimate": "Estimativa de cashback", "transactionDetails.category": "Categoria", "transactionDetails.exchangeRate": "Taxa de câmbio", "transactionDetails.location": "Localização", "transactionDetails.payment-approved": "Pagamento aprovado", "transactionDetails.payment-declined": "Pagamento recusado", "transactionDetails.payment-reversed": "Pagamento revertido", "transactionDetails.recharge.amountSentFromEarn.title": "Montante enviado da conta Earn", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "Re<PERSON><PERSON>gado para o cartão", "transactionDetails.recharge.rate.title": "Taxa", "transactionDetails.recharge.transactionId.title": "ID da transação", "transactionDetails.refund": "Reembolso", "transactionDetails.reversal": "<PERSON><PERSON><PERSON>", "transactionDetails.transactionCurrency": "Moeda da transação", "transactionDetails.transactionId": "ID da transação", "transactionDetails.type": "Transação", "transactionRequestWidget.approve.subtitle": "Para {target}", "transactionRequestWidget.p2p.subtitle": "Para {target}", "transactionRequestWidget.unknown.subtitle": "A usar {target}", "transactionSafetyChecksPopup.title": "Verificações de segurança da transação", "transactions.main.activity.title": "Atividade", "transactions.page.hiddenActivity.title": "Atividade oculta", "transactions.page.title": "Atividade", "transactions.viewTRXHistory.emptyState": "Ainda sem transferências", "transactions.viewTRXHistory.errorMessage": "Falha ao carregar o teu histórico de transferências", "transactions.viewTRXHistory.hidden.emptyState": "Nenhuma transferência oculta", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Atividade não suportada em testnets", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Atividade não suportada em testnets{br}<link>Ver no explorador</link>", "transfer_provider": "Fornecedor da transferência", "transfer_setup_with_different_wallet.subtitle": "As transferências bancárias estão configuradas com uma carteira diferente. Só podes ter uma carteira associada a transferências.", "transfer_setup_with_different_wallet.swtich_and_continue": "Mudar e continuar", "transfer_setup_with_different_wallet.title": "<PERSON><PERSON>", "tx-sent-to-wallet.button": "<PERSON><PERSON><PERSON>", "tx-sent-to-wallet.subtitle": "Continua em {wallet}", "unblockProviderInfo.fees": "Obténs as taxas mais baixas possíveis: 0% até 5k $ por mês e 0,2% acima disso.", "unblockProviderInfo.registration": "A Unblock está registada e autorizada pela FNTT para fornecer serviços de câmbio e custódia VASP, e é um fornecedor MSB registado na Fincen dos EUA. <link>Saber mais</link>", "unblockProviderInfo.selfCustody": "O dinheiro digital que recebes é de autocustódia e mais ninguém terá controlo sobre o teu ativo", "unblock_invalid_faster_payment_configuration.subtitle": "A conta bancária que forneceste não suporta transferências SEPA europeias ou UK Faster Payments. Fornece outra conta.", "unblock_invalid_faster_payment_configuration.title": "É necessária outra conta", "unknownTransaction.primaryText": "Transação com cartão", "unsupportedCountry.subtitle": "As transferências bancárias ainda não estão disponíveis no teu país.", "unsupportedCountry.title": "Indisponível em {country}", "update-app-popup.subtitle": "A atualização mais recente está repleta de correções, funcionalidades e mais magia. Atualiza para a versão mais recente e melhora a tua Zeal.", "update-app-popup.title": "At<PERSON>izar <PERSON><PERSON><PERSON>", "update-app-popup.update-now": "Atual<PERSON>r agora", "user_associated_with_other_merchant.subtitle": "Esta carteira não pode ser usada para transferências bancárias. Usa outra carteira ou contacta-nos no nosso Discord para apoio e atualizações.", "user_associated_with_other_merchant.title": "A carteira não pode ser usada", "user_associated_with_other_merchant.try_with_another_wallet": "Tentar com outra carteira", "user_email_already_exists.subtitle": "Já configuraste transferências bancárias com outra carteira. Tenta novamente com a carteira que usaste anteriormente.", "user_email_already_exists.title": "Transferências configuradas com outra carteira", "user_email_already_exists.try_with_another_wallet": "Tentar com outra carteira", "validation.invalid.iban": "IBAN inválido", "validation.required": "Obrigatório", "validation.required.first_name": "Nome próprio obrigatório", "validation.required.iban": "IBAN obrigatório", "validation.required.last_name": "Apelido obrigatório", "verify-passkey.cta": "Verificar passkey", "verify-passkey.subtitle": "Verifica que a tua passkey foi criada e está devidamente protegida.", "verify-passkey.title": "Verificar passkey", "view-cashback.cashback-next-cycle": "Taxa de cashback em {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "Deposita para obter cashback", "view-cashback.pending": "{money} Pendente", "view-cashback.pending-rewards.not_paid": "A receber em {days}d", "view-cashback.pending-rewards.paid": "Recebido esta semana", "view-cashback.received-rewards": "Recompensas recebidas", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Pagamentos não confirmados", "view-cashback.upcoming": "A receber {money}", "virtual-card-order.configure-safe.loading-text": "A criar cartão", "virtual-card-order.create-order.loading-text": "A ativar cartão", "virtual-card-order.create-order.success-text": "Cartão ativado", "virtualCard.activateCard": "At<PERSON>r <PERSON>", "walletDeleteConfirm.main_action": "Remover", "walletDeleteConfirm.subtitle": "Terás de importar novamente para veres o portefólio ou fazeres transações", "walletDeleteConfirm.title": "Remover carteira?", "walletSetting.header": "Definições da carteira", "wallet_connect.connect.cancel": "<PERSON><PERSON><PERSON>", "wallet_connect.connect.connect_button": "Ligar à Zeal", "wallet_connect.connect.title": "Conectar", "wallet_connect.connected.title": "Conectado", "wallet_connect_add_chain_missing.title": "Rede não suportada", "wallet_connect_proposal_expired.title": "Ligação expirada", "withdraw": "Levantar", "withdraw.confirmation.close": "<PERSON><PERSON><PERSON>", "withdraw.confirmation.continue": "Confirmar", "withdrawal_request.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withdrawal_request.pending": "Pendente", "zeal-dapp.connect-wallet.cta.primary.connecting": "A ligar...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Ligar", "zeal-dapp.connect-wallet.cta.secondary": "<PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.title": "Liga a carteira para continuar", "zealSmartWalletInfo.gas": "Paga a taxa de rede com vários tokens; usa tokens ERC20 populares em redes suportadas para pagares as taxas de rede, não apenas os tokens nativos", "zealSmartWalletInfo.recover": "Sem frases secretas; recupera com a passkey biométrica do teu gestor de palavras-passe, iCloud ou conta Google.", "zealSmartWalletInfo.selfCustodial": "Totalmente privada; as assinaturas da passkey são validadas on-chain para minimizar dependências centrais.", "zealSmartWalletInfo.title": "Sobre as smart wallets da Zeal", "zeal_a_rewards_already_claimed_error.title": "Recompensa já reclamada", "zwidget.minimizedDisconnected.label": "Zeal desconectada"}