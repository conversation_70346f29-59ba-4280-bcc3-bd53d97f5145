{"Account.ListItem.details.label": "Detaļ<PERSON>", "AddFromAddress.success": "<PERSON><PERSON><PERSON><PERSON>", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{Nav maciņu} one{{count} mac<PERSON><PERSON>š} other{{count} maciņi}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "<PERSON><PERSON><PERSON><PERSON> frāze {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Izveido jaunus maciņus no kādas no savām esošajām slepenajām frāzēm", "AddFromExistingSecretPhrase.SelectPhrase.title": "Izvēlies slepeno frāzi", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "<PERSON><PERSON> slepen<PERSON> frāze var dubl<PERSON><PERSON> daudzu<PERSON> mac<PERSON>. Izvēlies tos, kurus vēlies izman<PERSON>t.", "AddFromExistingSecretPhrase.WalletSelection.title": "<PERSON><PERSON>", "AddFromExistingSecretPhrase.success": "<PERSON><PERSON><PERSON><PERSON>", "AddFromHardwareWallet.subtitle": "Izvēlies savu aparatū<PERSON>, lai savie<PERSON>u ar <PERSON>", "AddFromHardwareWallet.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.WalletSelection.subtitle": "Izv<PERSON><PERSON> maciņus, kurus vēlies importēt", "AddFromNewSecretPhrase.WalletSelection.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.accounts": "<PERSON><PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "<PERSON>lepenā frāze darbojas kā atslēgu piekari<PERSON> maciņ<PERSON>, katram no kuriem ir unikāla privātā atslēga.{br}{br}Tu vari importēt tik daud<PERSON>, cik <PERSON>, tagad vai pievienot vairāk vēlāk.", "AddFromNewSecretPhrase.secretPhraseTip.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> frā<PERSON>", "AddFromNewSecretPhrase.subtitle": "<PERSON><PERSON><PERSON> savu slepeno fr<PERSON>, atdalot vārdus ar atstarpēm", "AddFromNewSecretPhrase.success_secret_phrase_added": "Slepenā frāze pievienota 🎉", "AddFromNewSecretPhrase.success_wallets_added": "<PERSON><PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.wallets": "<PERSON><PERSON><PERSON><PERSON>", "AddFromPrivateKey.subtitle": "<PERSON><PERSON><PERSON> savu privā<PERSON>", "AddFromPrivateKey.success": "Privātā atslēga pievienota 🎉", "AddFromPrivateKey.title": "<PERSON><PERSON><PERSON><PERSON>", "AddFromPrivateKey.typeOrPaste": "<PERSON><PERSON><PERSON> vai ielīmē šeit", "AddFromSecretPhrase.importWallets": "{count,plural,=0{Nav izvēl<PERSON>ti maciņi} one{Importēt maciņu} other{Importēt {count} maciņus}}", "AddFromTrezor.AccountSelection.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AddFromTrezor.hwWalletTip.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> maci<PERSON> satur miljoniem maciņu ar dažādām adresēm. Tu vari importēt tik daudz maciņ<PERSON>, cik <PERSON>, tagad vai pievienot vairāk vēlāk.", "AddFromTrezor.hwWalletTip.title": "Importēšana no aparatūras maciņiem", "AddFromTrezor.importAccounts": "{count,plural,=0{<PERSON><PERSON><PERSON>i nav izvēlēti} one{Importēt maciņu} other{Importēt {count} maciņus}}", "AddFromTrezor.success": "<PERSON><PERSON><PERSON><PERSON>", "ApprovalSpenderTypeCheck.failed.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, krāpniecība: tērēt<PERSON><PERSON>m jābūt līgumam", "ApprovalSpenderTypeCheck.failed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>, nevis līgums", "ApprovalSpenderTypeCheck.passed.subtitle": "Parasti tu a<PERSON><PERSON><PERSON> līd<PERSON><PERSON> līgumiem", "ApprovalSpenderTypeCheck.passed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir v<PERSON>s", "BestReturns.subtitle": "<PERSON><PERSON> ma<PERSON><PERSON>s sniegs visaugst<PERSON><PERSON> at<PERSON>, ieskaitot visas maksas.", "BestReturnsPopup.title": "Labākā atdeve", "BlacklistCheck.Failed.subtitle": "Ļaunprātīgi ziņo<PERSON><PERSON> no <source></source>", "BlacklistCheck.Failed.title": "<PERSON><PERSON><PERSON> ir melnajā sarak<PERSON>ā", "BlacklistCheck.Passed.subtitle": "Nav ļaunprāt<PERSON>gu ziņ<PERSON>jumu no <source></source>", "BlacklistCheck.Passed.title": "<PERSON>ietne nav melnajā sarakstā", "BlacklistCheck.failed.statusButton.label": "Par vietni ir zi<PERSON>", "BridgeRoute.slippage": "Slippage {slippage}", "BridgeRoute.title": "<PERSON>", "CheckConfirmation.InProgress": "Procesā...", "CheckConfirmation.success.splash": "<PERSON><PERSON><PERSON><PERSON>", "ChooseImportOrCreateSecretPhrase.subtitle": "Importē slepeno frāzi vai izveido jaunu", "ChooseImportOrCreateSecretPhrase.title": "<PERSON><PERSON><PERSON> slepeno fr<PERSON>", "ConfirmTransaction.Simuation.Skeleton.title": "<PERSON><PERSON><PERSON><PERSON>…", "ConnectionSafetyCheckResult.passed": "Drošības pārbaude izturēta", "ContactGnosisPaysupport": "Sazināties ar Gnosis Pay", "CopyKeyButton.copied": "Nokopēts", "CopyKeyButton.copyYourKey": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>lēgu", "CopyKeyButton.copyYourPhrase": "<PERSON><PERSON><PERSON><PERSON> savu frāzi", "DAppVerificationCheck.Failed.subtitle": "Vietne nav i<PERSON><PERSON><PERSON><PERSON> <source></source>", "DAppVerificationCheck.Failed.title": "Vietne nav atrasta lietotņu reģistros", "DAppVerificationCheck.Passed.subtitle": "<PERSON><PERSON><PERSON> <PERSON>r <PERSON><PERSON><PERSON><PERSON> <source></source>", "DAppVerificationCheck.Passed.title": "<PERSON><PERSON><PERSON> ir atrodama lietotņu reģistros", "DAppVerificationCheck.failed.statusButton.label": "Vietne nav atrasta lietotņu reģistros", "ERC20.tokens.emptyState": "Tokeni nav atrasti", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "<PERSON>it<PERSON><PERSON> maksa", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {period}: no {from} līdz {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "<PERSON><PERSON><PERSON> maksa: {baseFee} • Drošības rezerve: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Neizdev<PERSON><PERSON> i<PERSON> pa<PERSON>re<PERSON><PERSON><PERSON> bāzes maksu", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Augstāks par nāka<PERSON>. Iestrēgs.", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "<PERSON><PERSON> nevar būt mazāks par pašreizējo", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "<PERSON>evar<PERSON><PERSON>ām aprēķināt prioritātes maksu", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Neizdev<PERSON><PERSON> i<PERSON> pašreiz<PERSON><PERSON> maks<PERSON> maksu", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> maksa", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, lī<PERSON><PERSON> tīkla maksas", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bā<PERSON> maksa", "EditFeeModal.Custom.gasLimit.title": "Gāzes limits {gasLimit}", "EditFeeModal.Custom.title": "<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "<PERSON><PERSON><PERSON><PERSON><PERSON>, lī<PERSON><PERSON> bāzes maksa", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "<PERSON><PERSON> maksa. <PERSON><PERSON> i<PERSON>", "EditFeeModal.EditGasLimit.estimatedGas": "Apm. gāze: {estimated} • Drošības rezerve: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Mazāk par aprēķināto limitu. Darījums neizdosies", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Mazāk par ieteikto limitu. Darījums var neizdoties", "EditFeeModal.EditGasLimit.subtitle": "Maks. gāzes apjoms. Ja par zemu, neizdosies.", "EditFeeModal.EditGasLimit.title": "Rediģēt gāzes limitu", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Mazāk par minimālo tīkla maksas limitu: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "Augstāks par nā<PERSON><PERSON>. Darījums iestrēgs", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "<PERSON>ce nevar iestatīt zemāku par pašreizējo", "EditFeeModal.EditNonce.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ja nav n<PERSON><PERSON><PERSON><PERSON>.", "EditFeeModal.EditNonce.title": "Rediģēt Nonce", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "Vajag {amount} , lai iesniegtu", "EditFeeModal.Header.Time.unknown": "Laiks nezināms", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Maks. {fee}", "EditFeeModal.Header.fee.unknown": "<PERSON><PERSON><PERSON>", "EditFeeModal.Header.subsequent_failed": "<PERSON><PERSON> var būt veci, atsva<PERSON><PERSON> neizdevās.", "EditFeeModal.Layout.Header.ariaLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> maksa", "EditFeeModal.MaxFee.subtitle": "Maks. maksa ir dro<PERSON> rezerve tīklam.", "EditFeeModal.MaxFee.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tīkla maksa", "EditFeeModal.SelectPreset.Time.unknown": "Laiks nezināms", "EditFeeModal.SelectPreset.ariaLabel": "Izvēlies maksas iesta<PERSON>ījumu", "EditFeeModal.SelectPreset.fast": "<PERSON><PERSON>", "EditFeeModal.SelectPreset.normal": "<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.SelectPreset.slow": "<PERSON><PERSON><PERSON>", "EditFeeModal.ariaLabel": "Rediģēt tīkla maksu", "FailedSimulation.Confirmation.Item.subtitle": "<PERSON><PERSON> r<PERSON><PERSON><PERSON>", "FailedSimulation.Confirmation.Item.title": "Neizdev<PERSON><PERSON> simul<PERSON>t dar<PERSON>", "FailedSimulation.Confirmation.subtitle": "Vai tiešām vēlies turpināt?", "FailedSimulation.Confirmation.title": "<PERSON> paraksti bez pā<PERSON>aud<PERSON>", "FailedSimulation.Title": "Simulācijas kļūda", "FailedSimulation.footer.subtitle": "<PERSON><PERSON> r<PERSON><PERSON><PERSON>", "FailedSimulation.footer.title": "Neizdev<PERSON><PERSON> simul<PERSON>t dar<PERSON>", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Nepiecieša<PERSON> {amount} , lai iesniegtu darījumu", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Apstrā<PERSON> var aizņemt ilgu laiku", "FeeForecastWidget.networkFee": "<PERSON><PERSON><PERSON> maksa", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Neizdevās aprēķināt tīkla maksu", "FeeForecastWidget.subsequentFailed.message": "Aprēķini var būt no<PERSON>, pēdēj<PERSON> at<PERSON><PERSON><PERSON> ne<PERSON>s", "FeeForecastWidget.unknownDuration": "<PERSON><PERSON>inā<PERSON>", "FeeForecastWidget.unknownFee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "GasCurrencySelector.balance": "Saldo: {balance}", "GasCurrencySelector.networkFee": "<PERSON><PERSON><PERSON> maksa", "GasCurrencySelector.payNetworkFeesUsing": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> maks<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "GasCurrencySelector.removeDefaultGasToken.description": "<PERSON><PERSON><PERSON>t maksu no lielākā saldo", "GasCurrencySelector.removeDefaultGasToken.title": "Automātiska maks<PERSON> a<PERSON>", "GasCurrencySelector.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GoogleDriveBackup.BeforeYouBegin.first_point": "<PERSON>a aiz<PERSON><PERSON><PERSON>u savu <PERSON> paroli, es uz visiem laikiem pazaudēšu savus līd<PERSON>us", "GoogleDriveBackup.BeforeYouBegin.second_point": "<PERSON>a zaudē<PERSON>u piekļuvi savam Google Drive vai pārveidošu at<PERSON>p<PERSON>nas failu, es uz visiem laikiem pazaudēšu savus līd<PERSON>", "GoogleDriveBackup.BeforeYouBegin.subtitle": "<PERSON><PERSON><PERSON><PERSON>, izproti un piekrīti šiem punktiem par privāto mac<PERSON>:", "GoogleDriveBackup.BeforeYouBegin.third_point": "Zeal nevar man palī<PERSON><PERSON><PERSON><PERSON> atgūt Zeal paroli vai piekļuvi Google Drive", "GoogleDriveBackup.BeforeYouBegin.title": "Pirms sāc", "GoogleDriveBackup.loader.subtitle": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> pieprasījumu <PERSON>, lai aug<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> savu atkop<PERSON> failu", "GoogleDriveBackup.loader.title": "<PERSON><PERSON><PERSON> a<PERSON>...", "GoogleDriveBackup.success": "Dublējums veiksmīgs 🎉", "MonitorOffRamp.overServiceTime": "<PERSON><PERSON><PERSON><PERSON><PERSON> daļa pārskaitījumu tiek pabeigti {estimated_time}, bet daž<PERSON>iz papildu pārbaužu dēļ tas var aizņemt ilgāku laiku. Tā ir normāla prakse, un pārbaužu laikā tavi līdzekļi ir drošībā.{br}{br}Ja darījums netiek pabeigts {support_soft_deadline}, lū<PERSON><PERSON>, {contact_support}", "MonitorOnRamp.contactSupport": "Sazinies ar atbalstu", "MonitorOnRamp.from": "No", "MonitorOnRamp.fundsReceived": "Līdzek<PERSON><PERSON>", "MonitorOnRamp.overServiceTime": "Vairums pārskaitījumu neaizņem vairāk kā {estimated_time}, bet dažreiz papildu pārbaužu dēļ tas var aizņemt ilgāku laiku. Tā ir normāla prakse, un līdzekļi pārbaužu laikā ir drošībā.{br}{br}Ja darījums nav pabeigts {support_soft_deadline}, lūd<PERSON>, {contact_support}", "MonitorOnRamp.sendingToYourWallet": "<PERSON><PERSON><PERSON> uz tavu maciņu", "MonitorOnRamp.to": "Uz", "MonitorOnRamp.waitingForTransfer": "<PERSON><PERSON><PERSON>, kad pā<PERSON><PERSON>si līdzekļ<PERSON>", "NftCollectionCheck.failed.subtitle": "Kolekcija nav verificēta <source></source>", "NftCollectionCheck.failed.title": "Kolekcija nav verificēta", "NftCollectionCheck.passed.subtitle": "Kolekcija ir verificēta <source></source>", "NftCollectionCheck.passed.title": "Kolekcija ir verificēta", "NftCollectionInfo.entireCollection": "Visa kolekcija", "NoSigningKeyStore.createAccount": "Izveidot kontu", "NonceRangeError.biggerThanCurrent.message": "Darījums iestrēgs", "NonceRangeError.lessThanCurrent.message": "Darījums neizdosies", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "<PERSON><PERSON> ir augstāks par pašreizējo. <PERSON><PERSON><PERSON>, lai darījums neiestrēgtu.", "NonceRangeErrorPopup.biggerThanCurrent.title": "Darījums iestrēgs", "P2pReceiverTypeCheck.failed.subtitle": "Vai sūti uz pareizo adresi?", "P2pReceiverTypeCheck.failed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir v<PERSON>, nevis <PERSON>", "P2pReceiverTypeCheck.passed.subtitle": "Parasti tu sūti lī<PERSON> uz citiem maciņiem", "P2pReceiverTypeCheck.passed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir <PERSON>", "PasswordCheck.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>oli", "PasswordChecker.subtitle": "<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON> savu paroli, la<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka tas esi tu.", "PermitExpirationCheck.failed.subtitle": "Iestati ī<PERSON> termiņu – tik, cik nepieciešams", "PermitExpirationCheck.failed.title": "Ilgs derīguma termiņš", "PermitExpirationCheck.passed.subtitle": "Cik ilgi lietotne var izmantot tavus tokenus", "PermitExpirationCheck.passed.title": "<PERSON><PERSON><PERSON><PERSON> termiņš nav pārāk ilgs", "PrivateKeyValidationError.moreThanMaximumWords": "Maks. {count} v<PERSON>rdi", "PrivateKeyValidationError.notValidPrivateKey": "Šī nav derīga privātā atslēga", "PrivateKeyValidationError.secretPhraseIsInvalid": "Slepenā frāze nav derīga", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Vārds nr. {index} ir ar k<PERSON>du vai neder<PERSON>gs", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} vārds} other{{count} vārdi}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "<PERSON><PERSON><PERSON><PERSON><PERSON> frāzes un privātās atslēgas tiek šifrētas un nekad neatstāj šo ierīci", "SecretPhraseReveal.header": "<PERSON><PERSON><PERSON> s<PERSON> f<PERSON>", "SecretPhraseReveal.hint": "<PERSON>eiz<PERSON>ud savu frāzi nevienam. Glabā to droši un bezsaistē", "SecretPhraseReveal.skip.subtitle": "To var izdar<PERSON>t v<PERSON>, bet, ja pazaudēsi š<PERSON>, pirms esi pierakst<PERSON> f<PERSON>, tu zaudēsi visus līdzekļus š<PERSON>.", "SecretPhraseReveal.skip.takeTheRisk": "<PERSON><PERSON><PERSON><PERSON>", "SecretPhraseReveal.skip.title": "<PERSON><PERSON><PERSON><PERSON> frā<PERSON>?", "SecretPhraseReveal.skip.writeDown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SecretPhraseReveal.skipForNow": "<PERSON><PERSON><PERSON><PERSON>", "SecretPhraseReveal.subheader": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> to un glabā droši bezsaistē. Pēc tam mēs lūgsim to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "SecretPhraseReveal.verify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SelectCurrency.tokens": "Tokeni", "SelectCurrency.tokens.emptyState": "Tokeni nav atrasti", "SelectRoute.slippage": "Novirze {slippage}", "SelectRoutes.emptyState": "Šai maiņai neat<PERSON>ām nevienu ma<PERSON>", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "<PERSON><PERSON><PERSON>", "SendERC20.labelAddress.inputPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "SendERC20.labelAddress.subtitle": "<PERSON><PERSON><PERSON>, lai vē<PERSON>k to atrastu.", "SendERC20.labelAddress.title": "Nosaukt <PERSON>", "SendERC20.send_to": "<PERSON><PERSON><PERSON><PERSON><PERSON> kam", "SendERC20.tokens": "Tokeni", "SendOrReceive.bankTransfer.primaryText": "Bankas pārskaitījums", "SendOrReceive.bankTransfer.shortText": "<PERSON><PERSON><PERSON><PERSON><PERSON>, t<PERSON><PERSON>ī<PERSON><PERSON>ja iemaksa un izmaksa", "SendOrReceive.bridge.primaryText": "Bridge", "SendOrReceive.bridge.shortText": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>us starp tīk<PERSON>m", "SendOrReceive.receive.primaryText": "<PERSON><PERSON><PERSON><PERSON>", "SendOrReceive.receive.shortText": "Saņem tokenus vai kolekcijas priekšmetus", "SendOrReceive.send.primaryText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SendOrReceive.send.shortText": "<PERSON><PERSON><PERSON> <PERSON>us uz jeb<PERSON> adresi", "SendOrReceive.swap.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.swap.shortText": "<PERSON><PERSON> tokenus savā starpā", "SendSafeTransaction.Confirm.loading": "<PERSON>eic <PERSON>...", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Šifrēt atkopšanas failu ar paroli", "SetupRecoveryKit.google.subtitle": "Sinhroniz<PERSON><PERSON> {date}", "SetupRecoveryKit.google.title": "Google Drive dublējums", "SetupRecoveryKit.subtitle": "<PERSON>v būs <PERSON> vismaz viens veids, k<PERSON> at<PERSON> kont<PERSON>, ja atinstalēsi <PERSON> vai main<PERSON>.", "SetupRecoveryKit.title": "Iestatīt atkopšanas komplektu", "SetupRecoveryKit.writeDown.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> sle<PERSON>o fr<PERSON>", "SetupRecoveryKit.writeDown.title": "<PERSON><PERSON><PERSON><PERSON>", "Sign.CheckSafeDeployment.activate": "Aktivizēt", "Sign.CheckSafeDeployment.subtitle": "Aktivizē ierīci tīklā, lai to lietotu.", "Sign.CheckSafeDeployment.title": "Aktivizē ierīci šajā tīklā", "Sign.Simuation.Skeleton.title": "<PERSON>eic <PERSON>...", "SignMessageSafetyCheckResult.passed": "Drošības pārbaudes izturētas", "SignMessageSafetyChecksPopup.title.permits": "Atļauju droš<PERSON>", "SimulationFailedConfirmation.subtitle": "Si<PERSON><PERSON><PERSON><PERSON><PERSON> rā<PERSON>. <PERSON>aud<PERSON><PERSON> maksu.", "SimulationFailedConfirmation.title": "<PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON><PERSON>, ne<PERSON><PERSON><PERSON>", "SimulationNotSupported.Title": "Simulācija nav {br}atbalstīta {br}{network}", "SimulationNotSupported.footer.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vari iesniegt šo dar<PERSON>mu", "SimulationNotSupported.footer.title": "Simulācija nav atbalstīta", "SlippagePopup.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SlippagePopup.presetsHeader": "Maiņas slippage", "SlippagePopup.title": "Slippage iestatījumi", "SmartContractBlacklistCheck.failed.subtitle": "Ļaunprātīgi ziņo<PERSON><PERSON> no <source></source>", "SmartContractBlacklistCheck.failed.title": "<PERSON><PERSON><PERSON><PERSON> ir melnajā sarakstā", "SmartContractBlacklistCheck.passed.subtitle": "Nav ļaunprāt<PERSON>gu ziņ<PERSON>jumu no <source></source>", "SmartContractBlacklistCheck.passed.title": "<PERSON><PERSON><PERSON>s nav melnajā sarakstā", "SuspiciousCharactersCheck.Failed.subtitle": "<PERSON><PERSON> ir izplatīta pikšķerēšanas taktika", "SuspiciousCharactersCheck.Failed.title": "<PERSON><PERSON><PERSON> izplatītus pikšķerēšanas veidus", "SuspiciousCharactersCheck.Passed.subtitle": "<PERSON><PERSON><PERSON> pikšķerēšanas mēģinājumus", "SuspiciousCharactersCheck.Passed.title": "Adresē nav neparastu r<PERSON>tz<PERSON>", "SuspiciousCharactersCheck.failed.statusButton.label": "<PERSON><PERSON><PERSON> satur neparastas r<PERSON> ", "TokenVerificationCheck.failed.subtitle": "Tokens nav iekļauts sa<PERSON>ā <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} nav verific<PERSON><PERSON>s CoinGecko", "TokenVerificationCheck.passed.subtitle": "<PERSON><PERSON><PERSON> ir i<PERSON><PERSON> <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} ir verific<PERSON><PERSON><PERSON>G<PERSON>", "TopupDapp.MonitorTransaction.success.splash": "<PERSON><PERSON><PERSON><PERSON>", "TransactionSafetyCheckResult.passed": "Drošības pārbaudes izturētas", "TransactionSimulationCheck.failed.subtitle": "Kļūda: {errorMessage}", "TransactionSimulationCheck.failed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, dar<PERSON><PERSON><PERSON> neizdosies", "TransactionSimulationCheck.passed.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <source></source>", "TransactionSimulationCheck.passed.title": "Darījuma priekšskatījums bija veiksmīgs", "TrezorError.trezor_action_cancelled.action": "Aizvērt", "TrezorError.trezor_action_cancelled.subtitle": "<PERSON> transakciju savā maciņā", "TrezorError.trezor_device_used_elsewhere.action": "Sinhroni<PERSON><PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "Aizver citas sesijas, mēģini vēlreiz.", "TrezorError.trezor_method_cancelled.action": "Sinhroni<PERSON><PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "Atļauj Trezor eksportēt maciņus uz Zeal.", "TrezorError.trezor_permissions_not_granted.action": "Sinhroni<PERSON><PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "<PERSON><PERSON><PERSON><PERSON>, atļauj Zeal redzēt visus maci<PERSON>us.", "TrezorError.trezor_pin_cancelled.action": "Sinhroni<PERSON><PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "<PERSON><PERSON><PERSON> at<PERSON> i<PERSON>ī<PERSON>", "TrezorError.trezor_popup_closed.action": "Sinhroni<PERSON><PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "<PERSON><PERSON><PERSON> dialogs negai<PERSON><PERSON><PERSON> a<PERSON>s", "TrxLikelyToFail.lessThanEstimatedGas.message": "Darījums neizdosies", "TrxLikelyToFail.lessThanMinimumGas.message": "Darījums neizdosies", "TrxLikelyToFail.lessThanSuggestedGas.message": "<PERSON>istic<PERSON>ā<PERSON>, neizdosies", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "Gā<PERSON> limits par zemu. <PERSON><PERSON> to.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "<PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON><PERSON>, ne<PERSON><PERSON><PERSON>", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Gā<PERSON> limits par zemu. <PERSON><PERSON> to.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "Darījums neizdosies", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "<PERSON><PERSON>. bāzes maksa par zemu. <PERSON><PERSON><PERSON> to.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "Darījums iestrēgs", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "Maks. maksa par zemu. <PERSON><PERSON><PERSON> to.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "Darījums iestrēgs", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Prioritātes maksa par zemu. <PERSON><PERSON><PERSON> to.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "Darījums var aizņemt ilgu laiku", "UnsupportedMobileNetworkLayout.gotIt": "Skaidrs!", "UnsupportedMobileNetworkLayout.subtitle": "Tu vēl nevari veikt darījumus vai parakstīt ziņojumus tīklā ar ID {networkHexId} Zeal mobilajā versijā{br}{br}Pārslēdzies uz pārlūka pap<PERSON>, lai veiktu darījumus <PERSON> tīk<PERSON>, kamēr mēs cītīgi strādājam, lai pievienotu atbalstu šim tīklam 🚀", "UnsupportedMobileNetworkLayout.title": "Tīkls nav atbalstīts Zeal mobilajā versijā", "UnsupportedSafeNetworkLayout.subtitle": "Nevar veikt transakcijas vai parakstīt {network} ar Zeal Smart Wallet{br}{br}Lieto citu tīklu vai Legacy maciņu.", "UnsupportedSafeNetworkLayoutk.title": "Tīkls nav atbalstīts Smart Wallet", "UserConfirmationPopup.goBack": "Atcelt", "UserConfirmationPopup.submit": "<PERSON><PERSON><PERSON><PERSON>", "ViewPrivateKey.header": "Privātā at<PERSON>lēga", "ViewPrivateKey.hint": "Neizpaud savu privāto atslēgu nevienam. Glabā to droši un bezsaistē", "ViewPrivateKey.subheader.mobile": "Pie<PERSON><PERSON>, lai atklātu savu privāto atslēgu", "ViewPrivateKey.subheader.web": "<PERSON><PERSON>, lai atklātu savu privāto atsl<PERSON>gu", "ViewPrivateKey.unblur.mobile": "Pieskaries, lai atklātu", "ViewPrivateKey.unblur.web": "<PERSON><PERSON>, lai at<PERSON>", "ViewSecretPhrase.PasswordChecker.subtitle": "<PERSON><PERSON><PERSON> savu paroli, lai <PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON> failu. <PERSON><PERSON> būs jāat<PERSON>as arī turpmāk.", "ViewSecretPhrase.done": "Gatavs", "ViewSecretPhrase.header": "<PERSON><PERSON><PERSON><PERSON> frāze", "ViewSecretPhrase.hint": "<PERSON>eiz<PERSON>ud savu frāzi nevienam. Glabā to droši un bezsaistē", "ViewSecretPhrase.subheader.mobile": "<PERSON><PERSON><PERSON>, lai atklātu savu slepeno frāzi", "ViewSecretPhrase.subheader.web": "<PERSON><PERSON>, lai atkl<PERSON>tu savu slepeno frāzi", "ViewSecretPhrase.unblur.mobile": "Pieskaries, lai atklātu", "ViewSecretPhrase.unblur.web": "<PERSON><PERSON>, lai at<PERSON>", "account-details.monerium": "P<PERSON>rskait<PERSON><PERSON><PERSON> ve<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> Moneri<PERSON>, autorizētu un regulētu EMI. <link><PERSON><PERSON><PERSON></link>", "account-details.unblock": "Pārskaitīju<PERSON> – autorizēts un reģistrēts maiņas un glabāša<PERSON> pakalpojumu sniedzējs. <link><PERSON>zzin<PERSON><PERSON> v<PERSON></link>", "account-selector.empty-state": "Maciņi nav atrasti", "account-top-up.select-currency.title": "Tokeni", "account.accounts_not_found": "<PERSON><PERSON><PERSON>m atrast nevienu <PERSON>", "account.accounts_not_found_search_valid_address": "<PERSON><PERSON><PERSON><PERSON> nav tavā sarakstā", "account.add.create_new_secret_phrase": "Izveidot slepeno frāzi", "account.add.create_new_secret_phrase.subtext": "Jauna 12 vārdu slepenā frāze", "account.add.fromRecoveryKit.fileNotFound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> atrast tavu failu", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "Mēģināt vēlreiz", "account.add.fromRecoveryKit.fileNotFound.explanation": "<PERSON><PERSON><PERSON><PERSON><PERSON> pareizo kontu ar <PERSON> dub<PERSON>.", "account.add.fromRecoveryKit.fileNotValid": "<PERSON><PERSON><PERSON><PERSON><PERSON> fails nav derīgs", "account.add.fromRecoveryKit.fileNotValid.explanation": "Fails ir nepareiza tipa vai main<PERSON>ts.", "account.add.import_secret_phrase": "Importēt slepeno frāzi", "account.add.import_secret_phrase.subtext": "<PERSON><PERSON><PERSON><PERSON><PERSON> Zeal, Metamask vai citur", "account.add.select_type.add_hardware_wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "account.add.select_type.existing_smart_wallet": "<PERSON><PERSON><PERSON><PERSON> Wallet", "account.add.select_type.private_key": "Privātā at<PERSON>lēga", "account.add.select_type.seed_phrase": "<PERSON><PERSON><PERSON><PERSON>f<PERSON><PERSON><PERSON>", "account.add.select_type.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "account.add.select_type.zeal_recovery_file": "Zeal at<PERSON><PERSON><PERSON><PERSON> fails", "account.add.success.title": "Jauns <PERSON>ņš izveidots 🎉", "account.addLabel.header": "<PERSON><PERSON><PERSON> sa<PERSON>", "account.addLabel.labelError.labelAlreadyExist": "Nosauku<PERSON> jau pastāv. Izmēģini citu nosaukumu", "account.addLabel.labelError.maxStringLengthExceeded": "Sasnie<PERSON><PERSON> maks<PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON>", "account.add_active_wallet.primary_text": "<PERSON><PERSON><PERSON>", "account.add_active_wallet.short_text": "<PERSON><PERSON><PERSON><PERSON>, savieno vai importē <PERSON>", "account.add_from_ledger.success": "<PERSON><PERSON><PERSON><PERSON>", "account.add_tracked_wallet.primary_text": "<PERSON><PERSON><PERSON> tikai las<PERSON>", "account.add_tracked_wallet.short_text": "S<PERSON>īt portfeli un aktivitāti", "account.button.unlabelled-wallet": "<PERSON><PERSON><PERSON><PERSON> bez <PERSON>", "account.create_wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "account.label.edit.title": "Rediģēt maciņa nosaukumu", "account.recoveryKit.selectBackupFile.fileDate": "Izveidots {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "<PERSON><PERSON><PERSON><PERSON><PERSON> fails nav derīgs", "account.recoveryKit.selectBackupFile.subtitle": "Izvēlies atkopšanas failu, ko atjaunot", "account.recoveryKit.selectBackupFile.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> fails", "account.recoveryKit.success.recoveryFileFound": "<PERSON><PERSON><PERSON><PERSON><PERSON> fails atrasts 🎉", "account.select_type_of_account.create_eoa.short": "<PERSON><PERSON><PERSON> mac<PERSON> ekspertiem", "account.select_type_of_account.create_eoa.title": "Izveidot sākumfrā<PERSON> maciņu", "account.select_type_of_account.create_safe_wallet.title": "Izveidot Smart Wallet", "account.select_type_of_account.existing_smart_wallet": "<PERSON><PERSON>šs Smart Wallet", "account.select_type_of_account.hardware_wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "account.select_type_of_account.header": "<PERSON><PERSON><PERSON>", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON><PERSON> frāze", "account.select_type_of_account.read_only_wallet": "<PERSON><PERSON><PERSON>", "account.select_type_of_account.read_only_wallet.short": "Priekšskati jebkuru portfeli", "account.topup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "account.view.error.refreshAssets": "At<PERSON>uno<PERSON>", "account.widget.refresh": "Atsvaidzināt", "account.widget.settings": "Iestatījumi", "accounts.view.copied-text": "Nokopēts {formattedAddress}", "accounts.view.copiedAddress": "Nokopēts {formattedAddress}", "action.accept": "<PERSON><PERSON><PERSON><PERSON>", "action.accpet": "Piek<PERSON><PERSON><PERSON>", "action.allow": "<PERSON><PERSON><PERSON>", "action.back": "Atpakaļ", "action.cancel": "Atcelt", "action.card-activation.title": "Aktivizēt karti", "action.claim": "<PERSON><PERSON><PERSON><PERSON>", "action.close": "Aizvērt", "action.complete-steps": "<PERSON><PERSON><PERSON>ļ<PERSON>", "action.confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.copy-address-understand": "Labi kopēt ad<PERSON>i", "action.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.done": "Gatavs", "action.dontAllow": "<PERSON><PERSON><PERSON><PERSON>", "action.edit": "rediģēt", "action.email-required": "Ievadi e-pastu", "action.enterPhoneNumber": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON> numuru", "action.expand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.fix": "<PERSON><PERSON>", "action.getStarted": "<PERSON><PERSON><PERSON>", "action.got_it": "<PERSON><PERSON><PERSON><PERSON>", "action.hide": "Paslēpt", "action.import": "Importēt", "action.import-keys": "Importēt atslēgas", "action.importKeys": "Importēt atslēgas", "action.minimize": "Minimizēt", "action.next": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.ok": "<PERSON><PERSON>", "action.reduceAmount": "Samazin<PERSON>t lī<PERSON>z ma<PERSON>.", "action.refreshWebsite": "Atsvaidzināt vietni", "action.remove": "<PERSON><PERSON><PERSON><PERSON>", "action.remove-account": "Noņemt kontu", "action.requestCode": "<PERSON><PERSON><PERSON><PERSON><PERSON> kodu", "action.resend_code": "<PERSON><PERSON><PERSON><PERSON><PERSON> kodu vēlreiz", "action.resend_code_with_time": "<PERSON><PERSON><PERSON><PERSON><PERSON> kodu vēlreiz {time}", "action.retry": "Mēģināt vēlreiz", "action.reveal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action.save_changes": "Saglabāt RPC", "action.search": "Meklēt", "action.seeAll": "<PERSON><PERSON><PERSON><PERSON> visu", "action.select": "Izvēlēties", "action.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.skip": "<PERSON><PERSON><PERSON><PERSON>", "action.submit": "<PERSON><PERSON><PERSON><PERSON>", "action.understood": "<PERSON><PERSON><PERSON>", "action.update": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action.update-gnosis-pay-owner.complete": "<PERSON><PERSON><PERSON>s", "action.zeroAmount": "<PERSON><PERSON><PERSON> summu", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Kolekcionējamie priekšmeti", "action_bar_title.tokens": "Tokeni", "action_bar_title.transaction_request": "Transakcijas pieprasījums", "activate-monerium.loading": "Iestatām tavu personīgo kontu", "activate-monerium.success.title": "Monerium aktivizēts", "activate-physical-card-widget.subtitle": "<PERSON>g<PERSON><PERSON> var ilgt 3 ned<PERSON>ļ<PERSON>", "activate-physical-card-widget.title": "Aktivizēt fizisko karti", "activate-smart-wallet.title": "Aktivizēt <PERSON>", "active_and_tracked_wallets.title": "Zeal sedz visas tavas maksas {network}, ļaujot tev veikt transakcijas bez maksas!", "activity.approval-amount.revoked": "Atsaukts", "activity.approval-amount.unlimited": "Neierobežots", "activity.approval.approved_for": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.approval.approved_for_with_target": "Aps<PERSON><PERSON><PERSON><PERSON> {approvedTo}", "activity.approval.revoked_for": "Atsaukts", "activity.bank.serviceProvider": "Pakalpojuma sniedzējs", "activity.bridge.serviceProvider": "Pakalpojuma sniedzējs", "activity.cashback.period": "Cashback periods", "activity.filter.card": "<PERSON><PERSON>", "activity.rate": "<PERSON><PERSON><PERSON>", "activity.receive.receivedFrom": "Saņemts no", "activity.send.sendTo": "Nosūtīts uz", "activity.smartContract.unknown": "Nezinā<PERSON> līgums", "activity.smartContract.usingContract": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>", "activity.subtitle.pending_timer": "{timerString} <PERSON><PERSON><PERSON>", "activity.title.arbitrary_smart_contract_interaction": "{function} iekš {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "<PERSON><PERSON><PERSON><PERSON><PERSON> līguma darb<PERSON>ba", "activity.title.bridge.from": "Bridge no {token}", "activity.title.bridge.to": "Bridge uz {token}", "activity.title.buy": "Pirkums {asset}", "activity.title.card_owners_updated": "<PERSON><PERSON><PERSON>", "activity.title.card_spend_limit_updated": "<PERSON><PERSON><PERSON> limits iestatīts", "activity.title.cashback_deposit": "<PERSON><PERSON><PERSON><PERSON> kont<PERSON>", "activity.title.cashback_reward": "<PERSON>ņ<PERSON>ts cashback", "activity.title.cashback_withdraw": "<PERSON>zmaksa no Cashback konta", "activity.title.claimed_reward": "<PERSON><PERSON><PERSON><PERSON>l<PERSON>z<PERSON>", "activity.title.deployed_smart_wallet_gnosis": "Konts izveidots", "activity.title.deposit_from_bank": "Iemaksa no bankas", "activity.title.deposit_into_card": "<PERSON><PERSON><PERSON><PERSON> kartē", "activity.title.deposit_into_earn": "<PERSON><PERSON><PERSON><PERSON> {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} iekš {smartContract}", "activity.title.from": "No {sender}", "activity.title.pendidng_areward_claim": "<PERSON><PERSON><PERSON>", "activity.title.pendidng_breward_claim": "<PERSON><PERSON><PERSON>", "activity.title.recharge_disabledh": "<PERSON><PERSON><PERSON>", "activity.title.recharge_set": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mērķis iestatīts", "activity.title.recovered_smart_wallet_gnosis": "<PERSON><PERSON><PERSON> ierīces instalācija", "activity.title.send_pending": "Kam: {receiver}", "activity.title.send_to_bank": "Uz banku", "activity.title.swap": "Pirkums {token}", "activity.title.to": "Kam: {receiver}", "activity.title.withdraw_from_card": "<PERSON>zmaksa no kartes", "activity.title.withdraw_from_earn": "<PERSON><PERSON><PERSON><PERSON><PERSON> no {earn}", "activity.transaction.networkFees": "<PERSON><PERSON><PERSON> ma<PERSON>", "activity.transaction.state": "Pabeigta transakcija", "activity.transaction.state.completed": "<PERSON><PERSON><PERSON><PERSON>", "activity.transaction.state.failed": "Neizde<PERSON> da<PERSON>", "add-account.section.import.header": "Imports", "add-another-card-owner": "Pievienot vēl vienu kartes ī<PERSON>šnieku", "add-another-card-owner.Recommended.footnote": "Pievieno savu Zeal maciņu kā papildu īpašnieku savai Gnosis Pay kartei", "add-another-card-owner.Recommended.primaryText": "<PERSON><PERSON><PERSON> Zeal Gnosis Pay", "add-another-card-owner.recommended": "Ieteicams", "add-owner.confirmation.subtitle": "Drošības nolūkos iestatījumu maiņa aizņem 3 minūtes. <PERSON><PERSON><PERSON> laikā tava karte būs <PERSON> iesaldēta, un maksājumi nebūs iespējami.", "add-owner.confirmation.title": "<PERSON>va karte tiks iesaldēta uz 3 min., ka<PERSON><PERSON><PERSON>ja<PERSON> iestatījumi", "add-readonly-signer-if-not-exist.error.already_in_use.title": "<PERSON><PERSON><PERSON>, tas jau <PERSON>", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "Mēģināt citu maciņu", "add.account.backup.decrypt.success": "<PERSON><PERSON><PERSON><PERSON>", "add.account.backup.password.passwordIncorrectMessage": "Parole nav pareiza", "add.account.backup.password.subtitle": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ko i<PERSON><PERSON> sava atko<PERSON><PERSON>nas faila šifrē<PERSON>", "add.account.backup.password.title": "<PERSON><PERSON><PERSON>oli", "add.account.google.login.subtitle": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> pieprasījumu <PERSON>, lai sinhroniz<PERSON>tu savu atkopšanas failu", "add.account.google.login.title": "<PERSON><PERSON><PERSON> a<PERSON>...", "add.readonly.already_added": "<PERSON><PERSON><PERSON><PERSON> jau ir pievie<PERSON>s", "add.readonly.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add.readonly.empty": "<PERSON><PERSON><PERSON> ad<PERSON>i vai ENS", "addBankRecipient.title": "Pievienot bankas saņēmēju", "add_funds.deposit_from_bank_account": "Noguldījums no bankas konta", "add_funds.from_another_wallet": "No cita maciņa", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "<PERSON><PERSON><PERSON> ar p<PERSON><PERSON> dApp", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "<PERSON><PERSON><PERSON> jeb<PERSON><PERSON> maci<PERSON> Zeal papildināšanas dApp un ātri nosūti līdzekļus uz savu maciņu", "add_funds.from_crypto_wallet.header": "No cita maciņa", "add_funds.from_crypto_wallet.header.show_wallet_address": "<PERSON><PERSON><PERSON><PERSON><PERSON> sava maciņa adresi", "add_funds.from_exchange.header": "Sūtīt no biržas", "add_funds.from_exchange.header.copy_wallet_address": "<PERSON><PERSON><PERSON><PERSON> savu <PERSON> adresi", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance utt.", "add_funds.from_exchange.header.open_exchange": "Atvērt biržas lietotni vai vietni", "add_funds.from_exchange.header.selected_token": "<PERSON><PERSON><PERSON><PERSON><PERSON> {token} uz Zeal", "add_funds.from_exchange.header.selected_token.subtitle": "Tīklā {network}", "add_funds.from_exchange.header.send_selected_token": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add_funds.from_exchange.header.send_selected_token.subtitle": "Izvēlies atbalstītu tokenu un tīklu", "add_funds.import_wallet": "Importēt esošu k<PERSON>to <PERSON>", "add_funds.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>u", "add_funds.transfer_from_exchange": "Pārskaitījums no biržas", "address.add.header": "<PERSON><PERSON><PERSON><PERSON> savu <PERSON> Zeal{br}tikai skatī<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "address.add.subheader": "Ievadi savu adresi vai ENS, lai redzētu savus aktīvus visos EVM tīklos vienuviet. Vēlāk varēsi izveidot vai importēt vairāk maciņu.", "address_book.change_account.bank_transfers.header": "Bankas <PERSON>", "address_book.change_account.bank_transfers.primary": "<PERSON><PERSON>", "address_book.change_account.cta": "<PERSON><PERSON><PERSON><PERSON>ņ<PERSON>", "address_book.change_account.search_placeholder": "Pievienot vai meklēt adresi", "address_book.change_account.tracked_header": "<PERSON><PERSON><PERSON>", "address_book.change_account.wallets_header": "<PERSON><PERSON><PERSON><PERSON>", "app-association-check-failed.modal.cta": "Mēģināt vēlreiz", "app-association-check-failed.modal.subtitle": "Mēģini vēlre<PERSON>. <PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Passkeys.", "app-association-check-failed.modal.subtitle.creation": "Mēģini vēlre<PERSON>. <PERSON><PERSON>, ve<PERSON><PERSON><PERSON>.", "app-association-check-failed.modal.title.creation": "Ierīcei neizdevās izveidot <PERSON>key", "app-association-check-failed.modal.title.signing": "<PERSON>er<PERSON><PERSON><PERSON> neizdev<PERSON> i<PERSON><PERSON>s", "app.app_protocol_group.borrowed_tokens": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.claimable_amount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> summa", "app.app_protocol_group.health_rate": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "app.app_protocol_group.lending": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.locked_tokens": "Bloķētie tokeni", "app.app_protocol_group.nfts": "Kolekcionējamie", "app.app_protocol_group.reward_tokens": "Atlīdzī<PERSON>", "app.app_protocol_group.supplied_tokens": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.tokens": "Tokens", "app.app_protocol_group.vesting_token": "Vesting tokens", "app.appsGroupHeader.discoverMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Meklē vai ievadi URL", "app.error-banner.cory": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "app.error-banner.retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.list_item.rewards": "Atlīdz<PERSON><PERSON> {value}", "app.position_details.health_rate.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> tiek aprēķināts, dalot aizdevuma summu ar tava nodrošinājuma vērtību.", "app.position_details.health_rate.title": "Kas ir stāvokļa rādītāj<PERSON>?", "approval.edit-limit.label": "Rediģēt tēriņu limitu", "approval.permit_info": "Atļaujas informācija", "approval.spend-limit.edit-modal.cancel": "Atcelt", "approval.spend-limit.edit-modal.limit-label": "Tēriņu limits", "approval.spend-limit.edit-modal.max-limit-error": "Brī<PERSON>ā<PERSON>ms, augsts limits", "approval.spend-limit.edit-modal.revert": "Atcelt izmaiņas", "approval.spend-limit.edit-modal.set-to-unlimited": "Iestatīt <PERSON>", "approval.spend-limit.edit-modal.submit": "Saglabāt iz<PERSON>", "approval.spend-limit.edit-modal.title": "Rediģēt atļaujas", "approval.spend_limit_info": "Kas ir tēri<PERSON><PERSON> limits?", "approval.what_are_approvals": "<PERSON><PERSON> ir a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?", "apps_list.page.emptyState": "Nav aktīvu lietotņu", "backpace.removeLastDigit": "<PERSON><PERSON><PERSON><PERSON> pēdējo ciparu", "backup-banner.backup_now": "<PERSON><PERSON><PERSON><PERSON>", "backup-banner.risk_losing_funds": "<PERSON><PERSON><PERSON>, lai nepa<PERSON><PERSON><PERSON> savu<PERSON> lī<PERSON>.", "backup-banner.title": "<PERSON><PERSON><PERSON><PERSON> nav dubl<PERSON>", "backupRecoverySmartWallet.noExportPrivateKeys": "Automātiska dublēšana: Tavs Smart Wallet tiek saglabāts kā ieejas atslēga – nav nepieciešama slepenā frāze vai privātā atslēga.", "backupRecoverySmartWallet.safeContracts": "Vairāku atslēgu droš<PERSON>: <PERSON><PERSON> maciņi darbojas ar <PERSON>, tāpēc darījumu var apstiprināt vairākas ierīces. Nav viena kļū<PERSON>ta.", "backupRecoverySmartWallet.security": "Vairākas ierīces: Tu vari izmantot savu maciņu vairākās ierīcēs ar ieejas atslēgu. Katrai ierīcei ir sava privātā atslēga.", "backupRecoverySmartWallet.showLocalPrivateKey": "Eksperta režīms: Tu vari eksportēt šīs ierīces privāto atslēgu, izman<PERSON>t to citā maciņā un savienoties <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Rādīt privāto atslēgu</Key>", "backupRecoverySmartWallet.storingKeys": "Sinhronizēts ar mākoni: <PERSON><PERSON><PERSON> atslēga tiek droši glabāta iCloud, Google Password Manager vai tavā paroļu pārvaldniekā.", "backupRecoverySmartWallet.title": "Smart Wallet dublēšana un atkopšana", "balance-change.card.titile": "<PERSON><PERSON>", "balanceChange.pending": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Izvēlies pakalpojumu sniedzēju", "bank-transfer.change-deposit-receiver.subtitle": "<PERSON><PERSON> <PERSON> saņems visas bankas iemaksas.", "bank-transfer.change-deposit-receiver.title": "<PERSON>estati <PERSON><PERSON><PERSON><PERSON>", "bank-transfer.change-owner.subtitle": "<PERSON><PERSON>, lai pier<PERSON> un atgūtu savu bankas pārskaitījumu kontu.", "bank-transfer.change-owner.title": "Iestati konta īpašnieku", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Jebkura bankas iemaksa, ko s<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>.", "bank-transfer.configrm-change-deposit-receiver.title": "<PERSON><PERSON><PERSON>", "bank-transfer.configrm-change-owner.subtitle": "Vai tiešām vēlies mainīt konta īpašnieku? <PERSON><PERSON> mac<PERSON> tiek i<PERSON>, lai pierakst<PERSON>tos un atgūtu tavu bankas pārskaitījumu kontu.", "bank-transfer.configrm-change-owner.title": "<PERSON><PERSON>t k<PERSON>a ī<PERSON>šnieku", "bank-transfer.deposit.widget.status.complete": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.deposit.widget.status.funds_received": "Līdzek<PERSON><PERSON>", "bank-transfer.deposit.widget.status.sending_to_wallet": "<PERSON><PERSON><PERSON> u<PERSON>", "bank-transfer.deposit.widget.status.transfer-on-hold": "Pārskaitījums aizturēts", "bank-transfer.deposit.widget.status.transfer-received": "<PERSON><PERSON><PERSON> u<PERSON>", "bank-transfer.deposit.widget.subtitle": "{from} uz {to}", "bank-transfer.deposit.widget.title": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.intro.bulletlist.point_1": "Iestatīšana a<PERSON>", "bank-transfer.intro.bulletlist.point_2": "Pārskaitījumi starp EUR/GBP un vairāk nekā 10 tokeniem", "bank-transfer.intro.bulletlist.point_3": "0 % komisijas maksa līdz 5k $ mēnesī, pēc tam 0,2 %", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Sūta uz banku", "bank-transfer.withdrawal.widget.status.in-progress": "Notiek pārskaitījums", "bank-transfer.withdrawal.widget.status.on-hold": "Pārskaitījums aizturēts", "bank-transfer.withdrawal.widget.status.success": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.withdrawal.widget.subtitle": "{from} uz {to}", "bank-transfer.withdrawal.widget.title": "<PERSON>zmaks<PERSON>", "bank-transfers.bank-account-actions.remove-this-account": "Noņemt šo kontu", "bank-transfers.bank-account-actions.switch-to-this-account": "Pārslēgties uz šo kontu", "bank-transfers.deposit.fees-for-less-than-5k": "<PERSON><PERSON><PERSON><PERSON> maksa par 5k $ vai mazāk", "bank-transfers.deposit.fees-for-more-than-5k": "Komisijas maksa par vairāk nekā 5k $", "bank-transfers.set-receiving-bank.title": "Iestatīt saņēmējbanku", "bank-transfers.settings.account_owner": "Konta īpašnieks", "bank-transfers.settings.receiver_of_bank_deposits": "Bankas iemaks<PERSON>", "bank-transfers.settings.receiver_of_withdrawals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfers.settings.registered_email": "Reģistrētais e-pasts", "bank-transfers.settings.title": "Bankas pārskaitījumu i<PERSON>īju<PERSON>", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} konts", "bank-transfers.setup.bank-account": "Bankas konts", "bankTransfer.withdraw.max_loading": "Maks.: {amount}", "bank_details_do_not_match.got_it": "<PERSON><PERSON><PERSON>", "bank_details_do_not_match.subtitle": "Bankas kods un konta numurs nesakr<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, vai dati ievad<PERSON>ti par<PERSON>, un mēģini vēlre<PERSON>.", "bank_details_do_not_match.title": "<PERSON><PERSON> dati <PERSON>", "bank_tranfsers.select_country_of_residence.country_not_supported": "Atvaino, bankas pārskaitījumi nav pieejami {country} vēl", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Atver savu bankas lietotni", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Nosūti {fiatCurrencyCode} uz savu kontu", "bank_transfer.deposit.header": "{fullName}''s person<PERSON><PERSON><PERSON> konta&nbsp;dati", "bank_transfer.kyc_status_widget.subtitle": "Bankas pārskaitījumi", "bank_transfer.kyc_status_widget.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfer.personal_details.date_of_birth": "<PERSON><PERSON><PERSON><PERSON><PERSON> datums", "bank_transfer.personal_details.date_of_birth.invalid_format": "Datums nav derīgs", "bank_transfer.personal_details.date_of_birth.too_young": "<PERSON>v j<PERSON><PERSON><PERSON><PERSON> vismaz 18 gadus vecam", "bank_transfer.personal_details.first_name": "<PERSON><PERSON><PERSON>", "bank_transfer.personal_details.last_name": "Uzvārds", "bank_transfer.personal_details.title": "<PERSON><PERSON>", "bank_transfer.reference.label": "<PERSON><PERSON><PERSON><PERSON><PERSON> mērķis (nav obligāts)", "bank_transfer.reference_message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> no Zeal", "bank_transfer.residence_details.address": "<PERSON><PERSON> adrese", "bank_transfer.residence_details.city": "Pilsē<PERSON>", "bank_transfer.residence_details.country_of_residence": "Dzīvesvietas valsts", "bank_transfer.residence_details.country_placeholder": "Valsts", "bank_transfer.residence_details.postcode": "Pasta indekss", "bank_transfer.residence_details.street": "<PERSON><PERSON>", "bank_transfer.residence_details.your_residence": "<PERSON><PERSON>", "bank_transfers.choose-wallet.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.test": "<PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.warning.subtitle": "Vienlaikus vari piesaistīt tikai vienu maciņu. Piesaistīto maciņu nevarēsi nomain<PERSON>t.", "bank_transfers.choose-wallet.warning.title": "Rūpīgi izvēlies maciņu", "bank_transfers.choose_wallet.subtitle": "Izvēlies maciņu bankas pārskaitījumiem. ", "bank_transfers.choose_wallet.title": "Izvēlies maciņu", "bank_transfers.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.currency_is_currently_not_supported": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.account-name": "Konta no<PERSON>ukums", "bank_transfers.deposit.account-number-copied": "Konta numurs no<PERSON>ē<PERSON>", "bank_transfers.deposit.amount-input": "<PERSON><PERSON><PERSON><PERSON> summa", "bank_transfers.deposit.amount-output": "<PERSON>ņ<PERSON><PERSON><PERSON> summa", "bank_transfers.deposit.amount-output.error": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.buttet-point.receive-crypto": "Saņem {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.currency-not-supported.subtitle": "Bankas iemaksas no {code} ir atspējotas līdz turpmākam paziņojumam.", "bank_transfers.deposit.currency-not-supported.title": "{code} i<PERSON><PERSON><PERSON> pa<PERSON><PERSON>k netiek atbalstītas", "bank_transfers.deposit.default-token.balance": "Saldo {amount}", "bank_transfers.deposit.deposit-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.enter_amount": "<PERSON><PERSON><PERSON> summu", "bank_transfers.deposit.iban-copied": "IBAN nokopēts", "bank_transfers.deposit.increase-amount": "<PERSON><PERSON><PERSON><PERSON><PERSON> ir {limit}", "bank_transfers.deposit.loading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.max-limit-reached": "Summa pā<PERSON><PERSON><PERSON><PERSON> maks<PERSON><PERSON><PERSON> p<PERSON><PERSON>u", "bank_transfers.deposit.modal.kyc.button-text": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.modal.kyc.text": "<PERSON> p<PERSON>tu tavu identitāti, lū<PERSON><PERSON> personīgo informāciju un dokumentus. Iesniegšana parasti aizņem tikai pāris min<PERSON>.", "bank_transfers.deposit.modal.kyc.title": "<PERSON><PERSON><PERSON><PERSON> identitāti, lai pali<PERSON><PERSON> limitus", "bank_transfers.deposit.reduce_amount": "Samazini summu", "bank_transfers.deposit.show-account.account-number": "Konta numurs", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Bankas kods", "bank_transfers.deposit.sort-code-copied": "Bankas kods nokopēts", "bank_transfers.deposit.withdraw-header": "<PERSON>zmaks<PERSON>", "bank_transfers.failed_to_load_fee": "<PERSON><PERSON>inā<PERSON>", "bank_transfers.fees": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.increase-amount": "<PERSON><PERSON><PERSON><PERSON><PERSON> ir {limit}", "bank_transfers.insufficient-funds": "Nepietiek līdzekļu", "bank_transfers.select_country_of_residence.title": "Kur tu dzīvo?", "bank_transfers.setup.cta": "Iestatīt <PERSON>", "bank_transfers.setup.enter-amount": "<PERSON><PERSON><PERSON> summu", "bank_transfers.source_of_funds.form.business_income": "Ienākumi no uzņēmējdarbības", "bank_transfers.source_of_funds.form.other": "Cits", "bank_transfers.source_of_funds.form.pension": "Pensija", "bank_transfers.source_of_funds.form.salary": "Alga", "bank_transfers.source_of_funds.form.title": "<PERSON><PERSON>s l<PERSON> avots", "bank_transfers.source_of_funds_description.placeholder": "<PERSON><PERSON><PERSON> l<PERSON>d<PERSON> avotu...", "bank_transfers.source_of_funds_description.title": "Pastā<PERSON><PERSON> vairāk par savu līdzekļu avotu", "bank_transfers.withdraw-header": "<PERSON>zmaks<PERSON>", "bank_transfers.withdraw.amount-input": "<PERSON><PERSON><PERSON><PERSON><PERSON> summa", "bank_transfers.withdraw.max-limit-reached": "Summa pā<PERSON><PERSON><PERSON><PERSON> maks<PERSON><PERSON><PERSON> p<PERSON><PERSON>u", "bank_transfers.withdrawal.verify-id": "Samazini summu", "banner.above_maximum_limit.maximum_input_limit_exceeded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ievades limits", "banner.above_maximum_limit.maximum_limit_per_deposit": "<PERSON><PERSON> ir maks<PERSON><PERSON><PERSON> limits vienam de<PERSON>", "banner.above_maximum_limit.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ievades limits", "banner.above_maximum_limit.title": "Samazini summu līdz {amount} vai mazāk", "banner.above_maximum_limit.title.default": "Samazini summu", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Nav sasniegts minim<PERSON>lais ievades limits", "banner.below_minimum_limit.minimum_limit_for_token": "<PERSON><PERSON> ir minim<PERSON><PERSON>s limits šim <PERSON>am", "banner.below_minimum_limit.title": "<PERSON><PERSON><PERSON> summu līdz {amount} vai vairāk", "banner.below_minimum_limit.title.default": "<PERSON><PERSON><PERSON> summu", "breaard.in_porgress.info_popup.cta": "<PERSON><PERSON><PERSON><PERSON>, lai nopelnītu {earn}", "breaard.in_porgress.info_popup.footnote": "<PERSON><PERSON><PERSON><PERSON> Zeal un Gnosis Pay karti, tu piekr<PERSON><PERSON> š<PERSON> atlīdzības kampaņas noteikumiem.", "breaward.in_porgress.info_popup.bullet_point_1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {remaining} n<PERSON><PERSON><PERSON> {time} laik<PERSON>, lai saņ<PERSON>tu šo atl<PERSON>z<PERSON>.", "breaward.in_porgress.info_popup.bullet_point_2": "<PERSON>ika<PERSON> derīgi <PERSON> Pay pirkumi tiek ieskaitīti tavā iztērētajā summā.", "breaward.in_porgress.info_popup.bullet_point_3": "Pēc atlīdzības sa<PERSON> tā tiks nosūtīta uz tavu Zeal kontu.", "breaward.in_porgress.info_popup.header": "<PERSON><PERSON><PERSON> {earn}, i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {remaining}", "breward.celebration.for_spending": "<PERSON>r t<PERSON><PERSON> ar tavu karti", "breward.dc25-eligible-celebration.for_spending": "Tu esi starp pirmajiem {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "<PERSON> nebiji starp pirmajiem {limit} , kas izt<PERSON><PERSON><PERSON><PERSON>", "breward.expired_banner.earn_by_spending": "<PERSON><PERSON><PERSON> {earn} , izt<PERSON><PERSON><PERSON><PERSON><PERSON> {amount}", "breward.expired_banner.reward_expired": "{earn} atlīd<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "breward.in_progress_banner.cta.title": "<PERSON><PERSON><PERSON><PERSON>, lai nopelnītu {earn}", "breward.ready_to_claim.error.try_again": "Mēģināt vēlreiz", "breward.ready_to_claim.error_title": "Neizde<PERSON><PERSON><PERSON> atlīdzī<PERSON>", "breward.ready_to_claim.in_progress": "Saņemam atlīdzību", "breward.ready_to_claim.youve_earned": "Tu esi nopelnījis {earn}!", "breward_already_claimed.title": "Atlīdzība jau sa<PERSON>. Ja nē, sazinies.", "breward_cannotbe_claimed.title": "Atlīd<PERSON><PERSON><PERSON> nevar saņemt. Mēģini vēlāk.", "bridge.best_return": "<PERSON><PERSON><PERSON><PERSON> ar vis<PERSON><PERSON><PERSON><PERSON>", "bridge.best_serivce_time": "<PERSON><PERSON><PERSON><PERSON> ar ātrā<PERSON> laiku", "bridge.check_status.complete": "<PERSON><PERSON><PERSON><PERSON>", "bridge.check_status.progress_text": "Notiek bridge {from} uz {to}", "bridge.remove_topup": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>ildin<PERSON>ju<PERSON>", "bridge.request_status.completed": "<PERSON><PERSON><PERSON><PERSON>", "bridge.request_status.pending": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge.widget.completed": "<PERSON><PERSON><PERSON><PERSON>", "bridge.widget.currencies": "{from} uz {to}", "bridge_rote.widget.title": "Bridge", "browse.discover_more_apps": "Atk<PERSON><PERSON><PERSON> v<PERSON><PERSON>", "browse.google_search_term": "Mek<PERSON><PERSON><PERSON> \"{searchTerm}\"", "brward.celebration.you_earned": "Tu esi nopelnījis", "brward.expired_banner.subtitle": "<PERSON><PERSON><PERSON><PERSON>iz", "brward.in_progress_banner.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON> pēc {expiredInFormatted}", "buy": "Pirkt", "buy.enter_amount": "<PERSON><PERSON><PERSON> summu", "buy.loading": "Notiek iel<PERSON>de...", "buy.no_routes_found": "Maršruti nav atrasti", "buy.not_enough_balance": "Nepietiekams saldo", "buy.select-currency.title": "Izvēlies tokenu", "buy.select-to-currency.title": "Pirkt tokenus", "buy_form.title": "Pirkt tokenu", "cancelled-card.create-card-button.primary": "<PERSON><PERSON><PERSON><PERSON> jaunu virtuālo karti", "cancelled-card.switch-card-button.primary": "<PERSON><PERSON>t karti", "cancelled-card.switch-card-button.short-text": "Tev ir vēl viena aktīva karte", "card": "<PERSON><PERSON>", "card-add-cash.confirm-stage.banner.no-routes-found": "Nav <PERSON><PERSON><PERSON><PERSON><PERSON>, mēģini citu tokenu vai summu", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Tev vajag {amount} vēl {symbol} , lai samaks<PERSON>tu maksu", "card-add-cash.confirm-stage.banner.value-loss": "<PERSON> z<PERSON> {loss} no vērt<PERSON><PERSON>", "card-add-cash.confirm-stage.banner.value-loss.revert": "Atcelt", "card-add-cash.edit-stage.cta.cancel": "Atcelt", "card-add-cash.edit-stage.cta.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.enter-amount": "<PERSON><PERSON><PERSON> summu", "card-add-cash.edit-stage.cta.reduce-to-max": "Lietot maks.", "card-add-cash.edit-staget.banner.no-routes-found": "Nav <PERSON><PERSON><PERSON><PERSON><PERSON>, mēģini citu tokenu vai summu", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Darījums jāapstiprina hardware maciņā.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Parakstīt hardware maciņā", "card-balance": "Saldo: {balance}", "card-cashback.status.title": "<PERSON><PERSON>ksa cashback kont<PERSON>", "card-copy-safe-address.copy_address": "<PERSON><PERSON><PERSON><PERSON>", "card-copy-safe-address.copy_address.done": "Nokopēts", "card-copy-safe-address.warning.description": "<PERSON><PERSON> adrese var saņemt tikai {cardAsset} Gnosis Chain tīklā. Nesūti uz šo adresi līdzekļus no citiem tīkliem. Tie tiks zaudēti.", "card-copy-safe-address.warning.header": "<PERSON><PERSON><PERSON> tikai {cardAsset} Gnosis Chain tīklā", "card-marketing-card.center.subtitle": "Valūtas maiņas maksa", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "<PERSON><PERSON><PERSON>", "card-marketing-card.right.subtitle": "Reģistrācijas dāvana", "card-marketing-card.title": "Eiropas augsto procentu VISA karte", "card-marketing-tile.get-started": "<PERSON><PERSON><PERSON>", "card-select-from-token-title": "<PERSON><PERSON><PERSON><PERSON><PERSON>u, no kura sūt<PERSON>t", "card-top-up.banner.subtitle.completed": "<PERSON><PERSON><PERSON><PERSON>", "card-top-up.banner.subtitle.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "card-top-up.banner.subtitle.pending": "{timerString} Apstrādē", "card-top-up.banner.title": "<PERSON><PERSON><PERSON><PERSON> {amount}", "card-topup.select-token.emptyState": "Tokeni nav atrasti", "card.activate.card_number_not_valid": "Kartes numurs nav derīgs. Pārbaudi un mēģini vēlreiz.", "card.activate.invalid_card_number": "<PERSON><PERSON><PERSON><PERSON> kartes numurs.", "card.activation.activate_physical_card": "Aktivizēt fizisko karti", "card.add-cash.amount-to-withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> summa", "card.add-from-earn-form.title": "<PERSON><PERSON><PERSON> naudu kartei", "card.add-from-earn-form.withdraw-to-card": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.add-from-earn.amount-to-withdraw": "Summa izņemšanai uz karti", "card.add-from-earn.enter-amount": "<PERSON><PERSON><PERSON> summu", "card.add-from-earn.loading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.add-from-earn.max-label": "Saldo: {amount}", "card.add-from-earn.no-routes-found": "Maršruti nav atrasti", "card.add-from-earn.not-enough-balance": "Nepietiekams saldo", "card.add-owner.queued": "Īpašnieka pievienošana ievietota rindā", "card.add-to-wallet-flow.subtitle": "Veic maksājumus no sava maciņa", "card.add-to-wallet.copy-card-number": "Nokopē kartes numuru z<PERSON>k", "card.add-to-wallet.title": "Pievienot {platformName} maciņam", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "ATCELTA", "card.card-owner-not-found.disconnect-btn": "Atvienot karti no Zeal", "card.card-owner-not-found.subtitle": "<PERSON>, at<PERSON><PERSON><PERSON>.", "card.card-owner-not-found.title": "Atkārtoti savienot karti", "card.card-owner-not-found.update-owner-btn": "Atjaunin<PERSON><PERSON>", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} pēc {date}", "card.cashback.widgetNoCashback.subtitle": "<PERSON><PERSON><PERSON><PERSON>, lai sāktu peln<PERSON>t", "card.cashback.widgetNoCashback.title": "<PERSON><PERSON><PERSON> l<PERSON> {defaultPercentage} cashback", "card.cashback.widgetcashbackValue.rewards": "{amount} apstrādē", "card.cashback.widgetcashbackValue.title": "{percentage} cashback", "card.choose-wallet.connect_card": "<PERSON><PERSON><PERSON> karti", "card.choose-wallet.create-new": "<PERSON><PERSON><PERSON> jaunu mac<PERSON> k<PERSON>", "card.choose-wallet.import-another-wallet": "Importēt citu maciņu", "card.choose-wallet.import-current-owner": "Importēt pašreizējo kartes īpašnieku", "card.choose-wallet.import-current-owner.sub-text": "Importē privātās at<PERSON>lēgas vai sākum<PERSON>, kam pieder tava Gnosis Pay karte", "card.choose-wallet.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, lai pā<PERSON><PERSON><PERSON><PERSON> savu karti", "card.connectWalletToCardGuide": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "card.connectWalletToCardGuide.addGnosisPayOwner": "Pievienot Gnosis Pay īpašnieku", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Atver Gnosispay.com ar savu otru maciņu{br}2. Spied „Konts”{br}3. Spied „Konta dati”{br}4. Spied „Rediģēt<PERSON> blakus „Konta īpašnieks” un{br}5. Spied „Pievienot adresi”{br}6. Ielīmē savu Zeal adresi un spied „Saglabāt”", "card.connectWalletToCardGuide.header": "Pievieno {account} Gnosis Pay kartei", "card.connect_card.start": "<PERSON><PERSON><PERSON><PERSON>gt <PERSON> Pay karti", "card.copiedAddress": "Nokopēts {formattedAddress}", "card.disconnect-account.title": "<PERSON><PERSON><PERSON> k<PERSON>", "card.hw-wallet-support-drop.add-owner-btn": "<PERSON><PERSON><PERSON>", "card.hw-wallet-support-drop.disconnect-btn": "Atvienot karti no Zeal", "card.hw-wallet-support-drop.subtitle": "<PERSON><PERSON><PERSON>nieku uz ne-hardware maciņu.", "card.hw-wallet-support-drop.title": "Zeal vairs neatbalsta Hardware maciņus kartei", "card.kyc.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.list_item.title": "<PERSON><PERSON>", "card.onboarded.transactions.empty.description": "<PERSON> maks<PERSON><PERSON><PERSON> būs <PERSON>eit", "card.onboarded.transactions.empty.title": "<PERSON><PERSON><PERSON><PERSON>", "card.order.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "card.order.free_virtual_card": "<PERSON><PERSON><PERSON><PERSON> virtuālo karti", "card.order.start": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON> karti", "card.owner-not-imported.cancel": "Atcelt", "card.owner-not-imported.import": "Importēt", "card.owner-not-imported.subtitle": "Lai autorizētu <PERSON>, pie<PERSON><PERSON><PERSON> savu Gnosis Pay konta īpašnieka maciņu. Svarīgi: tas nav tas pats, kas tava Gnosis Pay maciņa pierakstīšanās.", "card.owner-not-imported.title": "Pievienot Gnosis Pay konta īpašnieku", "card.page.order_free_physical_card": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fi<PERSON> karti", "card.pin.change_pin_at_atm": "PIN kodu var nomainīt noteiktos bankomātos", "card.pin.timeout": "<PERSON><PERSON><PERSON><PERSON><PERSON> tiks aizv<PERSON>rts pēc {seconds} sek.", "card.quick-actions.add-assets": "<PERSON><PERSON><PERSON>", "card.quick-actions.add-cash": "<PERSON><PERSON><PERSON>", "card.quick-actions.details": "Detaļ<PERSON>", "card.quick-actions.freeze": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.freezing": "<PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.unfreeze": "Atsaldēt", "card.quick-actions.unfreezing": "Atsaldē", "card.quick-actions.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.read-only-detected.create-new": "<PERSON><PERSON><PERSON> jaunu mac<PERSON> k<PERSON>", "card.read-only-detected.import-current-owner": "Importēt atslēgas {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Importē privātās atslēgas vai sākumfr<PERSON><PERSON> maci<PERSON> {address}", "card.read-only-detected.title": "Karte atrasta tikai las<PERSON> maciņ<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON> mac<PERSON>, lai p<PERSON><PERSON><PERSON><PERSON><PERSON> karti", "card.remove-owner.queued": "Īpašnieka noņemšana ievietota rindā", "card.settings.disconnect-from-zeal": "Atvienot no Zeal", "card.settings.edit-owners": "<PERSON><PERSON><PERSON> ka<PERSON>", "card.settings.getCard": "<PERSON>ņemt vēl vienu karti", "card.settings.getCard.subtitle": "Virt<PERSON><PERSON><PERSON><PERSON><PERSON> vai fizisk<PERSON>s kartes", "card.settings.notRecharging": "Nav i<PERSON><PERSON><PERSON>ta", "card.settings.notifications.subtitle": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON><PERSON><PERSON> p<PERSON>", "card.settings.notifications.title": "<PERSON><PERSON><PERSON>", "card.settings.page.title": "<PERSON><PERSON><PERSON>", "card.settings.select-card.cancelled-cards": "<PERSON><PERSON><PERSON><PERSON><PERSON> kartes", "card.settings.setAutoRecharge": "Iestatīt automā<PERSON>", "card.settings.show-card-address": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> ad<PERSON>i", "card.settings.spend-limit": "<PERSON><PERSON><PERSON><PERSON><PERSON> tēri<PERSON> limitu", "card.settings.spend-limit-title": "Paš<PERSON>iz<PERSON><PERSON><PERSON> die<PERSON> limits: {limit}", "card.settings.switch-active-card": "Mainīt aktīvo karti", "card.settings.switch-active-card-description": "Aktīvā karte: {card}", "card.settings.switch-card.card-item.cancelled": "Atcelta", "card.settings.switch-card.card-item.frozen": "<PERSON><PERSON><PERSON>ē<PERSON>", "card.settings.switch-card.card-item.title": "Gnosis Pay karte", "card.settings.switch-card.card-item.title.physical": "Fiziskā karte", "card.settings.switch-card.card-item.title.virtual": "Virtu<PERSON><PERSON><PERSON> karte", "card.settings.switch-card.title": "Izvēlies karti", "card.settings.targetBalance": "Mērķa saldo: {threshold}", "card.settings.view-pin": "Skatīt PIN", "card.settings.view-pin-description": "Vienmēr <PERSON> savu PIN", "card.title": "<PERSON><PERSON>", "card.transactions.header": "<PERSON><PERSON><PERSON>", "card.transactions.see_all": "<PERSON><PERSON><PERSON><PERSON> visus dar<PERSON>", "card.virtual": "VIRTUĀLA", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Cashback tiek nosūtīts uz tavu karti nākamās nedē<PERSON>as sākumā.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "<PERSON>, jo v<PERSON><PERSON><PERSON> ar katru pirkumu.", "cardCashback.onboarding.title": "<PERSON><PERSON><PERSON> {percentage} cashback", "cardCashbackWithdraw.amount": "<PERSON><PERSON><PERSON><PERSON><PERSON> summa", "cardCashbackWithdraw.header": "Izmaksāt {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "Karte ir bloķēta un to nevarēja aktivizēt", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "Līdz {percentage}", "cardWidget.startEarning": "<PERSON><PERSON><PERSON>", "cardWithdraw.amount": "<PERSON>zņ<PERSON><PERSON><PERSON> summa", "cardWithdraw.header": "<PERSON>zņemt no kartes", "cardWithdraw.selectWithdrawWallet.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, {br}k<PERSON><PERSON> i<PERSON>", "cardWithdraw.success.cta": "Aizvērt", "cardWithdraw.success.subtitle": "Drošības nolūkos naudas izņemšana no Gnosis Pay kartes aizņem 3 minūtes", "cardWithdraw.success.title": "<PERSON><PERSON><PERSON> izmaiņas aizņems 3 minūtes", "card_top_up_trx.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card_top_up_trx.to": "<PERSON><PERSON>", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "<PERSON><PERSON><PERSON><PERSON>", "cards.card_number": "<PERSON><PERSON><PERSON> numurs", "cards.choose-wallet.no-active-accounts": "Tev nav aktīvu maciņu", "cards.copied_card_number": "<PERSON><PERSON><PERSON> num<PERSON>", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> die<PERSON> limits", "cards.transactions.decline_reason.incorrect_pin": "Nepareizs PIN", "cards.transactions.decline_reason.incorrect_security_code": "Nepareizs drošības kods", "cards.transactions.decline_reason.invalid_amount": "<PERSON><PERSON><PERSON><PERSON> summa", "cards.transactions.decline_reason.low_balance": "Nepietiekams saldo", "cards.transactions.decline_reason.other": "<PERSON><PERSON><PERSON><PERSON>", "cards.transactions.decline_reason.pin_tries_exceeded": "P<PERSON><PERSON>niegts PIN mēģinājumu skaits", "cards.transactions.status.refund": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cards.transactions.status.reversal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback-deposit.trx.title": "<PERSON><PERSON><PERSON><PERSON> kont<PERSON>", "cashback-estimate.text": "<PERSON><PERSON> ir aplēse, nevis garantētas izmaksas. Tiek piemēroti visi publiski zināmie cashback noteikumi, taču Gnosis Pay pēc saviem ieskatiem var izslēgt darījumus. Maksimālā tēriņu summa {amount} ned<PERSON>ļā kvalificējas cashback sa<PERSON><PERSON>, pat ja šī darījuma aplēse norādītu uz lielāku kopējo summu.", "cashback-estimate.text.fallback": "Aprēķins, ne garantija. Gnosis Pay lemj.", "cashback-estimate.title": "Aptuvenais cashback", "cashback-onbarding-tersm.subtitle": "Tavi kartes transakciju dati tiks nodoti <PERSON>, kas ir atbild<PERSON>gs par cashback atlīdz<PERSON><PERSON> sadali. Noklikšķinot uz „Piekr<PERSON>tu”, tu piekrīti Gnosis DAO cashback <terms>noteikumiem un nosacījumiem</terms>", "cashback-onbarding-tersm.title": "Lietošanas noteikumi un konfidencialitāte", "cashback-tx-activity.retry": "Mēģināt vēlreiz", "cashback-unconfirmed-payments-info.subtitle": "Maksājumi kvalificēsies cashback sa<PERSON><PERSON>ša<PERSON>, kad tie tiks apstiprināti pie tirgotāja. Līdz tam tie tiek uzrādīti kā neapstiprināti maksājumi. Neapstiprināti maksājumi nekvalificējas cashback saņem<PERSON>.", "cashback-unconfirmed-payments-info.title": "<PERSON>eaps<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "<PERSON><PERSON><PERSON><PERSON>", "cashback.activity.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.activity.withdrawal": "<PERSON>zmaks<PERSON>", "cashback.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.deposit.amount.label": "<PERSON><PERSON><PERSON><PERSON> summa", "cashback.deposit.change": "{from} uz {to}", "cashback.deposit.confirmation.subtitle": "Cashback likmes tiek atjauninātas reizi nedēļ<PERSON>. <PERSON><PERSON><PERSON><PERSON>, lai palielin<PERSON> nākam<PERSON>s nedē<PERSON>as cashback.", "cashback.deposit.confirmation.title": "<PERSON> sāksi peln<PERSON>t {percentage} no {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "<PERSON><PERSON><PERSON> tokenus pret {currency} tīklā {network} Chain", "cashback.deposit.get.tokens.title": "Saņemt {currency} tokenus", "cashback.deposit.header": "Iemaksāt {currency}", "cashback.deposit.max_label": "Maks.: {amount}", "cashback.deposit.select-wallet.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>ņ<PERSON>, no kura iema<PERSON>t", "cashback.deposit.yourcashback": "Tavs cashback", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, uz kuru{br}i<PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.transaction-details.network-label": "Tīkls", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "No", "cashback.transaction-details.top-row.label-rewards": "Cashback periods", "cashback.transaction-details.top-row.label-withdrawal": "<PERSON><PERSON>", "cashback.transaction-details.transaction": "Transakcijas ID", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Cashback transakcijas", "cashback.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.withdraw.confirmation.cashback_reduction": "<PERSON><PERSON><PERSON> cashback, <PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>, sa<PERSON>zin<PERSON><PERSON> no {before} uz {after}", "cashback.withdraw.queued": "<PERSON>zmaksa ievietota rindā", "cashback.withdrawal.change": "{from} uz {to}", "cashback.withdrawal.confirmation.subtitle": "S<PERSON><PERSON> izmaksu {amount} ar 3 minūšu aizkavi. Tas samazinās tavu cashback līdz {after}.", "cashback.withdrawal.confirmation.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> GNO, cashback samazināsies", "cashback.withdrawal.delayTransaction.title": "GNO izmaksa ar{br} 3 minūšu aizkavi", "cashback.withdrawal.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.withdrawal.yourcashback": "Tavs cashback", "celebration.aave": "<PERSON>ln<PERSON><PERSON> ar <PERSON>", "celebration.cashback.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {code}", "celebration.cashback.subtitleGNO": "{amount} pēd<PERSON><PERSON><PERSON> no<PERSON>s", "celebration.chf": "Nopelnīts ar Frankencoin", "celebration.lido": "Nopelnīts ar Lido", "celebration.sky": "Nopelnīts ar Sky", "celebration.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> cashback", "celebration.well_done.title": "Lieliski!", "change-withdrawal-account.add-new-account": "Pievienot citu bankas kontu", "change-withdrawal-account.item.shortText": "{currency} konts", "check-confirmation.approve.footer.for": "<PERSON><PERSON>", "checkConfirmation.title": "<PERSON><PERSON><PERSON><PERSON> rezult<PERSON>", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin un Ether", "collateral.ethereum": "Ethereum", "collateral.other": "Citi", "collateral.rwa": "<PERSON><PERSON><PERSON><PERSON><PERSON> pasaules aktīvi", "collateral.stablecoins": "Stablecoins (piesaistīti USD)", "collateral.us-t-bills": "ASV T-Bills", "confirm-bank-transfer-recipient.bullet-1": "Bez maksas par digitālajiem EUR", "confirm-bank-transfer-recipient.bullet-2": "Iemaksas uz {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Nodot Gnosis Pay konta datus Monerium, autorizētai un regulētai EMI. <link>Uzzināt vairāk</link>", "confirm-bank-transfer-recipient.bullet-4": "Piekrist Monerium <link>lietošanas noteikumiem</link>", "confirm-bank-transfer-recipient.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.cancel": "Atcelt", "confirm-change-withdrawal-account.confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.saving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.subtitle": "Visi naudas i<PERSON>, ko veiksi no Zeal, nonāks šajā bankas kontā.", "confirm-change-withdrawal-account.title": "<PERSON><PERSON><PERSON>ēj<PERSON>", "confirm-ramove-withdrawal-account.title": "Noņemt bankas kontu", "confirm-remove-withdrawal-account.subtitle": "<PERSON><PERSON> bankas konta dati tiks dzēsti no Zeal. <PERSON> varēsi to pievienot atkal jebkurā laikā.", "confirmTransaction.finalNetworkFee": "<PERSON><PERSON><PERSON> maksa", "confirmTransaction.importKeys": "Importēt atslēgas", "confirmTransaction.networkFee": "<PERSON><PERSON><PERSON> maksa", "confirmation.title": "<PERSON><PERSON><PERSON><PERSON>t {amount} uz {recipient}", "conflicting-monerium-account.add-owner": "Pievienot kā Gnosis Pay īpašnieku", "conflicting-monerium-account.create-wallet": "Izveidot jaunu <PERSON> Wallet", "conflicting-monerium-account.disconnect-card": "Atvienot karti no Zeal un savienot no jauna ar jauno <PERSON>", "conflicting-monerium-account.header": "{wallet} saistīts ar citu Monerium kontu", "conflicting-monerium-account.subtitle": "Maini savu Gnosis Pay īpašnieka maciņu", "connection.diconnected.got_it": "Skaidrs!", "connection.diconnected.page1.subtitle": "Zeal darb<PERSON>s vis<PERSON>, kur darb<PERSON><PERSON>. Vienkārši savieno kā ar Metamask.", "connection.diconnected.page1.title": "<PERSON><PERSON> savienot ar Zeal?", "connection.diconnected.page2.subtitle": "<PERSON> redzēsi daudz opciju. Zeal varētu būt viena no tām. <PERSON>a Zeal neparādās...", "connection.diconnected.page2.title": "Noklikšķini uz Connect Wallet", "connection.diconnected.page3.subtitle": "<PERSON><PERSON><PERSON> pied<PERSON>v<PERSON><PERSON>m savie<PERSON> ar <PERSON>. <PERSON><PERSON><PERSON> vai Injected vajadz<PERSON><PERSON> derēt. Pamēģini!", "connection.diconnected.page3.title": "Izvēlies Metamask", "connectionSafetyCheck.tag.caution": "<PERSON><PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.danger": "Bī<PERSON><PERSON>", "connectionSafetyCheck.tag.passed": "<PERSON>z<PERSON>ēts", "connectionSafetyConfirmation.subtitle": "Vai tiešām vēlies turpināt?", "connectionSafetyConfirmation.title": "<PERSON><PERSON> viet<PERSON> i<PERSON> b<PERSON>", "connection_state.connect.cancel": "Atcelt", "connection_state.connect.changeToMetamask": "Mainīt uz MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Pārslēgties uz MetaMask", "connection_state.connect.connect_button": "<PERSON><PERSON><PERSON>", "connection_state.connect.expanded.connected": "Savie<PERSON><PERSON>", "connection_state.connect.expanded.title": "<PERSON><PERSON><PERSON>", "connection_state.connect.safetyChecksLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON> vietnes dro<PERSON>", "connection_state.connect.safetyChecksLoadingError": "<PERSON><PERSON>zde<PERSON><PERSON><PERSON> p<PERSON><PERSON>t d<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "connection_state.connected.expanded.disconnectButton": "<PERSON><PERSON><PERSON>", "connection_state.connected.expanded.title": "Savie<PERSON><PERSON>", "copied-diagnostics": "Diagnostika nokopēta", "copy-diagnostics": "Kopēt diagnostiku", "counterparty.component.add_recipient_primary_text": "Pievienot bankas saņēmēju", "counterparty.country": "Valsts", "counterparty.countryTitle": "Saņēmēja valsts", "counterparty.currency": "<PERSON><PERSON><PERSON>", "counterparty.delete.success.title": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.edit.success.title": "Izmaiņ<PERSON> sag<PERSON>", "counterparty.errors.country_required": "Jānor<PERSON><PERSON> valsts", "counterparty.errors.first_name.invalid": "<PERSON><PERSON><PERSON><PERSON> jāb<PERSON>t garā<PERSON>m", "counterparty.errors.last_name.invalid": "Uzvārda<PERSON> jāb<PERSON>t garā<PERSON>m", "counterparty.first_name": "<PERSON><PERSON><PERSON>", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Sūtīt uz banku", "countrySelector.noCountryFound": "Neviena valsts nav atrasta", "countrySelector.title": "Izvēlies valsti", "create-passkey.cta": "Izveidot ieejas atslēgu", "create-passkey.extension.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create-passkey.footnote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create-passkey.mobile.cta": "<PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON>", "create-passkey.steps.enable-recovery": "Iestatīt at<PERSON>pšanu no mākoņa", "create-passkey.steps.setup-biometrics": "Iespējot biometrisko d<PERSON>š<PERSON>bu", "create-passkey.subtitle": "<PERSON><PERSON><PERSON> at<PERSON>lēgas ir drošākas par parolēm un tiek šifrētas mākoņkrātuvē, lai tās būtu viegli atgūt.", "create-passkey.title": "Konta drošība", "create-smart-wallet": "Izveidot Smart Wallet", "create-userop.progress.text": "Izveido", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Turpini Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Doties uz Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "Tu jau esi sācis kartes p<PERSON>. <PERSON><PERSON> atpa<PERSON>ļ uz Gnosis Pay vietni, lai to p<PERSON><PERSON><PERSON>.", "create_recharge_preferences.card": "<PERSON><PERSON>", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "Pelni {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "<PERSON><PERSON><PERSON><PERSON> naudu", "create_recharge_preferences.link_accounts_title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "Tava karte NETIKS automātiski papildināta pēc katra maksājuma.", "create_recharge_preferences.not_configured_title": "Pelni un tērē", "create_recharge_preferences.recharge_from_earn_accounts_description": "Tava karte tiek automātiski papildināta pēc katra maksājuma no tava Earn konta.", "create_recharge_preferences.subtitle": "gadā", "creating-account.loading": "<PERSON><PERSON><PERSON> kont<PERSON>...", "creating-gnosis-pay-account": "<PERSON><PERSON><PERSON> k<PERSON>", "currencies.bridge.select_routes.emptyState": "<PERSON><PERSON> ma<PERSON>", "currency.add_currency.add_token": "<PERSON><PERSON><PERSON>", "currency.add_currency.not_a_valid_address": "<PERSON><PERSON> nav derīga tokena adrese", "currency.add_currency.token_decimals_feild": "<PERSON><PERSON><PERSON>", "currency.add_currency.token_feild": "<PERSON><PERSON>a adrese", "currency.add_currency.token_symbol_feild": "Tokena simbols", "currency.add_currency.update_token": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.add_custom.remove_token.cta": "<PERSON><PERSON><PERSON><PERSON>", "currency.add_custom.remove_token.header": "Noņemt tokenu", "currency.add_custom.remove_token.subtitle": "Tokens paliks tavā <PERSON>, bet Zeal portfelī nebūs redzams.", "currency.add_custom.token_removed": "Tokens noņemts", "currency.add_custom.token_updated": "<PERSON><PERSON><PERSON> at<PERSON>", "currency.balance_label": "Saldo: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "Veiksmīgi p<PERSON> {fiat} uz {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "<PERSON><PERSON> {crypto}", "currency.bankTransfer.deposit_status.success": "Saņemts tavā maciņā", "currency.bankTransfer.deposit_status.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.check_bank_account": "Pārbaudi savu bankas kontu", "currency.bankTransfer.off_ramp.complete": "<PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Sūta uz tavu banku", "currency.bankTransfer.off_ramp.transferring_to_currency": "Pārskaita uz {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Līdzekļiem šobrīd jau vajadzētu būt tavā bankas kontā.", "currency.bankTransfer.withdrawal_status.success": "Nosūtīts uz tavu banku", "currency.bankTransfer.withdrawal_status.title": "<PERSON>zmaks<PERSON>", "currency.bank_transfer.create_unblock_user.email": "E-pasta adrese", "currency.bank_transfer.create_unblock_user.email_invalid": "Nederīga e-pasta adrese", "currency.bank_transfer.create_unblock_user.email_missing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_missing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "Atļauti tikai burti, cip<PERSON>, atstarpes un - . , & ( ) '.", "currency.bank_transfer.create_unblock_user.last_name": "Uzvārds", "currency.bank_transfer.create_unblock_user.last_name_missing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "Atļauti tikai burti, cip<PERSON>, atstarpes un - . , & ( ) '.", "currency.bank_transfer.create_unblock_user.note": "Turpinot tu piekr<PERSON><PERSON> (mūsu banku partnera) <terms>noteikumiem</terms> un <policy>privātuma politikai</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "<PERSON>eraks<PERSON> savu vārdu un uzvārdu tieši tā, kā bankas kontā", "currency.bank_transfer.create_unblock_user.title": "Piesaisti savu bankas kontu", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Konta numurs", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Bankas valsts", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "<PERSON><PERSON><PERSON><PERSON> valūta", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Bankas kods", "currency.bank_transfer.create_unblock_withdraw_account.success": "<PERSON><PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.title": "Piesaisti savu bankas kontu", "currency.bank_transfer.residence-form.address-required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lauks", "currency.bank_transfer.residence-form.address-unsupported-char": "Atļauti tikai burti, cipari, atstarpes un , ; {apostrophe} - \\\\ atļauti.", "currency.bank_transfer.residence-form.city-required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lauks", "currency.bank_transfer.residence-form.city-unsupported-char": "Atļauti tikai burti, cip<PERSON>, atstarpes un . , - & ( ) {apostrophe} atļauti.", "currency.bank_transfer.residence-form.postcode-invalid": "<PERSON><PERSON>īgs pasta indekss", "currency.bank_transfer.residence-form.postcode-required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lauks", "currency.bank_transfer.validation.invalid.account_number": "Ned<PERSON>īgs konta numurs", "currency.bank_transfer.validation.invalid.iban": "Nederīgs IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Nederīgs bankas kods", "currency.bridge.amount_label": "<PERSON>mma, ko sūt<PERSON>t caur Bridge", "currency.bridge.best_returns.subtitle": "Šis Bridge nodrošinātājs sniegs tev visliel<PERSON><PERSON> summu, ieskaitot visas komisijas.", "currency.bridge.best_returns_popup.title": "Labākā atdeve", "currency.bridge.bridge_from": "No", "currency.bridge.bridge_gas_fee_loading_failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>t tīkla maksu", "currency.bridge.bridge_low_slippage": "Ļoti zems slippage. <PERSON><PERSON><PERSON> to.", "currency.bridge.bridge_provider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bridge.bridge_provider_loading_failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> paka<PERSON> sniedz<PERSON>s", "currency.bridge.bridge_settings": "Bridge iestatīju<PERSON>", "currency.bridge.bridge_status.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> {name}", "currency.bridge.bridge_status.title": "Bridge", "currency.bridge.bridge_to": "Uz", "currency.bridge.fastest_route_popup.subtitle": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> piedāv<PERSON> trans<PERSON> ma<PERSON>.", "currency.bridge.fastest_route_popup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bridge.from": "No", "currency.bridge.success": "<PERSON><PERSON><PERSON><PERSON>", "currency.bridge.title": "Bridge", "currency.bridge.to": "Uz", "currency.bridge.topup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {symbol}", "currency.bridge.withdrawal_status.title": "<PERSON>zmaks<PERSON>", "currency.card.card_top_up_status.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> karti", "currency.destination_amount": "<PERSON>ņ<PERSON><PERSON><PERSON> summa", "currency.hide_currency.confirm.subtitle": "Paslēp šo tokenu no sava portfeļa. To varēsi jebkurā laikā parādīt atpa<PERSON>.", "currency.hide_currency.confirm.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>u", "currency.hide_currency.success.title": "Tokens paslēpts", "currency.label": "Nosaukums (nav obligāts)", "currency.last_name": "Uzvārds", "currency.max_loading": "Maks.:", "currency.swap.amount_to_swap": "<PERSON><PERSON><PERSON> summa", "currency.swap.best_return": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.swap.destination_amount": "<PERSON>ņ<PERSON><PERSON><PERSON> summa", "currency.swap.header": "<PERSON><PERSON><PERSON>", "currency.swap.max_label": "Saldo: {amount}", "currency.swap.provider.header": "<PERSON><PERSON><PERSON>", "currency.swap.select_to_token": "Izvēlies tokenu", "currency.swap.swap_gas_fee_loading_failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>t tīkla maksu", "currency.swap.swap_provider_loading_failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.swap.swap_settings": "<PERSON><PERSON><PERSON>", "currency.swap.swap_slippage_too_low": "Ļoti zema novirze. Mēģini to pali<PERSON><PERSON>t.", "currency.swaps_io_native_token_swap.subtitle": "Izmantojot Swaps.IO", "currency.swaps_io_native_token_swap.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.withdrawal.amount_from": "No", "currency.withdrawal.amount_to": "Uz", "currencySelector.title": "Izvēlies valūtu", "dApp.wallet-does-not-support-chain.subtitle": "Šķiet, ka tavs maciņ<PERSON> {network}. Mēģini izveidot savienojumu ar citu maciņu vai i<PERSON>.", "dApp.wallet-does-not-support-chain.title": "Neatbalstīts tīkls", "dapp.connection.manage.confirm.disconnect.all.cta": "Atvienot", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Vai tiešām vēlies atvienot visus savienojumus?", "dapp.connection.manage.confirm.disconnect.all.title": "<PERSON><PERSON><PERSON> visus", "dapp.connection.manage.connection_list.main.button.title": "Atvienot", "dapp.connection.manage.connection_list.no_connections": "Tev nav savienotu lietotņu", "dapp.connection.manage.connection_list.section.button.title": "<PERSON><PERSON><PERSON> visus", "dapp.connection.manage.connection_list.section.title": "<PERSON><PERSON><PERSON><PERSON>", "dapp.connection.manage.connection_list.title": "Savienoju<PERSON>", "dapp.connection.manage.disconnect.success.title": "Lietotnes atvienotas", "dapp.metamask_mode.title": "MetaMask režīms", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "<PERSON><PERSON><PERSON>", "dc25-card-marketing-card.right.subtitle": "100 cilvēkiem", "dc25-card-marketing-card.title": "<PERSON><PERSON><PERSON> 100, kas iztērēs €50, nopelnīs {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Tu nevarēsi veikt šo darbību 3 minūtes. Drošības apsvērumu dēļ jebkuras karšu iestatījumu izmaiņas vai izmaksas tiek apstrādātas 3 minūtes.", "delayQueueBusyBanner.processing-yout-action.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tavu da<PERSON>, l<PERSON><PERSON><PERSON>, uzgaidi", "delayQueueBusyWidget.cardFrozen": "<PERSON><PERSON>", "delayQueueBusyWidget.processingAction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tavu darbību", "delayQueueFailedBanner.action-incomplete.get-support": "Saņemt atbalstu", "delayQueueFailedBanner.action-incomplete.subtitle": "<PERSON><PERSON><PERSON>, rad<PERSON><PERSON> k<PERSON> ar naudas i<PERSON> vai iestatījumu at<PERSON>. <PERSON><PERSON><PERSON><PERSON>, sazinies ar atbalstu Discord.", "delayQueueFailedBanner.action-incomplete.title": "Darbība nav pabeigta", "delayQueueFailedWidget.actionIncomplete.title": "<PERSON><PERSON>s da<PERSON> nav pabeigta", "delayQueueFailedWidget.cardFrozen.subtitle": "<PERSON><PERSON>", "delayQueueFailedWidget.contactSupport": "Sazināties ar at<PERSON><PERSON> dienestu", "delay_queue_busy.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mainot kartes iestatījumus vai izņemot naudu, ir 3 minū<PERSON>u aizkave. <PERSON><PERSON><PERSON> laikā tava karte ir iesaldēta.", "delay_queue_busy.title": "<PERSON><PERSON> darb<PERSON>ba tiek apstrād<PERSON>ta", "delay_queue_failed.contact_support": "Sazinies", "delay_queue_failed.subtitle": "<PERSON><PERSON><PERSON>, rad<PERSON><PERSON> k<PERSON> ar naudas i<PERSON> vai iestatījumu at<PERSON>. <PERSON><PERSON><PERSON><PERSON>, sazinies ar atbalstu Discord.", "delay_queue_failed.title": "Sazinies ar atbalstu", "deploy-earn-form-smart-wallet.in-progress.title": "Sagatavo Earn", "deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disconnect-card-popup.cancel": "Atcelt", "disconnect-card-popup.disconnect": "Atvienot", "disconnect-card-popup.subtitle": "Tava karte tiks noņemta no Zeal lietotnes. <PERSON><PERSON><PERSON> j<PERSON> būs savienots ar karti Gnosis Pay lietotnē. Karti varēsi atkal pievienot jebkurā laikā.", "disconnect-card-popup.title": "Atvienot karti", "distance.long.days": "{count} dienas", "distance.long.hours": "{count} stundas", "distance.long.minutes": "{count} minūtes", "distance.long.months": "{count} m<PERSON><PERSON><PERSON>i", "distance.long.seconds": "{count} sekundes", "distance.long.years": "{count} gadi", "distance.short.days": "{count} d", "distance.short.hours": "{count} h", "distance.short.minutes": "{count} min", "distance.short.months": "{count} mēn.", "distance.short.seconds": "{count} sec", "distance.short.years": "{count} g", "duration.short.days": "{count}d", "duration.short.hours": "{count}h", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn-deposit-view.into": "<PERSON><PERSON>", "earn-deposit-view.to": "Uz", "earn-deposit.swap.transfer-provider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.accrued-realtime": "<PERSON>z<PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.asset-class": "Aktī<PERSON> klase", "earn-taker-investment-details.asset-coverage-ratio": "<PERSON><PERSON><PERSON><PERSON> seguma at<PERSON>", "earn-taker-investment-details.asset-reserve": "Aktīvu rezerve", "earn-taker-investment-details.base_currency.label": "<PERSON><PERSON><PERSON> val<PERSON>", "earn-taker-investment-details.chf.description": "Pelni procentus par saviem CHF, noguldot zCHF Frankencoin – uzticamā digitālās naudas tirgū. Procenti tiek gūti no zema riska, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nodrošinātiem aizdevumiem Frankencoin platformā un izmaksāti reāllaikā. Tavi līdzekļi ir drošībā apakškontā, ko kontrolē tikai tu.", "earn-taker-investment-details.chf.description.with_address_link": "Pelni procentus par saviem CHF, noguldot zCHF Frankencoin – uzticamā digitālās naudas tirgū. Procenti tiek gūti no zema riska, pā<PERSON><PERSON><PERSON><PERSON>gi nodrošinātiem aizdevumiem Frankencoin platformā un izmaksāti reāllaikā. Tavi līdzekļi ir drošībā apakškontā, <link>(kopēt 0x)</link> ko kontrolē tikai tu.", "earn-taker-investment-details.chf.label": "<PERSON><PERSON><PERSON><PERSON><PERSON> franks", "earn-taker-investment-details.collateral-composition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sastā<PERSON>", "earn-taker-investment-details.depositor-obligations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.eure.description": "Pelni procentus no saviem eiro, noguldot EURe Aave – uzticamā digitālās naudas tirgū. EURe ir pilnībā regulēts eiro stablecoin, ko izdevis Monerium un kas ir 1:1 nodrošināts aizsargātos kontos. Procenti tiek gūti no zema riska, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nodrošinātiem aizdevumiem Aave un izmaksāti reāllaikā. Tavi līdzekļi atrodas drošā apakškontā, ko kontrolē tikai tu.", "earn-taker-investment-details.eure.description.with_address_link": "Pelni procentus no saviem eiro, noguldot EURe Aave – uzticamā digitālās naudas tirgū. EURe ir pilnībā regulēts eiro stablecoin, ko izdevis Monerium un kas ir 1:1 nodrošināts aizsargātos kontos. Procenti tiek gūti no zema riska, pā<PERSON><PERSON><PERSON><PERSON>gi nodrošinātiem aizdevumiem Aave un izmaksāti reāllaikā. Tavi līdzekļi atrodas drošā apakškontā <link>(kopēt 0x)</link> , ko kontrolē tikai tu.", "earn-taker-investment-details.eure.label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (EURe)", "earn-taker-investment-details.faq": "BUJ", "earn-taker-investment-details.fixed-income": "Fiksēts ienākums", "earn-taker-investment-details.issuer": "Emitents", "earn-taker-investment-details.key-facts": "Svarīgākie fakti", "earn-taker-investment-details.liquidity": "Likviditāte", "earn-taker-investment-details.operator": "Tirgus operators", "earn-taker-investment-details.projected-yield": "Prognozētais gada ienesī<PERSON>s", "earn-taker-investment-details.see-other-faq": "<PERSON><PERSON>īt visus citus BUJ", "earn-taker-investment-details.see-realtime": "<PERSON><PERSON><PERSON><PERSON> datus", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "I<PERSON><PERSON>ī<PERSON><PERSON> pēd<PERSON> 12 mēnešos", "earn-taker-investment-details.total-collateral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "Kopējais ZCHF piedāvājums", "earn-taker-investment-details.total_deposits": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.usd.description": "Sky ir digitāls naudas tirgus, kas pied<PERSON><PERSON> stabilu, ASV dolāros denominētu ienesīgumu no īstermiņa ASV Valsts kases parādzīmēm un pārmērīgi nodrošinātiem aizdevumiem — bez kriptovalūtu svārstībām, ar 24/7 piekļuvi līdzekļiem un caurspīdīgu, ķēdē balstītu nodrošinājumu.", "earn-taker-investment-details.usd.description.with_address_link": "Sky ir digitāls naudas tirgus, kas pied<PERSON><PERSON> stabilu, ASV dolāros denominētu ienesīgumu no īstermiņa ASV Valsts kases parādzīmēm un pārmērīgi nodrošinātiem aizdevumiem — bez kriptovalūtu svārstībām, ar 24/7 piekļuvi līdzekļiem un caurspīdīgu, ķēdē balstītu nodrošinājumu. Ieguldījumi atrodas apakškontā, <link>(kopēt 0x)</link> ko kontrolē tu.", "earn-taker-investment-details.usd.ftx-difference": "Kā tas atšķiras no <PERSON>T<PERSON>, <PERSON><PERSON><PERSON>, BlockFi vai Luna?", "earn-taker-investment-details.usd.high-returns": "<PERSON><PERSON> ienesīgums var būt tik augsts, <PERSON><PERSON><PERSON><PERSON> salī<PERSON>ot ar tradicionālajām bankām?", "earn-taker-investment-details.usd.how-is-backed": "Kā tiek nodro<PERSON>ts Sky USD, un kas notiek ar tavu naudu, ja <PERSON><PERSON> bankrotē?", "earn-taker-investment-details.usd.income-sources": "Ienākumu avoti 2024", "earn-taker-investment-details.usd.insurance": "Vai tavi līdzekļi ir apdrošināti vai garantēti kādā iestādē (piemēram, FDIC vai līdzīgā)?", "earn-taker-investment-details.usd.label": "Digitālais ASV dolārs", "earn-taker-investment-details.usd.lose-principal": "Vai es reāli varu zaudēt savu pamatsummu, un kādos apstākļos?", "earn-taker-investment-details.variable-rate": "Aizdevumi ar <PERSON> likmi", "earn-taker-investment-details.withdraw-anytime": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "earn-taker-investment-details.yield": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-withdrawal-view.approve.for": "<PERSON><PERSON>", "earn-withdrawal-view.approve.into": "<PERSON><PERSON>", "earn-withdrawal-view.swap.into": "<PERSON><PERSON>", "earn-withdrawal-view.withdraw.to": "<PERSON><PERSON>", "earn.add_another_asset.title": "Izvēlies pelnīšanas aktīvu", "earn.add_asset": "<PERSON><PERSON><PERSON>", "earn.asset_view.title": "<PERSON><PERSON><PERSON>", "earn.base-currency-popup.text": "Bāzes valūtā tiek novērtēti un reģistrēti tavi noguldījumi, i<PERSON>sīgums un darījumi. Ja noguldi citā valūtā (piemēram, EUR uz USD), tavi līdzekļi tiek nekavējoties konvertēti bāzes valūtā pēc pašreizējā maiņas kursa. Pēc konvertācijas tavs saldo paliek stabils bāzes valūtā, bet turpmākās izmaksas var atkal ietvert valūtas konvertāciju.", "earn.base-currency-popup.title": "<PERSON><PERSON><PERSON> val<PERSON>", "earn.card-recharge.disabled.list-item.title": "Automā<PERSON><PERSON><PERSON> pap<PERSON>", "earn.card-recharge.enabled.list-item.title": "Automātiskā papil<PERSON> i<PERSON>ē<PERSON>ta", "earn.choose_wallet_to_deposit.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> no", "earn.config.currency.eth": "Pelni Ethereum", "earn.config.currency.on_chain_address_subtitle": "Ķēdes adrese", "earn.config.currency.us_dollars": "Iestatīt bankas pārskaitījumus", "earn.configured_widget.current_apy.title": "Pašreizējais APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} gadā", "earn.confirm.currency.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.currency.eth": "Pelni Ethereum", "earn.deploy.status.title": "Izveidot Earn kontu", "earn.deploy.status.title_with_taker": "<PERSON>zveidot {title} <PERSON><PERSON>n kontu", "earn.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.deposit.amount_to_deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> summa", "earn.deposit.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.deposit.enter_amount": "<PERSON><PERSON><PERSON> summu", "earn.deposit.no_routes_found": "Maršruti nav atrasti", "earn.deposit.not_enough_balance": "Nepietiekams saldo", "earn.deposit.select-currency.title": "Izvēlies tokenu noguldīšanai", "earn.deposit.select_account.title": "Izvēlies Earn kontu", "earn.desposit_form.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.earn_deposit.status.title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "earn.earn_deposit.trx.title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON> jebkur<PERSON> laikā", "earn.earn_withdraw.status.title": "Izmaksa no Earn konta", "earn.earn_withdraw.trx.title.approval": "Aps<PERSON>rin<PERSON><PERSON> iz<PERSON>ksu", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Izmaks<PERSON>t uz {asset}", "earn.earn_withdraw.trx.title.withdrawal": "Izmaksa no Earn konta", "earn.recharge.cta": "Saglabāt iz<PERSON>", "earn.recharge.earn_not_configured.enable_some_account.error": "Aktivizē kontu", "earn.recharge.earn_not_configured.enter_amount.error": "<PERSON><PERSON><PERSON> summu", "earn.recharge.select_taker.header": "<PERSON><PERSON><PERSON><PERSON><PERSON>t karti secībā no", "earn.recharge_card_tag.on": "ieslēgta", "earn.recharge_card_tag.recharge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharge_not_configured": "Automā<PERSON><PERSON><PERSON> p<PERSON>", "earn.recharge_card_tag.recharge_off": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharged": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharging": "<PERSON><PERSON><PERSON><PERSON>", "earn.recharge_configured.disable.trx.title": "Atslē<PERSON> automā<PERSON>", "earn.recharge_configured.trx.disclaimer": "Kad lieto savu karti, tiek izveidota Cowswap izsole, lai par tavu maksājuma summu nopirktu attiecīgu kriptovalūtu no tava Earn konta. Šis izsoles process parasti nodrošina labāko tirgus kursu, ta<PERSON><PERSON>em vērā, ka blokķēdes kurss var atšķirties no reālās pasaules valūtu kursiem.", "earn.recharge_configured.trx.subtitle": "Pēc katra maksājuma naudai no tava Earn konta(-iem) tiks automātiski papildināts kartes saldo, lai tas būtu {value}", "earn.recharge_configured.trx.title": "Iestatīt automā<PERSON> p<PERSON> uz {value}", "earn.recharge_configured.updated.trx.title": "Sagla<PERSON><PERSON><PERSON> p<PERSON> i<PERSON>", "earn.risk-banner.subtitle": "Privāts produkts. Nav aizsardzības.", "earn.risk-banner.title": "<PERSON><PERSON><PERSON><PERSON> riskus", "earn.set_recharge.status.title": "Iestatīt automā<PERSON>", "earn.setup_reacharge.input.disable.label": "At<PERSON>ē<PERSON><PERSON>", "earn.setup_reacharge.input.label": "Vēlamais kartes saldo", "earn.setup_reacharge_form.title": "Automātisk<PERSON> papildin<PERSON>na uztur tavu {br} kartes saldo ne<PERSON>gu", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Turi wstETH (Staked ETH) Gnosis Chain tīklā un aizdod caur Lido.", "earn.taker-bulletlist.point_1": "Pelni {apyValue} gadā. Atdeve mainās līdz ar tirgu.", "earn.taker-bulletlist.point_3": "Zeal ne<PERSON><PERSON><PERSON><PERSON> k<PERSON> ma<PERSON>.", "earn.taker-historical-returns": "Vēsturiskā atdeve", "earn.taker-historical-returns.chf": "CHF pieaugums attiec<PERSON>b<PERSON> pret USD", "earn.taker-investment-tile.apy.perYear": "gadā", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Pelnīšana no Aave Gnosis Chain tīklā", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Ienākumi no Frankencoin Gnosis Chain tīklā", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Pelnīšana no Lido Gnosis Chain tīklā", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Pelnīšana no Maker Gnosis Chain tīklā", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "<PERSON><PERSON> peln<PERSON><PERSON>na", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH Earn", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "ASV Valsts ka<PERSON> (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Lai gan tas ir <PERSON><PERSON><PERSON> reti, te<PERSON><PERSON><PERSON><PERSON> tas ir iespējams. <PERSON><PERSON><PERSON> l<PERSON> aizsargā stingra riska pārvaldība un augsts nodrošinājuma līmenis. Reālistiskākais sliktākais scenārijs iet<PERSON> nepie<PERSON>z<PERSON>tus tirgus apst<PERSON>, <PERSON><PERSON><PERSON><PERSON>, vairāku stablecoin vienlaicīgu piesaistes z<PERSON> — kas nekad agrāk nav noticis.", "earn.usd.can-I-lose-my-principal-popup.title": "Vai es reāli varu zaudēt savu pamatsummu, un kādos apstākļos?", "earn.usd.ftx-difference-popup.text": "Sky ir fundamentāli atšķirīgs. Atšķirībā no FTX, <PERSON><PERSON><PERSON>, Block<PERSON>i vai <PERSON>, kas liel<PERSON> mērā paļāvās uz centralizētu gla<PERSON>, necaurspīdīgu aktīvu pārvaldību un riskantām piesaistītā kapitā<PERSON>, Sky USD izmanto caurs<PERSON>, auditētus, decentralizētus viedos līgumus un uztur pilnīgu caurspīdīgumu ķēdē. Tu saglabā pilnīgu kontroli pār savu privāto maciņu, iev<PERSON><PERSON><PERSON><PERSON> samazinot darījuma partnera riskus, kas saistīti ar centralizētām neveiksmēm.", "earn.usd.ftx-difference-popup.title": "Kā tas atšķiras no <PERSON>T<PERSON>, <PERSON><PERSON><PERSON>, BlockFi vai Luna?", "earn.usd.high-returns-popup.text": "Sky USD gūst iene<PERSON><PERSON><PERSON><PERSON> gal<PERSON>, i<PERSON><PERSON><PERSON><PERSON> decentralizēto finan<PERSON> (DeFi) protokolus, kas automatizē savstarpējos aizdevumus un likviditātes nodrošināšanu, novēr<PERSON>ot tradicionālās banku administratīvās izmaksas un starpniekus. Šī efektivitāte apvienojumā ar stingru riska kontroli ļauj nodrošināt ievērojami augstāku ienesīgumu, salīdzinot ar tradicionālajām bankām.", "earn.usd.high-returns-popup.title": "<PERSON><PERSON> ienesīgums var būt tik augsts, <PERSON><PERSON><PERSON><PERSON> salī<PERSON>ot ar tradicionālajām bankām?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD ir pilnībā nodrošināts un pārmērīgi nodrošināts ar digitālo aktīvu un reālās pasaules aktīvu, <PERSON><PERSON><PERSON><PERSON>, ASV Valsts kases parādzī<PERSON>ju, k<PERSON><PERSON><PERSON><PERSON><PERSON>, kas tiek turēta dro<PERSON> viedaj<PERSON> līgumos. <PERSON><PERSON>ves var auditēt reāllaikā ķēdē, pat no Zeal lietotnes, nod<PERSON><PERSON><PERSON>t caurspīdīgumu un drošību. Maz ticamā gadījumā, ja Zeal pārtrauks darbību, tavi aktīvi paliks drošībā ķēdē, pilnībā tavā kontrolē un pieejami, izmantojot citus saderīgus maciņus.", "earn.usd.how-is-sky-backed-popup.title": "Kā tiek nodro<PERSON>ts Sky USD, un kas notiek ar tavu naudu, ja <PERSON><PERSON> bankrotē?", "earn.usd.insurance-popup.text": "Sky USD līdzekļi nav apdrošināti FDIC vai nodrošināti ar tradicionālām valdības garantijām, jo tas ir uz digitāliem aktīviem balstīts konts, nevis parasts bankas konts. Tā vietā Sky pārvalda visus riska mazinā<PERSON> pasāku<PERSON>, izmantojot auditētus viedos līgumus un rūpīgi pārbaudītus DeFi protokolus, <PERSON><PERSON><PERSON><PERSON><PERSON>, ka aktīvi paliek droši un caurspīdīgi.", "earn.usd.insurance-popup.title": "Vai tavi līdzekļi ir apdrošināti vai garantēti kādā iestādē (piemēram, FDIC vai līdzīgā)?", "earn.usd.lending-operations-popup.text": "Sky USD gūst i<PERSON><PERSON><PERSON><PERSON><PERSON>, aizdodot stablecoins decentralizētos aizdevumu tirgos, pie<PERSON><PERSON>ram, Morpho un Spark. Tavi stablecoins tiek aizdoti aizņē<PERSON>em, kuri nogulda ievērojami vairāk nodro<PERSON> — pie<PERSON><PERSON>ram, ETH vai BTC — nekā viņu aizdevuma vērtība. <PERSON><PERSON>, ko sauc par pārmēr<PERSON><PERSON><PERSON>, nod<PERSON><PERSON><PERSON>, ka vienmēr ir pietiekami daudz nodro<PERSON>, lai segtu aizdevumus, ievērojami samazinot risku. Iekasētie procenti un neregulārās likvid<PERSON>ci<PERSON> maks<PERSON>, ko maks<PERSON>, nod<PERSON><PERSON><PERSON>, caurspīdīgu un drošu peļņu.", "earn.usd.lending-operations-popup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.usd.market-making-operations-popup.text": "Sky USD gūst papil<PERSON> i<PERSON>, piedaloties decentralizē<PERSON><PERSON><PERSON><PERSON> (AMM), pie<PERSON><PERSON><PERSON>, Curve vai Uniswap. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> likvidit<PERSON>ti — ievietojot tavus stablecoins fondos, kas veicina kriptoval<PERSON> tir<PERSON> — Sky USD iegūst maksas, kas tiek radītas no darījumiem. Šie likviditātes fondi tiek rūpīgi atlasīti, lai samazin<PERSON> sv<PERSON>, galvenokārt izmantojot stablecoin-to-stablecoin pārus, lai iev<PERSON><PERSON><PERSON><PERSON> risk<PERSON>, pie<PERSON><PERSON><PERSON>, ne<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, saglab<PERSON>jo<PERSON> tavus aktīvus gan dro<PERSON>, gan pieejamus.", "earn.usd.market-making-operations-popup.title": "<PERSON><PERSON><PERSON>", "earn.usd.treasury-operations-popup.text": "Sky USD rada stabilu, konsekventu iene<PERSON>, veicot stratēģiskus ieguldījumus valsts kasē. Daļa no taviem stablecoin noguldījumiem tiek novirz<PERSON><PERSON> d<PERSON>, zema riska reālās pasaules aktīvos — galvenokārt īstermiņa valdības obligācijās un augstas drošības kredītinstrumentos. <PERSON><PERSON>, līdzīga tradicionālajai banku darbībai, nodro<PERSON>ina paredzamu un uzticamu ienesīgumu. Tavi aktīvi paliek droši, likvīdi un tiek pārvaldīti caurspīdīgi.", "earn.usd.treasury-operations-popup.title": "Valsts kases <PERSON>", "earn.view_earn.card_rechard_off": "Izsl.", "earn.view_earn.card_rechard_on": "Iesl.", "earn.view_earn.card_recharge": "<PERSON><PERSON><PERSON>", "earn.view_earn.total_balance_label": "P<PERSON>ļņ<PERSON> {percentage} gadā", "earn.view_earn.total_earnings_label": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>a", "earn.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.withdraw.amount_to_withdraw": "<PERSON>zņ<PERSON><PERSON><PERSON> summa", "earn.withdraw.enter_amount": "<PERSON><PERSON><PERSON> summu", "earn.withdraw.loading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.withdraw.no_routes_found": "Maršruti nav atrasti", "earn.withdraw.not_enough_balance": "Nepietiekams saldo", "earn.withdraw.select-currency.title": "Izvēlies tokenu", "earn.withdraw.select_to_token": "Izvēlies tokenu", "earn.withdraw.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.withdraw_form.title": "Izņemt no Earn", "earnings-view.earnings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.add-wallet": "<PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.add_wallet": "<PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.title": "<PERSON><PERSON><PERSON> ka<PERSON>", "edit-account-owners.card-owners": "<PERSON><PERSON><PERSON>", "edit-account-owners.external-wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editBankRecipient.title": "Rediģēt saņēmēju", "editNetwork.addCustomRPC": "<PERSON><PERSON><PERSON>āgotu RPC mezglu", "editNetwork.cannot_verify.subtitle": "Pielāgotais RPC mezgls neatbild. Pārbaudi URL un mēģini vēlreiz.", "editNetwork.cannot_verify.title": "Nevaram verificēt RPC mezglu", "editNetwork.cannot_verify.try_again": "Vēlreiz", "editNetwork.customRPCNode": "Pielāgots RPC mezgls", "editNetwork.defaultRPC": "Noklusējuma RPC", "editNetwork.networkRPC": "Tīkla RPC", "editNetwork.rpc_url.cannot_be_empty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editNetwork.rpc_url.not_a_valid_https_url": "Jābūt derīgam HTTP(S) URL", "editNetwork.safetyWarning.subtitle": "Zeal nevar garant<PERSON>t pielāgoto RPC privātumu, uzticamību un drošību. Vai tiešām vēlies izmantot pielāgotu RPC mezglu?", "editNetwork.safetyWarning.title": "Pielāgoti RPC var b<PERSON><PERSON>", "editNetwork.zealRPCNode": "Zeal RPC mezgls", "editNetworkRpc.headerTitle": "Pielāgots RPC mezgls", "editNetworkRpc.rpcNodeUrl": "RPC mezgla URL", "editing-locked.modal.description": "Atļaujas nevar rediģēt. Uzticies lietotnei.", "editing-locked.modal.title": "Rediģēšana bloķēta", "enable-recharge-for-smart-wallet.enabling-recharge.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enable-recharge-for-smart-wallet.recharge-enabled.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterCardnumber": "<PERSON><PERSON><PERSON> kartes numuru", "error.connectivity_error.subtitle": "Pārbaudi internetu un mēģini vēlreiz.", "error.connectivity_error.title": "Nav interneta savienojuma", "error.decrypt_incorrect_password.title": "Nepareiza parole", "error.encrypted_object_invalid_format.title": "<PERSON><PERSON><PERSON><PERSON> dati", "error.failed_to_fetch_google_auth_token.title": "Neizdev<PERSON><PERSON>", "error.list.item.cta.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error.trezor_action_cancelled.title": "Transakcija <PERSON>", "error.trezor_device_used_elsewhere.title": "Ierīce tiek lietota citā sesijā", "error.trezor_method_cancelled.title": "<PERSON><PERSON>z<PERSON><PERSON><PERSON><PERSON>", "error.trezor_permissions_not_granted.title": "<PERSON><PERSON>z<PERSON><PERSON><PERSON><PERSON>", "error.trezor_pin_cancelled.title": "<PERSON><PERSON>z<PERSON><PERSON><PERSON><PERSON>", "error.trezor_popup_closed.title": "<PERSON><PERSON>z<PERSON><PERSON><PERSON><PERSON>", "error.unblock_account_number_and_sort_code_mismatch": "Konta numurs un kods nesa<PERSON>rīt", "error.unblock_can_not_change_details_after_kyc": "<PERSON><PERSON><PERSON><PERSON>t datus pēc KYC", "error.unblock_hard_kyc_failure": "Negaidīts KYC statuss", "error.unblock_invalid_faster_payment_configuration.title": "Šī banka neatbalsta zibmaksājumus", "error.unblock_invalid_iban": "Nederīgs IBAN", "error.unblock_session_expired.title": "Unblock sesija beigusies", "error.unblock_user_with_address_already_exists.title": "Konts šai adresei jau ir izveidots", "error.unblock_user_with_such_email_already_exists.title": "Lietotājs ar š<PERSON>du e-pastu jau pastāv", "error.unknown_error.error_message": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>: ", "error.unknown_error.subtitle": "Sazinies ar atbalstu un nosūti šo info.", "error.unknown_error.title": "<PERSON><PERSON><PERSON><PERSON>", "eth-cost-warning-modal.subtitle": "Ethereum tīkla maksa augsta. Lieto citus.", "eth-cost-warning-modal.title": "Izvairies no Ethereum – augstas maksas", "exchange.form.button.chain_unsupported": "Tīkls nav atbalstīts", "exchange.form.button.refreshing": "<PERSON><PERSON><PERSON><PERSON>", "exchange.form.error.asset_not_supported.button": "Izvēlies citu aktīvu", "exchange.form.error.asset_not_supported.description": "Bridge neatbalsta šo aktīvu.", "exchange.form.error.asset_not_supported.title": "Aktīvs netiek atbalstīts", "exchange.form.error.bridge_quote_timeout.button": "Izvēlies citu aktīvu", "exchange.form.error.bridge_quote_timeout.description": "Mēģini citu tokenu pāri", "exchange.form.error.bridge_quote_timeout.title": "Maiņa nav atrasta", "exchange.form.error.different_receiver_not_supported.button": "Noņemt alternatīvo saņēmēju", "exchange.form.error.different_receiver_not_supported.description": "<PERSON><PERSON> maiņa <PERSON>sta sūtīšanu uz citu adresi.", "exchange.form.error.different_receiver_not_supported.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> un saņemšanas adresei jābūt vienādai", "exchange.form.error.insufficient_input_amount.button": "<PERSON><PERSON><PERSON><PERSON><PERSON> summu", "exchange.form.error.insufficient_liquidity.button": "<PERSON><PERSON><PERSON><PERSON><PERSON> summu", "exchange.form.error.insufficient_liquidity.description": "Bridge nav pietiekami daudz aktīvu. Mēģini mazāku summu.", "exchange.form.error.insufficient_liquidity.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>la summa", "exchange.form.error.max_amount_exceeded.button": "<PERSON><PERSON><PERSON><PERSON><PERSON> summu", "exchange.form.error.max_amount_exceeded.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> summa ir pārsniegta.", "exchange.form.error.max_amount_exceeded.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>la summa", "exchange.form.error.min_amount_not_met.button": "<PERSON><PERSON><PERSON><PERSON><PERSON> summu", "exchange.form.error.min_amount_not_met.description": "<PERSON><PERSON>am nav sasniegta minimālā maiņas summa.", "exchange.form.error.min_amount_not_met.description_with_amount": "<PERSON><PERSON><PERSON><PERSON><PERSON> maiņas summa ir {amount}.", "exchange.form.error.min_amount_not_met.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> maza summa", "exchange.form.error.min_amount_not_met.title_increase": "<PERSON><PERSON><PERSON><PERSON><PERSON> summu", "exchange.form.error.no_routes_found.button": "Izvēlies citu aktīvu", "exchange.form.error.no_routes_found.description": "<PERSON><PERSON> token<PERSON>/tīkla kombin<PERSON>jai nav pieejams maiņas mar<PERSON>.", "exchange.form.error.no_routes_found.title": "<PERSON>ņa nav pieejama", "exchange.form.error.not_enough_balance.button": "<PERSON><PERSON><PERSON><PERSON><PERSON> summu", "exchange.form.error.not_enough_balance.description": "Tev nav pietiekami daudz šī aktīva šai trans<PERSON>cijai.", "exchange.form.error.not_enough_balance.title": "Nepietiekams saldo", "exchange.form.error.slippage_passed_is_too_low.button": "Palielināt cenu svārst<PERSON>", "exchange.form.error.slippage_passed_is_too_low.description": "Atļautais slippage ir pār<PERSON>k zems.", "exchange.form.error.slippage_passed_is_too_low.title": "Pārāk zems slippage", "exchange.form.error.socket_internal_error.button": "Mēģini vēlāk", "exchange.form.error.socket_internal_error.description": "Bridge partnerim ir rad<PERSON><PERSON> probl<PERSON>. Mēģini vēlāk.", "exchange.form.error.socket_internal_error.title": "Kļūda Bridge partnera pusē", "exchange.form.error.stargatev2_requires_fee_in_native": "<PERSON><PERSON><PERSON> {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "<PERSON><PERSON><PERSON> {amount} , lai p<PERSON><PERSON><PERSON> dar<PERSON>", "exchange.form.error.stargatev2_requires_fee_in_native.title": "<PERSON><PERSON><PERSON> v<PERSON> {symbol}", "expiration-info.modal.description": "Lietotnes piekļuves laiks tokeniem. Saīsini.", "expiration-info.modal.title": "Kas ir derīguma term<PERSON>?", "expiration-time.high.modal.text": "Termiņiem jābūt īsiem un balstītiem uz to, cik ilgi tev atļauja būs vajadz<PERSON>ga. <PERSON><PERSON> termiņi ir <PERSON>, jo dod krāpniekiem vairāk iespēju ļaunprātīgi izmantot tavus tokenus.", "expiration-time.high.modal.title": "Ilgs derīguma termiņš", "failed.transaction.content": "Transakcija, visticamāk, neizdosies", "fee.unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "feedback-request.leave-message": "<PERSON><PERSON><PERSON><PERSON>", "feedback-request.not-now": "<PERSON><PERSON> tagad", "feedback-request.title": "Paldies! Kā mēs varam uz<PERSON>bot Zeal?", "float.input.period": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gnosis-activate-card.info-popup.subtitle": "<PERSON><PERSON><PERSON> ma<PERSON>ju<PERSON>: ievieto karti un PIN.", "gnosis-activate-card.info-popup.title": "Pirmajam maksājumam nepieciešams čips un PIN", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "<PERSON><PERSON><PERSON> savas kartes numuru, lai to aktivizētu.", "gnosis-activate-card.title": "<PERSON><PERSON><PERSON> numurs", "gnosis-pay-re-kyc-widget.btn-text": "Verificēt", "gnosis-pay-re-kyc-widget.title.not-started": "Verificē savu identitāti", "gnosis-pay.login.cta": "<PERSON><PERSON><PERSON> kontu", "gnosis-pay.login.title": "Tev jau ir G<PERSON> Pay konts", "gnosis-signup.confirm.subtitle": "Meklē e-pastu no Gnosis Pay, tas varētu būt mēstuļu mapē.", "gnosis-signup.confirm.title": "Nesaņēmi verifikācijas e-pastu?", "gnosis-signup.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gnosis-signup.dont_link_accounts": "Nesaist<PERSON><PERSON> kontus", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "<PERSON><PERSON>di <EMAIL>", "gnosis-signup.enter-email.title": "Ievadi e-pastu", "gnosis-signup.title": "Esmu <PERSON> un piekrītu Gnosis Pay <linkGnosisTNC>Noteikumiem un nosacījumiem</linkGnosisTNC> <monovateTerms><PERSON><PERSON><PERSON> turētāja noteikumiem</monovateTerms> un <linkMonerium>Monerium noteikumiem un nosacījumiem</linkMonerium>.", "gnosis-signup.verify-email.title": "Verificē e-pastu", "gnosis.confirm.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kodu? <PERSON><PERSON><PERSON><PERSON><PERSON>, vai tavs tālruņa numurs ir pareizs", "gnosis.confirm.title": "Kods nosūtīts uz {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Verificēt", "gnosisPayAccountStatus.success.title": "<PERSON><PERSON> importēta", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay tavā valstī vēl nav pieejams", "gnosisPayNoActiveCardsFound.title": "Nav aktīvu karšu", "gnosis_pay_card_delay_relay_not_empty_error.title": "Transakcijas kļūme. Mēģini vēlāk.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "Karte nav iespējama", "gnosiskyc.modal.approved.activate-free-card": "Aktivizēt bezmaksas karti", "gnosiskyc.modal.approved.button-text": "Iemaksāt no bankas konta", "gnosiskyc.modal.approved.title": "<PERSON><PERSON> person<PERSON> konta dati ir izveidoti", "gnosiskyc.modal.failed.close": "Aizvērt", "gnosiskyc.modal.failed.title": "<PERSON><PERSON><PERSON>, m<PERSON><PERSON> partneris Gnosis Pay nevar tev izveidot kontu", "gnosiskyc.modal.in-progress.title": "ID pārbaude var ilgt 24 stundas vai vairāk. <PERSON><PERSON><PERSON><PERSON>, esi pacietīgs.", "goToSettingsPopup.settings": "Iestatījumi", "goToSettingsPopup.title": "Ieslēdz paziņojumus savas ierīces iestatījumos jebkur<PERSON> laik<PERSON>.", "google_file.error.failed_to_fetch_auth_token.button_title": "Mēģināt vēlreiz", "google_file.error.failed_to_fetch_auth_token.subtitle": "Atļauj piekļ<PERSON> mākon<PERSON>, lai to lietotu.", "google_file.error.failed_to_fetch_auth_token.title": "Neizdev<PERSON><PERSON>", "hidden_tokens.widget.emptyState": "Nav slēptu tokenu", "how_to_connect_to_metamask.got_it": "<PERSON><PERSON>, skaidrs", "how_to_connect_to_metamask.story.subtitle": "Viegli p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> starp Zeal un citiem maciņiem jebkurā laikā.", "how_to_connect_to_metamask.story.title": "Zeal darb<PERSON>s k<PERSON> ar citiem maciņie<PERSON>", "how_to_connect_to_metamask.why_switch": "<PERSON><PERSON><PERSON><PERSON><PERSON> pārslēgties starp Zeal un citiem maciņiem?", "how_to_connect_to_metamask.why_switch.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> no tā, kuru mac<PERSON>, <PERSON>eal dro<PERSON><PERSON> pārbaudes tevi vienmēr aizsargās no ļaunprātīgām vietnēm un darījumiem.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "<PERSON><PERSON><PERSON>, ka ir grūti spert soli un sākt lietot jaunu maciņu. Tāpēc mēs esam padarījuši Zeal lietošanu līdzās tavam esošajam maciņam vieglu. Pārslēdzies jebkur<PERSON> laik<PERSON>.", "import-bank-transfer-owner.banner.title": "Bankas pārskaitīju<PERSON> mac<PERSON> ir mainīts. <PERSON>, import<PERSON><PERSON><PERSON> savu maciņu šajā ierīcē.", "import-bank-transfer-owner.title": "<PERSON><PERSON><PERSON><PERSON>, lai lietotu bankas pārskaitījumus š<PERSON> ier<PERSON>", "import_gnosispay_wallet.add-another-card-owner.footnote": "Importē privāto atslēgu vai sākum<PERSON>, kam pieder tava Gnosis Pay karte", "import_gnosispay_wallet.primaryText": "Importēt Gnosis Pay maciņu", "injected-wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "intercom.getHelp": "Saņ<PERSON>t palīdzību", "invalid_iban.got_it": "<PERSON><PERSON><PERSON>", "invalid_iban.subtitle": "Ievadītais IBAN nav derīgs. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, vai dati ir ievad<PERSON>ti par<PERSON>, un mēģini vēlre<PERSON>.", "invalid_iban.title": "Nederīgs IBAN", "keypad-0": "Tastatūras taustiņš 0", "keypad-1": "Tastatūras taustiņš 1", "keypad-2": "Tastatūras taustiņš 2", "keypad-3": "Tastatūras taustiņš 3", "keypad-4": "Tastatūras taustiņš 4", "keypad-5": "Tastatūras taustiņš 5", "keypad-6": "Tastatūras <PERSON> 6", "keypad-7": "Tastatūras taustiņš 7", "keypad-8": "Tastatūras taustiņš 8", "keypad-9": "Tastatūras taustiņš 9", "keypad.biometric-button": "Tastatūras biometrijas poga", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "<PERSON><PERSON><PERSON><PERSON> frāze <PERSON> 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "<PERSON><PERSON><PERSON><PERSON>", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Glabā savas slepenās frāzes bezsaistes kopiju droš<PERSON> viet<PERSON>, lai vēl<PERSON>k varētu atgūt savus līd<PERSON>us", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "Nemēģini uzminēt vārdu", "keystore.write_secret_phrase.before_you_begin.first_point": "<PERSON><PERSON>, ka <PERSON><PERSON><PERSON><PERSON>, kam ir mana slepen<PERSON> frāze, var pārskaitīt manus līdzek<PERSON>us", "keystore.write_secret_phrase.before_you_begin.second_point": "<PERSON><PERSON><PERSON> at<PERSON> par to, lai mana slepenā frāze būtu drošībā un slepenībā.", "keystore.write_secret_phrase.before_you_begin.subtitle": "<PERSON><PERSON><PERSON><PERSON>, izlasi un apstiprini šos punktus:", "keystore.write_secret_phrase.before_you_begin.third_point": "Es atrodos privātā vietā, kur apkārt nav citu cilvēku vai kameru", "keystore.write_secret_phrase.before_you_begin.title": "Pirms sāc", "keystore.write_secret_phrase.secret_phrase_test.title": "<PERSON>r<PERSON> ir {count} . vārds tavā slepenajā frāzē?", "keystore.write_secret_phrase.test_ps.lets_do_it": "Ai<PERSON><PERSON>", "keystore.write_secret_phrase.test_ps.subtitle": "<PERSON>v būs <PERSON> slepen<PERSON> frāze, lai at<PERSON> kontu šajā vai citās ierīcēs. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vai tava slepenā frāze ir pier<PERSON><PERSON>ta par<PERSON>.", "keystore.write_secret_phrase.test_ps.subtitle2": "<PERSON><PERSON><PERSON> tev jautāsim {count} vārdus no tavas frāzes.", "keystore.write_secret_phrase.test_ps.title": "Konta atjaunošanas tests", "kyc.modal.approved.button-text": "Veikt pārskaitījumu", "kyc.modal.approved.subtitle": "Tava verifikācija ir pabeigta. Tagad vari veikt neierobežotus bankas pārskaitījumus.", "kyc.modal.approved.title": "Bankas pārskaitījumi atbloķēti", "kyc.modal.continue-with-partner.button-text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc.modal.continue-with-partner.subtitle": "Mēs tevi pāradresēsim uz mūsu partnera vietni, lai savāktu dokumentus un pabeigtu verifikācijas pieteikumu.", "kyc.modal.continue-with-partner.title": "Turpināt pie mūsu partnera", "kyc.modal.failed.unblock.subtitle": "Unblock neapstiprināja tavu identitātes verifikāciju un nevar nodrošināt bankas pārskaitījumu pakalpojumus.", "kyc.modal.failed.unblock.title": "Unblock pieteikums nav apstiprināts", "kyc.modal.paused.button-text": "<PERSON><PERSON><PERSON><PERSON> datus", "kyc.modal.paused.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka daļa informācijas nav pareiza. Mēģini vēlreiz un pirms iesniegšanas pārbaudi datus.", "kyc.modal.paused.title": "<PERSON> dati i<PERSON>", "kyc.modal.pending.button-text": "Aizvērt", "kyc.modal.pending.subtitle": "Verifikācija parasti a<PERSON> ma<PERSON> par 10 minūtēm, bet da<PERSON><PERSON>iz var ilgt neda<PERSON> il<PERSON>.", "kyc.modal.pending.title": "<PERSON><PERSON><PERSON> tevi <PERSON>m", "kyc.modal.required.cta": "<PERSON><PERSON><PERSON> veri<PERSON>", "kyc.modal.required.subtitle": "Tu esi sasniedzis transakciju limitu. <PERSON>, verificē savu identitāti. Tas parasti aizņem pāris minūtes un prasa personīgo informāciju un dokumentus.", "kyc.modal.required.title": "Nepieciešama identitātes verifikācija", "kyc.submitted": "Pieteikums iesniegts", "kyc.submitted_short": "Iesniegts", "kyc_status.completed_status": "<PERSON><PERSON><PERSON><PERSON>", "kyc_status.failed_status": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc_status.paused_status": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc_status.subtitle": "Bankas pārskaitījumi", "kyc_status.subtitle.wrong_details": "Nepareiza informācija", "kyc_status.subtitle_in_progress": "<PERSON><PERSON> a<PERSON>", "kyc_status.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label.close": "Aizvērt", "label.saving": "Saglabā...", "labels.this-month": "Šomē<PERSON>", "labels.today": "Šodien", "labels.yesterday": "<PERSON><PERSON><PERSON>", "language.selector.title": "Valoda", "ledger.account_loaded.imported": "Importēts", "ledger.add.success.title": "Ledger ve<PERSON><PERSON><PERSON><PERSON> savienots 🎉", "ledger.connect.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.connect.step1": "Savieno Ledger ar savu <PERSON>", "ledger.connect.step2": "Atver Ethereum lietotni Ledger ierīcē", "ledger.connect.step3": "<PERSON>ēc tam sinhronizē savu Ledger 👇", "ledger.connect.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, lai import<PERSON><PERSON> savus <PERSON> mac<PERSON>", "ledger.connect.title": "<PERSON><PERSON><PERSON> ar <PERSON>", "ledger.error.ledger_is_locked.subtitle": "Atslēdz Ledger un atver Ethereum lietotni", "ledger.error.ledger_is_locked.title": "Ledger ir bloķēts", "ledger.error.ledger_not_connected.action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.error.ledger_not_connected.subtitle": "<PERSON><PERSON><PERSON>, atver Ethereum lietotni.", "ledger.error.ledger_not_connected.title": "Ledger nav pievienots", "ledger.error.ledger_running_non_eth_app.title": "Ethereum lietotne nav atvērta", "ledger.error.user_trx_denied_by_user.action": "Aizvērt", "ledger.error.user_trx_denied_by_user.subtitle": "<PERSON> transakciju savā maciņā", "ledger.error.user_trx_denied_by_user.title": "Transakcija <PERSON>", "ledger.hd_path.bip44.subtitle": "piem., Metamask, Trezor", "ledger.hd_path.bip44.title": "BIP44 standarts", "ledger.hd_path.ledger_live.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Legacy", "ledger.hd_path.phantom.subtitle": "piem., Phantom", "ledger.select.hd_path.subtitle": "HD ceļi ir veids, kā aparatūras maciņi kārto savus kontus. Tas ir līdzīgi kā indekss kārto lapas grāmatā.", "ledger.select.hd_path.title": "Izvēlēties HD ceļu", "ledger.select_account.import_wallets_count": "{count,plural,=0{<PERSON><PERSON><PERSON>i nav izvēlēti} one{Importēt maciņu} other{Importēt {count} maciņus}}", "ledger.select_account.path_settings": "<PERSON><PERSON><PERSON>", "ledger.select_account.subtitle": "<PERSON><PERSON><PERSON> gaid<PERSON>? Pamēģini nomainīt ceļa iestatījumus", "ledger.select_account.subtitle.group_header": "<PERSON><PERSON><PERSON><PERSON>", "ledger.select_account.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legend.lending-operations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legend.market_making-operations": "<PERSON><PERSON><PERSON>", "legend.treasury-operations": "Valsts kases <PERSON>", "link-existing-monerium-account-sign.button": "<PERSON><PERSON><PERSON><PERSON>", "link-existing-monerium-account-sign.subtitle": "Tev jau ir Monerium konts.", "link-existing-monerium-account-sign.title": "<PERSON><PERSON><PERSON> ar savu esošo Monerium kontu", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "<PERSON>v jau ir Monerium konts. <PERSON><PERSON><PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON>, lai p<PERSON><PERSON> i<PERSON>.", "link-existing-monerium-account.title": "Dodies uz Monerium, lai saist<PERSON>tu savu kontu", "loading.pin": "<PERSON><PERSON><PERSON><PERSON><PERSON> PIN...", "lockScreen.passwordIncorrectMessage": "Nepareiza parole", "lockScreen.passwordRequiredMessage": "Nepieciešama parole", "lockScreen.unlock.header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lockScreen.unlock.subheader": "<PERSON><PERSON><PERSON> savu paroli, lai ats<PERSON><PERSON><PERSON><PERSON>.", "mainTabs.activity.label": "Aktivitātes", "mainTabs.browse.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mainTabs.browse.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mainTabs.card.label": "<PERSON><PERSON>", "mainTabs.portfolio.label": "Port<PERSON>lis", "mainTabs.rewards.label": "Atlīdzības", "makeSpendable.cta": "<PERSON><PERSON><PERSON>", "makeSpendable.holdAsCash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeSpendable.shortText": "Nopelnīt {apy} gadā", "makeSpendable.title": "{amount} saņemts", "merchantCategory.agriculture": "Lauksaimniecība", "merchantCategory.alcohol": "Alkohols", "merchantCategory.antiques": "<PERSON>k<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.appliances": "<PERSON><PERSON><PERSON><PERSON> tehnika", "merchantCategory.artGalleries": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.autoRepair": "Autoremonts", "merchantCategory.autoRepairService": "Autoremonta serviss", "merchantCategory.beautyFitnessSpas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, fitness un SPA", "merchantCategory.beautyPersonalCare": "Skaistumkopšana un personīgā aprūpe", "merchantCategory.billiard": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.books": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.bowling": "Boulings", "merchantCategory.businessProfessionalServices": "Biznesa un profesionālie pakalpojumi", "merchantCategory.carRental": "Autonoma", "merchantCategory.carWash": "Automazgātava", "merchantCategory.cars": "Automašīnas", "merchantCategory.casino": "<PERSON><PERSON><PERSON>", "merchantCategory.casinoGambling": "Kazino un azartspēles", "merchantCategory.cellular": "<PERSON><PERSON><PERSON>", "merchantCategory.charity": "Labdarība", "merchantCategory.childcare": "<PERSON>ē<PERSON><PERSON> a<PERSON>rū<PERSON>", "merchantCategory.cigarette": "Cigaretes", "merchantCategory.cinema": "<PERSON><PERSON>", "merchantCategory.cinemaEvents": "Kino un pasākumi", "merchantCategory.cleaning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.cleaningMaintenance": "<PERSON><PERSON><PERSON><PERSON><PERSON>na un uzturēšana", "merchantCategory.clothes": "Apģērbs", "merchantCategory.clothingServices": "Apģērbu pakalpojumi", "merchantCategory.communicationServices": "<PERSON><PERSON><PERSON>", "merchantCategory.construction": "Būvniecība", "merchantCategory.cosmetics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.craftsArtSupplies": "Rokdarbi un mākslas piederumi", "merchantCategory.datingServices": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.delivery": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.dentist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.departmentStores": "Universālveikali", "merchantCategory.directMarketingSubscription": "<PERSON><PERSON><PERSON><PERSON> m<PERSON> un abonementi", "merchantCategory.discountStores": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.drugs": "<PERSON><PERSON><PERSON>", "merchantCategory.dutyFree": "Beznodok<PERSON><PERSON> veika<PERSON>", "merchantCategory.education": "Izglītība", "merchantCategory.electricity": "Elektrība", "merchantCategory.electronics": "Elektronika", "merchantCategory.emergencyServices": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.equipmentRental": "<PERSON><PERSON><PERSON><PERSON><PERSON> noma", "merchantCategory.evCharging": "Elektroauto uzlāde", "merchantCategory.financialInstitutions": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.financialProfessionalServices": "Finanšu un profesionālie pakalpojumi", "merchantCategory.finesPenalties": "Naudas sodi un soda naudas", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "Li<PERSON><PERSON><PERSON>", "merchantCategory.flowers": "<PERSON><PERSON><PERSON>", "merchantCategory.flowersGarden": "<PERSON><PERSON><PERSON> un dārzs", "merchantCategory.food": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.freight": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.fuel": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.funeralServices": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.furniture": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.games": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.gas": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.generalMerchandiseRetail": "Vis<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> preces un mazumtirdzniecība", "merchantCategory.gifts": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.government": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.governmentServices": "Valsts pakalpojumi", "merchantCategory.hardware": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.healthMedicine": "Veselība un medicīna", "merchantCategory.homeImprovement": "<PERSON><PERSON><PERSON>", "merchantCategory.homeServices": "Mājsaimniec<PERSON><PERSON>", "merchantCategory.hotel": "Viesnīca", "merchantCategory.housing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.insurance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.internet": "Internets", "merchantCategory.kids": "Bērniem", "merchantCategory.laundry": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.laundryCleaningServices": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> un tīrīšanas pakalpojumi", "merchantCategory.legalGovernmentFees": "Juridiskās un valsts nodevas", "merchantCategory.luxuries": "<PERSON><PERSON><PERSON> preces", "merchantCategory.luxuriesCollectibles": "Luksusa un kolekciju preces", "merchantCategory.magazines": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.magazinesNews": "Žurnāli un ziņas", "merchantCategory.marketplaces": "Tirdzniecības vietas", "merchantCategory.media": "<PERSON><PERSON><PERSON>", "merchantCategory.medicine": "Medicīna", "merchantCategory.mobileHomes": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.moneyTransferCrypto": "<PERSON><PERSON><PERSON> un kripto", "merchantCategory.musicRelated": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.musicalInstruments": "<PERSON><PERSON><PERSON><PERSON>i", "merchantCategory.optics": "Optika", "merchantCategory.organizationsClubs": "Organizācijas un klubi", "merchantCategory.other": "Citi", "merchantCategory.parking": "Autostāvvieta", "merchantCategory.pawnShops": "<PERSON><PERSON>", "merchantCategory.pets": "Mājdzīvnieki", "merchantCategory.photoServicesSupplies": "Fotoservisi un piederumi", "merchantCategory.postalServices": "Pasta pakalpojumi", "merchantCategory.professionalServicesOther": "Profesion<PERSON><PERSON> (citi)", "merchantCategory.publicTransport": "Sabiedriskais transports", "merchantCategory.purchases": "Pirkumi", "merchantCategory.purchasesMiscServices": "Pirkumi un dažādi p<PERSON>lpojumi", "merchantCategory.recreationServices": "Atpūtas pakalpojumi", "merchantCategory.religiousGoods": "Reliģiskās preces", "merchantCategory.secondhandRetail": "Lietotu pre<PERSON> tirdzniecī<PERSON>", "merchantCategory.shoeHatRepair": "Apavu un cepuru remonts", "merchantCategory.shoeRepair": "Apavu remonts", "merchantCategory.softwareApps": "Programmatūra un lietotnes", "merchantCategory.specializedRepairs": "Specializēti remonti", "merchantCategory.sport": "Sports", "merchantCategory.sportingGoods": "Sporta preces", "merchantCategory.sportingGoodsRecreation": "Sporta preces un atpūta", "merchantCategory.sportsClubsFields": "Sporta klubi un laukumi", "merchantCategory.stationaryPrinting": "<PERSON><PERSON><PERSON><PERSON> preces un druka", "merchantCategory.stationery": "<PERSON><PERSON><PERSON><PERSON> preces", "merchantCategory.storage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.taxes": "Nodokļi", "merchantCategory.taxi": "<PERSON><PERSON><PERSON>", "merchantCategory.telecomEquipment": "Telekomunikāciju aprīkojums", "merchantCategory.telephony": "Telefonija", "merchantCategory.tobacco": "Tabaka", "merchantCategory.tollRoad": "<PERSON><PERSON><PERSON>", "merchantCategory.tourismAttractionsAmusement": "<PERSON><PERSON><PERSON><PERSON>, apskates vietas un izklaide", "merchantCategory.towing": "Evakuācija", "merchantCategory.toys": "Rotaļlietas", "merchantCategory.toysHobbies": "Rotaļlietas un hobiji", "merchantCategory.trafficFine": "Satiksmes sods", "merchantCategory.train": "Vilciens", "merchantCategory.travelAgency": "Ceļ<PERSON><PERSON><PERSON> aģentūra", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, radio un straumēšana", "merchantCategory.utilities": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.waterTransport": "Ūdens transports", "merchantCategory.wholesaleClubs": "Vairumtirdzniecības klubi", "metaMask.subtitle": "<PERSON><PERSON><PERSON><PERSON>jo <PERSON>a<PERSON>, lai p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> visus MetaMask savienojumus uz Zeal. Noklikšķinot uz MetaMask dApps, notiks savie<PERSON>.", "metaMask.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> savienot ar <PERSON>?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Atver savu bankas lietotni", "monerium-bank-deposit.buttet-point.receive-crypto": "Sa<PERSON><PERSON> digit<PERSON> EUR", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Nosūti {fiatCurrencyCode} uz savu kontu", "monerium-bank-deposit.deposit-account-country": "Valsts", "monerium-bank-deposit.header": "{fullName} person<PERSON><PERSON>s konts", "monerium-bank-details.account-name": "Konta no<PERSON>ukums", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT nokopēts", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT nokopēts", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN nokopēts", "monerium-bank-details.to-wallet": "<PERSON><PERSON>", "monerium-bank-details.transfer-fee": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> maksa", "monerium-bank-transfer.enable-card.bullet-1": "Pabeidz identitātes verifikāciju", "monerium-bank-transfer.enable-card.bullet-2": "<PERSON><PERSON><PERSON> konta datus", "monerium-bank-transfer.enable-card.bullet-3": "Iemaksā no bankas konta", "monerium-card-delay-relay.success.cta": "Aizvērt", "monerium-card-delay-relay.success.subtitle": "Drošības apsvērumu dēļ karšu iestatījumu izmaiņu apstrāde aizņem 3 minūtes.", "monerium-card-delay-relay.success.title": "Atgriezies pēc 3 min, lai tur<PERSON><PERSON>tu Monerium iestatī<PERSON>nu", "monerium-deposit.account-details-info-popup.bullet-point-1": "<PERSON><PERSON><PERSON><PERSON><PERSON> {fiatCurrencyCode} , ko nosūtīsi uz šo kontu, tiks automātiski konvertēts par {cryptoCurrencyCode} tokeniem {cryptoCurrencyChain} Chain tīklā un nosūtīts uz tavu maciņu", "monerium-deposit.account-details-info-popup.bullet-point-2": "SŪTI TIKAI {fiatCurrencyCode} ({fiatCurrencySymbol}) uz savu kontu", "monerium-deposit.account-details-info-popup.title": "<PERSON>va konta dati", "monerium.check_order_status.sending": "<PERSON><PERSON><PERSON>", "monerium.not-eligible.cta": "Atpakaļ", "monerium.not-eligible.subtitle": "Monerium nevar tev atvērt kontu. <PERSON><PERSON><PERSON><PERSON>, izvēlies citu pakalpojumu sniedzēju.", "monerium.not-eligible.title": "Mēģini citu pakalpojumu sniedzēju", "monerium.setup-card.cancel": "Atcelt", "monerium.setup-card.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monerium.setup-card.create_account": "Izveidot kontu", "monerium.setup-card.login": "Pieslēgties Gnosis Pay", "monerium.setup-card.subtitle": "Izveido Gnosis Pay kontu vai pieslēdzies, lai aktivizētu tūlītējas bankas iemaksas.", "monerium.setup-card.subtitle_personal_account": "Izveido savu Gnosis Pay personīgo kontu pāris minūtēs:", "monerium.setup-card.title": "Aktivizēt bankas iemaksas", "moneriumDepositSuccess.goToWallet": "Doties uz maciņu", "moneriumDepositSuccess.title": "{symbol} sa<PERSON><PERSON><PERSON>", "moneriumInfo.fees": "Tev ir 0% komisijas maksa", "moneriumInfo.registration": "Monerium ir autorizēta un regulēta kā elektroniskās naudas iestāde saskaņā ar Islandes Elektronisko naudas likumu Nr. 17/2013 <link><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON></link>", "moneriumInfo.selfCustody": "Saņemtā digitālā nauda ir tavā privātā pārvaldībā, un nevienam citam nebūs kontroles pār taviem līdzekļiem", "moneriumWithdrawRejected.supportText": "<PERSON>s neizdev<PERSON><PERSON> pabe<PERSON>t tavu pārskaitījumu. <PERSON><PERSON><PERSON><PERSON>, mēģini vēlreiz. <PERSON>a joproj<PERSON> ne<PERSON>, tad <link>r<PERSON><PERSON> at<PERSON>.</link>", "moneriumWithdrawRejected.title": "Pārskaitījums atgriezts", "moneriumWithdrawRejected.tryAgain": "Mēģināt vēlreiz", "moneriumWithdrawSuccess.supportText": "Var paiet 24 stundas, līdz tavs {br}saņēmē<PERSON><PERSON> saņems līdzek<PERSON>us", "moneriumWithdrawSuccess.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monerium_enable_banner.text": "Aktivizē bankas pārskaitījumus tagad", "monerium_error_address_re_link_required.title": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>piesaista Monerium no jauna", "monerium_error_duplicate_order.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "Šveices franks", "mt-pelerin-fork.options.chf.short": "Tūlītēji un bez maksas ar Mt Pelerin", "mt-pelerin-fork.options.euro.primary": "Eiro", "mt-pelerin-fork.options.euro.short": "Tūlītēji un bez maksas ar Monerium", "mt-pelerin-fork.title": "Ko vēlies noguldīt?", "mtPelerinProviderInfo.fees": "Tu nemaksā 0 % komisijas maksu", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd ir saistīts ar SO-FIT, pašregulējošu iestādi, ko atzinusi Šveices Finanšu iestāde (FINMA) saskaņā ar Noziedzīgi iegūtu līdzekļu legalizācijas novēršanas likumu. <link>Uzzināt vairāk</link>", "mtPelerinProviderInfo.selfCustody": "Saņemtā digitālā nauda atrodas tavā privātajā maciņ<PERSON>, un nevienam citam nebūs kontroles pār taviem aktīviem", "network-fee-widget.title": "<PERSON><PERSON><PERSON>", "network.edit.verifying_rpc": "Pārbauda RPC", "network.editRpc.predefined_network_info.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> RPC, kas ne<PERSON>j izsekot tavus personas datus.{br}{br}Zeal noklusējuma RPC ir uzticami un pārbaudīti RPC nodrošinātāji.", "network.editRpc.predefined_network_info.title": "Zeal privātuma RPC", "network.filter.update_rpc_success": "RPC mezgls saglabāts", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "Visi tīkli", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "<PERSON><PERSON><PERSON>", "networks.filter.add_modal.chain_list.subtitle": "Pievieno jebkurus EVM tīklus", "networks.filter.add_modal.chain_list.title": "Doties uz Chainlist", "networks.filter.add_modal.dapp_tip.subtitle": "<PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dApps vienkārš<PERSON> pā<PERSON>lēd<PERSON>s uz EVM tīklu, ko vē<PERSON> izman<PERSON>, un Zeal tev pajautās, vai vēlies to pievienot savam mac<PERSON>.", "networks.filter.add_modal.dapp_tip.title": "Vai pievieno tīklu no jebkuras dApp", "networks.filter.add_networks.subtitle": "Visi EVM tīkli tiek atbalstīti", "networks.filter.add_networks.title": "<PERSON><PERSON><PERSON>", "networks.filter.add_test_networks.title": "<PERSON><PERSON><PERSON>", "networks.filter.tab.netwokrs": "<PERSON><PERSON><PERSON><PERSON>", "networks.filter.testnets.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nft.widget.emptystate": "Maciņā nav kolekcionējamu priekšmetu", "nft_collection.change_account_picture.subtitle": "Vai tiešām vēlies atjaunināt profila attēlu?", "nft_collection.change_account_picture.title": "Atjaunināt profila attēlu uz NFT", "nfts.allNfts.pricingPopup.description": "Kolekcionējamo p<PERSON>kšmetu cenas ir balstītas uz pēdējo darījuma cenu.", "nfts.allNfts.pricingPopup.title": "Kolekcionējamo p<PERSON>kšmetu cenas", "no-passkeys-found.modal.cta": "Aizvērt", "no-passkeys-found.modal.subtitle": "<PERSON><PERSON><PERSON> ne<PERSON>am atkl<PERSON>t <PERSON> ieejas atslēgas šajā ier<PERSON>. <PERSON><PERSON><PERSON><PERSON>, vai esi pieteicies māko<PERSON>a kont<PERSON>, ko <PERSON>, lai izve<PERSON>tu savu <PERSON> Wallet.", "no-passkeys-found.modal.title": "<PERSON><PERSON><PERSON> atslēgas nav atrastas", "notValidEmail.title": "Nederīga e-pasta adrese", "notValidPhone.title": "Šis nav derīgs tālruņa numurs", "notification-settings.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notification-settings.toggles.active-wallets": "<PERSON><PERSON><PERSON><PERSON>", "notification-settings.toggles.bank-transfers": "Bankas pārskaitījumi", "notification-settings.toggles.card-payments": "<PERSON><PERSON><PERSON><PERSON>", "notification-settings.toggles.readonly-wallets": "<PERSON><PERSON><PERSON>", "ntft.groupHeader.text": "Kolekcionējamie priekšmeti", "on_ramp.crypto_completed": "<PERSON><PERSON><PERSON><PERSON>", "on_ramp.fiat_completed": "<PERSON><PERSON><PERSON><PERSON>", "onboarding-widget.subtitle.card_created_from_order.left": "Visa karte", "onboarding-widget.subtitle.card_created_from_order.right": "Aktivizēt karti", "onboarding-widget.subtitle.card_order_ready.left": "Fiziskā Visa karte", "onboarding-widget.subtitle.default": "Bankas pārskaitījumi un Visa karte", "onboarding-widget.title.card-order-in-progress": "<PERSON><PERSON><PERSON><PERSON><PERSON> karte<PERSON> p<PERSON>", "onboarding-widget.title.card_created_from_order": "<PERSON><PERSON> ir no<PERSON><PERSON><PERSON><PERSON>", "onboarding-widget.title.kyc_approved": "Pārskaitījumi un karte ir gatavi", "onboarding-widget.title.kyc_failed": "Konta izveide nav iespējama", "onboarding-widget.title.kyc_not_started": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onboarding-widget.title.kyc_started_documents_requested": "<PERSON><PERSON><PERSON> veri<PERSON>", "onboarding-widget.title.kyc_started_resubmission_requested": "Mēģināt verificēt vēlreiz", "onboarding-widget.title.kyc_started_verification_in_progress": "Notiek identitātes verifikācija", "onboarding.loginOrCreateAccount.amountOfAssets": "Vairāk nekā 10 mljrd. $ līdzekļos", "onboarding.loginOrCreateAccount.cards.subtitle": "Pieejams tikai noteiktos reģionos. Turpinot tu piekr<PERSON>ti mūsu <Terms>Noteikumiem</Terms> un <PrivacyPolicy>Privātuma politikai</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Visa karte ar augstu{br}atdevi un bez maksas", "onboarding.loginOrCreateAccount.createAccount": "Izveidot kontu", "onboarding.loginOrCreateAccount.earn.subtitle": "Atdeve var atšķirties; kapitāls ir pakļauts riskam. Turpinot tu piekr<PERSON>ti mūsu <Terms>Noteikumiem</Terms> un <PrivacyPolicy>Privātuma politikai</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "P<PERSON>ļņa {percent} gadā{br}Mum<PERSON> u<PERSON> {currencySymbol}vairāk nekā 5 mljrd.", "onboarding.loginOrCreateAccount.earningPerYear": "<PERSON><PERSON><PERSON><PERSON>a {percent}{br}gadā", "onboarding.loginOrCreateAccount.login": "Pieslēgties", "onboarding.loginOrCreateAccount.trading.subtitle": "<PERSON><PERSON><PERSON><PERSON> ir pakļauts riskam. Turpinot tu piekr<PERSON>ti mūsu <Terms>Noteikumiem</Terms> un <PrivacyPolicy>Privātuma politikai</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Investē visā,{br}no BTC līdz S&P", "onboarding.loginOrCreateAccount.trustedBy": "<PERSON><PERSON><PERSON><PERSON> na<PERSON> tirgi{br}Mum<PERSON> u<PERSON> {assets}", "onboarding.wallet_stories.close": "Aizvērt", "onboarding.wallet_stories.previous": "Iepriek<PERSON><PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.into": "Uz", "otpIncorrectMessage": "Aps<PERSON>rinājuma kods ir nepareizs", "passkey-creation-not-possible.modal.close": "Aizvērt", "passkey-creation-not-possible.modal.subtitle": "Nevaram izveidot Passkey. Pārbaudi ierīci. <link><PERSON><PERSON><PERSON></link> ja probl<PERSON><PERSON> at<PERSON>.", "passkey-creation-not-possible.modal.title": "Nevar izveidot Passkey", "passkey-not-supported-in-mobile-browser.modal.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Smart Wallet nedarbojas mobilos pārlū<PERSON>.", "passkey-not-supported-in-mobile-browser.modal.title": "Le<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai tur<PERSON>", "passkey-recovery.recovering.deploy-signer.loading-text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "passkey-recovery.recovering.loading-text": "Atkop<PERSON>", "passkey-recovery.recovering.signer-not-found.subtitle": "<PERSON><PERSON><PERSON> saist<PERSON>t tavu ieejas atslēgu ar aktīvu maciņu. Ja tavā maciņā ir l<PERSON>, sa<PERSON><PERSON> ar <PERSON><PERSON> komandu, lai saņ<PERSON> atbalstu.", "passkey-recovery.recovering.signer-not-found.title": "Mac<PERSON>ņš nav atrasts", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "Mēģināt ar citu atslēgu", "passkey-recovery.select-passkey.banner.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka ierīcē esat pareizajā kontā. Ieejas atslēgas ir specifiskas katram kontam.", "passkey-recovery.select-passkey.banner.title": "<PERSON>eredzi sava maciņa ieejas atslēgu?", "passkey-recovery.select-passkey.continue": "<PERSON><PERSON><PERSON>", "passkey-recovery.select-passkey.subtitle": "Izvēlies i<PERSON><PERSON>, kas ir sa<PERSON><PERSON>ta ar tavu <PERSON>, lai atg<PERSON><PERSON>.", "passkey-recovery.select-passkey.title": "Izvēlēties ieejas atslēgu", "passkey-story_1.subtitle": "Ar <PERSON> tu vari maksāt tīkla maksu ar liel<PERSON><PERSON> da<PERSON>u tokenu un tev nav jāuz<PERSON>ucas par to.", "passkey-story_1.title": "<PERSON><PERSON>ā par tīkla maksu – maksā ar populār<PERSON><PERSON><PERSON><PERSON>", "passkey-story_2.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>t Safe nozares v<PERSON> v<PERSON>, ka<PERSON> <PERSON><PERSON><PERSON> vairāk nekā 100 miljardus USD vairāk nekā 20 miljonos maciņu.", "passkey-story_2.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "passkey-story_3.subtitle": "Smart Wallets darbojas lielākajos ar Ethereum saderīgos tīklos. Pirms aktīvu sūtīšanas pārbaudi atbalstītos tīklus.", "passkey-story_3.title": "Atbalstīti galvenie EVM tīkli", "password.add.header": "Izveidot paroli", "password.add.includeLowerAndUppercase": "Mazie un lielie burti", "password.add.includesNumberOrSpecialChar": "Viens cipars vai simbols", "password.add.info.subtitle": "<PERSON><PERSON>s nesūtām tavu paroli uz mūsu serveriem un neveidojam tās dublējumu.", "password.add.info.t_and_c": "Turpinot tu piekr<PERSON>ti mūsu <Terms>Noteikumiem</Terms> un <PrivacyPolicy>Privātuma politikai</PrivacyPolicy>", "password.add.info.title": "Tava parole paliek š<PERSON> ier<PERSON>", "password.add.inputPlaceholder": "Izveidot paroli", "password.add.shouldContainsMinCharsCheck": "10+ r<PERSON><PERSON><PERSON><PERSON>", "password.add.subheader": "<PERSON> izmantosi savu paroli, lai atsl<PERSON><PERSON><PERSON>.", "password.add.success.title": "Parole izveidota 🔥", "password.confirm.header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> paroli", "password.confirm.passwordDidNotMatch": "<PERSON><PERSON><PERSON><PERSON>", "password.confirm.subheader": "Ievadi savu paroli vēlreiz", "password.create_pin.subtitle": "Šis piekļuves kods aizslēdz Zeal lietotni.", "password.create_pin.title": "Izveido savu piekļuves kodu", "password.enter_pin.title": "<PERSON><PERSON><PERSON><PERSON> kodu", "password.incorrectPin": "Nepareizs piekļuves kods", "password.pin_is_not_same": "Piekļuves kods nesakrīt", "password.placeholder.enter": "<PERSON><PERSON><PERSON><PERSON><PERSON>oli", "password.placeholder.reenter": "Atkārtoti ievad<PERSON>t paroli", "password.re_enter_pin.subtitle": "<PERSON><PERSON><PERSON> to pašu piekļuves kodu vēlreiz", "password.re_enter_pin.title": "<PERSON><PERSON><PERSON><PERSON> piekļuves kodu", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "<PERSON><PERSON><PERSON>", "pending-send.details.pending": "<PERSON><PERSON><PERSON>", "pending-send.details.processing": "<PERSON><PERSON> a<PERSON>", "permit-info.modal.description": "Atļaujas ir pie<PERSON><PERSON><PERSON><PERSON><PERSON>, kas pēc parakst<PERSON><PERSON><PERSON>auj lietotnēm tavā vārdā pārvietot tavus tokenus, pie<PERSON><PERSON><PERSON>, lai veiktu maiņu.{br}Atļaujas ir līd<PERSON><PERSON><PERSON> a<PERSON><PERSON>, bet to parakst<PERSON><PERSON><PERSON> tev nemaksā tīkla maksu.", "permit-info.modal.title": "<PERSON><PERSON> ir <PERSON><PERSON><PERSON><PERSON>?", "permit.edit-expiration": "Rediģēt {currency} derīguma termiņu", "permit.edit-limit": "Rediģēt {currency} tēriņu limitu", "permit.edit-modal.expiresIn": "<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON> pē<PERSON>…", "permit.expiration-warning": "{currency} derīguma termiņa brīdin<PERSON>jums", "permit.expiration.info": "{currency} informācija par termiņu", "permit.expiration.never": "<PERSON><PERSON><PERSON>", "permit.spend-limit.info": "{currency} informācija par tēriņu limitu", "permit.spend-limit.warning": "{currency} brīdinājums par tēriņu limitu", "phoneNumber.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> numurs", "physicalCardOrderFlow.cardOrdered": "<PERSON><PERSON>", "physicalCardOrderFlow.city": "Pilsē<PERSON>", "physicalCardOrderFlow.orderCard": "Pa<PERSON><PERSON><PERSON><PERSON><PERSON> karti", "physicalCardOrderFlow.postcode": "Pasta indekss", "physicalCardOrderFlow.shippingAddress.subtitle": "Kur tiks nosūtīta tava karte", "physicalCardOrderFlow.shippingAddress.title": "<PERSON><PERSON><PERSON><PERSON> adrese", "physicalCardOrderFlow.street": "<PERSON><PERSON>", "placeholderDapps.1inch.description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholderDapps.aave.description": "Aizdod un aizņemies tokenus", "placeholderDapps.bungee.description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholderDapps.compound.description": "Aizdod un aizņemies tokenus", "placeholderDapps.cowswap.description": "<PERSON><PERSON><PERSON><PERSON> ar labā<PERSON>em kursiem Gnosis tīklā", "placeholderDapps.gnosis-pay.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> savu Gnosis Pay karti", "placeholderDapps.jumper.description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholderDapps.lido.description": "Ieguldi ETH, lai saņemtu vairāk ETH", "placeholderDapps.monerium.description": "E-nauda un bankas pārskaitījumi", "placeholderDapps.odos.description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholderDapps.stargate.description": "Izmanto Bridge vai ieguldi par <14% APY", "placeholderDapps.uniswap.description": "Viena no populārākajām maiņas vietām", "pleaseAllowNotifications.cardPayments": "<PERSON><PERSON><PERSON><PERSON>", "pleaseAllowNotifications.customiseInSettings": "<PERSON><PERSON><PERSON><PERSON>", "pleaseAllowNotifications.enable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pleaseAllowNotifications.forWalletActivity": "<PERSON><PERSON> <PERSON><PERSON><PERSON> da<PERSON>", "pleaseAllowNotifications.title": "<PERSON><PERSON><PERSON> p<PERSON>", "pleaseAllowNotifications.whenReceivingAssets": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "portfolio.quick-actions.add_funds": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.buy": "Pirkt", "portfolio.quick-actions.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portfolio.quick-actions.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portfolio.view.lastRefreshed": "Atjaunots {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "<PERSON><PERSON><PERSON><PERSON> savu testtīkla AVAX", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Doties uz Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "<PERSON><PERSON><PERSON><PERSON> savu testtīkla BNB", "portfolio.view.topupTestNet.BscTestnet.secondary": "Doties uz Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "<PERSON><PERSON><PERSON><PERSON> savu testtīkla SepETH", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Doties uz Sepolia Faucet", "portfolio.view.topupTestNet.FantomTestnet.primary": "<PERSON><PERSON><PERSON><PERSON> savu testtīkla FTM", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Doties uz Faucet", "privateKeyConfirmation.banner.subtitle": "<PERSON><PERSON><PERSON> privātā atslēga dod piekļuvi līdzekļiem. To prasa tikai krā<PERSON>nieki.", "privateKeyConfirmation.banner.title": "<PERSON><PERSON> saprotu riskus", "privateKeyConfirmation.title": "NEKAD NEVIENAM NEIZPAUD savu privāto atslēgu", "rating-request.not-now": "<PERSON><PERSON> tagad", "rating-request.title": "Vai ieteiktu <PERSON> citiem?", "receive_funds.address-text": "<PERSON><PERSON> ir tava unikālā maciņa adrese. Tu vari to dro<PERSON>i kop<PERSON>got ar citiem.", "receive_funds.copy_address": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "receive_funds.network-warning.eoa.subtitle": "<link><PERSON><PERSON><PERSON><PERSON> tīklu sarak<PERSON>u</link>. <PERSON><PERSON><PERSON><PERSON>, kas nos<PERSON><PERSON><PERSON><PERSON> no tī<PERSON><PERSON><PERSON>, kas nav EVM, tiks <PERSON>.", "receive_funds.network-warning.eoa.title": "Atbalstīti visi Ethereum bāzes tīkli", "receive_funds.network-warning.scw.subtitle": "<link><PERSON><PERSON><PERSON><PERSON> t<PERSON></link>. <PERSON><PERSON><PERSON><PERSON>, kas <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cito<PERSON> tī<PERSON>, tik<PERSON>.", "receive_funds.network-warning.scw.title": "Svarīgi: <PERSON><PERSON><PERSON> tikai at<PERSON> t<PERSON>", "receive_funds.scan_qr_code": "Skenēt QR kodu", "receiving.in.days": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc {days}d", "receiving.this.week": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "receiving.today": "<PERSON>ņ<PERSON><PERSON><PERSON>", "reference.error.maximum_number_of_characters_exceeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON> r<PERSON>", "referral-code.placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> ielū<PERSON>a saiti", "referral-code.subtitle": "Noklikšķini uz drauga saites vēlreiz vai ielīmē saiti zemāk. Mēs vēlamies pārliecin<PERSON>ties, ka tu saņem savas atlīdzības.", "referral-code.title": "Vai draugs tev atsūtīja {bReward}?", "rekyc.verification_deadline.subtitle": "Pabeidz verifikāciju {daysUntil} dienu la<PERSON>, lai turpinātu lietot savu karti.", "rekyc.verification_required.subtitle": "<PERSON><PERSON><PERSON><PERSON> veri<PERSON>, lai tur<PERSON><PERSON>tu lietot savu karti.", "reminder.fund": "💸 Pievieno lī<PERSON> — sāc pelnīt 6% uzreiz", "reminder.onboarding": "🏁 Pabeidz <PERSON> — pelni 6% no saviem noguldījumiem", "remove-owner.confirmation.subtitle": "Drošības nolūkos iestatījumu maiņa aizņem 3 minūtes. <PERSON><PERSON><PERSON> laikā tava karte tiks īslaicīgi iesaldēta, un maksājumi nebūs iespējami.", "remove-owner.confirmation.title": "Tava karte tiks iesaldēta uz 3 minūtēm, kam<PERSON>r i<PERSON>tīju<PERSON> atjaunināsies", "restore-smart-wallet.wallet-recovered": "<PERSON><PERSON><PERSON><PERSON> atkopts", "rewardClaimCelebration.claimedTitle": "Atlīdzības j<PERSON>", "rewardClaimCelebration.subtitle": "<PERSON><PERSON> <PERSON>ra<PERSON><PERSON>", "rewardClaimCelebration.title": "Tu esi nopelnījis", "rewards-warning.subtitle": "<PERSON><PERSON><PERSON><PERSON>ont<PERSON>, tiks apturēta piekļ<PERSON> saistītajām balvām. Tu vari jebkurā laikā at<PERSON><PERSON> kontu, lai tās sa<PERSON>.", "rewards-warning.title": "<PERSON> z<PERSON> piekļuvi savām balvām", "rewards.copiedInviteLink": "<PERSON><PERSON><PERSON><PERSON><PERSON> saite nokop<PERSON>ta", "rewards.createAccount": "<PERSON><PERSON><PERSON><PERSON>a saiti", "rewards.header.subtitle": "<PERSON><PERSON><PERSON> nos<PERSON>t<PERSON>sim {aReward} tev un {bReward} tavam draugam, kad viņ<PERSON> iztērēs {bSpendLimitReward}.", "rewards.header.title": "<PERSON><PERSON><PERSON> {amountA}{br}<PERSON><PERSON><PERSON><PERSON> {amountB}", "rewards.sendInvite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rewards.sendInviteTip": "<PERSON><PERSON><PERSON><PERSON><PERSON> draugu, un mēs viņam ied<PERSON>im {bAmount}", "route.fees": "<PERSON><PERSON><PERSON> {fees}", "routesNotFound.description": "<PERSON><PERSON><PERSON> maršruts {from}-{to} tīk<PERSON> kombinācijai nav pieejams.", "routesNotFound.title": "Maiņas maršruts nav pieejams", "rpc.OrderBuySignMessage.subtitle": "Izmantojot Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Izmantojot Swaps.IO", "rpc.addCustomNetwork.addNetwork": "<PERSON><PERSON><PERSON>", "rpc.addCustomNetwork.chainId": "Ķēdes ID", "rpc.addCustomNetwork.nativeToken": "Noklusējuma tokens", "rpc.addCustomNetwork.networkName": "<PERSON><PERSON><PERSON>", "rpc.addCustomNetwork.operationDescription": "Vietne pievienos tīklu. Apzinies riskus.", "rpc.addCustomNetwork.rpcUrl": "RPC URL", "rpc.addCustomNetwork.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> {name}", "rpc.addCustomNetwork.title": "<PERSON><PERSON><PERSON>", "rpc.send_token.network_not_supported.subtitle": "<PERSON><PERSON><PERSON> t<PERSON> būs pieejams. Paldies 🙏", "rpc.send_token.network_not_supported.title": "Tīk<PERSON> drīzum<PERSON> būs pieejams", "rpc.send_token.send_or_receive.settings": "Iestatījumi", "rpc.sign.accept": "<PERSON><PERSON><PERSON><PERSON>", "rpc.sign.cannot_parse_message.body": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ja uz<PERSON><PERSON>.{br}{br}<PERSON><PERSON><PERSON><PERSON> var dot kontroli pār taviem tokeniem.", "rpc.sign.cannot_parse_message.header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rpc.sign.import_private_key": "Importēt atslēgas", "rpc.sign.subtitle": "Kam: {name}", "rpc.sign.title": "Parakstīt", "safe-creation.success.title": "Mac<PERSON>ņš izveidots", "safe-safety-checks-popup.title": "Transakcijas drošības <PERSON>es", "safetyChecksPopup.title": "Vietnes droš<PERSON><PERSON>", "scan_qr_code.description": "<PERSON><PERSON><PERSON>a QR kodu vai izveido savienojumu ar lietotni", "scan_qr_code.show_qr_code": "<PERSON><PERSON><PERSON><PERSON><PERSON> manu QR kodu", "scan_qr_code.tryAgain": "Mēģināt vēlreiz", "scan_qr_code.unlockCamera": "Atbloķēt kameru", "screen-lock-missing.modal.close": "Aizvērt", "screen-lock-missing.modal.subtitle": "Passkeys prasa ekr<PERSON> s<PERSON>. Iestati.", "screen-lock-missing.modal.title": "Nav ekr<PERSON>a <PERSON>", "seedConfirmation.banner.subtitle": "<PERSON><PERSON><PERSON> slepen<PERSON> frāze dod piekļuvi līdzekļiem. To prasa tikai krā<PERSON>nieki.", "seedConfirmation.title": "NEKAD NEVIENAM NEIZPAUD savu slepeno frāzi", "select-active-owner.subtitle": "Ta<PERSON>i kartei ir piesaistīti vairāki maci<PERSON>i. Izvēlies vienu, ko savienot ar <PERSON>. Vari pārslēgties jebkur<PERSON> laik<PERSON>.", "select-active-owner.title": "Izvēlies maciņu", "select-card.title": "Izvēlies karti", "select-crypto-currency-title": "Izvēlies tokenu", "select-token.title": "Izvēlēties tokenu", "selectEarnAccount.chf.description.steps": "· <PERSON><PERSON><PERSON><PERSON> 24/7, be<PERSON> <PERSON><PERSON><PERSON><PERSON> {br}· Procenti uzkrājas katru sekundi {br}· Pārm<PERSON><PERSON><PERSON><PERSON> nodro<PERSON> noguldījumi ar <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} gadā CHF", "selectEarnAccount.eur.description.steps": "· <PERSON><PERSON><PERSON><PERSON> 24/7, bez bloķēšanas {br}· Procenti uzkrājas katru sekundi {br}· Pārnodrošin<PERSON><PERSON> aizdevumi ar <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} gadā EUR", "selectEarnAccount.subtitle": "<PERSON>ari main<PERSON>t jeb<PERSON>r<PERSON> laikā", "selectEarnAccount.title": "Izvēlies valūtu", "selectEarnAccount.usd.description.steps": "· <PERSON><PERSON><PERSON><PERSON> 24/7, bez bloķēšanas {br}· Procenti uzkrājas katru sekundi {br}· Pārnodr<PERSON><PERSON><PERSON><PERSON><PERSON> <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} gadā USD", "selectEarnAccount.zero.description_general": "<PERSON><PERSON>, nepel<PERSON> procentus", "selectEarnAccount.zero.title": "0% gadā", "selectRechargeThreshold.button.enterAmount": "<PERSON><PERSON><PERSON> summu", "selectRechargeThreshold.button.setTo": "Iestatīt uz {amount}", "selectRechargeThreshold.description.line1": "Kad kartes saldo nokr<PERSON>tas zem {amount}, tā automātiski tiek papildināta līdz {amount} no tava Earn konta.", "selectRechargeThreshold.description.line2": "Zemāks mērķis ļauj turēt vairāk līdzekļu tavā Earn kontā (pelnīt 3%). To vari mainīt jebkurā laikā.", "selectRechargeThreshold.title": "Iestati kartes mērķa saldo", "select_currency_to_withdraw.select_token_to_withdraw": "Izvēlies tokenu, ko izņ<PERSON>t", "send-card-token.form.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "send-card-token.form.send-amount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> summa", "send-card-token.form.title": "<PERSON><PERSON><PERSON> naudu kartei", "send-card-token.form.to-address": "<PERSON><PERSON>", "send-safe-transaction.network-fee-widget.error": "Tev vajag {amount} vai izvēlies citu tokenu", "send-safe-transaction.network-fee-widget.no-fee": "Bez maksas", "send-safe-transaction.network-fee-widget.title": "<PERSON><PERSON><PERSON>", "send-safe-transaction.network_fee_widget.title": "<PERSON><PERSON><PERSON> maksa", "send.banner.fees": "<PERSON>v ne<PERSON> {amount} vēl {currency} , lai samaks<PERSON><PERSON> maksas", "send.banner.toAddressNotSupportedNetwork.subtitle": "Saņē<PERSON><PERSON><PERSON> {network}. Nomaini uz atbalstītu tokenu.", "send.banner.toAddressNotSupportedNetwork.title": "<PERSON>ņ<PERSON><PERSON><PERSON><PERSON><PERSON>", "send.banner.walletNotSupportedNetwork.subtitle": "Smart Wallet nevar veikt dar<PERSON> {network}. Nomaini uz atbalstītu tokenu.", "send.banner.walletNotSupportedNetwork.title": "Tokena tīkls nav atbalstīts", "send.empty-portfolio.empty-state": "Tokeni nav atrasti", "send.empty-portfolio.header": "Tokeni", "send.titile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendLimit.success.subtitle": "Jaunais limits būs aktīvs pēc 3 minūtēm.", "sendLimit.success.title": "<PERSON><PERSON><PERSON> izmaiņas aizņems 3 minūtes", "send_crypto.form.disconnected.cta.addFunds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Pārslēgties uz {network}", "send_crypto.form.disconnected.label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> summa", "send_to.qr_code.description": "<PERSON><PERSON><PERSON> kodu, lai sūtītu uz maciņu", "send_to.qr_code.title": "Skenēt QR kodu", "send_to_card.header": "<PERSON><PERSON><PERSON><PERSON><PERSON> uz kartes adresi", "send_to_card.select_sender.add_wallet": "<PERSON><PERSON><PERSON>", "send_to_card.select_sender.header": "Izvēlies sūtītāju", "send_to_card.select_sender.search.default_placeholder": "Meklēt adresi vai ENS", "send_to_card.select_sender.show_card_address_button_description": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> ad<PERSON>i", "send_token.form.select-address": "Izvēlies adresi", "send_token.form.send-amount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> summa", "send_token.form.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setLimit.amount.error.zero_amount": "<PERSON> nevarēsi veikt maksā<PERSON>", "setLimit.error.max_limit_reached": "Iestatīt maks. limitu {amount}", "setLimit.error.same_as_current_limit": "Vienāds ar esošo limitu", "setLimit.placeholder": "Pašreizējais: {amount}", "setLimit.submit": "Iestatī<PERSON> limitu", "setLimit.submit.error.amount_required": "<PERSON><PERSON><PERSON> summu", "setLimit.subtitle": "<PERSON><PERSON> ir summa, ko vari iztērēt dienā ar savu karti.", "setLimit.title": "Iestatīt dienas tēriņu limitu", "settings.accounts": "<PERSON><PERSON>", "settings.accountsSeeAll": "<PERSON><PERSON><PERSON><PERSON> visus", "settings.addAccount": "<PERSON><PERSON><PERSON>", "settings.card": "<PERSON><PERSON><PERSON>", "settings.connections": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.currency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> val<PERSON>ta", "settings.default_currency_selector.title": "<PERSON><PERSON><PERSON>", "settings.discord": "Discord", "settings.experimentalMode": "Eksperiment<PERSON><PERSON><PERSON>", "settings.experimentalMode.subtitle": "Testē jaunas <PERSON>", "settings.language": "Valoda", "settings.lockZeal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.open_expanded_view": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON> skatu", "settings.privacyPolicy": "Privātuma politika", "settings.settings": "Iestatījumi", "settings.termsOfUse": "<PERSON><PERSON><PERSON><PERSON>", "settings.twitter": "𝕏 / Twitter", "settings.version": "V<PERSON><PERSON> {version} vide: {env}", "setup-card.confirmation": "<PERSON><PERSON><PERSON><PERSON> virtuālo karti", "setup-card.confirmation.subtitle": "Veic maksāju<PERSON> tiešsaistē un pievieno savam {type} maciņam bezkontakta maksājumiem.", "setup-card.getCard": "Saņemt karti", "setup-card.order.physicalCard": "Fiziska karte", "setup-card.order.physicalCard.steps": "· Fiziska VISA Gnosis Pay {br}· Piegāde aizņem līdz 3 nedēļām {br}· Lieto klātienes maksājumiem un bankomātos. {br}· Pievieno Apple/Google makam (kur atbalstīts)", "setup-card.order.subtitle1": "Tu vari lietot vairākas kartes vienlaikus", "setup-card.order.title": "Kāda veida karti?", "setup-card.order.virtualCard": "<PERSON><PERSON><PERSON><PERSON><PERSON> karte", "setup-card.order.virtual_card.steps": "· Digitāla VISA Gnosis Pay {br}· Lieto uzreiz maksājumiem tiešsaistē {br}· Pievieno Apple/Google makam (kur atbalstīts)", "setup-card.orderCard": "Pa<PERSON><PERSON><PERSON><PERSON><PERSON> karti", "setup-card.virtual-card": "<PERSON><PERSON><PERSON><PERSON> virtuālo karti", "setup.notifs.fakeAndroid.title": "Paziņojumi par maksājumiem un ienākošajiem pārskaitījumiem", "setup.notifs.fakeIos.subtitle": "Zeal var tev paziņot, kad saņem naudu vai tērē ar savu Visa karti. To var mainīt vēlāk.", "setup.notifs.fakeIos.title": "Paziņojumi par maksājumiem un ienākošajiem pārskaitījumiem", "sign.PermitAllowanceItem.spendLimit": "Tēriņu limits", "sign.ledger.subtitle": "<PERSON><PERSON><PERSON> nos<PERSON>ījām darījuma pieprasījumu uz tavu aparatūras maciņu. <PERSON><PERSON><PERSON><PERSON>, turpini tur.", "sign.ledger.title": "Parakstīt ar a<PERSON>", "sign.passkey.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> prasīs para<PERSON>t a<PERSON>.", "sign.passkey.title": "Izvēlies Passkey", "signal_aborted_for_uknown_reason.title": "Tīkla pieprasījums atcelts", "simulatedTransaction.BridgeTrx.info.title": "Bridge", "simulatedTransaction.CardTopUp.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.CardTopUpTrx.info.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> karti", "simulatedTransaction.NftCollectionApproval.approve": "NFT kolekcijas apstiprinājums", "simulatedTransaction.OrderBuySignMessage.title": "Pirkt", "simulatedTransaction.OrderCardTopupSignMessage.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.OrderEarnDepositBridge.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.P2PTransaction.info.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.PermitSignMessage.title": "Atļauja", "simulatedTransaction.SingleNftApproval.approve": "NFT apstiprinājums", "simulatedTransaction.UnknownSignMessage.title": "Parakstīt", "simulatedTransaction.Withdrawal.info.title": "<PERSON>zmaks<PERSON>", "simulatedTransaction.approval.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.approve.info.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.p2p.info.account": "<PERSON><PERSON>", "simulatedTransaction.p2p.info.unlabelledAccount": "Neap<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.info.receive": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.info.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.using": "<PERSON><PERSON><PERSON><PERSON><PERSON> {app}", "simulation.approval.modal.text": "<PERSON><PERSON><PERSON><PERSON><PERSON> atļauju, tu dod piekrišanu konkrētai lietotnei/viedlīgumam izmantot tavus tokenus vai NFT turpmākos darījumos.", "simulation.approval.modal.title": "<PERSON><PERSON> ir a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?", "simulation.approval.spend-limit.label": "Tēriņu limits", "simulation.approve.footer.for": "<PERSON><PERSON>", "simulation.approve.unlimited": "Neierobežots", "simulationNotAvailable.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smart-wallet-activation-view.on": "Tīklā", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password var bloķēt piekļuvi tavam maciņam", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Izmanto Apple vai Google, lai droši iestatītu Zeal", "smart-wallet.passkey-notice.title": "Neizmanto 1Password", "spend-limits.high.modal.text": "Iestati tēri<PERSON> limit<PERSON>, kas ir tuvu žetonu da<PERSON>, ko faktiski izmantosi ar lietotni vai viedo līgumu. Augsti limiti ir riskanti un var atvieglot krāpniekiem tavu žetonu nozagšanu.", "spend-limits.high.modal.text_sign_message": "<PERSON><PERSON><PERSON><PERSON><PERSON> limitam jābūt tuvu tam tokenu daud<PERSON>m, ko reāli izmantosi lietotnē vai viedajā līgumā. Augsti limiti ir riskanti un var atvieglot krāpniekiem tavu tokenu no<PERSON>.", "spend-limits.high.modal.title": "Augsts tēriņu limits", "spend-limits.modal.text": "Tēriņ<PERSON> limits ir žetonu daudzu<PERSON>, ko lietotne var izmantot tavā vārdā. Šo limitu vari jebkurā laikā mainīt vai noņemt. Drošībai ieteicams iestatīt tēriņu limitus, kas ir tuvu žetonu daud<PERSON>, ko faktiski izmantosi ar lietotni.", "spend-limits.modal.title": "Kas ir tēri<PERSON><PERSON> limits?", "spent-limit-info.modal.description": "<PERSON><PERSON>ri<PERSON><PERSON> limits nosaka, cik daudz tokenu lietotne var izmantot tavā vārdā. Tu vari mainīt vai noņemt šo limitu jebkurā laikā. Drošībai saglabā tēriņu limitus tuvu tam tokenu daudzumam, ko reāli izmantosi lietotnē.", "spent-limit-info.modal.title": "Kas ir tēri<PERSON><PERSON> limits?", "sswaps-io.transfer-provider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storage.accountDetails.activateWallet": "Aktivizēt <PERSON>", "storage.accountDetails.changeWalletLabel": "<PERSON><PERSON><PERSON>", "storage.accountDetails.deleteWallet": "<PERSON><PERSON><PERSON><PERSON>", "storage.accountDetails.setup_recovery_kit": "Atkopšanas komplekts", "storage.accountDetails.showPrivateKey": "<PERSON><PERSON><PERSON><PERSON><PERSON> privā<PERSON>", "storage.accountDetails.showWalletAddress": "<PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "storage.accountDetails.smartBackup": "Dub<PERSON>ēšana un atkopšana", "storage.accountDetails.viewSsecretPhrase": "<PERSON><PERSON><PERSON><PERSON> slepeno frāzi", "storage.accountDetails.zealSmartWallets": "<PERSON><PERSON> ir Zeal Smart Wallets?", "storage.manageAccounts.title": "<PERSON><PERSON><PERSON><PERSON>", "submit-userop.progress.text": "<PERSON><PERSON><PERSON>", "submit.error.amount_high": "Summa ir pā<PERSON><PERSON><PERSON>la", "submit.error.amount_hight": "Summa pā<PERSON><PERSON><PERSON>la", "submit.error.amount_low": "Summa ir pār<PERSON>k maza", "submit.error.amount_required": "<PERSON><PERSON><PERSON> summu", "submit.error.maximum_number_of_characters_exceeded": "Samazini rakstz<PERSON>", "submit.error.not_enough_balance": "Nepietiekams saldo", "submit.error.recipient_required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submit.error.routes_not_found": "Maršruti nav atrasti", "submitSafeTransaction.monitor.title": "Transakcijas rezultāts", "submitSafeTransaction.sign.title": "Transakcijas rezultāts", "submitSafeTransaction.state.sending": "<PERSON><PERSON><PERSON>", "submitSafeTransaction.state.sign": "Izveido", "submitSafeTransaction.submittingToRelayer.title": "Transakcijas rezultāts", "submitTransaction.cancel": "Atcelt", "submitTransaction.cancel.attemptingToStop": "Mēģina apturēt", "submitTransaction.cancel.failedToStop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.cancel.stopped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.cancel.title": "Darījuma priekšskatījums", "submitTransaction.failed.banner.description": "<PERSON><PERSON><PERSON><PERSON> negaidīti atcēla šo darījumu. Mēģini vēlreiz vai sazinies ar mums.", "submitTransaction.failed.banner.title": "Dar<PERSON><PERSON><PERSON> neizdev<PERSON>s", "submitTransaction.failed.execution_reverted.title": "Lietotnē rad<PERSON>", "submitTransaction.failed.execution_reverted_without_message.title": "Lietotnē rad<PERSON>", "submitTransaction.failed.out_of_gas.description": "<PERSON>ī<PERSON><PERSON> at<PERSON><PERSON><PERSON>, jo tas pā<PERSON><PERSON><PERSON> paredz<PERSON>to tīkla maksu", "submitTransaction.failed.out_of_gas.title": "Tīkla k<PERSON>ū<PERSON>", "submitTransaction.sign.title": "<PERSON><PERSON><PERSON><PERSON> rezult<PERSON>", "submitTransaction.speedUp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue": "Pievienots r<PERSON>i", "submitTransaction.state.addedToQueue.short": "Rind<PERSON>", "submitTransaction.state.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.complete": "{currencyCode} pievie<PERSON><PERSON>", "submitTransaction.state.complete.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> savu <PERSON> portfeli", "submitTransaction.state.completed": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.includedInBlock": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "submitTransaction.state.includedInBlock.short": "Blokā", "submitTransaction.state.replaced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.sendingToNetwork": "Sū<PERSON> uz tīklu", "submitTransaction.stop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.submit": "<PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.bundled": "Rind<PERSON>", "submitted-user-operation.state.completed": "<PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.pending": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.rejected": "<PERSON><PERSON><PERSON><PERSON>", "submittedTransaction.failed.title": "Dar<PERSON><PERSON><PERSON> neizdev<PERSON>s", "success_splash.card_activated": "Karte aktivizēta", "supportFork.give-feedback.title": "<PERSON><PERSON><PERSON>", "supportFork.itercom.description": "<PERSON>eal palīdz ar jautājumiem par iema<PERSON>ām, <PERSON><PERSON><PERSON>, balvām un citu.", "supportFork.itercom.title": "Jaut<PERSON><PERSON><PERSON> par maciņu", "supportFork.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tē<PERSON>", "supportFork.zendesk.subtitle": "Gnosis Pay atbild uz jautājumiem par karšu maksājumiem, identitā<PERSON> pārbaudēm vai atmaksām.", "supportFork.zendesk.title": "<PERSON><PERSON><PERSON><PERSON> maksājumi un identitāte", "supported-networks.ethereum.warning": "<PERSON><PERSON><PERSON> ma<PERSON>", "supportedNetworks.networks": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "supportedNetworks.oneAddressForAllNetworks": "Viena adrese visiem tīkliem", "supportedNetworks.receiveAnyAssets": "<PERSON>va adrese der visiem tīkliem.", "swap.form.error.no_routes_found": "Maršruti nav atrasti", "swap.form.error.not_enough_balance": "Nepietiekams saldo", "swaps-io-details.bank.serviceProvider": "Pakalpojuma sniedzējs", "swaps-io-details.details.processing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swaps-io-details.pending": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swaps-io-details.rate": "<PERSON><PERSON><PERSON>", "swaps-io-details.serviceProvider": "Pakalpojuma sniedzējs", "swaps-io-details.transaction.from.processing": "<PERSON><PERSON><PERSON><PERSON>", "swaps-io-details.transaction.networkFees": "<PERSON><PERSON><PERSON> ma<PERSON>", "swaps-io-details.transaction.state.completed-transaction": "<PERSON><PERSON><PERSON><PERSON>", "swaps-io-details.transaction.state.started-transaction": "<PERSON><PERSON><PERSON><PERSON>", "swaps-io-details.transaction.to.processing": "<PERSON><PERSON><PERSON><PERSON>", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Depozīts drīz tiks pabeigts. Kinetex vēl apstrādā tavu transakciju.", "swapsIO.monitoring.awaitingLiqSend.title": "Aizkavējies", "swapsIO.monitoring.awaitingRecive.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swapsIO.monitoring.awaitingSend.title": "Rind<PERSON>", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Tokeni tika nosūtīti uz Kinetex, bet drīz tiks atgriezti. Kinetex nevarēja pabeigt mērķa transakciju.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "<PERSON><PERSON><PERSON><PERSON>", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Tokeni netika pārsūtīti nezin<PERSON> kļūdas dēļ<PERSON>, mēģini vēlreiz.", "swapsIO.monitoring.cancelledNoSlash.title": "Tokeni atgriezti", "swapsIO.monitoring.cancelledSlashed.subtitle": "Tokeni ir atgriezti. Kinetex nevarēja pabeigt mērķa transakciju.", "swapsIO.monitoring.cancelledSlashed.title": "Tokeni atgriezti", "swapsIO.monitoring.completed.title": "<PERSON><PERSON><PERSON><PERSON>", "taker-metadata.earn": "Pelni digitālos USD ar Sky", "taker-metadata.earn.aave": "Pelni digitālos EUR ar Aave", "taker-metadata.earn.aave.cashout24": "<PERSON><PERSON><PERSON><PERSON> naudu u<PERSON>, 24/7", "taker-metadata.earn.aave.trusted": "Uzticēti 27 mljrd. USD, 2+ gadi", "taker-metadata.earn.aave.yield": "I<PERSON><PERSON><PERSON><PERSON><PERSON> uzkr<PERSON> katru sekundi", "taker-metadata.earn.chf": "Pelni digit<PERSON>lajos CHF", "taker-metadata.earn.chf.cashout24": "Tūlītēja izmaksa 24/7", "taker-metadata.earn.chf.trusted": "Uzticēti 28 milj. CHF", "taker-metadata.earn.chf.yield": "I<PERSON><PERSON><PERSON><PERSON><PERSON> uzkr<PERSON> katru sekundi", "taker-metadata.earn.usd.cashout24": "<PERSON><PERSON><PERSON><PERSON> naudu u<PERSON>, 24/7", "taker-metadata.earn.usd.trusted": "Uzticēti 10,7 mljrd. USD, 5+ gadi", "taker-metadata.earn.usd.yield": "I<PERSON><PERSON><PERSON><PERSON><PERSON> uzkr<PERSON> katru sekundi", "test": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "to.titile": "<PERSON>ņ<PERSON><PERSON>", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "<PERSON><PERSON><PERSON><PERSON>", "token.groupHeader.titleWithSum": "A<PERSON><PERSON><PERSON> {sum}", "token.hidden_tokens.page.title": "<PERSON><PERSON>ē<PERSON><PERSON>i", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "<PERSON><PERSON><PERSON>", "token.widget.cashback_empty": "Vēl nav transakciju", "token.widget.emptyState": "Maciņā nav tokenu", "tokens.cash": "<PERSON><PERSON>", "top-up-card-from-earn-view.approve.for": "<PERSON><PERSON>", "top-up-card-from-earn-view.approve.into": "<PERSON><PERSON>", "top-up-card-from-earn-view.swap.from": "No", "top-up-card-from-earn-view.swap.to": "<PERSON><PERSON>", "top-up-card-from-earn-view.withdraw.to": "<PERSON><PERSON>", "top-up-card-from-earn.trx.title.approval": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "top-up-card-from-earn.trx.title.swap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> karti", "top-up-card-from-earn.trx.title.withdrawal": "Izmaksa no Earn konta", "topUpDapp.connectWallet": "<PERSON><PERSON><PERSON>", "topup-fee-breakdown.bungee-fee": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> snied<PERSON><PERSON><PERSON> maksa", "topup-fee-breakdown.header": "<PERSON><PERSON><PERSON><PERSON> maksa", "topup-fee-breakdown.network-fee": "<PERSON><PERSON><PERSON> maksa", "topup-fee-breakdown.total-fee": "<PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>a", "topup.continue-in-wallet": "<PERSON><PERSON><PERSON> savā maciņā", "topup.send.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "topup.submit-transaction.close": "Aizvērt", "topup.submit-transaction.sent-to-wallet": "<PERSON><PERSON><PERSON><PERSON>t {amount}", "topup.to": "<PERSON><PERSON>", "topup.transaction.complete.close": "Aizvērt", "topup.transaction.complete.try-again": "Mēģināt vēlreiz", "transaction-request.nonce-too-low.modal.button-text": "Aizvērt", "transaction-request.nonce-too-low.modal.text": "Darījums ar tādu pašu sērijas numuru (nonce) jau ir pabeig<PERSON>, tāpēc šo darījumu vairs nevar iesniegt. <PERSON><PERSON> var notikt, ja veic darījumus ar mazu laika starpību vai ja mēģini paātrin<PERSON>t vai atcelt darījumu, kas jau ir pabeigts.", "transaction-request.nonce-too-low.modal.title": "Darīju<PERSON> ar tādu pašu nonce jau ir pabeigts", "transaction-request.replaced.modal.button-text": "Aizvērt", "transaction-request.replaced.modal.text": "<PERSON><PERSON><PERSON> nevaram izsekot šī darījuma statusu. Iespēja<PERSON>, to ir aizst<PERSON><PERSON>s cits darījums vai RPC mezglam ir problēmas.", "transaction-request.replaced.modal.title": "<PERSON><PERSON><PERSON>", "transaction.activity.details.modal.close": "Aizvērt", "transaction.cancel_popup.cancel": "Nē, pagaidīt", "transaction.cancel_popup.confirm": "Jā, apturēt", "transaction.cancel_popup.description": "<PERSON>, tev jāmaksā jauna tīkla maksa sākotnēj<PERSON>s maksas vietā {oldFee}", "transaction.cancel_popup.description_without_original": "<PERSON>, ir j<PERSON><PERSON><PERSON><PERSON> jauna tīkla maksa", "transaction.cancel_popup.not_supported.subtitle": "Darīju<PERSON> aptur<PERSON><PERSON>na nav atbalstīta tīklā {network}", "transaction.cancel_popup.not_supported.title": "Nav <PERSON>", "transaction.cancel_popup.stopping_fee": "<PERSON><PERSON><PERSON> maksa par a<PERSON><PERSON><PERSON><PERSON><PERSON>", "transaction.cancel_popup.title": "Aptur<PERSON>t dar<PERSON>?", "transaction.in-progress": "Procesā", "transaction.inProgress": "Procesā", "transaction.speed_up_popup.cancel": "Nē, pagaidīt", "transaction.speed_up_popup.confirm": "Jā, ātrāk", "transaction.speed_up_popup.description": "<PERSON>, tev ir jāmaksā jauna tīkla maksa sākotnējās maksas vietā {amount}", "transaction.speed_up_popup.description_without_original": "<PERSON>, tev ir jā<PERSON><PERSON><PERSON> jauna tīkla maksa", "transaction.speed_up_popup.seed_up_fee_title": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>a", "transaction.speed_up_popup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dar<PERSON>?", "transaction.speedup_popup.not_supported.subtitle": "Dar<PERSON><PERSON><PERSON> pa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nav atbalstīta {network}", "transaction.speedup_popup.not_supported.title": "Nav <PERSON>", "transaction.subTitle.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionDetails.cashback.not-qualified": "Nav piemērots", "transactionDetails.cashback.paid": "{amount} iz<PERSON><PERSON><PERSON>ts", "transactionDetails.cashback.pending": "{amount} apstrādē", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "<PERSON><PERSON>inā<PERSON>", "transactionDetails.cashback_estimate": "Aptuvenais cashback", "transactionDetails.category": "Kategorija", "transactionDetails.exchangeRate": "Valūtas kurss", "transactionDetails.location": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vieta", "transactionDetails.payment-approved": "Maksājums a<PERSON>tiprināts", "transactionDetails.payment-declined": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionDetails.payment-reversed": "<PERSON><PERSON><PERSON><PERSON><PERSON> atcelts", "transactionDetails.recharge.amountSentFromEarn.title": "Summa no <PERSON><PERSON>n konta", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>ts kartē", "transactionDetails.recharge.rate.title": "<PERSON><PERSON><PERSON>", "transactionDetails.recharge.transactionId.title": "Darījuma ID", "transactionDetails.refund": "<PERSON><PERSON><PERSON><PERSON>", "transactionDetails.reversal": "Atcelšana", "transactionDetails.transactionCurrency": "<PERSON><PERSON><PERSON><PERSON> val<PERSON>ta", "transactionDetails.transactionId": "Darījuma ID", "transactionDetails.type": "Darījums", "transactionRequestWidget.approve.subtitle": "<PERSON><PERSON> {target}", "transactionRequestWidget.p2p.subtitle": "<PERSON><PERSON> {target}", "transactionRequestWidget.unknown.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> {target}", "transactionSafetyChecksPopup.title": "<PERSON><PERSON><PERSON><PERSON>", "transactions.main.activity.title": "Aktivitātes", "transactions.page.hiddenActivity.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aktivit<PERSON>", "transactions.page.title": "Aktivitātes", "transactions.viewTRXHistory.emptyState": "<PERSON><PERSON>l nav darījumu", "transactions.viewTRXHistory.errorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>t tavu darījumu vēsturi", "transactions.viewTRXHistory.hidden.emptyState": "Nav slēptu darījumu", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Aktivitātes testa tīklos netiek atbalstītas", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Aktivitā<PERSON> testa tīklos netiek atbalstītas{br}<link><PERSON><PERSON><PERSON><PERSON> pārlūk<PERSON></link>", "transfer_provider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transfer_setup_with_different_wallet.subtitle": "Bankas pārskaitījumi ir iestatīti ar citu maciņu. Pārskaitījumiem var piesaistīt tikai vienu maciņu.", "transfer_setup_with_different_wallet.swtich_and_continue": "P<PERSON><PERSON>lēgties un turpināt", "transfer_setup_with_different_wallet.title": "<PERSON><PERSON><PERSON>", "tx-sent-to-wallet.button": "Aizvērt", "tx-sent-to-wallet.subtitle": "<PERSON><PERSON><PERSON> {wallet}", "unblockProviderInfo.fees": "Tu saņem zemākās iespējamās maksas: 0% līdz $5k mēnesī un 0,2% virs <PERSON><PERSON>s summas.", "unblockProviderInfo.registration": "Unblock ir reģistrēts un FNTT autorizēts, lai sniegtu VASP maiņas un glabāšanas pakalpojumus, un ir reģistrēts MSB pakalpojumu sniedzējs ASV Fincen. <link><PERSON><PERSON>n<PERSON><PERSON> vair<PERSON>k</link>", "unblockProviderInfo.selfCustody": "<PERSON>va saņemtā digitālā nauda ir privāta, un neviens cits nevarēs kontrolēt tavus aktīvus.", "unblock_invalid_faster_payment_configuration.subtitle": "Tevis norādītais bankas konts neatbalsta Eiropas SEPA pārskaitījumus vai Lielbritānijas ātro maksājumu sistēmu. <PERSON><PERSON><PERSON><PERSON>, norā<PERSON> citu kontu.", "unblock_invalid_faster_payment_configuration.title": "Nepieciešams cits konts", "unknownTransaction.primaryText": "<PERSON><PERSON><PERSON>", "unsupportedCountry.subtitle": "Bankas pārskaitījumi vēl nav pieejami.", "unsupportedCountry.title": "Nav pieejams {country}", "update-app-popup.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir labo<PERSON>, jaunas funkcijas un vēl vairāk. Atjaunini uz jaunāko versiju un uzlabo savu Z<PERSON> pieredzi.", "update-app-popup.title": "<PERSON><PERSON><PERSON><PERSON>", "update-app-popup.update-now": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tag<PERSON>", "user_associated_with_other_merchant.subtitle": "<PERSON><PERSON> maciņu nevar izmantot bankas pārskaitījumiem. <PERSON><PERSON><PERSON><PERSON>, izmanto citu maciņu vai sazinies ar mums <PERSON>, lai saņ<PERSON>tu atbalstu un jaunumus.", "user_associated_with_other_merchant.title": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON>", "user_associated_with_other_merchant.try_with_another_wallet": "Mēģināt ar citu maci<PERSON>u", "user_email_already_exists.subtitle": "Tu jau esi iestatījis bankas pārskaitījumus ar citu maciņu. <PERSON><PERSON><PERSON><PERSON>, mēģini vēlreiz ar iep<PERSON>k<PERSON> i<PERSON> maciņu.", "user_email_already_exists.title": "Pārskaitīju<PERSON> i<PERSON>īti ar citu maciņu", "user_email_already_exists.try_with_another_wallet": "Mēģināt ar citu maci<PERSON>u", "validation.invalid.iban": "Nederīgs IBAN", "validation.required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lauks", "validation.required.first_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> vā<PERSON>", "validation.required.iban": "Jānorāda IBAN", "validation.required.last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "verify-passkey.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "verify-passkey.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka tava ieejas atslēga ir izveidota un pienācīgi aizsargāta.", "verify-passkey.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view-cashback.cashback-next-cycle": "Cashback likme pēc {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "<PERSON><PERSON><PERSON><PERSON>, lai sa<PERSON><PERSON><PERSON> cashback", "view-cashback.pending": "{money} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view-cashback.pending-rewards.not_paid": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc {days}d", "view-cashback.pending-rewards.paid": "<PERSON>ņ<PERSON><PERSON> šonedēļ", "view-cashback.received-rewards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view-cashback.upcoming": "Gaid<PERSON><PERSON> {money}", "virtual-card-order.configure-safe.loading-text": "Veido karti", "virtual-card-order.create-order.loading-text": "Aktivizē karti", "virtual-card-order.create-order.success-text": "Karte aktivizēta", "virtualCard.activateCard": "Aktivizēt karti", "walletDeleteConfirm.main_action": "<PERSON><PERSON><PERSON><PERSON>", "walletDeleteConfirm.subtitle": "Tev būs tas jāimportē vē<PERSON><PERSON><PERSON>, lai skatītu portfeli vai veiktu dar<PERSON>.", "walletDeleteConfirm.title": "Noņ<PERSON>t maciņ<PERSON>?", "walletSetting.header": "<PERSON><PERSON>ņ<PERSON>", "wallet_connect.connect.cancel": "Atcelt", "wallet_connect.connect.connect_button": "<PERSON><PERSON><PERSON>", "wallet_connect.connect.title": "<PERSON><PERSON><PERSON>", "wallet_connect.connected.title": "Savie<PERSON><PERSON>", "wallet_connect_add_chain_missing.title": "Tīkls netiek atbalstīts", "wallet_connect_proposal_expired.title": "Savienojums beidzies", "withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withdraw.confirmation.close": "Atcelt", "withdraw.confirmation.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "withdrawal_request.completed": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal_request.pending": "<PERSON><PERSON> a<PERSON>", "zeal-dapp.connect-wallet.cta.primary.connecting": "Savieno...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "<PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.cta.secondary": "Atcelt", "zeal-dapp.connect-wallet.title": "<PERSON>, sa<PERSON><PERSON>", "zealSmartWalletInfo.gas": "<PERSON><PERSON><PERSON> tīkla maksu ar daud<PERSON> tokeniem; <PERSON><PERSON><PERSON> pop<PERSON> ERC20 tokenus atbalst<PERSON><PERSON><PERSON> t<PERSON>, lai maksā<PERSON> tīkla maksu, nevis tikai tīkla p<PERSON>.", "zealSmartWalletInfo.recover": "Bez sle<PERSON>ajām frāzēm; Atkop, izmantojot biometrisko ieejas atslēgu no sava paroļu p<PERSON>, iCloud vai Google konta.", "zealSmartWalletInfo.selfCustodial": "Pilnībā privā<PERSON>; <PERSON><PERSON><PERSON> atslēgas paraksti tiek apstiprināti ķēdē, lai samazinātu centralizētu atkarību.", "zealSmartWalletInfo.title": "Par Zeal Smart Wallets", "zeal_a_rewards_already_claimed_error.title": "Atlīdzība jau <PERSON>", "zwidget.minimizedDisconnected.label": "Zeal atvienots"}