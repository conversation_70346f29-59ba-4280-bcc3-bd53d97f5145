{"Account.ListItem.details.label": "Details", "AddFromAddress.success": "Portemon<PERSON>", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{<PERSON>n wallets} one{{count} wallet} other{{count} wallets}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Geheime zinsnede {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Maak nieuwe wallets aan met een van je bestaande geheime zinsneden.", "AddFromExistingSecretPhrase.SelectPhrase.title": "Kies een geheime zinsnede", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "Je geheime zinsnede kan meerdere wallets beveiligen. <PERSON><PERSON> welke je wilt gebruiken.", "AddFromExistingSecretPhrase.WalletSelection.title": "Snel een wallet toevoegen", "AddFromExistingSecretPhrase.success": "Wallets toegevoegd aan <PERSON>", "AddFromHardwareWallet.subtitle": "Selecteer je hardware-wallet om met Zeal te verbinden", "AddFromHardwareWallet.title": "Hardware-wallet", "AddFromNewSecretPhrase.WalletSelection.subtitle": "Selecteer de portemonnees die je wilt importeren", "AddFromNewSecretPhrase.WalletSelection.title": "Portemonnees importeren", "AddFromNewSecretPhrase.accounts": "Portemonnees", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "Een geheime zinsnede fungeert als een sleutelhanger voor miljoenen portemonnees, elk met een unieke privésleutel.{br}{br}Je kunt nu zoveel portemonnees importeren als je nodig hebt, of er later meer toevoegen.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Portemonnees met geheime zinsnede", "AddFromNewSecretPhrase.subtitle": "Voer je geheime zinsnede in, gescheiden door spaties", "AddFromNewSecretPhrase.success_secret_phrase_added": "Geheime zinsnede toegevoegd 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Portemon<PERSON> toe<PERSON>d aan <PERSON>", "AddFromNewSecretPhrase.wallets": "Wallets", "AddFromPrivateKey.subtitle": "<PERSON><PERSON><PERSON> je privésleutel in", "AddFromPrivateKey.success": "Privésleutel toegevoegd 🎉", "AddFromPrivateKey.title": "<PERSON><PERSON><PERSON>", "AddFromPrivateKey.typeOrPaste": "Typ of plak hier", "AddFromSecretPhrase.importWallets": "{count,plural,=0{<PERSON><PERSON> wallets geselecteerd} one{Importeer wallet} other{Importeer {count} wallets}}", "AddFromTrezor.AccountSelection.title": "I<PERSON><PERSON><PERSON>", "AddFromTrezor.hwWalletTip.subtitle": "Een hardware wallet bevat mil<PERSON><PERSON>n portemonnees met verschillende adressen. Je kunt nu zoveel portemonnees importeren als je nodig hebt, of later meer toevoegen.", "AddFromTrezor.hwWalletTip.title": "Importeren vanuit Hardware Wallets", "AddFromTrezor.importAccounts": "{count,plural,=0{<PERSON><PERSON> wallets geselecteerd} one{Importeer wallet} other{Importeer {count} wallets}}", "AddFromTrezor.success": "Portemon<PERSON> toe<PERSON>d aan <PERSON>", "ApprovalSpenderTypeCheck.failed.subtitle": "Waarschijnlijk oplichting: besteders horen contracten te zijn", "ApprovalSpenderTypeCheck.failed.title": "Besteder is een port<PERSON><PERSON>, geen contract", "ApprovalSpenderTypeCheck.passed.subtitle": "Je geeft doorgaans toestemming aan contracten", "ApprovalSpenderTypeCheck.passed.title": "Besteder is een smart contract", "BestReturns.subtitle": "Deze swap-a<PERSON><PERSON><PERSON> geeft je de hoogste opbre<PERSON>st, inclusief alle kosten.", "BestReturnsPopup.title": "Beste opbrengst", "BlacklistCheck.Failed.subtitle": "Kwaadaardige meldingen door <source></source>", "BlacklistCheck.Failed.title": "Site staat op een zwarte lijst", "BlacklistCheck.Passed.subtitle": "<PERSON>n k<PERSON>ardige meldingen door <source></source>", "BlacklistCheck.Passed.title": "Site staat niet op een zwarte lijst", "BlacklistCheck.failed.statusButton.label": "Site is gerapporteerd", "BridgeRoute.slippage": "Slippage {slippage}", "BridgeRoute.title": "Bridge provider", "CheckConfirmation.InProgress": "Bezig...", "CheckConfirmation.success.splash": "Voltooid", "ChooseImportOrCreateSecretPhrase.subtitle": "Importeer een geheime zinsnede of maak een nieuwe aan", "ChooseImportOrCreateSecretPhrase.title": "Geheime zinsnede toevoegen", "ConfirmTransaction.Simuation.Skeleton.title": "Veiligheidscontroles uitvoeren…", "ConnectionSafetyCheckResult.passed": "Veiligheidscontrole geslaagd", "ContactGnosisPaysupport": "Contacteer Gnosis Pay", "CopyKeyButton.copied": "Gekopieerd", "CopyKeyButton.copyYourKey": "<PERSON><PERSON><PERSON> je sleutel", "CopyKeyButton.copyYourPhrase": "<PERSON><PERSON><PERSON> je zin", "DAppVerificationCheck.Failed.subtitle": "Site staat niet vermeld op <source></source>", "DAppVerificationCheck.Failed.title": "Site niet gevonden in app-registers", "DAppVerificationCheck.Passed.subtitle": "Site staat vermeld op <source></source>", "DAppVerificationCheck.Passed.title": "Site komt voor in app-registers", "DAppVerificationCheck.failed.statusButton.label": "Site niet gevonden in app-registers", "ERC20.tokens.emptyState": "We hebben geen tokens gevonden", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Prioriteitskost", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Laatste {period}: tussen {from} en {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Basiskosten: {baseFee} • Veiligheidsbuffer: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Kon de huidige basiskosten niet ophalen", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Hoger dan volgende Nonce. Zal vastlopen", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "Kan nonce niet lager instellen dan huidige.", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "We konden de prioriteitskost niet berekenen", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Kon de huidige max. kosten niet ophalen", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "<PERSON>. kosten", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Kan vast<PERSON>en tot netwerkkosten dalen", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "<PERSON><PERSON>", "EditFeeModal.Custom.gasLimit.title": "Gaslimiet {gasLimit}", "EditFeeModal.Custom.title": "Geavanceerde instellingen", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Zal vastlopen tot basiskosten dalen", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Lage kost. Kan vastlopen", "EditFeeModal.EditGasLimit.estimatedGas": "Geschat gas: {estimated} • Veiligheidsbuffer: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "<PERSON>er dan geschatte limiet. Transactie mislukt", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Minder dan voorgestelde limiet. Transactie kan mislukken", "EditFeeModal.EditGasLimit.subtitle": "Stel de maximale hoeveelheid gas in die je voor deze overschrijving wilt gebruiken. Je overschrijving mislukt als je een lagere limiet instelt dan nodig.", "EditFeeModal.EditGasLimit.title": "Gaslimiet bewerken", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "<PERSON><PERSON> dan minimale gaslimiet: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "Hoger dan volgende Nonce. Zal vastlopen", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "<PERSON><PERSON> kan niet lager zijn dan de huidige", "EditFeeModal.EditNonce.subtitle": "Je overschrijving loopt vast als je een andere dan de volgende nonce instelt", "EditFeeModal.EditNonce.title": "<PERSON><PERSON> bewerken", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "Nodig {amount} om te verzenden", "EditFeeModal.Header.Time.unknown": "Tij<PERSON> onbekend", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Max. {fee}", "EditFeeModal.Header.fee.unknown": "<PERSON><PERSON> on<PERSON>end", "EditFeeModal.Header.subsequent_failed": "Schattingen zijn mogelijk verouderd, laatste update mislukt", "EditFeeModal.Layout.Header.ariaLabel": "Huidige kost", "EditFeeModal.MaxFee.subtitle": "De max. kost is het meeste dat je voor een overschrijving betaalt, maar meestal betaal je de voorspelde kost. Deze extra buffer zorgt ervoor dat je overschrijving doorgaat, zelfs als het netwerk vertraagt of duurder wordt.", "EditFeeModal.MaxFee.title": "Max. netwerkkost", "EditFeeModal.SelectPreset.Time.unknown": "Tij<PERSON> onbekend", "EditFeeModal.SelectPreset.ariaLabel": "<PERSON><PERSON> k<PERSON>", "EditFeeModal.SelectPreset.fast": "Snel", "EditFeeModal.SelectPreset.normal": "Normaal", "EditFeeModal.SelectPreset.slow": "<PERSON><PERSON><PERSON>", "EditFeeModal.ariaLabel": "Netwerkkost bewerken", "FailedSimulation.Confirmation.Item.subtitle": "Er was een interne fout", "FailedSimulation.Confirmation.Item.title": "<PERSON><PERSON> overschrijving niet simuleren", "FailedSimulation.Confirmation.subtitle": "Weet je zeker dat je wilt doorgaan?", "FailedSimulation.Confirmation.title": "<PERSON> ondertekent blind", "FailedSimulation.Title": "Simulatiefout", "FailedSimulation.footer.subtitle": "Er was een interne fout", "FailedSimulation.footer.title": "<PERSON><PERSON> overschrijving niet simuleren", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Nodig {amount} om de transactie te versturen", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Verwerking kan lang duren", "FeeForecastWidget.networkFee": "Netwerkkost", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Kon de netwerkkost niet berekenen", "FeeForecastWidget.subsequentFailed.message": "Schattingen zijn mogelijk verouderd, laatste update mislukt", "FeeForecastWidget.unknownDuration": "Onbekend", "FeeForecastWidget.unknownFee": "Onbekend", "GasCurrencySelector.balance": "Saldo: {balance}", "GasCurrencySelector.networkFee": "Netwerkkost", "GasCurrencySelector.payNetworkFeesUsing": "Netwerkkosten betalen met", "GasCurrencySelector.removeDefaultGasToken.description": "Betaal kosten vanuit het grootste saldo", "GasCurrencySelector.removeDefaultGasToken.title": "Automatische kostenafhandeling", "GasCurrencySelector.save": "Opsla<PERSON>", "GoogleDriveBackup.BeforeYouBegin.first_point": "Als ik mijn <PERSON>-wachtwoord vergeet, verlies ik mijn bezittingen voorgoed", "GoogleDriveBackup.BeforeYouBegin.second_point": "Als ik de toegang tot mijn Google Drive verlies of mijn herstelbestand wijzig, verlies ik mijn bezittingen voorgoed", "GoogleDriveBackup.BeforeYouBegin.subtitle": "<PERSON><PERSON><PERSON> het volgende punt over p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> te begrijpen en te aanvaarden:", "GoogleDriveBackup.BeforeYouBegin.third_point": "<PERSON>eal kan me niet helpen mijn <PERSON>-wachtwoord of mijn toegang tot Google Drive te herstellen", "GoogleDriveBackup.BeforeYouBegin.title": "<PERSON>oor je <PERSON>t", "GoogleDriveBackup.loader.subtitle": "<PERSON>ur het verzoek op Google Drive goed om je herstelbestand te uploaden", "GoogleDriveBackup.loader.title": "Wachten op goedkeuring...", "GoogleDriveBackup.success": "Back-up ges<PERSON><PERSON>d 🎉", "MonitorOffRamp.overServiceTime": "De meeste overschrijvingen worden voltooid binnen {estimated_time}, maar soms duren ze langer door extra controles. Dit is normaal en je geld is veilig tijdens deze controles.{br}{br}Als de transactie niet binnen  voltooid is {support_soft_deadline}, {contact_support}", "MonitorOnRamp.contactSupport": "Neem contact op met support", "MonitorOnRamp.from": "<PERSON>", "MonitorOnRamp.fundsReceived": "Geld ontvangen", "MonitorOnRamp.overServiceTime": "De meeste overschrijvingen zijn voltooid binnen {estimated_time}, maar soms duren ze langer door extra controles. Dit is normaal en je geld is veilig tijdens deze controles.{br}{br}Als de transactie niet is voltooid binnen {support_soft_deadline}, neem dan {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Verzenden naar je portemonnee", "MonitorOnRamp.to": "<PERSON>ar", "MonitorOnRamp.waitingForTransfer": "Wachten op je overschrijving", "NftCollectionCheck.failed.subtitle": "Collectie is niet geverifieerd op <source></source>", "NftCollectionCheck.failed.title": "Collectie is niet geverifieerd", "NftCollectionCheck.passed.subtitle": "Collectie is geverifieerd op <source></source>", "NftCollectionCheck.passed.title": "Collectie is geverifieerd", "NftCollectionInfo.entireCollection": "Volledige collectie", "NoSigningKeyStore.createAccount": "Account aan<PERSON>ken", "NonceRangeError.biggerThanCurrent.message": "Transactie zal vastlopen", "NonceRangeError.lessThanCurrent.message": "Transactie zal mislukken", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "<PERSON><PERSON> is hoger dan de huidige. Verlaag de Nonce om te voorkomen dat de transactie vastloopt.", "NonceRangeErrorPopup.biggerThanCurrent.title": "Transactie zal vastlopen", "P2pReceiverTypeCheck.failed.subtitle": "<PERSON>erstuur je naar het juiste adres?", "P2pReceiverTypeCheck.failed.title": "Ontvanger is een smart contract, geen portemonnee", "P2pReceiverTypeCheck.passed.subtitle": "Meestal verstuur je activa naar andere portemonnees", "P2pReceiverTypeCheck.passed.title": "Ontvanger is een port<PERSON>nee", "PasswordCheck.title": "<PERSON><PERSON><PERSON> wacht<PERSON> in", "PasswordChecker.subtitle": "<PERSON><PERSON><PERSON> je wachtwoord in om te verifiëren dat jij het bent", "PermitExpirationCheck.failed.subtitle": "Houd het kort en alleen zo lang als nodig", "PermitExpirationCheck.failed.title": "<PERSON> ve<PERSON>", "PermitExpirationCheck.passed.subtitle": "Hoelang een app je tokens kan gebruiken", "PermitExpirationCheck.passed.title": "Vervaltijd is niet te lang", "PrivateKeyValidationError.moreThanMaximumWords": "<PERSON>. {count} woorden", "PrivateKeyValidationError.notValidPrivateKey": "Dit is geen geldige privésleutel", "PrivateKeyValidationError.secretPhraseIsInvalid": "Geheime zin is niet geldig", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Woord #{index} is verkeerd gespeld of ongeldig", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} woord} other{{count} woorden}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Geheime zinsnedes en privésleutels worden versleuteld en verlaten dit apparaat nooit", "SecretPhraseReveal.header": "<PERSON><PERSON> geheime zin", "SecretPhraseReveal.hint": "<PERSON><PERSON> je zin met ni<PERSON><PERSON>. <PERSON><PERSON><PERSON> hem <PERSON> en offline", "SecretPhraseReveal.skip.subtitle": "Je kunt dit later doen, maar als je dit apparaat verliest voordat je je zin hebt genoteerd, verlies je alle bezittingen in deze portemonnee", "SecretPhraseReveal.skip.takeTheRisk": "Ik waag het", "SecretPhraseReveal.skip.title": "Zin noteren <PERSON>an?", "SecretPhraseReveal.skip.writeDown": "<PERSON><PERSON>", "SecretPhraseReveal.skipForNow": "<PERSON><PERSON>", "SecretPhraseReveal.subheader": "Noteer hem en bewaar hem veilig offline. <PERSON>arna vragen we je hem te verifiëren.", "SecretPhraseReveal.verify": "Verifiëren", "SelectCurrency.tokens": "Tokens", "SelectCurrency.tokens.emptyState": "We hebben geen tokens gevonden", "SelectRoute.slippage": "Slippage {slippage}", "SelectRoutes.emptyState": "We hebben geen routes voor deze swap gevonden", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Verbind portemon<PERSON>", "SendERC20.labelAddress.inputPlaceholder": "Walletlabel", "SendERC20.labelAddress.subtitle": "Label deze portemonnee voor later.", "SendERC20.labelAddress.title": "<PERSON><PERSON> deze portemonnee een label", "SendERC20.send_to": "<PERSON><PERSON><PERSON><PERSON> naar", "SendERC20.tokens": "Tokens", "SendOrReceive.bankTransfer.primaryText": "Bankoverschrijving", "SendOrReceive.bankTransfer.shortText": "<PERSON><PERSON><PERSON>, direct geld storten en opnemen", "SendOrReceive.bridge.primaryText": "Bridge", "SendOrReceive.bridge.shortText": "Tokens overschrijven tussen netwerken", "SendOrReceive.receive.primaryText": "Ontvangen", "SendOrReceive.receive.shortText": "Ontvang tokens of collectibles", "SendOrReceive.send.primaryText": "<PERSON><PERSON><PERSON><PERSON>", "SendOrReceive.send.shortText": "Verstuur tokens naar elk adres", "SendOrReceive.swap.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.swap.shortText": "Tokens wisselen", "SendSafeTransaction.Confirm.loading": "Veiligheidscontroles uitvoeren…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Versleutel herstelbestand met wachtwoord", "SetupRecoveryKit.google.subtitle": "Gesynchroniseerd {date}", "SetupRecoveryKit.google.title": "Google Drive-back-up", "SetupRecoveryKit.subtitle": "Je hebt minstens één manier nodig om je account te herstellen als je Zeal verwijdert of van apparaat wisselt", "SetupRecoveryKit.title": "Herstelkit instellen", "SetupRecoveryKit.writeDown.subtitle": "<PERSON><PERSON> geheime zin", "SetupRecoveryKit.writeDown.title": "Hand<PERSON><PERSON> back-up", "Sign.CheckSafeDeployment.activate": "<PERSON><PERSON>", "Sign.CheckSafeDeployment.subtitle": "Voordat je kunt inloggen bij een app of een off-chain bericht kunt ondertekenen, moet je je apparaat op dit netwerk activeren. Dit gebeurt nadat je een Smart Wallet hebt geïnstalleerd of hersteld.", "Sign.CheckSafeDeployment.title": "Activeer apparaat op dit netwerk", "Sign.Simuation.Skeleton.title": "Veiligheidscontroles uitvoeren…", "SignMessageSafetyCheckResult.passed": "Veiligheidscontroles geslaagd", "SignMessageSafetyChecksPopup.title.permits": "Permit-veiligheidscontroles", "SimulationFailedConfirmation.subtitle": "We hebben deze overschrijving gesimuleerd en een probleem gevonden waardoor deze zou mislukken. Je kunt de overschrijving indienen, maar deze zal waarschijnlijk mislukken en je kunt je netwerkkost verliezen.", "SimulationFailedConfirmation.title": "Overschrijving mislukt waarschijnlijk", "SimulationNotSupported.Title": "Simulatie niet{br}ondersteund op{br}{network}", "SimulationNotSupported.footer.subtitle": "Je kunt deze overschrijving nog steeds indienen", "SimulationNotSupported.footer.title": "Simulatie niet ondersteund", "SlippagePopup.custom": "Aangepast", "SlippagePopup.presetsHeader": "Swap slippage", "SlippagePopup.title": "Slippage-instellingen", "SmartContractBlacklistCheck.failed.subtitle": "Kwaadaardige meldingen door <source></source>", "SmartContractBlacklistCheck.failed.title": "Contract staat op een zwarte lijst", "SmartContractBlacklistCheck.passed.subtitle": "<PERSON>n k<PERSON>ardige meldingen door <source></source>", "SmartContractBlacklistCheck.passed.title": "Contract staat niet op een zwarte lijst", "SuspiciousCharactersCheck.Failed.subtitle": "Dit is een veelvoorkomende phishing-tactiek", "SuspiciousCharactersCheck.Failed.title": "We controleren op veelvoorkomende phishing-patronen", "SuspiciousCharactersCheck.Passed.subtitle": "We controleren op phishing-pogingen", "SuspiciousCharactersCheck.Passed.title": "Adres heeft geen ongebruikelijke tekens", "SuspiciousCharactersCheck.failed.statusButton.label": "Adres bevat ongebruikelijke tekens ", "TokenVerificationCheck.failed.subtitle": "Token staat niet genoteerd op <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} is niet geverifieerd door CoinGecko", "TokenVerificationCheck.passed.subtitle": "Token staat genoteerd op <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} is geverifieerd door CoinGecko", "TopupDapp.MonitorTransaction.success.splash": "Voltooid", "TransactionSafetyCheckResult.passed": "Veiligheidscontroles geslaagd", "TransactionSimulationCheck.failed.subtitle": "Fout: {errorMessage}", "TransactionSimulationCheck.failed.title": "Transactie zal waarschijnlijk mislukken", "TransactionSimulationCheck.passed.subtitle": "Simulatie uitgevoerd met <source></source>", "TransactionSimulationCheck.passed.title": "Transactievoorbeeld was succesvol", "TrezorError.trezor_action_cancelled.action": "Sluiten", "TrezorError.trezor_action_cancelled.subtitle": "Je hebt de transactie geweigerd op je hardware wallet", "TrezorError.trezor_device_used_elsewhere.action": "Synchronisee<PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "<PERSON>org ervoor dat je alle andere open sessies sluit en probeer je Trezor opnieuw te synchroniseren", "TrezorError.trezor_method_cancelled.action": "Synchronisee<PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "Zorg ervoor dat Trezor wallets naar Zeal mag exporteren", "TrezorError.trezor_permissions_not_granted.action": "Synchronisee<PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "<PERSON><PERSON> toestemming om alle wallets te zien", "TrezorError.trezor_pin_cancelled.action": "Synchronisee<PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "<PERSON><PERSON> gean<PERSON> op het apparaat", "TrezorError.trezor_popup_closed.action": "Synchronisee<PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "Het Trezor-dialoogvenster werd onverwacht gesloten", "TrxLikelyToFail.lessThanEstimatedGas.message": "Transactie zal mislukken", "TrxLikelyToFail.lessThanMinimumGas.message": "Transactie zal mislukken", "TrxLikelyToFail.lessThanSuggestedGas.message": "Zal waarschijnlijk mislukken", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "Gaslimiet van de overschrijving is te laag. Verhoog de gaslimiet naar de voorgestelde limiet om mislukking te voorkomen.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "Overschrijving mislukt waarschijnlijk", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Gaslimiet is lager dan geschat gas. Verhoog de gaslimiet naar de voorgestelde limiet.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "Overschrijving zal mislukken", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "<PERSON><PERSON> basiskost is lager dan de huidige basiskost. Verhoog de max. basiskost om te voorkomen dat de overschrijving vastloopt.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "Overschrijving zal vastlopen", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "Max. kost van de overschrijving is te laag. Verhoog de max. kost om te voorkomen dat de overschrijving vastloopt.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "Overschrijving zal vastlopen", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Prioriteitskost is lager dan aanbevolen. Verhoog de prioriteitskost om de overschrijving te versnellen.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "Overschrijving kan lang duren", "UnsupportedMobileNetworkLayout.gotIt": "Begrepen!", "UnsupportedMobileNetworkLayout.subtitle": "Je kunt nog geen transacties uitvoeren of berichten ondertekenen op een netwerk met id {networkHexId} met de mobiele versie van <PERSON>{br}{br}<PERSON><PERSON><PERSON> over naar de browserextensie om op dit netwerk te kunnen handelen, terwijl we hard werken aan de ondersteuning ervan 🚀", "UnsupportedMobileNetworkLayout.title": "Netwerk niet ondersteund op mobiele versie van Zeal", "UnsupportedSafeNetworkLayout.subtitle": "Je kunt geen transacties uitvoeren of berichten ondertekenen op {network} met een Zeal Smart Wallet{br}{br}<PERSON><PERSON><PERSON> over naar een ondersteund netwerk of gebruik een Legacy wallet.", "UnsupportedSafeNetworkLayoutk.title": "Netwerk wordt niet ondersteund voor Smart Wallet", "UserConfirmationPopup.goBack": "<PERSON><PERSON><PERSON>", "UserConfirmationPopup.submit": "<PERSON>ch zenden", "ViewPrivateKey.header": "Privésleutel", "ViewPrivateKey.hint": "<PERSON><PERSON> je pri<PERSON><PERSON><PERSON><PERSON><PERSON> met ni<PERSON><PERSON>. <PERSON><PERSON><PERSON> hem <PERSON> en offline", "ViewPrivateKey.subheader.mobile": "Tik om je privésleutel te onthullen", "ViewPrivateKey.subheader.web": "<PERSON><PERSON> de muis erboven om je privésleutel te onthullen", "ViewPrivateKey.unblur.mobile": "Tik om te onthullen", "ViewPrivateKey.unblur.web": "<PERSON><PERSON> de muis erboven", "ViewSecretPhrase.PasswordChecker.subtitle": "<PERSON>oer je wachtwoord in om het herstelbestand te versleutelen. Je moet dit onthouden voor de toekomst.", "ViewSecretPhrase.done": "<PERSON><PERSON><PERSON>", "ViewSecretPhrase.header": "Geheime zin", "ViewSecretPhrase.hint": "<PERSON><PERSON> je zin met ni<PERSON><PERSON>. <PERSON><PERSON><PERSON> hem <PERSON> en offline", "ViewSecretPhrase.subheader.mobile": "Tik om je geheime zin te onthullen", "ViewSecretPhrase.subheader.web": "<PERSON><PERSON> de muis erboven om je geheime zin te onthullen", "ViewSecretPhrase.unblur.mobile": "Tik om te onthullen", "ViewSecretPhrase.unblur.web": "<PERSON><PERSON> de muis erboven", "account-details.monerium": "Overschrijvingen worden uitgevoerd via Monerium, een geautoriseerde en gereguleerde EMI. <link>Meer informatie</link>", "account-details.unblock": "Overschrijvingen verlopen via Unblock, een geautoriseerde en geregistreerde dienstverlener voor wissel- en bewaardiensten. <link><PERSON><PERSON></link>", "account-selector.empty-state": "<PERSON>n port<PERSON> gevonden", "account-top-up.select-currency.title": "Tokens", "account.accounts_not_found": "We konden geen portemonnees vinden", "account.accounts_not_found_search_valid_address": "<PERSON><PERSON><PERSON> staat niet in je lijst", "account.add.create_new_secret_phrase": "Geheime zinsnede aanmaken", "account.add.create_new_secret_phrase.subtext": "<PERSON><PERSON> nieuwe geheime zinsnede van 12 woorden", "account.add.fromRecoveryKit.fileNotFound": "We konden je bestand niet vinden", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "<PERSON><PERSON><PERSON> opnieuw", "account.add.fromRecoveryKit.fileNotFound.explanation": "Controleer de account met je <PERSON><PERSON>.", "account.add.fromRecoveryKit.fileNotValid": "Herstelbestand is niet geldig", "account.add.fromRecoveryKit.fileNotValid.explanation": "<PERSON>t bestand is ongeldig of is gewijzigd.", "account.add.import_secret_phrase": "Geheime zinsnede importeren", "account.add.import_secret_phrase.subtext": "Aangemaakt op Zeal, Metamask of andere", "account.add.select_type.add_hardware_wallet": "Hardware Wallet", "account.add.select_type.existing_smart_wallet": "Bestaande Smart Wallet", "account.add.select_type.private_key": "Privésleutel", "account.add.select_type.seed_phrase": "<PERSON><PERSON><PERSON><PERSON>", "account.add.select_type.title": "Portemonnee importeren", "account.add.select_type.zeal_recovery_file": "<PERSON><PERSON>d", "account.add.success.title": "Nieuwe wallet aangemaakt 🎉", "account.addLabel.header": "<PERSON><PERSON> je portemonnee een naam", "account.addLabel.labelError.labelAlreadyExist": "<PERSON><PERSON> bestaat al. <PERSON> een andere naam.", "account.addLabel.labelError.maxStringLengthExceeded": "Maximum aantal tekens bereikt", "account.add_active_wallet.primary_text": "<PERSON><PERSON><PERSON>", "account.add_active_wallet.short_text": "<PERSON><PERSON><PERSON><PERSON>, verbind of importeer port<PERSON><PERSON>", "account.add_from_ledger.success": "Wallets toegevoegd aan <PERSON>", "account.add_tracked_wallet.primary_text": "Alleen-lezen port<PERSON>", "account.add_tracked_wallet.short_text": "Bekijk portfolio en activiteit", "account.button.unlabelled-wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> port<PERSON>", "account.create_wallet": "<PERSON>et a<PERSON>", "account.label.edit.title": "<PERSON><PERSON> van <PERSON> bewerken", "account.recoveryKit.selectBackupFile.fileDate": "Aangemaakt {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Herstelbestand is niet geldig", "account.recoveryKit.selectBackupFile.subtitle": "Selecteer het herstelbestand dat je wilt herstellen", "account.recoveryKit.selectBackupFile.title": "Herstelbestand", "account.recoveryKit.success.recoveryFileFound": "Herstelbestand gevonden 🎉", "account.select_type_of_account.create_eoa.short": "<PERSON><PERSON> portemon<PERSON> voor experts", "account.select_type_of_account.create_eoa.title": "<PERSON><PERSON><PERSON><PERSON> een port<PERSON> met <PERSON><PERSON><PERSON>", "account.select_type_of_account.create_safe_wallet.title": "<PERSON><PERSON><PERSON><PERSON> een <PERSON>", "account.select_type_of_account.existing_smart_wallet": "Bestaande Smart wallet", "account.select_type_of_account.hardware_wallet": "Hardware wallet", "account.select_type_of_account.header": "<PERSON><PERSON><PERSON>", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Privésleutel / Seed phrase", "account.select_type_of_account.read_only_wallet": "<PERSON><PERSON><PERSON>le<PERSON> port<PERSON>", "account.select_type_of_account.read_only_wallet.short": "Bekijk elke portfolio", "account.topup.title": "Geld toevo<PERSON>n aan <PERSON>", "account.view.error.refreshAssets": "<PERSON><PERSON><PERSON><PERSON>", "account.widget.refresh": "<PERSON><PERSON><PERSON><PERSON>", "account.widget.settings": "Instellingen", "accounts.view.copied-text": "Gekopieerd {formattedAddress}", "accounts.view.copiedAddress": "Gekopieerd {formattedAddress}", "action.accept": "Accept<PERSON><PERSON>", "action.accpet": "Accept<PERSON><PERSON>", "action.allow": "<PERSON><PERSON><PERSON>", "action.back": "Terug", "action.cancel": "<PERSON><PERSON><PERSON>", "action.card-activation.title": "<PERSON><PERSON>", "action.claim": "Claimen", "action.close": "Sluiten", "action.complete-steps": "<PERSON><PERSON><PERSON><PERSON>", "action.confirm": "Bevestigen", "action.continue": "Doorgaan", "action.copy-address-understand": "Ok - <PERSON><PERSON> k<PERSON>", "action.deposit": "<PERSON><PERSON><PERSON>", "action.done": "<PERSON><PERSON><PERSON>", "action.dontAllow": "<PERSON><PERSON>", "action.edit": "wij<PERSON>en", "action.email-required": "<PERSON><PERSON>r e-mail in", "action.enterPhoneNumber": "Voer telefoonnummer in", "action.expand": "Uitvouwen", "action.fix": "<PERSON><PERSON><PERSON>", "action.getStarted": "<PERSON><PERSON>", "action.got_it": "Begrepen", "action.hide": "Ver<PERSON>", "action.import": "Importeren", "action.import-keys": "Sleutels importeren", "action.importKeys": "Sleutels importeren", "action.minimize": "Minimaliseren", "action.next": "Volgende", "action.ok": "OK", "action.reduceAmount": "Verlagen tot max", "action.refreshWebsite": "Website vernieuwen", "action.remove": "Verwijderen", "action.remove-account": "Account verwij<PERSON>en", "action.requestCode": "Code aanvragen", "action.resend_code": "Code opnieuw verzenden", "action.resend_code_with_time": "Code opnieuw verzenden {time}", "action.retry": "Opnieuw proberen", "action.reveal": "<PERSON><PERSON>", "action.save": "Opsla<PERSON>", "action.save_changes": "RPC opslaan", "action.search": "<PERSON><PERSON>", "action.seeAll": "<PERSON><PERSON> be<PERSON>en", "action.select": "Selecteren", "action.send": "<PERSON><PERSON><PERSON><PERSON>", "action.skip": "Overslaan", "action.submit": "<PERSON><PERSON>", "action.understood": "<PERSON>k begri<PERSON>p het", "action.update": "Updaten", "action.update-gnosis-pay-owner.complete": "<PERSON><PERSON><PERSON><PERSON>", "action.zeroAmount": "<PERSON><PERSON>r bedrag in", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Verzamelobjecten", "action_bar_title.tokens": "Tokens", "action_bar_title.transaction_request": "Transactieverzoek", "activate-monerium.loading": "Je persoonlijke account instellen", "activate-monerium.success.title": "Monerium ingeschakeld", "activate-physical-card-widget.subtitle": "Levering kan 3 weken duren", "activate-physical-card-widget.title": "Fysieke kaart <PERSON>ren", "activate-smart-wallet.title": "<PERSON><PERSON><PERSON>", "active_and_tracked_wallets.title": "Zeal dekt al je kosten op {network}, zodat je gratis transacties kunt uitvoeren!", "activity.approval-amount.revoked": "Ingetrokken", "activity.approval-amount.unlimited": "Onbeperkt", "activity.approval.approved_for": "Goedgekeurd voor", "activity.approval.approved_for_with_target": "Goedgekeurd {approvedTo}", "activity.approval.revoked_for": "Ingetrokken voor", "activity.bank.serviceProvider": "Dienstverlener", "activity.bridge.serviceProvider": "Dienstverlener", "activity.cashback.period": "Cashbackperiode", "activity.filter.card": "<PERSON><PERSON>", "activity.rate": "Wisselkoers", "activity.receive.receivedFrom": "Ontvangen van", "activity.send.sendTo": "Verzonden naar", "activity.smartContract.unknown": "Onbekend contract", "activity.smartContract.usingContract": "Gebruikt contract", "activity.subtitle.pending_timer": "{timerString} In behandeling", "activity.title.arbitrary_smart_contract_interaction": "{function} op {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Onbekende contractinteractie", "activity.title.bridge.from": "Bridge van {token}", "activity.title.bridge.to": "Bridge naar {token}", "activity.title.buy": "Ko<PERSON> {asset}", "activity.title.card_owners_updated": "Kaarthouders bijgewerkt", "activity.title.card_spend_limit_updated": "Bestedingslimiet kaart ingesteld", "activity.title.cashback_deposit": "Storting naar <PERSON>back", "activity.title.cashback_reward": "Cashback beloning", "activity.title.cashback_withdraw": "Op<PERSON> <PERSON>", "activity.title.claimed_reward": "Beloning geclaimd", "activity.title.deployed_smart_wallet_gnosis": "Account a<PERSON><PERSON><PERSON><PERSON>", "activity.title.deposit_from_bank": "Storting vanaf bank", "activity.title.deposit_into_card": "Stort<PERSON> op kaart", "activity.title.deposit_into_earn": "<PERSON><PERSON><PERSON> naar {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} op {smartContract}", "activity.title.from": "<PERSON> {sender}", "activity.title.pendidng_areward_claim": "Beloning claimen", "activity.title.pendidng_breward_claim": "Beloning claimen", "activity.title.recharge_disabledh": "Automatisch opwaarderen kaart uitgeschakeld", "activity.title.recharge_set": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ing<PERSON>", "activity.title.recovered_smart_wallet_gnosis": "Nieuw apparaat geïnstalleerd", "activity.title.send_pending": "Naar {receiver}", "activity.title.send_to_bank": "Naar bank", "activity.title.swap": "<PERSON><PERSON> {token}", "activity.title.to": "Naar {receiver}", "activity.title.withdraw_from_card": "<PERSON><PERSON> van <PERSON>", "activity.title.withdraw_from_earn": "<PERSON><PERSON><PERSON> van {earn}", "activity.transaction.networkFees": "Netwerkkosten", "activity.transaction.state": "Voltooide transactie", "activity.transaction.state.completed": "Voltooide transactie", "activity.transaction.state.failed": "Mislukte transactie", "add-account.section.import.header": "Importeren", "add-another-card-owner": "Nog een kaarthouder toe<PERSON>n", "add-another-card-owner.Recommended.footnote": "Voeg je <PERSON>-wallet toe als extra houder van je Gnosis Pay-kaart", "add-another-card-owner.Recommended.primaryText": "<PERSON><PERSON><PERSON> toe aan <PERSON>", "add-another-card-owner.recommended": "Aanbevolen", "add-owner.confirmation.subtitle": "Uit veiligheidsoverwegingen duurt het verwerken van instellingen 3 minuten. <PERSON> kaart wordt tijdelijk geblokkeerd en betalingen zijn niet mogelijk.", "add-owner.confirmation.title": "Je kaart wordt 3 min. geblokkeerd tijdens de update", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Kan wallet niet toevoegen, is al in gebruik", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "<PERSON><PERSON>r een andere wallet", "add.account.backup.decrypt.success": "<PERSON><PERSON><PERSON>teld", "add.account.backup.password.passwordIncorrectMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> is onjuist", "add.account.backup.password.subtitle": "V<PERSON>r het wachtwoord in dat je hebt gebruikt om je herstelbestand te versleutelen", "add.account.backup.password.title": "<PERSON><PERSON><PERSON> wacht<PERSON> in", "add.account.google.login.subtitle": "<PERSON><PERSON> het verzoek op Google Drive goed om je herstelbestand te synchroniseren", "add.account.google.login.title": "Wachten op goedkeuring...", "add.readonly.already_added": "Wallet al toegevoegd", "add.readonly.continue": "Doorgaan", "add.readonly.empty": "<PERSON><PERSON><PERSON> een adres of ENS in", "addBankRecipient.title": "Bankontvanger toe<PERSON>n", "add_funds.deposit_from_bank_account": "<PERSON><PERSON><PERSON> vanaf bankrekening", "add_funds.from_another_wallet": "Van een andere wallet", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "<PERSON><PERSON><PERSON><PERSON> met top-up dApp", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Verbind een wallet met de Zeal top-up dApp en stuur snel geld.", "add_funds.from_crypto_wallet.header": "Van een andere wallet", "add_funds.from_crypto_wallet.header.show_wallet_address": "Toon je wallet<PERSON><PERSON>", "add_funds.from_exchange.header": "Versturen vanaf een exchange", "add_funds.from_exchange.header.copy_wallet_address": "<PERSON><PERSON><PERSON> je <PERSON>-adres", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance, etc.", "add_funds.from_exchange.header.open_exchange": "Open de exchange-app of -website", "add_funds.from_exchange.header.selected_token": "Vers<PERSON>ur {token} naar <PERSON>", "add_funds.from_exchange.header.selected_token.subtitle": "Op {network}", "add_funds.from_exchange.header.send_selected_token": "Verstuur ondersteunde token", "add_funds.from_exchange.header.send_selected_token.subtitle": "Selecteer ondersteunde token & netwerk", "add_funds.import_wallet": "Bestaande crypto wallet importeren", "add_funds.title": "Geld toe<PERSON>n", "add_funds.transfer_from_exchange": "Overschrijving vanaf een exchange", "address.add.header": "Bekijk je wallet in Zeal{br}in alleen-lezen modus", "address.add.subheader": "<PERSON><PERSON><PERSON> je adres of ENS in om je activa op alle EVM-netwerken op één plek te zien. Maak of importeer later meer wallets.", "address_book.change_account.bank_transfers.header": "Bankontvangers", "address_book.change_account.bank_transfers.primary": "Bankontvanger", "address_book.change_account.cta": "Wallet volgen", "address_book.change_account.search_placeholder": "<PERSON><PERSON> of zoeken", "address_book.change_account.tracked_header": "Alleen-lezen port<PERSON>", "address_book.change_account.wallets_header": "<PERSON><PERSON><PERSON>", "app-association-check-failed.modal.cta": "<PERSON><PERSON><PERSON> opnieuw", "app-association-check-failed.modal.subtitle": "Probeer het opnieuw. Verbindingsproblemen veroorzaken vertragingen bij het ophalen van je Passkeys. Als het pro<PERSON><PERSON> a<PERSON>, herstart <PERSON> en probeer het nog een keer.", "app-association-check-failed.modal.subtitle.creation": "Probeer het opnieuw. Verbindingsproblemen veroorzaken vertragingen bij het aanmaken van je Passkey. Als het probleem aan<PERSON>, herstart <PERSON> en probeer het nog een keer.", "app-association-check-failed.modal.title.creation": "Je apparaat kon geen passkey aanmaken", "app-association-check-failed.modal.title.signing": "Je apparaat kon de passkeys niet laden", "app.app_protocol_group.borrowed_tokens": "Geleende tokens", "app.app_protocol_group.claimable_amount": "Opneembaar bedrag", "app.app_protocol_group.health_rate": "Gezondheidsratio", "app.app_protocol_group.lending": "<PERSON><PERSON>", "app.app_protocol_group.locked_tokens": "Vastgezette tokens", "app.app_protocol_group.nfts": "Verzamelobjecten", "app.app_protocol_group.reward_tokens": "Beloningstokens", "app.app_protocol_group.supplied_tokens": "Ingelegde tokens", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Vesting token", "app.appsGroupHeader.discoverMore": "<PERSON><PERSON><PERSON> meer", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "<PERSON><PERSON> of voer URL in", "app.error-banner.cory": "Foutgegevens kopiëren", "app.error-banner.retry": "Opnieuw proberen", "app.list_item.rewards": "Beloningen {value}", "app.position_details.health_rate.description": "De health rate wordt berekend door de omvang van je lening te delen door de waarde van je onderpand.", "app.position_details.health_rate.title": "Wat is een health rate?", "approval.edit-limit.label": "Bestedingslimiet bewerken", "approval.permit_info": "Informatie over vergunning", "approval.spend-limit.edit-modal.cancel": "<PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.limit-label": "Bestedingslimiet", "approval.spend-limit.edit-modal.max-limit-error": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hoge limiet", "approval.spend-limit.edit-modal.revert": "Wijzigingen ongedaan maken", "approval.spend-limit.edit-modal.set-to-unlimited": "Instellen op Onbeperkt", "approval.spend-limit.edit-modal.submit": "Wijzigingen opslaan", "approval.spend-limit.edit-modal.title": "Toestemmingen bewerken", "approval.spend_limit_info": "Wat is een bestedingslimiet?", "approval.what_are_approvals": "Wat zijn goed<PERSON>uringen?", "apps_list.page.emptyState": "<PERSON>n actieve apps", "backpace.removeLastDigit": "Laatste cijfer verwijderen", "backup-banner.backup_now": "Back-up maken", "backup-banner.risk_losing_funds": "Maak nu een back-up of riskeer verl<PERSON> van je geld", "backup-banner.title": "Wallet heeft geen back-up", "backupRecoverySmartWallet.noExportPrivateKeys": "Automatische back-up: <PERSON> <PERSON> wordt opgeslagen als passkey. Geen seed phrase of priv<PERSON><PERSON><PERSON><PERSON> nodig.", "backupRecoverySmartWallet.safeContracts": "Beveiliging met meerdere sleutels: Zeal wallets draaien op Safe-contracten, dus meerdere apparaten kunnen een transactie goedkeuren. Geen single point of failure.", "backupRecoverySmartWallet.security": "Meerdere apparaten: Je kunt je wallet op meerdere apparaten g<PERSON><PERSON><PERSON><PERSON> met de passkey. Elk apparaat krijgt zijn eigen privésleutel.", "backupRecoverySmartWallet.showLocalPrivateKey": "Expertmodus: Je kunt de privésleutel van dit apparaat exporteren, in een andere wallet gebruiken en verbinding maken op <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Toon privésleutel</Key>", "backupRecoverySmartWallet.storingKeys": "Gesynchroniseerd met de cloud: De passkey wordt veilig opgeslagen in iCloud, Google Wachtwoordmanager of je wachtwoordmanager.", "backupRecoverySmartWallet.title": "Smart Wallet back-up & herstel", "balance-change.card.titile": "<PERSON><PERSON>", "balanceChange.pending": "In behandeling", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Selecteer <PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfer.change-deposit-receiver.subtitle": "Deze portemonnee ontvangt alle bankstortingen", "bank-transfer.change-deposit-receiver.title": "Stel ontvangende portemonnee in", "bank-transfer.change-owner.subtitle": "Deze portemonnee gebruik je om in te loggen en je account voor bankoverschrijvingen te herstellen.", "bank-transfer.change-owner.title": "Stel accounteigenaar in", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Alle bankstortingen die je naar <PERSON> stuurt, worden in deze portemonnee ontvangen.", "bank-transfer.configrm-change-deposit-receiver.title": "Wijzig ontvangende portemonnee", "bank-transfer.configrm-change-owner.subtitle": "Weet je zeker dat je de accounteigenaar wilt wijzigen? Deze portemonnee wordt gebruikt om in te loggen en je account voor bankoverschrijvingen te herstellen.", "bank-transfer.configrm-change-owner.title": "Wijzig accounteigenaar", "bank-transfer.deposit.widget.status.complete": "Voltooid", "bank-transfer.deposit.widget.status.funds_received": "Geld ontvangen", "bank-transfer.deposit.widget.status.sending_to_wallet": "Verzenden naar portemonnee", "bank-transfer.deposit.widget.status.transfer-on-hold": "Overschrijving in wacht", "bank-transfer.deposit.widget.status.transfer-received": "Verzenden naar portemonnee", "bank-transfer.deposit.widget.subtitle": "{from} naar {to}", "bank-transfer.deposit.widget.title": "<PERSON><PERSON><PERSON>", "bank-transfer.intro.bulletlist.point_1": "Instellen met <PERSON>", "bank-transfer.intro.bulletlist.point_2": "Maak over tussen EUR/GBP en meer dan 10 tokens", "bank-transfer.intro.bulletlist.point_3": "0% kosten tot $ 5k per maand, daarna 0,2%", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Verzenden naar bank", "bank-transfer.withdrawal.widget.status.in-progress": "Overschrijving bezig", "bank-transfer.withdrawal.widget.status.on-hold": "Overschrijving in wacht", "bank-transfer.withdrawal.widget.status.success": "Voltooid", "bank-transfer.withdrawal.widget.subtitle": "{from} naar {to}", "bank-transfer.withdrawal.widget.title": "Opname", "bank-transfers.bank-account-actions.remove-this-account": "Verwijder dit account", "bank-transfers.bank-account-actions.switch-to-this-account": "<PERSON><PERSON><PERSON> over naar dit account", "bank-transfers.deposit.fees-for-less-than-5k": "Kosten voor $ 5k of minder", "bank-transfers.deposit.fees-for-more-than-5k": "Kosten voor meer dan $ 5k", "bank-transfers.set-receiving-bank.title": "Stel ontvangende bank in", "bank-transfers.settings.account_owner": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfers.settings.receiver_of_bank_deposits": "Ontvanger van bankstortingen", "bank-transfers.settings.receiver_of_withdrawals": "Ontvan<PERSON> van opnames", "bank-transfers.settings.registered_email": "Geregistreerde e-mail", "bank-transfers.settings.title": "Instellingen bankoverschrijving", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} Rekening", "bank-transfers.setup.bank-account": "Bankrekening", "bankTransfer.withdraw.max_loading": "Max: {amount}", "bank_details_do_not_match.got_it": "Begrepen", "bank_details_do_not_match.subtitle": "De sorteercode en het rekeningnummer komen niet overeen. Controleer of de gegevens correct zijn ingevoerd en probeer het opnieuw.", "bank_details_do_not_match.title": "Bankgegevens komen niet overeen", "bank_tranfsers.select_country_of_residence.country_not_supported": "Sorry, bankoverschrijvingen worden niet ondersteund in {country} nog", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Open je bank-app", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Maak {fiatCurrencyCode} over naar je rekening", "bank_transfer.deposit.header": "{fullName}''s perso<PERSON><PERSON><PERSON><PERSON> rekening&nbsp;gegevens", "bank_transfer.kyc_status_widget.subtitle": "Bankoverschrijvingen", "bank_transfer.kyc_status_widget.title": "Identiteit verifiëren", "bank_transfer.personal_details.date_of_birth": "Geboortedatum", "bank_transfer.personal_details.date_of_birth.invalid_format": "Datum is ongeldig", "bank_transfer.personal_details.date_of_birth.too_young": "Je moet minstens 18 jaar oud zijn", "bank_transfer.personal_details.first_name": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.personal_details.last_name": "Achternaam", "bank_transfer.personal_details.title": "<PERSON><PERSON><PERSON>", "bank_transfer.reference.label": "Me<PERSON>eling (optioneel)", "bank_transfer.reference_message": "Verzonden via Zeal", "bank_transfer.residence_details.address": "<PERSON><PERSON><PERSON>", "bank_transfer.residence_details.city": "Stad", "bank_transfer.residence_details.country_of_residence": "<PERSON> van verblijf", "bank_transfer.residence_details.country_placeholder": "Land", "bank_transfer.residence_details.postcode": "Postcode", "bank_transfer.residence_details.street": "Straat", "bank_transfer.residence_details.your_residence": "<PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.continue": "Doorgaan", "bank_transfers.choose-wallet.test": "<PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.warning.subtitle": "Je kunt slechts één portemonnee tegelijk koppelen. Je kunt de gekoppelde portemonnee niet wijzigen.", "bank_transfers.choose-wallet.warning.title": "<PERSON><PERSON> je port<PERSON> z<PERSON>", "bank_transfers.choose_wallet.subtitle": "<PERSON><PERSON> port<PERSON> voor bankoverschrijvingen. ", "bank_transfers.choose_wallet.title": "<PERSON><PERSON>", "bank_transfers.continue": "Doorgaan", "bank_transfers.currency_is_currently_not_supported": "Doorgaan", "bank_transfers.deposit-header": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.account-name": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.account-number-copied": "Rekeningnummer gekopieerd", "bank_transfers.deposit.amount-input": "Bedrag om te storten", "bank_transfers.deposit.amount-output": "Bestemmingsbedrag", "bank_transfers.deposit.amount-output.error": "fout", "bank_transfers.deposit.buttet-point.receive-crypto": "Ontvang {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "Doorgaan", "bank_transfers.deposit.currency-not-supported.subtitle": "Bankstortingen in {code} zijn tot nader order uitgeschakeld.", "bank_transfers.deposit.currency-not-supported.title": "{code} stortingen momenteel niet ondersteund", "bank_transfers.deposit.default-token.balance": "Saldo {amount}", "bank_transfers.deposit.deposit-header": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.enter_amount": "<PERSON><PERSON>r bedrag in", "bank_transfers.deposit.iban-copied": "IBAN gekopieerd", "bank_transfers.deposit.increase-amount": "Minimale overschrijving is {limit}", "bank_transfers.deposit.loading": "Laden", "bank_transfers.deposit.max-limit-reached": "Bed<PERSON> overschrijdt de overschrijvingslimiet", "bank_transfers.deposit.modal.kyc.button-text": "Starten", "bank_transfers.deposit.modal.kyc.text": "Om je identiteit te verifiëren, hebben we wat persoonlijke gegevens en documenten nodig. Indienen duurt meestal slechts enkele minuten.", "bank_transfers.deposit.modal.kyc.title": "Verifieer je identiteit om limieten te verhogen", "bank_transfers.deposit.reduce_amount": "Verlaag bedrag", "bank_transfers.deposit.show-account.account-number": "Rekeningnummer", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Sorteercode", "bank_transfers.deposit.sort-code-copied": "Sorteercode gekopieerd", "bank_transfers.deposit.withdraw-header": "Opnemen", "bank_transfers.failed_to_load_fee": "Onbekend", "bank_transfers.fees": "<PERSON><PERSON>", "bank_transfers.increase-amount": "Minimale overschrijving is {limit}", "bank_transfers.insufficient-funds": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "bank_transfers.select_country_of_residence.title": "Waar woon je?", "bank_transfers.setup.cta": "Overschrijving instellen", "bank_transfers.setup.enter-amount": "<PERSON><PERSON>r bedrag in", "bank_transfers.source_of_funds.form.business_income": "Bedrijfsinkomsten", "bank_transfers.source_of_funds.form.other": "<PERSON><PERSON>", "bank_transfers.source_of_funds.form.pension": "<PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.salary": "<PERSON><PERSON>", "bank_transfers.source_of_funds.form.title": "<PERSON><PERSON><PERSON><PERSON> van je geld", "bank_transfers.source_of_funds_description.placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON> van je geld...", "bank_transfers.source_of_funds_description.title": "<PERSON><PERSON><PERSON> ons meer over de herko<PERSON>t van je geld", "bank_transfers.withdraw-header": "Opnemen", "bank_transfers.withdraw.amount-input": "Op te nemen bedrag", "bank_transfers.withdraw.max-limit-reached": "Bed<PERSON> overschrijdt de overschrijvingslimiet", "bank_transfers.withdrawal.verify-id": "Verlaag bedrag", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Maximale invoerlimiet overschreden", "banner.above_maximum_limit.maximum_limit_per_deposit": "Dit is de maximale limiet per storting", "banner.above_maximum_limit.subtitle": "Maximale invoerlimiet overschreden", "banner.above_maximum_limit.title": "V<PERSON><PERSON><PERSON> het bedrag naar {amount} of minder", "banner.above_maximum_limit.title.default": "Verlaag het bedrag", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Minimale invoerlimiet niet bereikt", "banner.below_minimum_limit.minimum_limit_for_token": "<PERSON><PERSON> is de minimale limiet voor deze token", "banner.below_minimum_limit.title": "Verhoog bedrag naar {amount} of meer", "banner.below_minimum_limit.title.default": "Verhoog het bedrag", "breaard.in_porgress.info_popup.cta": "Besteed en verdien {earn}", "breaard.in_porgress.info_popup.footnote": "Door Zeal en de Gnosis Pay-kaart te geb<PERSON>, ga je ak<PERSON><PERSON> met de voorwaarden van deze beloningscampagne.", "breaward.in_porgress.info_popup.bullet_point_1": "Besteed {remaining} binnen {time} om deze beloning te claimen.", "breaward.in_porgress.info_popup.bullet_point_2": "Alleen geldige Gnosis Pay-aankopen tellen mee voor je bestedingsbedrag.", "breaward.in_porgress.info_popup.bullet_point_3": "Nadat je je beloning hebt geclaimd, wordt deze naar je Zeal-account verzonden.", "breaward.in_porgress.info_popup.header": "<PERSON><PERSON> {earn}, met een uitga<PERSON> van {remaining}", "breward.celebration.for_spending": "<PERSON><PERSON><PERSON><PERSON> je met je kaart hebt betaald", "breward.dc25-eligible-celebration.for_spending": "Je bent een van de e<PERSON> {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "Je was niet bij de eerste {limit} die hebben besteed", "breward.expired_banner.earn_by_spending": "<PERSON><PERSON> {earn} met een besteding van {amount}", "breward.expired_banner.reward_expired": "{earn} beloning verlopen", "breward.in_progress_banner.cta.title": "Besteed en verdien {earn}", "breward.ready_to_claim.error.try_again": "Opnieuw proberen", "breward.ready_to_claim.error_title": "<PERSON><PERSON><PERSON> van beloning mislukt", "breward.ready_to_claim.in_progress": "Beloning claimen...", "breward.ready_to_claim.youve_earned": "Je hebt verdiend {earn}!", "breward_already_claimed.title": "Beloning is al geclaimd. Neem contact op met support als je de beloningstoken niet hebt ontvangen.", "breward_cannotbe_claimed.title": "Beloning kan nu niet worden geclaimd. Probeer het later opnieuw.", "bridge.best_return": "Route met beste rendement", "bridge.best_serivce_time": "Route met snelste verwerkingstijd", "bridge.check_status.complete": "Voltooid", "bridge.check_status.progress_text": "<PERSON>zig met bridgen {from} naar {to}", "bridge.remove_topup": "Opwaardering verwijderen", "bridge.request_status.completed": "Voltooid", "bridge.request_status.pending": "In behandeling", "bridge.widget.completed": "Voltooid", "bridge.widget.currencies": "{from} naar {to}", "bridge_rote.widget.title": "Bridge", "browse.discover_more_apps": "Ontdek meer apps", "browse.google_search_term": "<PERSON><PERSON> \"{searchTerm}\"", "brward.celebration.you_earned": "Je hebt verdiend", "brward.expired_banner.subtitle": "Volgende keer beter", "brward.in_progress_banner.subtitle": "<PERSON><PERSON><PERSON><PERSON> over {expiredInFormatted}", "buy": "<PERSON><PERSON>", "buy.enter_amount": "<PERSON><PERSON>r bedrag in", "buy.loading": "Laden...", "buy.no_routes_found": "Geen routes gevonden", "buy.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "buy.select-currency.title": "Selecteer token", "buy.select-to-currency.title": "Tokens kopen", "buy_form.title": "Token kopen", "cancelled-card.create-card-button.primary": "<PERSON><PERSON><PERSON> kaart a<PERSON>n", "cancelled-card.switch-card-button.primary": "<PERSON><PERSON><PERSON>", "cancelled-card.switch-card-button.short-text": "Je hebt een andere actieve kaart", "card": "<PERSON><PERSON>", "card-add-cash.confirm-stage.banner.no-routes-found": "Geen routes, probeer een ander token of bedrag", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Je hebt {amount} meer {symbol} nodig om de kosten te betalen", "card-add-cash.confirm-stage.banner.value-loss": "Je verliest {loss} aan waarde", "card-add-cash.confirm-stage.banner.value-loss.revert": "Ongedaan maken", "card-add-cash.edit-stage.cta.cancel": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.continue": "Doorgaan", "card-add-cash.edit-stage.cta.enter-amount": "<PERSON><PERSON><PERSON> in", "card-add-cash.edit-stage.cta.reduce-to-max": "Naar max", "card-add-cash.edit-staget.banner.no-routes-found": "Geen routes, probeer een ander token of bedrag", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Ga verder op je hardware wallet.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Teken op hardware wallet", "card-balance": "Saldo: {balance}", "card-cashback.status.title": "Storten op cashback", "card-copy-safe-address.copy_address": "<PERSON><PERSON>", "card-copy-safe-address.copy_address.done": "Gekopieerd", "card-copy-safe-address.warning.description": "Dit adres kan alleen {cardAsset} op Gnosis Chain ontvangen. Stuur geen activa op andere netwerken naar dit adres. Deze gaan verloren.", "card-copy-safe-address.warning.header": "<PERSON><PERSON><PERSON><PERSON> alleen {cardAsset} op Gnosis Chain", "card-marketing-card.center.subtitle": "Wisselkosten", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "Rente", "card-marketing-card.right.subtitle": "Aanmeldcadeau", "card-marketing-card.title": "Europa's VI<PERSON><PERSON><PERSON><PERSON> met hoge rente", "card-marketing-tile.get-started": "Begin nu", "card-select-from-token-title": "Selecteer '<PERSON>'-token", "card-top-up.banner.subtitle.completed": "Voltooid", "card-top-up.banner.subtitle.failed": "Mislukt", "card-top-up.banner.subtitle.pending": "{timerString} In behandeling", "card-top-up.banner.title": "<PERSON><PERSON><PERSON> {amount}", "card-topup.select-token.emptyState": "We hebben geen tokens gevonden", "card.activate.card_number_not_valid": "Ongeldig kaartnummer. Probeer het opnieuw.", "card.activate.invalid_card_number": "Ongeldig kaartnummer.", "card.activation.activate_physical_card": "Fysieke kaart <PERSON>ren", "card.add-cash.amount-to-withdraw": "Opwaardeerbedrag", "card.add-from-earn-form.title": "<PERSON><PERSON> toe<PERSON>n aan kaart", "card.add-from-earn-form.withdraw-to-card": "Doorgaan", "card.add-from-earn.amount-to-withdraw": "Bedrag om op te nemen naar kaart", "card.add-from-earn.enter-amount": "<PERSON><PERSON>r bedrag in", "card.add-from-earn.loading": "Laden", "card.add-from-earn.max-label": "Saldo: {amount}", "card.add-from-earn.no-routes-found": "Geen routes gevonden", "card.add-from-earn.not-enough-balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "card.add-owner.queued": "Eigenaar toevo<PERSON>n in wachtrij", "card.add-to-wallet-flow.subtitle": "Betaal rechtstreeks uit je portemonnee", "card.add-to-wallet.copy-card-number": "<PERSON><PERSON><PERSON> kaartnummer hieronder", "card.add-to-wallet.title": "Voeg toe aan {platformName} Wallet", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "GEANNULEERD", "card.card-owner-not-found.disconnect-btn": "<PERSON><PERSON> ka<PERSON>", "card.card-owner-not-found.subtitle": "Update eigenaar om kaart te herverbinden.", "card.card-owner-not-found.title": "<PERSON><PERSON> op<PERSON>w verbinden", "card.card-owner-not-found.update-owner-btn": "Ka<PERSON>igenaar updaten", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} in {date}", "card.cashback.widgetNoCashback.subtitle": "Stort om te beginnen met verdienen", "card.cashback.widgetNoCashback.title": "Krijg tot {defaultPercentage} cashback", "card.cashback.widgetcashbackValue.rewards": "{amount} in behandeling", "card.cashback.widgetcashbackValue.title": "{percentage} cashback", "card.choose-wallet.connect_card": "<PERSON><PERSON> k<PERSON>", "card.choose-wallet.create-new": "Nieuwe wallet als houder toevoegen", "card.choose-wallet.import-another-wallet": "Importeer een andere portemon<PERSON>", "card.choose-wallet.import-current-owner": "<PERSON><PERSON><PERSON> ka<PERSON>houder importeren", "card.choose-wallet.import-current-owner.sub-text": "Importeer priv<PERSON><PERSON><PERSON>ls of seed phrase van de houder van je Gnosis <PERSON>-kaart", "card.choose-wallet.title": "Selecteer wallet om je kaart te beheren", "card.connectWalletToCardGuide": "<PERSON><PERSON><PERSON>", "card.connectWalletToCardGuide.addGnosisPayOwner": "Gnosis Pay-eigenaar toevo<PERSON>n", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Open Gnosispay.com met je andere wallet{br}2. <PERSON><PERSON> op 'Account''{br}3. <PERSON><PERSON> op 'Accountgegevens''{br}4. <PERSON><PERSON> op 'Bewerken', naast 'Accounteigenaar', en{br}5. <PERSON><PERSON> op '<PERSON><PERSON> toevoegen''{br}6. <PERSON><PERSON> je Zeal-adres en klik op opslaan", "card.connectWalletToCardGuide.header": "<PERSON><PERSON> {account} aan <PERSON> Pay Card", "card.connect_card.start": "<PERSON><PERSON><PERSON> kaart koppelen", "card.copiedAddress": "Gekopieerd {formattedAddress}", "card.disconnect-account.title": "Account <PERSON>", "card.hw-wallet-support-drop.add-owner-btn": "Nieuwe eigenaar toe<PERSON>n", "card.hw-wallet-support-drop.disconnect-btn": "<PERSON><PERSON> ka<PERSON>", "card.hw-wallet-support-drop.subtitle": "Voeg een niet-hardware-wallet eigenaar toe.", "card.hw-wallet-support-drop.title": "Zeal: geen hardware wallets meer voor Kaart", "card.kyc.continue": "Installatie voortzetten", "card.list_item.title": "<PERSON><PERSON>", "card.onboarded.transactions.empty.description": "Je betalingsactiviteit verschijnt hier", "card.onboarded.transactions.empty.title": "Activiteit", "card.order.continue": "Bestelling voortzetten", "card.order.free_virtual_card": "<PERSON><PERSON><PERSON> kaart aan", "card.order.start": "<PERSON><PERSON><PERSON> ka<PERSON> bestellen", "card.owner-not-imported.cancel": "<PERSON><PERSON><PERSON>", "card.owner-not-imported.import": "<PERSON><PERSON><PERSON><PERSON>", "card.owner-not-imported.subtitle": "Om deze transactie te autoriseren, kop<PERSON> je de eigenaarsportemonnee van je Gnosis Pay-account aan <PERSON>. Let op: dit staat los van je gebruikelijke Gnosis Pay wallet-login.", "card.owner-not-imported.title": "Eigenaar Gnosis Pay-account toevoegen", "card.page.order_free_physical_card": "<PERSON>ys<PERSON>e kaart bestellen", "card.pin.change_pin_at_atm": "De pincode kan bij geselecteerde geldautomaten worden gewijzigd", "card.pin.timeout": "Scherm sluit over {seconds} sec.", "card.quick-actions.add-assets": "<PERSON><PERSON><PERSON>", "card.quick-actions.add-cash": "<PERSON><PERSON><PERSON>", "card.quick-actions.details": "Details", "card.quick-actions.freeze": "Blokkeren", "card.quick-actions.freezing": "Blokkeren...", "card.quick-actions.unfreeze": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.unfreezing": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "card.quick-actions.withdraw": "Opnemen", "card.read-only-detected.create-new": "Nieuwe wallet als houder toevoegen", "card.read-only-detected.import-current-owner": "Sleutels importeren voor {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Importeer priv<PERSON><PERSON><PERSON>ls of seed phrase van wallet {address}", "card.read-only-detected.title": "<PERSON><PERSON> gedetecteerd op alleen-lezen wallet. Selecteer wallet om kaart te beheren", "card.remove-owner.queued": "Verwijderen van houder in wachtrij", "card.settings.disconnect-from-zeal": "<PERSON><PERSON><PERSON><PERSON>", "card.settings.edit-owners": "Kaarteigenaren wijzigen", "card.settings.getCard": "Nog een kaart a<PERSON>vragen", "card.settings.getCard.subtitle": "Virtuele of fysieke kaarten", "card.settings.notRecharging": "Niet automatisch opwaarderen", "card.settings.notifications.subtitle": "Ontvang betalingsmeldingen", "card.settings.notifications.title": "Kaartmeldingen", "card.settings.page.title": "Kaartinstellingen", "card.settings.select-card.cancelled-cards": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>n", "card.settings.setAutoRecharge": "Automatisch opwaarderen instellen", "card.settings.show-card-address": "<PERSON><PERSON> ka<PERSON>", "card.settings.spend-limit": "Bestedingslimiet instellen", "card.settings.spend-limit-title": "Huidige daglimiet: {limit}", "card.settings.switch-active-card": "<PERSON><PERSON><PERSON> van actieve kaart", "card.settings.switch-active-card-description": "<PERSON><PERSON><PERSON> kaart: {card}", "card.settings.switch-card.card-item.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.frozen": "<PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.title": "Gnosis Pay-kaart", "card.settings.switch-card.card-item.title.physical": "<PERSON>ys<PERSON><PERSON> kaart", "card.settings.switch-card.card-item.title.virtual": "<PERSON>irt<PERSON><PERSON>", "card.settings.switch-card.title": "Selecteer kaart", "card.settings.targetBalance": "<PERSON><PERSON>aldo: {threshold}", "card.settings.view-pin": "PIN bekijken", "card.settings.view-pin-description": "Bescherm je pincode altijd", "card.title": "<PERSON><PERSON>", "card.transactions.header": "Kaarttransacties", "card.transactions.see_all": "Bekijk alle transacties", "card.virtual": "VIRTUEEL", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Cashback wordt naar je kaart gestuurd aan het begin van de week nadat je het hebt verdiend.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "Hoe meer je stort, hoe meer je verdient bij elke aankoop.", "cardCashback.onboarding.title": "<PERSON><PERSON><PERSON><PERSON> tot {percentage} cashback", "cardCashbackWithdraw.amount": "Opnamebedrag", "cardCashbackWithdraw.header": "Opnemen {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "<PERSON><PERSON> is geblok<PERSON>erd en kon niet worden geactiveerd", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "Tot {percentage}", "cardWidget.startEarning": "<PERSON><PERSON> met ve<PERSON><PERSON>n", "cardWithdraw.amount": "Op te nemen bedrag", "cardWithdraw.header": "<PERSON><PERSON> op<PERSON><PERSON> van <PERSON>", "cardWithdraw.selectWithdrawWallet.title": "<PERSON>es wallet om naar{br}op te nemen", "cardWithdraw.success.cta": "Sluiten", "cardWithdraw.success.subtitle": "Uit veiligheidsoverwegingen duurt het 3 minuten om opnames van de Gnosis Pay card te verwerken", "cardWithdraw.success.title": "<PERSON><PERSON> wij<PERSON>ing duurt 3 minuten", "card_top_up_trx.send": "Verzenden", "card_top_up_trx.to": "<PERSON>ar", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "Vervaldatum", "cards.card_number": "Kaartnummer", "cards.choose-wallet.no-active-accounts": "Je hebt geen actieve portemonnees", "cards.copied_card_number": "Kaartnummer gekopieerd", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Overschrijdt daglimiet", "cards.transactions.decline_reason.incorrect_pin": "Onjuiste pincode", "cards.transactions.decline_reason.incorrect_security_code": "Onju<PERSON>e beveiligingscode", "cards.transactions.decline_reason.invalid_amount": "Ongeldig bedrag", "cards.transactions.decline_reason.low_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "cards.transactions.decline_reason.other": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cards.transactions.decline_reason.pin_tries_exceeded": "Pincodepogingen overschreden", "cards.transactions.status.refund": "Terugbetaling", "cards.transactions.status.reversal": "Terugboeking", "cashback-deposit.trx.title": "<PERSON><PERSON><PERSON> naar <PERSON>", "cashback-estimate.text": "Dit is een schatting en GEEN gegarandeerde uitbetaling. Alle publiek bekende cashback-regels worden toegepast, maar Gnosis Pay kan naar eigen goeddunken transacties uitsluiten. Een maximale uitgave van {amount} per week komt in aanmerking voor cashback, zelfs als de schatting voor deze transactie een hoger totaalbedrag zou aangeven.", "cashback-estimate.text.fallback": "<PERSON><PERSON> is een schatting en GEEN gegarandeerde uitbetalingen. Alle publiek bekende cashback-regels worden toegepast, maar Gnosis Pay kan transacties naar eigen inzicht uitsluiten.", "cashback-estimate.title": "Cashbackschatting", "cashback-onbarding-tersm.subtitle": "Je kaarttransactiegegevens worden gede<PERSON> met <PERSON><PERSON><PERSON><PERSON>, die verantwoordelijk is voor het uitkeren van cashback-beloningen. Door op accepteren te klikken, ga je ak<PERSON><PERSON> met de Gnosis DAO Cashback <terms>Algemene voorwaarden</terms>", "cashback-onbarding-tersm.title": "Gebruiksvoorwaarden en privacy", "cashback-tx-activity.retry": "Opnieuw proberen", "cashback-unconfirmed-payments-info.subtitle": "Betalingen komen in aanmerking voor cashback wanneer ze zijn verrekend met de verkoper. Tot die tijd worden ze weergegeven als onbevestigde betalingen. Niet-verrekende betalingen komen niet in aanmerking voor cashback.", "cashback-unconfirmed-payments-info.title": "Onbevestigde kaartbetalingen", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "Storting", "cashback.activity.title": "Recente activiteit", "cashback.activity.withdrawal": "Opname", "cashback.deposit": "<PERSON><PERSON><PERSON>", "cashback.deposit.amount.label": "Stortingsbedrag", "cashback.deposit.change": "{from} naar {to}", "cashback.deposit.confirmation.subtitle": "Cashback-ta<PERSON>n worden wekelijks bijgewerkt. Stort nu om de cashback van volgende week te verhogen.", "cashback.deposit.confirmation.title": "<PERSON> <PERSON><PERSON> met ve<PERSON><PERSON><PERSON> van {percentage} op {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Wissel tokens in voor {currency} op {network} Chain", "cashback.deposit.get.tokens.title": "Krijg {currency} tokens", "cashback.deposit.header": "Stort {currency}", "cashback.deposit.max_label": "Max: {amount}", "cashback.deposit.select-wallet.title": "<PERSON><PERSON> port<PERSON> om vanaf te storten", "cashback.deposit.yourcashback": "<PERSON><PERSON><PERSON> cashback", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "<PERSON><PERSON> port<PERSON> om{br}naar op te nemen", "cashback.transaction-details.network-label": "Netwerk", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "<PERSON>", "cashback.transaction-details.top-row.label-rewards": "Cashbackperiode", "cashback.transaction-details.top-row.label-withdrawal": "<PERSON><PERSON>", "cashback.transaction-details.transaction": "Transactie-ID", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Cashback-transacties", "cashback.withdraw": "Opnemen", "cashback.withdraw.confirmation.cashback_reduction": "De cashback voor deze week, inclusief wat je al hebt verdiend, wordt verlaagd van {before} naar {after}", "cashback.withdraw.queued": "Opname in wachtrij", "cashback.withdrawal.change": "{from} naar {to}", "cashback.withdrawal.confirmation.subtitle": "Start opname van {amount} met een vertraging van 3 minuten. Dit verlaagt je cashback naar {after}.", "cashback.withdrawal.confirmation.title": "Cashback daalt als je GNO opneemt", "cashback.withdrawal.delayTransaction.title": "Start GNO-opname met{br} een vertraging van 3 minuten", "cashback.withdrawal.withdraw": "Opnemen", "cashback.withdrawal.yourcashback": "<PERSON><PERSON><PERSON> cashback", "celebration.aave": "<PERSON><PERSON> met <PERSON><PERSON>", "celebration.cashback.subtitle": "Uitgekeerd in {code}", "celebration.cashback.subtitleGNO": "{amount} laatst verdiend", "celebration.chf": "<PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON>", "celebration.lido": "<PERSON><PERSON> met <PERSON><PERSON>", "celebration.sky": "<PERSON><PERSON> met Sky", "celebration.title": "Totaal cashback", "celebration.well_done.title": "Goed gedaan!", "change-withdrawal-account.add-new-account": "Voeg nog een bankrekening toe", "change-withdrawal-account.item.shortText": "{currency} rekening", "check-confirmation.approve.footer.for": "Voor", "checkConfirmation.title": "Transactieresultaat", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin & Ether", "collateral.ethereum": "Ethereum", "collateral.other": "<PERSON><PERSON>", "collateral.rwa": "Reële Activa", "collateral.stablecoins": "Stablecoins (USD-gekoppeld)", "collateral.us-t-bills": "Amerikaanse T-Bills", "confirm-bank-transfer-recipient.bullet-1": "Geen kosten op digitale EUR", "confirm-bank-transfer-recipient.bullet-2": "Stortingen naar {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Deel Gnosis Pay-accountgegevens met Monerium, een geautoriseerde en gereguleerde EMI. <link>Meer informatie</link>", "confirm-bank-transfer-recipient.bullet-4": "Accepteer de Monerium <link>servicevoorwaarden</link>", "confirm-bank-transfer-recipient.title": "Voorwaarden accepteren", "confirm-change-withdrawal-account.cancel": "<PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.confirm": "Bevestigen", "confirm-change-withdrawal-account.saving": "Opsla<PERSON>", "confirm-change-withdrawal-account.subtitle": "Alle opnames van Zeal worden op deze bankrekening ontvangen.", "confirm-change-withdrawal-account.title": "Wijzig ontvangende bank", "confirm-ramove-withdrawal-account.title": "Verwijder bankrekening", "confirm-remove-withdrawal-account.subtitle": "Deze bankrekeninggegevens worden uit Zeal verwijderd. Je kunt ze op elk moment opnieuw toevoegen.", "confirmTransaction.finalNetworkFee": "Netwerkkost", "confirmTransaction.importKeys": "Sleutels importeren", "confirmTransaction.networkFee": "Netwerkkost", "confirmation.title": "Vers<PERSON>ur {amount} naar {recipient}", "conflicting-monerium-account.add-owner": "Toevoegen als Gnosis Pay-eigenaar", "conflicting-monerium-account.create-wallet": "Nieuwe smart wallet aanmaken", "conflicting-monerium-account.disconnect-card": "<PERSON><PERSON> kaart <PERSON> van Zeal en verbind opnieuw met nieu<PERSON> eigenaar.", "conflicting-monerium-account.header": "{wallet} gekoppeld aan ander Monerium-account", "conflicting-monerium-account.subtitle": "W<PERSON>jzig je Gnosis Pay-eigenaar-wallet", "connection.diconnected.got_it": "Begrepen!", "connection.diconnected.page1.subtitle": "Zeal werkt overal waar Metamask werkt. Verbind zoals je met Metamask zou doen.", "connection.diconnected.page1.title": "Hoe verbind je met <PERSON><PERSON>?", "connection.diconnected.page2.subtitle": "Je ziet veel opties. Zeal kan er een van zijn. Als Zeal niet verschijnt...", "connection.diconnected.page2.title": "Klik op Verbind Wallet", "connection.diconnected.page3.subtitle": "We vragen om een verbinding met <PERSON><PERSON>. <PERSON><PERSON>er of Injected zou ook moeten werken. <PERSON>beer het!", "connection.diconnected.page3.title": "<PERSON><PERSON>", "connectionSafetyCheck.tag.caution": "Let op", "connectionSafetyCheck.tag.danger": "<PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.passed": "Geslaagd", "connectionSafetyConfirmation.subtitle": "Weet je zeker dat je door wilt gaan?", "connectionSafetyConfirmation.title": "Deze site lijkt gevaarlijk", "connection_state.connect.cancel": "<PERSON><PERSON><PERSON>", "connection_state.connect.changeToMetamask": "Wissel naar MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Wissel naar MetaMask", "connection_state.connect.connect_button": "Verbinden", "connection_state.connect.expanded.connected": "Verbonden", "connection_state.connect.expanded.title": "Verbinden", "connection_state.connect.safetyChecksLoading": "Veiligheid van site controleren", "connection_state.connect.safetyChecksLoadingError": "<PERSON>n veiligheidscontroles niet volto<PERSON>en", "connection_state.connected.expanded.disconnectButton": "<PERSON><PERSON>", "connection_state.connected.expanded.title": "Verbonden", "copied-diagnostics": "Diagnostische gegevens gekopieerd", "copy-diagnostics": "Diagnostische gegevens kopiëren", "counterparty.component.add_recipient_primary_text": "Bankontvanger toe<PERSON>n", "counterparty.country": "Land", "counterparty.countryTitle": "<PERSON> van de ontvanger", "counterparty.currency": "Valuta", "counterparty.delete.success.title": "Verwijderd", "counterparty.edit.success.title": "Wijzigingen opgeslagen", "counterparty.errors.country_required": "Land vereist", "counterparty.errors.first_name.invalid": "<PERSON><PERSON><PERSON><PERSON> moet langer zijn", "counterparty.errors.last_name.invalid": "Achternaam moet langer zijn", "counterparty.first_name": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Naar bank verzenden", "countrySelector.noCountryFound": "Geen land gevonden", "countrySelector.title": "Kies land", "create-passkey.cta": "<PERSON><PERSON> a<PERSON>", "create-passkey.extension.cta": "Doorgaan", "create-passkey.footnote": "Mogelijk gemaakt door", "create-passkey.mobile.cta": "Start beveiligingssetup", "create-passkey.steps.enable-recovery": "Cloudherstel instellen", "create-passkey.steps.setup-biometrics": "Biometrische beveiliging inschakelen", "create-passkey.subtitle": "Passkeys zijn veiliger dan wachtwoorden en worden versleuteld opgeslagen in de cloud voor eenvoudig herstel.", "create-passkey.title": "Account beveiligen", "create-smart-wallet": "Smart Wallet aanmaken", "create-userop.progress.text": "Aanmaken", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Ga verder in Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Ga naar Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "Je bent al een kaartbestelling gestart. Ga terug naar de Gnosis Pay-site om deze te voltooien.", "create_recharge_preferences.card": "<PERSON><PERSON>", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "<PERSON>en {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "<PERSON><PERSON> a<PERSON>", "create_recharge_preferences.link_accounts_title": "Re<PERSON>en koppelen", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "Je kaart wordt NIET automatisch opgewaardeerd na elke betaling.", "create_recharge_preferences.not_configured_title": "Verdienen & uitgeven", "create_recharge_preferences.recharge_from_earn_accounts_description": "<PERSON> kaart wordt na elke betaling automatisch opgewaardeerd vanuit je Earn-rekening.", "create_recharge_preferences.subtitle": "per jaar", "creating-account.loading": "Account wordt aangemaakt", "creating-gnosis-pay-account": "Account aan<PERSON>ken", "currencies.bridge.select_routes.emptyState": "We hebben geen routes gevonden voor deze bridge", "currency.add_currency.add_token": "Token toe<PERSON>n", "currency.add_currency.not_a_valid_address": "Dit is geen geldig <PERSON>", "currency.add_currency.token_decimals_feild": "Token decimalen", "currency.add_currency.token_feild": "Tokenadres", "currency.add_currency.token_symbol_feild": "Tokensymbool", "currency.add_currency.update_token": "Token bijwerken", "currency.add_custom.remove_token.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.add_custom.remove_token.header": "Token verwijderen", "currency.add_custom.remove_token.subtitle": "Je portemonnee behoudt het saldo van de<PERSON> token, maar het wordt verborgen in je Zeal-portfolio.", "currency.add_custom.token_removed": "Token verwi<PERSON>derd", "currency.add_custom.token_updated": "Token bijgewerkt", "currency.balance_label": "Saldo: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "Met je bankoverschrijving is succesvol {fiat} overgemaakt naar {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "Je hebt ontvangen {crypto}", "currency.bankTransfer.deposit_status.success": "Ontvangen in je portemonnee", "currency.bankTransfer.deposit_status.title": "Storting", "currency.bankTransfer.off_ramp.check_bank_account": "Controleer je bankrekening", "currency.bankTransfer.off_ramp.complete": "Voltooid", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Verzenden naar je bank", "currency.bankTransfer.off_ramp.transferring_to_currency": "<PERSON><PERSON><PERSON><PERSON><PERSON> naar {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Het geld zou nu op je rekening moeten staan.", "currency.bankTransfer.withdrawal_status.success": "Verzonden naar je bank", "currency.bankTransfer.withdrawal_status.title": "Opname", "currency.bank_transfer.create_unblock_user.email": "E-mailadres", "currency.bank_transfer.create_unblock_user.email_invalid": "Ongeldig e-mailadres", "currency.bank_transfer.create_unblock_user.email_missing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_missing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "Alleen letters, cijfers, spaties en - . , & ( ) ' toegestaan.", "currency.bank_transfer.create_unblock_user.last_name": "Achternaam", "currency.bank_transfer.create_unblock_user.last_name_missing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "Alleen letters, cijfers, spaties en - . , & ( ) ' toegestaan.", "currency.bank_transfer.create_unblock_user.note": "Door verder te gaan, accepteer je <PERSON>'s (onze bankpartner) <terms>Voorwaarden</terms> en <policy>Privacybeleid</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> je naam precies zoals op je bankrekening", "currency.bank_transfer.create_unblock_user.title": "<PERSON><PERSON> je bankrekening", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Rekeningnummer", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Land van de bank", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Voorkeursvaluta", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Sorteercode", "currency.bank_transfer.create_unblock_withdraw_account.success": "<PERSON><PERSON> ingesteld", "currency.bank_transfer.create_unblock_withdraw_account.title": "<PERSON><PERSON> je bankrekening", "currency.bank_transfer.residence-form.address-required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.address-unsupported-char": "Alleen letters, cijfers, spaties en , ; {apostrophe} - \\\\ toegestaan.", "currency.bank_transfer.residence-form.city-required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.city-unsupported-char": "Alleen letters, cijfers, spaties en . , - & ( ) {apostrophe} toegestaan.", "currency.bank_transfer.residence-form.postcode-invalid": "Ongeldige postcode", "currency.bank_transfer.residence-form.postcode-required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.validation.invalid.account_number": "Ongeldig rekeningnummer", "currency.bank_transfer.validation.invalid.iban": "Ongeldige IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Ongeldige sorteercode", "currency.bridge.amount_label": "Bedrag voor bridge", "currency.bridge.best_returns.subtitle": "Deze bridge provider geeft je de hoogste opbrengst, inclusief alle kosten.", "currency.bridge.best_returns_popup.title": "Beste rendement", "currency.bridge.bridge_from": "<PERSON>", "currency.bridge.bridge_gas_fee_loading_failed": "Probleem bij het laden van de netwerkkost", "currency.bridge.bridge_low_slippage": "Zeer lage slippage. Probeer deze te verhogen", "currency.bridge.bridge_provider": "<PERSON><PERSON><PERSON><PERSON> overschrijving", "currency.bridge.bridge_provider_loading_failed": "We konden geen providers laden", "currency.bridge.bridge_settings": "Bridge-instellingen", "currency.bridge.bridge_status.subtitle": "Via {name}", "currency.bridge.bridge_status.title": "Bridge", "currency.bridge.bridge_to": "<PERSON>ar", "currency.bridge.fastest_route_popup.subtitle": "Deze bridge provider biedt je de snelste transactieroute.", "currency.bridge.fastest_route_popup.title": "Snelste route", "currency.bridge.from": "<PERSON>", "currency.bridge.success": "Voltooid", "currency.bridge.title": "Bridge", "currency.bridge.to": "<PERSON>ar", "currency.bridge.topup": "Waardeer op {symbol}", "currency.bridge.withdrawal_status.title": "Opname", "currency.card.card_top_up_status.title": "<PERSON><PERSON> toe<PERSON>n aan kaart", "currency.destination_amount": "Te ontvangen bedrag", "currency.hide_currency.confirm.subtitle": "<PERSON><PERSON><PERSON> deze token in je portfolio. Je kunt hem op elk moment weer zichtbaar maken.", "currency.hide_currency.confirm.title": "Token verbergen", "currency.hide_currency.success.title": "Token verborgen", "currency.label": "Label (optioneel)", "currency.last_name": "Achternaam", "currency.max_loading": "Max:", "currency.swap.amount_to_swap": "Bedrag om te swappen", "currency.swap.best_return": "Route met beste opbrengst", "currency.swap.destination_amount": "Te ontvangen bedrag", "currency.swap.header": "<PERSON><PERSON><PERSON>", "currency.swap.max_label": "Saldo: {amount}", "currency.swap.provider.header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.swap.select_to_token": "Selecteer token", "currency.swap.swap_gas_fee_loading_failed": "Probleem bij het laden van de netwerkkost", "currency.swap.swap_provider_loading_failed": "Probleem bij het laden van providers", "currency.swap.swap_settings": "Swap-instellingen", "currency.swap.swap_slippage_too_low": "Zeer lage slippage. Probeer deze te verhogen", "currency.swaps_io_native_token_swap.subtitle": "Via Swaps.IO", "currency.swaps_io_native_token_swap.title": "Verzenden", "currency.withdrawal.amount_from": "<PERSON>", "currency.withdrawal.amount_to": "<PERSON>ar", "currencySelector.title": "<PERSON><PERSON> valuta", "dApp.wallet-does-not-support-chain.subtitle": "Je portemonnee ondersteunt geen {network}. Gebruik een andere portemonnee of Zeal.", "dApp.wallet-does-not-support-chain.title": "Niet-ondersteund netwerk", "dapp.connection.manage.confirm.disconnect.all.cta": "Alles los", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Weet je zeker dat je alle verbindingen wilt los<PERSON>ppelen?", "dapp.connection.manage.confirm.disconnect.all.title": "<PERSON><PERSON>", "dapp.connection.manage.connection_list.main.button.title": "<PERSON><PERSON><PERSON><PERSON>", "dapp.connection.manage.connection_list.no_connections": "Je hebt geen verbonden apps", "dapp.connection.manage.connection_list.section.button.title": "<PERSON><PERSON>", "dapp.connection.manage.connection_list.section.title": "Actief", "dapp.connection.manage.connection_list.title": "Verbindingen", "dapp.connection.manage.disconnect.success.title": "<PERSON><PERSON>", "dapp.metamask_mode.title": "MetaMask-modus", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "Rente", "dc25-card-marketing-card.right.subtitle": "100 personen", "dc25-card-marketing-card.title": "Eerste 100 die €50 uitgeven verdienen {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Je kunt deze actie 3 minuten niet uitvoeren. Om veiligheidsredenen duurt het verwerken van kaartinstellingen of opnames 3 minuten.", "delayQueueBusyBanner.processing-yout-action.title": "Je actie wordt verwerkt, even geduld", "delayQueueBusyWidget.cardFrozen": "<PERSON><PERSON>", "delayQueueBusyWidget.processingAction": "Je actie wordt verwerkt", "delayQueueFailedBanner.action-incomplete.get-support": "<PERSON><PERSON><PERSON><PERSON>", "delayQueueFailedBanner.action-incomplete.subtitle": "Sorry, er is iets mis<PERSON><PERSON><PERSON> met je opname of de update van je instellingen. Neem contact op met support via Discord.", "delayQueueFailedBanner.action-incomplete.title": "<PERSON><PERSON>", "delayQueueFailedWidget.actionIncomplete.title": "Ka<PERSON><PERSON><PERSON> on<PERSON>lledig", "delayQueueFailedWidget.cardFrozen.subtitle": "<PERSON><PERSON>", "delayQueueFailedWidget.contactSupport": "Neem contact op met support", "delay_queue_busy.subtitle": "Om veiligheidsredenen duurt het 3 minuten om kaartinstellingen of opnames te verwerken. Gedurende deze tijd is je kaart bevroren.", "delay_queue_busy.title": "Je actie wordt verwerkt", "delay_queue_failed.contact_support": "Contact", "delay_queue_failed.subtitle": "Sorry, er is iets mis<PERSON><PERSON><PERSON> met je opname of de update van je instellingen. Neem contact op met support via Discord.", "delay_queue_failed.title": "Neem contact op met support", "deploy-earn-form-smart-wallet.in-progress.title": "Earn wordt voorbereid", "deposit": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.cancel": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.disconnect": "Ontkoppelen", "disconnect-card-popup.subtitle": "<PERSON><PERSON><PERSON> verwijder je je kaart uit de Zeal-app. Je portemonnee blijft verbonden met je kaart in de Gnosis Pay-app. Je kunt je kaart op elk moment opnieuw verbinden.", "disconnect-card-popup.title": "<PERSON><PERSON>", "distance.long.days": "{count} dagen", "distance.long.hours": "{count} uur", "distance.long.minutes": "{count} minuten", "distance.long.months": "{count} maanden", "distance.long.seconds": "{count} seconden", "distance.long.years": "{count} jaar", "distance.short.days": "{count} d", "distance.short.hours": "{count} u", "distance.short.minutes": "{count} min", "distance.short.months": "{count} mnd", "distance.short.seconds": "{count} sec", "distance.short.years": "{count} j", "duration.short.days": "{count}d", "duration.short.hours": "{count}u", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "Storting", "earn-deposit-view.into": "<PERSON>ar", "earn-deposit-view.to": "<PERSON>ar", "earn-deposit.swap.transfer-provider": "Transactieprovider", "earn-taker-investment-details.accrued-realtime": "Realtime opgebouwd", "earn-taker-investment-details.asset-class": "Activacategorie", "earn-taker-investment-details.asset-coverage-ratio": "Dekkingsgraad activa", "earn-taker-investment-details.asset-reserve": "Activareserve", "earn-taker-investment-details.base_currency.label": "Basisvaluta", "earn-taker-investment-details.chf.description": "Verdien rente op je CHF door zCHF te storten in Frankencoin - een vertrouwde digitale geldmarkt. De rente wordt gegenereerd uit leningen met een laag risico en over-collateralisatie op Frankencoin en wordt in realtime uitbetaald. Je geld staat veilig op een beveiligde sub-rekening die alleen jij beheert.", "earn-taker-investment-details.chf.description.with_address_link": "Verdien rente op je CHF door zCHF te storten in Frankencoin - een vertrouwde digitale geldmarkt. De rente wordt gegenereerd uit leningen met een laag risico en over-collateralisatie op Frankencoin en wordt in realtime uitbetaald. Je geld staat veilig op een beveiligde sub-rekening <link>(kopieer 0x)</link> die alleen jij beheert.", "earn-taker-investment-details.chf.label": "Digitale Zwitserse Frank", "earn-taker-investment-details.collateral-composition": "Samenstelling onderpand", "earn-taker-investment-details.depositor-obligations": "Verplichtingen aan inleggers", "earn-taker-investment-details.eure.description": "Verdien rente op je euro's door EURe te storten in Aave - een vertrouwde digitale geldmarkt. EURe is een volledig gereguleerde euro-stablecoin, uitgegeven door Monerium en 1:1 gedekt op beveiligde rekeningen. Rente wordt gegenereerd uit leningen met een laag risico en een hoog onderpand op Aave, en wordt realtime uitbetaald. Je geld staat op een veilige subrekening waar alleen jij controle over hebt.", "earn-taker-investment-details.eure.description.with_address_link": "Verdien rente op je euro's door EURe te storten in Aave - een vertrouwde digitale geldmarkt. EURe is een volledig gereguleerde euro-stablecoin, uitgegeven door Monerium en 1:1 gedekt op beveiligde rekeningen. Rente wordt gegenereerd uit leningen met een laag risico en een hoog onderpand op Aave, en wordt realtime uitbetaald. Je geld staat op een veilige subrekening <link>(kopieer 0x)</link> waar alleen jij controle over hebt.", "earn-taker-investment-details.eure.label": "Digitale Euro (EURe)", "earn-taker-investment-details.faq": "FAQ", "earn-taker-investment-details.fixed-income": "Vastrentend", "earn-taker-investment-details.issuer": "Uitgevende instelling", "earn-taker-investment-details.key-facts": "Kerngegevens", "earn-taker-investment-details.liquidity": "Liquiditeit", "earn-taker-investment-details.operator": "Marktoperator", "earn-taker-investment-details.projected-yield": "Verwacht jaarlijks rendement", "earn-taker-investment-details.see-other-faq": "Bekijk alle andere FAQ's", "earn-taker-investment-details.see-realtime": "Bekijk realtime gegevens", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Rendement afgelopen 12 maanden", "earn-taker-investment-details.total-collateral": "Totaal Onderpand", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "Totale ZCHF-voorraad", "earn-taker-investment-details.total_deposits": "Totaal Aave-stortingen", "earn-taker-investment-details.usd.description": "Sky is een digitale geldmarkt die stabiele, in Amerikaanse dollars luidende rendementen biedt uit kortlopende Amerikaanse staatsobligaties en leningen met een hoog onderpand—zonder crypto-volatiliteit, 24/7 toegang tot je geld en transparante, on-chain dekking.", "earn-taker-investment-details.usd.description.with_address_link": "Sky is een digitale geldmarkt die stabiele, in Amerikaanse dollars luidende rendementen biedt uit kortlopende Amerikaanse staatsobligaties en leningen met een hoog onderpand—zonder crypto-volatiliteit, 24/7 toegang tot je geld en transparante, on-chain dekking. Investeringen staan op een subrekening <link>(kopieer 0x)</link> waar jij de controle over hebt.", "earn-taker-investment-details.usd.ftx-difference": "<PERSON><PERSON> verschilt dit van <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>i of Luna?", "earn-taker-investment-details.usd.high-returns": "Hoe kan het rendement zo hoog zijn, zeker verge<PERSON> met traditionele banken?", "earn-taker-investment-details.usd.how-is-backed": "Hoe wordt Sky USD gedekt en wat gebeurt er met mijn geld als Zeal failliet gaat?", "earn-taker-investment-details.usd.income-sources": "Inkomstenbronnen 2024", "earn-taker-investment-details.usd.insurance": "Is mijn geld verz<PERSON><PERSON> of gegarandeerd door een instantie (zoals DGS of iets dergelijks)?", "earn-taker-investment-details.usd.label": "Digitale Amerikaanse dollar", "earn-taker-investment-details.usd.lose-principal": "Kan ik re<PERSON>el gezien mijn inleg verliezen, en onder welke omstandigheden?", "earn-taker-investment-details.variable-rate": "<PERSON>en tegen variabele rente", "earn-taker-investment-details.withdraw-anytime": "<PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.yield": "Rendement", "earn-withdrawal-view.approve.for": "Voor", "earn-withdrawal-view.approve.into": "<PERSON>ar", "earn-withdrawal-view.swap.into": "<PERSON>ar", "earn-withdrawal-view.withdraw.to": "<PERSON>ar", "earn.add_another_asset.title": "Selecteer Earning-activum", "earn.add_asset": "Activa toevoegen", "earn.asset_view.title": "<PERSON><PERSON><PERSON>", "earn.base-currency-popup.text": "De basisvaluta is de valuta waarin je stortingen, rendement en transacties worden gewaardeerd en vastgelegd. Als je in een andere valuta stort (zoals EUR in een USD-rekening), wordt je geld direct omgerekend naar de basisvaluta tegen de huidige wisselkoersen. Na de omrekening blijft je saldo stabiel in de basisvaluta, maar bij toekomstige opnames kunnen er opnieuw valutaomrekeningen plaatsvinden.", "earn.base-currency-popup.title": "Basisvaluta", "earn.card-recharge.disabled.list-item.title": "Automatisch opwaarderen uitgeschakeld", "earn.card-recharge.enabled.list-item.title": "Automatisch opwaarderen ingeschakeld", "earn.choose_wallet_to_deposit.title": "<PERSON><PERSON><PERSON>", "earn.config.currency.eth": "Verdien Ethereum", "earn.config.currency.on_chain_address_subtitle": "On-chain adres", "earn.config.currency.us_dollars": "Bankoverschrijvingen instellen", "earn.configured_widget.current_apy.title": "Huidige APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} Jaarlijks", "earn.confirm.currency.cta": "<PERSON><PERSON><PERSON>", "earn.currency.eth": "Verdien Ethereum", "earn.deploy.status.title": "Earn-account aanmaken", "earn.deploy.status.title_with_taker": "<PERSON><PERSON><PERSON><PERSON> {title} Earn-account", "earn.deposit": "<PERSON><PERSON><PERSON>", "earn.deposit.amount_to_deposit": "Te storten bedrag", "earn.deposit.deposit": "<PERSON><PERSON><PERSON>", "earn.deposit.enter_amount": "<PERSON><PERSON>r bedrag in", "earn.deposit.no_routes_found": "Geen routes gevonden", "earn.deposit.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "earn.deposit.select-currency.title": "Selecteer token om te storten", "earn.deposit.select_account.title": "Selecteer <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.desposit_form.title": "Storten in Earn", "earn.earn_deposit.status.title": "Storten op Earn", "earn.earn_deposit.trx.title": "<PERSON><PERSON><PERSON> naar <PERSON>n", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "<PERSON><PERSON><PERSON> geld op wanneer je wilt", "earn.earn_withdraw.status.title": "<PERSON>ne<PERSON>-account", "earn.earn_withdraw.trx.title.approval": "Opname goed<PERSON>uren", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Opnemen naar {asset}", "earn.earn_withdraw.trx.title.withdrawal": "<PERSON><PERSON><PERSON>", "earn.recharge.cta": "Wijzigingen opslaan", "earn.recharge.earn_not_configured.enable_some_account.error": "Activeer account", "earn.recharge.earn_not_configured.enter_amount.error": "<PERSON><PERSON>r bedrag in", "earn.recharge.select_taker.header": "<PERSON><PERSON> in volgorde van", "earn.recharge_card_tag.on": "aan", "earn.recharge_card_tag.recharge": "Opwaarderen", "earn.recharge_card_tag.recharge_not_configured": "Automatisch opwaarderen", "earn.recharge_card_tag.recharge_off": "Opwaarderen uit", "earn.recharge_card_tag.recharged": "Opgewaardeerd", "earn.recharge_card_tag.recharging": "Wordt opgewaardeerd", "earn.recharge_configured.disable.trx.title": "Automatisch opwaarderen uitschakelen", "earn.recharge_configured.trx.disclaimer": "<PERSON><PERSON> je je kaart geb<PERSON>, wordt er een Cowswap-veiling aangemaakt om met je Earn-tegoeden hetzelfde bedrag te kopen als je betaling. Dit veilingproces levert doorgaans de beste marktprijs op, maar houd er rekening mee dat de onchain-koers kan afwijken van de reël<PERSON> w<PERSON>rsen.", "earn.recharge_configured.trx.subtitle": "Na elke betaling wordt er automatisch geld van je Earn-account(s) toegevoegd om je kaardsaldo te behouden op {value}", "earn.recharge_configured.trx.title": "Automatisch opwaarderen instellen op {value}", "earn.recharge_configured.updated.trx.title": "Opwaardeerinstellingen opslaan", "earn.risk-banner.subtitle": "Dit is een product in eigen beheer zonder wettelijke bescherming tegen verlies.", "earn.risk-banner.title": "Begrijp de risico's", "earn.set_recharge.status.title": "Automatisch aanvullen instellen", "earn.setup_reacharge.input.disable.label": "Uitschakelen", "earn.setup_reacharge.input.label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.setup_reacharge_form.title": "Automatisch opwaarderen houdt je {br}kaartsaldo op hetzelfde niveau", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Houd wstETH (Staked ETH) aan op Gnosis Chain, en leen uit via Lido.", "earn.taker-bulletlist.point_1": "Verdien {apyValue} per jaar. Opbrengsten variëren met de markt.", "earn.taker-bulletlist.point_3": "Zeal rekent geen kosten.", "earn.taker-historical-returns": "Historische opbrengsten", "earn.taker-historical-returns.chf": "<PERSON><PERSON><PERSON> van CHF naar USD", "earn.taker-investment-tile.apy.perYear": "per jaar", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "<PERSON><PERSON><PERSON>", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Verdienen via Aave op Gnosis Chain", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Verdienen via Frankencoin op Gnosis Chain", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Verdienen via Lido op Gnosis Chain", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Verdienen via Maker op Gnosis Chain", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "ETH verdienen", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH Earn", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "Amerikaanse staatsobligaties (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Ho<PERSON>el het uiterst zeldzaam is, is het theoretisch mogelijk. Je geld wordt beschermd door streng risicobeheer en een hoge dekkingsgraad van onderpand. Het realistische worstcasescenario zou ongekende marktomstandigheden inhouden, zoals meerdere stablecoins die tegelijkertijd hun koppeling verliezen — iets wat nog nooit eerder is gebeurd.", "earn.usd.can-I-lose-my-principal-popup.title": "Kan ik re<PERSON>el gezien mijn inleg verliezen, en onder welke omstandigheden?", "earn.usd.ftx-difference-popup.text": "Sky is fundamenteel anders. In tegenstelling tot FTX, <PERSON><PERSON><PERSON>, <PERSON><PERSON>i of Luna - die sterk afhankelijk waren van gecentraliseerd beheer, ondoorzichtig vermogensbeheer en risicovolle hefboomposities - maakt Sky USD gebruik van transparante, gecontroleerde, decentrale smart contracts en handhaaft het volledige on-chain transparantie. Je behoudt de volledige controle over je privé portemon<PERSON>, waard<PERSON> de tegenpartijrisico's die gepaard gaan met gecentraliseerde storingen aanzienlijk worden verminderd.", "earn.usd.ftx-difference-popup.title": "<PERSON><PERSON> verschilt dit van <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>i of Luna?", "earn.usd.high-returns-popup.text": "Sky USD genereert voornamelijk rendement via decentrale financiële (DeFi) protocollen, die peer-to-peer leningen en liquiditeitsverschaffing automatiseren. Hierdoor worden traditionele bankkosten en tussenpersonen geëlimineerd. <PERSON><PERSON> efficiëntie, g<PERSON><PERSON><PERSON><PERSON> met robuuste risicocontroles, ma<PERSON>t aanzienlijk hogere rendementen mogelijk in vergelijking met traditionele banken.", "earn.usd.high-returns-popup.title": "Hoe kan het rendement zo hoog zijn, zeker verge<PERSON> met traditionele banken?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD wordt volledig gedekt en heeft een overschot aan onderpand door een combinatie van digitale activa in beveiligde smart contracts en activa uit de echte wereld, zoals Amerikaanse staatsobligaties. De reserves kunnen realtime on-chain worden gecontroleerd, zelf<PERSON> vanuit Zeal, wat zorgt voor transparantie en veiligheid. In het onwaarschijnlijke geval dat Zeal stopt, blijven je activa beveiligd on-chain, volledig onder jouw controle en toegankelijk via andere compatibele portemonnees.", "earn.usd.how-is-sky-backed-popup.title": "Hoe wordt Sky USD gedekt en wat gebeurt er met mijn geld als Zeal failliet gaat?", "earn.usd.insurance-popup.text": "Sky USD-tegoeden zijn niet verzekerd door het DGS of gedekt door traditionele overheidsgaranties, omdat het een rekening op basis van digitale activa is en geen conventionele bankrekening. In plaats daarvan beheert Sky alle risicobeperking via gecontroleerde smart contracts en zorgvuldig gescreende DeFi-protocollen, zodat activa veilig en transparant blijven.", "earn.usd.insurance-popup.title": "Is mijn geld verz<PERSON><PERSON> of gegarandeerd door een instantie (zoals DGS of iets dergelijks)?", "earn.usd.lending-operations-popup.text": "Sky USD genereert rendement door stablecoins uit te lenen via decentrale leenmarkten zoals Morpho en Spark. Je stablecoins worden uitgeleend aan leners die aanzienlijk meer onderpand storten - zoals ETH of BTC - dan de waarde van hun lening. <PERSON><PERSON> a<PERSON>, 'overcollateralization' g<PERSON><PERSON><PERSON>, zorgt ervoor dat er altijd voldoende onderpand is om leningen te dekken, wat het risico aanzienlijk verkleint. De geïnde rente en incidentele liquidatiekosten die door leners worden betaald, zorgen voor betrouwbare, transparante en veilige rendementen.", "earn.usd.lending-operations-popup.title": "Uitleenactiviteiten", "earn.usd.market-making-operations-popup.text": "Sky USD verdient extra rendement door deel te nemen aan decentrale beurzen (AMM's) zoals Curve of Uniswap. Door liquiditeit te verschaffen - je stablecoins in pools te plaatsen die cryptohandel faciliteren - vangt Sky USD vergoedingen op die door transacties worden gegenereerd. Deze liquiditeitspools worden zorgvuldig geselecteerd om de volatiliteit te minimaliseren, voornamelijk met stablecoin-naar-stablecoin-paren om risico's zoals 'impermanent loss' aanzienlijk te verminderen, waardoor je activa veilig en toegankelijk blijven.", "earn.usd.market-making-operations-popup.title": "Marketmaking-activiteiten", "earn.usd.treasury-operations-popup.text": "Sky USD genereert een stabiel, consistent rendement door strategische treasury-investeringen. <PERSON><PERSON> de<PERSON> van je stablecoin-stortingen wordt toegewezen aan veilige, laag-risico activa uit de echte wereld - voornamelijk kortlopende staatsobligaties en zeer veilige kredietinstrumenten. <PERSON><PERSON>, verge<PERSON><PERSON><PERSON><PERSON> met traditioneel bankieren, zorgt voor een voorspelbaar en betrouwbaar rendement. Je activa blijven veilig, liquide en worden transparant beheerd.", "earn.usd.treasury-operations-popup.title": "Treasury-activiteiten", "earn.view_earn.card_rechard_off": "Uit", "earn.view_earn.card_rechard_on": "<PERSON><PERSON>", "earn.view_earn.card_recharge": "<PERSON><PERSON>", "earn.view_earn.total_balance_label": "Verdient {percentage} per jaar", "earn.view_earn.total_earnings_label": "Totaal verdiend", "earn.withdraw": "Opnemen", "earn.withdraw.amount_to_withdraw": "Op te nemen bedrag", "earn.withdraw.enter_amount": "<PERSON><PERSON>r bedrag in", "earn.withdraw.loading": "Laden", "earn.withdraw.no_routes_found": "Geen routes gevonden", "earn.withdraw.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "earn.withdraw.select-currency.title": "Selecteer token", "earn.withdraw.select_to_token": "Selecteer token", "earn.withdraw.withdraw": "Opnemen", "earn.withdraw_form.title": "<PERSON><PERSON><PERSON>", "earnings-view.earnings": "<PERSON>e verdi<PERSON>ten", "edit-account-owners.add-owner.add-wallet": "<PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.add_wallet": "Wallet toevoegen", "edit-account-owners.add-owner.title": "Kaarthouder toe<PERSON>n", "edit-account-owners.card-owners": "<PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.external-wallet": "Externe wallet", "editBankRecipient.title": "Ontvanger bewerken", "editNetwork.addCustomRPC": "Aangepaste RPC-node toevoegen", "editNetwork.cannot_verify.subtitle": "De aangepaste RPC-node reageert niet. Controleer de URL en probeer het opnieuw.", "editNetwork.cannot_verify.title": "We kunnen de RPC-node niet veri<PERSON>", "editNetwork.cannot_verify.try_again": "Opnieuw", "editNetwork.customRPCNode": "Aangepaste RPC-node", "editNetwork.defaultRPC": "Standaard RPC", "editNetwork.networkRPC": "Netwerk-RPC", "editNetwork.rpc_url.cannot_be_empty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editNetwork.rpc_url.not_a_valid_https_url": "<PERSON>t een geldige HTTP(S)-URL zijn", "editNetwork.safetyWarning.subtitle": "Zeal kan de privacy, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> en veiligh<PERSON><PERSON> van aangepaste RPC's niet garanderen. Weet je zeker dat je een aangepaste RPC-node wilt gebruiken?", "editNetwork.safetyWarning.title": "Aangepaste RPC's kunnen onveilig zijn", "editNetwork.zealRPCNode": "Zeal RPC-node", "editNetworkRpc.headerTitle": "Aangepaste RPC-node", "editNetworkRpc.rpcNodeUrl": "RPC-node-URL", "editing-locked.modal.description": "In tegenstelling tot goedkeuringsoverschrijvingen kun je bij vergunningen de bestedingslimiet of vervaltijd niet bewerken. Zorg dat je een dApp vertrouwt voordat je een vergunning indient.", "editing-locked.modal.title": "Bewerken vergrendeld", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Opwaarderen wordt ingeschakeld", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Opwaardere<PERSON> ingeschakeld", "enterCardnumber": "<PERSON><PERSON><PERSON> ka<PERSON>nummer in", "error.connectivity_error.subtitle": "Controleer je internetverbinding en probeer het opnieuw.", "error.connectivity_error.title": "<PERSON>n internetverbinding", "error.decrypt_incorrect_password.title": "Onju<PERSON> wachtwoord", "error.encrypted_object_invalid_format.title": "Beschadigde gegevens", "error.failed_to_fetch_google_auth_token.title": "We kregen geen toegang", "error.list.item.cta.action": "Opnieuw proberen", "error.trezor_action_cancelled.title": "Transactie geweigerd", "error.trezor_device_used_elsewhere.title": "Apparaat wordt in een andere sessie gebruikt", "error.trezor_method_cancelled.title": "<PERSON>n T<PERSON> niet synchroniseren", "error.trezor_permissions_not_granted.title": "<PERSON>n T<PERSON> niet synchroniseren", "error.trezor_pin_cancelled.title": "<PERSON>n T<PERSON> niet synchroniseren", "error.trezor_popup_closed.title": "<PERSON>n T<PERSON> niet synchroniseren", "error.unblock_account_number_and_sort_code_mismatch": "Rekeningnummer en sort code komen niet overeen", "error.unblock_can_not_change_details_after_kyc": "Gegevens wijzigen na KYC is niet mogelijk.", "error.unblock_hard_kyc_failure": "Onverwachte KYC-status", "error.unblock_invalid_faster_payment_configuration.title": "Deze bank ondersteunt geen Faster Payments", "error.unblock_invalid_iban": "Ongeldig IBAN", "error.unblock_session_expired.title": "Unblock-se<PERSON> verlopen", "error.unblock_user_with_address_already_exists.title": "Account al ingesteld voor dit adres", "error.unblock_user_with_such_email_already_exists.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> met dit e-mailadres bestaat al", "error.unknown_error.error_message": "Foutmelding: ", "error.unknown_error.subtitle": "Sorry! Als je dringend hulp nodig hebt, neem dan contact op met support en deel de onderstaande details.", "error.unknown_error.title": "Systeemfout", "eth-cost-warning-modal.subtitle": "Smart Wallets werken op Ethereum, maar de netwerkkosten zijn erg hoog. We raden dan ook STERK aan om andere netwerken te gebruiken.", "eth-cost-warning-modal.title": "Vermijd Ethereum - netwerkkosten zijn hoog", "exchange.form.button.chain_unsupported": "Chain niet ondersteund", "exchange.form.button.refreshing": "<PERSON><PERSON><PERSON><PERSON>", "exchange.form.error.asset_not_supported.button": "Selecteer ander activum", "exchange.form.error.asset_not_supported.description": "De bridge ondersteunt deze token niet.", "exchange.form.error.asset_not_supported.title": "Token niet ondersteund", "exchange.form.error.bridge_quote_timeout.button": "Selecteer ander activum", "exchange.form.error.bridge_quote_timeout.description": "<PERSON><PERSON><PERSON> een ander <PERSON>ar", "exchange.form.error.bridge_quote_timeout.title": "<PERSON><PERSON> w<PERSON>lk<PERSON>rs gevonden", "exchange.form.error.different_receiver_not_supported.button": "Alternatieve ontvanger verwijderen", "exchange.form.error.different_receiver_not_supported.description": "Deze dienst ondersteunt het verzenden naar een ander adres niet.", "exchange.form.error.different_receiver_not_supported.title": "Verzend- en ontvangstadres moeten gelijk zijn", "exchange.form.error.insufficient_input_amount.button": "<PERSON><PERSON> verhogen", "exchange.form.error.insufficient_liquidity.button": "Bedrag verlagen", "exchange.form.error.insufficient_liquidity.description": "De bridge heeft onvoldoende tegoeden. Probeer een kleiner bedrag.", "exchange.form.error.insufficient_liquidity.title": "Bedrag te hoog", "exchange.form.error.max_amount_exceeded.button": "Bedrag verlagen", "exchange.form.error.max_amount_exceeded.description": "Het maximale bedrag is overschreden.", "exchange.form.error.max_amount_exceeded.title": "Bedrag te hoog", "exchange.form.error.min_amount_not_met.button": "<PERSON><PERSON> verhogen", "exchange.form.error.min_amount_not_met.description": "Het minimale wisselbedrag voor deze token is niet bereikt.", "exchange.form.error.min_amount_not_met.description_with_amount": "Het minimale wisselbedrag is {amount}.", "exchange.form.error.min_amount_not_met.title": "Bedrag te laag", "exchange.form.error.min_amount_not_met.title_increase": "<PERSON><PERSON> verhogen", "exchange.form.error.no_routes_found.button": "Selecteer ander activum", "exchange.form.error.no_routes_found.description": "Er is geen wisselroute beschikbaar voor deze combinatie van token en netwerk.", "exchange.form.error.no_routes_found.title": "<PERSON><PERSON> wissel-<PERSON><PERSON> be<PERSON>", "exchange.form.error.not_enough_balance.button": "Bedrag verlagen", "exchange.form.error.not_enough_balance.description": "Je hebt onvoldo<PERSON>e saldo van deze token voor de transactie.", "exchange.form.error.not_enough_balance.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "exchange.form.error.slippage_passed_is_too_low.button": "Slippage verhogen", "exchange.form.error.slippage_passed_is_too_low.description": "De toegestane slippage is te laag voor deze token.", "exchange.form.error.slippage_passed_is_too_low.title": "Slippage te laag", "exchange.form.error.socket_internal_error.button": "<PERSON><PERSON><PERSON> later opnieuw", "exchange.form.error.socket_internal_error.description": "De bridge-partner he<PERSON>t problemen. <PERSON>beer het later opnieuw.", "exchange.form.error.socket_internal_error.title": "Fout bij bridge-partner", "exchange.form.error.stargatev2_requires_fee_in_native": "Voeg toe {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Voeg toe {amount} om de transactie te voltooien", "exchange.form.error.stargatev2_requires_fee_in_native.title": "<PERSON><PERSON> nodig {symbol}", "expiration-info.modal.description": "De vervaltijd bepaalt hoe lang een app je tokens kan gebruiken. Als de tijd om is, verliezen ze de toegang tot jij anders beslist. Houd de vervaltijd kort voor de veiligheid.", "expiration-info.modal.title": "Wat is de vervaltijd?", "expiration-time.high.modal.text": "<PERSON><PERSON> vervaltijden kort. Lange tijden zijn riskant, ze geven oplichters meer kans om je tokens te misbruiken.", "expiration-time.high.modal.title": "<PERSON> ve<PERSON>", "failed.transaction.content": "Transactie mislukt waarschijnlijk", "fee.unknown": "Onbekend", "feedback-request.leave-message": "Laat een bericht achter", "feedback-request.not-now": "Niet nu", "feedback-request.title": "Bedankt! Hoe kunnen we Zeal verbeteren?", "float.input.period": "Decimaal scheidingsteken", "gnosis-activate-card.info-popup.subtitle": "Eerst chip & pin, dan contactloos.", "gnosis-activate-card.info-popup.title": "Eerste betaling vereist chip & pin", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "<PERSON><PERSON>r je kaartnummer in om te activeren.", "gnosis-activate-card.title": "Kaartnummer", "gnosis-pay-re-kyc-widget.btn-text": "Verifiëren", "gnosis-pay-re-kyc-widget.title.not-started": "Verifieer je identiteit", "gnosis-pay.login.cta": "Bestaand account koppelen", "gnosis-pay.login.title": "Je hebt al een Gnosis Pay-account", "gnosis-signup.confirm.subtitle": "<PERSON><PERSON> naar een e-mail van Gnosis Pay. Deze kan in je spamfolder zitten.", "gnosis-signup.confirm.title": "Geen verificatie-e-mail ontvangen?", "gnosis-signup.continue": "Doorgaan", "gnosis-signup.dont_link_accounts": "<PERSON><PERSON><PERSON> niet koppelen", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "<PERSON><PERSON><PERSON> <EMAIL> in", "gnosis-signup.enter-email.title": "Voer e-mailadres in", "gnosis-signup.title": "<PERSON>k ga <PERSON><PERSON><PERSON><PERSON> met Gnosis Pay's <linkGnosisTNC>Algemene Voorwaarden</linkGnosisTNC> <monovateTerms>Voorwaarden voor Kaarthouders</monovateTerms> en <linkMonerium>Algemene Voorwaarden van Monerium</linkMonerium>.", "gnosis-signup.verify-email.title": "Verifieer e-mailadres", "gnosis.confirm.subtitle": "Geen code ontvangen? Controleer of je telefoonnummer klopt", "gnosis.confirm.title": "Code verzonden naar {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Verifiëren", "gnosisPayAccountStatus.success.title": "<PERSON><PERSON>", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay is nog niet besch<PERSON> in jouw land", "gnosisPayNoActiveCardsFound.title": "<PERSON><PERSON> actieve ka<PERSON>n", "gnosis_pay_card_delay_relay_not_empty_error.title": "Je transactie kan nu niet worden verwerkt. Probeer het later opnieuw.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "<PERSON><PERSON> niet mogel<PERSON>jk", "gnosiskyc.modal.approved.activate-free-card": "<PERSON>er gratis kaart", "gnosiskyc.modal.approved.button-text": "Stort vanaf bankrekening", "gnosiskyc.modal.approved.title": "Je persoonlijke accountgegevens zijn aangemaakt", "gnosiskyc.modal.failed.close": "Sluiten", "gnosiskyc.modal.failed.title": "Sorry, onze partner Gnosis Pay kan geen account voor je aanmaken", "gnosiskyc.modal.in-progress.title": "ID-verificatie kan 24 uur of langer duren. Even ged<PERSON>, alsjeblieft", "goToSettingsPopup.settings": "Instellingen", "goToSettingsPopup.title": "<PERSON><PERSON><PERSON> meldingen op elk moment in via je apparaatinstellingen", "google_file.error.failed_to_fetch_auth_token.button_title": "<PERSON><PERSON><PERSON> opnieuw", "google_file.error.failed_to_fetch_auth_token.subtitle": "<PERSON><PERSON> ons toegang tot je persoonlijke cloud om je Herstelbestand te kunnen gebruiken.", "google_file.error.failed_to_fetch_auth_token.title": "We kregen geen toegang", "hidden_tokens.widget.emptyState": "Geen verborgen tokens", "how_to_connect_to_metamask.got_it": "<PERSON><PERSON>, begrepen", "how_to_connect_to_metamask.story.subtitle": "Wissel eenvoudig en op elk moment tussen Zeal en andere wallets.", "how_to_connect_to_metamask.story.title": "Zeal werkt naast andere wallets", "how_to_connect_to_metamask.why_switch": "Waarom wisselen tussen Zeal en andere wallets?", "how_to_connect_to_metamask.why_switch.description": "Welke wallet je ook kiest, je krijgt altijd de veiligheidscontroles van Zeal die je beschermen tegen schadelijke sites en transacties.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "We weten dat het moeilijk is om de sprong te wagen en een nieuwe wallet te gebruiken. <PERSON><PERSON><PERSON> hebben we het makkelijk gemaakt om Zeal naast je huidige wallet te gebruiken. Wissel wanneer je wilt.", "import-bank-transfer-owner.banner.title": "De gekoppelde wallet is gewijzigd. Importeer hem om overschrijvingen te blijven gebruiken.", "import-bank-transfer-owner.title": "Importeer portemonnee voor bankoverschrijvingen op dit apparaat", "import_gnosispay_wallet.add-another-card-owner.footnote": "Importe<PERSON> pri<PERSON><PERSON><PERSON><PERSON><PERSON> of seed phrase van de houder van je Gnosis <PERSON>-kaart", "import_gnosispay_wallet.primaryText": "Gnosis Pay-wallet importeren", "injected-wallet": "Browserportemonnee", "intercom.getHelp": "<PERSON><PERSON><PERSON> vragen", "invalid_iban.got_it": "Begrepen", "invalid_iban.subtitle": "De ingevoerde IBAN is niet geldig. Controleer of de gegevens correct zijn ingevoerd en probeer het opnieuw.", "invalid_iban.title": "Ongeldige IBAN", "keypad-0": "Toetsenblok toets 0", "keypad-1": "Toetsenblok toets 1", "keypad-2": "Toetsenblok toets 2", "keypad-3": "Toetsenblok toets 3", "keypad-4": "Toetsenblok toets 4", "keypad-5": "Toetsenblok toets 5", "keypad-6": "Toetsenblok toets 6", "keypad-7": "Toetsenblok toets 7", "keypad-8": "Toetsenblok toets 8", "keypad-9": "Toetsenblok toets 9", "keypad.biometric-button": "Biometrische knop op toetsenbord", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Geheime zin beveiligd 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "<PERSON><PERSON> be<PERSON>", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "<PERSON><PERSON><PERSON> een veil<PERSON>, offline kopie van je geheime zin zodat je later je bezittingen kunt herstellen", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "<PERSON><PERSON>r het woord niet te raden", "keystore.write_secret_phrase.before_you_begin.first_point": "<PERSON><PERSON> beg<PERSON><PERSON> dat <PERSON><PERSON><PERSON> met mijn geheime zin mijn bezittingen kan overschrijven", "keystore.write_secret_phrase.before_you_begin.second_point": "<PERSON><PERSON> <PERSON>rantwoordelijk voor het geheim en veilig houden van mijn geheime zin", "keystore.write_secret_phrase.before_you_begin.subtitle": "Lees en aanvaard de volgende punten:", "keystore.write_secret_phrase.before_you_begin.third_point": "<PERSON>k ben op een priv<PERSON><PERSON><PERSON><PERSON> zonder mensen of camera's om me heen", "keystore.write_secret_phrase.before_you_begin.title": "<PERSON>oor je <PERSON>t", "keystore.write_secret_phrase.secret_phrase_test.title": "Wat is woord {count} in je geheime zin?", "keystore.write_secret_phrase.test_ps.lets_do_it": "Laten we beginnen", "keystore.write_secret_phrase.test_ps.subtitle": "Je hebt je geheime zin nodig om je account op dit of andere apparaten te herstellen. Laten we testen of je geheime zin correct is opgeschreven.", "keystore.write_secret_phrase.test_ps.subtitle2": "We vragen je om {count} woorden uit je zin.", "keystore.write_secret_phrase.test_ps.title": "Testherstel van account", "kyc.modal.approved.button-text": "Bankoverschrijving doen", "kyc.modal.approved.subtitle": "Je verificatie is voltooid. Je kunt nu onbeperkt bankoverschrijvingen doen.", "kyc.modal.approved.title": "Bankoverschrijvingen ontgrendeld", "kyc.modal.continue-with-partner.button-text": "Doorgaan", "kyc.modal.continue-with-partner.subtitle": "We sturen je nu door naar onze partner om je documenten te verzamelen en de verificatie af te ronden.", "kyc.modal.continue-with-partner.title": "<PERSON><PERSON> <PERSON><PERSON><PERSON> met onze partner", "kyc.modal.failed.unblock.subtitle": "Unblock heeft je identiteitsverificatie niet goedgekeurd en kan je geen bankoverschrijvingen aanbieden.", "kyc.modal.failed.unblock.title": "Unblock-a<PERSON><PERSON><PERSON><PERSON> niet goedge<PERSON>d", "kyc.modal.paused.button-text": "Gegevens bijwerken", "kyc.modal.paused.subtitle": "Het lijkt erop dat sommige van je gegevens onjuist zijn. Probeer het opnieuw en controleer je gegevens voordat je ze indient.", "kyc.modal.paused.title": "<PERSON> gegevens lijken onjuist", "kyc.modal.pending.button-text": "Sluiten", "kyc.modal.pending.subtitle": "Verificatie duurt normaal gesproken minder dan 10 minuten, maar kan soms iets langer duren.", "kyc.modal.pending.title": "We houden je op de hoogte", "kyc.modal.required.cta": "Start verificatie", "kyc.modal.required.subtitle": "Je hebt de transactielimiet bereikt. Verifieer je identiteit om verder te gaan. Dit duurt meestal maar een paar minuten en vereist enkele persoonlijke gegevens en documenten.", "kyc.modal.required.title": "Identiteitsverificatie vereist", "kyc.submitted": "Aanvraag ingediend", "kyc.submitted_short": "Ingediend", "kyc_status.completed_status": "Voltooid", "kyc_status.failed_status": "Mislukt", "kyc_status.paused_status": "Beoordelen", "kyc_status.subtitle": "Bankoverschrijvingen", "kyc_status.subtitle.wrong_details": "<PERSON><PERSON><PERSON><PERSON>", "kyc_status.subtitle_in_progress": "Bezig", "kyc_status.title": "Identiteit verifiëren", "label.close": "Sluiten", "label.saving": "Opslaan...", "labels.this-month": "Deze maand", "labels.today": "Vandaag", "labels.yesterday": "Gisteren", "language.selector.title": "Taal", "ledger.account_loaded.imported": "Geïmporteerd", "ledger.add.success.title": "Ledger succesvol verbonden 🎉", "ledger.connect.cta": "Ledger synchroniseren", "ledger.connect.step1": "<PERSON><PERSON><PERSON><PERSON> met je apparaat", "ledger.connect.step2": "Open de Ethereum-app op Ledger", "ledger.connect.step3": "Synchroniseer dan je Ledger 👇", "ledger.connect.subtitle": "Volg deze stappen om je Ledger-wallets in Zeal te importeren", "ledger.connect.title": "<PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON>", "ledger.error.ledger_is_locked.subtitle": "Ontgrendel Ledger en open de Ethereum-app", "ledger.error.ledger_is_locked.title": "Ledger is vergrendeld", "ledger.error.ledger_not_connected.action": "Synchroniseer Ledger", "ledger.error.ledger_not_connected.subtitle": "Verbind je hardware wallet met je apparaat en open de Ethereum-app", "ledger.error.ledger_not_connected.title": "Ledger is niet verbonden", "ledger.error.ledger_running_non_eth_app.title": "Ethereum-app niet geopend", "ledger.error.user_trx_denied_by_user.action": "Sluiten", "ledger.error.user_trx_denied_by_user.subtitle": "Je hebt de transactie geweigerd op je hardware wallet", "ledger.error.user_trx_denied_by_user.title": "Transactie geweigerd", "ledger.hd_path.bip44.subtitle": "bv. <PERSON>, Trezor", "ledger.hd_path.bip44.title": "BIP<PERSON>", "ledger.hd_path.ledger_live.subtitle": "Standaard", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Legacy", "ledger.hd_path.phantom.subtitle": "bv. <PERSON>", "ledger.select.hd_path.subtitle": "HD-paden zijn de manier waarop hardware wallets hun accounts sorteren. <PERSON><PERSON> is <PERSON><PERSON><PERSON><PERSON><PERSON> met hoe een index pagina's in een boek sorteert.", "ledger.select.hd_path.title": "Selecteer HD-pad", "ledger.select_account.import_wallets_count": "{count,plural,=0{<PERSON><PERSON> wallets geselecteerd} one{Importeer wallet} other{Importeer {count} wallets}}", "ledger.select_account.path_settings": "Padinstellingen", "ledger.select_account.subtitle": "Zie je niet de portemonnees die je verwacht? <PERSON><PERSON><PERSON> de padinstellingen te wijzigen", "ledger.select_account.subtitle.group_header": "Portemonnees", "ledger.select_account.title": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "legend.lending-operations": "Uitleenactiviteiten", "legend.market_making-operations": "Marketmaking-activiteiten", "legend.treasury-operations": "Treasury-activiteiten", "link-existing-monerium-account-sign.button": "<PERSON><PERSON>", "link-existing-monerium-account-sign.subtitle": "Je hebt al een Monerium-account.", "link-existing-monerium-account-sign.title": "<PERSON><PERSON> Zeal aan je Monerium-account", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Je hebt al een Monerium-account. Voltooi de installatie in de Monerium-app.", "link-existing-monerium-account.title": "Ga naar Monerium om je account te koppelen", "loading.pin": "Pincode laden...", "lockScreen.passwordIncorrectMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> is onjuist", "lockScreen.passwordRequiredMessage": "Wachtwoord vereist", "lockScreen.unlock.header": "Ontgrendelen", "lockScreen.unlock.subheader": "Gebruik je wachtwoord om Z<PERSON> te ontgrendelen", "mainTabs.activity.label": "Activiteit", "mainTabs.browse.label": "Ontdekken", "mainTabs.browse.title": "Ontdekken", "mainTabs.card.label": "<PERSON><PERSON>", "mainTabs.portfolio.label": "Portefeuille", "mainTabs.rewards.label": "Beloningen", "makeSpendable.cta": "<PERSON><PERSON><PERSON><PERSON> maken", "makeSpendable.holdAsCash": "<PERSON><PERSON><PERSON><PERSON> als contant geld", "makeSpendable.shortText": "Verdien {apy} per jaar", "makeSpendable.title": "{amount} ontvangen", "merchantCategory.agriculture": "Landbouw", "merchantCategory.alcohol": "Alcohol", "merchantCategory.antiques": "<PERSON><PERSON>", "merchantCategory.appliances": "Huishoudapparaten", "merchantCategory.artGalleries": "Kunstgalerijen", "merchantCategory.autoRepair": "Autoreparatie", "merchantCategory.autoRepairService": "Autoreparatieservice", "merchantCategory.beautyFitnessSpas": "Schoonheid, Fitness & Spa's", "merchantCategory.beautyPersonalCare": "Schoonheid & persoonlijke verzorging", "merchantCategory.billiard": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.books": "<PERSON><PERSON><PERSON>", "merchantCategory.bowling": "Bowling", "merchantCategory.businessProfessionalServices": "Zakelijke & professionele diensten", "merchantCategory.carRental": "Autoverhuur", "merchantCategory.carWash": "Carwash", "merchantCategory.cars": "Auto's", "merchantCategory.casino": "Casino", "merchantCategory.casinoGambling": "Casino & gokken", "merchantCategory.cellular": "Mobiel", "merchantCategory.charity": "<PERSON><PERSON>n", "merchantCategory.childcare": "Kinderopvang", "merchantCategory.cigarette": "Sigaretten", "merchantCategory.cinema": "Bioscoop", "merchantCategory.cinemaEvents": "Bioscoop & evenementen", "merchantCategory.cleaning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.cleaningMaintenance": "Schoonmaak & onderhoud", "merchantCategory.clothes": "<PERSON><PERSON><PERSON>", "merchantCategory.clothingServices": "Kledingdiensten", "merchantCategory.communicationServices": "Communicatiediensten", "merchantCategory.construction": "<PERSON><PERSON><PERSON>", "merchantCategory.cosmetics": "Cosmetica", "merchantCategory.craftsArtSupplies": "Hobby- & kunstbenodigdheden", "merchantCategory.datingServices": "Datingdiensten", "merchantCategory.delivery": "Bezorging", "merchantCategory.dentist": "Tandarts", "merchantCategory.departmentStores": "Warenhuizen", "merchantCategory.directMarketingSubscription": "Direct Marketing & Abonnement", "merchantCategory.discountStores": "Discountwin<PERSON>s", "merchantCategory.drugs": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.dutyFree": "Duty Free", "merchantCategory.education": "Onderwi<PERSON><PERSON>", "merchantCategory.electricity": "Elektriciteit", "merchantCategory.electronics": "Elektronica", "merchantCategory.emergencyServices": "Hu<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.equipmentRental": "<PERSON><PERSON><PERSON><PERSON> van <PERSON>", "merchantCategory.evCharging": "EV opladen", "merchantCategory.financialInstitutions": "Financiële instellingen", "merchantCategory.financialProfessionalServices": "Financiële & Professionele Diensten", "merchantCategory.finesPenalties": "Boetes & Sancties", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "Vluchten", "merchantCategory.flowers": "Bloemen", "merchantCategory.flowersGarden": "Bloemen & Tuin", "merchantCategory.food": "<PERSON>ten en drinken", "merchantCategory.freight": "<PERSON><PERSON><PERSON>", "merchantCategory.fuel": "<PERSON><PERSON><PERSON>", "merchantCategory.funeralServices": "Uitvaartdiensten", "merchantCategory.furniture": "<PERSON><PERSON><PERSON>", "merchantCategory.games": "Games", "merchantCategory.gas": "<PERSON><PERSON>", "merchantCategory.generalMerchandiseRetail": "Algemene Handelswaar & Detailhandel", "merchantCategory.gifts": "<PERSON><PERSON>", "merchantCategory.government": "<PERSON><PERSON><PERSON>", "merchantCategory.governmentServices": "Overheidsdiensten", "merchantCategory.hardware": "Hardware", "merchantCategory.healthMedicine": "Gezondheid & Geneeskunde", "merchantCategory.homeImprovement": "Woningverbetering", "merchantCategory.homeServices": "<PERSON><PERSON><PERSON> aan huis", "merchantCategory.hotel": "Hotel", "merchantCategory.housing": "<PERSON><PERSON>", "merchantCategory.insurance": "Verzekering", "merchantCategory.internet": "Internet", "merchantCategory.kids": "Kinderen", "merchantCategory.laundry": "<PERSON><PERSON><PERSON>", "merchantCategory.laundryCleaningServices": "Was- & Schoonmaakdiensten", "merchantCategory.legalGovernmentFees": "Juridische & Overheidskosten", "merchantCategory.luxuries": "Luxe", "merchantCategory.luxuriesCollectibles": "Luxe & Verzamelobjecten", "merchantCategory.magazines": "Tijdschriften", "merchantCategory.magazinesNews": "Tijdschriften & Nieuws", "merchantCategory.marketplaces": "Marktplaatsen", "merchantCategory.media": "Media", "merchantCategory.medicine": "Medisch", "merchantCategory.mobileHomes": "Stacaravans", "merchantCategory.moneyTransferCrypto": "Geldoverdracht & Crypto", "merchantCategory.musicRelated": "Muziekgerelateerd", "merchantCategory.musicalInstruments": "Muziekinstrumenten", "merchantCategory.optics": "Optiek", "merchantCategory.organizationsClubs": "Organisaties & Clubs", "merchantCategory.other": "<PERSON><PERSON>", "merchantCategory.parking": "<PERSON><PERSON>", "merchantCategory.pawnShops": "Pandjeshuizen", "merchantCategory.pets": "Huisdieren", "merchantCategory.photoServicesSupplies": "Fotodiensten & -benodigdheden", "merchantCategory.postalServices": "Postdiensten", "merchantCategory.professionalServicesOther": "Profess<PERSON><PERSON> (Overig)", "merchantCategory.publicTransport": "<PERSON><PERSON><PERSON> vervo<PERSON>", "merchantCategory.purchases": "Aankopen", "merchantCategory.purchasesMiscServices": "Aankopen & Diverse Diensten", "merchantCategory.recreationServices": "Recreatied<PERSON>ten", "merchantCategory.religiousGoods": "Religieuze <PERSON>ren", "merchantCategory.secondhandRetail": "T<PERSON><PERSON>dswinkels", "merchantCategory.shoeHatRepair": "Schoen- & Hoedenreparatie", "merchantCategory.shoeRepair": "Schoenreparatie", "merchantCategory.softwareApps": "Software & Apps", "merchantCategory.specializedRepairs": "Gespecialiseerde <PERSON>", "merchantCategory.sport": "Sport", "merchantCategory.sportingGoods": "Sportartikelen", "merchantCategory.sportingGoodsRecreation": "Sportartikelen & Recreatie", "merchantCategory.sportsClubsFields": "Sportclubs & -velden", "merchantCategory.stationaryPrinting": "Kantoorartikelen & Drukwerk", "merchantCategory.stationery": "Kantoorartikelen", "merchantCategory.storage": "Opslag", "merchantCategory.taxes": "Belastingen", "merchantCategory.taxi": "Taxi", "merchantCategory.telecomEquipment": "Telecomapparatuur", "merchantCategory.telephony": "Telefonie", "merchantCategory.tobacco": "Tabak", "merchantCategory.tollRoad": "Tolweg", "merchantCategory.tourismAttractionsAmusement": "Toerisme, Attracties & Amusement", "merchantCategory.towing": "Sleepdiensten", "merchantCategory.toys": "Speelgoed", "merchantCategory.toysHobbies": "Speelgoed & Hobby's", "merchantCategory.trafficFine": "Verkeersboete", "merchantCategory.train": "<PERSON><PERSON><PERSON>", "merchantCategory.travelAgency": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.tv": "Tv", "merchantCategory.tvRadioStreaming": "TV, Radio & Streaming", "merchantCategory.utilities": "Nutsvoorzieningen", "merchantCategory.waterTransport": "Vervoer over water", "merchantCategory.wholesaleClubs": "Groothandelsclubs", "metaMask.subtitle": "<PERSON><PERSON><PERSON> MetaMask-modus in om alle MetaMask-verbindingen om te leiden naar Zeal. <PERSON>s je in dApps op MetaMask klikt, maak je verbinding met <PERSON>eal.", "metaMask.title": "Kun je niet verbinden met <PERSON><PERSON>?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Open je bank-app", "monerium-bank-deposit.buttet-point.receive-crypto": "Ontvang digitale EUR", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Verstuur {fiatCurrencyCode} naar je account", "monerium-bank-deposit.deposit-account-country": "Land", "monerium-bank-deposit.header": "{fullName}''s perso<PERSON><PERSON><PERSON><PERSON> account", "monerium-bank-details.account-name": "<PERSON><PERSON>", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT gekopieerd", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT gekopieerd", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN gekopieerd", "monerium-bank-details.to-wallet": "<PERSON><PERSON>", "monerium-bank-details.transfer-fee": "Overschrijvingskosten", "monerium-bank-transfer.enable-card.bullet-1": "Voltooi identiteitsverificatie", "monerium-bank-transfer.enable-card.bullet-2": "Ontvang persoonlijke accountgegevens", "monerium-bank-transfer.enable-card.bullet-3": "<PERSON>ort vanaf je bankrekening", "monerium-card-delay-relay.success.cta": "Sluiten", "monerium-card-delay-relay.success.subtitle": "Om veiligheidsredenen duurt het 3 minuten om wijzigingen in kaartinstellingen te verwerken.", "monerium-card-delay-relay.success.title": "<PERSON>m over 3 min terug om verder te gaan.", "monerium-deposit.account-details-info-popup.bullet-point-1": "Elke {fiatCurrencyCode} die je naar dit account stuurt, wordt automatisch omgezet in {cryptoCurrencyCode} tokens op {cryptoCurrencyChain} Chain en naar je portemonnee verzonden", "monerium-deposit.account-details-info-popup.bullet-point-2": "STUUR ALLEEN {fiatCurrencyCode} ({fiatCurrencySymbol}) naar je account", "monerium-deposit.account-details-info-popup.title": "<PERSON>", "monerium.check_order_status.sending": "Verzenden...", "monerium.not-eligible.cta": "Terug", "monerium.not-eligible.subtitle": "Monerium kan geen account voor je openen. Selecteer een andere aanbieder.", "monerium.not-eligible.title": "<PERSON><PERSON><PERSON> een andere aan<PERSON>der", "monerium.setup-card.cancel": "<PERSON><PERSON><PERSON>", "monerium.setup-card.continue": "Doorgaan", "monerium.setup-card.create_account": "Account aan<PERSON>ken", "monerium.setup-card.login": "Inloggen bij Gnosis Pay", "monerium.setup-card.subtitle": "Maak een Gnosis Pay-account aan of log in om directe bankstortingen mogelijk te maken.", "monerium.setup-card.subtitle_personal_account": "Open binnen enkele minuten je persoonlijke account bij Gnosis Pay:", "monerium.setup-card.title": "Bankstortingen inschakelen", "moneriumDepositSuccess.goToWallet": "Naar wallet", "moneriumDepositSuccess.title": "{symbol} ontvangen", "moneriumInfo.fees": "Je betaalt 0% kosten", "moneriumInfo.registration": "Monerium is een geautoriseerde en gereguleerde Instelling voor Elektronisch Geld onder de IJslandse wet op elektronisch geld nr. 17/2013 <link>Meer informatie</link>", "moneriumInfo.selfCustody": "Het digitale geld dat je ontvangt, beheer je zelf en niemand anders heeft controle over je bezittingen", "moneriumWithdrawRejected.supportText": "We konden je overschrijving niet voltooien. <PERSON><PERSON>r het opnieuw en als het nog steeds niet werkt, <link>contacteer support.</link>", "moneriumWithdrawRejected.title": "Overschrijving teruggedraaid", "moneriumWithdrawRejected.tryAgain": "Opnieuw proberen", "moneriumWithdrawSuccess.supportText": "Het kan 24 uur duren voordat je{br}ontvanger het geld ontvangt", "moneriumWithdrawSuccess.title": "Verzonden", "monerium_enable_banner.text": "Activeer bankoverschrijvingen", "monerium_error_address_re_link_required.title": "Wallet moet opnieuw aan Monerium worden gekoppeld", "monerium_error_duplicate_order.title": "Dubbele order", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "<PERSON><PERSON><PERSON><PERSON>", "mt-pelerin-fork.options.chf.short": "Direct en gratis met <PERSON> Pelerin", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Direct en gratis met Monerium", "mt-pelerin-fork.title": "Wat wil je storten?", "mtPelerinProviderInfo.fees": "Je betaalt 0% kosten", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd is aangesloten bij SO-FIT, een zelfregulerende organisatie die erkend is door de Zwitserse financiële autoriteit (FINMA) onder de anti-witwaswet. <link>Meer informatie</link>", "mtPelerinProviderInfo.selfCustody": "Het digitale geld dat je ontvangt, beheer je zelf en niemand anders heeft controle over je vermogen", "network-fee-widget.title": "<PERSON><PERSON>", "network.edit.verifying_rpc": "RPC verifiëren", "network.editRpc.predefined_network_info.subtitle": "Net als een VPN gebruikt Zeal RPC's die voorkomen dat je persoonlijke gegevens worden gevolgd.{br}{br}De standaard RPC's van Zeal zijn betrouwbare, in de praktijk geteste RPC-providers.", "network.editRpc.predefined_network_info.title": "Zeal privacy-RPC", "network.filter.update_rpc_success": "RPC-node opgeslagen", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "Alle netwerken", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "Netwerken toevoegen", "networks.filter.add_modal.chain_list.subtitle": "Voeg alle EVM-netwerken toe", "networks.filter.add_modal.chain_list.title": "Ga naar Chainlist", "networks.filter.add_modal.dapp_tip.subtitle": "In je favoriete d<PERSON>pps, wissel naar het EVM-netwerk dat je wilt gebruiken. <PERSON>eal vraagt dan of je het wilt toevoegen.", "networks.filter.add_modal.dapp_tip.title": "Of voeg een netwerk toe vanuit een dApp", "networks.filter.add_networks.subtitle": "Alle EVM-netwerken ondersteund", "networks.filter.add_networks.title": "Netwerken toevoegen", "networks.filter.add_test_networks.title": "<PERSON><PERSON><PERSON>", "networks.filter.tab.netwokrs": "Netwerken", "networks.filter.testnets.title": "<PERSON><PERSON><PERSON>", "nft.widget.emptystate": "<PERSON><PERSON> verz<PERSON> in portemonnee", "nft_collection.change_account_picture.subtitle": "Weet je zeker dat je je profielfoto wilt bijwerken?", "nft_collection.change_account_picture.title": "Profielfoto bijwerken naar NFT", "nfts.allNfts.pricingPopup.description": "Prijzen gebaseerd op laatste transactie.", "nfts.allNfts.pricingPopup.title": "<PERSON><PERSON><PERSON><PERSON> van verzamelobjecten", "no-passkeys-found.modal.cta": "Sluiten", "no-passkeys-found.modal.subtitle": "We kunnen geen Zeal-passkeys op dit apparaat detecteren. Zorg ervoor dat je bent ingelogd op het cloud-account dat je hebt gebruikt om je Smart Wallet aan te maken.", "no-passkeys-found.modal.title": "<PERSON>n passkeys gevonden", "notValidEmail.title": "<PERSON><PERSON> geldig e-mailadres", "notValidPhone.title": "Dit is geen geldig telefoonnummer", "notification-settings.title": "Meldingsinstellingen", "notification-settings.toggles.active-wallets": "<PERSON><PERSON><PERSON>", "notification-settings.toggles.bank-transfers": "Bankoverschrijvingen", "notification-settings.toggles.card-payments": "<PERSON><PERSON><PERSON>aling<PERSON>", "notification-settings.toggles.readonly-wallets": "Alleen-lezen port<PERSON>", "ntft.groupHeader.text": "Verzamelobjecten", "on_ramp.crypto_completed": "Voltooid", "on_ramp.fiat_completed": "Voltooid", "onboarding-widget.subtitle.card_created_from_order.left": "Visa-kaart", "onboarding-widget.subtitle.card_created_from_order.right": "<PERSON><PERSON>", "onboarding-widget.subtitle.card_order_ready.left": "Fysieke Visa-kaart", "onboarding-widget.subtitle.default": "Bankoverschrijvingen & Visa-kaart", "onboarding-widget.title.card-order-in-progress": "Kaartbestelling voortzetten", "onboarding-widget.title.card_created_from_order": "<PERSON><PERSON> is verzonden", "onboarding-widget.title.kyc_approved": "Overschrijvingen & kaart k<PERSON>ar", "onboarding-widget.title.kyc_failed": "Account niet m<PERSON>jk", "onboarding-widget.title.kyc_not_started": "Installatie voortzetten", "onboarding-widget.title.kyc_started_documents_requested": "Verificatie voltooien", "onboarding-widget.title.kyc_started_resubmission_requested": "Verificatie opnieuw proberen", "onboarding-widget.title.kyc_started_verification_in_progress": "Identiteit verifiëren", "onboarding.loginOrCreateAccount.amountOfAssets": "$ 10+ mld. aan activa", "onboarding.loginOrCreateAccount.cards.subtitle": "<PERSON><PERSON> in bepaalde regio's. Door verder te gaan, aan<PERSON><PERSON> je onze <Terms>Voorwaarden</Terms> & <PrivacyPolicy>Privacybeleid</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Visa-<PERSON><PERSON> met hoge{br}rendementen en geen kosten", "onboarding.loginOrCreateAccount.createAccount": "Account aan<PERSON>ken", "onboarding.loginOrCreateAccount.earn.subtitle": "Rendementen variëren; kapit<PERSON> loopt risico. Door verder te gaan, aan<PERSON><PERSON> je onze <Terms>Voorwaarden</Terms> & <PrivacyPolicy>Privacybeleid</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "<PERSON>en {percent} per jaar{br}Vertrouwd door {currencySymbol}5+ mld.", "onboarding.loginOrCreateAccount.earningPerYear": "Verdien {percent}{br}per jaar", "onboarding.loginOrCreateAccount.login": "Inloggen", "onboarding.loginOrCreateAccount.trading.subtitle": "Kapitaal loopt risico. Door verder te gaan, a<PERSON><PERSON><PERSON> je onze <Terms>Voorwaarden</Terms> & <PrivacyPolicy>Privacybeleid</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Investeer in alles,{br}van BTC tot S&P", "onboarding.loginOrCreateAccount.trustedBy": "Digitale geldmarkten{br}Vertrouwd door {assets}", "onboarding.wallet_stories.close": "Sluiten", "onboarding.wallet_stories.previous": "Vorige", "order-earn-deposit-bridge.deposit": "Storting", "order-earn-deposit-bridge.into": "In", "otpIncorrectMessage": "Bevestigingscode is onjuist", "passkey-creation-not-possible.modal.close": "Sluiten", "passkey-creation-not-possible.modal.subtitle": "We konden geen passkey aanmaken voor je wallet. Zorg ervoor dat je apparaat passkeys ondersteunt en probeer het opnieuw. <link>Neem contact op met support</link> als het probleem aanhoudt.", "passkey-creation-not-possible.modal.title": "Kan geen passkey aanmaken", "passkey-not-supported-in-mobile-browser.modal.cta": "Download Zeal", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Smart Wallets worden niet ondersteund in mobiele browsers.", "passkey-not-supported-in-mobile-browser.modal.title": "Download de Zeal-app om door te gaan", "passkey-recovery.recovering.deploy-signer.loading-text": "<PERSON>key verifiëren", "passkey-recovery.recovering.loading-text": "<PERSON><PERSON><PERSON>", "passkey-recovery.recovering.signer-not-found.subtitle": "We konden je passkey niet koppelen aan een actieve portemonnee. Als je geld in je portemonnee hebt, neem dan contact op met het Zeal-team voor ondersteuning.", "passkey-recovery.recovering.signer-not-found.title": "<PERSON><PERSON>", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "<PERSON><PERSON><PERSON> and<PERSON> passkey", "passkey-recovery.select-passkey.banner.subtitle": "Log in op het juiste account. Passkeys zijn accountspecifiek.", "passkey-recovery.select-passkey.banner.title": "Zie je de passkey van je portemonnee niet?", "passkey-recovery.select-passkey.continue": "Selecteer passkey", "passkey-recovery.select-passkey.subtitle": "Selecteer de passkey die aan je portemonnee is gekoppeld om weer toegang te krijgen.", "passkey-recovery.select-passkey.title": "Selecteer passkey", "passkey-story_1.subtitle": "Met een <PERSON> Wallet betaal je netwerkkosten met de meeste tokens. Maak je dus geen zorgen over de transactiekosten.", "passkey-story_1.title": "<PERSON><PERSON> z<PERSON>gen om netwerkkosten: betaal met de meeste tokens", "passkey-story_2.subtitle": "Gebouwd op de toonaangevende smart contracts van Safe, die meer dan $100 miljard beveiligen in meer dan 20 miljoen wallets.", "passkey-story_2.title": "Beveiligd door Safe", "passkey-story_3.subtitle": "Smart Wallets werken op de belangrijkste Ethereum-compatibele netwerken. Controleer de ondersteunde netwerken voordat je activa verstuurt.", "passkey-story_3.title": "Belangrijkste EVM-netwerken ondersteund", "password.add.header": "Wachtwoord aanmaken", "password.add.includeLowerAndUppercase": "Kleine letters en hoofdletters", "password.add.includesNumberOrSpecialChar": "<PERSON><PERSON> c<PERSON> of symbool", "password.add.info.subtitle": "We sturen je wachtwoord niet naar onze servers en maken er geen back-up van", "password.add.info.t_and_c": "Door verder te gaan, a<PERSON><PERSON><PERSON> je onze <Terms>Voorwaarden</Terms> & <PrivacyPolicy>Privacybeleid</PrivacyPolicy>", "password.add.info.title": "Je wachtwoord blijft op dit apparaat", "password.add.inputPlaceholder": "Wachtwoord aanmaken", "password.add.shouldContainsMinCharsCheck": "10+ tekens", "password.add.subheader": "Je gebruikt je wachtwoord om Z<PERSON> te ontgrendelen", "password.add.success.title": "Wachtwoord aangemaakt 🔥", "password.confirm.header": "Bevestig wachtwoord", "password.confirm.passwordDidNotMatch": "Wachtwoorden moeten overeenkomen", "password.confirm.subheader": "<PERSON><PERSON>r je wachtwoord nog een keer in", "password.create_pin.subtitle": "Deze toegangscode vergrendelt de Zeal-app", "password.create_pin.title": "Maak je toegangscode aan", "password.enter_pin.title": "<PERSON><PERSON>r toegangscode in", "password.incorrectPin": "<PERSON>ju<PERSON><PERSON>", "password.pin_is_not_same": "Toegangscode komt niet overeen", "password.placeholder.enter": "<PERSON><PERSON><PERSON> wacht<PERSON> in", "password.placeholder.reenter": "<PERSON><PERSON><PERSON> wachtwoord opnieuw in", "password.re_enter_pin.subtitle": "V<PERSON>r dezelfde toegangscode opnieuw in", "password.re_enter_pin.title": "Bevestig toegangscode", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "In behandeling", "pending-send.details.pending": "In behandeling", "pending-send.details.processing": "Verwerken", "permit-info.modal.description": "Permits zijn ve<PERSON> die, na ondertekening, apps toestaan om namens jou je tokens te verplaatsen, bijvoorbeeld voor een swap.{br}Permits zi<PERSON> met <PERSON><PERSON><PERSON><PERSON><PERSON>, maar kosten je geen netwerkkosten om te ondertekenen.", "permit-info.modal.title": "Wat zijn Permits?", "permit.edit-expiration": "Bewerk {currency} vervaltijd", "permit.edit-limit": "Bewerk {currency} bestedingslimiet", "permit.edit-modal.expiresIn": "<PERSON><PERSON><PERSON><PERSON> over…", "permit.expiration-warning": "{currency} vervaltijdwaarschuwing", "permit.expiration.info": "{currency} vervaltijdinfo", "permit.expiration.never": "Nooit", "permit.spend-limit.info": "{currency} bestedingslimietinfo", "permit.spend-limit.warning": "{currency} waarschuwing bestedingslimiet", "phoneNumber.title": "telefoonnummer", "physicalCardOrderFlow.cardOrdered": "<PERSON><PERSON>", "physicalCardOrderFlow.city": "Stad", "physicalCardOrderFlow.orderCard": "<PERSON><PERSON> bestellen", "physicalCardOrderFlow.postcode": "Postcode", "physicalCardOrderFlow.shippingAddress.subtitle": "Waar je kaart naartoe wordt gestuurd", "physicalCardOrderFlow.shippingAddress.title": "Verzendadres", "physicalCardOrderFlow.street": "Straat", "placeholderDapps.1inch.description": "Wissel via de beste routes", "placeholderDapps.aave.description": "Tokens uitlenen en lenen", "placeholderDapps.bungee.description": "Bridge netwerken via de beste routes", "placeholderDapps.compound.description": "Tokens uitlenen en lenen", "placeholderDapps.cowswap.description": "Wissel tegen de beste tarieven op Gnosis", "placeholderDapps.gnosis-pay.description": "<PERSON><PERSON><PERSON> je <PERSON>-kaart", "placeholderDapps.jumper.description": "Bridge netwerken via de beste routes", "placeholderDapps.lido.description": "Stake ETH voor meer ETH", "placeholderDapps.monerium.description": "eMoney en bankoverschrijvingen", "placeholderDapps.odos.description": "Wissel via de beste routes", "placeholderDapps.stargate.description": "Bridge of stake voor <14% APY", "placeholderDapps.uniswap.description": "<PERSON><PERSON> exchanges", "pleaseAllowNotifications.cardPayments": "<PERSON><PERSON><PERSON>aling<PERSON>", "pleaseAllowNotifications.customiseInSettings": "Aanpassen in instellingen", "pleaseAllowNotifications.enable": "Inschakelen", "pleaseAllowNotifications.forWalletActivity": "Voor portemonnee-activiteit", "pleaseAllowNotifications.title": "Ontvang <PERSON>-meldingen", "pleaseAllowNotifications.whenReceivingAssets": "<PERSON><PERSON><PERSON> on<PERSON><PERSON><PERSON><PERSON> van tego<PERSON>n", "portfolio.quick-actions.add_funds": "Bijvullen", "portfolio.quick-actions.buy": "<PERSON><PERSON>", "portfolio.quick-actions.deposit": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.send": "<PERSON><PERSON><PERSON><PERSON>", "portfolio.view.lastRefreshed": "Vernieuwd {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Waardeer je testnet-AVAX op", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Ga naar Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "Waardeer je testnet-BNB op", "portfolio.view.topupTestNet.BscTestnet.secondary": "Ga naar Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Waar<PERSON>er je testnet-SepETH op", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Ga naar Sepolia Faucet", "portfolio.view.topupTestNet.FantomTestnet.primary": "Waardeer je testnet-FTM op", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Ga naar Faucet", "privateKeyConfirmation.banner.subtitle": "<PERSON><PERSON><PERSON> met je priv<PERSON><PERSON><PERSON><PERSON> heeft toegang tot je wallet en geld. Alleen oplichters vragen ernaar.", "privateKeyConfirmation.banner.title": "<PERSON><PERSON><PERSON><PERSON> risico's", "privateKeyConfirmation.title": "DEEL NOOIT je privésleutel met i<PERSON>d", "rating-request.not-now": "Niet nu", "rating-request.title": "Zou je <PERSON> a<PERSON>?", "receive_funds.address-text": "<PERSON>t is je unieke walletadres. Je kunt het veilig met andere<PERSON> delen.", "receive_funds.copy_address": "<PERSON><PERSON><PERSON>", "receive_funds.network-warning.eoa.subtitle": "<link>Bekijk netwerken</link>. Activa die op niet-EVM-netwerken worden verzonden, gaan verloren.", "receive_funds.network-warning.eoa.title": "Alle op Ethereum-gebaseerde netwerken worden ondersteund", "receive_funds.network-warning.scw.subtitle": "<link>Bekijk netwerken</link>. Activa die op andere netwerken worden verzonden, gaan verloren.", "receive_funds.network-warning.scw.title": "Belangrijk: gebruik alleen ondersteunde netwerken", "receive_funds.scan_qr_code": "Scan een QR-code", "receiving.in.days": "Ontvangst over {days}d", "receiving.this.week": "Ontvangst deze week", "receiving.today": "Ontvangst vandaag", "reference.error.maximum_number_of_characters_exceeded": "Te veel tekens", "referral-code.placeholder": "Plak uitnodigingslink", "referral-code.subtitle": "Klik nogmaals op de link van je vriend of plak de link hieronder. We willen zeker weten dat je je beloningen krijgt.", "referral-code.title": "Heeft een vriend je {bReward} gestuurd?", "rekyc.verification_deadline.subtitle": "Voltooi de verificatie binnen {daysUntil} dagen om je kaart te blijven geb<PERSON>iken.", "rekyc.verification_required.subtitle": "Voltooi de verificatie om je kaart te blijven gebruiken.", "reminder.fund": "💸 Geld toevoegen — verdien direct 6%", "reminder.onboarding": "🏁 Voltooi de installatie — verdien 6% op je stortingen", "remove-owner.confirmation.subtitle": "Uit veiligheidsoverwegingen duurt het 3 minuten om wijzigingen in instellingen te verwerken. Gedurende deze tijd wordt je kaart tijdelijk geblokkeerd en zijn betalingen niet mogelijk.", "remove-owner.confirmation.title": "Je kaart wordt 3 min. geblok<PERSON><PERSON> tijdens het bijwerken van de instellingen", "restore-smart-wallet.wallet-recovered": "<PERSON><PERSON><PERSON>teld", "rewardClaimCelebration.claimedTitle": "Beloningen al geclaimd", "rewardClaimCelebration.subtitle": "Voor het uitnodigen van vrienden", "rewardClaimCelebration.title": "Je hebt verdiend", "rewards-warning.subtitle": "Het verwijderen van dit account pauzeert de toegang tot gekoppelde beloningen. Je kunt het account altijd herstellen om ze te claimen.", "rewards-warning.title": "Je verliest toegang tot je beloningen", "rewards.copiedInviteLink": "Uitnodigingslink gekopieerd", "rewards.createAccount": "<PERSON><PERSON><PERSON> uitnodi<PERSON>link", "rewards.header.subtitle": "We sturen {aR<PERSON>ard} naar jou en {bReward} naar je vriend, wanneer die {bSpendLimitReward} besteedt.", "rewards.header.title": "Krijg {amountA}{br}Geef {amountB}", "rewards.sendInvite": "Verstuur uitnodiging", "rewards.sendInviteTip": "Kies een vriend en we geven die {bAmount}", "route.fees": "<PERSON><PERSON> {fees}", "routesNotFound.description": "De wisselroute voor de {from}-{to} netwerkcombinatie is niet be<PERSON><PERSON>.", "routesNotFound.title": "<PERSON>n wisselroute beschik<PERSON>ar", "rpc.OrderBuySignMessage.subtitle": "Via Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Via Swaps.IO", "rpc.addCustomNetwork.addNetwork": "Voeg toe", "rpc.addCustomNetwork.chainId": "Chain ID", "rpc.addCustomNetwork.nativeToken": "Eigen token", "rpc.addCustomNetwork.networkName": "Netwerknaam", "rpc.addCustomNetwork.operationDescription": "Staat deze website toe een netwerk aan je portemonnee toe te voegen. Zeal kan de veiligheid van aangepaste netwerken niet controleren, zorg dat je de risico's begrijpt.", "rpc.addCustomNetwork.rpcUrl": "RPC-URL", "rpc.addCustomNetwork.subtitle": "Via {name}", "rpc.addCustomNetwork.title": "Netwerk toevoegen", "rpc.send_token.network_not_supported.subtitle": "We werken eraan om overschrijvingen op dit netwerk mogelijk te maken. Bedankt voor je geduld 🙏", "rpc.send_token.network_not_supported.title": "Netwerk binnenkort beschikbaar", "rpc.send_token.send_or_receive.settings": "Instellingen", "rpc.sign.accept": "Accept<PERSON><PERSON>", "rpc.sign.cannot_parse_message.body": "We konden dit bericht niet decoderen. Accepteer dit verzoek alleen als je deze app vertrouwt.{br}{br}Berichten kunnen gebruikt worden om je in te loggen bij een app, maar kunnen apps ook controle geven over je tokens.", "rpc.sign.cannot_parse_message.header": "Ga voorzichtig te werk", "rpc.sign.import_private_key": "Sleutels importeren", "rpc.sign.subtitle": "<PERSON><PERSON> {name}", "rpc.sign.title": "Ondertekenen", "safe-creation.success.title": "<PERSON><PERSON> a<PERSON>", "safe-safety-checks-popup.title": "Veiligheidscontroles transactie", "safetyChecksPopup.title": "Veiligheidscontroles site", "scan_qr_code.description": "Scan wallet-QR of verbind met een app", "scan_qr_code.show_qr_code": "Toon mijn QR-code", "scan_qr_code.tryAgain": "<PERSON><PERSON><PERSON> opnieuw", "scan_qr_code.unlockCamera": "Ontgrendel camera", "screen-lock-missing.modal.close": "Sluiten", "screen-lock-missing.modal.subtitle": "Je apparaat vereist een schermvergrendeling om passkeys te gebruiken. Stel een schermvergrendeling in en probeer het opnieuw.", "screen-lock-missing.modal.title": "Schermvergrendeling ontbreekt", "seedConfirmation.banner.subtitle": "<PERSON><PERSON><PERSON> met je geheime zin heeft toegang tot je wallet en geld. Alleen oplichters vragen ernaar.", "seedConfirmation.title": "DEEL NOOIT je geheime zin met iemand", "select-active-owner.subtitle": "Je hebt meerdere wallets aan je kaart gekoppeld. Selecteer er een om met Zeal te verbinden. Je kunt op elk moment wisselen.", "select-active-owner.title": "Selecteer wallet", "select-card.title": "Selecteer kaart", "select-crypto-currency-title": "Selecteer token", "select-token.title": "Selecteer token", "selectEarnAccount.chf.description.steps": "· Geld opnemen 24/7, geen vastzetperiodes {br}· Rente groeit elke seconde aan {br}· Extra beschermde stortingen in <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} per jaar in CHF", "selectEarnAccount.eur.description.steps": "· Geld 24/7 opne<PERSON>, geen blokkades {br}· Rente groeit elke seconde aan {br}· Extra beschermde leningen met <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} per jaar in EUR", "selectEarnAccount.subtitle": "Je kunt dit altijd wijzigen", "selectEarnAccount.title": "Selecteer valuta", "selectEarnAccount.usd.description.steps": "· Geld 24/7 opnemen, geen blokkades {br}· Rente groeit elke seconde aan {br}· Extra beschermde deposito's in <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} per jaar in USD", "selectEarnAccount.zero.description_general": "Digitaal geld aanhouden zonder rente te verdienen", "selectEarnAccount.zero.title": "0% per jaar", "selectRechargeThreshold.button.enterAmount": "<PERSON><PERSON>r bedrag in", "selectRechargeThreshold.button.setTo": "Instellen op {amount}", "selectRechargeThreshold.description.line1": "<PERSON><PERSON> je kaartsaldo onder {amount} komt, wordt het automatisch opgewaardeerd tot {amount} vanuit je Earn-rekening.", "selectRechargeThreshold.description.line2": "<PERSON>en lager doelbedrag betekent meer op je Earn-rekening (die 3% oplevert). Je kunt dit altijd wijzigen.", "selectRechargeThreshold.title": "<PERSON><PERSON><PERSON> ka<PERSON> instellen", "select_currency_to_withdraw.select_token_to_withdraw": "Selecteer token om op te nemen", "send-card-token.form.send": "Verzenden", "send-card-token.form.send-amount": "Opwaardeerbedrag", "send-card-token.form.title": "<PERSON><PERSON> toe<PERSON>n aan kaart", "send-card-token.form.to-address": "<PERSON><PERSON>", "send-safe-transaction.network-fee-widget.error": "Je hebt {amount} nodig of kies een andere token", "send-safe-transaction.network-fee-widget.no-fee": "<PERSON><PERSON> kosten", "send-safe-transaction.network-fee-widget.title": "Netwerkkosten", "send-safe-transaction.network_fee_widget.title": "Netwerkkost", "send.banner.fees": "Je hebt {amount} meer {currency} nodig om de kosten te betalen", "send.banner.toAddressNotSupportedNetwork.subtitle": "<PERSON> port<PERSON><PERSON> van de ontvanger ondersteunt geen {network}. Wissel naar een ondersteunde token.", "send.banner.toAddressNotSupportedNetwork.title": "Netwerk niet ondersteund voor ontvanger", "send.banner.walletNotSupportedNetwork.subtitle": "Smart Wallets kunnen geen transacties doen op {network}. Wissel naar een ondersteunde token.", "send.banner.walletNotSupportedNetwork.title": "Tokennetwerk niet ondersteund", "send.empty-portfolio.empty-state": "We hebben geen tokens gevonden", "send.empty-portfolio.header": "Tokens", "send.titile": "Verzenden", "sendLimit.success.subtitle": "Tot dan geldt je huidige limiet.", "sendLimit.success.title": "<PERSON><PERSON> wij<PERSON>ing duurt 3 minuten", "send_crypto.form.disconnected.cta.addFunds": "Geld toe<PERSON>n", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Wissel naar {network}", "send_crypto.form.disconnected.label": "Over te schrijven bedrag", "send_to.qr_code.description": "Scan een QR-code om te versturen", "send_to.qr_code.title": "Scan QR-code", "send_to_card.header": "Verzenden naar kaartadres", "send_to_card.select_sender.add_wallet": "Wallet toevoegen", "send_to_card.select_sender.header": "Selecteer afzender", "send_to_card.select_sender.search.default_placeholder": "<PERSON><PERSON> of ENS", "send_to_card.select_sender.show_card_address_button_description": "<PERSON><PERSON> ka<PERSON>", "send_token.form.select-address": "Selecteer adres", "send_token.form.send-amount": "Verzendbedrag", "send_token.form.title": "<PERSON><PERSON><PERSON><PERSON>", "setLimit.amount.error.zero_amount": "Je kunt geen betalingen doen", "setLimit.error.max_limit_reached": "Limiet instellen op max {amount}", "setLimit.error.same_as_current_limit": "Gelijk aan huidige limiet", "setLimit.placeholder": "Huidig: {amount}", "setLimit.submit": "Limiet instellen", "setLimit.submit.error.amount_required": "<PERSON><PERSON>r bedrag in", "setLimit.subtitle": "<PERSON><PERSON> is je dagelijkse bestedingslimiet.", "setLimit.title": "Dagelijkse bestedingslimiet instellen", "settings.accounts": "Accounts", "settings.accountsSeeAll": "<PERSON>es weergeven", "settings.addAccount": "<PERSON><PERSON><PERSON>", "settings.card": "Kaartinstellingen", "settings.connections": "App-verbindingen", "settings.currency": "Standaardvaluta", "settings.default_currency_selector.title": "Valuta", "settings.discord": "Discord", "settings.experimentalMode": "Experimentele modus", "settings.experimentalMode.subtitle": "Test nieuwe functies", "settings.language": "Taal", "settings.lockZeal": "Zeal vergrendelen", "settings.notifications": "Meldingen", "settings.open_expanded_view": "Uitgebreide weergave openen", "settings.privacyPolicy": "Privacybeleid", "settings.settings": "Instellingen", "settings.termsOfUse": "Gebruiksvoorwaarden", "settings.twitter": "𝕏 / Twitter", "settings.version": "Versie {version} env: {env}", "setup-card.confirmation": "Virt<PERSON><PERSON> kaart a<PERSON>n", "setup-card.confirmation.subtitle": "Doe online betalingen en voeg toe aan je {type} portemonnee voor contactloos betalen.", "setup-card.getCard": "<PERSON><PERSON>", "setup-card.order.physicalCard": "<PERSON>ys<PERSON><PERSON> kaart", "setup-card.order.physicalCard.steps": "· Een fysieke VISA Gnosis Pay {br}· Verzending duurt tot 3 weken {br}· Voor betalingen en geldautomaten. {br}· Apple/Google Wallet (indien ondersteund)", "setup-card.order.subtitle1": "Je kunt meerdere kaarten tegelijk gebruiken", "setup-card.order.title": "Welk type kaart?", "setup-card.order.virtualCard": "<PERSON>irt<PERSON><PERSON>", "setup-card.order.virtual_card.steps": "· Digitale VISA Gnosis Pay {br}· Direct te gebruiken voor online betalingen {br}· Apple/Google Wallet (indien ondersteund)", "setup-card.orderCard": "<PERSON><PERSON> bestellen", "setup-card.virtual-card": "Virt<PERSON><PERSON> kaart a<PERSON>n", "setup.notifs.fakeAndroid.title": "Meldingen voor betalingen en inkomende overschrijvingen", "setup.notifs.fakeIos.subtitle": "Zeal kan je een melding sturen als je geld ont<PERSON>gt of uitgeeft met je <PERSON>-kaart. Je kunt dit later wijzigen.", "setup.notifs.fakeIos.title": "Meldingen voor betalingen en inkomende overschrijvingen", "sign.PermitAllowanceItem.spendLimit": "Bestedingslimiet", "sign.ledger.subtitle": "We hebben het transactieverzoek naar je hardware wallet gestuurd. Ga daar verder.", "sign.ledger.title": "Hardware wallet ondertekenen", "sign.passkey.subtitle": "Je <PERSON> zou je moeten vragen om te ondertekenen met de passkey die bij deze wallet hoort. Ga daar verder.", "sign.passkey.title": "Selecteer passkey", "signal_aborted_for_uknown_reason.title": "Netwerkverzoek geannuleerd", "simulatedTransaction.BridgeTrx.info.title": "Bridge", "simulatedTransaction.CardTopUp.info.title": "<PERSON><PERSON> toe<PERSON>n aan kaart", "simulatedTransaction.CardTopUpTrx.info.title": "<PERSON><PERSON> toe<PERSON>n aan kaart", "simulatedTransaction.NftCollectionApproval.approve": "NFT-<PERSON><PERSON> go<PERSON>", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "<PERSON><PERSON><PERSON><PERSON> aan kaart", "simulatedTransaction.OrderEarnDepositBridge.title": "Storten in Earn", "simulatedTransaction.P2PTransaction.info.title": "Verzenden", "simulatedTransaction.PermitSignMessage.title": "Vergunning", "simulatedTransaction.SingleNftApproval.approve": "NFT goedkeuren", "simulatedTransaction.UnknownSignMessage.title": "Ondertekenen", "simulatedTransaction.Withdrawal.info.title": "Opname", "simulatedTransaction.approval.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.approve.info.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.p2p.info.account": "<PERSON>ar", "simulatedTransaction.p2p.info.unlabelledAccount": "Portemonnee zonder label", "simulatedTransaction.unknown.info.receive": "Ontvangen", "simulatedTransaction.unknown.info.send": "Verzenden", "simulatedTransaction.unknown.using": "Via {app}", "simulation.approval.modal.text": "<PERSON>neer je een goedkeuring accepteert, geef je een specifieke app/smart contract toestemming om je tokens of NFT's in toekomstige transacties te gebruiken.", "simulation.approval.modal.title": "Wat zijn goed<PERSON>uringen?", "simulation.approval.spend-limit.label": "Bestedingslimiet", "simulation.approve.footer.for": "Voor", "simulation.approve.unlimited": "Onbeperkt", "simulationNotAvailable.title": "On<PERSON>ende actie", "smart-wallet-activation-view.on": "Op", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1<PERSON><PERSON><PERSON> kan de toegang tot je portemon<PERSON> blokkeren", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Gebruik Apple of Google om Zeal veilig in te stellen", "smart-wallet.passkey-notice.title": "Vermijd 1Password", "spend-limits.high.modal.text": "Stel een bestedingslimiet in die in de buurt komt van het aantal tokens dat je daadwerkelijk zult gebruiken met een app of smart contract. Hoge limieten zijn riskant en kunnen het voor oplichters makkelijker maken om je tokens te stelen.", "spend-limits.high.modal.text_sign_message": "De bestedingslimiet moet dicht bij je werkelijke gebruik liggen. Hoge limieten zijn riskant en maken het makkelijker voor oplichters om je tokens te stelen.", "spend-limits.high.modal.title": "Hoge bestedingslimiet", "spend-limits.modal.text": "Een bestedingslimiet is het aantal tokens dat een app namens jou kan gebruiken. Je kunt deze limiet op elk moment wijzigen of verwijderen. Voor je veiligheid is het verstandig om bestedingslimieten in te stellen die in de buurt komen van het aantal tokens dat je da<PERSON><PERSON><PERSON><PERSON><PERSON> met een app zult gebruiken.", "spend-limits.modal.title": "Wat is een bestedingslimiet?", "spent-limit-info.modal.description": "Een bestedingslimiet bepaalt hoeveel tokens een app namens jou mag gebruiken. Je kunt dit altijd aanpassen. Houd limieten laag voor de veiligheid, passend bij je gebruik.", "spent-limit-info.modal.title": "Wat is een bestedingslimiet?", "sswaps-io.transfer-provider": "Transactieprovider", "storage.accountDetails.activateWallet": "Wallet activeren", "storage.accountDetails.changeWalletLabel": "Walletlabel wijzigen", "storage.accountDetails.deleteWallet": "Wallet verwijderen", "storage.accountDetails.setup_recovery_kit": "Herstelkit", "storage.accountDetails.showPrivateKey": "<PERSON>n privésle<PERSON>l", "storage.accountDetails.showWalletAddress": "<PERSON>n <PERSON>", "storage.accountDetails.smartBackup": "Back-up & herstel", "storage.accountDetails.viewSsecretPhrase": "Bekijk geheime zinsnede", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets?", "storage.manageAccounts.title": "Wallets", "submit-userop.progress.text": "Verzenden", "submit.error.amount_high": "Bedrag te hoog", "submit.error.amount_hight": "Bedrag te hoog", "submit.error.amount_low": "Bedrag te laag", "submit.error.amount_required": "<PERSON><PERSON>r bedrag in", "submit.error.maximum_number_of_characters_exceeded": "Verkort het aantal tekens", "submit.error.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "submit.error.recipient_required": "Ontvanger vereist", "submit.error.routes_not_found": "Routes niet gevonden", "submitSafeTransaction.monitor.title": "Transactieresultaat", "submitSafeTransaction.sign.title": "Transactieresultaat", "submitSafeTransaction.state.sending": "Verzenden", "submitSafeTransaction.state.sign": "Aanmaken", "submitSafeTransaction.submittingToRelayer.title": "Transactieresultaat", "submitTransaction.cancel": "<PERSON><PERSON><PERSON>", "submitTransaction.cancel.attemptingToStop": "Bezig met stoppen", "submitTransaction.cancel.failedToStop": "<PERSON><PERSON> mislukt", "submitTransaction.cancel.stopped": "Gestopt", "submitTransaction.cancel.title": "Transactievoorbeeld", "submitTransaction.failed.banner.description": "Het netwerk heeft deze transactie onverwacht geannuleerd. Probeer het opnieuw of neem contact op.", "submitTransaction.failed.banner.title": "Transactie mislukt", "submitTransaction.failed.execution_reverted.title": "Er is een fout opgetreden in de app", "submitTransaction.failed.execution_reverted_without_message.title": "Er is een fout opgetreden in de app", "submitTransaction.failed.out_of_gas.description": "Netwerk heeft transactie geannuleerd omdat er meer netwerkkosten werden gebruikt dan verwacht", "submitTransaction.failed.out_of_gas.title": "Netwerkfout", "submitTransaction.sign.title": "Transactieresultaat", "submitTransaction.speedUp": "Versnellen", "submitTransaction.state.addedToQueue": "Toege<PERSON><PERSON><PERSON> aan wacht<PERSON>j", "submitTransaction.state.addedToQueue.short": "In wachtrij", "submitTransaction.state.cancelled": "Gestopt", "submitTransaction.state.complete": "{currencyCode} toege<PERSON><PERSON>d aan <PERSON>", "submitTransaction.state.complete.subtitle": "Controleer j<PERSON>-portfolio", "submitTransaction.state.completed": "Voltooid", "submitTransaction.state.failed": "Mislukt", "submitTransaction.state.includedInBlock": "Opgenomen in block", "submitTransaction.state.includedInBlock.short": "In block", "submitTransaction.state.replaced": "Vervangen", "submitTransaction.state.sendingToNetwork": "Verzenden naar netwerk", "submitTransaction.stop": "Stoppen", "submitTransaction.submit": "<PERSON><PERSON>", "submitted-user-operation.state.bundled": "In wachtrij", "submitted-user-operation.state.completed": "Voltooid", "submitted-user-operation.state.failed": "Mislukt", "submitted-user-operation.state.pending": "Doorsturen", "submitted-user-operation.state.rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submittedTransaction.failed.title": "Transactie mislukt", "success_splash.card_activated": "<PERSON><PERSON>", "supportFork.give-feedback.title": "Feedback geven", "supportFork.itercom.description": "Zeal behandelt vragen over stortingen, Earn, beloningen of andere zaken", "supportFork.itercom.title": "<PERSON><PERSON><PERSON> over portemon<PERSON>", "supportFork.title": "<PERSON><PERSON><PERSON> bij", "supportFork.zendesk.subtitle": "Gnosis Pay behandelt vragen over kaartbetalingen, identiteitscontroles of terugbetalingen", "supportFork.zendesk.title": "Kaartbetalingen & identiteit", "supported-networks.ethereum.warning": "Hoge kosten", "supportedNetworks.networks": "Ondersteunde netwerken", "supportedNetworks.oneAddressForAllNetworks": "<PERSON>én ad<PERSON> voor alle netwerken", "supportedNetworks.receiveAnyAssets": "Ontvang activa van alle ondersteunde netwerken direct in je Zeal-<PERSON><PERSON><PERSON> met he<PERSON><PERSON><PERSON> <PERSON>.", "swap.form.error.no_routes_found": "Geen routes gevonden", "swap.form.error.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "swaps-io-details.bank.serviceProvider": "Dienstverlener", "swaps-io-details.details.processing": "Verwerken", "swaps-io-details.pending": "In behandeling", "swaps-io-details.rate": "Wisselkoers", "swaps-io-details.serviceProvider": "Dienstverlener", "swaps-io-details.transaction.from.processing": "Gestarte transactie", "swaps-io-details.transaction.networkFees": "Netwerkkosten", "swaps-io-details.transaction.state.completed-transaction": "Voltooide transactie", "swaps-io-details.transaction.state.started-transaction": "Gestarte transactie", "swaps-io-details.transaction.to.processing": "Voltooide transactie", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Storting wordt snel voltooid. Kinetex verwerkt je transactie nog.", "swapsIO.monitoring.awaitingLiqSend.title": "Vertraagd", "swapsIO.monitoring.awaitingRecive.title": "Doorsturen", "swapsIO.monitoring.awaitingSend.title": "In wachtrij", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Tokens zijn naar Kinetex verstuurd, maar worden snel teruggestort. Kinetex kon de bestemmingstransactie niet voltooien.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Tokens retourneren", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Tokens zijn niet overgeschreven door een onbekende fout. Probeer het opnieuw.", "swapsIO.monitoring.cancelledNoSlash.title": "To<PERSON>s geretourneerd", "swapsIO.monitoring.cancelledSlashed.subtitle": "Tokens zijn gere<PERSON>d. Kinetex kon de bestemmingstransactie niet voltooien.", "swapsIO.monitoring.cancelledSlashed.title": "To<PERSON>s geretourneerd", "swapsIO.monitoring.completed.title": "Voltooid", "taker-metadata.earn": "Verdien in digitale USD met Sky", "taker-metadata.earn.aave": "Verdien in digitale EUR met Aave", "taker-metadata.earn.aave.cashout24": "Direct opnemen, 24/7", "taker-metadata.earn.aave.trusted": "Vertrouwd met $ 27 mld., 2+ jaar", "taker-metadata.earn.aave.yield": "Ren<PERSON><PERSON> gro<PERSON> elke seconde", "taker-metadata.earn.chf": "Verdien in digitale CHF", "taker-metadata.earn.chf.cashout24": "Direct uitbetalen, 24/7", "taker-metadata.earn.chf.trusted": "Toevertrouwd met Fr. 28M", "taker-metadata.earn.chf.yield": "Ren<PERSON><PERSON> gro<PERSON> elke seconde aan", "taker-metadata.earn.usd.cashout24": "Direct opnemen, 24/7", "taker-metadata.earn.usd.trusted": "Vertrouwd met $ 10,7 mld., 5+ jaar", "taker-metadata.earn.usd.yield": "Ren<PERSON><PERSON> gro<PERSON> elke seconde", "test": "<PERSON><PERSON><PERSON>", "to.titile": "<PERSON>ar", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "Activa", "token.groupHeader.titleWithSum": "Activa {sum}", "token.hidden_tokens.page.title": "Verborgen tokens", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "Token toe<PERSON>n", "token.widget.cashback_empty": "Nog geen transacties", "token.widget.emptyState": "Geen tokens in portemonnee", "tokens.cash": "Geld", "top-up-card-from-earn-view.approve.for": "Voor", "top-up-card-from-earn-view.approve.into": "<PERSON>ar", "top-up-card-from-earn-view.swap.from": "<PERSON>", "top-up-card-from-earn-view.swap.to": "<PERSON>ar", "top-up-card-from-earn-view.withdraw.to": "<PERSON>ar", "top-up-card-from-earn.trx.title.approval": "<PERSON><PERSON><PERSON> go<PERSON>", "top-up-card-from-earn.trx.title.swap": "<PERSON><PERSON><PERSON><PERSON> aan kaart", "top-up-card-from-earn.trx.title.withdrawal": "<PERSON><PERSON><PERSON>", "topUpDapp.connectWallet": "Verbind portemon<PERSON>", "topup-fee-breakdown.bungee-fee": "Kosten externe provider", "topup-fee-breakdown.header": "Transactiekosten", "topup-fee-breakdown.network-fee": "Netwerkkost", "topup-fee-breakdown.total-fee": "Totale kosten", "topup.continue-in-wallet": "Ga verder in je portemonnee", "topup.send.title": "<PERSON><PERSON><PERSON><PERSON>", "topup.submit-transaction.close": "Sluiten", "topup.submit-transaction.sent-to-wallet": "Verstuur {amount}", "topup.to": "<PERSON><PERSON>", "topup.transaction.complete.close": "Sluiten", "topup.transaction.complete.try-again": "<PERSON><PERSON><PERSON> opnieuw", "transaction-request.nonce-too-low.modal.button-text": "Sluiten", "transaction-request.nonce-too-low.modal.text": "Een transactie met hetz<PERSON><PERSON> serienummer (nonce) is al voltooid, dus je kunt deze transactie niet meer indienen. Dit kan gebeuren als je transacties kort na elkaar uitvoert of als je een transactie probeert te versnellen of annuleren die al is voltooid.", "transaction-request.nonce-too-low.modal.title": "Transactie met dezelfde nonce is voltooid", "transaction-request.replaced.modal.button-text": "Sluiten", "transaction-request.replaced.modal.text": "We kunnen de status van deze transactie niet volgen. <PERSON><PERSON> is ofwel vervangen door een andere transactie, of de RPC-node heeft problemen.", "transaction-request.replaced.modal.title": "Kan transactiestatus niet vinden", "transaction.activity.details.modal.close": "Sluiten", "transaction.cancel_popup.cancel": "Nee, wacht", "transaction.cancel_popup.confirm": "Ja, stoppen", "transaction.cancel_popup.description": "<PERSON>m te stoppen, moet je een nieuwe netwerkkost betalen in plaats van de oorspronkelijke kost van {oldFee}", "transaction.cancel_popup.description_without_original": "<PERSON>m te stoppen, moet je een nieuwe netwerkkost betalen", "transaction.cancel_popup.not_supported.subtitle": "Transacties stoppen wordt niet ondersteund op {network}", "transaction.cancel_popup.not_supported.title": "<PERSON><PERSON>", "transaction.cancel_popup.stopping_fee": "Netwerkkost voor stoppen", "transaction.cancel_popup.title": "Transactie stoppen?", "transaction.in-progress": "Wordt verwerkt", "transaction.inProgress": "Wordt verwerkt", "transaction.speed_up_popup.cancel": "Nee, wacht", "transaction.speed_up_popup.confirm": "Ja, versnel", "transaction.speed_up_popup.description": "<PERSON>m te ve<PERSON>, betaal je een nieuwe netwerkkost in plaats van de oorspronkelijke kost van {amount}", "transaction.speed_up_popup.description_without_original": "<PERSON>m te ve<PERSON>, betaal je een nieuwe netwerkkost", "transaction.speed_up_popup.seed_up_fee_title": "Netwerkkost versnelling", "transaction.speed_up_popup.title": "Transactie versnellen?", "transaction.speedup_popup.not_supported.subtitle": "Het versnellen van transacties wordt niet ondersteund op {network}", "transaction.speedup_popup.not_supported.title": "<PERSON><PERSON>", "transaction.subTitle.failed": "Mislukt", "transactionDetails.cashback.not-qualified": "<PERSON><PERSON>", "transactionDetails.cashback.paid": "{amount} betaald", "transactionDetails.cashback.pending": "{amount} in behandeling", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "Onbekend", "transactionDetails.cashback_estimate": "Cashbackschatting", "transactionDetails.category": "Categorie", "transactionDetails.exchangeRate": "Wisselkoers", "transactionDetails.location": "Locatie", "transactionDetails.payment-approved": "<PERSON><PERSON> goedge<PERSON>urd", "transactionDetails.payment-declined": "<PERSON><PERSON> geweigerd", "transactionDetails.payment-reversed": "<PERSON>ling teruggedraaid", "transactionDetails.recharge.amountSentFromEarn.title": "<PERSON><PERSON> verzonden vanuit Earn", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "Opgewaardeerd naar kaart", "transactionDetails.recharge.rate.title": "<PERSON><PERSON>", "transactionDetails.recharge.transactionId.title": "Transactie-ID", "transactionDetails.refund": "Terugbetaling", "transactionDetails.reversal": "Terugboeking", "transactionDetails.transactionCurrency": "Transactievaluta", "transactionDetails.transactionId": "Transactie-ID", "transactionDetails.type": "Transactie", "transactionRequestWidget.approve.subtitle": "Voor {target}", "transactionRequestWidget.p2p.subtitle": "Aan {target}", "transactionRequestWidget.unknown.subtitle": "Via {target}", "transactionSafetyChecksPopup.title": "Veiligheidscontroles overschrijving", "transactions.main.activity.title": "Activiteit", "transactions.page.hiddenActivity.title": "Verborgen activiteit", "transactions.page.title": "Activiteit", "transactions.viewTRXHistory.emptyState": "Nog geen transacties", "transactions.viewTRXHistory.errorMessage": "Het <PERSON> van je transactiegeschiedenis is mislukt", "transactions.viewTRXHistory.hidden.emptyState": "<PERSON>n verborgen transacties", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Activiteit niet ondersteund voor testnetwerken", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Activiteit niet ondersteund voor testnetwerken{br}<link>Open in explorer</link>", "transfer_provider": "Dienstverlener overschrijving", "transfer_setup_with_different_wallet.subtitle": "Bankoverschrijvingen zijn <PERSON> met een andere portemonnee. Je kunt maar één portemonnee verbinden voor overschrijvingen.", "transfer_setup_with_different_wallet.swtich_and_continue": "<PERSON><PERSON><PERSON> en doorgaan", "transfer_setup_with_different_wallet.title": "<PERSON><PERSON><PERSON>", "tx-sent-to-wallet.button": "Sluiten", "tx-sent-to-wallet.subtitle": "Ga verder in {wallet}", "unblockProviderInfo.fees": "<PERSON> krijgt de laagst mogelijke kosten: 0% tot $5k per maand en 0,2% daarboven.", "unblockProviderInfo.registration": "Unblock is geregistreerd & geautoriseerd door FNTT om VASP-uitwisselings- en bewaardiensten te leveren, en is een geregistreerde MSB-provider bij US Fincen. <link>Meer informatie</link>", "unblockProviderInfo.selfCustody": "Het digitale geld dat je ontvangt, bewaar je zelf en niemand anders heeft controle over je activa.", "unblock_invalid_faster_payment_configuration.subtitle": "De opgegeven bankrekening ondersteunt geen Europese SEPA-overschrijvingen of UK Faster Payments. Geef een andere rekening op", "unblock_invalid_faster_payment_configuration.title": "And<PERSON> rekening vereist", "unknownTransaction.primaryText": "Kaarttransactie", "unsupportedCountry.subtitle": "Bankoverschrijvingen zijn nog niet besch<PERSON> in jouw land.", "unsupportedCountry.title": "<PERSON><PERSON> in {country}", "update-app-popup.subtitle": "De nieuwste update zit vol met oplossingen, functies en meer magie. Update naar de nieuwste versie en verbeter je Zeal-ervaring.", "update-app-popup.title": "Zeal-versie updaten", "update-app-popup.update-now": "Nu updaten", "user_associated_with_other_merchant.subtitle": "Deze portemonnee kan niet worden gebruikt voor bankoverschrijvingen. Gebruik een andere portemonnee of meld je op onze Discord voor ondersteuning en updates.", "user_associated_with_other_merchant.title": "Portemonnee kan niet worden gebruikt", "user_associated_with_other_merchant.try_with_another_wallet": "Andere <PERSON> proberen", "user_email_already_exists.subtitle": "Je hebt al bankoverschrij<PERSON><PERSON> ing<PERSON><PERSON> met een andere portemonnee. <PERSON><PERSON><PERSON> het op<PERSON><PERSON><PERSON> met de portemonnee die je eerder hebt gebruikt.", "user_email_already_exists.title": "Overschrij<PERSON><PERSON> ing<PERSON> met andere port<PERSON>", "user_email_already_exists.try_with_another_wallet": "Probeer andere wallet", "validation.invalid.iban": "Ongeldige IBAN", "validation.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validation.required.first_name": "<PERSON><PERSON><PERSON><PERSON> vereist", "validation.required.iban": "IBAN vereist", "validation.required.last_name": "Achternaam vereist", "verify-passkey.cta": "<PERSON>key verifiëren", "verify-passkey.subtitle": "Verifieer dat je passkey is aangemaakt en correct beveiligd is.", "verify-passkey.title": "<PERSON>key verifiëren", "view-cashback.cashback-next-cycle": "Cashback-tarief over {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "Stort om cashback te krijgen", "view-cashback.pending": "{money} In behandeling", "view-cashback.pending-rewards.not_paid": "Ontvangst over {days}d", "view-cashback.pending-rewards.paid": "Deze week ontvangen", "view-cashback.received-rewards": "Ontvangen beloningen", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Onbevestigde betalingen", "view-cashback.upcoming": "Aankomend {money}", "virtual-card-order.configure-safe.loading-text": "<PERSON><PERSON> wordt aangemaakt", "virtual-card-order.create-order.loading-text": "<PERSON><PERSON> wordt geactiveerd", "virtual-card-order.create-order.success-text": "<PERSON><PERSON>", "virtualCard.activateCard": "<PERSON><PERSON> kaart", "walletDeleteConfirm.main_action": "Verwijderen", "walletDeleteConfirm.subtitle": "Je moet opnieuw importeren om portfolio te bekijken of transacties te doen", "walletDeleteConfirm.title": "Wallet verwijderen?", "walletSetting.header": "Walletinstellingen", "wallet_connect.connect.cancel": "<PERSON><PERSON><PERSON>", "wallet_connect.connect.connect_button": "Ver<PERSON><PERSON>", "wallet_connect.connect.title": "Verbinden", "wallet_connect.connected.title": "Verbonden", "wallet_connect_add_chain_missing.title": "Netwerk niet ondersteund", "wallet_connect_proposal_expired.title": "Verbinding verlopen", "withdraw": "Opnemen", "withdraw.confirmation.close": "<PERSON><PERSON><PERSON>", "withdraw.confirmation.continue": "Bevestigen", "withdrawal_request.completed": "Voltooid", "withdrawal_request.pending": "In behandeling", "zeal-dapp.connect-wallet.cta.primary.connecting": "Verbinden...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Verbinden", "zeal-dapp.connect-wallet.cta.secondary": "<PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.title": "Verbind portemonnee om door te gaan", "zealSmartWalletInfo.gas": "Betaal netwerkkosten met veel tokens; gebruik populaire ERC20-tokens op ondersteunde chains om netwerkkosten te betalen, niet alleen de eigen tokens van het netwerk.", "zealSmartWalletInfo.recover": "<PERSON><PERSON> geheime z<PERSON>; herstel met biometrische passkey via je wacht<PERSON>ordmana<PERSON>, iCloud of Google-account.", "zealSmartWalletInfo.selfCustodial": "Volledig in eigen beheer; passkey-handtekeningen worden on-chain gevalideerd om centrale afhankelijkheden te minimaliseren.", "zealSmartWalletInfo.title": "Over Zeal Smart Wallets", "zeal_a_rewards_already_claimed_error.title": "Beloning al geclaimd", "zwidget.minimizedDisconnected.label": "Zeal <PERSON>"}