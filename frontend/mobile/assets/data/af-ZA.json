{"Account.ListItem.details.label": "Besonderhede", "AddFromAddress.success": "<PERSON><PERSON><PERSON>", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count, plural, offset:0 =0 {Geen beursies} one {{count} beursie} other {{count} beursies}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Geheime Frase {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Skep nuwe beursies vanaf een van jou bestaande Geheime Frases", "AddFromExistingSecretPhrase.SelectPhrase.title": "Kies 'n Geheime Frase", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "<PERSON><PERSON><PERSON>ase rugsteun baie beursies. Kies dié wat jy wil gebruik.", "AddFromExistingSecretPhrase.WalletSelection.title": "Voeg vinnig 'n beursie by", "AddFromExistingSecretPhrase.success": "Beursies by <PERSON><PERSON><PERSON><PERSON>", "AddFromHardwareWallet.subtitle": "<PERSON><PERSON> jou hardeware-beursie om aan Z<PERSON> te koppel", "AddFromHardwareWallet.title": "Hardeware-beursie", "AddFromNewSecretPhrase.WalletSelection.subtitle": "Kies die beursies wat jy wil invoer", "AddFromNewSecretPhrase.WalletSelection.title": "<PERSON><PERSON><PERSON> beursies in", "AddFromNewSecretPhrase.accounts": "Beursies", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "'n <PERSON><PERSON><PERSON><PERSON> Frase is soos 'n sleutelhouer vir miljoene beursies, el<PERSON><PERSON> met 'n unieke privaat sleutel.{br}{br}Jy kan nou soveel beursies invoer as jy wil, of later nog byvoeg.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Geheime Frase-beursies", "AddFromNewSecretPhrase.subtitle": "<PERSON><PERSON><PERSON> Phrase in, met spasies tussenin", "AddFromNewSecretPhrase.success_secret_phrase_added": "Secret Phrase byge<PERSON><PERSON> 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Beursies by <PERSON><PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.wallets": "Beursies", "AddFromPrivateKey.subtitle": "<PERSON><PERSON><PERSON> jou privaat sleutel in", "AddFromPrivateKey.success": "Privaat sleutel bygevoeg 🎉", "AddFromPrivateKey.title": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "AddFromPrivateKey.typeOrPaste": "Tik of plak hier", "AddFromSecretPhrase.importWallets": "{count, plural, offset:0 =0 {Geen beursies gekies} one {<PERSON><PERSON><PERSON> beursie in} other {Voer {count} beursies in}}", "AddFromTrezor.AccountSelection.title": "<PERSON><PERSON><PERSON> in", "AddFromTrezor.hwWalletTip.subtitle": "'n Hardeware-beursie hou miljoene beursies met verskillende adresse. V<PERSON>r nou soveel in as jy wil, of voeg later nog by.", "AddFromTrezor.hwWalletTip.title": "Invoer vanaf hardeware-beursies", "AddFromTrezor.importAccounts": "{count, plural, offset:0 =0 {Geen beursies gekies} one {<PERSON><PERSON><PERSON> beursie in} other {Voer {count} beursies in}}", "AddFromTrezor.success": "Beursies by <PERSON><PERSON><PERSON><PERSON>", "ApprovalSpenderTypeCheck.failed.subtitle": "Waarskynlik 'n swendelary: besteders moet kontrakte wees", "ApprovalSpenderTypeCheck.failed.title": "Besteder is 'n beursie, nie 'n kontrak nie", "ApprovalSpenderTypeCheck.passed.subtitle": "<PERSON><PERSON> keur gewoonlik bates aan kontrakte goed", "ApprovalSpenderTypeCheck.passed.title": "<PERSON><PERSON><PERSON> is 'n slim kontrak", "BestReturns.subtitle": "<PERSON><PERSON><PERSON> sal jou die hoogste opbrengs gee, alle fooie ingesluit.", "BestReturnsPopup.title": "Beste opbrengs", "BlacklistCheck.Failed.subtitle": "Kwaadwillige verslae deur <source></source>", "BlacklistCheck.Failed.title": "<PERSON><PERSON> is op swartlys", "BlacklistCheck.Passed.subtitle": "<PERSON><PERSON> verslae deur <source></source>", "BlacklistCheck.Passed.title": "Werf is nie op swartlys nie", "BlacklistCheck.failed.statusButton.label": "Werf is aangemeld", "BridgeRoute.slippage": "Glip {slippage}", "BridgeRoute.title": "Brugverskaffer", "CheckConfirmation.InProgress": "Aan die gang...", "CheckConfirmation.success.splash": "Voltooi", "ChooseImportOrCreateSecretPhrase.subtitle": "<PERSON><PERSON><PERSON> <PERSON><PERSON> Geh<PERSON>e Frase in of skep 'n nuwe een", "ChooseImportOrCreateSecretPhrase.title": "<PERSON><PERSON>g Geheime Frase by", "ConfirmTransaction.Simuation.Skeleton.title": "<PERSON><PERSON> ...", "ConnectionSafetyCheckResult.passed": "Veiligheidskontrole geslaag", "ContactGnosisPaysupport": "Kontak Gnosis Pay-hulp", "CopyKeyButton.copied": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CopyKeyButton.copyYourKey": "<PERSON><PERSON><PERSON> jou sleutel", "CopyKeyButton.copyYourPhrase": "<PERSON><PERSON><PERSON> jou frase", "DAppVerificationCheck.Failed.subtitle": "Werf is nie gelys op <source></source>", "DAppVerificationCheck.Failed.title": "Werf nie in app-registers gevind nie", "DAppVerificationCheck.Passed.subtitle": "Werf is gelys op <source></source>", "DAppVerificationCheck.Passed.title": "Werf verskyn in app-registers", "DAppVerificationCheck.failed.statusButton.label": "Werf nie in app-registers gevind nie", "ERC20.tokens.emptyState": "Ons kon geen tokens vind nie", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Prioriteitsfooi", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Laaste {period}: tussen {from} en {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Basisfooi: {baseFee} • Veiligheidsbuffer: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Ons kon nie die huidige Basisfooi kry nie", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "<PERSON><PERSON><PERSON> as volgende Nonce. Sal vassit", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "<PERSON>ce kan nie laer as huidige een wees nie.", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "Ons kon nie Prioriteitsfooi bereken nie", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Ons kon nie die huidige Maks. fooi kry nie", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "Maks. fooi", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Kan vashaak tot netwerkfooie daal", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "<PERSON><PERSON><PERSON>", "EditFeeModal.Custom.gasLimit.title": "Gaslimiet {gasLimit}", "EditFeeModal.Custom.title": "Gevorderde instellings", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "<PERSON> vashaak totdat die Basisfooi daal", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Lae fooi. Kan dalk vassit", "EditFeeModal.EditGasLimit.estimatedGas": "Geskatte gas: {estimated} • Veiligheidsbuffer: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "<PERSON><PERSON> as bera<PERSON><PERSON> limiet. Transaksie sal misluk", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "<PERSON><PERSON> as voorgestelde limiet. Transaksie kan misluk", "EditFeeModal.EditGasLimit.subtitle": "Stel die maksimum gas wat dié transaksie mag gebruik. <PERSON><PERSON> trans<PERSON>ie sal misluk as jy 'n limiet stel wat laer is as wat dit nodig het", "EditFeeModal.EditGasLimit.title": "<PERSON><PERSON><PERSON> gas<PERSON>", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Minder as minimum netwerkfooilimiet: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "<PERSON><PERSON><PERSON> as volgende Nonce. Sal vashaak", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "Kan nie nonce laer as huidige nonce stel nie", "EditFeeModal.EditNonce.subtitle": "<PERSON><PERSON> <PERSON><PERSON><PERSON> vashaa<PERSON> as jy nie die volgende Nonce gebruik nie", "EditFeeModal.EditNonce.title": "<PERSON><PERSON><PERSON> nonce", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "Benodig {amount} om in te dien", "EditFeeModal.Header.Time.unknown": "<PERSON><PERSON> onbekend", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Maks. {fee}", "EditFeeModal.Header.fee.unknown": "<PERSON>oo<PERSON> onbekend", "EditFeeModal.Header.subsequent_failed": "<PERSON><PERSON> is dalk oud, laaste verfrissing het misluk", "EditFeeModal.Layout.Header.ariaLabel": "<PERSON>dige fooi", "EditFeeModal.MaxFee.subtitle": "Die Maks. fooi is die meeste wat jy vir 'n transaksie sal betaal, maar jy betaal gewoonlik die voorspelde fooi. Hierdie ekstra buffer help jou trans<PERSON><PERSON>, selfs as die netwerk stadiger of duurder word.", "EditFeeModal.MaxFee.title": "<PERSON><PERSON><PERSON><PERSON> netwerkfooi", "EditFeeModal.SelectPreset.Time.unknown": "<PERSON><PERSON> onbekend", "EditFeeModal.SelectPreset.ariaLabel": "<PERSON><PERSON> fooi-voorins<PERSON>ling", "EditFeeModal.SelectPreset.fast": "<PERSON><PERSON><PERSON>", "EditFeeModal.SelectPreset.normal": "Normaal", "EditFeeModal.SelectPreset.slow": "<PERSON><PERSON><PERSON>", "EditFeeModal.ariaLabel": "Wysig netwerkfooi", "FailedSimulation.Confirmation.Item.subtitle": "Ons het 'n interne fout gehad", "FailedSimulation.Confirmation.Item.title": "Kon nie transaksie simuleer nie", "FailedSimulation.Confirmation.subtitle": "Is jy seker jy wil voor<PERSON>?", "FailedSimulation.Confirmation.title": "<PERSON><PERSON> onder<PERSON> blindelings", "FailedSimulation.Title": "Simulasiefout", "FailedSimulation.footer.subtitle": "Ons het 'n interne fout gehad", "FailedSimulation.footer.title": "Kon nie transaksie simuleer nie", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Benodig {amount} om transaksie te stuur", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Kan lank neem om te verwerk", "FeeForecastWidget.networkFee": "Netwerkfooi", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Ons kon nie die netwerkfooi bereken nie", "FeeForecastWidget.subsequentFailed.message": "Beramings is dalk oud, laaste verfrissing het misluk", "FeeForecastWidget.unknownDuration": "Onbekend", "FeeForecastWidget.unknownFee": "Onbekend", "GasCurrencySelector.balance": "Saldo: {balance}", "GasCurrencySelector.networkFee": "Netwerkfooi", "GasCurrencySelector.payNetworkFeesUsing": "Betaal netwerkfooie met", "GasCurrencySelector.removeDefaultGasToken.description": "<PERSON>al fooie van die grootste saldo", "GasCurrencySelector.removeDefaultGasToken.title": "Outomatiese fooi-hantering", "GasCurrencySelector.save": "Stoor", "GoogleDriveBackup.BeforeYouBegin.first_point": "As ek my Zeal-wagwoord vergeet, sal ek my bates vir altyd verloor", "GoogleDriveBackup.BeforeYouBegin.second_point": "As ek toegang tot my Google Drive verloor of my Herstell<PERSON>er wysig, sal ek my bates vir altyd verloor", "GoogleDriveBackup.BeforeYouBegin.subtitle": "Verstaan en aanvaar asseblief die volgende punt oor selfbewaring:", "GoogleDriveBackup.BeforeYouBegin.third_point": "Zeal kan my nie help om my Zeal-wagwoord of my toegang tot Google Drive te herstel nie", "GoogleDriveBackup.BeforeYouBegin.title": "<PERSON><PERSON><PERSON>t jy begin", "GoogleDriveBackup.loader.subtitle": "<PERSON><PERSON> asseblief die versoek op Google Drive goed om jou <PERSON> op te laai", "GoogleDriveBackup.loader.title": "Wag vir goedkeuring...", "GoogleDriveBackup.success": "<PERSON><PERSON><PERSON><PERSON> suksesvol 🎉", "MonitorOffRamp.overServiceTime": "Meeste oorbetalings word voltooi binne {estimated_time}, maar soms neem dit langer weens ekstra kontroles. Dis normaal en jou fondse is veilig.{br}{br}As die transaksie nie voltooi binne {support_soft_deadline}, asseblief {contact_support}", "MonitorOnRamp.contactSupport": "Kontak ondersteuning", "MonitorOnRamp.from": "<PERSON>", "MonitorOnRamp.fundsReceived": "Fondse ontvang", "MonitorOnRamp.overServiceTime": "Meeste oorbetalings word binne {estimated_time} voltooi, maar soms kan dit langer neem weens addisionele kontroles. Dit is normaal en fondse is veilig terwyl hierdie kontroles gedoen word.{br}{br}Indien die transaksie nie binne {support_soft_deadline} voltooi nie, kontak asseblief {contact_support}", "MonitorOnRamp.sendingToYourWallet": "<PERSON><PERSON><PERSON> na jou beursie", "MonitorOnRamp.to": "Na", "MonitorOnRamp.waitingForTransfer": "Wag vir jou om fondse oor te dra", "NftCollectionCheck.failed.subtitle": "Versameling is nie geverifieer op <source></source>", "NftCollectionCheck.failed.title": "Versameling is nie geverifieer nie", "NftCollectionCheck.passed.subtitle": "Versameling is geverifieer op <source></source>", "NftCollectionCheck.passed.title": "Versameling is geverifieer", "NftCollectionInfo.entireCollection": "<PERSON><PERSON> versameling", "NoSigningKeyStore.createAccount": "Skep rekening", "NonceRangeError.biggerThanCurrent.message": "Transaksie sal vashaak", "NonceRangeError.lessThanCurrent.message": "Transaksie sal misluk", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "<PERSON><PERSON> is hoër as huid<PERSON>. Verlaag <PERSON> om te keer dat transaksie vashaak.", "NonceRangeErrorPopup.biggerThanCurrent.title": "Transaksie sal vashaak", "P2pReceiverTypeCheck.failed.subtitle": "<PERSON>uur jy na die korrekte adres?", "P2pReceiverTypeCheck.failed.title": "Ontvanger is 'n slim kontrak, nie 'n beursie nie", "P2pReceiverTypeCheck.passed.subtitle": "<PERSON><PERSON> stuur gewoonlik bates na ander beursies", "P2pReceiverTypeCheck.passed.title": "Ontvanger is 'n beursie", "PasswordCheck.title": "<PERSON><PERSON><PERSON> wagwoord in", "PasswordChecker.subtitle": "<PERSON><PERSON><PERSON> asb. jou wagwoord in om te bevestig dis jy", "PermitExpirationCheck.failed.subtitle": "Hou dit kort en net so lank as wat jy nodig het", "PermitExpirationCheck.failed.title": "Lang vervalt<PERSON>", "PermitExpirationCheck.passed.subtitle": "Hoe lank 'n app jou tokens kan gebruik", "PermitExpirationCheck.passed.title": "Vervaltyd is nie te lank nie", "PrivateKeyValidationError.moreThanMaximumWords": "Maks {count} woorde", "PrivateKeyValidationError.notValidPrivateKey": "Dis nie 'n geldige privaat sleutel nie.", "PrivateKeyValidationError.secretPhraseIsInvalid": "Geheime frase is nie geldig nie", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Woord #{index} is verkeerd g<PERSON><PERSON> of ongeldig", "PrivateKeyValidationError.wordsCount": "{count, plural, offset:0 =0 {} one {{count} woord} other {{count} woorde}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Geheime Frases en privaat sleutels word geënkripteer en verlaat nooit hierdie toestel nie.", "SecretPhraseReveal.header": "<PERSON><PERSON><PERSON><PERSON>", "SecretPhraseReveal.hint": "<PERSON><PERSON> jou frase met en<PERSON><PERSON><PERSON> de<PERSON> nie. Hou dit veilig en vanlyn", "SecretPhraseReveal.skip.subtitle": "<PERSON><PERSON><PERSON><PERSON> jy dit later kan doen, as jy hierdie toestel verloor voordat jy jou frase ne<PERSON><PERSON><PERSON>, sal jy alle bates wat jy by hierdie be<PERSON>ie g<PERSON> het, verloor", "SecretPhraseReveal.skip.takeTheRisk": "<PERSON>k waag dit", "SecretPhraseReveal.skip.title": "<PERSON><PERSON><PERSON> frase-<PERSON><PERSON><PERSON><PERSON><PERSON> oor?", "SecretPhraseReveal.skip.writeDown": "<PERSON><PERSON><PERSON><PERSON>", "SecretPhraseReveal.skipForNow": "<PERSON><PERSON><PERSON> oor", "SecretPhraseReveal.subheader": "<PERSON><PERSON><PERSON><PERSON> dit asseblief neer en hou dit veilig van<PERSON>. Ons sal jou dan vra om dit te verifieer.", "SecretPhraseReveal.verify": "Verifieer", "SelectCurrency.tokens": "Tokens", "SelectCurrency.tokens.emptyState": "Ons kon geen tokens vind nie", "SelectRoute.slippage": "Glip {slippage}", "SelectRoutes.emptyState": "Ons kon geen roetes vir hierdie ruil vind nie", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "<PERSON><PERSON>", "SendERC20.labelAddress.inputPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SendERC20.labelAddress.subtitle": "Gee hierdie beursie 'n etiket sodat jy dit later kan vind.", "SendERC20.labelAddress.title": "<PERSON> hierdie beursie 'n etiket", "SendERC20.send_to": "Stuur na", "SendERC20.tokens": "Tokens", "SendOrReceive.bankTransfer.primaryText": "Bankoorbetaling", "SendOrReceive.bankTransfer.shortText": "<PERSON><PERSON><PERSON>, kits aan- en af-oprit", "SendOrReceive.bridge.primaryText": "Brug", "SendOrReceive.bridge.shortText": "Dra tokens oor tussen netwerke", "SendOrReceive.receive.primaryText": "Ontvang", "SendOrReceive.receive.shortText": "Ontvang tokens of versamelstukke", "SendOrReceive.send.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.send.shortText": "Stuur tokens na enige adres", "SendOrReceive.swap.primaryText": "R<PERSON> om", "SendOrReceive.swap.shortText": "Ruil tokens om", "SendSafeTransaction.Confirm.loading": "<PERSON><PERSON>…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met wag<PERSON><PERSON>", "SetupRecoveryKit.google.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "SetupRecoveryKit.google.title": "Google Drive-rugsteun", "SetupRecoveryKit.subtitle": "Jy sal ten minste een manier nodig hê om jou rekening te herstel as <PERSON><PERSON> <PERSON><PERSON> of van toestel verander", "SetupRecoveryKit.title": "Stel Herstelstel op", "SetupRecoveryKit.writeDown.subtitle": "<PERSON><PERSON><PERSON><PERSON>", "SetupRecoveryKit.writeDown.title": "<PERSON><PERSON><PERSON> rug<PERSON>", "Sign.CheckSafeDeployment.activate": "Aktiveer", "Sign.CheckSafeDeployment.subtitle": "Voordat jy by 'n app kan aanmeld of 'n buite-ketting-boodskap kan onderteken, moet jy jou toestel op hierdie netwerk aktiveer. Dit gebeur nadat jy 'n slim beursie geïnstalleer of herstel het.", "Sign.CheckSafeDeployment.title": "Aktiveer toestel op hierdie netwerk", "Sign.Simuation.Skeleton.title": "<PERSON><PERSON>…", "SignMessageSafetyCheckResult.passed": "Veiligheidskontroles Geslaag", "SignMessageSafetyChecksPopup.title.permits": "Permit-veiligheidskontroles", "SimulationFailedConfirmation.subtitle": "Ons het hierdie transaksie gesimuleer en 'n probleem gevind wat dit sal laat misluk. Jy kan die transaksie indien, maar dit sal waarskynlik misluk en jy kan jou netwerkfooi verloor.", "SimulationFailedConfirmation.title": "Transaksie sal waarskynlik misluk", "SimulationNotSupported.Title": "Simulasie nie{br}ondersteun op{br}{network}", "SimulationNotSupported.footer.subtitle": "<PERSON>y kan steeds hierdie transaksie indien", "SimulationNotSupported.footer.title": "Simulasie nie ondersteun nie", "SlippagePopup.custom": "Pasgemaak", "SlippagePopup.presetsHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SlippagePopup.title": "Prysglip-instellings", "SmartContractBlacklistCheck.failed.subtitle": "Kwaadwillige verslae deur <source></source>", "SmartContractBlacklistCheck.failed.title": "<PERSON><PERSON><PERSON> is op die swartlys", "SmartContractBlacklistCheck.passed.subtitle": "<PERSON><PERSON> verslae deur <source></source>", "SmartContractBlacklistCheck.passed.title": "<PERSON><PERSON><PERSON> is nie op die swartlys nie", "SuspiciousCharactersCheck.Failed.subtitle": "Dit is 'n algemene uitvissingstaktiek", "SuspiciousCharactersCheck.Failed.title": "Ons kontroleer vir algemene uitvissingspatrone", "SuspiciousCharactersCheck.Passed.subtitle": "Ons kyk vir uitvissingspogings", "SuspiciousCharactersCheck.Passed.title": "Adres het geen ongewone karakters nie", "SuspiciousCharactersCheck.failed.statusButton.label": "<PERSON><PERSON> het ongewone karakters ", "TokenVerificationCheck.failed.subtitle": "Token is nie gelys op <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} is nie deur CoinGecko geverifieer nie", "TokenVerificationCheck.passed.subtitle": "Token is gelys op <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} is deur <PERSON>inGeck<PERSON> geverifieer", "TopupDapp.MonitorTransaction.success.splash": "Voltooi", "TransactionSafetyCheckResult.passed": "Veiligheidskontroles Geslaag", "TransactionSimulationCheck.failed.subtitle": "Fout: {errorMessage}", "TransactionSimulationCheck.failed.title": "Transaksie sal waarskynlik misluk", "TransactionSimulationCheck.passed.subtitle": "<PERSON><PERSON><PERSON><PERSON> gedoen met <source></source>", "TransactionSimulationCheck.passed.title": "Transaksievoorskou was suksesvol", "TrezorError.trezor_action_cancelled.action": "Maak toe", "TrezorError.trezor_action_cancelled.subtitle": "Jy het die transaksie op jou hardeware-beursie verwerp", "TrezorError.trezor_device_used_elsewhere.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "<PERSON><PERSON> seker jy sluit alle ander oop sessies en probeer weer om jou Trezor te sinkroniseer", "TrezorError.trezor_method_cancelled.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "<PERSON><PERSON> seker jy laat Trezor toe om beursies na Zeal uit te voer", "TrezorError.trezor_permissions_not_granted.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "Gee asseblief vir Zeal toestemming om alle beursies te sien", "TrezorError.trezor_pin_cancelled.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "Sessie op die toestel gekanselleer", "TrezorError.trezor_popup_closed.action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "Die Trezor-dialoog het onverwags gesluit", "TrxLikelyToFail.lessThanEstimatedGas.message": "Transaksie sal misluk", "TrxLikelyToFail.lessThanMinimumGas.message": "Transaksie sal misluk", "TrxLikelyToFail.lessThanSuggestedGas.message": "Sal waarskynlik misluk", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "Transaksie se Gaslimiet is te laag. Verhoog Gaslimiet na voorgestelde limiet om mislukking te voorkom.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "Transaksie sal waarskynlik misluk", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Gaslimiet is laer as beraamde gas. Verhoog Gaslimiet na voorgestelde limiet.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "Transaksie sal misluk", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "<PERSON><PERSON><PERSON><PERSON>e Fooi is laer as huidige basiese fooi. Verhoog Maksimum Basiese Fooi om te verhoed dat transaksie vassit.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "Transaksie sal vassit", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "Transaksie se Maksimum Fooi is te laag. V<PERSON><PERSON><PERSON><PERSON> Fooi om te verhoed dat transaksie vassit.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "Transaksie sal vassit", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Prioriteitsfooi is laer as aanbeveel. Verhoog Prioriteitsfooi om transaksie te bespoedig.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "Transaksie kan lank neem om te voltooi", "UnsupportedMobileNetworkLayout.gotIt": "Verstaan!", "UnsupportedMobileNetworkLayout.subtitle": "<PERSON>y kan nog nie transaksies maak of boodskappe teken op die netwerk met ID {networkHexId} met die mobiele weergawe van Zeal nie{br}{br}Skakel oor na die blaaieruitbreiding om op hierdie netwerk transaksies te kan doen, terwyl ons hard werk om ondersteuning vir hierdie netwerk by te voeg 🚀", "UnsupportedMobileNetworkLayout.title": "Netwerk word nie deur die mobiele weergawe van Zeal ondersteun nie", "UnsupportedSafeNetworkLayout.subtitle": "<PERSON>y kan nie trans<PERSON><PERSON> maak of boodskappe onderteken op {network} met 'n Zeal Slim Beursie nie{br}{br}Skakel oor na 'n ondersteunde netwerk of gebruik 'n Legacy-beursie.", "UnsupportedSafeNetworkLayoutk.title": "Netwerk word nie vir <PERSON> ondersteun nie", "UserConfirmationPopup.goBack": "<PERSON><PERSON><PERSON><PERSON>", "UserConfirmationPopup.submit": "Dien tog in", "ViewPrivateKey.header": "Privaat Sleutel", "ViewPrivateKey.hint": "<PERSON><PERSON> jou privaat sleutel met en<PERSON><PERSON><PERSON> de<PERSON> nie. Hou dit veilig en vanlyn", "ViewPrivateKey.subheader.mobile": "Tik om jou Privaat Sleutel te openbaar", "ViewPrivateKey.subheader.web": "Beweeg oor om jou Privaat Sleutel te openbaar", "ViewPrivateKey.unblur.mobile": "Tik om te open<PERSON>ar", "ViewPrivateKey.unblur.web": "Beweeg oor om te openbaar", "ViewSecretPhrase.PasswordChecker.subtitle": "<PERSON><PERSON><PERSON> jou wagwoord in om die Herstellêer te enkripteer. <PERSON><PERSON> sal dit in die toekoms moet onthou.", "ViewSecretPhrase.done": "<PERSON><PERSON><PERSON>", "ViewSecretPhrase.header": "Geheime Frase", "ViewSecretPhrase.hint": "<PERSON><PERSON> jou frase met en<PERSON><PERSON><PERSON> de<PERSON> nie. Hou dit veilig en vanlyn", "ViewSecretPhrase.subheader.mobile": "Tik om jou G<PERSON>eime Frase te openbaar", "ViewSecretPhrase.subheader.web": "Beweeg oor om jou <PERSON>eime Frase te openbaar", "ViewSecretPhrase.unblur.mobile": "Tik om te open<PERSON>ar", "ViewSecretPhrase.unblur.web": "Beweeg oor om te openbaar", "account-details.monerium": "Oorbetalings word gedoen deur Monerium, 'n gemagtigde en gereguleerde EMI. <link><PERSON><PERSON></link>", "account-details.unblock": "Oorbetalings is via Unblock, 'n gemagtigde diens. <link><PERSON><PERSON>er</link>", "account-selector.empty-state": "Geen beursies gevind nie", "account-top-up.select-currency.title": "Tokens", "account.accounts_not_found": "Ons kon geen beursies vind nie", "account.accounts_not_found_search_valid_address": "<PERSON><PERSON><PERSON> is nie in jou lys nie", "account.add.create_new_secret_phrase": "Skep Geheime Frase", "account.add.create_new_secret_phrase.subtext": "'n Nuwe 12-woord geheime frase", "account.add.fromRecoveryKit.fileNotFound": "Ons kon nie jou lêer vind nie", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "<PERSON><PERSON><PERSON> weer", "account.add.fromRecoveryKit.fileNotFound.explanation": "<PERSON><PERSON> seker jy het by die regte rekening met 'n <PERSON><PERSON>up-lê<PERSON>gi<PERSON> aang<PERSON>.", "account.add.fromRecoveryKit.fileNotValid": "<PERSON><PERSON><PERSON><PERSON><PERSON> is nie geldig nie", "account.add.fromRecoveryKit.fileNotValid.explanation": "Ons het jou lêer na<PERSON>. Dis óf die verkeerde tipe óf dis gewysig.", "account.add.import_secret_phrase": "<PERSON><PERSON><PERSON> Geheime Frase in", "account.add.import_secret_phrase.subtext": "Geskep op Z<PERSON>, Metamask, of ander", "account.add.select_type.add_hardware_wallet": "Hardeware-beursie", "account.add.select_type.existing_smart_wallet": "Bestaande Smart Wallet", "account.add.select_type.private_key": "Privaat Sleutel", "account.add.select_type.seed_phrase": "Geheime Frase", "account.add.select_type.title": "<PERSON><PERSON><PERSON> be<PERSON> in", "account.add.select_type.zeal_recovery_file": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>", "account.add.success.title": "<PERSON><PERSON><PERSON> beursie geskep 🎉", "account.addLabel.header": "Gee jou beursie 'n naam", "account.addLabel.labelError.labelAlreadyExist": "<PERSON><PERSON> bestaan reeds. <PERSON><PERSON><PERSON> <PERSON><PERSON> ander een", "account.addLabel.labelError.maxStringLengthExceeded": "<PERSON><PERSON><PERSON><PERSON> karakters bereik", "account.add_active_wallet.primary_text": "<PERSON><PERSON><PERSON> be<PERSON>ie by", "account.add_active_wallet.short_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> of Voer Beursie in", "account.add_from_ledger.success": "Beursies by <PERSON><PERSON><PERSON><PERSON>", "account.add_tracked_wallet.primary_text": "<PERSON><PERSON><PERSON> lees-alleen be<PERSON>ie by", "account.add_tracked_wallet.short_text": "Sien portefeulje en aktiwiteit", "account.button.unlabelled-wallet": "<PERSON><PERSON><PERSON> sonder naam", "account.create_wallet": "<PERSON><PERSON><PERSON> be<PERSON>ie", "account.label.edit.title": "<PERSON><PERSON>ig beursie se naam", "account.recoveryKit.selectBackupFile.fileDate": "Geskep {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "<PERSON><PERSON><PERSON><PERSON><PERSON> is nie geldig nie", "account.recoveryKit.selectBackupFile.subtitle": "<PERSON><PERSON> die Herstellêer wat jy wil herstel", "account.recoveryKit.selectBackupFile.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "account.recoveryKit.success.recoveryFileFound": "<PERSON><PERSON><PERSON><PERSON><PERSON> gek<PERSON> 🎉", "account.select_type_of_account.create_eoa.short": "Tradisionele beursie vir kenners", "account.select_type_of_account.create_eoa.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "account.select_type_of_account.create_safe_wallet.title": "Skep Smart Wallet", "account.select_type_of_account.existing_smart_wallet": "Bestaande Smart Wallet", "account.select_type_of_account.hardware_wallet": "Hardeware-beursie", "account.select_type_of_account.header": "<PERSON><PERSON><PERSON> be<PERSON>ie by", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Privaat sleutel / Saadfrase", "account.select_type_of_account.read_only_wallet": "<PERSON><PERSON>-<PERSON><PERSON> beursie", "account.select_type_of_account.read_only_wallet.short": "Kyk na enige portefeulje", "account.topup.title": "Voeg fondse by <PERSON><PERSON>", "account.view.error.refreshAssets": "<PERSON><PERSON><PERSON><PERSON>", "account.widget.refresh": "<PERSON><PERSON><PERSON><PERSON>", "account.widget.settings": "Instellings", "accounts.view.copied-text": "Gek<PERSON>ieer {formattedAddress}", "accounts.view.copiedAddress": "Gek<PERSON>ieer {formattedAddress}", "action.accept": "<PERSON><PERSON><PERSON><PERSON>", "action.accpet": "<PERSON><PERSON><PERSON><PERSON>", "action.allow": "Laat toe", "action.back": "Terug", "action.cancel": "<PERSON><PERSON><PERSON><PERSON>", "action.card-activation.title": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "action.claim": "<PERSON><PERSON>", "action.close": "Maak toe", "action.complete-steps": "Voltooi", "action.confirm": "Bevestig", "action.continue": "Gaan voort", "action.copy-address-understand": "<PERSON> <PERSON> <PERSON><PERSON><PERSON> adres", "action.deposit": "Deponeer", "action.done": "<PERSON><PERSON><PERSON>", "action.dontAllow": "<PERSON><PERSON> toe<PERSON> nie", "action.edit": "wysig", "action.email-required": "Voer e-pos in", "action.enterPhoneNumber": "<PERSON><PERSON><PERSON> foonnommer in", "action.expand": "Brei uit", "action.fix": "Maak reg", "action.getStarted": "<PERSON><PERSON>", "action.got_it": "<PERSON><PERSON> verst<PERSON>", "action.hide": "Steek weg", "action.import": "<PERSON><PERSON><PERSON> in", "action.import-keys": "<PERSON><PERSON><PERSON> sleutels in", "action.importKeys": "<PERSON><PERSON><PERSON> sleutels in", "action.minimize": "Minimeer", "action.next": "Volgende", "action.ok": "OK", "action.reduceAmount": "<PERSON><PERSON><PERSON><PERSON> tot maks", "action.refreshWebsite": "<PERSON><PERSON><PERSON><PERSON>f", "action.remove": "Verwyder", "action.remove-account": "Verwyder rekening", "action.requestCode": "Versoek kode", "action.resend_code": "<PERSON><PERSON>ur kode weer", "action.resend_code_with_time": "<PERSON><PERSON>ur kode weer {time}", "action.retry": "<PERSON><PERSON><PERSON> weer", "action.reveal": "Wys", "action.save": "Stoor", "action.save_changes": "Stoor RPC", "action.search": "<PERSON><PERSON>", "action.seeAll": "<PERSON><PERSON> alles", "action.select": "<PERSON><PERSON>", "action.send": "<PERSON><PERSON><PERSON>", "action.skip": "<PERSON><PERSON><PERSON> oor", "action.submit": "Dien in", "action.understood": "<PERSON><PERSON> verst<PERSON>", "action.update": "Dateer op", "action.update-gnosis-pay-owner.complete": "Voltooi", "action.zeroAmount": "<PERSON><PERSON>r bedrag in", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Versamelstukke", "action_bar_title.tokens": "Tokens", "action_bar_title.transaction_request": "Transaksieversoek", "activate-monerium.loading": "<PERSON>el jou persoonlike rekening op", "activate-monerium.success.title": "Monerium geaktiveer", "activate-physical-card-widget.subtitle": "Aflewering kan 3 weke neem", "activate-physical-card-widget.title": "Akt<PERSON><PERSON> fi<PERSON>e kaart", "activate-smart-wallet.title": "Akt<PERSON><PERSON> beursie", "active_and_tracked_wallets.title": "Zeal dek al jou fooie op {network}, wat jou toelaat om gratis transaksies te doen!", "activity.approval-amount.revoked": "<PERSON><PERSON><PERSON>", "activity.approval-amount.unlimited": "Onbeperk", "activity.approval.approved_for": "Goedgekeur vir", "activity.approval.approved_for_with_target": "Goedgekeur {approvedTo}", "activity.approval.revoked_for": "<PERSON><PERSON><PERSON> vir", "activity.bank.serviceProvider": "Diensverskaffer", "activity.bridge.serviceProvider": "Diensverskaffer", "activity.cashback.period": "Terugbetalingstydperk", "activity.filter.card": "<PERSON><PERSON>", "activity.rate": "<PERSON><PERSON>", "activity.receive.receivedFrom": "<PERSON><PERSON><PERSON><PERSON> van", "activity.send.sendTo": "Gestuur na", "activity.smartContract.unknown": "Onbekende kontrak", "activity.smartContract.usingContract": "Gebruik kontrak", "activity.subtitle.pending_timer": "{timerString} <PERSON><PERSON><PERSON>", "activity.title.arbitrary_smart_contract_interaction": "{function} op {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Onbekende kontrakinteraksie", "activity.title.bridge.from": "Brug van {token}", "activity.title.bridge.to": "Brug na {token}", "activity.title.buy": "Ko<PERSON> {asset}", "activity.title.card_owners_updated": "Kaarteienaars opgedateer", "activity.title.card_spend_limit_updated": "<PERSON><PERSON> se bestedings<PERSON><PERSON> gestel", "activity.title.cashback_deposit": "Deposito na terugbetaling", "activity.title.cashback_reward": "Terugbetalingbeloning", "activity.title.cashback_withdraw": "<PERSON><PERSON><PERSON>", "activity.title.claimed_reward": "Beloning geëis", "activity.title.deployed_smart_wallet_gnosis": "<PERSON><PERSON> geskep", "activity.title.deposit_from_bank": "Deposito vanaf bank", "activity.title.deposit_into_card": "Deposito na kaart", "activity.title.deposit_into_earn": "Deposito na {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} op {smartContract}", "activity.title.from": "<PERSON> {sender}", "activity.title.pendidng_areward_claim": "Eis beloning", "activity.title.pendidng_breward_claim": "Eis beloning", "activity.title.recharge_disabledh": "<PERSON><PERSON> se her<PERSON><PERSON> g<PERSON>er", "activity.title.recharge_set": "Herlaaiteiken gestel", "activity.title.recovered_smart_wallet_gnosis": "<PERSON><PERSON><PERSON>-installasie", "activity.title.send_pending": "<PERSON><PERSON> {receiver}", "activity.title.send_to_bank": "Na bank", "activity.title.swap": "<PERSON><PERSON> {token}", "activity.title.to": "<PERSON><PERSON> {receiver}", "activity.title.withdraw_from_card": "<PERSON><PERSON><PERSON>", "activity.title.withdraw_from_earn": "<PERSON><PERSON><PERSON> van {earn}", "activity.transaction.networkFees": "Netwerkfooie", "activity.transaction.state": "Voltooide transaksie", "activity.transaction.state.completed": "Voltooide Transaksie", "activity.transaction.state.failed": "Mislukte Transaksie", "add-account.section.import.header": "<PERSON><PERSON><PERSON> in", "add-another-card-owner": "Voeg nog 'n ka<PERSON> by", "add-another-card-owner.Recommended.footnote": "<PERSON><PERSON>g jou <PERSON>-<PERSON><PERSON><PERSON> as 'n ekstra eienaar by j<PERSON>", "add-another-card-owner.Recommended.primaryText": "V<PERSON><PERSON> by Gnosis Pay", "add-another-card-owner.recommended": "Aanbeveel", "add-owner.confirmation.subtitle": "Vir sekuriteit neem verstellings 3 minute om te verwerk. <PERSON><PERSON> kaart sal tydelik gevries wees en geen betalings sal moontlik wees nie.", "add-owner.confirmation.title": "<PERSON><PERSON> kaart sal vir 3min gevries wees terwyl verstellings opdateer", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Kan nie beursie byvoeg nie, dit is reeds in gebruik", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "<PERSON><PERSON><PERSON> ander be<PERSON>ie", "add.account.backup.decrypt.success": "<PERSON><PERSON><PERSON>", "add.account.backup.password.passwordIncorrectMessage": "Wagwo<PERSON> is verkeerd", "add.account.backup.password.subtitle": "<PERSON><PERSON>r asseblief die wagwoord in wat jy gebruik het om jou <PERSON>êer te enkripteer", "add.account.backup.password.title": "<PERSON><PERSON><PERSON> wagwoord in", "add.account.google.login.subtitle": "<PERSON><PERSON> asseblief die versoek op Google Drive goed om jou <PERSON> te sinkroniseer", "add.account.google.login.title": "Wag vir goedkeuring...", "add.readonly.already_added": "<PERSON><PERSON><PERSON> is reeds bygevoeg", "add.readonly.continue": "Gaan voort", "add.readonly.empty": "<PERSON><PERSON><PERSON> <PERSON><PERSON> adres of ENS in", "addBankRecipient.title": "<PERSON><PERSON>g bank<PERSON>vanger by", "add_funds.deposit_from_bank_account": "Deponeer vanaf bankrekening", "add_funds.from_another_wallet": "<PERSON><PERSON> <PERSON><PERSON> ander beursie", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "<PERSON><PERSON> aan top-up dApp", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "<PERSON><PERSON> enige beursie aan die Zeal top-up dApp en stuur vinnig fondse na jou beursie", "add_funds.from_crypto_wallet.header": "<PERSON><PERSON> <PERSON><PERSON> ander beursie", "add_funds.from_crypto_wallet.header.show_wallet_address": "<PERSON><PERSON> jou beursie-adres", "add_funds.from_exchange.header": "<PERSON><PERSON><PERSON> vanaf 'n beurs", "add_funds.from_exchange.header.copy_wallet_address": "<PERSON><PERSON><PERSON> j<PERSON>-ad<PERSON>", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance, ens.", "add_funds.from_exchange.header.open_exchange": "Maak beurs-toep of -werf oop", "add_funds.from_exchange.header.selected_token": "Stuur {token} na Zeal", "add_funds.from_exchange.header.selected_token.subtitle": "Op {network}", "add_funds.from_exchange.header.send_selected_token": "<PERSON><PERSON>ur ondersteunde 'token'", "add_funds.from_exchange.header.send_selected_token.subtitle": "Kies ondersteunde 'token' & netwerk", "add_funds.import_wallet": "<PERSON><PERSON><PERSON> bestaande crypto-beursie in", "add_funds.title": "Befonds jou rekening", "add_funds.transfer_from_exchange": "Oorbetaling vanaf beurs", "address.add.header": "<PERSON>en jou beursie in Zeal{br}in leesalleen-modus", "address.add.subheader": "<PERSON><PERSON><PERSON> jou adres of ENS in om jou bates op alle EVM-netwerke op een plek te sien. <PERSON><PERSON><PERSON> of voer later nog beursies in.", "address_book.change_account.bank_transfers.header": "Bankontvangers", "address_book.change_account.bank_transfers.primary": "Bankontvanger", "address_book.change_account.cta": "<PERSON>g beursie", "address_book.change_account.search_placeholder": "Voeg by of soek adres", "address_book.change_account.tracked_header": "<PERSON><PERSON>-<PERSON><PERSON> beursies", "address_book.change_account.wallets_header": "Aktiewe beursies", "app-association-check-failed.modal.cta": "<PERSON><PERSON><PERSON> weer", "app-association-check-failed.modal.subtitle": "<PERSON>beer asseblief weer. Konnektiwiteitsprobleme veroorsaak vertragings met die laai van jou <PERSON>keys. As die probleem voortduur, herb<PERSON><PERSON> en probeer weer.", "app-association-check-failed.modal.subtitle.creation": "<PERSON><PERSON>r asseblief weer. Konnektiwiteitsprobleme veroorsaak vertragings met die skep van j<PERSON>. As die probleem voortduur, herb<PERSON><PERSON> en probeer weer.", "app-association-check-failed.modal.title.creation": "<PERSON>u <PERSON>tel kon nie 'n passkey skep nie", "app-association-check-failed.modal.title.signing": "<PERSON><PERSON>tel kon nie passkeys laai nie", "app.app_protocol_group.borrowed_tokens": "Geleende tokens", "app.app_protocol_group.claimable_amount": "Opeisbare bedrag", "app.app_protocol_group.health_rate": "Gesondheidskoers", "app.app_protocol_group.lending": "<PERSON><PERSON><PERSON>", "app.app_protocol_group.locked_tokens": "Geslote tokens", "app.app_protocol_group.nfts": "Versamelstukke", "app.app_protocol_group.reward_tokens": "Beloningstokens", "app.app_protocol_group.supplied_tokens": "Voorsiende tokens", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Vesting token", "app.appsGroupHeader.discoverMore": "<PERSON><PERSON><PERSON> meer", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Soek of voer URL in", "app.error-banner.cory": "<PERSON><PERSON><PERSON> fout<PERSON>", "app.error-banner.retry": "<PERSON><PERSON><PERSON> weer", "app.list_item.rewards": "Belonings {value}", "app.position_details.health_rate.description": "Die gesondheid word bereken deur die bedrag van jou lening deur die waarde van jou kollateraal te deel.", "app.position_details.health_rate.title": "Wat is 'n gesondheidsko<PERSON>?", "approval.edit-limit.label": "<PERSON><PERSON><PERSON> bestedingslimiet", "approval.permit_info": "Permit-inligting", "approval.spend-limit.edit-modal.cancel": "<PERSON><PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.limit-label": "Bestedingslimiet", "approval.spend-limit.edit-modal.max-limit-error": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hoë limiet", "approval.spend-limit.edit-modal.revert": "<PERSON><PERSON> veranderinge terug", "approval.spend-limit.edit-modal.set-to-unlimited": "Stel na onbeperk", "approval.spend-limit.edit-modal.submit": "Stoor veranderinge", "approval.spend-limit.edit-modal.title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "approval.spend_limit_info": "Wat is 'n bestedingslimiet?", "approval.what_are_approvals": "Wat is goedkeurings?", "apps_list.page.emptyState": "<PERSON><PERSON> akt<PERSON> toe<PERSON>s", "backpace.removeLastDigit": "Verwyder laaste syfer", "backup-banner.backup_now": "<PERSON><PERSON><PERSON><PERSON>", "backup-banner.risk_losing_funds": "Rugs<PERSON>un nou of waag fondsverlies", "backup-banner.title": "<PERSON><PERSON><PERSON> is nie ger<PERSON>teun nie", "backupRecoverySmartWallet.noExportPrivateKeys": "Outomatiese rugsteun: <PERSON><PERSON> word as 'n wagwoordsleutel gestoor - geen herstelfrase of privaat sleutel nodig nie.", "backupRecoverySmartWallet.safeContracts": "Multi-sleutel sekuriteit: Zeal-beursies loop op Safe-kontrakte, so verskeie toestelle kan 'n transaksie goedkeur. <PERSON><PERSON> enkele punt van mislukking nie.", "backupRecoverySmartWallet.security": "Veelvuldige toestelle: <PERSON><PERSON> kan jou beursie op verskeie toestelle met die wagwoordsleutel gebruik. <PERSON><PERSON> toestel kry sy eie privaat sleutel.", "backupRecoverySmartWallet.showLocalPrivateKey": "Kundige modus: <PERSON><PERSON> kan hierdie toestel se privaat sleutel uitvoer, dit in 'n ander beursie gebruik en koppel op <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Wys privaat sleutel</Key>", "backupRecoverySmartWallet.storingKeys": "Wolk-gesinkroniseer: Die wagwoordsleutel word veilig in iCloud, Google Password Manager, of jou wagwoordbestuurder gestoor.", "backupRecoverySmartWallet.title": "Smart Wallet-rugsteun & -herstel", "balance-change.card.titile": "<PERSON><PERSON>", "balanceChange.pending": "<PERSON><PERSON><PERSON>", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "<PERSON><PERSON>", "bank-transfer.change-deposit-receiver.subtitle": "<PERSON><PERSON>ie beursie sal alle bankdeposito's ontvang", "bank-transfer.change-deposit-receiver.title": "Stel ontvangende beursie", "bank-transfer.change-owner.subtitle": "Hierdie beursie word gebruik om aan te meld en jou bankoorbetalingsrekening te herstel", "bank-transfer.change-owner.title": "<PERSON><PERSON>", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Enige en alle bankdeposito's wat jy na Zeal stuur, sal deur hierdie beursie ontvang word.", "bank-transfer.configrm-change-deposit-receiver.title": "<PERSON>nder ontvangende beursie", "bank-transfer.configrm-change-owner.subtitle": "Is jy seker jy wil die rekeningeienaar verander? Hierdie beursie word gebruik om aan te meld en jou bankoorbetalingsrekening te herstel.", "bank-transfer.configrm-change-owner.title": "<PERSON><PERSON>", "bank-transfer.deposit.widget.status.complete": "Voltooi", "bank-transfer.deposit.widget.status.funds_received": "Fondse ontvang", "bank-transfer.deposit.widget.status.sending_to_wallet": "St<PERSON>ur na beursie", "bank-transfer.deposit.widget.status.transfer-on-hold": "Oorbetaling opgehou", "bank-transfer.deposit.widget.status.transfer-received": "Fondse ontvang", "bank-transfer.deposit.widget.subtitle": "{from} na {to}", "bank-transfer.deposit.widget.title": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.intro.bulletlist.point_1": "Stel op met Unblock", "bank-transfer.intro.bulletlist.point_2": "Dra oor tussen EUR/GBP en meer as 10 tokens", "bank-transfer.intro.bulletlist.point_3": "0% fooie tot $5k maandeliks, 0.2% daarna", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Stuur na bank", "bank-transfer.withdrawal.widget.status.in-progress": "<PERSON><PERSON> o<PERSON>", "bank-transfer.withdrawal.widget.status.on-hold": "Oorbetaling opgehou", "bank-transfer.withdrawal.widget.status.success": "Voltooi", "bank-transfer.withdrawal.widget.subtitle": "{from} na {to}", "bank-transfer.withdrawal.widget.title": "Onttrekking", "bank-transfers.bank-account-actions.remove-this-account": "<PERSON><PERSON><PERSON><PERSON> hierdie rekening", "bank-transfers.bank-account-actions.switch-to-this-account": "<PERSON><PERSON><PERSON> oor na hierdie rekening", "bank-transfers.deposit.fees-for-less-than-5k": "Fooie vir $5k of minder", "bank-transfers.deposit.fees-for-more-than-5k": "Fooie vir meer as $5k", "bank-transfers.set-receiving-bank.title": "Stel ontvangende bank", "bank-transfers.settings.account_owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfers.settings.receiver_of_bank_deposits": "Ontvanger van bankdeposito's", "bank-transfers.settings.receiver_of_withdrawals": "Ontvanger van onttrekkings", "bank-transfers.settings.registered_email": "Geregistreerde E-pos", "bank-transfers.settings.title": "Bankoorbetaling-instellings", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} Rekening", "bank-transfers.setup.bank-account": "Bankrekening", "bankTransfer.withdraw.max_loading": "Maks: {amount}", "bank_details_do_not_match.got_it": "<PERSON><PERSON><PERSON><PERSON>", "bank_details_do_not_match.subtitle": "Die sort-kode en rekeningnommer stem nie ooreen nie. Maak asseblief seker die besonderhede is korrek ingevoer en probeer weer.", "bank_details_do_not_match.title": "Bankbesonderhede stem nie ooreen nie", "bank_tranfsers.select_country_of_residence.country_not_supported": "Jammer, bankoorbetalings in {country} word nog nie ondersteun nie", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Maak jou bank-app oop", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Stuur {fiatCurrencyCode} na jou rekening", "bank_transfer.deposit.header": "{fullName} se persoonlike rekening&nbsp;besonderhede", "bank_transfer.kyc_status_widget.subtitle": "Bankoorbetalings", "bank_transfer.kyc_status_widget.title": "Verifieer identiteit", "bank_transfer.personal_details.date_of_birth": "Geboortedatum", "bank_transfer.personal_details.date_of_birth.invalid_format": "Datum is ongeldig", "bank_transfer.personal_details.date_of_birth.too_young": "<PERSON><PERSON> moet minstens 18 jaar oud wees", "bank_transfer.personal_details.first_name": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.personal_details.last_name": "<PERSON>", "bank_transfer.personal_details.title": "<PERSON><PERSON>", "bank_transfer.reference.label": "Verwysing (Opsioneel)", "bank_transfer.reference_message": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.residence_details.address": "<PERSON><PERSON>", "bank_transfer.residence_details.city": "Stad", "bank_transfer.residence_details.country_of_residence": "<PERSON> van verblyf", "bank_transfer.residence_details.country_placeholder": "Land", "bank_transfer.residence_details.postcode": "Poskode", "bank_transfer.residence_details.street": "Straat", "bank_transfer.residence_details.your_residence": "<PERSON><PERSON>", "bank_transfers.choose-wallet.continue": "Gaan voort", "bank_transfers.choose-wallet.test": "<PERSON><PERSON><PERSON> be<PERSON>ie by", "bank_transfers.choose-wallet.warning.subtitle": "Jy kan net een beursie op 'n slag koppel. Jy sal nie die gekoppelde beursie kan verander nie.", "bank_transfers.choose-wallet.warning.title": "<PERSON><PERSON> jou beursie o<PERSON>", "bank_transfers.choose_wallet.subtitle": "Kies die beursie om aan jou bank te koppel. ", "bank_transfers.choose_wallet.title": "<PERSON><PERSON> be<PERSON>ie", "bank_transfers.continue": "Gaan voort", "bank_transfers.currency_is_currently_not_supported": "Gaan voort", "bank_transfers.deposit-header": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.account-name": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.account-number-copied": "Rekeningnommer gekopieer", "bank_transfers.deposit.amount-input": "Bedrag om te deponeer", "bank_transfers.deposit.amount-output": "Bestemmingsbedrag", "bank_transfers.deposit.amount-output.error": "fout", "bank_transfers.deposit.buttet-point.receive-crypto": "Ontvang {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "Gaan voort", "bank_transfers.deposit.currency-not-supported.subtitle": "Bankdeposito's vanaf {code} is tot verdere kennisgewing gedeaktiveer.", "bank_transfers.deposit.currency-not-supported.title": "{code} deposito's word tans nie ondersteun nie", "bank_transfers.deposit.default-token.balance": "Saldo {amount}", "bank_transfers.deposit.deposit-header": "Deponeer", "bank_transfers.deposit.enter_amount": "<PERSON><PERSON>r bedrag in", "bank_transfers.deposit.iban-copied": "IBAN gekopieer", "bank_transfers.deposit.increase-amount": "Minimum oorbetaling is {limit}", "bank_transfers.deposit.loading": "Laai...", "bank_transfers.deposit.max-limit-reached": "Bedrag oorskry maksimum oorbetalingslimiet", "bank_transfers.deposit.modal.kyc.button-text": "Begin nou", "bank_transfers.deposit.modal.kyc.text": "Ons benodig jou besonderhede en dokumente om jou identiteit te verifieer. Dit neem net 'n paar minute.", "bank_transfers.deposit.modal.kyc.title": "Verifieer jou identiteit om limiete te verhoog", "bank_transfers.deposit.reduce_amount": "<PERSON><PERSON><PERSON><PERSON> bedrag", "bank_transfers.deposit.show-account.account-number": "Rekeningnommer", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Sorteerkode", "bank_transfers.deposit.sort-code-copied": "Sorteerkode gekopieer", "bank_transfers.deposit.withdraw-header": "Onttrek", "bank_transfers.failed_to_load_fee": "Onbekend", "bank_transfers.fees": "Fooie", "bank_transfers.increase-amount": "Minimum oorbetaling is {limit}", "bank_transfers.insufficient-funds": "On<PERSON><PERSON><PERSON><PERSON> fondse", "bank_transfers.select_country_of_residence.title": "Waar woon jy?", "bank_transfers.setup.cta": "Stel bankoorbetalings op", "bank_transfers.setup.enter-amount": "<PERSON><PERSON>r bedrag in", "bank_transfers.source_of_funds.form.business_income": "Besigheidsinkomste", "bank_transfers.source_of_funds.form.other": "Ander", "bank_transfers.source_of_funds.form.pension": "<PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.salary": "<PERSON><PERSON>", "bank_transfers.source_of_funds.form.title": "<PERSON><PERSON> <PERSON><PERSON>", "bank_transfers.source_of_funds_description.placeholder": "<PERSON><PERSON><PERSON><PERSON> bron van fondse...", "bank_transfers.source_of_funds_description.title": "<PERSON><PERSON><PERSON> ons meer oor jou bron van fondse", "bank_transfers.withdraw-header": "Onttrek", "bank_transfers.withdraw.amount-input": "Bedrag om te onttrek", "bank_transfers.withdraw.max-limit-reached": "Bedrag oorskry maksimum oorbetalingslimiet", "bank_transfers.withdrawal.verify-id": "<PERSON><PERSON><PERSON><PERSON> bedrag", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Maksimum invoerlimiet oorskry", "banner.above_maximum_limit.maximum_limit_per_deposit": "Dit is die maksimum limiet per deposito", "banner.above_maximum_limit.subtitle": "Hierdie is die maksimum limiet per deposito", "banner.above_maximum_limit.title": "Verminder die bedrag na {amount} of minder", "banner.above_maximum_limit.title.default": "<PERSON>erminder die bedrag", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Minimum invoerlimiet oorskry", "banner.below_minimum_limit.minimum_limit_for_token": "Dit is die minimum limiet vir hierdie token", "banner.below_minimum_limit.title": "Verhoog bedrag na {amount} of meer", "banner.below_minimum_limit.title.default": "Verhoog die bedrag", "breaard.in_porgress.info_popup.cta": "Spandeer om te verdien {earn}", "breaard.in_porgress.info_popup.footnote": "Deur die Zeal en Gnosis Pay-kaart te gebruik, stem jy tot die beloningsveldtog se bepalings in.", "breaward.in_porgress.info_popup.bullet_point_1": "Spande<PERSON> {remaining} binne die volgende {time} om hierdie beloning te eis.", "breaward.in_porgress.info_popup.bullet_point_2": "Slegs Gnosis Pay-aankope tel vir jou spandeerbedrag.", "breaward.in_porgress.info_popup.bullet_point_3": "Nadat jy jou beloning ge<PERSON>is het, sal dit na jou <PERSON>-rekening gestuur word.", "breaward.in_porgress.info_popup.header": "<PERSON><PERSON> {earn}, deur te spandeer {remaining}", "breward.celebration.for_spending": "<PERSON><PERSON><PERSON><PERSON> jy met jou kaart spandeer het", "breward.dc25-eligible-celebration.for_spending": "Jy is een van die eerste {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "Jy was nie onder die eerste {limit} wat gespandeer het nie", "breward.expired_banner.earn_by_spending": "<PERSON><PERSON> {earn} deur te spandeer {amount}", "breward.expired_banner.reward_expired": "{earn} beloning het verval", "breward.in_progress_banner.cta.title": "Spandeer om te verdien {earn}", "breward.ready_to_claim.error.try_again": "<PERSON><PERSON><PERSON> weer", "breward.ready_to_claim.error_title": "Kon nie beloning eis nie", "breward.ready_to_claim.in_progress": "Jy het {earn} verdien!", "breward.ready_to_claim.youve_earned": "Jy het verdien {earn}!", "breward_already_claimed.title": "Beloning is reeds geëis. Kontak steun indien jy nie jou beloningstoken ontvang het nie", "breward_cannotbe_claimed.title": "Beloning kan nie nou geëis word nie. <PERSON><PERSON><PERSON> asseblief later weer", "bridge.best_return": "Beste opbrengsroete", "bridge.best_serivce_time": "Vinnigste roete", "bridge.check_status.complete": "Voltooi", "bridge.check_status.progress_text": "Besig om te brug {from} na {to}", "bridge.remove_topup": "Verwyder <PERSON>", "bridge.request_status.completed": "Voltooi", "bridge.request_status.pending": "<PERSON><PERSON><PERSON>", "bridge.widget.completed": "Voltooi", "bridge.widget.currencies": "{from} na {to}", "bridge_rote.widget.title": "Brug", "browse.discover_more_apps": "<PERSON><PERSON><PERSON> meer toepassings", "browse.google_search_term": "Soek \"{searchTerm}\"", "brward.celebration.you_earned": "<PERSON><PERSON> het verdien", "brward.expired_banner.subtitle": "<PERSON>er geluk volgende keer", "brward.in_progress_banner.subtitle": "Verval oor {expiredInFormatted}", "buy": "<PERSON><PERSON>", "buy.enter_amount": "<PERSON><PERSON>r bedrag in", "buy.loading": "Laai tans...", "buy.no_routes_found": "<PERSON><PERSON> roetes gevind", "buy.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "buy.select-currency.title": "Kies token", "buy.select-to-currency.title": "Koop tokens", "buy_form.title": "Ko<PERSON> token", "cancelled-card.create-card-button.primary": "<PERSON><PERSON> <PERSON>uwe <PERSON> kaart", "cancelled-card.switch-card-button.primary": "<PERSON><PERSON><PERSON>art", "cancelled-card.switch-card-button.short-text": "Jy het nog 'n aktiewe kaart", "card": "<PERSON><PERSON>", "card-add-cash.confirm-stage.banner.no-routes-found": "<PERSON><PERSON> r<PERSON>, probeer 'n ander token of bedrag", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Jy het nog {amount} {symbol} nodig om fooie te betaal", "card-add-cash.confirm-stage.banner.value-loss": "<PERSON>y sal {loss} van die waarde verloor", "card-add-cash.confirm-stage.banner.value-loss.revert": "<PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.cancel": "<PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.continue": "Gaan voort", "card-add-cash.edit-stage.cta.enter-amount": "<PERSON><PERSON><PERSON> bedrag", "card-add-cash.edit-stage.cta.reduce-to-max": "Gebruik maks", "card-add-cash.edit-staget.banner.no-routes-found": "<PERSON><PERSON> r<PERSON>, probeer 'n ander token of bedrag", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Ons het die transaksieversoek na jou hardeware-beursie gestuur. Gaan asseblief daar voort.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "<PERSON><PERSON> hardeware-beursie", "card-balance": "Saldo: {balance}", "card-cashback.status.title": "Deponeer in Terugbetaling", "card-copy-safe-address.copy_address": "<PERSON><PERSON><PERSON>", "card-copy-safe-address.copy_address.done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card-copy-safe-address.warning.description": "<PERSON><PERSON>ie adres kan slegs {cardAsset} op Gnosis Chain ontvang. Bates op ander netwerke sal verlore gaan.", "card-copy-safe-address.warning.header": "Stuur slegs {cardAsset} op Gnosis Chain", "card-marketing-card.center.subtitle": "FX-fooie", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "Rente", "card-marketing-card.right.subtitle": "Aansluitingsgeskenk", "card-marketing-card.title": "Europa se hoërente-VISA-kaart", "card-marketing-tile.get-started": "Begin nou", "card-select-from-token-title": "<PERSON><PERSON> bron-token", "card-top-up.banner.subtitle.completed": "Voltooi", "card-top-up.banner.subtitle.failed": "Misluk", "card-top-up.banner.subtitle.pending": "{timerString} Hangend", "card-top-up.banner.title": "Deponeer {amount}", "card-topup.select-token.emptyState": "Geen tokens gekry nie", "card.activate.card_number_not_valid": "Kaartnommer is nie geldig nie. Kontroleer en probeer weer.", "card.activate.invalid_card_number": "Ongeldige kaartnommer.", "card.activation.activate_physical_card": "Akt<PERSON><PERSON> fi<PERSON>e kaart", "card.add-cash.amount-to-withdraw": "<PERSON><PERSON><PERSON><PERSON>", "card.add-from-earn-form.title": "<PERSON><PERSON><PERSON> kont<PERSON> by <PERSON><PERSON>", "card.add-from-earn-form.withdraw-to-card": "Gaan voort", "card.add-from-earn.amount-to-withdraw": "Bedrag om na <PERSON> te onttrek", "card.add-from-earn.enter-amount": "<PERSON><PERSON>r bedrag in", "card.add-from-earn.loading": "Laai tans", "card.add-from-earn.max-label": "Saldo: {amount}", "card.add-from-earn.no-routes-found": "<PERSON><PERSON> roetes gekry nie", "card.add-from-earn.not-enough-balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "card.add-owner.queued": "<PERSON><PERSON>g e<PERSON> by tou g<PERSON><PERSON>", "card.add-to-wallet-flow.subtitle": "Maak betalings vanaf jou beursie.", "card.add-to-wallet.copy-card-number": "<PERSON><PERSON><PERSON> kaartnommer hieronder", "card.add-to-wallet.title": "Voeg by {<PERSON><PERSON>ame} Wallet", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "GEKANSELLEER", "card.card-owner-not-found.disconnect-btn": "<PERSON><PERSON><PERSON><PERSON>", "card.card-owner-not-found.subtitle": "<PERSON>er jou ka<PERSON>aar op om jou Gnosis Pay-kaart in Zeal te herkoppel.", "card.card-owner-not-found.title": "<PERSON><PERSON><PERSON> ka<PERSON>", "card.card-owner-not-found.update-owner-btn": "<PERSON><PERSON> op", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} in {date}", "card.cashback.widgetNoCashback.subtitle": "Deponeer om te begin verdien", "card.cashback.widgetNoCashback.title": "<PERSON><PERSON> tot {defaultPercentage} Terugbetaling", "card.cashback.widgetcashbackValue.rewards": "{amount} hangende", "card.cashback.widgetcashbackValue.title": "{percentage} Terugbetaling", "card.choose-wallet.connect_card": "<PERSON><PERSON>", "card.choose-wallet.create-new": "<PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON>u<PERSON> be<PERSON><PERSON> as e<PERSON><PERSON> by", "card.choose-wallet.import-another-wallet": "<PERSON><PERSON><PERSON> <PERSON><PERSON> ander beursie in", "card.choose-wallet.import-current-owner": "<PERSON><PERSON><PERSON> huidige kaarteienaar in", "card.choose-wallet.import-current-owner.sub-text": "Voer privaat sleutels of saadfrase in wat jou Gnosis Pay-kaart besit", "card.choose-wallet.title": "<PERSON><PERSON> beursie om jou kaart te bestuur", "card.connectWalletToCardGuide": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "card.connectWalletToCardGuide.addGnosisPayOwner": "Voeg Gnosis Pay-eienaar by", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Maak Gnosispay.com oop met jou ander beursie{br}2. <PERSON><PERSON> “Account”{br}3. <PERSON><PERSON> “Account details”{br}4. <PERSON><PERSON> “Edit”, langs “Account Owner”, en{br}5. <PERSON><PERSON> “Add address”{br}6. <PERSON><PERSON> jou <PERSON>-adres en klik “Save”", "card.connectWalletToCardGuide.header": "<PERSON><PERSON> {account} aan <PERSON>", "card.connect_card.start": "<PERSON><PERSON> bestaande kaart", "card.copiedAddress": "Gek<PERSON>ieer {formattedAddress}", "card.disconnect-account.title": "<PERSON><PERSON><PERSON><PERSON> rekening", "card.hw-wallet-support-drop.add-owner-btn": "<PERSON><PERSON>g nuwe e<PERSON> by", "card.hw-wallet-support-drop.disconnect-btn": "<PERSON><PERSON><PERSON><PERSON>", "card.hw-wallet-support-drop.subtitle": "Voeg 'n nuwe eienaar by wat nie 'n hardeware-be<PERSON><PERSON> is nie om jou kaart in Zeal te bly gebruik.", "card.hw-wallet-support-drop.title": "Zeal ondersteun nie meer hardeware-beursies vir die kaart nie", "card.kyc.continue": "<PERSON><PERSON> v<PERSON>t met <PERSON>ste<PERSON>", "card.list_item.title": "<PERSON><PERSON>", "card.onboarded.transactions.empty.description": "<PERSON><PERSON> sal hier verskyn", "card.onboarded.transactions.empty.title": "Aktiwiteit", "card.order.continue": "Vervolg bestelling", "card.order.free_virtual_card": "<PERSON><PERSON> gratis <PERSON> kaart", "card.order.start": "<PERSON><PERSON> ka<PERSON> gratis", "card.owner-not-imported.cancel": "<PERSON><PERSON><PERSON><PERSON>", "card.owner-not-imported.import": "<PERSON><PERSON><PERSON><PERSON>", "card.owner-not-imported.subtitle": "Om hierdie transaksie te magtig, koppel die eienaarbeursie van jou Gnosis Pay-rekening a<PERSON>. Let wel: Di<PERSON> is apart van jou gewone Gnosis Pay-beursie-aanmelding.", "card.owner-not-imported.title": "Voeg Gnosis Pay-rekening<PERSON><PERSON>ar by", "card.page.order_free_physical_card": "<PERSON><PERSON> gratis fi<PERSON>e kaart", "card.pin.change_pin_at_atm": "Die PIN kan by uitgesoekte OTM's verander word", "card.pin.timeout": "Skerm sal toemaak oor {seconds} sek", "card.quick-actions.add-assets": "<PERSON><PERSON> aan", "card.quick-actions.add-cash": "<PERSON><PERSON> aan", "card.quick-actions.details": "Details", "card.quick-actions.freeze": "<PERSON><PERSON>", "card.quick-actions.freezing": "Vries tans", "card.quick-actions.unfreeze": "Ontvries", "card.quick-actions.unfreezing": "Ontvries tans", "card.quick-actions.withdraw": "Onttrek", "card.read-only-detected.create-new": "<PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON>u<PERSON> be<PERSON><PERSON> as e<PERSON><PERSON> by", "card.read-only-detected.import-current-owner": "<PERSON><PERSON><PERSON> sleutels in vir {wallet}", "card.read-only-detected.import-current-owner.sub-text": "<PERSON><PERSON><PERSON> privaat sleutels of <PERSON><PERSON><PERSON><PERSON> van <PERSON> in {address}", "card.read-only-detected.title": "Kaart op lees-alleen beursie bespeur. Kies beursie om kaart te bestuur", "card.remove-owner.queued": "<PERSON><PERSON><PERSON><PERSON> e<PERSON> by tou g<PERSON><PERSON>", "card.settings.disconnect-from-zeal": "<PERSON><PERSON><PERSON><PERSON>", "card.settings.edit-owners": "<PERSON><PERSON>", "card.settings.getCard": "<PERSON><PERSON> nog 'n kaart", "card.settings.getCard.subtitle": "Virtuele of fi<PERSON>e ka<PERSON>", "card.settings.notRecharging": "Vul nie aan nie", "card.settings.notifications.subtitle": "Kry betalingkennisgewings", "card.settings.notifications.title": "MetaMask-modus", "card.settings.page.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.select-card.cancelled-cards": "Gekanselleerd<PERSON>", "card.settings.setAutoRecharge": "Stel outo-aanvulling", "card.settings.show-card-address": "<PERSON><PERSON>", "card.settings.spend-limit": "Stel spandeerlimiet", "card.settings.spend-limit-title": "Huidige daaglikse limiet: {limit}", "card.settings.switch-active-card": "<PERSON>isse<PERSON> aktie<PERSON> kaart", "card.settings.switch-active-card-description": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>: {card}", "card.settings.switch-card.card-item.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.frozen": "G<PERSON><PERSON>", "card.settings.switch-card.card-item.title": "Gnosis Pay-kaart", "card.settings.switch-card.card-item.title.physical": "<PERSON><PERSON><PERSON> ka<PERSON>", "card.settings.switch-card.card-item.title.virtual": "<PERSON>irt<PERSON><PERSON>", "card.settings.switch-card.title": "<PERSON><PERSON> ka<PERSON>", "card.settings.targetBalance": "Teikensaldo: {threshold}", "card.settings.view-pin": "Wys PIN", "card.settings.view-pin-description": "Beskerm altyd jou <PERSON>", "card.title": "<PERSON><PERSON>", "card.transactions.header": "Kaarttransaksies", "card.transactions.see_all": "<PERSON>en alle transaksies", "card.virtual": "VIRTUEEL", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Terugbetaling word aan die begin van die week nadat dit verdien is, na jou kaart gestuur.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "Deponeer meer. <PERSON><PERSON> meer met el<PERSON> koop.", "cardCashback.onboarding.title": "<PERSON><PERSON> tot {percentage} terugbetaling", "cardCashbackWithdraw.amount": "Onttrekkingsbedrag", "cardCashbackWithdraw.header": "Onttrek {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "<PERSON><PERSON> is geb<PERSON>kkeer en kon nie geaktiveer word nie", "cardWidget.cashback": "Terugbetaling", "cardWidget.cashbackUpToDefaultPercentage": "Tot {percentage}", "cardWidget.startEarning": "<PERSON><PERSON> verdien", "cardWithdraw.amount": "Onttrek bedrag", "cardWithdraw.header": "<PERSON><PERSON><PERSON>", "cardWithdraw.selectWithdrawWallet.title": "Kies beursie om{br}na te onttrek", "cardWithdraw.success.cta": "Maak toe", "cardWithdraw.success.subtitle": "Vir sekuriteit neem alle onttrekkings van die Gnosis Pay-kaart 3 minute om te verwerk", "cardWithdraw.success.title": "<PERSON><PERSON><PERSON> verandering sal 3 minute neem", "card_top_up_trx.send": "<PERSON><PERSON><PERSON>", "card_top_up_trx.to": "<PERSON><PERSON>", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "Vervaldatum", "cards.card_number": "Kaartnommer", "cards.choose-wallet.no-active-accounts": "Jy het geen aktiewe beursies nie", "cards.copied_card_number": "Kaartnommer gekopieer", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Oorskry daaglikse limiet", "cards.transactions.decline_reason.incorrect_pin": "Verkeerde PIN", "cards.transactions.decline_reason.incorrect_security_code": "Verkeerde sekuriteitskode", "cards.transactions.decline_reason.invalid_amount": "Ongeldige bedrag", "cards.transactions.decline_reason.low_balance": "<PERSON><PERSON> saldo", "cards.transactions.decline_reason.other": "<PERSON><PERSON><PERSON><PERSON>", "cards.transactions.decline_reason.pin_tries_exceeded": "PIN-pogings oorskry", "cards.transactions.status.refund": "Terugbetaling", "cards.transactions.status.reversal": "<PERSON><PERSON><PERSON><PERSON>", "cashback-deposit.trx.title": "Deposito na terugbetaling", "cashback-estimate.text": "Hierdie is 'n skatting en NIE 'n gewaarborgde uitbetaling nie. Alle publiek bekende terugbetalingsreëls word toegepas, maar Gnosis Pay kan na goeddunke transaksies uitsluit. 'n Maksimum besteding van {amount} per week kwalifiseer vir terugbetaling, selfs al sou die skatting vir hierdie transaksie 'n hoër totale bedrag aandui.", "cashback-estimate.text.fallback": "Hierdie is 'n skatting en NIE 'n gewaarborgde uitbetaling nie. Alle openbaar bekende terugbetalingreëls word toegepas, maar Gnosis Pay kan transaksies na goeddunke uitsluit.", "cashback-estimate.title": "Terugbetalingskatting", "cashback-onbarding-tersm.subtitle": "<PERSON><PERSON><PERSON><PERSON> sal met Ka<PERSON><PERSON>key gedeel word, wat verantwoordelik is vir die verspreiding van terugbetalingsbelonings. Deur te aanvaar, stem jy in tot Gnosis DAO Terugbetaling se <terms>Bepalings en Voorwaardes</terms>", "cashback-onbarding-tersm.title": "Gebruiksvoorwaardes en privaatheid", "cashback-tx-activity.retry": "<PERSON><PERSON><PERSON> weer", "cashback-unconfirmed-payments-info.subtitle": "Betalings kwalifiseer vir terugbetaling wanneer dit met die handelaar vereffen is. Tot dan wys dit as onbevestigde betalings. Onvereffende betalings kwalifiseer nie vir terugbetaling nie.", "cashback-unconfirmed-payments-info.title": "Onbevestigde ka<PERSON>alings", "cashback.activity.cashback": "Terugbetaling", "cashback.activity.deposit": "<PERSON><PERSON><PERSON><PERSON>", "cashback.activity.title": "Onlangse aktiwiteit", "cashback.activity.withdrawal": "Onttrekking", "cashback.deposit": "Deponeer", "cashback.deposit.amount.label": "Depositobed<PERSON>", "cashback.deposit.change": "{from} na {to}", "cashback.deposit.confirmation.subtitle": "Terugbetaling vir hierdie week, insluitend wat jy reeds verdien het, sal verminder van {before} na {after}", "cashback.deposit.confirmation.title": "<PERSON>y sal begin verdien {percentage} op {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Ruil tokens vir {currency} op {network} Chain", "cashback.deposit.get.tokens.title": "Kry {currency} tokens", "cashback.deposit.header": "Deponeer {currency}", "cashback.deposit.max_label": "Maks: {amount}", "cashback.deposit.select-wallet.title": "<PERSON><PERSON> beursie om van te deponeer", "cashback.deposit.yourcashback": "<PERSON><PERSON>", "cashback.header": "Terugbetaling", "cashback.selectWithdrawWallet.title": "Kies beursie om na{br}te onttrek", "cashback.transaction-details.network-label": "Netwerk", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "<PERSON>", "cashback.transaction-details.top-row.label-rewards": "Terugbetalingstydperk", "cashback.transaction-details.top-row.label-withdrawal": "<PERSON><PERSON>", "cashback.transaction-details.transaction": "Transaksie-ID", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Terugbetaling-transaksies", "cashback.withdraw": "Onttrek", "cashback.withdraw.confirmation.cashback_reduction": "Terugbetaling vir hierdie week, insluitend wat jy reeds verdien het, sal verminder van {before} na {after}", "cashback.withdraw.queued": "Onttrekking in tou", "cashback.withdrawal.change": "{from} na {to}", "cashback.withdrawal.confirmation.subtitle": "Begin onttrekking van {amount} met 'n 3-minuut-vertraging. Dit sal jou terugbetaling verminder na {after}.", "cashback.withdrawal.confirmation.title": "<PERSON><PERSON><PERSON><PERSON> sal verminder as jy <PERSON><PERSON><PERSON> onttrek", "cashback.withdrawal.delayTransaction.title": "Begin GNO-onttrekking met{br} 'n 3-minuut-vertraging", "cashback.withdrawal.withdraw": "Onttrek", "cashback.withdrawal.yourcashback": "<PERSON><PERSON>", "celebration.aave": "<PERSON><PERSON> met <PERSON><PERSON>", "celebration.cashback.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> in {code}", "celebration.cashback.subtitleGNO": "{amount} laas verdien", "celebration.chf": "<PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON>", "celebration.lido": "<PERSON><PERSON> met <PERSON><PERSON>", "celebration.sky": "<PERSON><PERSON> met <PERSON>", "celebration.title": "<PERSON><PERSON> so!", "celebration.well_done.title": "<PERSON><PERSON> so!", "change-withdrawal-account.add-new-account": "Voeg nog 'n bankrekening by", "change-withdrawal-account.item.shortText": "{currency} Rekening", "check-confirmation.approve.footer.for": "Vir", "checkConfirmation.title": "Transaksie-uitslag", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin & Ether", "collateral.ethereum": "Ethereum", "collateral.other": "Ander", "collateral.rwa": "<PERSON><PERSON><PERSON>", "collateral.stablecoins": "Stablecoins (USD-gekoppel)", "collateral.us-t-bills": "US T-Bills", "confirm-bank-transfer-recipient.bullet-1": "Geen fooie op digitale EUR", "confirm-bank-transfer-recipient.bullet-2": "<PERSON><PERSON><PERSON><PERSON>'s na {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Deel Gnosis Pay-rekeningbesonderhede met Monerium, 'n gemagtigde en gereguleerde EMI. <link><PERSON><PERSON></link>", "confirm-bank-transfer-recipient.bullet-4": "Aanvaar Monerium se <link>diensbepalings</link>", "confirm-bank-transfer-recipient.title": "<PERSON><PERSON><PERSON><PERSON> bepal<PERSON>", "confirm-change-withdrawal-account.cancel": "<PERSON><PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.confirm": "Bevestig", "confirm-change-withdrawal-account.saving": "Stoor tans", "confirm-change-withdrawal-account.subtitle": "Alle onttrekkings wat jy vanaf <PERSON> stuur, sal deur hierdie bankrekening ontvang word.", "confirm-change-withdrawal-account.title": "Verander ontvangende bank", "confirm-ramove-withdrawal-account.title": "Verwyder bankrekening", "confirm-remove-withdrawal-account.subtitle": "Hierdie bankrekeningbesonderhede sal van Zeal verwyder word. Jy kan dit enige tyd weer byvoeg.", "confirmTransaction.finalNetworkFee": "Netwerkfooi", "confirmTransaction.importKeys": "<PERSON><PERSON><PERSON> sleutels in", "confirmTransaction.networkFee": "Netwerkfooi", "confirmation.title": "Stuur {amount} na {recipient}", "conflicting-monerium-account.add-owner": "Voeg by as <PERSON><PERSON>", "conflicting-monerium-account.create-wallet": "Ske<PERSON> 'n nuwe slim beursie", "conflicting-monerium-account.disconnect-card": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON> van <PERSON> en <PERSON> met nu<PERSON> <PERSON><PERSON><PERSON>", "conflicting-monerium-account.header": "{wallet} gekoppel aan 'n ander Monerium-rekening", "conflicting-monerium-account.subtitle": "<PERSON><PERSON> jou <PERSON> Pay-eienaarbeursie", "connection.diconnected.got_it": "Verstaan!", "connection.diconnected.page1.subtitle": "Zeal werk oral waar Metamask werk. <PERSON><PERSON> eenvoudig soos jy met Metamask sou doen.", "connection.diconnected.page1.title": "Hoe om met Zeal te koppel?", "connection.diconnected.page2.subtitle": "Jy sal baie opsies sien. Zeal mag een van hulle wees. As Zeal nie verskyn nie...", "connection.diconnected.page2.title": "<PERSON><PERSON> Beursie", "connection.diconnected.page3.subtitle": "Ons sal 'n koppeling met <PERSON><PERSON> vers<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON> of Injected behoort ook te werk. <PERSON>beer dit!", "connection.diconnected.page3.title": "<PERSON><PERSON>", "connectionSafetyCheck.tag.caution": "Waarskuwing", "connectionSafetyCheck.tag.danger": "<PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.passed": "Geslaag", "connectionSafetyConfirmation.subtitle": "Is jy seker jy wil voor<PERSON>?", "connectionSafetyConfirmation.title": "Hierdie werf lyk gevaarlik", "connection_state.connect.cancel": "<PERSON><PERSON><PERSON><PERSON>", "connection_state.connect.changeToMetamask": "Verander na MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Verander na MetaMask", "connection_state.connect.connect_button": "<PERSON><PERSON>", "connection_state.connect.expanded.connected": "Gekoppel", "connection_state.connect.expanded.title": "<PERSON><PERSON>", "connection_state.connect.safetyChecksLoading": "Kontroleer werfveiligheid", "connection_state.connect.safetyChecksLoadingError": "Kon nie veiligheidskontroles voltooi nie", "connection_state.connected.expanded.disconnectButton": "<PERSON><PERSON><PERSON><PERSON>", "connection_state.connected.expanded.title": "Gekoppel", "copied-diagnostics": "Diagnostiek gekopieer", "copy-diagnostics": "<PERSON><PERSON><PERSON> diagnos<PERSON>", "counterparty.component.add_recipient_primary_text": "<PERSON><PERSON>g bank<PERSON>vanger by", "counterparty.country": "Land", "counterparty.countryTitle": "Ontvanger se land", "counterparty.currency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "counterparty.delete.success.title": "Verwyder", "counterparty.edit.success.title": "Wysigings gestoor", "counterparty.errors.country_required": "Land is verpligtend", "counterparty.errors.first_name.invalid": "<PERSON><PERSON><PERSON><PERSON> moet langer wees", "counterparty.errors.last_name.invalid": "<PERSON> moet langer wees", "counterparty.first_name": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Stuur na bank", "countrySelector.noCountryFound": "Geen land gekry nie", "countrySelector.title": "Kies land", "create-passkey.cta": "<PERSON><PERSON><PERSON> wag<PERSON><PERSON><PERSON><PERSON>l", "create-passkey.extension.cta": "Gaan voort", "create-passkey.footnote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create-passkey.mobile.cta": "Begin veilige opstel", "create-passkey.steps.enable-recovery": "Stel wolk-herstel op", "create-passkey.steps.setup-biometrics": "Aktiveer biometriese sekuriteit", "create-passkey.subtitle": "Wagwoordsleutels is veiliger as wagwoorde, en word in wolkberging geënkripteer vir maklike herwinning.", "create-passkey.title": "Beveilig rekening", "create-smart-wallet": "Skep Smart Wallet", "create-userop.progress.text": "Besig om te skep", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "<PERSON><PERSON> voort in Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Gaan na Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "<PERSON>y het reeds jou kaartbestelling begin. Gaan terug na die Gnosis Pay-werf om dit te voltooi.", "create_recharge_preferences.card": "<PERSON><PERSON>", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "<PERSON>en {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "<PERSON><PERSON> k<PERSON>", "create_recharge_preferences.link_accounts_title": "<PERSON><PERSON>", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "<PERSON><PERSON> ka<PERSON> sal NIE outomaties herlaai na elke betaling nie.", "create_recharge_preferences.not_configured_title": "Verdien & Spandeer", "create_recharge_preferences.recharge_from_earn_accounts_description": "<PERSON><PERSON> ka<PERSON> her<PERSON> outomaties na elke betaling vanaf jou <PERSON>-rekening.", "create_recharge_preferences.subtitle": "per jaar", "creating-account.loading": "Skep tans rekening", "creating-gnosis-pay-account": "Skep tans rekening", "currencies.bridge.select_routes.emptyState": "Ons kon geen roetes vir hierdie brug vind nie", "currency.add_currency.add_token": "Voeg token by", "currency.add_currency.not_a_valid_address": "Hierdie is nie 'n geldige <PERSON>res nie", "currency.add_currency.token_decimals_feild": "<PERSON><PERSON> desimale", "currency.add_currency.token_feild": "Tokenadres", "currency.add_currency.token_symbol_feild": "Token simbool", "currency.add_currency.update_token": "Dateer token op", "currency.add_custom.remove_token.cta": "Skrap token", "currency.add_custom.remove_token.header": "Verwyder token", "currency.add_custom.remove_token.subtitle": "<PERSON>u beursie sal steeds enige saldo van hierdie token hou, maar dit sal van j<PERSON>-port<PERSON><PERSON><PERSON><PERSON>saldo's weggesteek word.", "currency.add_custom.token_removed": "Verifieer RPC", "currency.add_custom.token_updated": "<PERSON><PERSON> opged<PERSON>er", "currency.balance_label": "Saldo: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "<PERSON><PERSON> bank<PERSON> het suksesvol oorgedra {fiat} na {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "Jy het ontvang {crypto}", "currency.bankTransfer.deposit_status.success": "Ontvang in jou beursie", "currency.bankTransfer.deposit_status.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.check_bank_account": "Gaan jou bankrekening na", "currency.bankTransfer.off_ramp.complete": "Gaan jou bankrekening na", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Stuur na jou bank", "currency.bankTransfer.off_ramp.transferring_to_currency": "Dra oor na {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Fondse moet nou in jou bankrekening wees.", "currency.bankTransfer.withdrawal_status.success": "Gestuur na jou bank", "currency.bankTransfer.withdrawal_status.title": "Onttrekking", "currency.bank_transfer.create_unblock_user.email": "E-posadres", "currency.bank_transfer.create_unblock_user.email_invalid": "Ongeldige e-pos", "currency.bank_transfer.create_unblock_user.email_missing": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_missing": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "Slegs letters, syfers, spasies en - . , & ( ) ' toegelaat.", "currency.bank_transfer.create_unblock_user.last_name": "<PERSON>", "currency.bank_transfer.create_unblock_user.last_name_missing": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "Slegs letters, syfers, spasies en - . , & ( ) ' toegelaat.", "currency.bank_transfer.create_unblock_user.note": "Deur voort te gaan, aan<PERSON><PERSON> jy <PERSON> (ons bankvennoot) se <terms>Bepalings</terms> en <policy>Privaatheidsbeleid</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Spel jou naam presies soos op jou bankrekening", "currency.bank_transfer.create_unblock_user.title": "<PERSON><PERSON> jou bankrekening", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Rekeningnommer", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Bank se land", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Voorkeurgeldeenheid", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Sort-kode", "currency.bank_transfer.create_unblock_withdraw_account.success": "Rekening opgestel", "currency.bank_transfer.create_unblock_withdraw_account.title": "<PERSON><PERSON> jou bankrekening", "currency.bank_transfer.residence-form.address-required": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.address-unsupported-char": "Slegs letters, syfers, spasies, en , ; {apostrophe} - \\\\ toegelaat.", "currency.bank_transfer.residence-form.city-required": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.residence-form.city-unsupported-char": "Slegs letters, syfers, spasies en . , - & ( ) {apostrophe} word toegelaat.", "currency.bank_transfer.residence-form.postcode-invalid": "Ongeldige poskode", "currency.bank_transfer.residence-form.postcode-required": "<PERSON><PERSON><PERSON>", "currency.bank_transfer.validation.invalid.account_number": "Ongeldige rekeningnommer", "currency.bank_transfer.validation.invalid.iban": "Ongeldige IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Ongeldige sorteerkode", "currency.bridge.amount_label": "Bedrag om te brug", "currency.bridge.best_returns.subtitle": "<PERSON><PERSON><PERSON> brugverskaffer sal jou die hoogste opbrengs gee, insluitend alle fooie.", "currency.bridge.best_returns_popup.title": "Beste opbrengs", "currency.bridge.bridge_from": "<PERSON>", "currency.bridge.bridge_gas_fee_loading_failed": "Ons het probleme on<PERSON><PERSON><PERSON> met die laai van die netwerkfooi", "currency.bridge.bridge_low_slippage": "<PERSON><PERSON> lae glip. Pro<PERSON>r dit verhoog", "currency.bridge.bridge_provider": "Oorbetalingsverskaffer", "currency.bridge.bridge_provider_loading_failed": "Ons het probleme on<PERSON><PERSON><PERSON> met die la<PERSON> van verskaffers", "currency.bridge.bridge_settings": "Brug-instellings", "currency.bridge.bridge_status.subtitle": "<PERSON><PERSON><PERSON><PERSON> {name}", "currency.bridge.bridge_status.title": "Brug", "currency.bridge.bridge_to": "Na", "currency.bridge.fastest_route_popup.subtitle": "<PERSON>erdie brugverskaffer sal jou die vinnigste transaksieroete gee.", "currency.bridge.fastest_route_popup.title": "Vinnigste roete", "currency.bridge.from": "<PERSON>", "currency.bridge.success": "Voltooi", "currency.bridge.title": "Brug", "currency.bridge.to": "Na", "currency.bridge.topup": "Vul aan {symbol}", "currency.bridge.withdrawal_status.title": "Onttrekking", "currency.card.card_top_up_status.title": "<PERSON><PERSON>g kontant by kaart", "currency.destination_amount": "Bestemmingsbedrag", "currency.hide_currency.confirm.subtitle": "Steek hierdie token weg van jou portefeulje. Jy kan dit enige tyd weer sigbaar maak.", "currency.hide_currency.confirm.title": "Steek token weg", "currency.hide_currency.success.title": "<PERSON><PERSON> wegg<PERSON>", "currency.label": "Etiket (Opsioneel)", "currency.last_name": "<PERSON>", "currency.max_loading": "Maks:", "currency.swap.amount_to_swap": "Bedrag om te ruil", "currency.swap.best_return": "<PERSON><PERSON> met beste opbre<PERSON>s", "currency.swap.destination_amount": "Bestemmingsbedrag", "currency.swap.header": "<PERSON><PERSON>", "currency.swap.max_label": "Saldo: {amount}", "currency.swap.provider.header": "Ruilverskaffer", "currency.swap.select_to_token": "Kies token", "currency.swap.swap_gas_fee_loading_failed": "Ons kon nie die netwerkfooi laai nie", "currency.swap.swap_provider_loading_failed": "Ons kon nie die verskaffers laai nie", "currency.swap.swap_settings": "<PERSON><PERSON>-instellings", "currency.swap.swap_slippage_too_low": "<PERSON><PERSON> lae glip. Pro<PERSON>r dit verhoog", "currency.swaps_io_native_token_swap.subtitle": "Gebruik Swaps.IO", "currency.swaps_io_native_token_swap.title": "<PERSON><PERSON><PERSON>", "currency.withdrawal.amount_from": "Vanaf", "currency.withdrawal.amount_to": "<PERSON><PERSON>", "currencySelector.title": "<PERSON><PERSON>", "dApp.wallet-does-not-support-chain.subtitle": "<PERSON>u beursie ondersteun skynbaar nie {network} nie. <PERSON><PERSON><PERSON> '<PERSON> ander beursie kop<PERSON>, of gebruik e<PERSON>er Z<PERSON>.", "dApp.wallet-does-not-support-chain.title": "Netwerk word nie ondersteun nie", "dapp.connection.manage.confirm.disconnect.all.cta": "Ontko<PERSON> al", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Is jy seker jy wil alle verbindings ont<PERSON>?", "dapp.connection.manage.confirm.disconnect.all.title": "Ontkoppel almal", "dapp.connection.manage.connection_list.main.button.title": "<PERSON><PERSON><PERSON><PERSON>", "dapp.connection.manage.connection_list.no_connections": "Jy het geen gekoppelde toepassings nie", "dapp.connection.manage.connection_list.section.button.title": "Ontkoppel almal", "dapp.connection.manage.connection_list.section.title": "Aktief", "dapp.connection.manage.connection_list.title": "Verbindings", "dapp.connection.manage.disconnect.success.title": "<PERSON><PERSON><PERSON>", "dapp.metamask_mode.title": "MetaMask-modus", "dc25-card-marketing-card.center.subtitle": "Terugbetaling", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "Rente", "dc25-card-marketing-card.right.subtitle": "100 mense", "dc25-card-marketing-card.title": "Eerste 100 wat €50 spandeer, verdien {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "<PERSON>y sal nie hierdie aksie vir 3 min kan doen nie. Vir sekuriteitsredes neem enige kaartinstellingsveranderinge of onttrekkings 3 minute om verwerk te word.", "delayQueueBusyBanner.processing-yout-action.title": "Verwerk jou aksie, wag asseblief", "delayQueueBusyWidget.cardFrozen": "<PERSON><PERSON> gevries", "delayQueueBusyWidget.processingAction": "Verwerk jou aksie", "delayQueueFailedBanner.action-incomplete.get-support": "<PERSON><PERSON><PERSON>", "delayQueueFailedBanner.action-incomplete.subtitle": "<PERSON><PERSON>, iets het verkeerd geloop met jou onttrekking of instellingsopdatering. Kontak asseblief steun op <PERSON>rd.", "delayQueueFailedBanner.action-incomplete.title": "<PERSON><PERSON><PERSON>", "delayQueueFailedWidget.actionIncomplete.title": "<PERSON><PERSON><PERSON><PERSON>", "delayQueueFailedWidget.cardFrozen.subtitle": "<PERSON><PERSON> gevries", "delayQueueFailedWidget.contactSupport": "Kontak ondersteuning", "delay_queue_busy.subtitle": "Vir sekuriteitsredes neem enige kaartinstellingsveranderinge of onttrekkings 3 minute om verwerk te word. Gedurende hierdie tyd word jou kaart gevries.", "delay_queue_busy.title": "Jou aksie word verwerk", "delay_queue_failed.contact_support": "Kontak steun", "delay_queue_failed.subtitle": "<PERSON><PERSON>, iets het verkeerd geloop met jou onttrekking of instellingsopdatering. Kontak asseblief steun op <PERSON>rd.", "delay_queue_failed.title": "Kontak steun", "deploy-earn-form-smart-wallet.in-progress.title": "Berei Verdien voor", "deposit": "Deponeer", "disconnect-card-popup.cancel": "<PERSON><PERSON><PERSON><PERSON>", "disconnect-card-popup.disconnect": "<PERSON><PERSON><PERSON><PERSON>", "disconnect-card-popup.subtitle": "Dit sal jou kaart van die Zeal-toep verwyder. <PERSON><PERSON> be<PERSON><PERSON> sal steeds aan jou kaart in die Gnosis Pay-toep gekoppel wees. <PERSON>y kan jou kaart enige tyd weer koppel.", "disconnect-card-popup.title": "<PERSON><PERSON><PERSON><PERSON> kaart", "distance.long.days": "{count} dae", "distance.long.hours": "{count} ure", "distance.long.minutes": "{count} minute", "distance.long.months": "{count} maande", "distance.long.seconds": "{count} sekondes", "distance.long.years": "{count} jare", "distance.short.days": "{count} d", "distance.short.hours": "{count} u", "distance.short.minutes": "{count} min", "distance.short.months": "{count} m", "distance.short.seconds": "{count} sek", "distance.short.years": "{count} j", "duration.short.days": "{count}d", "duration.short.hours": "{count}u", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn-deposit-view.into": "Na", "earn-deposit-view.to": "<PERSON><PERSON>", "earn-deposit.swap.transfer-provider": "Oordragverskaffer", "earn-taker-investment-details.accrued-realtime": "Loop intyds op", "earn-taker-investment-details.asset-class": "<PERSON><PERSON><PERSON>", "earn-taker-investment-details.asset-coverage-ratio": "Batedekkingsverhouding", "earn-taker-investment-details.asset-reserve": "Batereserwe", "earn-taker-investment-details.base_currency.label": "Basisgeldeenheid", "earn-taker-investment-details.chf.description": "Verdien rente op jou CHF deur zCHF in Frankencoin te deponeer - 'n betroubare digitale geldmark. Rente word gegenereer uit lae-risiko, oor-gekolateraliseerde lenings op Frankencoin en word intyds uitbetaal. <PERSON><PERSON> fondse bly veilig in 'n sekure sub-rekening wat net jy beheer.", "earn-taker-investment-details.chf.description.with_address_link": "Verdien rente op jou CHF deur zCHF in Frankencoin te deponeer - 'n betroubare digitale geldmark. Rente word gegenereer uit lae-risiko, oor-gekolateraliseerde lenings op Frankencoin en word intyds uitbetaal. <PERSON>u fondse bly veilig in 'n sekure sub-rekening <link>(kopieer 0x)</link> wat net jy beheer.", "earn-taker-investment-details.chf.label": "<PERSON>e Switserse Frank", "earn-taker-investment-details.collateral-composition": "<PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.depositor-obligations": "Deponeerderverpligtinge", "earn-taker-investment-details.eure.description": "Verdien rente op jou euro's. Deponeer EURe in Aave – 'n betroubare digitale geldmark. EURe is 'n ten volle gereguleerde euro-stablecoin van Monerium, 1:1 gedek in beveiligde rekeninge. Rente kom van laerisiko-lenings op Aave en word intyds uitbetaal. <PERSON><PERSON> fondse bly in 'n veilige subrekening wat net jy beheer.", "earn-taker-investment-details.eure.description.with_address_link": "Verdien rente op jou euro's. Deponeer EURe in Aave – 'n betroubare digitale geldmark. EURe is 'n ten volle gereguleerde euro-stablecoin van Monerium, 1:1 gedek in beveiligde rekeninge. Rente kom van laerisiko-lenings op Aave en word intyds uitbetaal. <PERSON>u fondse bly in 'n veilige subrekening <link>(kopieer 0x)</link> wat net jy beheer.", "earn-taker-investment-details.eure.label": "Digitale Euro (EURe)", "earn-taker-investment-details.faq": "GVV", "earn-taker-investment-details.fixed-income": "Vaste inkomste", "earn-taker-investment-details.issuer": "Uitreiker", "earn-taker-investment-details.key-facts": "Sleutelfeite", "earn-taker-investment-details.liquidity": "Likiditeit", "earn-taker-investment-details.operator": "Markoperateur", "earn-taker-investment-details.projected-yield": "Geprojekteerde jaarlikse opbrengs", "earn-taker-investment-details.see-other-faq": "Sien alle ander GVV's", "earn-taker-investment-details.see-realtime": "Sien intydse data", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Lopende 12-ma<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.total-collateral": "Totale Kollateraal", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "Totale ZCHF-voorraad", "earn-taker-investment-details.total_deposits": "Totale Aave-deposito's", "earn-taker-investment-details.usd.description": "Sky is 'n digitale geldmark wat stabiele, VSA-dollar-gedenomineerde opbrengste bied uit korttermyn VSA-tesourieë en oor-gekollateraliseerde lenings—sonder kripto-onbestendigheid, 24/7 fondstoegang, en deursigtige, op-ketting-ondersteuning.", "earn-taker-investment-details.usd.description.with_address_link": "Sky is 'n digitale geldmark. Dit bied stabiele opbrengste in VSA-dollar van korttermyn-VSA-tesourieë en oorgekollateraliseerde lenings. Daar is geen kripto-onbestendigheid nie, jy het 24/7 toegang tot fondse, en deursigtige op-ketting-dekking. Beleggings is in 'n subrekening <link>(kopieer 0x)</link> wat deur jou beheer word.", "earn-taker-investment-details.usd.ftx-difference": "<PERSON><PERSON> verskil dit van <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>i of Luna?", "earn-taker-investment-details.usd.high-returns": "Hoe kan die opbrengs so hoog wees, veral in vergelyking met tradisionele banke?", "earn-taker-investment-details.usd.how-is-backed": "Hoe word Sky USD gerugsteun, en wat gebeur met my geld as <PERSON><PERSON> bankrot gaan?", "earn-taker-investment-details.usd.income-sources": "Inkomstebronne 2024", "earn-taker-investment-details.usd.insurance": "Word my fondse deur enige entiteit (soos FDIC of soortgelyk) verse<PERSON> of gewaarborg?", "earn-taker-investment-details.usd.label": "Digitale VSA-dollar", "earn-taker-investment-details.usd.lose-principal": "Kan ek werklik my hoofsom verloor, en onder watter omstandighede?", "earn-taker-investment-details.variable-rate": "Veranderlike koerslenings", "earn-taker-investment-details.withdraw-anytime": "Onttrek enige tyd", "earn-taker-investment-details.yield": "Opbrengs", "earn-withdrawal-view.approve.for": "Vir", "earn-withdrawal-view.approve.into": "Na", "earn-withdrawal-view.swap.into": "Na", "earn-withdrawal-view.withdraw.to": "Na", "earn.add_another_asset.title": "<PERSON><PERSON> <PERSON>n verdien<PERSON>", "earn.add_asset": "<PERSON><PERSON><PERSON> bate by", "earn.asset_view.title": "<PERSON><PERSON>", "earn.base-currency-popup.text": "Die basisgeldeenheid is hoe jou deposito's, opbrengs en transaksies gewaardeer en aangeteken word. As jy in 'n ander geldeenheid deponeer (soos EUR in USD), word jou fondse onmiddellik na die basisgeldeenheid omgeskakel teen huidige wisselkoerse. Na omskakeling bly jou saldo stabiel in die basisgeldeenheid, maar toekomstige onttrekkings kan weer valuta-omskakelings behels.", "earn.base-currency-popup.title": "Basisgeldeenheid", "earn.card-recharge.disabled.list-item.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "earn.card-recharge.enabled.list-item.title": "Outo-her<PERSON>ai geaktiveer", "earn.choose_wallet_to_deposit.title": "<PERSON><PERSON><PERSON><PERSON>", "earn.config.currency.eth": "Verdien Ethereum", "earn.config.currency.on_chain_address_subtitle": "Onchain-adres", "earn.config.currency.us_dollars": "Stel bankoorbetalings op", "earn.configured_widget.current_apy.title": "Huidige APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} Jaarliks", "earn.confirm.currency.cta": "<PERSON><PERSON><PERSON><PERSON>", "earn.currency.eth": "Verdien Ethereum", "earn.deploy.status.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.deploy.status.title_with_taker": "Skep {title} Earn-rekening", "earn.deposit": "Deponeer", "earn.deposit.amount_to_deposit": "Bedrag om te deponeer", "earn.deposit.deposit": "Deponeer", "earn.deposit.enter_amount": "<PERSON><PERSON>r bedrag in", "earn.deposit.no_routes_found": "<PERSON><PERSON> roetes gevind nie", "earn.deposit.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "earn.deposit.select-currency.title": "Kies token om te deponeer", "earn.deposit.select_account.title": "<PERSON><PERSON>", "earn.desposit_form.title": "Deponeer in Verdien", "earn.earn_deposit.status.title": "Deponeer in Earn", "earn.earn_deposit.trx.title": "Deposito na Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "Onttrek fondse enige tyd", "earn.earn_withdraw.status.title": "<PERSON><PERSON><PERSON> uit <PERSON>-<PERSON>kening", "earn.earn_withdraw.trx.title.approval": "<PERSON><PERSON> onttrekking goed", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Onttrek na {asset}", "earn.earn_withdraw.trx.title.withdrawal": "<PERSON><PERSON><PERSON>", "earn.recharge.cta": "Stoor veranderinge", "earn.recharge.earn_not_configured.enable_some_account.error": "<PERSON>kt<PERSON><PERSON> rekening", "earn.recharge.earn_not_configured.enter_amount.error": "<PERSON><PERSON>r bedrag in", "earn.recharge.select_taker.header": "<PERSON><PERSON><PERSON> ka<PERSON> in volgorde van", "earn.recharge_card_tag.on": "aan", "earn.recharge_card_tag.recharge": "<PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharge_not_configured": "Out<PERSON>-<PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharge_off": "Herlaai af", "earn.recharge_card_tag.recharged": "<PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharging": "Besig om te her<PERSON>ai", "earn.recharge_configured.disable.trx.title": "<PERSON><PERSON><PERSON><PERSON> out<PERSON><PERSON><PERSON>", "earn.recharge_configured.trx.disclaimer": "<PERSON>neer jy jou kaart geb<PERSON>, word 'n Cowswap-veiling geskep om dieselfde bedrag as jou betaling met jou <PERSON>ar<PERSON>-bates te koop. Hierdie veilingsproses gee jou gewoonlik die beste markkoers, maar let wel dat die onchain-koers kan verskil van werklike wisselkoerse.", "earn.recharge_configured.trx.subtitle": "Na elke betaling sal kontant outomaties vanaf jou Earn-rekening(s) bygevoeg word om jou kaart se saldo op {value}", "earn.recharge_configured.trx.title": "Stel outomatiese herlaai op {value}", "earn.recharge_configured.updated.trx.title": "<PERSON><PERSON>-instellings", "earn.risk-banner.subtitle": "<PERSON><PERSON><PERSON> is 'n privaat produk met geen <PERSON><PERSON>e beskerming teen verlies nie.", "earn.risk-banner.title": "Verstaan die risiko's", "earn.set_recharge.status.title": "Stel <PERSON>ai", "earn.setup_reacharge.input.disable.label": "<PERSON><PERSON><PERSON><PERSON>", "earn.setup_reacharge.input.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.setup_reacharge_form.title": "<PERSON><PERSON>-<PERSON><PERSON><PERSON> hou jou {br}kaart op dieselfde saldo", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Hou wstETH (Staked ETH) op Gnosis Chain, en leen via Lido uit.", "earn.taker-bulletlist.point_1": "Verdien {apyV<PERSON>ue} jaar<PERSON>s. Opbrengste wissel met die mark.", "earn.taker-bulletlist.point_3": "Zeal hef geen fooie nie.", "earn.taker-historical-returns": "Historiese opbrengste", "earn.taker-historical-returns.chf": "G<PERSON>ei van CHF na USD", "earn.taker-investment-tile.apy.perYear": "per jaar", "earn.takerAPY": "{takerApy} JOP", "earn.takerListItem.apy": "{takerApy} JOP", "earn.takerListItem.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "<PERSON><PERSON> op Gnosis Chain", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "<PERSON><PERSON> van Frankencoin op Gnosis Chain", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "<PERSON><PERSON> van Lido op Gnosis Chain", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "<PERSON><PERSON> van Maker op Gnosis Chain", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "Eth verdien", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH Verdien", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR-mark", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD-mark", "earn.us-treasuries": "VSA-tesourieë (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Alhoe<PERSON><PERSON> uiters skaars, is dit teoreties moontlik. Jou fondse word beskerm deur streng risikobestuur en hoë kollateralisering. Die realistiese slegste scenario sou ongekende marktoestande behels, soos verskeie stablecoins wat gelyktydig hul koppeling verloor—iets wat nog nooit voorheen gebeur het nie.", "earn.usd.can-I-lose-my-principal-popup.title": "Kan ek werklik my hoofsom verloor, en onder watter omstandighede?", "earn.usd.ftx-difference-popup.text": "Sky is fundamenteel anders. <PERSON> as <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> of Luna—wat staatgemaak het op gesentraliseerde bewaring, ondeursigtige batebestuur en riskante hefboomposisies—gebruik Sky USD deursigtige, geo<PERSON><PERSON>rde, gedesentraliseerde slimkontrakte en handhaaf volle op-ketting-deursigtigheid. <PERSON>y behou volle beheer oor jou fondse, wat teenpartyrisiko's verbonde aan gesentraliseerde mislukkings aansienlik verminder.", "earn.usd.ftx-difference-popup.title": "<PERSON><PERSON> verskil dit van <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>i of Luna?", "earn.usd.high-returns-popup.text": "Sky USD genereer hoofsaaklik opbrengste deur gedesentraliseerde finansies (DeFi)-<PERSON><PERSON>olle, wat eweknie-lenings en likiditeitsvoorsiening outomatiseer en tradisionele bankkoste en tussengangers uitskakel. <PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON><PERSON> met robu<PERSON><PERSON> risi<PERSON>troles, maak a<PERSON><PERSON><PERSON> hoër opbrengste moontlik in vergelyking met tradisionele banke.", "earn.usd.high-returns-popup.title": "Hoe kan die opbrengs so hoog wees, veral in vergelyking met tradisionele banke?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD word ten volle gerugsteun en oor-gekollateraliseer deur 'n kombinasie van digitale bates in veilige slimkontrakte en werklike bates soos VSA-tesourieë. Reserwes kan intyds op die ketting geouditeer word, selfs vanuit Zeal, wat deursigtigheid en sekuriteit bied. In die onwaarskynlike geval dat Zeal sluit, bly jou bates op die ketting beveilig, ten volle onder jou beheer, en toeganklik deur ander versoenbare beursies.", "earn.usd.how-is-sky-backed-popup.title": "Hoe word Sky USD gerugsteun, en wat gebeur met my geld as <PERSON><PERSON> bankrot gaan?", "earn.usd.insurance-popup.text": "Sky USD-fondse is nie FDIC-verseker of gerugsteun deur tradisionele staatswaarborge nie, want dit is 'n digitale bate-gebaseerde rekening, nie 'n konvensionele bankrekening nie. In plaas daarvan bestuur Sky alle risikobeperking deur geouditeerde slimkontrakte en noukeurig gekeurde DeFi-protokolle, wat verseker dat bates veilig en deursigtig bly.", "earn.usd.insurance-popup.title": "Word my fondse deur enige entiteit (soos FDIC of soortgelyk) verse<PERSON> of gewaarborg?", "earn.usd.lending-operations-popup.text": "Sky USD genereer opbrengs deur stablecoins uit te leen via gedesentraliseerde leningsmarkte soos Morpho en Spark. Jou stablecoins word aan leners geleen wat aansienlik meer kollateraal—soos ETH of BTC—deponeer as die waarde van hul lening. <PERSON><PERSON><PERSON>, gena<PERSON>d oor-kollateralisering, verseker dat daar altyd genoeg kollateraal is om lenings te dek, wat risiko aansienlik verminder. Die ingesamelde rente en af en toe likwidasiefooie wat deur leners betaal word, bied betroubare, deursigtige en veilige opbrengste.", "earn.usd.lending-operations-popup.title": "Leningsbedrywighede", "earn.usd.market-making-operations-popup.text": "Sky USD verdien bykomende opbrengs deur deel te neem aan gedesentral<PERSON> beurse (AMM's) soos Curve of Uniswap. Deur likiditeit te verskaf—deur jou stablecoins in poele te plaas wat kriptohandel fasiliteer—vang Sky USD fooie wat uit transaksies gegenereer word. Hierdie likiditeitspoele word noukeurig gekies om wisselvalligheid te minimaliseer, hoofsaaklik deur stablecoin-tot-stablecoin-pare te gebruik om risiko's soos impermanente verlies aansienlik te verminder, en hou jou bates veilig en toeganklik.", "earn.usd.market-making-operations-popup.title": "Markmakingsbedrywighede", "earn.usd.treasury-operations-popup.text": "Sky USD genereer stabiele, konsekwente opbrengs deur strategiese tesouriebeleggings. 'n <PERSON><PERSON> van j<PERSON> stablecoin-deposito's word toegewys aan veilige, lae-risiko werklike bates—hoofsaaklik korttermyn-staatseffekte en hoogs veilige kredietinstrumente. <PERSON><PERSON><PERSON>, soortgelyk aan tradisionele bankdienste, verseker voorspelbare en betroubare opbrengs. <PERSON><PERSON> bates bly veilig, likied en word deursigtig bestuur.", "earn.usd.treasury-operations-popup.title": "Te<PERSON>uri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.view_earn.card_rechard_off": "Af", "earn.view_earn.card_rechard_on": "<PERSON><PERSON>", "earn.view_earn.card_recharge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.view_earn.total_balance_label": "<PERSON>en {percentage} per jaar", "earn.view_earn.total_earnings_label": "Totale verdienste", "earn.withdraw": "Onttrek", "earn.withdraw.amount_to_withdraw": "Bedrag om te onttrek", "earn.withdraw.enter_amount": "<PERSON><PERSON>r bedrag in", "earn.withdraw.loading": "Laai tans", "earn.withdraw.no_routes_found": "<PERSON><PERSON> roetes gevind nie", "earn.withdraw.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "earn.withdraw.select-currency.title": "Kies token", "earn.withdraw.select_to_token": "Kies token", "earn.withdraw.withdraw": "Onttrek", "earn.withdraw_form.title": "Onttrek uit Verdien", "earnings-view.earnings": "Totale verdienste", "edit-account-owners.add-owner.add-wallet": "<PERSON><PERSON><PERSON> e<PERSON> by", "edit-account-owners.add-owner.add_wallet": "<PERSON><PERSON><PERSON> be<PERSON>ie by", "edit-account-owners.add-owner.title": "<PERSON><PERSON><PERSON> by", "edit-account-owners.card-owners": "Kaarteienaars", "edit-account-owners.external-wallet": "Eksterne beursie", "editBankRecipient.title": "<PERSON><PERSON><PERSON> on<PERSON>", "editNetwork.addCustomRPC": "Voeg pasgemaakte RPC-nodus by", "editNetwork.cannot_verify.subtitle": "Pasgemaakte RPC reageer nie. Gaan die URL na.", "editNetwork.cannot_verify.title": "Ons kan nie die RPC-nodus verifieer nie", "editNetwork.cannot_verify.try_again": "<PERSON><PERSON><PERSON> weer", "editNetwork.customRPCNode": "Pasgemaakte RPC-nodus", "editNetwork.defaultRPC": "Standaard-RPC", "editNetwork.networkRPC": "Netwerk-RPC", "editNetwork.rpc_url.cannot_be_empty": "<PERSON><PERSON><PERSON>", "editNetwork.rpc_url.not_a_valid_https_url": "Moet 'n geldige HTTP(S) URL wees", "editNetwork.safetyWarning.subtitle": "Let wel: Pasgemaakte RPC's is riskant.", "editNetwork.safetyWarning.title": "Pasgemaakte RPC's kan onveilig wees", "editNetwork.zealRPCNode": "Zeal RPC-nodus", "editNetworkRpc.headerTitle": "Pasgemaakte RPC-nodus", "editNetworkRpc.rpcNodeUrl": "RPC-nodus URL", "editing-locked.modal.description": "<PERSON> as <PERSON><PERSON><PERSON><PERSON>stransaks<PERSON>, laat <PERSON><PERSON><PERSON> jou nie toe om die bestedingslimiet of vervaltyd te wysig nie. <PERSON>ak seker jy vertrou 'n dApp voordat jy 'n Permit indien.", "editing-locked.modal.title": "Wysiging gesluit", "enable-recharge-for-smart-wallet.enabling-recharge.title": "<PERSON><PERSON><PERSON><PERSON>", "enable-recharge-for-smart-wallet.recharge-enabled.title": "<PERSON><PERSON><PERSON> geaktiveer", "enterCardnumber": "<PERSON><PERSON><PERSON> ka<PERSON> in", "error.connectivity_error.subtitle": "Gaan asseblief jou internetverbinding na en probeer weer.", "error.connectivity_error.title": "<PERSON>n internetverbinding", "error.decrypt_incorrect_password.title": "Verkeerde wagwoord", "error.encrypted_object_invalid_format.title": "Beskadigde data", "error.failed_to_fetch_google_auth_token.title": "Ons kon nie toegang kry nie", "error.list.item.cta.action": "<PERSON><PERSON><PERSON> weer", "error.trezor_action_cancelled.title": "Transaksie verwerp", "error.trezor_device_used_elsewhere.title": "Toestel word in 'n ander sessie gebruik", "error.trezor_method_cancelled.title": "Kon nie Trez<PERSON> sink<PERSON>er nie", "error.trezor_permissions_not_granted.title": "Kon nie Trez<PERSON> sink<PERSON>er nie", "error.trezor_pin_cancelled.title": "Kon nie Trez<PERSON> sink<PERSON>er nie", "error.trezor_popup_closed.title": "Kon nie Trez<PERSON> sink<PERSON>er nie", "error.unblock_account_number_and_sort_code_mismatch": "Rekeningnommer en sort-kode stem nie ooreen nie", "error.unblock_can_not_change_details_after_kyc": "Kan nie besonderhede na KYC verander nie", "error.unblock_hard_kyc_failure": "Onverwagte KYC-status", "error.unblock_invalid_faster_payment_configuration.title": "Hierdie bank ondersteun nie Vinniger Betalings nie", "error.unblock_invalid_iban": "Ongeldige IBAN", "error.unblock_session_expired.title": "Unblock-sessie het verval", "error.unblock_user_with_address_already_exists.title": "Rekening reeds opgestel vir adres", "error.unblock_user_with_such_email_already_exists.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON> <PERSON><PERSON>pos bestaan reeds", "error.unknown_error.error_message": "Foutboodskap: ", "error.unknown_error.subtitle": "Jammer! As jy dringende hulp nodig het, kontak asseblief bystand en deel die besonderhede hieronder.", "error.unknown_error.title": "Stelselfout", "eth-cost-warning-modal.subtitle": "Slim beursies werk op Ethereum, maar die fooie is baie hoog en ons beveel STERK aan om eerder ander netwerke te gebruik.", "eth-cost-warning-modal.title": "<PERSON>ermy Ethereum - netwerkfooie is hoog", "exchange.form.button.chain_unsupported": "Ketting nie ondersteun nie", "exchange.form.button.refreshing": "Ver<PERSON>ris tans", "exchange.form.error.asset_not_supported.button": "<PERSON><PERSON> <PERSON><PERSON> ander bate", "exchange.form.error.asset_not_supported.description": "Brug ondersteun nie die oorbrugging van hierdie bate nie.", "exchange.form.error.asset_not_supported.title": "<PERSON>e nie onders<PERSON>un nie", "exchange.form.error.bridge_quote_timeout.button": "<PERSON><PERSON> <PERSON><PERSON> ander bate", "exchange.form.error.bridge_quote_timeout.description": "Probeer 'n ander paar tokens", "exchange.form.error.bridge_quote_timeout.title": "<PERSON>n omruiling gevind nie", "exchange.form.error.different_receiver_not_supported.button": "Verwyder alternatiewe ontvanger", "exchange.form.error.different_receiver_not_supported.description": "<PERSON><PERSON>ie omruiling ondersteun nie die stuur na 'n ander adres nie.", "exchange.form.error.different_receiver_not_supported.title": "Stuur- en ontvangsadres moet dieselfde wees", "exchange.form.error.insufficient_input_amount.button": "Verhoog bedrag", "exchange.form.error.insufficient_liquidity.button": "<PERSON><PERSON><PERSON><PERSON> bedrag", "exchange.form.error.insufficient_liquidity.description": "Die brug het nie genoeg bates nie. Probeer 'n kleiner bedrag.", "exchange.form.error.insufficient_liquidity.title": "Bedrag te hoog", "exchange.form.error.max_amount_exceeded.button": "<PERSON><PERSON><PERSON><PERSON> bedrag", "exchange.form.error.max_amount_exceeded.description": "Die maksimum bedrag is oorskry.", "exchange.form.error.max_amount_exceeded.title": "Bedrag te hoog", "exchange.form.error.min_amount_not_met.button": "Verhoog bedrag", "exchange.form.error.min_amount_not_met.description": "Daar word nie aan die minimum omruilbedrag vir hierdie token voldoen nie.", "exchange.form.error.min_amount_not_met.description_with_amount": "Die minimum omruilbedrag is {amount}.", "exchange.form.error.min_amount_not_met.title": "Bedrag te laag", "exchange.form.error.min_amount_not_met.title_increase": "Verhoog bedrag", "exchange.form.error.no_routes_found.button": "<PERSON><PERSON> <PERSON><PERSON> ander bate", "exchange.form.error.no_routes_found.description": "<PERSON><PERSON> is geen omruilroete beskikbaar vir hierdie token/netwerk-kombinasie nie.", "exchange.form.error.no_routes_found.title": "<PERSON><PERSON> om<PERSON><PERSON> beski<PERSON> nie", "exchange.form.error.not_enough_balance.button": "<PERSON><PERSON><PERSON><PERSON> bedrag", "exchange.form.error.not_enough_balance.description": "<PERSON>y het nie geno<PERSON> van hierdie bate vir die transaksie nie.", "exchange.form.error.not_enough_balance.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "exchange.form.error.slippage_passed_is_too_low.button": "Verhoog glip", "exchange.form.error.slippage_passed_is_too_low.description": "Toegelate glip is te laag vir hierdie bate.", "exchange.form.error.slippage_passed_is_too_low.title": "<PERSON>lip te laag", "exchange.form.error.socket_internal_error.button": "<PERSON><PERSON><PERSON> later weer", "exchange.form.error.socket_internal_error.description": "Oorbruggingsvennoot ondervind probleme. Probeer later weer.", "exchange.form.error.socket_internal_error.title": "Fout by oorbruggingsvennoot", "exchange.form.error.stargatev2_requires_fee_in_native": "Voeg by {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Voeg by {amount} om transaksie te voltooi", "exchange.form.error.stargatev2_requires_fee_in_native.title": "<PERSON><PERSON><PERSON> {symbol}", "expiration-info.modal.description": "Vervaltyd is hoe lank 'n app jou tokens kan gebruik. Wanneer die tyd om is, verloor hulle toegang totdat jy anders sê. <PERSON>ir veiligheid, hou vervaltye kort.", "expiration-info.modal.title": "Wat is vervaltyd?", "expiration-time.high.modal.text": "<PERSON><PERSON><PERSON><PERSON><PERSON> moet kort wees en gebaseer op hoe lank jy dit werklik benodig. <PERSON> tye is riskant en gee swendelaars meer kans om jou tokens te misbruik.", "expiration-time.high.modal.title": "Lang vervalt<PERSON>", "failed.transaction.content": "Transaksie gaan waarskynlik misluk", "fee.unknown": "Onbekend", "feedback-request.leave-message": "Los 'n boodskap", "feedback-request.not-now": "Nie nou nie", "feedback-request.title": "Dankie! Hoe kan ons Zeal verbeter?", "float.input.period": "<PERSON><PERSON><PERSON>", "gnosis-activate-card.info-popup.subtitle": "Vir jou eerste transaksie moet jy jou kaart insit en jou PIN invoer. <PERSON><PERSON>na sal kontaklose betalings werk.", "gnosis-activate-card.info-popup.title": "Eerste betaling vereis Chip & PIN", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "<PERSON><PERSON><PERSON> jou kaart<PERSON> in om dit te aktiveer.", "gnosis-activate-card.title": "Kaartnommer", "gnosis-pay-re-kyc-widget.btn-text": "Verifieer", "gnosis-pay-re-kyc-widget.title.not-started": "Verifieer jou identiteit", "gnosis-pay.login.cta": "<PERSON><PERSON> bestaande rekening", "gnosis-pay.login.title": "Jy het reeds 'n Gnosis Pay-rekening", "gnosis-signup.confirm.subtitle": "Soek vir 'n e-pos van G<PERSON>, dit mag dalk in jou spam-lêer wees.", "gnosis-signup.confirm.title": "Nie verifikasie-e-pos ontvang nie?", "gnosis-signup.continue": "Gaan voort", "gnosis-signup.dont_link_accounts": "<PERSON><PERSON> rekeninge verbind", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "<PERSON><PERSON><PERSON> <EMAIL> in", "gnosis-signup.enter-email.title": "Voer e-pos in", "gnosis-signup.title": "Ek het gelees en stem saam met Gnosis Pay se <linkGnosisTNC>Bepalings & V.</linkGnosisTNC> <monovateTerms>Ka<PERSON>houer se bep.</monovateTerms> en <linkMonerium>Monerium se B&V</linkMonerium>.", "gnosis-signup.verify-email.title": "Verifieer e-pos", "gnosis.confirm.subtitle": "Kode nie ontvang nie? Maak seker jou foonnommer is korrek", "gnosis.confirm.title": "Kode gestuur na {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Verifieer", "gnosisPayAccountStatus.success.title": "<PERSON><PERSON>", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay is nog nie in jou land beskikbaar nie", "gnosisPayNoActiveCardsFound.title": "<PERSON><PERSON> a<PERSON>", "gnosis_pay_card_delay_relay_not_empty_error.title": "<PERSON>u transaksie kan nie nou verwerk word nie. <PERSON><PERSON><PERSON> asseb<PERSON>f later weer", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "<PERSON><PERSON> nie moontlik nie", "gnosiskyc.modal.approved.activate-free-card": "Akt<PERSON><PERSON> gratis kaart", "gnosiskyc.modal.approved.button-text": "Deponeer vanaf bank", "gnosiskyc.modal.approved.title": "Jou persoonlike rekeningbesonderhede is geskep", "gnosiskyc.modal.failed.close": "Maak toe", "gnosiskyc.modal.failed.title": "<PERSON><PERSON>, ons vennoot Gnosis Pay kan nie 'n rekening vir jou skep nie", "gnosiskyc.modal.in-progress.title": "ID-verifi<PERSON><PERSON> kan 24 uur of langer neem. Wees asseblief geduldig", "goToSettingsPopup.settings": "Instellings", "goToSettingsPopup.title": "Aktiveer kennisgewings in jou instellings.", "google_file.error.failed_to_fetch_auth_token.button_title": "<PERSON><PERSON><PERSON> weer", "google_file.error.failed_to_fetch_auth_token.subtitle": "Om ons toe te laat om jou Her<PERSON>er te gebruik, verleen asseblief toegang op jou persoonlike wolk.", "google_file.error.failed_to_fetch_auth_token.title": "Ons kon nie toegang kry nie", "hidden_tokens.widget.emptyState": "Geen verborge tokens nie", "how_to_connect_to_metamask.got_it": "OK, ek verstaan", "how_to_connect_to_metamask.story.subtitle": "Wissel enige tyd maklik tussen Zeal en ander beursies.", "how_to_connect_to_metamask.story.title": "Zeal werk saam met ander beursies", "how_to_connect_to_metamask.why_switch": "Hoekom wissel tussen Zeal en ander beursies?", "how_to_connect_to_metamask.why_switch.description": "Ons weet dis moeilik om die sprong te waag en 'n nuwe beursie te begin gebruik. <PERSON><PERSON><PERSON> het ons dit maklik gemaak om Zeal saam met jou bestaande beursie te gebruik. Wissel enige tyd.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "Ons weet dis moeilik om die sprong te waag en 'n nuwe beursie te begin gebruik. <PERSON><PERSON><PERSON> het ons dit maklik gemaak om Zeal saam met jou bestaande beursie te gebruik. Skakel enige tyd oor.", "import-bank-transfer-owner.banner.title": "Die bankoorplasing-beursie is verander. V<PERSON><PERSON> jou beursie in om voort te gaan.", "import-bank-transfer-owner.title": "<PERSON><PERSON><PERSON> beursie in om bankoorbetalings op hierdie toestel te gebruik", "import_gnosispay_wallet.add-another-card-owner.footnote": "<PERSON><PERSON><PERSON> privaat sleutel of saadfrase in wat jou Gnosis Pay-kaart besit", "import_gnosispay_wallet.primaryText": "<PERSON><PERSON><PERSON>-beursie in", "injected-wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "intercom.getHelp": "<PERSON><PERSON> hulp", "invalid_iban.got_it": "<PERSON><PERSON><PERSON><PERSON>", "invalid_iban.subtitle": "Die IBAN wat ingevoer is, is nie geldig nie. Ma<PERSON> asseblief seker die besonderhede is kor<PERSON> ingevoer en probeer weer.", "invalid_iban.title": "Ongeldige IBAN", "keypad-0": "Sleutelbord sleutel 0", "keypad-1": "Sleutelbord sleutel 1", "keypad-2": "Sleutelbord sleutel 2", "keypad-3": "Sleutelbord sleutel 3", "keypad-4": "Sleutelbord sleutel 4", "keypad-5": "Sleutelbord sleutel 5", "keypad-6": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sleutel 6", "keypad-7": "Sleutelbord sleutel 7", "keypad-8": "Sleutelbord sleutel 8", "keypad-9": "Sleutelbord sleutel 9", "keypad.biometric-button": "Biometriese knoppie vir sleutelbord", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Geheime Frase beveilig 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "Bekyk frase", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "<PERSON><PERSON><PERSON> <PERSON>n <PERSON><PERSON> vanlyn kopie van jou Geheime Frase sodat jy later jou bates kan herstel", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "<PERSON><PERSON> probeer om die woord te raai nie", "keystore.write_secret_phrase.before_you_begin.first_point": "<PERSON><PERSON> verstaan dat en<PERSON><PERSON><PERSON> met my Geheime Frase my bates kan oordra", "keystore.write_secret_phrase.before_you_begin.second_point": "<PERSON><PERSON> is verantwoordelik om my Geheime Frase geheim en veilig te hou", "keystore.write_secret_phrase.before_you_begin.subtitle": "Lees en a<PERSON>ar asseblief die volgende punte:", "keystore.write_secret_phrase.before_you_begin.third_point": "<PERSON><PERSON> is op 'n private plek met geen mense of kameras om my nie", "keystore.write_secret_phrase.before_you_begin.title": "<PERSON><PERSON><PERSON>t jy begin", "keystore.write_secret_phrase.secret_phrase_test.title": "Wat is woord {count} in jou Geheime Frase?", "keystore.write_secret_phrase.test_ps.lets_do_it": "Kom ons doen dit", "keystore.write_secret_phrase.test_ps.subtitle": "<PERSON>y sal jou Geheime Frase nodig hê om jou rekening op hierdie of ander toestelle te herstel. Kom ons toets of jou Geheime Frase korrek ne<PERSON>geskryf is.", "keystore.write_secret_phrase.test_ps.subtitle2": "Ons sal jou vra vir {count} woorde in jou frase.", "keystore.write_secret_phrase.test_ps.title": "Toets Rekeningherstel", "kyc.modal.approved.button-text": "<PERSON><PERSON>", "kyc.modal.approved.subtitle": "<PERSON><PERSON> veri<PERSON> is voltooi. Jy kan nou onbeperkte bankoorbetalings doen.", "kyc.modal.approved.title": "Bankoorbetalings ontsluit", "kyc.modal.continue-with-partner.button-text": "Gaan voort", "kyc.modal.continue-with-partner.subtitle": "Ons stuur jou nou na ons vennoot om jou dokumente te versamel en verifikasie te voltooi.", "kyc.modal.continue-with-partner.title": "<PERSON><PERSON> voort met ons vennoot", "kyc.modal.failed.unblock.subtitle": "Unblock het nie jou identiteitsverifikasie goedgekeur nie en kan nie bankoorbetalingsdienste aan jou verskaf nie", "kyc.modal.failed.unblock.title": "Unblock-aansoek nie goedgekeur nie", "kyc.modal.paused.button-text": "<PERSON><PERSON> besonder<PERSON>e op", "kyc.modal.paused.subtitle": "<PERSON> jou inligting blyk verkeerd te wees. <PERSON><PERSON>r weer en maak seker jou besonderhede is korrek voordat jy dit indien.", "kyc.modal.paused.title": "<PERSON><PERSON> beson<PERSON><PERSON>e lyk verkeerd", "kyc.modal.pending.button-text": "Maak toe", "kyc.modal.pending.subtitle": "Verifi<PERSON><PERSON> neem gewoonlik minder as 10 minute, maar dit kan soms langer duur.", "kyc.modal.pending.title": "Ons hou jou op hoogte", "kyc.modal.required.cta": "<PERSON><PERSON> verifi<PERSON>ie", "kyc.modal.required.subtitle": "Jy het die transaksielimiet bereik. Verifieer asb. jou identiteit om voort te gaan. Dit neem gewoonlik net 'n paar minute en vereis persoonlike besonderhede en dokumentasie.", "kyc.modal.required.title": "Identiteitsverifikasie vereis", "kyc.submitted": "Ingedien", "kyc.submitted_short": "Ingedien", "kyc_status.completed_status": "Voltooi", "kyc_status.failed_status": "Misluk", "kyc_status.paused_status": "Hersiening", "kyc_status.subtitle": "Bankoorbetalings", "kyc_status.subtitle.wrong_details": "Verkeerde besonderhede", "kyc_status.subtitle_in_progress": "In proses", "kyc_status.title": "Verifieer identiteit", "label.close": "Maak toe", "label.saving": "Stoor...", "labels.this-month": "<PERSON><PERSON><PERSON> maand", "labels.today": "Vandag", "labels.yesterday": "Gister", "language.selector.title": "Taal", "ledger.account_loaded.imported": "Ingevoer", "ledger.add.success.title": "Ledger suksesvol gekoppel 🎉", "ledger.connect.cta": "Sinkron<PERSON><PERSON> Ledger", "ledger.connect.step1": "Koppel Ledger aan j<PERSON>", "ledger.connect.step2": "Maak die Ethereum-toe<PERSON> op Ledger oop", "ledger.connect.step3": "Sinkroniseer dan jou <PERSON> 👇", "ledger.connect.subtitle": "Volg hierdie stappe om jou Ledger-beursies na Zeal in te voer", "ledger.connect.title": "<PERSON>ppel Ledger a<PERSON>", "ledger.error.ledger_is_locked.subtitle": "Ontsluit Ledger en maak die Ethereum-toep oop", "ledger.error.ledger_is_locked.title": "Ledger is gesluit", "ledger.error.ledger_not_connected.action": "Sinkron<PERSON><PERSON> Ledger", "ledger.error.ledger_not_connected.subtitle": "<PERSON><PERSON> jou hardeware-beursie aan jou toestel en maak die Ethereum-toep oop", "ledger.error.ledger_not_connected.title": "Ledger is nie gekoppel nie", "ledger.error.ledger_running_non_eth_app.title": "Ethereum-toe<PERSON> nie oop<PERSON><PERSON><PERSON> nie", "ledger.error.user_trx_denied_by_user.action": "Maak toe", "ledger.error.user_trx_denied_by_user.subtitle": "Jy het die transaksie op jou hardeware-beursie verwerp", "ledger.error.user_trx_denied_by_user.title": "Transaksie verwerp", "ledger.hd_path.bip44.subtitle": "bv. <PERSON>, Trezor", "ledger.hd_path.bip44.title": "BIP44-<PERSON><PERSON><PERSON>", "ledger.hd_path.ledger_live.subtitle": "Verstek", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Legacy", "ledger.hd_path.phantom.subtitle": "bv. <PERSON>", "ledger.select.hd_path.subtitle": "HD-roe<PERSON> is hoe hardeware-beursies hul rekeninge sorteer. Dis soos hoe 'n indeks bladsye in 'n boek sorteer.", "ledger.select.hd_path.title": "<PERSON><PERSON>-roe<PERSON>", "ledger.select_account.import_wallets_count": "{count, plural, offset:0 =0 {<PERSON><PERSON> beursies gekies nie} one {<PERSON><PERSON><PERSON> beursie in} other {Voer {count} beursies in}}", "ledger.select_account.path_settings": "Roete-instellings", "ledger.select_account.subtitle": "Sien jy nie die beursies wat jy verwag nie? <PERSON><PERSON>r die roete-instellings verander", "ledger.select_account.subtitle.group_header": "Beursies", "ledger.select_account.title": "<PERSON><PERSON><PERSON>-beurs<PERSON> in", "legend.lending-operations": "Leningsbedrywighede", "legend.market_making-operations": "Markmakingsbedrywighede", "legend.treasury-operations": "Te<PERSON>uri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link-existing-monerium-account-sign.button": "<PERSON><PERSON>", "link-existing-monerium-account-sign.subtitle": "Jy het reeds 'n Monerium-rekening.", "link-existing-monerium-account-sign.title": "<PERSON><PERSON> Z<PERSON> aan jou bestaande Monerium-rekening", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "<PERSON>y het reeds 'n Monerium-rekening. Gaan asseblief na die Monerium-toep om opstelling te voltooi.", "link-existing-monerium-account.title": "Gaan na Monerium om jou rekening te koppel", "loading.pin": "Laai PIN...", "lockScreen.passwordIncorrectMessage": "Wagwo<PERSON> is verkeerd", "lockScreen.passwordRequiredMessage": "Wagwoord vereis", "lockScreen.unlock.header": "Ontsluit", "lockScreen.unlock.subheader": "Gebruik jou wagwoord om Zeal te ontsluit", "mainTabs.activity.label": "Aktiwiteit", "mainTabs.browse.label": "B<PERSON>ai", "mainTabs.browse.title": "B<PERSON>ai", "mainTabs.card.label": "<PERSON><PERSON>", "mainTabs.portfolio.label": "Portefeulje", "mainTabs.rewards.label": "Belonings", "makeSpendable.cta": "<PERSON><PERSON>", "makeSpendable.holdAsCash": "<PERSON><PERSON> as kontant", "makeSpendable.shortText": "Verdien {apy} per jaar", "makeSpendable.title": "{amount} ontvang", "merchantCategory.agriculture": "Landbou", "merchantCategory.alcohol": "Alkohol", "merchantCategory.antiques": "Antiekware", "merchantCategory.appliances": "<PERSON><PERSON><PERSON>", "merchantCategory.artGalleries": "Kunsgalerye", "merchantCategory.autoRepair": "Motorherstelwerk", "merchantCategory.autoRepairService": "Motorhersteldiens", "merchantCategory.beautyFitnessSpas": "Skoonheid, Fiksheid & Spas", "merchantCategory.beautyPersonalCare": "Skoonheid & Persoonlike Sorg", "merchantCategory.billiard": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.books": "<PERSON><PERSON><PERSON>", "merchantCategory.bowling": "Boul", "merchantCategory.businessProfessionalServices": "Besigheid & Professionele Dienste", "merchantCategory.carRental": "Motorhuur", "merchantCategory.carWash": "Motorwas", "merchantCategory.cars": "Motors", "merchantCategory.casino": "Casino", "merchantCategory.casinoGambling": "Casino & Dobbel", "merchantCategory.cellular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.charity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.childcare": "Kindersorg", "merchantCategory.cigarette": "Si<PERSON><PERSON>", "merchantCategory.cinema": "Bioskoop", "merchantCategory.cinemaEvents": "Bioskoop & Geleenthede", "merchantCategory.cleaning": "S<PERSON>onmaak", "merchantCategory.cleaningMaintenance": "Skoonmaak & Instandhouding", "merchantCategory.clothes": "<PERSON><PERSON><PERSON>", "merchantCategory.clothingServices": "Kleredienste", "merchantCategory.communicationServices": "Kommunikasiedienste", "merchantCategory.construction": "Konstruksie", "merchantCategory.cosmetics": "Skoonheidsmiddels", "merchantCategory.craftsArtSupplies": "Handwerk & Kunsvoorrade", "merchantCategory.datingServices": "Afspraakdienste", "merchantCategory.delivery": "Aflewering", "merchantCategory.dentist": "Tandarts", "merchantCategory.departmentStores": "Afdelingswinkels", "merchantCategory.directMarketingSubscription": "Direkte Bemarking & Intekening", "merchantCategory.discountStores": "Afslagwinkels", "merchantCategory.drugs": "Medisyne", "merchantCategory.dutyFree": "Belastingvry", "merchantCategory.education": "Onderwys", "merchantCategory.electricity": "Elektrisiteit", "merchantCategory.electronics": "Elektronika", "merchantCategory.emergencyServices": "Nooddienste", "merchantCategory.equipmentRental": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.evCharging": "EV-laai", "merchantCategory.financialInstitutions": "Finansiële instellings", "merchantCategory.financialProfessionalServices": "Finansiële & Professionele Dienste", "merchantCategory.finesPenalties": "Boetes & Strawwe", "merchantCategory.fitness": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.flights": "Vlugte", "merchantCategory.flowers": "Blomme", "merchantCategory.flowersGarden": "Blomme & Tuin", "merchantCategory.food": "<PERSON><PERSON>", "merchantCategory.freight": "Vrag", "merchantCategory.fuel": "<PERSON><PERSON><PERSON>", "merchantCategory.funeralServices": "Begrafnisdienste", "merchantCategory.furniture": "<PERSON><PERSON><PERSON>", "merchantCategory.games": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.gas": "Gas", "merchantCategory.generalMerchandiseRetail": "Algemene Handelsware & Kleinhandel", "merchantCategory.gifts": "Geskenke", "merchantCategory.government": "Regering", "merchantCategory.governmentServices": "Regeringsdienste", "merchantCategory.hardware": "Hardeware", "merchantCategory.healthMedicine": "Gesondheid & Medisyne", "merchantCategory.homeImprovement": "Huisverbetering", "merchantCategory.homeServices": "Huisdienste", "merchantCategory.hotel": "Hotel", "merchantCategory.housing": "Be<PERSON>sing", "merchantCategory.insurance": "Versekering", "merchantCategory.internet": "Internet", "merchantCategory.kids": "Kinders", "merchantCategory.laundry": "Wassery", "merchantCategory.laundryCleaningServices": "Wassery & Skoonmaakdienste", "merchantCategory.legalGovernmentFees": "Regs- & Regeringsfooie", "merchantCategory.luxuries": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.luxuriesCollectibles": "Luukshede & Versamelstukke", "merchantCategory.magazines": "Tydskrifte", "merchantCategory.magazinesNews": "Tydskrifte & Nuus", "merchantCategory.marketplaces": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.media": "Media", "merchantCategory.medicine": "Medisyne", "merchantCategory.mobileHomes": "<PERSON><PERSON><PERSON>", "merchantCategory.moneyTransferCrypto": "Geldoordrag & Kripto", "merchantCategory.musicRelated": "Musiekverwant", "merchantCategory.musicalInstruments": "Musiekinstrumente", "merchantCategory.optics": "Optika", "merchantCategory.organizationsClubs": "Organisasies & Klubs", "merchantCategory.other": "Ander", "merchantCategory.parking": "<PERSON><PERSON>", "merchantCategory.pawnShops": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.pets": "Troeteldiere", "merchantCategory.photoServicesSupplies": "Fotodienste & -voorrade", "merchantCategory.postalServices": "Posdienste", "merchantCategory.professionalServicesOther": "Profess<PERSON><PERSON> Dienste (Ander)", "merchantCategory.publicTransport": "<PERSON><PERSON><PERSON> vervoer", "merchantCategory.purchases": "Aankope", "merchantCategory.purchasesMiscServices": "Aankope & Diverse Dienste", "merchantCategory.recreationServices": "Ontspanningsdienste", "merchantCategory.religiousGoods": "Godsdienstige Goedere", "merchantCategory.secondhandRetail": "Tweedehandse Kleinhandel", "merchantCategory.shoeHatRepair": "Skoen- & Hoedherstel", "merchantCategory.shoeRepair": "Skoenherstel", "merchantCategory.softwareApps": "Sagteware & Toeps", "merchantCategory.specializedRepairs": "Gespesialiseerde Herstelwerk", "merchantCategory.sport": "Sport", "merchantCategory.sportingGoods": "Sportgoedere", "merchantCategory.sportingGoodsRecreation": "Sportgoedere & Ontspanning", "merchantCategory.sportsClubsFields": "Sportklubs & -velde", "merchantCategory.stationaryPrinting": "Skryfbehoeftes & Drukwerk", "merchantCategory.stationery": "Skryfbehoeftes", "merchantCategory.storage": "<PERSON><PERSON>", "merchantCategory.taxes": "Belasting", "merchantCategory.taxi": "Taxi", "merchantCategory.telecomEquipment": "Telekom-toerusting", "merchantCategory.telephony": "Telefonie", "merchantCategory.tobacco": "Tabak", "merchantCategory.tollRoad": "Tolpad", "merchantCategory.tourismAttractionsAmusement": "Toerisme, Besienswaardighede & Vermaak", "merchantCategory.towing": "Sleepdienste", "merchantCategory.toys": "Speelgoed", "merchantCategory.toysHobbies": "Speelgoed & Stokperdjies", "merchantCategory.trafficFine": "Verkeersboete", "merchantCategory.train": "<PERSON><PERSON><PERSON>", "merchantCategory.travelAgency": "Reisagentskap", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, Radio & Stroming", "merchantCategory.utilities": "Nutdienste", "merchantCategory.waterTransport": "Watervervoer", "merchantCategory.wholesaleClubs": "Groothandelklubs", "metaMask.subtitle": "Aktiveer MetaMask-modus om alle MetaMask-verbindings na Zeal te herlei. As jy op MetaMask in dApps klik, sal dit dan aan <PERSON> koppel.", "metaMask.title": "Kan jy nie met <PERSON><PERSON> koppel nie?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "<PERSON><PERSON> jou <PERSON>ep oop", "monerium-bank-deposit.buttet-point.receive-crypto": "Ontvang digitale EUR", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Stuur {fiatCurrencyCode} na jou rekening", "monerium-bank-deposit.deposit-account-country": "Land", "monerium-bank-deposit.header": "{fullName} se persoonlike rekening", "monerium-bank-details.account-name": "<PERSON><PERSON><PERSON><PERSON>", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT gekopieer", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT gekopieer", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN gekopieer", "monerium-bank-details.to-wallet": "<PERSON> beursie", "monerium-bank-details.transfer-fee": "Oorbetalingsfooi", "monerium-bank-transfer.enable-card.bullet-1": "Voltooi identiteitsverifikasie", "monerium-bank-transfer.enable-card.bullet-2": "<PERSON>ry persoonlike rekeningbesonderhede", "monerium-bank-transfer.enable-card.bullet-3": "Deponeer vanaf bankrekening", "monerium-card-delay-relay.success.cta": "Maak toe", "monerium-card-delay-relay.success.subtitle": "Vir sekuriteitsredes neem veranderinge aan kaartinstellings 3 minute om verwerk te word.", "monerium-card-delay-relay.success.title": "Kom terug oor 3min om Monerium-opstelling voort te sit", "monerium-deposit.account-details-info-popup.bullet-point-1": "Enige {fiatCurrencyCode} wat jy na hierdie rekening stuur, sal outomaties omskakel na {cryptoCurrencyCode} tokens op {cryptoCurrencyChain} Chain en na jou beursie gestuur word", "monerium-deposit.account-details-info-popup.bullet-point-2": "STUUR SLEGS {fiatCurrencyCode} ({fiatCurrencySymbol}) na jou rekening", "monerium-deposit.account-details-info-popup.title": "<PERSON><PERSON>", "monerium.check_order_status.sending": "Stuur tans", "monerium.not-eligible.cta": "Terug", "monerium.not-eligible.subtitle": "Monerium kan nie 'n rekening vir jou oopmaak nie. Kies asseblief 'n alternatiewe verskaffer.", "monerium.not-eligible.title": "<PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> ve<PERSON>", "monerium.setup-card.cancel": "<PERSON><PERSON><PERSON><PERSON>", "monerium.setup-card.continue": "Gaan voort", "monerium.setup-card.create_account": "Skep rekening", "monerium.setup-card.login": "Meld aan by Gnosis Pay", "monerium.setup-card.subtitle": "Skep of meld aan by jou <PERSON> Pay-rekening om kitsbankdeposito's te aktiveer.", "monerium.setup-card.subtitle_personal_account": "<PERSON><PERSON> jou persoonlike rekening by <PERSON><PERSON> <PERSON> binne minute:", "monerium.setup-card.title": "Aktiveer bankdeposito's", "moneriumDepositSuccess.goToWallet": "Gaan na beursie", "moneriumDepositSuccess.title": "{symbol} ontvang", "moneriumInfo.fees": "<PERSON>y kry 0% fooie", "moneriumInfo.registration": "Monerium is gemagtig en gereguleer as 'n elektroniesegeld-instelling onder die Yslandse Wet op Elektroniese Geld No. 17/2013 <link><PERSON><PERSON></link>", "moneriumInfo.selfCustody": "Die digitale kontant wat jy ontvang is in jou privaat beursie en niemand anders het beheer oor jou bate nie.", "moneriumWithdrawRejected.supportText": "Ons kon nie jou oorbetaling voltooi nie. Probeer asseblief weer en as dit steeds nie werk nie, <link>kontak hulp.</link>", "moneriumWithdrawRejected.title": "Oorbetaling omgekeer", "moneriumWithdrawRejected.tryAgain": "<PERSON><PERSON><PERSON> weer", "moneriumWithdrawSuccess.supportText": "Dit kan tot 24 uur neem vir jou {br}ontvanger om die fondse te ontvang", "moneriumWithdrawSuccess.title": "<PERSON><PERSON><PERSON><PERSON>", "monerium_enable_banner.text": "Aktiveer bankoorbetalings nou", "monerium_error_address_re_link_required.title": "<PERSON><PERSON><PERSON> moet weer aan Monerium gekoppel word", "monerium_error_duplicate_order.title": "Du<PERSON><PERSON><PERSON><PERSON> bestelling", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "<PERSON><PERSON><PERSON><PERSON>", "mt-pelerin-fork.options.chf.short": "Oombliklik & Gratis met Mt Pelerin", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Oombliklik & Gratis met Monerium", "mt-pelerin-fork.title": "Wat wil jy deponeer?", "mtPelerinProviderInfo.fees": "<PERSON><PERSON>al 0% fooie", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd is geaffilieer met SO-FIT, 'n selfregulerende liggaam erken deur die Switserse Finansiële Owerheid (FINMA) onder die Wet op Teen-Geldwassery. <link><PERSON><PERSON></link>", "mtPelerinProviderInfo.selfCustody": "Die digitale kontant wat jy ontvang, is in jou privaat beursie en niemand anders sal beheer oor jou bates hê nie", "network-fee-widget.title": "Fooie", "network.edit.verifying_rpc": "Verifieer RPC", "network.editRpc.predefined_network_info.subtitle": "Zeal RPC's beskerm jou data, soos 'n VPN.{br}{br}Zeal se Standaard-RPC's is betrou<PERSON>ar.", "network.editRpc.predefined_network_info.title": "Zeal privaatheid-RPC", "network.filter.update_rpc_success": "RPC-nodus gestoor", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "Alle netwerke", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "Voeg netwerke by", "networks.filter.add_modal.chain_list.subtitle": "Voeg enige EVM-netwerke by", "networks.filter.add_modal.chain_list.title": "<PERSON><PERSON> na Chainlist", "networks.filter.add_modal.dapp_tip.subtitle": "Gebruik dApps om netwerke by te voeg.", "networks.filter.add_modal.dapp_tip.title": "Of voeg 'n netwerk vanaf enige dApp by", "networks.filter.add_networks.subtitle": "Alle EVM-netwerke ondersteun", "networks.filter.add_networks.title": "Voeg netwerke by", "networks.filter.add_test_networks.title": "<PERSON><PERSON><PERSON> <PERSON> by", "networks.filter.tab.netwokrs": "Netwerke", "networks.filter.testnets.title": "<PERSON><PERSON><PERSON><PERSON>", "nft.widget.emptystate": "<PERSON><PERSON> in beursie", "nft_collection.change_account_picture.subtitle": "Is jy seker jy wil jou profielfoto opdateer?", "nft_collection.change_account_picture.title": "Dateer profielfoto op na NFT", "nfts.allNfts.pricingPopup.description": "<PERSON><PERSON><PERSON> geb<PERSON> op laaste transaksie.", "nfts.allNfts.pricingPopup.title": "<PERSON><PERSON><PERSON>", "no-passkeys-found.modal.cta": "Maak toe", "no-passkeys-found.modal.subtitle": "Ons kan geen Zeal-passkeys op hierdie toestel vind nie. <PERSON><PERSON> seker jy is aangemeld by die wolkrekening waarmee jy jou Smart <PERSON>et geskep het.", "no-passkeys-found.modal.title": "Geen passkeys gevind", "notValidEmail.title": "Nie 'n geldige e-posadres nie", "notValidPhone.title": "Hierdie is nie 'n geldige foonnommer nie", "notification-settings.title": "Kennisgewinginstellings", "notification-settings.toggles.active-wallets": "Aktiewe beursies", "notification-settings.toggles.bank-transfers": "Bankoorbetalings", "notification-settings.toggles.card-payments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notification-settings.toggles.readonly-wallets": "<PERSON><PERSON>-<PERSON><PERSON> beursies", "ntft.groupHeader.text": "Versamelstukke", "on_ramp.crypto_completed": "Voltooi", "on_ramp.fiat_completed": "Voltooi", "onboarding-widget.subtitle.card_created_from_order.left": "Visa-kaart", "onboarding-widget.subtitle.card_created_from_order.right": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "onboarding-widget.subtitle.card_order_ready.left": "Fisiese Visa-kaart", "onboarding-widget.subtitle.default": "Bankoorplasings & Visa-kaart", "onboarding-widget.title.card-order-in-progress": "<PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON><PERSON>", "onboarding-widget.title.card_created_from_order": "<PERSON><PERSON> is gestuur", "onboarding-widget.title.kyc_approved": "Oorbetalings & kaart gereed", "onboarding-widget.title.kyc_failed": "Rekening nie moontlik nie", "onboarding-widget.title.kyc_not_started": "<PERSON><PERSON> v<PERSON>t met <PERSON>ste<PERSON>", "onboarding-widget.title.kyc_started_documents_requested": "Voltooi verifikasie", "onboarding-widget.title.kyc_started_resubmission_requested": "<PERSON><PERSON><PERSON> veri<PERSON><PERSON>ie weer", "onboarding-widget.title.kyc_started_verification_in_progress": "Verifieer tans identiteit", "onboarding.loginOrCreateAccount.amountOfAssets": "$10bn+ se bates", "onboarding.loginOrCreateAccount.cards.subtitle": "Slegs be<PERSON> in sekere streke. Deur voort te gaan, aanvaar jy ons <Terms>Bepalings</Terms> en <PrivacyPolicy>Privaatheidsbeleid</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "<PERSON><PERSON><PERSON><PERSON> met ho<PERSON>{br}opbrengste en geen fooie", "onboarding.loginOrCreateAccount.createAccount": "Skep rekening", "onboarding.loginOrCreateAccount.earn.subtitle": "Opbrengste wissel; kapit<PERSON> is in gevaar. Deur voort te gaan, aanvaar jy ons <Terms>Bepalings</Terms> en <PrivacyPolicy>Privaatheidsbeleid</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "Verdien {percent} per jaar{br}Vertrou deur {currencySymbol}5 miljard+", "onboarding.loginOrCreateAccount.earningPerYear": "Verdien {percent}{br}per jaar", "onboarding.loginOrCreateAccount.login": "Teken in", "onboarding.loginOrCreateAccount.trading.subtitle": "Kapitaal in gevaar. Deur voort te gaan, aanvaar jy ons <Terms>Bepalings</Terms> en <PrivacyPolicy>Privaatheidsbeleid</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Belê in alles,{br}BTC tot S&P", "onboarding.loginOrCreateAccount.trustedBy": "Digitale geldmarkte{br}Vertrou deur {assets}", "onboarding.wallet_stories.close": "Maak toe", "onboarding.wallet_stories.previous": "Vorige", "order-earn-deposit-bridge.deposit": "<PERSON><PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.into": "In", "otpIncorrectMessage": "Bevestigingskode is verkeerd", "passkey-creation-not-possible.modal.close": "Maak toe", "passkey-creation-not-possible.modal.subtitle": "Ons kon nie 'n passkey vir jou beursie skep nie. <PERSON>ak seker jou toestel ondersteun passkeys en probeer weer. <link>Kontak bystand</link> as die probleem voortduur.", "passkey-creation-not-possible.modal.title": "Kon nie passkey skep nie", "passkey-not-supported-in-mobile-browser.modal.cta": "Laai Zeal af", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Slim beursies word nie op mobiele blaaiers ondersteun nie.", "passkey-not-supported-in-mobile-browser.modal.title": "Laai die Zeal-toep af om voort te gaan", "passkey-recovery.recovering.deploy-signer.loading-text": "Verifieer passkey", "passkey-recovery.recovering.loading-text": "Her<PERSON><PERSON> beursie...", "passkey-recovery.recovering.signer-not-found.subtitle": "Ons kon nie jou passkey aan 'n aktiewe beursie koppel nie. As jy fondse in jou beursie het, kontak die Zeal-span vir hulp.", "passkey-recovery.recovering.signer-not-found.title": "<PERSON><PERSON> beursie gevind", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "<PERSON><PERSON><PERSON> '<PERSON> ander passkey", "passkey-recovery.select-passkey.banner.subtitle": "<PERSON>ak seker jy is by die korrekte rekening op jou toestel aangemeld. Passkeys is rekening-spesifiek.", "passkey-recovery.select-passkey.banner.title": "Sien jy nie jou beursie se passkey nie?", "passkey-recovery.select-passkey.continue": "<PERSON><PERSON> passkey", "passkey-recovery.select-passkey.subtitle": "Kies die passkey wat aan jou beursie gekoppel is om weer toegang te kry.", "passkey-recovery.select-passkey.title": "<PERSON><PERSON>", "passkey-story_1.subtitle": "Met 'n Smart Wallet kan jy netwerkfooie met die meeste tokens betaal, en hoef jy jou nie oor 'gas' te bekommer nie.", "passkey-story_1.title": "Vergeet 'gas' – betaal netwerkfooie met die meeste tokens", "passkey-story_2.subtitle": "Gebou op Safe se toonaangewende slimkontrakte wat meer as $100 miljard in meer as 20 miljoen beursies beveilig.", "passkey-story_2.title": "Beveilig deur <PERSON>", "passkey-story_3.subtitle": "Smart Wallets werk op groot Ethereum-versoenbare netwerke. Gaan die ondersteunde netwerke na voordat jy bates stuur.", "passkey-story_3.title": "Groot EVM-netwerke ondersteun", "password.add.header": "Skep wagwoord", "password.add.includeLowerAndUppercase": "Klein- en hoofletters", "password.add.includesNumberOrSpecialChar": "<PERSON>en nommer of simbool", "password.add.info.subtitle": "Jou wagwoord word nooit gedeel nie.", "password.add.info.t_and_c": "Deur voort te gaan, aanvaar jy ons <Terms>Bepalings</Terms> & <PrivacyPolicy>Privaatheidsbeleid</PrivacyPolicy>", "password.add.info.title": "<PERSON>u wagwoord bly op hierdie toestel", "password.add.inputPlaceholder": "Skep wagwoord", "password.add.shouldContainsMinCharsCheck": "10+ karakters", "password.add.subheader": "<PERSON>y sal jou wagwoord gebruik om Zeal te ontsluit", "password.add.success.title": "Wagwoord geskep 🔥", "password.confirm.header": "<PERSON><PERSON><PERSON><PERSON> wagwoord", "password.confirm.passwordDidNotMatch": "Wagwoorde moet ooreenstem", "password.confirm.subheader": "<PERSON><PERSON><PERSON> jou wagwoord nog een keer in", "password.create_pin.subtitle": "<PERSON><PERSON><PERSON> wagkode sluit die Zeal-toepassing", "password.create_pin.title": "<PERSON><PERSON><PERSON> jou wagkode", "password.enter_pin.title": "<PERSON><PERSON><PERSON> wag<PERSON> in", "password.incorrectPin": "Verkeerde wagkode", "password.pin_is_not_same": "Wagkode stem nie ooreen nie", "password.placeholder.enter": "<PERSON><PERSON><PERSON> wagwoord in", "password.placeholder.reenter": "<PERSON><PERSON>r wagwoord weer in", "password.re_enter_pin.subtitle": "Voer dieselfde wagkode weer in", "password.re_enter_pin.title": "Beves<PERSON>g wagkode", "pending-card-balance": "{amount} {timer}", "pending-send.deatils.pending": "<PERSON><PERSON><PERSON>", "pending-send.details.pending": "<PERSON><PERSON><PERSON>", "pending-send.details.processing": "Word verwerk", "permit-info.modal.description": "Permitte is vers<PERSON><PERSON> wat, indien onderteken, apps toelaat om jou tokens namens jou te skuif, byvoorbeeld om 'n ruiltransaksie te doen.{br}Permitte is so<PERSON><PERSON><PERSON> a<PERSON>, maar dit kos jou geen netwerkfooie om te onderteken nie.", "permit-info.modal.title": "Wat is Permit<PERSON>?", "permit.edit-expiration": "Wysig {currency} vervaltyd", "permit.edit-limit": "Wysig {currency} bestedingslimiet", "permit.edit-modal.expiresIn": "Verval oor ...", "permit.expiration-warning": "{currency} vervaltydwaarskuwing", "permit.expiration.info": "{currency} vervaltyd-inligting", "permit.expiration.never": "Nooit", "permit.spend-limit.info": "{currency} bestedingslimiet-inligting", "permit.spend-limit.warning": "{currency} bestedingslimiet-waarskuwing", "phoneNumber.title": "foonnommer", "physicalCardOrderFlow.cardOrdered": "<PERSON><PERSON> bestel", "physicalCardOrderFlow.city": "Stad", "physicalCardOrderFlow.orderCard": "<PERSON><PERSON> ka<PERSON>", "physicalCardOrderFlow.postcode": "Poskode", "physicalCardOrderFlow.shippingAddress.subtitle": "<PERSON><PERSON><PERSON><PERSON> jou kaart gestuur sal word", "physicalCardOrderFlow.shippingAddress.title": "Afleweringsadres", "physicalCardOrderFlow.street": "Straat", "placeholderDapps.1inch.description": "<PERSON><PERSON> om met die beste roetes", "placeholderDapps.aave.description": "Leen en leen tokens uit", "placeholderDapps.bungee.description": "Brug netwerke via die beste roetes", "placeholderDapps.compound.description": "Leen en leen tokens uit", "placeholderDapps.cowswap.description": "<PERSON><PERSON> teen die beste tariewe op Gnosis", "placeholderDapps.gnosis-pay.description": "<PERSON><PERSON><PERSON> jou <PERSON>-kaart", "placeholderDapps.jumper.description": "Brug netwerke via die beste roetes", "placeholderDapps.lido.description": "'Stake' ETH vir meer ETH", "placeholderDapps.monerium.description": "eMoney- en bankoorbetalings", "placeholderDapps.odos.description": "<PERSON><PERSON> om met die beste roetes", "placeholderDapps.stargate.description": "Brug of 'stake' vir <14% APY", "placeholderDapps.uniswap.description": "<PERSON><PERSON> van die gewildste omruilings", "pleaseAllowNotifications.cardPayments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pleaseAllowNotifications.customiseInSettings": "Pas aan in instellings", "pleaseAllowNotifications.enable": "Aktiveer", "pleaseAllowNotifications.forWalletActivity": "Vir beursie-aktiwiteit", "pleaseAllowNotifications.title": "<PERSON><PERSON> be<PERSON>ie-kennisgewings", "pleaseAllowNotifications.whenReceivingAssets": "Wan<PERSON> bates ontvang word", "portfolio.quick-actions.add_funds": "Voeg by", "portfolio.quick-actions.buy": "<PERSON><PERSON>", "portfolio.quick-actions.deposit": "<PERSON><PERSON><PERSON><PERSON>", "portfolio.quick-actions.send": "<PERSON><PERSON><PERSON>", "portfolio.view.lastRefreshed": "<PERSON><PERSON><PERSON><PERSON> {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Vul jou toetsnet-AVAX aan", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Gaan na Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "<PERSON><PERSON> jou toe<PERSON>net-BNB aan", "portfolio.view.topupTestNet.BscTestnet.secondary": "Gaan na Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "<PERSON><PERSON> jou <PERSON>-SepETH aan", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Gaan na Sepolia Faucet", "portfolio.view.topupTestNet.FantomTestnet.primary": "<PERSON><PERSON> jou toetsnet-FTM aan", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Gaan na Faucet", "privateKeyConfirmation.banner.subtitle": "Jou privaat sleutel gee toegang tot fondse. Net swendelaars vra.", "privateKeyConfirmation.banner.title": "Ek verstaan die risiko's", "privateKeyConfirmation.title": "MOET NOOIT jou Privaat Sleutel met en<PERSON><PERSON><PERSON> de<PERSON> nie", "rating-request.not-now": "Nie nou nie", "rating-request.title": "Sal jy Zeal aanbeveel?", "receive_funds.address-text": "<PERSON><PERSON><PERSON> is jou unieke beursie-adres. <PERSON><PERSON> kan dit veilig met ander deel.", "receive_funds.copy_address": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "receive_funds.network-warning.eoa.subtitle": "<link><PERSON><PERSON> standaard netwerklys</link>. <PERSON> wat op nie-EVM-netwerke gestuur word, sal verlore gaan.", "receive_funds.network-warning.eoa.title": "Alle Ethereum-gebaseerde netwerke word ondersteun", "receive_funds.network-warning.scw.subtitle": "<link>Sien ondersteunde netwerke</link>. <PERSON> wat op ander netwerke gestuur word, sal verlore gaan.", "receive_funds.network-warning.scw.title": "Belangrik: Gebruik slegs ondersteunde netwerke", "receive_funds.scan_qr_code": "Skandeer 'n QR-kode", "receiving.in.days": "Ontvang oor {days}d", "receiving.this.week": "Ontvang hierdie week", "receiving.today": "<PERSON><PERSON><PERSON><PERSON> van<PERSON>", "reference.error.maximum_number_of_characters_exceeded": "Te veel karakters", "referral-code.placeholder": "<PERSON><PERSON> uitnodigingskakel", "referral-code.subtitle": "<PERSON>lik weer op jou vriend se skakel, of plak die skakel hieronder. Ons wil seker maak jy kry jou belonings.", "referral-code.title": "Het 'n vriend vir jou {bReward} gestuur?", "rekyc.verification_deadline.subtitle": "Voltooi verifikasie binne {daysUntil} dae om jou kaart te bly geb<PERSON>.", "rekyc.verification_required.subtitle": "Voltooi verifikasie om jou kaart te bly geb<PERSON>ik.", "reminder.fund": "💸 Voeg fondse by — verdien dad<PERSON> 6%", "reminder.onboarding": "🏁 Voltooi opstelling — verdien 6%", "remove-owner.confirmation.subtitle": "Vir sekuriteit neem verstellings 3 minute om te verwerk. <PERSON><PERSON> kaart sal tydelik gevries wees en geen betalings sal moontlik wees nie.", "remove-owner.confirmation.title": "<PERSON><PERSON> kaart sal vir 3min gevries wees terwyl verstellings opdateer", "restore-smart-wallet.wallet-recovered": "<PERSON><PERSON><PERSON>", "rewardClaimCelebration.claimedTitle": "Belonings reeds geëis", "rewardClaimCelebration.subtitle": "Omdat jy vriende genooi het", "rewardClaimCelebration.title": "<PERSON><PERSON> het verdien", "rewards-warning.subtitle": "As jy hierdie rekening verwyder, sal toegang tot enige gekoppelde belonings onderbreek word. Jy kan die rekening enige tyd herstel om dit op te eis.", "rewards-warning.title": "<PERSON>y sal toegang tot jou belonings verloor", "rewards.copiedInviteLink": "Uitnodigingskakel gekopieer", "rewards.createAccount": "<PERSON><PERSON><PERSON>", "rewards.header.subtitle": "Ons sal {aR<PERSON><PERSON>} aan jou en {bReward} aan jou vriend stuur wanneer hulle {bSpendLimitReward} spandeer.", "rewards.header.title": "<PERSON><PERSON> {amountA}{br}Gee {amountB}", "rewards.sendInvite": "St<PERSON>ur uitnodiging", "rewards.sendInviteTip": "Ki<PERSON> 'n vriend en ons gee vir hulle {bAmount}", "route.fees": "Fooie {fees}", "routesNotFound.description": "Die omruilroete vir die {from}-{to} netwerkkombinasie is nie beskikbaar nie.", "routesNotFound.title": "<PERSON>n omruil<PERSON><PERSON> beski<PERSON> nie", "rpc.OrderBuySignMessage.subtitle": "Gebruik Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Gebruik Swaps.IO", "rpc.addCustomNetwork.addNetwork": "Voeg by", "rpc.addCustomNetwork.chainId": "Ketting-ID", "rpc.addCustomNetwork.nativeToken": "Inheemse token", "rpc.addCustomNetwork.networkName": "Netwerknaam", "rpc.addCustomNetwork.operationDescription": "Laat hierdie webwerf toe om 'n netwerk by jou beursie te voeg. Zeal kan nie die veiligheid van pasgemaakte netwerke nagaan nie, maak seker jy verstaan die risiko's.", "rpc.addCustomNetwork.rpcUrl": "RPC-URL", "rpc.addCustomNetwork.subtitle": "<PERSON><PERSON><PERSON><PERSON> {name}", "rpc.addCustomNetwork.title": "Voeg netwerk by", "rpc.send_token.network_not_supported.subtitle": "Ons werk daaraan om transaksies op hierdie netwerk moontlik te maak. Dankie vir jou geduld 🙏", "rpc.send_token.network_not_supported.title": "Netwerk kom binnekort", "rpc.send_token.send_or_receive.settings": "Instellings", "rpc.sign.accept": "<PERSON><PERSON><PERSON><PERSON>", "rpc.sign.cannot_parse_message.body": "Ons kon nie hierdie boodskap dekodeer nie. Aanvaar slegs hierdie versoek as jy hierdie app vertrou.{br}{br}Boodskappe kan gebruik word om jou by 'n app aan te meld, maar kan ook apps beheer oor jou tokens gee.", "rpc.sign.cannot_parse_message.header": "Gaan versigtig voort", "rpc.sign.import_private_key": "<PERSON><PERSON><PERSON> sleutels in", "rpc.sign.subtitle": "Vir {name}", "rpc.sign.title": "Onderteken", "safe-creation.success.title": "<PERSON><PERSON><PERSON> ges<PERSON>p", "safe-safety-checks-popup.title": "Transaksie-veiligheidskontroles", "safetyChecksPopup.title": "Werf-veiligheidskontroles", "scan_qr_code.description": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>ie-Q<PERSON> of koppel aan 'n toepassing", "scan_qr_code.show_qr_code": "Wys my QR-kode", "scan_qr_code.tryAgain": "<PERSON><PERSON><PERSON> weer", "scan_qr_code.unlockCamera": "Ontsluit kamera", "screen-lock-missing.modal.close": "Maak toe", "screen-lock-missing.modal.subtitle": "<PERSON>u <PERSON>tel benodig 'n skermslot om passkeys te gebruik. Stel asseblief 'n skermslot op en probeer weer.", "screen-lock-missing.modal.title": "Skermslot ontbreek", "seedConfirmation.banner.subtitle": "Enigeen met jou <PERSON><PERSON><PERSON><PERSON> Frase het toegang tot jou beursie en fondse. Net swendelaars vra daarvoor.", "seedConfirmation.title": "MOET NOOIT j<PERSON> met en<PERSON><PERSON><PERSON> de<PERSON> nie", "select-active-owner.subtitle": "Jy het verskeie beursies aan jou kaart gekoppel. Kies een om aan <PERSON> te koppel. Jy kan enige tyd wissel.", "select-active-owner.title": "<PERSON><PERSON> be<PERSON>ie", "select-card.title": "<PERSON><PERSON> ka<PERSON>", "select-crypto-currency-title": "Kies token", "select-token.title": "Kies 'token'", "selectEarnAccount.chf.description.steps": "· Onttrek fondse 24/7, geen vassluitperiodes nie {br}· Rente groei elke sekonde {br}· <PERSON><PERSON>si<PERSON>'s met ekstra beskerming in <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} per jaar in CHF", "selectEarnAccount.eur.description.steps": "· Onttrek fondse 24/7, geen <PERSON><PERSON> {br}· Rente loop elke sekonde op {br}· Oorbeskermde lenings met <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} per jaar in EUR", "selectEarnAccount.subtitle": "<PERSON>y kan enige tyd verander", "selectEarnAccount.title": "<PERSON><PERSON>", "selectEarnAccount.usd.description.steps": "· Onttrek fondse 24/7, geen toesluitings {br}· Rente loop elke sekonde op {br}· Oorbeskermde deposito's in <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} per jaar in USD", "selectEarnAccount.zero.description_general": "Hou digitale kontant sonder om rente te verdien", "selectEarnAccount.zero.title": "0% per jaar", "selectRechargeThreshold.button.enterAmount": "<PERSON><PERSON>r bedrag in", "selectRechargeThreshold.button.setTo": "Stel op {amount}", "selectRechargeThreshold.description.line1": "<PERSON><PERSON> jou kaart se saldo onder {amount} daal, her<PERSON><PERSON> dit outomaties terug na {amount} vanaf jou <PERSON>-rekening.", "selectRechargeThreshold.description.line2": "'n <PERSON><PERSON> teiken hou meer in jou Verdien-rekening (verdien 3%). <PERSON><PERSON> kan dit enige tyd verander.", "selectRechargeThreshold.title": "<PERSON><PERSON>", "select_currency_to_withdraw.select_token_to_withdraw": "Kies token om te onttrek", "send-card-token.form.send": "<PERSON><PERSON><PERSON>", "send-card-token.form.send-amount": "<PERSON><PERSON><PERSON><PERSON>", "send-card-token.form.title": "<PERSON><PERSON><PERSON> kont<PERSON> by <PERSON><PERSON>", "send-card-token.form.to-address": "<PERSON><PERSON>", "send-safe-transaction.network-fee-widget.error": "<PERSON><PERSON> ben<PERSON> {amount} of kies 'n ander token", "send-safe-transaction.network-fee-widget.no-fee": "<PERSON>n fooie", "send-safe-transaction.network-fee-widget.title": "Netwerkfooi", "send-safe-transaction.network_fee_widget.title": "Netwerkfooi", "send.banner.fees": "<PERSON><PERSON> {amount} meer {currency} om fooie te betaal", "send.banner.toAddressNotSupportedNetwork.subtitle": "Die ontvanger se beursie ondersteun nie {network} nie. Verander na 'n ondersteunde token.", "send.banner.toAddressNotSupportedNetwork.title": "Netwerk nie ondersteun vir ontvanger nie", "send.banner.walletNotSupportedNetwork.subtitle": "Slim beursies kan nie transaksies op {network} maak nie. Verander na 'n ondersteunde token.", "send.banner.walletNotSupportedNetwork.title": "Token-netwerk word nie ondersteun nie", "send.empty-portfolio.empty-state": "Ons het geen tokens gevind nie", "send.empty-portfolio.header": "Tokens", "send.titile": "<PERSON><PERSON><PERSON>", "sendLimit.success.subtitle": "<PERSON><PERSON> limiet is oor 3 min. opgedateer. Gebruik intussen jou huidige limiet.", "sendLimit.success.title": "<PERSON><PERSON><PERSON> verandering sal 3 minute neem", "send_crypto.form.disconnected.cta.addFunds": "<PERSON><PERSON><PERSON> fondse by", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Skakel oor na {network}", "send_crypto.form.disconnected.label": "Bedrag vir oorbetaling", "send_to.qr_code.description": "Skandeer 'n QR-kode om na 'n beursie te stuur", "send_to.qr_code.title": "Skandeer QR-kode", "send_to_card.header": "St<PERSON>ur na Kaart-adres", "send_to_card.select_sender.add_wallet": "<PERSON><PERSON><PERSON> be<PERSON>ie by", "send_to_card.select_sender.header": "Kies sender", "send_to_card.select_sender.search.default_placeholder": "<PERSON><PERSON> adres of ENS", "send_to_card.select_sender.show_card_address_button_description": "<PERSON><PERSON>", "send_token.form.select-address": "<PERSON><PERSON> adres", "send_token.form.send-amount": "<PERSON><PERSON><PERSON> bedrag", "send_token.form.title": "<PERSON><PERSON><PERSON>", "setLimit.amount.error.zero_amount": "<PERSON>y sal geen betalings kan maak nie", "setLimit.error.max_limit_reached": "Stel limiet op maks {amount}", "setLimit.error.same_as_current_limit": "Dieselfde limiet", "setLimit.placeholder": "Huidig: {amount}", "setLimit.submit": "Stel limiet", "setLimit.submit.error.amount_required": "<PERSON><PERSON>r bedrag in", "setLimit.subtitle": "Hierdie is die bedrag wat jy per dag met jou kaart kan spandeer.", "setLimit.title": "Stel daaglikse spandeerlimiet", "settings.accounts": "<PERSON><PERSON><PERSON>", "settings.accountsSeeAll": "<PERSON><PERSON> alles", "settings.addAccount": "<PERSON><PERSON><PERSON> be<PERSON>ie by", "settings.card": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.connections": "Toep-verbindings", "settings.currency": "Standaardgeldeenheid", "settings.default_currency_selector.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.discord": "Discord", "settings.experimentalMode": "Eksperimentele modus", "settings.experimentalMode.subtitle": "Toets nuwe funksies", "settings.language": "Taal", "settings.lockZeal": "Sluit Zeal", "settings.notifications": "Kennisgewings", "settings.open_expanded_view": "<PERSON>ak uitgebre<PERSON> aansig oop", "settings.privacyPolicy": "Privaatheidsbeleid", "settings.settings": "Instellings", "settings.termsOfUse": "Gebruiksvoorwaardes", "settings.twitter": "𝕏 / Twitter", "settings.version": "Weergawe {version} omgewing: {env}", "setup-card.confirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setup-card.confirmation.subtitle": "<PERSON>ak aanlyn betalings en voeg by jou {type} beursie vir kontaklose betalings.", "setup-card.getCard": "<PERSON><PERSON>", "setup-card.order.physicalCard": "<PERSON><PERSON><PERSON> ka<PERSON>", "setup-card.order.physicalCard.steps": "· 'n Fisiese VISA Gnosis Pay {br}· Neem tot 3 weke om aan jou te stuur {br}· Gebruik vir persoonlike betalings en by OTM'e. {br}· Voeg by Apple/Google-beursie (slegs vir ondersteunde lande", "setup-card.order.subtitle1": "J<PERSON> kan verskeie kaarte gelyktydig gebruik", "setup-card.order.title": "Watter tipe kaart?", "setup-card.order.virtualCard": "<PERSON>irt<PERSON><PERSON>", "setup-card.order.virtual_card.steps": "· Digitale VISA Gnosis Pay {br}· Gebruik onmiddellik vir aanlyn betalings {br}· Voeg by Apple/Google-beursie (slegs vir ondersteunde lande)", "setup-card.orderCard": "<PERSON><PERSON> ka<PERSON>", "setup-card.virtual-card": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setup.notifs.fakeAndroid.title": "Kennisgewings vir betalings en oorbetalings", "setup.notifs.fakeIos.subtitle": "Zeal kan jou wa<PERSON> as jy kontant ont<PERSON>g of met jou <PERSON>-kaart spandeer. <PERSON>y kan dit later verander.", "setup.notifs.fakeIos.title": "Kennisgewings vir betalings en oorbetalings", "sign.PermitAllowanceItem.spendLimit": "Bestedingslimiet", "sign.ledger.subtitle": "Ons het die transaksieversoek na jou hardewarebeursie gestuur. Gaan asseblief daar voort.", "sign.ledger.title": "<PERSON><PERSON> met hardew<PERSON><PERSON><PERSON><PERSON>", "sign.passkey.subtitle": "<PERSON><PERSON> b<PERSON><PERSON> behoort jou te vra om met die wagwoordsleutel van hierdie beursie te onderteken. Gaan asseblief daar voort.", "sign.passkey.title": "Onderteken met wagwoordsleutel", "signal_aborted_for_uknown_reason.title": "Netwerkversoek gekanselleer", "simulatedTransaction.BridgeTrx.info.title": "Brug", "simulatedTransaction.CardTopUp.info.title": "<PERSON><PERSON>g kontant by kaart", "simulatedTransaction.CardTopUpTrx.info.title": "<PERSON><PERSON>g kontant by kaart", "simulatedTransaction.NftCollectionApproval.approve": "<PERSON>ur NFT-versameling goed", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "Voeg by kaart", "simulatedTransaction.OrderEarnDepositBridge.title": "Deponeer in Earn", "simulatedTransaction.P2PTransaction.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.PermitSignMessage.title": "Permit", "simulatedTransaction.SingleNftApproval.approve": "Keur NFT goed", "simulatedTransaction.UnknownSignMessage.title": "Onderteken", "simulatedTransaction.Withdrawal.info.title": "Onttrekking", "simulatedTransaction.approval.title": "<PERSON><PERSON> goed", "simulatedTransaction.approve.info.title": "<PERSON><PERSON> goed", "simulatedTransaction.p2p.info.account": "<PERSON><PERSON>", "simulatedTransaction.p2p.info.unlabelledAccount": "Onbenoemde be<PERSON>ie", "simulatedTransaction.unknown.info.receive": "Ontvang", "simulatedTransaction.unknown.info.send": "<PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.using": "Gebruik {app}", "simulation.approval.modal.text": "<PERSON><PERSON> jy 'n goed<PERSON><PERSON> a<PERSON><PERSON><PERSON>, gee jy 'n spesifieke app/slim kontrak toestemming om jou tokens of NFT's in toekomstige transaksies te gebruik.", "simulation.approval.modal.title": "Wat is <PERSON><PERSON><PERSON><PERSON><PERSON>?", "simulation.approval.spend-limit.label": "Bestedingslimiet", "simulation.approve.footer.for": "Vir", "simulation.approve.unlimited": "Onbeperk", "simulationNotAvailable.title": "Onbekende Aksie", "smart-wallet-activation-view.on": "Op", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1<PERSON><PERSON><PERSON> kan toegang tot jou beursie blokkeer", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Gebruik Apple of Google om Zeal veilig op te stel", "smart-wallet.passkey-notice.title": "Vermy 1Password", "spend-limits.high.modal.text": "Stel 'n bestedingslimiet wat naby is aan die hoeveelheid tokens wat jy werklik met 'n app of slim kontrak sal gebruik. <PERSON><PERSON> limiete is riskant en kan dit makliker maak vir swendelaars om jou tokens te steel.", "spend-limits.high.modal.text_sign_message": "Die bestedingslimiet moet naby die bedrag tokens wees wat jy werklik met 'n app of slimkontrak sal gebruik. <PERSON><PERSON> limiete is riskant en maak dit makliker vir swendelaars om jou tokens te steel.", "spend-limits.high.modal.title": "Hoë bestedingslimiet", "spend-limits.modal.text": "'n Bestedingslimiet is hoeveel tokens 'n app namens jou kan gebruik. Jy kan hierdie limiet enige tyd verander of verwyder. Om veilig te bly, hou bestedingslimiete naby aan die hoeveelheid tokens wat jy werklik met 'n app sal gebruik.", "spend-limits.modal.title": "Wat is 'n bestedingslimiet?", "spent-limit-info.modal.description": "Bestedingslimiet is hoeveel tokens 'n app namens jou kan gebruik. Jy kan hierdie limiet enige tyd verander of verwyder. Vir veiligheid, hou bestedingslimiete naby aan die hoeveelheid tokens wat jy werklik met 'n app sal gebruik.", "spent-limit-info.modal.title": "Wat is 'n bestedingslimiet?", "sswaps-io.transfer-provider": "Oorbetalingsverskaffer", "storage.accountDetails.activateWallet": "Akt<PERSON><PERSON> beursie", "storage.accountDetails.changeWalletLabel": "<PERSON><PERSON> be<PERSON><PERSON><PERSON><PERSON>", "storage.accountDetails.deleteWallet": "Verwyder beursie", "storage.accountDetails.setup_recovery_kit": "Herstelstel", "storage.accountDetails.showPrivateKey": "Wys privaat sleutel", "storage.accountDetails.showWalletAddress": "<PERSON><PERSON> be<PERSON><PERSON>", "storage.accountDetails.smartBackup": "Rugsteun & Herstel", "storage.accountDetails.viewSsecretPhrase": "<PERSON>en geheime frase", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets?", "storage.manageAccounts.title": "Beursies", "submit-userop.progress.text": "Besig om te stuur", "submit.error.amount_high": "Bedrag te hoog", "submit.error.amount_hight": "Bedrag te hoog", "submit.error.amount_low": "Bedrag te laag", "submit.error.amount_required": "<PERSON><PERSON>r bedrag in", "submit.error.maximum_number_of_characters_exceeded": "Boodskap te lank", "submit.error.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "submit.error.recipient_required": "Ontvanger vereis", "submit.error.routes_not_found": "<PERSON><PERSON> roetes gekry nie", "submitSafeTransaction.monitor.title": "Transaksie-uitslag", "submitSafeTransaction.sign.title": "Transaksie-uitslag", "submitSafeTransaction.state.sending": "Stuur tans", "submitSafeTransaction.state.sign": "Besig om te skep", "submitSafeTransaction.submittingToRelayer.title": "Transaksie-uitslag", "submitTransaction.cancel": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.cancel.attemptingToStop": "Probeer tans stop", "submitTransaction.cancel.failedToStop": "Kon nie stop nie", "submitTransaction.cancel.stopped": "Gestop", "submitTransaction.cancel.title": "Transaksievoorskou", "submitTransaction.failed.banner.description": "Die netwerk het hierdie transaksie onverwags gekanselleer. <PERSON><PERSON><PERSON> weer of kontak ons.", "submitTransaction.failed.banner.title": "Transaksie het misluk", "submitTransaction.failed.execution_reverted.title": "Die toep het 'n fout gehad", "submitTransaction.failed.execution_reverted_without_message.title": "Die toep het 'n fout gehad", "submitTransaction.failed.out_of_gas.description": "Netwerk het transaksie gekanselleer omdat dit meer netwerkfooie as verwag gebruik het", "submitTransaction.failed.out_of_gas.title": "Netwerkfout", "submitTransaction.sign.title": "Transaksie-uitslag", "submitTransaction.speedUp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue": "By tou g<PERSON>eg", "submitTransaction.state.addedToQueue.short": "In tou", "submitTransaction.state.cancelled": "Gestop", "submitTransaction.state.complete": "{currencyCode} by <PERSON><PERSON>", "submitTransaction.state.complete.subtitle": "<PERSON><PERSON> j<PERSON>-portefeulje na", "submitTransaction.state.completed": "Voltooi", "submitTransaction.state.failed": "Misluk", "submitTransaction.state.includedInBlock": "Ingesluit in blok", "submitTransaction.state.includedInBlock.short": "In blok", "submitTransaction.state.replaced": "Vervang", "submitTransaction.state.sendingToNetwork": "St<PERSON>ur tans na netwerk", "submitTransaction.stop": "Stop", "submitTransaction.submit": "Dien in", "submitted-user-operation.state.bundled": "In tou", "submitted-user-operation.state.completed": "Voltooi", "submitted-user-operation.state.failed": "Misluk", "submitted-user-operation.state.pending": "<PERSON><PERSON><PERSON> aan", "submitted-user-operation.state.rejected": "Verwerp", "submittedTransaction.failed.title": "Transaksie het misluk", "success_splash.card_activated": "<PERSON><PERSON> geak<PERSON>er", "supportFork.give-feedback.title": "<PERSON>", "supportFork.itercom.description": "Zeal hanteer vrae oor deposito's, Earn, belonings of enigiets anders", "supportFork.itercom.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supportFork.title": "<PERSON><PERSON> hulp met", "supportFork.zendesk.subtitle": "Gnosis Pay hanteer vrae oor kaartbetalings, identiteitskontroles of terugbetalings", "supportFork.zendesk.title": "Kaartbetalings en identiteit", "supported-networks.ethereum.warning": "Hoë fooie", "supportedNetworks.networks": "Ondersteunde netwerke", "supportedNetworks.oneAddressForAllNetworks": "Een adres vir alle netwerke", "supportedNetworks.receiveAnyAssets": "<PERSON>en adres vir bates van alle netwerke.", "swap.form.error.no_routes_found": "<PERSON><PERSON> roetes gevind", "swap.form.error.not_enough_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON> saldo", "swaps-io-details.bank.serviceProvider": "Diensverskaffer", "swaps-io-details.details.processing": "Verwerk", "swaps-io-details.pending": "<PERSON><PERSON><PERSON>", "swaps-io-details.rate": "<PERSON><PERSON>", "swaps-io-details.serviceProvider": "Diensverskaffer", "swaps-io-details.transaction.from.processing": "<PERSON><PERSON><PERSON>", "swaps-io-details.transaction.networkFees": "Netwerkfooie", "swaps-io-details.transaction.state.completed-transaction": "Voltooide Transaksie", "swaps-io-details.transaction.state.started-transaction": "<PERSON><PERSON><PERSON>", "swaps-io-details.transaction.to.processing": "Voltooide Transaksie", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Deposito behoort binnekort te voltooi. Kinetex is steeds besig om jou transaksie te verwerk.", "swapsIO.monitoring.awaitingLiqSend.title": "Vertraag", "swapsIO.monitoring.awaitingRecive.title": "<PERSON><PERSON>i tans", "swapsIO.monitoring.awaitingSend.title": "In tou", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Tokens is na Kinetex gestuur, maar sal binnekort terugbesorg word. Kinetex kon nie die bestemmingstransaksie voltooi nie.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Besig om tokens terug te stuur", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Tokens is nie oorgedra nie weens 'n onbekende fout. Probeer asseblief weer.", "swapsIO.monitoring.cancelledNoSlash.title": "Tokens terugbesorg", "swapsIO.monitoring.cancelledSlashed.subtitle": "Tokens is terugbesorg. Kinetex kon nie die bestemmingstransaksie voltooi nie.", "swapsIO.monitoring.cancelledSlashed.title": "Tokens terugbesorg", "swapsIO.monitoring.completed.title": "Voltooi", "taker-metadata.earn": "Verdien in digitale USD met Sky", "taker-metadata.earn.aave": "Verdien in digitale EUR met Aave", "taker-metadata.earn.aave.cashout24": "<PERSON><PERSON><PERSON>, 24/7", "taker-metadata.earn.aave.trusted": "Vertrou met $27B, 2+ jaar", "taker-metadata.earn.aave.yield": "Opbrengs groei elke sekonde", "taker-metadata.earn.chf": "Verdien in digitale CHF", "taker-metadata.earn.chf.cashout24": "<PERSON><PERSON><PERSON>, 24/7", "taker-metadata.earn.chf.trusted": "Vertrou met Fr. 28M", "taker-metadata.earn.chf.yield": "Opbrengs groei elke sekonde", "taker-metadata.earn.usd.cashout24": "<PERSON><PERSON> onmiddellik uit, 24/7", "taker-metadata.earn.usd.trusted": "Vertrou met $10.7B, 5+ jaar", "taker-metadata.earn.usd.yield": "Opbrengs loop elke sekonde op", "test": "Deponeer", "to.titile": "Na", "token.groupHeader.cashback": "Terugbetaling", "token.groupHeader.title": "<PERSON>", "token.groupHeader.titleWithSum": "<PERSON> {sum}", "token.hidden_tokens.page.title": "Verborge tokens", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "Voeg token by", "token.widget.cashback_empty": "Nog geen transaksies", "token.widget.emptyState": "Geen tokens in beursie nie", "tokens.cash": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.approve.for": "Vir", "top-up-card-from-earn-view.approve.into": "Na", "top-up-card-from-earn-view.swap.from": "Vanaf", "top-up-card-from-earn-view.swap.to": "Na", "top-up-card-from-earn-view.withdraw.to": "Na", "top-up-card-from-earn.trx.title.approval": "<PERSON><PERSON> omru<PERSON> goed", "top-up-card-from-earn.trx.title.swap": "Voeg by kaart", "top-up-card-from-earn.trx.title.withdrawal": "<PERSON><PERSON><PERSON>", "topUpDapp.connectWallet": "<PERSON><PERSON>", "topup-fee-breakdown.bungee-fee": "Eksterne verskafferfooi", "topup-fee-breakdown.header": "Transaksiefooi", "topup-fee-breakdown.network-fee": "Netwerkfooi", "topup-fee-breakdown.total-fee": "Totale fooi", "topup.continue-in-wallet": "G<PERSON> voort in jou beursie", "topup.send.title": "<PERSON><PERSON><PERSON>", "topup.submit-transaction.close": "Maak toe", "topup.submit-transaction.sent-to-wallet": "Stuur {amount}", "topup.to": "<PERSON><PERSON>", "topup.transaction.complete.close": "Maak toe", "topup.transaction.complete.try-again": "<PERSON><PERSON><PERSON> weer", "transaction-request.nonce-too-low.modal.button-text": "Maak toe", "transaction-request.nonce-too-low.modal.text": "'n Transaksie met dieselfde reeksnommer (nonce) is reeds voltooi, so jy kan nie meer hierdie transaksie indien nie. <PERSON><PERSON> kan gebeur as jy transaksies kort na mekaar maak of as jy 'n transaksie probeer bespoedig of kanselleer wat reeds voltooi is.", "transaction-request.nonce-too-low.modal.title": "Transaksie met dieselfde nonce is reeds voltooi", "transaction-request.replaced.modal.button-text": "Maak toe", "transaction-request.replaced.modal.text": "Ons kan nie die status van hierdie transaksie naspoor nie. <PERSON><PERSON> is óf deur 'n ander transaksie vervang óf die RPC-node het probleme.", "transaction-request.replaced.modal.title": "Kon nie transaksie-status vind nie", "transaction.activity.details.modal.close": "Maak toe", "transaction.cancel_popup.cancel": "<PERSON>ee, wag", "transaction.cancel_popup.confirm": "Ja, stop", "transaction.cancel_popup.description": "Om te stop, moet jy 'n nuwe netwerkfooi betaal in plaas van die oorspronklike fooi van {oldFee}", "transaction.cancel_popup.description_without_original": "Om te stop, moet jy 'n nuwe netwerkfooi betaal", "transaction.cancel_popup.not_supported.subtitle": "Die stop van transaksies word nie ondersteun op {network}", "transaction.cancel_popup.not_supported.title": "Nie on<PERSON>un nie", "transaction.cancel_popup.stopping_fee": "Netwerk-stopfooi", "transaction.cancel_popup.title": "Stop transaksie?", "transaction.in-progress": "<PERSON><PERSON> die gang", "transaction.inProgress": "<PERSON><PERSON> die gang", "transaction.speed_up_popup.cancel": "<PERSON>ee, wag", "transaction.speed_up_popup.confirm": "<PERSON><PERSON>, spoed op", "transaction.speed_up_popup.description": "Om te bespoedig, moet jy 'n nuwe netwerkfooi betaal in plaas van die oorspronklike fooi van {amount}", "transaction.speed_up_popup.description_without_original": "Om te bespoedig, moet jy 'n nuwe netwerkfooi betaal", "transaction.speed_up_popup.seed_up_fee_title": "Netwerk bespoedigingsfooi", "transaction.speed_up_popup.title": "<PERSON><PERSON><PERSON> bespoedig?", "transaction.speedup_popup.not_supported.subtitle": "Transaksie-bespoediging word nie ondersteun op {network}", "transaction.speedup_popup.not_supported.title": "Nie on<PERSON>un nie", "transaction.subTitle.failed": "Misluk", "transactionDetails.cashback.not-qualified": "Nie gekwalif<PERSON>er nie", "transactionDetails.cashback.paid": "{amount} betaal", "transactionDetails.cashback.pending": "{amount} hangende", "transactionDetails.cashback.title": "Terugbetalingskatting", "transactionDetails.cashback.unknown": "Onbekend", "transactionDetails.cashback_estimate": "Geskatte terugbetaling", "transactionDetails.category": "<PERSON><PERSON><PERSON>", "transactionDetails.exchangeRate": "Wisselkoers", "transactionDetails.location": "Ligging", "transactionDetails.payment-approved": "<PERSON><PERSON> goedgekeur", "transactionDetails.payment-declined": "<PERSON>ling afgekeur", "transactionDetails.payment-reversed": "<PERSON><PERSON> omgekeer", "transactionDetails.recharge.amountSentFromEarn.title": "<PERSON><PERSON> vanaf <PERSON>n gestuur", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "<PERSON> kaart a<PERSON>", "transactionDetails.recharge.rate.title": "<PERSON><PERSON>", "transactionDetails.recharge.transactionId.title": "Transaksie-ID", "transactionDetails.refund": "Terugbetaling", "transactionDetails.reversal": "<PERSON><PERSON><PERSON><PERSON>", "transactionDetails.transactionCurrency": "Transaksiegeldeenheid", "transactionDetails.transactionId": "Transaksie-ID", "transactionDetails.type": "Transaksie", "transactionRequestWidget.approve.subtitle": "Vir {target}", "transactionRequestWidget.p2p.subtitle": "Aan {target}", "transactionRequestWidget.unknown.subtitle": "Gebruik {target}", "transactionSafetyChecksPopup.title": "Transaksie-veiligheidstoetse", "transactions.main.activity.title": "Aktiwiteit", "transactions.page.hiddenActivity.title": "Versteekte aktiwiteit", "transactions.page.title": "Aktiwiteit", "transactions.viewTRXHistory.emptyState": "Nog geen transaksies nie", "transactions.viewTRXHistory.errorMessage": "Ons kon nie jou transaksiegeskiedenis laai nie", "transactions.viewTRXHistory.hidden.emptyState": "<PERSON><PERSON> verst<PERSON> transaksies", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Aktiwiteit nie ondersteun vir toetsnette nie", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Aktiwiteit nie ondersteun vir toetsnette nie{br}<link>Gaan na blokverkenner</link>", "transfer_provider": "Oorbetalingsverskaffer", "transfer_setup_with_different_wallet.subtitle": "Bankoorbetalings is met 'n ander beursie opgestel. Jy kan net een beursie aan oorbetalings koppel.", "transfer_setup_with_different_wallet.swtich_and_continue": "<PERSON>isse<PERSON> en gaan voort", "transfer_setup_with_different_wallet.title": "<PERSON>isse<PERSON> beursie", "tx-sent-to-wallet.button": "Maak toe", "tx-sent-to-wallet.subtitle": "<PERSON><PERSON> voort in {wallet}", "unblockProviderInfo.fees": "<PERSON>y kry die laagste moontlike fooie: 0% tot $5k per maand en 0.2% daarbo.", "unblockProviderInfo.registration": "Unblock is geregistreer & gemagtig deur FNTT om VASP-ruil- en bewaringsdienste te verskaf, en is 'n geregistreerde MSB-verskaffer by US Fincen. <link><PERSON><PERSON> meer</link>", "unblockProviderInfo.selfCustody": "Die digitale kontant wat jy ontvang, bewaar jy self en niemand anders het beheer oor jou bate nie.", "unblock_invalid_faster_payment_configuration.subtitle": "Die bankrekening wat jy verskaf het, ondersteun nie Europese SEPA-oorbetalings of UK Faster Payments nie. Verskaf asseblief 'n ander rekening", "unblock_invalid_faster_payment_configuration.title": "'n <PERSON><PERSON> rekening word vereis", "unknownTransaction.primaryText": "Kaarttransaksie", "unsupportedCountry.subtitle": "Bankoorbetalings is nog nie in jou land beskikbaar nie.", "unsupportedCountry.title": "<PERSON><PERSON> in {country}", "update-app-popup.subtitle": "Die nuutste opdatering is vol regstellings, kenmerke en meer towerkrag. Dateer op na die nuutste weergawe en neem jou <PERSON> na die volgende vlak.", "update-app-popup.title": "<PERSON><PERSON>-weergawe op", "update-app-popup.update-now": "Dateer nou op", "user_associated_with_other_merchant.subtitle": "Hierdie beursie kan nie vir bankoorbetalings gebruik word nie. Gebruik asseblief 'n ander beursie of rapporteer op ons Discord vir ondersteuning en opdaterings.", "user_associated_with_other_merchant.title": "Be<PERSON>ie kan nie gebruik word nie", "user_associated_with_other_merchant.try_with_another_wallet": "<PERSON><PERSON><PERSON> ander be<PERSON>ie", "user_email_already_exists.subtitle": "Jy het reeds bankoorbetalings met 'n ander beursie opgestel. <PERSON><PERSON><PERSON> asseblief weer met die beursie wat jy voorheen gebruik het.", "user_email_already_exists.title": "Oorbetalings met 'n ander beursie opgestel", "user_email_already_exists.try_with_another_wallet": "<PERSON><PERSON><PERSON> ander be<PERSON>ie", "validation.invalid.iban": "Ongeldige IBAN", "validation.required": "<PERSON><PERSON><PERSON>", "validation.required.first_name": "Voornaam vereis", "validation.required.iban": "IBAN vereis", "validation.required.last_name": "<PERSON>", "verify-passkey.cta": "Verifieer wagwoordsleutel", "verify-passkey.subtitle": "Verifieer dat jou wagwoordsleutel geskep en behoorlik beveilig is.", "verify-passkey.title": "Verifieer wagwoordsleutel", "view-cashback.cashback-next-cycle": "Terugbetalingskoers oor {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "Deponeer om terugbetaling te kry", "view-cashback.pending": "{money} <PERSON><PERSON><PERSON>", "view-cashback.pending-rewards.not_paid": "Ontvang oor {days}d", "view-cashback.pending-rewards.paid": "Hierdie week ontvang", "view-cashback.received-rewards": "Ontvangde belonings", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Onbevestigde betalings", "view-cashback.upcoming": "Opkomende {money}", "virtual-card-order.configure-safe.loading-text": "<PERSON><PERSON>p tans kaart", "virtual-card-order.create-order.loading-text": "Akt<PERSON><PERSON> tans kaart", "virtual-card-order.create-order.success-text": "<PERSON><PERSON> geak<PERSON>er", "virtualCard.activateCard": "<PERSON><PERSON><PERSON><PERSON>", "walletDeleteConfirm.main_action": "Verwyder", "walletDeleteConfirm.subtitle": "<PERSON>y sal weer moet invoer om die portefeulje te sien of transaksies te doen", "walletDeleteConfirm.title": "Verwyder beursie?", "walletSetting.header": "<PERSON><PERSON><PERSON>-instellings", "wallet_connect.connect.cancel": "<PERSON><PERSON><PERSON><PERSON>", "wallet_connect.connect.connect_button": "<PERSON><PERSON>", "wallet_connect.connect.title": "<PERSON><PERSON>", "wallet_connect.connected.title": "Gekoppel", "wallet_connect_add_chain_missing.title": "Netwerk nie ondersteun nie", "wallet_connect_proposal_expired.title": "<PERSON><PERSON><PERSON> het verval", "withdraw": "Onttrek", "withdraw.confirmation.close": "<PERSON><PERSON><PERSON><PERSON>", "withdraw.confirmation.continue": "Bevestig", "withdrawal_request.completed": "Voltooi", "withdrawal_request.pending": "<PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.cta.primary.connecting": "<PERSON><PERSON> tans...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "<PERSON><PERSON>", "zeal-dapp.connect-wallet.cta.secondary": "<PERSON><PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.title": "<PERSON><PERSON> beursie om voort te gaan", "zealSmartWalletInfo.gas": "Betaal netwerkfooie met baie tokens; gebruik gewilde ERC20-tokens op ondersteunde kettings om netwerkfooie te betaal, nie net inheemse tokens nie", "zealSmartWalletInfo.recover": "<PERSON><PERSON> nie; <PERSON><PERSON><PERSON> met biometries<PERSON> wag<PERSON><PERSON><PERSON><PERSON><PERSON> van j<PERSON> wag<PERSON>bestuurder, iCloud of Google-rekening.", "zealSmartWalletInfo.selfCustodial": "Ten volle privaat beursie; Wagwoordsleutel-handtekeninge word op die ketting bekragtig om sentrale afhanklikhede te verminder.", "zealSmartWalletInfo.title": "Oor Zeal Smart Wallets", "zeal_a_rewards_already_claimed_error.title": "Beloning reeds geëis", "zwidget.minimizedDisconnected.label": "<PERSON><PERSON> on<PERSON>"}