{"Account.ListItem.details.label": "Détails", "AddFromAddress.success": "Portefeuille enregistré", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{Aucun portefeuille} one{{count} portefeuille} other{{count} portefeuilles}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Phrase secrète {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Crée de nouveaux portefeuilles depuis une de tes phrases secrètes.", "AddFromExistingSecretPhrase.SelectPhrase.title": "Choisir une phrase secrète", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "Ta phrase secrète peut sauvegarder plusieurs portefeuilles. <PERSON><PERSON> ceux que tu veux utiliser.", "AddFromExistingSecretPhrase.WalletSelection.title": "Ajout rapide d'un portefeuille", "AddFromExistingSecretPhrase.success": "Portefeuilles ajoutés à Zeal", "AddFromHardwareWallet.subtitle": "Sélectionne ton portefeuille matériel pour te connecter à Zeal", "AddFromHardwareWallet.title": "Portefeuille matériel", "AddFromNewSecretPhrase.WalletSelection.subtitle": "Sélectionne les portefeuilles que tu veux importer", "AddFromNewSecretPhrase.WalletSelection.title": "Importer des portefeuilles", "AddFromNewSecretPhrase.accounts": "Portefeuilles", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "Une phrase secrète est comme un porte-clés pour des millions de portefeuilles, chacun avec sa propre clé privée.{br}{br}Tu peux importer autant de portefeuilles que tu le souhaites maintenant ou en ajouter plus tard.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Portefeuilles à phrase secrète", "AddFromNewSecretPhrase.subtitle": "Saisis ta phrase secrète avec des espaces entre les mots", "AddFromNewSecretPhrase.success_secret_phrase_added": "Phrase secrète a<PERSON> 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Portefeuilles ajoutés à Zeal", "AddFromNewSecretPhrase.wallets": "Portefeuilles", "AddFromPrivateKey.subtitle": "Saisis ta clé privée", "AddFromPrivateKey.success": "Clé privée ajoutée 🎉", "AddFromPrivateKey.title": "Restaurer un portefeuille", "AddFromPrivateKey.typeOrPaste": "<PERSON><PERSON> ou colle ici", "AddFromSecretPhrase.importWallets": "{count,plural,=0{<PERSON>cune sélection} one{Importer le portefeuille} other{Importer les {count}}}", "AddFromTrezor.AccountSelection.title": "Importer des portefeuilles Trezor", "AddFromTrezor.hwWalletTip.subtitle": "Un portefeuille matériel contient des millions de portefeuilles avec des adresses différentes. Tu peux en importer autant que tu veux maintenant ou en ajouter plus tard.", "AddFromTrezor.hwWalletTip.title": "Importation depuis des portefeuilles matériels", "AddFromTrezor.importAccounts": "{count,plural,=0{Aucun compte sélectionné} one{Importer le compte} other{Importer {count} comptes}}", "AddFromTrezor.success": "Portefeuilles ajoutés à Zeal", "ApprovalSpenderTypeCheck.failed.subtitle": "Arnaque probable : un contrat est attendu", "ApprovalSpenderTypeCheck.failed.title": "Bénéficiaire : portefeuille, non un contrat", "ApprovalSpenderTypeCheck.passed.subtitle": "L'approbation vise en général un contrat", "ApprovalSpenderTypeCheck.passed.title": "Le bénéficiaire est un contrat intelligent", "BestReturns.subtitle": "Ce fournisseur de swap vous donnera le meilleur rendement, tous frais inclus.", "BestReturnsPopup.title": "Meilleurs rendements", "BlacklistCheck.Failed.subtitle": "Signalements malveillants par <source></source>", "BlacklistCheck.Failed.title": "Ce site est sur liste noire", "BlacklistCheck.Passed.subtitle": "Aucun signalement malveillant par <source></source>", "BlacklistCheck.Passed.title": "Ce site n'est pas sur liste noire", "BlacklistCheck.failed.statusButton.label": "Ce site a été signalé", "BridgeRoute.slippage": "Glissement {slippage}", "BridgeRoute.title": "Fournisseur de passerelle", "CheckConfirmation.InProgress": "En cours...", "CheckConfirmation.success.splash": "<PERSON><PERSON><PERSON><PERSON>", "ChooseImportOrCreateSecretPhrase.subtitle": "Importe une phrase secrète ou crées-en une nouvelle", "ChooseImportOrCreateSecretPhrase.title": "Ajouter une phrase secrète", "ConfirmTransaction.Simuation.Skeleton.title": "Vérifications de sécurité en cours…", "ConnectionSafetyCheckResult.passed": "Vérification de sécurité validée", "ContactGnosisPaysupport": "Support Gnosis Pay", "CopyKeyButton.copied": "<PERSON><PERSON><PERSON>", "CopyKeyButton.copyYourKey": "Copier ta clé", "CopyKeyButton.copyYourPhrase": "Copier ta phrase", "DAppVerificationCheck.Failed.subtitle": "Le site n'est pas listé sur <source></source>", "DAppVerificationCheck.Failed.title": "Site non trouvé dans les registres d'apps", "DAppVerificationCheck.Passed.subtitle": "Le site est listé sur <source></source>", "DAppVerificationCheck.Passed.title": "Le site figure dans les registres d'apps", "DAppVerificationCheck.failed.statusButton.label": "Site non trouvé dans les registres d'apps", "ERC20.tokens.emptyState": "<PERSON><PERSON>n jeton trouvé", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Frais de priorité", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Der<PERSON>s {period} : entre {from} et {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Frais de base : {baseFee} • Marge de sécurité : {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Récupération des frais de base impossible", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Nonce supérieur au suivant. Transaction bloquée.", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "Le nonce ne peut être inférieur au nonce actuel", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "Impossible de calculer les frais de priorité", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Récupération des frais max impossible", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "Frais max", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Peut être bloquée si les frais sont bas", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "Frais de base max", "EditFeeModal.Custom.gasLimit.title": "Limite de gaz {gasLimit}", "EditFeeModal.Custom.title": "Paramètres avancés", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Bloquée jusqu'à baisse des frais de base", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Frais bas. Risque de blocage.", "EditFeeModal.EditGasLimit.estimatedGas": "Gaz est. : {estimated} • Marge de sécurité : {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "<PERSON>ite trop basse, la transaction échouera", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "<PERSON><PERSON> basse, la transaction peut échouer", "EditFeeModal.EditGasLimit.subtitle": "Définis la quantité maximale de gaz que cette transaction peut utiliser. Elle échouera si tu fixes une limite trop basse", "EditFeeModal.EditGasLimit.title": "Modifier la limite de gaz", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Inférieur à la limite de gaz minimale : {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "<PERSON>ce trop élevé, transaction sera bloquée", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "Nonce inférieur à l'actuel impossible", "EditFeeModal.EditNonce.subtitle": "Ta transaction sera bloquée si tu n'utilises pas le prochain nonce", "EditFeeModal.EditNonce.title": "Modifier le nonce", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "Il faut {amount} pour envoyer", "EditFeeModal.Header.Time.unknown": "Temps inconnu", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Max {fee}", "EditFeeModal.Header.fee.unknown": "<PERSON><PERSON> incon<PERSON>", "EditFeeModal.Header.subsequent_failed": "Les estimations peuvent être obsolètes, la dernière actualisation a échoué", "EditFeeModal.Layout.Header.ariaLabel": "Frais actuels", "EditFeeModal.MaxFee.subtitle": "Les frais max sont le montant le plus élevé que tu paieras pour une transaction, mais tu paieras généralement les frais prévus. Cette marge supplémentaire aide ta transaction à passer, même si le réseau ralentit ou devient plus cher.", "EditFeeModal.MaxFee.title": "Frais réseau max", "EditFeeModal.SelectPreset.Time.unknown": "Temps inconnu", "EditFeeModal.SelectPreset.ariaLabel": "Sélectionner le préréglage des frais", "EditFeeModal.SelectPreset.fast": "Rapide", "EditFeeModal.SelectPreset.normal": "Normal", "EditFeeModal.SelectPreset.slow": "<PERSON><PERSON>", "EditFeeModal.ariaLabel": "Modifier les frais réseau", "FailedSimulation.Confirmation.Item.subtitle": "Nous avons rencontré une erreur interne", "FailedSimulation.Confirmation.Item.title": "Impossible de simuler la transaction", "FailedSimulation.Confirmation.subtitle": "Es-tu sûr de vouloir continuer ?", "FailedSimulation.Confirmation.title": "Tu signes à l'aveugle", "FailedSimulation.Title": "Erreur de simulation", "FailedSimulation.footer.subtitle": "Nous avons rencontré une erreur interne", "FailedSimulation.footer.title": "Impossible de simuler la transaction", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Il te faut {amount} pour soumettre la transaction", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Le traitement pourrait être long", "FeeForecastWidget.networkFee": "<PERSON><PERSON>", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Impossible de calculer les frais réseau", "FeeForecastWidget.subsequentFailed.message": "Estimations anciennes : mise à jour échouée", "FeeForecastWidget.unknownDuration": "Inconnu", "FeeForecastWidget.unknownFee": "Inconnu", "GasCurrencySelector.balance": "Solde : {balance}", "GasCurrencySelector.networkFee": "<PERSON><PERSON> r<PERSON>", "GasCurrencySelector.payNetworkFeesUsing": "Payer les frais de réseau avec", "GasCurrencySelector.removeDefaultGasToken.description": "Payer les frais depuis le solde le plus élevé", "GasCurrencySelector.removeDefaultGasToken.title": "Gestion automatique des frais", "GasCurrencySelector.save": "Enregistrer", "GoogleDriveBackup.BeforeYouBegin.first_point": "Si j'oublie mon mot de passe Zeal, je perdrai mes actifs à jamais", "GoogleDriveBackup.BeforeYouBegin.second_point": "Si je perds l'accès à mon Google Drive ou modifie mon fichier de récupération, je perdrai mes actifs à jamais", "GoogleDriveBackup.BeforeYouBegin.subtitle": "Veuillez comprendre et accepter le point suivant sur la conservation privée :", "GoogleDriveBackup.BeforeYouBegin.third_point": "Zeal ne peut pas m'aider à récupérer mon mot de passe Zeal ou mon accès à Google Drive", "GoogleDriveBackup.BeforeYouBegin.title": "Avant de commencer", "GoogleDriveBackup.loader.subtitle": "Approuve la demande sur Google Drive pour y charger ton fichier de récupération", "GoogleDriveBackup.loader.title": "En attente d'approbation...", "GoogleDriveBackup.success": "Sauvegarde réussie 🎉", "MonitorOffRamp.overServiceTime": "La plupart des virements sont effectués en {estimated_time}, mais ils peuvent parfois prendre plus de temps en raison de vérifications supplémentaires. C'est normal et tes fonds sont en sécurité pendant ces vérifications.{br}{br}Si la transaction n'est pas finalisée sous {support_soft_deadline}, merci de {contact_support}", "MonitorOnRamp.contactSupport": "<PERSON>er le support", "MonitorOnRamp.from": "De", "MonitorOnRamp.fundsReceived": "<PERSON><PERSON><PERSON> reçus", "MonitorOnRamp.overServiceTime": "La plupart des virements sont effectués en {estimated_time}, mais peuvent parfois prendre plus de temps en raison de vérifications supplémentaires. C'est normal et tes fonds sont en sécurité pendant ces vérifications.{br}{br}Si la transaction n'est pas terminée d'ici {support_soft_deadline}, merci de {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Envoi vers ton portefeuille", "MonitorOnRamp.to": "À", "MonitorOnRamp.waitingForTransfer": "En attente de ton virement", "NftCollectionCheck.failed.subtitle": "La collection n'est pas vérifiée sur <source></source>", "NftCollectionCheck.failed.title": "Collection non vérifiée", "NftCollectionCheck.passed.subtitle": "La collection est vérifiée sur <source></source>", "NftCollectionCheck.passed.title": "Collection vérifiée", "NftCollectionInfo.entireCollection": "Collection complète", "NoSigningKeyStore.createAccount": "<PERSON><PERSON><PERSON> un compte", "NonceRangeError.biggerThanCurrent.message": "La transaction sera bloquée", "NonceRangeError.lessThanCurrent.message": "La transaction échouera", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "Diminue le nonce pour éviter le blocage.", "NonceRangeErrorPopup.biggerThanCurrent.title": "La transaction sera bloquée", "P2pReceiverTypeCheck.failed.subtitle": "Envoies-tu à la bonne adresse ?", "P2pReceiverTypeCheck.failed.title": "Destinataire : contrat, et non portefeuille", "P2pReceiverTypeCheck.passed.subtitle": "En général, tu envoies à un portefeuille", "P2pReceiverTypeCheck.passed.title": "Le destinataire est un portefeuille", "PasswordCheck.title": "<PERSON><PERSON> le mot de passe", "PasswordChecker.subtitle": "Saisis ton mot de passe pour vérifier que c'est bien toi.", "PermitExpirationCheck.failed.subtitle": "Limite la durée au strict nécessaire", "PermitExpirationCheck.failed.title": "Durée d'expiration longue", "PermitExpirationCheck.passed.subtitle": "Durée d'usage de tes jetons par l'app", "PermitExpirationCheck.passed.title": "Durée d'expiration raisonnable", "PrivateKeyValidationError.moreThanMaximumWords": "Max. {count} mots", "PrivateKeyValidationError.notValidPrivateKey": "Clé privée non valide", "PrivateKeyValidationError.secretPhraseIsInvalid": "Phrase secrète non valide", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Mot n°{index} mal orthographié ou invalide", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} mot} other{{count} mots}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Les phrases secrètes et les clés privées sont chiffrées et ne quittent jamais cet appareil", "SecretPhraseReveal.header": "Note ta phrase secrète", "SecretPhraseReveal.hint": "Ne partage ta phrase avec personne. Garde-la en sécurité et hors ligne.", "SecretPhraseReveal.skip.subtitle": "Tu peux le faire plus tard, mais si tu perds cet appareil avant de noter ta phrase, tu perdras tous les actifs de ce portefeuille.", "SecretPhraseReveal.skip.takeTheRisk": "Passer outre", "SecretPhraseReveal.skip.title": "Ne pas noter la phrase ?", "SecretPhraseReveal.skip.writeDown": "Noter", "SecretPhraseReveal.skipForNow": "Plus tard", "SecretPhraseReveal.subheader": "Note-la bien et conserve-la hors ligne. Nous te demanderons ensuite de la vérifier.", "SecretPhraseReveal.verify": "Vérifier", "SelectCurrency.tokens": "Jet<PERSON>", "SelectCurrency.tokens.emptyState": "<PERSON><PERSON>n jeton trouvé", "SelectRoute.slippage": "Slippage {slippage}", "SelectRoutes.emptyState": "Aucun itinéraire trouvé pour ce swap", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Connecter un portefeuille", "SendERC20.labelAddress.inputPlaceholder": "Étiquette du portefeuille", "SendERC20.labelAddress.subtitle": "Donne un nom à ce portefeuille pour le retrouver plus tard.", "SendERC20.labelAddress.title": "Nommer ce portefeuille", "SendERC20.send_to": "Envoyer à", "SendERC20.tokens": "Jet<PERSON>", "SendOrReceive.bankTransfer.primaryText": "Virement bancaire", "SendOrReceive.bankTransfer.shortText": "Dépôt et retrait gratuits et instantanés", "SendOrReceive.bridge.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.bridge.shortText": "Transférer des jetons entre réseaux", "SendOrReceive.receive.primaryText": "Recevoir", "SendOrReceive.receive.shortText": "Recevoir des jetons ou des objets de collection", "SendOrReceive.send.primaryText": "Envoyer", "SendOrReceive.send.shortText": "Envoyer des jetons à n'importe quelle adresse", "SendOrReceive.swap.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.swap.shortText": "Échanger des jetons", "SendSafeTransaction.Confirm.loading": "Vérifications de sécurité en cours…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Chiffrer un fichier de récupération avec un mot de passe", "SetupRecoveryKit.google.subtitle": "Synchronisé {date}", "SetupRecoveryKit.google.title": "Sauvegarde Google Drive", "SetupRecoveryKit.subtitle": "Il te faut au moins une méthode pour restaurer ton compte si tu changes d'appareil ou désinstalles Zeal.", "SetupRecoveryKit.title": "Configurer le kit de récupération", "SetupRecoveryKit.writeDown.subtitle": "Noter la phrase secrète", "SetupRecoveryKit.writeDown.title": "<PERSON><PERSON><PERSON><PERSON>", "Sign.CheckSafeDeployment.activate": "Activer", "Sign.CheckSafeDeployment.subtitle": "Avant de pouvoir te connecter à une app ou signer un message hors chaîne, tu dois activer ton appareil sur ce réseau. Ceci se produit après l'installation ou la récupération d'un Smart Wallet.", "Sign.CheckSafeDeployment.title": "Activer l'appareil sur ce réseau", "Sign.Simuation.Skeleton.title": "Vérifications de sécurité en cours…", "SignMessageSafetyCheckResult.passed": "Vérifications de sécurité validées", "SignMessageSafetyChecksPopup.title.permits": "Vérifications de sécurité du permis", "SimulationFailedConfirmation.subtitle": "Nous avons simulé cette transaction et trouvé un problème qui la ferait échouer. Tu peux la soumettre, mais elle échouera probablement et tu pourrais perdre les frais réseau.", "SimulationFailedConfirmation.title": "La transaction risque d'échouer", "SimulationNotSupported.Title": "Simulation non{br}disponible sur{br}{network}", "SimulationNotSupported.footer.subtitle": "Tu peux quand même soumettre cette transaction", "SimulationNotSupported.footer.title": "Simulation non prise en charge", "SlippagePopup.custom": "<PERSON><PERSON><PERSON><PERSON>", "SlippagePopup.presetsHeader": "Slippage du swap", "SlippagePopup.title": "Paramètres de slippage", "SmartContractBlacklistCheck.failed.subtitle": "Signalements malveillants par <source></source>", "SmartContractBlacklistCheck.failed.title": "Contrat sur liste noire", "SmartContractBlacklistCheck.passed.subtitle": "Aucun signalement malveillant par <source></source>", "SmartContractBlacklistCheck.passed.title": "Contrat non listé sur liste noire", "SuspiciousCharactersCheck.Failed.subtitle": "C'est une tactique de phishing courante", "SuspiciousCharactersCheck.Failed.title": "Nous cherchons les schémas de phishing courants", "SuspiciousCharactersCheck.Passed.subtitle": "Nous vérifions les tentatives de phishing", "SuspiciousCharactersCheck.Passed.title": "L'adresse n'a pas de caractères inhabituels", "SuspiciousCharactersCheck.failed.statusButton.label": "Caractères inhabituels dans l'adresse ", "TokenVerificationCheck.failed.subtitle": "Jeton non listé sur <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} n'est pas vérifié par CoinGecko", "TokenVerificationCheck.passed.subtitle": "Jeton listé sur <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} est vérifié par CoinGecko", "TopupDapp.MonitorTransaction.success.splash": "<PERSON><PERSON><PERSON><PERSON>", "TransactionSafetyCheckResult.passed": "Vérifications de sécurité validées", "TransactionSimulationCheck.failed.subtitle": "Erreur : {errorMessage}", "TransactionSimulationCheck.failed.title": "La transaction va probablement échouer", "TransactionSimulationCheck.passed.subtitle": "Simulation effectuée avec <source></source>", "TransactionSimulationCheck.passed.title": "L'aperçu de la transaction a réussi", "TrezorError.trezor_action_cancelled.action": "<PERSON><PERSON><PERSON>", "TrezorError.trezor_action_cancelled.subtitle": "Tu as rejeté la transaction sur ton portefeuille matériel", "TrezorError.trezor_device_used_elsewhere.action": "Synchroniser le Trezor", "TrezorError.trezor_device_used_elsewhere.subtitle": "Assure-toi de fermer toutes les autres sessions ouvertes et réessaie de synchroniser ton Trezor", "TrezorError.trezor_method_cancelled.action": "Synchroniser le Trezor", "TrezorError.trezor_method_cancelled.subtitle": "Assure-toi d'autoriser Trezor à exporter les portefeuilles vers Zeal", "TrezorError.trezor_permissions_not_granted.action": "Synchroniser le Trezor", "TrezorError.trezor_permissions_not_granted.subtitle": "<PERSON>ne à Zeal les autorisations pour voir tous les portefeuilles", "TrezorError.trezor_pin_cancelled.action": "Synchroniser le Trezor", "TrezorError.trezor_pin_cancelled.subtitle": "Session annulée sur l'appareil", "TrezorError.trezor_popup_closed.action": "Synchroniser le Trezor", "TrezorError.trezor_popup_closed.subtitle": "La boîte de dialogue Trezor s'est fermée de manière inattendue", "TrxLikelyToFail.lessThanEstimatedGas.message": "La transaction échouera", "TrxLikelyToFail.lessThanMinimumGas.message": "La transaction échouera", "TrxLikelyToFail.lessThanSuggestedGas.message": "Échec probable", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "La limite de gaz de la transaction est trop basse. Augmente-la jusqu'à la limite suggérée pour éviter un échec.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "La transaction risque d'échouer", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "La limite de gaz est inférieure au gaz estimé. Augmente-la jusqu'à la limite suggérée.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "La transaction échouera", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "Les frais de base max sont inférieurs aux frais de base actuels. Augmente-les pour éviter que la transaction ne soit bloquée.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "La transaction sera bloquée", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "Les frais max de la transaction sont trop bas. Augmente-les pour éviter que la transaction ne soit bloquée.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "La transaction sera bloquée", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Les frais de priorité sont inférieurs à la recommandation. Augmente-les pour accélérer la transaction.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "La transaction pourrait être longue à finaliser", "UnsupportedMobileNetworkLayout.gotIt": "<PERSON><PERSON><PERSON>", "UnsupportedMobileNetworkLayout.subtitle": "Tu ne peux pas encore effectuer de transactions ni signer de messages sur le réseau avec l'ID {networkHexId} avec la version mobile de Zeal.{br}{br}Passe à l'extension de navigateur pour effectuer des transactions sur ce réseau. Nous travaillons activement pour ajouter le support de ce réseau 🚀", "UnsupportedMobileNetworkLayout.title": "Réseau non pris en charge par la version mobile de Zeal", "UnsupportedSafeNetworkLayout.subtitle": "Tu ne peux pas effectuer de transactions ni signer de messages sur {network} avec un Smart Wallet Zeal.{br}{br}Passe à un réseau pris en charge ou utilise un portefeuille Legacy.", "UnsupportedSafeNetworkLayoutk.title": "Réseau non pris en charge pour le Smart Wallet", "UserConfirmationPopup.goBack": "Annuler", "UserConfirmationPopup.submit": "<PERSON><PERSON><PERSON>", "ViewPrivateKey.header": "Clé privée", "ViewPrivateKey.hint": "Ne partage ta clé privée avec personne. Garde-la en sécurité et hors ligne.", "ViewPrivateKey.subheader.mobile": "Touche pour afficher ta clé privée", "ViewPrivateKey.subheader.web": "Survole pour afficher ta clé privée", "ViewPrivateKey.unblur.mobile": "Touche pour afficher", "ViewPrivateKey.unblur.web": "Survole pour afficher", "ViewSecretPhrase.PasswordChecker.subtitle": "Saisis ton mot de passe pour chiffrer le fichier de récupération. Tu devras t'en souvenir.", "ViewSecretPhrase.done": "<PERSON><PERSON><PERSON><PERSON>", "ViewSecretPhrase.header": "Phrase secrète", "ViewSecretPhrase.hint": "Ne partage ta phrase avec personne. Garde-la en sécurité et hors ligne.", "ViewSecretPhrase.subheader.mobile": "Touche pour afficher ta phrase secrète", "ViewSecretPhrase.subheader.web": "Survole pour afficher ta phrase secrète", "ViewSecretPhrase.unblur.mobile": "Touche pour afficher", "ViewSecretPhrase.unblur.web": "Survole pour afficher", "account-details.monerium": "Les virements sont effectués via Monerium, un IME agréé et réglementé. <link>En savoir plus</link>", "account-details.unblock": "Les virements sont effectués via Unblock, un service d'échange et de conservation agréé et enregistré. <link>En savoir plus</link>", "account-selector.empty-state": "Aucun portefeuille trouvé", "account-top-up.select-currency.title": "Jet<PERSON>", "account.accounts_not_found": "Nous n'avons trouvé aucun portefeuille", "account.accounts_not_found_search_valid_address": "Ce portefeuille n'est pas dans ta liste", "account.add.create_new_secret_phrase": "<PERSON><PERSON>er une phrase secrète", "account.add.create_new_secret_phrase.subtext": "Une nouvelle phrase secrète de 12 mots", "account.add.fromRecoveryKit.fileNotFound": "Fichier introuvable", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "<PERSON><PERSON><PERSON><PERSON>", "account.add.fromRecoveryKit.fileNotFound.explanation": "Vérifie que tu es connecté au bon compte contenant un dossier Zeal Backup", "account.add.fromRecoveryKit.fileNotValid": "Fichier de récupération non valide", "account.add.fromRecoveryKit.fileNotValid.explanation": "Nous avons vérifié ton fichier : ce n'est pas le bon type ou il a été modifié.", "account.add.import_secret_phrase": "Importer une phrase secrète", "account.add.import_secret_phrase.subtext": "<PERSON><PERSON><PERSON>, Metamask ou autre", "account.add.select_type.add_hardware_wallet": "Portefeuille matériel", "account.add.select_type.existing_smart_wallet": "Smart Wallet existant", "account.add.select_type.private_key": "Clé privée", "account.add.select_type.seed_phrase": "Phrase de récupération", "account.add.select_type.title": "Importer un portefeuille", "account.add.select_type.zeal_recovery_file": "Fichier de récupération Zeal", "account.add.success.title": "Nouveau portefeuille créé 🎉", "account.addLabel.header": "Nomme ton portefeuille", "account.addLabel.labelError.labelAlreadyExist": "Ce nom existe déjà. Essaie un autre nom", "account.addLabel.labelError.maxStringLengthExceeded": "Nombre maximal de caractères atteint", "account.add_active_wallet.primary_text": "Ajouter un portefeuille", "account.add_active_wallet.short_text": "<PERSON><PERSON><PERSON>, connecter ou importer un portefeuille", "account.add_from_ledger.success": "Portefeuilles ajoutés à Zeal", "account.add_tracked_wallet.primary_text": "Ajouter un portefeuille en lecture seule", "account.add_tracked_wallet.short_text": "Voir le portefeuille et l'activité", "account.button.unlabelled-wallet": "Portefeuille sans nom", "account.create_wallet": "Créer un portefeuille", "account.label.edit.title": "Modifier le nom du portefeuille", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON> {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Fichier de récupération non valide", "account.recoveryKit.selectBackupFile.subtitle": "Sélectionne le fichier de récupération que tu veux restaurer", "account.recoveryKit.selectBackupFile.title": "Fichier de récupération", "account.recoveryKit.success.recoveryFileFound": "Fichier de récupération trouvé 🎉", "account.select_type_of_account.create_eoa.short": "Portefeuille classique pour experts", "account.select_type_of_account.create_eoa.title": "Créer un portefeuille à phrase de récupération", "account.select_type_of_account.create_safe_wallet.title": "<PERSON><PERSON><PERSON> un Smart Wallet", "account.select_type_of_account.existing_smart_wallet": "Smart Wallet existant", "account.select_type_of_account.hardware_wallet": "Portefeuille matériel", "account.select_type_of_account.header": "Ajouter un portefeuille", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Clé privée / Phrase de récupération", "account.select_type_of_account.read_only_wallet": "Portefeuille en lecture seule", "account.select_type_of_account.read_only_wallet.short": "Aperçu de n'importe quel portefeuille", "account.topup.title": "Ajouter des fonds à Zeal", "account.view.error.refreshAssets": "Actualiser", "account.widget.refresh": "Actualiser", "account.widget.settings": "Paramètres", "accounts.view.copied-text": "Co<PERSON>é {formattedAddress}", "accounts.view.copiedAddress": "Co<PERSON>é {formattedAddress}", "action.accept": "Accepter", "action.accpet": "Accepter", "action.allow": "Autoriser", "action.back": "Retour", "action.cancel": "Annuler", "action.card-activation.title": "Activer la carte", "action.claim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.close": "<PERSON><PERSON><PERSON>", "action.complete-steps": "<PERSON><PERSON><PERSON>", "action.confirm": "Confirmer", "action.continue": "<PERSON><PERSON><PERSON>", "action.copy-address-understand": "OK - <PERSON><PERSON><PERSON> l'adresse", "action.deposit": "<PERSON><PERSON><PERSON>r", "action.done": "<PERSON><PERSON><PERSON><PERSON>", "action.dontAllow": "Ne pas autoriser", "action.edit": "modifier", "action.email-required": "Saisir l'e-mail", "action.enterPhoneNumber": "Saisir le n° de téléphone", "action.expand": "Développer", "action.fix": "<PERSON><PERSON><PERSON>", "action.getStarted": "Commencer", "action.got_it": "<PERSON><PERSON><PERSON>", "action.hide": "Masquer", "action.import": "Importer", "action.import-keys": "Importer les clés", "action.importKeys": "Importer les clés", "action.minimize": "<PERSON><PERSON><PERSON><PERSON>", "action.next": "Suivant", "action.ok": "OK", "action.reduceAmount": "Réduire au max", "action.refreshWebsite": "Actualiser le site", "action.remove": "<PERSON><PERSON><PERSON><PERSON>", "action.remove-account": "Supprimer le compte", "action.requestCode": "Demander le code", "action.resend_code": "Renvoyer le code", "action.resend_code_with_time": "<PERSON><PERSON><PERSON> le code {time}", "action.retry": "<PERSON><PERSON><PERSON><PERSON>", "action.reveal": "<PERSON><PERSON><PERSON><PERSON>", "action.save": "Enregistrer", "action.save_changes": "Enregistrer", "action.search": "<PERSON><PERSON><PERSON>", "action.seeAll": "Tout voir", "action.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.send": "Envoyer", "action.skip": "Passer", "action.submit": "Envoyer", "action.understood": "J'ai compris", "action.update": "Valider", "action.update-gnosis-pay-owner.complete": "<PERSON><PERSON><PERSON>", "action.zeroAmount": "<PERSON><PERSON> un montant", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Objets de collection", "action_bar_title.tokens": "Jet<PERSON>", "action_bar_title.transaction_request": "Demande de transaction", "activate-monerium.loading": "Configuration de ton compte personnel", "activate-monerium.success.title": "Monerium activé", "activate-physical-card-widget.subtitle": "La livraison peut prendre 3 semaines", "activate-physical-card-widget.title": "<PERSON>r la carte physique", "activate-smart-wallet.title": "<PERSON><PERSON> le portefeuille", "active_and_tracked_wallets.title": "Zeal couvre tous tes frais sur {network}, te permettant d'effectuer des transactions gratuitement.", "activity.approval-amount.revoked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.approval-amount.unlimited": "Illimitée", "activity.approval.approved_for": "Approuvé pour", "activity.approval.approved_for_with_target": "Approuvé {approvedTo}", "activity.approval.revoked_for": "Révoqué pour", "activity.bank.serviceProvider": "Fournisseur de services", "activity.bridge.serviceProvider": "Fournisseur de services", "activity.cashback.period": "Période de cashback", "activity.filter.card": "<PERSON><PERSON>", "activity.rate": "<PERSON><PERSON>", "activity.receive.receivedFrom": "Re<PERSON><PERSON> de", "activity.send.sendTo": "<PERSON><PERSON><PERSON>", "activity.smartContract.unknown": "Contrat inconnu", "activity.smartContract.usingContract": "Utilisation du contrat", "activity.subtitle.pending_timer": "{timerString} En attente", "activity.title.arbitrary_smart_contract_interaction": "{function} sur {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Interaction avec un contrat inconnu", "activity.title.bridge.from": "<PERSON><PERSON><PERSON> depuis {token}", "activity.title.bridge.to": "<PERSON><PERSON><PERSON> vers {token}", "activity.title.buy": "<PERSON><PERSON><PERSON> de {asset}", "activity.title.card_owners_updated": "Titulaires de la carte mis à jour", "activity.title.card_spend_limit_updated": "Plafond de la carte défini", "activity.title.cashback_deposit": "Dépôt sur le Cashback", "activity.title.cashback_reward": "Cashback re<PERSON><PERSON>", "activity.title.cashback_withdraw": "Retrait du Cashback", "activity.title.claimed_reward": "Récompen<PERSON> ré<PERSON><PERSON>", "activity.title.deployed_smart_wallet_gnosis": "<PERSON><PERSON><PERSON>", "activity.title.deposit_from_bank": "Dépôt depuis la banque", "activity.title.deposit_into_card": "Dépôt sur la carte", "activity.title.deposit_into_earn": "<PERSON><PERSON><PERSON><PERSON><PERSON> sur {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} sur {smartContract}", "activity.title.from": "De {sender}", "activity.title.pendidng_areward_claim": "Récupération de la récompense", "activity.title.pendidng_breward_claim": "Récupération de la récompense", "activity.title.recharge_disabledh": "Recharge de la carte désactivée", "activity.title.recharge_set": "Objectif de recharge défini", "activity.title.recovered_smart_wallet_gnosis": "Installation d'un nouvel appareil", "activity.title.send_pending": "À {receiver}", "activity.title.send_to_bank": "Vers la banque", "activity.title.swap": "<PERSON><PERSON><PERSON> de {token}", "activity.title.to": "À {receiver}", "activity.title.withdraw_from_card": "Retrait de la carte", "activity.title.withdraw_from_earn": "Retrait de {earn}", "activity.transaction.networkFees": "<PERSON><PERSON> r<PERSON>", "activity.transaction.state": "Transaction terminée", "activity.transaction.state.completed": "Transaction terminée", "activity.transaction.state.failed": "Transaction échouée", "add-account.section.import.header": "Importer", "add-another-card-owner": "Ajouter un autre titulaire", "add-another-card-owner.Recommended.footnote": "<PERSON><PERSON><PERSON> comme cotitulaire de ta carte", "add-another-card-owner.Recommended.primaryText": "Ajouter Zeal à Gnosis Pay", "add-another-card-owner.recommended": "Recommandé", "add-owner.confirmation.subtitle": "<PERSON>r s<PERSON><PERSON><PERSON><PERSON>, la mise à jour des paramètres prend 3 minutes. Durant ce dé<PERSON>, ta carte sera temporairement bloquée et aucun paiement ne sera possible.", "add-owner.confirmation.title": "Ta carte sera bloquée 3 min pendant la mise à jour", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Portefeuille déjà utilisé, ajout impossible", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "<PERSON>r <PERSON><PERSON><PERSON>", "add.account.backup.decrypt.success": "Portefeuille restauré", "add.account.backup.password.passwordIncorrectMessage": "Le mot de passe est incorrect", "add.account.backup.password.subtitle": "Saisis le mot de passe que tu as utilisé pour chiffrer ton fichier de récupération", "add.account.backup.password.title": "<PERSON><PERSON> le mot de passe", "add.account.google.login.subtitle": "Approuve la demande sur Google Drive pour synchroniser ton fichier de récupération", "add.account.google.login.title": "En attente d'approbation...", "add.readonly.already_added": "Portefeuille d<PERSON><PERSON><PERSON> a<PERSON>", "add.readonly.continue": "<PERSON><PERSON><PERSON>", "add.readonly.empty": "<PERSON><PERSON> une adresse ou un ENS", "addBankRecipient.title": "Ajouter un bénéficiaire", "add_funds.deposit_from_bank_account": "Dépôt depuis un compte bancaire", "add_funds.from_another_wallet": "Depuis un autre portefeuille", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Connecter à la dApp de recharge", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Connecte un portefeuille à la dApp de recharge Zeal et envoie rapidement des fonds sur ton portefeuille", "add_funds.from_crypto_wallet.header": "Depuis un autre portefeuille", "add_funds.from_crypto_wallet.header.show_wallet_address": "Afficher l'adresse de ton portefeuille", "add_funds.from_exchange.header": "Envoyer depuis un exchange", "add_funds.from_exchange.header.copy_wallet_address": "Copier ton adresse Zeal", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance, etc.", "add_funds.from_exchange.header.open_exchange": "<PERSON><PERSON><PERSON><PERSON>r l'app ou le site de l'exchange", "add_funds.from_exchange.header.selected_token": "Envoyer {token} sur Zeal", "add_funds.from_exchange.header.selected_token.subtitle": "Sur {network}", "add_funds.from_exchange.header.send_selected_token": "Envoyer un jeton compatible", "add_funds.from_exchange.header.send_selected_token.subtitle": "Sélectionner jeton et réseau compatibles", "add_funds.import_wallet": "Importer un portefeuille crypto existant", "add_funds.title": "Alimenter ton compte", "add_funds.transfer_from_exchange": "Virement depuis un exchange", "address.add.header": "Vois ton portefeuille dans Zeal{br}en mode lecture seule", "address.add.subheader": "Saisis ton adresse ou ton ENS pour voir tes actifs sur tous les réseaux EVM en un seul endroit. Tu pourras créer ou importer d'autres portefeuilles plus tard.", "address_book.change_account.bank_transfers.header": "Bénéficiaires bancaires", "address_book.change_account.bank_transfers.primary": "Bénéficiaire bancaire", "address_book.change_account.cta": "Su<PERSON><PERSON> le portefeuille", "address_book.change_account.search_placeholder": "A<PERSON>ter ou chercher une adresse", "address_book.change_account.tracked_header": "Portefeuilles en lecture seule", "address_book.change_account.wallets_header": "Portefeuilles actifs", "app-association-check-failed.modal.cta": "<PERSON><PERSON><PERSON><PERSON>", "app-association-check-failed.modal.subtitle": "Veuillez réessayer. Des problèmes de connectivité ralentissent la récupération de tes Passkeys. Si le problème persiste, redémarre <PERSON> et essaie à nouveau.", "app-association-check-failed.modal.subtitle.creation": "Veuillez réessayer. Des problèmes de connectivité ralentissent la création du Passkey. Si le problème persiste, redé<PERSON><PERSON> et essaie à nouveau.", "app-association-check-failed.modal.title.creation": "Ton appareil n'a pas pu créer de <PERSON>", "app-association-check-failed.modal.title.signing": "Ton appareil n'a pas pu charger les Passkeys", "app.app_protocol_group.borrowed_tokens": "Jetons empruntés", "app.app_protocol_group.claimable_amount": "<PERSON><PERSON> r<PERSON>", "app.app_protocol_group.health_rate": "Indice de sant<PERSON>", "app.app_protocol_group.lending": "<PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.locked_tokens": "Jetons verrouillés", "app.app_protocol_group.nfts": "Objets de collection", "app.app_protocol_group.reward_tokens": "Jetons de récompense", "app.app_protocol_group.supplied_tokens": "<PERSON><PERSON> fournis", "app.app_protocol_group.tokens": "<PERSON><PERSON>", "app.app_protocol_group.vesting_token": "Jeton en acquisition", "app.appsGroupHeader.discoverMore": "En découvrir plus", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Recher<PERSON> ou saisir une URL", "app.error-banner.cory": "<PERSON><PERSON><PERSON> les données d'erreur", "app.error-banner.retry": "<PERSON><PERSON><PERSON><PERSON>", "app.list_item.rewards": "Récompenses {value}", "app.position_details.health_rate.description": "La santé est calculée en divisant le montant de ton prêt par la valeur de ta garantie.", "app.position_details.health_rate.title": "Qu'est-ce que le taux de santé ?", "approval.edit-limit.label": "Modifier le plafond de dépense", "approval.permit_info": "Informations sur le permis", "approval.spend-limit.edit-modal.cancel": "Annuler", "approval.spend-limit.edit-modal.limit-label": "Plafond de <PERSON>", "approval.spend-limit.edit-modal.max-limit-error": "Attention, plafond élevé", "approval.spend-limit.edit-modal.revert": "Annuler les modifications", "approval.spend-limit.edit-modal.set-to-unlimited": "Définir sur Illimité", "approval.spend-limit.edit-modal.submit": "Enregistrer", "approval.spend-limit.edit-modal.title": "Modifier les autorisations", "approval.spend_limit_info": "Qu'est-ce qu'un plafond de dépense ?", "approval.what_are_approvals": "Que sont les approbations ?", "apps_list.page.emptyState": "Aucune application active", "backpace.removeLastDigit": "<PERSON><PERSON><PERSON><PERSON> le dernier chiffre", "backup-banner.backup_now": "<PERSON><PERSON><PERSON><PERSON>", "backup-banner.risk_losing_funds": "Sauvegarde maintenant pour ne pas perdre tes fonds", "backup-banner.title": "Portefeuille non sauvegardé", "backupRecoverySmartWallet.noExportPrivateKeys": "Sauvegarde auto : ton Smart Wallet est enregistré comme passkey. Pas de phrase secrète ni de clé privée.", "backupRecoverySmartWallet.safeContracts": "Sécurité multi-clés : les portefeuilles Zeal utilisent les contrats Safe, plusieurs appareils peuvent donc approuver une transaction. Aucun point de défaillance unique.", "backupRecoverySmartWallet.security": "Multi-appareil : tu peux utiliser ton portefeuille sur plusieurs appareils avec le passkey. Chaque appareil a sa propre clé privée.", "backupRecoverySmartWallet.showLocalPrivateKey": "Mode expert : tu peux exporter la clé privée de cet appareil, l'utiliser dans un autre portefeuille et te connecter sur <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Afficher la clé privée</Key>", "backupRecoverySmartWallet.storingKeys": "Synchro cloud : le passkey est stocké en sécurité dans iCloud, Google Password Manager ou ton gestionnaire de mots de passe.", "backupRecoverySmartWallet.title": "Sauvegarde et récupération du Smart Wallet", "balance-change.card.titile": "<PERSON><PERSON>", "balanceChange.pending": "En attente", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Sé<PERSON><PERSON>ner le prestataire", "bank-transfer.change-deposit-receiver.subtitle": "Ce portefeuille recevra tous les dépôts bancaires", "bank-transfer.change-deposit-receiver.title": "Définir le portefeuille de réception", "bank-transfer.change-owner.subtitle": "Ce portefeuille sert à te connecter et à récupérer ton compte de virement", "bank-transfer.change-owner.title": "Définir le propriétaire du compte", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Tout dépôt bancaire envoyé à Zeal sera reçu par ce portefeuille.", "bank-transfer.configrm-change-deposit-receiver.title": "Changer le portefeuille de réception", "bank-transfer.configrm-change-owner.subtitle": "Ce portefeuille sert à te connecter et à récupérer ton compte de virement bancaire.", "bank-transfer.configrm-change-owner.title": "Changer le propriétaire du compte", "bank-transfer.deposit.widget.status.complete": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.deposit.widget.status.funds_received": "<PERSON><PERSON><PERSON> reçus", "bank-transfer.deposit.widget.status.sending_to_wallet": "Envoi au portefeuille", "bank-transfer.deposit.widget.status.transfer-on-hold": "Virement en attente", "bank-transfer.deposit.widget.status.transfer-received": "Envoi au portefeuille", "bank-transfer.deposit.widget.subtitle": "{from} en {to}", "bank-transfer.deposit.widget.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfer.intro.bulletlist.point_1": "Configuration avec Unblock", "bank-transfer.intro.bulletlist.point_2": "Virements entre EUR/GBP et plus de 10 jetons", "bank-transfer.intro.bulletlist.point_3": "0 % de frais jusqu'à 5k $/mois, puis 0,2 %", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Envoi vers la banque", "bank-transfer.withdrawal.widget.status.in-progress": "Virement en cours", "bank-transfer.withdrawal.widget.status.on-hold": "Virement en attente", "bank-transfer.withdrawal.widget.status.success": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.withdrawal.widget.subtitle": "{from} vers {to}", "bank-transfer.withdrawal.widget.title": "Retrait", "bank-transfers.bank-account-actions.remove-this-account": "Supprimer ce compte", "bank-transfers.bank-account-actions.switch-to-this-account": "Utiliser ce compte", "bank-transfers.deposit.fees-for-less-than-5k": "Frais pour 5k $ ou moins", "bank-transfers.deposit.fees-for-more-than-5k": "Frais pour plus de 5k $", "bank-transfers.set-receiving-bank.title": "Définir la banque de réception", "bank-transfers.settings.account_owner": "Titulaire du compte", "bank-transfers.settings.receiver_of_bank_deposits": "Bénéficiaire des dépôts bancaires", "bank-transfers.settings.receiver_of_withdrawals": "Bénéficiaire des retraits", "bank-transfers.settings.registered_email": "E-mail enregistré", "bank-transfers.settings.title": "Paramètres des virements bancaires", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} Co<PERSON>e", "bank-transfers.setup.bank-account": "Compte bancaire", "bankTransfer.withdraw.max_loading": "Max : {amount}", "bank_details_do_not_match.got_it": "<PERSON><PERSON><PERSON>", "bank_details_do_not_match.subtitle": "Le code de tri et le numéro de compte ne correspondent pas. Vérifie les informations et réessaie.", "bank_details_do_not_match.title": "Coordonnées bancaires incorrectes", "bank_tranfsers.select_country_of_residence.country_not_supported": "<PERSON><PERSON><PERSON><PERSON>, les virements bancaires ne sont pas pris en charge en {country} pour l'instant", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Ouvre ton application bancaire", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Envoie {fiatCurrencyCode} sur ton compte", "bank_transfer.deposit.header": "{fullName}&nbsp;: coordonnées du compte personnel", "bank_transfer.kyc_status_widget.subtitle": "Virements bancaires", "bank_transfer.kyc_status_widget.title": "Vérification d'identité", "bank_transfer.personal_details.date_of_birth": "Date de naissance", "bank_transfer.personal_details.date_of_birth.invalid_format": "Date invalide", "bank_transfer.personal_details.date_of_birth.too_young": "Tu dois avoir au moins 18 ans", "bank_transfer.personal_details.first_name": "Prénom", "bank_transfer.personal_details.last_name": "Nom de famille", "bank_transfer.personal_details.title": "Tes informations", "bank_transfer.reference.label": "Réfé<PERSON>ce (facultatif)", "bank_transfer.reference_message": "Envoy<PERSON> de<PERSON>", "bank_transfer.residence_details.address": "Ton adresse", "bank_transfer.residence_details.city": "Ville", "bank_transfer.residence_details.country_of_residence": "Pays de résidence", "bank_transfer.residence_details.country_placeholder": "Pays", "bank_transfer.residence_details.postcode": "Code postal", "bank_transfer.residence_details.street": "Rue", "bank_transfer.residence_details.your_residence": "Ton lieu de résidence", "bank_transfers.choose-wallet.continue": "<PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.test": "Ajouter un portefeuille", "bank_transfers.choose-wallet.warning.subtitle": "Tu ne peux lier qu'un seul portefeuille à la fois. Tu ne pourras pas modifier le portefeuille lié.", "bank_transfers.choose-wallet.warning.title": "Choisis bien ton portefeuille", "bank_transfers.choose_wallet.subtitle": "Choi<PERSON> le portefeuille à lier à ton compte. ", "bank_transfers.choose_wallet.title": "Choisir le portefeuille", "bank_transfers.continue": "<PERSON><PERSON><PERSON>", "bank_transfers.currency_is_currently_not_supported": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit-header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.account-name": "Nom du compte", "bank_transfers.deposit.account-number-copied": "Numéro de compte copié", "bank_transfers.deposit.amount-input": "<PERSON><PERSON> déposer", "bank_transfers.deposit.amount-output": "Montant de destination", "bank_transfers.deposit.amount-output.error": "erreur", "bank_transfers.deposit.buttet-point.receive-crypto": "Reçois {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "<PERSON><PERSON><PERSON>", "bank_transfers.deposit.currency-not-supported.subtitle": "Les dépôts bancaires depuis {code} sont désactivés jusqu'à nouvel ordre.", "bank_transfers.deposit.currency-not-supported.title": "{code} d<PERSON><PERSON><PERSON>ts actuellement non pris en charge", "bank_transfers.deposit.default-token.balance": "Solde {amount}", "bank_transfers.deposit.deposit-header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.enter_amount": "<PERSON><PERSON> un montant", "bank_transfers.deposit.iban-copied": "IBAN copié", "bank_transfers.deposit.increase-amount": "Virement minimum : {limit}", "bank_transfers.deposit.loading": "Chargement", "bank_transfers.deposit.max-limit-reached": "Le montant dépasse la limite de virement", "bank_transfers.deposit.modal.kyc.button-text": "Commencer", "bank_transfers.deposit.modal.kyc.text": "Pour vérifier ton identité, nous aurons besoin de quelques informations personnelles et de documents. La soumission ne prend généralement que quelques minutes.", "bank_transfers.deposit.modal.kyc.title": "Vérifie ton identité pour augmenter tes limites", "bank_transfers.deposit.reduce_amount": "<PERSON><PERSON><PERSON><PERSON> le montant", "bank_transfers.deposit.show-account.account-number": "Numéro de compte", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Code de tri", "bank_transfers.deposit.sort-code-copied": "Code de tri copié", "bank_transfers.deposit.withdraw-header": "Retrait", "bank_transfers.failed_to_load_fee": "Inconnu", "bank_transfers.fees": "<PERSON><PERSON>", "bank_transfers.increase-amount": "Virement minimum : {limit}", "bank_transfers.insufficient-funds": "Fonds insuffisants", "bank_transfers.select_country_of_residence.title": "Où habites-tu ?", "bank_transfers.setup.cta": "Configurer les virements", "bank_transfers.setup.enter-amount": "<PERSON><PERSON> un montant", "bank_transfers.source_of_funds.form.business_income": "Revenus professionnels", "bank_transfers.source_of_funds.form.other": "<PERSON><PERSON>", "bank_transfers.source_of_funds.form.pension": "Retraite", "bank_transfers.source_of_funds.form.salary": "Salaire", "bank_transfers.source_of_funds.form.title": "Ta source de fonds", "bank_transfers.source_of_funds_description.placeholder": "Décris la source de tes fonds…", "bank_transfers.source_of_funds_description.title": "Dis-nous en plus sur ta source de fonds", "bank_transfers.withdraw-header": "Retrait", "bank_transfers.withdraw.amount-input": "<PERSON><PERSON>", "bank_transfers.withdraw.max-limit-reached": "Le montant dépasse la limite de virement", "bank_transfers.withdrawal.verify-id": "<PERSON><PERSON><PERSON><PERSON> le montant", "banner.above_maximum_limit.maximum_input_limit_exceeded": "Limite de saisie maximale dépassée", "banner.above_maximum_limit.maximum_limit_per_deposit": "C'est la limite maximale par dépôt", "banner.above_maximum_limit.subtitle": "Limite de saisie maximale dépassée", "banner.above_maximum_limit.title": "<PERSON><PERSON><PERSON><PERSON> le montant à {amount} ou moins", "banner.above_maximum_limit.title.default": "<PERSON><PERSON><PERSON><PERSON> le montant", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Limite de saisie minimale dépassée", "banner.below_minimum_limit.minimum_limit_for_token": "C'est la limite minimale pour ce jeton", "banner.below_minimum_limit.title": "Augmente le montant à {amount} ou plus", "banner.below_minimum_limit.title.default": "Augmente le montant", "breaard.in_porgress.info_popup.cta": "<PERSON><PERSON><PERSON>se pour gagner {earn}", "breaard.in_porgress.info_popup.footnote": "En utilisant Zeal et la carte Gnosis Pay, tu acceptes les conditions de cette campagne de récompenses.", "breaward.in_porgress.info_popup.bullet_point_1": "Dépense {remaining} d'ici {time} pour obtenir cette récompense.", "breaward.in_porgress.info_popup.bullet_point_2": "Seuls les achats Gnosis Pay valides sont comptabilisés dans tes dépenses.", "breaward.in_porgress.info_popup.bullet_point_3": "Une fois la récompense obtenue, elle sera versée sur ton compte Zeal.", "breaward.in_porgress.info_popup.header": "<PERSON><PERSON><PERSON> {earn}, en dépensant {remaining}", "breward.celebration.for_spending": "Pour avoir utilisé ta carte", "breward.dc25-eligible-celebration.for_spending": "Tu fais partie des premiers {limit} !", "breward.dc25-non-eligible-celebration.for_spending": "Tu ne faisais pas partie des premiers {limit} à avoir dépensé", "breward.expired_banner.earn_by_spending": "<PERSON><PERSON><PERSON> {earn} en dépensant {amount}", "breward.expired_banner.reward_expired": "{earn} récompense expirée", "breward.in_progress_banner.cta.title": "<PERSON><PERSON><PERSON>se pour gagner {earn}", "breward.ready_to_claim.error.try_again": "<PERSON><PERSON><PERSON><PERSON>", "breward.ready_to_claim.error_title": "Échec de l'obtention de la récompense", "breward.ready_to_claim.in_progress": "Obtention de la récompense en cours", "breward.ready_to_claim.youve_earned": "Tu as gagné {earn} !", "breward_already_claimed.title": "Récompense déjà récupérée. Si tu n'as pas reçu le jeton de récompense, contacte le support.", "breward_cannotbe_claimed.title": "La récompense ne peut pas être récupérée pour le moment. Réessaie plus tard.", "bridge.best_return": "Itinéraire le plus rentable", "bridge.best_serivce_time": "Itinéraire le plus rapide", "bridge.check_status.complete": "<PERSON><PERSON><PERSON><PERSON>", "bridge.check_status.progress_text": "<PERSON><PERSON><PERSON> de {from} vers {to}", "bridge.remove_topup": "Supprimer la recharge", "bridge.request_status.completed": "<PERSON><PERSON><PERSON><PERSON>", "bridge.request_status.pending": "En attente", "bridge.widget.completed": "<PERSON><PERSON><PERSON><PERSON>", "bridge.widget.currencies": "{from} vers {to}", "bridge_rote.widget.title": "<PERSON><PERSON><PERSON>", "browse.discover_more_apps": "Découvrir plus d'applications", "browse.google_search_term": "Rechercher « {searchTerm} »", "brward.celebration.you_earned": "<PERSON> as gagné", "brward.expired_banner.subtitle": "Plus de chance la prochaine fois", "brward.in_progress_banner.subtitle": "Expire dans {expiredInFormatted}", "buy": "<PERSON><PERSON><PERSON>", "buy.enter_amount": "<PERSON><PERSON> un montant", "buy.loading": "Chargement…", "buy.no_routes_found": "Aucun itinéraire trouvé", "buy.not_enough_balance": "Solde insuffisant", "buy.select-currency.title": "Sélectionner un jeton", "buy.select-to-currency.title": "Acheter des jetons", "buy_form.title": "Acheter un jeton", "cancelled-card.create-card-button.primary": "Obtenir une nouvelle carte virtuelle", "cancelled-card.switch-card-button.primary": "Changer de carte", "cancelled-card.switch-card-button.short-text": "Tu as une autre carte active", "card": "<PERSON><PERSON>", "card-add-cash.confirm-stage.banner.no-routes-found": "Aucune option trouvée, essaie un autre jeton ou montant", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Il te faut {amount} de plus {symbol} pour payer les frais", "card-add-cash.confirm-stage.banner.value-loss": "Tu vas perdre {loss} de valeur", "card-add-cash.confirm-stage.banner.value-loss.revert": "Annuler", "card-add-cash.edit-stage.cta.cancel": "Annuler", "card-add-cash.edit-stage.cta.continue": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.enter-amount": "Saisir mt.", "card-add-cash.edit-stage.cta.reduce-to-max": "Mets au max", "card-add-cash.edit-staget.banner.no-routes-found": "Aucune option trouvée, essaie un autre jeton ou montant", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Valide sur ton portefeuille matériel.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Signer sur le portefeuille matériel", "card-balance": "Solde : {balance}", "card-cashback.status.title": "Dépôt sur le cashback", "card-copy-safe-address.copy_address": "Co<PERSON>r l'adresse", "card-copy-safe-address.copy_address.done": "Copiée", "card-copy-safe-address.warning.description": "Cette adresse ne peut recevoir que {cardAsset} sur la Gnosis Chain. N'envoie pas d'actifs d'autres réseaux à cette adresse. Ils seront perdus.", "card-copy-safe-address.warning.header": "Envoie uniquement {cardAsset} sur la Gnosis Chain", "card-marketing-card.center.subtitle": "Frais de change", "card-marketing-card.center.title": "0 %", "card-marketing-card.left.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card-marketing-card.right.subtitle": "<PERSON>au d'inscription", "card-marketing-card.title": "La carte VISA européenne à intérêts élevés", "card-marketing-tile.get-started": "Commencer", "card-select-from-token-title": "Sé<PERSON><PERSON>ner le jeton à envoyer", "card-top-up.banner.subtitle.completed": "<PERSON><PERSON><PERSON><PERSON>", "card-top-up.banner.subtitle.failed": "Échec", "card-top-up.banner.subtitle.pending": "{timerString} En cours", "card-top-up.banner.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> de {amount}", "card-topup.select-token.emptyState": "<PERSON><PERSON>n jeton trouvé", "card.activate.card_number_not_valid": "Nº de carte invalide. Vérifie et réessaie.", "card.activate.invalid_card_number": "Numéro de carte invalide.", "card.activation.activate_physical_card": "<PERSON>r la carte physique", "card.add-cash.amount-to-withdraw": "<PERSON><PERSON> déposer", "card.add-from-earn-form.title": "Ajouter des fonds à la carte", "card.add-from-earn-form.withdraw-to-card": "<PERSON><PERSON><PERSON>", "card.add-from-earn.amount-to-withdraw": "Montant à virer sur la carte", "card.add-from-earn.enter-amount": "<PERSON><PERSON> un montant", "card.add-from-earn.loading": "Chargement", "card.add-from-earn.max-label": "Solde : {amount}", "card.add-from-earn.no-routes-found": "Aucun itinéraire trouvé", "card.add-from-earn.not-enough-balance": "Solde insuffisant", "card.add-owner.queued": "Ajout de propriétaire en attente", "card.add-to-wallet-flow.subtitle": "<PERSON>ie depuis ton portefeuille.", "card.add-to-wallet.copy-card-number": "Co<PERSON> le numéro de carte ci-dessous", "card.add-to-wallet.title": "Ajouter à {platformName} Wallet", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "ANNULÉE", "card.card-owner-not-found.disconnect-btn": "Déconnecter carte de Zeal", "card.card-owner-not-found.subtitle": "Mets à jour le propriétaire de la carte pour la reconnecter.", "card.card-owner-not-found.title": "Reconnecter la carte", "card.card-owner-not-found.update-owner-btn": "Modifier le propriétaire", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} dans {date}", "card.cashback.widgetNoCashback.subtitle": "Fais un dépôt pour commencer à gagner", "card.cashback.widgetNoCashback.title": "Jusqu'à {defaultPercentage} de cashback", "card.cashback.widgetcashbackValue.rewards": "{amount} en attente", "card.cashback.widgetcashbackValue.title": "{percentage} de cashback", "card.choose-wallet.connect_card": "Connecter la carte", "card.choose-wallet.create-new": "Ajouter un portefeuille titulaire", "card.choose-wallet.import-another-wallet": "Importer un autre portefeuille", "card.choose-wallet.import-current-owner": "Importer le titulaire actuel", "card.choose-wallet.import-current-owner.sub-text": "Importe les clés du portefeuille titulaire", "card.choose-wallet.title": "Sélectionne un portefeuille pour ta carte", "card.connectWalletToCardGuide": "Copier l'adresse du portefeuille", "card.connectWalletToCardGuide.addGnosisPayOwner": "Ajouter un propriétaire Gnosis Pay", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. <PERSON>uvre Gnosispay.com avec ton autre portefeuille{br}2. <PERSON><PERSON> sur « Compte »{br}3. <PERSON><PERSON> sur « Détails du compte »{br}4. <PERSON><PERSON> sur « Modifier » à côté de « Propriétaire du compte », et{br}5. Clique sur « Ajouter une adresse »{br}6. <PERSON>le ton adresse Zeal et clique sur Enregistrer", "card.connectWalletToCardGuide.header": "Connecter {account} à la carte Gnosis Pay", "card.connect_card.start": "Lier ma carte Gnosis Pay", "card.copiedAddress": "Co<PERSON>é {formattedAddress}", "card.disconnect-account.title": "Déconnecter le compte", "card.hw-wallet-support-drop.add-owner-btn": "Ajouter un propriétaire", "card.hw-wallet-support-drop.disconnect-btn": "Déconnecter carte de Zeal", "card.hw-wallet-support-drop.subtitle": "Pour continuer, ajoute un propriétaire autre qu'un portefeuille matériel.", "card.hw-wallet-support-drop.title": "Portefeuilles matériels non pris en charge pour la carte", "card.kyc.continue": "Continuer la vérification", "card.list_item.title": "<PERSON><PERSON>", "card.onboarded.transactions.empty.description": "Ton activité de paiement apparaîtra ici", "card.onboarded.transactions.empty.title": "Activité", "card.order.continue": "Poursuivre la commande", "card.order.free_virtual_card": "<PERSON><PERSON> gratuite", "card.order.start": "Commander carte gratuite", "card.owner-not-imported.cancel": "Annuler", "card.owner-not-imported.import": "Importer", "card.owner-not-imported.subtitle": "Pour autoriser cette transaction, associe le portefeuille propriétaire de ton compte Gnosis Pay à Zeal. Remarque : ceci est différent de ta connexion habituelle au portefeuille Gnosis Pay.", "card.owner-not-imported.title": "Ajouter le propriétaire du compte Gnosis Pay", "card.page.order_free_physical_card": "Commander carte physique", "card.pin.change_pin_at_atm": "Le PIN se change à certains distributeurs", "card.pin.timeout": "L'écran se fermera dans {seconds} s", "card.quick-actions.add-assets": "Ajouter", "card.quick-actions.add-cash": "Ajouter", "card.quick-actions.details": "Détails", "card.quick-actions.freeze": "Bloquer", "card.quick-actions.freezing": "Blocage en cours", "card.quick-actions.unfreeze": "Débloquer", "card.quick-actions.unfreezing": "Déblocage en cours", "card.quick-actions.withdraw": "<PERSON><PERSON><PERSON>", "card.read-only-detected.create-new": "Ajouter un portefeuille titulaire", "card.read-only-detected.import-current-owner": "Importer les clés de {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Importe les clés privées du portefeuille {address}", "card.read-only-detected.title": "Carte sur un portefeuille en lecture seule", "card.remove-owner.queued": "Suppression du titulaire en attente", "card.settings.disconnect-from-zeal": "Déconnecter de Zeal", "card.settings.edit-owners": "Changer les propriétaires", "card.settings.getCard": "Obtenir une autre carte", "card.settings.getCard.subtitle": "Cartes virtuelles ou physiques", "card.settings.notRecharging": "Re<PERSON><PERSON>", "card.settings.notifications.subtitle": "Recevoir les notifications de paiement", "card.settings.notifications.title": "Notifications de la carte", "card.settings.page.title": "Paramètres de la carte", "card.settings.select-card.cancelled-cards": "Cartes annulées", "card.settings.setAutoRecharge": "Définir la recharge auto", "card.settings.show-card-address": "Afficher l'adresse de la carte", "card.settings.spend-limit": "Définir une limite de dépense", "card.settings.spend-limit-title": "Limite quotidienne actuelle : {limit}", "card.settings.switch-active-card": "Changer de carte active", "card.settings.switch-active-card-description": "Carte active : {card}", "card.settings.switch-card.card-item.cancelled": "<PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.frozen": "Bloquée", "card.settings.switch-card.card-item.title": "Carte G<PERSON>", "card.settings.switch-card.card-item.title.physical": "<PERSON>te physique", "card.settings.switch-card.card-item.title.virtual": "<PERSON><PERSON>", "card.settings.switch-card.title": "Sélectionner une carte", "card.settings.targetBalance": "Solde cible : {threshold}", "card.settings.view-pin": "Voir le code PIN", "card.settings.view-pin-description": "Protège toujours ton code PIN", "card.title": "<PERSON><PERSON>", "card.transactions.header": "Transactions de la carte", "card.transactions.see_all": "Voir toutes les transactions", "card.virtual": "VIRTUELLE", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Le cashback est versé sur ta carte au début de la semaine suivant celle où il a été gagné.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "Plus tu déposes, plus tu gagnes sur chaque achat.", "cardCashback.onboarding.title": "Jusqu'à {percentage} de cashback", "cardCashbackWithdraw.amount": "<PERSON><PERSON>", "cardCashbackWithdraw.header": "Retirer {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "<PERSON><PERSON> b<PERSON>, activation impossible", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "Jusq<PERSON>'à {percentage}", "cardWidget.startEarning": "Commencer à gagner", "cardWithdraw.amount": "<PERSON><PERSON>", "cardWithdraw.header": "<PERSON><PERSON><PERSON> de la carte", "cardWithdraw.selectWithdrawWallet.title": "Choisir le portefeuille{br}de destination", "cardWithdraw.success.cta": "<PERSON><PERSON><PERSON>", "cardWithdraw.success.subtitle": "Par sécurité, tous les retraits de la carte Gnosis Pay prennent 3 minutes à être traités", "cardWithdraw.success.title": "Cette opération prendra 3 minutes", "card_top_up_trx.send": "Envoi", "card_top_up_trx.to": "À", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "Date d'expiration", "cards.card_number": "Numéro de carte", "cards.choose-wallet.no-active-accounts": "Tu n'as aucun portefeuille actif", "cards.copied_card_number": "Numéro de carte copié", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Plafond quotidien dé<PERSON>", "cards.transactions.decline_reason.incorrect_pin": "PIN incorrect", "cards.transactions.decline_reason.incorrect_security_code": "Code de sécurité incorrect", "cards.transactions.decline_reason.invalid_amount": "<PERSON><PERSON> invalide", "cards.transactions.decline_reason.low_balance": "Solde insuffisant", "cards.transactions.decline_reason.other": "<PERSON><PERSON><PERSON><PERSON>", "cards.transactions.decline_reason.pin_tries_exceeded": "Trop de tentatives de PIN", "cards.transactions.status.refund": "Remboursement", "cards.transactions.status.reversal": "Annulation", "cashback-deposit.trx.title": "Dépôt sur le Cashback", "cashback-estimate.text": "Il s'agit d'une estimation et non d'un paiement garanti. Toutes les règles de cashback connues sont appliquées, mais Gnosis Pay peut exclure des transactions à sa discrétion. Une dépense maximale de {amount} par semaine est éligible au cashback, même si l'estimation pour cette transaction indique un montant total plus élevé.", "cashback-estimate.text.fallback": "Ceci est une estimation et non un paiement garanti. Bien que toutes les règles de cashback connues soient appliquées, Gnosis Pay peut exclure des transactions à sa discrétion.", "cashback-estimate.title": "Estimation du cashback", "cashback-onbarding-tersm.subtitle": "Les données de transaction de ta carte seront partagées avec <PERSON>, qui est responsable de la distribution des récompenses de cashback. En cliquant sur Accepter, tu acceptes le programme de Cashback de Gnosis DAO et ses <terms>Conditions générales</terms>", "cashback-onbarding-tersm.title": "Conditions d'utilisation et confidentialité", "cashback-tx-activity.retry": "<PERSON><PERSON><PERSON><PERSON>", "cashback-unconfirmed-payments-info.subtitle": "Les paiements sont éligibles au cashback une fois qu'ils sont réglés avec le commerçant. D'ici là, ils apparaissent comme des paiements non confirmés. Les paiements non réglés ne donnent pas droit au cashback.", "cashback-unconfirmed-payments-info.title": "Paiements par carte non confirmés", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.activity.title": "Activité récente", "cashback.activity.withdrawal": "Retrait", "cashback.deposit": "<PERSON><PERSON><PERSON>r", "cashback.deposit.amount.label": "<PERSON><PERSON> déposer", "cashback.deposit.change": "{from} à {to}", "cashback.deposit.confirmation.subtitle": "Les taux de cashback sont mis à jour une fois par semaine. Dépose maintenant pour augmenter le cashback de la semaine prochaine.", "cashback.deposit.confirmation.title": "Tu commenceras à gagner {percentage} le {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Échange des jetons contre des {currency} sur {network} Chain", "cashback.deposit.get.tokens.title": "Obtenir des {currency} jetons", "cashback.deposit.header": "Déposer des {currency}", "cashback.deposit.max_label": "Max : {amount}", "cashback.deposit.select-wallet.title": "<PERSON><PERSON> le portefeuille depuis le<PERSON> dé<PERSON>r", "cashback.deposit.yourcashback": "Ton cashback", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "<PERSON><PERSON> le portefeuille{br}vers lequel retirer", "cashback.transaction-details.network-label": "<PERSON><PERSON><PERSON>", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "De", "cashback.transaction-details.top-row.label-rewards": "Période du cashback", "cashback.transaction-details.top-row.label-withdrawal": "À", "cashback.transaction-details.transaction": "ID de la transaction", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Transactions de cashback", "cashback.withdraw": "<PERSON><PERSON><PERSON>", "cashback.withdraw.confirmation.cashback_reduction": "Le cashback de cette semaine, y compris ce que tu as dé<PERSON><PERSON> gagné, passera de {before} à {after}", "cashback.withdraw.queued": "Retrait en file d'attente", "cashback.withdrawal.change": "{from} à {to}", "cashback.withdrawal.confirmation.subtitle": "Lancer le retrait de {amount} avec un délai de 3 minutes. <PERSON><PERSON> r<PERSON> ton cashback à {after}.", "cashback.withdrawal.confirmation.title": "Le cashback diminuera si tu retires des GNO", "cashback.withdrawal.delayTransaction.title": "Lancer le retrait de GNO avec{br} un délai de 3 minutes", "cashback.withdrawal.withdraw": "<PERSON><PERSON><PERSON>", "cashback.withdrawal.yourcashback": "Ton cashback", "celebration.aave": "Gagné avec <PERSON>", "celebration.cashback.subtitle": "<PERSON><PERSON><PERSON> en {code}", "celebration.cashback.subtitleGNO": "{amount} gagnés la dernière fois", "celebration.chf": "Gagné avec Frankencoin", "celebration.lido": "Gagné avec Lido", "celebration.sky": "Gagné avec Sky", "celebration.title": "Cashback total", "celebration.well_done.title": "Bien joué !", "change-withdrawal-account.add-new-account": "Ajouter un autre compte bancaire", "change-withdrawal-account.item.shortText": "{currency} Co<PERSON>e", "check-confirmation.approve.footer.for": "Pour", "checkConfirmation.title": "Résultat de la transaction", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin et Ether", "collateral.ethereum": "Ethereum", "collateral.other": "Autres", "collateral.rwa": "Actifs du monde réel", "collateral.stablecoins": "Stablecoins (indexés sur l'USD)", "collateral.us-t-bills": "Bons du Trésor US", "confirm-bank-transfer-recipient.bullet-1": "Aucuns frais sur l'EUR numérique", "confirm-bank-transfer-recipient.bullet-2": "<PERSON><PERSON><PERSON><PERSON><PERSON> vers {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Partager les informations du compte Gnosis Pay avec Monerium, un EME agréé et réglementé. <link>En savoir plus</link>", "confirm-bank-transfer-recipient.bullet-4": "Accepter les <link>conditions d'utilisation</link>", "confirm-bank-transfer-recipient.title": "Accepter les conditions", "confirm-change-withdrawal-account.cancel": "Annuler", "confirm-change-withdrawal-account.confirm": "Confirmer", "confirm-change-withdrawal-account.saving": "<PERSON><PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.subtitle": "Tous tes retraits depuis Zeal arriveront sur ce compte bancaire.", "confirm-change-withdrawal-account.title": "Changer de banque de réception", "confirm-ramove-withdrawal-account.title": "Supprimer le compte bancaire", "confirm-remove-withdrawal-account.subtitle": "Ce compte sera supprimé de Zeal. Tu pourras le rajouter à tout moment.", "confirmTransaction.finalNetworkFee": "<PERSON><PERSON>", "confirmTransaction.importKeys": "Importer les clés", "confirmTransaction.networkFee": "<PERSON><PERSON>", "confirmation.title": "Envoyer {amount} à {recipient}", "conflicting-monerium-account.add-owner": "Ajouter comme propriétaire G<PERSON> Pay", "conflicting-monerium-account.create-wallet": "Créer un nouveau Smart Wallet", "conflicting-monerium-account.disconnect-card": "Déconnecter la carte de Zeal et la reconnecter avec le nouveau propriétaire", "conflicting-monerium-account.header": "{wallet} lié à un autre compte Monerium", "conflicting-monerium-account.subtitle": "Change le portefeuille propriétaire de Gnosis Pay", "connection.diconnected.got_it": "<PERSON><PERSON><PERSON>", "connection.diconnected.page1.subtitle": "Zeal fonctionne partout où MetaMask est accepté. Connecte-toi comme tu le ferais avec MetaMask.", "connection.diconnected.page1.title": "Comment se connecter avec Zeal ?", "connection.diconnected.page2.subtitle": "Tu verras plusieurs options. Zeal peut en faire partie. Si Zeal n'apparaît pas…", "connection.diconnected.page2.title": "Clique sur Connecter le portefeuille", "connection.diconnected.page3.subtitle": "Nous lancerons une connexion avec Zeal. Navigateur ou Injecté devrait aussi fonctionner. Essaie.", "connection.diconnected.page3.title": "<PERSON><PERSON>", "connectionSafetyCheck.tag.caution": "Prudence", "connectionSafetyCheck.tag.danger": "Danger", "connectionSafetyCheck.tag.passed": "<PERSON><PERSON><PERSON>", "connectionSafetyConfirmation.subtitle": "Es-tu sûr de vouloir continuer ?", "connectionSafetyConfirmation.title": "Ce site semble dangereux", "connection_state.connect.cancel": "Annuler", "connection_state.connect.changeToMetamask": "Passer à MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Passer à MetaMask", "connection_state.connect.connect_button": "Connecter", "connection_state.connect.expanded.connected": "Connecté", "connection_state.connect.expanded.title": "Connexion", "connection_state.connect.safetyChecksLoading": "Vérification de la sécurité du site", "connection_state.connect.safetyChecksLoadingError": "Impossible de vérifier la sécurité", "connection_state.connected.expanded.disconnectButton": "Déconnecter Zeal", "connection_state.connected.expanded.title": "Connecté", "copied-diagnostics": "Diagnostics copiés", "copy-diagnostics": "<PERSON><PERSON><PERSON> les diagnostics", "counterparty.component.add_recipient_primary_text": "Ajouter un bénéficiaire", "counterparty.country": "Pays", "counterparty.countryTitle": "Pays du bénéficiaire", "counterparty.currency": "<PERSON><PERSON>", "counterparty.delete.success.title": "Supprimé", "counterparty.edit.success.title": "Modifications enregistrées", "counterparty.errors.country_required": "Pays requis", "counterparty.errors.first_name.invalid": "Le prénom doit être plus long", "counterparty.errors.last_name.invalid": "Le nom doit être plus long", "counterparty.first_name": "Prénom", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Envoyer à une banque", "countrySelector.noCountryFound": "Aucun pays trouvé", "countrySelector.title": "Choisir un pays", "create-passkey.cta": "<PERSON><PERSON><PERSON> un passkey", "create-passkey.extension.cta": "<PERSON><PERSON><PERSON>", "create-passkey.footnote": "Proposé par", "create-passkey.mobile.cta": "<PERSON><PERSON><PERSON><PERSON> la configuration", "create-passkey.steps.enable-recovery": "Configurer la récupération cloud", "create-passkey.steps.setup-biometrics": "Activer la sécurité biométrique", "create-passkey.subtitle": "Les passkeys sont plus sûrs que les mots de passe. Chiffrés dans le cloud, ils facilitent la récupération.", "create-passkey.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> le compte", "create-smart-wallet": "<PERSON><PERSON><PERSON> un Smart Wallet", "create-userop.progress.text": "Création en cours", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Continuer sur Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Aller sur Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "<PERSON> as dé<PERSON><PERSON> commencé à commander ta carte. Retourne sur le site Gnosis Pay pour finaliser.", "create_recharge_preferences.card": "<PERSON><PERSON>", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "Gagner {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "Conserver des liquidités", "create_recharge_preferences.link_accounts_title": "<PERSON>r les comptes", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "Ta carte ne se rechargera PAS automatiquement après chaque paiement.", "create_recharge_preferences.not_configured_title": "<PERSON><PERSON><PERSON> et dé<PERSON>ser", "create_recharge_preferences.recharge_from_earn_accounts_description": "Ta carte se recharge automatiquement depuis ton compte Earn après chaque paiement.", "create_recharge_preferences.subtitle": "par an", "creating-account.loading": "Création du compte", "creating-gnosis-pay-account": "Création du compte", "currencies.bridge.select_routes.emptyState": "Aucun itinéraire trouvé pour cette passerelle", "currency.add_currency.add_token": "Ajouter un jeton", "currency.add_currency.not_a_valid_address": "L'adresse de ce jeton n'est pas valide", "currency.add_currency.token_decimals_feild": "Décimales du jeton", "currency.add_currency.token_feild": "<PERSON><PERSON><PERSON>", "currency.add_currency.token_symbol_feild": "Symbole du jeton", "currency.add_currency.update_token": "Mettre à jour le jeton", "currency.add_custom.remove_token.cta": "<PERSON><PERSON><PERSON><PERSON>", "currency.add_custom.remove_token.header": "<PERSON><PERSON><PERSON><PERSON> le jeton", "currency.add_custom.remove_token.subtitle": "Ton portefeuille conservera tout solde de ce jeton, mais il sera masqué de ton portfolio Zeal.", "currency.add_custom.token_removed": "<PERSON>on supprimé", "currency.add_custom.token_updated": "<PERSON>on mis à jour", "currency.balance_label": "Solde : {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "Ton virement bancaire a bien transféré {fiat} en {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "Tu as reçu {crypto}", "currency.bankTransfer.deposit_status.success": "Reçu dans ton portefeuille", "currency.bankTransfer.deposit_status.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.check_bank_account": "Vérifie ton compte bancaire", "currency.bankTransfer.off_ramp.complete": "<PERSON><PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Envoi vers ta banque", "currency.bankTransfer.off_ramp.transferring_to_currency": "Virement vers {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Les fonds devraient être arrivés sur ton compte bancaire.", "currency.bankTransfer.withdrawal_status.success": "Envoyé à ta banque", "currency.bankTransfer.withdrawal_status.title": "Retrait", "currency.bank_transfer.create_unblock_user.email": "Adresse e-mail", "currency.bank_transfer.create_unblock_user.email_invalid": "E-mail non valide", "currency.bank_transfer.create_unblock_user.email_missing": "Requis", "currency.bank_transfer.create_unblock_user.first_name": "Prénom", "currency.bank_transfer.create_unblock_user.first_name_missing": "Requis", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "Lettres, chiffres, espaces et - . , & ( ) ' uniquement.", "currency.bank_transfer.create_unblock_user.last_name": "Nom de famille", "currency.bank_transfer.create_unblock_user.last_name_missing": "Requis", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "Lettres, chiffres, espaces et - . , & ( ) ' uniquement.", "currency.bank_transfer.create_unblock_user.note": "En continuant, tu acceptes les <terms>Conditions d'utilisation d'Unblock</terms> et sa <policy>Politique de confidentialité</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Écris ton nom exactement comme sur ton compte bancaire", "currency.bank_transfer.create_unblock_user.title": "Lier ton compte bancaire", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Numéro de compte", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Pays de la banque", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "<PERSON><PERSON>f<PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Code de tri", "currency.bank_transfer.create_unblock_withdraw_account.success": "Compte configuré", "currency.bank_transfer.create_unblock_withdraw_account.title": "Lier ton compte bancaire", "currency.bank_transfer.residence-form.address-required": "Requis", "currency.bank_transfer.residence-form.address-unsupported-char": "Seuls les lettres, chiffres, espaces et , ; {apostrophe} - \\\\ sont autorisés.", "currency.bank_transfer.residence-form.city-required": "Requis", "currency.bank_transfer.residence-form.city-unsupported-char": "Seuls les lettres, chiffres, espaces et . , - & ( ) {apostrophe} sont autorisés.", "currency.bank_transfer.residence-form.postcode-invalid": "Code postal invalide", "currency.bank_transfer.residence-form.postcode-required": "Requis", "currency.bank_transfer.validation.invalid.account_number": "Numéro de compte invalide", "currency.bank_transfer.validation.invalid.iban": "IBAN invalide", "currency.bank_transfer.validation.invalid.sort_code": "Code de tri invalide", "currency.bridge.amount_label": "<PERSON><PERSON> <PERSON> transférer", "currency.bridge.best_returns.subtitle": "Ce fournisseur de passerelle t'offre le montant final le plus élevé, tous frais inclus.", "currency.bridge.best_returns_popup.title": "Mei<PERSON>ur rendement", "currency.bridge.bridge_from": "De", "currency.bridge.bridge_gas_fee_loading_failed": "Erreur de chargement des frais réseau", "currency.bridge.bridge_low_slippage": "Glissement très faible. Essaie de l'augmenter.", "currency.bridge.bridge_provider": "Prestataire du virement", "currency.bridge.bridge_provider_loading_failed": "Nous n'avons pas pu charger les fournisseurs", "currency.bridge.bridge_settings": "Paramètres de la passerelle", "currency.bridge.bridge_status.subtitle": "Via {name}", "currency.bridge.bridge_status.title": "<PERSON><PERSON><PERSON>", "currency.bridge.bridge_to": "À", "currency.bridge.fastest_route_popup.subtitle": "Ce fournisseur de passerelle offre l'itinéraire de transaction le plus rapide.", "currency.bridge.fastest_route_popup.title": "Itinéraire le plus rapide", "currency.bridge.from": "De", "currency.bridge.success": "<PERSON><PERSON><PERSON><PERSON>", "currency.bridge.title": "<PERSON><PERSON><PERSON>", "currency.bridge.to": "À", "currency.bridge.topup": "Recharger {symbol}", "currency.bridge.withdrawal_status.title": "Retrait", "currency.card.card_top_up_status.title": "Ajouter des fonds à la carte", "currency.destination_amount": "Montant de destination", "currency.hide_currency.confirm.subtitle": "Masque ce jeton de ton portfolio. Tu pourras le réafficher à tout moment.", "currency.hide_currency.confirm.title": "Masquer le jeton", "currency.hide_currency.success.title": "<PERSON><PERSON>", "currency.label": "Libellé (facultatif)", "currency.last_name": "Nom", "currency.max_loading": "Max :", "currency.swap.amount_to_swap": "<PERSON><PERSON> à échanger", "currency.swap.best_return": "Itinéraire au meilleur rendement", "currency.swap.destination_amount": "Montant de destination", "currency.swap.header": "Échange", "currency.swap.max_label": "Solde : {amount}", "currency.swap.provider.header": "Fournisseur de swap", "currency.swap.select_to_token": "Sélectionner un jeton", "currency.swap.swap_gas_fee_loading_failed": "Erreur de chargement des frais réseau", "currency.swap.swap_provider_loading_failed": "Erreur de chargement des fournisseurs", "currency.swap.swap_settings": "Paramètres d'échange", "currency.swap.swap_slippage_too_low": "Slippage très faible. Essaie de l'augmenter.", "currency.swaps_io_native_token_swap.subtitle": "Via Swaps.IO", "currency.swaps_io_native_token_swap.title": "Envoi", "currency.withdrawal.amount_from": "De", "currency.withdrawal.amount_to": "À", "currencySelector.title": "Choisir une devise", "dApp.wallet-does-not-support-chain.subtitle": "Ton portefeuille ne semble pas prendre en charge {network}. Essaie de te connecter avec un autre portefeuille, ou utilise Zeal.", "dApp.wallet-does-not-support-chain.title": "R<PERSON>eau non pris en charge", "dapp.connection.manage.confirm.disconnect.all.cta": "<PERSON><PERSON>", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Es-tu sûr de vouloir tout déconnecter ?", "dapp.connection.manage.confirm.disconnect.all.title": "Tout déconnecter", "dapp.connection.manage.connection_list.main.button.title": "Déconnecter", "dapp.connection.manage.connection_list.no_connections": "Tu n'as aucune application connectée.", "dapp.connection.manage.connection_list.section.button.title": "Tout déconnecter", "dapp.connection.manage.connection_list.section.title": "Actives", "dapp.connection.manage.connection_list.title": "Connexions", "dapp.connection.manage.disconnect.success.title": "Applications déconnectées", "dapp.metamask_mode.title": "Mode MetaMask", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4 %", "dc25-card-marketing-card.left.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dc25-card-marketing-card.right.subtitle": "100 personnes", "dc25-card-marketing-card.title": "Les 100 premiers à dépenser 50 € gagneront {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Tu ne pourras pas effectuer cette action pendant 3 min. Pour des raisons de sécurité, tout changement des paramètres de la carte ou tout retrait prend 3 minutes pour être traité.", "delayQueueBusyBanner.processing-yout-action.title": "Traitement de ton action, merci de patienter", "delayQueueBusyWidget.cardFrozen": "<PERSON><PERSON> blo<PERSON>", "delayQueueBusyWidget.processingAction": "Traitement de ton action", "delayQueueFailedBanner.action-incomplete.get-support": "<PERSON><PERSON><PERSON><PERSON> <PERSON> l'aide", "delayQueueFailedBanner.action-incomplete.subtitle": "<PERSON><PERSON><PERSON><PERSON>, une erreur est survenue lors de ton retrait ou de la mise à jour des paramètres. Contacte le support sur Discord.", "delayQueueFailedBanner.action-incomplete.title": "Action incomplète", "delayQueueFailedWidget.actionIncomplete.title": "Action sur la carte incomplète", "delayQueueFailedWidget.cardFrozen.subtitle": "<PERSON><PERSON> blo<PERSON>", "delayQueueFailedWidget.contactSupport": "<PERSON>er le support", "delay_queue_busy.subtitle": "Pour ta sécurité, les modifications ou retraits prennent 3 minutes. Ta carte est bloquée pendant ce temps.", "delay_queue_busy.title": "Action en cours de traitement", "delay_queue_failed.contact_support": "Support", "delay_queue_failed.subtitle": "<PERSON><PERSON><PERSON><PERSON>, une erreur est survenue lors de ton retrait ou de la mise à jour des paramètres. Contacte le support sur Discord.", "delay_queue_failed.title": "<PERSON>er le support", "deploy-earn-form-smart-wallet.in-progress.title": "Préparation de Earn", "deposit": "<PERSON><PERSON><PERSON>r", "disconnect-card-popup.cancel": "Annuler", "disconnect-card-popup.disconnect": "Déconnecter", "disconnect-card-popup.subtitle": "Ceci retirera ta carte de l'app Zeal. Ton portefeuille restera connecté à ta carte dans l'app Gnosis Pay. Tu pourras la reconnecter à tout moment.", "disconnect-card-popup.title": "Déconnecter la carte", "distance.long.days": "{count} jours", "distance.long.hours": "{count} heures", "distance.long.minutes": "{count} minutes", "distance.long.months": "{count} mois", "distance.long.seconds": "{count} secondes", "distance.long.years": "{count} ans", "distance.short.days": "{count} j", "distance.short.hours": "{count} h", "distance.short.minutes": "{count} min", "distance.short.months": "{count} mois", "distance.short.seconds": "{count} s", "distance.short.years": "{count} an", "duration.short.days": "{count}j", "duration.short.hours": "{count}h", "duration.short.minutes": "{count}m", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-deposit-view.into": "<PERSON><PERSON>", "earn-deposit-view.to": "À", "earn-deposit.swap.transfer-provider": "Fournisseur de virement", "earn-taker-investment-details.accrued-realtime": "Accumulés en temps réel", "earn-taker-investment-details.asset-class": "Classe d'actifs", "earn-taker-investment-details.asset-coverage-ratio": "Ratio de couverture des actifs", "earn-taker-investment-details.asset-reserve": "Réserve d'actifs", "earn-taker-investment-details.base_currency.label": "Devise de <PERSON>", "earn-taker-investment-details.chf.description": "<PERSON><PERSON><PERSON> des intérêts sur tes CHF en déposant des zCHF dans Frankencoin, un marché monétaire numérique de confiance. Les intérêts sont générés par des prêts surcollatéralisés à faible risque sur Frankencoin et versés en temps réel. Tes fonds sont en sécurité dans un sous-compte sécurisé que toi seul contrôles.", "earn-taker-investment-details.chf.description.with_address_link": "<PERSON><PERSON>ne des intérêts sur tes CHF en déposant des zCHF dans Frankencoin, un marché monétaire numérique de confiance. Les intérêts sont générés par des prêts surcollatéralisés à faible risque sur Frankencoin et versés en temps réel. Tes fonds sont en sécurité dans un sous-compte sécurisé <link>(copier 0x)</link> que toi seul contrôles.", "earn-taker-investment-details.chf.label": "Franc suisse numérique", "earn-taker-investment-details.collateral-composition": "Composition de la garantie", "earn-taker-investment-details.depositor-obligations": "<PERSON><PERSON> envers les déposants", "earn-taker-investment-details.eure.description": "Gagne des intérêts sur tes euros en déposant des EURe dans Aave, un marché monétaire numérique de confiance. L'EURe est un stablecoin euro entièrement réglementé, émis par Monerium et garanti 1:1 sur des comptes sécurisés. Les intérêts proviennent de prêts surgarantis à faible risque sur Aave et sont versés en temps réel. Tes fonds restent sur un sous-compte sécurisé que tu es seul à contrôler.", "earn-taker-investment-details.eure.description.with_address_link": "Gagne des intérêts sur tes euros en déposant des EURe dans Aave, un marché monétaire numérique de confiance. L'EURe est un stablecoin euro entièrement réglementé, émis par Monerium et garanti 1:1 sur des comptes sécurisés. Les intérêts proviennent de prêts surgarantis à faible risque sur Aave et sont versés en temps réel. Tes fonds restent sur un sous-compte sécurisé <link>(copier 0x)</link> que tu es seul à contrôler.", "earn-taker-investment-details.eure.label": "Euro numérique (EURe)", "earn-taker-investment-details.faq": "FAQ", "earn-taker-investment-details.fixed-income": "Revenu fixe", "earn-taker-investment-details.issuer": "<PERSON><PERSON><PERSON>", "earn-taker-investment-details.key-facts": "<PERSON><PERSON>s clés", "earn-taker-investment-details.liquidity": "Liquidité", "earn-taker-investment-details.operator": "Opérateur de marché", "earn-taker-investment-details.projected-yield": "Rendement annuel projeté", "earn-taker-investment-details.see-other-faq": "Voir toutes les autres FAQ", "earn-taker-investment-details.see-realtime": "Voir les données en temps réel", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Rendement sur 12 mois glissants", "earn-taker-investment-details.total-collateral": "Garantie totale", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "Offre totale de ZCHF", "earn-taker-investment-details.total_deposits": "Total des dépôts sur Aave", "earn-taker-investment-details.usd.description": "Sky est un marché monétaire numérique offrant des rendements stables, libellés en dollars US, provenant de bons du Trésor américain à court terme et de prêts surgarantis. Le tout sans la volatilité des cryptos, avec un accès aux fonds 24/7 et une garantie transparente on-chain.", "earn-taker-investment-details.usd.description.with_address_link": "Sky est un marché monétaire numérique offrant des rendements stables, libellés en dollars US, provenant de bons du Trésor américain à court terme et de prêts surgarantis, sans la volatilité des cryptos, avec un accès aux fonds 24/7 et une garantie transparente on-chain. Les investissements sont dans un sous-compte <link>(copier 0x)</link> que tu contrôles.", "earn-taker-investment-details.usd.ftx-difference": "En quoi est-ce différent de FTX, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ou <PERSON> ?", "earn-taker-investment-details.usd.high-returns": "Comment les rendements peuvent-ils être si élevés, surtout par rapport aux banques traditionnelles ?", "earn-taker-investment-details.usd.how-is-backed": "Comment Sky USD est-il garanti et qu'advient-il de mon argent si Zeal fait faillite ?", "earn-taker-investment-details.usd.income-sources": "Sources de revenus 2024", "earn-taker-investment-details.usd.insurance": "Mes fonds sont-ils assurés ou garantis par une entité (comme le FDIC ou similaire) ?", "earn-taker-investment-details.usd.label": "Dollar US numérique", "earn-taker-investment-details.usd.lose-principal": "Puis-je réellement perdre mon capital et dans quelles circonstances ?", "earn-taker-investment-details.variable-rate": "Prêts à taux variable", "earn-taker-investment-details.withdraw-anytime": "Retrait à tout moment", "earn-taker-investment-details.yield": "Rendement", "earn-withdrawal-view.approve.for": "Pour", "earn-withdrawal-view.approve.into": "<PERSON><PERSON>", "earn-withdrawal-view.swap.into": "En", "earn-withdrawal-view.withdraw.to": "Vers", "earn.add_another_asset.title": "Sélectionner un actif à investir", "earn.add_asset": "Ajouter un actif", "earn.asset_view.title": "<PERSON><PERSON><PERSON>", "earn.base-currency-popup.text": "La devise de base est celle utilisée pour évaluer et enregistrer tes dépôts, ton rendement et tes transactions. Si tu déposes dans une autre devise (par exemple, des EUR en USD), tes fonds sont immédiatement convertis dans la devise de base aux taux de change actuels. Après conversion, ton solde reste stable dans la devise de base, mais les retraits futurs peuvent de nouveau impliquer des conversions de devises.", "earn.base-currency-popup.title": "Devise de <PERSON>", "earn.card-recharge.disabled.list-item.title": "Recharge auto désactivée", "earn.card-recharge.enabled.list-item.title": "Recharge auto activée", "earn.choose_wallet_to_deposit.title": "<PERSON><PERSON><PERSON><PERSON> depuis", "earn.config.currency.eth": "Gagner de l'Ethereum", "earn.config.currency.on_chain_address_subtitle": "<PERSON><PERSON><PERSON> on-chain", "earn.config.currency.us_dollars": "Configurer les virements bancaires", "earn.configured_widget.current_apy.title": "APY actuel", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} annuels", "earn.confirm.currency.cta": "<PERSON><PERSON><PERSON>r", "earn.currency.eth": "Gagner de l'Ethereum", "earn.deploy.status.title": "<PERSON><PERSON><PERSON> un compte Earn", "earn.deploy.status.title_with_taker": "<PERSON><PERSON><PERSON> un compte {title} Earn", "earn.deposit": "<PERSON><PERSON><PERSON>r", "earn.deposit.amount_to_deposit": "<PERSON><PERSON> déposer", "earn.deposit.deposit": "<PERSON><PERSON><PERSON>r", "earn.deposit.enter_amount": "<PERSON><PERSON> un montant", "earn.deposit.no_routes_found": "Aucun itinéraire trouvé", "earn.deposit.not_enough_balance": "Solde insuffisant", "earn.deposit.select-currency.title": "Sé<PERSON><PERSON>ner le jeton à déposer", "earn.deposit.select_account.title": "Sélectionner un compte Earn", "earn.desposit_form.title": "<PERSON>é<PERSON>r dans Earn", "earn.earn_deposit.status.title": "Dépôt sur Earn", "earn.earn_deposit.trx.title": "Dépôt sur Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "Retire tes fonds à tout moment", "earn.earn_withdraw.status.title": "Retrait du compte Earn", "earn.earn_withdraw.trx.title.approval": "Approuver le retrait", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Retirer en {asset}", "earn.earn_withdraw.trx.title.withdrawal": "Retrait de Earn", "earn.recharge.cta": "Enregistrer", "earn.recharge.earn_not_configured.enable_some_account.error": "Activer un compte", "earn.recharge.earn_not_configured.enter_amount.error": "<PERSON><PERSON> un montant", "earn.recharge.select_taker.header": "Recharger la carte dans l'ordre depuis", "earn.recharge_card_tag.on": "activée", "earn.recharge_card_tag.recharge": "Recharge", "earn.recharge_card_tag.recharge_not_configured": "Recharge auto", "earn.recharge_card_tag.recharge_off": "Re<PERSON><PERSON>", "earn.recharge_card_tag.recharged": "<PERSON><PERSON><PERSON><PERSON>", "earn.recharge_card_tag.recharging": "Rechargement en cours", "earn.recharge_configured.disable.trx.title": "Désactiver la recharge auto", "earn.recharge_configured.trx.disclaimer": "Quand tu utilises ta carte, une enchère Cowswap est créée pour acheter le même montant que ton paiement en utilisant tes actifs Earn. Ce processus d'enchère te donne généralement le meilleur taux du marché, mais sache que le taux on-chain peut différer des taux de change réels.", "earn.recharge_configured.trx.subtitle": "Après chaque paiement, des fonds seront automatiquement ajoutés de tes comptes Earn pour maintenir le solde de ta carte à {value}", "earn.recharge_configured.trx.title": "Définir la recharge auto à {value}", "earn.recharge_configured.updated.trx.title": "Enregistrer les paramètres de recharge", "earn.risk-banner.subtitle": "Ce produit est auto-dépositaire, sans protection réglementaire contre les pertes.", "earn.risk-banner.title": "Comprendre les risques", "earn.set_recharge.status.title": "Configurer l'auto-recharge", "earn.setup_reacharge.input.disable.label": "Désactiver", "earn.setup_reacharge.input.label": "Solde cible de la carte", "earn.setup_reacharge_form.title": "La recharge auto maintient ta {br}carte au même solde", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Conserve du wstETH (ETH staké) sur Gnosis Chain et prête via Lido.", "earn.taker-bulletlist.point_1": "<PERSON><PERSON><PERSON> {apy<PERSON><PERSON><PERSON>} par an. Les rendements varient avec le marché.", "earn.taker-bulletlist.point_3": "Zeal ne facture aucun frais.", "earn.taker-historical-returns": "Rendements historiques", "earn.taker-historical-returns.chf": "Croissance du CHF en USD", "earn.taker-investment-tile.apy.perYear": "par an", "earn.takerAPY": "{takerApy} d'APY", "earn.takerListItem.apy": "{takerApy} d'APY", "earn.takerListItem.deposit": "<PERSON><PERSON><PERSON>r", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Rendement via Aave sur Gnosis Chain", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Rendement avec Frankencoin sur Gnosis Chain", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Rendement via Lido sur Gnosis Chain", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Rendement via Maker sur Gnosis Chain", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "Rendement ETH", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "Rendement ETH", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "Bons du Trésor US (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Bien que ce soit extrêmement rare, c'est théoriquement possible. Tes fonds sont protégés par une gestion des risques stricte et une surgarantisation élevée. Le pire scénario réaliste impliquerait des conditions de marché sans précédent, comme la perte simultanée de l'ancrage de plusieurs stablecoins, ce qui ne s'est jamais produit.", "earn.usd.can-I-lose-my-principal-popup.title": "Puis-je réellement perdre mon capital et dans quelles circonstances ?", "earn.usd.ftx-difference-popup.text": "Sky est fondamentalement différent. Contrairement à FTX, <PERSON><PERSON><PERSON>, Block<PERSON>i ou Luna, qui reposaient sur une conservation centralisée, une gestion d'actifs opaque et des positions à effet de levier risquées, Sky USD utilise des smart contracts décentralisés, transparents et audités, et maintient une transparence totale on-chain. Tu conserves le contrôle total et privé de tes fonds, réduisant considérablement les risques de contrepartie liés aux défaillances des systèmes centralisés.", "earn.usd.ftx-difference-popup.title": "En quoi est-ce différent de FTX, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ou <PERSON> ?", "earn.usd.high-returns-popup.text": "Sky USD génère des rendements principalement via des protocoles de finance décentralisée (DeFi), qui automatisent les prêts peer-to-peer et l'apport de liquidité, éliminant les frais généraux et les intermédiaires bancaires traditionnels. Cette efficacité, combinée à des contrôles de risque robustes, permet des rendements bien plus élevés que ceux des banques traditionnelles.", "earn.usd.high-returns-popup.title": "Comment les rendements peuvent-ils être si élevés, surtout par rapport aux banques traditionnelles ?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD est entièrement garanti et surcollatéralisé par une combinaison d'actifs numériques détenus dans des smart contracts sécurisés et d'actifs du monde réel comme les bons du Trésor américain. Les réserves peuvent être auditées en temps réel on-chain, même depuis Zeal, offrant transparence et sécurité. Dans le cas improbable où Zeal cesserait ses activités, tes actifs resteraient sécurisés on-chain, sous ton contrôle total et accessibles via d'autres portefeuilles compatibles.", "earn.usd.how-is-sky-backed-popup.title": "Comment Sky USD est-il garanti et qu'advient-il de mon argent si Zeal fait faillite ?", "earn.usd.insurance-popup.text": "Les fonds Sky USD ne sont pas assurés par le FDIC ni couverts par des garanties gouvernementales traditionnelles, car il s'agit d'un compte basé sur des actifs numériques et non d'un compte bancaire classique. À la place, Sky gère l'atténuation des risques via des smart contracts audités et des protocoles DeFi rigoureusement sélectionnés, garantissant que les actifs restent sécurisés et transparents.", "earn.usd.insurance-popup.title": "Mes fonds sont-ils assurés ou garantis par une entité (comme le FDIC ou similaire) ?", "earn.usd.lending-operations-popup.text": "Sky USD génère du rendement en prêtant des stablecoins sur des marchés de prêt décentralisés comme Morpho et Spark. Tes stablecoins sont prêtés à des emprunteurs qui déposent une garantie (comme de l'ETH ou du BTC) d'une valeur bien supérieure à celle de leur prêt. Cette approche, appelée surcollatéralisation, garantit qu'il y a toujours assez de garantie pour couvrir les prêts, ce qui réduit considérablement le risque. Les intérêts perçus et les frais de liquidation occasionnels payés par les emprunteurs assurent des rendements fiables, transparents et sécurisés.", "earn.usd.lending-operations-popup.title": "Opérations de prêt", "earn.usd.market-making-operations-popup.text": "Sky USD génère un rendement additionnel en participant à des plateformes d'échange décentralisées (AMM) comme Curve ou Uniswap. En fournissant de la liquidité (en plaçant tes stablecoins dans des pools qui facilitent le trading de cryptos), Sky USD perçoit les frais générés par les échanges. Ces pools de liquidité sont soigneusement sélectionnés pour minimiser la volatilité, utilisant principalement des paires stablecoin contre stablecoin pour réduire considérablement les risques comme la perte impermanente, tout en gardant tes actifs en sécurité et accessibles.", "earn.usd.market-making-operations-popup.title": "Opérations de tenue de marché", "earn.usd.treasury-operations-popup.text": "Sky USD génère un rendement stable et constant grâce à des investissements de trésorerie stratégiques. Une partie de tes dépôts de stablecoins est allouée à des actifs du monde réel sûrs et à faible risque, principalement des obligations d'État à court terme et des instruments de crédit très sécurisés. Cette approche, similaire à celle des banques traditionnelles, assure un rendement prévisible et fiable. Tes actifs restent sécurisés, liquides et gérés de manière transparente.", "earn.usd.treasury-operations-popup.title": "Opérations de trésorerie", "earn.view_earn.card_rechard_off": "Désactivé", "earn.view_earn.card_rechard_on": "Activé", "earn.view_earn.card_recharge": "Recharge de la carte", "earn.view_earn.total_balance_label": "<PERSON><PERSON> de {percentage} par an", "earn.view_earn.total_earnings_label": "<PERSON><PERSON><PERSON>", "earn.withdraw": "<PERSON><PERSON><PERSON>", "earn.withdraw.amount_to_withdraw": "<PERSON><PERSON>", "earn.withdraw.enter_amount": "<PERSON><PERSON> le montant", "earn.withdraw.loading": "Chargement", "earn.withdraw.no_routes_found": "Aucun itinéraire trouvé", "earn.withdraw.not_enough_balance": "Solde insuffisant", "earn.withdraw.select-currency.title": "Sé<PERSON><PERSON><PERSON> le jeton", "earn.withdraw.select_to_token": "Sé<PERSON><PERSON><PERSON> le jeton", "earn.withdraw.withdraw": "<PERSON><PERSON><PERSON>", "earn.withdraw_form.title": "<PERSON><PERSON><PERSON>", "earnings-view.earnings": "<PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.add-wallet": "Ajouter un titulaire", "edit-account-owners.add-owner.add_wallet": "Ajouter un portefeuille", "edit-account-owners.add-owner.title": "Ajouter un titulaire", "edit-account-owners.card-owners": "Titulaires de la carte", "edit-account-owners.external-wallet": "Portefeuille externe", "editBankRecipient.title": "Modifier le bénéficiaire", "editNetwork.addCustomRPC": "Ajouter un nœud RPC personnalisé", "editNetwork.cannot_verify.subtitle": "Le nœud RPC personnalisé ne répond pas. Vérifie l'URL et réessaye.", "editNetwork.cannot_verify.title": "Impossible de vérifier le nœud RPC", "editNetwork.cannot_verify.try_again": "<PERSON><PERSON><PERSON><PERSON>", "editNetwork.customRPCNode": "Nœud RPC personnalisé", "editNetwork.defaultRPC": "RPC par défaut", "editNetwork.networkRPC": "RPC du réseau", "editNetwork.rpc_url.cannot_be_empty": "Requis", "editNetwork.rpc_url.not_a_valid_https_url": "Doit être une URL HTTP(S) valide", "editNetwork.safetyWarning.subtitle": "Zeal ne peut garantir la confidentialité, la fiabilité et la sécurité des RPC personnalisés. Es-tu sûr de vouloir utiliser un nœud RPC personnalisé ?", "editNetwork.safetyWarning.title": "Les RPC personnalisés peuvent être risqués", "editNetwork.zealRPCNode": "Nœud RPC Zeal", "editNetworkRpc.headerTitle": "Nœud RPC personnalisé", "editNetworkRpc.rpcNodeUrl": "URL du nœud RPC", "editing-locked.modal.description": "Contrairement aux transactions d'approbation, les permis ne te permettent pas de modifier la limite de dépense ou la date d'expiration. Assure-toi de faire confiance à l'application avant de soumettre un permis.", "editing-locked.modal.title": "Modification verrouillée", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Activation de la recharge", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Recharge activée", "enterCardnumber": "<PERSON><PERSON> le numéro", "error.connectivity_error.subtitle": "Vérifie ta connexion Internet et réessaie.", "error.connectivity_error.title": "Pas de connexion Internet", "error.decrypt_incorrect_password.title": "Mot de passe incorrect", "error.encrypted_object_invalid_format.title": "<PERSON><PERSON><PERSON> co<PERSON>", "error.failed_to_fetch_google_auth_token.title": "Nous n'avons pas pu obtenir l'accès", "error.list.item.cta.action": "<PERSON><PERSON><PERSON><PERSON>", "error.trezor_action_cancelled.title": "Transaction rejetée", "error.trezor_device_used_elsewhere.title": "L'appareil est utilisé dans une autre session", "error.trezor_method_cancelled.title": "Impossible de synchroniser le Trezor", "error.trezor_permissions_not_granted.title": "Impossible de synchroniser le Trezor", "error.trezor_pin_cancelled.title": "Impossible de synchroniser le Trezor", "error.trezor_popup_closed.title": "Impossible de synchroniser le Trezor", "error.unblock_account_number_and_sort_code_mismatch": "Le numéro de compte et le code de tri ne correspondent pas", "error.unblock_can_not_change_details_after_kyc": "Détails non modifiables après KYC", "error.unblock_hard_kyc_failure": "État KYC inattendu", "error.unblock_invalid_faster_payment_configuration.title": "Cette banque ne prend pas en charge les Faster Payments", "error.unblock_invalid_iban": "IBAN non valide", "error.unblock_session_expired.title": "Session Unblock expirée", "error.unblock_user_with_address_already_exists.title": "Compte d<PERSON><PERSON>à configuré pour cette adresse", "error.unblock_user_with_such_email_already_exists.title": "Un utilisateur avec cet e-mail existe déjà", "error.unknown_error.error_message": "Message d'erreur : ", "error.unknown_error.subtitle": "Désolé ! Si tu as besoin d'une aide urgente, contacte le support et partage les détails ci-dessous.", "error.unknown_error.title": "<PERSON><PERSON>ur système", "eth-cost-warning-modal.subtitle": "Les Smart Wallets fonctionnent sur Ethereum, mais les frais y sont très élevés. Nous te recommandons FORTEMENT d'utiliser d'autres réseaux.", "eth-cost-warning-modal.title": "Évite <PERSON> : les frais réseau sont élevés", "exchange.form.button.chain_unsupported": "R<PERSON>eau non pris en charge", "exchange.form.button.refreshing": "Actualisation", "exchange.form.error.asset_not_supported.button": "Choisir un autre actif", "exchange.form.error.asset_not_supported.description": "La passerelle ne prend pas en charge cet actif.", "exchange.form.error.asset_not_supported.title": "Actif non pris en charge", "exchange.form.error.bridge_quote_timeout.button": "Choisir un autre actif", "exchange.form.error.bridge_quote_timeout.description": "Essaie une autre paire de jetons", "exchange.form.error.bridge_quote_timeout.title": "Aucun échange trouvé", "exchange.form.error.different_receiver_not_supported.button": "Retirer le destinataire alternatif", "exchange.form.error.different_receiver_not_supported.description": "Cet échange ne permet pas d'envoyer à une autre adresse.", "exchange.form.error.different_receiver_not_supported.title": "L'adresse d'envoi et de réception doit être la même", "exchange.form.error.insufficient_input_amount.button": "Augmenter le montant", "exchange.form.error.insufficient_liquidity.button": "<PERSON><PERSON><PERSON><PERSON> le montant", "exchange.form.error.insufficient_liquidity.description": "La passerelle n'a pas assez d'actifs. Essaie un montant plus petit.", "exchange.form.error.insufficient_liquidity.title": "<PERSON>ant trop élevé", "exchange.form.error.max_amount_exceeded.button": "<PERSON><PERSON><PERSON><PERSON> le montant", "exchange.form.error.max_amount_exceeded.description": "Le montant maximum a été dépassé.", "exchange.form.error.max_amount_exceeded.title": "<PERSON>ant trop élevé", "exchange.form.error.min_amount_not_met.button": "Augmenter le montant", "exchange.form.error.min_amount_not_met.description": "Le montant d'échange minimum pour ce jeton n'est pas atteint.", "exchange.form.error.min_amount_not_met.description_with_amount": "Le montant d'échange minimum est de {amount}.", "exchange.form.error.min_amount_not_met.title": "Montant trop faible", "exchange.form.error.min_amount_not_met.title_increase": "Augmenter le montant", "exchange.form.error.no_routes_found.button": "Choisir un autre actif", "exchange.form.error.no_routes_found.description": "Aucun itinéraire d'échange n'est disponible pour cette combinaison jeton/réseau.", "exchange.form.error.no_routes_found.title": "Aucun échange disponible", "exchange.form.error.not_enough_balance.button": "<PERSON><PERSON><PERSON><PERSON> le montant", "exchange.form.error.not_enough_balance.description": "Tu n'as pas assez de cet actif pour la transaction.", "exchange.form.error.not_enough_balance.title": "Solde insuffisant", "exchange.form.error.slippage_passed_is_too_low.button": "Augmenter le slippage", "exchange.form.error.slippage_passed_is_too_low.description": "Le glissement autorisé est trop faible pour cet actif.", "exchange.form.error.slippage_passed_is_too_low.title": "Glissement trop faible", "exchange.form.error.socket_internal_error.button": "Réessayer plus tard", "exchange.form.error.socket_internal_error.description": "Le partenaire de passerelle rencontre des problèmes. Réessaye plus tard.", "exchange.form.error.socket_internal_error.title": "Erreur chez le partenaire de passerelle", "exchange.form.error.stargatev2_requires_fee_in_native": "Ajouter {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Ajoute {amount} pour finaliser la transaction", "exchange.form.error.stargatev2_requires_fee_in_native.title": "Besoin de plus de {symbol}", "expiration-info.modal.description": "La date d'expiration détermine combien de temps une application peut utiliser tes jetons. Passé ce d<PERSON>, elle perd l'accès jusqu'à nouvel ordre de ta part. Pour plus de sécurité, choisis une date d'expiration courte.", "expiration-info.modal.title": "Qu'est-ce qu'une date d'expiration ?", "expiration-time.high.modal.text": "Les délais d'expiration doivent être courts. Un délai long est risqué et donne plus de chances aux escrocs de détourner tes jetons.", "expiration-time.high.modal.title": "<PERSON><PERSON><PERSON> d'expiration long", "failed.transaction.content": "La transaction va probablement échouer", "fee.unknown": "Inconnu", "feedback-request.leave-message": "<PERSON><PERSON> un message", "feedback-request.not-now": "Plus tard", "feedback-request.title": "Merci ! Comment pouvons-nous améliorer Zeal ?", "float.input.period": "Séparateur décimal", "gnosis-activate-card.info-popup.subtitle": "Pour ton premier paiement, insère la carte et saisis le PIN. Le sans contact sera activé après.", "gnosis-activate-card.info-popup.title": "Premier paiement : puce et PIN", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "Saisis ton numéro de carte pour l'activer.", "gnosis-activate-card.title": "Numéro de carte", "gnosis-pay-re-kyc-widget.btn-text": "Vérifier", "gnosis-pay-re-kyc-widget.title.not-started": "Vérifier ton identité", "gnosis-pay.login.cta": "Connecter ton compte", "gnosis-pay.login.title": "Tu as déjà un compte Gnosis Pay", "gnosis-signup.confirm.subtitle": "Recherche l'e-mail de Gnosis Pay, il est peut-être dans tes spams.", "gnosis-signup.confirm.title": "E-mail de vérification non reçu ?", "gnosis-signup.continue": "<PERSON><PERSON><PERSON>", "gnosis-signup.dont_link_accounts": "Ne pas lier les comptes", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "Saisis <EMAIL>", "gnosis-signup.enter-email.title": "Saisir l'e-mail", "gnosis-signup.title": "J'ai lu et j'accepte les <linkGnosisTNC>Conditions</linkGnosisTNC> <monovateTerms>Conditions du titulaire de la carte</monovateTerms> et les <linkMonerium>CGU Monerium</linkMonerium>.", "gnosis-signup.verify-email.title": "Vérifier l'e-mail", "gnosis.confirm.subtitle": "Code non reçu ? Vérifie ton numéro.", "gnosis.confirm.title": "Code envoyé au {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "Vérifier", "gnosisPayAccountStatus.success.title": "Carte importée", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay n'est pas encore disponible dans ton pays", "gnosisPayNoActiveCardsFound.title": "Aucune carte active", "gnosis_pay_card_delay_relay_not_empty_error.title": "Ta transaction n'a pas pu être traitée pour le moment. Réessaie plus tard.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "<PERSON><PERSON> impossible", "gnosiskyc.modal.approved.activate-free-card": "Activer la carte gratuite", "gnosiskyc.modal.approved.button-text": "Dépôt bancaire", "gnosiskyc.modal.approved.title": "Ton compte personnel a été créé", "gnosiskyc.modal.failed.close": "<PERSON><PERSON><PERSON>", "gnosiskyc.modal.failed.title": "<PERSON><PERSON><PERSON><PERSON>, Gnosis Pay ne peut créer ton compte", "gnosiskyc.modal.in-progress.title": "Vérification d'ID : jusqu'à 24h. Patience.", "goToSettingsPopup.settings": "Réglages", "goToSettingsPopup.title": "Active les notifications dans les réglages de ton appareil", "google_file.error.failed_to_fetch_auth_token.button_title": "<PERSON><PERSON><PERSON><PERSON>", "google_file.error.failed_to_fetch_auth_token.subtitle": "Pour nous permettre d'utiliser ton fichier de récupération, autorise l'accès à ton cloud personnel.", "google_file.error.failed_to_fetch_auth_token.title": "Nous n'avons pas pu obtenir l'accès", "hidden_tokens.widget.emptyState": "<PERSON><PERSON><PERSON> jeton masqué", "how_to_connect_to_metamask.got_it": "OK, compris", "how_to_connect_to_metamask.story.subtitle": "Bascule facilement entre Zeal et d'autres portefeuilles à tout moment.", "how_to_connect_to_metamask.story.title": "Zeal fonctionne avec d'autres portefeuilles", "how_to_connect_to_metamask.why_switch": "Pourquoi basculer entre Zeal et d'autres portefeuilles ?", "how_to_connect_to_metamask.why_switch.description": "Quel que soit le portefeuille que tu choisis, les contrôles de sécurité Zeal te protègent toujours des sites et transactions malveillants.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "Nous savons qu'il est difficile de sauter le pas et d'utiliser un nouveau portefeuille. Nous avons donc facilité l'utilisation de Zeal avec ton portefeuille actuel. Tu peux changer à tout moment.", "import-bank-transfer-owner.banner.title": "Le portefeuille de virement a changé. Importez-le pour continuer.", "import-bank-transfer-owner.title": "Importer le portefeuille pour utiliser les virements sur cet appareil", "import_gnosispay_wallet.add-another-card-owner.footnote": "Importe les clés du portefeuille de la carte.", "import_gnosispay_wallet.primaryText": "Importer le portefeuille Gnosis Pay", "injected-wallet": "Portefeuille de navigateur", "intercom.getHelp": "<PERSON><PERSON><PERSON><PERSON> <PERSON> l'aide", "invalid_iban.got_it": "<PERSON><PERSON><PERSON>", "invalid_iban.subtitle": "L'IBAN saisi n'est pas valide. Vérifie que les informations sont correctes et essaie à nouveau.", "invalid_iban.title": "IBAN invalide", "keypad-0": "Touche du clavier numérique 0", "keypad-1": "Touche du clavier numérique 1", "keypad-2": "Touche du clavier numérique 2", "keypad-3": "Touche du clavier numérique 3", "keypad-4": "Touche du clavier numérique 4", "keypad-5": "Touche du clavier numérique 5", "keypad-6": "Touche du clavier numérique 6", "keypad-7": "Touche du clavier numérique 7", "keypad-8": "Touche du clavier numérique 8", "keypad-9": "Touche du clavier numérique 9", "keypad.biometric-button": "Bouton biométrique du clavier", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "Phrase secrète sécurisée 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "Voir la phrase", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Conserve une copie hors ligne de ta phrase secrète pour pouvoir récupérer tes actifs plus tard", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "N'essaie pas de deviner le mot", "keystore.write_secret_phrase.before_you_begin.first_point": "Je comprends que toute personne possédant ma phrase secrète peut virer mes actifs", "keystore.write_secret_phrase.before_you_begin.second_point": "Il est de ma responsabilité de garder ma phrase secrète en lieu sûr", "keystore.write_secret_phrase.before_you_begin.subtitle": "Veuillez lire et accepter les points suivants :", "keystore.write_secret_phrase.before_you_begin.third_point": "Je suis dans un lieu privé, sans personne ni caméra autour de moi", "keystore.write_secret_phrase.before_you_begin.title": "Avant de commencer", "keystore.write_secret_phrase.secret_phrase_test.title": "Quel est le mot {count} de ta phrase secrète ?", "keystore.write_secret_phrase.test_ps.lets_do_it": "C'est parti", "keystore.write_secret_phrase.test_ps.subtitle": "Tu auras besoin de ta phrase secrète pour restaurer ton compte sur cet appareil ou d'autres. Vérifions qu'elle est bien écrite.", "keystore.write_secret_phrase.test_ps.subtitle2": "Nous te demanderons {count} mots de ta phrase.", "keystore.write_secret_phrase.test_ps.title": "Tester la récupération du compte", "kyc.modal.approved.button-text": "Faire un virement", "kyc.modal.approved.subtitle": "Ta vérification est terminée, tu peux désormais faire des virements illimités.", "kyc.modal.approved.title": "Virements bancaires débloqués", "kyc.modal.continue-with-partner.button-text": "<PERSON><PERSON><PERSON>", "kyc.modal.continue-with-partner.subtitle": "Nous allons te rediriger vers notre partenaire pour recueillir tes documents et finaliser ta demande de vérification.", "kyc.modal.continue-with-partner.title": "Continuer avec notre partenaire", "kyc.modal.failed.unblock.subtitle": "Unblock n'a pas validé ton identité et ne peut te fournir de service de virement.", "kyc.modal.failed.unblock.title": "<PERSON><PERSON><PERSON> Unblock non approuvée", "kyc.modal.paused.button-text": "Mettre à jour", "kyc.modal.paused.subtitle": "Tes informations semblent incorrectes. Réessaye en les vérifiant bien.", "kyc.modal.paused.title": "Tes informations semblent incorrectes", "kyc.modal.pending.button-text": "<PERSON><PERSON><PERSON>", "kyc.modal.pending.subtitle": "La vérification prend généralement moins de 10 minutes, mais peut parfois être plus longue.", "kyc.modal.pending.title": "Nous te tiendrons au courant", "kyc.modal.required.cta": "Commencer la vérification", "kyc.modal.required.subtitle": "Limite de transactions atteinte. Vérifie ton identité pour continuer.", "kyc.modal.required.title": "Vérification d'identité requise", "kyc.submitted": "<PERSON><PERSON><PERSON> envoy<PERSON>", "kyc.submitted_short": "<PERSON><PERSON><PERSON>", "kyc_status.completed_status": "<PERSON><PERSON><PERSON><PERSON>", "kyc_status.failed_status": "Échec", "kyc_status.paused_status": "En révision", "kyc_status.subtitle": "Virements bancaires", "kyc_status.subtitle.wrong_details": "Informations incorrectes", "kyc_status.subtitle_in_progress": "En cours", "kyc_status.title": "Vérification d'identité", "label.close": "<PERSON><PERSON><PERSON>", "label.saving": "En cours...", "labels.this-month": "Ce mois-ci", "labels.today": "<PERSON><PERSON><PERSON>'hui", "labels.yesterday": "<PERSON>er", "language.selector.title": "<PERSON><PERSON>", "ledger.account_loaded.imported": "Importé", "ledger.add.success.title": "Ledger connecté avec succès 🎉", "ledger.connect.cta": "Synchroniser Ledger", "ledger.connect.step1": "Connecte le Ledger à ton appareil", "ledger.connect.step2": "Ouvre l'application Ethereum sur le Ledger", "ledger.connect.step3": "<PERSON><PERSON>s synchronise ton Ledger 👇", "ledger.connect.subtitle": "Suis ces étapes pour importer tes portefeuilles Ledger sur Zeal", "ledger.connect.title": "Connect<PERSON> le Ledger à Zeal", "ledger.error.ledger_is_locked.subtitle": "Déverrouille ton Ledger et ouvre l'application Ethereum", "ledger.error.ledger_is_locked.title": "<PERSON><PERSON>", "ledger.error.ledger_not_connected.action": "Synchroniser le Ledger", "ledger.error.ledger_not_connected.subtitle": "Connecte ton portefeuille matériel à ton appareil et ouvre l'application Ethereum", "ledger.error.ledger_not_connected.title": "Ledger non connecté", "ledger.error.ledger_running_non_eth_app.title": "Application Ethereum non ouverte", "ledger.error.user_trx_denied_by_user.action": "<PERSON><PERSON><PERSON>", "ledger.error.user_trx_denied_by_user.subtitle": "Tu as rejeté la transaction sur ton portefeuille matériel", "ledger.error.user_trx_denied_by_user.title": "Transaction rejetée", "ledger.hd_path.bip44.subtitle": "p. ex. <PERSON>, Trezor", "ledger.hd_path.bip44.title": "Standard BIP44", "ledger.hd_path.ledger_live.subtitle": "<PERSON><PERSON> <PERSON><PERSON>", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Legacy", "ledger.hd_path.phantom.subtitle": "p. ex. Phantom", "ledger.select.hd_path.subtitle": "Les chemins HD permettent aux portefeuilles matériels de trier leurs comptes, un peu comme l'index d'un livre.", "ledger.select.hd_path.title": "Sélectionner le chemin HD", "ledger.select_account.import_wallets_count": "{count,plural,=0{Aucun compte sélectionné} one{Importer le compte} other{Importer {count} comptes}}", "ledger.select_account.path_settings": "Paramètres de chemin", "ledger.select_account.subtitle": "Tu ne vois pas les portefeuilles attendus ? Essaie de changer les paramètres de chemin", "ledger.select_account.subtitle.group_header": "Portefeuilles", "ledger.select_account.title": "Importer des portefeuilles Ledger", "legend.lending-operations": "Opérations de prêt", "legend.market_making-operations": "Opérations de tenue de marché", "legend.treasury-operations": "Opérations de trésorerie", "link-existing-monerium-account-sign.button": "Associer Zeal", "link-existing-monerium-account-sign.subtitle": "<PERSON> as déjà un compte Monerium.", "link-existing-monerium-account-sign.title": "Associe Zeal à ton compte Monerium existant", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Tu as déjà un compte Monerium. Rends-toi sur l'app Monerium pour finaliser la configuration.", "link-existing-monerium-account.title": "Va sur Monerium pour associer ton compte", "loading.pin": "Chargement du code PIN...", "lockScreen.passwordIncorrectMessage": "Mot de passe incorrect", "lockScreen.passwordRequiredMessage": "Mot de passe requis", "lockScreen.unlock.header": "Déverrouiller", "lockScreen.unlock.subheader": "Utilise ton mot de passe pour déverrouiller Zeal", "mainTabs.activity.label": "Activité", "mainTabs.browse.label": "Explorer", "mainTabs.browse.title": "Explorer", "mainTabs.card.label": "<PERSON><PERSON>", "mainTabs.portfolio.label": "Portefeuille", "mainTabs.rewards.label": "Récompenses", "makeSpendable.cta": "Rendre disponible", "makeSpendable.holdAsCash": "Garder en l'état", "makeSpendable.shortText": "<PERSON><PERSON><PERSON> {apy} par an", "makeSpendable.title": "{amount} reçus", "merchantCategory.agriculture": "Agriculture", "merchantCategory.alcohol": "Alcool", "merchantCategory.antiques": "Antiquités", "merchantCategory.appliances": "Appareils ménagers", "merchantCategory.artGalleries": "Galeries d'art", "merchantCategory.autoRepair": "Réparation auto", "merchantCategory.autoRepairService": "Service de réparation auto", "merchantCategory.beautyFitnessSpas": "Beauté, fitness et spas", "merchantCategory.beautyPersonalCare": "Beauté et soins personnels", "merchantCategory.billiard": "<PERSON><PERSON>", "merchantCategory.books": "Livres", "merchantCategory.bowling": "Bowling", "merchantCategory.businessProfessionalServices": "Services professionnels", "merchantCategory.carRental": "Location de voiture", "merchantCategory.carWash": "Lavage auto", "merchantCategory.cars": "Voitures", "merchantCategory.casino": "Casino", "merchantCategory.casinoGambling": "Casino et jeux d'argent", "merchantCategory.cellular": "Téléphonie mobile", "merchantCategory.charity": "Don<PERSON> caritati<PERSON>", "merchantCategory.childcare": "Garde d'enfants", "merchantCategory.cigarette": "Cigarettes", "merchantCategory.cinema": "Cinéma", "merchantCategory.cinemaEvents": "Cinéma et événements", "merchantCategory.cleaning": "Nettoyage", "merchantCategory.cleaningMaintenance": "Nettoyage et entretien", "merchantCategory.clothes": "Vêtements", "merchantCategory.clothingServices": "Services vestimentaires", "merchantCategory.communicationServices": "Services de communication", "merchantCategory.construction": "Construction", "merchantCategory.cosmetics": "Cosmétiques", "merchantCategory.craftsArtSupplies": "Artisanat et art", "merchantCategory.datingServices": "Sites de rencontre", "merchantCategory.delivery": "<PERSON><PERSON><PERSON>", "merchantCategory.dentist": "<PERSON><PERSON><PERSON>", "merchantCategory.departmentStores": "Grands magasins", "merchantCategory.directMarketingSubscription": "Marketing direct et abonnement", "merchantCategory.discountStores": "Magasins discount", "merchantCategory.drugs": "Pharmacie", "merchantCategory.dutyFree": "Duty free", "merchantCategory.education": "Éducation", "merchantCategory.electricity": "Électricité", "merchantCategory.electronics": "Électronique", "merchantCategory.emergencyServices": "Services d'urgence", "merchantCategory.equipmentRental": "Location de matériel", "merchantCategory.evCharging": "Recharge de VE", "merchantCategory.financialInstitutions": "Institutions financières", "merchantCategory.financialProfessionalServices": "Services financiers et professionnels", "merchantCategory.finesPenalties": "Amendes et pénalités", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "Vols", "merchantCategory.flowers": "Fleurs", "merchantCategory.flowersGarden": "Fleurs et jardin", "merchantCategory.food": "Nourriture", "merchantCategory.freight": "Fret", "merchantCategory.fuel": "Carburant", "merchantCategory.funeralServices": "Services funéraires", "merchantCategory.furniture": "<PERSON><PERSON><PERSON>", "merchantCategory.games": "<PERSON><PERSON>", "merchantCategory.gas": "Essence", "merchantCategory.generalMerchandiseRetail": "Produits divers et détail", "merchantCategory.gifts": "<PERSON><PERSON>", "merchantCategory.government": "Gouvernement", "merchantCategory.governmentServices": "Services gouvernementaux", "merchantCategory.hardware": "Quincaillerie", "merchantCategory.healthMedicine": "Santé et médecine", "merchantCategory.homeImprovement": "Rénovation", "merchantCategory.homeServices": "Services à domicile", "merchantCategory.hotel": "Hôtel", "merchantCategory.housing": "Logement", "merchantCategory.insurance": "Assurance", "merchantCategory.internet": "Internet", "merchantCategory.kids": "<PERSON><PERSON><PERSON>", "merchantCategory.laundry": "Blanchisserie", "merchantCategory.laundryCleaningServices": "Blanchisserie et nettoyage", "merchantCategory.legalGovernmentFees": "Frais juridiques et gouvernementaux", "merchantCategory.luxuries": "Produits de luxe", "merchantCategory.luxuriesCollectibles": "Luxe et objets de collection", "merchantCategory.magazines": "Magazines", "merchantCategory.magazinesNews": "Magazines et actualités", "merchantCategory.marketplaces": "Marketplaces", "merchantCategory.media": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.medicine": "Médecine", "merchantCategory.mobileHomes": "Mobil-homes", "merchantCategory.moneyTransferCrypto": "Transfert d'argent et crypto", "merchantCategory.musicRelated": "Musique", "merchantCategory.musicalInstruments": "Instruments de musique", "merchantCategory.optics": "Optique", "merchantCategory.organizationsClubs": "Organisations et clubs", "merchantCategory.other": "<PERSON><PERSON>", "merchantCategory.parking": "Parking", "merchantCategory.pawnShops": "Prêteurs sur gages", "merchantCategory.pets": "<PERSON><PERSON><PERSON>", "merchantCategory.photoServicesSupplies": "Services et fournitures photo", "merchantCategory.postalServices": "Services postaux", "merchantCategory.professionalServicesOther": "Services professionnels (autres)", "merchantCategory.publicTransport": "Transports en commun", "merchantCategory.purchases": "Achats", "merchantCategory.purchasesMiscServices": "Achats et services divers", "merchantCategory.recreationServices": "Services de loisirs", "merchantCategory.religiousGoods": "Articles religieux", "merchantCategory.secondhandRetail": "Vente d'occasion", "merchantCategory.shoeHatRepair": "Réparation chaussures et chapeaux", "merchantCategory.shoeRepair": "Cordonnerie", "merchantCategory.softwareApps": "Logiciels et applications", "merchantCategory.specializedRepairs": "Réparations spécialisées", "merchantCategory.sport": "Sport", "merchantCategory.sportingGoods": "Articles de sport", "merchantCategory.sportingGoodsRecreation": "Articles de sport et loisirs", "merchantCategory.sportsClubsFields": "Clubs et terrains de sport", "merchantCategory.stationaryPrinting": "Papeterie et imprimerie", "merchantCategory.stationery": "Papeterie", "merchantCategory.storage": "Stockage", "merchantCategory.taxes": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.taxi": "Taxi", "merchantCategory.telecomEquipment": "Équipement télécom", "merchantCategory.telephony": "Téléphonie", "merchantCategory.tobacco": "Tabac", "merchantCategory.tollRoad": "<PERSON><PERSON><PERSON>", "merchantCategory.tourismAttractionsAmusement": "Tourisme, attractions et loisirs", "merchantCategory.towing": "Remorquage", "merchantCategory.toys": "<PERSON><PERSON><PERSON>", "merchantCategory.toysHobbies": "<PERSON><PERSON><PERSON> et loisirs", "merchantCategory.trafficFine": "Amende de circulation", "merchantCategory.train": "Train", "merchantCategory.travelAgency": "Agence de voyages", "merchantCategory.tv": "Télévision", "merchantCategory.tvRadioStreaming": "TV, radio et streaming", "merchantCategory.utilities": "Services publics", "merchantCategory.waterTransport": "Transport fluvial", "merchantCategory.wholesaleClubs": "Clubs de vente en gros", "metaMask.subtitle": "Active le mode MetaMask pour rediriger toutes les connexions MetaMask vers Zeal. Cliquer sur MetaMask dans les dApps te connectera alors à Zeal.", "metaMask.title": "Connexion impossible avec Zeal ?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Ouvre ton application bancaire", "monerium-bank-deposit.buttet-point.receive-crypto": "Reçois des EUR numériques", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Envoie des {fiatCurrencyCode} sur ton compte", "monerium-bank-deposit.deposit-account-country": "Pays", "monerium-bank-deposit.header": "{fullName} - Compte personnel", "monerium-bank-details.account-name": "Nom du compte", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT copié", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT copié", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN copié", "monerium-bank-details.to-wallet": "Vers le portefeuille", "monerium-bank-details.transfer-fee": "Frais de virement", "monerium-bank-transfer.enable-card.bullet-1": "Finaliser la vérification d'identité", "monerium-bank-transfer.enable-card.bullet-2": "Obtenir les informations du compte personnel", "monerium-bank-transfer.enable-card.bullet-3": "Dé<PERSON>r depuis un compte bancaire", "monerium-card-delay-relay.success.cta": "<PERSON><PERSON><PERSON>", "monerium-card-delay-relay.success.subtitle": "Pour des raisons de sécurité, les changements de paramètres de la carte prennent 3 minutes.", "monerium-card-delay-relay.success.title": "Reviens dans 3 min pour continuer la configuration de Monerium", "monerium-deposit.account-details-info-popup.bullet-point-1": "Tout {fiatCurrencyCode} que tu envoies sur ce compte sera automatiquement converti en {cryptoCurrencyCode} jetons sur la {cryptoCurrencyChain} Chain et envoyé à ton portefeuille", "monerium-deposit.account-details-info-popup.bullet-point-2": "ENVOIE UNIQUEMENT DES {fiatCurrencyCode} ({fiatCurrencySymbol}) sur ton compte", "monerium-deposit.account-details-info-popup.title": "Tes coordonnées de compte", "monerium.check_order_status.sending": "Envoi en cours", "monerium.not-eligible.cta": "Retour", "monerium.not-eligible.subtitle": "Monerium ne peut pas t'ouvrir de compte. Choisis un autre prestataire.", "monerium.not-eligible.title": "Essaie un autre prestataire", "monerium.setup-card.cancel": "Annuler", "monerium.setup-card.continue": "<PERSON><PERSON><PERSON>", "monerium.setup-card.create_account": "<PERSON><PERSON><PERSON> un compte", "monerium.setup-card.login": "Connexion à Gnosis Pay", "monerium.setup-card.subtitle": "C<PERSON>e ou connecte-toi à ton compte Gnosis Pay pour activer les dépôts bancaires.", "monerium.setup-card.subtitle_personal_account": "Obtiens ton compte personnel avec Gnosis Pay en quelques minutes :", "monerium.setup-card.title": "Activer les dépôts bancaires", "moneriumDepositSuccess.goToWallet": "Aller au portefeuille", "moneriumDepositSuccess.title": "{symbol} reçu", "moneriumInfo.fees": "Tu ne paies aucun frais", "moneriumInfo.registration": "Monerium est un Établissement de Monnaie Électronique agréé et réglementé en vertu de la loi islandaise sur la monnaie électronique n° 17/2013 <link>En savoir plus</link>", "moneriumInfo.selfCustody": "Les fonds numériques que tu reçois sont sur un portefeuille privé et personne d'autre n'aura le contrôle de tes actifs", "moneriumWithdrawRejected.supportText": "Nous n'avons pas pu effectuer ton virement. Essaie à nouveau et si ça ne fonctionne toujours pas, <link>contacte le support.</link>", "moneriumWithdrawRejected.title": "Virement annulé", "moneriumWithdrawRejected.tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "moneriumWithdrawSuccess.supportText": "<PERSON><PERSON> peut prendre 24h pour que ton {br}bénéficiaire reçoive les fonds", "moneriumWithdrawSuccess.title": "<PERSON><PERSON><PERSON>", "monerium_enable_banner.text": "Activer les virements bancaires", "monerium_error_address_re_link_required.title": "Le portefeuille doit être reconnecté à Monerium", "monerium_error_duplicate_order.title": "Ordre en double", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "Franc suisse", "mt-pelerin-fork.options.chf.short": "Instantané et gratuit avec Mt Pelerin", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Instantané et gratuit avec Monerium", "mt-pelerin-fork.title": "Que veux-tu déposer ?", "mtPelerinProviderInfo.fees": "Tu ne paies aucun frais", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd est affiliée à SO-FIT, un organisme d'autorégulation reconnu par l'Autorité fédérale de surveillance des marchés financiers (FINMA) en vertu de la loi sur le blanchiment d'argent. <link>En savoir plus</link>", "mtPelerinProviderInfo.selfCustody": "Les liquidités numériques que tu reçois sont dans ton portefeuille privé et personne d'autre ne contrôlera tes actifs", "network-fee-widget.title": "<PERSON><PERSON>", "network.edit.verifying_rpc": "Vérification du RPC", "network.editRpc.predefined_network_info.subtitle": "Comme un VPN, Zeal utilise des RPC qui empêchent le suivi de tes données personnelles.{br}{br}Les RPC par défaut de Zeal sont des fournisseurs fiables et éprouvés.", "network.editRpc.predefined_network_info.title": "RPC <PERSON>", "network.filter.update_rpc_success": "Nœud RPC enregistré", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "Tous les réseaux", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "Ajouter des réseaux", "networks.filter.add_modal.chain_list.subtitle": "Ajoute n'importe quel réseau EVM", "networks.filter.add_modal.chain_list.title": "<PERSON><PERSON> Chainlist", "networks.filter.add_modal.dapp_tip.subtitle": "Dans tes dApps préférées, change simplement pour le réseau EVM que tu veux utiliser et Zeal te demandera si tu veux l'ajouter à ton portefeuille.", "networks.filter.add_modal.dapp_tip.title": "Ou ajoute un réseau depuis une dApp", "networks.filter.add_networks.subtitle": "Tous les réseaux EVM sont pris en charge", "networks.filter.add_networks.title": "Ajouter des réseaux", "networks.filter.add_test_networks.title": "Ajouter des testnets", "networks.filter.tab.netwokrs": "Réseaux", "networks.filter.testnets.title": "Testnets", "nft.widget.emptystate": "Aucun objet de collection dans le portefeuille", "nft_collection.change_account_picture.subtitle": "Modifier la photo de profil ?", "nft_collection.change_account_picture.title": "Utiliser un NFT comme photo de profil", "nfts.allNfts.pricingPopup.description": "Le prix des objets de collection est basé sur leur dernier prix d'échange.", "nfts.allNfts.pricingPopup.title": "Prix des objets de collection", "no-passkeys-found.modal.cta": "<PERSON><PERSON><PERSON>", "no-passkeys-found.modal.subtitle": "Nous ne détectons aucune clé d'accès Zeal sur cet appareil. Assure-toi d'être connecté au compte cloud que tu as utilisé pour créer ton Smart Wallet.", "no-passkeys-found.modal.title": "Aucune clé d'accès trouvée", "notValidEmail.title": "Adresse e-mail invalide", "notValidPhone.title": "Ce numéro de téléphone n'est pas valide", "notification-settings.title": "Réglages des notifications", "notification-settings.toggles.active-wallets": "Portefeuilles actifs", "notification-settings.toggles.bank-transfers": "Virements bancaires", "notification-settings.toggles.card-payments": "Paiements par carte", "notification-settings.toggles.readonly-wallets": "Portefeuilles en lecture seule", "ntft.groupHeader.text": "Objets de collection", "on_ramp.crypto_completed": "<PERSON><PERSON><PERSON><PERSON>", "on_ramp.fiat_completed": "<PERSON><PERSON><PERSON><PERSON>", "onboarding-widget.subtitle.card_created_from_order.left": "Carte Visa", "onboarding-widget.subtitle.card_created_from_order.right": "Activer la carte", "onboarding-widget.subtitle.card_order_ready.left": "Carte Visa physique", "onboarding-widget.subtitle.default": "Virements bancaires et carte Visa", "onboarding-widget.title.card-order-in-progress": "Continuer la commande de carte", "onboarding-widget.title.card_created_from_order": "La carte a été envoyée", "onboarding-widget.title.kyc_approved": "Virements et carte prêts", "onboarding-widget.title.kyc_failed": "Création de compte impossible", "onboarding-widget.title.kyc_not_started": "Continuer la configuration", "onboarding-widget.title.kyc_started_documents_requested": "Terminer la vérification", "onboarding-widget.title.kyc_started_resubmission_requested": "Réessayer la vérification", "onboarding-widget.title.kyc_started_verification_in_progress": "Vérification de l'identité en cours", "onboarding.loginOrCreateAccount.amountOfAssets": "+10 Md$ d'actifs", "onboarding.loginOrCreateAccount.cards.subtitle": "Disponible uniquement dans certaines régions. En continuant, tu acceptes nos <Terms>Conditions d'utilisation</Terms> et notre <PrivacyPolicy>Politique de confidentialité</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Carte Visa à hauts{br}rendements et sans frais", "onboarding.loginOrCreateAccount.createAccount": "<PERSON><PERSON><PERSON> un compte", "onboarding.loginOrCreateAccount.earn.subtitle": "Les rendements varient, capital à risque. En continuant, tu acceptes nos <Terms>Conditions d'utilisation</Terms> et notre <PrivacyPolicy>Politique de confidentialité</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "<PERSON><PERSON><PERSON> jusqu'<PERSON> {percent} par an{br}Approuvé par {currencySymbol}+5 Md$", "onboarding.loginOrCreateAccount.earningPerYear": "<PERSON><PERSON><PERSON> jusqu'à {percent}{br}par an", "onboarding.loginOrCreateAccount.login": "Connexion", "onboarding.loginOrCreateAccount.trading.subtitle": "Capital à risque. En continuant, tu acceptes nos <Terms>Conditions d'utilisation</Terms> et notre <PrivacyPolicy>Politique de confidentialité</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Investis dans tout,{br}du BTC au S&P", "onboarding.loginOrCreateAccount.trustedBy": "Marchés monétaires numériques{br}La confiance de {assets}", "onboarding.wallet_stories.close": "<PERSON><PERSON><PERSON>", "onboarding.wallet_stories.previous": "Précédent", "order-earn-deposit-bridge.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.into": "<PERSON><PERSON>", "otpIncorrectMessage": "Le code de confirmation est incorrect", "passkey-creation-not-possible.modal.close": "<PERSON><PERSON><PERSON>", "passkey-creation-not-possible.modal.subtitle": "Nous n'avons pas pu créer de Passkey pour ton portefeuille. Assure-toi que ton appareil est compatible avec les Passkeys et réessaie. <link>Contacter le support</link> si le problème persiste.", "passkey-creation-not-possible.modal.title": "<PERSON> de créer le <PERSON>key", "passkey-not-supported-in-mobile-browser.modal.cta": "Télécharger Zeal", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Les Smart Wallets ne sont pas pris en charge sur les navigateurs mobiles.", "passkey-not-supported-in-mobile-browser.modal.title": "Télécharge l'app Zeal pour continuer", "passkey-recovery.recovering.deploy-signer.loading-text": "Vérification de la clé d'accès", "passkey-recovery.recovering.loading-text": "Récupération du portefeuille", "passkey-recovery.recovering.signer-not-found.subtitle": "Nous n'avons pas pu lier ta clé d'accès à un portefeuille actif. Si tu as des fonds dans ton portefeuille, contacte l'équipe Zeal pour obtenir de l'aide.", "passkey-recovery.recovering.signer-not-found.title": "Aucun portefeuille trouvé", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "Essayer une autre clé", "passkey-recovery.select-passkey.banner.subtitle": "Véri<PERSON> le compte connecté sur ton appareil. Les clés d'accès sont spécifiques au compte.", "passkey-recovery.select-passkey.banner.title": "Tu ne vois pas la clé d'accès de ton portefeuille ?", "passkey-recovery.select-passkey.continue": "Choisir la clé d'accès", "passkey-recovery.select-passkey.subtitle": "Sélectionne la clé d'accès liée à ton portefeuille pour y accéder à nouveau.", "passkey-recovery.select-passkey.title": "Sélectionner la clé d'accès", "passkey-story_1.subtitle": "Avec un Smart Wallet, paie les frais réseau avec la plupart des jetons, sans te soucier du reste.", "passkey-story_1.title": "Paie les frais réseau avec de nombreux jetons", "passkey-story_2.subtitle": "Bas<PERSON> sur les contrats intelligents de <PERSON>, leader du secteur, qui sécurisent plus de 100 milliards de dollars dans plus de 20 millions de portefeuilles.", "passkey-story_2.title": "Sécurisé par Safe", "passkey-story_3.subtitle": "Les Smart Wallets fonctionnent sur les principaux réseaux compatibles avec Ethereum. Vérifie les réseaux pris en charge avant d'envoyer des actifs.", "passkey-story_3.title": "Principaux réseaux EVM pris en charge", "password.add.header": "<PERSON><PERSON><PERSON> un mot de passe", "password.add.includeLowerAndUppercase": "Majuscules et minuscules", "password.add.includesNumberOrSpecialChar": "Un chiffre ou un symbole", "password.add.info.subtitle": "Nous n'envoyons pas ton mot de passe sur nos serveurs et ne le sauvegardons pas pour toi.", "password.add.info.t_and_c": "En continuant, tu acceptes nos <Terms>Conditions d'utilisation</Terms> et notre <PrivacyPolicy>Politique de confidentialité</PrivacyPolicy>", "password.add.info.title": "Ton mot de passe reste sur cet appareil", "password.add.inputPlaceholder": "<PERSON><PERSON><PERSON> un mot de passe", "password.add.shouldContainsMinCharsCheck": "10+ caractères", "password.add.subheader": "Tu utiliseras ton mot de passe pour déverrouiller Zeal.", "password.add.success.title": "Mot de passe créé 🔥", "password.confirm.header": "Confirmer le mot de passe", "password.confirm.passwordDidNotMatch": "Les mots de passe doivent correspondre", "password.confirm.subheader": "Saisis ton mot de passe une nouvelle fois", "password.create_pin.subtitle": "Ce code verrouille l'application Zeal", "password.create_pin.title": "Crée ton code d'accès", "password.enter_pin.title": "Saisir le code d'accès", "password.incorrectPin": "Code d'accès incorrect", "password.pin_is_not_same": "Le code d'accès ne correspond pas", "password.placeholder.enter": "<PERSON><PERSON> le mot de passe", "password.placeholder.reenter": "<PERSON><PERSON><PERSON><PERSON> le mot de passe", "password.re_enter_pin.subtitle": "Saisis à nouveau le même code d'accès", "password.re_enter_pin.title": "Confirmer le code d'accès", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "En attente", "pending-send.details.pending": "En attente", "pending-send.details.processing": "En cours", "permit-info.modal.description": "Les permis sont des requêtes qui, une fois signées, autorisent des apps à déplacer tes jetons en ton nom, par exemple pour un swap.{br}Les permis sont similaires aux approbations, mais leur signature n'entraîne aucuns frais réseau.", "permit-info.modal.title": "Que sont les permis ?", "permit.edit-expiration": "Modifier {currency} l'expiration", "permit.edit-limit": "Modifier la {currency} limite de dépense", "permit.edit-modal.expiresIn": "Expire dans…", "permit.expiration-warning": "{currency} avertissement d'expiration", "permit.expiration.info": "{currency} infos d'expiration", "permit.expiration.never": "<PERSON><PERSON>", "permit.spend-limit.info": "{currency} infos sur la limite de dépense", "permit.spend-limit.warning": "{currency} avertissement sur la limite de dépense", "phoneNumber.title": "numéro de téléphone", "physicalCardOrderFlow.cardOrdered": "Carte commandée", "physicalCardOrderFlow.city": "Ville", "physicalCardOrderFlow.orderCard": "Commander la carte", "physicalCardOrderFlow.postcode": "Code postal", "physicalCardOrderFlow.shippingAddress.subtitle": "L'adresse de livraison de ta carte", "physicalCardOrderFlow.shippingAddress.title": "<PERSON><PERSON><PERSON>", "physicalCardOrderFlow.street": "Rue", "placeholderDapps.1inch.description": "Échange via les meilleurs itinéraires", "placeholderDapps.aave.description": "Prê<PERSON> et emprunter des jetons", "placeholderDapps.bungee.description": "Passerelle entre réseaux via les meilleurs itinéraires", "placeholderDapps.compound.description": "Prê<PERSON> et emprunter des jetons", "placeholderDapps.cowswap.description": "Échange aux meilleurs taux sur Gnosis", "placeholderDapps.gnosis-pay.description": "G<PERSON>rer ta carte Gnosis Pay", "placeholderDapps.jumper.description": "Passerelle entre réseaux via les meilleurs itinéraires", "placeholderDapps.lido.description": "Staker des ETH pour plus d'ETH", "placeholderDapps.monerium.description": "Monnaie électronique et virements bancaires", "placeholderDapps.odos.description": "Échange via les meilleurs itinéraires", "placeholderDapps.stargate.description": "Passerelle ou staking pour <14% APY", "placeholderDapps.uniswap.description": "L'une des plateformes d'échange les plus populaires", "pleaseAllowNotifications.cardPayments": "Paiements par carte", "pleaseAllowNotifications.customiseInSettings": "Personnalise dans les réglages", "pleaseAllowNotifications.enable": "Activer", "pleaseAllowNotifications.forWalletActivity": "Pour l'activité du portefeuille", "pleaseAllowNotifications.title": "Recevoir les notifications du portefeuille", "pleaseAllowNotifications.whenReceivingAssets": "Lors de la réception d'actifs", "portfolio.quick-actions.add_funds": "Ajouter", "portfolio.quick-actions.buy": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portfolio.quick-actions.send": "Envoyer", "portfolio.view.lastRefreshed": "Actualisé {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Recharge tes AVAX de testnet", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Aller au Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "Recharge tes BNB de testnet", "portfolio.view.topupTestNet.BscTestnet.secondary": "Aller au Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Recharge tes SepETH de testnet", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Aller au Faucet Sepolia", "portfolio.view.topupTestNet.FantomTestnet.primary": "Recharge tes FTM de testnet", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Aller au Faucet", "privateKeyConfirmation.banner.subtitle": "Ta clé privée donne accès à ton portefeuille et tes fonds. Seuls les escrocs la demandent.", "privateKeyConfirmation.banner.title": "Je comprends les risques", "privateKeyConfirmation.title": "NE PARTAGE JAMAIS ta clé privée", "rating-request.not-now": "Plus tard", "rating-request.title": "Recommanderais-tu <PERSON><PERSON> ?", "receive_funds.address-text": "Ceci est ton adresse de portefeuille unique. Tu peux la partager en toute sécurité.", "receive_funds.copy_address": "Co<PERSON>r l'adresse", "receive_funds.network-warning.eoa.subtitle": "<link>Voir les réseaux</link>. Les actifs envoyés sur des réseaux non-EVM seront perdus.", "receive_funds.network-warning.eoa.title": "Tous les réseaux basés sur Ethereum sont pris en charge", "receive_funds.network-warning.scw.subtitle": "<link>Voir les réseaux</link>. Les actifs envoyés sur d'autres réseaux seront perdus.", "receive_funds.network-warning.scw.title": "Important : utilise uniquement les réseaux pris en charge", "receive_funds.scan_qr_code": "Scanner un QR code", "receiving.in.days": "R<PERSON><PERSON> dans {days} j", "receiving.this.week": "<PERSON><PERSON><PERSON> cette semaine", "receiving.today": "<PERSON><PERSON><PERSON> aujou<PERSON>'hui", "reference.error.maximum_number_of_characters_exceeded": "Trop de caractères", "referral-code.placeholder": "Coller le lien d'invitation", "referral-code.subtitle": "Clique à nouveau sur le lien de ton ami, ou colle-le ci-dessous. Nous voulons nous assurer que tu reçoives tes récompenses.", "referral-code.title": "Un ami t'a envoyé {bReward} ?", "rekyc.verification_deadline.subtitle": "Termine la vérification sous {daysUntil} jours pour continuer à utiliser ta carte.", "rekyc.verification_required.subtitle": "Vérification requise pour ta carte.", "reminder.fund": "💸 Ajoute des fonds — gagne 6 % instantanément", "reminder.onboarding": "🏁 Termine la configuration — gagne 6 % sur tes dépôts", "remove-owner.confirmation.subtitle": "<PERSON>r s<PERSON><PERSON><PERSON><PERSON>, les changements de paramètres prennent 3 minutes. Pendant ce temps, ta carte sera temporairement bloquée et les paiements impossibles.", "remove-owner.confirmation.title": "Ta carte bloquée 3 min pour la mise à jour", "restore-smart-wallet.wallet-recovered": "Portefeuille récupéré", "rewardClaimCelebration.claimedTitle": "Récompenses déjà obtenues", "rewardClaimCelebration.subtitle": "Pour avoir invité des amis", "rewardClaimCelebration.title": "<PERSON> as gagné", "rewards-warning.subtitle": "Supprimer ce compte suspendra l'accès aux récompenses. Tu pourras le restaurer à tout moment pour les récupérer.", "rewards-warning.title": "Tu perdras l'accès à tes récompenses", "rewards.copiedInviteLink": "Lien d'invitation copié", "rewards.createAccount": "Copier lien d'invitation", "rewards.header.subtitle": "Nous enverrons {aReward} à toi et {bReward} à ton ami, quand il dépensera {bSpendLimitReward}.", "rewards.header.title": "<PERSON><PERSON><PERSON> {amountA}{br}<PERSON><PERSON> {amountB}", "rewards.sendInvite": "Envoyer une invitation", "rewards.sendInviteTip": "Choisis un ami et nous lui donnerons {bAmount}", "route.fees": "Frais {fees}", "routesNotFound.description": "L'itinéraire d'échange pour la combinaison de réseaux {from}-{to} n'est pas disponible.", "routesNotFound.title": "Aucun itinéraire d'échange disponible", "rpc.OrderBuySignMessage.subtitle": "Via Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Via Swaps.IO", "rpc.addCustomNetwork.addNetwork": "Ajouter", "rpc.addCustomNetwork.chainId": "ID de la chaîne", "rpc.addCustomNetwork.nativeToken": "<PERSON><PERSON> natif", "rpc.addCustomNetwork.networkName": "Nom du réseau", "rpc.addCustomNetwork.operationDescription": "Autorise ce site web à ajouter un réseau à ton portefeuille. Zeal ne peut pas vérifier la sécurité des réseaux personnalisés, assure-toi de bien comprendre les risques.", "rpc.addCustomNetwork.rpcUrl": "URL RPC", "rpc.addCustomNetwork.subtitle": "Via {name}", "rpc.addCustomNetwork.title": "A<PERSON>ter un réseau", "rpc.send_token.network_not_supported.subtitle": "Nous travaillons à l'activation des transactions sur ce réseau. <PERSON><PERSON><PERSON> de ta <PERSON> 🙏", "rpc.send_token.network_not_supported.title": "R<PERSON><PERSON> bi<PERSON> disponible", "rpc.send_token.send_or_receive.settings": "Paramètres", "rpc.sign.accept": "Accepter", "rpc.sign.cannot_parse_message.body": "Nous n'avons pas pu décoder ce message. N'accepte cette demande que si tu fais confiance à cette application.{br}{br}Les messages peuvent servir à te connecter à une application, mais aussi donner à des applications le contrôle de tes jetons.", "rpc.sign.cannot_parse_message.header": "Agir avec prudence", "rpc.sign.import_private_key": "Importer les clés", "rpc.sign.subtitle": "Pour {name}", "rpc.sign.title": "Signer", "safe-creation.success.title": "Portefeuille créé", "safe-safety-checks-popup.title": "Vérifications de sécurité de la transaction", "safetyChecksPopup.title": "Vérifications de sécurité du site", "scan_qr_code.description": "Scanne un QR code de portefeuille ou connecte-toi à une app", "scan_qr_code.show_qr_code": "Afficher mon QR code", "scan_qr_code.tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "scan_qr_code.unlockCamera": "Activer la caméra", "screen-lock-missing.modal.close": "<PERSON><PERSON><PERSON>", "screen-lock-missing.modal.subtitle": "Ton appareil nécessite un verrouillage d'écran pour utiliser les Passkeys. Configure un verrouillage d'écran et réessaie.", "screen-lock-missing.modal.title": "Verrouillage d'écran manquant", "seedConfirmation.banner.subtitle": "Ta phrase secrète donne accès à ton portefeuille et tes fonds. Seuls les escrocs la demandent.", "seedConfirmation.title": "NE PARTAGE JAMAIS ta phrase secrète", "select-active-owner.subtitle": "Plusieurs portefeuilles sont liés à ta carte. Choisis celui à connecter à Zeal. Tu pourras changer plus tard.", "select-active-owner.title": "Sélectionner un portefeuille", "select-card.title": "Sélectionner une carte", "select-crypto-currency-title": "Sélectionner un jeton", "select-token.title": "Sélectionner un jeton", "selectEarnAccount.chf.description.steps": "· Retrait des fonds 24/7, sans blocage {br}· Intérêts cumulés chaque seconde {br}· Dépôts surprotégés dans <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} par an en CHF", "selectEarnAccount.eur.description.steps": "· Retraits 24/7, sans période de blocage {br}· Intérêts accumulés chaque seconde {br}· Prêts sur-garantis avec <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} par an en EUR", "selectEarnAccount.subtitle": "Tu peux changer à tout moment", "selectEarnAccount.title": "Sélectionner une devise", "selectEarnAccount.usd.description.steps": "· Retraits 24/7, sans période de blocage {br}· Intérêts accumulés chaque seconde {br}· Dépôts sur-garantis sur <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} par an en USD", "selectEarnAccount.zero.description_general": "Conserver des liquidités numériques sans gagner d'intérêts", "selectEarnAccount.zero.title": "0 % par an", "selectRechargeThreshold.button.enterAmount": "<PERSON><PERSON> un montant", "selectRechargeThreshold.button.setTo": "Définir à {amount}", "selectRechargeThreshold.description.line1": "Quand le solde de ta carte passe sous {amount}, elle est automatiquement rechargée jusqu'à {amount} depuis ton compte Earn.", "selectRechargeThreshold.description.line2": "Un seuil plus bas conserve plus de fonds sur ton compte Earn (qui rapporte 3 %). Tu peux le modifier à tout moment.", "selectRechargeThreshold.title": "Définir le solde cible de la carte", "select_currency_to_withdraw.select_token_to_withdraw": "Sélectionner le jeton à retirer", "send-card-token.form.send": "Envoyer", "send-card-token.form.send-amount": "Montant à ajouter", "send-card-token.form.title": "Ajouter des fonds à la carte", "send-card-token.form.to-address": "<PERSON><PERSON>", "send-safe-transaction.network-fee-widget.error": "<PERSON> as besoin de {amount} ou choisis un autre jeton", "send-safe-transaction.network-fee-widget.no-fee": "Aucuns frais", "send-safe-transaction.network-fee-widget.title": "<PERSON><PERSON>", "send-safe-transaction.network_fee_widget.title": "<PERSON><PERSON>", "send.banner.fees": "Il te faut {amount} de plus en {currency} pour payer les frais", "send.banner.toAddressNotSupportedNetwork.subtitle": "Le portefeuille du destinataire ne prend pas en charge {network}. Choisis un jeton pris en charge.", "send.banner.toAddressNotSupportedNetwork.title": "Réseau non pris en charge par le destinataire", "send.banner.walletNotSupportedNetwork.subtitle": "Les Smart Wallets ne peuvent pas faire de transactions sur {network}. Choisis un jeton supporté.", "send.banner.walletNotSupportedNetwork.title": "Réseau du jeton non pris en charge", "send.empty-portfolio.empty-state": "<PERSON><PERSON>n jeton trouvé", "send.empty-portfolio.header": "Jet<PERSON>", "send.titile": "Envoi", "sendLimit.success.subtitle": "Nouvelle limite active dans 3 min. Continue avec la limite actuelle en attendant.", "sendLimit.success.title": "Ce changement prendra 3 minutes", "send_crypto.form.disconnected.cta.addFunds": "Ajouter des fonds", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Passer à {network}", "send_crypto.form.disconnected.label": "<PERSON><PERSON> à virer", "send_to.qr_code.description": "Scanne un QR code pour envoyer à un portefeuille", "send_to.qr_code.title": "Scanner un QR code", "send_to_card.header": "Envoyer à l'adresse de la carte", "send_to_card.select_sender.add_wallet": "Ajouter un portefeuille", "send_to_card.select_sender.header": "Sélectionner l'expéditeur", "send_to_card.select_sender.search.default_placeholder": "Rechercher une adresse ou un ENS", "send_to_card.select_sender.show_card_address_button_description": "Afficher l'adresse de la carte", "send_token.form.select-address": "<PERSON><PERSON><PERSON><PERSON>ner une adresse", "send_token.form.send-amount": "<PERSON><PERSON> à envoyer", "send_token.form.title": "Envoyer", "setLimit.amount.error.zero_amount": "Tu ne pourras effectuer aucun paiement", "setLimit.error.max_limit_reached": "Définir la limite max {amount}", "setLimit.error.same_as_current_limit": "Limite inchangée", "setLimit.placeholder": "Actuelle : {amount}", "setLimit.submit": "Définir la limite", "setLimit.submit.error.amount_required": "<PERSON><PERSON> un montant", "setLimit.subtitle": "C'est le montant que tu peux dépenser par jour avec ta carte.", "setLimit.title": "Définir la limite de dépense quotidienne", "settings.accounts": "<PERSON><PERSON><PERSON>", "settings.accountsSeeAll": "Tout voir", "settings.addAccount": "Ajouter un portefeuille", "settings.card": "Paramètres de la carte", "settings.connections": "Connexions aux applications", "settings.currency": "<PERSON><PERSON> par défaut", "settings.default_currency_selector.title": "<PERSON><PERSON>", "settings.discord": "Discord", "settings.experimentalMode": "Mode expérimental", "settings.experimentalMode.subtitle": "Teste les nouvelles fonctionnalités", "settings.language": "<PERSON><PERSON>", "settings.lockZeal": "<PERSON>errou<PERSON>r Z<PERSON>", "settings.notifications": "Notifications", "settings.open_expanded_view": "<PERSON><PERSON><PERSON><PERSON>r la vue étendue", "settings.privacyPolicy": "Politique de confidentialité", "settings.settings": "Paramètres", "settings.termsOfUse": "Conditions d'utilisation", "settings.twitter": "𝕏 / Twitter", "settings.version": "Version {version} env : {env}", "setup-card.confirmation": "Obtenir une carte virtuelle", "setup-card.confirmation.subtitle": "Fais des paiements en ligne et ajoute-la à ton {type} wallet pour les paiements sans contact.", "setup-card.getCard": "Obtenir la carte", "setup-card.order.physicalCard": "<PERSON>te physique", "setup-card.order.physicalCard.steps": "· Une carte physique VISA Gnosis Pay {br}· Expédition sous 3 semaines {br}· Pour les paiements en personne et aux distributeurs. {br}· Ajout à Apple/Google Wallet (pays compatibles uniquement", "setup-card.order.subtitle1": "Tu peux utiliser plusieurs cartes en même temps", "setup-card.order.title": "Quel type de carte ?", "setup-card.order.virtualCard": "<PERSON><PERSON>", "setup-card.order.virtual_card.steps": "· Une carte numérique VISA Gnosis Pay {br}· Utilisation immédiate pour les paiements en ligne {br}· Ajout à Apple/Google Wallet (pays compatibles uniquement)", "setup-card.orderCard": "Commander la carte", "setup-card.virtual-card": "Obtenir carte virtuelle", "setup.notifs.fakeAndroid.title": "Notifications pour les paiements et les virements entrants", "setup.notifs.fakeIos.subtitle": "Zeal peut t'alerter quand tu reçois des fonds ou dépenses avec ta carte Visa. Tu pourras modifier ce réglage plus tard.", "setup.notifs.fakeIos.title": "Notifications pour les paiements et les virements entrants", "sign.PermitAllowanceItem.spendLimit": "<PERSON><PERSON>", "sign.ledger.subtitle": "Vérifie ton portefeuille matériel.", "sign.ledger.title": "Signer avec le portefeuille matériel", "sign.passkey.subtitle": "Ton navigateur dev<PERSON>t t'inviter à signer avec le Passkey associé à ce portefeuille. Continue là-bas.", "sign.passkey.title": "Sélection<PERSON> le Passkey", "signal_aborted_for_uknown_reason.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON><PERSON> annu<PERSON>", "simulatedTransaction.BridgeTrx.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.CardTopUp.info.title": "Ajouter des fonds à la carte", "simulatedTransaction.CardTopUpTrx.info.title": "Ajouter des fonds à la carte", "simulatedTransaction.NftCollectionApproval.approve": "Approuver la collection de NFT", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "Ajouter à la carte", "simulatedTransaction.OrderEarnDepositBridge.title": "Dépôt dans Earn", "simulatedTransaction.P2PTransaction.info.title": "Envoyer", "simulatedTransaction.PermitSignMessage.title": "<PERSON><PERSON>", "simulatedTransaction.SingleNftApproval.approve": "Approuver le NFT", "simulatedTransaction.UnknownSignMessage.title": "Signer", "simulatedTransaction.Withdrawal.info.title": "Retrait", "simulatedTransaction.approval.title": "Approuver", "simulatedTransaction.approve.info.title": "Approuver", "simulatedTransaction.p2p.info.account": "À", "simulatedTransaction.p2p.info.unlabelledAccount": "Portefeuille non étiqueté", "simulatedTransaction.unknown.info.receive": "Ré<PERSON>", "simulatedTransaction.unknown.info.send": "Envoi", "simulatedTransaction.unknown.using": "Via {app}", "simulation.approval.modal.text": "Quand tu acceptes une approbation, tu donnes la permission à une application ou à un contrat intelligent spécifique d'utiliser tes jetons ou NFT dans des transactions futures.", "simulation.approval.modal.title": "Que sont les approbations ?", "simulation.approval.spend-limit.label": "Plafond de <PERSON>", "simulation.approve.footer.for": "Pour", "simulation.approve.unlimited": "Illimité", "simulationNotAvailable.title": "Action inconnue", "smart-wallet-activation-view.on": "Sur", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password peut bloquer l'accès à ton portefeuille", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Utilise Apple ou Google pour configurer Zeal en toute sécurité", "smart-wallet.passkey-notice.title": "Éviter 1Password", "spend-limits.high.modal.text": "Définis un plafond de dépense proche du montant que tu vas réellement utiliser avec une app ou un contrat intelligent. Les plafonds élevés sont risqués et peuvent faciliter le vol de tes jetons par des fraudeurs.", "spend-limits.high.modal.text_sign_message": "La limite de dépense doit être proche du montant que tu utiliseras. Une limite élevée est risquée et peut faciliter le vol de tes jetons par des escrocs.", "spend-limits.high.modal.title": "Plafond de dépense élevé", "spend-limits.modal.text": "Le plafond de dépense est le nombre de jetons qu'une app peut utiliser en ton nom. Tu peux le modifier ou le supprimer à tout moment. Pour ta sécurité, définis des plafonds proches du montant que tu comptes réellement utiliser avec une app.", "spend-limits.modal.title": "Qu'est-ce qu'un plafond de dépense ?", "spent-limit-info.modal.description": "La limite de dépense est le montant qu'une app peut utiliser en ton nom. Tu peux la modifier ou la supprimer à tout moment. Pour ta sécurité, garde cette limite proche du montant que tu comptes utiliser avec une app.", "spent-limit-info.modal.title": "Qu'est-ce qu'une limite de dépense ?", "sswaps-io.transfer-provider": "Fournisseur de virement", "storage.accountDetails.activateWallet": "<PERSON><PERSON> le portefeuille", "storage.accountDetails.changeWalletLabel": "Changer le nom du portefeuille", "storage.accountDetails.deleteWallet": "Supp<PERSON>er le portefeuille", "storage.accountDetails.setup_recovery_kit": "Kit de récupération", "storage.accountDetails.showPrivateKey": "Afficher la clé privée", "storage.accountDetails.showWalletAddress": "Afficher l'adresse du portefeuille", "storage.accountDetails.smartBackup": "Sauvegarde et récupération", "storage.accountDetails.viewSsecretPhrase": "Afficher la phrase secrète", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets ?", "storage.manageAccounts.title": "Portefeuilles", "submit-userop.progress.text": "Envoi en cours", "submit.error.amount_high": "<PERSON>ant trop élevé", "submit.error.amount_hight": "<PERSON>ant trop élevé", "submit.error.amount_low": "Montant trop faible", "submit.error.amount_required": "<PERSON><PERSON> un montant", "submit.error.maximum_number_of_characters_exceeded": "Réduis le nombre de caractères", "submit.error.not_enough_balance": "Solde insuffisant", "submit.error.recipient_required": "Bénéficiaire requis", "submit.error.routes_not_found": "Aucun itinéraire trouvé", "submitSafeTransaction.monitor.title": "Résultat de la transaction", "submitSafeTransaction.sign.title": "Résultat de la transaction", "submitSafeTransaction.state.sending": "Envoi en cours", "submitSafeTransaction.state.sign": "Création en cours", "submitSafeTransaction.submittingToRelayer.title": "Résultat de la transaction", "submitTransaction.cancel": "Annuler", "submitTransaction.cancel.attemptingToStop": "Tentative d'a<PERSON><PERSON><PERSON>", "submitTransaction.cancel.failedToStop": "Échec de l'arrêt", "submitTransaction.cancel.stopped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.cancel.title": "Aperçu de la transaction", "submitTransaction.failed.banner.description": "Le réseau a annulé cette transaction. Essaie à nouveau ou contacte-nous.", "submitTransaction.failed.banner.title": "La transaction a échoué", "submitTransaction.failed.execution_reverted.title": "L'application a rencontré une erreur", "submitTransaction.failed.execution_reverted_without_message.title": "L'application a rencontré une erreur", "submitTransaction.failed.out_of_gas.description": "Frais réseau dépassés. Transaction annulée.", "submitTransaction.failed.out_of_gas.title": "<PERSON><PERSON><PERSON>", "submitTransaction.sign.title": "Résultat de la transaction", "submitTransaction.speedUp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.addedToQueue": "Ajoutée à la file d'attente", "submitTransaction.state.addedToQueue.short": "En attente", "submitTransaction.state.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.complete": "{currencyCode} ajoutés à Zeal", "submitTransaction.state.complete.subtitle": "Consulte ton portefeuille Zeal", "submitTransaction.state.completed": "Terminée", "submitTransaction.state.failed": "Échec", "submitTransaction.state.includedInBlock": "Incluse dans le bloc", "submitTransaction.state.includedInBlock.short": "<PERSON>s le bloc", "submitTransaction.state.replaced": "Remplacée", "submitTransaction.state.sendingToNetwork": "Envoi au réseau", "submitTransaction.stop": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.submit": "Envoyer", "submitted-user-operation.state.bundled": "En file d'attente", "submitted-user-operation.state.completed": "Terminée", "submitted-user-operation.state.failed": "É<PERSON><PERSON>e", "submitted-user-operation.state.pending": "Re<PERSON>s en cours", "submitted-user-operation.state.rejected": "Rejetée", "submittedTransaction.failed.title": "La transaction a échoué", "success_splash.card_activated": "Carte activée", "supportFork.give-feedback.title": "Donner mon avis", "supportFork.itercom.description": "Pour les dépôts, <PERSON><PERSON>n, récom<PERSON>ses, etc.", "supportFork.itercom.title": "Questions sur le portefeuille", "supportFork.title": "<PERSON><PERSON><PERSON><PERSON> de l'aide pour", "supportFork.zendesk.subtitle": "Pour carte, identité, remboursements.", "supportFork.zendesk.title": "Paiements par carte et identité", "supported-networks.ethereum.warning": "Frais élevés", "supportedNetworks.networks": "Réseaux pris en charge", "supportedNetworks.oneAddressForAllNetworks": "Une seule adresse pour tous les réseaux", "supportedNetworks.receiveAnyAssets": "Reçois n'importe quel actif des réseaux pris en charge directement sur ton portefeuille Zeal avec la même adresse", "swap.form.error.no_routes_found": "Aucun itinéraire trouvé", "swap.form.error.not_enough_balance": "Solde insuffisant", "swaps-io-details.bank.serviceProvider": "Fournisseur de services", "swaps-io-details.details.processing": "En cours de traitement", "swaps-io-details.pending": "En attente", "swaps-io-details.rate": "<PERSON><PERSON>", "swaps-io-details.serviceProvider": "Fournisseur de services", "swaps-io-details.transaction.from.processing": "Transaction démarrée", "swaps-io-details.transaction.networkFees": "<PERSON><PERSON> r<PERSON>", "swaps-io-details.transaction.state.completed-transaction": "Transaction terminée", "swaps-io-details.transaction.state.started-transaction": "Transaction démarrée", "swaps-io-details.transaction.to.processing": "Transaction terminée", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Le dépôt sera bientôt terminé. Kinetex traite toujours ta transaction.", "swapsIO.monitoring.awaitingLiqSend.title": "Retardé", "swapsIO.monitoring.awaitingRecive.title": "Re<PERSON>s en cours", "swapsIO.monitoring.awaitingSend.title": "En file d'attente", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Les jetons ont été envoyés à Kinetex, mais seront bientôt retournés. Kinetex n'a pas pu finaliser la transaction de destination.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Retour des jetons", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Les jetons n'ont pas été transférés en raison d'une erreur inconnue. Essaie à nouveau.", "swapsIO.monitoring.cancelledNoSlash.title": "Jetons retournés", "swapsIO.monitoring.cancelledSlashed.subtitle": "Les jetons ont été retournés. Kinetex n'a pas pu finaliser la transaction de destination.", "swapsIO.monitoring.cancelledSlashed.title": "Jetons retournés", "swapsIO.monitoring.completed.title": "<PERSON><PERSON><PERSON><PERSON>", "taker-metadata.earn": "Gagne des USD numériques avec Sky", "taker-metadata.earn.aave": "Gagne des EUR numériques avec Aave", "taker-metadata.earn.aave.cashout24": "Retrait instantané, 24/7", "taker-metadata.earn.aave.trusted": "27 Md$ confiés, + de 2 ans", "taker-metadata.earn.aave.yield": "Rendement accumulé chaque seconde", "taker-metadata.earn.chf": "Gagne en CHF numérique", "taker-metadata.earn.chf.cashout24": "Retrait instantané, 24/7", "taker-metadata.earn.chf.trusted": "Déjà 28 M de Fr. dépo<PERSON>és", "taker-metadata.earn.chf.yield": "Ren<PERSON><PERSON> cumulé chaque seconde", "taker-metadata.earn.usd.cashout24": "Retrait instantané, 24/7", "taker-metadata.earn.usd.trusted": "10,7 Md$ confiés, + de 5 ans", "taker-metadata.earn.usd.yield": "Rendement accumulé chaque seconde", "test": "<PERSON><PERSON><PERSON>r", "to.titile": "À", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "Actifs", "token.groupHeader.titleWithSum": "Actifs {sum}", "token.hidden_tokens.page.title": "Jetons masqués", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "Ajouter un jeton", "token.widget.cashback_empty": "Aucune transaction pour l'instant", "token.widget.emptyState": "Aucun jeton dans le portefeuille", "tokens.cash": "Espèces", "top-up-card-from-earn-view.approve.for": "Pour", "top-up-card-from-earn-view.approve.into": "<PERSON><PERSON>", "top-up-card-from-earn-view.swap.from": "De", "top-up-card-from-earn-view.swap.to": "Vers", "top-up-card-from-earn-view.withdraw.to": "Vers", "top-up-card-from-earn.trx.title.approval": "Approuver l'échange", "top-up-card-from-earn.trx.title.swap": "Ajouter à la carte", "top-up-card-from-earn.trx.title.withdrawal": "Retrait de Earn", "topUpDapp.connectWallet": "Connecter un portefeuille", "topup-fee-breakdown.bungee-fee": "Frais de fournisseur externe", "topup-fee-breakdown.header": "Frais de transaction", "topup-fee-breakdown.network-fee": "<PERSON><PERSON>", "topup-fee-breakdown.total-fee": "<PERSON><PERSON>", "topup.continue-in-wallet": "Continue dans ton portefeuille", "topup.send.title": "Envoyer", "topup.submit-transaction.close": "<PERSON><PERSON><PERSON>", "topup.submit-transaction.sent-to-wallet": "Envoyer {amount}", "topup.to": "À", "topup.transaction.complete.close": "<PERSON><PERSON><PERSON>", "topup.transaction.complete.try-again": "<PERSON><PERSON><PERSON><PERSON>", "transaction-request.nonce-too-low.modal.button-text": "<PERSON><PERSON><PERSON>", "transaction-request.nonce-too-low.modal.text": "Une transaction avec le même numéro de série (nonce) est déjà terminée, tu ne peux donc plus soumettre celle-ci. <PERSON><PERSON> peut arriver si tu effectues des transactions très rapprochées ou si tu tentes d'accélérer ou d'annuler une transaction déjà terminée.", "transaction-request.nonce-too-low.modal.title": "Transaction avec ce nonce déjà terminée", "transaction-request.replaced.modal.button-text": "<PERSON><PERSON><PERSON>", "transaction-request.replaced.modal.text": "Nous ne pouvons pas suivre le statut de cette transaction. Soit elle a été remplacée par une autre, soit le nœud RPC rencontre des problèmes.", "transaction-request.replaced.modal.title": "Statut de transaction introuvable", "transaction.activity.details.modal.close": "<PERSON><PERSON><PERSON>", "transaction.cancel_popup.cancel": "<PERSON><PERSON><PERSON>", "transaction.cancel_popup.confirm": "<PERSON><PERSON>,arr<PERSON><PERSON>", "transaction.cancel_popup.description": "<PERSON>ur a<PERSON><PERSON><PERSON>, tu dois payer de nouveaux frais réseau à la place des frais initiaux de {oldFee}", "transaction.cancel_popup.description_without_original": "<PERSON><PERSON><PERSON><PERSON> nécessite de nouveaux frais réseau.", "transaction.cancel_popup.not_supported.subtitle": "Arrêt non pris en charge sur {network}", "transaction.cancel_popup.not_supported.title": "Non pris en charge", "transaction.cancel_popup.stopping_fee": "Frais réseau pour l'arrêt", "transaction.cancel_popup.title": "Arrêter la transaction ?", "transaction.in-progress": "En cours", "transaction.inProgress": "En cours", "transaction.speed_up_popup.cancel": "<PERSON><PERSON><PERSON>", "transaction.speed_up_popup.confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transaction.speed_up_popup.description": "Pour accé<PERSON><PERSON>, tu dois payer de nouveaux frais de réseau à la place des frais initiaux de {amount}", "transaction.speed_up_popup.description_without_original": "Pour accé<PERSON><PERSON>, tu dois payer de nouveaux frais de réseau", "transaction.speed_up_popup.seed_up_fee_title": "Frais d'accélération du réseau", "transaction.speed_up_popup.title": "Accélérer la transaction ?", "transaction.speedup_popup.not_supported.subtitle": "L'accélération des transactions n'est pas prise en charge sur {network}", "transaction.speedup_popup.not_supported.title": "Non pris en charge", "transaction.subTitle.failed": "Échec", "transactionDetails.cashback.not-qualified": "Non éligible", "transactionDetails.cashback.paid": "{amount} payé", "transactionDetails.cashback.pending": "{amount} en attente", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "Inconnu", "transactionDetails.cashback_estimate": "Estimation du cashback", "transactionDetails.category": "<PERSON><PERSON><PERSON><PERSON>", "transactionDetails.exchangeRate": "Taux de <PERSON>", "transactionDetails.location": "<PERSON><PERSON>", "transactionDetails.payment-approved": "Paiement approuvé", "transactionDetails.payment-declined": "Paiement refusé", "transactionDetails.payment-reversed": "Paiement annulé", "transactionDetails.recharge.amountSentFromEarn.title": "Montant envoy<PERSON> de<PERSON><PERSON>", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "Montant rechargé sur la carte", "transactionDetails.recharge.rate.title": "<PERSON><PERSON>", "transactionDetails.recharge.transactionId.title": "ID de transaction", "transactionDetails.refund": "Remboursement", "transactionDetails.reversal": "Annulation", "transactionDetails.transactionCurrency": "Devise de la transaction", "transactionDetails.transactionId": "ID de transaction", "transactionDetails.type": "Transaction", "transactionRequestWidget.approve.subtitle": "Pour {target}", "transactionRequestWidget.p2p.subtitle": "À {target}", "transactionRequestWidget.unknown.subtitle": "Via {target}", "transactionSafetyChecksPopup.title": "Vérifications de sécurité de la transaction", "transactions.main.activity.title": "Activité", "transactions.page.hiddenActivity.title": "Activité masquée", "transactions.page.title": "Activité", "transactions.viewTRXHistory.emptyState": "Aucune transaction pour le moment", "transactions.viewTRXHistory.errorMessage": "Nous n'avons pas pu charger ton historique de transactions", "transactions.viewTRXHistory.hidden.emptyState": "Aucune transaction masquée", "transactions.viewTRXHistory.noTxHistoryForTestNets": "L'activité n'est pas prise en charge pour les testnets", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "L'activité n'est pas prise en charge pour les testnets{br}<link>Sur l'explorateur</link>", "transfer_provider": "Fournisseur de virement", "transfer_setup_with_different_wallet.subtitle": "Les virements bancaires sont configurés avec un autre portefeuille. Un seul peut y être connecté.", "transfer_setup_with_different_wallet.swtich_and_continue": "Changer et continuer", "transfer_setup_with_different_wallet.title": "<PERSON>r <PERSON><PERSON><PERSON>", "tx-sent-to-wallet.button": "<PERSON><PERSON><PERSON>", "tx-sent-to-wallet.subtitle": "Continue dans {wallet}", "unblockProviderInfo.fees": "Tu obtiens les frais les plus bas possibles : 0 % jusqu'à 5 k $ par mois et 0,2 % au-delà.", "unblockProviderInfo.registration": "Unblock est enregistré et autorisé par la FNTT pour fournir des services d'échange et de conservation VASP, et est un fournisseur MSB enregistré auprès de la Fincen américaine. <link>En savoir plus</link>", "unblockProviderInfo.selfCustody": "L'argent numérique que tu reçois est sous ton propre contrôle et personne d'autre n'a accès à tes actifs.", "unblock_invalid_faster_payment_configuration.subtitle": "Le compte bancaire fourni ne prend pas en charge les virements SEPA ou UK Faster Payments. Merci d'utiliser un autre compte.", "unblock_invalid_faster_payment_configuration.title": "Autre compte requis", "unknownTransaction.primaryText": "Transaction par carte", "unsupportedCountry.subtitle": "Les virements bancaires ne sont pas encore disponibles dans ton pays.", "unsupportedCountry.title": "Non disponible en {country}", "update-app-popup.subtitle": "La dernière mise à jour contient des correctifs, des nouveautés et plus de magie. Installe-la pour profiter au mieux de Zeal.", "update-app-popup.title": "Mettre à jour Zeal", "update-app-popup.update-now": "Mettre à jour", "user_associated_with_other_merchant.subtitle": "Ce portefeuille ne peut être utilisé pour les virements. Utilise un autre portefeuille ou contacte-nous sur Discord.", "user_associated_with_other_merchant.title": "Portefeuille non utilisable", "user_associated_with_other_merchant.try_with_another_wallet": "<PERSON>r <PERSON><PERSON><PERSON>", "user_email_already_exists.subtitle": "<PERSON> as d<PERSON><PERSON><PERSON> configuré les virements avec un autre portefeuille. Réessaye avec celui-ci.", "user_email_already_exists.title": "Virements configurés avec un autre portefeuille", "user_email_already_exists.try_with_another_wallet": "Essayer un autre wallet", "validation.invalid.iban": "IBAN non valide", "validation.required": "Requis", "validation.required.first_name": "Prénom requis", "validation.required.iban": "IBAN requis", "validation.required.last_name": "Nom requis", "verify-passkey.cta": "Véri<PERSON><PERSON> le passkey", "verify-passkey.subtitle": "Vérifie que ton passkey est créé et bien sécurisé.", "verify-passkey.title": "Véri<PERSON><PERSON> le passkey", "view-cashback.cashback-next-cycle": "Taux de cashback dans {time}", "view-cashback.no-cashback": "0 %", "view-cashback.no-cashback.subtitle": "Fais un dépôt pour obtenir du cashback", "view-cashback.pending": "{money} en attente", "view-cashback.pending-rewards.not_paid": "R<PERSON><PERSON> dans {days} j", "view-cashback.pending-rewards.paid": "<PERSON><PERSON><PERSON> cette semaine", "view-cashback.received-rewards": "Récompenses reçues", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Paiements non confirmés", "view-cashback.upcoming": "À venir {money}", "virtual-card-order.configure-safe.loading-text": "Création de la carte", "virtual-card-order.create-order.loading-text": "Activation de la carte", "virtual-card-order.create-order.success-text": "Carte activée", "virtualCard.activateCard": "Activer la carte", "walletDeleteConfirm.main_action": "<PERSON><PERSON><PERSON><PERSON>", "walletDeleteConfirm.subtitle": "Tu devras l'importer à nouveau pour voir le portefeuille ou faire des transactions.", "walletDeleteConfirm.title": "Supprimer le portefeuille ?", "walletSetting.header": "Paramètres du portefeuille", "wallet_connect.connect.cancel": "Annuler", "wallet_connect.connect.connect_button": "Connecter", "wallet_connect.connect.title": "Connexion", "wallet_connect.connected.title": "Connecté", "wallet_connect_add_chain_missing.title": "R<PERSON>eau non pris en charge", "wallet_connect_proposal_expired.title": "Connexion expirée", "withdraw": "<PERSON><PERSON><PERSON>", "withdraw.confirmation.close": "Annuler", "withdraw.confirmation.continue": "Confirmer", "withdrawal_request.completed": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal_request.pending": "En attente", "zeal-dapp.connect-wallet.cta.primary.connecting": "Connexion...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Connecter", "zeal-dapp.connect-wallet.cta.secondary": "Annuler", "zeal-dapp.connect-wallet.title": "Connecte ton portefeuille pour continuer", "zealSmartWalletInfo.gas": "Paie les frais réseau avec de nombreux jetons : utilise des jetons ERC20 populaires sur les chaînes prises en charge, pas seulement les jetons natifs.", "zealSmartWalletInfo.recover": "Pas de phrases secrètes : récupère ton compte avec le passkey biométrique de ton gestionnaire de mots de passe, iCloud ou Google.", "zealSmartWalletInfo.selfCustodial": "Portefeuille entièrement privé : les signatures passkey sont validées on-chain pour minimiser les dépendances centrales.", "zealSmartWalletInfo.title": "À propos des Smart Wallets Zeal", "zeal_a_rewards_already_claimed_error.title": "Récompense déjà récupérée", "zwidget.minimizedDisconnected.label": "Zeal déconnecté"}