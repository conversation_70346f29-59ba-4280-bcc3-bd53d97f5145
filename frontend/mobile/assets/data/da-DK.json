{"Account.ListItem.details.label": "<PERSON><PERSON><PERSON>", "AddFromAddress.success": "Tegnebog gemt", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{Ingen wallets} one{{count} wallet} other{{count} wallets}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "<PERSON><PERSON><PERSON><PERSON> sæ<PERSON> {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Opret nye wallets fra en af dine eksisterende hemmelige sætninger", "AddFromExistingSecretPhrase.SelectPhrase.title": "Vælg en hemmelig sætning", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "Din hemmelige sætning kan sikkerhedskopiere mange wallets. <PERSON><PERSON><PERSON><PERSON> dem, du vil bruge.", "AddFromExistingSecretPhrase.WalletSelection.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> en <PERSON>", "AddFromExistingSecretPhrase.success": "Wallets føjet til Zeal", "AddFromHardwareWallet.subtitle": "Væ<PERSON>g din hardware-wallet for at forbinde til Zeal", "AddFromHardwareWallet.title": "Hardware-wallet", "AddFromNewSecretPhrase.WalletSelection.subtitle": "<PERSON><PERSON><PERSON><PERSON>, du vil importere", "AddFromNewSecretPhrase.WalletSelection.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.accounts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "En hemmelig sætning er som en hovednøgle.{br}{br}Importé<PERSON> tegne<PERSON>øger nu, flere senere.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Tegnebøger med hemmelig sætning", "AddFromNewSecretPhrase.subtitle": "Indtast hemmelig sætning med mellemrum.", "AddFromNewSecretPhrase.success_secret_phrase_added": "<PERSON><PERSON>lig sætning tilføjet 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Tegnebøger tilføjet til Zeal", "AddFromNewSecretPhrase.wallets": "Wallets", "AddFromPrivateKey.subtitle": "Indtast din private nøgle", "AddFromPrivateKey.success": "Privat nøgle tilføjet 🎉", "AddFromPrivateKey.title": "Gendan tegnebog", "AddFromPrivateKey.typeOrPaste": "<PERSON><PERSON><PERSON><PERSON> eller in<PERSON>æt her", "AddFromSecretPhrase.importWallets": "{count,plural,=0{Ingen wallets valgt} one{Importér wallet} other{Importér {count} wallets}}", "AddFromTrezor.AccountSelection.title": "<PERSON><PERSON>rt<PERSON><PERSON>", "AddFromTrezor.hwWalletTip.subtitle": "<PERSON><PERSON>rt<PERSON><PERSON>, du skal bruge fra den.", "AddFromTrezor.hwWalletTip.title": "Import fra hardware-tegnebøger", "AddFromTrezor.importAccounts": "{count,plural,=0{<PERSON><PERSON> tegne<PERSON>øger valgt} one{Importér tegnebog} other{Importér {count} tegnebø<PERSON>}}", "AddFromTrezor.success": "Tegnebøger tilføjet til Zeal", "ApprovalSpenderTypeCheck.failed.subtitle": "Muligvis svindel: Bør være en kontrakt", "ApprovalSpenderTypeCheck.failed.title": "Modtager er en wallet, ikke en kontrakt", "ApprovalSpenderTypeCheck.passed.subtitle": "Normalt godkender du aktiver til kontrakter", "ApprovalSpenderTypeCheck.passed.title": "Modtager er en smart contract", "BestReturns.subtitle": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>, inkl. alle gebyrer.", "BestReturnsPopup.title": "Bedste afkast", "BlacklistCheck.Failed.subtitle": "Ondsindede anmeldelser fra <source></source>", "BlacklistCheck.Failed.title": "Siden er sortlistet", "BlacklistCheck.Passed.subtitle": "Ingen ondsindede anmeldelser fra <source></source>", "BlacklistCheck.Passed.title": "Siden er ikke sortlistet", "BlacklistCheck.failed.statusButton.label": "Siden er blevet anmeldt", "BridgeRoute.slippage": "Slippage {slippage}", "BridgeRoute.title": "Bridge-udbyder", "CheckConfirmation.InProgress": "I gang...", "CheckConfirmation.success.splash": "Gennemført", "ChooseImportOrCreateSecretPhrase.subtitle": "Importér en hemmelig sætning, eller opret en ny", "ChooseImportOrCreateSecretPhrase.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> sætning", "ConfirmTransaction.Simuation.Skeleton.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "ConnectionSafetyCheckResult.passed": "Sikkerhedstjek bestået", "ContactGnosisPaysupport": "Kontakt Gnosis Pay", "CopyKeyButton.copied": "<PERSON><PERSON><PERSON>", "CopyKeyButton.copyYourKey": "<PERSON><PERSON><PERSON><PERSON> nø<PERSON>", "CopyKeyButton.copyYourPhrase": "<PERSON><PERSON><PERSON><PERSON> din sætning", "DAppVerificationCheck.Failed.subtitle": "<PERSON>n er ikke opført på <source></source>", "DAppVerificationCheck.Failed.title": "Siden blev ikke fundet i app-registre", "DAppVerificationCheck.Passed.subtitle": "<PERSON>n er opført på <source></source>", "DAppVerificationCheck.Passed.title": "<PERSON>n findes i app-registre", "DAppVerificationCheck.failed.statusButton.label": "Siden blev ikke fundet i app-registre", "ERC20.tokens.emptyState": "Vi fandt ingen tokens", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Prioritetsgebyr", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Sidste {period}: mellem {from} og {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Grundgebyr: {baseFee} • Sikkerhedsbuffer: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "Vi kunne ikke hente nuværende grundgebyr", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Højere end næste Nonce. Bliver hængende", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "Nonce kan ikke være lavere end nuværende", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "Vi kunne ikke beregne prioritetsgebyr", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "Vi kunne ikke hente nuværende maks. gebyr", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "Maks. gebyr", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "Kan sidde fast, til netværksgebyrer falder", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "Ma<PERSON><PERSON> grun<PERSON>r", "EditFeeModal.Custom.gasLimit.title": "Gasgrænse {gasLimit}", "EditFeeModal.Custom.title": "<PERSON><PERSON><PERSON><PERSON> in<PERSON>", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "Sidder fast, til grundgebyret falder", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Lavt gebyr. Kan sidde fast", "EditFeeModal.EditGasLimit.estimatedGas": "Est. gas: {estimated} • Sikkerhedsbuffer: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Under estimeret grænse. Transaktionen vil fejle", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Under foreslået grænse. Transaktionen kan fejle", "EditFeeModal.EditGasLimit.subtitle": "Inds<PERSON> den maksimale mængde gas, du vil bruge på denne transaktion. Din transaktion vil fejle, hvis du sætter en lavere grænse end nødvendigt", "EditFeeModal.EditGasLimit.title": "Rediger gasgrænse", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Under minimumsgr<PERSON>nsen for gas: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "Højere end næste nonce. Sidder fast", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "Nonce kan ikke være lavere end nuværende", "EditFeeModal.EditNonce.subtitle": "<PERSON> transaktion vil sidde fast, hvis du ikke vælger den næste nonce", "EditFeeModal.EditNonce.title": "Rediger nonce", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "<PERSON>r<PERSON>ver {amount} for at sende", "EditFeeModal.Header.Time.unknown": "Ukendt tid", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Maks. {fee}", "EditFeeModal.Header.fee.unknown": "Ukendt gebyr", "EditFeeModal.Header.subsequent_failed": "Estimater kan være gamle, seneste op<PERSON><PERSON> fejlede", "EditFeeModal.Layout.Header.ariaLabel": "Nuværende gebyr", "EditFeeModal.MaxFee.subtitle": "Ma<PERSON>. gebyret er det meste, du kan komme til at betale, men du betaler normalt det forventede gebyr. Denne buffer hjælper transaktionen igennem, hvis netværket bliver langsommere eller dyrere.", "EditFeeModal.MaxFee.title": "Maks. netværksgebyr", "EditFeeModal.SelectPreset.Time.unknown": "Ukendt tid", "EditFeeModal.SelectPreset.ariaLabel": "<PERSON><PERSON><PERSON>g gebyrindstilling", "EditFeeModal.SelectPreset.fast": "<PERSON><PERSON>", "EditFeeModal.SelectPreset.normal": "Normal", "EditFeeModal.SelectPreset.slow": "Langsom", "EditFeeModal.ariaLabel": "Rediger netværksgebyr", "FailedSimulation.Confirmation.Item.subtitle": "Vi oplevede en intern fejl", "FailedSimulation.Confirmation.Item.title": "Kunne ikke simulere transaktionen", "FailedSimulation.Confirmation.subtitle": "Er du sikker på, du vil fortsætte?", "FailedSimulation.Confirmation.title": "Du underskriver i blinde", "FailedSimulation.Title": "Simulationsfejl", "FailedSimulation.footer.subtitle": "Vi oplevede en intern fejl", "FailedSimulation.footer.title": "Kunne ikke simulere transaktionen", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Kræver {amount} for at sende transaktionen", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "Kan tage lang tid at behandle", "FeeForecastWidget.networkFee": "Netværksgebyr", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "Vi kunne ikke beregne netværksgebyret", "FeeForecastWidget.subsequentFailed.message": "Estimater kan være gamle, seneste opdat. fejlede", "FeeForecastWidget.unknownDuration": "Ukendt", "FeeForecastWidget.unknownFee": "Ukendt", "GasCurrencySelector.balance": "Saldo: {balance}", "GasCurrencySelector.networkFee": "Netværksgebyr", "GasCurrencySelector.payNetworkFeesUsing": "Betal netværksgebyrer med", "GasCurrencySelector.removeDefaultGasToken.description": "<PERSON><PERSON> gebyrer fra den største saldo", "GasCurrencySelector.removeDefaultGasToken.title": "Automatisk gebyrhåndtering", "GasCurrencySelector.save": "Gem", "GoogleDriveBackup.BeforeYouBegin.first_point": "<PERSON><PERSON> jeg glemmer min <PERSON>-adgangskode, mister jeg mine midler for altid", "GoogleDriveBackup.BeforeYouBegin.second_point": "<PERSON><PERSON> jeg mister adgangen til mit Google Drev eller ændrer min gendannelsesfil, mister jeg mine midler for altid", "GoogleDriveBackup.BeforeYouBegin.subtitle": "<PERSON><PERSON><PERSON> og accept<PERSON>r venligst følgende om privat opbevaring:", "GoogleDriveBackup.BeforeYouBegin.third_point": "<PERSON>eal kan ikke hjælpe mig med at gendanne min Zeal-adgangskode eller min adgang til Google Drev", "GoogleDriveBackup.BeforeYouBegin.title": "<PERSON><PERSON> du begynder", "GoogleDriveBackup.loader.subtitle": "Godkend venligst anmodningen på Google Drev for at uploade din gendannelsesfil", "GoogleDriveBackup.loader.title": "Venter på godkendelse...", "GoogleDriveBackup.success": "Backup lykkedes 🎉", "MonitorOffRamp.overServiceTime": "De fleste overførsler gennemføres inden for {estimated_time}, men nogle gange kan det tage længere tid på grund af ekstra kontrol. Det er normalt, og dine penge er i sikkerhed, mens dette sker.{br}{br}Hvis transaktionen ikke er gennemført inden for {support_soft_deadline}, bedes du {contact_support}", "MonitorOnRamp.contactSupport": "Kontakt support", "MonitorOnRamp.from": "<PERSON>a", "MonitorOnRamp.fundsReceived": "<PERSON><PERSON> mod<PERSON>t", "MonitorOnRamp.overServiceTime": "De fleste overførsler gennemføres inden for {estimated_time}, men nogle gange kan det tage længere tid på grund af yderligere kontrol. Dette er normalt, og dine midler er i sikkerhed, mens kontrollen udføres.{br}{br}Hvis transaktionen ikke er gennemført inden for {support_soft_deadline}, bedes du {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Sender til din wallet", "MonitorOnRamp.to": "Til", "MonitorOnRamp.waitingForTransfer": "Venter på, at du overfører midler", "NftCollectionCheck.failed.subtitle": "Samlingen er ikke verificeret på <source></source>", "NftCollectionCheck.failed.title": "Samlingen er ikke verificeret", "NftCollectionCheck.passed.subtitle": "Samlingen er verificeret på <source></source>", "NftCollectionCheck.passed.title": "Samlingen er verificeret", "NftCollectionInfo.entireCollection": "<PERSON><PERSON>", "NoSigningKeyStore.createAccount": "<PERSON><PERSON> konto", "NonceRangeError.biggerThanCurrent.message": "Transaktionen vil sidde fast", "NonceRangeError.lessThanCurrent.message": "Transaktionen vil fejle", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "Nonce for høj, vil sidde fast. Sænk den.", "NonceRangeErrorPopup.biggerThanCurrent.title": "Transaktionen vil sidde fast", "P2pReceiverTypeCheck.failed.subtitle": "Sender du til den korrekte adresse?", "P2pReceiverTypeCheck.failed.title": "Modtager er en smart contract, ikke en wallet", "P2pReceiverTypeCheck.passed.subtitle": "Normalt sender du aktiver til andre wallets", "P2pReceiverTypeCheck.passed.title": "Modtager er en wallet", "PasswordCheck.title": "Indtast adgangskode", "PasswordChecker.subtitle": "Indtast adgangskode for at bekræfte dig.", "PermitExpirationCheck.failed.subtitle": "Hold den kort og kun så længe som nødvendigt", "PermitExpirationCheck.failed.title": "Lang udløbstid", "PermitExpirationCheck.passed.subtitle": "<PERSON><PERSON> længe en app kan bruge dine tokens", "PermitExpirationCheck.passed.title": "Udløbstid er ikke for lang", "PrivateKeyValidationError.moreThanMaximumWords": "Maks. {count} ord", "PrivateKeyValidationError.notValidPrivateKey": "Dette er ikke en gyldig privat nøgle", "PrivateKeyValidationError.secretPhraseIsInvalid": "Hemmelig sætning er ikke gyldig", "PrivateKeyValidationError.wordMisspelledOrInvalid": "Ord #{index} er stavet forkert eller ugyldigt", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} ord} other{{count} ord}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "<PERSON>e nøgler er krypteret lokalt på enheden.", "SecretPhraseReveal.header": "Skriv din hemmelige sætning ned", "SecretPhraseReveal.hint": "Del ikke din sætning med nogen. Opbevar den sikkert og offline", "SecretPhraseReveal.skip.subtitle": "Du kan gøre det senere, men hvis du mister denne enhed, før du skriver din sætning ned, mister du alle midler, du har tilføjet til denne tegnebog", "SecretPhraseReveal.skip.takeTheRisk": "Tag risikoen", "SecretPhraseReveal.skip.title": "Springe nedskrivning over?", "SecretPhraseReveal.skip.writeDown": "Skriv ned", "SecretPhraseReveal.skipForNow": "Spring over", "SecretPhraseReveal.subheader": "Skriv den venligst ned, og opbevar den sikkert offline. Bagefter beder vi dig om at bekræfte den.", "SecretPhraseReveal.verify": "Bekræft", "SelectCurrency.tokens": "Tokens", "SelectCurrency.tokens.emptyState": "Vi fandt ingen tokens", "SelectRoute.slippage": "Slippage {slippage}", "SelectRoutes.emptyState": "Vi fandt ingen ruter for dette swap", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Forbind wallet", "SendERC20.labelAddress.inputPlaceholder": "Navn på tegnebog", "SendERC20.labelAddress.subtitle": "<PERSON><PERSON> tegnebogen et navn, så du kan finde den.", "SendERC20.labelAddress.title": "Navngiv denne te<PERSON>g", "SendERC20.send_to": "Send til", "SendERC20.tokens": "Tokens", "SendOrReceive.bankTransfer.primaryText": "Bankoverførsel", "SendOrReceive.bankTransfer.shortText": "Gratis og øjeblikkelig ind- og udbetaling", "SendOrReceive.bridge.primaryText": "Bridge", "SendOrReceive.bridge.shortText": "Overfør tokens mellem netværk", "SendOrReceive.receive.primaryText": "Modtag", "SendOrReceive.receive.shortText": "Modtag tokens eller samleobjekter", "SendOrReceive.send.primaryText": "Send", "SendOrReceive.send.shortText": "Send tokens til enhver adresse", "SendOrReceive.swap.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.swap.shortText": "Swap mellem tokens", "SendSafeTransaction.Confirm.loading": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Krypter en gendannelsesfil med adgangskode", "SetupRecoveryKit.google.subtitle": "Synkroniseret {date}", "SetupRecoveryKit.google.title": "Google Drev-backup", "SetupRecoveryKit.subtitle": "Du skal bruge mindst én metode til at gendanne din konto, hvis du afinstallerer Zeal eller skifter enhed", "SetupRecoveryKit.title": "Konfigurer gendannelsessæt", "SetupRecoveryKit.writeDown.subtitle": "Sk<PERSON>v hemmelig sætning ned", "SetupRecoveryKit.writeDown.title": "<PERSON> backup", "Sign.CheckSafeDeployment.activate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sign.CheckSafeDeployment.subtitle": "<PERSON><PERSON><PERSON> du kan logge ind i en app eller underskrive en off-chain-besked, skal du aktivere din enhed på dette netværk. <PERSON><PERSON>, efter du har installeret eller gendannet en Smart Wallet.", "Sign.CheckSafeDeployment.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> enhed på dette netværk", "Sign.Simuation.Skeleton.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "SignMessageSafetyCheckResult.passed": "Sikkerhedstjek bestået", "SignMessageSafetyChecksPopup.title.permits": "Sikkerhedstjek af Permit", "SimulationFailedConfirmation.subtitle": "Vi simulerede denne transaktion og fandt et problem, der vil få den til at fejle. Du kan indsende transaktionen, men den vil sandsynligvis fejle, og du kan miste dit netværksgebyr.", "SimulationFailedConfirmation.title": "Transaktionen vil sandsynligvis fejle", "SimulationNotSupported.Title": "Simulering ikke{br}understøttet på{br}{network}", "SimulationNotSupported.footer.subtitle": "Du kan stadig indsende denne transaktion", "SimulationNotSupported.footer.title": "Simulering understøttes ikke", "SlippagePopup.custom": "Brugerdefineret", "SlippagePopup.presetsHeader": "Swap-slippage", "SlippagePopup.title": "Slippage-inds<PERSON>linger", "SmartContractBlacklistCheck.failed.subtitle": "Ondsindede rapporter fra <source></source>", "SmartContractBlacklistCheck.failed.title": "Kont<PERSON><PERSON> er blacklisted", "SmartContractBlacklistCheck.passed.subtitle": "Ingen ondsindede rapporter fra <source></source>", "SmartContractBlacklistCheck.passed.title": "Kontrakten er ikke blacklisted", "SuspiciousCharactersCheck.Failed.subtitle": "Dette er en almindelig phishing-taktik", "SuspiciousCharactersCheck.Failed.title": "<PERSON><PERSON> tjekker for almindelige phishing-mønstre", "SuspiciousCharactersCheck.Passed.subtitle": "<PERSON><PERSON> tjekker for phishing-forsøg", "SuspiciousCharactersCheck.Passed.title": "Adressen har ingen usædvanlige tegn", "SuspiciousCharactersCheck.failed.statusButton.label": "<PERSON><PERSON><PERSON> har usædvanlige tegn ", "TokenVerificationCheck.failed.subtitle": "Token er ikke på listen hos <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} er ikke verificeret af CoinGecko", "TokenVerificationCheck.passed.subtitle": "Token er på listen hos <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} er verificeret af CoinGecko", "TopupDapp.MonitorTransaction.success.splash": "Gennemført", "TransactionSafetyCheckResult.passed": "Sikkerhedstjek bestået", "TransactionSimulationCheck.failed.subtitle": "Fejl: {errorMessage}", "TransactionSimulationCheck.failed.title": "Transaktionen vil sandsynligvis fejle", "TransactionSimulationCheck.passed.subtitle": "Simulering udført med <source></source>", "TransactionSimulationCheck.passed.title": "Forhåndsvisning af transaktion lykkedes", "TrezorError.trezor_action_cancelled.action": "Luk", "TrezorError.trezor_action_cancelled.subtitle": "Du afviste transaktionen på din hardware-wallet", "TrezorError.trezor_device_used_elsewhere.action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "<PERSON><PERSON><PERSON> for at lukke alle andre <PERSON> sessioner og prøv at synkronisere din Trezor igen", "TrezorError.trezor_method_cancelled.action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "<PERSON><PERSON><PERSON> for at tillade Trezor at eksportere wallets til Zeal", "TrezorError.trezor_permissions_not_granted.action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "<PERSON><PERSON> ven<PERSON><PERSON>t Zeal tilladelse til at se alle wallets", "TrezorError.trezor_pin_cancelled.action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "Session annulleret på enheden", "TrezorError.trezor_popup_closed.action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "Trezor-dialogboksen lukkede uventet", "TrxLikelyToFail.lessThanEstimatedGas.message": "Transaktionen vil fejle", "TrxLikelyToFail.lessThanMinimumGas.message": "Transaktionen vil fejle", "TrxLikelyToFail.lessThanSuggestedGas.message": "<PERSON><PERSON> fejle", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "Transaktionens gasgrænse er for lav. Forøg gasgrænsen til den foreslåede grænse for at undgå fejl.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "Transaktionen vil sandsynligvis fejle", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "Gasgrænsen er lavere end den estimerede gas. Forøg gasgrænsen til den foreslåede grænse.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "Transaktionen vil fejle", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "Maks. basisgebyr er lavere end det nuværende basisgebyr. Forøg maks. basisgebyr for at undgå, at transaktionen sidder fast.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "Transaktionen vil sidde fast", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "Transaktionens maks. gebyr er for lavt. Forøg maks. gebyr for at undgå, at transaktionen sidder fast.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "Transaktionen vil sidde fast", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "Prioritetsgebyret er lavere end anbefalet. Forøg prioritetsgebyret for at fremskynde transaktionen.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "Transaktionen kan tage lang tid at gennemføre", "UnsupportedMobileNetworkLayout.gotIt": "Forstået!", "UnsupportedMobileNetworkLayout.subtitle": "Du kan ikke lave transaktioner eller signere beskeder på netværk med id {networkHexId} med mobilversionen af Zeal endnu{br}{br}Brug browser-appen. Vi arbejder på det 🚀", "UnsupportedMobileNetworkLayout.title": "Netværk ikke understøttet på mobil.", "UnsupportedSafeNetworkLayout.subtitle": "Du kan ikke lave transaktioner eller underskrive beskeder på {network} med en Zeal Smart Wallet{br}{br}Skift til et understøttet netværk eller brug en Legacy wallet.", "UnsupportedSafeNetworkLayoutk.title": "Netværk understøttes ikke for Smart Wallet", "UserConfirmationPopup.goBack": "<PERSON><PERSON><PERSON>", "UserConfirmationPopup.submit": "Fortsæt", "ViewPrivateKey.header": "Privat nøgle", "ViewPrivateKey.hint": "Del ikke din private nøgle med nogen. Opbevar den sikkert og offline", "ViewPrivateKey.subheader.mobile": "Tryk for at se din private nøgle", "ViewPrivateKey.subheader.web": "Hold <PERSON><PERSON><PERSON> over for at se din private nøgle", "ViewPrivateKey.unblur.mobile": "Tryk for at se", "ViewPrivateKey.unblur.web": "Hold over for at se", "ViewSecretPhrase.PasswordChecker.subtitle": "Indtast din adgangskode for at kryptere gendannelsesfilen. Du skal huske den i fremtiden.", "ViewSecretPhrase.done": "<PERSON><PERSON><PERSON><PERSON>", "ViewSecretPhrase.header": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "ViewSecretPhrase.hint": "Del ikke din sætning med nogen. Opbevar den sikkert og offline", "ViewSecretPhrase.subheader.mobile": "Tryk for at se din hemmelige sætning", "ViewSecretPhrase.subheader.web": "Hold <PERSON><PERSON><PERSON> over for at se din hemmelige sætning", "ViewSecretPhrase.unblur.mobile": "Tryk for at se", "ViewSecretPhrase.unblur.web": "Hold over for at se", "account-details.monerium": "Overfø<PERSON><PERSON> via Monerium, et reguleret EMI. <link><PERSON><PERSON>s mere</link>", "account-details.unblock": "Overfø<PERSON><PERSON> sker via Unblock, en autoriseret og registreret udbyder af vekslings- og depottjenester. <link><PERSON><PERSON>s mere</link>", "account-selector.empty-state": "Ingen tegnebøger fundet", "account-top-up.select-currency.title": "Tokens", "account.accounts_not_found": "Vi kunne ikke finde nogen tegnebøger", "account.accounts_not_found_search_valid_address": "Tegnebogen er ikke på din liste", "account.add.create_new_secret_phrase": "<PERSON><PERSON> hem<PERSON>ig sætning", "account.add.create_new_secret_phrase.subtext": "En ny hemmelig sætning på 12 ord", "account.add.fromRecoveryKit.fileNotFound": "Vi kunne ikke finde din fil", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "<PERSON><PERSON><PERSON><PERSON> igen", "account.add.fromRecoveryKit.fileNotFound.explanation": "<PERSON><PERSON><PERSON>, om du er på kontoen med Zeal Backup", "account.add.fromRecoveryKit.fileNotValid": "Gendannelsesfil er ikke gyldig", "account.add.fromRecoveryKit.fileNotValid.explanation": "Filen er enten forkert type eller ændret", "account.add.import_secret_phrase": "<PERSON><PERSON>rt<PERSON><PERSON> s<PERSON>t<PERSON>", "account.add.import_secret_phrase.subtext": "<PERSON>ret<PERSON><PERSON> p<PERSON>, Metamask eller andre", "account.add.select_type.add_hardware_wallet": "Hardware-tegnebog", "account.add.select_type.existing_smart_wallet": "Eksisterende Smart Wallet", "account.add.select_type.private_key": "Privat nøgle", "account.add.select_type.seed_phrase": "Seed-sætning", "account.add.select_type.title": "Import<PERSON><PERSON>", "account.add.select_type.zeal_recovery_file": "Zeal-gendannelsesfil", "account.add.success.title": "Ny wallet oprettet 🎉", "account.addLabel.header": "Giv din tegnebog et navn", "account.addLabel.labelError.labelAlreadyExist": "Nav<PERSON> findes allerede. Prøv et andet.", "account.addLabel.labelError.maxStringLengthExceeded": "Maks<PERSON>lt antal tegn er nået", "account.add_active_wallet.primary_text": "Til<PERSON><PERSON>j te<PERSON>g", "account.add_active_wallet.short_text": "<PERSON><PERSON>, tils<PERSON> eller importér te<PERSON>g", "account.add_from_ledger.success": "Wallets føjet til Zeal", "account.add_tracked_wallet.primary_text": "Tilføj skrivebeskyttet tegnebog", "account.add_tracked_wallet.short_text": "Se portefølje og aktivitet", "account.button.unlabelled-wallet": "Unavngiven tegnebog", "account.create_wallet": "Opret wallet", "account.label.edit.title": "Rediger navn på tegnebog", "account.recoveryKit.selectBackupFile.fileDate": "Oprettet {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "Gendannelsesfil er ikke gyldig", "account.recoveryKit.selectBackupFile.subtitle": "<PERSON><PERSON><PERSON><PERSON> den gendannelsesfil, du vil gendanne", "account.recoveryKit.selectBackupFile.title": "Gendannelsesfil", "account.recoveryKit.success.recoveryFileFound": "Gendannelsesfil fundet 🎉", "account.select_type_of_account.create_eoa.short": "Traditionel tegnebog for eksperter", "account.select_type_of_account.create_eoa.title": "<PERSON><PERSON> tegnebog med seed-sætning", "account.select_type_of_account.create_safe_wallet.title": "Opret Smart Wallet", "account.select_type_of_account.existing_smart_wallet": "Eksisterende Smart wallet", "account.select_type_of_account.hardware_wallet": "Hardware wallet", "account.select_type_of_account.header": "Til<PERSON><PERSON>j te<PERSON>g", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Privat nøgle / Seed phrase", "account.select_type_of_account.read_only_wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "account.select_type_of_account.read_only_wallet.short": "Se en hvilken som helst portefølje", "account.topup.title": "Indsæt penge på <PERSON>", "account.view.error.refreshAssets": "Opdater", "account.widget.refresh": "<PERSON><PERSON><PERSON><PERSON>", "account.widget.settings": "<PERSON><PERSON><PERSON><PERSON>", "accounts.view.copied-text": "<PERSON><PERSON><PERSON> {formattedAddress}", "accounts.view.copiedAddress": "<PERSON><PERSON><PERSON> {formattedAddress}", "action.accept": "Accepter", "action.accpet": "Accepter", "action.allow": "<PERSON><PERSON>", "action.back": "Tilbage", "action.cancel": "<PERSON><PERSON><PERSON>", "action.card-activation.title": "Aktiver kort", "action.claim": "Indløs", "action.close": "Luk", "action.complete-steps": "<PERSON><PERSON><PERSON><PERSON><PERSON> trin", "action.confirm": "Bekræft", "action.continue": "Fortsæt", "action.copy-address-understand": "OK – <PERSON><PERSON><PERSON><PERSON>", "action.deposit": "Indbetal", "action.done": "<PERSON><PERSON><PERSON><PERSON>", "action.dontAllow": "<PERSON>ad ikke", "action.edit": "rediger", "action.email-required": "Indtast e-mail", "action.enterPhoneNumber": "Indtast telefonnummer", "action.expand": "<PERSON><PERSON><PERSON>", "action.fix": "Ret", "action.getStarted": "Kom i gang", "action.got_it": "Forstået", "action.hide": "Skjul", "action.import": "Importer", "action.import-keys": "Importer nøgler", "action.importKeys": "Importer nøgler", "action.minimize": "<PERSON><PERSON>", "action.next": "<PERSON><PERSON><PERSON>", "action.ok": "OK", "action.reduceAmount": "Reducer til maks.", "action.refreshWebsite": "Genindlæs webside", "action.remove": "<PERSON><PERSON><PERSON>", "action.remove-account": "<PERSON><PERSON><PERSON> konto", "action.requestCode": "An<PERSON>d om kode", "action.resend_code": "Gensend kode", "action.resend_code_with_time": "Gensend kode {time}", "action.retry": "<PERSON><PERSON><PERSON><PERSON> igen", "action.reveal": "Vis", "action.save": "Gem", "action.save_changes": "Gem RPC", "action.search": "<PERSON><PERSON><PERSON>", "action.seeAll": "Se alle", "action.select": "<PERSON><PERSON><PERSON><PERSON>", "action.send": "Send", "action.skip": "Spring over", "action.submit": "Indsend", "action.understood": "<PERSON><PERSON>", "action.update": "Opdater", "action.update-gnosis-pay-owner.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON> trin", "action.zeroAmount": "Indtast beløb", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Samleobjekter", "action_bar_title.tokens": "Tokens", "action_bar_title.transaction_request": "Anmodning om transaktion", "activate-monerium.loading": "Opretter din personlige konto", "activate-monerium.success.title": "Monerium aktiveret", "activate-physical-card-widget.subtitle": "Levering kan tage 3 uger", "activate-physical-card-widget.title": "Aktivér fysisk kort", "activate-smart-wallet.title": "Aktivér wallet", "active_and_tracked_wallets.title": "Zeal dækker alle dine gebyrer på {network}, så du kan handle gratis!", "activity.approval-amount.revoked": "Tilbagekaldt", "activity.approval-amount.unlimited": "Ubegrænset", "activity.approval.approved_for": "<PERSON><PERSON><PERSON> for", "activity.approval.approved_for_with_target": "Godkendt {approvedTo}", "activity.approval.revoked_for": "Tilbagekaldt for", "activity.bank.serviceProvider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.bridge.serviceProvider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.cashback.period": "Cashback-periode", "activity.filter.card": "<PERSON><PERSON>", "activity.rate": "<PERSON><PERSON>", "activity.receive.receivedFrom": "Modtaget fra", "activity.send.sendTo": "Sendt til", "activity.smartContract.unknown": "Ukendt kontrakt", "activity.smartContract.usingContract": "<PERSON>ruger kontrakt", "activity.subtitle.pending_timer": "{timerString} Afventer", "activity.title.arbitrary_smart_contract_interaction": "{function} på {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Ukendt kontraktinteraktion", "activity.title.bridge.from": "Bridge fra {token}", "activity.title.bridge.to": "Bridge til {token}", "activity.title.buy": "<PERSON><PERSON><PERSON> {asset}", "activity.title.card_owners_updated": "Kortejere opdateret", "activity.title.card_spend_limit_updated": "Kortets forbrugsgrænse er angivet", "activity.title.cashback_deposit": "Indbetaling til Cashback", "activity.title.cashback_reward": "Modtaget Cashback", "activity.title.cashback_withdraw": "Hævning fra Cashback", "activity.title.claimed_reward": "<PERSON><PERSON><PERSON>", "activity.title.deployed_smart_wallet_gnosis": "Konto oprettet", "activity.title.deposit_from_bank": "Indbetaling fra bank", "activity.title.deposit_into_card": "Indbetaling til kort", "activity.title.deposit_into_earn": "Indbetal til {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} på {smartContract}", "activity.title.from": "Fra {sender}", "activity.title.pendidng_areward_claim": "<PERSON><PERSON>", "activity.title.pendidng_breward_claim": "<PERSON><PERSON>", "activity.title.recharge_disabledh": "Automatisk kortopfyldning deaktiveret", "activity.title.recharge_set": "Genopfyldningsmål angivet", "activity.title.recovered_smart_wallet_gnosis": "Installation af ny enhed", "activity.title.send_pending": "Til {receiver}", "activity.title.send_to_bank": "Til bank", "activity.title.swap": "<PERSON><PERSON><PERSON> {token}", "activity.title.to": "Til {receiver}", "activity.title.withdraw_from_card": "Hævning fra kort", "activity.title.withdraw_from_earn": "Hæv fra {earn}", "activity.transaction.networkFees": "Netværksgebyrer", "activity.transaction.state": "Gennemført transaktion", "activity.transaction.state.completed": "Gennemført transaktion", "activity.transaction.state.failed": "Mislykket transaktion", "add-account.section.import.header": "Importer", "add-another-card-owner": "Tilføj endnu en kortejer", "add-another-card-owner.Recommended.footnote": "<PERSON><PERSON><PERSON><PERSON><PERSON> som medejer af dit kort", "add-another-card-owner.Recommended.primaryText": "<PERSON><PERSON><PERSON><PERSON><PERSON> til Gnosis Pay", "add-another-card-owner.recommended": "An<PERSON><PERSON><PERSON>", "add-owner.confirmation.subtitle": "<PERSON>f sikkerhedsgrunde tager ændringer i indstillinger 3 minutter. Dit kort er midlertidigt spærret imens, og betalinger er ikke mulige.", "add-owner.confirmation.title": "Dit kort vil blive spærret i 3 min., mens indstillingerne opdateres", "add-readonly-signer-if-not-exist.error.already_in_use.title": "Kan ikke tilføje <PERSON>, den er allerede i brug", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "Prøv en anden wallet", "add.account.backup.decrypt.success": "Tegnebog gendannet", "add.account.backup.password.passwordIncorrectMessage": "Adgangskoden er forkert", "add.account.backup.password.subtitle": "Indtast kodeordet til din gendannelsesfil.", "add.account.backup.password.title": "Indtast adgangskode", "add.account.google.login.subtitle": "Godkend i Google Drive for at hente fil.", "add.account.google.login.title": "Venter på godkendelse ...", "add.readonly.already_added": "Wallet er allerede tilføjet", "add.readonly.continue": "Fortsæt", "add.readonly.empty": "Indtast en adresse eller ENS", "addBankRecipient.title": "Tilføj bankmodtager", "add_funds.deposit_from_bank_account": "Indbetal fra bankkonto", "add_funds.from_another_wallet": "Fra en anden wallet", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Forbind til top-up dApp", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Forbind en wallet til Zeal top-up dApp'en, og send hurtigt penge til din wallet.", "add_funds.from_crypto_wallet.header": "Fra en anden wallet", "add_funds.from_crypto_wallet.header.show_wallet_address": "Vis din wallet-adresse", "add_funds.from_exchange.header": "Send fra en børs", "add_funds.from_exchange.header.copy_wallet_address": "<PERSON><PERSON><PERSON><PERSON>-ad<PERSON>e", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance osv.", "add_funds.from_exchange.header.open_exchange": "Åbn børsens app eller website", "add_funds.from_exchange.header.selected_token": "Send {token} til <PERSON><PERSON>", "add_funds.from_exchange.header.selected_token.subtitle": "På {network}", "add_funds.from_exchange.header.send_selected_token": "Send understøttet token", "add_funds.from_exchange.header.send_selected_token.subtitle": "Vælg understøttet token & netværk", "add_funds.import_wallet": "Importér e<PERSON>e crypto-wallet", "add_funds.title": "Indsæt penge på din konto", "add_funds.transfer_from_exchange": "Overførsel fra en børs", "address.add.header": "Se din wallet i Zeal{br}med skrivebeskyttet tilstand", "address.add.subheader": "Indtast din adresse eller ENS for at se dine aktiver på alle EVM-netværk samlet ét sted. Opret eller importér flere wallets senere.", "address_book.change_account.bank_transfers.header": "Bankmodtagere", "address_book.change_account.bank_transfers.primary": "Bankmodtager", "address_book.change_account.cta": "<PERSON><PERSON><PERSON><PERSON>", "address_book.change_account.search_placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> eller søg efter adresse", "address_book.change_account.tracked_header": "Skrivebeskyttede tegnebøger", "address_book.change_account.wallets_header": "Aktive tegnebøger", "app-association-check-failed.modal.cta": "<PERSON><PERSON><PERSON><PERSON> igen", "app-association-check-failed.modal.subtitle": "Prøv venligst igen. Forbindelsesproblemer forsinker hentning af dine Passkeys. Hvis problemet fortsætter, genstart Zeal og prøv igen.", "app-association-check-failed.modal.subtitle.creation": "Prøv venligst igen. Forbindelsesproblemer forsinker oprettelsen af din Passkey. Hvis problemet fortsætter, genstart Zeal og prøv igen.", "app-association-check-failed.modal.title.creation": "Din enhed kunne ikke oprette en passkey", "app-association-check-failed.modal.title.signing": "Din enhed kunne ikke indlæse passkeys", "app.app_protocol_group.borrowed_tokens": "Lånte tokens", "app.app_protocol_group.claimable_amount": "Beløb til udbetaling", "app.app_protocol_group.health_rate": "Sundhedsrate", "app.app_protocol_group.lending": "<PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.locked_tokens": "Låste tokens", "app.app_protocol_group.nfts": "Samleobjekter", "app.app_protocol_group.reward_tokens": "Belønningstokens", "app.app_protocol_group.supplied_tokens": "Indskudte tokens", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "Optjeningstoken", "app.appsGroupHeader.discoverMore": "Opdag mere", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "<PERSON><PERSON><PERSON> eller indtast URL", "app.error-banner.cory": "<PERSON><PERSON><PERSON><PERSON>", "app.error-banner.retry": "<PERSON><PERSON><PERSON><PERSON> igen", "app.list_item.rewards": "<PERSON><PERSON><PERSON><PERSON> {value}", "app.position_details.health_rate.description": "<PERSON>t lån delt med værdien af sikkerheden.", "app.position_details.health_rate.title": "Hvad er sundhedsrate?", "approval.edit-limit.label": "Rediger forbrugsgrænse", "approval.permit_info": "Tilladelsesoplysninger", "approval.spend-limit.edit-modal.cancel": "<PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.limit-label": "Forbrugsgrænse", "approval.spend-limit.edit-modal.max-limit-error": "<PERSON><PERSON><PERSON>, høj grænse", "approval.spend-limit.edit-modal.revert": "<PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.set-to-unlimited": "Indstil til Ubegrænset", "approval.spend-limit.edit-modal.submit": "<PERSON><PERSON>", "approval.spend-limit.edit-modal.title": "<PERSON><PERSON> till<PERSON>", "approval.spend_limit_info": "Hvad er en forbrugsgrænse?", "approval.what_are_approvals": "Hvad er godkendelser?", "apps_list.page.emptyState": "Ingen aktive apps", "backpace.removeLastDigit": "<PERSON><PERSON><PERSON> sids<PERSON> ciffer", "backup-banner.backup_now": "Tag backup nu", "backup-banner.risk_losing_funds": "Tag backup nu, el<PERSON> risikerer du at miste dine midler", "backup-banner.title": "Wallet er ikke sikkerhedskopieret", "backupRecoverySmartWallet.noExportPrivateKeys": "Automatisk backup: <PERSON> gemmes som en passkey - ingen seed phrase eller private nøgler er nødvendige.", "backupRecoverySmartWallet.safeContracts": "Sikkerhed med flere nøgler: Zeal-wallets bruger Safe-kontrakter, så flere enheder kan godkende en transaktion. Der er ikke ét enkelt punkt, der kan fejle.", "backupRecoverySmartWallet.security": "Flere enheder: Du kan bruge din wallet på flere enheder med din passkey. Hver enhed får sin egen private nøgle.", "backupRecoverySmartWallet.showLocalPrivateKey": "Eksperttilstand: Du kan eksportere denne enheds private nøgle, bruge den i en anden wallet og forbinde på <SafeGlobal>https://safe.global</SafeGlobal>. <Key>Vis privat nøgle</Key>", "backupRecoverySmartWallet.storingKeys": "Synkroniseret med skyen: Din passkey gemmes sikkert i iCloud, Google Adgangskodeadministrator eller din adgangskodeadministrator.", "backupRecoverySmartWallet.title": "Smart Wallet backup & gendannelse", "balance-change.card.titile": "<PERSON><PERSON>", "balanceChange.pending": "<PERSON><PERSON><PERSON><PERSON>", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.change-deposit-receiver.subtitle": "Denne wallet vil modtage alle bankindbetalinger", "bank-transfer.change-deposit-receiver.title": "Indstil modtagende wallet", "bank-transfer.change-owner.subtitle": "Denne wallet bruges til login og gendannelse af din bankoverførselskonto", "bank-transfer.change-owner.title": "<PERSON><PERSON><PERSON>", "bank-transfer.configrm-change-deposit-receiver.subtitle": "<PERSON>e bank<PERSON><PERSON>, du sender til <PERSON>eal, vil blive modtaget af denne wallet.", "bank-transfer.configrm-change-deposit-receiver.title": "Skift modtagende wallet", "bank-transfer.configrm-change-owner.subtitle": "Er du sikker på, at du vil skifte kontoejer? Denne wallet bruges til login og gendannelse af din bankoverførselskonto.", "bank-transfer.configrm-change-owner.title": "Skift kontoejer", "bank-transfer.deposit.widget.status.complete": "Gennemført", "bank-transfer.deposit.widget.status.funds_received": "<PERSON><PERSON> mod<PERSON>t", "bank-transfer.deposit.widget.status.sending_to_wallet": "Sender til wallet", "bank-transfer.deposit.widget.status.transfer-on-hold": "<PERSON><PERSON><PERSON><PERSON><PERSON> sat på pause", "bank-transfer.deposit.widget.status.transfer-received": "Sender til wallet", "bank-transfer.deposit.widget.subtitle": "{from} til {to}", "bank-transfer.deposit.widget.title": "Indbetaling", "bank-transfer.intro.bulletlist.point_1": "Opsætning med Unblock", "bank-transfer.intro.bulletlist.point_2": "Overfør mellem EUR/GBP og mere end 10 tokens", "bank-transfer.intro.bulletlist.point_3": "0 % gebyr op til 5k $ m<PERSON><PERSON><PERSON><PERSON>, der<PERSON><PERSON> 0,2 %", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Sender til din bank", "bank-transfer.withdrawal.widget.status.in-progress": "Overførsel i gang", "bank-transfer.withdrawal.widget.status.on-hold": "<PERSON><PERSON><PERSON><PERSON><PERSON> sat på pause", "bank-transfer.withdrawal.widget.status.success": "Gennemført", "bank-transfer.withdrawal.widget.subtitle": "{from} til {to}", "bank-transfer.withdrawal.widget.title": "Udbetaling", "bank-transfers.bank-account-actions.remove-this-account": "<PERSON><PERSON><PERSON> denne konto", "bank-transfers.bank-account-actions.switch-to-this-account": "<PERSON>ft til denne konto", "bank-transfers.deposit.fees-for-less-than-5k": "Gebyrer for 5k $ eller mindre", "bank-transfers.deposit.fees-for-more-than-5k": "Gebyrer for mere end 5k $", "bank-transfers.set-receiving-bank.title": "Indstil modtagende bank", "bank-transfers.settings.account_owner": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfers.settings.receiver_of_bank_deposits": "Modtager af bankindbetalinger", "bank-transfers.settings.receiver_of_withdrawals": "Modtager af udbetalinger", "bank-transfers.settings.registered_email": "Registreret e-mail", "bank-transfers.settings.title": "<PERSON><PERSON><PERSON><PERSON> for bankoverførsel", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} konto", "bank-transfers.setup.bank-account": "Bankkonto", "bankTransfer.withdraw.max_loading": "Maks.: {amount}", "bank_details_do_not_match.got_it": "Forstået", "bank_details_do_not_match.subtitle": "Registrerings- og kontonummeret matcher ikke. Dobbelttjek, at oplysningerne er korrekte, og prøv igen.", "bank_details_do_not_match.title": "Bankoplysningerne matcher ikke", "bank_tranfsers.select_country_of_residence.country_not_supported": "Bankoverførsler understøttes desværre ikke i {country} endnu", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Åbn din bank-app", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Send {fiatCurrencyCode} til din konto", "bank_transfer.deposit.header": "{fullName}s personlige konto&nbsp;oplysninger", "bank_transfer.kyc_status_widget.subtitle": "Bankoverfø<PERSON><PERSON>", "bank_transfer.kyc_status_widget.title": "Bekræfter identitet", "bank_transfer.personal_details.date_of_birth": "Fødselsdato", "bank_transfer.personal_details.date_of_birth.invalid_format": "<PERSON><PERSON>ld<PERSON> dato", "bank_transfer.personal_details.date_of_birth.too_young": "Du skal være mindst 18 år gammel", "bank_transfer.personal_details.first_name": "Fornavn", "bank_transfer.personal_details.last_name": "Efternavn", "bank_transfer.personal_details.title": "<PERSON><PERSON>", "bank_transfer.reference.label": "Reference (valgfri)", "bank_transfer.reference_message": "<PERSON>t fra <PERSON>", "bank_transfer.residence_details.address": "<PERSON> ad<PERSON>e", "bank_transfer.residence_details.city": "By", "bank_transfer.residence_details.country_of_residence": "Bopælsland", "bank_transfer.residence_details.country_placeholder": "Land", "bank_transfer.residence_details.postcode": "Postnummer", "bank_transfer.residence_details.street": "Vejnavn", "bank_transfer.residence_details.your_residence": "<PERSON> b<PERSON>", "bank_transfers.choose-wallet.continue": "Fortsæt", "bank_transfers.choose-wallet.test": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.warning.subtitle": "Du kan kun tilknytte én wallet ad gangen. Du kan ikke ændre den tilknyttede wallet.", "bank_transfers.choose-wallet.warning.title": "<PERSON><PERSON><PERSON><PERSON> din wallet med omhu", "bank_transfers.choose_wallet.subtitle": "<PERSON><PERSON><PERSON><PERSON> wallet at forbinde med din bank. ", "bank_transfers.choose_wallet.title": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.continue": "Fortsæt", "bank_transfers.currency_is_currently_not_supported": "Fortsæt", "bank_transfers.deposit-header": "Indbetal", "bank_transfers.deposit.account-name": "Kontonavn", "bank_transfers.deposit.account-number-copied": "Kontonummer kopieret", "bank_transfers.deposit.amount-input": "Beløb til indbetaling", "bank_transfers.deposit.amount-output": "Destinationsbeløb", "bank_transfers.deposit.amount-output.error": "fejl", "bank_transfers.deposit.buttet-point.receive-crypto": "Modtag {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "Fortsæt", "bank_transfers.deposit.currency-not-supported.subtitle": "Bankindbetalinger fra {code} er blevet deaktiveret indtil videre.", "bank_transfers.deposit.currency-not-supported.title": "{code} indbetalinger understøttes ikke i øjeblikket", "bank_transfers.deposit.default-token.balance": "Saldo {amount}", "bank_transfers.deposit.deposit-header": "Indbetal", "bank_transfers.deposit.enter_amount": "Indtast beløb", "bank_transfers.deposit.iban-copied": "IBAN kopieret", "bank_transfers.deposit.increase-amount": "Minimumsoverfø<PERSON><PERSON> er {limit}", "bank_transfers.deposit.loading": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.max-limit-reached": "Beløbet overstiger den maksimale overførselsgrænse", "bank_transfers.deposit.modal.kyc.button-text": "Kom i gang", "bank_transfers.deposit.modal.kyc.text": "For at bekræfte din identitet skal vi bruge nogle oplysninger og dokumenter. Det tager normalt kun et par minutter.", "bank_transfers.deposit.modal.kyc.title": "Bekræft din identitet for højere grænser", "bank_transfers.deposit.reduce_amount": "<PERSON><PERSON><PERSON> be<PERSON>", "bank_transfers.deposit.show-account.account-number": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Registreringsnummer", "bank_transfers.deposit.sort-code-copied": "Registreringsnummer kopieret", "bank_transfers.deposit.withdraw-header": "Udbetal", "bank_transfers.failed_to_load_fee": "Ukendt", "bank_transfers.fees": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.increase-amount": "Minimumsoverfø<PERSON><PERSON> er {limit}", "bank_transfers.insufficient-funds": "Utilstrækkelig saldo", "bank_transfers.select_country_of_residence.title": "Hvor bor du?", "bank_transfers.setup.cta": "Opsæt bankoverførsler", "bank_transfers.setup.enter-amount": "Indtast beløb", "bank_transfers.source_of_funds.form.business_income": "Indkomst fra virksomhed", "bank_transfers.source_of_funds.form.other": "And<PERSON>", "bank_transfers.source_of_funds.form.pension": "Pension", "bank_transfers.source_of_funds.form.salary": "<PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.title": "<PERSON><PERSON> dine penge kommer fra", "bank_transfers.source_of_funds_description.placeholder": "Beskriv hvor pengene kommer fra...", "bank_transfers.source_of_funds_description.title": "<PERSON><PERSON><PERSON> os mere om hvor pengene kommer fra", "bank_transfers.withdraw-header": "Udbetal", "bank_transfers.withdraw.amount-input": "Beløb til udbetaling", "bank_transfers.withdraw.max-limit-reached": "Beløbet overstiger den maksimale overførselsgrænse", "bank_transfers.withdrawal.verify-id": "<PERSON><PERSON><PERSON> be<PERSON>", "banner.above_maximum_limit.maximum_input_limit_exceeded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "banner.above_maximum_limit.maximum_limit_per_deposit": "Dette er maksimumgrænsen pr. indbetaling", "banner.above_maximum_limit.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "banner.above_maximum_limit.title": "Reducer beløbet til {amount} eller mindre", "banner.above_maximum_limit.title.default": "<PERSON>uce<PERSON> be<PERSON>", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Minimumsbeløb ikke nået", "banner.below_minimum_limit.minimum_limit_for_token": "<PERSON><PERSON> er <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for denne token", "banner.below_minimum_limit.title": "<PERSON><PERSON>g beløbet til {amount} eller mere", "banner.below_minimum_limit.title.default": "<PERSON><PERSON><PERSON>", "breaard.in_porgress.info_popup.cta": "Brug penge for at tjene {earn}", "breaard.in_porgress.info_popup.footnote": "Brug af appen er accept af kampagnevilkår.", "breaward.in_porgress.info_popup.bullet_point_1": "Brug {remaining} inden for de næste {time} for at indløse denne belø<PERSON>.", "breaward.in_porgress.info_popup.bullet_point_2": "<PERSON>n gyldige Gnosis Pay-køb tæller.", "breaward.in_porgress.info_popup.bullet_point_3": "<PERSON><PERSON><PERSON><PERSON> sendes til din Zeal-konto.", "breaward.in_porgress.info_popup.header": "Tjen {earn}, ved at bruge {remaining}", "breward.celebration.for_spending": "For at bruge dit kort", "breward.dc25-eligible-celebration.for_spending": "Du er blandt de første {limit}!", "breward.dc25-non-eligible-celebration.for_spending": "Du var ikke blandt de første {limit} der brugte penge", "breward.expired_banner.earn_by_spending": "Tjen {earn} ved at bruge {amount}", "breward.expired_banner.reward_expired": "{earn} belø<PERSON> er udløbet", "breward.in_progress_banner.cta.title": "Brug penge for at tjene {earn}", "breward.ready_to_claim.error.try_again": "<PERSON><PERSON><PERSON><PERSON> igen", "breward.ready_to_claim.error_title": "<PERSON>nne ikke indløse belønning", "breward.ready_to_claim.in_progress": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>ø<PERSON>", "breward.ready_to_claim.youve_earned": "Du har tjent {earn}!", "breward_already_claimed.title": "Belønningen er allerede hævet. Kontakt support, hvis du ikke har modtaget den.", "breward_cannotbe_claimed.title": "Belønningen kan ikke hæves lige nu. Prøv venligst igen senere.", "bridge.best_return": "Rute med bedste afkast", "bridge.best_serivce_time": "Hurtigste rute", "bridge.check_status.complete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge.check_status.progress_text": "Bridger {from} til {to}", "bridge.remove_topup": "<PERSON><PERSON><PERSON> optankning", "bridge.request_status.completed": "Gennemført", "bridge.request_status.pending": "<PERSON><PERSON><PERSON><PERSON>", "bridge.widget.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge.widget.currencies": "{from} til {to}", "bridge_rote.widget.title": "Bridge", "browse.discover_more_apps": "Opdag flere apps", "browse.google_search_term": "Søg \"{searchTerm}\"", "brward.celebration.you_earned": "Du har tjent", "brward.expired_banner.subtitle": "<PERSON><PERSON> held næste gang", "brward.in_progress_banner.subtitle": "Udløber om {expiredInFormatted}", "buy": "<PERSON><PERSON><PERSON>", "buy.enter_amount": "Indtast beløb", "buy.loading": "Indlæser...", "buy.no_routes_found": "Ingen ruter fundet", "buy.not_enough_balance": "Ikke nok saldo", "buy.select-currency.title": "Vælg token", "buy.select-to-currency.title": "Køb tokens", "buy_form.title": "<PERSON><PERSON><PERSON>", "cancelled-card.create-card-button.primary": "Få nyt virtuelt kort", "cancelled-card.switch-card-button.primary": "Skift kort", "cancelled-card.switch-card-button.short-text": "Du har et andet aktivt kort", "card": "<PERSON><PERSON>", "card-add-cash.confirm-stage.banner.no-routes-found": "Ingen ruter fundet. Prøv en anden token eller et andet beløb", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "Du mangler {amount} mere {symbol} for at betale gebyrer", "card-add-cash.confirm-stage.banner.value-loss": "<PERSON> mister {loss} i værdi", "card-add-cash.confirm-stage.banner.value-loss.revert": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.cancel": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.continue": "Fortsæt", "card-add-cash.edit-stage.cta.enter-amount": "<PERSON><PERSON>", "card-add-cash.edit-stage.cta.reduce-to-max": "Brug maks.", "card-add-cash.edit-staget.banner.no-routes-found": "Ingen ruter fundet. Prøv en anden token eller et andet beløb", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Vi har sendt transaktionsanmodningen til din hardware wallet. Fortsæt venligst der.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "Signér på hardware wallet", "card-balance": "Saldo: {balance}", "card-cashback.status.title": "Indbetal til cashback", "card-copy-safe-address.copy_address": "<PERSON><PERSON><PERSON><PERSON>", "card-copy-safe-address.copy_address.done": "<PERSON><PERSON><PERSON>", "card-copy-safe-address.warning.description": "Denne adresse kan kun modtage {cardAsset} på Gnosis Chain. Send ikke aktiver fra andre netværk til denne adresse. De vil gå tabt.", "card-copy-safe-address.warning.header": "Send kun {cardAsset} på Gnosis Chain", "card-marketing-card.center.subtitle": "Valutagebyrer", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "Rente", "card-marketing-card.right.subtitle": "Velkomstgave", "card-marketing-card.title": "Europas højrente-VISA-kort", "card-marketing-tile.get-started": "Kom i gang", "card-select-from-token-title": "<PERSON><PERSON><PERSON><PERSON> token at sende fra", "card-top-up.banner.subtitle.completed": "Gennemført", "card-top-up.banner.subtitle.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "card-top-up.banner.subtitle.pending": "{timerString} Afventer", "card-top-up.banner.title": "Indbetaler {amount}", "card-topup.select-token.emptyState": "Vi fandt ingen tokens", "card.activate.card_number_not_valid": "Kortnummeret er ugyldigt. Tjek det, og prøv igen.", "card.activate.invalid_card_number": "Ugyldigt kortnummer.", "card.activation.activate_physical_card": "Aktivér fysisk kort", "card.add-cash.amount-to-withdraw": "Beløb til optankning", "card.add-from-earn-form.title": "<PERSON><PERSON>d kontanter på kortet", "card.add-from-earn-form.withdraw-to-card": "Fortsæt", "card.add-from-earn.amount-to-withdraw": "Beløb til udbetaling til kort", "card.add-from-earn.enter-amount": "Indtast beløb", "card.add-from-earn.loading": "<PERSON><PERSON><PERSON><PERSON>", "card.add-from-earn.max-label": "Saldo: {amount}", "card.add-from-earn.no-routes-found": "Ingen ruter fundet", "card.add-from-earn.not-enough-balance": "Ikke nok saldo", "card.add-owner.queued": "Tilføj ejer i kø", "card.add-to-wallet-flow.subtitle": "Betal direkte fra din wallet", "card.add-to-wallet.copy-card-number": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>t nedenfor", "card.add-to-wallet.title": "<PERSON><PERSON><PERSON> til {platformName} Wallet", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "ANNULLERET", "card.card-owner-not-found.disconnect-btn": "Frakobl kort fra Zeal", "card.card-owner-not-found.subtitle": "For at forts<PERSON>tte med at bruge dit Gnosis Pay-kort i Zeal skal du opdatere din kortejer for at genoprette forbindelsen", "card.card-owner-not-found.title": "Gentilkobl kort", "card.card-owner-not-found.update-owner-btn": "<PERSON><PERSON><PERSON><PERSON>", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} om {date}", "card.cashback.widgetNoCashback.subtitle": "Indbetal for at begynde at optjene", "card.cashback.widgetNoCashback.title": "Få op til {defaultPercentage} cashback", "card.cashback.widgetcashbackValue.rewards": "{amount} afventer", "card.cashback.widgetcashbackValue.title": "{percentage} cashback", "card.choose-wallet.connect_card": "T<PERSON><PERSON> kort", "card.choose-wallet.create-new": "Til<PERSON><PERSON><PERSON> en ny wallet som ejer", "card.choose-wallet.import-another-wallet": "Importer en anden wallet", "card.choose-wallet.import-current-owner": "Importer nuværende kortejer", "card.choose-wallet.import-current-owner.sub-text": "Importer nøgler til din kort-wallet", "card.choose-wallet.title": "Væ<PERSON>g wallet til at styre dit kort", "card.connectWalletToCardGuide": "<PERSON><PERSON><PERSON><PERSON> wallet-adresse", "card.connectWalletToCardGuide.addGnosisPayOwner": "Tilføj Gnosis Pay-ejer", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Åbn Gnosispay.com med din anden wallet{br}2. <PERSON><PERSON> på 'Konto''{br}3. <PERSON><PERSON> på 'Kontodetaljer''{br}4. <PERSON><PERSON> på '<PERSON><PERSON>' ved siden af 'Kontoejer', og{br}5. <PERSON><PERSON> på 'Tilføj adresse''{br}6. Indsæt din Zeal-adresse og klik på Gem", "card.connectWalletToCardGuide.header": "Tilslut {account} til Gnosis Pay-kort", "card.connect_card.start": "Tilslut Gnosis Pay-kort", "card.copiedAddress": "<PERSON><PERSON><PERSON> {formattedAddress}", "card.disconnect-account.title": "Frakobl konto", "card.hw-wallet-support-drop.add-owner-btn": "Tilføj ny ejer til kort", "card.hw-wallet-support-drop.disconnect-btn": "Frakobl kort fra Zeal", "card.hw-wallet-support-drop.subtitle": "For at fortsætte med at bruge dit Gnosis Pay-kort i Zeal skal du tilføje en anden ejer til dit kort, som ikke er en Hardware Wallet.", "card.hw-wallet-support-drop.title": "Zeal understøtter ikke længere hardware wallets til kort", "card.kyc.continue": "Fortsæt opsætning", "card.list_item.title": "<PERSON><PERSON>", "card.onboarded.transactions.empty.description": "<PERSON>e <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vises her", "card.onboarded.transactions.empty.title": "Aktivitet", "card.order.continue": "Fortsæt kortbestilling", "card.order.free_virtual_card": "<PERSON><PERSON> gratis <PERSON>lt kort", "card.order.start": "<PERSON>il kort gratis", "card.owner-not-imported.cancel": "<PERSON><PERSON><PERSON>", "card.owner-not-imported.import": "<PERSON><PERSON>rt<PERSON><PERSON>", "card.owner-not-imported.subtitle": "For at godkende transaktionen skal du knytte den tegnebog, der ejer din Gnosis Pay-konto, til Zeal. Bemærk: Dette er ikke dit almindelige Gnosis Pay-login.", "card.owner-not-imported.title": "Tilføj ejer af Gnosis Pay-konto", "card.page.order_free_physical_card": "<PERSON>il gratis fysisk kort", "card.pin.change_pin_at_atm": "Skift pinkode i udvalgte automater", "card.pin.timeout": "<PERSON><PERSON><PERSON><PERSON><PERSON> lukker om {seconds} sek", "card.quick-actions.add-assets": "Fyld op", "card.quick-actions.add-cash": "Fyld op", "card.quick-actions.details": "<PERSON><PERSON><PERSON>", "card.quick-actions.freeze": "<PERSON><PERSON>", "card.quick-actions.freezing": "<PERSON><PERSON>", "card.quick-actions.unfreeze": "<PERSON><PERSON><PERSON>", "card.quick-actions.unfreezing": "Frigiver", "card.quick-actions.withdraw": "<PERSON><PERSON><PERSON>", "card.read-only-detected.create-new": "Til<PERSON><PERSON><PERSON> en ny wallet som ejer", "card.read-only-detected.import-current-owner": "Importer nøgler til {wallet}", "card.read-only-detected.import-current-owner.sub-text": "Importer nøglerne til wallet {address}", "card.read-only-detected.title": "Dit kort er skrivebeskyttet. Vælg wallet", "card.remove-owner.queued": "Fjernelse af ejer er i kø", "card.settings.disconnect-from-zeal": "Frakobl fra Zeal", "card.settings.edit-owners": "Skift kortejere", "card.settings.getCard": "Bestil endnu et kort", "card.settings.getCard.subtitle": "Virtuelle eller fysiske kort", "card.settings.notRecharging": "Ingen automatisk optankning", "card.settings.notifications.subtitle": "Modtag betalingsnotifikationer", "card.settings.notifications.title": "Kortnotifika<PERSON><PERSON>", "card.settings.page.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.select-card.cancelled-cards": "Ann<PERSON><PERSON><PERSON> kort", "card.settings.setAutoRecharge": "Indstil automatisk optankning", "card.settings.show-card-address": "<PERSON><PERSON> k<PERSON>e", "card.settings.spend-limit": "Indstil forbrugsgrænse", "card.settings.spend-limit-title": "Nuværende daglige grænse: {limit}", "card.settings.switch-active-card": "Skift aktivt kort", "card.settings.switch-active-card-description": "Aktivt kort: {card}", "card.settings.switch-card.card-item.cancelled": "<PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.frozen": "S<PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.title": "Gnosis Pay-kort", "card.settings.switch-card.card-item.title.physical": "Fysisk kort", "card.settings.switch-card.card-item.title.virtual": "Virt<PERSON><PERSON> kort", "card.settings.switch-card.title": "<PERSON><PERSON><PERSON><PERSON> kort", "card.settings.targetBalance": "Målsaldo: {threshold}", "card.settings.view-pin": "Vis PIN-kode", "card.settings.view-pin-description": "Beskyt altid din PIN-kode", "card.title": "<PERSON><PERSON>", "card.transactions.header": "Korttransaktioner", "card.transactions.see_all": "Se alle transaktioner", "card.virtual": "VIRTUELT", "cardCashback.onboarding.bullets.cashback_sent_weekly": "Cashback sendes til dit kort i starten af ugen efter, den er optjent.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "<PERSON> mere du indbetaler, jo mere optjener du på hvert køb.", "cardCashback.onboarding.title": "Få op til {percentage} cashback", "cardCashbackWithdraw.amount": "Udbetalingsbeløb", "cardCashbackWithdraw.header": "Udbetal {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "Kortet er spærret og kunne ikke aktiveres", "cardWidget.cashback": "Cashback", "cardWidget.cashbackUpToDefaultPercentage": "Op til {percentage}", "cardWidget.startEarning": "Begynd at optjene", "cardWithdraw.amount": "Beløb til udbetaling", "cardWithdraw.header": "<PERSON>æv fra kort", "cardWithdraw.selectWithdrawWallet.title": "Vælg wallet at{br}hæve til", "cardWithdraw.success.cta": "Luk", "cardWithdraw.success.subtitle": "<PERSON><PERSON> si<PERSON>grunde tager alle hævninger fra Gnosis Pay-kortet 3 minutter at behandle", "cardWithdraw.success.title": "<PERSON><PERSON> tager 3 minutter", "card_top_up_trx.send": "Send", "card_top_up_trx.to": "Til", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "<PERSON><PERSON><PERSON><PERSON>da<PERSON>", "cards.card_number": "Kortnummer", "cards.choose-wallet.no-active-accounts": "Du har ingen aktive wallets", "cards.copied_card_number": "Kortnummer kopieret", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "Overstiger daglig grænse", "cards.transactions.decline_reason.incorrect_pin": "<PERSON><PERSON> pink<PERSON>", "cards.transactions.decline_reason.incorrect_security_code": "<PERSON><PERSON>", "cards.transactions.decline_reason.invalid_amount": "Ugy<PERSON><PERSON><PERSON> beløb", "cards.transactions.decline_reason.low_balance": "For lav saldo", "cards.transactions.decline_reason.other": "<PERSON><PERSON><PERSON>", "cards.transactions.decline_reason.pin_tries_exceeded": "For mange pink<PERSON><PERSON><PERSON><PERSON>g", "cards.transactions.status.refund": "Refusion", "cards.transactions.status.reversal": "Tilbageførsel", "cashback-deposit.trx.title": "Indbetal til Cashback", "cashback-estimate.text": "Dette er et skøn og IKKE en garanteret udbetaling. Alle offentligt kendte cashback-regler anvendes, men Gnosis Pay kan efter eget skøn udelukke transaktioner. Et maksimalt forbrug på {amount} om ugen kvalificerer til cashback, selvom skønnet for denne transaktion ville indikere et højere samlet beløb.", "cashback-estimate.text.fallback": "Dette er et estimat og ikke en garanteret udbetaling. Alle offentligt kendte cashback-regler er an<PERSON><PERSON>, men Gnosis Pay kan udelukke transaktioner efter eget skøn.", "cashback-estimate.title": "Cashback-skøn", "cashback-onbarding-tersm.subtitle": "Dine korttransaktionsdata vil blive delt med Ka<PERSON>atkey, som er ansvarlig for at udbetale cashback-belønninger. Ved at klikke på 'Accepter' accepterer du Gnosis DAO Cashback <terms>Vilkår og betingelser</terms>", "cashback-onbarding-tersm.title": "Brugsvilkår og privatliv", "cashback-tx-activity.retry": "<PERSON><PERSON><PERSON><PERSON> igen", "cashback-unconfirmed-payments-info.subtitle": "Betalinger kvalificerer sig til cashback, n<PERSON>r de er afregnet med forhandleren. Indtil da vises de som ubekræftede betalinger. Uafregnede betalinger kvalificerer ikke til cashback.", "cashback-unconfirmed-payments-info.title": "U<PERSON><PERSON><PERSON><PERSON><PERSON> kort<PERSON>alinger", "cashback.activity.cashback": "Cashback", "cashback.activity.deposit": "Indbetaling", "cashback.activity.title": "Seneste aktivitet", "cashback.activity.withdrawal": "Udbetaling", "cashback.deposit": "Indbetal", "cashback.deposit.amount.label": "Indbetalingsbeløb", "cashback.deposit.change": "{from} til {to}", "cashback.deposit.confirmation.subtitle": "Cashback-satser opdateres en gang om ugen. Indbetal nu for at øge næste uges cashback.", "cashback.deposit.confirmation.title": "<PERSON> begynder at optjene {percentage} den {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Veksl tokens til {currency} på {network} Chain", "cashback.deposit.get.tokens.title": "Få {currency} tokens", "cashback.deposit.header": "Indbetal {currency}", "cashback.deposit.max_label": "Maks: {amount}", "cashback.deposit.select-wallet.title": "Vælg tegnebog at indbetale fra", "cashback.deposit.yourcashback": "Din cashback", "cashback.header": "Cashback", "cashback.selectWithdrawWallet.title": "Vælg tegnebog at {br}udbetale til", "cashback.transaction-details.network-label": "Netværk", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "<PERSON>a", "cashback.transaction-details.top-row.label-rewards": "Cashback-periode", "cashback.transaction-details.top-row.label-withdrawal": "Til", "cashback.transaction-details.transaction": "Transaktions-id", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Cashback-transaktioner", "cashback.withdraw": "Udbetal", "cashback.withdraw.confirmation.cashback_reduction": "Cashback for denne u<PERSON>, inklusive det du allerede har optjent, vil falde fra {before} til {after}", "cashback.withdraw.queued": "Udbetaling i kø", "cashback.withdrawal.change": "{from} til {to}", "cashback.withdrawal.confirmation.subtitle": "Start udbetaling af {amount} med 3 minutters forsinkelse. Dette vil reducere din cashback til {after}.", "cashback.withdrawal.confirmation.title": "<PERSON><PERSON> falder, hvis du udbetaler GNO", "cashback.withdrawal.delayTransaction.title": "Start udbetaling af GNO med{br} 3 minutters forsinkelse", "cashback.withdrawal.withdraw": "<PERSON><PERSON><PERSON>", "cashback.withdrawal.yourcashback": "Din cashback", "celebration.aave": "Optjent med Aave", "celebration.cashback.subtitle": "Udbetalt i {code}", "celebration.cashback.subtitleGNO": "{amount} senest optjent", "celebration.chf": "Optjent med Frankencoin", "celebration.lido": "Optjent med Lido", "celebration.sky": "Optjent med Sky", "celebration.title": "Samlet cashback", "celebration.well_done.title": "<PERSON>t g<PERSON>et!", "change-withdrawal-account.add-new-account": "Tilføj en anden bankkonto", "change-withdrawal-account.item.shortText": "{currency} konto", "check-confirmation.approve.footer.for": "For", "checkConfirmation.title": "Transaktionsresultat", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin & Ether", "collateral.ethereum": "Ethereum", "collateral.other": "And<PERSON>", "collateral.rwa": "Virkelige aktiver", "collateral.stablecoins": "Stablecoins (USD-bundet)", "collateral.us-t-bills": "Amerikanske statsobligationer", "confirm-bank-transfer-recipient.bullet-1": "Ingen gebyrer på digital EUR", "confirm-bank-transfer-recipient.bullet-2": "Indbetalinger til {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "Del Gnosis Pay-kontooplysninger med Monerium, et autoriseret og reguleret e-pengeinstitut. <link><PERSON><PERSON>s mere</link>", "confirm-bank-transfer-recipient.bullet-4": "Accepter Moneriums <link>servicevilkår</link>", "confirm-bank-transfer-recipient.title": "Accepter v<PERSON>", "confirm-change-withdrawal-account.cancel": "<PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.confirm": "Bekræft", "confirm-change-withdrawal-account.saving": "<PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.subtitle": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, du sender fra <PERSON>, vil blive modtaget af denne bankkonto.", "confirm-change-withdrawal-account.title": "Skift modtagende bank", "confirm-ramove-withdrawal-account.title": "Fjern bankkonto", "confirm-remove-withdrawal-account.subtitle": "Disse bankkontooplysninger vil blive fjernet fra Zeal. Du kan tilføje dem igen når som helst.", "confirmTransaction.finalNetworkFee": "Netværksgebyr", "confirmTransaction.importKeys": "Importer nøgler", "confirmTransaction.networkFee": "Netværksgebyr", "confirmation.title": "Send {amount} til {recipient}", "conflicting-monerium-account.add-owner": "<PERSON><PERSON><PERSON><PERSON><PERSON> som Gnosis Pay-ejer", "conflicting-monerium-account.create-wallet": "Opret en ny smart wallet", "conflicting-monerium-account.disconnect-card": "<PERSON><PERSON><PERSON><PERSON> kort, genforbind med ny ejer.", "conflicting-monerium-account.header": "{wallet} er knyttet til en anden Monerium-konto", "conflicting-monerium-account.subtitle": "Skift din Gnosis Pay-ejer-wallet", "connection.diconnected.got_it": "Forstået!", "connection.diconnected.page1.subtitle": "Forbind med <PERSON>, ligesom med MetaMask.", "connection.diconnected.page1.title": "<PERSON><PERSON><PERSON> forbinder jeg med Zeal?", "connection.diconnected.page2.subtitle": "Du ser mange valg. <PERSON><PERSON> ikke er der...", "connection.diconnected.page2.title": "Klik på Forbind Wallet", "connection.diconnected.page3.subtitle": "<PERSON><PERSON><PERSON><PERSON> for at forbinde Zeal.", "connection.diconnected.page3.title": "<PERSON><PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.caution": "<PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.danger": "Fare", "connectionSafetyCheck.tag.passed": "Bestået", "connectionSafetyConfirmation.subtitle": "Er du sikker på, at du vil fortsætte?", "connectionSafetyConfirmation.title": "Denne side ser farlig ud", "connection_state.connect.cancel": "<PERSON><PERSON><PERSON>", "connection_state.connect.changeToMetamask": "Skift til MetaMask 🦊", "connection_state.connect.changeToMetamask.label": "Skift til MetaMask", "connection_state.connect.connect_button": "Forbind", "connection_state.connect.expanded.connected": "Forbundet", "connection_state.connect.expanded.title": "<PERSON><PERSON>", "connection_state.connect.safetyChecksLoading": "T<PERSON><PERSON> sidens sikkerhed", "connection_state.connect.safetyChecksLoadingError": "<PERSON>nne ikke gennemfø<PERSON> si<PERSON>", "connection_state.connected.expanded.disconnectButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connection_state.connected.expanded.title": "Forbundet", "copied-diagnostics": "Diagnostik kopieret", "copy-diagnostics": "<PERSON><PERSON><PERSON><PERSON>ag<PERSON>", "counterparty.component.add_recipient_primary_text": "Tilføj bankmodtager", "counterparty.country": "Land", "counterparty.countryTitle": "Modtagerens land", "counterparty.currency": "Valuta", "counterparty.delete.success.title": "<PERSON><PERSON><PERSON>", "counterparty.edit.success.title": "Ændringer gemt", "counterparty.errors.country_required": "Land er påkrævet", "counterparty.errors.first_name.invalid": "Fornavn skal være længere", "counterparty.errors.last_name.invalid": "Efternavn skal være længere", "counterparty.first_name": "Fornavn", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Send til bank", "countrySelector.noCountryFound": "Intet land fundet", "countrySelector.title": "Vælg land", "create-passkey.cta": "<PERSON><PERSON>", "create-passkey.extension.cta": "Fortsæt", "create-passkey.footnote": "Leveret af", "create-passkey.mobile.cta": "Start sikkerhedsopsætning", "create-passkey.steps.enable-recovery": "Opsæt gendannelse via skyen", "create-passkey.steps.setup-biometrics": "Aktivér biometrisk sikkerhed", "create-passkey.subtitle": "Adgangsnøgler er mere sikre end adgangskoder og krypteres i skyen, så du nemt kan gendanne dem.", "create-passkey.title": "Sikr din konto", "create-smart-wallet": "Opret Smart Wallet", "create-userop.progress.text": "Opretter", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Fortsæt i Gnosis Pay", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Gå til Gnosispay.com", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> din bestilling hos Gnosis Pay.", "create_recharge_preferences.card": "<PERSON><PERSON>", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "<PERSON><PERSON><PERSON><PERSON> {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "<PERSON><PERSON> kontanter", "create_recharge_preferences.link_accounts_title": "Tilknyt konti", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "Dit kort vil IKKE blive genopladt automatisk efter hver betaling.", "create_recharge_preferences.not_configured_title": "Optjen og brug", "create_recharge_preferences.recharge_from_earn_accounts_description": "Dit kort genoplades automatisk efter hver betaling fra din Optjen-konto.", "create_recharge_preferences.subtitle": "om året", "creating-account.loading": "<PERSON><PERSON><PERSON> konto", "creating-gnosis-pay-account": "<PERSON><PERSON><PERSON> konto", "currencies.bridge.select_routes.emptyState": "Vi fandt ingen ruter til denne bridge", "currency.add_currency.add_token": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "currency.add_currency.not_a_valid_address": "Dette er ikke en gyldig token-adresse", "currency.add_currency.token_decimals_feild": "Token-decimaler", "currency.add_currency.token_feild": "To<PERSON>-adresse", "currency.add_currency.token_symbol_feild": "Token-symbol", "currency.add_currency.update_token": "Opdater token", "currency.add_custom.remove_token.cta": "F<PERSON>n token", "currency.add_custom.remove_token.header": "F<PERSON>n token", "currency.add_custom.remove_token.subtitle": "<PERSON> tegnebog beholder sa<PERSON><PERSON>, men den vil blive skjult i din Zeal-portefølje.", "currency.add_custom.token_removed": "<PERSON><PERSON> fjernet", "currency.add_custom.token_updated": "Token opdateret", "currency.balance_label": "Saldo: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "<PERSON> bankoverfø<PERSON>l har overført {fiat} til {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "Du har modtaget {crypto}", "currency.bankTransfer.deposit_status.success": "Modtaget i din wallet", "currency.bankTransfer.deposit_status.title": "Indbetaling", "currency.bankTransfer.off_ramp.check_bank_account": "Tjek din bankkonto", "currency.bankTransfer.off_ramp.complete": "Gennemført", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Sender til din bank", "currency.bankTransfer.off_ramp.transferring_to_currency": "<PERSON><PERSON><PERSON><PERSON> til {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Pengene burde være på din bankkonto nu.", "currency.bankTransfer.withdrawal_status.success": "Sendt til din bank", "currency.bankTransfer.withdrawal_status.title": "Udbetaling", "currency.bank_transfer.create_unblock_user.email": "E-mailadresse", "currency.bank_transfer.create_unblock_user.email_invalid": "Ugyldig e-mail", "currency.bank_transfer.create_unblock_user.email_missing": "Påkrævet", "currency.bank_transfer.create_unblock_user.first_name": "Fornavn", "currency.bank_transfer.create_unblock_user.first_name_missing": "Påkrævet", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "Kun bogstaver, tal, mellemrum og - . , & ( ) ' er tilladt.", "currency.bank_transfer.create_unblock_user.last_name": "Efternavn", "currency.bank_transfer.create_unblock_user.last_name_missing": "Påkrævet", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "Kun bogstaver, tal, mellemrum og - . , & ( ) ' er tilladt.", "currency.bank_transfer.create_unblock_user.note": "Ved at fortsætte accepterer du Unblocks (vores bankpartner) <terms>betinge<PERSON>er</terms> og <policy>privatlivspolitik</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "Stav dit navn præcis som det står på din bankkonto", "currency.bank_transfer.create_unblock_user.title": "Tilknyt din bankkonto", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "<PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Bankens land", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Foretrukken valuta", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Registreringsnummer", "currency.bank_transfer.create_unblock_withdraw_account.success": "Konto oprettet", "currency.bank_transfer.create_unblock_withdraw_account.title": "Tilknyt din bankkonto", "currency.bank_transfer.residence-form.address-required": "Påkrævet", "currency.bank_transfer.residence-form.address-unsupported-char": "Kun bogstaver, tal, mellemrum og , ; {apostrophe} - \\\\ er tilladt.", "currency.bank_transfer.residence-form.city-required": "Påkrævet", "currency.bank_transfer.residence-form.city-unsupported-char": "<PERSON>n bogstaver, tal, mellemrum og . , - & ( ) {apostrophe} er tilladt.", "currency.bank_transfer.residence-form.postcode-invalid": "Ugyldigt postnummer", "currency.bank_transfer.residence-form.postcode-required": "Påkrævet", "currency.bank_transfer.validation.invalid.account_number": "Ugyldigt kontonummer", "currency.bank_transfer.validation.invalid.iban": "Ugyldigt IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Ugyldigt registreringsnummer", "currency.bridge.amount_label": "Bel<PERSON>b til bridge", "currency.bridge.best_returns.subtitle": "<PERSON><PERSON> ud<PERSON>der giver dig det højeste afkast, når alle gebyrer er medregnet.", "currency.bridge.best_returns_popup.title": "Bedste afkast", "currency.bridge.bridge_from": "<PERSON>a", "currency.bridge.bridge_gas_fee_loading_failed": "Vi havde problemer med at indlæse netværksgebyr", "currency.bridge.bridge_low_slippage": "<PERSON>et lav slippage. Prøv at forøge den", "currency.bridge.bridge_provider": "Overførselsudbyder", "currency.bridge.bridge_provider_loading_failed": "Vi havde problemer med at hente udbydere", "currency.bridge.bridge_settings": "Bridge-<PERSON><PERSON><PERSON><PERSON>", "currency.bridge.bridge_status.subtitle": "<PERSON><PERSON><PERSON> {name}", "currency.bridge.bridge_status.title": "Bridge", "currency.bridge.bridge_to": "Til", "currency.bridge.fastest_route_popup.subtitle": "Denne bridge-udbyder giver dig den hurtigste transaktionsrute.", "currency.bridge.fastest_route_popup.title": "Hurtigste rute", "currency.bridge.from": "<PERSON>a", "currency.bridge.success": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bridge.title": "Bridge", "currency.bridge.to": "Til", "currency.bridge.topup": "Tank op {symbol}", "currency.bridge.withdrawal_status.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.card.card_top_up_status.title": "Indbetal penge på kort", "currency.destination_amount": "Modtagerbeløb", "currency.hide_currency.confirm.subtitle": "<PERSON>k<PERSON><PERSON> denne token fra din portefølje. Du kan altid vise den igen.", "currency.hide_currency.confirm.title": "Skjul token", "currency.hide_currency.success.title": "Token skjult", "currency.label": "Etiket (valgfri)", "currency.last_name": "Efternavn", "currency.max_loading": "Maks.:", "currency.swap.amount_to_swap": "<PERSON><PERSON><PERSON>, der skal swappes", "currency.swap.best_return": "Rute med bedste afkast", "currency.swap.destination_amount": "Modtagerbeløb", "currency.swap.header": "<PERSON><PERSON><PERSON>", "currency.swap.max_label": "Saldo: {amount}", "currency.swap.provider.header": "Swap<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.swap.select_to_token": "Vælg token", "currency.swap.swap_gas_fee_loading_failed": "Vi havde problemer med at indlæse netværksgebyr", "currency.swap.swap_provider_loading_failed": "Vi havde problemer med at indlæse udbydere", "currency.swap.swap_settings": "<PERSON>wa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.swap.swap_slippage_too_low": "Meget lav slippage. Prøv at øge den.", "currency.swaps_io_native_token_swap.subtitle": "Bruger Swaps.IO", "currency.swaps_io_native_token_swap.title": "Send", "currency.withdrawal.amount_from": "<PERSON>a", "currency.withdrawal.amount_to": "Til", "currencySelector.title": "<PERSON><PERSON><PERSON><PERSON> valuta", "dApp.wallet-does-not-support-chain.subtitle": "Din wallet ser ikke ud til at understøtte {network}. Prøv at oprette forbindelse med en anden wallet, eller brug Zeal i stedet.", "dApp.wallet-does-not-support-chain.title": "Netværk ikke understøttet", "dapp.connection.manage.confirm.disconnect.all.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON> alle", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Vil du afbryde alle forbindelser?", "dapp.connection.manage.confirm.disconnect.all.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> alle", "dapp.connection.manage.connection_list.main.button.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dapp.connection.manage.connection_list.no_connections": "Du har ingen tilsluttede apps", "dapp.connection.manage.connection_list.section.button.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> alle", "dapp.connection.manage.connection_list.section.title": "Aktive", "dapp.connection.manage.connection_list.title": "For<PERSON><PERSON><PERSON>", "dapp.connection.manage.disconnect.success.title": "<PERSON><PERSON> afbrudt", "dapp.metamask_mode.title": "MetaMask-tilstand", "dc25-card-marketing-card.center.subtitle": "Cashback", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "Rente", "dc25-card-marketing-card.right.subtitle": "100 personer", "dc25-card-marketing-card.title": "<PERSON> fø<PERSON><PERSON> 100, der bruger 50 €, opt<PERSON><PERSON> {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Du kan ikke ud<PERSON><PERSON><PERSON> denne handling i 3 minutter. Af sikkerhedsmæssige årsager tager ændringer i kortindstillinger eller udbetalinger 3 minutter at behandle.", "delayQueueBusyBanner.processing-yout-action.title": "Behandler din handling, vent venligst", "delayQueueBusyWidget.cardFrozen": "<PERSON><PERSON> spæ<PERSON>t", "delayQueueBusyWidget.processingAction": "Behandler din handling", "delayQueueFailedBanner.action-incomplete.get-support": "<PERSON><PERSON> hjælp", "delayQueueFailedBanner.action-incomplete.subtitle": "<PERSON><PERSON><PERSON>, der opstod en fejl med din hævning eller opdatering af indstillinger. Kontakt venligst support på Discord.", "delayQueueFailedBanner.action-incomplete.title": "Handling ikke fuldført", "delayQueueFailedWidget.actionIncomplete.title": "Korthandling ikke fuldført", "delayQueueFailedWidget.cardFrozen.subtitle": "<PERSON><PERSON> spæ<PERSON>t", "delayQueueFailedWidget.contactSupport": "Kontakt support", "delay_queue_busy.subtitle": "<PERSON>f sikker<PERSON>sgrunde tager det 3 minutter at behandle ændringer i kortindstillinger eller hævninger. Dit kort er frosset i mellemtiden.", "delay_queue_busy.title": "Din handling behandles", "delay_queue_failed.contact_support": "<PERSON><PERSON> hjælp", "delay_queue_failed.subtitle": "<PERSON><PERSON><PERSON>, der opstod en fejl med din hævning eller opdatering af indstillinger. Kontakt venligst support på Discord.", "delay_queue_failed.title": "Kontakt support", "deploy-earn-form-smart-wallet.in-progress.title": "<PERSON><PERSON><PERSON><PERSON>", "deposit": "Indbetal", "disconnect-card-popup.cancel": "<PERSON><PERSON><PERSON>", "disconnect-card-popup.disconnect": "Frakobl", "disconnect-card-popup.subtitle": "<PERSON><PERSON> fjerner dit kort fra Zeal-appen. Din wallet er stadig forbundet til dit kort i Gnosis Pay-appen. Du kan altid forbinde dit kort igen.", "disconnect-card-popup.title": "Frakobl kort", "distance.long.days": "{count} dage", "distance.long.hours": "{count} timer", "distance.long.minutes": "{count} minutter", "distance.long.months": "{count} m<PERSON><PERSON><PERSON>", "distance.long.seconds": "{count} sekunder", "distance.long.years": "{count} år", "distance.short.days": "{count} d", "distance.short.hours": "{count} t", "distance.short.minutes": "{count} min", "distance.short.months": "{count} md", "distance.short.seconds": "{count} sek", "distance.short.years": "{count} år", "duration.short.days": "{count}d", "duration.short.hours": "{count}t", "duration.short.minutes": "{count}min", "duration.short.seconds": "{count}s", "earn-deposit-view.deposit": "Indbetaling", "earn-deposit-view.into": "Til", "earn-deposit-view.to": "Til", "earn-deposit.swap.transfer-provider": "Overførselsudbyder", "earn-taker-investment-details.accrued-realtime": "Optjenes i realtid", "earn-taker-investment-details.asset-class": "Aktivklasse", "earn-taker-investment-details.asset-coverage-ratio": "Dækningsgrad for aktiver", "earn-taker-investment-details.asset-reserve": "Aktivreserve", "earn-taker-investment-details.base_currency.label": "Basisvaluta", "earn-taker-investment-details.chf.description": "<PERSON><PERSON><PERSON><PERSON> renter på dine CHF ved at indskyde zCHF i Frankencoin – et anerkendt digitalt pengemarked. Renter genereres fra lån med lav risiko og overbelåningssikkerhed på Frankencoin og udbetales i realtid. Dine midler er sikre på en underkonto, som kun du kontrollerer.", "earn-taker-investment-details.chf.description.with_address_link": "<PERSON><PERSON>jen renter på dine CHF ved at indskyde zCHF i Frankencoin – et anerkendt digitalt pengemarked. Renter genereres fra lån med lav risiko og overbelåningssikkerhed på Frankencoin og udbetales i realtid. Dine midler er sikre på en underkonto <link>(kopier 0x)</link> som kun du kontrollerer.", "earn-taker-investment-details.chf.label": "Digital schweizerfranc", "earn-taker-investment-details.collateral-composition": "Sikkerhedssammensætning", "earn-taker-investment-details.depositor-obligations": "Indskyderforpligtelser", "earn-taker-investment-details.eure.description": "<PERSON><PERSON><PERSON><PERSON> renter på dine euro ved at indbetale EURe til Aave – et betroet digitalt pengemarked. EURe er en fuldt reguleret euro-stablecoin udstedt af Monerium og bakket op 1:1 på sikrede konti. Renter genereres fra overbelånte lån med lav risiko på Aave og udbetales i realtid. Dine penge forbliver på en sikker underkonto, som kun du kontrollerer.", "earn-taker-investment-details.eure.description.with_address_link": "<PERSON><PERSON>je<PERSON> renter på dine euro ved at indbetale EURe til Aave – et betroet digitalt pengemarked. EURe er en fuldt reguleret euro-stablecoin udstedt af Monerium og bakket op 1:1 på sikrede konti. Renter genereres fra overbelånte lån med lav risiko på Aave og udbetales i realtid. Dine penge forbliver på en sikker underkonto <link>(kopier 0x)</link> , som kun du kontrollerer.", "earn-taker-investment-details.eure.label": "Digital Euro (EURe)", "earn-taker-investment-details.faq": "FAQ", "earn-taker-investment-details.fixed-income": "Fast indkomst", "earn-taker-investment-details.issuer": "Udsteder", "earn-taker-investment-details.key-facts": "Nøglefakta", "earn-taker-investment-details.liquidity": "Likviditet", "earn-taker-investment-details.operator": "Markedsoperatør", "earn-taker-investment-details.projected-yield": "Forventet årligt afkast", "earn-taker-investment-details.see-other-faq": "Se alle andre FAQ'er", "earn-taker-investment-details.see-realtime": "Se data i realtid", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Afkast for de seneste 12 måneder", "earn-taker-investment-details.total-collateral": "Sam<PERSON> sikkerhed", "earn-taker-investment-details.total-deposits": "$27,253,300,208", "earn-taker-investment-details.total-zchf-supply": "Samlet ZCHF-udbud", "earn-taker-investment-details.total_deposits": "Samlede Aave-indskud", "earn-taker-investment-details.usd.description": "Sky er et digitalt pengemarked, der tilbyder stabile, amerikanske dollar-denominerede afkast fra kortfristede amerikanske statsobligationer og overbelånte udlån – uden kryptovolatilitet, adgang til midler 24/7 og gennemsigtig on-chain-sikkerhed.", "earn-taker-investment-details.usd.description.with_address_link": "Sky er et digitalt pengemarked, der tilbyder stabile, amerikanske dollar-denominerede afkast fra kortfristede amerikanske statsobligationer og overbelånte udlån – uden kryptovolatilitet, adgang til midler 24/7 og gennemsigtig on-chain-sikkerhed. Investeringer er på en underkonto <link>(kopier 0x)</link> , som du kontrollerer.", "earn-taker-investment-details.usd.ftx-difference": "<PERSON><PERSON>dan adskiller dette sig fra FT<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>?", "earn-taker-investment-details.usd.high-returns": "<PERSON><PERSON><PERSON> kan afkastet være så højt, især sammenlignet med traditionelle banker?", "earn-taker-investment-details.usd.how-is-backed": "<PERSON><PERSON>dan er <PERSON> sikret, og hvad sker der med mine penge, hvis Z<PERSON> går konkurs?", "earn-taker-investment-details.usd.income-sources": "Indkomstkilder 2024", "earn-taker-investment-details.usd.insurance": "Er mine midler for<PERSON>ret eller garanteret af nogen enhed (som FDIC eller lignende)?", "earn-taker-investment-details.usd.label": "Digital US Dollar", "earn-taker-investment-details.usd.lose-principal": "Kan jeg reelt set miste min hovedstol, og under hvilke omstændigheder?", "earn-taker-investment-details.variable-rate": "Udlån med variabel rente", "earn-taker-investment-details.withdraw-anytime": "<PERSON><PERSON><PERSON> når som helst", "earn-taker-investment-details.yield": "Afkast", "earn-withdrawal-view.approve.for": "For", "earn-withdrawal-view.approve.into": "Til", "earn-withdrawal-view.swap.into": "Til", "earn-withdrawal-view.withdraw.to": "Til", "earn.add_another_asset.title": "Vælg aktiv til Earn", "earn.add_asset": "Tilføj aktiv", "earn.asset_view.title": "<PERSON><PERSON><PERSON><PERSON>", "earn.base-currency-popup.text": "Basisvalutaen er den valuta, dine indskud, afkast og transaktioner værdiansættes og registreres i. Hvis du indbetaler i en anden valuta (f.eks. EUR til USD), bliver dine penge straks vekslet til basisvalutaen til den gældende kurs. Efter veksling forbliver din saldo stabil i basisvalutaen, men fremtidige hævninger kan igen medføre valutaomregning.", "earn.base-currency-popup.title": "Basisvaluta", "earn.card-recharge.disabled.list-item.title": "Automatisk optankning slået fra", "earn.card-recharge.enabled.list-item.title": "Automatisk optankning slået til", "earn.choose_wallet_to_deposit.title": "Indbetal fra", "earn.config.currency.eth": "Optjen Ethereum", "earn.config.currency.on_chain_address_subtitle": "On-chain adresse", "earn.config.currency.us_dollars": "Opsæt bankoverførsler", "earn.configured_widget.current_apy.title": "Nuværende APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} årligt", "earn.confirm.currency.cta": "Indbetal", "earn.currency.eth": "Tjen Ethereum", "earn.deploy.status.title": "<PERSON><PERSON>konto", "earn.deploy.status.title_with_taker": "Opret {title} Earn-konto", "earn.deposit": "Indbetal", "earn.deposit.amount_to_deposit": "Beløb til indbetaling", "earn.deposit.deposit": "Indbetal", "earn.deposit.enter_amount": "Indtast beløb", "earn.deposit.no_routes_found": "Ingen ruter fundet", "earn.deposit.not_enough_balance": "Utilstrækkelig saldo", "earn.deposit.select-currency.title": "Vælg token til indbetaling", "earn.deposit.select_account.title": "<PERSON><PERSON><PERSON><PERSON>k<PERSON>o", "earn.desposit_form.title": "Indbetal til Earn", "earn.earn_deposit.status.title": "Indbetal til Earn", "earn.earn_deposit.trx.title": "Indbetal til Earn", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "Udbetal penge når som helst", "earn.earn_withdraw.status.title": "Hæv fra E<PERSON>n-konto", "earn.earn_withdraw.trx.title.approval": "Godkend hævning", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Hæv til {asset}", "earn.earn_withdraw.trx.title.withdrawal": "<PERSON>æv fra <PERSON>n", "earn.recharge.cta": "<PERSON><PERSON>", "earn.recharge.earn_not_configured.enable_some_account.error": "Aktiver konto", "earn.recharge.earn_not_configured.enter_amount.error": "Indtast beløb", "earn.recharge.select_taker.header": "Optank kort i rækkefølge fra", "earn.recharge_card_tag.on": "Til", "earn.recharge_card_tag.recharge": "Optankning", "earn.recharge_card_tag.recharge_not_configured": "Automatisk optankning", "earn.recharge_card_tag.recharge_off": "Optankning Fra", "earn.recharge_card_tag.recharged": "Optanket", "earn.recharge_card_tag.recharging": "Optanker", "earn.recharge_configured.disable.trx.title": "Deaktiver automatisk opfyldning", "earn.recharge_configured.trx.disclaimer": "<PERSON><PERSON><PERSON> du bruger dit kort, starter en Cowswap-auktion, der køber for samme beløb som din betaling med dine <PERSON>arn-midler. Auktionen giver dig typisk den bedste markedspris, men on-chain-kursen kan afvige fra den reelle vekselkurs.", "earn.recharge_configured.trx.subtitle": "Efter hver betaling fyldes dit kort automatisk op fra din(e) Earn-konto(er), så din saldo holdes på {value}", "earn.recharge_configured.trx.title": "Indstil automatisk opfyldning til {value}", "earn.recharge_configured.updated.trx.title": "<PERSON><PERSON> inds<PERSON>linger for opfyldning", "earn.risk-banner.subtitle": "<PERSON>te er et produkt, hvor du selv har kontrollen, uden lovmæssig beskyttelse mod tab.", "earn.risk-banner.title": "Forstå risiciene", "earn.set_recharge.status.title": "Indstil automatisk optankning", "earn.setup_reacharge.input.disable.label": "<PERSON><PERSON><PERSON><PERSON>", "earn.setup_reacharge.input.label": "<PERSON><PERSON><PERSON><PERSON> for kort", "earn.setup_reacharge_form.title": "Auto-Optankning holder dit {br}kort på samme saldo", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Hold wstETH på Gnosis, og udlån via Lido.", "earn.taker-bulletlist.point_1": "Tjen {apy<PERSON><PERSON>ue} årligt. Afkast varierer med markedet.", "earn.taker-bulletlist.point_3": "Zeal opkræver ingen gebyrer.", "earn.taker-historical-returns": "Historisk afkast", "earn.taker-historical-returns.chf": "Vækst for CHF til USD", "earn.taker-investment-tile.apy.perYear": "pr. <PERSON>r", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "Indbetal", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Indtjening fra Aave på Gnosis Chain", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Optjening fra Frankencoin på Gnosis Chain", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Indtjening fra Lido på Gnosis Chain", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Indtjening fra Maker på Gnosis Chain", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "Eth earn", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH Earn", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "Amerikanske statsobligationer (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "Selvom det er yderst sjældent, er det teoretisk muligt. Dine midler er beskyttet af streng risikostyring og høj sikkerhedsstillelse. Det realistiske worst-case-scenarie ville involvere hidtil usete markedsforhold, som f.eks. at flere stablecoins mister deres binding til dollaren samtidigt – noget, der aldrig er sket før.", "earn.usd.can-I-lose-my-principal-popup.title": "Kan jeg reelt set miste min hovedstol, og under hvilke omstændigheder?", "earn.usd.ftx-difference-popup.text": "Sky er fundamentalt anderledes. I modsætning til FTX, <PERSON><PERSON><PERSON>, BlockFi eller Luna, som var stærkt afhængige af centraliseret opbevaring, uigennemsigtig kapitalforvaltning og risikable gearede positioner, bruger Sky USD gennemsigtige, reviderede, decentrale smart contracts og opretholder fuld on-chain gennemsigtighed. Du bevarer fuld kontrol over din private tegnebog, hvilket markant reducerer modpartsrisikoen forbundet med centraliserede kollaps.", "earn.usd.ftx-difference-popup.title": "<PERSON><PERSON>dan adskiller dette sig fra FT<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>?", "earn.usd.high-returns-popup.text": "Sky USD genererer primært afkast gennem decentraliserede finansprotokoller (DeFi), som automatiserer peer-to-peer-udlån og likviditetsforsyning, hvilket fjerner traditionelle bankomkostninger og mellemmænd. Disse effektiviseringer, kombineret med robuste risikokontroller, muligg<PERSON><PERSON> betydeligt højere afkast sammenlignet med traditionelle banker.", "earn.usd.high-returns-popup.title": "<PERSON><PERSON><PERSON> kan afkastet være så højt, især sammenlignet med traditionelle banker?", "earn.usd.how-is-sky-backed-popup.text": "Sky USD er fuldt ud dækket og over-sikret af en kombination af digitale aktiver i sikre smart contracts og virkelige aktiver som amerikanske statsobligationer. Reserverne kan revideres i realtid on-chain, selv inde fra Zeal, hvilket giver gennemsigtighed og sikkerhed. I det usandsynlige tilfælde, at Zeal lukker ned, forbliver dine aktiver sikret on-chain, fuldt under din kontrol og tilgængelige via andre kompatible tegnebøger.", "earn.usd.how-is-sky-backed-popup.title": "<PERSON><PERSON>dan er <PERSON> sikret, og hvad sker der med mine penge, hvis Z<PERSON> går konkurs?", "earn.usd.insurance-popup.text": "Sky USD-midler er ikke FDIC-forsikrede eller dækket af traditionelle statsgarantier, da det er en konto baseret på digitale aktiver, ikke en traditionel bankkonto. I stedet håndterer Sky al risikoreduktion gennem reviderede smart contracts og nøje udvalgte DeFi-protokoller, hvilket sikrer, at aktiverne forbliver sikre og gennemsigtige.", "earn.usd.insurance-popup.title": "Er mine midler for<PERSON>ret eller garanteret af nogen enhed (som FDIC eller lignende)?", "earn.usd.lending-operations-popup.text": "Sky USD genererer afkast ved at udlåne stablecoins gennem decentrale udlånsmarkeder som Morpho og Spark. Dine stablecoins lånes ud til låntagere, der deponerer betydeligt mere sikkerhed – såsom ETH eller BTC – end værdien af deres lån. <PERSON><PERSON>, ka<PERSON><PERSON> over-sikker<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, si<PERSON><PERSON>, at der altid er tilstrækkelig sikkerhed til at dække lån, hvilket reducerer risikoen betydeligt. De indsamlede renter og lejlighedsvise likvidationsgebyrer betalt af låntagere giver et pålideligt, gennemsigtigt og sikkert afkast.", "earn.usd.lending-operations-popup.title": "Udlånsvirksomhed", "earn.usd.market-making-operations-popup.text": "Sky USD tjener yderligere afkast ved at deltage i decentrale børser (AMM'er) som Curve eller Uniswap. Ved at levere likviditet – ved at placere dine stablecoins i puljer, der muliggør kryptohandel – fanger Sky USD gebyrer fra handler. Disse likviditetspuljer er nøje udvalgt for at minimere volatilitet, primært ved at bruge stablecoin-til-stablecoin-par for at reducere risici som impermanent loss markant, hvilket holder dine aktiver både sikre og tilgængelige.", "earn.usd.market-making-operations-popup.title": "Market making-aktiviteter", "earn.usd.treasury-operations-popup.text": "Sky USD genererer et stabilt, konsistent afkast gennem strategiske investeringer i statspapirer. En del af dine stablecoin-indskud allokeres til sikre, lavrisiko-aktiver i den virkelige verden – primært kortfristede statsobligationer og meget sikre kreditinstrumenter. <PERSON><PERSON> tilgang, der minder om traditionel bankvirksomhed, sikrer et forudsigeligt og pålideligt afkast. Dine aktiver forbliver sikre, likvide og gennemsigtigt forvaltede.", "earn.usd.treasury-operations-popup.title": "Kapitalforvaltning", "earn.view_earn.card_rechard_off": "<PERSON>a", "earn.view_earn.card_rechard_on": "Til", "earn.view_earn.card_recharge": "Kortoptankning", "earn.view_earn.total_balance_label": "<PERSON><PERSON><PERSON><PERSON> {percentage} om året", "earn.view_earn.total_earnings_label": "Samlet optjening", "earn.withdraw": "<PERSON><PERSON><PERSON>", "earn.withdraw.amount_to_withdraw": "Beløb at hæve", "earn.withdraw.enter_amount": "Indtast beløb", "earn.withdraw.loading": "<PERSON><PERSON><PERSON><PERSON>", "earn.withdraw.no_routes_found": "Ingen ruter fundet", "earn.withdraw.not_enough_balance": "Utilstrækkelig saldo", "earn.withdraw.select-currency.title": "Vælg token", "earn.withdraw.select_to_token": "Vælg token", "earn.withdraw.withdraw": "<PERSON><PERSON><PERSON>", "earn.withdraw_form.title": "<PERSON>æv fra <PERSON>n", "earnings-view.earnings": "Samlet indtjening", "edit-account-owners.add-owner.add-wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.add_wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "edit-account-owners.card-owners": "<PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.external-wallet": "Ekstern wallet", "editBankRecipient.title": "Rediger modtager", "editNetwork.addCustomRPC": "Tilføj brugerdefineret RPC-node", "editNetwork.cannot_verify.subtitle": "RPC-noden svarer ikke. Tjek URL. Prøv igen.", "editNetwork.cannot_verify.title": "Kan ikke bekræfte RPC-node", "editNetwork.cannot_verify.try_again": "<PERSON><PERSON><PERSON><PERSON> igen", "editNetwork.customRPCNode": "Brugerdefineret RPC-node", "editNetwork.defaultRPC": "Standard RPC", "editNetwork.networkRPC": "Netværks-RPC", "editNetwork.rpc_url.cannot_be_empty": "Påkrævet", "editNetwork.rpc_url.not_a_valid_https_url": "Skal være en gyldig HTTP(S) URL", "editNetwork.safetyWarning.subtitle": "Zeal kan ikke garantere for privatliv, på<PERSON>lighed og sikkerhed for brugerdefinerede RPC'er. Er du sikker på, at du vil bruge en brugerdefineret RPC-node?", "editNetwork.safetyWarning.title": "Brugerdefinerede RPC'er kan være usikre", "editNetwork.zealRPCNode": "Zeal RPC-node", "editNetworkRpc.headerTitle": "Brugerdefineret RPC-node", "editNetworkRpc.rpcNodeUrl": "RPC-node URL", "editing-locked.modal.description": "I modsætning til godkendelsestransaktioner kan du ikke redigere forbrugsgrænse eller udløbstid for tilladelser. <PERSON><PERSON><PERSON> for, at du stoler på en dApp, før du sender en tilladelse.", "editing-locked.modal.title": "Red<PERSON><PERSON> lå<PERSON>", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Aktiverer optankning", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Optankning aktiveret", "enterCardnumber": "Indtast kortnummer", "error.connectivity_error.subtitle": "Tjek venligst din internetforbindelse og prøv igen.", "error.connectivity_error.title": "Ingen internetforbindelse", "error.decrypt_incorrect_password.title": "<PERSON><PERSON>", "error.encrypted_object_invalid_format.title": "Beskadiget data", "error.failed_to_fetch_google_auth_token.title": "<PERSON>i kunne ikke få adgang", "error.list.item.cta.action": "<PERSON><PERSON><PERSON><PERSON> igen", "error.trezor_action_cancelled.title": "Transaktion afvist", "error.trezor_device_used_elsewhere.title": "Enheden bruges i en anden session", "error.trezor_method_cancelled.title": "Kunne ikke synkronisere Trezor", "error.trezor_permissions_not_granted.title": "Kunne ikke synkronisere Trezor", "error.trezor_pin_cancelled.title": "Kunne ikke synkronisere Trezor", "error.trezor_popup_closed.title": "Kunne ikke synkronisere Trezor", "error.unblock_account_number_and_sort_code_mismatch": "Kontonummer og registreringsnummer stemmer ikke overens", "error.unblock_can_not_change_details_after_kyc": "Kan ikke ændre oplysninger efter KYC", "error.unblock_hard_kyc_failure": "Uventet KYC-status", "error.unblock_invalid_faster_payment_configuration.title": "Denne bank understøtter ikke Faster Payments", "error.unblock_invalid_iban": "Ugyldigt IBAN", "error.unblock_session_expired.title": "Unblock-session udl<PERSON><PERSON>", "error.unblock_user_with_address_already_exists.title": "Konto er allerede oprettet for adressen", "error.unblock_user_with_such_email_already_exists.title": "Bruger med denne e-mail findes allerede", "error.unknown_error.error_message": "Fejlmeddelelse: ", "error.unknown_error.subtitle": "Beklager! H<PERSON> du har brug for akut hjæ<PERSON><PERSON>, bedes du kontakte support og dele nedenstående oplysninger.", "error.unknown_error.title": "Systemfejl", "eth-cost-warning-modal.subtitle": "Smart Wallets virker på Ethereum, men netværksgebyrerne er meget høje. Vi anbefaler KRAFTIGT, at du bruger andre netværk i stedet.", "eth-cost-warning-modal.title": "Undgå Ethereum – netværksgebyrerne er høje", "exchange.form.button.chain_unsupported": "Netværk ikke understøttet", "exchange.form.button.refreshing": "<PERSON><PERSON><PERSON>", "exchange.form.error.asset_not_supported.button": "Vælg et andet aktiv", "exchange.form.error.asset_not_supported.description": "Dette aktiv kan ikke bridges.", "exchange.form.error.asset_not_supported.title": "Aktiv ikke understøttet", "exchange.form.error.bridge_quote_timeout.button": "Vælg et andet aktiv", "exchange.form.error.bridge_quote_timeout.description": "Prøv et andet par tokens", "exchange.form.error.bridge_quote_timeout.title": "Ingen veksling fundet", "exchange.form.error.different_receiver_not_supported.button": "Fjern alternativ modtager", "exchange.form.error.different_receiver_not_supported.description": "<PERSON>ne udbyder understøtter ikke afsendelse til en anden adresse.", "exchange.form.error.different_receiver_not_supported.title": "Afender- og modtageradresse skal være den samme", "exchange.form.error.insufficient_input_amount.button": "<PERSON><PERSON><PERSON>", "exchange.form.error.insufficient_liquidity.button": "<PERSON><PERSON><PERSON> be<PERSON>", "exchange.form.error.insufficient_liquidity.description": "Bridgen har ikke nok aktiver. Prøv med et mindre beløb.", "exchange.form.error.insufficient_liquidity.title": "<PERSON><PERSON><PERSON> er for højt", "exchange.form.error.max_amount_exceeded.button": "<PERSON><PERSON><PERSON> be<PERSON>", "exchange.form.error.max_amount_exceeded.description": "Det maksimale beløb er overskredet.", "exchange.form.error.max_amount_exceeded.title": "<PERSON><PERSON><PERSON> er for højt", "exchange.form.error.min_amount_not_met.button": "<PERSON><PERSON><PERSON>", "exchange.form.error.min_amount_not_met.description": "Minimumsbeløbet for denne token er ikke opfyldt.", "exchange.form.error.min_amount_not_met.description_with_amount": "Minimumsbeløbet for veksling er {amount}.", "exchange.form.error.min_amount_not_met.title": "Beløbet er for lavt", "exchange.form.error.min_amount_not_met.title_increase": "<PERSON><PERSON><PERSON>", "exchange.form.error.no_routes_found.button": "Vælg et andet aktiv", "exchange.form.error.no_routes_found.description": "Der er ingen vekselrute tilgængelig for denne kombination af token/netværk.", "exchange.form.error.no_routes_found.title": "Ingen veksling tilgængelig", "exchange.form.error.not_enough_balance.button": "<PERSON><PERSON><PERSON> be<PERSON>", "exchange.form.error.not_enough_balance.description": "Du har ikke nok af dette aktiv til transaktionen.", "exchange.form.error.not_enough_balance.title": "Ikke nok saldo", "exchange.form.error.slippage_passed_is_too_low.button": "Forøg slippage", "exchange.form.error.slippage_passed_is_too_low.description": "Tilladt slippage er for lav for dette aktiv.", "exchange.form.error.slippage_passed_is_too_low.title": "Slippage er for lav", "exchange.form.error.socket_internal_error.button": "<PERSON><PERSON><PERSON><PERSON> igen senere", "exchange.form.error.socket_internal_error.description": "Vores bridge-partner har problemer. Prøv igen senere.", "exchange.form.error.socket_internal_error.title": "<PERSON><PERSON><PERSON> ho<PERSON> bridge-partner", "exchange.form.error.stargatev2_requires_fee_in_native": "<PERSON><PERSON><PERSON><PERSON><PERSON> {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Tilføj {amount} for at gennemføre transaktionen", "exchange.form.error.stargatev2_requires_fee_in_native.title": "Brug for mere {symbol}", "expiration-info.modal.description": "Udl<PERSON>bstid er den periode, en app kan bruge dine tokens. <PERSON><PERSON><PERSON> tiden er gået, mister de adgangen, indtil du giver lov igen. For din sikkerheds skyld bør udløbstiden være kort.", "expiration-info.modal.title": "Hvad er udløbstid?", "expiration-time.high.modal.text": "Hold udløbstid kort. Lang udløbstid øger risikoen for, at svindlere kan misbruge dine tokens.", "expiration-time.high.modal.title": "Lang udløbstid", "failed.transaction.content": "Transaktionen vil sandsynligvis mislykkes", "fee.unknown": "Ukendt", "feedback-request.leave-message": "Skriv en besked", "feedback-request.not-now": "Ikke nu", "feedback-request.title": "Tak! <PERSON><PERSON><PERSON> kan vi forbedre Zeal?", "float.input.period": "Decimaltegn", "gnosis-activate-card.info-popup.subtitle": "Ved din første transaktion skal du indsætte kortet og indtaste din PIN-kode. Derefter vil kontaktløse betalinger fungere.", "gnosis-activate-card.info-popup.title": "F<PERSON>rste betaling kræver chip og PIN-kode", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "Indtast dit kortnummer for at aktivere det.", "gnosis-activate-card.title": "Kortnummer", "gnosis-pay-re-kyc-widget.btn-text": "<PERSON><PERSON><PERSON><PERSON>", "gnosis-pay-re-kyc-widget.title.not-started": "Bekræft din identitet", "gnosis-pay.login.cta": "Tilslut eks. konto", "gnosis-pay.login.title": "Du har allerede en Gnosis Pay-konto", "gnosis-signup.confirm.subtitle": "Tjek spam for mail fra Gnosis Pay", "gnosis-signup.confirm.title": "Ikke modtaget bekræftelsesmail?", "gnosis-signup.continue": "Fortsæt", "gnosis-signup.dont_link_accounts": "Tilknyt ikke konti", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "Indtast <EMAIL>", "gnosis-signup.enter-email.title": "Indtast e-mail", "gnosis-signup.title": "Jeg har læst og accepterer Gnosis Pays <linkGnosisTNC>Betingelser</linkGnosisTNC> <monovateTerms>Kortholderbetingelser</monovateTerms> og <linkMonerium>Moneriums vilkår</linkMonerium>.", "gnosis-signup.verify-email.title": "Bekræft e-mail", "gnosis.confirm.subtitle": "Ingen kode? Tjek at dit nummer er korrekt.", "gnosis.confirm.title": "Kode sendt til {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "<PERSON><PERSON><PERSON><PERSON>", "gnosisPayAccountStatus.success.title": "Kort importeret", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay er endnu ikke tilgængeligt i dit land", "gnosisPayNoActiveCardsFound.title": "Ingen aktive kort", "gnosis_pay_card_delay_relay_not_empty_error.title": "Din transaktion kunne ikke behandles lige nu. Prøv venligst igen senere.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "<PERSON><PERSON> ikke muligt", "gnosiskyc.modal.approved.activate-free-card": "Aktiver gratis kort", "gnosiskyc.modal.approved.button-text": "Indbetal fra bankkonto", "gnosiskyc.modal.approved.title": "Dine personlige kontooplysninger er oprettet", "gnosiskyc.modal.failed.close": "Luk", "gnosiskyc.modal.failed.title": "<PERSON><PERSON><PERSON>, vores partner <PERSON><PERSON> Pay kan ikke oprette en konto til dig", "gnosiskyc.modal.in-progress.title": "ID-verificering kan tage 24 timer el<PERSON> længer<PERSON>. Hav venligst tålmodighed.", "goToSettingsPopup.settings": "Indstilling", "goToSettingsPopup.title": "Slå notifikationer til i Indstillinger.", "google_file.error.failed_to_fetch_auth_token.button_title": "<PERSON><PERSON><PERSON><PERSON> igen", "google_file.error.failed_to_fetch_auth_token.subtitle": "For at bruge din gendannelsesfil, skal du give os adgang til din personlige cloud.", "google_file.error.failed_to_fetch_auth_token.title": "<PERSON>i kunne ikke få adgang", "hidden_tokens.widget.emptyState": "Ingen skjulte tokens", "how_to_connect_to_metamask.got_it": "OK, forstået", "how_to_connect_to_metamask.story.subtitle": "Skift nemt mellem Zeal og andre wallets.", "how_to_connect_to_metamask.story.title": "Zeal virker sammen med andre wallets", "how_to_connect_to_metamask.why_switch": "H<PERSON>for skifte mellem wallets?", "how_to_connect_to_metamask.why_switch.description": "Zeals si<PERSON><PERSON><PERSON><PERSON><PERSON> beskytter dig altid.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "Brug Zeal med din gamle wallet. Skift nemt.", "import-bank-transfer-owner.banner.title": "Din wallet til bankoverførsel er ændret. Importér for at fortsætte.", "import-bank-transfer-owner.title": "Importer wallet for at bruge bankoverførsler på denne enhed", "import_gnosispay_wallet.add-another-card-owner.footnote": "Importer nøglerne til din kort-wallet", "import_gnosispay_wallet.primaryText": "Importer Gnosis Pay-wallet", "injected-wallet": "Browser-wallet", "intercom.getHelp": "<PERSON><PERSON> hjælp", "invalid_iban.got_it": "Forstået", "invalid_iban.subtitle": "Det indtastede IBAN er ikke gyldigt. Dobbelttjek, at oplysningerne er korrekte, og prøv igen.", "invalid_iban.title": "Ugyldigt IBAN", "keypad-0": "Tastaturknap 0", "keypad-1": "Tastaturknap 1", "keypad-2": "Tastaturknap 2", "keypad-3": "Tastaturknap 3", "keypad-4": "Tastaturknap 4", "keypad-5": "Tastaturknap 5", "keypad-6": "Tastaturknap 6", "keypad-7": "Tastaturknap 7", "keypad-8": "Tastaturknap 8", "keypad-9": "Tastaturknap 9", "keypad.biometric-button": "Biometrisk knap på tastatur", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "<PERSON><PERSON>lig sætning sikret 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "Se sætning", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Opbevar en sikker offline kopi af din hemmelige sætning, så du kan gendanne dine midler senere", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "Forsøg ikke at gætte ordet", "keystore.write_secret_phrase.before_you_begin.first_point": "<PERSON><PERSON> for<PERSON>, at enhver med min hemmelige sætning kan overføre mine midler", "keystore.write_secret_phrase.before_you_begin.second_point": "Jeg er ansvarlig for at holde min hemmelige sætning hemmelig og sikker", "keystore.write_secret_phrase.before_you_begin.subtitle": "<PERSON><PERSON><PERSON> og acceptér venligst følgende punkter:", "keystore.write_secret_phrase.before_you_begin.third_point": "Jeg er et privat sted uden andre mennesker eller kameraer omkring mig", "keystore.write_secret_phrase.before_you_begin.title": "<PERSON><PERSON> du begynder", "keystore.write_secret_phrase.secret_phrase_test.title": "Hvad er ord {count} i din hemmelige sætning?", "keystore.write_secret_phrase.test_ps.lets_do_it": "Lad os gøre det", "keystore.write_secret_phrase.test_ps.subtitle": "Du skal bruge din hemmelige sætning til at gendanne din konto på denne eller andre enheder. Lad os teste, at din hemmelige sætning er skrevet korrekt ned.", "keystore.write_secret_phrase.test_ps.subtitle2": "Vi vil bede dig om {count} ord i din sætning.", "keystore.write_secret_phrase.test_ps.title": "Test kontogendannelse", "kyc.modal.approved.button-text": "Lav bankoverførsel", "kyc.modal.approved.subtitle": "Din verificering er fuldført. Du kan nu lave ubegrænsede bankoverførsler.", "kyc.modal.approved.title": "Bankoverførsler er låst op", "kyc.modal.continue-with-partner.button-text": "Fortsæt", "kyc.modal.continue-with-partner.subtitle": "Vi sender dig videre til vores partner, som indsamler din dokumentation og færdiggør ansøgningen.", "kyc.modal.continue-with-partner.title": "Fortsæt hos vores partner", "kyc.modal.failed.unblock.subtitle": "Unblock har ikke godkendt din identitetsverificering og kan ikke tilbyde dig bankoverførsler.", "kyc.modal.failed.unblock.title": "Unblock-ansøgning ikke godkendt", "kyc.modal.paused.button-text": "<PERSON><PERSON><PERSON>", "kyc.modal.paused.subtitle": "Det ser ud til, at nogle af dine oplysninger er forkerte. Prøv igen, og dobbelttjek dine oplysninger, før du sender.", "kyc.modal.paused.title": "Dine oplysninger ser forkerte ud", "kyc.modal.pending.button-text": "Luk", "kyc.modal.pending.subtitle": "Verificering tager normalt under 10 minutter, men nogle gange kan det tage lidt længere tid.", "kyc.modal.pending.title": "Vi holder dig opdateret", "kyc.modal.required.cta": "Start verificering", "kyc.modal.required.subtitle": "Du har nået transaktionsgrænsen. Verificer din identitet for at fortsætte. Det tager normalt kun et par minutter og kræver nogle personlige oplysninger og dokumentation.", "kyc.modal.required.title": "Identitetsverificering påkrævet", "kyc.submitted": "Ansøgning indsendt", "kyc.submitted_short": "Indsendt", "kyc_status.completed_status": "Gennemført", "kyc_status.failed_status": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc_status.paused_status": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "kyc_status.subtitle": "Bankoverfø<PERSON><PERSON>", "kyc_status.subtitle.wrong_details": "<PERSON><PERSON><PERSON> oply<PERSON>er", "kyc_status.subtitle_in_progress": "I gang", "kyc_status.title": "Bekræfter identitet", "label.close": "Luk", "label.saving": "Gemmer...", "labels.this-month": "<PERSON><PERSON>", "labels.today": "I dag", "labels.yesterday": "<PERSON> går", "language.selector.title": "Sp<PERSON>", "ledger.account_loaded.imported": "Importeret", "ledger.add.success.title": "Ledger forbundet 🎉", "ledger.connect.cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.connect.step1": "Forbind Ledger til din enhed", "ledger.connect.step2": "Åbn Ethereum-appen på din Ledger", "ledger.connect.step3": "Synkronisér s<PERSON> din Ledger 👇", "ledger.connect.subtitle": "<PERSON><PERSON><PERSON><PERSON> disse trin for at importere dine Ledger-wallets til Zeal", "ledger.connect.title": "Forbind Ledger til Zeal", "ledger.error.ledger_is_locked.subtitle": "<PERSON><PERSON><PERSON> din Ledger op og åbn Ethereum-appen", "ledger.error.ledger_is_locked.title": "<PERSON><PERSON> er låst", "ledger.error.ledger_not_connected.action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.error.ledger_not_connected.subtitle": "Tilslut din hardware-wallet til din enhed, og åbn Ethereum-appen", "ledger.error.ledger_not_connected.title": "Ledger er ikke tilsluttet", "ledger.error.ledger_running_non_eth_app.title": "Ethereum-app er ikke åben", "ledger.error.user_trx_denied_by_user.action": "Luk", "ledger.error.user_trx_denied_by_user.subtitle": "Du afviste transaktionen på din hardware-wallet", "ledger.error.user_trx_denied_by_user.title": "Transaktion afvist", "ledger.hd_path.bip44.subtitle": "f.e<PERSON><PERSON>, Trezor", "ledger.hd_path.bip44.title": "BIP44 Standard", "ledger.hd_path.ledger_live.subtitle": "Standard", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Legacy", "ledger.hd_path.phantom.subtitle": "f.eks. Phantom", "ledger.select.hd_path.subtitle": "HD-stier sorterer dine hardware-konti.", "ledger.select.hd_path.title": "Vælg HD-sti", "ledger.select_account.import_wallets_count": "{count,plural,=0{<PERSON><PERSON> tegne<PERSON>øger valgt} one{Importér tegnebog} other{Importér {count} tegnebø<PERSON>}}", "ledger.select_account.path_settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.select_account.subtitle": "Ser du ikke din tegnebog? <PERSON>rø<PERSON> anden sti.", "ledger.select_account.subtitle.group_header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.select_account.title": "I<PERSON>rt<PERSON><PERSON>", "legend.lending-operations": "Udlånsvirksomhed", "legend.market_making-operations": "Market making-aktiviteter", "legend.treasury-operations": "Kapitalforvaltning", "link-existing-monerium-account-sign.button": "For<PERSON><PERSON>", "link-existing-monerium-account-sign.subtitle": "Du har allerede en Monerium-konto.", "link-existing-monerium-account-sign.title": "Forbind Zeal med din Monerium-konto", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "Du har en Monerium-konto. Åbn Monerium.", "link-existing-monerium-account.title": "Gå til Monerium for at forbinde din konto", "loading.pin": "He<PERSON> pinkode...", "lockScreen.passwordIncorrectMessage": "Adgangskoden er forkert", "lockScreen.passwordRequiredMessage": "Adgangskode påkrævet", "lockScreen.unlock.header": "Lås op", "lockScreen.unlock.subheader": "Brug din adgangskode til at låse Zeal op", "mainTabs.activity.label": "Aktivitet", "mainTabs.browse.label": "Udforsk", "mainTabs.browse.title": "Udforsk", "mainTabs.card.label": "<PERSON><PERSON>", "mainTabs.portfolio.label": "Portefølje", "mainTabs.rewards.label": "<PERSON><PERSON><PERSON><PERSON>", "makeSpendable.cta": "Klargør til brug", "makeSpendable.holdAsCash": "Behold som kontanter", "makeSpendable.shortText": "Optjen {apy} om året", "makeSpendable.title": "{amount} modtaget", "merchantCategory.agriculture": "Landbrug", "merchantCategory.alcohol": "Alkohol", "merchantCategory.antiques": "Antikviteter", "merchantCategory.appliances": "Apparater", "merchantCategory.artGalleries": "Kunstgallerier", "merchantCategory.autoRepair": "Autoreparation", "merchantCategory.autoRepairService": "Autoreparationsservice", "merchantCategory.beautyFitnessSpas": "Skønhed, fitness og spa", "merchantCategory.beautyPersonalCare": "Skønhed og personlig pleje", "merchantCategory.billiard": "<PERSON><PERSON>", "merchantCategory.books": "<PERSON><PERSON><PERSON>", "merchantCategory.bowling": "Bowling", "merchantCategory.businessProfessionalServices": "Erhvervs- og professionelle tjenester", "merchantCategory.carRental": "<PERSON><PERSON><PERSON>", "merchantCategory.carWash": "Bilvask", "merchantCategory.cars": "Biler", "merchantCategory.casino": "<PERSON><PERSON><PERSON>", "merchantCategory.casinoGambling": "Kasino og spil", "merchantCategory.cellular": "Mobiltelefoni", "merchantCategory.charity": "Velgørenhed", "merchantCategory.childcare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.cigarette": "Cigaretter", "merchantCategory.cinema": "Biograf", "merchantCategory.cinemaEvents": "Biograf og events", "merchantCategory.cleaning": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.cleaningMaintenance": "Rengøring og vedligeholdelse", "merchantCategory.clothes": "<PERSON><PERSON><PERSON>", "merchantCategory.clothingServices": "Tøjservice", "merchantCategory.communicationServices": "Kommunikationstjenester", "merchantCategory.construction": "<PERSON><PERSON><PERSON>", "merchantCategory.cosmetics": "Kosmetik", "merchantCategory.craftsArtSupplies": "Hobby- og kunstartikler", "merchantCategory.datingServices": "Datingtjenester", "merchantCategory.delivery": "Levering", "merchantCategory.dentist": "Tandlæge", "merchantCategory.departmentStores": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.directMarketingSubscription": "Direkte marketing og abonnement", "merchantCategory.discountStores": "Discountbutikker", "merchantCategory.drugs": "Medicin", "merchantCategory.dutyFree": "<PERSON><PERSON><PERSON>", "merchantCategory.education": "Uddannelse", "merchantCategory.electricity": "El", "merchantCategory.electronics": "Elektronik", "merchantCategory.emergencyServices": "N<PERSON>dtjenester", "merchantCategory.equipmentRental": "Udlejning af udstyr", "merchantCategory.evCharging": "Opladning af elbil", "merchantCategory.financialInstitutions": "Finansielle institutioner", "merchantCategory.financialProfessionalServices": "Finansiel og professionel service", "merchantCategory.finesPenalties": "Bøder og gebyrer", "merchantCategory.fitness": "Fitness", "merchantCategory.flights": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.flowers": "Blomster", "merchantCategory.flowersGarden": "Blomster og have", "merchantCategory.food": "Mad", "merchantCategory.freight": "<PERSON><PERSON><PERSON>", "merchantCategory.fuel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.funeralServices": "Bedemandsservice", "merchantCategory.furniture": "<PERSON><PERSON><PERSON>", "merchantCategory.games": "<PERSON><PERSON><PERSON>", "merchantCategory.gas": "Benzin", "merchantCategory.generalMerchandiseRetail": "<PERSON><PERSON><PERSON> varer og detailhandel", "merchantCategory.gifts": "<PERSON><PERSON>", "merchantCategory.government": "<PERSON><PERSON><PERSON><PERSON> yd<PERSON>", "merchantCategory.governmentServices": "Offentlige tjenester", "merchantCategory.hardware": "Isenkram", "merchantCategory.healthMedicine": "Sundhed og medicin", "merchantCategory.homeImprovement": "Boligforbedring", "merchantCategory.homeServices": "Hjemmeservice", "merchantCategory.hotel": "Hotel", "merchantCategory.housing": "<PERSON><PERSON><PERSON>", "merchantCategory.insurance": "Forsikring", "merchantCategory.internet": "Internet", "merchantCategory.kids": "<PERSON><PERSON><PERSON>", "merchantCategory.laundry": "<PERSON><PERSON><PERSON>", "merchantCategory.laundryCleaningServices": "Vaskeri og rengøring", "merchantCategory.legalGovernmentFees": "Juridiske og offentlige gebyrer", "merchantCategory.luxuries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.luxuriesCollectibles": "Luksus og samleobjekter", "merchantCategory.magazines": "<PERSON><PERSON><PERSON>", "merchantCategory.magazinesNews": "Magasiner og nyheder", "merchantCategory.marketplaces": "<PERSON>edsp<PERSON><PERSON>", "merchantCategory.media": "Medier", "merchantCategory.medicine": "Medicin", "merchantCategory.mobileHomes": "Mobilhomes", "merchantCategory.moneyTransferCrypto": "Pengeoverførsel og krypto", "merchantCategory.musicRelated": "Musikrelateret", "merchantCategory.musicalInstruments": "Musikinstrumenter", "merchantCategory.optics": "Optik", "merchantCategory.organizationsClubs": "Organisationer og klubber", "merchantCategory.other": "And<PERSON>", "merchantCategory.parking": "<PERSON><PERSON>", "merchantCategory.pawnShops": "Pantelånere", "merchantCategory.pets": "Kæledyr", "merchantCategory.photoServicesSupplies": "Fotoservice og -udstyr", "merchantCategory.postalServices": "Posttjenester", "merchantCategory.professionalServicesOther": "Professionel service (andet)", "merchantCategory.publicTransport": "Offentlig transport", "merchantCategory.purchases": "<PERSON><PERSON><PERSON>", "merchantCategory.purchasesMiscServices": "Køb og diverse serviceydelser", "merchantCategory.recreationServices": "Fritidstjenester", "merchantCategory.religiousGoods": "<PERSON><PERSON><PERSON><PERSON><PERSON> varer", "merchantCategory.secondhandRetail": "Genbrugsbutikker", "merchantCategory.shoeHatRepair": "Sko- og hattereparation", "merchantCategory.shoeRepair": "Skoreparation", "merchantCategory.softwareApps": "Software og apps", "merchantCategory.specializedRepairs": "Specialreparationer", "merchantCategory.sport": "Sport", "merchantCategory.sportingGoods": "Sportsudstyr", "merchantCategory.sportingGoodsRecreation": "Sportsudstyr og fritid", "merchantCategory.sportsClubsFields": "Sportsklubber og -baner", "merchantCategory.stationaryPrinting": "Kontorartikler og tryk", "merchantCategory.stationery": "Kontorartikler", "merchantCategory.storage": "Opbevaring", "merchantCategory.taxes": "Skatter", "merchantCategory.taxi": "Taxa", "merchantCategory.telecomEquipment": "Telekommunikationsudstyr", "merchantCategory.telephony": "Telefoni", "merchantCategory.tobacco": "Tobak", "merchantCategory.tollRoad": "Betalingsvej", "merchantCategory.tourismAttractionsAmusement": "<PERSON><PERSON><PERSON>, attraktioner og forlystelser", "merchantCategory.towing": "<PERSON><PERSON>", "merchantCategory.toys": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.toysHobbies": "Legetøj og hobbyartikler", "merchantCategory.trafficFine": "Trafikbøde", "merchantCategory.train": "<PERSON><PERSON>", "merchantCategory.travelAgency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, radio og streaming", "merchantCategory.utilities": "Forsyning", "merchantCategory.waterTransport": "Vandtransport", "merchantCategory.wholesaleClubs": "Engrosklubber", "metaMask.subtitle": "Slå tilstand til. Forbind via MetaMask.", "metaMask.title": "Kan du ikke forbinde med Zeal?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Åbn din bank-app", "monerium-bank-deposit.buttet-point.receive-crypto": "Modtag digitale EUR", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Send {fiatCurrencyCode} til din konto", "monerium-bank-deposit.deposit-account-country": "Land", "monerium-bank-deposit.header": "{fullName}s personlige konto", "monerium-bank-details.account-name": "Kontonavn", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT kopieret", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT kopieret", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN kopieret", "monerium-bank-details.to-wallet": "Til wallet", "monerium-bank-details.transfer-fee": "Overførselsgebyr", "monerium-bank-transfer.enable-card.bullet-1": "Gennemfør identitetsbekræftelse", "monerium-bank-transfer.enable-card.bullet-2": "Få personlige kontooplysninger", "monerium-bank-transfer.enable-card.bullet-3": "Indbetal fra bankkonto", "monerium-card-delay-relay.success.cta": "Luk", "monerium-card-delay-relay.success.subtitle": "<PERSON><PERSON> si<PERSON> tager det 3 minutter.", "monerium-card-delay-relay.success.title": "Kom tilbage om 3 min. for at fortsætte.", "monerium-deposit.account-details-info-popup.bullet-point-1": "Alle {fiatCurrencyCode} , du sender til denne konto, bliver automatisk konverteret til {cryptoCurrencyCode} tokens på {cryptoCurrencyChain} Chain og sendt til din wallet", "monerium-deposit.account-details-info-popup.bullet-point-2": "SEND KUN {fiatCurrencyCode} ({fiatCurrencySymbol}) til din konto", "monerium-deposit.account-details-info-popup.title": "<PERSON><PERSON>", "monerium.check_order_status.sending": "Sender", "monerium.not-eligible.cta": "Tilbage", "monerium.not-eligible.subtitle": "Monerium kan ikke oprette en konto til dig. Vælg venligst en anden udbyder.", "monerium.not-eligible.title": "<PERSON>rø<PERSON> en anden udbyder", "monerium.setup-card.cancel": "<PERSON><PERSON><PERSON>", "monerium.setup-card.continue": "Fortsæt", "monerium.setup-card.create_account": "<PERSON><PERSON> konto", "monerium.setup-card.login": "Log ind på Gnosis Pay", "monerium.setup-card.subtitle": "<PERSON><PERSON> eller log ind på din Gnosis Pay-konto for at aktivere øjeblikkelige bankindbetalinger.", "monerium.setup-card.subtitle_personal_account": "Få din personlige konto med Gnosis Pay på få minutter:", "monerium.setup-card.title": "Aktiver bankindbetalinger", "moneriumDepositSuccess.goToWallet": "<PERSON><PERSON> til wallet", "moneriumDepositSuccess.title": "{symbol} modtaget", "moneriumInfo.fees": "Du får 0 % i gebyrer", "moneriumInfo.registration": "Monerium er autoriseret og reguleret som et e-pengeinstitut under den islandske lov om elektroniske penge nr. 17/2013 <link>Læs mere</link>", "moneriumInfo.selfCustody": "De digitale kontanter, du modtager, er i din egen varetægt, og ingen andre har kontrol over dine aktiver.", "moneriumWithdrawRejected.supportText": "<PERSON><PERSON> ikke overføre. P<PERSON><PERSON><PERSON> i<PERSON>, eller <link>kontakt support.</link>", "moneriumWithdrawRejected.title": "Overførsel tilbageført", "moneriumWithdrawRejected.tryAgain": "<PERSON><PERSON><PERSON><PERSON> igen", "moneriumWithdrawSuccess.supportText": "Det kan tage op til 24 timer for din{br}modtager at modtage pengene", "moneriumWithdrawSuccess.title": "Send<PERSON>", "monerium_enable_banner.text": "Aktiver bankoverførsler nu", "monerium_error_address_re_link_required.title": "Wallet skal gen-tilknyttes til Monerium", "monerium_error_duplicate_order.title": "<PERSON><PERSON><PERSON> findes allerede", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "Schweizerfranc", "mt-pelerin-fork.options.chf.short": "Øjeblikkeligt og gratis med Mt Pelerin", "mt-pelerin-fork.options.euro.primary": "Euro", "mt-pelerin-fork.options.euro.short": "Øjeblikkeligt og gratis med Monerium", "mt-pelerin-fork.title": "Hvad vil du indbetale?", "mtPelerinProviderInfo.fees": "<PERSON> 0 % i gebyrer", "mtPelerinProviderInfo.registration": "Mt Pelerin Group Ltd er tilknyttet SO-FIT, et selvregulerende organ anerkendt af de schweiziske finanstilsyn (FINMA) i henhold til loven om hvidvaskning af penge. <link><PERSON>æs mere</link>", "mtPelerinProviderInfo.selfCustody": "De digitale kontanter, du modtager, opbevar<PERSON> i din private tegnebog, og ingen andre har kontrol over dine aktiver", "network-fee-widget.title": "<PERSON><PERSON><PERSON><PERSON>", "network.edit.verifying_rpc": "Veri<PERSON><PERSON> RPC", "network.editRpc.predefined_network_info.subtitle": "Ligesom en VPN forhindrer Zeals RPC'er sporing af dine data.{br}{br}Zeals standard-RPC'er er pålidelige og gennemtestede.", "network.editRpc.predefined_network_info.title": "Zeal privatlivs-RPC", "network.filter.update_rpc_success": "RPC-node gemt", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "Alle netværk", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "Tilføj netværk", "networks.filter.add_modal.chain_list.subtitle": "Tilføj alle EVM-netværk", "networks.filter.add_modal.chain_list.title": "<PERSON><PERSON> <PERSON>", "networks.filter.add_modal.dapp_tip.subtitle": "Skift til dit ønskede EVM-netværk i din yndlings-dApp, s<PERSON> s<PERSON><PERSON><PERSON>, om du vil tilføje det.", "networks.filter.add_modal.dapp_tip.title": "<PERSON><PERSON>ø<PERSON> et netværk fra en dApp", "networks.filter.add_networks.subtitle": "Alle EVM-netværk understøttes", "networks.filter.add_networks.title": "Tilføj netværk", "networks.filter.add_test_networks.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> testnets", "networks.filter.tab.netwokrs": "Netværk", "networks.filter.testnets.title": "Testnets", "nft.widget.emptystate": "Ingen samleobjekter i din wallet", "nft_collection.change_account_picture.subtitle": "Er du sikker på, at du vil opdatere dit profilbillede?", "nft_collection.change_account_picture.title": "Opdater profilbillede til NFT", "nfts.allNfts.pricingPopup.description": "<PERSON><PERSON><PERSON> på samleobjekter er baseret på den senest handlede pris.", "nfts.allNfts.pricingPopup.title": "Prissætning af samleobjekter", "no-passkeys-found.modal.cta": "Luk", "no-passkeys-found.modal.subtitle": "Ingen Zeal-passkeys. <PERSON><PERSON><PERSON> din cloud-konto.", "no-passkeys-found.modal.title": "Ingen passkeys fundet", "notValidEmail.title": "Ikke en gyldig e-mailadresse", "notValidPhone.title": "Dette er ikke et gyldigt telefonnummer", "notification-settings.title": "<PERSON><PERSON><PERSON><PERSON> for notifikationer", "notification-settings.toggles.active-wallets": "Aktive wallets", "notification-settings.toggles.bank-transfers": "Bankoverfø<PERSON><PERSON>", "notification-settings.toggles.card-payments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notification-settings.toggles.readonly-wallets": "Skrivebeskyttede wallets", "ntft.groupHeader.text": "Samleobjekter", "on_ramp.crypto_completed": "Gennemført", "on_ramp.fiat_completed": "Gennemført", "onboarding-widget.subtitle.card_created_from_order.left": "Visa-kort", "onboarding-widget.subtitle.card_created_from_order.right": "<PERSON>kt<PERSON><PERSON><PERSON> k<PERSON>", "onboarding-widget.subtitle.card_order_ready.left": "Fysisk Visa-kort", "onboarding-widget.subtitle.default": "Bankoverførsler og Visa-kort", "onboarding-widget.title.card-order-in-progress": "Fortsæt kortbestilling", "onboarding-widget.title.card_created_from_order": "Kortet er afsendt", "onboarding-widget.title.kyc_approved": "Overførsler og kort er klar", "onboarding-widget.title.kyc_failed": "Konto ikke mulig", "onboarding-widget.title.kyc_not_started": "Fortsæt opsætning", "onboarding-widget.title.kyc_started_documents_requested": "<PERSON>nem<PERSON><PERSON><PERSON> verificering", "onboarding-widget.title.kyc_started_resubmission_requested": "<PERSON>rø<PERSON> verificering igen", "onboarding-widget.title.kyc_started_verification_in_progress": "Bekræfter identitet", "onboarding.loginOrCreateAccount.amountOfAssets": "$10+ mia. i midler", "onboarding.loginOrCreateAccount.cards.subtitle": "Kun tilgængelig i visse regioner. Ved at fortsætte accepterer du vores <Terms>betingelser</Terms> og <PrivacyPolicy>privatlivspolitik</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Visa-kort med højt{br}afkast og ingen gebyrer", "onboarding.loginOrCreateAccount.createAccount": "<PERSON><PERSON> konto", "onboarding.loginOrCreateAccount.earn.subtitle": "<PERSON><PERSON><PERSON><PERSON> varierer; kapital i fare. Ved at fortsætte accepterer du vores <Terms>betingelser</Terms> og <PrivacyPolicy>privatlivspolitik</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "Tjen {percent} om året{br}Betroet af {currencySymbol}5+ mia.", "onboarding.loginOrCreateAccount.earningPerYear": "Tjen {percent}{br}om året", "onboarding.loginOrCreateAccount.login": "Log ind", "onboarding.loginOrCreateAccount.trading.subtitle": "Kapital i fare. Ved at fortsætte accepterer du vores <Terms>betinge<PERSON>er</Terms> og <PrivacyPolicy>privatlivspolitik</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Invester i alt,{br}BTC til S&P", "onboarding.loginOrCreateAccount.trustedBy": "Digitale pengemarkeder{br}Betroet af {assets}", "onboarding.wallet_stories.close": "Luk", "onboarding.wallet_stories.previous": "<PERSON><PERSON><PERSON>", "order-earn-deposit-bridge.deposit": "Indbetaling", "order-earn-deposit-bridge.into": "Ind på", "otpIncorrectMessage": "Bekræftelseskoden er forkert", "passkey-creation-not-possible.modal.close": "Luk", "passkey-creation-not-possible.modal.subtitle": "Vi kunne ikke oprette en passkey til din wallet. <PERSON><PERSON><PERSON> for, at din enhed understøtter passkeys, og prøv igen. <link>Kontakt support</link> hvis problemet fortsætter.", "passkey-creation-not-possible.modal.title": "Kan ikke oprette passkey", "passkey-not-supported-in-mobile-browser.modal.cta": "Download Zeal", "passkey-not-supported-in-mobile-browser.modal.subtitle": "Smart Wallets understøttes ikke på mobilbrowsere.", "passkey-not-supported-in-mobile-browser.modal.title": "Download Zeal-appen for at fortsætte", "passkey-recovery.recovering.deploy-signer.loading-text": "<PERSON><PERSON><PERSON><PERSON><PERSON> passkey", "passkey-recovery.recovering.loading-text": "Gendanner tegnebog", "passkey-recovery.recovering.signer-not-found.subtitle": "Passkey kunne ikke linkes. Kontakt Zeal.", "passkey-recovery.recovering.signer-not-found.title": "Ingen tegnebog fundet", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "<PERSON>rø<PERSON> en anden passkey", "passkey-recovery.select-passkey.banner.subtitle": "<PERSON><PERSON><PERSON> for, at du er logget ind på den korrekte konto på din enhed. Passkeys er kontospecifikke.", "passkey-recovery.select-passkey.banner.title": "Kan du ikke se din tegnebogs passkey?", "passkey-recovery.select-passkey.continue": "<PERSON><PERSON><PERSON><PERSON> passkey", "passkey-recovery.select-passkey.subtitle": "Vælg din tegnebogs passkey for adgang.", "passkey-recovery.select-passkey.title": "<PERSON><PERSON><PERSON><PERSON> passkey", "passkey-story_1.subtitle": "Med en Smart Wallet kan du betale netværksgebyrer med de fleste tokens, så du ikke behøver at have netværkets egen mønt.", "passkey-story_1.title": "Fleksible netværksgebyrer – betal med de fleste tokens", "passkey-story_2.subtitle": "Bygget på Safes brancheførende smart contracts, der sikrer mere end $100 milliarder i over 20 millioner wallets.", "passkey-story_2.title": "<PERSON><PERSON><PERSON> af <PERSON>", "passkey-story_3.subtitle": "Smart Wallets virker på store Ethereum-kompatible netværk. Tjek de understøttede netværk, før du sender aktiver.", "passkey-story_3.title": "Store EVM-netværk understøttes", "password.add.header": "<PERSON><PERSON> adgangskode", "password.add.includeLowerAndUppercase": "Små og store bogstaver", "password.add.includesNumberOrSpecialChar": "Et tal eller symbol", "password.add.info.subtitle": "<PERSON> adgangskode bliver på din enhed.", "password.add.info.t_and_c": "Ved at fortsætte accepterer du vores <Terms>betinge<PERSON><PERSON></Terms> og <PrivacyPolicy>privatlivspolitik</PrivacyPolicy>", "password.add.info.title": "<PERSON> adgangskode bliver på denne enhed", "password.add.inputPlaceholder": "<PERSON><PERSON> adgangskode", "password.add.shouldContainsMinCharsCheck": "10+ tegn", "password.add.subheader": "Du skal bruge din adgangskode til at låse Zeal op", "password.add.success.title": "Adgangskode oprettet 🔥", "password.confirm.header": "Bekræft adgangskode", "password.confirm.passwordDidNotMatch": "Adgangskoderne skal være ens", "password.confirm.subheader": "Indtast din adgangskode en gang til", "password.create_pin.subtitle": "<PERSON>ne adgangskode låser Zeal-appen", "password.create_pin.title": "Opret din adgangskode", "password.enter_pin.title": "Indtast adgangskode", "password.incorrectPin": "<PERSON><PERSON>", "password.pin_is_not_same": "Adgangskoderne er ikke ens", "password.placeholder.enter": "Indtast adgangskode", "password.placeholder.reenter": "Indtast adgangskode igen", "password.re_enter_pin.subtitle": "Indtast den samme adgangskode igen", "password.re_enter_pin.title": "Bekræft adgangskode", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "<PERSON><PERSON><PERSON><PERSON>", "pending-send.details.pending": "<PERSON><PERSON><PERSON><PERSON>", "pending-send.details.processing": "<PERSON><PERSON><PERSON>", "permit-info.modal.description": "Permits er <PERSON><PERSON><PERSON><PERSON>, der, hvis de underskrives, lader apps flytte dine tokens på dine vegne, f.eks. for at foretage et swap.{br}Permits minder o<PERSON> god<PERSON>, men koster dig ingen netværksgebyrer at underskrive.", "permit-info.modal.title": "Hvad er Permits?", "permit.edit-expiration": "Rediger {currency} udløb", "permit.edit-limit": "Rediger {currency} forbrugsgrænse", "permit.edit-modal.expiresIn": "Udløber om...", "permit.expiration-warning": "{currency} advar<PERSON> om udløb", "permit.expiration.info": "{currency} info om udløb", "permit.expiration.never": "Aldrig", "permit.spend-limit.info": "{currency} info om forbrugsgrænse", "permit.spend-limit.warning": "{currency} advarsel om forbrugsgrænse", "phoneNumber.title": "telefonnummer", "physicalCardOrderFlow.cardOrdered": "<PERSON>rt bestilt", "physicalCardOrderFlow.city": "By", "physicalCardOrderFlow.orderCard": "<PERSON><PERSON> kort", "physicalCardOrderFlow.postcode": "Postnummer", "physicalCardOrderFlow.shippingAddress.subtitle": "Her sendes dit kort hen", "physicalCardOrderFlow.shippingAddress.title": "<PERSON>ering<PERSON><PERSON><PERSON>", "physicalCardOrderFlow.street": "Vejnavn og nummer", "placeholderDapps.1inch.description": "Veksl via de bedste ruter", "placeholderDapps.aave.description": "Lån og udlån tokens", "placeholderDapps.bungee.description": "Bridge netværk via de bedste ruter", "placeholderDapps.compound.description": "Lån og udlån tokens", "placeholderDapps.cowswap.description": "Veksl til de bedste kurser på Gnosis", "placeholderDapps.gnosis-pay.description": "Administrer dit Gnosis Pay-kort", "placeholderDapps.jumper.description": "Bridge netværk via de bedste ruter", "placeholderDapps.lido.description": "Stake ETH for at få flere ETH", "placeholderDapps.monerium.description": "e-penge og bankoverførsler", "placeholderDapps.odos.description": "Veksl via de bedste ruter", "placeholderDapps.stargate.description": "Bridge eller stake for <14% APY", "placeholderDapps.uniswap.description": "En af de mest populære børser", "pleaseAllowNotifications.cardPayments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pleaseAllowNotifications.customiseInSettings": "<PERSON>il<PERSON> i indstillinger", "pleaseAllowNotifications.enable": "Aktiver", "pleaseAllowNotifications.forWalletActivity": "For wallet-aktivitet", "pleaseAllowNotifications.title": "Få wallet-notifikationer", "pleaseAllowNotifications.whenReceivingAssets": "Når du modtager aktiver", "portfolio.quick-actions.add_funds": "Indbetal", "portfolio.quick-actions.buy": "<PERSON><PERSON><PERSON>", "portfolio.quick-actions.deposit": "Indsæt", "portfolio.quick-actions.send": "Send", "portfolio.view.lastRefreshed": "Opdateret {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Fyld din testnet AVAX op", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Gå til Faucet", "portfolio.view.topupTestNet.BscTestnet.primary": "Fyld din testnet BNB op", "portfolio.view.topupTestNet.BscTestnet.secondary": "Gå til Faucet", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Fyld din testnet SepETH op", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Gå til Sepolia Faucet", "portfolio.view.topupTestNet.FantomTestnet.primary": "Fyld din testnet FTM op", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Gå til Faucet", "privateKeyConfirmation.banner.subtitle": "Enhver med din private nøgle har adgang til dine midler. Kun svindlere vil bede om den.", "privateKeyConfirmation.banner.title": "<PERSON><PERSON> <PERSON><PERSON><PERSON> r<PERSON>", "privateKeyConfirmation.title": "DEL ALDRIG din private nøgle med nogen", "rating-request.not-now": "Ikke nu", "rating-request.title": "Vil du anbefale <PERSON>?", "receive_funds.address-text": "Dette er din unikke wallet-adresse. Du kan trygt dele den med andre.", "receive_funds.copy_address": "<PERSON><PERSON><PERSON><PERSON> wallet-adresse", "receive_funds.network-warning.eoa.subtitle": "<link>Se liste over standardnetværk</link>. Aktiver sendt på ikke-EVM-netværk vil gå tabt.", "receive_funds.network-warning.eoa.title": "Alle Ethereum-baserede netværk understøttes", "receive_funds.network-warning.scw.subtitle": "<link>Se understøttede netværk</link>. Aktiver sendt på andre netværk vil gå tabt.", "receive_funds.network-warning.scw.title": "Vigtigt: Brug kun understøttede netværk", "receive_funds.scan_qr_code": "Scan en QR-kode", "receiving.in.days": "Udbetales om {days}d", "receiving.this.week": "Udbetales i denne uge", "receiving.today": "Udbetales i dag", "reference.error.maximum_number_of_characters_exceeded": "For mange tegn", "referral-code.placeholder": "Indsæt invitationslink", "referral-code.subtitle": "<PERSON>lik på din vens link, eller indsæt det.", "referral-code.title": "Har en ven sendt dig {bReward}?", "rekyc.verification_deadline.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> verificering inden for {daysUntil} dage for at blive ved med at bruge dit kort.", "rekyc.verification_required.subtitle": "Verificer for at fortsat bruge dit kort.", "reminder.fund": "💸 <PERSON><PERSON><PERSON><PERSON><PERSON> midler — optjen 6 % med det samme", "reminder.onboarding": "🏁 Gør opsætning færdig — optjen 6 % på dine indskud", "remove-owner.confirmation.subtitle": "<PERSON><PERSON> si<PERSON><PERSON><PERSON> tager ændringer 3 min. Dit kort fryses mid<PERSON>, og betalinger er ikke mulige.", "remove-owner.confirmation.title": "<PERSON>rtet fryses i 3 min. under opdatering", "restore-smart-wallet.wallet-recovered": "Tegnebog gendannet", "rewardClaimCelebration.claimedTitle": "Belønninger er allerede indløst", "rewardClaimCelebration.subtitle": "For at invitere venner", "rewardClaimCelebration.title": "Du har tjent", "rewards-warning.subtitle": "<PERSON><PERSON> du fjerner denne kont<PERSON>, mister du midlertidigt adgang til tilknyttede belønninger. Du kan altid gendanne kontoen for at få adgang til dem igen.", "rewards-warning.title": "Du mister adgang til dine belønninger", "rewards.copiedInviteLink": "Invitationslink kopieret", "rewards.createAccount": "<PERSON><PERSON>r invitationslink", "rewards.header.subtitle": "Vi sender {aR<PERSON><PERSON>} til dig og {bReward} til din ven, når de bruger {bSpendLimitReward}.", "rewards.header.title": "Få {amountA}{br}Giv {amountB}", "rewards.sendInvite": "Send invitation", "rewards.sendInviteTip": "<PERSON><PERSON><PERSON><PERSON> en ven, så giver vi dem {bAmount}", "route.fees": "G<PERSON><PERSON>r {fees}", "routesNotFound.description": "Vek<PERSON>ruten for {from}-{to} -netværkskombinationen er ikke tilgængelig.", "routesNotFound.title": "Ingen vekselrute tilgængelig", "rpc.OrderBuySignMessage.subtitle": "Via Swaps.IO", "rpc.OrderCardTopupSignMessage.subtitle": "Via Swaps.IO", "rpc.addCustomNetwork.addNetwork": "Tilføj", "rpc.addCustomNetwork.chainId": "Kæde-ID", "rpc.addCustomNetwork.nativeToken": "Netværkets token", "rpc.addCustomNetwork.networkName": "Netværksnavn", "rpc.addCustomNetwork.operationDescription": "Giver dette website tilladelse til at tilføje et netværk til din wallet. Zeal kan ikke kontrollere sikkerheden for brugerdefinerede netværk, så sørg for at du forstår risiciene.", "rpc.addCustomNetwork.rpcUrl": "RPC-URL", "rpc.addCustomNetwork.subtitle": "<PERSON><PERSON><PERSON> {name}", "rpc.addCustomNetwork.title": "Tilføj netværk", "rpc.send_token.network_not_supported.subtitle": "Vi arbejder på at muliggøre transaktioner på dette netværk. Tak for din tålmodighed 🙏", "rpc.send_token.network_not_supported.title": "Netværk kommer snart", "rpc.send_token.send_or_receive.settings": "<PERSON><PERSON><PERSON><PERSON>", "rpc.sign.accept": "Accepter", "rpc.sign.cannot_parse_message.body": "Vi kunne ikke afkode denne besked. Acceptér kun denne an<PERSON>, hvis du stoler på appen.{br}{br}Beskeder kan bruges til at logge dig ind i en app, men de kan og<PERSON>å give apps kontrol over dine tokens.", "rpc.sign.cannot_parse_message.header": "Fortsæt med forsigtighed", "rpc.sign.import_private_key": "Importer nøgler", "rpc.sign.subtitle": "For {name}", "rpc.sign.title": "Underskriv", "safe-creation.success.title": "Wallet oprettet", "safe-safety-checks-popup.title": "Sikkerhedstjek af transaktion", "safetyChecksPopup.title": "Sikkerhedstjek af side", "scan_qr_code.description": "Scan en wallet-QR eller forbind til en app", "scan_qr_code.show_qr_code": "Vis min QR-kode", "scan_qr_code.tryAgain": "<PERSON><PERSON><PERSON><PERSON> igen", "scan_qr_code.unlockCamera": "Giv kameraadgang", "screen-lock-missing.modal.close": "Luk", "screen-lock-missing.modal.subtitle": "<PERSON> enhed kræver en skærmlå<PERSON> for at bruge passkeys. Indstil en skærmlås og prøv igen.", "screen-lock-missing.modal.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mangler", "seedConfirmation.banner.subtitle": "Enhver med din hemmelige sætning har adgang til dine midler. Kun svindlere vil bede om den.", "seedConfirmation.title": "DEL ALDRIG din hemmelige sætning med nogen", "select-active-owner.subtitle": "Flere wallets er tilknyttet. Vælg én til Zeal. Du kan altid skifte.", "select-active-owner.title": "<PERSON><PERSON><PERSON><PERSON>", "select-card.title": "<PERSON><PERSON><PERSON><PERSON> kort", "select-crypto-currency-title": "Vælg token", "select-token.title": "Vælg token", "selectEarnAccount.chf.description.steps": "· Hæv penge 24/7, ingen binding {br}· Renter tilskrives hvert sekund {br}· Overbeskyttede indskud i <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} om året i CHF", "selectEarnAccount.eur.description.steps": "· Hæv penge 24/7, ingen binding {br}· <PERSON><PERSON> tilskrives hvert sekund {br}· Oversikrede lån med <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} om året i EUR", "selectEarnAccount.subtitle": "Du kan ændre det når som helst", "selectEarnAccount.title": "<PERSON><PERSON><PERSON><PERSON> valuta", "selectEarnAccount.usd.description.steps": "· Hæv penge 24/7, ingen binding {br}· Renter tilskrives hvert sekund {br}· Oversikrede indskud i <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} om året i USD", "selectEarnAccount.zero.description_general": "Behold digitale kontanter uden at optjene renter", "selectEarnAccount.zero.title": "0% om året", "selectRechargeThreshold.button.enterAmount": "Indtast beløb", "selectRechargeThreshold.button.setTo": "Indstil til {amount}", "selectRechargeThreshold.description.line1": "<PERSON><PERSON>r dit kort falder til under {amount}, genoplades det automatisk tilbage til {amount} fra din Optjen-konto.", "selectRechargeThreshold.description.line2": "<PERSON>t lavere må<PERSON><PERSON><PERSON>b holder flere penge på din Optjen-konto (optjener 3%). Du kan ændre det når som helst.", "selectRechargeThreshold.title": "Indstil kortets målsaldo", "select_currency_to_withdraw.select_token_to_withdraw": "Vælg token til udbetaling", "send-card-token.form.send": "Send", "send-card-token.form.send-amount": "Optankningsbeløb", "send-card-token.form.title": "<PERSON><PERSON>d kontanter på kortet", "send-card-token.form.to-address": "<PERSON><PERSON>", "send-safe-transaction.network-fee-widget.error": "Du skal bruge {amount} eller vælge et andet token", "send-safe-transaction.network-fee-widget.no-fee": "<PERSON><PERSON> gebyrer", "send-safe-transaction.network-fee-widget.title": "<PERSON><PERSON><PERSON><PERSON>", "send-safe-transaction.network_fee_widget.title": "Netværksgebyr", "send.banner.fees": "Du mangler {amount} mere {currency} for at betale gebyrer", "send.banner.toAddressNotSupportedNetwork.subtitle": "Modtagerens wallet understøtter ikke {network}. Skift til et understøttet token.", "send.banner.toAddressNotSupportedNetwork.title": "Netværk understøttes ikke af modtager", "send.banner.walletNotSupportedNetwork.subtitle": "Smart Wallets kan ikke lave transaktioner på {network}. Skift til et understøttet token.", "send.banner.walletNotSupportedNetwork.title": "Tokenets netværk understøttes ikke", "send.empty-portfolio.empty-state": "Vi fandt ingen tokens", "send.empty-portfolio.header": "Tokens", "send.titile": "Send", "sendLimit.success.subtitle": "Din daglige forbrugsgrænse opdateres om 3 minutter. Indtil da kan du fortsat bruge inden for din nuværende grænse.", "sendLimit.success.title": "<PERSON><PERSON> tager 3 minutter", "send_crypto.form.disconnected.cta.addFunds": "Indsæt penge", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Skift til {network}", "send_crypto.form.disconnected.label": "Beløb til overførsel", "send_to.qr_code.description": "Scan en QR-kode for at sende til en wallet", "send_to.qr_code.title": "Scan QR-kode", "send_to_card.header": "Send til kortadresse", "send_to_card.select_sender.add_wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "send_to_card.select_sender.header": "<PERSON><PERSON><PERSON><PERSON>", "send_to_card.select_sender.search.default_placeholder": "<PERSON><PERSON><PERSON> efter adresse eller <PERSON>", "send_to_card.select_sender.show_card_address_button_description": "<PERSON><PERSON> k<PERSON>e", "send_token.form.select-address": "<PERSON><PERSON><PERSON><PERSON> adresse", "send_token.form.send-amount": "Send beløb", "send_token.form.title": "Send", "setLimit.amount.error.zero_amount": "Du vil ikke kunne foretage betalinger", "setLimit.error.max_limit_reached": "<PERSON><PERSON>t grænse til maks. {amount}", "setLimit.error.same_as_current_limit": "<PERSON>me som nuværende", "setLimit.placeholder": "Nuværende: {amount}", "setLimit.submit": "Indstil grænse", "setLimit.submit.error.amount_required": "<PERSON><PERSON>", "setLimit.subtitle": "<PERSON>te er det beløb, du kan bruge pr. dag med dit kort.", "setLimit.title": "Indstil daglig forbrugsgrænse", "settings.accounts": "<PERSON><PERSON>", "settings.accountsSeeAll": "Se alle", "settings.addAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.card": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.connections": "App-forbin<PERSON><PERSON>", "settings.currency": "Standardvaluta", "settings.default_currency_selector.title": "Valuta", "settings.discord": "Discord", "settings.experimentalMode": "Eksperimentel tilstand", "settings.experimentalMode.subtitle": "Test nye funktioner", "settings.language": "Sp<PERSON>", "settings.lockZeal": "<PERSON><PERSON><PERSON>", "settings.notifications": "Notifikationer", "settings.open_expanded_view": "Åbn udvidet visning", "settings.privacyPolicy": "Privatlivspolitik", "settings.settings": "<PERSON><PERSON><PERSON><PERSON>", "settings.termsOfUse": "Brugsbetingelser", "settings.twitter": "𝕏 / Twitter", "settings.version": "Version {version} env: {env}", "setup-card.confirmation": "Få et virtuelt kort", "setup-card.confirmation.subtitle": "Betal online, og tilføj det til din {type} wallet for kontaktløse betalinger.", "setup-card.getCard": "<PERSON><PERSON> kort", "setup-card.order.physicalCard": "Fysisk kort", "setup-card.order.physicalCard.steps": "· Et fysisk VISA Gnosis Pay {br}· Tager op til 3 uger at levere {br}· Brug til betalinger i butikker og i hæveautomater. {br}· Tilføj til Apple/Google Wallet (kun i understøttede lande", "setup-card.order.subtitle1": "Du kan bruge flere kort på samme tid", "setup-card.order.title": "Hvilken type kort?", "setup-card.order.virtualCard": "Virt<PERSON><PERSON> kort", "setup-card.order.virtual_card.steps": "· Et digitalt VISA Gnosis Pay {br}· Brug med det samme til onlinebetalinger {br}· Tilføj til Apple/Google Wallet (kun i understøttede lande)", "setup-card.orderCard": "<PERSON><PERSON> kort", "setup-card.virtual-card": "Få et virtuelt kort", "setup.notifs.fakeAndroid.title": "Notifikationer for betalinger og indgående overførsler", "setup.notifs.fakeIos.subtitle": "Zeal kan give dig besked, når du modtager penge eller bruger dit Visa-kort. Du kan ændre dette senere.", "setup.notifs.fakeIos.title": "Notifikationer for betalinger og indgående overførsler", "sign.PermitAllowanceItem.spendLimit": "Forbrugsgrænse", "sign.ledger.subtitle": "Vi har sendt transaktionsanmodningen til din hardware-wallet. Fortsæt venligst der.", "sign.ledger.title": "Signer med hardware-wallet", "sign.passkey.subtitle": "Din <PERSON> bør bede dig om at underskrive med den passkey, der er knyttet til denne wallet. Fortsæt venligst der.", "sign.passkey.title": "<PERSON><PERSON><PERSON><PERSON> passkey", "signal_aborted_for_uknown_reason.title": "Netværksanmodning annulleret", "simulatedTransaction.BridgeTrx.info.title": "Bridge", "simulatedTransaction.CardTopUp.info.title": "Indbetal penge på kort", "simulatedTransaction.CardTopUpTrx.info.title": "Indsæt penge på kortet", "simulatedTransaction.NftCollectionApproval.approve": "Godkend NFT-samling", "simulatedTransaction.OrderBuySignMessage.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.OrderCardTopupSignMessage.title": "Fyld op på kort", "simulatedTransaction.OrderEarnDepositBridge.title": "Indbetal til Earn", "simulatedTransaction.P2PTransaction.info.title": "Send", "simulatedTransaction.PermitSignMessage.title": "Tilladelse", "simulatedTransaction.SingleNftApproval.approve": "Godkend NFT", "simulatedTransaction.UnknownSignMessage.title": "Underskriv", "simulatedTransaction.Withdrawal.info.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.approval.title": "Godkend", "simulatedTransaction.approve.info.title": "Godkend", "simulatedTransaction.p2p.info.account": "Til", "simulatedTransaction.p2p.info.unlabelledAccount": "Wallet uden navn", "simulatedTransaction.unknown.info.receive": "Modtag", "simulatedTransaction.unknown.info.send": "Send", "simulatedTransaction.unknown.using": "<PERSON><PERSON><PERSON> {app}", "simulation.approval.modal.text": "<PERSON><PERSON><PERSON>, giver du en app eller smart contract lov til at bruge dine tokens eller NFT'er i fremtidige transaktioner.", "simulation.approval.modal.title": "Hvad er godkendelser?", "simulation.approval.spend-limit.label": "Forbrugsgrænse", "simulation.approve.footer.for": "For", "simulation.approve.unlimited": "Ubegrænset", "simulationNotAvailable.title": "Ukendt handling", "smart-wallet-activation-view.on": "På", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "1Password kan blokere adgangen til din tegnebog", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "Brug Apple eller Google til sikkert at opsætte Zeal", "smart-wallet.passkey-notice.title": "Undgå 1Password", "spend-limits.high.modal.text": "Angiv en forbrugsgrænse, der ligger tæt på det beløb, du reelt vil bruge med en app eller smart contract. Høje grænser er risikable og kan gøre det lettere for svindlere at stj<PERSON>le dine tokens.", "spend-limits.high.modal.text_sign_message": "<PERSON><PERSON>t grænsen tæt på det, du skal bruge. <PERSON><PERSON>je grænser øger risikoen for, at svindlere stjæler dine tokens.", "spend-limits.high.modal.title": "H<PERSON>j forbrugsgrænse", "spend-limits.modal.text": "En forbrugsgrænse bestemmer, hvor mange tokens en app må bruge på dine vegne. Du kan ændre eller fjerne grænsen når som helst. For din sikkerheds skyld bør du holde forbrugsgrænser tæt på det beløb, du reelt vil bruge med en app.", "spend-limits.modal.title": "Hvad er en forbrugsgrænse?", "spent-limit-info.modal.description": "Forbrugsg<PERSON><PERSON>nsen er, hvor mange tokens en app må bruge for dig. Du kan altid ændre den. For din sikkerheds skyld, sæt grænsen tæt på det, du reelt bruger.", "spent-limit-info.modal.title": "Hvad er en forbrugsgrænse?", "sswaps-io.transfer-provider": "Overførselsudbyder", "storage.accountDetails.activateWallet": "Aktivér wallet", "storage.accountDetails.changeWalletLabel": "Skift wallet-navn", "storage.accountDetails.deleteWallet": "<PERSON><PERSON><PERSON> wallet", "storage.accountDetails.setup_recovery_kit": "Gendannelsessæt", "storage.accountDetails.showPrivateKey": "Vis privat nøgle", "storage.accountDetails.showWalletAddress": "Vis wallet-adresse", "storage.accountDetails.smartBackup": "Backup & gendannelse", "storage.accountDetails.viewSsecretPhrase": "Vis hemmelig sætning", "storage.accountDetails.zealSmartWallets": "Zeal Smart Wallets?", "storage.manageAccounts.title": "Wallets", "submit-userop.progress.text": "Sender", "submit.error.amount_high": "<PERSON><PERSON><PERSON> er for højt", "submit.error.amount_hight": "<PERSON><PERSON><PERSON> er for højt", "submit.error.amount_low": "Beløbet er for lavt", "submit.error.amount_required": "Indtast beløb", "submit.error.maximum_number_of_characters_exceeded": "Reducer antallet af tegn i beskeden", "submit.error.not_enough_balance": "Ikke nok saldo", "submit.error.recipient_required": "Modtager er påkrævet", "submit.error.routes_not_found": "Ruter ikke fundet", "submitSafeTransaction.monitor.title": "Transaktionsresultat", "submitSafeTransaction.sign.title": "Transaktionsresultat", "submitSafeTransaction.state.sending": "Sender", "submitSafeTransaction.state.sign": "Opretter", "submitSafeTransaction.submittingToRelayer.title": "Transaktionsresultat", "submitTransaction.cancel": "<PERSON><PERSON><PERSON>", "submitTransaction.cancel.attemptingToStop": "Forsøger at stoppe", "submitTransaction.cancel.failedToStop": "Stop mislykkedes", "submitTransaction.cancel.stopped": "Stoppet", "submitTransaction.cancel.title": "Forhåndsvisning af transaktion", "submitTransaction.failed.banner.description": "Netværket annullerede transaktionen. Prøv igen, eller kontakt os.", "submitTransaction.failed.banner.title": "Transaktion mislykkedes", "submitTransaction.failed.execution_reverted.title": "Appen havde en fejl", "submitTransaction.failed.execution_reverted_without_message.title": "Appen havde en fejl", "submitTransaction.failed.out_of_gas.description": "Netværket annullerede transaktionen, fordi den brugte flere netværksgebyrer end forventet", "submitTransaction.failed.out_of_gas.title": "Netværksfejl", "submitTransaction.sign.title": "Transaktionsresultat", "submitTransaction.speedUp": "Fremskynd", "submitTransaction.state.addedToQueue": "Føjet til kø", "submitTransaction.state.addedToQueue.short": "<PERSON> kø", "submitTransaction.state.cancelled": "Stoppet", "submitTransaction.state.complete": "{currencyCode} tilføjet til Zeal", "submitTransaction.state.complete.subtitle": "Tjek din Zeal-portefølje", "submitTransaction.state.completed": "Gennemført", "submitTransaction.state.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.includedInBlock": "Inkluderet i blok", "submitTransaction.state.includedInBlock.short": "I blok", "submitTransaction.state.replaced": "Erstattet", "submitTransaction.state.sendingToNetwork": "Sender til netværk", "submitTransaction.stop": "Stop", "submitTransaction.submit": "Indsend", "submitted-user-operation.state.bundled": "<PERSON> kø", "submitted-user-operation.state.completed": "Gennemført", "submitted-user-operation.state.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.pending": "<PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.rejected": "<PERSON><PERSON><PERSON>", "submittedTransaction.failed.title": "Transaktion mislykkedes", "success_splash.card_activated": "Kort aktiveret", "supportFork.give-feedback.title": "Giv feedback", "supportFork.itercom.description": "Zeal hå<PERSON><PERSON>er spørgsmål om indbetalinger, <PERSON><PERSON><PERSON>, belønninger og alt andet", "supportFork.itercom.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>l om wallet", "supportFork.title": "Få hjælp med", "supportFork.zendesk.subtitle": "Gnosis Pay håndterer spørgsmål om kortbetalinger, identitetskontrol eller refusioner", "supportFork.zendesk.title": "Kortbetalinger og identitet", "supported-networks.ethereum.warning": "<PERSON><PERSON><PERSON>", "supportedNetworks.networks": "Understøttede netværk", "supportedNetworks.oneAddressForAllNetworks": "<PERSON>n adresse til alle netværk", "supportedNetworks.receiveAnyAssets": "Modtag aktiver fra understøttede netværk direkte til din Zeal-wallet med den samme adresse", "swap.form.error.no_routes_found": "Ingen ruter fundet", "swap.form.error.not_enough_balance": "Ikke nok saldo", "swaps-io-details.bank.serviceProvider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swaps-io-details.details.processing": "<PERSON><PERSON><PERSON>", "swaps-io-details.pending": "<PERSON><PERSON><PERSON><PERSON>", "swaps-io-details.rate": "<PERSON><PERSON>", "swaps-io-details.serviceProvider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swaps-io-details.transaction.from.processing": "Startet transaktion", "swaps-io-details.transaction.networkFees": "Netværksgebyrer", "swaps-io-details.transaction.state.completed-transaction": "Gennemført transaktion", "swaps-io-details.transaction.state.started-transaction": "Startet transaktion", "swaps-io-details.transaction.to.processing": "Gennemført transaktion", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "Indbetalingen skulle snart være fuldført. Kinetex behandler stadig din transaktion.", "swapsIO.monitoring.awaitingLiqSend.title": "Forsinket", "swapsIO.monitoring.awaitingRecive.title": "<PERSON><PERSON><PERSON><PERSON>", "swapsIO.monitoring.awaitingSend.title": "<PERSON> kø", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "Tokens blev sendt til Kinetex, men returneres snart. Kinetex kunne ikke fuldføre transaktionen på destinationen.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Returnerer tokens", "swapsIO.monitoring.cancelledNoSlash.subtitle": "Tokens er ikke blevet overført pga. en ukendt fejl. Prøv venligst igen.", "swapsIO.monitoring.cancelledNoSlash.title": "Tokens returneret", "swapsIO.monitoring.cancelledSlashed.subtitle": "Tokens er blevet returneret. Kinetex kunne ikke fuldføre transaktionen på destinationen.", "swapsIO.monitoring.cancelledSlashed.title": "Tokens returneret", "swapsIO.monitoring.completed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "taker-metadata.earn": "Optjen i digital USD med Sky", "taker-metadata.earn.aave": "Optjen i digital EUR med Aave", "taker-metadata.earn.aave.cashout24": "<PERSON>æv med det samme, 24/7", "taker-metadata.earn.aave.trusted": "Betroet med $27 mia., 2+ år", "taker-metadata.earn.aave.yield": "Afkast optjenes hvert sekund", "taker-metadata.earn.chf": "Optjen i digitale CHF", "taker-metadata.earn.chf.cashout24": "<PERSON>æv med det samme, 24/7", "taker-metadata.earn.chf.trusted": "Betroet med 28 mio. CHF", "taker-metadata.earn.chf.yield": "Afkast tilskrives hvert sekund", "taker-metadata.earn.usd.cashout24": "<PERSON>æv med det samme, 24/7", "taker-metadata.earn.usd.trusted": "Betroet med $10,7 mia., 5+ år", "taker-metadata.earn.usd.yield": "Afkast optjenes hvert sekund", "test": "Indbetal", "to.titile": "Til", "token.groupHeader.cashback": "Cashback", "token.groupHeader.title": "Aktiver", "token.groupHeader.titleWithSum": "Aktiver {sum}", "token.hidden_tokens.page.title": "Skjulte tokens", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "token.widget.cashback_empty": "Ingen transaktioner endnu", "token.widget.emptyState": "Ingen tokens i din wallet", "tokens.cash": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.approve.for": "For", "top-up-card-from-earn-view.approve.into": "Til", "top-up-card-from-earn-view.swap.from": "<PERSON>a", "top-up-card-from-earn-view.swap.to": "Til", "top-up-card-from-earn-view.withdraw.to": "Til", "top-up-card-from-earn.trx.title.approval": "Godkend swap", "top-up-card-from-earn.trx.title.swap": "Indsæt på kort", "top-up-card-from-earn.trx.title.withdrawal": "<PERSON>æv fra <PERSON>n", "topUpDapp.connectWallet": "Forbind wallet", "topup-fee-breakdown.bungee-fee": "Gebyr fra ekstern udbyder", "topup-fee-breakdown.header": "Transaktionsgebyr", "topup-fee-breakdown.network-fee": "Netværksgebyr", "topup-fee-breakdown.total-fee": "Samlet gebyr", "topup.continue-in-wallet": "Fortsæt i din wallet", "topup.send.title": "Send", "topup.submit-transaction.close": "Luk", "topup.submit-transaction.sent-to-wallet": "Send {amount}", "topup.to": "Til", "topup.transaction.complete.close": "Luk", "topup.transaction.complete.try-again": "<PERSON><PERSON><PERSON><PERSON> igen", "transaction-request.nonce-too-low.modal.button-text": "Luk", "transaction-request.nonce-too-low.modal.text": "En transaktion med det samme serienummer (nonce) er allerede gennemført, så du kan ikke længere indsende denne transaktion. <PERSON><PERSON> kan ske, hvis du laver transaktioner tæt på hinanden, eller hvis du forsøger at fremskynde eller stoppe en transaktion, der allerede er gennemført.", "transaction-request.nonce-too-low.modal.title": "Transaktion med samme nonce er gennemført", "transaction-request.replaced.modal.button-text": "Luk", "transaction-request.replaced.modal.text": "Vi kan ikke spore status for denne transaktion. Enten er den blevet erstattet af en anden transaktion, eller RPC-noden har problemer.", "transaction-request.replaced.modal.title": "Kunne ikke finde transaktionsstatus", "transaction.activity.details.modal.close": "Luk", "transaction.cancel_popup.cancel": "Nej, vent", "transaction.cancel_popup.confirm": "Ja, stop", "transaction.cancel_popup.description": "For at stoppe skal du betale et nyt netværksgebyr i stedet for det oprindelige gebyr på {oldFee}", "transaction.cancel_popup.description_without_original": "For at stoppe skal du betale et nyt netværksgebyr", "transaction.cancel_popup.not_supported.subtitle": "Stop af transaktioner understøttes ikke på {network}", "transaction.cancel_popup.not_supported.title": "<PERSON>kke <PERSON>", "transaction.cancel_popup.stopping_fee": "Netværksgebyr for stop", "transaction.cancel_popup.title": "Stop transaktion?", "transaction.in-progress": "I gang", "transaction.inProgress": "I gang", "transaction.speed_up_popup.cancel": "Nej, vent", "transaction.speed_up_popup.confirm": "<PERSON><PERSON>, <PERSON>nd på", "transaction.speed_up_popup.description": "For at fremskynde skal du betale et nyt netværksgebyr i stedet for det oprindelige gebyr på {amount}", "transaction.speed_up_popup.description_without_original": "For at fremskynde skal du betale et nyt netværksgebyr", "transaction.speed_up_popup.seed_up_fee_title": "Netværksgebyr for fremskyndelse", "transaction.speed_up_popup.title": "Fremskynde transaktion?", "transaction.speedup_popup.not_supported.subtitle": "Fremskyndelse af transaktioner understøttes ikke på {network}", "transaction.speedup_popup.not_supported.title": "<PERSON>kke <PERSON>", "transaction.subTitle.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionDetails.cashback.not-qualified": "Ikke kvalificeret", "transactionDetails.cashback.paid": "{amount} udbetalt", "transactionDetails.cashback.pending": "{amount} afventer", "transactionDetails.cashback.title": "Cashback", "transactionDetails.cashback.unknown": "Ukendt", "transactionDetails.cashback_estimate": "Cashback-skøn", "transactionDetails.category": "<PERSON><PERSON><PERSON>", "transactionDetails.exchangeRate": "Valutakurs", "transactionDetails.location": "Placering", "transactionDetails.payment-approved": "<PERSON><PERSON> godkendt", "transactionDetails.payment-declined": "<PERSON><PERSON> afvist", "transactionDetails.payment-reversed": "<PERSON><PERSON> til<PERSON>ørt", "transactionDetails.recharge.amountSentFromEarn.title": "<PERSON><PERSON><PERSON> sendt fra <PERSON>", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "Genopladt til kort", "transactionDetails.recharge.rate.title": "<PERSON><PERSON>", "transactionDetails.recharge.transactionId.title": "Transaktions-ID", "transactionDetails.refund": "Refusion", "transactionDetails.reversal": "Tilbageførsel", "transactionDetails.transactionCurrency": "Transaktionsvaluta", "transactionDetails.transactionId": "Transaktions-ID", "transactionDetails.type": "Transaktion", "transactionRequestWidget.approve.subtitle": "For {target}", "transactionRequestWidget.p2p.subtitle": "Til {target}", "transactionRequestWidget.unknown.subtitle": "<PERSON><PERSON><PERSON> {target}", "transactionSafetyChecksPopup.title": "Sikkerhedstjek af transaktion", "transactions.main.activity.title": "Aktivitet", "transactions.page.hiddenActivity.title": "Skjult aktivitet", "transactions.page.title": "Aktivitet", "transactions.viewTRXHistory.emptyState": "Ingen transaktioner endnu", "transactions.viewTRXHistory.errorMessage": "Vi kunne ikke indlæse din transaktionshistorik", "transactions.viewTRXHistory.hidden.emptyState": "Ingen skjulte transaktioner", "transactions.viewTRXHistory.noTxHistoryForTestNets": "Aktivitet understøttes ikke for testnet", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "Aktivitet understøttes ikke for testnet{br}<link>Gå til block explorer</link>", "transfer_provider": "Overførselsudbyder", "transfer_setup_with_different_wallet.subtitle": "Bankoverførsler er konfigureret med en anden wallet. Du kan kun have én wallet tilsluttet overførsler.", "transfer_setup_with_different_wallet.swtich_and_continue": "Skift og fortsæt", "transfer_setup_with_different_wallet.title": "Skift wallet", "tx-sent-to-wallet.button": "Luk", "tx-sent-to-wallet.subtitle": "Fortsæt i {wallet}", "unblockProviderInfo.fees": "<PERSON> får de lavest mulige gebyrer: 0 % op til 5.000 $ pr. måned og 0,2 % derover.", "unblockProviderInfo.registration": "Unblock er registreret og godkendt af FNTT til at levere VASP-udvekslings- og depot-tjenester og er en registreret MSB-udbyder hos US Fincen. <link>Læs mere</link>", "unblockProviderInfo.selfCustody": "De <PERSON>e penge, du modtager, er i din egen varetægt, og ingen andre vil have kontrol over dine aktiver.", "unblock_invalid_faster_payment_configuration.subtitle": "Den angivne bankkonto understøtter ikke europæiske SEPA-overførsler eller UK Faster Payments. Angiv venligst en anden konto.", "unblock_invalid_faster_payment_configuration.title": "<PERSON><PERSON> konto p<PERSON>", "unknownTransaction.primaryText": "Korttransaktion", "unsupportedCountry.subtitle": "Bankoverførsler er endnu ikke tilgængelige i dit land.", "unsupportedCountry.title": "<PERSON><PERSON><PERSON> tilgænge<PERSON>g i {country}", "update-app-popup.subtitle": "Den seneste opdatering er spækket med rettelser, funktioner og mere magi. Opdater til den nyeste version, og få endnu mere ud af Zeal.", "update-app-popup.title": "<PERSON><PERSON><PERSON>", "update-app-popup.update-now": "Opdater nu", "user_associated_with_other_merchant.subtitle": "Denne wallet kan ikke bruges til bankoverførsler. Brug venligst en anden wallet, eller kontakt os på Discord for support og opdateringer.", "user_associated_with_other_merchant.title": "Wallet kan ikke bruges", "user_associated_with_other_merchant.try_with_another_wallet": "Prøv med en anden wallet", "user_email_already_exists.subtitle": "Du har allerede konfigureret bankoverførsel med en anden wallet. Prøv igen med den wallet, du brugte tidligere.", "user_email_already_exists.title": "Overførsler konfigureret med en anden wallet", "user_email_already_exists.try_with_another_wallet": "Prøv med en anden wallet", "validation.invalid.iban": "Ugyldigt IBAN", "validation.required": "Påkrævet", "validation.required.first_name": "Fornavn er påkrævet", "validation.required.iban": "IBAN er påkrævet", "validation.required.last_name": "Efternavn er påkrævet", "verify-passkey.cta": "Bekræft adgangsnøgle", "verify-passkey.subtitle": "Be<PERSON><PERSON><PERSON><PERSON>, at din adgangsnøgle er oprettet og sikret korrekt.", "verify-passkey.title": "Bekræft adgangsnøgle", "view-cashback.cashback-next-cycle": "Cashback-sats om {time}", "view-cashback.no-cashback": "0 %", "view-cashback.no-cashback.subtitle": "Indbetal for at få cashback", "view-cashback.pending": "{money} <PERSON><PERSON><PERSON><PERSON>", "view-cashback.pending-rewards.not_paid": "Udbetales om {days}d", "view-cashback.pending-rewards.paid": "Modtaget i denne uge", "view-cashback.received-rewards": "<PERSON><PERSON><PERSON><PERSON>", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> betalinger", "view-cashback.upcoming": "Kommende {money}", "virtual-card-order.configure-safe.loading-text": "<PERSON><PERSON><PERSON> kort", "virtual-card-order.create-order.loading-text": "Aktiverer kort", "virtual-card-order.create-order.success-text": "Kort aktiveret", "virtualCard.activateCard": "Aktiver kort", "walletDeleteConfirm.main_action": "<PERSON><PERSON><PERSON>", "walletDeleteConfirm.subtitle": "Du skal importere den igen for at se portefølje eller lave transaktioner", "walletDeleteConfirm.title": "Fjern wallet?", "walletSetting.header": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "wallet_connect.connect.cancel": "<PERSON><PERSON><PERSON>", "wallet_connect.connect.connect_button": "For<PERSON><PERSON>", "wallet_connect.connect.title": "<PERSON><PERSON>", "wallet_connect.connected.title": "Forbundet", "wallet_connect_add_chain_missing.title": "Netværk ikke understøttet", "wallet_connect_proposal_expired.title": "Forbindelse udløbet", "withdraw": "<PERSON><PERSON><PERSON>", "withdraw.confirmation.close": "<PERSON><PERSON><PERSON>", "withdraw.confirmation.continue": "Bekræft", "withdrawal_request.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withdrawal_request.pending": "<PERSON><PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.cta.primary.connecting": "Forbinder...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Forbind", "zeal-dapp.connect-wallet.cta.secondary": "<PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.title": "Forbind din wallet for at fortsætte", "zealSmartWalletInfo.gas": "Betal netværksgebyrer fleksibelt: Brug populære ERC20-tokens på understøttede chains i stedet for kun netværkets egen token.", "zealSmartWalletInfo.recover": "Ingen seed phrases; Gendan med biometrisk passkey fra din adgangskodeadministrator, iCloud eller Google-konto.", "zealSmartWalletInfo.selfCustodial": "Fuld kontrol med en privat tegnebog; Passkey-signaturer valideres on-chain for at minimere central afhængighed.", "zealSmartWalletInfo.title": "Om Zeal Smart Wallets", "zeal_a_rewards_already_claimed_error.title": "<PERSON><PERSON><PERSON> allerede hævet", "zwidget.minimizedDisconnected.label": "<PERSON>eal afb<PERSON>t"}