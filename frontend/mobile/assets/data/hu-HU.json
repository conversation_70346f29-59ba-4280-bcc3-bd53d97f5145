{"Account.ListItem.details.label": "Részletek", "AddFromAddress.success": "Pénztárca elmentve", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.subtitle": "{count,plural,=0{<PERSON><PERSON><PERSON><PERSON>} one{{count} tárca} other{{count} tárca}}", "AddFromExistingSecretPhrase.SelectPhrase.PhraseItem.title": "Titkos kifejezés {index}", "AddFromExistingSecretPhrase.SelectPhrase.subtitle": "Hozz létre új tárcákat egy meglévő titkos kifejezéseddel", "AddFromExistingSecretPhrase.SelectPhrase.title": "Válassz egy titkos kifejezést", "AddFromExistingSecretPhrase.WalletSelection.subtitle": "A titkos kifejezéseddel több tárcát is lementhetsz. Válaszd ki, mely<PERSON>t szeretnéd has<PERSON>nálni.", "AddFromExistingSecretPhrase.WalletSelection.title": "Tárca gyors hozzáadása", "AddFromExistingSecretPhrase.success": "Tárcák hozzáadva a Zealhoz", "AddFromHardwareWallet.subtitle": "Válaszd ki a hardvertárcádat a Zealhoz való csatlakozáshoz", "AddFromHardwareWallet.title": "Hardvertárca", "AddFromNewSecretPhrase.WalletSelection.subtitle": "Válaszd ki az importálni kívánt pénztárcákat", "AddFromNewSecretPhrase.WalletSelection.title": "Pénztárcák importálása", "AddFromNewSecretPhrase.accounts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AddFromNewSecretPhrase.secretPhraseTip.subtitle": "A titkos kifejezés egy kulcstartóként működik több millió pénztárcához, mindegyik egyedi privát kulccsal.{br}{br}Most annyi pénztárcát <PERSON>, amenny<PERSON>l, <PERSON>s k<PERSON> is hozzáadhatsz újakat.", "AddFromNewSecretPhrase.secretPhraseTip.title": "Titkos kifejezéses pénztárcák", "AddFromNewSecretPhrase.subtitle": "Add meg a titkos kifejezésedet szóközökkel elválasztva", "AddFromNewSecretPhrase.success_secret_phrase_added": "Titkos kifejezés hozzáadva 🎉", "AddFromNewSecretPhrase.success_wallets_added": "Pénztárcák hozzáadva a Zealhez", "AddFromNewSecretPhrase.wallets": "<PERSON><PERSON><PERSON><PERSON>", "AddFromPrivateKey.subtitle": "Add meg a privát kulcs<PERSON>t", "AddFromPrivateKey.success": "Privát kulcs hozzáadva 🎉", "AddFromPrivateKey.title": "Pénztárca helyreállítása", "AddFromPrivateKey.typeOrPaste": "Írd be vagy illeszd be ide", "AddFromSecretPhrase.importWallets": "{count,plural,=0{Nincs tárca kiválasztva} one{Tárca importálása} other{{count} tárca importálása}}", "AddFromTrezor.AccountSelection.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>á<PERSON>", "AddFromTrezor.hwWalletTip.subtitle": "A hardveres pénztárca több millió, különböző című pénztárcát tartalmaz. Most annyit import<PERSON>lhatsz, amennyit szeretnél, és később is hozzáadhatsz újakat.", "AddFromTrezor.hwWalletTip.title": "Importálás hardveres pénztárcák<PERSON>ól", "AddFromTrezor.importAccounts": "{count,plural,=0{Nincs tárca kiválasztva} one{Tárca importálása} other{{count} tárca importálása}}", "AddFromTrezor.success": "Pénztárcák hozzáadva a Zealhez", "ApprovalSpenderTypeCheck.failed.subtitle": "<PERSON><PERSON><PERSON>, mert a felhasználó nem szerződés.", "ApprovalSpenderTypeCheck.failed.title": "A felhasználó p<PERSON>ztárca, nem szerződés", "ApprovalSpenderTypeCheck.passed.subtitle": "Jóváhagyást általában szerződések kapnak.", "ApprovalSpenderTypeCheck.passed.title": "A felhasználó egy okosszerződés", "BestReturns.subtitle": "Ez a swap szolgáltató adja a legmagasabb ho<PERSON>ot, az összes díj<PERSON><PERSON>.", "BestReturnsPopup.title": "<PERSON><PERSON><PERSON><PERSON> hozam", "BlacklistCheck.Failed.subtitle": "Kártékony jelentések a következő által: <source></source>", "BlacklistCheck.Failed.title": "<PERSON>z oldal fekete<PERSON><PERSON> van", "BlacklistCheck.Passed.subtitle": "<PERSON>ncs k<PERSON>rtékony jelentés a következő által: <source></source>", "BlacklistCheck.Passed.title": "<PERSON>z oldal nincs feketelistán", "BlacklistCheck.failed.statusButton.label": "<PERSON>z oldalt jelentették", "BridgeRoute.slippage": "Árfolyameltérés {slippage}", "BridgeRoute.title": "<PERSON><PERSON><PERSON>", "CheckConfirmation.InProgress": "Folyamatban...", "CheckConfirmation.success.splash": "<PERSON><PERSON><PERSON>", "ChooseImportOrCreateSecretPhrase.subtitle": "Importálj egy titkos kife<PERSON>zést, vagy hozz létre egy újat", "ChooseImportOrCreateSecretPhrase.title": "Titkos kifejezés hozzáadása", "ConfirmTransaction.Simuation.Skeleton.title": "Biztonsági <PERSON>...", "ConnectionSafetyCheckResult.passed": "Biztonsági ellenőrz<PERSON>", "ContactGnosisPaysupport": "Gnosis Pay támogatás", "CopyKeyButton.copied": "M<PERSON>ol<PERSON>", "CopyKeyButton.copyYourKey": "Kulcs másolása", "CopyKeyButton.copyYourPhrase": "A kifejezésed másolása", "DAppVerificationCheck.Failed.subtitle": "Az oldal nem szerepel a(z) <source></source>", "DAppVerificationCheck.Failed.title": "Az oldal nem található az app-regiszterekben", "DAppVerificationCheck.Passed.subtitle": "Az oldal szerepel a(z) <source></source>", "DAppVerificationCheck.Passed.title": "Az oldal szerepel az app-regiszterekben", "DAppVerificationCheck.failed.statusButton.label": "Az oldal nem található az app-regiszterekben", "ERC20.tokens.emptyState": "<PERSON><PERSON>", "EditFeeModal.Custom.Eip1559Form.maxPriorityFee.title": "Prioritási díj", "EditFeeModal.Custom.Eip1559Form.priorityFeeNetworkState": "Utolsó {period}: {from} és {to}", "EditFeeModal.Custom.InputMaxBaseFee.networkStateBuffer": "Alapdíj: {baseFee} • Biztonsági puffer: {buffer}x", "EditFeeModal.Custom.InputMaxBaseFee.pollableFailedToFetch": "<PERSON><PERSON> le<PERSON>érni az alapdíjat", "EditFeeModal.Custom.InputNonce.biggerThanCurrent": "Magasabb, mint a következő Nonce. El fog akadni.", "EditFeeModal.Custom.InputNonce.lessThanCurrent": "A nonce nem lehet alacsonyabb a jelenleginél", "EditFeeModal.Custom.InputNonce.nonce": "Nonce {nonce}", "EditFeeModal.Custom.InputPriorityFee.pollableFailedToFetch": "<PERSON><PERSON> k<PERSON>zámítani a prioritási díjat", "EditFeeModal.Custom.LegacyForm.gasPrice.pollableFailedToFetch": "<PERSON><PERSON> a maximális díjat", "EditFeeModal.Custom.LegacyForm.gasPrice.title": "<PERSON><PERSON><PERSON>", "EditFeeModal.Custom.LegacyForm.gasPrice.trxMayTakeLongToProceedGasPriceLow": "<PERSON><PERSON><PERSON><PERSON>, amíg a díjak nem csökkennek", "EditFeeModal.Custom.LegacyForm.maxBaseFee.title": "<PERSON><PERSON><PERSON>", "EditFeeModal.Custom.gasLimit.title": "Gas limit {gasLimit}", "EditFeeModal.Custom.title": "Spec<PERSON><PERSON><PERSON>", "EditFeeModal.Custom.trxMayTakeLongToProceedBaseFeeLow": "El fog akadni, amíg az alapdíj csökken.", "EditFeeModal.Custom.trxMayTakeLongToProceedPriorityFeeLow": "Alacsony dí<PERSON>. <PERSON>.", "EditFeeModal.EditGasLimit.estimatedGas": "Becsült gas: {estimated} • Biztonsági puffer: {multiplier}x", "EditFeeModal.EditGasLimit.lessThanEstimatedGas": "Becsült limit alatt. Sikertelen lesz.", "EditFeeModal.EditGasLimit.lessThanSuggestedGas": "Javasolt limit alatt. Sikertelen lehet.", "EditFeeModal.EditGasLimit.subtitle": "Add meg a tranzakcióhoz használni kívánt gáz maximális mennyiségét. A tranzakciód sikertelen lesz, ha a szükségesnél alacsonyabb limitet állítasz be.", "EditFeeModal.EditGasLimit.title": "Gas limit szerkesztése", "EditFeeModal.EditGasLimit.trxWillFailLessThanMinimumGas": "Kevesebb a minimális gázlimitnél: {minimumLimit}", "EditFeeModal.EditNonce.biggerThanCurrent": "Túl magas a Nonce. El fog akadni.", "EditFeeModal.EditNonce.inputLabel": "<PERSON><PERSON>", "EditFeeModal.EditNonce.lessThanCurrent": "A nonce nem lehet a jelen<PERSON><PERSON><PERSON><PERSON> kisebb.", "EditFeeModal.EditNonce.subtitle": "A tranzakciód el fog akadni, ha nem a következő Nonce-ot adod meg.", "EditFeeModal.EditNonce.title": "<PERSON><PERSON>", "EditFeeModal.Header.NotEnoughBalance.errorMessage": "Szükséges: {amount} a beküldéshez", "EditFeeModal.Header.Time.unknown": "Ismeretlen idő", "EditFeeModal.Header.fee.maxInDefaultCurrency": "Max. {fee}", "EditFeeModal.Header.fee.unknown": "Ismeretlen díj", "EditFeeModal.Header.subsequent_failed": "A becslések elavultak lehetnek, az utolsó frissítés si<PERSON>.", "EditFeeModal.Layout.Header.ariaLabel": "Je<PERSON>leg<PERSON> díj", "EditFeeModal.MaxFee.subtitle": "A maximális díj a leg<PERSON><PERSON><PERSON>, amit egy tranzakció<PERSON>, de általában csak a becsült díjat kell kifizetni. Ez a plusz puffer segít, hogy a tranzakci<PERSON> akkor is tel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ha a hálózat lelassul vagy dr<PERSON>g<PERSON><PERSON> lesz.", "EditFeeModal.MaxFee.title": "<PERSON><PERSON><PERSON> díj", "EditFeeModal.SelectPreset.Time.unknown": "Ismeretlen idő", "EditFeeModal.SelectPreset.ariaLabel": "Díjbeállítás kiválasztása", "EditFeeModal.SelectPreset.fast": "Gyors", "EditFeeModal.SelectPreset.normal": "<PERSON><PERSON><PERSON><PERSON>", "EditFeeModal.SelectPreset.slow": "Lassú", "EditFeeModal.ariaLabel": "Hálózati díj <PERSON>", "FailedSimulation.Confirmation.Item.subtitle": "Belső hiba történt", "FailedSimulation.Confirmation.Item.title": "<PERSON><PERSON> szimulálni a tranzakciót", "FailedSimulation.Confirmation.subtitle": "Biztosan folytatni szeretnéd?", "FailedSimulation.Confirmation.title": "<PERSON><PERSON><PERSON>", "FailedSimulation.Title": "Szimu<PERSON><PERSON><PERSON><PERSON> hiba", "FailedSimulation.footer.subtitle": "Belső hiba történt", "FailedSimulation.footer.title": "<PERSON><PERSON> szimulálni a tranzakciót", "FeeForecastWidget.NotEnoughBalance.errorMessage": "Szükséges {amount} a tranzakció elküldéséhez", "FeeForecastWidget.TrxMayTakeLongToProceed.errorMessage": "A feldolgozás sokáig tarthat", "FeeForecastWidget.networkFee": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> díj", "FeeForecastWidget.pollableErroredAndUserDidNotSelectCustomPreset.widgetErrorMessage": "<PERSON><PERSON> a hálózati díj számítása.", "FeeForecastWidget.subsequentFailed.message": "<PERSON><PERSON><PERSON>, a frissítés sikertelen.", "FeeForecastWidget.unknownDuration": "Ismeretlen", "FeeForecastWidget.unknownFee": "Ismeretlen", "GasCurrencySelector.balance": "Egyenleg: {balance}", "GasCurrencySelector.networkFee": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> díj", "GasCurrencySelector.payNetworkFeesUsing": "Hálózati díjak fizetése ezzel:", "GasCurrencySelector.removeDefaultGasToken.description": "Díjfizetés a legnagyobb egyenlegből", "GasCurrencySelector.removeDefaultGasToken.title": "Automatikus díjkezelés", "GasCurrencySelector.save": "Men<PERSON>s", "GoogleDriveBackup.BeforeYouBegin.first_point": "Ha elfelejtem a Z<PERSON> j<PERSON>, örökre elveszítem az eszközeimet", "GoogleDriveBackup.BeforeYouBegin.second_point": "Ha elveszítem a hozzáférést a Google Drive-omhoz, v<PERSON><PERSON> m<PERSON> a helyreállítási fájlomat, örökre elveszítem az eszközeimet", "GoogleDriveBackup.BeforeYouBegin.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rtsd meg és fogadd el a következő pontot a privát pénztá<PERSON><PERSON><PERSON> ka<PERSON>ban:", "GoogleDriveBackup.BeforeYouBegin.third_point": "A Zeal nem tud segíteni a Zeal jelszavam vagy a Google Drive-hozzáférésem helyreállításában", "GoogleDriveBackup.BeforeYouBegin.title": "<PERSON><PERSON><PERSON><PERSON> el<PERSON>denéd", "GoogleDriveBackup.loader.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hagyd jóvá a Google Drive-on a helyreállítási fájl feltöltés<PERSON><PERSON><PERSON><PERSON><PERSON> kérést", "GoogleDriveBackup.loader.title": "Jóváhagyásra vár...", "GoogleDriveBackup.success": "Si<PERSON>es mentés 🎉", "MonitorOffRamp.overServiceTime": "A legtöbb átutalás befejeződik {estimated_time}, de néha a tová<PERSON>i ellenőrzések miatt tovább tarthatnak. <PERSON><PERSON> norm<PERSON>, és a pénzed biztonságban van, amíg ezeket az ellenőrzéseket elvégzik.{br}{br}Ha a tranzakció nem fejeződik be {support_soft_deadline}-n belül, kér<PERSON><PERSON><PERSON>, {contact_support}", "MonitorOnRamp.contactSupport": "Lépj kapcsolatba az ügyfélszolgálattal", "MonitorOnRamp.from": "Innen", "MonitorOnRamp.fundsReceived": "Összeg megérkezett", "MonitorOnRamp.overServiceTime": "A legtöbb átutalás ennyi időn belül lezárul: {estimated_time}, de néha a további ellenőrzések miatt tovább tarthatnak. <PERSON><PERSON> norm<PERSON>, az összeg biztonságban van az ellenőrzések alatt.{br}{br}Ha a tranzakció nem zárul le ennyi időn belül: {support_soft_deadline}, k<PERSON><PERSON><PERSON>, {contact_support}", "MonitorOnRamp.sendingToYourWallet": "Küldés a pénztárcádba", "MonitorOnRamp.to": "Ide", "MonitorOnRamp.waitingForTransfer": "<PERSON><PERSON><PERSON>, hogy elutald az összeget", "NftCollectionCheck.failed.subtitle": "A gyűjtemény nincs ellenőrizve itt: <source></source>", "NftCollectionCheck.failed.title": "A gyűjtemény nincs ellenőrizve", "NftCollectionCheck.passed.subtitle": "A gyűjtemény ellenőrizve van itt: <source></source>", "NftCollectionCheck.passed.title": "A gyűjtemény ellenőrizve van", "NftCollectionInfo.entireCollection": "<PERSON><PERSON><PERSON>", "NoSigningKeyStore.createAccount": "Fiók létrehozása", "NonceRangeError.biggerThanCurrent.message": "A tranzakció el fog akadni", "NonceRangeError.lessThanCurrent.message": "A tranzakció sikertelen lesz", "NonceRangeErrorPopup.biggerThanCurrent.subtitle": "A Nonce túl magas. Csökkentsd, hogy a tranzakció ne akadjon el.", "NonceRangeErrorPopup.biggerThanCurrent.title": "A tranzakció el fog akadni", "P2pReceiverTypeCheck.failed.subtitle": "<PERSON><PERSON><PERSON>, hogy a helyes c<PERSON>re küldöd?", "P2pReceiverTypeCheck.failed.title": "A címzett okosszerződés, nem pénztárca.", "P2pReceiverTypeCheck.passed.subtitle": "Általában más pénztárcáknak küldesz.", "P2pReceiverTypeCheck.passed.title": "A címzett egy pénztárca", "PasswordCheck.title": "Jelszó megadása", "PasswordChecker.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, add meg a j<PERSON>, hogy megbizonyosodjunk róla, te vagy az", "PermitExpirationCheck.failed.subtitle": "<PERSON><PERSON><PERSON>, csak a szükséges ideig.", "PermitExpirationCheck.failed.title": "Hosszú lejárati id<PERSON>", "PermitExpirationCheck.passed.subtitle": "Meddig használhatja egy app a tokenjeidet.", "PermitExpirationCheck.passed.title": "A lejárati idő nem túl hosszú", "PrivateKeyValidationError.moreThanMaximumWords": "<PERSON>. {count} sz<PERSON>", "PrivateKeyValidationError.notValidPrivateKey": "Ez nem érvényes privát kulcs", "PrivateKeyValidationError.secretPhraseIsInvalid": "A titkos kifejezés érvénytelen", "PrivateKeyValidationError.wordMisspelledOrInvalid": "#{index} . sz<PERSON> hibás vagy érvénytelen", "PrivateKeyValidationError.wordsCount": "{count,plural,=0{} one{{count} szó} other{{count} szó}}", "RestoreAccount.secretPhraseAndPKNeverLeaveThisDevice": "Titkos adataid nem hagyják el az eszközt.", "SecretPhraseReveal.header": "<PERSON>rd le a titkos kifejezést", "SecretPhraseReveal.hint": "Ne oszd meg a kifejezést senkivel. Tartsd biztonságban, offline.", "SecretPhraseReveal.skip.subtitle": "<PERSON><PERSON><PERSON> ezt k<PERSON><PERSON><PERSON> is megteh<PERSON>, ha a kifejezés leírása előtt elveszíted ezt az eszközt, minden, a tárc<PERSON><PERSON>z adott eszközödet elveszíted", "SecretPhraseReveal.skip.takeTheRisk": "V<PERSON><PERSON>lo<PERSON>", "SecretPhraseReveal.skip.title": "Kihagyod a kifejezés leírását?", "SecretPhraseReveal.skip.writeDown": "<PERSON><PERSON><PERSON>", "SecretPhraseReveal.skipForNow": "Kihagyom", "SecretPhraseReveal.subheader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> le és tartsd biztonságban, offline. <PERSON><PERSON><PERSON><PERSON>, hogy erősítsd meg.", "SecretPhraseReveal.verify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SelectCurrency.tokens": "Tokenek", "SelectCurrency.tokens.emptyState": "<PERSON><PERSON>", "SelectRoute.slippage": "Slippage {slippage}", "SelectRoutes.emptyState": "Nem találtunk útvonalat ehhez a swap-hoz", "SendCryptoCurrency.Flow.Form.Disconnected.Modal.WalletSelector.title": "Tárca csatlakoztatása", "SendERC20.labelAddress.inputPlaceholder": "Pénztárca címke", "SendERC20.labelAddress.subtitle": "Címkézd fel ezt a pénztárcát, hogy később megtaláld.", "SendERC20.labelAddress.title": "Pénztárca felcímkézése", "SendERC20.send_to": "Címzett", "SendERC20.tokens": "Tokenek", "SendOrReceive.bankTransfer.primaryText": "Banki átutalás", "SendOrReceive.bankTransfer.shortText": "Ingy<PERSON><PERSON>, azonnali be- és kiutalás", "SendOrReceive.bridge.primaryText": "<PERSON><PERSON><PERSON>", "SendOrReceive.bridge.shortText": "Tokenek átutalása hálózatok között", "SendOrReceive.receive.primaryText": "Fogadás", "SendOrReceive.receive.shortText": "Tokenek vagy gyűjtemények fogadása", "SendOrReceive.send.primaryText": "<PERSON><PERSON><PERSON><PERSON>", "SendOrReceive.send.shortText": "Tokenek küldése bá<PERSON>ilyen címre", "SendOrReceive.swap.primaryText": "Csere", "SendOrReceive.swap.shortText": "Csere a tokenek között", "SendSafeTransaction.Confirm.loading": "Biztonsági ellenőrzések futtatása…", "SetupRecoveryKit.google.encryptARecoveryFileWithPassword": "Helyreállítási fájl titkosítása jelszóval", "SetupRecoveryKit.google.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "SetupRecoveryKit.google.title": "Google Drive biztonsági mentés", "SetupRecoveryKit.subtitle": "Legalább egy módszerre szükséged lesz a fiókod helyreállításához, ha eltávolítod a Zeal-t vagy eszközt váltasz", "SetupRecoveryKit.title": "Helyreállítási készlet beállítása", "SetupRecoveryKit.writeDown.subtitle": "Titkos kifejezés leírása", "SetupRecoveryKit.writeDown.title": "<PERSON><PERSON><PERSON><PERSON>", "Sign.CheckSafeDeployment.activate": "Aktiválás", "Sign.CheckSafeDeployment.subtitle": "<PERSON><PERSON><PERSON>tt bejelentkeznél egy alkalmazásba, vagy <PERSON> egy off-chain üzenetet, aktiválnod kell az eszközödet ezen a hálózaton. Ez egy Smart Wallet telepítése vagy helyreállítása után történik.", "Sign.CheckSafeDeployment.title": "Eszköz aktiválása ezen a hálózaton", "Sign.Simuation.Skeleton.title": "Biztonsági ellenőrzések futtatása…", "SignMessageSafetyCheckResult.passed": "Biztonsági ellenőrzések <PERSON>", "SignMessageSafetyChecksPopup.title.permits": "Engedély bi<PERSON><PERSON>gi el<PERSON>", "SimulationFailedConfirmation.subtitle": "Szimuláltuk a tran<PERSON><PERSON><PERSON>, és olyan problé<PERSON> tal<PERSON>lt<PERSON>, amely miatt sikertelen lenne. Beküldheted a tranzak<PERSON><PERSON>t, de valószínűleg sikertelen lesz, és elveszítheted a hálózati díjat.", "SimulationFailedConfirmation.title": "A tranzakció valószínűleg sikertelen lesz", "SimulationNotSupported.Title": "A szimuláció nem{br}támogatott ezen:{br}{network}", "SimulationNotSupported.footer.subtitle": "Ettől függetlenül beküldheted a tranzakciót", "SimulationNotSupported.footer.title": "A szimuláció nem támogatott", "SlippagePopup.custom": "<PERSON><PERSON><PERSON><PERSON>", "SlippagePopup.presetsHeader": "Váltási tolerancia", "SlippagePopup.title": "Váltási tolerancia beállí<PERSON>ásai", "SmartContractBlacklistCheck.failed.subtitle": "Kártékony jelentések ettől: <source></source>", "SmartContractBlacklistCheck.failed.title": "A szerződés feketelistán van", "SmartContractBlacklistCheck.passed.subtitle": "<PERSON><PERSON><PERSON> k<PERSON>ékony jelentés ettől: <source></source>", "SmartContractBlacklistCheck.passed.title": "A szerződés nincs feketelistán", "SuspiciousCharactersCheck.Failed.subtitle": "Ez egy gyakori adathalász taktika", "SuspiciousCharactersCheck.Failed.title": "Gyakori adathalász mintákat keresünk", "SuspiciousCharactersCheck.Passed.subtitle": "Ellenőrizzük az adathalász kísérleteket", "SuspiciousCharactersCheck.Passed.title": "A cím nem tartalmaz szokatlan karaktereket", "SuspiciousCharactersCheck.failed.statusButton.label": "A cím szokatlan karaktereket tartalmaz. ", "TokenVerificationCheck.failed.subtitle": "A token nincs listázva itt: <source></source>", "TokenVerificationCheck.failed.title": "{tokenCode} nincs el<PERSON> a CoinGecko által", "TokenVerificationCheck.passed.subtitle": "A token list<PERSON><PERSON><PERSON> van itt: <source></source>", "TokenVerificationCheck.passed.title": "{tokenCode} <PERSON><PERSON><PERSON><PERSON><PERSON> van a CoinGecko által", "TopupDapp.MonitorTransaction.success.splash": "<PERSON><PERSON><PERSON>", "TransactionSafetyCheckResult.passed": "Biztonsági ellenőrzések <PERSON>", "TransactionSimulationCheck.failed.subtitle": "Hiba: {errorMessage}", "TransactionSimulationCheck.failed.title": "A tranzakció valószínűleg sikertelen.", "TransactionSimulationCheck.passed.subtitle": "A szimuláció a következővel készült: <source></source>", "TransactionSimulationCheck.passed.title": "A tranzakció előnézete sikeres volt", "TrezorError.trezor_action_cancelled.action": "Bezárás", "TrezorError.trezor_action_cancelled.subtitle": "Elutasítottad a tranzakciót a hardveres tárc<PERSON>don", "TrezorError.trezor_device_used_elsewhere.action": "<PERSON><PERSON><PERSON>", "TrezorError.trezor_device_used_elsewhere.subtitle": "<PERSON><PERSON><PERSON><PERSON> be minden más nyitott munkamenetet, és próbáld meg újra szinkronizálni a Trezort", "TrezorError.trezor_method_cancelled.action": "<PERSON><PERSON><PERSON>", "TrezorError.trezor_method_cancelled.subtitle": "Engedélyezd a Trezornak a tárcák exportálását a Zealbe", "TrezorError.trezor_permissions_not_granted.action": "<PERSON><PERSON><PERSON>", "TrezorError.trezor_permissions_not_granted.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adj engedélyt a Zealnek az összes tárca megtekintéséhez", "TrezorError.trezor_pin_cancelled.action": "<PERSON><PERSON><PERSON>", "TrezorError.trezor_pin_cancelled.subtitle": "Munkamenet megszakítva az eszközön", "TrezorError.trezor_popup_closed.action": "<PERSON><PERSON><PERSON>", "TrezorError.trezor_popup_closed.subtitle": "A Trezor párbeszédablak váratlanul bezárult", "TrxLikelyToFail.lessThanEstimatedGas.message": "A tranzakció sikertelen lesz", "TrxLikelyToFail.lessThanMinimumGas.message": "A tranzakció sikertelen lesz", "TrxLikelyToFail.lessThanSuggestedGas.message": "Valószínűleg sikertelen lesz", "TrxLikelyToFailPopup.less_than_suggested_gas.subtitle": "A tranzakció Gas limitje túl alacsony. Emeld meg a Gas limitet a javasolt szintre a hiba elkerülése érdekében.", "TrxLikelyToFailPopup.less_than_suggested_gas.title": "A tranzakció valószínűleg sikertelen lesz", "TrxLikelyToFailPopup.less_them_estimated_gas.subtitle": "A Gas limit alacsonyabb a becsült gáznál. Emeld meg a Gas limitet a javasolt szintre.", "TrxLikelyToFailPopup.less_them_estimated_gas.title": "A tranzakció sikertelen lesz", "TrxMayTakeLongToProceedBaseFeeLowPopup.subtitle": "A maximális alapdíj al<PERSON> a jelenlegi alapdíjnál. Emeld meg a maximális alapdí<PERSON>t, hogy a tranzakció ne akadjon el.", "TrxMayTakeLongToProceedBaseFeeLowPopup.title": "A tranzakció el fog akadni", "TrxMayTakeLongToProceedGasPriceLowPopup.subtitle": "A tranzakció maximális díja túl al<PERSON>. Emeld meg a maximális díjat, hogy a tranzakció ne akadjon el.", "TrxMayTakeLongToProceedGasPriceLowPopup.title": "A tranzakció el fog akadni", "TrxMayTakeLongToProceedPriorityFeeLowPopup.subtitle": "A prioritási díj al<PERSON>sonyabb az ajánlottnál. Emeld meg a prioritási díjat a tranzakció felgyorsításához.", "TrxMayTakeLongToProceedPriorityFeeLowPopup.title": "A tranzakció végrehajtása sokáig tarthat", "UnsupportedMobileNetworkLayout.gotIt": "Értem!", "UnsupportedMobileNetworkLayout.subtitle": "Még nem tudsz tranzakciókat végrehajtani vagy üzeneteket aláírni a(z) {networkHexId} azonosítójú hálózaton a Zeal mobil verziójával{br}{br}Válts a böngészőbővítményre, hogy ezen a hálózaton tranzakciókat hajthass végre, amíg mi gőzerővel dolgozunk a hálózat támogatásán 🚀", "UnsupportedMobileNetworkLayout.title": "A hálózatot nem támogatja a Zeal mobil verziója", "UnsupportedSafeNetworkLayout.subtitle": "Nem végezhetsz tranzakciókat és nem írhatsz alá üzeneteket itt: {network} egy Zeal Smart Wallet-tel{br}{br}V<PERSON>lts egy támo<PERSON>ott hálózatra, v<PERSON><PERSON> has<PERSON>j egy Legacy tárcát.", "UnsupportedSafeNetworkLayoutk.title": "A hálózat nem támogatott a Smart Wallet számára", "UserConfirmationPopup.goBack": "<PERSON><PERSON><PERSON><PERSON>", "UserConfirmationPopup.submit": "<PERSON><PERSON><PERSON>", "ViewPrivateKey.header": "Priv<PERSON><PERSON> k<PERSON>", "ViewPrivateKey.hint": "Ne oszd meg a privát kulcsodat senkivel. Tartsd biztonságban, offline.", "ViewPrivateKey.subheader.mobile": "Koppints a privát kulcsod felfedéséhez", "ViewPrivateKey.subheader.web": "Vidd fölé a kurzort a privát kulcsod felfedéséhez", "ViewPrivateKey.unblur.mobile": "Koppints a felfedéshez", "ViewPrivateKey.unblur.web": "Vidd fölé a kurzort a felfedéshez", "ViewSecretPhrase.PasswordChecker.subtitle": "Add meg a jelszavadat a helyreállítási fájl titkosításához. A jövőben emlékezned kell majd rá.", "ViewSecretPhrase.done": "<PERSON><PERSON><PERSON>", "ViewSecretPhrase.header": "Titkos kifejezés", "ViewSecretPhrase.hint": "Ne oszd meg a kifejezést senkivel. Tartsd biztonságban, offline.", "ViewSecretPhrase.subheader.mobile": "Koppints a titkos kifejezésed felfedéséhez", "ViewSecretPhrase.subheader.web": "Vidd fölé a kurzort a titkos kifejezésed felfedéséhez", "ViewSecretPhrase.unblur.mobile": "Koppints a felfedéshez", "ViewSecretPhrase.unblur.web": "Vidd fölé a kurzort a felfedéshez", "account-details.monerium": "<PERSON>z átutalás<PERSON> a Monerium, egy engedélyezett és szabályozott EMI végzi. <link><PERSON>dj meg többet</link>", "account-details.unblock": "Az átutalásokat az Unblock, egy engedélyezett és bejegyzett tőzsdei és letétkezelési szolgáltató végzi. <link>Tudj meg többet</link>", "account-selector.empty-state": "<PERSON><PERSON> p<PERSON>zt<PERSON>", "account-top-up.select-currency.title": "Tokenek", "account.accounts_not_found": "<PERSON>em tal<PERSON> egyetlen pénztárcát sem", "account.accounts_not_found_search_valid_address": "A pénztárca nincs a listádon", "account.add.create_new_secret_phrase": "Titkos kifejezés létrehozása", "account.add.create_new_secret_phrase.subtext": "<PERSON><PERSON>, 12 szavas titkos kifejezés", "account.add.fromRecoveryKit.fileNotFound": "<PERSON><PERSON> a fájlodat", "account.add.fromRecoveryKit.fileNotFound.buttonTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "account.add.fromRecoveryKit.fileNotFound.explanation": "<PERSON><PERSON><PERSON><PERSON>, hogy a megfelelő fiókba léptél-e be, am<PERSON><PERSON> Zeal Backup mappával rendelkezik.", "account.add.fromRecoveryKit.fileNotValid": "A helyreállítási fájl érvénytelen", "account.add.fromRecoveryKit.fileNotValid.explanation": "Ellenőriztük a fájlodat, és vagy nem megfele<PERSON><PERSON> tí<PERSON>, vagy m<PERSON>.", "account.add.import_secret_phrase": "Titkos kifejezés importálása", "account.add.import_secret_phrase.subtext": "<PERSON><PERSON>, Metamask vagy e<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON>", "account.add.select_type.add_hardware_wallet": "<PERSON><PERSON><PERSON>", "account.add.select_type.existing_smart_wallet": "Meglévő Smart Wallet", "account.add.select_type.private_key": "Priv<PERSON><PERSON> k<PERSON>", "account.add.select_type.seed_phrase": "Seed-kifejezés", "account.add.select_type.title": "Pénztárca importálása", "account.add.select_type.zeal_recovery_file": "Zeal he<PERSON>reállítási fájl", "account.add.success.title": "Új tárca létrehozva 🎉", "account.addLabel.header": "Nevezd el a pénztárcádat", "account.addLabel.labelError.labelAlreadyExist": "A címke már létezik. Próbálj másikat.", "account.addLabel.labelError.maxStringLengthExceeded": "Elérted a maximális karakters<PERSON>mot", "account.add_active_wallet.primary_text": "Pénztárca hozzáadása", "account.add_active_wallet.short_text": "Létrehozás, csatlakoztatás vagy importálás", "account.add_from_ledger.success": "Tárcák hozzáadva a Zealhoz", "account.add_tracked_wallet.primary_text": "Írásvédett pénztárca hozzáadása", "account.add_tracked_wallet.short_text": "Portfólió és tevékenység megtekintése", "account.button.unlabelled-wallet": "Címkézetlen pénztárca", "account.create_wallet": "Tárca létrehozása", "account.label.edit.title": "Pénztárca címkéjének szerkesztése", "account.recoveryKit.selectBackupFile.fileDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "account.recoveryKit.selectBackupFile.list.fileCorrupted": "A helyreállítási fájl érvénytelen", "account.recoveryKit.selectBackupFile.subtitle": "Válaszd ki a helyreállítani kívánt fájlt", "account.recoveryKit.selectBackupFile.title": "Helyreállítási fájl", "account.recoveryKit.success.recoveryFileFound": "<PERSON><PERSON>reállítási fájl megtalálva 🎉", "account.select_type_of_account.create_eoa.short": "Hagyományos <PERSON> szakértőknek", "account.select_type_of_account.create_eoa.title": "Seed-kifejezéses pénztárca létrehozása", "account.select_type_of_account.create_safe_wallet.title": "Smart Wallet létrehozása", "account.select_type_of_account.existing_smart_wallet": "Meglévő Smart Wallet", "account.select_type_of_account.hardware_wallet": "<PERSON><PERSON><PERSON>", "account.select_type_of_account.header": "Pénztárca hozzáadása", "account.select_type_of_account.private_key_or_seed_phraze_wallet": "Privát kul<PERSON> / Helyreállító k<PERSON>", "account.select_type_of_account.read_only_wallet": "Csak olvasható pénztárca", "account.select_type_of_account.read_only_wallet.short": "B<PERSON>rmely portfólió megtekintése", "account.topup.title": "Pénz hozzáadása a Zealhoz", "account.view.error.refreshAssets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "account.widget.refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "account.widget.settings": "Beállítások", "accounts.view.copied-text": "Másolva {formattedAddress}", "accounts.view.copiedAddress": "Másolva {formattedAddress}", "action.accept": "Elfogadás", "action.accpet": "<PERSON><PERSON><PERSON><PERSON>", "action.allow": "Engedélyezés", "action.back": "<PERSON><PERSON><PERSON>", "action.cancel": "<PERSON><PERSON><PERSON><PERSON>", "action.card-activation.title": "Kártya aktiválása", "action.claim": "Igénylés", "action.close": "Bezárás", "action.complete-steps": "Befejezés", "action.confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action.continue": "<PERSON><PERSON><PERSON><PERSON>", "action.copy-address-understand": "Ok - <PERSON><PERSON><PERSON> m<PERSON>", "action.deposit": "Be<PERSON>ze<PERSON>s", "action.done": "<PERSON><PERSON><PERSON>", "action.dontAllow": "<PERSON><PERSON> engedélyezd", "action.edit": "szerkesztés", "action.email-required": "E-mail cím megadása", "action.enterPhoneNumber": "Telefonszám megadása", "action.expand": "Kibontás", "action.fix": "Javítás", "action.getStarted": "Kezdés", "action.got_it": "É<PERSON><PERSON>", "action.hide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.import": "Importálás", "action.import-keys": "Kulcsok importálása", "action.importKeys": "Kulcsok importálása", "action.minimize": "<PERSON><PERSON> m<PERSON>", "action.next": "<PERSON><PERSON><PERSON><PERSON>", "action.ok": "OK", "action.reduceAmount": "Maximum beállítása", "action.refreshWebsite": "<PERSON><PERSON><PERSON> f<PERSON>", "action.remove": "Eltávolítás", "action.remove-account": "Fiók eltávolítása", "action.requestCode": "<PERSON><PERSON><PERSON>", "action.resend_code": "<PERSON><PERSON><PERSON>", "action.resend_code_with_time": "<PERSON><PERSON><PERSON> {time}", "action.retry": "Újra", "action.reveal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action.save": "Men<PERSON>s", "action.save_changes": "RPC mentése", "action.search": "Keresés", "action.seeAll": "Összes megtekintése", "action.select": "Kiválasztás", "action.send": "<PERSON><PERSON><PERSON><PERSON>", "action.skip": "Kiha<PERSON><PERSON>", "action.submit": "<PERSON><PERSON><PERSON><PERSON>", "action.understood": "É<PERSON><PERSON>", "action.update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action.update-gnosis-pay-owner.complete": "Befejezés", "action.zeroAmount": "Összeg megadása", "action_bar_title.defi": "<PERSON><PERSON><PERSON>", "action_bar_title.nfts": "Gyűjtemények", "action_bar_title.tokens": "Tokenek", "action_bar_title.transaction_request": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activate-monerium.loading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activate-monerium.success.title": "Monerium engedélyezve", "activate-physical-card-widget.subtitle": "A kézbesítés 3 hetet is igénybe vehet", "activate-physical-card-widget.title": "Fizikai kártya aktiválása", "activate-smart-wallet.title": "Pénztárca aktiválása", "active_and_tracked_wallets.title": "A Zeal minden díjadat fedezi a(z) {network} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, így ingyenesen tranzaktálhatsz!", "activity.approval-amount.revoked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.approval-amount.unlimited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.approval.approved_for": "Jóváhagyva neki:", "activity.approval.approved_for_with_target": "Jóváhagyva {approvedTo}", "activity.approval.revoked_for": "<PERSON><PERSON><PERSON><PERSON>va tőle:", "activity.bank.serviceProvider": "S<PERSON>lgáltató", "activity.bridge.serviceProvider": "S<PERSON>lgáltató", "activity.cashback.period": "Pénzvisszatérítés időszaka", "activity.filter.card": "<PERSON><PERSON><PERSON><PERSON>", "activity.rate": "Árfolyam", "activity.receive.receivedFrom": "Fogadva tőle:", "activity.send.sendTo": "<PERSON><PERSON><PERSON><PERSON> neki:", "activity.smartContract.unknown": "Ismeretlen szerződés", "activity.smartContract.usingContract": "<PERSON><PERSON><PERSON><PERSON> szerződés:", "activity.subtitle.pending_timer": "{timerString} Függőben", "activity.title.arbitrary_smart_contract_interaction": "{function} ezen: {smartContract}", "activity.title.arbitrary_unknown_smart_contract": "Ismeretlen szerződésinterakció", "activity.title.bridge.from": "<PERSON><PERSON>d innen: {token}", "activity.title.bridge.to": "Híd ide: {token}", "activity.title.buy": "Vásárlás: {asset}", "activity.title.card_owners_updated": "Kártyatulajdonosok frissítve", "activity.title.card_spend_limit_updated": "Kártya költési limit beállítva", "activity.title.cashback_deposit": "Befizetés a Pénzvisszatérítésbe", "activity.title.cashback_reward": "Jóvá<PERSON>rt pénzvisszatérítés", "activity.title.cashback_withdraw": "Kifizetés a Pénzvisszatérítésből", "activity.title.claimed_reward": "Igényelt jutalom", "activity.title.deployed_smart_wallet_gnosis": "Fiók létrehozva", "activity.title.deposit_from_bank": "Befizetés bankból", "activity.title.deposit_into_card": "Befize<PERSON>s <PERSON>", "activity.title.deposit_into_earn": "Befizetés ide: {earn}", "activity.title.failed_transaction": "{trxHash}", "activity.title.failed_transaction_with_function": "{function} ezen: {smartContract}", "activity.title.from": "<PERSON><PERSON><PERSON>: {sender}", "activity.title.pendidng_areward_claim": "Jutalom igénylése", "activity.title.pendidng_breward_claim": "Jutalom igénylése", "activity.title.recharge_disabledh": "Kártyafeltöltés letiltva", "activity.title.recharge_set": "Feltöltési cél beállí<PERSON>va", "activity.title.recovered_smart_wallet_gnosis": "Új eszköz telepítése", "activity.title.send_pending": "Cím<PERSON>tt: {receiver}", "activity.title.send_to_bank": "Banknak", "activity.title.swap": "Vásárlás: {token}", "activity.title.to": "Cím<PERSON>tt: {receiver}", "activity.title.withdraw_from_card": "Kifizetés a kártyáról", "activity.title.withdraw_from_earn": "Ki<PERSON><PERSON><PERSON>s innen: {earn}", "activity.transaction.networkFees": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "activity.transaction.state": "Befejezett t<PERSON>ó", "activity.transaction.state.completed": "Befejezett t<PERSON>ó", "activity.transaction.state.failed": "Sikertelen tranzakció", "add-account.section.import.header": "Importálás", "add-another-card-owner": "Másik kártyatulajdonos hozzáadása", "add-another-card-owner.Recommended.footnote": "Add hozzá a Zeal tárcádat további tulajdonosként a Gnosis Pay kártyádhoz", "add-another-card-owner.Recommended.primaryText": "Zeal hozzáadása a Gnosis Payhez", "add-another-card-owner.recommended": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add-owner.confirmation.subtitle": "Biztonsági okokból a beállítások módosítása 3 percet vesz igénybe, ezalatt a kártyádat ideiglenesen befagyasztjuk, és a fizetések nem lesznek lehetségesek.", "add-owner.confirmation.title": "A kártyádat 3 percre befagyasztjuk a beállítások frissítése alatt", "add-readonly-signer-if-not-exist.error.already_in_use.title": "A tárca nem adható ho<PERSON>, m<PERSON><PERSON> <PERSON> van", "add-readonly-signer-if-not-exist.error.already_in_use.try_another_wallet": "Próbálj másik tárcát", "add.account.backup.decrypt.success": "Pénztárca helyreállítva", "add.account.backup.password.passwordIncorrectMessage": "A j<PERSON>zó he<PERSON>en", "add.account.backup.password.subtitle": "Add meg a j<PERSON><PERSON><PERSON><PERSON>, amivel a helyreállítási fájlodat titkosítottad", "add.account.backup.password.title": "Jelszó megadása", "add.account.google.login.subtitle": "Hagyd jóvá a kérést a Google Drive-on.", "add.account.google.login.title": "Jóváhagyásra vár...", "add.readonly.already_added": "A tárca már hozzá van adva", "add.readonly.continue": "<PERSON><PERSON><PERSON><PERSON>", "add.readonly.empty": "Adj meg egy címet vagy <PERSON>-t", "addBankRecipient.title": "Banki kedvezményezett hozzáadása", "add_funds.deposit_from_bank_account": "Befizetés bankszámláról", "add_funds.from_another_wallet": "Másik tárcából", "add_funds.from_crypto_wallet.connect_to_top_up_dapp": "Csatlakozás a topup dApphoz", "add_funds.from_crypto_wallet.connect_to_top_up_dapp.subtitle": "Csatlakoztass b<PERSON><PERSON><PERSON><PERSON> t<PERSON> a Zeal topup dApphoz, és küldj gyorsan pénzt a tárcádba", "add_funds.from_crypto_wallet.header": "Másik tárcából", "add_funds.from_crypto_wallet.header.show_wallet_address": "Tárcacím me<PERSON>", "add_funds.from_exchange.header": "Küldés tőzsdéről", "add_funds.from_exchange.header.copy_wallet_address": "Zeal címed m<PERSON>", "add_funds.from_exchange.header.list_of_exchanges": "Coinbase, Binance stb.", "add_funds.from_exchange.header.open_exchange": "Nyisd meg a tőzsde appot vagy weboldalt", "add_funds.from_exchange.header.selected_token": "Küldés: {token} a Zealba", "add_funds.from_exchange.header.selected_token.subtitle": "Ezen: {network}", "add_funds.from_exchange.header.send_selected_token": "Támogatott token küldése", "add_funds.from_exchange.header.send_selected_token.subtitle": "Válassz támogatott tokent és hálózatot", "add_funds.import_wallet": "Meglévő kriptotárca importálása", "add_funds.title": "Töltsd fel a számlád", "add_funds.transfer_from_exchange": "Átutalás tőzsdéről", "address.add.header": "Lásd a tárc<PERSON> a Zealban{br}csak olvasható módban", "address.add.subheader": "Add meg a címed vagy <PERSON> neved, hogy egy helyen lásd eszközeidet az összes EVM hálózaton. Később létrehozhatsz vagy importálhatsz további tárcákat.", "address_book.change_account.bank_transfers.header": "Banki címzettek", "address_book.change_account.bank_transfers.primary": "Banki címzett", "address_book.change_account.cta": "Tárca követése", "address_book.change_account.search_placeholder": "Cím ho<PERSON>adása vagy keres<PERSON>e", "address_book.change_account.tracked_header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "address_book.change_account.wallets_header": "<PERSON>ktív <PERSON>", "app-association-check-failed.modal.cta": "Újraprób<PERSON><PERSON><PERSON>", "app-association-check-failed.modal.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. Csatlakozási problémák késleltetést okoznak a Passkey-k lekérésekor. Ha a probléma továbbra is fen<PERSON><PERSON>, indítsd újra a Zealt, és próbáld meg még egyszer.", "app-association-check-failed.modal.subtitle.creation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. Csatlakozási problémák késleltetést okoznak a Passkey létrehozásakor. Ha a probléma továbbra is fen<PERSON><PERSON>, indítsd újra a Zealt, és próbáld meg még egyszer.", "app-association-check-failed.modal.title.creation": "<PERSON>z eszközöd nem tudta létrehozni a Passkey-t", "app-association-check-failed.modal.title.signing": "Az eszközöd nem töltötte be a Passkey-ket", "app.app_protocol_group.borrowed_tokens": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.claimable_amount": "Igényelhető összeg", "app.app_protocol_group.health_rate": "Egészs<PERSON><PERSON> mutat<PERSON>", "app.app_protocol_group.lending": "Hitelezés", "app.app_protocol_group.locked_tokens": "<PERSON><PERSON><PERSON><PERSON>", "app.app_protocol_group.nfts": "Gyűjtemények", "app.app_protocol_group.reward_tokens": "<PERSON><PERSON><PERSON>", "app.app_protocol_group.supplied_tokens": "<PERSON><PERSON><PERSON><PERSON> he<PERSON>", "app.app_protocol_group.tokens": "Token", "app.app_protocol_group.vesting_token": "É<PERSON><PERSON><PERSON>dő token", "app.appsGroupHeader.discoverMore": "Továbbiak felfedezése", "app.appsGroupHeader.text": "<PERSON><PERSON><PERSON>", "app.browser.search.placeholder": "Keresés vagy URL beírása", "app.error-banner.cory": "Hibaadatok másolás<PERSON>", "app.error-banner.retry": "Újra", "app.list_item.rewards": "Jutalmak {value}", "app.position_details.health_rate.description": "Az állapotot a kölcsön összegének és a fedezet értékének aránya adja meg.", "app.position_details.health_rate.title": "Mi az állapotmutató?", "approval.edit-limit.label": "Költési limit szerkesztése", "approval.permit_info": "Engedélyinformáció", "approval.spend-limit.edit-modal.cancel": "<PERSON><PERSON><PERSON><PERSON>", "approval.spend-limit.edit-modal.limit-label": "Költési limit", "approval.spend-limit.edit-modal.max-limit-error": "Figyelem, magas limit", "approval.spend-limit.edit-modal.revert": "Változtatások elvetése", "approval.spend-limit.edit-modal.set-to-unlimited": "Beállítás korlátlanra", "approval.spend-limit.edit-modal.submit": "Men<PERSON>s", "approval.spend-limit.edit-modal.title": "Engedé<PERSON>ek szerkesztése", "approval.spend_limit_info": "Mi az a költési limit?", "approval.what_are_approvals": "Mik azok a jóváhagyások?", "apps_list.page.emptyState": "Nincsenek aktív alkalmazások", "backpace.removeLastDigit": "Utolsó számjegy törlése", "backup-banner.backup_now": "Biztons<PERSON>gi <PERSON>", "backup-banner.risk_losing_funds": "Készíts biztons<PERSON> men<PERSON>, vagy kock<PERSON>z<PERSON>od a pénzed elvesztését", "backup-banner.title": "A pénztárcáról nem készült biztonsági mentés", "backupRecoverySmartWallet.noExportPrivateKeys": "Automatikus mentés: A Smart Wallet egy passkey-k<PERSON><PERSON> van <PERSON>ve – nincs szükség seed-kifejezésre vagy privát kul<PERSON>.", "backupRecoverySmartWallet.safeContracts": "Többkulcsos biztonság: A Zeal tárcák Safe szerződéseken futnak, így több eszköz is jóváhagyhat egy tranzakciót. Nincs egyetlen meghibásodási pont.", "backupRecoverySmartWallet.security": "Több eszköz: A tárcádat több eszközön is használhatod a Passkey segítségével. Minden eszköz saját privát kulcsot kap.", "backupRecoverySmartWallet.showLocalPrivateKey": "Szakértői mód: Exportálhatod ennek az eszköznek a privát kulcsát, hasz<PERSON>lhatod egy másik tárc<PERSON>, és csatlakozhatsz a <SafeGlobal>https://safe.global</SafeGlobal> oldalon. <Key>Privát kulcs megjelenítése</Key>", "backupRecoverySmartWallet.storingKeys": "Felhő-szinkronizált: A passkey biztonságosan tárolódik az iCloudban, a Google Jelszókezelőben vagy a jelszókezelődben.", "backupRecoverySmartWallet.title": "Smart Wallet mentés és helyreállítás", "balance-change.card.titile": "<PERSON><PERSON><PERSON><PERSON>", "balanceChange.pending": "Függőben", "balanceChange.symbol-with-network": "{symbol} · {network}", "bank-transfer-select-service-provider-title": "Szolgáltató kiválasztása", "bank-transfer.change-deposit-receiver.subtitle": "Ez a pénztárca fogadja az összes banki befizetést", "bank-transfer.change-deposit-receiver.title": "Fogadó pénztárca beállítása", "bank-transfer.change-owner.subtitle": "Ezzel a pénztárcával jelent<PERSON>zel be és állítod helyre a banki átutalási fiókodat.", "bank-transfer.change-owner.title": "Fióktulajdonos be<PERSON>llítása", "bank-transfer.configrm-change-deposit-receiver.subtitle": "Minden, a Zealbe küldött banki befizetés ebbe a pénztárcába érkezik.", "bank-transfer.configrm-change-deposit-receiver.title": "Fogadó pénztárca módosítása", "bank-transfer.configrm-change-owner.subtitle": "Biztosan megváltoztatod a fióktulajdonost? Ezzel a pénztárcával jelent<PERSON>zel be és állítod helyre a banki átutalási fiókodat.", "bank-transfer.configrm-change-owner.title": "Fióktulajdonos módosítása", "bank-transfer.deposit.widget.status.complete": "<PERSON><PERSON><PERSON>", "bank-transfer.deposit.widget.status.funds_received": "Összeg megérkezett", "bank-transfer.deposit.widget.status.sending_to_wallet": "Küldés a pénztárcába", "bank-transfer.deposit.widget.status.transfer-on-hold": "Átutalás felfüggesztve", "bank-transfer.deposit.widget.status.transfer-received": "Küldés a pénztárcába", "bank-transfer.deposit.widget.subtitle": "{from} -> {to}", "bank-transfer.deposit.widget.title": "Be<PERSON>ze<PERSON>s", "bank-transfer.intro.bulletlist.point_1": "Beállítás az Unblockkal", "bank-transfer.intro.bulletlist.point_2": "Átutalás EUR/GBP és több mint 10 token között", "bank-transfer.intro.bulletlist.point_3": "0% d<PERSON>j havi 5 e<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 0,2%", "bank-transfer.withdrawal.widget.status.fiat-transfer-issued": "Küldés a bankodba", "bank-transfer.withdrawal.widget.status.in-progress": "<PERSON><PERSON><PERSON><PERSON>", "bank-transfer.withdrawal.widget.status.on-hold": "Átutalás felfüggesztve", "bank-transfer.withdrawal.widget.status.success": "<PERSON><PERSON><PERSON>", "bank-transfer.withdrawal.widget.subtitle": "{from} -> {to}", "bank-transfer.withdrawal.widget.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfers.bank-account-actions.remove-this-account": "Fiók eltávolítása", "bank-transfers.bank-account-actions.switch-to-this-account": "Váltás erre a fiókra", "bank-transfers.deposit.fees-for-less-than-5k": "Díjak 5 ezer dollárig", "bank-transfers.deposit.fees-for-more-than-5k": "Díjak 5 ezer dollár felett", "bank-transfers.set-receiving-bank.title": "Fogadó bank beállítása", "bank-transfers.settings.account_owner": "Sz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bank-transfers.settings.receiver_of_bank_deposits": "Banki befizetések fogadója", "bank-transfers.settings.receiver_of_withdrawals": "Kifizetések fogadója", "bank-transfers.settings.registered_email": "Regisztrált e-mail-cím", "bank-transfers.settings.title": "Banki átutalás beállításai", "bank-transfers.settings.withdrawal_receiver_account_code": "{currency} számla", "bank-transfers.setup.bank-account": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bankTransfer.withdraw.max_loading": "Max.: {amount}", "bank_details_do_not_match.got_it": "É<PERSON><PERSON>", "bank_details_do_not_match.subtitle": "A banki azonosító kód és a számlaszám nem egyezik. <PERSON><PERSON><PERSON><PERSON>, hogy he<PERSON><PERSON>n adtad-e meg az adato<PERSON>, majd <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "bank_details_do_not_match.title": "A banki adatok nem egyeznek", "bank_tranfsers.select_country_of_residence.country_not_supported": "Sajnos a banki átutalás még nem érhető el {country} területén", "bank_transfer.deposit.bullet-point.open-your-bank-app": "Nyisd meg a banki alkalmazásodat", "bank_transfer.deposit.bullet-point.send-eur-to-your-account": "Küldj {fiatCurrencyCode} összeget a számládra", "bank_transfer.deposit.header": "{fullName} s<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.kyc_status_widget.subtitle": "Banki átutalások", "bank_transfer.kyc_status_widget.title": "Személyazonosság ellenőrzése", "bank_transfer.personal_details.date_of_birth": "Születési d<PERSON>", "bank_transfer.personal_details.date_of_birth.invalid_format": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfer.personal_details.date_of_birth.too_young": "Legalább 18 évesnek kell lenned", "bank_transfer.personal_details.first_name": "Keresztnév", "bank_transfer.personal_details.last_name": "Vezetéknév", "bank_transfer.personal_details.title": "A te adataid", "bank_transfer.reference.label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (opcionális)", "bank_transfer.reference_message": "K<PERSON>ldve a Zeal-ból", "bank_transfer.residence_details.address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfer.residence_details.city": "<PERSON><PERSON><PERSON>", "bank_transfer.residence_details.country_of_residence": "Lakóhel<PERSON> s<PERSON>inti ország", "bank_transfer.residence_details.country_placeholder": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfer.residence_details.postcode": "Irányí<PERSON>", "bank_transfer.residence_details.street": "Utca", "bank_transfer.residence_details.your_residence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.choose-wallet.test": "Pénztárca hozzáadása", "bank_transfers.choose-wallet.warning.subtitle": "Egyszerre csak egy pénztárcát kapcsolhatsz hozzá. A kapcsolt pénztárcát nem fogod tudni megváltoztatni.", "bank_transfers.choose-wallet.warning.title": "Válassz pénztárcát körültekintően", "bank_transfers.choose_wallet.subtitle": "Válaszd ki a pénztárcát a bankszámládhoz. ", "bank_transfers.choose_wallet.title": "Pénztárca kiválasztása", "bank_transfers.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.currency_is_currently_not_supported": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit-header": "Be<PERSON>ze<PERSON>s", "bank_transfers.deposit.account-name": "Számlatulajdonos neve", "bank_transfers.deposit.account-number-copied": "Számlaszá<PERSON> másolva", "bank_transfers.deposit.amount-input": "Befizetendő összeg", "bank_transfers.deposit.amount-output": "Célösszeg", "bank_transfers.deposit.amount-output.error": "hiba", "bank_transfers.deposit.buttet-point.receive-crypto": "Érkezik: {cryptoCurrencyCode}", "bank_transfers.deposit.continue": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.deposit.currency-not-supported.subtitle": "Banki befizetések {code} pénznemben további <PERSON>sítésig szünetelnek.", "bank_transfers.deposit.currency-not-supported.title": "{code} befizetések jelenleg nem támogatottak", "bank_transfers.deposit.default-token.balance": "Egyenleg: {amount}", "bank_transfers.deposit.deposit-header": "Be<PERSON>ze<PERSON>s", "bank_transfers.deposit.enter_amount": "Add meg az összeget", "bank_transfers.deposit.iban-copied": "IBAN másolva", "bank_transfers.deposit.increase-amount": "A minimális <PERSON> {limit}", "bank_transfers.deposit.loading": "Betöltés", "bank_transfers.deposit.max-limit-reached": "Az összeg meghaladja a maximális átutalási limitet", "bank_transfers.deposit.modal.kyc.button-text": "Kezdés", "bank_transfers.deposit.modal.kyc.text": "Személyazonosságod ellenőrzéséhez szükségünk lesz néhány személyes adatra és dokumentumra. A beküldés általában csak pár percet vesz igénybe.", "bank_transfers.deposit.modal.kyc.title": "Igazold személyazonosságod a limitek növeléséhez", "bank_transfers.deposit.reduce_amount": "Csökkentsd az összeget", "bank_transfers.deposit.show-account.account-number": "Számlaszám", "bank_transfers.deposit.show-account.bic": "BIC/SWIFT", "bank_transfers.deposit.show-account.iban": "IBAN", "bank_transfers.deposit.show-account.sort-code": "Banki azonosító kód", "bank_transfers.deposit.sort-code-copied": "Banki azonosító kód másolva", "bank_transfers.deposit.withdraw-header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.failed_to_load_fee": "Ismeretlen", "bank_transfers.fees": "<PERSON><PERSON><PERSON>", "bank_transfers.increase-amount": "A minimális <PERSON> {limit}", "bank_transfers.insufficient-funds": "<PERSON><PERSON><PERSON> <PERSON> fedezet", "bank_transfers.select_country_of_residence.title": "Hol élsz?", "bank_transfers.setup.cta": "Banki utalás beállítása", "bank_transfers.setup.enter-amount": "Add meg az összeget", "bank_transfers.source_of_funds.form.business_income": "Vállalkozásból származó jövedelem", "bank_transfers.source_of_funds.form.other": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.pension": "Nyugd<PERSON>j", "bank_transfers.source_of_funds.form.salary": "<PERSON><PERSON><PERSON><PERSON>", "bank_transfers.source_of_funds.form.title": "Pénzeszközeid forrása", "bank_transfers.source_of_funds_description.placeholder": "Írd le a pénzeszközök forrását...", "bank_transfers.source_of_funds_description.title": "Mondj többet a pénzeszközeid forrásáról", "bank_transfers.withdraw-header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_transfers.withdraw.amount-input": "Kifizetendő összeg", "bank_transfers.withdraw.max-limit-reached": "Az összeg meghaladja a maximális átutalási limitet", "bank_transfers.withdrawal.verify-id": "Csökkentsd az összeget", "banner.above_maximum_limit.maximum_input_limit_exceeded": "<PERSON><PERSON><PERSON> beviteli limit túllépve", "banner.above_maximum_limit.maximum_limit_per_deposit": "Ez a befizetésenkénti maximális limit", "banner.above_maximum_limit.subtitle": "<PERSON><PERSON><PERSON> beviteli limit túllépve", "banner.above_maximum_limit.title": "Csökkentsd az összeget erre: {amount} vagy k<PERSON>b", "banner.above_maximum_limit.title.default": "Csökkentsd az összeget", "banner.below_minimum_limit.minimum_input_limit_exceeded": "Minimális beviteli limit túllépve", "banner.below_minimum_limit.minimum_limit_for_token": "Ez a minimális limit enn<PERSON>l a tokennél", "banner.below_minimum_limit.title": "Növeld az összeget erre: {amount} vagy több", "banner.below_minimum_limit.title.default": "Növeld az összeget", "breaard.in_porgress.info_popup.cta": "<PERSON><PERSON><PERSON> és szerezz {earn}", "breaard.in_porgress.info_popup.footnote": "A Zeal és a Gnosis Pay kártya használatával elfogadod a jutalomkampány feltételeit.", "breaward.in_porgress.info_popup.bullet_point_1": "<PERSON><PERSON><PERSON> el {remaining} a következő {time} alatt, hogy igényelhesd a jutalmat.", "breaward.in_porgress.info_popup.bullet_point_2": "Csak az érvényes Gnosis Pay vásárlások számítanak bele a költéseidbe.", "breaward.in_porgress.info_popup.bullet_point_3": "A jutalom igénylése után elküldjük a Zeal-fiókodba.", "breaward.in_porgress.info_popup.header": "Szerezz {earn}, ha elköltesz {remaining}", "breward.celebration.for_spending": "Mert a kártyáddal költöttél", "breward.dc25-eligible-celebration.for_spending": "<PERSON><PERSON> <PERSON><PERSON> {limit} között vagy!", "breward.dc25-non-eligible-celebration.for_spending": "<PERSON>em vol<PERSON> az el<PERSON>ő {limit} költő között", "breward.expired_banner.earn_by_spending": "Szerezz {earn} , ha elköltesz {amount}", "breward.expired_banner.reward_expired": "{earn} juta<PERSON><PERSON> le<PERSON>", "breward.in_progress_banner.cta.title": "<PERSON><PERSON><PERSON> és szerezz {earn}", "breward.ready_to_claim.error.try_again": "Újra", "breward.ready_to_claim.error_title": "A jutalom igénylése si<PERSON>telen", "breward.ready_to_claim.in_progress": "Jutalom igénylése...", "breward.ready_to_claim.youve_earned": "<PERSON><PERSON><PERSON><PERSON><PERSON> {earn}!", "breward_already_claimed.title": "A jutalmat már i<PERSON>ted. Ha nem kaptad meg a jutalom tokent, vedd fel a kapcsolatot az ügyfélszolgálattal.", "breward_cannotbe_claimed.title": "A jutalmat jelenleg nem lehet igényelni. K<PERSON><PERSON>j<PERSON>k, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "bridge.best_return": "<PERSON><PERSON><PERSON><PERSON>", "bridge.best_serivce_time": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge.check_status.complete": "<PERSON><PERSON><PERSON>", "bridge.check_status.progress_text": "Hídépítés: {from} → {to}", "bridge.remove_topup": "Feltöltés eltávolítása", "bridge.request_status.completed": "Befejezve", "bridge.request_status.pending": "Függőben", "bridge.widget.completed": "<PERSON><PERSON><PERSON>", "bridge.widget.currencies": "{from} → {to}", "bridge_rote.widget.title": "<PERSON><PERSON><PERSON>", "browse.discover_more_apps": "További alkalmazások felfedezése", "browse.google_search_term": "Keresés: \"{searchTerm}\"", "brward.celebration.you_earned": "<PERSON><PERSON><PERSON>", "brward.expired_banner.subtitle": "Legközelebb több szer<PERSON>t", "brward.in_progress_banner.subtitle": "<PERSON><PERSON><PERSON><PERSON>: {expiredInFormatted}", "buy": "Vásárlás", "buy.enter_amount": "Add meg az összeget", "buy.loading": "Betöltés...", "buy.no_routes_found": "<PERSON><PERSON><PERSON> elér<PERSON><PERSON> útvonal", "buy.not_enough_balance": "<PERSON><PERSON><PERSON> el<PERSON> e<PERSON>", "buy.select-currency.title": "Token kiválasztása", "buy.select-to-currency.title": "Tokenek vásárlása", "buy_form.title": "Token vásárlása", "cancelled-card.create-card-button.primary": "Új virtuális kártya igénylése", "cancelled-card.switch-card-button.primary": "Kártyaváltás", "cancelled-card.switch-card-button.short-text": "Van másik aktív kártyád", "card": "<PERSON><PERSON><PERSON><PERSON>", "card-add-cash.confirm-stage.banner.no-routes-found": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> meg másik <PERSON>t vagy <PERSON>", "card-add-cash.confirm-stage.banner.not-enough-balance-for-fee": "<PERSON><PERSON><PERSON><PERSON><PERSON>ged van {amount} tov<PERSON><PERSON><PERSON> {symbol} a díjak kifizeté<PERSON>", "card-add-cash.confirm-stage.banner.value-loss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {loss} értékben", "card-add-cash.confirm-stage.banner.value-loss.revert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.cancel": "<PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.continue": "<PERSON><PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.enter-amount": "<PERSON><PERSON><PERSON>", "card-add-cash.edit-stage.cta.reduce-to-max": "<PERSON><PERSON>", "card-add-cash.edit-staget.banner.no-routes-found": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> meg másik <PERSON>t vagy <PERSON>", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle": "Elküldtük a kérést. Folytasd a tárcádon.", "card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title": "<PERSON><PERSON> al<PERSON> a hardver tárcán", "card-balance": "Egyenleg: {balance}", "card-cashback.status.title": "Pénzvisszatérítés feltöltése", "card-copy-safe-address.copy_address": "<PERSON><PERSON>m <PERSON>", "card-copy-safe-address.copy_address.done": "M<PERSON>ol<PERSON>", "card-copy-safe-address.warning.description": "Ez a cím csak {cardAsset} fogadására alkalmas a Gnosis Chainen. Ne küldj eszközöket más hálózatokról erre a címre, mert elvesznek.", "card-copy-safe-address.warning.header": "Csak {cardAsset} küldj a Gnosis Chainen", "card-marketing-card.center.subtitle": "Valutaváltási díjak", "card-marketing-card.center.title": "0%", "card-marketing-card.left.subtitle": "<PERSON><PERSON>", "card-marketing-card.right.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "card-marketing-card.title": "Európa magas kamatozású VISA kártyája", "card-marketing-tile.get-started": "Kezdés", "card-select-from-token-title": "Kiindulási token kiválasztása", "card-top-up.banner.subtitle.completed": "Befejezve", "card-top-up.banner.subtitle.failed": "Sikertelen", "card-top-up.banner.subtitle.pending": "{timerString} Függőben", "card-top-up.banner.title": "Befizetés {amount}", "card-topup.select-token.emptyState": "<PERSON><PERSON>", "card.activate.card_number_not_valid": "Érvénytelen kártyaszám. Próbáld <PERSON>.", "card.activate.invalid_card_number": "Érvénytelen kártyaszám.", "card.activation.activate_physical_card": "Fizikai kártya aktiválása", "card.add-cash.amount-to-withdraw": "Feltöltendő összeg", "card.add-from-earn-form.title": "Pénz hozzáadása a kártyához", "card.add-from-earn-form.withdraw-to-card": "<PERSON><PERSON><PERSON><PERSON>", "card.add-from-earn.amount-to-withdraw": "Kártyára kiveendő összeg", "card.add-from-earn.enter-amount": "Összeg megadása", "card.add-from-earn.loading": "Betöltés", "card.add-from-earn.max-label": "Egyenleg: {amount}", "card.add-from-earn.no-routes-found": "<PERSON><PERSON><PERSON> elér<PERSON><PERSON> útvonal", "card.add-from-earn.not-enough-balance": "<PERSON><PERSON><PERSON> el<PERSON> e<PERSON>", "card.add-owner.queued": "Tulajdonos hozzáadása várólistán", "card.add-to-wallet-flow.subtitle": "Fizess a pénztárcádból.", "card.add-to-wallet.copy-card-number": "Másold ki al<PERSON>bb a kártyaszámot", "card.add-to-wallet.title": "Hozzáadás a {platformName} <PERSON><PERSON><PERSON>", "card.add-to-wallet.title.apple": "Apple", "card.add-to-wallet.title.google": "Google", "card.addCash.to-amount-percentage-loss": "(-{percentageLoss}) {toAmount}", "card.cancelled": "TÖRÖLVE", "card.card-owner-not-found.disconnect-btn": "K<PERSON><PERSON><PERSON>lasztása", "card.card-owner-not-found.subtitle": "Frissítsd a kártyatulajdonost.", "card.card-owner-not-found.title": "Kártya újracsatlakoztatása", "card.card-owner-not-found.update-owner-btn": "<PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>", "card.cashback.nextWeekCashbackPercentage.title": "{percentage} m<PERSON>va {date}", "card.cashback.widgetNoCashback.subtitle": "<PERSON><PERSON><PERSON> be a pénzkereséshez", "card.cashback.widgetNoCashback.title": "Akár {defaultPercentage} pénzvisszatérítés", "card.cashback.widgetcashbackValue.rewards": "{amount} függ<PERSON>ben", "card.cashback.widgetcashbackValue.title": "{percentage} pénzvisszatérítés", "card.choose-wallet.connect_card": "<PERSON><PERSON><PERSON><PERSON>", "card.choose-wallet.create-new": "Új tárca hozzáadása tulajdonosként", "card.choose-wallet.import-another-wallet": "Másik pénztárca importálása", "card.choose-wallet.import-current-owner": "Jelenlegi kártyatulajdonos importálása", "card.choose-wallet.import-current-owner.sub-text": "A Gnosis Pay kártyádhoz tartozó privát kulcsok vagy seed phrase importálása", "card.choose-wallet.title": "Válassz tárcát a kártyád kezeléséhez", "card.connectWalletToCardGuide": "Pénztárca címének másolása", "card.connectWalletToCardGuide.addGnosisPayOwner": "Gnosis Pay tulajdonos hozzáadása", "card.connectWalletToCardGuide.addGnosisPayOwner.steps": "1. Nyisd meg a Gnosispay.com oldalt a másik pénztárcáddal{br}2. <PERSON><PERSON><PERSON> a „Fiók” gombra{br}3. <PERSON><PERSON><PERSON> a „Fiókadatok” gombra{br}4. <PERSON><PERSON><PERSON> a „Szerkesztés” gombra a „Fióktulajdonos” mellett, és{br}5. <PERSON><PERSON><PERSON> a „Cím hozzáadása” gombra{br}6. <PERSON><PERSON><PERSON><PERSON> be a Zeal címedet, és kattints a mentésre", "card.connectWalletToCardGuide.header": "Csatlakoztatás: {account} a Gnosis Pay kártyához", "card.connect_card.start": "Meglévő kártya csatolása", "card.copiedAddress": "Másolva {formattedAddress}", "card.disconnect-account.title": "Fiók le<PERSON>lasztása", "card.hw-wallet-support-drop.add-owner-btn": "<PERSON>j tulajdon<PERSON> ho<PERSON>", "card.hw-wallet-support-drop.disconnect-btn": "K<PERSON><PERSON><PERSON>lasztása", "card.hw-wallet-support-drop.subtitle": "Adj hozzá másik tulajdonost a kártyádhoz.", "card.hw-wallet-support-drop.title": "A Zeal nem támogatja a hardver tárcákat.", "card.kyc.continue": "Beállítás folytatása", "card.list_item.title": "<PERSON><PERSON><PERSON><PERSON>", "card.onboarded.transactions.empty.description": "A fizetési tevékenységeid itt fognak megjelenni", "card.onboarded.transactions.empty.title": "Tevékenység", "card.order.continue": "<PERSON><PERSON><PERSON>yarendelés folytatása", "card.order.free_virtual_card": "Ingyenes virtuá<PERSON> k<PERSON>", "card.order.start": "<PERSON><PERSON><PERSON><PERSON>", "card.owner-not-imported.cancel": "<PERSON><PERSON><PERSON><PERSON>", "card.owner-not-imported.import": "Importálás", "card.owner-not-imported.subtitle": "A tranzakció engedélyezéséhez kapcsold össze a Gnosis Pay-fiókod tulajdonosi tárcáját a Zeallel. Megjegyzés: Ez nem azonos a megszokott Gnosis Pay-tárca bejelentkezéseddel.", "card.owner-not-imported.title": "Gnosis Pay-fiók tulajdonos hozzáadása", "card.page.order_free_physical_card": "Ingyenes fizikai kártya", "card.pin.change_pin_at_atm": "A PIN-kód megváltoztatható a kijelölt ATM-eknél", "card.pin.timeout": "A képernyő bezárul ennyi idő múlva: {seconds} mp", "card.quick-actions.add-assets": "Feltöltés", "card.quick-actions.add-cash": "Feltöltés", "card.quick-actions.details": "Részletek", "card.quick-actions.freeze": "<PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.freezing": "Z<PERSON><PERSON><PERSON>...", "card.quick-actions.unfreeze": "<PERSON><PERSON><PERSON><PERSON>", "card.quick-actions.unfreezing": "Feloldás...", "card.quick-actions.withdraw": "Kivétel", "card.read-only-detected.create-new": "Új tárca hozzáadása tulajdonosként", "card.read-only-detected.import-current-owner": "Kulcsok importálása ehhez: {wallet}", "card.read-only-detected.import-current-owner.sub-text": "A tárca privát kulcsainak vagy seed phrase-ének importálása {address}", "card.read-only-detected.title": "Kártya csak olvasható tárcán. Válassz tárcát a kezeléshez.", "card.remove-owner.queued": "Tulajdonos eltávolítása várólistán", "card.settings.disconnect-from-zeal": "Leválasztás a Zealról", "card.settings.edit-owners": "Kártyatulajdonosok módosítása", "card.settings.getCard": "Újabb kártya igénylése", "card.settings.getCard.subtitle": "Virtuális vagy fizikai kártyák", "card.settings.notRecharging": "<PERSON><PERSON><PERSON> automatik<PERSON>", "card.settings.notifications.subtitle": "Értesítések a fizetésekről", "card.settings.notifications.title": "Kártyaértesítések", "card.settings.page.title": "Kártyabeállítások", "card.settings.select-card.cancelled-cards": "Megszüntetett kártyák", "card.settings.setAutoRecharge": "Automatikus feltöltés beállítása", "card.settings.show-card-address": "K<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "card.settings.spend-limit": "Költési limit beállítása", "card.settings.spend-limit-title": "Jelenlegi napi limit: {limit}", "card.settings.switch-active-card": "Aktív kártya váltása", "card.settings.switch-active-card-description": "Aktív k<PERSON>ya: {card}", "card.settings.switch-card.card-item.cancelled": "Megszüntetve", "card.settings.switch-card.card-item.frozen": "<PERSON><PERSON><PERSON><PERSON>", "card.settings.switch-card.card-item.title": "Gnosis Pay kártya", "card.settings.switch-card.card-item.title.physical": "Fizikai kártya", "card.settings.switch-card.card-item.title.virtual": "Virt<PERSON><PERSON><PERSON>", "card.settings.switch-card.title": "Kártya kiválasztása", "card.settings.targetBalance": "<PERSON><PERSON><PERSON> egyenleg: {threshold}", "card.settings.view-pin": "PIN kód megtekintése", "card.settings.view-pin-description": "Mindig védd a PIN kódodat", "card.title": "<PERSON><PERSON><PERSON><PERSON>", "card.transactions.header": "Kártyatranzakciók", "card.transactions.see_all": "Összes tranzakció megtekintése", "card.virtual": "VIRTUÁLIS", "cardCashback.onboarding.bullets.cashback_sent_weekly": "A pénzvisszatérítést a megszerzését követő hét elején küldjük a kártyádra.", "cardCashback.onboarding.bullets.more_deposit_more_earn": "<PERSON><PERSON><PERSON> többet fize<PERSON>z be, an<PERSON>l többet keresel minden vásárlásnál.", "cardCashback.onboarding.title": "A<PERSON><PERSON><PERSON> {percentage} pénzvisszatérítés", "cardCashbackWithdraw.amount": "Kivét összege", "cardCashbackWithdraw.header": "Kivét: {currency}", "cardIsBlockedAndCouldNotBeActivated.title": "A kártya <PERSON> tilt<PERSON>, és nem lehetett aktiválni", "cardWidget.cashback": "Pénzvisszatérítés", "cardWidget.cashbackUpToDefaultPercentage": "<PERSON><PERSON><PERSON><PERSON> {percentage}", "cardWidget.startEarning": "Kezdj el gyűjteni", "cardWithdraw.amount": "Kivételi összeg", "cardWithdraw.header": "Kivétel a kártyáról", "cardWithdraw.selectWithdrawWallet.title": "Válaszd ki a pénztárcát{br} a kivételhez", "cardWithdraw.success.cta": "Bezárás", "cardWithdraw.success.subtitle": "Biztonsági okokból a Gnosis Pay kártyáról történő összes kivétel feldolgozása 3 percet vesz igénybe", "cardWithdraw.success.title": "Ez a módosítás 3 percet vesz igénybe", "card_top_up_trx.send": "<PERSON><PERSON><PERSON><PERSON>", "card_top_up_trx.to": "Címzett", "cards.card_cvv.label": "CVV", "cards.card_expiry_date.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cards.card_number": "K<PERSON>rtyaszám", "cards.choose-wallet.no-active-accounts": "Nincsenek aktív pénztárcáid", "cards.copied_card_number": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cards.transactions.decline_reason.exceeds_approval_amount_limit": "<PERSON><PERSON><PERSON><PERSON> a napi limitet", "cards.transactions.decline_reason.incorrect_pin": "Helytelen PIN-kód", "cards.transactions.decline_reason.incorrect_security_code": "Helytelen bi<PERSON> kód", "cards.transactions.decline_reason.invalid_amount": "Érvénytelen összeg", "cards.transactions.decline_reason.low_balance": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "cards.transactions.decline_reason.other": "Elutasítva", "cards.transactions.decline_reason.pin_tries_exceeded": "Túl sok PIN-próbálkozás", "cards.transactions.status.refund": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cards.transactions.status.reversal": "Sztornó", "cashback-deposit.trx.title": "Befizetés a Pénzvisszatérítésbe", "cashback-estimate.text": "<PERSON><PERSON> egy be<PERSON>, NEM garantált kifizetés. Az összes ismert pénzvisszatérítési szab<PERSON><PERSON> alkalmazzuk, de a Gnosis Pay saját belátása szerint kizárhat tranzakciókat. Hetente legfeljebb {amount} költés jogosít pénzvisszatérítésre, még akkor is, ha a tranzakció becslése magasabb összeget jelezne.", "cashback-estimate.text.fallback": "<PERSON>z egy be<PERSON>, és nem garantált kifizetés. Minden nyilvánosan ismert pénzvisszatérítési szabályt alkalmazunk, de a Gnosis Pay saját belátása szerint kizárhat tranzakciókat.", "cashback-estimate.title": "Pénzvisszatérítés be<PERSON>", "cashback-onbarding-tersm.subtitle": "A kártyatranzakciós adataidat megosztjuk a Karpatkey-vel, amely a pénzvisszatérítési jutalmakat osztja ki. Az elfogadás gombra kattintva elfogadod a Gnosis DAO pénzvisszatérítési <terms>Általános Szerződési Feltételeit</terms>", "cashback-onbarding-tersm.title": "Felhasználási feltételek és adatvédelem", "cashback-tx-activity.retry": "Újra", "cashback-unconfirmed-payments-info.subtitle": "A kifizetések akkor válnak jogosulttá a pénzvisszatérítésre, ha a kereskedővel elszámolták őket. Addig megerősítetlen kifizetésként jelennek meg. Az elszámolatlan kifizetések nem jogosultak pénzvisszatérítésre.", "cashback-unconfirmed-payments-info.title": "Megerősítetlen kártyás kifizetések", "cashback.activity.cashback": "Pénzvisszatérítés", "cashback.activity.deposit": "Be<PERSON>ze<PERSON>s", "cashback.activity.title": "Legutóbbi tevékenységek", "cashback.activity.withdrawal": "<PERSON><PERSON><PERSON><PERSON>", "cashback.deposit": "Be<PERSON>ze<PERSON>s", "cashback.deposit.amount.label": "Befizetés összege", "cashback.deposit.change": "{from} -> {to}", "cashback.deposit.confirmation.subtitle": "A pénzvisszatérítési ráták hetente egyszer frissülnek. <PERSON>zess be most, hogy növeld a jövő heti pénzvisszatérítésedet.", "cashback.deposit.confirmation.title": "Ekkora pénzvisszatérítést kapsz: {percentage} ekkortól: {nextCycleStart}", "cashback.deposit.get.tokens.subtitle": "Válts tokeneket {currency} tokenre a {network} Chainen", "cashback.deposit.get.tokens.title": "Szerezz {currency} tokeneket", "cashback.deposit.header": "Befizetés: {currency}", "cashback.deposit.max_label": "Max.: {amount}", "cashback.deposit.select-wallet.title": "Válaszd ki a tárcát a befizetéshez", "cashback.deposit.yourcashback": "Pénzvisszatérítésed", "cashback.header": "Pénzvisszatérítés", "cashback.selectWithdrawWallet.title": "Válaszd ki a t<PERSON><PERSON><PERSON><PERSON>,{br} ahova a kiv<PERSON><PERSON>t kéred", "cashback.transaction-details.network-label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cashback.transaction-details.reward-period": "{start} - {end}", "cashback.transaction-details.top-row.label-deposit": "<PERSON><PERSON><PERSON>", "cashback.transaction-details.top-row.label-rewards": "Pénzvisszatérítési időszak", "cashback.transaction-details.top-row.label-withdrawal": "Címzett", "cashback.transaction-details.transaction": "Tranzakcióazonosító", "cashback.transaction-details.transaction-hash": "{txHash}", "cashback.transactions.header": "Pénzvisszatérítési tranzakciók", "cashback.withdraw": "<PERSON><PERSON><PERSON><PERSON>", "cashback.withdraw.confirmation.cashback_reduction": "Az e heti pénzvisszatérí<PERSON>sed, bele<PERSON>rtve a már megkeresett összeget is, erről csökken: {before} -> {after}", "cashback.withdraw.queued": "Kivét a várólistán", "cashback.withdrawal.change": "{from} -> {to}", "cashback.withdrawal.confirmation.subtitle": "Indítsd el a(z) {amount} kivételét 3 perces késleltetéssel. Ez a pénzvisszatérítésedet ennyire csökkenti: {after}.", "cashback.withdrawal.confirmation.title": "A pénzvisszatérítés c<PERSON>ö<PERSON>, ha GNO-t veszel ki", "cashback.withdrawal.delayTransaction.title": "GNO-kivét indítása{br} 3 perces késleltetéssel", "cashback.withdrawal.withdraw": "Kivétel", "cashback.withdrawal.yourcashback": "Pénzvisszatérítésed", "celebration.aave": "Aave-val szerzett hozam", "celebration.cashback.subtitle": "Kifizetve {code}", "celebration.cashback.subtitleGNO": "{amount} legutóbb megszerzett", "celebration.chf": "Frankencoinnal szerzett hozam", "celebration.lido": "Lido-val szerzett hozam", "celebration.sky": "Sky-jal szer<PERSON>tt hozam", "celebration.title": "Teljes pénzvisszatérítés", "celebration.well_done.title": "<PERSON><PERSON><PERSON><PERSON> munka!", "change-withdrawal-account.add-new-account": "Másik <PERSON>la ho<PERSON>a", "change-withdrawal-account.item.shortText": "{currency} számla", "check-confirmation.approve.footer.for": "A következőhöz:", "checkConfirmation.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collateral.bitcoin": "Bitcoin", "collateral.bitcoin-ether": "Bitcoin és Ether", "collateral.ethereum": "Ethereum", "collateral.other": "<PERSON><PERSON><PERSON><PERSON>", "collateral.rwa": "<PERSON><PERSON>", "collateral.stablecoins": "Stablecoinok (USD-hez kötött)", "collateral.us-t-bills": "Amerikai államkötvények", "confirm-bank-transfer-recipient.bullet-1": "Nincs díj a <PERSON><PERSON><PERSON>", "confirm-bank-transfer-recipient.bullet-2": "Befizetések ide: {walletLabel} {walletAddress}", "confirm-bank-transfer-recipient.bullet-3": "A Gnosis Pay-fiók adatainak megosztása a Moneriummal, egy engedélyezett és szabályozott EMI-vel. <link>Tudj meg többet</link>", "confirm-bank-transfer-recipient.bullet-4": "Fogadd el a Monerium <link>szolgáltatási feltételeit</link>", "confirm-bank-transfer-recipient.title": "Feltételek elfogadása", "confirm-change-withdrawal-account.cancel": "<PERSON><PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm-change-withdrawal-account.saving": "Men<PERSON>s", "confirm-change-withdrawal-account.subtitle": "Minden Zealből indított kiutalásod erre a bankszámlára érkezik.", "confirm-change-withdrawal-account.title": "Fogadó bank módosítása", "confirm-ramove-withdrawal-account.title": "Bankszámla eltávolítása", "confirm-remove-withdrawal-account.subtitle": "Ezek a bankszámlaadatok törlődnek a Zealből. Bármikor újra hozzáadhatod.", "confirmTransaction.finalNetworkFee": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> díj", "confirmTransaction.importKeys": "Kulcsok importálása", "confirmTransaction.networkFee": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> díj", "confirmation.title": "<PERSON>üld<PERSON>: {amount} neki: {recipient}", "conflicting-monerium-account.add-owner": "Hozzáadás Gnosis Pay-tulajdonosként", "conflicting-monerium-account.create-wallet": "Új Smart Wallet létrehozása", "conflicting-monerium-account.disconnect-card": "Kártya leválasztása a Zeal-ról és újracsatlakozás az új tulajdonossal", "conflicting-monerium-account.header": "{wallet} egy másik Monerium-<PERSON><PERSON><PERSON><PERSON>", "conflicting-monerium-account.subtitle": "Változtasd meg a Gnosis Pay tulajdonosi pénztárcádat", "connection.diconnected.got_it": "Értem!", "connection.diconnected.page1.subtitle": "A Zeal mindenhol m<PERSON>, ahol a MetaMask. Csatlakozz ugyanúgy, mint a MetaMask-kal.", "connection.diconnected.page1.title": "<PERSON><PERSON><PERSON> c<PERSON> a Zeal-lal?", "connection.diconnected.page2.subtitle": "Sok lehetőséget fogsz látni. A Zeal lehet köztük. Ha a Zeal nem jelenik meg...", "connection.diconnected.page2.title": "Kattints a Connect Wallet-re", "connection.diconnected.page3.subtitle": "Felajánlunk egy c<PERSON>t a Zeal-lal. A Browser vagy az Injected is működhet. Próbáld ki!", "connection.diconnected.page3.title": "Válaszd a MetaMask-ot", "connectionSafetyCheck.tag.caution": "Figyelem", "connectionSafetyCheck.tag.danger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectionSafetyCheck.tag.passed": "<PERSON><PERSON><PERSON>", "connectionSafetyConfirmation.subtitle": "Biztosan folytatni szeretnéd?", "connectionSafetyConfirmation.title": "Ez az oldal veszélyesnek tűnik", "connection_state.connect.cancel": "<PERSON><PERSON><PERSON><PERSON>", "connection_state.connect.changeToMetamask": "Váltás MetaMask-ra 🦊", "connection_state.connect.changeToMetamask.label": "Váltás MetaMask-ra", "connection_state.connect.connect_button": "Csatlakozás", "connection_state.connect.expanded.connected": "Csatlakoztatva", "connection_state.connect.expanded.title": "Csatlakozás", "connection_state.connect.safetyChecksLoading": "Oldal biztonságának ellenőrzése", "connection_state.connect.safetyChecksLoadingError": "A biztonsági ellenőrzéseket nem sikerült befejezni", "connection_state.connected.expanded.disconnectButton": "Zeal lecsatlakoz<PERSON>a", "connection_state.connected.expanded.title": "Csatlakoztatva", "copied-diagnostics": "Diagnosztika másolva", "copy-diagnostics": "Diagnosztika másolása", "counterparty.component.add_recipient_primary_text": "Banki kedvezményezett hozzáadása", "counterparty.country": "<PERSON><PERSON><PERSON><PERSON>", "counterparty.countryTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "counterparty.currency": "Pénznem", "counterparty.delete.success.title": "Eltávolítva", "counterparty.edit.success.title": "Módosítások mentve", "counterparty.errors.country_required": "Ország megadása kötelező", "counterparty.errors.first_name.invalid": "A keresztnév legyen hosszabb", "counterparty.errors.last_name.invalid": "A vezetéknév legyen hosszabb", "counterparty.first_name": "Keresztnév", "counterparty.iban": "IBAN", "counterparty.selectCounterparty.title": "Küldés bankba", "countrySelector.noCountryFound": "<PERSON><PERSON><PERSON>", "countrySelector.title": "Ország kiválasztása", "create-passkey.cta": "Jelszókulcs létrehozása", "create-passkey.extension.cta": "<PERSON><PERSON><PERSON><PERSON>", "create-passkey.footnote": "Működteti:", "create-passkey.mobile.cta": "Biztonság <PERSON>", "create-passkey.steps.enable-recovery": "Felhőalapú helyreállítás beállítása", "create-passkey.steps.setup-biometrics": "Biometrikus bi<PERSON>ág engedélyezése", "create-passkey.subtitle": "A jelszókulcsok biztonságosabbak a jelszavaknál, és a felhőben titkosítva tárolódnak a könnyű helyreállítás érdekében.", "create-passkey.title": "Fiók biztonsága", "create-smart-wallet": "Smart Wallet létrehozása", "create-userop.progress.text": "Létrehozás", "createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title": "Folytatás a Gnosis Payben", "createCardOrder.redirectToGnosisPay.go_to_gnosis_pay": "Ugrás a Gnosispay.com-ra", "createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle": "<PERSON><PERSON><PERSON>ed a kártyarendelést. A befejezéshez térj vissza a Gnosis Pay oldalára.", "create_recharge_preferences.card": "<PERSON><PERSON><PERSON><PERSON>", "create_recharge_preferences.earn_account.apy_percentage": "{apy}", "create_recharge_preferences.earn_account.earn_label": "Keress {label}", "create_recharge_preferences.earn_account.label": "{label} {apy}", "create_recharge_preferences.hold_cash": "Készpénz tartása", "create_recharge_preferences.link_accounts_title": "Fiókok összekapcsolása", "create_recharge_preferences.no_recharge_from_earn_accounts_description": "A kártyád NEM fog automatikusan feltöltődni minden fizetés után.", "create_recharge_preferences.not_configured_title": "Kamatoztatás és költés", "create_recharge_preferences.recharge_from_earn_accounts_description": "A kártyád minden fizetés után automatikusan feltöltődik a Kamatozó fiókodból.", "create_recharge_preferences.subtitle": "évente", "creating-account.loading": "Fiók létrehozása...", "creating-gnosis-pay-account": "Fiók létrehozása", "currencies.bridge.select_routes.emptyState": "Nem talá<PERSON>unk útvonalat ehhez a hídhoz", "currency.add_currency.add_token": "Token hozz<PERSON>a", "currency.add_currency.not_a_valid_address": "Ez nem érvényes token cím", "currency.add_currency.token_decimals_feild": "<PERSON><PERSON> t<PERSON>", "currency.add_currency.token_feild": "<PERSON><PERSON> c<PERSON>me", "currency.add_currency.token_symbol_feild": "<PERSON><PERSON> s<PERSON>", "currency.add_currency.update_token": "To<PERSON> fris<PERSON>", "currency.add_custom.remove_token.cta": "Törlés", "currency.add_custom.remove_token.header": "Token törlése", "currency.add_custom.remove_token.subtitle": "A tárcád to<PERSON> is tartja a token egy<PERSON><PERSON><PERSON><PERSON>, de az rejtve lesz a Zeal portfóliódból.", "currency.add_custom.token_removed": "Token eltávolítva", "currency.add_custom.token_updated": "Token frissítve", "currency.balance_label": "Egyenleg: {amount}", "currency.bankTransfer.deposit_status.finished.subtitle": "A banki átutalásod sikeresen átváltott {fiat} összeget erre: {crypto}.", "currency.bankTransfer.deposit_status.finished.title": "Megkaptad: {crypto}", "currency.bankTransfer.deposit_status.success": "Megérkezett a pénztárcádba", "currency.bankTransfer.deposit_status.title": "Be<PERSON>ze<PERSON>s", "currency.bankTransfer.off_ramp.check_bank_account": "Ellenőrizd a bankszámládat", "currency.bankTransfer.off_ramp.complete": "<PERSON><PERSON><PERSON>", "currency.bankTransfer.off_ramp.fiat_transfer_issued": "Küldés a bankodba", "currency.bankTransfer.off_ramp.transferring_to_currency": "Átv<PERSON><PERSON><PERSON> erre: {toCurrency}", "currency.bankTransfer.withdrawal_status.finished.subtitle": "Az összegnek mostanra meg kellett érkeznie a bankszámládra.", "currency.bankTransfer.withdrawal_status.success": "Elküldve a bankodba", "currency.bankTransfer.withdrawal_status.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_user.email": "E-mail cím", "currency.bank_transfer.create_unblock_user.email_invalid": "Érvénytelen e-mail cím", "currency.bank_transfer.create_unblock_user.email_missing": "Kötelező", "currency.bank_transfer.create_unblock_user.first_name": "Keresztnév", "currency.bank_transfer.create_unblock_user.first_name_missing": "Kötelező", "currency.bank_transfer.create_unblock_user.first_name_unsupported-char": "<PERSON><PERSON>k <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, szók<PERSON>z és - . , & ( ) ' en<PERSON><PERSON><PERSON><PERSON><PERSON>.", "currency.bank_transfer.create_unblock_user.last_name": "Vezetéknév", "currency.bank_transfer.create_unblock_user.last_name_missing": "Kötelező", "currency.bank_transfer.create_unblock_user.last_name_unsupported-char": "<PERSON><PERSON>k <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, szók<PERSON>z és - . , & ( ) ' en<PERSON><PERSON><PERSON><PERSON><PERSON>.", "currency.bank_transfer.create_unblock_user.note": "A folytatással elfogadod az Unblock (banki partnerünk) <terms>Felhasználási feltételeit</terms> és <policy>Adatvédelmi irányelveit</policy>", "currency.bank_transfer.create_unblock_user.subtitle": "A nevedet pontosan úgy add meg, ahogy a bankszámládon szerepel", "currency.bank_transfer.create_unblock_user.title": "Bankszámla összekapcsolása", "currency.bank_transfer.create_unblock_withdraw_account.account_number": "Számlaszám", "currency.bank_transfer.create_unblock_withdraw_account.bank_country": "Bank országa", "currency.bank_transfer.create_unblock_withdraw_account.iban": "IBAN", "currency.bank_transfer.create_unblock_withdraw_account.preferred_currency": "Előnyben részesített pénznem", "currency.bank_transfer.create_unblock_withdraw_account.sort_code": "Banki azonosító kód", "currency.bank_transfer.create_unblock_withdraw_account.success": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bank_transfer.create_unblock_withdraw_account.title": "Kapcsold össze a bankszámládat", "currency.bank_transfer.residence-form.address-required": "Kötelező", "currency.bank_transfer.residence-form.address-unsupported-char": "<PERSON><PERSON>k <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, szóköz<PERSON><PERSON> és , ; {apostrophe} - \\\\ engedélyezett.", "currency.bank_transfer.residence-form.city-required": "Kötelező", "currency.bank_transfer.residence-form.city-unsupported-char": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, szóközök és . , - & ( ) {apostrophe} enged<PERSON><PERSON><PERSON><PERSON>.", "currency.bank_transfer.residence-form.postcode-invalid": "Érvénytelen irányítószám", "currency.bank_transfer.residence-form.postcode-required": "Kötelező", "currency.bank_transfer.validation.invalid.account_number": "Érvénytelen számlaszám", "currency.bank_transfer.validation.invalid.iban": "Érvénytelen IBAN", "currency.bank_transfer.validation.invalid.sort_code": "Érvénytelen banki azonosító kód", "currency.bridge.amount_label": "Áthidalandó <PERSON>", "currency.bridge.best_returns.subtitle": "Ezzel a híd s<PERSON>ó<PERSON> kapod a legtö<PERSON>, az összes díjat beleszámítva.", "currency.bridge.best_returns_popup.title": "<PERSON><PERSON><PERSON><PERSON> hozam", "currency.bridge.bridge_from": "Innen", "currency.bridge.bridge_gas_fee_loading_failed": "Hiba történt a hálózati díj betöltésekor", "currency.bridge.bridge_low_slippage": "<PERSON><PERSON> al<PERSON> árfolyameltérés. Növeld meg.", "currency.bridge.bridge_provider": "Átutalási szolgáltató", "currency.bridge.bridge_provider_loading_failed": "Probléma merült fel a szolgáltatók betöltésekor", "currency.bridge.bridge_settings": "<PERSON><PERSON><PERSON>", "currency.bridge.bridge_status.subtitle": "Szolgáltató: {name}", "currency.bridge.bridge_status.title": "<PERSON><PERSON><PERSON>", "currency.bridge.bridge_to": "Ide", "currency.bridge.fastest_route_popup.subtitle": "Ez a híd szolgáltató biztosítja a leggyorsabb tranzakciós útvonalat.", "currency.bridge.fastest_route_popup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.bridge.from": "Innen", "currency.bridge.success": "<PERSON><PERSON><PERSON>", "currency.bridge.title": "<PERSON><PERSON><PERSON>", "currency.bridge.to": "Ide", "currency.bridge.topup": "Feltöltés {symbol}", "currency.bridge.withdrawal_status.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currency.card.card_top_up_status.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.destination_amount": "Célösszeg", "currency.hide_currency.confirm.subtitle": "Rejtsd el ezt a tokent a portfóliódból. Bármikor újra megjelenítheted.", "currency.hide_currency.confirm.title": "<PERSON><PERSON>", "currency.hide_currency.success.title": "Token elrejtve", "currency.label": "Címke (opcionális)", "currency.last_name": "Vezetéknév", "currency.max_loading": "Max.:", "currency.swap.amount_to_swap": "Átváltandó <PERSON>ze<PERSON>", "currency.swap.best_return": "<PERSON><PERSON><PERSON><PERSON>", "currency.swap.destination_amount": "Célösszeg", "currency.swap.header": "Csere", "currency.swap.max_label": "Egyenleg: {amount}", "currency.swap.provider.header": "<PERSON><PERSON><PERSON>", "currency.swap.select_to_token": "Token kiválasztása", "currency.swap.swap_gas_fee_loading_failed": "Hiba történt a hálózati díj betöltésekor", "currency.swap.swap_provider_loading_failed": "Hiba történt a szolgáltatók betöltésekor", "currency.swap.swap_settings": "<PERSON>sere be<PERSON>", "currency.swap.swap_slippage_too_low": "<PERSON><PERSON> al<PERSON> a slippage. Próbáld meg növelni.", "currency.swaps_io_native_token_swap.subtitle": "Szolgáltató: Swaps.IO", "currency.swaps_io_native_token_swap.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.withdrawal.amount_from": "Innen", "currency.withdrawal.amount_to": "Ide", "currencySelector.title": "Pénznem kiválasztása", "dApp.wallet-does-not-support-chain.subtitle": "A tárcád nem támogatja ezt: {network}. Próbálj másik tá<PERSON><PERSON><PERSON>, v<PERSON><PERSON> has<PERSON> a Zealt.", "dApp.wallet-does-not-support-chain.title": "<PERSON><PERSON>", "dapp.connection.manage.confirm.disconnect.all.cta": "<PERSON><PERSON> bont", "dapp.connection.manage.confirm.disconnect.all.subtitle": "Biztosan le akarsz választani minden kapcsolatot?", "dapp.connection.manage.confirm.disconnect.all.title": "Összes leválasztása", "dapp.connection.manage.connection_list.main.button.title": "Lecsatlakoztatás", "dapp.connection.manage.connection_list.no_connections": "Nincsenek csatlakoztatott alkalmazásaid", "dapp.connection.manage.connection_list.section.button.title": "Összes leválasztása", "dapp.connection.manage.connection_list.section.title": "Aktív", "dapp.connection.manage.connection_list.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dapp.connection.manage.disconnect.success.title": "Alkalmazások leválasztva", "dapp.metamask_mode.title": "MetaMask Mód", "dc25-card-marketing-card.center.subtitle": "Pénzvisszatérítés", "dc25-card-marketing-card.center.title": "4%", "dc25-card-marketing-card.left.subtitle": "<PERSON><PERSON>", "dc25-card-marketing-card.right.subtitle": "100 fő", "dc25-card-marketing-card.title": "<PERSON><PERSON> el<PERSON>ő 100, aki 50 eur<PERSON>t költ, kap {total}", "delayQueueBusyBanner.processing-yout-action.subtitle": "Ezt a műveletet 3 percig nem tudod elvégezni. Biztonsági okokból a kártyabeállítások módosítása vagy a kivétek feldolgozása 3 percet vesz igénybe.", "delayQueueBusyBanner.processing-yout-action.title": "Művelet feldolgozása, <PERSON><PERSON><PERSON><PERSON>, vá<PERSON>j", "delayQueueBusyWidget.cardFrozen": "<PERSON><PERSON><PERSON><PERSON>", "delayQueueBusyWidget.processingAction": "Művelet feldolgozása", "delayQueueFailedBanner.action-incomplete.get-support": "Segítségkérés", "delayQueueFailedBanner.action-incomplete.subtitle": "Sajnos hiba történt a pénzfelvétel vagy a beállítások frissítésekor. Kérjük, keresd a támogatást Discordon.", "delayQueueFailedBanner.action-incomplete.title": "A művelet nem fejeződött be", "delayQueueFailedWidget.actionIncomplete.title": "Befejezetlen kártyaművelet", "delayQueueFailedWidget.cardFrozen.subtitle": "<PERSON><PERSON><PERSON><PERSON>", "delayQueueFailedWidget.contactSupport": "Lépj kapcsolatba az ügyfélszolgálattal", "delay_queue_busy.subtitle": "Biztonsági okokból a kártyabeállítások módosítása vagy a pénzfelvétel 3 percet vesz igénybe. Ezalatt a kártyád zárolva van.", "delay_queue_busy.title": "A művelet feldolgozása folyamatban", "delay_queue_failed.contact_support": "Támogatás", "delay_queue_failed.subtitle": "Sajnos hiba történt a pénzfelvétel vagy a beállítások frissítésekor. Kérjük, keresd a támogatást Discordon.", "delay_queue_failed.title": "Lépj kapcsolatba a támogatással", "deploy-earn-form-smart-wallet.in-progress.title": "<PERSON><PERSON><PERSON>", "deposit": "Be<PERSON>ze<PERSON>s", "disconnect-card-popup.cancel": "<PERSON><PERSON><PERSON><PERSON>", "disconnect-card-popup.disconnect": "Leválasztás", "disconnect-card-popup.subtitle": "Ezzel eltávolítod a kártyát a Zeal appból. A pénztárcád a Gnosis Pay appban csatlakoztatva marad. A kártyádat bármikor újra csatlakoztathatod.", "disconnect-card-popup.title": "K<PERSON><PERSON><PERSON>lasztása", "distance.long.days": "{count} nap", "distance.long.hours": "{count} <PERSON>ra", "distance.long.minutes": "{count} perc", "distance.long.months": "{count} hónap", "distance.long.seconds": "{count} m<PERSON>od<PERSON><PERSON>", "distance.long.years": "{count} év", "distance.short.days": "{count} n", "distance.short.hours": "{count} ó", "distance.short.minutes": "{count} p", "distance.short.months": "{count} hó", "distance.short.seconds": "{count} mp", "distance.short.years": "{count} é", "duration.short.days": "{count}n", "duration.short.hours": "{count}ó", "duration.short.minutes": "{count}p", "duration.short.seconds": "{count}mp", "earn-deposit-view.deposit": "Be<PERSON>ze<PERSON>s", "earn-deposit-view.into": "<PERSON><PERSON>", "earn-deposit-view.to": "Címzett:", "earn-deposit.swap.transfer-provider": "Átutalási szolgáltató", "earn-taker-investment-details.accrued-realtime": "Valós időben jóváírva", "earn-taker-investment-details.asset-class": "Eszk<PERSON><PERSON>sz<PERSON><PERSON><PERSON>", "earn-taker-investment-details.asset-coverage-ratio": "Eszközfedezeti arány", "earn-taker-investment-details.asset-reserve": "Eszköztartalék", "earn-taker-investment-details.base_currency.label": "Alapdeviza", "earn-taker-investment-details.chf.description": "Szerezz kamatot a CHF egyenlegedre zCHF befizetésével a Frankencoinba – egy megbízható digitális pénzpiacra. A kamatot a Frankencoinon nyújtott alacsony kockázatú, túlbiztosított hitelek generálják, és valós időben fizetik ki. A pénzed biztonságban van egy külön alszámlán, amelyet csak te kezelsz.", "earn-taker-investment-details.chf.description.with_address_link": "Szerezz kamatot a CHF egyenlegedre zCHF befizetésével a Frankencoinba – egy megbízható digitális pénzpiacra. A kamatot a Frankencoinon nyújtott alacsony kockázatú, túlbiztosított hitelek generálják, és valós időben fizetik ki. A pénzed biztonságban van egy külön alszámlán <link>(0x másolása)</link> , amelyet csak te kezelsz.", "earn-taker-investment-details.chf.label": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON> frank", "earn-taker-investment-details.collateral-composition": "Fedezet összetétele", "earn-taker-investment-details.depositor-obligations": "Betétesekkel szembeni kötelezettségek", "earn-taker-investment-details.eure.description": "Szerezz kamatot az euróidra az EURe Aave-be történ<PERSON> befizetésével – ez egy megbízható digitális p<PERSON>. Az EURe a Monerium által kibocsá<PERSON>tt, te<PERSON><PERSON><PERSON> sza<PERSON>ott euró stablecoin, 1:1 ar<PERSON><PERSON><PERSON> fed<PERSON>, biz<PERSON><PERSON><PERSON>tt számlákon. A kamat az Aave-n nyújtott, al<PERSON>sony kockázatú, túlbiz<PERSON><PERSON><PERSON>tt hitelekből származik, és valós időben kerül kifizetésre. Pénzed egy biztonságos al<PERSON>, amelyet csak te kezelsz.", "earn-taker-investment-details.eure.description.with_address_link": "Szerezz kamatot az euróidra az EURe Aave-be tört<PERSON><PERSON> befizetésével – ez egy megbízható digitális pénz<PERSON>. Az EURe a Monerium által kibocsá<PERSON>tt, te<PERSON><PERSON><PERSON> sza<PERSON>ott euró stablecoin, 1:1 ar<PERSON><PERSON><PERSON> fed<PERSON>, biztos<PERSON><PERSON>tt számlákon. A kamat az Aave-n nyújtott, al<PERSON>sony kockázatú, túlbiztosí<PERSON>tt hitelekből származik, és valós időben kerül kifizetésre. Pénzed egy biztonságos alszámlán marad, <link>(0x másolása)</link> amelyet csak te kezelsz.", "earn-taker-investment-details.eure.label": "<PERSON><PERSON><PERSON><PERSON> (EURe)", "earn-taker-investment-details.faq": "GYIK", "earn-taker-investment-details.fixed-income": "<PERSON><PERSON>", "earn-taker-investment-details.issuer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.key-facts": "Főbb tudnivalók", "earn-taker-investment-details.liquidity": "Likviditás", "earn-taker-investment-details.operator": "Piacműködtető", "earn-taker-investment-details.projected-yield": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn-taker-investment-details.see-other-faq": "Összes GYIK megtekintése", "earn-taker-investment-details.see-realtime": "Valós idejű adatok megtekintése", "earn-taker-investment-details.sky": "Sky", "earn-taker-investment-details.tailing-yield": "Elmúlt 12 hónap hozama", "earn-taker-investment-details.total-collateral": "<PERSON><PERSON>s fed<PERSON>", "earn-taker-investment-details.total-deposits": "27 253 300 208 $", "earn-taker-investment-details.total-zchf-supply": "Teljes ZCHF-kínálat", "earn-taker-investment-details.total_deposits": "Összes Aave befize<PERSON>s", "earn-taker-investment-details.usd.description": "A Sky egy digitá<PERSON>, amely stabil, amerikai dollárban denominált hozamokat kínál rövid le<PERSON>ú amerikai államkötvényekből és túlbiztosított hitelezésből – kriptovolatilitás nélkül, 24/7-es hozzáféréssel a pénzedhez és átlátható, láncon belü<PERSON> fedezette<PERSON>.", "earn-taker-investment-details.usd.description.with_address_link": "A Sky egy digitá<PERSON>, amely stabil, amerikai dollárban denominált hozamokat kínál rövid le<PERSON>ú amerikai államkötvényekből és túlbiztosított hitelezésből – kriptovolatilitás nélkül, 24/7-es hozzáféréssel a pénzedhez és átlátható, láncon belüli fedezettel. A befektetések egy alszámlán van<PERSON>, <link>(0x másolása)</link> amelyet te kezelsz.", "earn-taker-investment-details.usd.ftx-difference": "Miben <PERSON>önbözik ez az FTX-től, Celsiustól, BlockFi-tól vagy a Lunától?", "earn-taker-investment-details.usd.high-returns": "Hogy lehetnek ilyen magasak a hozamok, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>sen a hagyományos bankokhoz képest?", "earn-taker-investment-details.usd.how-is-backed": "<PERSON><PERSON><PERSON> fedez<PERSON> áll a Sky USD mögött, és mi történik a pénzemmel, ha a Zeal csődbe megy?", "earn-taker-investment-details.usd.income-sources": "Bevételi források 2024", "earn-taker-investment-details.usd.insurance": "Biztosítja vagy garantálja a pénzemet bármilyen s<PERSON>vezet (például FDIC vagy has<PERSON>)?", "earn-taker-investment-details.usd.label": "<PERSON><PERSON><PERSON><PERSON> am<PERSON>", "earn-taker-investment-details.usd.lose-principal": "Elveszíthetem reá<PERSON> a tő<PERSON>, és ha igen, milyen körülmények között?", "earn-taker-investment-details.variable-rate": "Változó kamatozású hitelezés", "earn-taker-investment-details.withdraw-anytime": "Bármikor k<PERSON>he<PERSON>ő", "earn-taker-investment-details.yield": "<PERSON><PERSON>", "earn-withdrawal-view.approve.for": "<PERSON><PERSON><PERSON>", "earn-withdrawal-view.approve.into": "<PERSON><PERSON>", "earn-withdrawal-view.swap.into": "<PERSON><PERSON><PERSON>", "earn-withdrawal-view.withdraw.to": "<PERSON><PERSON>", "earn.add_another_asset.title": "Válassz kamatozó eszközt", "earn.add_asset": "Eszköz hozz<PERSON>adása", "earn.asset_view.title": "<PERSON><PERSON>", "earn.base-currency-popup.text": "Az alapdeviza az a pénznem, amelyben a befizetéseid, a hozamod és a tranzakcióid értékelve és rögzítve vannak. Ha másik pénznemben fizetsz be (például EUR-t USD-be), a pénzedet azonnal átváltjuk az alapdevizára az aktuális árfolyamon. Az átváltás után az egyenleged stabil marad az alapdeviz<PERSON>ban, de a jövőbeni pénzfelvételek ismét devizaátváltással járhatnak.", "earn.base-currency-popup.title": "Alapdeviza", "earn.card-recharge.disabled.list-item.title": "Automatikus feltöltés letiltva", "earn.card-recharge.enabled.list-item.title": "Automatikus feltöltés engedélyezve", "earn.choose_wallet_to_deposit.title": "Befizetés innen", "earn.config.currency.eth": "Ethereum hozam", "earn.config.currency.on_chain_address_subtitle": "On-chain cím", "earn.config.currency.us_dollars": "Banki átutalások beállítása", "earn.configured_widget.current_apy.title": "Jelenlegi APY", "earn.configured_widget.curreny_apy.anual_earnings": "{forcastedEarnings} évente", "earn.confirm.currency.cta": "Be<PERSON>ze<PERSON>s", "earn.currency.eth": "Kamatoz<PERSON><PERSON> Ethereumot", "earn.deploy.status.title": "<PERSON><PERSON>n <PERSON> l<PERSON>hoz<PERSON>", "earn.deploy.status.title_with_taker": "<PERSON><PERSON> {title} <PERSON><PERSON><PERSON>", "earn.deposit": "Be<PERSON>ze<PERSON>s", "earn.deposit.amount_to_deposit": "Befizetendő összeg", "earn.deposit.deposit": "Be<PERSON>ze<PERSON>s", "earn.deposit.enter_amount": "Add meg az összeget", "earn.deposit.no_routes_found": "<PERSON>nc<PERSON>ek útvonalak", "earn.deposit.not_enough_balance": "<PERSON><PERSON><PERSON> el<PERSON> fed<PERSON>", "earn.deposit.select-currency.title": "Válaszd ki a befizetendő tokent", "earn.deposit.select_account.title": "<PERSON><PERSON>n <PERSON> kiválasztása", "earn.desposit_form.title": "Befizetés a Hozamba", "earn.earn_deposit.status.title": "Befizetés az Earn <PERSON>", "earn.earn_deposit.trx.title": "Befizetés az Earnbe", "earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card": "Bármikor kiveheted a pénzt", "earn.earn_withdraw.status.title": "Kivétel az Earn fi<PERSON>l", "earn.earn_withdraw.trx.title.approval": "Kifizetés jóváhagy<PERSON>a", "earn.earn_withdraw.trx.title.withdraw_into_asset": "Kifize<PERSON>s erre: {asset}", "earn.earn_withdraw.trx.title.withdrawal": "Kifizetés az Earnből", "earn.recharge.cta": "Változtatások mentése", "earn.recharge.earn_not_configured.enable_some_account.error": "Engedélyezz egy számlát", "earn.recharge.earn_not_configured.enter_amount.error": "Add meg az összeget", "earn.recharge.select_taker.header": "<PERSON><PERSON><PERSON><PERSON> feltöltése ebben a sorrendben", "earn.recharge_card_tag.on": "be", "earn.recharge_card_tag.recharge": "Feltöltés", "earn.recharge_card_tag.recharge_not_configured": "Automatikus feltöltés", "earn.recharge_card_tag.recharge_off": "Feltöltés ki", "earn.recharge_card_tag.recharged": "Feltöltve", "earn.recharge_card_tag.recharging": "Feltöltés...", "earn.recharge_configured.disable.trx.title": "Automatikus feltöltés letiltása", "earn.recharge_configured.trx.disclaimer": "Amikor a kárty<PERSON><PERSON>, egy <PERSON><PERSON> auk<PERSON>, hogy a fizetéseddel megegyező összeget megvásárolja az Earn eszközeidből. Ez az aukciós folyamat által<PERSON> a legjobb piaci árfolyamot biztosítja, de vedd fi<PERSON>, hogy a láncon belüli árfolyam eltérhet a valós árfolyamoktól.", "earn.recharge_configured.trx.subtitle": "Minden fizetés után automatikusan pénz kerül a kártyádra az Earn fiókodból/fiókjaidból, hogy a kártyaegyenleged ezen az értéken maradjon: {value}", "earn.recharge_configured.trx.title": "Automatikus feltöltés beállítása erre: {value}", "earn.recharge_configured.updated.trx.title": "Feltöltési beállítások mentése", "earn.risk-banner.subtitle": "<PERSON>z egy priv<PERSON>t <PERSON>, amely<PERSON> nem vonatkozik a veszteségek elleni szabályozói védelem.", "earn.risk-banner.title": "Ismerd meg a kockázatokat", "earn.set_recharge.status.title": "Automatikus feltöltés beállítása", "earn.setup_reacharge.input.disable.label": "<PERSON><PERSON><PERSON>", "earn.setup_reacharge.input.label": "<PERSON><PERSON><PERSON>", "earn.setup_reacharge_form.title": "Az Automatikus Feltöltés fenntartja a {br} kártyád egyenleg<PERSON>t", "earn.sky": "Sky", "earn.taker-bulletlist.eth.point_2": "Tarts wstETH-t (Staked ETH) a Gnosis Chain-en, és adj kölcsön a Lido-n keresztül.", "earn.taker-bulletlist.point_1": "Keress {apyValue} évente. A hozamok a piactól függően változnak.", "earn.taker-bulletlist.point_3": "A Zeal nem s<PERSON>ámít fel díjakat.", "earn.taker-historical-returns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.taker-historical-returns.chf": "CHF növekedése USD-ben", "earn.taker-investment-tile.apy.perYear": "évente", "earn.takerAPY": "{takerApy} APY", "earn.takerListItem.apy": "{takerApy} APY", "earn.takerListItem.deposit": "Be<PERSON>ze<PERSON>s", "earn.takerListItem.earnCHF.title": "Frankencoin CHF", "earn.takerListItem.earnETH.title": "Ethereum", "earn.takerListItem.earnEURO.title": "Aave EUR", "earn.takerListItem.earnFromAaveOnGnosis.footnote": "Kamatoztatás az Aave-val a Gnosis Chain-en", "earn.takerListItem.earnFromFrankencoinOnGnosis.footnote": "Hozamszerzés a Frankencoinnal a Gnosis Chainen", "earn.takerListItem.earnFromLidoOnGnosis.footnote": "Kamatoztatás a Lido-val a Gnosis Chain-en", "earn.takerListItem.earnFromMakerOnGnosis.footnote": "Kamatoztatás a Maker-rel a Gnosis Chain-en", "earn.takerListItem.earnUSD.title": "Sky USD", "earn.takerListItemShort.earnCHF.title": "Frankencoin CHF", "earn.takerListItemShort.earnETH.title": "<PERSON><PERSON> kamatoz<PERSON>", "earn.takerListItemShort.earnEURO.title": "Aave EUR", "earn.takerListItemShort.earnUSD.title": "Sky USD", "earn.takerListItemWithSuffix.earnCHF.title": "Frankencoin CHF", "earn.takerListItemWithSuffix.earnETH.title": "ETH kamatoztatás", "earn.takerListItemWithSuffix.earnEURO.title": "Aave EUR", "earn.takerListItemWithSuffix.earnUSD.title": "Sky USD", "earn.us-treasuries": "USA államkötvények (SHV)", "earn.usd.can-I-lose-my-principal-popup.text": "<PERSON><PERSON><PERSON> re<PERSON><PERSON><PERSON> rit<PERSON>, elméletileg lehetséges. A pénzedet szigorú kockázatkezelés és magas fedezeti arány védi. A reális legrosszabb forgatókönyv példátlan piaci körülményeket feltételezne, például több stablecoin egyidejűleg veszítené el az árfolyamrögzítését – ami még soha nem fordult elő.", "earn.usd.can-I-lose-my-principal-popup.title": "Elveszíthetem reá<PERSON> a tő<PERSON>, és ha igen, milyen körülmények között?", "earn.usd.ftx-difference-popup.text": "A Sky alapvetően más. Ellentétben az FTX-szel, a Celsiusszal, a BlockFi-jal vagy a Lunával – amelyek nagymértékben központosított letétkezelésre, átláthatatlan vagyonkezelésre és kockázatos tőkeáttételes pozíciókra támaszkodtak – a Sky USD átlátható, auditált, decentralizált okosszerződéseket használ, és teljes on-chain átláthatóságot biztosít. Te tartod a teljes, privát pénztárca feletti irán<PERSON>, ami jelent<PERSON>sen csökkenti a központosított hibákból adódó partnerkockázatokat.", "earn.usd.ftx-difference-popup.title": "Miben <PERSON>önbözik ez az FTX-től, Celsiustól, BlockFi-tól vagy a Lunától?", "earn.usd.high-returns-popup.text": "A Sky USD a hozamot elsősorban decentralizált pénzügyi (DeFi) protokollokon keresztül termeli, amelyek automatizálják a peer-to-peer hitelezést és a likviditás biztosítását, kik<PERSON>szöbölve a hagyományos banki költségeket és közvetítőket. Ez a hatékonyság, a robusztus kockázatkezeléssel kombinálva, lényegesen magasabb hozamot tesz lehetővé a hagyományos bankokhoz képest.", "earn.usd.high-returns-popup.title": "Hogy lehetnek ilyen magasak a hozamok, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>sen a hagyományos bankokhoz képest?", "earn.usd.how-is-sky-backed-popup.text": "A Sky USD teljes fedezettel és túlbiztosítással rendelkezik, amelyet biztonságos okosszerződésekben tartott digitális eszközök és valós eszközök, például amerikai államkötvények kombinációja biztosít. A tartalékok valós időben, on-chain módon auditálhatók, ak<PERSON>r a Zealon belülről is, ami <PERSON>ágot és biztonságot nyújt. Abban a valószínűtlen esetben, ha a Zeal leállna, az eszközeid on-chain biztonságban maradnak, teljes mértékben a te irányításod alatt, és más kompatibilis pénztárcákon keresztül is elérhetők.", "earn.usd.how-is-sky-backed-popup.title": "<PERSON><PERSON><PERSON> fedez<PERSON> áll a Sky USD mögött, és mi történik a pénzemmel, ha a Zeal csődbe megy?", "earn.usd.insurance-popup.text": "A Sky USD-ben tartott pénzeszközök nem FDIC-biztosítottak, és nem áll mögöttük hagyományos <PERSON>, mivel ez egy digitális eszközalap<PERSON>, nem pedig hagyományos <PERSON>. Ehelyett a Sky a kockázatcsökkentést auditált okosszerződéseken és gondosan ellenőrzött DeFi protokollokon keresztül kezeli, biztosítva az eszközök biztonságát és átláthatóságát.", "earn.usd.insurance-popup.title": "Biztosítja vagy garantálja a pénzemet bármilyen s<PERSON>vezet (például FDIC vagy has<PERSON>)?", "earn.usd.lending-operations-popup.text": "A Sky USD hozamot termel az<PERSON>ltal, hogy stablecoinokat hitelez olyan decentralizált hitelezési piacokon, mint a Morpho és a Spark. A stablecoinjaidat olyan hitelfelvevőknek adjuk kö<PERSON>ön, akik a kölcsönüknél lényegesen több fedezetet – például ETH-t vagy BTC-t – helyeznek el. Ez a túlbiztosításnak nevezett megközelítés biztosítja, hogy mindig elegendő fedezet álljon rendelkezésre a hitelek fedezésére, jelentősen csökkentve a kockázatot. A beszedett kamatok és a hitelfelvevők által fizetett eseti likvidálási díjak megbízható, átlátható és biztonságos hozamot biztosítanak.", "earn.usd.lending-operations-popup.title": "Hitelezési műveletek", "earn.usd.market-making-operations-popup.text": "A Sky USD további hozamot ér el azáltal, hogy részt vesz decentralizált tőzsdéken (AMM), mint például a Curve vagy az Uniswap. A likviditás biztosításával – azaz a stablecoinjaid elhelyezésével a kriptokereskedést megkönnyítő poolokban – a Sky USD a kereskedésekből származó díjakat szerzi meg. Ezeket a likviditási poolokat gondosan választjuk ki a volatilitás minimalizálása érdekében, elsősorban stablecoin-stablecoin párokat használva, hogy jelentősen csökkentsük az olyan kockázatokat, mint az impermanens veszteség, így az eszközeid biztonságban és elérhetően maradnak.", "earn.usd.market-making-operations-popup.title": "Piacépítési mű<PERSON>k", "earn.usd.treasury-operations-popup.text": "A Sky USD stabil, következetes hozamot termel stratégiai kincstári befektetések révén. A stablecoin betéteid egy részét biztonságos, alacsony kockázatú valós es<PERSON>kbe – elsősorban rövid le<PERSON>ötvényekbe és rendkívül biztonságos hitelviszonyt megtestesítő értékpapírokba – fektetjük. Ez a hagyományos banki gyakorlathoz hasonló megközelítés kiszámítható és megbízható hozamot biztosít. Az eszközeid biztonságban, likviden és átláthatóan kezelve maradnak.", "earn.usd.treasury-operations-popup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earn.view_earn.card_rechard_off": "<PERSON>", "earn.view_earn.card_rechard_on": "Be", "earn.view_earn.card_recharge": "<PERSON><PERSON><PERSON><PERSON>", "earn.view_earn.total_balance_label": "Hozam: {percentage} évente", "earn.view_earn.total_earnings_label": "Összes hozam", "earn.withdraw": "Kivétel", "earn.withdraw.amount_to_withdraw": "Kiveendő összeg", "earn.withdraw.enter_amount": "Add meg az összeget", "earn.withdraw.loading": "Betöltés", "earn.withdraw.no_routes_found": "<PERSON><PERSON><PERSON>", "earn.withdraw.not_enough_balance": "<PERSON><PERSON><PERSON> el<PERSON> e<PERSON>", "earn.withdraw.select-currency.title": "Token kiválasztása", "earn.withdraw.select_to_token": "Token kiválasztása", "earn.withdraw.withdraw": "Kivétel", "earn.withdraw_form.title": "Kivétel az Earn-ből", "earnings-view.earnings": "Összes bevétel", "edit-account-owners.add-owner.add-wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.add_wallet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit-account-owners.add-owner.title": "Kártyatulajdonos ho<PERSON>adása", "edit-account-owners.card-owners": "Kártyatulajdonosok", "edit-account-owners.external-wallet": "Külső tárca", "editBankRecipient.title": "Kedvezményezett szerkesztése", "editNetwork.addCustomRPC": "Egyéni RPC-csom<PERSON>pont hozz<PERSON>adása", "editNetwork.cannot_verify.subtitle": "Az egyéni RPC-csomópont nem válaszol megfelelően. Ellenőrizd az URL-t, és pr<PERSON><PERSON><PERSON>ld <PERSON>.", "editNetwork.cannot_verify.title": "Nem tudjuk ellenőrizni az RPC-csomópontot", "editNetwork.cannot_verify.try_again": "Újra", "editNetwork.customRPCNode": "Egyéni RPC-csomópont", "editNetwork.defaultRPC": "Alapértelmezett RPC", "editNetwork.networkRPC": "H<PERSON>lózati RPC", "editNetwork.rpc_url.cannot_be_empty": "Kötelező", "editNetwork.rpc_url.not_a_valid_https_url": "Érvényes HTTP(S) URL-nek kell lennie", "editNetwork.safetyWarning.subtitle": "A Zeal nem garantálja az egyéni RPC-k adatvédelmét és biztonságát. Biztosan egyéni RPC-t aka<PERSON><PERSON>lni?", "editNetwork.safetyWarning.title": "Az egyéni RPC-k veszélyesek lehetnek", "editNetwork.zealRPCNode": "Zeal RPC-csomópont", "editNetworkRpc.headerTitle": "Egyéni RPC-csomópont", "editNetworkRpc.rpcNodeUrl": "RPC-csomópont URL-címe", "editing-locked.modal.description": "A jóváhagyási tranzakciókkal ellentétben az engedélyek (Permit) nem teszik lehetővé a költési limit vagy a lejárati idő szerkesztését. Győződj meg róla, hogy megbízol a dAppban, miel<PERSON><PERSON> engedélyt adsz.", "editing-locked.modal.title": "Szerkesztés z<PERSON>va", "enable-recharge-for-smart-wallet.enabling-recharge.title": "Feltöltés engedélyezése", "enable-recharge-for-smart-wallet.recharge-enabled.title": "Feltöltés engedélyezve", "enterCardnumber": "Add meg a kártyaszámot", "error.connectivity_error.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> az internetkapcsola<PERSON>dat, <PERSON>s pr<PERSON><PERSON><PERSON><PERSON>.", "error.connectivity_error.title": "Nincs internetkapcsolat", "error.decrypt_incorrect_password.title": "<PERSON><PERSON><PERSON><PERSON>", "error.encrypted_object_invalid_format.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error.failed_to_fetch_google_auth_token.title": "<PERSON><PERSON>", "error.list.item.cta.action": "Újra", "error.trezor_action_cancelled.title": "T<PERSON><PERSON><PERSON><PERSON> elutasítva", "error.trezor_device_used_elsewhere.title": "Az eszközt máshol has<PERSON>ják", "error.trezor_method_cancelled.title": "<PERSON><PERSON> a Trezor szinkronizálása", "error.trezor_permissions_not_granted.title": "<PERSON><PERSON> a Trezor szinkronizálása", "error.trezor_pin_cancelled.title": "<PERSON><PERSON> a Trezor szinkronizálása", "error.trezor_popup_closed.title": "<PERSON><PERSON> a Trezor szinkronizálása", "error.unblock_account_number_and_sort_code_mismatch": "A számlaszám és a bankkód nem egyezik", "error.unblock_can_not_change_details_after_kyc": "A KYC után nem módosíthatod az adataidat.", "error.unblock_hard_kyc_failure": "Váratlan KYC állapot", "error.unblock_invalid_faster_payment_configuration.title": "Ez a bank nem támogatja a gyorsfizetést", "error.unblock_invalid_iban": "Érvénytelen IBAN", "error.unblock_session_expired.title": "Az Unblock munkamenet lejárt", "error.unblock_user_with_address_already_exists.title": "E<PERSON>ez a címhez már létezik fiók", "error.unblock_user_with_such_email_already_exists.title": "Ilyen e-mail címmel már létezik felhasz<PERSON>ó", "error.unknown_error.error_message": "Hibaüzenet: ", "error.unknown_error.subtitle": "Sajnáljuk! Ha sürgős segítségre van szükséged, vedd fel a kapcsolatot az ügyfélszolgálattal, és oszd meg az alábbi adatokat.", "error.unknown_error.title": "Rendszerhiba", "eth-cost-warning-modal.subtitle": "A Smart Wallet-ek működnek az Ethereumon, de a díjak nagyon magasak, ez<PERSON>rt ERŐSEN javasoljuk más hálózatok használatát.", "eth-cost-warning-modal.title": "<PERSON><PERSON><PERSON><PERSON> az Ethereumot - magasak a hálózati díjak", "exchange.form.button.chain_unsupported": "A lánc nem támo<PERSON>ott", "exchange.form.button.refreshing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exchange.form.error.asset_not_supported.button": "Válassz másik eszközt", "exchange.form.error.asset_not_supported.description": "A híd nem támogatja ennek az eszköznek az áthidalását.", "exchange.form.error.asset_not_supported.title": "<PERSON><PERSON> eszköz", "exchange.form.error.bridge_quote_timeout.button": "Válassz másik eszközt", "exchange.form.error.bridge_quote_timeout.description": "Próbálj meg egy másik token párt", "exchange.form.error.bridge_quote_timeout.title": "<PERSON><PERSON>s elérhet<PERSON> váltás", "exchange.form.error.different_receiver_not_supported.button": "Alternatív címzett eltávolítása", "exchange.form.error.different_receiver_not_supported.description": "Ez a szolgáltató nem támogatja a másik címre való küldést.", "exchange.form.error.different_receiver_not_supported.title": "A küldő és a fogadó címnek meg kell egyeznie", "exchange.form.error.insufficient_input_amount.button": "Növeld az összeget", "exchange.form.error.insufficient_liquidity.button": "Csökkentsd az összeget", "exchange.form.error.insufficient_liquidity.description": "A hídnak nincs elegendő eszköze. Próbálj meg kisebb összeget.", "exchange.form.error.insufficient_liquidity.title": "Túl nagy összeg", "exchange.form.error.max_amount_exceeded.button": "Csökkentsd az összeget", "exchange.form.error.max_amount_exceeded.description": "A maximális ö<PERSON>get túllépted.", "exchange.form.error.max_amount_exceeded.title": "Túl magas összeg", "exchange.form.error.min_amount_not_met.button": "Növeld az összeget", "exchange.form.error.min_amount_not_met.description": "<PERSON>em te<PERSON> a minimális váltási összeg ennél a tokennél.", "exchange.form.error.min_amount_not_met.description_with_amount": "A minimális váltási összeg {amount}.", "exchange.form.error.min_amount_not_met.title": "<PERSON><PERSON>", "exchange.form.error.min_amount_not_met.title_increase": "Növeld az összeget", "exchange.form.error.no_routes_found.button": "Válassz másik eszközt", "exchange.form.error.no_routes_found.description": "<PERSON><PERSON>s elérhető váltási útvonal ehhez a token/hálózat kombinációhoz.", "exchange.form.error.no_routes_found.title": "<PERSON><PERSON>s elérhet<PERSON> váltás", "exchange.form.error.not_enough_balance.button": "Csökkentsd az összeget", "exchange.form.error.not_enough_balance.description": "<PERSON><PERSON><PERSON> el<PERSON> e<PERSON>ed ebből az eszközből a tranzakcióhoz.", "exchange.form.error.not_enough_balance.title": "<PERSON><PERSON><PERSON> el<PERSON> e<PERSON>", "exchange.form.error.slippage_passed_is_too_low.button": "Növeld a slippage-et", "exchange.form.error.slippage_passed_is_too_low.description": "A megengedett árfolyameltérés túl alacsony ehhez az eszközhöz.", "exchange.form.error.slippage_passed_is_too_low.title": "<PERSON><PERSON>", "exchange.form.error.socket_internal_error.button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exchange.form.error.socket_internal_error.description": "A hídpartnernél problémák merültek fel. Próbáld ú<PERSON>.", "exchange.form.error.socket_internal_error.title": "Hiba a hídpartnernél", "exchange.form.error.stargatev2_requires_fee_in_native": "<PERSON><PERSON> {symbol}", "exchange.form.error.stargatev2_requires_fee_in_native.description": "Adj hozzá {amount} a tranzakció befejezéséhez", "exchange.form.error.stargatev2_requires_fee_in_native.title": "Több {symbol}", "expiration-info.modal.description": "A lejárati idő azt jelenti, hogy egy alkalmazás mennyi ideig használhatja a tokenjeidet. Ha lejár az idő, elveszítik a hozzáférést, amíg újra nem engedélyezed. A biztonság érdekében tartsd a lejárati időt rövidre.", "expiration-info.modal.title": "Mi a lejárati idő?", "expiration-time.high.modal.text": "A lejárati idő legyen rövid, <PERSON>s attól függ<PERSON>ön, mennyi ideig van rá ténylegesen szükséged. A hosszú időtartam kockáza<PERSON>, mert nagyobb esélyt ad a csalóknak a tokenjeiddel való visszaélésre.", "expiration-time.high.modal.title": "Hosszú lejárati id<PERSON>", "failed.transaction.content": "A tranzakció valószínűleg sikertelen lesz", "fee.unknown": "Ismeretlen", "feedback-request.leave-message": "Üzenet írása", "feedback-request.not-now": "Most nem", "feedback-request.title": "Köszi! Hogyan tehetnénk jobbá a Zealt?", "float.input.period": "Tizedesjel", "gnosis-activate-card.info-popup.subtitle": "Elsőre használd a PIN-kódot.", "gnosis-activate-card.info-popup.title": "Első fizetés: chip és PIN", "gnosis-activate-card.placeholder": "0000 0000 0000 0000", "gnosis-activate-card.subtitle": "Add meg a kártyaszámod az aktiváláshoz.", "gnosis-activate-card.title": "K<PERSON>rtyaszám", "gnosis-pay-re-kyc-widget.btn-text": "<PERSON><PERSON><PERSON><PERSON>", "gnosis-pay-re-kyc-widget.title.not-started": "Igazold a személyazonosságod", "gnosis-pay.login.cta": "Meglévő fiók kapcsolása", "gnosis-pay.login.title": "<PERSON><PERSON><PERSON> fiókod", "gnosis-signup.confirm.subtitle": "<PERSON>ress egy e-mailt a Gnosis Paytől, le<PERSON>t, hogy a spam mappában rejtőzik.", "gnosis-signup.confirm.title": "Nem kaptad meg az ellenőrző e-mailt?", "gnosis-signup.continue": "<PERSON><PERSON><PERSON><PERSON>", "gnosis-signup.dont_link_accounts": "Fiókok ne kapcsolódjanak", "gnosis-signup.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis-signup.enter-email.placeholder": "<EMAIL>", "gnosis-signup.enter-email.title": "E-mail-cím megad<PERSON>a", "gnosis-signup.title": "Elolvastam és elfogadom a Gnosis Pay <linkGnosisTNC>Feltételeit</linkGnosisTNC> <monovateTerms>Kártyabirtokosi Feltételeit</monovateTerms> és a <linkMonerium>Monerium ÁSZF-ét</linkMonerium>.", "gnosis-signup.verify-email.title": "E-mail-c<PERSON><PERSON>", "gnosis.confirm.subtitle": "Nem kaptad meg a kódot? Ellenőrizd, hogy a telefonszámod helyes-e", "gnosis.confirm.title": "Kódot küldtünk ide: {phone}", "gnosis.enter-code.placeholder": "✱✱✱✱✱✱", "gnosis.verify.title": "<PERSON><PERSON><PERSON><PERSON>", "gnosisPayAccountStatus.success.title": "K<PERSON><PERSON><PERSON>", "gnosisPayIsNotAvailableInThisCountry.title": "GnosisPay nem elérhető az országodban", "gnosisPayNoActiveCardsFound.title": "Nincsenek aktív kártyák", "gnosis_pay_card_delay_relay_not_empty_error.title": "A tranzakciódat jelenleg nem sikerült feldolgozni. K<PERSON>rjük, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "gnosis_pay_user_doesnt_meet_risk_score_criteria.title": "Kártya nem lehetséges", "gnosiskyc.modal.approved.activate-free-card": "Ingyenes kártya aktiválás", "gnosiskyc.modal.approved.button-text": "Befizetés bankszámláról", "gnosiskyc.modal.approved.title": "A személyes fiókadataid létrejöttek", "gnosiskyc.modal.failed.close": "Bezárás", "gnosiskyc.modal.failed.title": "<PERSON><PERSON><PERSON>, a Gnosis Pay nem tud fiókot létrehozni neked", "gnosiskyc.modal.in-progress.title": "A személyazonosítás 24 ór<PERSON>t vagy többet is igénybe vehet. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, l<PERSON><PERSON> tü<PERSON>.", "goToSettingsPopup.settings": "Beállítások", "goToSettingsPopup.title": "Az értesítéseket bármikor engedélyezheted az eszközöd beállításaiban", "google_file.error.failed_to_fetch_auth_token.button_title": "Újraprób<PERSON><PERSON><PERSON>", "google_file.error.failed_to_fetch_auth_token.subtitle": "<PERSON><PERSON><PERSON>, hogy használni tudjuk a helyreállítási fájlodat, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adj hozzáférést a személyes felhődben.", "google_file.error.failed_to_fetch_auth_token.title": "<PERSON><PERSON>", "hidden_tokens.widget.emptyState": "<PERSON><PERSON><PERSON><PERSON> rejtett <PERSON>", "how_to_connect_to_metamask.got_it": "OK, értem", "how_to_connect_to_metamask.story.subtitle": "Válts egyszerűen a Zeal és más pénztárcák között bármikor.", "how_to_connect_to_metamask.story.title": "A Zeal más pénz<PERSON><PERSON><PERSON><PERSON> melle<PERSON> is működik", "how_to_connect_to_metamask.why_switch": "<PERSON><PERSON><PERSON> válts a Zeal és más pénztárcák között?", "how_to_connect_to_metamask.why_switch.description": "Bármelyik pénztárcát is v<PERSON><PERSON><PERSON><PERSON><PERSON>, a Zeal biztonsági ellenőrzései mindig védeni fognak a rosszindulatú webhelyektől és tranzakcióktól.", "how_to_connect_to_metamask.why_switch.description_easy_switch": "<PERSON><PERSON><PERSON><PERSON>, hogy nehéz belevágni és új pénztárcát használni. Ezért megkönnyítettük a Zeal használatát a meglévő pénztárcád mellett. Válts bármikor.", "import-bank-transfer-owner.banner.title": "Megváltozott a banki utalások tárcája. A folytatáshoz importálja.", "import-bank-transfer-owner.title": "Importáld a pénztárcát a banki átutalások használatához ezen az eszközön", "import_gnosispay_wallet.add-another-card-owner.footnote": "A Gnosis Pay kártyádhoz tartozó privát kulcs vagy seed phrase importálása", "import_gnosispay_wallet.primaryText": "Gnosis Pay tárca importálása", "injected-wallet": "Böngészőtárca", "intercom.getHelp": "Segítségkérés", "invalid_iban.got_it": "É<PERSON><PERSON>", "invalid_iban.subtitle": "A megadott IBAN érvénytelen. <PERSON><PERSON><PERSON><PERSON>, hogy he<PERSON>n ad<PERSON>-e meg az adato<PERSON>, majd p<PERSON><PERSON><PERSON><PERSON><PERSON>.", "invalid_iban.title": "Érvénytelen IBAN", "keypad-0": "Billentyűzet 0-s gomb", "keypad-1": "Billentyűzet 1-es gomb", "keypad-2": "Billentyűzet 2-es gomb", "keypad-3": "Billentyűzet 3-as gomb", "keypad-4": "Billentyűzet 4-es gomb", "keypad-5": "Billentyűzet 5-<PERSON><PERSON> gomb", "keypad-6": "Billentyűzet 6-o<PERSON> gomb", "keypad-7": "Billentyűzet 7-es gomb", "keypad-8": "Billentyűzet 8-as gomb", "keypad-9": "Billentyűzet 9-es gomb", "keypad.biometric-button": "Biometrikus gomb a billentyűzeten", "keystore.SecretPhraseTest.SecretPhraseVerified.title": "A titkos kifejezés biztonságban 🎉", "keystore.secret_phrase_test.wrong_answer.view_phrase_cta": "Kifejezés megtekintése", "keystore.secret_phrase_test.wrong_answer.view_phrase_subtitle": "Tartsd a titkos kifejezésed egy biztonságos, offline másolatát, hogy később helyreállíthasd az eszközeidet", "keystore.secret_phrase_test.wrong_answer.view_phrase_title": "Ne próbáld meg kitalálni a szót", "keystore.write_secret_phrase.before_you_begin.first_point": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ho<PERSON> b<PERSON>, aki is<PERSON>i a titkos kifejezésemet, átutalhatja az eszközeimet", "keystore.write_secret_phrase.before_you_begin.second_point": "Én vagyok a felelős a titkos kifejezésem titokban és biztonságban tartásáért", "keystore.write_secret_phrase.before_you_begin.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, olvasd el és fogadd el a következő pontokat:", "keystore.write_secret_phrase.before_you_begin.third_point": "<PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON> vag<PERSON>, ninc<PERSON><PERSON> körülöttem emberek vagy kamer<PERSON>", "keystore.write_secret_phrase.before_you_begin.title": "<PERSON><PERSON><PERSON><PERSON> el<PERSON>denéd", "keystore.write_secret_phrase.secret_phrase_test.title": "Melyik szó a(z) {count} . a titkos kifejezésedben?", "keystore.write_secret_phrase.test_ps.lets_do_it": "Vágjunk bele", "keystore.write_secret_phrase.test_ps.subtitle": "Szükséged lesz a titkos kifejezésedre a fiókod helyreállításához ezen vagy más eszközökön. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy helyesen írtad-e le a titkos kifejezést.", "keystore.write_secret_phrase.test_ps.subtitle2": "<PERSON><PERSON> fogunk kérdezni {count} szóra a kifejezésedből.", "keystore.write_secret_phrase.test_ps.title": "Fiók-helyreállítás tesztelése", "kyc.modal.approved.button-text": "Banki átutalás", "kyc.modal.approved.subtitle": "Az ellenőrzésed kész. Mostantól korlátlanul utalhatsz.", "kyc.modal.approved.title": "Banki átutalások feloldva", "kyc.modal.continue-with-partner.button-text": "<PERSON><PERSON><PERSON><PERSON>", "kyc.modal.continue-with-partner.subtitle": "Most átirányítunk a partnerünkhöz a dokumentumok összegyűjtéséhez és az ellenőrzési kérelem kitöltéséhez.", "kyc.modal.continue-with-partner.title": "Folytatás a partnerünknél", "kyc.modal.failed.unblock.subtitle": "Az Unblock nem hagyta jóvá az azonosításodat, így nem tud banki átutalást biztosítani.", "kyc.modal.failed.unblock.title": "Unblock kérelem eluta<PERSON>ítva", "kyc.modal.paused.button-text": "Adatok f<PERSON>", "kyc.modal.paused.subtitle": "Valamelyik adatod hibásnak tűnik. <PERSON>r<PERSON><PERSON><PERSON><PERSON>, és ellenőrizd az adataidat küldés el<PERSON>tt.", "kyc.modal.paused.title": "<PERSON>z adataid hibásnak tűnnek", "kyc.modal.pending.button-text": "Bezárás", "kyc.modal.pending.subtitle": "<PERSON>z ellenőrzés <PERSON> 10 percen belül k<PERSON>z, de néha tovább tarthat.", "kyc.modal.pending.title": "Tájékoztatni fogunk", "kyc.modal.required.cta": "Ellenőrzés indítása", "kyc.modal.required.subtitle": "Elérted a tranzakciós limited. A folytatáshoz igazold magad. Ez általában pár perc, és néhány sze<PERSON>lyes adatra, dokumentumra lesz szükség.", "kyc.modal.required.title": "Személyazonosság igazolása szükséges", "kyc.submitted": "K<PERSON><PERSON>em <PERSON>", "kyc.submitted_short": "Beküldve", "kyc_status.completed_status": "<PERSON><PERSON><PERSON>", "kyc_status.failed_status": "Sikertelen", "kyc_status.paused_status": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>g<PERSON><PERSON>", "kyc_status.subtitle": "Banki átutalások", "kyc_status.subtitle.wrong_details": "<PERSON><PERSON><PERSON><PERSON>", "kyc_status.subtitle_in_progress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kyc_status.title": "Személyazonosság ellenőrzése", "label.close": "Bezárás", "label.saving": "Mentés...", "labels.this-month": "Ez a hónap", "labels.today": "Ma", "labels.yesterday": "Tegnap", "language.selector.title": "Nyelv", "ledger.account_loaded.imported": "I<PERSON>rt<PERSON><PERSON><PERSON>", "ledger.add.success.title": "Ledger si<PERSON><PERSON>n c<PERSON> 🎉", "ledger.connect.cta": "<PERSON><PERSON>", "ledger.connect.step1": "Csatlakoztasd a Ledgert az eszközödhöz", "ledger.connect.step2": "Nyisd meg az Ethereum appot a Ledgeren", "ledger.connect.step3": "<PERSON><PERSON> <PERSON><PERSON> a <PERSON>gert 👇", "ledger.connect.subtitle": "Kövesd ezeket a lépéseket a Ledger tárcáid Zealba importálásához", "ledger.connect.title": "<PERSON><PERSON> c<PERSON>lakoztatása a Zealhoz", "ledger.error.ledger_is_locked.subtitle": "Oldd fel a Ledgert és nyisd meg az Ethereum appot", "ledger.error.ledger_is_locked.title": "A Ledger z<PERSON><PERSON> van", "ledger.error.ledger_not_connected.action": "<PERSON><PERSON>", "ledger.error.ledger_not_connected.subtitle": "Csatlakoztasd a hardveres pénztárcádat a készülékedhez és nyisd meg az Ethereum alkalmazást.", "ledger.error.ledger_not_connected.title": "A Ledger nincs c<PERSON>", "ledger.error.ledger_running_non_eth_app.title": "Az Ethereum app nincs megnyitva", "ledger.error.user_trx_denied_by_user.action": "Bezárás", "ledger.error.user_trx_denied_by_user.subtitle": "Elutasítottad a tranzakciót a hardveres tárc<PERSON>don", "ledger.error.user_trx_denied_by_user.title": "T<PERSON><PERSON><PERSON><PERSON> elutasítva", "ledger.hd_path.bip44.subtitle": "pl. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "ledger.hd_path.bip44.title": "BIP44 sza<PERSON>y", "ledger.hd_path.ledger_live.subtitle": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.hd_path.legacy.subtitle": "MEW/MyCrypto", "ledger.hd_path.legacy.title": "Legacy", "ledger.hd_path.phantom.subtitle": "pl. Phantom", "ledger.select.hd_path.subtitle": "A HD elérési utak segítségével rendezik a hardveres pénztárcák a fiókokat. <PERSON><PERSON><PERSON><PERSON><PERSON>, mint egy tárgymutató a könyv oldalait.", "ledger.select.hd_path.title": "HD elérési út kiválasztása", "ledger.select_account.import_wallets_count": "{count,plural,=0{Nincs tárca kiválasztva} one{Tárca importálása} other{{count} tárca importálása}}", "ledger.select_account.path_settings": "Elérési út beállításai", "ledger.select_account.subtitle": "Nem látod a várt pénztárcákat? Próbáld megváltoztatni az elérési út beállításait", "ledger.select_account.subtitle.group_header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ledger.select_account.title": "Led<PERSON><PERSON><PERSON><PERSON>", "legend.lending-operations": "Hitelezési műveletek", "legend.market_making-operations": "Piacépítési mű<PERSON>k", "legend.treasury-operations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link-existing-monerium-account-sign.button": "<PERSON><PERSON> összeka<PERSON>", "link-existing-monerium-account-sign.subtitle": "<PERSON><PERSON><PERSON>-fió<PERSON>d.", "link-existing-monerium-account-sign.title": "Ka<PERSON><PERSON>old össze a Zealt a meglévő Monerium-fiókoddal", "link-existing-monerium-account.button": "Monerium.app", "link-existing-monerium-account.subtitle": "<PERSON><PERSON><PERSON> Monerium-fiókod. A beállítás befejezéséhez lépj a Monerium alkalmazásba.", "link-existing-monerium-account.title": "Lépj a Moneriumba a fiókod összekapcsolásához", "loading.pin": "PIN-kód betö<PERSON>...", "lockScreen.passwordIncorrectMessage": "<PERSON><PERSON><PERSON><PERSON>", "lockScreen.passwordRequiredMessage": "Jelszó megadása kötelező", "lockScreen.unlock.header": "<PERSON><PERSON><PERSON><PERSON>", "lockScreen.unlock.subheader": "A Zeal felold<PERSON><PERSON><PERSON><PERSON> hasz<PERSON>ld a j<PERSON>zavad", "mainTabs.activity.label": "Tevékenység", "mainTabs.browse.label": "Böngészés", "mainTabs.browse.title": "Böngészés", "mainTabs.card.label": "<PERSON><PERSON><PERSON><PERSON>", "mainTabs.portfolio.label": "Portfólió", "mainTabs.rewards.label": "Jutalmak", "makeSpendable.cta": "Elkölthetővé teszem", "makeSpendable.holdAsCash": "Készpénzként tartom", "makeSpendable.shortText": "<PERSON><PERSON> {apy} hozam", "makeSpendable.title": "{amount} be<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.agriculture": "Mezőgazdaság", "merchantCategory.alcohol": "Alkohol", "merchantCategory.antiques": "Régiségek", "merchantCategory.appliances": "Háztartási gépek", "merchantCategory.artGalleries": "Művészeti galériák", "merchantCategory.autoRepair": "Autójavítás", "merchantCategory.autoRepairService": "Autójavító s<PERSON>", "merchantCategory.beautyFitnessSpas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, fitnesz és gyógyfürdők", "merchantCategory.beautyPersonalCare": "Szépség- és testápolás", "merchantCategory.billiard": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.books": "Könyvek", "merchantCategory.bowling": "Bowling", "merchantCategory.businessProfessionalServices": "Üzleti és professzionális szolgáltatások", "merchantCategory.carRental": "Autóbérlés", "merchantCategory.carWash": "Au<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.cars": "Autók", "merchantCategory.casino": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.casinoGambling": "<PERSON><PERSON><PERSON><PERSON>s szerencsejáték", "merchantCategory.cellular": "Mobil", "merchantCategory.charity": "<PERSON><PERSON><PERSON><PERSON>ys<PERSON>", "merchantCategory.childcare": "Gyermekfelügyelet", "merchantCategory.cigarette": "Cigaretta", "merchantCategory.cinema": "<PERSON><PERSON>", "merchantCategory.cinemaEvents": "Mozi és események", "merchantCategory.cleaning": "Takarítás", "merchantCategory.cleaningMaintenance": "Takarítás és karbantartás", "merchantCategory.clothes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.clothingServices": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>gá<PERSON>ások", "merchantCategory.communicationServices": "Kommunik<PERSON><PERSON><PERSON> s<PERSON>ások", "merchantCategory.construction": "Építkezés", "merchantCategory.cosmetics": "Kozmetikumok", "merchantCategory.craftsArtSupplies": "Kézműves- és művészellátó", "merchantCategory.datingServices": "Társkereső szolgáltatások", "merchantCategory.delivery": "Kiszállítás", "merchantCategory.dentist": "Fogorvos", "merchantCategory.departmentStores": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.directMarketingSubscription": "Direkt marketing és előfizetés", "merchantCategory.discountStores": "Diszkont üzletek", "merchantCategory.drugs": "Gyógyszerek", "merchantCategory.dutyFree": "Vámmentes", "merchantCategory.education": "Oktatás", "merchantCategory.electricity": "Villamos energia", "merchantCategory.electronics": "Elektronika", "merchantCategory.emergencyServices": "Sürgősségi szolgáltatások", "merchantCategory.equipmentRental": "Eszközkölcsönzés", "merchantCategory.evCharging": "E-aut<PERSON> t<PERSON>", "merchantCategory.financialInstitutions": "Pénzintézetek", "merchantCategory.financialProfessionalServices": "Pénzügyi és professzionális szolgáltatások", "merchantCategory.finesPenalties": "Bírságok és büntetések", "merchantCategory.fitness": "Fitnesz", "merchantCategory.flights": "Repülőjegyek", "merchantCategory.flowers": "Virágok", "merchantCategory.flowersGarden": "V<PERSON><PERSON><PERSON> kert", "merchantCategory.food": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.freight": "Szállítmányozás", "merchantCategory.fuel": "Üzemanyag", "merchantCategory.funeralServices": "Temetkezési szolgáltatások", "merchantCategory.furniture": "<PERSON><PERSON><PERSON>", "merchantCategory.games": "Játékok", "merchantCategory.gas": "Üzemanyag", "merchantCategory.generalMerchandiseRetail": "Általános áru<PERSON>kkek és kiskereskedelem", "merchantCategory.gifts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.government": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.governmentServices": "Kormányzati szolgáltatások", "merchantCategory.hardware": "Barkácsáru", "merchantCategory.healthMedicine": "Egészség és gyógyszer", "merchantCategory.homeImprovement": "Lakásfelújítás", "merchantCategory.homeServices": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.hotel": "Szálloda", "merchantCategory.housing": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.insurance": "Biztosítás", "merchantCategory.internet": "Internet", "merchantCategory.kids": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.laundry": "Mo<PERSON><PERSON>", "merchantCategory.laundryCleaningServices": "Mosoda és takarítás", "merchantCategory.legalGovernmentFees": "<PERSON><PERSON> kormányzati <PERSON>", "merchantCategory.luxuries": "Luxuscikkek", "merchantCategory.luxuriesCollectibles": "Luxuscikkek és gyűjtemények", "merchantCategory.magazines": "Magazinok", "merchantCategory.magazinesNews": "Magazinok és hírek", "merchantCategory.marketplaces": "Piacterek", "merchantCategory.media": "Média", "merchantCategory.medicine": "Gyógyszer", "merchantCategory.mobileHomes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.moneyTransferCrypto": "Pénzátutalás és kripto", "merchantCategory.musicRelated": "<PERSON><PERSON>", "merchantCategory.musicalInstruments": "Hangszerek", "merchantCategory.optics": "Optika", "merchantCategory.organizationsClubs": "Szervezetek és klubok", "merchantCategory.other": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.parking": "Parkolás", "merchantCategory.pawnShops": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.pets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.photoServicesSupplies": "Fotószolgáltatások és kellékek", "merchantCategory.postalServices": "Postai szolgáltatások", "merchantCategory.professionalServicesOther": "Professzionális <PERSON>zo<PERSON>g<PERSON>ások (egyéb)", "merchantCategory.publicTransport": "Tömegközlekedés", "merchantCategory.purchases": "Vásárlások", "merchantCategory.purchasesMiscServices": "Vásárlások és egyéb szolgáltatások", "merchantCategory.recreationServices": "Szabadidős szolgáltatások", "merchantCategory.religiousGoods": "Vallási cikkek", "merchantCategory.secondhandRetail": "Használtcikk-kereskedelem", "merchantCategory.shoeHatRepair": "Cipő- és <PERSON>", "merchantCategory.shoeRepair": "Cipőjavítás", "merchantCategory.softwareApps": "Szoftverek és alkalmazások", "merchantCategory.specializedRepairs": "Szakszervizek", "merchantCategory.sport": "Sport", "merchantCategory.sportingGoods": "Sportszerek", "merchantCategory.sportingGoodsRecreation": "Sportfelszerelés és szabadidő", "merchantCategory.sportsClubsFields": "Sportklubok és pályák", "merchantCategory.stationaryPrinting": "Papír<PERSON><PERSON><PERSON><PERSON>zer és nyomtatás", "merchantCategory.stationery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.storage": "<PERSON><PERSON><PERSON><PERSON>", "merchantCategory.taxes": "<PERSON><PERSON><PERSON>", "merchantCategory.taxi": "Taxi", "merchantCategory.telecomEquipment": "Telekommunikációs <PERSON>közök", "merchantCategory.telephony": "Telefonszolgáltatás", "merchantCategory.tobacco": "Dohányáru", "merchantCategory.tollRoad": "Fizetős út", "merchantCategory.tourismAttractionsAmusement": "<PERSON><PERSON><PERSON>, látnivalók és szórakozás", "merchantCategory.towing": "Autómentés", "merchantCategory.toys": "Játékok", "merchantCategory.toysHobbies": "Játékok és hobbik", "merchantCategory.trafficFine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchantCategory.train": "Vonat", "merchantCategory.travelAgency": "Utazási iroda", "merchantCategory.tv": "TV", "merchantCategory.tvRadioStreaming": "TV, rádió és streaming", "merchantCategory.utilities": "Közművek", "merchantCategory.waterTransport": "<PERSON><PERSON><PERSON>", "merchantCategory.wholesaleClubs": "Nagykereskedelmi klubok", "metaMask.subtitle": "Engedélyezd a MetaMask Módot, hogy minden MetaMask kapcsolatot a Zeal-ra irányíts. Így a dApp-okban a MetaMask-ra kattintva a Zeal-hoz csatlakozol.", "metaMask.title": "<PERSON>em t<PERSON> c<PERSON> a Zeal-lal?", "monerium-bank-deposit.bullet-point.open-your-bank-app": "Nyisd meg a banki alkalmazásod", "monerium-bank-deposit.buttet-point.receive-crypto": "Fogadj <PERSON>", "monerium-bank-deposit.buttet-point.send-eur-to-your-account": "Küldj {fiatCurrencyCode} összeget a fiókodra", "monerium-bank-deposit.deposit-account-country": "<PERSON><PERSON><PERSON><PERSON>", "monerium-bank-deposit.header": "{fullName} s<PERSON><PERSON><PERSON><PERSON>", "monerium-bank-details.account-name": "Számlatulajdonos neve", "monerium-bank-details.bic-swift": "BIC/SWIFT", "monerium-bank-details.bic-swift-copied": "BIC/SWIFT másolva", "monerium-bank-details.bic_swift_copied": "BIC/SWIFT másolva", "monerium-bank-details.iban": "IBAN", "monerium-bank-details.iban_copied": "IBAN másolva", "monerium-bank-details.to-wallet": "Pénztárcába", "monerium-bank-details.transfer-fee": "Átutalási díj", "monerium-bank-transfer.enable-card.bullet-1": "Végezd el a személyazonosság-ellenőrzést", "monerium-bank-transfer.enable-card.bullet-2": "<PERSON><PERSON><PERSON><PERSON><PERSON>tok megkapása", "monerium-bank-transfer.enable-card.bullet-3": "Befizetés bankszámláról", "monerium-card-delay-relay.success.cta": "Bezárás", "monerium-card-delay-relay.success.subtitle": "Biztonsági okokból a kártyabeállítások módosításának feldolgozása 3 percet vesz igénybe.", "monerium-card-delay-relay.success.title": "Gyere vissza 3 perc múlva a Monerium beállításának folytatásához", "monerium-deposit.account-details-info-popup.bullet-point-1": "<PERSON><PERSON><PERSON><PERSON><PERSON> {fiatCurrencyCode} , amit erre a sz<PERSON><PERSON><PERSON><PERSON><PERSON>, automatikusan átváltódik {cryptoCurrencyCode} tokenre a(z) {cryptoCurrency<PERSON>hain} láncon, és a pénztárcádba kerül", "monerium-deposit.account-details-info-popup.bullet-point-2": "CSAK {fiatCurrencyCode} ({fiatCurrencySymbol}) küldj a számládra", "monerium-deposit.account-details-info-popup.title": "A számlaadataid", "monerium.check_order_status.sending": "<PERSON><PERSON><PERSON><PERSON>...", "monerium.not-eligible.cta": "<PERSON><PERSON><PERSON>", "monerium.not-eligible.subtitle": "A Monerium nem tud s<PERSON>mlát nyitni neked. Válassz másik szolgáltatót.", "monerium.not-eligible.title": "Próbálj másik szolgáltatót", "monerium.setup-card.cancel": "<PERSON><PERSON><PERSON><PERSON>", "monerium.setup-card.continue": "<PERSON><PERSON><PERSON><PERSON>", "monerium.setup-card.create_account": "Fiók létrehozása", "monerium.setup-card.login": "Bejelentkezés Gnosis Pay", "monerium.setup-card.subtitle": "Azonnali banki befizetéshez hozz létre vagy lépj be a Gnosis Pay fiókodba.", "monerium.setup-card.subtitle_personal_account": "Szerezd meg s<PERSON><PERSON><PERSON>es Gnosis Pay-fiókodat percek alatt:", "monerium.setup-card.title": "Banki befizetések engedélyezése", "moneriumDepositSuccess.goToWallet": "Pénztárca megnyitása", "moneriumDepositSuccess.title": "{symbol} me<PERSON><PERSON><PERSON><PERSON>", "moneriumInfo.fees": "0%-os díjak<PERSON> számolhatsz", "moneriumInfo.registration": "A Monerium az izlandi 17/2013. sz. elektronikus pénzről szóló törvény alapján engedélyezett és szabályozott Elektronikus Pénzintézet. <link>Tudj meg többet</link>", "moneriumInfo.selfCustody": "A kapott digitális készpénz a te felügyeleted alatt <PERSON>, és senki más nem férhet hozzá az eszközeidhez.", "moneriumWithdrawRejected.supportText": "<PERSON><PERSON> végrehajtani az átutalást. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, és ha még mindig nem működik, ak<PERSON> <link>vedd fel a kapcsolatot az ügyfélszolgálattal.</link>", "moneriumWithdrawRejected.title": "<PERSON><PERSON><PERSON><PERSON>", "moneriumWithdrawRejected.tryAgain": "Újra", "moneriumWithdrawSuccess.supportText": "Akár 24 ór<PERSON>t is i<PERSON><PERSON><PERSON> vehet, amíg a {br}kedvezményezett megkapja az összeget", "moneriumWithdrawSuccess.title": "Elküldve", "monerium_enable_banner.text": "Banki átutalások aktiválása most", "monerium_error_address_re_link_required.title": "A tárcát új<PERSON> kell kötni a Moneriumhoz", "monerium_error_duplicate_order.title": "Ismétlődő megbízás", "money.FormattedFeeInNativeTokenCurrency": "{amount} {code}", "money.FormattedFeeInNativeTokenCurrency.truncated": "< {limit}", "mt-pelerin-fork.options.chf.primary": "<PERSON><PERSON><PERSON><PERSON><PERSON> frank", "mt-pelerin-fork.options.chf.short": "Azonnali és ingyenes a Mt Pelerinnel", "mt-pelerin-fork.options.euro.primary": "<PERSON><PERSON><PERSON>", "mt-pelerin-fork.options.euro.short": "Azonnali és ingyenes a Moneriummal", "mt-pelerin-fork.title": "<PERSON>t s<PERSON>et<PERSON> befize<PERSON>?", "mtPelerinProviderInfo.fees": "0% díjat fi<PERSON>z", "mtPelerinProviderInfo.registration": "A Mt Pelerin Group Ltd a SO-FIT tagja, amely a svájci pénzügyi felügyelet (FINMA) által a pénzmosás elleni törvény értelmében elismert öns<PERSON> testü<PERSON>. <link>További információ</link>", "mtPelerinProviderInfo.selfCustody": "A kapott digitális készpénz a te privát pénztárcádba kerül, és senki más nem férhet hozzá az eszközeidhez", "network-fee-widget.title": "<PERSON><PERSON><PERSON>", "network.edit.verifying_rpc": "RPC ellenőrzése", "network.editRpc.predefined_network_info.subtitle": "A VPN-he<PERSON> has<PERSON> a Zeal olyan RPC-ket hasz<PERSON>, am<PERSON><PERSON> mega<PERSON>ozz<PERSON> a sze<PERSON><PERSON>es adataid nyomon követését.{br}{br}A Zeal alapértelmezett RPC-i megbízható, sokat bizonyított RPC-szolgáltatók.", "network.editRpc.predefined_network_info.title": "Zeal adat<PERSON><PERSON><PERSON>mi R<PERSON>", "network.filter.update_rpc_success": "RPC-csomópont mentve", "network.name.Arbitrum": "Arbitrum", "network.name.Aurora": "Aurora", "network.name.Avalanche": "Avalanche", "network.name.AvalancheFuji": "Ava<PERSON>", "network.name.BSC": "BNB Chain", "network.name.Base": "Base", "network.name.Blast": "Blast", "network.name.BscTestnet": "BNB Chain Testnet", "network.name.Celo": "<PERSON><PERSON>", "network.name.Cronos": "Cronos", "network.name.Ethereum": "Ethereum", "network.name.EthereumSepolia": "Ethereum <PERSON>", "network.name.Fantom": "<PERSON><PERSON>", "network.name.FantomTestnet": "Fantom Testnet", "network.name.Gnosis": "Gnosis", "network.name.Linea": "Linea", "network.name.Manta": "Manta", "network.name.Mantle": "Mantle", "network.name.OPBNB": "OPBNB", "network.name.Optimism": "Optimism", "network.name.Polygon": "Polygon", "network.name.PolygonZkevm": "Polygon zkEVM", "network.name.allNetworks": "<PERSON><PERSON>", "network.name.zkSync": "zkSync", "networks.filter.add_modal.add_networks": "Hálózatok hozzáadása", "networks.filter.add_modal.chain_list.subtitle": "Bármely EVM-hálózat hozzáadása", "networks.filter.add_modal.chain_list.title": "Ugrás a Chainlistre", "networks.filter.add_modal.dapp_tip.subtitle": "A kedvenc dAppjaidban egyszerűen válts arra az EVM-hálózatra, amelyet has<PERSON>lni <PERSON>él, és a Zeal meg<PERSON>é<PERSON>zi, hogy hozzá akarod-e adni a pénztárcádhoz.", "networks.filter.add_modal.dapp_tip.title": "<PERSON><PERSON><PERSON> adj hozz<PERSON> h<PERSON><PERSON>óza<PERSON>t bármely dAppból", "networks.filter.add_networks.subtitle": "Minden EVM-h<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>ott", "networks.filter.add_networks.title": "Hálózatok hozzáadása", "networks.filter.add_test_networks.title": "Teszthálózatok hozzáadása", "networks.filter.tab.netwokrs": "Hálózatok", "networks.filter.testnets.title": "Teszthálózatok", "nft.widget.emptystate": "Nincsenek gyűjtemények a tárcádban", "nft_collection.change_account_picture.subtitle": "Biztosan frissíteni szeretnéd a profilképed?", "nft_collection.change_account_picture.title": "Profilkép frissítése NFT-re", "nfts.allNfts.pricingPopup.description": "A gyűjtemények árai a legutóbbi kereskedési áron alapulnak.", "nfts.allNfts.pricingPopup.title": "Gyűjtemények árazása", "no-passkeys-found.modal.cta": "Bezárás", "no-passkeys-found.modal.subtitle": "Nem <PERSON>ünk <PERSON>eal hozzáférési kulcsokat ezen az eszközön. Győződj meg róla, hogy abba a felhőfiókba vagy <PERSON>, amellyel a Smart Walletot létrehoztad.", "no-passkeys-found.modal.title": "<PERSON><PERSON> ho<PERSON>é<PERSON> kulcs", "notValidEmail.title": "Érvénytelen e-mail-cím", "notValidPhone.title": "Ez nem érvényes telefonszám", "notification-settings.title": "Értesítési be<PERSON>", "notification-settings.toggles.active-wallets": "<PERSON>ktív <PERSON>", "notification-settings.toggles.bank-transfers": "Banki átutalások", "notification-settings.toggles.card-payments": "K<PERSON><PERSON><PERSON>ás fi<PERSON>", "notification-settings.toggles.readonly-wallets": "Csak olvasható <PERSON>", "ntft.groupHeader.text": "Gyűjtemények", "on_ramp.crypto_completed": "<PERSON><PERSON><PERSON>", "on_ramp.fiat_completed": "<PERSON><PERSON><PERSON>", "onboarding-widget.subtitle.card_created_from_order.left": "Visa kártya", "onboarding-widget.subtitle.card_created_from_order.right": "Kártya aktiválása", "onboarding-widget.subtitle.card_order_ready.left": "Fizikai Visa kártya", "onboarding-widget.subtitle.default": "Banki átutalások és Visa kártya", "onboarding-widget.title.card-order-in-progress": "<PERSON><PERSON><PERSON>yarendelés folytatása", "onboarding-widget.title.card_created_from_order": "A kártyát postáztuk", "onboarding-widget.title.kyc_approved": "Átutalások és kártya használatra kész", "onboarding-widget.title.kyc_failed": "Fiók létrehozása nem lehetséges", "onboarding-widget.title.kyc_not_started": "Beállítás folytatása", "onboarding-widget.title.kyc_started_documents_requested": "Hitelesítés befejezése", "onboarding-widget.title.kyc_started_resubmission_requested": "<PERSON><PERSON><PERSON><PERSON>", "onboarding-widget.title.kyc_started_verification_in_progress": "Személyazonosság ellenőrzése", "onboarding.loginOrCreateAccount.amountOfAssets": "10+ milli<PERSON><PERSON> eszköz", "onboarding.loginOrCreateAccount.cards.subtitle": "Csak bizonyos régiókban érhető el. A folytatással elfogadod a(z) <Terms>Felhasználási feltételeket</Terms> és a(z) <PrivacyPolicy>Adatvédelmi irányelveket</PrivacyPolicy>", "onboarding.loginOrCreateAccount.cards.title": "Visa kártya magas {br}hozammal és díjak nélkül", "onboarding.loginOrCreateAccount.createAccount": "Fiók létrehozása", "onboarding.loginOrCreateAccount.earn.subtitle": "A hozamok változhatnak; a tőke kockázatnak van kitéve. A folytatással elfogadod a(z) <Terms>Felhasználási feltételeket</Terms> és a(z) <PrivacyPolicy>Adatvédelmi irányelveket</PrivacyPolicy>", "onboarding.loginOrCreateAccount.earn.title": "<PERSON><PERSON><PERSON><PERSON> {percent} <PERSON><PERSON> hozam{br}Több mint {currencySymbol}5+ milliárd <PERSON> eszköz", "onboarding.loginOrCreateAccount.earningPerYear": "<PERSON><PERSON><PERSON><PERSON> {percent}{br} <PERSON><PERSON> hozam", "onboarding.loginOrCreateAccount.login": "Bejelentkezés", "onboarding.loginOrCreateAccount.trading.subtitle": "A tőke kockázatnak van kitéve. A folytatással elfogadod a(z) <Terms>Felhasználási feltételeket</Terms> és a(z) <PrivacyPolicy>Adatvédelmi irányelveket</PrivacyPolicy>", "onboarding.loginOrCreateAccount.trading.title": "Fektess be b<PERSON><PERSON><PERSON>, {br}BTC-től az S&P-ig", "onboarding.loginOrCreateAccount.trustedBy": "Digitális pénzpiacok{br}Több mint {assets}", "onboarding.wallet_stories.close": "Bezárás", "onboarding.wallet_stories.previous": "Előző", "order-earn-deposit-bridge.deposit": "Be<PERSON>ze<PERSON>s", "order-earn-deposit-bridge.into": "Ide:", "otpIncorrectMessage": "A megerősítő kód helytelen", "passkey-creation-not-possible.modal.close": "Bezárás", "passkey-creation-not-possible.modal.subtitle": "<PERSON><PERSON>-t létrehozni a tárcádhoz. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, győződj meg róla, hogy az eszközöd támogatja a Passkey-ket, és próbáld <PERSON>. <link>Lépj kapcsolatba az ügyfélszolgálattal</link> , ha a probléma továbbra is fennáll.", "passkey-creation-not-possible.modal.title": "<PERSON><PERSON>-t létrehozni", "passkey-not-supported-in-mobile-browser.modal.cta": "Zeal letöltése", "passkey-not-supported-in-mobile-browser.modal.subtitle": "A Smart Wallet-ek nem támogatottak mobilböngészőkben.", "passkey-not-supported-in-mobile-browser.modal.title": "A folytatáshoz töltsd le a Zeal alkalmazást", "passkey-recovery.recovering.deploy-signer.loading-text": "Hozzáférési kulcs ellenőrzése", "passkey-recovery.recovering.loading-text": "Pénztárca helyreállítása...", "passkey-recovery.recovering.signer-not-found.subtitle": "<PERSON>em tud<PERSON>k a hozzáférési kulcsodat egy aktív pénztárcához kapcsolni. Ha van pénz a pénz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vedd fel a kap<PERSON>ola<PERSON>t a Zeal csapattal.", "passkey-recovery.recovering.signer-not-found.title": "<PERSON><PERSON> p<PERSON>zt<PERSON>", "passkey-recovery.recovering.signer-not-found.try-with-different-passkey": "Másik kulcs kipróbálása", "passkey-recovery.select-passkey.banner.subtitle": "Jelentkezz be a jó fiókba. A hozzáférési kulcsok fiókhoz kötöttek.", "passkey-recovery.select-passkey.banner.title": "Nem l<PERSON>d a pénztárcád hozzáférési kul<PERSON>?", "passkey-recovery.select-passkey.continue": "Belépőkulcs kiválasztása", "passkey-recovery.select-passkey.subtitle": "A hozzáférés visszaszerzéséhez válaszd ki a pénztárcádhoz kapcsolt hozzáférési kulcsot.", "passkey-recovery.select-passkey.title": "Hozzáférési kulcs kiválasztása", "passkey-story_1.subtitle": "A Smart Wallet segítségével a legtöbb tokennel fizetheted a hálózati díjakat, és nem kell a natív tokenek miatt aggódnod.", "passkey-story_1.title": "<PERSON><PERSON><PERSON> díjat a legtöbb tokennel", "passkey-story_2.subtitle": "A Safe iparágvezető okosszerződéseire épül, amelyek több mint 100 milliárd dollárt biztosítanak több mint 20 millió tárcában.", "passkey-story_2.title": "A Safe biztosítja", "passkey-story_3.subtitle": "A Smart Wallet-ek a főbb Ethereum-kompatibilis hálózatokon működnek. Eszközök küldése előtt ellenőrizd a támogatott hálózatokat.", "passkey-story_3.title": "Főbb EVM hálózatok támogatottak", "password.add.header": "Jelszó létrehozása", "password.add.includeLowerAndUppercase": "Kis- és nagy<PERSON>", "password.add.includesNumberOrSpecialChar": "<PERSON><PERSON> s<PERSON> vagy s<PERSON>", "password.add.info.subtitle": "<PERSON>em <PERSON> el a j<PERSON>zavad a szervereinkre, és nem készítünk róla biztonsági mentést", "password.add.info.t_and_c": "A folytatással elfogadod a(z) <Terms>Felhasználási feltételeket</Terms> és a(z) <PrivacyPolicy>Adatvédelmi irányelveket</PrivacyPolicy>", "password.add.info.title": "A jelszavad ezen az eszközön marad", "password.add.inputPlaceholder": "Jelszó létrehozása", "password.add.shouldContainsMinCharsCheck": "10+ karakter", "password.add.subheader": "A jelszavaddal tudod majd feloldani a Zealt", "password.add.success.title": "Jelszó létrehozva 🔥", "password.confirm.header": "<PERSON><PERSON><PERSON>ó megerősítése", "password.confirm.passwordDidNotMatch": "A jelszavaknak egyezniük kell", "password.confirm.subheader": "Add meg a j<PERSON>zavad még egyszer", "password.create_pin.subtitle": "Ez a jelkód zárolja a Zeal alkalmazást", "password.create_pin.title": "<PERSON><PERSON><PERSON><PERSON>", "password.enter_pin.title": "<PERSON><PERSON><PERSON><PERSON>ga<PERSON>", "password.incorrectPin": "<PERSON><PERSON><PERSON><PERSON>", "password.pin_is_not_same": "A jelkódok nem egyeznek", "password.placeholder.enter": "Jelszó megadása", "password.placeholder.reenter": "<PERSON><PERSON><PERSON><PERSON>", "password.re_enter_pin.subtitle": "Add meg újra ugyanazt a jelkódot", "password.re_enter_pin.title": "<PERSON><PERSON><PERSON><PERSON>", "pending-card-balance": "{amount} · {timer}", "pending-send.deatils.pending": "Függőben", "pending-send.details.pending": "Függőben", "pending-send.details.processing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "permit-info.modal.description": "<PERSON>z engedély<PERSON> o<PERSON>, am<PERSON><PERSON> al<PERSON>írása esetén lehetővé teszik az alkalmazások számára, hogy a nevedben mozgas<PERSON>ák a tokenje<PERSON>t, péld<PERSON><PERSON> egy csere végrehajtásához.{br}<PERSON>z engedélyek hasonlóak a jóváhagyásokhoz, de aláírásuk nem kerül hálózati díjba.", "permit-info.modal.title": "Mik azok az engedélyek?", "permit.edit-expiration": "Szerkesztés: {currency} lejárata", "permit.edit-limit": "Szerkesztés: {currency} költési limit", "permit.edit-modal.expiresIn": "<PERSON><PERSON><PERSON><PERSON>…", "permit.expiration-warning": "{currency} lej<PERSON><PERSON><PERSON>", "permit.expiration.info": "{currency} lej<PERSON>rati <PERSON>", "permit.expiration.never": "<PERSON><PERSON>", "permit.spend-limit.info": "{currency} költési limit információ", "permit.spend-limit.warning": "{currency} költési limit figyelmeztetés", "phoneNumber.title": "telefonszám", "physicalCardOrderFlow.cardOrdered": "<PERSON><PERSON><PERSON><PERSON>", "physicalCardOrderFlow.city": "<PERSON><PERSON><PERSON>", "physicalCardOrderFlow.orderCard": "<PERSON><PERSON><PERSON><PERSON>", "physicalCardOrderFlow.postcode": "Irányí<PERSON>", "physicalCardOrderFlow.shippingAddress.subtitle": "Ide fogjuk küldeni a kártyádat", "physicalCardOrderFlow.shippingAddress.title": "Szállítási cím", "physicalCardOrderFlow.street": "Utca, házszám", "placeholderDapps.1inch.description": "Váltás a legjobb útvonalakon", "placeholderDapps.aave.description": "Tokenek kölcsönadása és -vétele", "placeholderDapps.bungee.description": "Hálózatok <PERSON>ása a legjobb útvonalakon", "placeholderDapps.compound.description": "Tokenek kölcsönadása és -vétele", "placeholderDapps.cowswap.description": "V<PERSON><PERSON><PERSON> a legjobb árfolyamon a Gnosison", "placeholderDapps.gnosis-pay.description": "Gnosis Pay kártyád kezel<PERSON>e", "placeholderDapps.jumper.description": "Hálózatok <PERSON>ása a legjobb útvonalakon", "placeholderDapps.lido.description": "Stakelj ETH-t több ETH-ért", "placeholderDapps.monerium.description": "ePénz és banki átutalások", "placeholderDapps.odos.description": "Váltás a legjobb útvonalakon", "placeholderDapps.stargate.description": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> <14% APY-ért", "placeholderDapps.uniswap.description": "Az egyik legnépszerűbb váltó", "pleaseAllowNotifications.cardPayments": "K<PERSON><PERSON><PERSON>ás fi<PERSON>", "pleaseAllowNotifications.customiseInSettings": "Testreszabás a beállításokban", "pleaseAllowNotifications.enable": "Engedélyezés", "pleaseAllowNotifications.forWalletActivity": "Pénztárca-tevékenységhez", "pleaseAllowNotifications.title": "Értesítések a pénztárcáról", "pleaseAllowNotifications.whenReceivingAssets": "Eszközök fogadásakor", "portfolio.quick-actions.add_funds": "Be<PERSON>ze<PERSON>s", "portfolio.quick-actions.buy": "Vétel", "portfolio.quick-actions.deposit": "Be<PERSON>ze<PERSON>s", "portfolio.quick-actions.send": "<PERSON><PERSON><PERSON><PERSON>", "portfolio.view.lastRefreshed": "Frissítve: {date}", "portfolio.view.topupTestNet.AvalancheFuji.primary": "Töltsd fel a teszthálózati AVAX-egyenleged", "portfolio.view.topupTestNet.AvalancheFuji.secondary": "Ugrás a Faucetre", "portfolio.view.topupTestNet.BscTestnet.primary": "Töltsd fel a teszthálózati BNB-egyenleged", "portfolio.view.topupTestNet.BscTestnet.secondary": "Ugrás a Faucetre", "portfolio.view.topupTestNet.EthereumSepolia.primary": "Töltsd fel a teszthálózati SepETH-egyenleged", "portfolio.view.topupTestNet.EthereumSepolia.secondary": "Ugrás a Sepolia Faucetre", "portfolio.view.topupTestNet.FantomTestnet.primary": "Töltsd fel a teszthálózati FTM-egyenleged", "portfolio.view.topupTestNet.FantomTestnet.secondary": "Ugrás a Faucetre", "privateKeyConfirmation.banner.subtitle": "<PERSON><PERSON><PERSON> a priv<PERSON>t k<PERSON>, a<PERSON><PERSON> a p<PERSON>. Csak a csalók kérik el.", "privateKeyConfirmation.banner.title": "Megértettem a kockázatokat", "privateKeyConfirmation.title": "SOHA NE OSZD MEG a privát kulcsodat senkivel", "rating-request.not-now": "Most nem", "rating-request.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a <PERSON>?", "receive_funds.address-text": "Ez az egyedi tárcacímed. Biztonságosan megoszthatod másokkal.", "receive_funds.copy_address": "Tárcacím másolása", "receive_funds.network-warning.eoa.subtitle": "<link>Standard hálózatok listája</link>. A nem EVM-hálózatokon küldött eszközök elvesznek.", "receive_funds.network-warning.eoa.title": "Minden Ethereum-alapú h<PERSON>óza<PERSON> t<PERSON>ott", "receive_funds.network-warning.scw.subtitle": "<link>Támogatott hálózatok</link>. <PERSON>ás hálózatokon küldött eszközök elvesznek.", "receive_funds.network-warning.scw.title": "Fontos: Csak támogatott hálózatokat használj", "receive_funds.scan_qr_code": "QR-k<PERSON><PERSON>", "receiving.in.days": "Érkezik {days} nap múlva", "receiving.this.week": "Ezen a héten érkezik", "receiving.today": "<PERSON>", "reference.error.maximum_number_of_characters_exceeded": "<PERSON><PERSON> sok karakter", "referral-code.placeholder": "Meghívó link beillesztése", "referral-code.subtitle": "Kattints újra a bar<PERSON><PERSON><PERSON> link<PERSON>, vagy illeszd be a linket alább. <PERSON><PERSON><PERSON><PERSON>, ha biztosan megkapnád a jutalmad.", "referral-code.title": "<PERSON><PERSON><PERSON><PERSON> neked {bReward}?", "rekyc.verification_deadline.subtitle": "Hajtsd végre az ellenőrzést {daysUntil} napon belül, hogy tov<PERSON><PERSON> hasz<PERSON>hasd a kártyádat.", "rekyc.verification_required.subtitle": "A kártya további használatához végezd el az ellenőrzést.", "reminder.fund": "💸 Fizess be — keress azonnal 6%-ot", "reminder.onboarding": "🏁 Fejezd be a beállítást — keress 6%-ot a befizetéseiddel", "remove-owner.confirmation.subtitle": "Biztonsági okokból a beállítások módosítása 3 percet vesz igénybe, ezalatt a kártyádat ideiglenesen zároljuk, és a fizetés nem lesz lehetséges.", "remove-owner.confirmation.title": "Kártyád 3 percre zárolva lesz frissítéskor", "restore-smart-wallet.wallet-recovered": "Pénztárca helyreállítva", "rewardClaimCelebration.claimedTitle": "Ju<PERSON><PERSON><PERSON> m<PERSON>", "rewardClaimCelebration.subtitle": "Barátok <PERSON>ghív<PERSON>", "rewardClaimCelebration.title": "<PERSON><PERSON><PERSON>", "rewards-warning.subtitle": "A fiók eltávolítása szünetelteti a hozzáférést a kapcsolódó jutalmakhoz. A fiókot bármikor visszaállíthatod, hogy igényeld őket.", "rewards-warning.title": "Elveszíted a hozzáférést a jutalmaidhoz", "rewards.copiedInviteLink": "Meghívó link másolva", "rewards.createAccount": "Meghívó link másolása", "rewards.header.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> {aReward} neked és {bReward} a barátodnak, ha elkölt {bSpendLimitReward}.", "rewards.header.title": "Szerezz {amountA}{br}Adj {amountB}", "rewards.sendInvite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rewards.sendInviteTip": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>, <PERSON><PERSON> ad<PERSON> {bAmount}", "route.fees": "<PERSON><PERSON><PERSON> {fees}", "routesNotFound.description": "A váltási útvonal ehhez: {from}-{to} h<PERSON><PERSON><PERSON>zati kombinációhoz nem érhető el.", "routesNotFound.title": "<PERSON><PERSON>s elérhető váltási útvonal", "rpc.OrderBuySignMessage.subtitle": "A Swaps.IO használatával", "rpc.OrderCardTopupSignMessage.subtitle": "A Swaps.IO használatával", "rpc.addCustomNetwork.addNetwork": "Hozzáadás", "rpc.addCustomNetwork.chainId": "Láncazonosító", "rpc.addCustomNetwork.nativeToken": "Natív token", "rpc.addCustomNetwork.networkName": "Hálózat neve", "rpc.addCustomNetwork.operationDescription": "Ez a webhely hálózatot adhat a tárcádhoz. A Zeal nem tudja ellenőrizni az egyéni hálózatok biztonságát, e<PERSON><PERSON><PERSON> győződj meg róla, hogy tisztában vagy a kockázatokkal.", "rpc.addCustomNetwork.rpcUrl": "RPC URL", "rpc.addCustomNetwork.subtitle": "Hasz<PERSON>lat<PERSON>: {name}", "rpc.addCustomNetwork.title": "Hálózat ho<PERSON>ad<PERSON>a", "rpc.send_token.network_not_supported.subtitle": "Dolgozunk a tranzakciók engedélyezésén ezen a hálózaton. Köszönjük a türelmedet 🙏", "rpc.send_token.network_not_supported.title": "Hálózat hamarosan elérhető", "rpc.send_token.send_or_receive.settings": "Beállítások", "rpc.sign.accept": "Elfogadás", "rpc.sign.cannot_parse_message.body": "Nem tudtuk dekódolni ezt az üzenetet. Csak akkor fogadd el a kérést, ha megbízol ebben az alkalmazásban.{br}{br}Az üzenetekkel bejelentkezhetsz egy alkalmazásba, de az alkalmazások a tokenjeid feletti irányítást is megszerezhetik velük.", "rpc.sign.cannot_parse_message.header": "Körültekintően járj el", "rpc.sign.import_private_key": "Kulcsok importálása", "rpc.sign.subtitle": "Alkalmazás: {name}", "rpc.sign.title": "Aláírás", "safe-creation.success.title": "Tárca létrehozva", "safe-safety-checks-popup.title": "Tranzakció biztonsági ellenőrz<PERSON>ei", "safetyChecksPopup.title": "Oldal biztonsági ellenőrz<PERSON>ei", "scan_qr_code.description": "<PERSON><PERSON><PERSON><PERSON> be tárca QR-kódot vagy csatlakozz egy apphoz", "scan_qr_code.show_qr_code": "Saját QR-kódom", "scan_qr_code.tryAgain": "Újra", "scan_qr_code.unlockCamera": "Kamera engedélyez<PERSON>e", "screen-lock-missing.modal.close": "Bezárás", "screen-lock-missing.modal.subtitle": "A Passkey-k használatához az eszközödnek képernyőzárral kell rendelkeznie. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ll<PERSON><PERSON> be egy képernyőzárat, és próbáld <PERSON>.", "screen-lock-missing.modal.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seedConfirmation.banner.subtitle": "<PERSON><PERSON><PERSON> a titkos kifejezésed, a<PERSON><PERSON> a p<PERSON>zed. Csak a csalók kérik el.", "seedConfirmation.title": "SOHA NE OSZD MEG a titkos kifejezésedet senkivel", "select-active-owner.subtitle": "<PERSON><PERSON><PERSON> tá<PERSON> is a kártyádhoz van kapcsolva. Válassz egyet a Zealhez való csatlakozáshoz. Bármikor válthatsz.", "select-active-owner.title": "Tárca kiválasztása", "select-card.title": "Kártya kiválasztása", "select-crypto-currency-title": "Token kiválasztása", "select-token.title": "Token kiválasztása", "selectEarnAccount.chf.description.steps": "· Pénzfelvétel a nap 24 ó<PERSON><PERSON><PERSON><PERSON><PERSON>, lekötés nélkül {br}· A kamat másod<PERSON>cenként növekszik {br}· Túlbiztosított betétek itt: <link>Frankencoin</link>", "selectEarnAccount.chf.title": "{apy} <PERSON><PERSON> ho<PERSON>-<PERSON>", "selectEarnAccount.eur.description.steps": "· Pénzkivétel a nap 24 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> n<PERSON> {br}· Kamatozás másodpercenként {br}· Túlbiztosított hitelek a(z) <link>Aave</link>", "selectEarnAccount.eur.title": "{apy} évente EUR-ban", "selectEarnAccount.subtitle": "Bármikor megváltoztathatod", "selectEarnAccount.title": "Pénznem kiválasztása", "selectEarnAccount.usd.description.steps": "· Pénzkivétel a nap 24 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> n<PERSON> {br}· Kamatozás másodpercenként {br}· Túlbiztosított betétek a(z) <link>Sky</link>", "selectEarnAccount.usd.title": "{apy} évente USD-ben", "selectEarnAccount.zero.description_general": "Digitális készpénz tartása kamatozás nélkül", "selectEarnAccount.zero.title": "0% évente", "selectRechargeThreshold.button.enterAmount": "Összeg megadása", "selectRechargeThreshold.button.setTo": "Beállítás: {amount}", "selectRechargeThreshold.description.line1": "Amikor a kártyád egyenlege ez alá csökken: {amount}, automatikusan feltöltődik erre az összegre: {amount} a Kamatozó fiókodból.", "selectRechargeThreshold.description.line2": "<PERSON>z alacsonyabb célértékkel több marad a Kamatozó fiókodban (3%-ot kamatozva). Ezt bármikor megváltoztathatod.", "selectRechargeThreshold.title": "Kártya célegyenlegének beállítása", "select_currency_to_withdraw.select_token_to_withdraw": "Válaszd ki a kivenni kívánt tokent", "send-card-token.form.send": "<PERSON><PERSON><PERSON><PERSON>", "send-card-token.form.send-amount": "Feltöltési összeg", "send-card-token.form.title": "Pénz hozzáadása a kártyához", "send-card-token.form.to-address": "<PERSON><PERSON><PERSON><PERSON>", "send-safe-transaction.network-fee-widget.error": "<PERSON><PERSON><PERSON><PERSON><PERSON>ged van {amount} összegre, vagy v<PERSON><PERSON> másik tokent", "send-safe-transaction.network-fee-widget.no-fee": "<PERSON><PERSON><PERSON><PERSON>", "send-safe-transaction.network-fee-widget.title": "<PERSON><PERSON><PERSON>", "send-safe-transaction.network_fee_widget.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> díj", "send.banner.fees": "Szükséged van {amount} tov<PERSON>bbi {currency} -ra a díjak<PERSON>z", "send.banner.toAddressNotSupportedNetwork.subtitle": "A címzett tárcája nem támogatja a következőt: {network}. Válts egy támogatott tokenre.", "send.banner.toAddressNotSupportedNetwork.title": "Hálózat nem támo<PERSON>ott a címzettnél", "send.banner.walletNotSupportedNetwork.subtitle": "<PERSON>z okostárcák nem tudnak tranzakciókat végrehajtani ezen: {network}. Válts egy támogatott tokenre.", "send.banner.walletNotSupportedNetwork.title": "A token hálózata nem tá<PERSON>ott", "send.empty-portfolio.empty-state": "<PERSON><PERSON>", "send.empty-portfolio.header": "Tokenek", "send.titile": "<PERSON><PERSON><PERSON><PERSON>", "sendLimit.success.subtitle": "A limited 3 perc múlva frissül.", "sendLimit.success.title": "A változás 3 percet vesz igénybe", "send_crypto.form.disconnected.cta.addFunds": "<PERSON><PERSON>z <PERSON>", "send_crypto.form.disconnected.cta.connectToSelectedNetwork": "Váltás erre: {network}", "send_crypto.form.disconnected.label": "Átutalandó <PERSON>", "send_to.qr_code.description": "QR-kód beol<PERSON>a tárcába küldéshez", "send_to.qr_code.title": "QR-k<PERSON><PERSON>", "send_to_card.header": "Küldés a kártyacímre", "send_to_card.select_sender.add_wallet": "Tárca hozzáadása", "send_to_card.select_sender.header": "Küldő kiválasztása", "send_to_card.select_sender.search.default_placeholder": "Keresés cím vagy <PERSON> alapján", "send_to_card.select_sender.show_card_address_button_description": "K<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "send_token.form.select-address": "Cím k<PERSON>lasztása", "send_token.form.send-amount": "Küldendő összeg", "send_token.form.title": "<PERSON><PERSON><PERSON><PERSON>", "setLimit.amount.error.zero_amount": "<PERSON><PERSON> tudni fizetéseket indítani", "setLimit.error.max_limit_reached": "Max. limit be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {amount}", "setLimit.error.same_as_current_limit": "Megegyezik a jelenlegivel", "setLimit.placeholder": "<PERSON><PERSON><PERSON><PERSON>: {amount}", "setLimit.submit": "<PERSON><PERSON>", "setLimit.submit.error.amount_required": "Add meg az összeget", "setLimit.subtitle": "Ennyit költhetsz naponta a kártyáddal.", "setLimit.title": "Napi költési limit beállítása", "settings.accounts": "Fiókok", "settings.accountsSeeAll": "Összes", "settings.addAccount": "Pénztárca hozzáadása", "settings.card": "Kártyabeállítások", "settings.connections": "Alkalmazás-kapcsolatok", "settings.currency": "Alapértelmezett pénznem", "settings.default_currency_selector.title": "Pénznem", "settings.discord": "Discord", "settings.experimentalMode": "<PERSON><PERSON><PERSON><PERSON><PERSON> mód", "settings.experimentalMode.subtitle": "Új funkciók tesztelése", "settings.language": "Nyelv", "settings.lockZeal": "Zeal z<PERSON><PERSON>", "settings.notifications": "Értesítések", "settings.open_expanded_view": "Bővített nézet megnyitása", "settings.privacyPolicy": "Adatvédelmi <PERSON>", "settings.settings": "Beállítások", "settings.termsOfUse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.twitter": "𝕏 / Twitter", "settings.version": "Ver<PERSON><PERSON> {version} körny.: {env}", "setup-card.confirmation": "Virtuális kártya igénylése", "setup-card.confirmation.subtitle": "Fizess online és add hozzá a {type} tárcádhoz az érintésmentes fizetéshez.", "setup-card.getCard": "<PERSON><PERSON><PERSON><PERSON>", "setup-card.order.physicalCard": "Fizikai kártya", "setup-card.order.physicalCard.steps": "· Fizikai VISA Gnosis Pay {br}· A kézbesítés akár 3 hét is lehet {br}· Személyes és ATM-es fizetéshez. {br}· Apple/Google tárcához adható (csak támogatott országokban", "setup-card.order.subtitle1": "<PERSON><PERSON><PERSON> is használhatsz egyszerre", "setup-card.order.title": "<PERSON><PERSON><PERSON> t<PERSON> k<PERSON>é<PERSON>?", "setup-card.order.virtualCard": "Virt<PERSON><PERSON><PERSON>", "setup-card.order.virtual_card.steps": "· Digitális VISA Gnosis Pay {br}· Azonnal használható online fizetésre {br}· Apple/Google tárcához adható (csak támogatott országokban)", "setup-card.orderCard": "<PERSON><PERSON><PERSON><PERSON>", "setup-card.virtual-card": "Virtuális k<PERSON> k<PERSON>", "setup.notifs.fakeAndroid.title": "Értesítések a fizetésekről és a bejövő átutalásokról", "setup.notifs.fakeIos.subtitle": "A Zeal é<PERSON><PERSON>, ha p<PERSON>t ka<PERSON>, vagy a Visa kártyáddal fizetsz. Ezt később megváltoztathatod.", "setup.notifs.fakeIos.title": "Értesítések a fizetésekről és a bejövő átutalásokról", "sign.PermitAllowanceItem.spendLimit": "Költési limit", "sign.ledger.subtitle": "Elküldtük a tranzakciós kérelmet a hardveres pénztárcádra. Kérjük, ott foly<PERSON>d.", "sign.ledger.title": "Megerősí<PERSON>s hardvere<PERSON> pénztárc<PERSON>", "sign.passkey.subtitle": "A böngésződnek fel kell a<PERSON>, hogy írj al<PERSON> a tárcához tartozó <PERSON>key-jel<PERSON><PERSON><PERSON>, ott foly<PERSON>.", "sign.passkey.title": "Passkey kiválasztása", "signal_aborted_for_uknown_reason.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>zakítva", "simulatedTransaction.BridgeTrx.info.title": "<PERSON><PERSON><PERSON>", "simulatedTransaction.CardTopUp.info.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.CardTopUpTrx.info.title": "Pénz hozzáadása a kártyához", "simulatedTransaction.NftCollectionApproval.approve": "NFT-kollekció jóváhagyása", "simulatedTransaction.OrderBuySignMessage.title": "Vásárlás", "simulatedTransaction.OrderCardTopupSignMessage.title": "Hozzáadás kártyához", "simulatedTransaction.OrderEarnDepositBridge.title": "Befizetés az Earn-be", "simulatedTransaction.P2PTransaction.info.title": "<PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.PermitSignMessage.title": "Engedélyezés", "simulatedTransaction.SingleNftApproval.approve": "NFT jóváhagyása", "simulatedTransaction.UnknownSignMessage.title": "Aláírás", "simulatedTransaction.Withdrawal.info.title": "Kivétel", "simulatedTransaction.approval.title": "Jóváhagyás", "simulatedTransaction.approve.info.title": "Jóváhagyás", "simulatedTransaction.p2p.info.account": "Címzett", "simulatedTransaction.p2p.info.unlabelledAccount": "Címkézetlen pénztárca", "simulatedTransaction.unknown.info.receive": "<PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.info.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulatedTransaction.unknown.using": "Alkalmazás: {app}", "simulation.approval.modal.text": "A jóváhagyással engedélyezed egy alkalmazásnak vagy okosszerződésnek, hogy tokenjeidet vagy NFT-idet használja a jövőbeni tranzakciókhoz.", "simulation.approval.modal.title": "Mik azok a jóváhagyások?", "simulation.approval.spend-limit.label": "Költési limit", "simulation.approve.footer.for": "Címzett:", "simulation.approve.unlimited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulationNotAvailable.title": "Ismeretlen művelet", "smart-wallet-activation-view.on": "Hálózat:", "smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet": "Az 1Password blokkolhatja a hozzáférést a tárcádhoz", "smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal": "<PERSON><PERSON><PERSON>ld az Apple-t vagy a Google-t a Zeal biztonságos beállításához", "smart-wallet.passkey-notice.title": "Kerüld az 1Password használatát", "spend-limits.high.modal.text": "Állíts be egy olyan költ<PERSON> limitet, amely közel áll a tokenek ténylegesen felhasznált mennyiségéhez egy alkalmazással vagy okosszerződéssel. A magas limitek kockázatosak, és megkönnyíthetik a csalók számára a tokenjeid ellopását.", "spend-limits.high.modal.text_sign_message": "A költési limitnek közel kell lennie a tokenek azon mennyiségéhez, amelyet ténylegesen használni fogsz egy alkalmazással vagy okosszerződéssel. A magas limitek kockázatosak, és megkönnyíthetik a csalók számára a tokenjeid ellopását.", "spend-limits.high.modal.title": "Magas költési limit", "spend-limits.modal.text": "A költési limit azt jelenti, hogy egy alkalmazás mennyi tokent használhat fel a nevedben. Ezt a limitet bármikor megváltoztathatod vagy eltávolíthatod. A biztonság érdekében a költési limiteket tartsd közel a ténylegesen felhasznált tokenek mennyiségéhez.", "spend-limits.modal.title": "Mi az a költési limit?", "spent-limit-info.modal.description": "A költési limit azt jelenti, hogy egy alkalmazás mennyi tokent használhat fel a nevedben. Ezt a limitet bármikor megváltoztathatod vagy eltávolíthatod. A biztonság érdekében a költési limittartsd közel a tokenek azon mennyiségéhez, amelyet ténylegesen használni fogsz egy alkalmazással.", "spent-limit-info.modal.title": "Mi az a költési limit?", "sswaps-io.transfer-provider": "Átutalási szolgáltató", "storage.accountDetails.activateWallet": "Tárca aktiválása", "storage.accountDetails.changeWalletLabel": "Tárca címkéjének módosítása", "storage.accountDetails.deleteWallet": "Tárca eltávolítása", "storage.accountDetails.setup_recovery_kit": "<PERSON><PERSON><PERSON>állí<PERSON><PERSON><PERSON>", "storage.accountDetails.showPrivateKey": "Privát kulcs megjelenítése", "storage.accountDetails.showWalletAddress": "Tárcacím me<PERSON>", "storage.accountDetails.smartBackup": "Mentés és helyreállítás", "storage.accountDetails.viewSsecretPhrase": "Titkos kifejezés megtekintése", "storage.accountDetails.zealSmartWallets": "Zeal Smart <PERSON>?", "storage.manageAccounts.title": "<PERSON><PERSON><PERSON><PERSON>", "submit-userop.progress.text": "<PERSON><PERSON><PERSON><PERSON>", "submit.error.amount_high": "Túl magas összeg", "submit.error.amount_hight": "Az összeg túl magas", "submit.error.amount_low": "<PERSON><PERSON>", "submit.error.amount_required": "Add meg az összeget", "submit.error.maximum_number_of_characters_exceeded": "Csökkentsd az üzenet hosszát", "submit.error.not_enough_balance": "<PERSON><PERSON><PERSON> el<PERSON> e<PERSON>", "submit.error.recipient_required": "Kedvezményezett megadása kötelező", "submit.error.routes_not_found": "<PERSON><PERSON><PERSON> elér<PERSON><PERSON> útvonal", "submitSafeTransaction.monitor.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitSafeTransaction.sign.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitSafeTransaction.state.sending": "<PERSON><PERSON><PERSON><PERSON>", "submitSafeTransaction.state.sign": "Létrehozás", "submitSafeTransaction.submittingToRelayer.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.cancel": "<PERSON><PERSON><PERSON><PERSON>", "submitTransaction.cancel.attemptingToStop": "Leállítási kísérlet", "submitTransaction.cancel.failedToStop": "<PERSON><PERSON><PERSON><PERSON>ll<PERSON>", "submitTransaction.cancel.stopped": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.cancel.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> előnéze<PERSON>", "submitTransaction.failed.banner.description": "A hálózat törölte a tranzakciót. Próbáld újra vagy keress minket.", "submitTransaction.failed.banner.title": "Sikertelen tranzakció", "submitTransaction.failed.execution_reverted.title": "Hiba történt az alkalmazásban", "submitTransaction.failed.execution_reverted_without_message.title": "Hiba történt az alkalmazásban", "submitTransaction.failed.out_of_gas.description": "A hálózat törölte a díj túllépése miatt.", "submitTransaction.failed.out_of_gas.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hiba", "submitTransaction.sign.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.speedUp": "Gyorsítás", "submitTransaction.state.addedToQueue": "Hozzáadva a várólistához", "submitTransaction.state.addedToQueue.short": "Várólist<PERSON>", "submitTransaction.state.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.state.complete": "{currencyCode} hozzáadva a Zealhoz", "submitTransaction.state.complete.subtitle": "Ellenőrizd a Zeal-portfóliódat", "submitTransaction.state.completed": "<PERSON><PERSON><PERSON>", "submitTransaction.state.failed": "Sikertelen", "submitTransaction.state.includedInBlock": "Blokkba foglalva", "submitTransaction.state.includedInBlock.short": "Blokkban", "submitTransaction.state.replaced": "<PERSON><PERSON>ettes<PERSON><PERSON>", "submitTransaction.state.sendingToNetwork": "Küldés a hálózatra", "submitTransaction.stop": "Le<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitTransaction.submit": "<PERSON><PERSON><PERSON><PERSON>", "submitted-user-operation.state.bundled": "Várólist<PERSON>", "submitted-user-operation.state.completed": "Befejezve", "submitted-user-operation.state.failed": "Sikertelen", "submitted-user-operation.state.pending": "Továbbítás", "submitted-user-operation.state.rejected": "Elutasítva", "submittedTransaction.failed.title": "Sikertelen tranzakció", "success_splash.card_activated": "<PERSON><PERSON><PERSON><PERSON> aktiválva", "supportFork.give-feedback.title": "Visszajelzés küldése", "supportFork.itercom.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, juta<PERSON>k esetén.", "supportFork.itercom.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supportFork.title": "Segítség a következőkhöz:", "supportFork.zendesk.subtitle": "Kártyás fizetés, azonosítás? Gnosis Pay", "supportFork.zendesk.title": "Kártyás fizetés és személyazonosság", "supported-networks.ethereum.warning": "Magas d<PERSON>jak", "supportedNetworks.networks": "Támogatott hálózatok", "supportedNetworks.oneAddressForAllNetworks": "<PERSON>gy cím minden h<PERSON>", "supportedNetworks.receiveAnyAssets": "Fogadj eszközöket a támogatott hálózatokról ugyanarra a Zeal-címre", "swap.form.error.no_routes_found": "<PERSON><PERSON><PERSON>", "swap.form.error.not_enough_balance": "<PERSON><PERSON><PERSON> el<PERSON> e<PERSON>", "swaps-io-details.bank.serviceProvider": "S<PERSON>lgáltató", "swaps-io-details.details.processing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swaps-io-details.pending": "Függőben", "swaps-io-details.rate": "Árfolyam", "swaps-io-details.serviceProvider": "S<PERSON>lgáltató", "swaps-io-details.transaction.from.processing": "<PERSON>nd<PERSON><PERSON><PERSON>", "swaps-io-details.transaction.networkFees": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swaps-io-details.transaction.state.completed-transaction": "Befejezett t<PERSON>ó", "swaps-io-details.transaction.state.started-transaction": "<PERSON>nd<PERSON><PERSON><PERSON>", "swaps-io-details.transaction.to.processing": "Befejezett t<PERSON>ó", "swapsIO.monitoring.AwaitingLiqSend.subtitle": "A befizetés hamarosan befejeződik. A Kinetex még feldolgozza a tranzakciódat.", "swapsIO.monitoring.awaitingLiqSend.title": "<PERSON><PERSON><PERSON>", "swapsIO.monitoring.awaitingRecive.title": "Továbbítás", "swapsIO.monitoring.awaitingSend.title": "Várakozás", "swapsIO.monitoring.cancelledAwaitingSlash.subtitle": "A tokeneket elküldtük a Kinetexnek, de hamarosan visszakapod őket. A Kinetex nem tudta végrehajtani a cél tranzakciót.", "swapsIO.monitoring.cancelledAwaitingSlash.title": "Tokenek visszaküldése", "swapsIO.monitoring.cancelledNoSlash.subtitle": "A tokenek <PERSON> ismeretlen hiba miatt nem történt meg. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "swapsIO.monitoring.cancelledNoSlash.title": "Tokenek visszaküldve", "swapsIO.monitoring.cancelledSlashed.subtitle": "A tokeneket visszaküldtük. A Kinetex nem tudta végrehajtani a cél tranzakciót.", "swapsIO.monitoring.cancelledSlashed.title": "Tokenek visszaküldve", "swapsIO.monitoring.completed.title": "Befejezve", "taker-metadata.earn": "Szerezz hozamot digitális USD-ben a <PERSON>-jal", "taker-metadata.earn.aave": "Szerezz hozamot digitális EUR-ban az Aave-val", "taker-metadata.earn.aave.cashout24": "Azonnali pénzfelvétel, 24/7", "taker-metadata.earn.aave.trusted": "27 milli<PERSON>rd <PERSON><PERSON><PERSON>, 2+ <PERSON><PERSON>", "taker-metadata.earn.aave.yield": "A hozam másodpercenként íródik jóvá", "taker-metadata.earn.chf": "Hozamszerzés digitális CHF-ben", "taker-metadata.earn.chf.cashout24": "Azonnali kifizetés a nap 24 órájában", "taker-metadata.earn.chf.trusted": "28 millió CHF értékben bíznak benne", "taker-metadata.earn.chf.yield": "A hozam másodpercenként növekszik", "taker-metadata.earn.usd.cashout24": "Azonnali pénzfelvétel, 24/7", "taker-metadata.earn.usd.trusted": "10,7 milli<PERSON>rd <PERSON>, 5+ <PERSON><PERSON>", "taker-metadata.earn.usd.yield": "A hozam másodpercenként íródik jóvá", "test": "Be<PERSON>ze<PERSON>s", "to.titile": "<PERSON><PERSON><PERSON>", "token.groupHeader.cashback": "Pénzvisszatérítés", "token.groupHeader.title": "Eszközök", "token.groupHeader.titleWithSum": "Eszközök {sum}", "token.hidden_tokens.page.title": "<PERSON><PERSON><PERSON><PERSON>", "token.list_item.network_ticker": "{ticker} · {network}", "token.widget.addTokens": "Token hozz<PERSON>a", "token.widget.cashback_empty": "<PERSON><PERSON>g ninc<PERSON> t<PERSON>zakciók", "token.widget.emptyState": "<PERSON><PERSON>senek tokenek a pénztárcában", "tokens.cash": "Készpénz", "top-up-card-from-earn-view.approve.for": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.approve.into": "<PERSON><PERSON>", "top-up-card-from-earn-view.swap.from": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.swap.to": "<PERSON><PERSON><PERSON>", "top-up-card-from-earn-view.withdraw.to": "<PERSON><PERSON>", "top-up-card-from-earn.trx.title.approval": "Csere jóváhagyása", "top-up-card-from-earn.trx.title.swap": "Hozzáadás a kártyához", "top-up-card-from-earn.trx.title.withdrawal": "Kifizetés az Earnből", "topUpDapp.connectWallet": "Tárca csatlakoztatása", "topup-fee-breakdown.bungee-fee": "Külső szolgáltató díja", "topup-fee-breakdown.header": "<PERSON><PERSON><PERSON><PERSON><PERSON> díj", "topup-fee-breakdown.network-fee": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> díj", "topup-fee-breakdown.total-fee": "<PERSON><PERSON><PERSON> d<PERSON>", "topup.continue-in-wallet": "Folytasd a tárcádban", "topup.send.title": "<PERSON><PERSON><PERSON><PERSON>", "topup.submit-transaction.close": "Bezárás", "topup.submit-transaction.sent-to-wallet": "<PERSON><PERSON><PERSON><PERSON> {amount}", "topup.to": "Címzett", "topup.transaction.complete.close": "Bezárás", "topup.transaction.complete.try-again": "Újra", "transaction-request.nonce-too-low.modal.button-text": "Bezárás", "transaction-request.nonce-too-low.modal.text": "Egy azonos sorszámú (nonce) tranzakció már tel<PERSON>, így ezt már nem küldheted be. Ez akkor fordulhat el<PERSON>, ha gyorsan egymás után indítasz tranzakciókat, vagy ha egy már telje<PERSON>ült tranzakciót próbálsz gyorsítani vagy leállítani.", "transaction-request.nonce-too-low.modal.title": "Azonos sorszámú tranzakció már tel<PERSON>", "transaction-request.replaced.modal.button-text": "Bezárás", "transaction-request.replaced.modal.text": "<PERSON>em tud<PERSON>k nyo<PERSON> k<PERSON> a tranzakció állapotát. Vagy egy másik tranzakció he<PERSON>í<PERSON>tte, vagy az RPC csomóponttal van probléma.", "transaction-request.replaced.modal.title": "A tranzakció állapota nem található", "transaction.activity.details.modal.close": "Bezárás", "transaction.cancel_popup.cancel": "Nem, várok", "transaction.cancel_popup.confirm": "Igen, leáll", "transaction.cancel_popup.description": "<PERSON><PERSON> h<PERSON>lózati díj kell a leállításhoz, az eredeti helyett: {oldFee}", "transaction.cancel_popup.description_without_original": "Új hálózati díj szükséges a leállításhoz.", "transaction.cancel_popup.not_supported.subtitle": "A tranzakciók leállítása nem támogatott a(z) {network}", "transaction.cancel_popup.not_supported.title": "<PERSON><PERSON>", "transaction.cancel_popup.stopping_fee": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>llítási díj", "transaction.cancel_popup.title": "Leállítod a tranzakciót?", "transaction.in-progress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transaction.inProgress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transaction.speed_up_popup.cancel": "Nem, várok", "transaction.speed_up_popup.confirm": "Felgyorsítás", "transaction.speed_up_popup.description": "A gyorsításhoz új hálózati díjat kell fizetned az eredeti díj helyett: {amount}", "transaction.speed_up_popup.description_without_original": "A gyorsításhoz új hálózati díjat kell fizetned", "transaction.speed_up_popup.seed_up_fee_title": "Hálózati gyorsítási díj", "transaction.speed_up_popup.title": "G<PERSON><PERSON><PERSON><PERSON>d a tranzakciót?", "transaction.speedup_popup.not_supported.subtitle": "A tranzakciók gyorsítása nem támogatott ezen: {network}", "transaction.speedup_popup.not_supported.title": "<PERSON><PERSON>", "transaction.subTitle.failed": "Sikertelen", "transactionDetails.cashback.not-qualified": "<PERSON><PERSON>", "transactionDetails.cashback.paid": "{amount} kifizetve", "transactionDetails.cashback.pending": "{amount} függ<PERSON>ben", "transactionDetails.cashback.title": "Pénzvisszatérítés", "transactionDetails.cashback.unknown": "Ismeretlen", "transactionDetails.cashback_estimate": "Pénzvisszatérítés be<PERSON>", "transactionDetails.category": "Kategória", "transactionDetails.exchangeRate": "Árfolyam", "transactionDetails.location": "<PERSON><PERSON><PERSON><PERSON>", "transactionDetails.payment-approved": "Fizetés jóváhagyva", "transactionDetails.payment-declined": "<PERSON><PERSON><PERSON><PERSON>", "transactionDetails.payment-reversed": "<PERSON><PERSON><PERSON><PERSON>", "transactionDetails.recharge.amountSentFromEarn.title": "<PERSON><PERSON><PERSON><PERSON>kból küldött összeg", "transactionDetails.recharge.amountSentFromEarn.value": "{amount}", "transactionDetails.recharge.amountSentToCard.title": "Feltöltve a kártyára", "transactionDetails.recharge.rate.title": "Árfolyam", "transactionDetails.recharge.transactionId.title": "Tranzakcióazonosító", "transactionDetails.refund": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionDetails.reversal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionDetails.transactionCurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionDetails.transactionId": "Tranzakcióazonosító", "transactionDetails.type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionRequestWidget.approve.subtitle": "A következőhöz: {target}", "transactionRequestWidget.p2p.subtitle": "Címzett: {target}", "transactionRequestWidget.unknown.subtitle": "A következővel: {target}", "transactionSafetyChecksPopup.title": "Tranzak<PERSON>ós bi<PERSON>tons<PERSON>gi el<PERSON>rz<PERSON>", "transactions.main.activity.title": "Tevékenység", "transactions.page.hiddenActivity.title": "Rejtett tevékenység", "transactions.page.title": "Tevékenység", "transactions.viewTRXHistory.emptyState": "<PERSON><PERSON>g ninc<PERSON> t<PERSON>zakciók", "transactions.viewTRXHistory.errorMessage": "<PERSON><PERSON> betölteni a tranzakciós előzményeidet", "transactions.viewTRXHistory.hidden.emptyState": "<PERSON>ncsenek rejtett tranzakciók", "transactions.viewTRXHistory.noTxHistoryForTestNets": "A teszthálózatoknál a tevékenység nem támogatott", "transactions.viewTRXHistory.noTxHistoryForTestNetsWithLink": "A teszthálózatoknál a tevékenység nem támogatott{br}<link>Blokkböngészőre</link>", "transfer_provider": "Átutalási szolgáltató", "transfer_setup_with_different_wallet.subtitle": "A banki átutalás másik tárcával van beállítva. Csak egy tárcát köthetsz össze.", "transfer_setup_with_different_wallet.swtich_and_continue": "Váltás és folytatás", "transfer_setup_with_different_wallet.title": "Pénztárca váltása", "tx-sent-to-wallet.button": "Bezárás", "tx-sent-to-wallet.subtitle": "Folytasd itt: {wallet}", "unblockProviderInfo.fees": "A lehető legalacsonyabb díjakat kapod: 0% havi 5 ezer <PERSON>, és 0,2% afölött.", "unblockProviderInfo.registration": "Az Unblockot az FNTT regisztrálta és engedélyezte VASP tőzsdei és letétkezelési szolgáltatások nyújtására, és bejegyzett MSB szolgáltató az amerikai Fincennél. <link>Tudj meg többet</link>", "unblockProviderInfo.selfCustody": "A kapott digitális készpénz a te felügyeleted alatt <PERSON>ll, senki más nem férhet hozzá az eszközeidhez.", "unblock_invalid_faster_payment_configuration.subtitle": "A megadott bankszámla nem támogatja az európai SEPA átutalásokat vagy az Egyesült Királyság Gyorsabb Fizetési rendszerét. <PERSON><PERSON><PERSON><PERSON>, adj meg egy másik számlát.", "unblock_invalid_faster_payment_configuration.title": "Másik számla szükséges", "unknownTransaction.primaryText": "K<PERSON>rtyás tran<PERSON>ó", "unsupportedCountry.subtitle": "A banki átutalások még nem érhetők el az országodban.", "unsupportedCountry.title": "<PERSON>em ér<PERSON>ő el itt: {country}", "update-app-popup.subtitle": "A legújabb frissítés tele van javításokkal, funkciókkal és egyéb újdonságokkal. Frissíts a legújabb verzióra, és lépj szintet a Zeal-lal.", "update-app-popup.title": "Zeal verzi<PERSON> f<PERSON>", "update-app-popup.update-now": "<PERSON><PERSON><PERSON><PERSON><PERSON> most", "user_associated_with_other_merchant.subtitle": "Ez a pénztárca nem használható banki átutalásokhoz. <PERSON><PERSON><PERSON><PERSON>, hasz<PERSON><PERSON>j egy másik pénztá<PERSON>, vagy j<PERSON> a problémát a Discord csatornánkon a támogatásért és a frissítésekért.", "user_associated_with_other_merchant.title": "A pénztárca nem használható", "user_associated_with_other_merchant.try_with_another_wallet": "<PERSON>r<PERSON><PERSON><PERSON>ld másik tárcával", "user_email_already_exists.subtitle": "<PERSON><PERSON><PERSON>d a banki átutalást egy másik tárcával. Pr<PERSON><PERSON><PERSON><PERSON>, amit k<PERSON><PERSON><PERSON><PERSON> has<PERSON>.", "user_email_already_exists.title": "Átutalások másik tárcával be<PERSON>llítva", "user_email_already_exists.try_with_another_wallet": "<PERSON>r<PERSON><PERSON><PERSON>ld másik tárcával", "validation.invalid.iban": "Érvénytelen IBAN", "validation.required": "Kötelező", "validation.required.first_name": "Keresztnév megadása kötelező", "validation.required.iban": "IBAN megadása kötelező", "validation.required.last_name": "Vezetéknév megadása kötelező", "verify-passkey.cta": "Jelszókulcs ellenőrzése", "verify-passkey.subtitle": "<PERSON><PERSON><PERSON><PERSON>, hogy a jelszókulcsod létrejött-e és megfelelően biztonságos-e.", "verify-passkey.title": "Jelszókulcs ellenőrzése", "view-cashback.cashback-next-cycle": "Pénzvisszatérítési ráta ennyi idő múlva: {time}", "view-cashback.no-cashback": "0%", "view-cashback.no-cashback.subtitle": "<PERSON>zess be pénzvisszatérítésért", "view-cashback.pending": "{money} Függ<PERSON>ben", "view-cashback.pending-rewards.not_paid": "Érkezik {days} nap múlva", "view-cashback.pending-rewards.paid": "E heti jóváírás", "view-cashback.received-rewards": "<PERSON><PERSON><PERSON>", "view-cashback.rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.total-rewards.value": "{tokenAmount} ({defaultCurrencyAmount})", "view-cashback.unconfirmed-rewards": "Megerősítetlen kifizetések", "view-cashback.upcoming": "Közelgő {money}", "virtual-card-order.configure-safe.loading-text": "Kártya létrehozása", "virtual-card-order.create-order.loading-text": "Kártya aktiválása", "virtual-card-order.create-order.success-text": "<PERSON><PERSON><PERSON><PERSON> aktiválva", "virtualCard.activateCard": "Kártya aktiválása", "walletDeleteConfirm.main_action": "Eltávolítás", "walletDeleteConfirm.subtitle": "Újra importálnod kell a portfólió megtekintéséhez vagy tranzakciókhoz", "walletDeleteConfirm.title": "Eltávolítod a tárcát?", "walletSetting.header": "Tárca beállít<PERSON>ai", "wallet_connect.connect.cancel": "<PERSON><PERSON><PERSON><PERSON>", "wallet_connect.connect.connect_button": "Csatlakozás", "wallet_connect.connect.title": "Csatlakozás", "wallet_connect.connected.title": "Csatlakoztatva", "wallet_connect_add_chain_missing.title": "A hálózat nem támo<PERSON>ott", "wallet_connect_proposal_expired.title": "A kapcsolat lejárt", "withdraw": "Kivétel", "withdraw.confirmation.close": "<PERSON><PERSON><PERSON><PERSON>", "withdraw.confirmation.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "withdrawal_request.completed": "Befejezve", "withdrawal_request.pending": "Függőben", "zeal-dapp.connect-wallet.cta.primary.connecting": "Csatlakozás...", "zeal-dapp.connect-wallet.cta.primary.disconnected": "Csatlakozás", "zeal-dapp.connect-wallet.cta.secondary": "<PERSON><PERSON><PERSON><PERSON>", "zeal-dapp.connect-wallet.title": "A folytatáshoz csatlakoztass tárcát", "zealSmartWalletInfo.gas": "Fizess hálózati díjat sokféle tokennel; használj népszerű ERC20 tokeneket a támogatott láncokon a hálózati díjak fizetésére, ne csak natív tokeneket.", "zealSmartWalletInfo.recover": "Nincsenek seed-kifejezések; állítsd helyre biometrikus passkey segítségével a jelszókezelődből, iCloudból vagy Google-fiókból.", "zealSmartWalletInfo.selfCustodial": "Teljesen privát pénztárca; A passkey-aláírások a láncon kerülnek hitelesítésre a központi függőségek minimalizálása érdekében.", "zealSmartWalletInfo.title": "A Zeal Smart Walletekről", "zeal_a_rewards_already_claimed_error.title": "A jutalom már i<PERSON>elve", "zwidget.minimizedDisconnected.label": "Zeal lecs<PERSON>"}