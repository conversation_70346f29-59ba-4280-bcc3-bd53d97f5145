import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'

import {
    ParsedLog,
    SafeModuleTransactionLog,
    UserOperationEventLog,
} from '@zeal/domains/RPCRequest'

export const getUserOperationRelatedLogs = ({
    logs,
    userOperationHash,
}: {
    userOperationHash: Hexadecimal.Hexadecimal
    logs: ParsedLog[]
}): {
    userOpLog: UserOperationEventLog
    safeModuleTransactionLog: SafeModuleTransactionLog
    userOpRelatedLogs: ParsedLog[]
} => {
    const { userOpEvent: userOpLog, userOpRelatedLogs } = logs
        .toSorted((a, b) => a.logIndex - b.logIndex)
        .reduceRight(
            (acc, log) => {
                switch (log.type) {
                    case 'erc20_transfer':
                    case 'safe_module_transaction':
                    case 'safe_module_transaction_for_native_fee_payment':
                    case 'account_deployed':
                    case 'added_owner':
                    case 'approval':
                    case 'disable_module':
                    case 'enable_module':
                    case 'safe_received':
                    case 'set_allowance':
                    case 'threshold_updated':
                    case 'unknown':
                    case 'user_operation_revert_reason':
                        break
                    case 'user_operation_event':
                        acc.userOpEvent = log
                        break
                    /* istanbul ignore next */
                    default:
                        notReachable(log)
                }

                if (
                    acc.userOpEvent &&
                    acc.userOpEvent.userOpHash === userOperationHash
                ) {
                    acc.userOpRelatedLogs.unshift(log)
                }

                return acc
            },
            { userOpRelatedLogs: [], userOpEvent: null } as {
                userOpRelatedLogs: ParsedLog[]
                userOpEvent: UserOperationEventLog | null
            }
        )

    if (userOpRelatedLogs.length === 0 || userOpLog === null) {
        throw new ImperativeError(
            'Were not able to find a user operation logs in receipt',
            {
                userOperationHash,
                userOpRelatedLogs,
                userOpEvent: userOpLog,
            }
        )
    }

    const safeModuleTransactionLog = userOpRelatedLogs.find(
        (log): log is SafeModuleTransactionLog => {
            switch (log.type) {
                case 'safe_module_transaction':
                    return true
                case 'erc20_transfer':
                case 'safe_module_transaction_for_native_fee_payment':
                case 'account_deployed':
                case 'added_owner':
                case 'approval':
                case 'disable_module':
                case 'enable_module':
                case 'safe_received':
                case 'set_allowance':
                case 'threshold_updated':
                case 'unknown':
                case 'user_operation_event':
                case 'user_operation_revert_reason':
                    return false
                /* istanbul ignore next */
                default:
                    return notReachable(log)
            }
        }
    )

    if (!safeModuleTransactionLog) {
        throw new ImperativeError(
            'Were not able to find a safe module transaction log in receipt',
            {
                userOperationHash,
                userOpRelatedLogs,
            }
        )
    }

    return {
        safeModuleTransactionLog,
        userOpLog,
        userOpRelatedLogs,
    }
}
