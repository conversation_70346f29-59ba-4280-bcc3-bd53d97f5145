import {
    boolean,
    failure,
    match,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'

import {
    SumSubEvent,
    SumSubStatus,
    SumSubStatusChangeEvent,
    SumSubWebSdkMsg,
} from '@zeal/domains/KYC'

const sumSubEventMap: Record<SumSubWebSdkMsg, true> = {
    'idCheck.actionCompleted': true,
    'idCheck.applicantReviewComplete': true,
    'idCheck.applicantStatus': true,
    'idCheck.livenessCompleted': true,
    'idCheck.moduleResultPresented': true,
    'idCheck.onActionSubmitted': true,
    'idCheck.onAgreementSigned': true,
    'idCheck.onApplicantActionCompleted': true,
    'idCheck.onApplicantActionLoaded': true,
    'idCheck.onApplicantActionSubmitted': true,
    'idCheck.onApplicantLoaded': true,
    'idCheck.onApplicantResubmitted': true,
    'idCheck.onApplicantStatusChanged': true,
    'idCheck.onApplicantSubmitted': true,
    'idCheck.onError': true,
    'idCheck.onInitialized': true,
    'idCheck.onLanguageChanged': true,
    'idCheck.onNavigationUiControlsStateChanged': true,
    'idCheck.onReady': true,
    'idCheck.onResize': true,
    'idCheck.onStepCompleted': true,
    'idCheck.onStepInitiated': true,
    'idCheck.onUploadError': true,
    'idCheck.onUploadWarning': true,
    'idCheck.onUserAction': true,
    'idCheck.onVideoIdentCallStarted': true,
    'idCheck.onVideoIdentCompleted': true,
    'idCheck.onVideoIdentModeratorJoined': true,
    'idCheck.restoreScrollPosition': true,
    'idCheck.stepCompleted': true,
}

const statusMap: Record<SumSubStatus, true> = {
    ActionCompleted: true,
    Approved: true,
    Failed: true,
    FinallyRejected: true,
    Incomplete: true,
    Initial: true,
    NetworkError: true,
    Pending: true,
    Ready: true,
    TemporarilyDeclined: true,
}

export const parseSumsubStatus = (
    input: unknown
): Result<unknown, SumSubStatus> =>
    string(input).andThen((str) =>
        statusMap[str as SumSubStatus]
            ? success(str as SumSubStatus)
            : failure({ type: 'sumsub_status_not_matching' })
    )

export const parseSumsubWebSdkMessage = (
    input: unknown
): Result<unknown, SumSubWebSdkMsg> =>
    string(input).andThen((str) =>
        sumSubEventMap[str as SumSubWebSdkMsg]
            ? success(str as SumSubWebSdkMsg)
            : failure({ type: 'not_matching_event_type', eventType: str })
    )

export const parseSumsubStatusChangeEvent = (
    input: unknown
): Result<unknown, SumSubStatusChangeEvent> =>
    object(input).andThen((obj) =>
        shape({
            prevStatus: parseSumsubStatus(obj.prevStatus),
            newStatus: parseSumsubStatus(obj.newStatus),
        })
    )

export const parseSumSubEvent = (
    input: unknown
): Result<unknown, SumSubEvent> =>
    object(input).andThen((obj) =>
        oneOf(obj.eventType, [
            shape({
                eventType: match(obj.eventType, 'ApplicantLoaded' as const),
                payload: object(obj.payload).andThen((payload) =>
                    shape({
                        applicantId: string(payload.applicantId),
                    })
                ),
            }),
            shape({
                eventType: match(obj.eventType, 'StepInitiated' as const),
                payload: object(obj.payload).andThen((payload) =>
                    shape({
                        idDocSetType: string(payload.idDocSetType),
                    })
                ),
            }),
            shape({
                eventType: match(obj.eventType, 'StepCompleted' as const),
                payload: object(obj.payload).andThen((payload) =>
                    shape({
                        idDocSetType: string(payload.idDocSetType),
                        isCancelled: boolean(payload.isCancelled),
                    })
                ),
            }),
            shape({
                eventType: match(obj.eventType, 'Analytics' as const),
                payload: object(obj.payload).andThen((payload) =>
                    shape({
                        eventName: string(payload.eventName),
                        eventPayload: object(payload.eventPayload),
                    })
                ),
            }),
        ])
    )
