import { COUNTRY_TO_CURRENCY_MAP } from '@zeal/domains/Country/constants'
import { tryToGetUserTimeZoneCountries } from '@zeal/domains/Country/helpers/tryToGetUserTimeZoneCountries'
import { DefaultCurrency } from '@zeal/domains/Currency'
import {
    FIAT_CURRENCIES,
    INITIAL_DEFAULT_CURRENCY,
} from '@zeal/domains/Currency/constants'

import { OnboardingStorage, Storage } from '../index'

export const initOnbaoardingStorage = (): OnboardingStorage => {
    const countriesResult = tryToGetUserTimeZoneCountries()

    const defaultCurrency = (() => {
        const countries = countriesResult.getSuccessResult() || []
        if (countries.length > 0) {
            const countryCode = countries[0].code
            const currencyCode = COUNTRY_TO_CURRENCY_MAP[countryCode]

            return currencyCode && FIAT_CURRENCIES[currencyCode]
                ? FIAT_CURRENCIES[currencyCode]
                : INITIAL_DEFAULT_CURRENCY
        }

        return INITIAL_DEFAULT_CURRENCY
    })() as DefaultCurrency // FIXME @resetko-zeal remove cast

    return {
        accounts: {},
        portfolios: {},
        defaultCurrencyConfig: {
            defaultCurrency,
        },
        customCurrencies: {},
        currencyHiddenMap: {},
        keystoreMap: {},
        networkRPCMap: {},
    }
}

export const init = (encryptedPassword: string): Storage => {
    const onboardingStorage = initOnbaoardingStorage()
    return {
        ...onboardingStorage,

        selectedAddress: null,
        accounts: {},
        bankTransferInfo: { type: 'not_started' },
        browserTabState: { lastVisitedURL: null },
        cardConfig: { type: 'card_readonly_signer_address_is_not_selected' },
        counterparties: [],
        currencyHiddenMap: {},
        currencyPinMap: {},
        customNetworkMap: {},
        dApps: {},
        encryptedPassword,
        feePresetMap: {},
        fetchedAt: new Date(),
        gasCurrencyPresetMap: {},
        isEthereumNetworkFeeWarningSeen: false,
        isOnboardingStorySeen: false,
        keystoreMap: {},
        networkRPCMap: {},
        portfolios: {},
        submitedBridges: {},
        submittedOffRampTransactions: [],
        swapSlippagePercent: null,
        transactionRequests: {},
        notificationsConfig: {
            bankTransfers: true,
            cardPayments: true,
            wallets: {},
        },
        totalEarningsInDefaultCurrencyMap: {},

        earnHistoricalTakerUserCurrencyRateMap: {
            eth: {},
            usd: {},
            eur: {},
            chf: {},
        },
        swapsIOSwapRequestsMap: {},
        transactionActivitiesCacheMap: {},
        earnTakerMetrics: { usd: null, eur: null, chf: null },
        celebrationConfig: { cashback: null, earn: {} },
        appRating: { type: 'not_rated_yet' },
        experimentalMode: false,
    }
}
