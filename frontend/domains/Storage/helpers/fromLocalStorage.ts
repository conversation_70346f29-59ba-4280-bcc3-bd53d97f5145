import { notReachable } from '@zeal/toolkit'
import { uuid } from '@zeal/toolkit/Crypto'
import { parseDate } from '@zeal/toolkit/Date'
import { values } from '@zeal/toolkit/Object'
import {
    arrayOf,
    boolean,
    combine,
    match,
    nullable,
    nullableOf,
    number,
    object,
    oneOf,
    recordOf,
    recordStrict,
    Result,
    safeArrayOf,
    shape,
    string,
    success,
    unknown,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { parse as parseAccount } from '@zeal/domains/Account/helpers/parse'
import { parseAppBrowserURL } from '@zeal/domains/App/helpers/parseAppBrowserURL'
import { parseCardCashback } from '@zeal/domains/Card/domains/Cashback/helpers/parseCardCashback'
import { parseBReward } from '@zeal/domains/Card/domains/Reward/helpers/parseBReward'
import { parseCardTransactionFromStorage } from '@zeal/domains/Card/helpers/parseCardTransactionFromStorage'
import { parseGnosisPayAccountNotOnboardedStateFromStorage } from '@zeal/domains/Card/helpers/parseGnosisPayAccountNotOnboardedStateFromStorage'
import { parseGnosisPayOnboardedKycStatus } from '@zeal/domains/Card/helpers/parseGnosisPayOnboardedKycStatus'
import { parseCountryISOCode } from '@zeal/domains/Country/helpers/parseCountryISOCode'
import { DefaultCurrency } from '@zeal/domains/Currency'
import { INITIAL_DEFAULT_CURRENCY } from '@zeal/domains/Currency/constants'
import { parseBankTransferInfo } from '@zeal/domains/Currency/domains/BankTransfer/helpers/parseBankTransferInfo'
import { parseCounterparty } from '@zeal/domains/Currency/domains/BankTransfer/helpers/parseCounterparty'
import { parseSubmittedOfframpTransaction } from '@zeal/domains/Currency/domains/BankTransfer/helpers/parseSubmittedWithdrawalTransaction'
import { parseBridgeSubmitted } from '@zeal/domains/Currency/domains/Bridge/parsers/parseBridgeSubmitted'
import { parseSwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO/parsers/parseSwapsIOSwapRequestsMap'
import { parseCryptoCurrency } from '@zeal/domains/Currency/helpers/parse'
import { parseDefaultCurrencyConfig } from '@zeal/domains/Currency/parsers/parseDefaultCurrencyConfig'
import {
    parseChfTakerMetricsFromStorage,
    parseEureTakerMetricsFromStorage,
    parseHistoricalTakerUserCurrencyRateMapFromStorage,
    parseTakerType,
    parseUsdTakerMetricsFromStorage,
} from '@zeal/domains/Earn/parsers/parseEarn'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { parse as parseKeyStoreMap } from '@zeal/domains/KeyStore/parsers/parse'
import {
    parseCryptoMoneyFromStorage,
    parseFiatMoneyFromStorage,
    parseMoneyFromStorage,
} from '@zeal/domains/Money/helpers/parse'
import { PREDEFINED_AND_TEST_NETWORKS } from '@zeal/domains/Network/constants'
import { parseNetworkHexId } from '@zeal/domains/Network/helpers/parse'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { Storage } from '@zeal/domains/Storage'
import { parseDAppConnectionState } from '@zeal/domains/Storage/domains/DAppConnectionState'
import { parseAppRating } from '@zeal/domains/Support/domains/Feedback/parsers/parseAppRating'
import { parseSubmitted } from '@zeal/domains/TransactionRequest/parsers/parseTransactionRequest'
import { parseTransactionActivitiesCache } from '@zeal/domains/Transactions/helpers/parseTransactionActivitiesCache'

export const parseLocalStorage = (
    local: unknown,
    portfolioMap: PortfolioMap
): Result<unknown, Storage> =>
    object(local).andThen((obj) => {
        const accounts = recordOf(obj.accounts, {
            keyParser: Web3.address.parse,
            valueParser: parseAccount,
        })

        const keystoreMap = oneOf(obj.keystoreMap, [
            parseKeyStoreMap(obj.keystoreMap),
            success({}),
        ])

        const notificationsConfig = oneOf(obj, [
            object(obj.notificationsConfig).andThen((notifConfigObj) =>
                shape({
                    bankTransfers: boolean(notifConfigObj.bankTransfers),
                    cardPayments: boolean(notifConfigObj.cardPayments),
                    wallets: object(notifConfigObj.wallets).andThen(
                        (walletsMap) =>
                            recordStrict(walletsMap, {
                                keyParser: Web3.address.parse,
                                valueParser: boolean,
                            })
                    ),
                })
            ),
            shape({
                bankTransfers: success(true),
                cardPayments: success(true),
                wallets: shape({ accounts, keystoreMap }).map(
                    ({ accounts, keystoreMap }) =>
                        values(accounts).reduce(
                            (hash, account) => {
                                const address = account.address

                                const keystore = getKeyStore({
                                    address,
                                    keyStoreMap: keystoreMap,
                                })

                                switch (keystore.type) {
                                    case 'private_key_store':
                                    case 'ledger':
                                    case 'secret_phrase_key':
                                    case 'trezor':
                                    case 'safe_4337':
                                        hash[address] = true
                                        return hash

                                    case 'track_only':
                                        return hash

                                    default:
                                        return notReachable(keystore)
                                }
                            },
                            {} as Record<Web3.address.Address, boolean>
                        )
                ),
            }),
        ])

        return shape({
            accounts,
            keystoreMap,
            notificationsConfig,
            defaultCurrencyConfig: oneOf(obj, [
                parseDefaultCurrencyConfig(obj.defaultCurrencyConfig),
                success({
                    defaultCurrency:
                        INITIAL_DEFAULT_CURRENCY as DefaultCurrency,
                }),
            ]),
            currencyHiddenMap: oneOf(obj, [
                object(obj.currencyHiddenMap).andThen((currencyHiddenMap) =>
                    recordStrict(currencyHiddenMap, {
                        keyParser: string,
                        valueParser: boolean,
                    })
                ),
                success({}),
            ]),
            currencyPinMap: oneOf(obj.currencyPinMap, [
                object(obj.currencyPinMap).andThen((currencyPinMap) =>
                    recordStrict(currencyPinMap, {
                        keyParser: string,
                        valueParser: boolean,
                    })
                ),
                success({}),
            ]),
            gasCurrencyPresetMap: oneOf(obj.gasCurrencyPresetMap, [
                object(obj.gasCurrencyPresetMap).andThen(
                    (gasCurrencyPresetMap) =>
                        recordStrict(gasCurrencyPresetMap, {
                            keyParser: parseNetworkHexId,
                            valueParser: string,
                        })
                ),
                success({}),
            ]),
            isOnboardingStorySeen: oneOf(obj.isOnboardingStorySeen, [
                boolean(obj.isOnboardingStorySeen),
                success(false),
            ]),
            isEthereumNetworkFeeWarningSeen: oneOf(
                obj.isEthereumNetworkFeeWarningSeen,
                [boolean(obj.isEthereumNetworkFeeWarningSeen), success(false)]
            ),
            feePresetMap: oneOf(obj.feePresetMap, [
                object(obj.feePresetMap).andThen((obj) =>
                    recordStrict(obj, {
                        keyParser: parseNetworkHexId,
                        valueParser: (preset) =>
                            object(preset).andThen((presetObj) =>
                                oneOf(presetObj, [
                                    shape({
                                        type: match(
                                            presetObj.type,
                                            'Slow' as const
                                        ),
                                    }),
                                    shape({
                                        type: match(
                                            presetObj.type,
                                            'Normal' as const
                                        ),
                                    }),
                                    shape({
                                        type: match(
                                            presetObj.type,
                                            'Fast' as const
                                        ),
                                    }),
                                ])
                            ),
                    })
                ),
                success({}),
            ]),
            customNetworkMap: oneOf(obj.customNetworkMap, [
                object(obj.customNetworkMap).andThen((obj) =>
                    recordStrict(obj, {
                        keyParser: string,
                        valueParser: (customNetwork) =>
                            object(customNetwork).andThen((customNetwork) =>
                                shape({
                                    type: success('custom' as const),
                                    name: string(customNetwork.name),
                                    nativeCurrency: object(
                                        customNetwork.nativeCurrency
                                    ).andThen(parseCryptoCurrency),
                                    hexChainId: parseNetworkHexId(
                                        customNetwork.hexChainId
                                    ),
                                    blockExplorerUrl: nullableOf(
                                        customNetwork.blockExplorerUrl,
                                        string
                                    ),
                                    defaultRpcUrl: oneOf(customNetwork, [
                                        string(customNetwork.defaultRpcUrl),
                                        string(customNetwork.rpcUrl),
                                    ]),
                                    rpcUrl: string(customNetwork.rpcUrl),
                                    isSimulationSupported: success(
                                        false as const
                                    ),
                                    smartWalletSupport: success({
                                        type: 'not_supported' as const,
                                    }),
                                    trxType: oneOf(customNetwork.trxType, [
                                        match(
                                            customNetwork.trxType,
                                            'legacy' as const
                                        ),
                                        match(
                                            customNetwork.trxType,
                                            'eip1559' as const
                                        ),
                                    ]),
                                })
                            ),
                    })
                ),
                success({}),
            ]),
            networkRPCMap: oneOf(obj.networkRPCMap, [
                object(obj.networkRPCMap).andThen((obj) =>
                    recordStrict(obj, {
                        keyParser: string,
                        valueParser: (networkRPC) =>
                            object(networkRPC).andThen((networkRPC) =>
                                shape({
                                    current: object(networkRPC.current).andThen(
                                        (current) =>
                                            oneOf(current.type, [
                                                shape({
                                                    type: oneOf(current.type, [
                                                        match(
                                                            current.type,
                                                            'default' as const
                                                        ).map(
                                                            () =>
                                                                'zeal_rpc' as const
                                                        ),
                                                        match(
                                                            current.type,
                                                            'zeal_rpc' as const
                                                        ),
                                                    ]),
                                                }),
                                                shape({
                                                    type: match(
                                                        current.type,
                                                        'custom' as const
                                                    ),
                                                    url: string(current.url),
                                                }),
                                            ])
                                    ),

                                    available: safeArrayOf(
                                        networkRPC.available,
                                        string
                                    ),
                                })
                            ),
                    })
                ),
                success({}),
            ]),
            selectedAddress: nullableOf(
                obj.selectedAddress,
                Web3.address.parse
            ),
            fetchedAt: parseDate(obj.fetchedAt),
            portfolios: success(portfolioMap),
            encryptedPassword: string(obj.encryptedPassword),
            customCurrencies: oneOf(obj.customCurrencies, [
                object(obj.customCurrencies)
                    .andThen((curriencies) =>
                        recordStrict(curriencies, {
                            keyParser: string,
                            valueParser: (val) =>
                                object(val).andThen(parseCryptoCurrency),
                        })
                    )
                    .map((customCurrencies) =>
                        values(customCurrencies).reduce(
                            (hash, currency) => {
                                const network =
                                    PREDEFINED_AND_TEST_NETWORKS.find(
                                        (net) =>
                                            net.hexChainId ===
                                            currency.networkHexChainId
                                    )

                                const newCurrency = {
                                    ...currency,
                                    id: network
                                        ? [
                                              network.name,
                                              currency.address.toLocaleLowerCase(),
                                          ].join('|')
                                        : [
                                              currency.networkHexChainId,
                                              currency.address.toLocaleLowerCase(),
                                          ].join('|'),
                                }

                                hash[newCurrency.id] = newCurrency

                                return hash
                            },
                            {} as typeof customCurrencies
                        )
                    ),
                success({}),
            ]),
            dApps: oneOf(obj.dApps, [
                recordOf(obj.dApps, {
                    keyParser: string,
                    valueParser: parseDAppConnectionState,
                }),
                success({}),
            ]),
            transactionRequests: oneOf(obj.transactionRequests, [
                recordOf(obj.transactionRequests, {
                    keyParser: Web3.address.parse,
                    valueParser: (arrDto) =>
                        safeArrayOf(arrDto, parseSubmitted),
                }),
                success({}),
            ]),
            submitedBridges: oneOf(obj.submitedBridges, [
                object(obj.submitedBridges).andThen((dto) =>
                    recordStrict(dto, {
                        keyParser: Web3.address.parse,
                        valueParser: (bridges: unknown) =>
                            arrayOf(bridges, unknown).andThen((arr) =>
                                combine(
                                    arr.map((val) =>
                                        object(val).andThen((valObj) =>
                                            shape({
                                                submittedBridge:
                                                    parseBridgeSubmitted(
                                                        valObj.submittedBridge
                                                    ),
                                                dismissed: boolean(
                                                    valObj.dismissed
                                                ),
                                            })
                                        )
                                    )
                                )
                            ),
                    })
                ),
                success({}),
            ]),
            submittedOffRampTransactions: oneOf(
                obj.submittedOffRampTransactions,
                [
                    safeArrayOf(
                        obj.submittedOffRampTransactions,
                        parseSubmittedOfframpTransaction
                    ),
                    success([]),
                ]
            ),
            installationId: oneOf(obj.installationId, [
                string(obj.installationId),
                success(uuid()),
            ]),
            swapSlippagePercent: oneOf(obj.swapSlippagePercent, [
                number(obj.swapSlippagePercent),
                nullable(obj.swapSlippagePercent),
                success(null),
            ]),
            counterparties: oneOf(obj.counterparties, [
                safeArrayOf(obj.counterparties, parseCounterparty),
                success([]),
            ]),

            bankTransferInfo: parseBankTransferInfo(obj.bankTransferInfo),
            cardConfig: oneOf(obj.cardConfig, [
                object(obj.cardConfig).andThen((cardConfig) =>
                    shape({
                        type: match(
                            cardConfig.type,
                            'card_readonly_signer_address_is_selected_fully_onboarded' as const
                        ),
                        userId: nullableOf(cardConfig.userId, string),
                        isCreatedViaZeal: oneOf(cardConfig.isCreatedViaZeal, [
                            boolean(cardConfig.isCreatedViaZeal),
                            success(false),
                        ]),
                        rewards: oneOf(cardConfig.rewards, [
                            parseBReward(cardConfig.rewards),

                            success({ type: 'not_eligible' as const }),
                        ]),

                        dissmissedAddToWalletBanner: oneOf(
                            cardConfig.dissmissedAddToWalletBanner,
                            [
                                boolean(cardConfig.dissmissedAddToWalletBanner),
                                success(false),
                            ]
                        ),
                        lastDismissedKycBannerState: oneOf(
                            cardConfig.lastDismissedKycBannerState,
                            [
                                parseGnosisPayOnboardedKycStatus(
                                    cardConfig.lastDismissedKycBannerState
                                ),
                                success(null),
                            ]
                        ),
                        country: oneOf(cardConfig.country, [
                            parseCountryISOCode(cardConfig.country),
                            success(null),
                        ]),
                        currency: object(cardConfig.currency).andThen(
                            parseCryptoCurrency
                        ),
                        lastSeenSafeAddress: Web3.address.parse(
                            cardConfig.lastSeenSafeAddress
                        ),
                        readonlySignerAddress: Web3.address.parse(
                            cardConfig.readonlySignerAddress
                        ),
                        cardTransactionsCache: oneOf(
                            cardConfig.cardTransactionsCache,
                            [
                                safeArrayOf(
                                    cardConfig.cardTransactionsCache,
                                    parseCardTransactionFromStorage
                                ),
                                success(null),
                            ]
                        ),
                        cashback: parseCardCashback(cardConfig.cashback),
                        lastRechargeTransactionHash: nullableOf(
                            cardConfig.lastRechargeTransactionHash,
                            string
                        ),
                        selectedCardId: nullableOf(
                            cardConfig.selectedCardId,
                            string
                        ),
                    })
                ),
                object(obj.cardConfig).andThen((cardConfig) =>
                    shape({
                        type: match(
                            cardConfig.type,
                            'card_readonly_signer_address_is_selected' as const
                        ),
                        readonlySignerAddress: Web3.address.parse(
                            cardConfig.readonlySignerAddress
                        ),
                        userId: nullableOf(cardConfig.userId, string),
                        lastDismissedOnboardingBannerState: oneOf(
                            cardConfig.lastDismissedOnboardingBannerState,
                            [
                                parseGnosisPayAccountNotOnboardedStateFromStorage(
                                    cardConfig.lastDismissedOnboardingBannerState
                                ),
                                success(null),
                            ]
                        ),
                    })
                ),
                success({
                    type: 'card_readonly_signer_address_is_not_selected' as const,
                }),
            ]),
            browserTabState: oneOf(obj.browserTabState, [
                object(obj.browserTabState).andThen((obj) =>
                    shape({
                        lastVisitedURL: nullableOf(
                            obj.lastVisitedURL,
                            parseAppBrowserURL
                        ),
                    })
                ),
                success({ lastVisitedURL: null }),
            ]),
            earnHistoricalTakerUserCurrencyRateMap: oneOf(
                obj.earnTakerUserCurrencyRateMap,
                [
                    parseHistoricalTakerUserCurrencyRateMapFromStorage(
                        obj.earnTakerUserCurrencyRateMap
                    ),
                    success({
                        usd: {},
                        eur: {},
                        eth: {},
                        chf: {},
                    }),
                ]
            ),
            totalEarningsInDefaultCurrencyMap: oneOf(
                obj.totalEarningsInDefaultCurrencyMap,
                [
                    recordOf(obj.totalEarningsInDefaultCurrencyMap, {
                        keyParser: Web3.address.parse,
                        valueParser: (valueInput) =>
                            nullableOf(valueInput, (valueInput) =>
                                object(valueInput).andThen((valueObj) =>
                                    shape({
                                        amount: parseFiatMoneyFromStorage(
                                            valueObj.amount
                                        ),
                                        earningsPerMs:
                                            parseFiatMoneyFromStorage(
                                                valueObj.earningsPerMs
                                            ),
                                    })
                                )
                            ),
                    }),
                    success({}),
                ]
            ),
            earnTakerMetrics: oneOf(obj.earnTakerMetrics, [
                object(obj.earnTakerMetrics).andThen((metricsObj) =>
                    shape({
                        usd: nullableOf(
                            metricsObj.usd,
                            parseUsdTakerMetricsFromStorage
                        ),
                        eur: nullableOf(
                            metricsObj.eur,
                            parseEureTakerMetricsFromStorage
                        ),
                        chf: nullableOf(
                            metricsObj.chf,
                            parseChfTakerMetricsFromStorage
                        ),
                    })
                ),
                success({
                    usd: null,
                    eur: null,
                    chf: null,
                }),
            ]),
            celebrationConfig: oneOf(obj.celebrationConfig, [
                object(obj.celebrationConfig).andThen((obj) =>
                    shape({
                        cashback: nullableOf(obj.cashback, (valueInput) =>
                            object(valueInput).andThen((valueObject) =>
                                shape({
                                    largestRewardsBalanceCelebrated:
                                        parseCryptoMoneyFromStorage(
                                            valueObject.largestRewardsBalanceCelebrated
                                        ),
                                })
                            )
                        ),
                        earn: oneOf(obj.earn, [
                            recordOf(obj.earn, {
                                keyParser: Web3.address.parse,
                                valueParser: (valueInput) =>
                                    object(valueInput).andThen(
                                        (earnConfigObj) =>
                                            shape({
                                                highestTotalEarningCelebratedInUserCurrency:
                                                    recordOf(
                                                        earnConfigObj.highestTotalEarningCelebratedInUserCurrency,
                                                        {
                                                            keyParser:
                                                                parseTakerType,
                                                            valueParser: (
                                                                takerEarning
                                                            ) =>
                                                                nullableOf(
                                                                    takerEarning,
                                                                    parseMoneyFromStorage
                                                                ),
                                                        }
                                                    ),
                                            })
                                    ),
                            }),
                            success({}),
                        ]),
                    })
                ),
                success({ cashback: null, earn: {} }),
            ]),
            swapsIOSwapRequestsMap: oneOf(obj.swapsIOSwapRequestsMap, [
                parseSwapsIOSwapRequestsMap(obj.swapsIOSwapRequestsMap),
                success({}),
            ]),
            transactionActivitiesCacheMap: oneOf(
                obj.transactionActivitiesCacheMap,
                [
                    object(obj.transactionActivitiesCacheMap).andThen((obj) =>
                        recordOf(obj, {
                            keyParser: Web3.address.parse,
                            valueParser: parseTransactionActivitiesCache,
                        })
                    ),
                    success({}),
                ]
            ),
            appRating: oneOf(obj.appRating, [
                parseAppRating(obj.appRating),
                success({ type: 'not_rated_yet' as const }),
            ]),
            experimentalMode: oneOf(obj.experimentalMode, [
                boolean(obj.experimentalMode),
                success(false),
            ]),
        })
    })
