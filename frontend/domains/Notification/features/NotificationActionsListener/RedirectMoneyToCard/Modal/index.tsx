import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { LoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    ConfiguredEarn,
    DeployedTaker,
    EarnTakerMetrics,
} from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { RedirectToCard } from './RedirectToCard'
import { RedirectToEarn } from './RedirectToEarn'

import { Data } from '../api/fetchData'

type Props = {
    earnTakerMetrics: EarnTakerMetrics
    recievedMoney: CryptoMoney
    state: State
    loadable: LoadableData<Data, unknown>

    initialSender: Account

    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    installationId: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig

    defaultCurrencyConfig: DefaultCurrencyConfig
    experimentalMode: boolean

    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof RedirectToCard> | MsgOf<typeof RedirectToEarn>

export type State =
    | { type: 'closed' }
    | { type: 'redirect_to_card' }
    | { type: 'redirect_to_earn'; earn: ConfiguredEarn; taker: DeployedTaker }

export const Modal = ({
    earnTakerMetrics,
    recievedMoney,
    state,
    sessionPassword,
    portfolioMap,
    onMsg,
    networkRPCMap,
    networkMap,
    loadable,
    keyStoreMap,
    installationId,
    initialSender,
    gasCurrencyPresetMap,
    feePresetMap,
    defaultCurrencyConfig,
    customCurrencies,
    currencyPinMap,
    currencyHiddenMap,
    cardConfig,
    experimentalMode,
    accountsMap,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'redirect_to_card':
            return (
                <UIModal>
                    <RedirectToCard
                        experimentalMode={experimentalMode}
                        earnTakerMetrics={earnTakerMetrics}
                        initialAmountToTopup={recievedMoney}
                        sessionPassword={sessionPassword}
                        portfolioMap={portfolioMap}
                        customCurrencies={customCurrencies}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        feePresetMap={feePresetMap}
                        currencyPinMap={currencyPinMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        initialSender={initialSender}
                        installationId={installationId}
                        currencyHiddenMap={currencyHiddenMap}
                        keyStoreMap={keyStoreMap}
                        cardConfig={cardConfig}
                        loadable={loadable}
                        accountsMap={accountsMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'redirect_to_earn':
            return (
                <UIModal>
                    <RedirectToEarn
                        taker={state.taker}
                        recievedMoney={recievedMoney}
                        earn={state.earn}
                        initialSender={initialSender}
                        cardConfig={cardConfig}
                        keyStoreMap={keyStoreMap}
                        loadable={loadable}
                        onMsg={onMsg}
                        installationId={installationId}
                        currencyPinMap={currencyPinMap}
                        currencyHiddenMap={currencyHiddenMap}
                        sessionPassword={sessionPassword}
                        portfolioMap={portfolioMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        networkMap={networkMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        networkRPCMap={networkRPCMap}
                        accountsMap={accountsMap}
                    />
                </UIModal>
            )
        default:
            return notReachable(state)
    }
}
