import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { LoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { isCardTopupVanityEnabled } from '@zeal/domains/ABTest'
import { Account, AccountsMap } from '@zeal/domains/Account'
import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
// eslint-disable-next-line zeal-domains/no-feature-deep-import
import { CardAddCashV2 } from '@zeal/domains/Card/features/CardAddCash/CardAddCashV2' // FIXME :: @Nicvaniek
import { TopUpFlow } from '@zeal/domains/Card/features/TopUpFlow'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Data } from '../api/fetchData'

type Props = {
    initialAmountToTopup: CryptoMoney | null
    loadable: LoadableData<Data, unknown>

    initialSender: Account

    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    installationId: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    earnTakerMetrics: EarnTakerMetrics

    experimentalMode: boolean
    defaultCurrencyConfig: DefaultCurrencyConfig

    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof TopUpFlow>
    | MsgOf<typeof CardAddCashV2>
    | { type: 'close' }
    | { type: 'on_redirect_to_card_try_again_clicked' }

export const RedirectToCard = ({
    initialAmountToTopup,
    loadable,
    sessionPassword,
    portfolioMap,
    onMsg,
    networkRPCMap,
    networkMap,
    keyStoreMap,
    installationId,
    initialSender,
    gasCurrencyPresetMap,
    feePresetMap,
    defaultCurrencyConfig,
    customCurrencies,
    currencyPinMap,
    currencyHiddenMap,
    cardConfig,
    earnTakerMetrics,
    accountsMap,
    experimentalMode,
}: Props) => {
    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout
                    title={null}
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={
                            <ActionBar
                                left={
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => onMsg({ type: 'close' })}
                                    >
                                        {({ color }) => (
                                            <BackIcon size={24} color={color} />
                                        )}
                                    </IconButton>
                                }
                            />
                        }
                        onClose={() => onMsg({ type: 'close' })}
                    />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    onMsg({
                                        type: 'on_redirect_to_card_try_again_clicked',
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        case 'loaded':
            return isCardTopupVanityEnabled({ experimentalMode }) ? (
                <CardAddCashV2
                    initialTopUpAmount={initialAmountToTopup}
                    installationId={installationId}
                    initialSenderAddress={initialSender.address}
                    cardConfig={cardConfig}
                    accountsMap={accountsMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    keyStoreMap={keyStoreMap}
                    currencyHiddenMap={currencyHiddenMap}
                    sessionPassword={sessionPassword}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    currencyPinMap={currencyPinMap}
                    customCurrencyMap={customCurrencies}
                    portfolioMap={portfolioMap}
                    feePresetMap={feePresetMap}
                    earnTakerMetrics={earnTakerMetrics}
                    onMsg={onMsg}
                />
            ) : (
                <TopUpFlow
                    initialAmountToTopup={initialAmountToTopup}
                    portfolio={loadable.data.portfolio}
                    sender={initialSender}
                    installationId={installationId}
                    supportedTopupCurrencies={
                        loadable.data.supportedTopupCurrencies
                    }
                    currencyPinMap={currencyPinMap}
                    currencyHiddenMap={currencyHiddenMap}
                    customCurrencies={customCurrencies}
                    sessionPassword={sessionPassword}
                    portfolioMap={portfolioMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    contractsMap={loadable.data.contractsMap}
                    networkMap={networkMap}
                    cardConfig={cardConfig}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    cardBalance={loadable.data.cardBalance}
                    networkRPCMap={networkRPCMap}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    onMsg={onMsg}
                />
            )

        default:
            return notReachable(loadable)
    }
}
