import { useEffect, useState } from 'react'

import { LanguageSettings } from '@zeal/uikit/Language'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { values } from '@zeal/toolkit/Object'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { groupBySecretPhrase } from '@zeal/domains/Account/helpers/groupBySecretPhrase'
import { CardConfig } from '@zeal/domains/Card'
import { UserAReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { ConnectionMap } from '@zeal/domains/DApp/domains/ConnectionState'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Mode } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    installationId: string
    mode: Mode
    earnTakerMetrics: EarnTakerMetrics
    connections: ConnectionMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    notificationsConfig: NotificationsConfig
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    languageSettings: LanguageSettings
    cardConfig: CardConfig
    isEthereumNetworkFeeWarningSeen: boolean
    encryptedPassword: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationCampaign: string | null
    sessionPassword: string
    selectedAccount: Account
    customCurrencyMap: CustomCurrencyMap
    userAReferralConfig: UserAReferralConfig
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'account_item_clicked'
                  | 'add_wallet_clicked'
                  | 'import_card_owner_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_account_label_change_submit'
                  | 'on_default_currency_selected'
                  | 'on_language_settings_language_selected'
                  | 'on_delete_all_dapps_confirm_click'
                  | 'on_card_disconnected'
                  | 'on_disconnect_dapps_click'
                  | 'on_switch_card_new_card_selected'
                  | 'confirm_account_delete_click'
                  | 'on_rewards_warning_confirm_account_delete_click'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_new_virtual_card_created_successfully'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_recharge_configured'
                  | 'on_physical_card_activated_info_screen_closed'
                  | 'on_new_physical_card_created_successfully'
                  | 'on_card_order_redirect_to_gnosis_pay_clicked'
                  | 'hardware_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_add_private_key_click'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_notifications_config_changed'
                  | 'on_recovery_kit_setup'
                  | 'recover_safe_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'track_wallet_clicked'
                  | 'import_keys_button_clicked'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
                  | 'session_password_decrypted'
          }
      >
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'account_item_clicked'
                  | 'on_lock_zeal_click'
                  | 'on_open_fullscreen_view_click'
                  | 'track_wallet_clicked'
                  | 'close'
                  | 'on_experimental_change_clicked'
          }
      >

export const Settings = ({
    mode,
    earnTakerMetrics,
    onMsg,
    userAReferralConfig,
    connections,
    notificationsConfig,
    installationCampaign,
    encryptedPassword,
    networkRPCMap,
    networkMap,
    accountsMap,
    feePresetMap,
    currencyHiddenMap,
    gasCurrencyPresetMap,
    portfolioMap,
    sessionPassword,
    keyStoreMap,
    defaultCurrencyConfig,
    installationId,
    selectedAccount,
    cardConfig,
    isEthereumNetworkFeeWarningSeen,
    customCurrencyMap,
    languageSettings,
    experimentalMode,
}: Props) => {
    const [state, setState] = useState<ModalState>({ type: 'closed' })

    useEffect(() => {
        postUserEvent({
            type: 'SettingsEnteredEvent',
            installationId,
        })
    }, [installationId])

    return (
        <>
            <Layout
                experimentalMode={experimentalMode}
                installationId={installationId}
                keyStoreMap={keyStoreMap}
                accountsMap={accountsMap}
                currencyHiddenMap={currencyHiddenMap}
                portfolioMap={portfolioMap}
                selectedAccount={selectedAccount}
                cardConfig={cardConfig}
                defaultCurrencyConfig={defaultCurrencyConfig}
                languageSettings={languageSettings}
                mode={mode}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_select_default_currency_click':
                            setState({ type: 'default_currency_selector' })
                            break
                        case 'on_lock_zeal_click':
                        case 'account_item_clicked':
                        case 'close':
                        case 'on_experimental_change_clicked':
                            onMsg(msg)
                            break
                        case 'settings_add_new_account_click':
                            setState({ type: 'select_type_of_account_to_add' })
                            break
                        case 'on_open_fullscreen_view_click':
                            postUserEvent({
                                type: 'ExpandedViewEnteredEvent',
                                location: 'settings',
                                installationId,
                            })
                            onMsg(msg)
                            break
                        case 'on_manage_connections_click':
                            setState({ type: 'manage_connections' })
                            break

                        case 'on_notifications_settings_click':
                            setState({ type: 'notifications_settings' })
                            break

                        case 'on_card_settings_click':
                            setState({
                                type: 'card_settings',
                                cardConfig: msg.cardConfig,
                            })
                            break

                        case 'account_details_clicked':
                            setState({
                                type: 'account_details',
                                address: msg.account.address,
                            })
                            break

                        case 'on_select_account_clicked':
                            setState({ type: 'select_account' })
                            break
                        case 'on_select_language_click':
                            setState({ type: 'select_language_selector' })
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                languageSettings={languageSettings}
                userAReferralConfig={userAReferralConfig}
                earnTakerMetrics={earnTakerMetrics}
                installationCampaign={installationCampaign}
                cardConfig={cardConfig}
                customCurrencyMap={customCurrencyMap}
                selectedAccount={selectedAccount}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                defaultCurrencyConfig={defaultCurrencyConfig}
                currencyHiddenMap={currencyHiddenMap}
                accountsMap={accountsMap}
                keyStoreMap={keyStoreMap}
                notificationsConfig={notificationsConfig}
                portfolioMap={portfolioMap}
                installationId={installationId}
                sessionPassword={sessionPassword}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                encryptedPassword={encryptedPassword}
                state={state}
                connections={connections}
                onMsg={async (msg) => {
                    switch (msg.type) {
                        case 'close':
                            setState({ type: 'closed' })
                            break

                        case 'account_item_clicked':
                        case 'add_wallet_clicked':
                        case 'import_card_owner_clicked':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'on_account_label_change_submit':
                        case 'on_default_currency_selected':
                        case 'on_language_settings_language_selected':
                        case 'on_delete_all_dapps_confirm_click':
                        case 'on_card_disconnected':
                        case 'on_disconnect_dapps_click':
                        case 'on_switch_card_new_card_selected':
                        case 'confirm_account_delete_click':
                        case 'on_rewards_warning_confirm_account_delete_click':
                        case 'on_accounts_create_success_animation_finished':
                        case 'on_new_virtual_card_created_successfully':
                        case 'on_card_import_on_import_keys_clicked':
                        case 'on_card_imported_success_animation_complete':
                        case 'on_onboarded_card_imported_success_animation_complete':
                        case 'on_create_smart_wallet_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_recharge_configured':
                        case 'on_physical_card_activated_info_screen_closed':
                        case 'on_new_physical_card_created_successfully':
                        case 'on_card_order_redirect_to_gnosis_pay_clicked':
                            setState({ type: 'closed' })
                            onMsg(msg)
                            break

                        case 'create_clicked':
                            try {
                                const secretPhraseMap =
                                    await groupBySecretPhrase(
                                        values(accountsMap),
                                        keyStoreMap,
                                        sessionPassword
                                    )

                                setState({
                                    type: 'add_from_secret_phrase',
                                    secretPhraseMap,
                                })
                            } catch (e) {
                                captureError(e)
                            }
                            break
                        case 'hardware_wallet_clicked':
                        case 'on_account_create_request':
                        case 'on_add_private_key_click':
                        case 'on_address_scanned':
                        case 'on_address_scanned_and_add_label':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_notifications_config_changed':
                        case 'on_recovery_kit_setup':
                        case 'recover_safe_wallet_clicked':
                        case 'safe_wallet_clicked':
                        case 'track_wallet_clicked':
                        case 'import_keys_button_clicked':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_chf_taker_metrics_loaded':
                        case 'session_password_decrypted':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
