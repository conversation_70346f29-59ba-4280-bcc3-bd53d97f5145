import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { fetchDelayQueueState } from '@zeal/domains/Card/api/fetchDelayQueueState'
import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import { CardSettings } from '@zeal/domains/Card/features/CardSettings'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { Earn, EarnTakerMetrics } from '@zeal/domains/Earn'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    cardOwnerEarn: Earn
    earnTakerMetrics: EarnTakerMetrics
    keyStore: CardSlientSignKeyStore

    notificationsConfig: NotificationsConfig
    encryptedPassword: string
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    currencyHiddenMap: CurrencyHiddenMap
    customCurrencyMap: CustomCurrencyMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    keyStoreMap: KeyStoreMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    cardReadonlySigner: Account
    installationId: string
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    onMsg: (msg: Msg) => void
}

type Msg = Extract<
    MsgOf<typeof CardSettings>,
    {
        type:
            | 'close'
            | 'on_4337_auto_gas_token_selection_clicked'
            | 'on_4337_gas_currency_selected'
            | 'import_card_owner_clicked'
            | 'add_wallet_clicked'
            | 'on_notifications_config_changed'
            | 'on_card_disconnected'
            | 'on_switch_card_new_card_selected'
            | 'on_new_virtual_card_created_successfully'
            | 'on_predefined_fee_preset_selected'
            | 'on_recharge_configured'
            | 'import_keys_button_clicked'
            | 'on_usd_taker_metrics_loaded'
            | 'on_eur_taker_metrics_loaded'
            | 'on_chf_taker_metrics_loaded'
            | 'on_physical_card_activated_info_screen_closed'
            | 'on_new_physical_card_created_successfully'
            | 'on_card_order_redirect_to_gnosis_pay_clicked'
            | 'session_password_decrypted'
    }
>

const POLL_INTERVAL_MS = 5_000

export const Flow = ({
    onMsg,
    cardConfig,
    cardOwnerEarn,
    earnTakerMetrics,
    notificationsConfig,
    encryptedPassword,
    networkRPCMap,
    networkMap,
    accountsMap,
    feePresetMap,
    currencyHiddenMap,
    customCurrencyMap,
    gasCurrencyPresetMap,
    portfolioMap,
    sessionPassword,
    keyStoreMap,
    defaultCurrencyConfig,
    gnosisPayAccountOnboardedState,
    cardReadonlySigner,
    keyStore,
    installationId,
}: Props) => {
    const captureErrorOnce = useCaptureErrorOnce()

    const [delayQueueStatePollable, setDelayQueueStatePollable] =
        usePollableData(
            fetchDelayQueueState,
            {
                type: 'loading',
                params: {
                    cardSafeAddress:
                        gnosisPayAccountOnboardedState.cardSafe.address,
                    network: CARD_NETWORK,
                    networkRPCMap,
                },
            },
            { pollIntervalMilliseconds: POLL_INTERVAL_MS }
        )

    useEffect(() => {
        switch (delayQueueStatePollable.type) {
            case 'loaded':
            case 'reloading':
            case 'loading':
                break
            case 'subsequent_failed':
            case 'error':
                captureErrorOnce(delayQueueStatePollable.error)
                break

            /* istanbul ignore next */
            default:
                return notReachable(delayQueueStatePollable)
        }
    }, [delayQueueStatePollable, captureErrorOnce])

    return (
        <CardSettings
            cardConfig={cardConfig}
            cardOwnerEarn={cardOwnerEarn}
            earnTakerMetrics={earnTakerMetrics}
            cardBalance={gnosisPayAccountOnboardedState.balance}
            installationId={installationId}
            cardReadonlySigner={cardReadonlySigner}
            delayQueueStatePollable={delayQueueStatePollable}
            gnosisPayAccountOnboardedState={gnosisPayAccountOnboardedState}
            sessionPassword={sessionPassword}
            keyStore={keyStore}
            keyStoreMap={keyStoreMap}
            feePresetMap={feePresetMap}
            gasCurrencyPresetMap={gasCurrencyPresetMap}
            currencyHiddenMap={currencyHiddenMap}
            customCurrencyMap={customCurrencyMap}
            portfolioMap={portfolioMap}
            accountsMap={accountsMap}
            networkMap={networkMap}
            networkRPCMap={networkRPCMap}
            encryptedPassword={encryptedPassword}
            notificationsConfig={notificationsConfig}
            defaultCurrencyConfig={defaultCurrencyConfig}
            onMsg={(msg) => {
                switch (msg.type) {
                    case 'close':
                    case 'on_4337_auto_gas_token_selection_clicked':
                    case 'on_4337_gas_currency_selected':
                    case 'import_card_owner_clicked':
                    case 'add_wallet_clicked':
                    case 'on_notifications_config_changed':
                    case 'on_card_disconnected':
                    case 'on_switch_card_new_card_selected':
                    case 'on_new_virtual_card_created_successfully':
                    case 'on_predefined_fee_preset_selected':
                    case 'on_recharge_configured':
                    case 'import_keys_button_clicked':
                    case 'on_usd_taker_metrics_loaded':
                    case 'on_eur_taker_metrics_loaded':
                    case 'on_chf_taker_metrics_loaded':
                    case 'on_physical_card_activated_info_screen_closed':
                    case 'on_new_physical_card_created_successfully':
                    case 'on_card_order_redirect_to_gnosis_pay_clicked':
                    case 'session_password_decrypted':
                        onMsg(msg)
                        break

                    case 'on_delay_queue_pollable_try_again_clicked':
                    case 'on_add_card_owner_queued_successfully':
                    case 'on_remove_card_owner_queued_successfully':
                    case 'on_spend_limit_changed_successfully_close_clicked':
                        setDelayQueueStatePollable((old) => {
                            switch (old.type) {
                                case 'loading':
                                case 'error':
                                    return {
                                        type: 'loading',
                                        params: old.params,
                                    }
                                case 'loaded':
                                case 'reloading':
                                case 'subsequent_failed':
                                    return {
                                        type: 'loaded',
                                        params: old.params,
                                        data: {
                                            type: 'busy',
                                            lastTxCreatedAtMs: Date.now(),
                                        },
                                    }
                                /* istanbul ignore next */
                                default:
                                    return notReachable(old)
                            }
                        })
                        break
                    default:
                        notReachable(msg)
                }
            }}
        />
    )
}
