import { keys } from '@zeal/toolkit/Object'
import * as Web3 from '@zeal/toolkit/Web3'

import { CryptoCurrency, CurrencyId } from '@zeal/domains/Currency'
import {
    FIAT_CURRENCIES,
    GNOSIS_AAVE_EURE,
    GNOSIS_SDAI,
    GNOSIS_SVZCHF,
    GNOSIS_WSTETH,
} from '@zeal/domains/Currency/constants'
import { TakerApyMap, TakerType } from '@zeal/domains/Earn'
import { Money2 } from '@zeal/domains/Money'
import { ETHEREUM, GNOSIS } from '@zeal/domains/Network/constants'

export const SKY_URL = 'https://sky.money/'
export const SKY_COLLATERAL_URL = 'https://info.sky.money/collateral'
export const SKY_ZEAL_FAQ_URL = 'https://docs.zeal.app/faq/sky-usd'

export const FRANKENCOIN_URL = 'https://frankencoin.com/'
export const FRANKENCOIN_MONITORING_URL =
    'https://app.frankencoin.com/monitoring'

export const EARN_USD_TAKER_USER_CURRENCY = FIAT_CURRENCIES.USD
export const EARN_EUR_TAKER_USER_CURRENCY = FIAT_CURRENCIES.EUR
export const EARN_CHF_TAKER_USER_CURRENCY = FIAT_CURRENCIES.CHF
export const EARN_ETH_TAKER_USER_CURRENCY = ETHEREUM.nativeCurrency

export const DUST_AMOUNT: Record<TakerType, bigint> = {
    usd: 1_000_000_000_000_000_000n, // GNOSIS_SDAI
    eur: 1_000_000_000_000_000_000n, // GNOSIS_AAVE_EURE
    chf: 1_000_000_000_000_000_000n, // GNOSIS_svZCHF
    eth: 1_000_000_000_000_00n, // GNOSIS_WSTETH
}

export const EARN_PRIMARY_INVESTMENT_ASSETS_MAP: Record<
    TakerType,
    CryptoCurrency
> = {
    usd: GNOSIS_SDAI,
    eur: GNOSIS_AAVE_EURE,
    chf: GNOSIS_SVZCHF,
    eth: GNOSIS_WSTETH,
}

export const EARN_PRIMARY_INVESTMENT_ASSET_TO_TAKER_TYPE_MAP: Record<
    CurrencyId,
    TakerType
> = keys(EARN_PRIMARY_INVESTMENT_ASSETS_MAP).reduce(
    (acc, takerType) => ({
        ...acc,
        [EARN_PRIMARY_INVESTMENT_ASSETS_MAP[takerType].id]: takerType,
    }),
    {} as Record<CurrencyId, TakerType>
)

export const EARN_TAKER_TYPES = keys(EARN_PRIMARY_INVESTMENT_ASSETS_MAP)

export const DEFAULT_TAKER_APY_MAP: TakerApyMap = {
    usd: 8.9,
    eur: 1.9,
    eth: 2.8,
    chf: 3.0,
} as const

export const DEFAULT_ETH_TAKER_APY = 2.8

export const GNOSIS_S_DAI_VAULT_ADDRESS = Web3.address.staticFromString(
    '******************************************'
)

export const GNOSIS_SV_ZCHF_VAULT_ADDRESS = Web3.address.staticFromString(
    '******************************************'
)

export const EURE_AAVE_POOL_ADDRESS = Web3.address
    .fromString('******************************************')
    .getSuccessResultOrThrow('')

export const EARN_NETWORK = GNOSIS

export const ACCOUNT_FACTORY_ADDRESS = Web3.address.staticFromString(
    '******************************************'
)

export const COORDINATOR_FACTORY_ADDRESS = Web3.address
    .fromString('******************************************')
    .getSuccessResultOrThrow('')

export const COORDINATOR_IMPLEMENTATION_ADDRESS = Web3.address.staticFromString(
    '******************************************'
)

export const ACCOUNT_IMPLEMENTATION_ADDRESS = Web3.address.staticFromString(
    '******************************************'
)

export const COORDINATOR_FACTORY_ABI = [
    {
        inputs: [
            {
                internalType: 'address',
                name: '_owner',
                type: 'address',
            },
            {
                internalType: 'uint256',
                name: '_threshold',
                type: 'uint256',
            },
            {
                internalType: 'address',
                name: '_recipient',
                type: 'address',
            },
            {
                internalType: 'address',
                name: '_recipientToken',
                type: 'address',
            },
            {
                internalType: 'address',
                name: '_investmentToken',
                type: 'address',
            },
            {
                internalType: 'uint256',
                name: '_dustAmount',
                type: 'uint256',
            },
            {
                internalType: 'bool',
                name: '_isRebalanceAccount',
                type: 'bool',
            },
        ],
        name: 'deploy',
        outputs: [
            {
                internalType: 'address',
                name: '',
                type: 'address',
            },
            {
                internalType: 'address',
                name: '',
                type: 'address',
            },
        ],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        type: 'function',
        name: 'getContractAddress',
        inputs: [
            {
                name: '_salt', // just hash(0x000owner)
                type: 'bytes32',
                internalType: 'bytes32',
            },
        ],
        outputs: [
            {
                name: '',
                type: 'address',
                internalType: 'address',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'isContractDeployed',
        inputs: [
            {
                name: '_salt',
                type: 'bytes32',
                internalType: 'bytes32',
            },
        ],
        outputs: [
            {
                name: '',
                type: 'bool',
                internalType: 'bool',
            },
        ],
        stateMutability: 'view',
    },
] as const

export const HOLDER_ABI = [
    {
        type: 'constructor',
        inputs: [
            {
                name: '_takerFactory',
                type: 'address',
                internalType: 'address',
            },
        ],
        stateMutability: 'nonpayable',
    },
    {
        type: 'function',
        name: 'MAX_ACCOUNTS',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'uint256',
                internalType: 'uint256',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'accountCount',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'uint256',
                internalType: 'uint256',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'accounts',
        inputs: [
            {
                name: '',
                type: 'uint256',
                internalType: 'uint256',
            },
        ],
        outputs: [
            {
                name: '',
                type: 'address',
                internalType: 'address',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'addAccount',
        inputs: [
            {
                name: '_investmentToken',
                type: 'address',
                internalType: 'address',
            },
            {
                name: '_dustAmount',
                type: 'uint256',
                internalType: 'uint256',
            },
        ],
        outputs: [],
        stateMutability: 'nonpayable',
    },
    {
        type: 'function',
        name: 'amountToRebalance',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'uint256',
                internalType: 'uint256',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'getAccountInfo',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'tuple[]',
                internalType: 'struct Coordinator.AccountInfo[]',
                components: [
                    {
                        name: 'account',
                        type: 'address',
                        internalType: 'address',
                    },
                    {
                        name: 'status',
                        type: 'bool',
                        internalType: 'bool',
                    },
                    {
                        name: 'asset',
                        type: 'address',
                        internalType: 'address',
                    },
                    {
                        name: 'balance',
                        type: 'uint256',
                        internalType: 'uint256',
                    },
                ],
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'getAccounts',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'address[8]',
                internalType: 'address[8]',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'getAccountAssets',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'uint256[]',
                internalType: 'uint256[]',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'getAccountTokens',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'address[]',
                internalType: 'address[]',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'isRebalanceAccount',
        inputs: [
            {
                name: '',
                type: 'address',
                internalType: 'address',
            },
        ],
        outputs: [
            {
                name: '',
                type: 'bool',
                internalType: 'bool',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'owner',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'address',
                internalType: 'address',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'rebalanceTrigger',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'address',
                internalType: 'address',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'recipient',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'address',
                internalType: 'address',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'recipientToken',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'address',
                internalType: 'address',
            },
        ],
        stateMutability: 'view',
    },

    {
        type: 'function',
        name: 'renounceOwnership',
        inputs: [],
        outputs: [],
        stateMutability: 'nonpayable',
    },
    {
        type: 'function',
        name: 'setAccountOrder',
        inputs: [
            {
                name: '_accounts',
                type: 'address[]',
                internalType: 'address[]',
            },
        ],
        outputs: [],
        stateMutability: 'nonpayable',
    },
    {
        type: 'function',
        name: 'setRecipient',
        inputs: [
            {
                name: '_recipient',
                type: 'address',
                internalType: 'address',
            },
            {
                name: '_recipientToken',
                type: 'address',
                internalType: 'address',
            },
        ],
        outputs: [],
        stateMutability: 'nonpayable',
    },
    {
        type: 'function',
        name: 'setTakerStatus',
        inputs: [
            {
                name: '_account',
                type: 'address',
                internalType: 'address',
            },
            {
                name: '_status',
                type: 'bool',
                internalType: 'bool',
            },
        ],
        outputs: [],
        stateMutability: 'nonpayable',
    },
    {
        type: 'function',
        name: 'setThreshold',
        inputs: [
            {
                name: '_threshold',
                type: 'uint256',
                internalType: 'uint256',
            },
        ],
        outputs: [],
        stateMutability: 'nonpayable',
    },
    {
        type: 'function',
        name: 'setThresholdWithRecipient',
        inputs: [
            {
                name: '_recipient',
                type: 'address',
                internalType: 'address',
            },
            {
                name: '_recipientToken',
                type: 'address',
                internalType: 'address',
            },
            {
                name: '_rebalancer',
                type: 'address[]',
                internalType: 'address[]',
            },
            {
                name: '_threshold',
                type: 'uint256',
                internalType: 'uint256',
            },
        ],
        outputs: [],
        stateMutability: 'nonpayable',
    },
    {
        type: 'function',
        name: 'sweep',
        inputs: [
            {
                name: '_tokens',
                type: 'address[]',
                internalType: 'address[]',
            },
            {
                name: '_amounts',
                type: 'uint256[]',
                internalType: 'uint256[]',
            },
        ],
        outputs: [],
        stateMutability: 'nonpayable',
    },
    {
        type: 'function',
        name: 'takerFactory',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'address',
                internalType: 'address',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'threshold',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'uint256',
                internalType: 'uint256',
            },
        ],
        stateMutability: 'view',
    },

    {
        type: 'function',
        name: 'withdraw',
        inputs: [
            {
                name: '_takers',
                type: 'address[]',
                internalType: 'address[]',
            },
            {
                name: '_amounts',
                type: 'uint256[]',
                internalType: 'uint256[]',
            },
        ],
        outputs: [],
        stateMutability: 'nonpayable',
    },
] as const

export const GNOSIS_S_DAI_VAULT_APY_ABI = [
    {
        inputs: [],
        name: 'vaultAPY',
        outputs: [
            {
                internalType: 'uint256',
                name: '',
                type: 'uint256',
            },
        ],
        stateMutability: 'view',
        type: 'function',
    },
] as const

export const GNOSIS_SV_ZCHF_VAULT_ABI = [
    {
        inputs: [],
        name: 'currentRatePPM',
        outputs: [
            {
                internalType: 'uint24',
                name: '',
                type: 'uint24',
            },
        ],
        stateMutability: 'view',
        type: 'function',
    },
] as const

export const GNOSIS_SV_ZCHF_ABI = [
    {
        inputs: [
            {
                internalType: 'uint256',
                name: 'shares',
                type: 'uint256',
            },
        ],
        name: 'convertToAssets',
        outputs: [
            {
                internalType: 'uint256',
                name: '',
                type: 'uint256',
            },
        ],
        stateMutability: 'view',
        type: 'function',
    },
] as const

export const GNOSIS_SDAI_ABI = [
    {
        inputs: [{ internalType: 'uint256', name: 'shares', type: 'uint256' }],
        name: 'convertToAssets',
        outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
] as const

export const ETHEREUM_WSTETH_ADDRESS = Web3.address
    .fromString('******************************************')
    .getSuccessResultOrThrow('')

export const ETHEREUM_WSTETH_ABI = [
    {
        inputs: [],
        name: 'stEthPerToken',
        outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
] as const

export const AAVE_MARKET_GET_RESERVE_DATA_ABI = [
    {
        inputs: [
            {
                internalType: 'address',
                name: 'asset',
                type: 'address',
            },
        ],
        name: 'getReserveData',
        outputs: [
            {
                components: [
                    {
                        components: [
                            {
                                internalType: 'uint256',
                                name: 'data',
                                type: 'uint256',
                            },
                        ],
                        internalType:
                            'struct DataTypes.ReserveConfigurationMap',
                        name: 'configuration',
                        type: 'tuple',
                    },
                    {
                        internalType: 'uint128',
                        name: 'liquidityIndex',
                        type: 'uint128',
                    },
                    {
                        internalType: 'uint128',
                        name: 'currentLiquidityRate',
                        type: 'uint128',
                    },
                    {
                        internalType: 'uint128',
                        name: 'variableBorrowIndex',
                        type: 'uint128',
                    },
                    {
                        internalType: 'uint128',
                        name: 'currentVariableBorrowRate',
                        type: 'uint128',
                    },
                    {
                        internalType: 'uint128',
                        name: 'currentStableBorrowRate',
                        type: 'uint128',
                    },
                    {
                        internalType: 'uint40',
                        name: 'lastUpdateTimestamp',
                        type: 'uint40',
                    },
                    {
                        internalType: 'uint16',
                        name: 'id',
                        type: 'uint16',
                    },
                    {
                        internalType: 'address',
                        name: 'aTokenAddress',
                        type: 'address',
                    },
                    {
                        internalType: 'address',
                        name: 'stableDebtTokenAddress',
                        type: 'address',
                    },
                    {
                        internalType: 'address',
                        name: 'variableDebtTokenAddress',
                        type: 'address',
                    },
                    {
                        internalType: 'address',
                        name: 'interestRateStrategyAddress',
                        type: 'address',
                    },
                    {
                        internalType: 'uint128',
                        name: 'accruedToTreasury',
                        type: 'uint128',
                    },
                    {
                        internalType: 'uint128',
                        name: 'unbacked',
                        type: 'uint128',
                    },
                    {
                        internalType: 'uint128',
                        name: 'isolationModeTotalDebt',
                        type: 'uint128',
                    },
                ],
                internalType: 'struct DataTypes.ReserveData',
                name: '',
                type: 'tuple',
            },
        ],
        stateMutability: 'view',
        type: 'function',
    },
] as const

export const EARN_CELEBRATION_INTERVALS: Record<
    TakerType,
    { lowerBound: Money2; upperBound: Money2 }[]
> = {
    usd: [
        {
            lowerBound: {
                amount: 1000000000000000000000n, // 1000 * 10^18
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 1010000000000000000000n, // 1010 * 10^18
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
        },
        {
            lowerBound: {
                amount: 100000000000000000000n, // 100 * 10^18
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 102000000000000000000n, // 102 * 10^18
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
        },
        {
            lowerBound: {
                amount: 10000000000000000000n, // 10 * 10^18
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 11000000000000000000n, // 11 * 10^18
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
        },
        {
            lowerBound: {
                amount: 1000000000000000000n, // 1 * 10^18
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 2000000000000000000n, // 2 * 10^18
                currency: EARN_USD_TAKER_USER_CURRENCY,
            },
        },
    ],
    eth: [
        {
            lowerBound: {
                amount: 1000000000000000000n, // 1 ETH
                currency: EARN_ETH_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 2000000000000000000n, // 2 ETH
                currency: EARN_ETH_TAKER_USER_CURRENCY,
            },
        },
        {
            lowerBound: {
                amount: 100000000000000000n, // 0.1 ETH
                currency: EARN_ETH_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 110000000000000000n, // 0.11 ETH
                currency: EARN_ETH_TAKER_USER_CURRENCY,
            },
        },
        {
            lowerBound: {
                amount: 10000000000000000n, // 0.01 ETH
                currency: EARN_ETH_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 11000000000000000n, // 0.011 ETH
                currency: EARN_ETH_TAKER_USER_CURRENCY,
            },
        },
        {
            lowerBound: {
                amount: 1000000000000000n, // 0.001 ETH
                currency: EARN_ETH_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 2000000000000000n, // 0.002 ETH
                currency: EARN_ETH_TAKER_USER_CURRENCY,
            },
        },
    ],
    eur: [
        {
            lowerBound: {
                amount: 1000000000000000000000n, // 1000 * 10^18
                currency: EARN_EUR_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 1010000000000000000000n, // 1010 * 10^18
                currency: EARN_EUR_TAKER_USER_CURRENCY,
            },
        },
        {
            lowerBound: {
                amount: 100000000000000000000n, // 100 * 10^18
                currency: EARN_EUR_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 102000000000000000000n, // 102 * 10^18
                currency: EARN_EUR_TAKER_USER_CURRENCY,
            },
        },
        {
            lowerBound: {
                amount: 10000000000000000000n, // 10 * 10^18
                currency: EARN_EUR_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 11000000000000000000n, // 11 * 10^18
                currency: EARN_EUR_TAKER_USER_CURRENCY,
            },
        },
        {
            lowerBound: {
                amount: 1000000000000000000n, // 1 * 10^18
                currency: EARN_EUR_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 2000000000000000000n, // 2 * 10^18
                currency: EARN_EUR_TAKER_USER_CURRENCY,
            },
        },
    ],
    chf: [
        {
            lowerBound: {
                amount: 1000000000000000000000n, // 1000 * 10^18
                currency: EARN_CHF_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 1010000000000000000000n, // 1010 * 10^18
                currency: EARN_CHF_TAKER_USER_CURRENCY,
            },
        },
        {
            lowerBound: {
                amount: 100000000000000000000n, // 100 * 10^18
                currency: EARN_CHF_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 102000000000000000000n, // 102 * 10^18
                currency: EARN_CHF_TAKER_USER_CURRENCY,
            },
        },
        {
            lowerBound: {
                amount: 10000000000000000000n, // 10 * 10^18
                currency: EARN_CHF_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 11000000000000000000n, // 11 * 10^18
                currency: EARN_CHF_TAKER_USER_CURRENCY,
            },
        },
        {
            lowerBound: {
                amount: 1000000000000000000n, // 1 * 10^18
                currency: EARN_CHF_TAKER_USER_CURRENCY,
            },
            upperBound: {
                amount: 2000000000000000000n, // 2 * 10^18
                currency: EARN_CHF_TAKER_USER_CURRENCY,
            },
        },
    ],
}
