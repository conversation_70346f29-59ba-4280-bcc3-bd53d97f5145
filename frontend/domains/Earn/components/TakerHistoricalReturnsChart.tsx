import { FormattedMessage } from 'react-intl'
import { ImageBackground } from 'react-native'

import { ChartLegend, SkyHistoricalReturnsChart } from '@zeal/uikit/Chart'
import { Column } from '@zeal/uikit/Column'
import { Row } from '@zeal/uikit/Row'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'

import { Taker } from '@zeal/domains/Earn'

type Props = {
    taker: Taker
}

export const TakerHistoricalReturnsChart = ({ taker }: Props) => {
    return (
        <Column spacing={16}>
            {(() => {
                switch (taker.type) {
                    case 'usd':
                        return (
                            <>
                                <Text
                                    variant="callout"
                                    weight="medium"
                                    color="gray20"
                                >
                                    <FormattedMessage
                                        id="earn.taker-historical-returns"
                                        defaultMessage="Historical Returns"
                                    />
                                </Text>
                                <SkyHistoricalReturnsChart />
                                <Row spacing={16}>
                                    <ChartLegend
                                        variant="large"
                                        title={
                                            <FormattedMessage
                                                id="earn.sky"
                                                defaultMessage="Sky"
                                            />
                                        }
                                        color="orange40"
                                    />
                                    <ChartLegend
                                        variant="large"
                                        title={
                                            <FormattedMessage
                                                id="earn.us-treasuries"
                                                defaultMessage="US Treasuries (SHV)"
                                            />
                                        }
                                        color="gray40"
                                    />
                                </Row>
                            </>
                        )
                    case 'eur':
                        return (
                            <>
                                <Text
                                    variant="callout"
                                    weight="medium"
                                    color="gray20"
                                >
                                    <FormattedMessage
                                        id="earn.taker-historical-returns"
                                        defaultMessage="Historical Returns"
                                    />
                                </Text>
                                <ImageBackground
                                    resizeMode="contain"
                                    style={{
                                        aspectRatio: 328 / 188,
                                    }}
                                    source={require('@zeal/assets/aave_taker_chart.png')}
                                />
                            </>
                        )

                    case 'chf':
                        return (
                            <>
                                <Text
                                    variant="callout"
                                    weight="medium"
                                    color="gray20"
                                >
                                    <FormattedMessage
                                        id="earn.taker-historical-returns.chf"
                                        defaultMessage="Growth of CHF to USD"
                                    />
                                </Text>
                                <ImageBackground
                                    resizeMode="contain"
                                    style={{
                                        aspectRatio: 252 / 154,
                                    }}
                                    source={require('@zeal/assets/chf_taker_chart.png')}
                                />
                            </>
                        )

                    case 'eth':
                        return null
                    /* istanbul ignore next */
                    default:
                        return notReachable(taker.type)
                }
            })()}
        </Column>
    )
}
