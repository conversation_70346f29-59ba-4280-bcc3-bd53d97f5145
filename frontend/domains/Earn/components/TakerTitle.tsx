import { FormattedMessage } from 'react-intl'

import { notReachable } from '@zeal/toolkit'

import { TakerType } from '@zeal/domains/Earn'

type Props = {
    takerType: TakerType
}

export const TakerTitle = ({ takerType }: Props) => {
    switch (takerType) {
        case 'usd':
            return (
                <FormattedMessage
                    id="earn.takerListItem.earnUSD.title"
                    defaultMessage="Sky USD"
                />
            )
        case 'eur':
            return (
                <FormattedMessage
                    id="earn.takerListItem.earnEURO.title"
                    defaultMessage="Aave EUR"
                />
            )
        case 'eth':
            return (
                <FormattedMessage
                    id="earn.takerListItem.earnETH.title"
                    defaultMessage="Ethereum"
                />
            )

        case 'chf':
            return (
                <FormattedMessage
                    id="earn.takerListItem.earnCHF.title"
                    defaultMessage="Frankencoin CHF"
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(takerType)
    }
}
