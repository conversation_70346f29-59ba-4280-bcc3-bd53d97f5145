import { FormattedMessage } from 'react-intl'

import { notReachable } from '@zeal/toolkit'

import { TakerType } from '@zeal/domains/Earn'

type Props = {
    takerType: TakerType
}

// TODO :: @Nicvaniek Harmonize Taker title components
export const TakerTitleWithSuffix = ({ takerType }: Props) => {
    switch (takerType) {
        case 'usd':
            return (
                <FormattedMessage
                    id="earn.takerListItemWithSuffix.earnUSD.title"
                    defaultMessage="Sky USD"
                />
            )
        case 'eur':
            return (
                <FormattedMessage
                    id="earn.takerListItemWithSuffix.earnEURO.title"
                    defaultMessage="Aave EUR"
                />
            )
        case 'eth':
            return (
                <FormattedMessage
                    id="earn.takerListItemWithSuffix.earnETH.title"
                    defaultMessage="ETH Earn"
                />
            )
        case 'chf':
            return (
                <FormattedMessage
                    id="earn.takerListItemWithSuffix.earnCHF.title"
                    defaultMessage="Frankencoin CHF"
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(takerType)
    }
}
