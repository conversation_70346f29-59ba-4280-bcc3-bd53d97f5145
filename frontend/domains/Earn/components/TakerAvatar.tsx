import { ComponentProps } from 'react'

import { Avatar, AvatarSize } from '@zeal/uikit/Avatar'
import { EarnCHF } from '@zeal/uikit/Icon/EarnCHF'
import { EarnEth } from '@zeal/uikit/Icon/EarnEth'
import { EarnEURO } from '@zeal/uikit/Icon/EarnEURO'
import { EarnUSD } from '@zeal/uikit/Icon/EarnUSD'

import { notReachable } from '@zeal/toolkit'

import { Taker } from '..'

type Props = {
    takerType: Taker['type']
    size: AvatarSize
    rightBadge?: ComponentProps<typeof Avatar>['rightBadge']
    vairiant?: ComponentProps<typeof Avatar>['variant']
    border?: ComponentProps<typeof Avatar>['border']
}

export const TakerAvatar = ({
    takerType,
    size,
    vairiant,
    border,
    rightBadge,
}: Props) => {
    switch (takerType) {
        case 'usd':
            return (
                <Avatar
                    size={size}
                    variant={vairiant}
                    border={border}
                    rightBadge={rightBadge}
                >
                    <EarnUSD size={size} />
                </Avatar>
            )
        case 'eur':
            return (
                <Avatar
                    size={size}
                    variant={vairiant}
                    border={border}
                    rightBadge={rightBadge}
                >
                    <EarnEURO size={size} />
                </Avatar>
            )
        case 'eth':
            return (
                <Avatar
                    size={size}
                    variant={vairiant}
                    border={border}
                    rightBadge={rightBadge}
                >
                    <EarnEth size={size} />
                </Avatar>
            )
        case 'chf':
            return (
                <Avatar
                    size={size}
                    variant={vairiant}
                    border={border}
                    rightBadge={rightBadge}
                >
                    <EarnCHF size={size} />
                </Avatar>
            )

        /* istanbul ignore next */
        default:
            return notReachable(takerType)
    }
}
