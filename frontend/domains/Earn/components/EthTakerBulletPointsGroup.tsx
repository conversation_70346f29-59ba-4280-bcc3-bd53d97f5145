import { FormattedMessage } from 'react-intl'

import { Avatar } from '@zeal/uikit/Avatar'
import { Group } from '@zeal/uikit/Group'
import { BoldDiscount } from '@zeal/uikit/Icon/BoldDiscount'
import { EarnLidoLogo } from '@zeal/uikit/Icon/EarnLidoLogo'
import { ZealEarn } from '@zeal/uikit/Icon/ZealEarn'
import { BulletpointsListItem } from '@zeal/uikit/ListItem/BulletpointsListItem'

import { getFormattedPercentage } from '@zeal/toolkit/Percentage'

import { TakerApyMap, TakerType } from '@zeal/domains/Earn'

type Props = {
    takerType: Extract<TakerType, 'eth'>
    takerApyMap: TakerApyMap
}

export const EthTakerBulletPointsGroup = ({
    takerApyMap,
    takerType: _,
}: Props) => {
    return (
        <Group variant="default" scroll>
            <BulletpointsListItem
                avatar={({ size }) => <ZealEarn size={size} />}
                text={
                    <FormattedMessage
                        id="earn.taker-bulletlist.point_1"
                        defaultMessage="Earn {apyValue} annually. Returns vary with market."
                        values={{
                            apyValue: getFormattedPercentage(
                                takerApyMap['eth']
                            ),
                        }}
                    />
                }
                rightIcon={null}
            />
            <BulletpointsListItem
                avatar={({ size }) => <EarnLidoLogo size={size} />}
                text={
                    <FormattedMessage
                        id="earn.taker-bulletlist.eth.point_2"
                        defaultMessage="Hold wstETH (Staked ETH) on Gnosis Chain, and lend via Lido."
                    />
                }
                rightIcon={null}
            />
            <BulletpointsListItem
                avatar={({ size }) => (
                    <Avatar size={size}>
                        <BoldDiscount color="green30" size={size} />
                    </Avatar>
                )}
                text={
                    <FormattedMessage
                        id="earn.taker-bulletlist.point_3"
                        defaultMessage="Zeal charges no fees."
                    />
                }
                rightIcon={null}
            />
        </Group>
    )
}
