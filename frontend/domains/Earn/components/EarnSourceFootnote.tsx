import { ComponentProps } from 'react'
import { FormattedMessage } from 'react-intl'

import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'

import { Taker } from '..'

type Props = {
    color: ComponentProps<typeof Text>['color']
    taker: Taker
}

export const EarnSourceFootnote = ({ taker, color }: Props) => {
    switch (taker.type) {
        case 'usd':
            return (
                <Text variant="footnote" color={color}>
                    <FormattedMessage
                        id="earn.takerListItem.earnFromMakerOnGnosis.footnote"
                        defaultMessage="Earning from Maker on Gnosis Chain"
                    />
                </Text>
            )
        case 'eur':
            return (
                <Text variant="footnote" color={color}>
                    <FormattedMessage
                        id="earn.takerListItem.earnFromAaveOnGnosis.footnote"
                        defaultMessage="Earning from Aave on Gnosis Chain"
                    />
                </Text>
            )
        case 'eth':
            return (
                <Text variant="footnote" color={color}>
                    <FormattedMessage
                        id="earn.takerListItem.earnFromLidoOnGnosis.footnote"
                        defaultMessage="Earning from Lido on Gnosis Chain"
                    />
                </Text>
            )
        case 'chf':
            return (
                <Text variant="footnote" color={color}>
                    <FormattedMessage
                        id="earn.takerListItem.earnFromFrankencoinOnGnosis.footnote"
                        defaultMessage="Earning from Frankencoin on Gnosis Chain"
                    />
                </Text>
            )

        /* istanbul ignore next */
        default:
            return notReachable(taker.type)
    }
}
