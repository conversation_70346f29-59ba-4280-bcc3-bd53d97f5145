import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { FancyButton } from '@zeal/uikit/FancyButton'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { ListItemButtonLarge } from '@zeal/uikit/ListItem'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'

import { Taker, TakerApyMap } from '@zeal/domains/Earn'
import { captureError } from '@zeal/domains/Error/helpers/captureError'

import { TakerAPYLabel } from '../TakerAPYLabel'
import { TakerAvatar } from '../TakerAvatar'
import { TakerTitle } from '../TakerTitle'

type Props = {
    takersToChoose: Taker[]
    takerApyMap: TakerApyMap
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | { type: 'on_earn_deposit_asset_click'; taker: Taker }
    | { type: 'on_taker_info_clicked'; taker: Taker }

export const Layout = ({ onMsg, takersToChoose, takerApyMap }: Props) => {
    useEffect(() => {
        if (takersToChoose.length === 0) {
            captureError(
                new ImperativeError(
                    'got empty list of takers config  in select taker screen'
                )
            )
        }
    }, [takersToChoose.length])

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={16}>
                <ActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <Text
                                    variant="title3"
                                    weight="semi_bold"
                                    color="textPrimary"
                                >
                                    <FormattedMessage
                                        id="earn.add_another_asset.title"
                                        defaultMessage="Select Earning asset"
                                    />
                                </Text>
                            </Row>
                        </Clickable>
                    }
                />

                <Column spacing={8}>
                    {takersToChoose.map((taker) => {
                        switch (taker.type) {
                            case 'usd':
                            case 'eur':
                            case 'eth':
                            case 'chf':
                                return (
                                    <ListItemButtonLarge
                                        primaryText={
                                            <TakerTitle
                                                takerType={taker.type}
                                            />
                                        }
                                        avatar={({ size }) => (
                                            <TakerAvatar
                                                takerType={taker.type}
                                                size={size}
                                            />
                                        )}
                                        title={({
                                            color,
                                            textVariant,
                                            textWeight,
                                        }) => (
                                            <Text
                                                variant={textVariant}
                                                weight={textWeight}
                                                color={color}
                                            >
                                                <TakerAPYLabel
                                                    taker={taker}
                                                    takerApyMap={takerApyMap}
                                                />
                                            </Text>
                                        )}
                                        key={taker.type}
                                        background={`earn_${taker.type}`}
                                        side={{
                                            rightIcon: () => (
                                                <FancyButton
                                                    right={null}
                                                    color="secondary"
                                                    rounded
                                                    size="large"
                                                    left={({
                                                        textVariant,
                                                        textWeight,
                                                    }) => (
                                                        <Text
                                                            color="gray10"
                                                            variant={
                                                                textVariant
                                                            }
                                                            weight={textWeight}
                                                        >
                                                            <FormattedMessage
                                                                id="earn.takerListItem.deposit"
                                                                defaultMessage="Deposit"
                                                            />
                                                        </Text>
                                                    )}
                                                    onClick={() =>
                                                        onMsg({
                                                            type: 'on_earn_deposit_asset_click',
                                                            taker: taker,
                                                        })
                                                    }
                                                />
                                            ),
                                        }}
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_taker_info_clicked',
                                                taker: taker,
                                            })
                                        }
                                    />
                                )
                            default:
                                return notReachable(taker.type)
                        }
                    })}
                </Column>
            </Column>
        </Screen>
    )
}
