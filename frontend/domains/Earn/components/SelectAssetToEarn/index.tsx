import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { keys } from '@zeal/toolkit/Object'

import { Earn, EarnTakerMetrics } from '@zeal/domains/Earn'
import { KeyStore } from '@zeal/domains/KeyStore'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { keystoreToUserEventType } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, State } from './Modal'

import { EARN_PRIMARY_INVESTMENT_ASSETS_MAP } from '../../constants'

type Props = {
    defaultCurrencyConfig: DefaultCurrencyConfig
    earn: Earn
    installationId: string
    keyStore: KeyStore
    earnTakerMetrics: EarnTakerMetrics
    variant: 'all_takers' | 'not_deployed_takers'
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_earn_deposit_asset_click'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
          }
      >
    | Extract<MsgOf<typeof Layout>, { type: 'on_earn_deposit_asset_click' }>

export const SelectAssetToEarn = ({
    defaultCurrencyConfig,
    earn,
    installationId,
    variant,
    earnTakerMetrics,
    keyStore,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'closed' })

    useEffect(() => {
        postUserEvent({
            type: 'EarnSelectAssetEnteredEvent',
            earnStatus: (() => {
                switch (earn.type) {
                    case 'not_configured':
                        return 'disabled'
                    case 'configured':
                        return 'enabled'

                    default:
                        return notReachable(earn)
                }
            })(),
            keystoreType: keystoreToUserEventType(keyStore),
            installationId,
        })
    }, [installationId, keyStore, earn])

    const takersToChoose = earn.takers
        .filter((taker) =>
            keys(EARN_PRIMARY_INVESTMENT_ASSETS_MAP).some(
                (takerType) => takerType === taker.type
            )
        )
        .filter((taker) => {
            switch (variant) {
                case 'all_takers':
                    return true

                case 'not_deployed_takers': {
                    switch (taker.state) {
                        case 'deployed':
                            return false

                        case 'not_deployed':
                            return true

                        /* istanbul ignore next */
                        default:
                            return notReachable(taker)
                    }
                }

                /* istanbul ignore next */
                default:
                    return notReachable(variant)
            }
        })

    return (
        <>
            <Layout
                takerApyMap={earn.takerApyMap}
                takersToChoose={takersToChoose}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_earn_deposit_asset_click':
                            onMsg(msg)
                            break
                        case 'on_taker_info_clicked':
                            setState({
                                type: 'taker_info_screen',
                                taker: msg.taker,
                                takerApyMap: earn.takerApyMap,
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                defaultCurrencyConfig={defaultCurrencyConfig}
                state={state}
                installationId={installationId}
                earnTakerMetrics={earnTakerMetrics}
                takerPortfolioMap={earn.takerPortfolioMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setState({ type: 'closed' })
                            break
                        case 'on_earn_deposit_asset_click':
                            setState({ type: 'closed' })
                            onMsg(msg)
                            break
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_chf_taker_metrics_loaded':
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
