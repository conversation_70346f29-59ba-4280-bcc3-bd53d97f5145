import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import { Percentage } from '@zeal/toolkit/Percentage'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    Currency,
    DefaultCurrency,
} from '@zeal/domains/Currency'
import { SwapRoute } from '@zeal/domains/Currency/domains/SwapQuote'
import { SwapsIOContractsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { FXRate2 } from '@zeal/domains/FXRate'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { <PERSON>ptoMoney, FiatMoney, Money2 } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

export type Earn = NotConfiguredEarn | ConfiguredEarn

export type NotConfiguredEarn = {
    type: 'not_configured'
    holder: Web3.address.Address
    takers: NotDeployedTaker[]
    takerPortfolioMap: TakerPortfolioMap2
    takerApyMap: TakerApyMap
}

export type ConfiguredEarn = {
    type: 'configured'
    holder: Web3.address.Address
    takers: Taker[]
    takerPortfolioMap: TakerPortfolioMap2
    cardRecharge: CardRecharge
    takerApyMap: TakerApyMap
}

export type CardRecharge = CardRechargeDisabled | CardRechargeEnabled

export type CardRechargeDisabled = { type: 'recharge_disabled' }
export type CardRechargeEnabled = {
    type: 'recharge_enabled'
    threshold: bigint
    rebalancers: NonEmptyArray<DeployedTaker>
    cardSafeAddress: Web3.address.Address
    cardCurrency: CryptoCurrency
}
export type USDTakerType = 'usd'
export type EURTakerType = 'eur'
export type CHFTakerType = 'chf'

export type TakerType = USDTakerType | EURTakerType | CHFTakerType | 'eth'

export type NotDeployedTaker = {
    type: TakerType
    address: Web3.address.Address
    cryptoCurrency: CryptoCurrency
    state: 'not_deployed'
}

export type DeployedTaker = {
    type: TakerType
    address: Web3.address.Address
    cryptoCurrency: CryptoCurrency
    state: 'deployed'
}

export type Taker = NotDeployedTaker | DeployedTaker

export type TakerPortfolio = {
    assetBalance: CryptoMoney
    userCurrencyRate: FXRate2<CryptoCurrency, Currency>
    userCurrencyToDefaultCurrencyRate: FXRate2<Currency, DefaultCurrency> | null
    apy: Percentage

    dataTimestampMs: number
}

export type TakerPortfolioMap2 = Record<TakerType, TakerPortfolio>

export type TakerApyMap = Record<TakerType, Percentage>

export type HistoricalTakerUserCurrencyRateMap = Record<
    TakerType,
    BlockUserCurrencyRateMap
>

export type BlockUserCurrencyRateMap = Record<
    number,
    FXRate2<CryptoCurrency, Currency>
>

export type HistoricalTakerUserCurrencyRateStorageMap = Record<
    TakerType,
    Record<number, bigint>
>

export type EarnDepositWithSwapRequest = {
    taker: Taker
    fromAccount: Account
    fromAccountPortfolio: ServerPortfolio2
    from: CryptoMoney
    swapRoute: SwapRoute
    takerUserCurrencyRate: FXRate2<CryptoCurrency, Currency>
}

export type EarnDepositDirectSendRequest = {
    taker: Taker
    fromAccount: Account
    fromAccountPortfolio: ServerPortfolio2
    from: CryptoMoney
    takerUserCurrencyRate: FXRate2<CryptoCurrency, Currency>
    ethSendTransaction: EthSendTransaction
}

export type EarnDepositSwapRouteRequest = {
    networkMap: NetworkMap
    fromAccount: Account
    amount: string | null
    fromCurrency: CryptoCurrency
    taker: Taker
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    keyStoreMap: KeyStoreMap
}

export type EarnDepositBridgeQouteRequest = {
    networkMap: NetworkMap
    fromAccount: Account
    amount: string | null
    fromCurrency: CryptoCurrency
    taker: Taker
    networkRPCMap: NetworkRPCMap
    swapsIOContractsMap: SwapsIOContractsMap
    defaultCurrencyConfig: DefaultCurrencyConfig
}

export type EarnWithdrawRequest = {
    earn: ConfiguredEarn
    swapRoute: SwapRoute
    owner: Account
    taker: Taker
    fromAmountInUserCurrency: Money2
    toAmount: CryptoMoney
    investmentAssetAmount: bigint
}

export type UsdEarnTakerMetrics = {
    reserveTotal: FiatMoney // collateral
    depositorObligations: FiatMoney // total supply
    assetCoverageRatio: number
    collateralComposition: {
        stableCoins: FiatMoney
        bitcoinEther: FiatMoney
        usTBills: FiatMoney
        other: FiatMoney
    }
}

export type CHFEarnTakerMetrics = {
    totalSupply: FiatMoney
    totalCollateral: FiatMoney
    assetCoverageRatio: number
    collateralComposition: {
        bitcoin: FiatMoney
        ethereum: FiatMoney
        realWorldAssets: FiatMoney
        other: FiatMoney
    }
}

export type AaveEureTakerMetrics = {
    trailing12MonthYield: number // % as float
}

export type EarnTakerMetrics = {
    usd: UsdEarnTakerMetrics | null
    eur: AaveEureTakerMetrics | null
    chf: CHFEarnTakerMetrics | null
}

export type Earnings<M extends Money2> = {
    amount: M
    earningsPerMs: M
}

export type TotalEarningsInDefaultCurrencyMap = Record<
    Web3.address.Address,
    Earnings<FiatMoney> | null
>

export type EarnCelebrationConfigForOneAccount = {
    highestTotalEarningCelebratedInUserCurrency: Partial<
        Record<TakerType, Money2 | null>
    >
}

export type EarnCelebrationConfig = Partial<
    Record<Web3.address.Address, EarnCelebrationConfigForOneAccount>
>

export type TakerToCelebrate = {
    taker: DeployedTaker
    totalTakerEarningsInUserCurrency: Money2
}
