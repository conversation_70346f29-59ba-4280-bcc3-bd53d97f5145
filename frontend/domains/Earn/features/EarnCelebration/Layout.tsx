import { useState } from 'react'
import { FormattedMessage, useIntl } from 'react-intl'

import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { <PERSON><PERSON><PERSON> } from '@zeal/uikit/Confetti'
import { getLanguageRule } from '@zeal/uikit/Language'
import { useLanguage } from '@zeal/uikit/Language/LanguageContext'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { unsafe_toNumberWithFraction } from '@zeal/toolkit/BigInt'
import { useCurrentTimestamp } from '@zeal/toolkit/Date/useCurrentTimestamp'

import {
    TakerPortfolio,
    TakerPortfolioMap2,
    TakerToCelebrate,
} from '@zeal/domains/Earn'
import { calculateEarningInUserCurrencyPerMS } from '@zeal/domains/Earn/helpers/calculateEarningInUserCurrencyPerMS'
import { FiatMoney, Money2 } from '@zeal/domains/Money'
import { FormattedMoneyWithParts } from '@zeal/domains/Money/components/FormattedMoneyWithParts'
import { addCurrencySymbolToFormatedMoneyParts } from '@zeal/domains/Money/helpers/addCurrencySymbolToFormatedMoney'
import { getTickingDecimalsNeeded } from '@zeal/domains/Money/helpers/getTickingDecimalsNeeded'
import { mulByNumber } from '@zeal/domains/Money/helpers/mul'
import { sumMoney } from '@zeal/domains/Money/helpers/sum'
import { useMoneyFormat } from '@zeal/domains/Money/hooks/useMoneyFormat'

type Props = {
    takerPortfolioMap: TakerPortfolioMap2
    takerToCelebrate: TakerToCelebrate
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const Layout = ({
    takerToCelebrate,
    takerPortfolioMap,
    onMsg,
}: Props) => {
    const { totalTakerEarningsInUserCurrency, taker } = takerToCelebrate

    const takerPortfolio = takerPortfolioMap[taker.type]

    switch (taker.type) {
        case 'usd':
            return (
                <Screen
                    padding="form"
                    background="skyCelebrationArtwork"
                    onNavigateBack={() => onMsg({ type: 'close' })}
                >
                    <Confetti />
                    <Column spacing={0} fill alignX="center" alignY="center">
                        <Text
                            variant="largeTitle"
                            color="orange30"
                            weight="medium"
                        >
                            <FormattedMessage
                                id="celebration.well_done.title"
                                defaultMessage="Well done!"
                            />
                        </Text>

                        <Earnings
                            takerPortfolio={takerPortfolio}
                            totalTakerEarningsInUserCurrency={
                                totalTakerEarningsInUserCurrency
                            }
                        />

                        <Text variant="title1" color="gray30" weight="medium">
                            <FormattedMessage
                                id="celebration.sky"
                                defaultMessage="Earned with Sky"
                            />
                        </Text>
                    </Column>
                    <Column spacing={16}>
                        <Actions variant="default">
                            <Button
                                variant="secondary"
                                size="regular"
                                onClick={() => onMsg({ type: 'close' })}
                            >
                                <FormattedMessage
                                    id="action.continue"
                                    defaultMessage="Continue"
                                />
                            </Button>
                        </Actions>
                    </Column>
                </Screen>
            )
        case 'eur':
            return (
                <Screen
                    padding="form"
                    background="aaveCelebrationArtwork"
                    onNavigateBack={() => onMsg({ type: 'close' })}
                >
                    <Confetti />
                    <Column spacing={0} fill alignX="center" alignY="center">
                        <Text
                            variant="largeTitle"
                            color="blue25"
                            weight="medium"
                        >
                            <FormattedMessage
                                id="celebration.well_done.title"
                                defaultMessage="Well done!"
                            />
                        </Text>

                        <Earnings
                            takerPortfolio={takerPortfolio}
                            totalTakerEarningsInUserCurrency={
                                totalTakerEarningsInUserCurrency
                            }
                        />

                        <Text variant="title1" color="gray30" weight="medium">
                            <FormattedMessage
                                id="celebration.aave"
                                defaultMessage="Earned with Aave"
                            />
                        </Text>
                    </Column>
                    <Column spacing={16}>
                        <Actions variant="default">
                            <Button
                                variant="secondary"
                                size="regular"
                                onClick={() => onMsg({ type: 'close' })}
                            >
                                <FormattedMessage
                                    id="action.continue"
                                    defaultMessage="Continue"
                                />
                            </Button>
                        </Actions>
                    </Column>
                </Screen>
            )
        case 'eth': {
            return (
                <Screen
                    padding="form"
                    background="lidoCelebrationArtwork"
                    onNavigateBack={() => onMsg({ type: 'close' })}
                >
                    <Confetti />
                    <Column spacing={0} fill alignX="center" alignY="center">
                        <Text
                            variant="largeTitle"
                            color="gray30"
                            weight="medium"
                        >
                            <FormattedMessage
                                id="celebration.well_done.title"
                                defaultMessage="Well done!"
                            />
                        </Text>

                        <Earnings
                            takerPortfolio={takerPortfolio}
                            totalTakerEarningsInUserCurrency={
                                totalTakerEarningsInUserCurrency
                            }
                        />

                        <Text variant="title1" color="gray30" weight="medium">
                            <FormattedMessage
                                id="celebration.lido"
                                defaultMessage="Earned with Lido"
                            />
                        </Text>
                    </Column>
                    <Column spacing={16}>
                        <Actions variant="default">
                            <Button
                                variant="secondary"
                                size="regular"
                                onClick={() => onMsg({ type: 'close' })}
                            >
                                <FormattedMessage
                                    id="action.continue"
                                    defaultMessage="Continue"
                                />
                            </Button>
                        </Actions>
                    </Column>
                </Screen>
            )
        }

        case 'chf':
            return (
                <Screen
                    padding="form"
                    background="frankencoinCelebrationArtwork"
                    onNavigateBack={() => onMsg({ type: 'close' })}
                >
                    <Confetti />
                    <Column spacing={0} fill alignX="center" alignY="center">
                        <Text
                            variant="largeTitle"
                            color="red30"
                            weight="medium"
                        >
                            <FormattedMessage
                                id="celebration.well_done.title"
                                defaultMessage="Well done!"
                            />
                        </Text>

                        <Earnings
                            takerPortfolio={takerPortfolio}
                            totalTakerEarningsInUserCurrency={
                                totalTakerEarningsInUserCurrency
                            }
                        />

                        <Text variant="title1" color="gray30" weight="medium">
                            <FormattedMessage
                                id="celebration.chf"
                                defaultMessage="Earned with Frankencoin"
                            />
                        </Text>
                    </Column>
                    <Column spacing={16}>
                        <Actions variant="default">
                            <Button
                                variant="secondary"
                                size="regular"
                                onClick={() => onMsg({ type: 'close' })}
                            >
                                <FormattedMessage
                                    id="action.continue"
                                    defaultMessage="Continue"
                                />
                            </Button>
                        </Actions>
                    </Column>
                </Screen>
            )

        default:
            return notReachable(taker.type)
    }
}

const Earnings = ({
    totalTakerEarningsInUserCurrency,
    takerPortfolio,
}: {
    totalTakerEarningsInUserCurrency: Money2
    takerPortfolio: TakerPortfolio
}) => {
    const { formattedMoneyPreciseParts } = useMoneyFormat()

    switch (totalTakerEarningsInUserCurrency.currency.type) {
        case 'FiatCurrency':
            return (
                <TickingEarnings
                    takerPortfolio={takerPortfolio}
                    totalTakerEarningsInUserCurrency={{
                        amount: totalTakerEarningsInUserCurrency.amount,
                        currency: totalTakerEarningsInUserCurrency.currency,
                    }}
                />
            )
        case 'CryptoCurrency':
            const { formattedIntPart, formattedDecimalsWithSeparator } =
                formattedMoneyPreciseParts({
                    money: totalTakerEarningsInUserCurrency,
                    sign: null,
                })
            return (
                <FormattedBalance
                    formattedDecimalsWithSeparator={
                        formattedDecimalsWithSeparator
                    }
                    formattedIntPart={formattedIntPart}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(totalTakerEarningsInUserCurrency.currency)
    }
}

const MAX_EARNING_DIGITS = 14

const TickingEarnings = ({
    totalTakerEarningsInUserCurrency,
    takerPortfolio,
}: {
    takerPortfolio: TakerPortfolio
    totalTakerEarningsInUserCurrency: FiatMoney // earnings include earnings since last timestamp
}) => {
    const { currentSelectedLanguage } = useLanguage()
    const rule = getLanguageRule(currentSelectedLanguage)

    const { formatNumber } = useIntl()
    const [initialRenderTimeStamp] = useState(Date.now()) // we count earnings from initial render since total earnings already includes earnings from last timestamp

    const rerenderInterval = 300
    const _rerender = useCurrentTimestamp({
        refreshIntervalMs: rerenderInterval,
    })

    const earningsPerMs = calculateEarningInUserCurrencyPerMS({
        takerPortfolio,
    })

    const timeSinceLastEarningsEvent = Date.now() - initialRenderTimeStamp

    const earnedSinceInitialRender = mulByNumber(
        earningsPerMs,
        timeSinceLastEarningsEvent
    )

    const totalEarnings = sumMoney(
        totalTakerEarningsInUserCurrency,
        earnedSinceInitialRender
    )

    const earningsAmount = unsafe_toNumberWithFraction(
        totalEarnings.amount,
        totalEarnings.currency.fraction
    )

    const tickingDecimalsNeeded = getTickingDecimalsNeeded({
        earningsPerMs,
        tickIntervalMs: rerenderInterval,
    })

    const decimalPlacesNeeded =
        tickingDecimalsNeeded > MAX_EARNING_DIGITS
            ? MAX_EARNING_DIGITS
            : tickingDecimalsNeeded

    const formattedAmount = formatNumber(earningsAmount, {
        maximumFractionDigits: decimalPlacesNeeded,
        minimumFractionDigits: decimalPlacesNeeded,
    })
    const [integer, decimals] = formattedAmount.split(rule.decimalSeparator)
    const { formattedIntPart, formattedDecimalsWithSeparator } =
        addCurrencySymbolToFormatedMoneyParts({
            formattedIntPartWithoutSymbol: integer,
            formattedDesimalsPartWithoutSymbol: decimals,
            rule,
            currency: totalEarnings.currency,
        })

    return (
        <FormattedBalance
            formattedIntPart={formattedIntPart}
            formattedDecimalsWithSeparator={formattedDecimalsWithSeparator}
        />
    )
}

const FormattedBalance = ({
    formattedDecimalsWithSeparator,
    formattedIntPart,
}: {
    formattedIntPart: string
    formattedDecimalsWithSeparator: string | null
}) => (
    <FormattedMoneyWithParts
        intPart={{
            formattedIntPart,
            variant: 'titleXXL',
            color: 'gray5',
            weight: 'semi_bold',
        }}
        decimalsWithSeparator={
            formattedDecimalsWithSeparator
                ? {
                      formattedDecimalsWithSeparator,
                      variant: 'title1',
                      color: 'gray30',
                      weight: 'medium',
                  }
                : null
        }
    />
)
