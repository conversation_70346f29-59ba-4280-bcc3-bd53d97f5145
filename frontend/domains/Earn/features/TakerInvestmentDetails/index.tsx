import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import {
    EarnTakerMetrics,
    Taker,
    TakerApyMap,
    TakerPortfolioMap2,
} from '@zeal/domains/Earn'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { UserEvent } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { AaveEureTakerInvestmentDetails } from './AaveEureTakerInvestmentDetails'
import { FrankencoinInvestmentDetails } from './FrankencoinInvestmentDetails'
import { UsdTakerInvestmentDetails } from './UsdTakerInvestmentDetails'

type Props = {
    variant: 'just_information' | 'information_and_interaction'
    defaultCurrencyConfig: DefaultCurrencyConfig
    taker: Taker
    takerApyMap: TakerApyMap
    takerPortfolioMap: TakerPortfolioMap2
    earnTakerMetrics: EarnTakerMetrics
    installationId: string
    location: Extract<
        UserEvent,
        { type: 'EarnAssetDetailsEnteredEvent' }
    >['location']
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof UsdTakerInvestmentDetails>
    | MsgOf<typeof AaveEureTakerInvestmentDetails>
    | MsgOf<typeof FrankencoinInvestmentDetails>

export const TakerInvestmentDetails = ({
    variant,
    defaultCurrencyConfig,
    taker,
    takerApyMap,
    takerPortfolioMap,
    earnTakerMetrics,
    installationId,
    location,
    onMsg,
}: Props) => {
    useEffect(() => {
        postUserEvent({
            type: 'EarnAssetDetailsEnteredEvent',
            asset: taker.type,
            installationId,
            location,
        })
    }, [installationId, location, taker.type])

    switch (taker.type) {
        case 'usd':
            return (
                <UsdTakerInvestmentDetails
                    variant={variant}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    installationId={installationId}
                    taker={taker}
                    takerApyMap={takerApyMap}
                    takerPortfolioMap={takerPortfolioMap}
                    takerMetrics={earnTakerMetrics[taker.type]}
                    onMsg={onMsg}
                />
            )
        case 'eur':
            return (
                <AaveEureTakerInvestmentDetails
                    variant={variant}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    takerMetrics={earnTakerMetrics[taker.type]}
                    installationId={installationId}
                    taker={taker}
                    takerApyMap={takerApyMap}
                    takerPortfolioMap={takerPortfolioMap}
                    onMsg={onMsg}
                />
            )
        case 'chf':
            return (
                <FrankencoinInvestmentDetails
                    variant={variant}
                    taker={taker}
                    takerApyMap={takerApyMap}
                    takerPortfolioMap={takerPortfolioMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    installationId={installationId}
                    takerMetrics={earnTakerMetrics[taker.type]}
                    onMsg={onMsg}
                />
            )
        case 'eth':
            return null
        /* istanbul ignore next */
        default:
            return notReachable(taker.type)
    }
}
