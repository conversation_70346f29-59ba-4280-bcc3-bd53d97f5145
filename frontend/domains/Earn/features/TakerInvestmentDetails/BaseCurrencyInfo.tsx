import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { IconButton } from '@zeal/uikit/IconButton'
import { Popup } from '@zeal/uikit/Popup'
import { Text } from '@zeal/uikit/Text'

type Props = {
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const BaseCurrencyInfo = ({ onMsg }: Props) => (
    <Popup.Layout onMsg={onMsg}>
        <Popup.Content>
            <ActionBar
                left={null}
                right={
                    <IconButton
                        variant="on_light"
                        onClick={() =>
                            onMsg({
                                type: 'close',
                            })
                        }
                    >
                        {({ color }) => <CloseCross size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={12}>
                <Text variant="title3" weight="semi_bold" color="gray5">
                    <FormattedMessage
                        id="earn.base-currency-popup.title"
                        defaultMessage="Base currency"
                    />
                </Text>
                <Text variant="callout" weight="regular" color="gray20">
                    <FormattedMessage
                        id="earn.base-currency-popup.text"
                        defaultMessage="The base currency is how your deposits, yield, and transactions are valued and recorded. If you deposit in a different currency (such as EUR into USD), your funds are immediately converted into the base currency using current exchange rates. After conversion, your balance remains stable in the base currency, but future withdrawals may involve currency conversions again."
                    />
                </Text>
            </Column>
        </Popup.Content>
    </Popup.Layout>
)
