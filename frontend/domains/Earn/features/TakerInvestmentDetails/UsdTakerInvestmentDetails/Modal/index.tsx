import { FormattedMessage } from 'react-intl'

import { Tick } from '@zeal/uikit/Icon/Tick'
import { Modal as UIModal } from '@zeal/uikit/Modal'
import { Toast, ToastContainer, ToastText } from '@zeal/uikit/Toast'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { CanILoseMyPrincipal } from './CanILoseMyPrincipal'
import { HighReturns } from './HighReturns'
import { HowIsSkyBacked } from './HowIsSkyBacked'
import { LendingOperationsInfo } from './LendingOperationsInfo'
import { MarketMakingOperationsInfo } from './MarketMakingOperationsInfo'
import { SkyFtxDifference } from './SkyFtxDifference'
import { SkyInsurance } from './SkyInsurance'
import { TreasuryOperationsInfo } from './TreasuryOperationsInfo'

import { BaseCurrencyInfo } from '../../BaseCurrencyInfo'

type Props = {
    state: State
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof LendingOperationsInfo>
    | MsgOf<typeof MarketMakingOperationsInfo>
    | MsgOf<typeof TreasuryOperationsInfo>
    | MsgOf<typeof BaseCurrencyInfo>
    | MsgOf<typeof HowIsSkyBacked>
    | MsgOf<typeof CanILoseMyPrincipal>
    | MsgOf<typeof HighReturns>
    | MsgOf<typeof SkyFtxDifference>
    | MsgOf<typeof SkyInsurance>

export type State =
    | { type: 'closed' }
    | { type: 'lending_operations_info' }
    | { type: 'market_making_operations_info' }
    | { type: 'treasury_operations_info' }
    | { type: 'base_currency_info' }
    | { type: 'faq_how_is_sky_backed' }
    | { type: 'faq_can_lose_principal' }
    | { type: 'faq_high_returns' }
    | { type: 'faq_ftx_difference' }
    | { type: 'faq_insurance' }
    | { type: 'copy_address_toast'; address: Web3.address.Address }

export const Modal = ({ state, onMsg }: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'lending_operations_info':
            return <LendingOperationsInfo onMsg={onMsg} />
        case 'market_making_operations_info':
            return <MarketMakingOperationsInfo onMsg={onMsg} />
        case 'treasury_operations_info':
            return <TreasuryOperationsInfo onMsg={onMsg} />
        case 'base_currency_info':
            return <BaseCurrencyInfo onMsg={onMsg} />
        case 'faq_how_is_sky_backed':
            return <HowIsSkyBacked onMsg={onMsg} />
        case 'faq_can_lose_principal':
            return <CanILoseMyPrincipal onMsg={onMsg} />
        case 'faq_high_returns':
            return <HighReturns onMsg={onMsg} />
        case 'faq_ftx_difference':
            return <SkyFtxDifference onMsg={onMsg} />
        case 'faq_insurance':
            return <SkyInsurance onMsg={onMsg} />
        case 'copy_address_toast':
            return (
                <UIModal>
                    <ToastContainer navOffset>
                        <Toast>
                            <Tick size={20} color="backgroundLight" />
                            <ToastText>
                                <FormattedMessage
                                    id="accounts.view.copied-text"
                                    defaultMessage="Copied {formattedAddress}"
                                    values={{
                                        formattedAddress: Web3.address.format(
                                            state.address
                                        ),
                                    }}
                                />
                            </ToastText>
                        </Toast>
                    </ToastContainer>
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
