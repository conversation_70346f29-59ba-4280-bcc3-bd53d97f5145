import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { useCopyTextToClipboard } from '@zeal/toolkit/Clipboard/hooks/useCopyTextToClipboard'
import {
    ReloadableData,
    useReloadableData,
} from '@zeal/toolkit/LoadableData/ReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    CHFEarnTakerMetrics,
    Taker,
    TakerApyMap,
    TakerPortfolioMap2,
} from '@zeal/domains/Earn'
import { fetchCHFEarnTakerMetrics } from '@zeal/domains/Earn/api/fetchCHFEarnTakerMetrics'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Layout } from './Layout'
import { Modal, State } from './Modal'

type Msg =
    | {
          type: 'on_chf_taker_metrics_loaded'
          metrics: CHFEarnTakerMetrics
      }
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'close'
                  | 'on_earn_deposit_asset_click'
                  | 'on_earn_withdrawal_asset_click'
          }
      >

type Props = {
    variant: 'just_information' | 'information_and_interaction'
    taker: Taker
    takerApyMap: TakerApyMap
    takerPortfolioMap: TakerPortfolioMap2
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    takerMetrics: CHFEarnTakerMetrics | null
    onMsg: (msg: Msg) => void
}

export const FrankencoinInvestmentDetails = ({
    variant,
    taker,
    takerApyMap,
    takerPortfolioMap,
    defaultCurrencyConfig,
    installationId,
    takerMetrics,
    onMsg,
}: Props) => {
    const [copy, setCopy] = useCopyTextToClipboard()
    const [state, setState] = useState<State>({ type: 'closed' })

    const [loadable] = useReloadableData(
        fetchCHFEarnTakerMetrics,
        (): ReloadableData<CHFEarnTakerMetrics, undefined> =>
            takerMetrics
                ? {
                      type: 'reloading' as const,
                      params: undefined,
                      data: takerMetrics,
                  }
                : { type: 'loading', params: undefined }
    )

    const liveMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveMsg.current({
                    type: 'on_chf_taker_metrics_loaded',
                    metrics: loadable.data,
                })
                break
            case 'error':
            case 'subsequent_failed':
                captureError(loadable.error)
                break
            case 'loading':
            case 'reloading':
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveMsg])

    useEffect(() => {
        switch (copy.type) {
            case 'not_asked':
                setState({ type: 'closed' })
                break
            case 'loading':
                break
            case 'loaded':
                setState({
                    type: 'copy_address_toast',
                    address: copy.params.stringToCopy as Web3.address.Address,
                })
                break
            case 'error':
                captureError(copy.error)
                break
            /* istanbul ignore next */
            default:
                return notReachable(copy)
        }
    }, [copy])

    return (
        <>
            <Layout
                variant={variant}
                taker={taker}
                takerApyMap={takerApyMap}
                takerPortfolioMap={takerPortfolioMap}
                takerMetricsLoadable={loadable}
                defaultCurrencyConfig={defaultCurrencyConfig}
                installationId={installationId}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_earn_deposit_asset_click':
                        case 'on_earn_withdrawal_asset_click':
                            onMsg(msg)
                            break
                        case 'on_copy_taker_address_clicked':
                            setCopy({
                                type: 'loading',
                                params: { stringToCopy: msg.address },
                            })
                            break
                        case 'on_base_currency_info_click':
                            setState({ type: 'base_currency_info' })
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                state={state}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setState({ type: 'closed' })
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg.type)
                    }
                }}
            />
        </>
    )
}
