import { FormattedMessage } from 'react-intl'

import { Tick } from '@zeal/uikit/Icon/Tick'
import { Modal as UIModal } from '@zeal/uikit/Modal'
import { Toast, ToastContainer, ToastText } from '@zeal/uikit/Toast'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { BaseCurrencyInfo } from '../BaseCurrencyInfo'

type Props = {
    state: State
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof BaseCurrencyInfo>

export type State =
    | { type: 'closed' }
    | { type: 'base_currency_info' }
    | { type: 'copy_address_toast'; address: Web3.address.Address }

export const Modal = ({ state, onMsg }: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'base_currency_info':
            return <BaseCurrencyInfo onMsg={onMsg} />
        case 'copy_address_toast':
            return (
                <UIModal>
                    <ToastContainer navOffset>
                        <Toast>
                            <Tick size={20} color="backgroundLight" />
                            <ToastText>
                                <FormattedMessage
                                    id="accounts.view.copied-text"
                                    defaultMessage="Copied {formattedAddress}"
                                    values={{
                                        formattedAddress: Web3.address.format(
                                            state.address
                                        ),
                                    }}
                                />
                            </ToastText>
                        </Toast>
                    </ToastContainer>
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
