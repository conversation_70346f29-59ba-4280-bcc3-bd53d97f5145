import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    ConfiguredEarn,
    Earnings,
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
    TakerPortfolioMap2,
    TotalEarningsInDefaultCurrencyMap,
} from '@zeal/domains/Earn'
import { fetchTakerPortfolioMap } from '@zeal/domains/Earn/api/fetchTakerPortfolioMap'
import {
    fetchTakerTransactionsMap,
    TakerTransactionsMap,
} from '@zeal/domains/Earn/api/fetchTakerTransactionsMap'
import { calculateTotalEarningsInDefaultCurrency } from '@zeal/domains/Earn/helpers/calculateTotalEarningsInDefaultCurrency'
import { shouldCelebrateTaker } from '@zeal/domains/Earn/helpers/shouldCelbrateTaker'
import { sumTakerPortfolio } from '@zeal/domains/Earn/helpers/sumEarn'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { FiatMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { UserEvent } from '@zeal/domains/UserEvents'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    owner: Account
    earn: ConfiguredEarn
    totalEarningsInDefaultCurrencyMap: TotalEarningsInDefaultCurrencyMap
    accounts: AccountsMap
    keystores: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    installationId: string
    portfolioMap: PortfolioMap
    isEthereumNetworkFeeWarningSeen: boolean
    networkRPCMap: NetworkRPCMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    customCurrencies: CustomCurrencyMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    cardConfig: CardConfig
    earnTakerMetrics: EarnTakerMetrics
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    location: Extract<UserEvent, { type: 'EarnFlowEnteredEvent' }>['location']
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | {
          type: 'on_earnings_fetched'
          address: Web3.address.Address
          newRates: HistoricalTakerUserCurrencyRateMap
          totalEarningsInDefaultCurrency: Earnings<FiatMoney> | null
      }
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'add_wallet_clicked'
                  | 'on_account_create_request'
                  | 'hardware_wallet_clicked'
                  | 'import_keys_button_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_asset_added_to_earn'
                  | 'on_earn_deposit_success'
                  | 'on_earn_withdrawal_success'
                  | 'on_predefined_fee_preset_selected'
                  | 'safe_wallet_clicked'
                  | 'track_wallet_clicked'
                  | 'on_recharge_configured'
                  | 'on_external_earn_deposit_completed_close_click'
                  | 'on_earn_taker_address_click'
                  | 'on_bank_transfer_selected'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'cancel_submitted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_earn_configured'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
                  | 'on_app_rating_submitted'
                  | 'on_earn_celebration_triggered'
                  | 'on_swaps_io_swap_request_created'
          }
      >

const fetch = async ({
    cache,
    defaultCurrencyConfig,
    earn,
    networkMap,
    networkRPCMap,
    earnOwnerAddress,
    signal,
}: {
    earn: ConfiguredEarn
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    cache: HistoricalTakerUserCurrencyRateMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnOwnerAddress: Web3.address.Address
    signal?: AbortSignal
}): Promise<{
    takerTransactionsMap: TakerTransactionsMap
    takerPortfolioMap: TakerPortfolioMap2
    takerHistoricalUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
}> => {
    const [transactions, takerPortfolioMap] = await Promise.all([
        fetchTakerTransactionsMap({
            cache,
            earn,
            networkRPCMap,
            networkMap,
            signal,
        }),
        fetchTakerPortfolioMap({
            defaultCurrencyConfig,
            networkMap,
            networkRPCMap,
            signal,
            earnOwnerAddress,
        }),
    ])

    return {
        takerHistoricalUserCurrencyRateMap:
            transactions.takerHistoricalUserCurrencyRateMap,
        takerTransactionsMap: transactions.takerTransactionsMap,
        takerPortfolioMap,
    }
}

const EARNINGS_POLLING_INTERVAL_MS = 30_000

const calculateModalState = ({
    takerPortfolioMap,
    takerTransactionsMap,
    celebrationConfig,
    owner,
    earn,
    defaultCurrencyConfig,
}: {
    celebrationConfig: CelebrationConfig
    earn: ConfiguredEarn
    takerTransactionsMap: TakerTransactionsMap
    takerPortfolioMap: TakerPortfolioMap2
    owner: Account
    defaultCurrencyConfig: DefaultCurrencyConfig
}): ModalState => {
    switch (ZealPlatform.OS) {
        case 'web':
            return { type: 'closed' }
        case 'ios':
        case 'android':
            const earnCelebrationConfig = celebrationConfig.earn[
                owner.address
            ] || {
                highestTotalEarningCelebratedInUserCurrency: {},
            }
            const takersToCelebrate = earn.takers
                .map((taker) => {
                    switch (taker.state) {
                        case 'not_deployed':
                            return null
                        case 'deployed':
                            const result = shouldCelebrateTaker({
                                takerType: taker.type,
                                celebrationConfig: earnCelebrationConfig,
                                takerPortfolioMap,
                                transactionsMap: takerTransactionsMap,
                            })

                            switch (result.type) {
                                case 'should_not_celebrate':
                                    return null
                                case 'should_celebrate':
                                    earnCelebrationConfig.highestTotalEarningCelebratedInUserCurrency[
                                        taker.type
                                    ] = result.totalEarningsInUserCurrency
                                    return {
                                        taker,
                                        totalTakerEarningsInUserCurrency:
                                            result.totalEarningsInUserCurrency,
                                    }
                                /* istanbul ignore next */
                                default:
                                    return notReachable(result)
                            }
                        /* istanbul ignore next */
                        default:
                            return notReachable(taker)
                    }
                })
                .filter(excludeNullValues)

            if (!takersToCelebrate.length) {
                return { type: 'closed' }
            }

            const takerToCelebrate = takersToCelebrate.reduce((acc, value) => {
                const valueMoney = sumTakerPortfolio({
                    takerPortfolioMap,
                    taker: value.taker,
                    defaultCurrencyConfig,
                })
                const accMoney = sumTakerPortfolio({
                    takerPortfolioMap,
                    taker: acc.taker,
                    defaultCurrencyConfig,
                })

                return valueMoney.amount > accMoney.amount ? value : acc
            }, takersToCelebrate[0])

            return {
                type: 'earn_celebration',
                takerToCelebrate,
                takerPortfolioMap,
                earnCelebrationConfig,
            }

        /* istanbul ignore next */
        default:
            return notReachable(ZealPlatform)
    }
}

export const ConfiguredEarnWidget = ({
    earn,
    totalEarningsInDefaultCurrencyMap,
    sessionPassword,
    networkRPCMap,
    networkMap,
    customCurrencies,
    feePresetMap,
    gasCurrencyPresetMap,
    earnHistoricalTakerUserCurrencyRateMap,
    accounts,
    earnTakerMetrics,
    owner,
    keystores,
    isEthereumNetworkFeeWarningSeen,
    installationId,
    cardConfig,
    currencyPinMap,
    currencyHiddenMap,
    portfolioMap,
    location,
    defaultCurrencyConfig,
    appRating,
    celebrationConfig,
    onMsg,
}: Props) => {
    const [pollable, setPollable] = usePollableData(
        fetch,
        {
            type: 'loading',
            params: {
                earn,
                cache: earnHistoricalTakerUserCurrencyRateMap,
                networkMap,
                earnOwnerAddress: owner.address,
                networkRPCMap,
                defaultCurrencyConfig,
            },
        },
        {
            stopIf: () => false,
            pollIntervalMilliseconds: EARNINGS_POLLING_INTERVAL_MS,
        }
    )

    const [modal, setModal] = useState<ModalState>({ type: 'closed' })
    const liveMsg = useLiveRef(onMsg)
    const liveModal = useLiveRef(modal)
    const liveCelebrationConfig = useLiveRef(celebrationConfig)
    const liveEarn = useLiveRef(earn)
    const captureErrorOnce = useCaptureErrorOnce()

    useEffect(() => {
        switch (pollable.type) {
            case 'loaded':
                switch (liveModal.current.type) {
                    case 'closed':
                        setModal(
                            calculateModalState({
                                takerPortfolioMap:
                                    pollable.data.takerPortfolioMap,
                                takerTransactionsMap:
                                    pollable.data.takerTransactionsMap,
                                celebrationConfig:
                                    liveCelebrationConfig.current,
                                owner,
                                earn: liveEarn.current,
                                defaultCurrencyConfig,
                            })
                        )
                        break
                    case 'view_earn':
                    case 'earn_celebration':
                        break
                    /* istanbul ignore next */
                    default:
                        return notReachable(liveModal.current)
                }
                break
            case 'reloading':
            case 'subsequent_failed':
            case 'loading':
            case 'error':
                break
            /* istanbul ignore next */
            default:
                return notReachable(pollable)
        }
    }, [
        pollable,
        liveModal,
        liveCelebrationConfig,
        liveEarn,
        owner,
        defaultCurrencyConfig,
    ])

    useEffect(() => {
        switch (pollable.type) {
            case 'loading':
            case 'reloading':
                break
            case 'loaded': {
                liveMsg.current({
                    type: 'on_earnings_fetched',
                    address: owner.address,
                    newRates: pollable.data.takerHistoricalUserCurrencyRateMap,
                    totalEarningsInDefaultCurrency:
                        calculateTotalEarningsInDefaultCurrency({
                            takerPortfolioMap: pollable.data.takerPortfolioMap,
                            transactionsMap: pollable.data.takerTransactionsMap,
                        }),
                })
                break
            }
            case 'error':
            case 'subsequent_failed':
                captureErrorOnce(pollable.error)
                break
            /* istanbul ignore next */
            default:
                return notReachable(pollable)
        }
    }, [captureErrorOnce, liveMsg, pollable, owner.address])

    return (
        <>
            <Layout
                defaultCurrencyConfig={defaultCurrencyConfig}
                pollable={pollable}
                owner={owner}
                earn={earn}
                totaleEarningsInDefaultCurrencyMap={
                    totalEarningsInDefaultCurrencyMap
                }
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_earn_clicked':
                            setModal({ type: 'view_earn' })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg.type)
                    }
                }}
            />
            <Modal
                appRating={appRating}
                celebrationConfig={celebrationConfig}
                earnTakerMetrics={earnTakerMetrics}
                defaultCurrencyConfig={defaultCurrencyConfig}
                customCurrencies={customCurrencies}
                portfolioMap={portfolioMap}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                totalEarningsInDefaultCurrencyMap={
                    totalEarningsInDefaultCurrencyMap
                }
                earn={earn}
                cardConfig={cardConfig}
                accounts={accounts}
                keystores={keystores}
                networkMap={networkMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                sessionPassword={sessionPassword}
                installationId={installationId}
                networkRPCMap={networkRPCMap}
                currencyPinMap={currencyPinMap}
                currencyHiddenMap={currencyHiddenMap}
                owner={owner}
                state={modal}
                pollable={pollable}
                location={location}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_earn_withdrawal_success':
                        case 'on_earn_deposit_success':
                            // on deposit or withdraw you need to give at least 3-5 secons for inderxer to index transaciton
                            setTimeout(() => {
                                setPollable((old) => {
                                    switch (old.type) {
                                        case 'loaded':
                                        case 'reloading':
                                        case 'subsequent_failed':
                                            return {
                                                type: 'reloading',
                                                params: old.params,
                                                data: old.data,
                                            }
                                        case 'loading':
                                        case 'error':
                                            return {
                                                type: 'loading',
                                                params: old.params,
                                            }
                                        default:
                                            return notReachable(old)
                                    }
                                })
                            }, 3_000)
                            onMsg(msg)
                            break
                        case 'on_earn_configured':
                            setPollable((old) => {
                                switch (old.type) {
                                    case 'loaded':
                                    case 'reloading':
                                    case 'subsequent_failed':
                                        return {
                                            type: 'reloading',
                                            params: old.params,
                                            data: old.data,
                                        }
                                    case 'loading':
                                    case 'error':
                                        return {
                                            type: 'loading',
                                            params: old.params,
                                        }
                                    default:
                                        return notReachable(old)
                                }
                            })
                            onMsg(msg)
                            break
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_recharge_configured':
                        case 'on_earn_taker_address_click':
                        case 'add_wallet_clicked':
                        case 'on_bank_transfer_selected':
                        case 'track_wallet_clicked':
                        case 'on_account_create_request':
                        case 'on_accounts_create_success_animation_finished':
                        case 'hardware_wallet_clicked':
                        case 'on_add_label_to_track_only_account_during_send':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'cancel_submitted':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_request_replaced':
                        case 'transaction_submited':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_address_scanned':
                        case 'on_address_scanned_and_add_label':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_chf_taker_metrics_loaded':
                        case 'on_app_rating_submitted':
                        case 'on_earn_celebration_triggered':
                        case 'on_swaps_io_swap_request_created':
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
