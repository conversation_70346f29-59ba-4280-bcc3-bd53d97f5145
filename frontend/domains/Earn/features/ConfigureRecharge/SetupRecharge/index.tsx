import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardBalance,
    CardSlientSignKeyStore,
    ReadonlySignerSelectedOnboardedCardConfig,
    RechargePreferences,
} from '@zeal/domains/Card'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { Earn, EarnTakerMetrics } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { UserEvent } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { CreateRechargePreferences } from './CreateRechargePreferences'
import { EnableRecharge } from './EnableRecharge'

type Msg =
    | { type: 'close' }
    | MsgOf<typeof EnableRecharge>
    | Extract<
          MsgOf<typeof CreateRechargePreferences>,
          {
              type:
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
          }
      >

type Props = {
    earn: Earn
    portfolioMap: PortfolioMap
    keyStore: CardSlientSignKeyStore
    keyStoreMap: KeyStoreMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    cardBalance: CardBalance | null
    networkRPCMap: NetworkRPCMap
    sessionPassword: string

    accountsMap: AccountsMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    location: Extract<
        UserEvent,
        { type: 'RechargeConfigFlowEnteredEvent' }
    >['location']
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnTakerMetrics: EarnTakerMetrics

    onMsg: (msg: Msg) => void
}

type State =
    | { type: 'create_recharge_preferences' }
    | {
          type: 'enable_recharge'
          rechargePreferences: RechargePreferences
      }

export const SetupRecharge = ({
    earn,
    keyStore,
    cardBalance,
    cardConfig,
    installationId,
    location,
    portfolioMap,
    networkMap,
    networkRPCMap,
    sessionPassword,
    feePresetMap,
    gasCurrencyPresetMap,
    keyStoreMap,
    accountsMap,
    defaultCurrencyConfig,
    earnTakerMetrics,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'create_recharge_preferences',
    })

    useEffect(() => {
        postUserEvent({
            type: 'RechargeConfigFlowEnteredEvent',
            rechargeState: 'off',
            installationId,
            location,
        })
    }, [installationId, location])

    switch (state.type) {
        case 'create_recharge_preferences':
            return (
                <CreateRechargePreferences
                    earn={earn}
                    cardBalance={cardBalance}
                    cardConfig={cardConfig}
                    installationId={installationId}
                    portfolioMap={portfolioMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    earnTakerMetrics={earnTakerMetrics}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_dont_link_account_clicked':
                            case 'on_zero_percent_per_year_selected':
                                onMsg({ type: 'close' })
                                break
                            case 'recharge_preferences_created':
                                setState({
                                    type: 'enable_recharge',
                                    rechargePreferences:
                                        msg.rechargePreferences,
                                })
                                break
                            case 'on_eur_taker_metrics_loaded':
                            case 'on_usd_taker_metrics_loaded':
                            case 'on_chf_taker_metrics_loaded':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'enable_recharge':
            return (
                <EnableRecharge
                    cardConfig={cardConfig}
                    earn={earn}
                    keyStore={keyStore}
                    keyStoreMap={keyStoreMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    rechargePreferences={state.rechargePreferences}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    accountsMap={accountsMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
