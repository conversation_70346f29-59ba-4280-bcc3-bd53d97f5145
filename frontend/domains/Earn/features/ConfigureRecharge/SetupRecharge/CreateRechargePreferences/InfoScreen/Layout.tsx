import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { CombinedTiles, Tile } from '@zeal/uikit/HomeTile'
import { ArrowDown } from '@zeal/uikit/Icon/ArrowDown'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { RechargeLightningRotated } from '@zeal/uikit/Icon/RechargeLightningRotated'
import { VisaLogo } from '@zeal/uikit/Icon/VisaLogo'
import { IconButton } from '@zeal/uikit/IconButton'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { getFormattedPercentage } from '@zeal/toolkit/Percentage'

import {
    CardBalance,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import {
    ConfiguredEarn,
    DeployedTaker,
    EURTakerType,
    NotConfiguredEarn,
    USDTakerType,
} from '@zeal/domains/Earn'
import { getTakerLabel } from '@zeal/domains/Earn/helpers/getTakerLabel'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { SupportButton } from '@zeal/domains/Support/features/SupportButton'

type Props = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    cardBalance: CardBalance | null
    installationId: string
    onMsg: (msg: Msg) => void
    rechargeEarnPreferences: RechargeEarnPreferences
}

type Msg =
    | {
          type: 'on_choose_earn_clicked'
          earn: NotConfiguredEarn
          takerType: USDTakerType | EURTakerType | null
      }
    | {
          type: 'on_continue_clicked'
          selectedTaker: RechargeEarnPreferences
      }
    | {
          type: 'on_dont_link_account_clicked'
      }
    | {
          type: 'close'
      }

export type RechargeEarnPreferences =
    | {
          type: 'taker_for_configured_earn'
          taker: DeployedTaker
          earn: ConfiguredEarn
      }
    | RechargeEarnPreferencesNotYetConfigured

export type RechargeEarnPreferencesNotYetConfigured = {
    type: 'taker_for_not_configured_earn'
    takerType: USDTakerType | EURTakerType | null
    earn: NotConfiguredEarn
}

export const Layout = ({
    cardConfig,
    cardBalance,
    installationId,
    onMsg,
    rechargeEarnPreferences,
}: Props) => {
    return (
        <Screen
            padding="form"
            background="default"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ScrollContainer withFloatingActions={false}>
                <Column spacing={24} fill>
                    <ActionBar
                        right={
                            <SupportButton
                                variant={{
                                    type: 'intercom_and_zendesk',
                                    cardConfig,
                                }}
                                layoutVariant="icon_button"
                                installationId={installationId}
                                location="gnosis_card_order"
                            />
                        }
                        left={
                            <IconButton
                                variant="on_light"
                                onClick={() => onMsg({ type: 'close' })}
                            >
                                {({ color }) => (
                                    <BackIcon size={24} color={color} />
                                )}
                            </IconButton>
                        }
                    />
                    <HeaderV2
                        size="large"
                        align="left"
                        title={(() => {
                            switch (rechargeEarnPreferences.earn.type) {
                                case 'not_configured':
                                    return (
                                        <FormattedMessage
                                            id="create_recharge_preferences.not_configured_title"
                                            defaultMessage="Earn & Spend"
                                        />
                                    )
                                case 'configured':
                                    return (
                                        <FormattedMessage
                                            id="create_recharge_preferences.link_accounts_title"
                                            defaultMessage="Link accounts"
                                        />
                                    )
                                /* istanbul ignore next */
                                default:
                                    return notReachable(
                                        rechargeEarnPreferences.earn
                                    )
                            }
                        })()}
                        subtitle={null}
                    />
                    <CombinedTiles
                        left={(() => {
                            switch (rechargeEarnPreferences.type) {
                                case 'taker_for_configured_earn':
                                    return (
                                        <Tile
                                            variant="blue"
                                            label={({
                                                variant,
                                                color,
                                                weight,
                                            }) => {
                                                return (
                                                    <Text
                                                        variant={variant}
                                                        color={color}
                                                        weight={weight}
                                                    >
                                                        <FormattedMessage
                                                            id="create_recharge_preferences.earn_account.label"
                                                            defaultMessage="{label} {apy}"
                                                            values={{
                                                                label: getTakerLabel(
                                                                    rechargeEarnPreferences
                                                                        .taker
                                                                        .type
                                                                ),
                                                                apy: getFormattedPercentage(
                                                                    rechargeEarnPreferences
                                                                        .earn
                                                                        .takerApyMap[
                                                                        rechargeEarnPreferences
                                                                            .taker
                                                                            .type
                                                                    ]
                                                                ),
                                                            }}
                                                        />
                                                    </Text>
                                                )
                                            }}
                                            title={(() => {
                                                const takerPortfolio =
                                                    rechargeEarnPreferences.earn
                                                        .takerPortfolioMap[
                                                        rechargeEarnPreferences
                                                            .taker.type
                                                    ]
                                                return (
                                                    <FormattedMoneyPrecise
                                                        sign={null}
                                                        withSymbol={true}
                                                        money={applyRate2({
                                                            baseAmount:
                                                                takerPortfolio.assetBalance,
                                                            rate: takerPortfolio.userCurrencyRate,
                                                        })}
                                                    />
                                                )
                                            })()}
                                            onClick={null}
                                        />
                                    )
                                case 'taker_for_not_configured_earn':
                                    return (
                                        <EarnOptionTile
                                            rechargeEarnPreferences={
                                                rechargeEarnPreferences
                                            }
                                            onMsg={onMsg}
                                        />
                                    )
                                /* istanbul ignore next */
                                default:
                                    return notReachable(rechargeEarnPreferences)
                            }
                        })()}
                        centerIcon={({ size }) => (
                            <RechargeLightningRotated
                                size={size}
                                color="gray20"
                            />
                        )}
                        right={
                            <Tile
                                variant="green"
                                label={({ variant, color, weight }) => (
                                    <Row spacing={4} alignY="center">
                                        <Tile.SvgLabel variant={variant}>
                                            {({ size }) => (
                                                <VisaLogo
                                                    height={size}
                                                    color={color}
                                                />
                                            )}
                                        </Tile.SvgLabel>
                                        <Text
                                            variant={variant}
                                            color={color}
                                            weight={weight}
                                        >
                                            <FormattedMessage
                                                id="create_recharge_preferences.card"
                                                defaultMessage="Card"
                                            />
                                        </Text>
                                    </Row>
                                )}
                                title={
                                    cardBalance && (
                                        <FormattedMoneyPrecise
                                            money={cardBalance.total}
                                            sign={null}
                                            withSymbol={true}
                                        />
                                    )
                                }
                                onClick={null}
                            />
                        }
                    />
                    <Text color="gray20" variant="callout" weight="medium">
                        {(() => {
                            switch (rechargeEarnPreferences.type) {
                                case 'taker_for_not_configured_earn':
                                    switch (rechargeEarnPreferences.takerType) {
                                        case null:
                                            return (
                                                <FormattedMessage
                                                    id="create_recharge_preferences.no_recharge_from_earn_accounts_description"
                                                    defaultMessage="Your card will NOT recharge automatically after each payment."
                                                />
                                            )
                                        case 'eur':
                                        case 'usd':
                                            return (
                                                <FormattedMessage
                                                    id="create_recharge_preferences.recharge_from_earn_accounts_description"
                                                    defaultMessage="Your card recharges automatically after each payment from your Earn account."
                                                />
                                            )

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(
                                                rechargeEarnPreferences.takerType
                                            )
                                    }
                                    break
                                case 'taker_for_configured_earn':
                                    return (
                                        <FormattedMessage
                                            id="create_recharge_preferences.recharge_from_earn_accounts_description"
                                            defaultMessage="Your card recharges automatically after each payment from your Earn account."
                                        />
                                    )
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(rechargeEarnPreferences)
                            }
                        })()}
                    </Text>
                </Column>
            </ScrollContainer>
            <Actions variant="default" direction="column">
                {(() => {
                    switch (rechargeEarnPreferences.earn.type) {
                        case 'not_configured':
                            return null
                        case 'configured':
                            return (
                                <Button
                                    variant="secondary"
                                    size="regular"
                                    onClick={() =>
                                        onMsg({
                                            type: 'on_dont_link_account_clicked',
                                        })
                                    }
                                >
                                    <FormattedMessage
                                        id="gnosis-signup.dont_link_accounts"
                                        defaultMessage="Don’t link accounts"
                                    />
                                </Button>
                            )
                        /* istanbul ignore next */
                        default:
                            return notReachable(rechargeEarnPreferences.earn)
                    }
                })()}

                <Button
                    variant="primary"
                    size="regular"
                    onClick={() =>
                        onMsg({
                            // FIXME @katie change behavior on continue to remove selection screen
                            type: 'on_continue_clicked',
                            selectedTaker: rechargeEarnPreferences,
                        })
                    }
                >
                    <FormattedMessage
                        id="gnosis-signup.continue"
                        defaultMessage="Continue"
                    />
                </Button>
            </Actions>
        </Screen>
    )
}

const EarnOptionTile = ({
    onMsg,
    rechargeEarnPreferences,
}: {
    onMsg: (msg: Msg) => void
    rechargeEarnPreferences: RechargeEarnPreferencesNotYetConfigured
}) => {
    switch (rechargeEarnPreferences.takerType) {
        case null:
            return (
                <Tile
                    onClick={() =>
                        onMsg({
                            type: 'on_choose_earn_clicked',
                            earn: rechargeEarnPreferences.earn,
                            takerType: null,
                        })
                    }
                    variant="gray"
                    label={({ variant, color, weight }) => {
                        return (
                            <Row spacing={4}>
                                <Text
                                    variant={variant}
                                    color={color}
                                    weight={weight}
                                >
                                    <FormattedMessage
                                        id="create_recharge_preferences.hold_cash"
                                        defaultMessage="Hold cash"
                                    />
                                </Text>
                                <ArrowDown color="gray0" size={24} />
                            </Row>
                        )
                    }}
                    title={(() => {
                        return (
                            <FormattedMessage
                                id="create_recharge_preferences.earn_account.apy_percentage"
                                defaultMessage="{apy}"
                                values={{
                                    apy: getFormattedPercentage(0),
                                }}
                            />
                        )
                    })()}
                    subtitle={({ variant, color, weight }) => {
                        return (
                            <Text
                                variant={variant}
                                color={color}
                                weight={weight}
                            >
                                <FormattedMessage
                                    id="create_recharge_preferences.subtitle"
                                    defaultMessage="per year"
                                />
                            </Text>
                        )
                    }}
                />
            )
        case 'eur':
        case 'usd':
            return (
                <Tile
                    onClick={() =>
                        onMsg({
                            type: 'on_choose_earn_clicked',
                            earn: rechargeEarnPreferences.earn,
                            takerType: rechargeEarnPreferences.takerType,
                        })
                    }
                    variant="blue"
                    label={({ variant, color, weight }) => {
                        return (
                            <Row spacing={4}>
                                <Text
                                    variant={variant}
                                    color={color}
                                    weight={weight}
                                >
                                    {rechargeEarnPreferences.takerType ===
                                    'eur' ? (
                                        <FormattedMessage
                                            id="create_recharge_preferences.earn_account.earn_label"
                                            defaultMessage="Earn {label}"
                                            values={{
                                                label: FIAT_CURRENCIES['EUR']
                                                    .name,
                                            }}
                                        />
                                    ) : (
                                        <FormattedMessage
                                            id="create_recharge_preferences.earn_account.earn_label"
                                            defaultMessage="Earn {label}"
                                            values={{
                                                label: FIAT_CURRENCIES['USD']
                                                    .name,
                                            }}
                                        />
                                    )}
                                </Text>
                                <ArrowDown color={color} size={24} />
                            </Row>
                        )
                    }}
                    title={(() => (
                        <FormattedMessage
                            id="create_recharge_preferences.earn_account.apy_percentage"
                            defaultMessage="{apy}"
                            values={{
                                apy: getFormattedPercentage(
                                    rechargeEarnPreferences.earn.takerApyMap[
                                        rechargeEarnPreferences.takerType
                                    ]
                                ),
                            }}
                        />
                    ))()}
                    subtitle={({ variant, color, weight }) => {
                        return (
                            <Text
                                variant={variant}
                                color={color}
                                weight={weight}
                            >
                                <FormattedMessage
                                    id="create_recharge_preferences.subtitle"
                                    defaultMessage="per year"
                                />
                            </Text>
                        )
                    }}
                />
            )

        /* istanbul ignore next */
        default:
            notReachable(rechargeEarnPreferences.takerType)
    }
}
