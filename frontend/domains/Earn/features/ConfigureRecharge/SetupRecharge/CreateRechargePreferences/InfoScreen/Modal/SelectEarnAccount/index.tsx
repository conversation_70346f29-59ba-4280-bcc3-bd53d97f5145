import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import {
    EarnTakerMetrics,
    EURTakerType,
    NotConfiguredEarn,
    USDTakerType,
} from '@zeal/domains/Earn'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    earn: NotConfiguredEarn
    installationId: string
    onMsg: (msg: Msg) => void
    defaultSelectedTakerType: USDTakerType | EURTakerType | null
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnTakerMetrics: EarnTakerMetrics
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
}
type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_earn_account_selected'
                  | 'on_zero_percent_per_year_selected'
                  | 'close'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
          }
      >

export const SelectEarnAccount = ({
    onMsg,
    earn,
    installationId,
    defaultSelectedTakerType,
    cardConfig,
    defaultCurrencyConfig,
    earnTakerMetrics,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })
    return (
        <>
            <Layout
                earn={earn}
                installationId={installationId}
                cardConfig={cardConfig}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_earn_account_selected':
                        case 'on_zero_percent_per_year_selected':
                        case 'close':
                            onMsg(msg)
                            break
                        case 'on_taker_info_clicked':
                            setModal({
                                type: 'taker_investment_details',
                                taker: msg.taker,
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
                defaultSelectedTakerType={defaultSelectedTakerType}
            />
            <Modal
                state={modal}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
                defaultCurrencyConfig={defaultCurrencyConfig}
                installationId={installationId}
                earn={earn}
                earnTakerMetrics={earnTakerMetrics}
            />
        </>
    )
}
