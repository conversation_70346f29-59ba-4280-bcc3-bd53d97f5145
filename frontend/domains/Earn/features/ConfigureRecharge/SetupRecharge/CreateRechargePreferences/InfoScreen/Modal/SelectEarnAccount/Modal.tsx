import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import {
    EarnTakerMetrics,
    NotConfiguredEarn,
    NotDeployedTaker,
} from '@zeal/domains/Earn'
import { TakerInvestmentDetails } from '@zeal/domains/Earn/features/TakerInvestmentDetails'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    onMsg: (msg: Msg) => void
    state: State
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnTakerMetrics: EarnTakerMetrics
    installationId: string
    earn: NotConfiguredEarn
}
export type State =
    | { type: 'closed' }
    | { type: 'taker_investment_details'; taker: NotDeployedTaker }
type Msg = Extract<
    MsgOf<typeof TakerInvestmentDetails>,
    {
        type:
            | 'close'
            | 'on_usd_taker_metrics_loaded'
            | 'on_eur_taker_metrics_loaded'
    }
>

export const Modal = ({
    onMsg,
    state,
    defaultCurrencyConfig,
    installationId,
    earn,
    earnTakerMetrics,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'taker_investment_details':
            return (
                <UIModal>
                    <TakerInvestmentDetails
                        variant="just_information"
                        taker={state.taker}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        takerApyMap={earn.takerApyMap}
                        takerPortfolioMap={earn.takerPortfolioMap}
                        earnTakerMetrics={earnTakerMetrics}
                        installationId={installationId}
                        location="card_order_flow"
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                case 'on_usd_taker_metrics_loaded':
                                case 'on_eur_taker_metrics_loaded':
                                    onMsg(msg)
                                    break
                                case 'on_earn_withdrawal_asset_click':
                                case 'on_earn_deposit_asset_click':
                                    throw new ImperativeError(
                                        'not possible to withdraw or deposit to earn from card order flow'
                                    )
                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </UIModal>
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
