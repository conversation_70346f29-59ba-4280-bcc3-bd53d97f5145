import { StepWizard } from '@zeal/uikit/StepWizard'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import {
    CardBalance,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { EEA_COUNTRIES } from '@zeal/domains/Country/constants'
import { tryToGetUserCurrentCountry } from '@zeal/domains/Country/helpers/tryToGetUserCurrentCountry'
import {
    CHFTakerType,
    DeployedTaker,
    Earn,
    EarnTakerMetrics,
    EURTakerType,
    TakerType,
    USDTakerType,
} from '@zeal/domains/Earn'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { InfoScreen } from './InfoScreen'
import { RechargeEarnPreferences } from './InfoScreen/Layout'
import { SelectRechargeThreshold } from './SelectRechargeThreshold'

type Props = {
    portfolioMap: PortfolioMap
    earn: Earn
    installationId: string
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    cardBalance: CardBalance | null
    onMsg: (msg: Msg) => void
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnTakerMetrics: EarnTakerMetrics
}
type Msg =
    | Extract<
          MsgOf<typeof InfoScreen>,
          {
              type:
                  | 'on_dont_link_account_clicked'
                  | 'close'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
          }
      >
    | Extract<
          MsgOf<typeof SelectRechargeThreshold>,
          {
              type: 'recharge_preferences_created'
          }
      >
    | { type: 'on_zero_percent_per_year_selected' }

type State =
    | {
          type: 'info_screen'
          rechargeEarnPreferences: RechargeEarnPreferences
      }
    | {
          type: 'select_recharge_threshold'
          takerType: TakerType
      }

const getLocationBasedDefault = ():
    | USDTakerType
    | EURTakerType
    | CHFTakerType => {
    const userCountryResult = tryToGetUserCurrentCountry()

    switch (userCountryResult.type) {
        case 'Success':
            const countryCode = userCountryResult.data.code
            const isSwiss = countryCode === 'CH'
            const isEEA = EEA_COUNTRIES.includes(countryCode)
            return isSwiss ? 'chf' : isEEA ? 'eur' : 'usd'
        case 'Failure':
            return 'usd'
        // istanbul ignore next
        default:
            return notReachable(userCountryResult)
    }
}

const getRechargeEarnPreferences = (earn: Earn): RechargeEarnPreferences => {
    switch (earn.type) {
        case 'not_configured':
            return {
                type: 'taker_for_not_configured_earn',
                takerType: getLocationBasedDefault(),
                earn,
            }
        case 'configured':
            const taker = earn.takers
                .filter((taker): taker is DeployedTaker => {
                    switch (taker.state) {
                        case 'not_deployed':
                            return false
                        case 'deployed':
                            return true
                        /* istanbul ignore next */
                        default:
                            return notReachable(taker)
                    }
                })
                .reduce((highestValueTaker, taker) => {
                    const highestValueTakerPortfolio =
                        earn.takerPortfolioMap[highestValueTaker.type]
                    const takerPortfolio = earn.takerPortfolioMap[taker.type]

                    return highestValueTakerPortfolio.assetBalance.amount >
                        takerPortfolio.assetBalance.amount
                        ? highestValueTaker
                        : taker
                })

            return {
                type: 'taker_for_configured_earn',
                taker,
                earn,
            }
        /* istanbul ignore next */
        default:
            return notReachable(earn)
    }
}
export const CreateRechargePreferences = ({
    earn,
    cardConfig,
    cardBalance,
    installationId,
    onMsg,
    defaultCurrencyConfig,
    earnTakerMetrics,
}: Props) => {
    return (
        <StepWizard<State>
            initialStep={{
                type: 'info_screen',
                rechargeEarnPreferences: getRechargeEarnPreferences(earn),
            }}
        >
            {({ step, forwardTo, backTo }) => {
                switch (step.type) {
                    case 'info_screen':
                        return (
                            <InfoScreen
                                initialRechargeEarnPreferences={
                                    step.rechargeEarnPreferences
                                }
                                cardConfig={cardConfig}
                                cardBalance={cardBalance}
                                installationId={installationId}
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                earnTakerMetrics={earnTakerMetrics}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'on_continue_clicked':
                                            switch (msg.selectedTaker.type) {
                                                case 'taker_for_configured_earn':
                                                    forwardTo({
                                                        type: 'select_recharge_threshold',
                                                        takerType:
                                                            msg.selectedTaker
                                                                .taker.type,
                                                    })
                                                    break
                                                case 'taker_for_not_configured_earn':
                                                    if (
                                                        !msg.selectedTaker
                                                            .takerType
                                                    ) {
                                                        onMsg({
                                                            type: 'on_zero_percent_per_year_selected',
                                                        })
                                                        break
                                                    }

                                                    forwardTo({
                                                        type: 'select_recharge_threshold',
                                                        takerType:
                                                            msg.selectedTaker
                                                                .takerType,
                                                    })
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(
                                                        msg.selectedTaker
                                                    )
                                            }
                                            break
                                        case 'on_dont_link_account_clicked':
                                        case 'on_usd_taker_metrics_loaded':
                                        case 'on_eur_taker_metrics_loaded':
                                        case 'on_chf_taker_metrics_loaded':
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )

                    case 'select_recharge_threshold':
                        return (
                            <SelectRechargeThreshold
                                takerType={step.takerType}
                                cardConfig={cardConfig}
                                installationId={installationId}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'recharge_preferences_created':
                                            onMsg(msg)
                                            break
                                        case 'close': {
                                            switch (earn.type) {
                                                case 'configured':
                                                    backTo({
                                                        type: 'info_screen',
                                                        rechargeEarnPreferences:
                                                            getRechargeEarnPreferences(
                                                                earn
                                                            ),
                                                    })
                                                    break
                                                case 'not_configured':
                                                    switch (step.takerType) {
                                                        case 'eth':
                                                            backTo({
                                                                type: 'info_screen',
                                                                rechargeEarnPreferences:
                                                                    getRechargeEarnPreferences(
                                                                        earn
                                                                    ),
                                                            })
                                                            break
                                                        case 'usd':
                                                        case 'eur':
                                                        case 'chf':
                                                            backTo({
                                                                type: 'info_screen',
                                                                rechargeEarnPreferences:
                                                                    {
                                                                        type: 'taker_for_not_configured_earn',
                                                                        earn,
                                                                        takerType:
                                                                            step.takerType,
                                                                    },
                                                            })
                                                            break
                                                        default:
                                                            notReachable(
                                                                step.takerType
                                                            )
                                                            break
                                                    }

                                                    break
                                                default:
                                                    return notReachable(earn)
                                            }
                                            break
                                        }
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )
                    /* istanbul ignore next */
                    default:
                        return notReachable(step)
                }
            }}
        </StepWizard>
    )
}
