import { useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { Input } from '@zeal/uikit/Input'
import { FloatInput } from '@zeal/uikit/Input/FloatInput'
import { Screen } from '@zeal/uikit/Screen'
import { TagsRow } from '@zeal/uikit/TagsRow'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { failure, Result, success } from '@zeal/toolkit/Result'

import {
    ReadonlySignerSelectedOnboardedCardConfig,
    RechargePreferences,
} from '@zeal/domains/Card'
import { CryptoCurrency } from '@zeal/domains/Currency'
import { convertStableCoinCurrencyToFiatCurrency } from '@zeal/domains/Currency/helpers/convertStableCoinCurrencyToFiatCurrency'
import { TakerType } from '@zeal/domains/Earn'
import { SupportButton } from '@zeal/domains/Support/features/SupportButton'

type Props = {
    takerType: TakerType
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'recharge_preferences_created'
          rechargePreferences: RechargePreferences
      }
    | {
          type: 'close'
      }

const TAG_INPUT_AMOUNTS = ['50', '200', '600', '1000'] as const
const INPUT_FRACTION = 2

type Form = {
    threshold: string | null
}

type FormError = { type: 'enter_amount' }

const validateAmount = (
    form: Form,
    cryptoCurrency: CryptoCurrency
): Result<FormError, bigint> => {
    if (!form.threshold) {
        return failure({ type: 'enter_amount' })
    }

    const amount = fromFixedWithFraction(
        form.threshold,
        cryptoCurrency.fraction
    )

    if (amount <= 0n) {
        return failure({ type: 'enter_amount' })
    }

    return success(amount)
}

export const SelectRechargeThreshold = ({
    takerType,
    cardConfig,
    installationId,
    onMsg,
}: Props) => {
    const [form, setForm] = useState<Form>({ threshold: '200' })

    const formResult = validateAmount(form, cardConfig.currency)
    const formErrors = formResult.getFailureReason()

    const fiatCurrency = convertStableCoinCurrencyToFiatCurrency({
        cryptoCurrency: cardConfig.currency,
    })

    const onSubmitForm = () => {
        switch (formResult.type) {
            case 'Failure':
                return

            case 'Success': {
                const rechargePreferences: RechargePreferences = {
                    taker: takerType,
                    threshold: formResult.data,
                }

                onMsg({
                    type: 'recharge_preferences_created',
                    rechargePreferences,
                })
                break
            }

            default:
                return notReachable(formResult)
        }
    }

    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
                right={
                    <SupportButton
                        variant={{
                            type: 'intercom_and_zendesk',
                            cardConfig,
                        }}
                        layoutVariant="icon_button"
                        installationId={installationId}
                        location="select_recharge_threshold"
                    />
                }
            />

            <Column spacing={24} fill>
                <HeaderV2
                    size="large"
                    align="left"
                    title={
                        <FormattedMessage
                            id="selectRechargeThreshold.title"
                            defaultMessage="Set card target balance"
                        />
                    }
                    subtitle={null}
                />

                <Column spacing={16} fill>
                    <FloatInput
                        value={form.threshold}
                        prefix={fiatCurrency.symbol}
                        fraction={INPUT_FRACTION}
                        onChange={(value) => {
                            setForm({
                                ...form,
                                threshold: value,
                            })
                        }}
                    >
                        {({ value, onChange }) => (
                            <Input
                                variant="regular"
                                value={value}
                                state="normal"
                                type="text"
                                onChange={onChange}
                                onSubmitEditing={onSubmitForm}
                                keyboardType="numeric"
                                placeholder={`${fiatCurrency.symbol}0`}
                                autoFocus={false}
                                disabled={false}
                                returnKeyType="go"
                                autoCapitalize="none"
                                spellCheck={false}
                                autoComplete="off"
                            />
                        )}
                    </FloatInput>

                    <TagsRow>
                        {TAG_INPUT_AMOUNTS.map((value) => (
                            <TagsRow.TagButtonStretch
                                key={value}
                                onClick={() => {
                                    setForm({
                                        threshold: value,
                                    })
                                }}
                            >
                                <Text
                                    variant="callout"
                                    weight="regular"
                                    color="textPrimary"
                                >
                                    {`${fiatCurrency.symbol}${value}`}
                                </Text>
                            </TagsRow.TagButtonStretch>
                        ))}
                    </TagsRow>

                    <Column spacing={12}>
                        <Text
                            variant="paragraph"
                            weight="regular"
                            color="textPrimary"
                        >
                            <FormattedMessage
                                id="selectRechargeThreshold.description.line1"
                                defaultMessage="When your card drops below {amount}, it automatically recharges back to {amount} from your Earn account."
                                values={{
                                    amount: form.threshold
                                        ? `${fiatCurrency.symbol}${form.threshold}`
                                        : 'your target',
                                }}
                            />
                        </Text>

                        <Text
                            variant="paragraph"
                            weight="regular"
                            color="textPrimary"
                        >
                            <FormattedMessage
                                id="selectRechargeThreshold.description.line2"
                                defaultMessage="A lower target keeps more in your Earn account (earning 3%). You can change this anytime."
                            />
                        </Text>
                    </Column>
                </Column>

                <Actions variant="default">
                    <Button
                        variant="primary"
                        size="regular"
                        onClick={onSubmitForm}
                        disabled={!!formErrors}
                    >
                        {formErrors ? (
                            <FormattedMessage
                                id="selectRechargeThreshold.button.enterAmount"
                                defaultMessage="Enter amount"
                            />
                        ) : (
                            <FormattedMessage
                                id="selectRechargeThreshold.button.setTo"
                                defaultMessage="Set to {amount}"
                                values={{
                                    amount: `${fiatCurrency.symbol}${form.threshold}`,
                                }}
                            />
                        )}
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}
