import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardBalance,
    CardSlientSignKeyStore,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { ConfiguredEarn, Earn, EarnTakerMetrics } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { UserEvent } from '@zeal/domains/UserEvents'

import { SetupRecharge } from './SetupRecharge'
import { UpdateRecharge } from './UpdateRecharge'

type Props = {
    cardOwnerEarn: Earn
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    keyStore: CardSlientSignKeyStore

    portfolioMap: PortfolioMap
    keyStoreMap: KeyStoreMap
    cardBalance: CardBalance | null
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    accountsMap: AccountsMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    location: Extract<
        UserEvent,
        { type: 'RechargeConfigFlowEnteredEvent' }
    >['location']
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnTakerMetrics: EarnTakerMetrics
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_recharge_configured'
          earn: ConfiguredEarn
          earnOwnerAddress: Web3.address.Address
      }
    | Extract<
          MsgOf<typeof SetupRecharge>,
          {
              type:
                  | 'close'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
          }
      >
    | Extract<
          MsgOf<typeof UpdateRecharge>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
          }
      >

export const ConfigureRecharge = ({
    onMsg,
    cardOwnerEarn,
    installationId,
    location,
    defaultCurrencyConfig,
    earnTakerMetrics,
    keyStore,
    keyStoreMap,
    accountsMap,
    feePresetMap,
    portfolioMap,
    networkMap,
    networkRPCMap,
    gasCurrencyPresetMap,
    cardConfig,
    cardBalance,
    sessionPassword,
}: Props) => {
    switch (cardOwnerEarn.type) {
        case 'not_configured':
            return (
                <SetupRecharge
                    earn={cardOwnerEarn}
                    cardBalance={cardBalance}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    cardConfig={cardConfig}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    installationId={installationId}
                    sessionPassword={sessionPassword}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    feePresetMap={feePresetMap}
                    keyStore={keyStore}
                    earnTakerMetrics={earnTakerMetrics}
                    location={location}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_usd_taker_metrics_loaded':
                            case 'on_eur_taker_metrics_loaded':
                            case 'on_chf_taker_metrics_loaded':
                                onMsg(msg)
                                break
                            case 'on_recharge_setup_successfully':
                                onMsg({
                                    type: 'on_recharge_configured',
                                    earn: msg.earn,
                                    earnOwnerAddress:
                                        cardConfig.readonlySignerAddress,
                                })
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'configured':
            switch (cardOwnerEarn.cardRecharge.type) {
                case 'recharge_disabled':
                    return (
                        <SetupRecharge
                            earn={cardOwnerEarn}
                            cardBalance={cardBalance}
                            accountsMap={accountsMap}
                            keyStoreMap={keyStoreMap}
                            portfolioMap={portfolioMap}
                            cardConfig={cardConfig}
                            networkRPCMap={networkRPCMap}
                            networkMap={networkMap}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            installationId={installationId}
                            sessionPassword={sessionPassword}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            feePresetMap={feePresetMap}
                            keyStore={keyStore}
                            earnTakerMetrics={earnTakerMetrics}
                            location={location}
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'close':
                                    case 'on_predefined_fee_preset_selected':
                                    case 'on_usd_taker_metrics_loaded':
                                    case 'on_eur_taker_metrics_loaded':
                                    case 'on_chf_taker_metrics_loaded':
                                        onMsg(msg)
                                        break
                                    case 'on_recharge_setup_successfully':
                                        onMsg({
                                            type: 'on_recharge_configured',
                                            earn: msg.earn,
                                            earnOwnerAddress:
                                                cardConfig.readonlySignerAddress,
                                        })
                                        break
                                    default:
                                        return notReachable(msg)
                                }
                            }}
                        />
                    )
                case 'recharge_enabled':
                    return (
                        <UpdateRecharge
                            cardRecharge={cardOwnerEarn.cardRecharge}
                            earnHolderAddress={cardOwnerEarn.holder}
                            takerPortfolioMap={cardOwnerEarn.takerPortfolioMap}
                            takerApyMap={cardOwnerEarn.takerApyMap}
                            earnTakers={cardOwnerEarn.takers}
                            cardConfig={cardConfig}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            portfolioMap={portfolioMap}
                            accountsMap={accountsMap}
                            keystores={keyStoreMap}
                            networkMap={networkMap}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            sessionPassword={sessionPassword}
                            installationId={installationId}
                            networkRPCMap={networkRPCMap}
                            location={location}
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'close':
                                    case 'on_4337_auto_gas_token_selection_clicked':
                                    case 'on_4337_gas_currency_selected':
                                    case 'import_keys_button_clicked':
                                    case 'on_predefined_fee_preset_selected':
                                        onMsg(msg)
                                        break
                                    case 'on_earn_recharge_updated':
                                        onMsg({
                                            type: 'on_recharge_configured',
                                            earn: {
                                                ...cardOwnerEarn,
                                                cardRecharge: msg.cardRecharge,
                                            },
                                            earnOwnerAddress:
                                                cardConfig.readonlySignerAddress,
                                        })
                                        break
                                    default:
                                        return notReachable(msg)
                                }
                            }}
                        />
                    )
                default:
                    return notReachable(cardOwnerEarn.cardRecharge)
            }
        default:
            return notReachable(cardOwnerEarn)
    }
}
