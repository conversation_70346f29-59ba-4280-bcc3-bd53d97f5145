import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { BannerSolid } from '@zeal/uikit/BannerSolid'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { EarnEth } from '@zeal/uikit/Icon/EarnEth'
import { IconButton } from '@zeal/uikit/IconButton'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    EarnTakerMetrics,
    Taker,
    TakerApyMap,
    TakerPortfolioMap2,
} from '@zeal/domains/Earn'
import { EthTakerBulletPointsGroup } from '@zeal/domains/Earn/components/EthTakerBulletPointsGroup'
import { TakerInvestmentDetails } from '@zeal/domains/Earn/features/TakerInvestmentDetails'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    defaultCurrencyConfig: DefaultCurrencyConfig
    taker: Taker
    takerApyMap: TakerApyMap
    earnTakerMetrics: EarnTakerMetrics
    takerPortfolioMap: TakerPortfolioMap2
    installationId: string
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | { type: 'on_earn_deposit_asset_click'; taker: Taker }
    | { type: 'on_earn_withdrawal_asset_click'; taker: Taker }
    | { type: 'on_earn_taker_address_click'; taker: Taker }
    | MsgOf<typeof TakerInvestmentDetails>

export const EarnAssetDetails = ({
    defaultCurrencyConfig,
    taker,
    takerApyMap,
    installationId,
    takerPortfolioMap,
    earnTakerMetrics,
    onMsg,
}: Props) => {
    const { formatMessage } = useIntl()

    switch (taker.type) {
        case 'usd':
        case 'eur':
        case 'chf':
            return (
                <TakerInvestmentDetails
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    takerPortfolioMap={takerPortfolioMap}
                    taker={taker}
                    takerApyMap={takerApyMap}
                    earnTakerMetrics={earnTakerMetrics}
                    installationId={installationId}
                    location="earn_screen"
                    onMsg={onMsg}
                    variant="information_and_interaction"
                />
            )
        case 'eth':
            return (
                <Screen
                    padding="form"
                    background="light"
                    onNavigateBack={() => onMsg({ type: 'close' })}
                >
                    <ActionBar
                        left={
                            <IconButton
                                variant="on_light"
                                onClick={() => {
                                    onMsg({ type: 'close' })
                                }}
                            >
                                {({ color }) => (
                                    <BackIcon size={24} color={color} />
                                )}
                            </IconButton>
                        }
                    />
                    <Column spacing={8} fill shrink>
                        <Column spacing={16} fill shrink>
                            <Header
                                icon={({ size }) => <EarnEth size={size} />}
                                title={
                                    <FormattedMessage
                                        id="earn.config.currency.eth"
                                        defaultMessage="Earn Ethereum"
                                    />
                                }
                                subtitle={
                                    <Row spacing={4}>
                                        <Text
                                            variant="paragraph"
                                            weight="regular"
                                            color="textSecondary"
                                        >
                                            <FormattedMessage
                                                id="earn.config.currency.on_chain_address_subtitle"
                                                defaultMessage="Onchain address"
                                            />
                                        </Text>
                                        <Tertiary
                                            color="on_light"
                                            size="regular"
                                            onClick={() => {
                                                onMsg({
                                                    type: 'on_earn_taker_address_click',
                                                    taker,
                                                })
                                            }}
                                        >
                                            {() => (
                                                <Text
                                                    variant="paragraph"
                                                    weight="regular"
                                                    color="textSecondary"
                                                    textDecorationLine="underline"
                                                >
                                                    {Web3.address.format(
                                                        taker.address
                                                    )}
                                                </Text>
                                            )}
                                        </Tertiary>
                                    </Row>
                                }
                            />
                            <Column spacing={0} alignY="stretch">
                                <EthTakerBulletPointsGroup
                                    takerType={taker.type}
                                    takerApyMap={takerApyMap}
                                />
                                <BannerSolid
                                    variant="light"
                                    rounded
                                    title={
                                        <FormattedMessage
                                            id="earn.risk-banner.title"
                                            defaultMessage="Understand the risks"
                                        />
                                    }
                                    subtitle={
                                        <FormattedMessage
                                            id="earn.risk-banner.subtitle"
                                            defaultMessage="This is a self-custodial product with no regulatory protection against loss."
                                        />
                                    }
                                />
                            </Column>
                        </Column>
                        <Actions variant="default">
                            <Button
                                size="regular"
                                variant="secondary"
                                onClick={() => {
                                    postUserEvent({
                                        type: 'EarnWithdrawEarnFlowEnteredEvent',
                                        location: 'earn_asset_modal',
                                        asset: taker.type,
                                        installationId,
                                    })
                                    onMsg({
                                        type: 'on_earn_withdrawal_asset_click',
                                        taker,
                                    })
                                }}
                            >
                                {formatMessage({
                                    id: 'earn.withdraw',
                                    defaultMessage: 'Withdraw',
                                })}
                            </Button>
                            <Button
                                size="regular"
                                variant="primary"
                                onClick={() => {
                                    onMsg({
                                        type: 'on_earn_deposit_asset_click',
                                        taker,
                                    })
                                }}
                            >
                                {formatMessage({
                                    id: 'earn.deposit',
                                    defaultMessage: 'Deposit',
                                })}
                            </Button>
                        </Actions>
                    </Column>
                </Screen>
            )
        /* istanbul ignore next */
        default:
            return notReachable(taker.type)
    }
}
