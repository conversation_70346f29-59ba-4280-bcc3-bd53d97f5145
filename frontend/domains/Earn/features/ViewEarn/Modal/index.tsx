import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardConfig,
    CardSlientSignKeyStore,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    CardRechargeEnabled,
    ConfiguredEarn,
    EarnCelebrationConfigForOneAccount,
    EarnTakerMetrics,
    Taker,
    TakerPortfolioMap2,
    TakerToCelebrate,
} from '@zeal/domains/Earn'
import { ConfigureRecharge } from '@zeal/domains/Earn/features/ConfigureRecharge'
import { Deposit } from '@zeal/domains/Earn/features/Deposit'
import { EarnCelebration } from '@zeal/domains/Earn/features/EarnCelebration'
import { SelectAssetAndDeposit } from '@zeal/domains/Earn/features/SelectAssetAndDeposit'
import { Withdrawal } from '@zeal/domains/Earn/features/Withdrawal'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { ZealDApp } from '@zeal/domains/Main/features/ZealDApp'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { EarnAssetDetails } from './EarnAssetDetails'

type Props = {
    state: State
    earn: ConfiguredEarn
    owner: Account

    accounts: AccountsMap
    keystores: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    isEthereumNetworkFeeWarningSeen: boolean
    sessionPassword: string
    installationId: string
    portfolioMap: PortfolioMap
    cardConfig: CardConfig
    networkRPCMap: NetworkRPCMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    customCurrencies: CustomCurrencyMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnTakerMetrics: EarnTakerMetrics
    appRating: AppRating
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | MsgOf<typeof SelectAssetAndDeposit>
    | MsgOf<typeof EarnAssetDetails>
    | MsgOf<typeof Withdrawal>
    | MsgOf<typeof Deposit>
    | MsgOf<typeof ConfigureRecharge>
    | Extract<
          MsgOf<typeof ZealDApp>,
          { type: 'close' | 'on_external_earn_deposit_completed_close_click' }
      >
    | MsgOf<typeof EarnCelebration>

export type State =
    | { type: 'closed' }
    | { type: 'asset_details'; taker: Taker }
    | { type: 'add_another_asset' }
    | { type: 'withdraw_asset'; taker: Taker }
    | {
          type: 'update_recharge'
          cardConfig: ReadonlySignerSelectedOnboardedCardConfig
          cardRecharge: CardRechargeEnabled
          earn: ConfiguredEarn
          keyStore: CardSlientSignKeyStore
      }
    | {
          type: 'setup_recharge'
          cardConfig: ReadonlySignerSelectedOnboardedCardConfig
          keyStore: CardSlientSignKeyStore
      }
    | { type: 'deposit_assets_from_zeal_wallet'; taker: Taker }
    | {
          type: 'earn_celebration'
          takerPortfolioMap: TakerPortfolioMap2
          earnCelebrationConfig: EarnCelebrationConfigForOneAccount
          takerToCelebrate: TakerToCelebrate
      }

export const Modal = ({
    state,
    earn,
    installationId,
    keystores,
    networkMap,
    networkRPCMap,
    earnTakerMetrics,
    sessionPassword,
    gasCurrencyPresetMap,
    feePresetMap,
    accounts,
    owner,
    portfolioMap,
    currencyPinMap,
    cardConfig,
    currencyHiddenMap,
    customCurrencies,
    isEthereumNetworkFeeWarningSeen,
    defaultCurrencyConfig,
    appRating,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'earn_celebration':
            return (
                <UIModal>
                    <EarnCelebration
                        location="earn_details"
                        appRating={appRating}
                        takerPortfolioMap={state.takerPortfolioMap}
                        takerToCelebrate={state.takerToCelebrate}
                        onMsg={onMsg}
                        installationId={installationId}
                        owner={owner}
                        earnCelebrationConfig={state.earnCelebrationConfig}
                    />
                </UIModal>
            )
        case 'deposit_assets_from_zeal_wallet':
            return (
                <UIModal>
                    <Deposit
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        earn={earn}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        currencyPinMap={currencyPinMap}
                        currencyHiddenMap={currencyHiddenMap}
                        portfolioMap={portfolioMap}
                        taker={state.taker}
                        networkRPCMap={networkRPCMap}
                        sessionPassword={sessionPassword}
                        installationId={installationId}
                        keyStoreMap={keystores}
                        networkMap={networkMap}
                        cardConfig={cardConfig}
                        customCurrencies={customCurrencies}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        accountsMap={accounts}
                        owner={owner}
                        eventLocation="earn_screen"
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'withdraw_asset':
            return (
                <UIModal>
                    <Withdrawal
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        cardConfig={cardConfig}
                        currencyHiddenMap={currencyHiddenMap}
                        currencyPinMap={currencyPinMap}
                        earn={earn}
                        portfolioMap={portfolioMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        owner={owner}
                        taker={state.taker}
                        sessionPassword={sessionPassword}
                        installationId={installationId}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        accountsMap={accounts}
                        keystoreMap={keystores}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'asset_details':
            return (
                <UIModal>
                    <EarnAssetDetails
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        takerPortfolioMap={earn.takerPortfolioMap}
                        earnTakerMetrics={earnTakerMetrics}
                        taker={state.taker}
                        takerApyMap={earn.takerApyMap}
                        installationId={installationId}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'add_another_asset':
            return (
                <UIModal>
                    <SelectAssetAndDeposit
                        earnTakerMetrics={earnTakerMetrics}
                        variant="not_deployed_takers"
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        cardConfig={cardConfig}
                        customCurrencyMap={customCurrencies}
                        owner={owner}
                        earn={earn}
                        networkMap={networkMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        sessionPassword={sessionPassword}
                        installationId={installationId}
                        portfolioMap={portfolioMap}
                        networkRPCMap={networkRPCMap}
                        onMsg={onMsg}
                        accountsMap={accounts}
                        keyStoreMap={keystores}
                        currencyHiddenMap={currencyHiddenMap}
                        currencyPinMap={currencyPinMap}
                        eventLocation="earn_screen"
                    />
                </UIModal>
            )

        case 'setup_recharge':
        case 'update_recharge':
            const cachedPortfolio = unsafe_GetPortfolioCache2({
                address: state.cardConfig.readonlySignerAddress,
                portfolioMap,
            })

            return (
                <UIModal>
                    <ConfigureRecharge
                        cardOwnerEarn={earn}
                        cardConfig={state.cardConfig}
                        keyStore={state.keyStore}
                        cardBalance={cachedPortfolio?.cardBalance || null}
                        accountsMap={accounts}
                        keyStoreMap={keystores}
                        portfolioMap={portfolioMap}
                        networkRPCMap={networkRPCMap}
                        networkMap={networkMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        sessionPassword={sessionPassword}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        feePresetMap={feePresetMap}
                        earnTakerMetrics={earnTakerMetrics}
                        onMsg={onMsg}
                        location="earn_screen"
                    />
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
