import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Taker, TakerApyMap } from '@zeal/domains/Earn'

import { AaveTakerInvestmentTile } from './AaveTakerInvestmentTile'
import { FrankencoinTakerInvestmentTile } from './FrankencoinTakerInvestmentTile'
import { UsdTakerInvestmentTile } from './UsdTakerInvestmentTile'

type Props = {
    taker: Taker
    takerApyMap: TakerApyMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof UsdTakerInvestmentTile>
    | MsgOf<typeof AaveTakerInvestmentTile>

export const TakerInvestmentTile = ({
    taker,
    takerApyMap,
    installationId,
    onMsg,
}: Props) => {
    switch (taker.type) {
        case 'usd':
            return (
                <UsdTakerInvestmentTile
                    installationId={installationId}
                    taker={taker}
                    takerApyMap={takerApyMap}
                    onMsg={onMsg}
                />
            )
        case 'eur':
            return (
                <AaveTakerInvestmentTile
                    installationId={installationId}
                    taker={taker}
                    takerApyMap={takerApyMap}
                    onMsg={onMsg}
                />
            )
        case 'chf':
            return (
                <FrankencoinTakerInvestmentTile
                    installationId={installationId}
                    taker={taker}
                    takerApyMap={takerApyMap}
                    onMsg={onMsg}
                />
            )
        case 'eth':
            return null
        /* istanbul ignore next */
        default:
            return notReachable(taker.type)
    }
}
