import { ComponentProps, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { Account } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { Earn, EarnTakerMetrics, Taker } from '@zeal/domains/Earn'
import { SelectAssetToEarn } from '@zeal/domains/Earn/components/SelectAssetToEarn'
import { Deposit } from '@zeal/domains/Earn/features/Deposit'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { EarnEventLocation } from '@zeal/domains/UserEvents'

type State =
    | {
          type: 'select_asset_to_earn'
      }
    | {
          type: 'deploy_and_deposit_earn'
          taker: Taker
      }

type Props = {
    owner: Account

    variant: ComponentProps<typeof SelectAssetToEarn>['variant']
    earn: Earn
    accountsMap: AccountsMap
    isEthereumNetworkFeeWarningSeen: boolean
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    customCurrencyMap: CustomCurrencyMap
    cardConfig: CardConfig
    earnTakerMetrics: EarnTakerMetrics
    defaultCurrencyConfig: DefaultCurrencyConfig
    eventLocation: EarnEventLocation
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'close'
      }
    | MsgOf<typeof Deposit>
    | Extract<
          MsgOf<typeof SelectAssetToEarn>,
          {
              type:
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
          }
      >

export const SelectAssetAndDeposit = ({
    owner,
    variant,
    earn,
    accountsMap,
    keyStoreMap,
    networkMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    networkRPCMap,
    sessionPassword,
    portfolioMap,
    cardConfig,
    currencyHiddenMap,
    customCurrencyMap,
    earnTakerMetrics,
    isEthereumNetworkFeeWarningSeen,
    currencyPinMap,
    defaultCurrencyConfig,
    eventLocation,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'select_asset_to_earn' })

    switch (state.type) {
        case 'select_asset_to_earn':
            return (
                <SelectAssetToEarn
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    earnTakerMetrics={earnTakerMetrics}
                    installationId={installationId}
                    keyStore={getKeyStore({
                        address: owner.address,
                        keyStoreMap,
                    })}
                    earn={earn}
                    variant={variant}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_usd_taker_metrics_loaded':
                            case 'on_eur_taker_metrics_loaded':
                            case 'on_chf_taker_metrics_loaded':
                                onMsg(msg)
                                break
                            case 'on_earn_deposit_asset_click':
                                setState({
                                    type: 'deploy_and_deposit_earn',
                                    taker: msg.taker,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'deploy_and_deposit_earn':
            return (
                <Deposit
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    earn={earn}
                    currencyPinMap={currencyPinMap}
                    currencyHiddenMap={currencyHiddenMap}
                    customCurrencies={customCurrencyMap}
                    portfolioMap={portfolioMap}
                    taker={state.taker}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    cardConfig={cardConfig}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    accountsMap={accountsMap}
                    owner={owner}
                    eventLocation={eventLocation}
                    onMsg={onMsg}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
