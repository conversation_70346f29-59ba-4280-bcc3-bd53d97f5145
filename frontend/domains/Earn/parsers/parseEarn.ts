import { notReachable } from '@zeal/toolkit'
import { keys, mapValues } from '@zeal/toolkit/Object'
import {
    arrayOf,
    bigint,
    failure,
    match,
    nonEmptyArray,
    nullableOf,
    number,
    numberString,
    Obj,
    object,
    oneOf,
    recordOf,
    recordStrict,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    CryptoCurrency,
    Currency,
    DefaultCurrency,
} from '@zeal/domains/Currency'
import {
    FIAT_CURRENCIES,
    GNOSIS_AAVE_EURE,
    GNOSIS_SDAI,
    GNOSIS_SVZCHF,
    GNOSIS_WSTETH,
} from '@zeal/domains/Currency/constants'
import {
    parse as parseCurrency,
    parseCryptoCurrency,
    parseFiatCurrency,
} from '@zeal/domains/Currency/helpers/parse'
import {
    AaveEureTakerMetrics,
    CHFEarnTakerMetrics,
    DeployedTaker,
    Earn,
    HistoricalTakerUserCurrencyRateMap,
    NotDeployedTaker,
    Taker,
    Taker<PERSON>pyMap,
    TakerPort<PERSON>lio,
    TakerPortfolioMap2,
    UsdEarnTakerMetrics,
} from '@zeal/domains/Earn'
import {
    DEFAULT_TAKER_APY_MAP,
    EARN_PRIMARY_INVESTMENT_ASSETS_MAP,
} from '@zeal/domains/Earn/constants'
import { FXRate2 } from '@zeal/domains/FXRate'
import {
    parseCryptoMoneyFromStorage,
    parseFiatMoneyFromStorage,
} from '@zeal/domains/Money/helpers/parse'
import { ETHEREUM } from '@zeal/domains/Network/constants'

const TAKER_TYPE_MAP: Record<Taker['type'], true> = {
    usd: true,
    eur: true,
    eth: true,
    chf: true,
}

const _dont_forget = (earn: Earn) => {
    switch (earn.type) {
        case 'not_configured':
        case 'configured':
            // !! don't forget to add EARN parser
            break
        /* istanbul ignore next */
        default:
            return notReachable(earn)
    }
}

const parseTakerPortfolio = (input: unknown): Result<unknown, TakerPortfolio> =>
    object(input).andThen((obj) =>
        shape({
            assetBalance: parseCryptoMoneyFromStorage(obj.assetBalance),
            userCurrencyRate: parseTakerUserCurrencyRate(obj.userCurrencyRate),
            userCurrencyToDefaultCurrencyRate: nullableOf(
                obj.userCurrencyToDefaultCurrencyRate,
                (defCurInput) =>
                    object(defCurInput).andThen((rateObj) =>
                        shape({
                            base: object(rateObj.base).andThen(parseCurrency),
                            quote: object(rateObj.quote)
                                .andThen(parseFiatCurrency)
                                .map((curr) => curr as DefaultCurrency),
                            rate: bigint(rateObj.rate),
                        })
                    )
            ),
            apy: number(obj.apy),
            dataTimestampMs: number(obj.dataTimestampMs),
        })
    )

export const parseEarn = (input: unknown): Result<unknown, Earn> =>
    object(input).andThen((obj) =>
        oneOf(obj, [
            shape({
                type: match(obj.type, 'not_configured'),
                takerApyMap: parseTakerApyMap(obj),
                holder: Web3.address.parse(obj.holder),
                takers: parseNotDeployedTakersArray(obj.takers),
                takerPortfolioMap: parseTakerPortfolioMap(
                    obj.takerPortfolioMap
                ),
            }),
            shape({
                type: match(obj.type, 'configured'),
                holder: Web3.address.parse(obj.holder),
                takers: parseTakersArray(obj.takers),
                takerApyMap: parseTakerApyMap(obj),
                takerPortfolioMap: parseTakerPortfolioMap(
                    obj.takerPortfolioMap
                ),
                cardRecharge: oneOf(obj.cardRecharge, [
                    object(obj.cardRecharge).andThen((cardRecharge) =>
                        shape({
                            type: match(cardRecharge.type, 'recharge_enabled'),
                            threshold: bigint(cardRecharge.threshold),
                            rebalancers: arrayOf(
                                cardRecharge.rebalancers,
                                parseDeployedTaker
                            ).andThen(nonEmptyArray),
                            cardSafeAddress: Web3.address.parse(
                                cardRecharge.cardSafeAddress ||
                                    cardRecharge.recipient
                            ),
                            cardCurrency: object(
                                cardRecharge.cardCurrency ||
                                    cardRecharge.recipientToken
                            ).andThen(parseCryptoCurrency),
                            lastRechargeTransactionHash: nullableOf(
                                cardRecharge.lastRechargeTransactionHash,
                                string
                            ),
                        })
                    ),
                    success({
                        type: 'recharge_disabled' as const,
                    }),
                ]),
            }),
        ])
    )

const parseTakerApyMap = (obj: Obj): Result<unknown, TakerApyMap> => {
    return oneOf(obj.takerApyMap, [
        object(obj.takerApyMap).andThen((takerApyMap) =>
            recordStrict(takerApyMap, {
                valueParser: number,
                keyParser: parseTakerType,
            })
        ),
        success(DEFAULT_TAKER_APY_MAP),
    ])
}

export const parseTakerType = (
    input: unknown
): Result<unknown, Taker['type']> => {
    return string(input).andThen((objType) =>
        TAKER_TYPE_MAP[objType as Taker['type']]
            ? success(objType as Taker['type'])
            : failure(`unknown taker ${objType}`)
    )
}

const parseTakerPortfolioMap = (
    input: unknown
): Result<unknown, TakerPortfolioMap2> =>
    object(input).andThen((map) =>
        recordStrict(map, {
            valueParser: parseTakerPortfolio,
            keyParser: parseTakerType,
        }).andThen((takerPortfolioRecord) => {
            if (
                keys(EARN_PRIMARY_INVESTMENT_ASSETS_MAP).every(
                    (takerType) => takerPortfolioRecord[takerType]
                )
            ) {
                return success(takerPortfolioRecord)
            }
            return failure({
                type: 'takerPortfolioMap_does_not_have_all_takers',
            })
        })
    )

const parseTakersArray = (input: unknown): Result<unknown, Taker[]> =>
    arrayOf(input, parseTaker).andThen((takers) => {
        if (
            keys(EARN_PRIMARY_INVESTMENT_ASSETS_MAP).every((takerType) =>
                takers.some((taker) => taker.type === takerType)
            )
        ) {
            return success(takers)
        }

        return failure({ type: 'takers_array_does_not_have_all_takers' })
    })

const parseNotDeployedTakersArray = (
    input: unknown
): Result<unknown, NotDeployedTaker[]> =>
    arrayOf(input, parseNotDeployedTaker).andThen((takers) => {
        if (
            keys(EARN_PRIMARY_INVESTMENT_ASSETS_MAP).every((takerType) =>
                takers.some((taker) => taker.type === takerType)
            )
        ) {
            return success(takers)
        }

        return failure({ type: 'takers_array_does_not_have_all_takers' })
    })

export const parseHistoricalTakerUserCurrencyRateMapFromStorage = (
    input: unknown
): Result<unknown, HistoricalTakerUserCurrencyRateMap> =>
    recordOf(input, {
        keyParser: parseTakerType,
        valueParser: (mapInput) =>
            recordOf(mapInput, {
                keyParser: numberString,
                valueParser: bigint,
            }),
    }).map((storageType) =>
        mapValues(storageType, (takerType, rates) =>
            mapValues(rates, (_, rate) => {
                switch (takerType) {
                    case 'usd':
                        return {
                            base: GNOSIS_SDAI,
                            quote: FIAT_CURRENCIES.USD,
                            rate,
                        }
                    case 'eur':
                        return {
                            base: GNOSIS_AAVE_EURE,
                            quote: FIAT_CURRENCIES.EUR,
                            rate,
                        }
                    case 'eth':
                        return {
                            base: GNOSIS_WSTETH,
                            quote: ETHEREUM.nativeCurrency,
                            rate,
                        }
                    case 'chf':
                        return {
                            base: GNOSIS_SVZCHF,
                            quote: FIAT_CURRENCIES.CHF,
                            rate,
                        }
                    /* istanbul ignore next */
                    default:
                        return notReachable(takerType)
                }
            })
        )
    )

const parseTakerUserCurrencyRate = (
    input: unknown
): Result<unknown, FXRate2<CryptoCurrency, Currency>> =>
    object(input).andThen((obj) =>
        shape({
            base: object(obj.base).andThen(parseCryptoCurrency),
            quote: object(obj.quote).andThen(parseCurrency),
            rate: bigint(obj.rate),
        })
    )

export const parseTaker = (input: unknown): Result<unknown, Taker> =>
    object(input).andThen((obj) =>
        shape({
            type: parseTakerType(obj.type),
            state: parseTakerState(obj.state),
            address: Web3.address.parse(obj.address),
            cryptoCurrency: object(obj.cryptoCurrency).andThen(
                parseCryptoCurrency
            ),
        })
    )

export const parseNotDeployedTaker = (
    input: unknown
): Result<unknown, NotDeployedTaker> =>
    object(input).andThen((obj) =>
        shape({
            type: parseTakerType(obj.type),
            state: match(obj.state, 'not_deployed'),
            address: Web3.address.parse(obj.address),
            cryptoCurrency: object(obj.cryptoCurrency).andThen(
                parseCryptoCurrency
            ),
        })
    )

export const parseDeployedTaker = (
    input: unknown
): Result<unknown, DeployedTaker> =>
    object(input).andThen((obj) =>
        shape({
            type: parseTakerType(obj.type),
            state: match(obj.state, 'deployed'),
            address: Web3.address.parse(obj.address),
            cryptoCurrency: object(obj.cryptoCurrency).andThen(
                parseCryptoCurrency
            ),
        })
    )

const TAKER_STATE_MAP: Record<Taker['state'], true> = {
    deployed: true,
    not_deployed: true,
}

const parseTakerState = (input: unknown): Result<unknown, Taker['state']> =>
    string(input).andThen((state) => {
        return TAKER_STATE_MAP[state as Taker['state']]
            ? success(state as Taker['state'])
            : failure(`unknown taker state ${state}`)
    })
export const parseEureTakerMetricsFromStorage = (
    input: unknown
): Result<unknown, AaveEureTakerMetrics> => {
    return object(input).andThen((obj) => {
        return shape({
            trailing12MonthYield: number(obj.trailing12MonthYield),
        })
    })
}

export const parseUsdTakerMetricsFromStorage = (
    input: unknown
): Result<unknown, UsdEarnTakerMetrics> =>
    object(input).andThen((obj) =>
        shape({
            reserveTotal: parseFiatMoneyFromStorage(obj.reserveTotal),
            depositorObligations: parseFiatMoneyFromStorage(
                obj.depositorObligations
            ),
            assetCoverageRatio: number(obj.assetCoverageRatio),
            collateralComposition: object(obj.collateralComposition).andThen(
                (composition) =>
                    shape({
                        stableCoins: parseFiatMoneyFromStorage(
                            composition.stableCoins
                        ),
                        bitcoinEther: parseFiatMoneyFromStorage(
                            composition.bitcoinEther
                        ),
                        usTBills: parseFiatMoneyFromStorage(
                            composition.usTBills
                        ),
                        other: parseFiatMoneyFromStorage(composition.other),
                    })
            ),
        })
    )

export const parseChfTakerMetricsFromStorage = (
    input: unknown
): Result<unknown, CHFEarnTakerMetrics> =>
    object(input).andThen((obj) =>
        shape({
            totalSupply: parseFiatMoneyFromStorage(obj.totalSupply),
            totalCollateral: parseFiatMoneyFromStorage(obj.totalCollateral),
            assetCoverageRatio: number(obj.assetCoverageRatio),
            collateralComposition: object(obj.collateralComposition).andThen(
                (composition) =>
                    shape({
                        bitcoin: parseFiatMoneyFromStorage(composition.bitcoin),
                        ethereum: parseFiatMoneyFromStorage(
                            composition.ethereum
                        ),
                        realWorldAssets: parseFiatMoneyFromStorage(
                            composition.realWorldAssets
                        ),
                        other: parseFiatMoneyFromStorage(composition.other),
                    })
            ),
        })
    )
