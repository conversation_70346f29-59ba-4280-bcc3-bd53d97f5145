import { notReachable } from '@zeal/toolkit'
import { LimitedSizeArray } from '@zeal/toolkit/Array/helpers/chunk'
import { unsafe_toNumberWithFraction } from '@zeal/toolkit/BigInt'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import { Percentage } from '@zeal/toolkit/Percentage'
import * as Web3 from '@zeal/toolkit/Web3'

import { NULL_ADDRESS } from '@zeal/domains/Address/constants'
import { CryptoCurrency, Currency, FiatCurrency } from '@zeal/domains/Currency'
import {
    GNOSIS_EURE,
    GNOSIS_SDAI,
    GNOSIS_SVZCHF,
    GNOSIS_WSTETH,
} from '@zeal/domains/Currency/constants'
import { FXRate2 } from '@zeal/domains/FXRate'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import {
    ETHEREUM,
    findNetworkByHexChainId,
} from '@zeal/domains/Network/constants'
import { EthCall, RPCRequest } from '@zeal/domains/RPCRequest'
import {
    fetchPublicRPCBatch,
    Request,
} from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import {
    fetchRPCBatchWithRetry,
    fetchRPCResponseWithRetry,
} from '@zeal/domains/RPCRequest/api/fetchRPCResponse'

import { fetchEthTakerApy } from './fetchEthTakerApy'

import { TakerType } from '..'
import {
    AAVE_MARKET_GET_RESERVE_DATA_ABI,
    EARN_CHF_TAKER_USER_CURRENCY,
    EARN_ETH_TAKER_USER_CURRENCY,
    EARN_EUR_TAKER_USER_CURRENCY,
    EARN_NETWORK,
    EARN_PRIMARY_INVESTMENT_ASSETS_MAP,
    EARN_USD_TAKER_USER_CURRENCY,
    ETHEREUM_WSTETH_ABI,
    ETHEREUM_WSTETH_ADDRESS,
    EURE_AAVE_POOL_ADDRESS,
    GNOSIS_S_DAI_VAULT_ADDRESS,
    GNOSIS_S_DAI_VAULT_APY_ABI,
    GNOSIS_SDAI_ABI,
    GNOSIS_SV_ZCHF_ABI,
    GNOSIS_SV_ZCHF_VAULT_ABI,
    GNOSIS_SV_ZCHF_VAULT_ADDRESS,
} from '../constants'

export const fetchHistoricalTakerUserCurrencyRates = async ({
    blocks,
    networkMap,
    networkRPCMap,
    takerType,
    signal,
}: {
    takerType: TakerType
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    blocks: LimitedSizeArray<number, 50>
    signal?: AbortSignal
}): Promise<FXRate2<CryptoCurrency, Currency>[]> => {
    const investmentCurrency = EARN_PRIMARY_INVESTMENT_ASSETS_MAP[takerType]

    const network = findNetworkByHexChainId(
        investmentCurrency.networkHexChainId,
        networkMap
    )

    switch (takerType) {
        case 'usd': {
            const responses = await fetchRPCBatchWithRetry({
                network,
                networkRPCMap,
                requests: blocks.map((block) => {
                    const request: EthCall = {
                        id: generateRandomNumber(),
                        jsonrpc: '2.0',
                        method: 'eth_call',
                        params: [
                            {
                                data: Web3.abi.encodeFunctionData({
                                    abi: GNOSIS_SDAI_ABI,
                                    functionName: 'convertToAssets',
                                    args: [
                                        10n **
                                            BigInt(
                                                EARN_USD_TAKER_USER_CURRENCY.rateFraction
                                            ),
                                    ],
                                }),
                                to: GNOSIS_SDAI.address,
                            },
                            Hexadecimal.unpad(Hexadecimal.fromNumber(block)),
                        ],
                    }

                    return request
                }),
                signal,
            })

            return responses
                .map((sDAIconvertToAssetsResponse) =>
                    Web3.abi.decodeFunctionResult({
                        abi: GNOSIS_SDAI_ABI,
                        data: sDAIconvertToAssetsResponse as `0x${string}`,
                        functionName: 'convertToAssets',
                    })
                )
                .map((sDAIconvertToAssetRate) => ({
                    base: investmentCurrency,
                    quote: EARN_USD_TAKER_USER_CURRENCY,
                    rate: sDAIconvertToAssetRate,
                }))
        }
        case 'eur':
            return blocks.map(() => ({
                base: investmentCurrency,
                quote: EARN_EUR_TAKER_USER_CURRENCY,
                rate: 10n ** BigInt(EARN_EUR_TAKER_USER_CURRENCY.rateFraction),
            }))

        case 'chf': {
            const responses = await fetchRPCBatchWithRetry({
                network,
                networkRPCMap,
                requests: blocks.map((block) => {
                    const request: EthCall = {
                        id: generateRandomNumber(),
                        jsonrpc: '2.0',
                        method: 'eth_call',
                        params: [
                            {
                                data: Web3.abi.encodeFunctionData({
                                    abi: GNOSIS_SV_ZCHF_ABI,
                                    functionName: 'convertToAssets',
                                    args: [
                                        10n **
                                            BigInt(
                                                EARN_CHF_TAKER_USER_CURRENCY.fraction
                                            ),
                                    ],
                                }),
                                to: GNOSIS_SVZCHF.address,
                            },
                            Hexadecimal.unpad(Hexadecimal.fromNumber(block)),
                        ],
                    }

                    return request
                }),
                signal,
            })

            return responses
                .map((convertToAssetsResponse) =>
                    Web3.abi.decodeFunctionResult({
                        abi: GNOSIS_SV_ZCHF_ABI,
                        data: convertToAssetsResponse as `0x${string}`,
                        functionName: 'convertToAssets',
                    })
                )
                .map((convertToAssetRate) => ({
                    base: investmentCurrency,
                    quote: EARN_CHF_TAKER_USER_CURRENCY,
                    rate: convertToAssetRate,
                }))
        }

        case 'eth':
            // FIXME :: @mike we fetch Eth rate from etehreum network, so we can't use same block as for EARN_NETWORK
            const ethRate = await fetchEthTakerUserCurrencyRate({
                networkRPCMap,
                signal,
            })
            return blocks.map(() => ethRate)

        default:
            return notReachable(takerType)
    }
}

export const fetchEthTakerUserCurrencyRate = async ({
    networkRPCMap,
    signal,
}: {
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<FXRate2<CryptoCurrency, CryptoCurrency>> => {
    const wstEthStEthPerTokenRequest: RPCRequest = {
        id: generateRandomNumber(),
        jsonrpc: '2.0',
        method: 'eth_call',
        params: [
            {
                data: Web3.abi.encodeFunctionData({
                    abi: ETHEREUM_WSTETH_ABI,
                    functionName: 'stEthPerToken',
                    args: [],
                }),
                to: ETHEREUM_WSTETH_ADDRESS,
            },
            'latest',
        ],
    }

    const wstEthStEthPerTokenResponse = await fetchRPCResponseWithRetry({
        request: wstEthStEthPerTokenRequest,
        signal,
        network: ETHEREUM,
        networkRPCMap,
    })

    const wstEthStEthPerToken = Web3.abi.decodeFunctionResult({
        abi: ETHEREUM_WSTETH_ABI,
        data: wstEthStEthPerTokenResponse as `0x${string}`,
        functionName: 'stEthPerToken',
    })

    return {
        base: GNOSIS_WSTETH,
        quote: EARN_ETH_TAKER_USER_CURRENCY,
        rate: wstEthStEthPerToken,
    }
}

export const fetchTakerUserCurrencyRateAndApy = async ({
    networkRPCMap,
    takerType,
    signal,
}: {
    takerType: TakerType
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<{
    takerType: TakerType
    userCurrencyRate: FXRate2<CryptoCurrency, Currency>
    apy: Percentage
}> => {
    const investmentCurrency = EARN_PRIMARY_INVESTMENT_ASSETS_MAP[takerType]

    switch (takerType) {
        case 'usd': {
            const [sDaiVaultAPY, sDAIconvertToAssets] =
                await fetchPublicRPCBatch(
                    [requestSDaiAPY(), requestSDaiConvertToAssets()],
                    {
                        network: EARN_NETWORK,
                        signal,
                    }
                )

            const apy = unsafe_toNumberWithFraction(sDaiVaultAPY, 16)

            const userCurrencyRate: FXRate2<CryptoCurrency, FiatCurrency> = {
                base: investmentCurrency,
                quote: EARN_USD_TAKER_USER_CURRENCY,
                rate: sDAIconvertToAssets,
            }

            return {
                takerType,
                apy,
                userCurrencyRate,
            }
        }

        case 'eur': {
            const [aaveMarketDataResponse] = await fetchPublicRPCBatch(
                [requestAAVEEureAPY()],
                {
                    network: EARN_NETWORK,
                    signal,
                }
            )

            const userCurrencyRate = {
                base: investmentCurrency,
                quote: EARN_EUR_TAKER_USER_CURRENCY,
                rate: 10n ** BigInt(EARN_EUR_TAKER_USER_CURRENCY.rateFraction),
            }

            const apy = unsafe_toNumberWithFraction(aaveMarketDataResponse, 25)

            return {
                takerType,
                apy,
                userCurrencyRate,
            }
        }

        case 'chf': {
            const [apyResponse, convertToAssetRate] = await fetchPublicRPCBatch(
                [requestSvZchfAPY(), requestSvZchfConvertToAssets()],
                {
                    network: EARN_NETWORK,
                    signal,
                }
            )

            const apy = unsafe_toNumberWithFraction(apyResponse, 4)

            return {
                takerType,
                apy,
                userCurrencyRate: {
                    base: investmentCurrency,
                    quote: EARN_CHF_TAKER_USER_CURRENCY,
                    rate: convertToAssetRate,
                },
            }
        }

        case 'eth': {
            const [apy, userCurrencyRate] = await Promise.all([
                fetchEthTakerApy(),
                fetchEthTakerUserCurrencyRate({ networkRPCMap, signal }),
            ])

            return {
                takerType,
                apy,
                userCurrencyRate,
            }
        }

        default:
            return notReachable(takerType)
    }
}

const ethCall = (param: object): EthCall => {
    return {
        id: generateRandomNumber(),
        jsonrpc: '2.0',
        method: 'eth_call',
        params: [param, 'latest'],
    }
}

export const requestSDaiAPY = (): Request<bigint> => {
    return {
        request: ethCall({
            from: NULL_ADDRESS,
            data: Web3.abi.encodeFunctionData({
                abi: GNOSIS_S_DAI_VAULT_APY_ABI,
                functionName: 'vaultAPY',
            }),
            to: GNOSIS_S_DAI_VAULT_ADDRESS,
        }),
        parser: (input) => {
            return Web3.abi.decodeFunctionResult({
                abi: GNOSIS_S_DAI_VAULT_APY_ABI,
                data: input as `0x${string}`,
                functionName: 'vaultAPY',
            })
        },
    }
}

export const requestSvZchfAPY = (): Request<bigint> => {
    return {
        request: ethCall({
            from: NULL_ADDRESS,
            data: Web3.abi.encodeFunctionData({
                abi: GNOSIS_SV_ZCHF_VAULT_ABI,
                functionName: 'currentRatePPM',
            }),
            to: GNOSIS_SV_ZCHF_VAULT_ADDRESS,
        }),
        parser: (input) => {
            const ratePPM = Web3.abi.decodeFunctionResult({
                abi: GNOSIS_SV_ZCHF_VAULT_ABI,
                data: input as `0x${string}`,
                functionName: 'currentRatePPM',
            })

            return BigInt(ratePPM)
        },
    }
}

export const requestSDaiConvertToAssets = (): Request<bigint> => {
    return {
        request: ethCall({
            data: Web3.abi.encodeFunctionData({
                abi: GNOSIS_SDAI_ABI,
                functionName: 'convertToAssets',
                args: [
                    10n ** BigInt(EARN_USD_TAKER_USER_CURRENCY.rateFraction),
                ],
            }),
            to: GNOSIS_SDAI.address,
        }),
        parser: (input) => {
            return Web3.abi.decodeFunctionResult({
                abi: GNOSIS_SDAI_ABI,
                data: input as `0x${string}`,
                functionName: 'convertToAssets',
            })
        },
    }
}

export const requestSvZchfConvertToAssets = (): Request<bigint> => {
    return {
        request: ethCall({
            data: Web3.abi.encodeFunctionData({
                abi: GNOSIS_SV_ZCHF_ABI,
                functionName: 'convertToAssets',
                args: [10n ** BigInt(EARN_CHF_TAKER_USER_CURRENCY.fraction)],
            }),
            to: GNOSIS_SVZCHF.address,
        }),
        parser: (input) => {
            return Web3.abi.decodeFunctionResult({
                abi: GNOSIS_SV_ZCHF_ABI,
                data: input as `0x${string}`,
                functionName: 'convertToAssets',
            })
        },
    }
}

export const requestAAVEEureAPY = (): Request<bigint> => {
    return {
        request: ethCall({
            from: NULL_ADDRESS,
            to: EURE_AAVE_POOL_ADDRESS,
            data: Web3.abi.encodeFunctionData({
                abi: AAVE_MARKET_GET_RESERVE_DATA_ABI,
                functionName: 'getReserveData',
                args: [GNOSIS_EURE.address as `0x${string}`],
            }),
        }),

        parser: (res) => {
            return Web3.abi.decodeFunctionResult({
                abi: AAVE_MARKET_GET_RESERVE_DATA_ABI,
                data: res as `0x${string}`,
                functionName: 'getReserveData',
            }).currentLiquidityRate
        },
    }
}
