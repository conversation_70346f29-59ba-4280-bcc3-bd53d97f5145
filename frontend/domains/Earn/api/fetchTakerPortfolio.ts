import { notReachable } from '@zeal/toolkit'
import { unsafe_toNumberWithFraction } from '@zeal/toolkit/BigInt'
import { withRetries } from '@zeal/toolkit/Function'
import { memoizeOne } from '@zeal/toolkit/Function/memoizeOne'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { toBigInt } from '@zeal/toolkit/Hexadecimal'
import { arrayOf, number, object, oneOf } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { requestBalanceOfToken } from '@zeal/domains/Address/api/fetchBalanceOf'
import {
    CryptoCurrency,
    Currency,
    DefaultCurrency,
    FiatCurrency,
} from '@zeal/domains/Currency'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import { FXRate2 } from '@zeal/domains/FXRate'
import {
    fetchDefaultCurrencyRateFromUSD,
    fetchDefaultCurrencyRateToFiatCurrency,
} from '@zeal/domains/FXRate/api/fetchDefaultCurrencyRateToUSD'
import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ETHEREUM } from '@zeal/domains/Network/constants'
import { requestBlockNumber } from '@zeal/domains/RPCRequest/api/fetchBlockNumber'
import { fetchBlockTimestamp } from '@zeal/domains/RPCRequest/api/fetchBlockTimestamp'
import { requestLogs } from '@zeal/domains/RPCRequest/api/fetchLogs'
import {
    fetchPublicRPCBatch,
    fetchRPCBatch2WithRetry,
} from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { fetchEthTakerApy } from './fetchEthTakerApy'
import {
    fetchEthTakerUserCurrencyRate,
    requestAAVEEureAPY,
    requestSDaiAPY,
    requestSDaiConvertToAssets,
    requestSvZchfAPY,
    requestSvZchfConvertToAssets,
} from './fetchTakerUserCurrencyRate'

import {
    CHFTakerType,
    Taker,
    TakerPortfolio,
    TakerType,
    USDTakerType,
} from '..'
import {
    EARN_CHF_TAKER_USER_CURRENCY,
    EARN_EUR_TAKER_USER_CURRENCY,
    EARN_NETWORK,
    EARN_PRIMARY_INVESTMENT_ASSETS_MAP,
    EARN_USD_TAKER_USER_CURRENCY,
} from '../constants'
import { getHolderPredictedAddress } from '../helpers/getHolderPredictedAddress'
import { getTakerPredictedAddress } from '../helpers/getTakerPredictedAddress'

export const fetchTakerPortfolioByAddress = async ({
    defaultCurrencyConfig,
    takerAddress,
    networkMap,
    networkRPCMap,
    takerType,
    signal,
}: {
    takerType: TakerType
    takerAddress: Web3.address.Address
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<TakerPortfolio> => {
    const currency = EARN_PRIMARY_INVESTMENT_ASSETS_MAP[takerType]
    const network = EARN_NETWORK
    switch (takerType) {
        case 'usd': {
            const [
                [
                    amount,
                    sDaiVaultAPYResponse,
                    sDAIconvertToAssetRate,
                    blockNumber,
                ],
                userCurrencyToDefaultCurrencyRate,
            ] = await Promise.all([
                fetchPublicRPCBatch(
                    [
                        requestBalanceOfToken({
                            address: takerAddress,
                            tokenAddress: currency.address,
                        }),
                        requestSDaiAPY(),
                        requestSDaiConvertToAssets(),
                        requestBlockNumber(),
                    ],
                    {
                        network,
                        signal,
                    }
                ),

                fetchTakerUserCurrencyToDefaultCurrencyRate({
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    takerType,
                    signal,
                }),
            ])
            const dataTimestampMs =
                amount > 0
                    ? await fetchLatestEarningEventTimestampWithRetry(
                          takerType,
                          blockNumber,
                          networkRPCMap
                      )
                    : Date.now() // we don't want to spam RPC to find latest deposit/withdrawal if there is no balance

            const apy = unsafe_toNumberWithFraction(sDaiVaultAPYResponse, 16)

            const userCurrencyRate: FXRate2<CryptoCurrency, FiatCurrency> = {
                base: currency,
                quote: EARN_USD_TAKER_USER_CURRENCY,
                rate: sDAIconvertToAssetRate,
            }

            return {
                apy,
                assetBalance: { amount, currency },
                userCurrencyRate,
                dataTimestampMs,
                userCurrencyToDefaultCurrencyRate,
            }
        }

        case 'eur': {
            const [
                [amount, aaveMarketDataResponse],
                userCurrencyToDefaultCurrencyRate,
            ] = await Promise.all([
                fetchPublicRPCBatch(
                    [
                        requestBalanceOfToken({
                            address: takerAddress,
                            tokenAddress: currency.address,
                        }),
                        requestAAVEEureAPY(),
                    ],
                    {
                        network,
                        signal,
                    }
                ),
                fetchTakerUserCurrencyToDefaultCurrencyRate({
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    takerType,
                    signal,
                }),
            ])

            const userCurrencyRate = {
                base: currency,
                quote: EARN_EUR_TAKER_USER_CURRENCY,
                rate: 10n ** BigInt(EARN_EUR_TAKER_USER_CURRENCY.rateFraction),
            }

            const apy = unsafe_toNumberWithFraction(aaveMarketDataResponse, 25)

            return {
                apy,
                assetBalance: { amount, currency },
                userCurrencyRate,
                dataTimestampMs: Date.now(),
                userCurrencyToDefaultCurrencyRate,
            }
        }

        case 'chf':
            const [
                [amount, apyResponse, convertToAssetRate, blockNumber],
                userCurrencyToDefaultCurrencyRate,
            ] = await Promise.all([
                fetchPublicRPCBatch(
                    [
                        requestBalanceOfToken({
                            address: takerAddress,
                            tokenAddress: currency.address,
                        }),
                        requestSvZchfAPY(),
                        requestSvZchfConvertToAssets(),
                        requestBlockNumber(),
                    ],
                    {
                        network,
                        signal,
                    }
                ),

                fetchTakerUserCurrencyToDefaultCurrencyRate({
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    takerType,
                    signal,
                }),
            ])
            const dataTimestampMs =
                amount > 0
                    ? await fetchLatestEarningEventTimestampWithRetry(
                          takerType,
                          blockNumber,
                          networkRPCMap
                      )
                    : Date.now() // we don't want to spam RPC to find latest deposit/withdrawal if there is no balance

            const apy = unsafe_toNumberWithFraction(apyResponse, 4)

            const userCurrencyRate: FXRate2<CryptoCurrency, FiatCurrency> = {
                base: currency,
                quote: EARN_CHF_TAKER_USER_CURRENCY,
                rate: convertToAssetRate,
            }

            return {
                apy,
                assetBalance: { amount, currency },
                userCurrencyRate,
                dataTimestampMs,
                userCurrencyToDefaultCurrencyRate,
            }

        case 'eth': {
            const [
                apy,
                [amount],
                userCurrencyRate,
                userCurrencyToDefaultCurrencyRate,
            ] = await Promise.all([
                fetchEthTakerApy(),
                fetchPublicRPCBatch(
                    [
                        requestBalanceOfToken({
                            address: takerAddress,
                            tokenAddress: currency.address,
                        }),
                    ],
                    {
                        signal,
                        network,
                    }
                ),
                fetchEthTakerUserCurrencyRate({ networkRPCMap, signal }),
                fetchTakerUserCurrencyToDefaultCurrencyRate({
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    takerType,
                    signal,
                }),
            ])

            return {
                apy,
                assetBalance: { amount, currency },
                userCurrencyRate,
                dataTimestampMs: Date.now(),
                userCurrencyToDefaultCurrencyRate,
            }
        }

        default:
            return notReachable(takerType)
    }
}
export const fetchTakerPortfolioByEarnOwner = async ({
    defaultCurrencyConfig,
    earnOwnerAddress,
    networkMap,
    networkRPCMap,
    takerType,
    signal,
}: {
    takerType: TakerType
    earnOwnerAddress: Web3.address.Address
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<TakerPortfolio> => {
    const holder = getHolderPredictedAddress({ earnOwnerAddress })
    const takerAddress = getTakerPredictedAddress({ holder, takerType })
    return fetchTakerPortfolioByAddress({
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        takerAddress,
        takerType,
        signal,
    })
}
type Params = {
    taker: Taker
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}
export const fetchTakerPortfolio = async ({
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    taker,
    signal,
}: Params): Promise<TakerPortfolio> => {
    return fetchTakerPortfolioByAddress({
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        takerAddress: taker.address,
        takerType: taker.type,
        signal,
    })
}

export const fetchTakerPortfolioWithCache = memoizeOne(
    ({
        cacheKey: _,
        signal: __,
        ...rest
    }: Params & { cacheKey: string }): Promise<TakerPortfolio> =>
        fetchTakerPortfolio(rest),
    ({ cacheKey, taker }) => {
        return `${cacheKey}${taker.address}`
    }
)

const fetchTakerUserCurrencyToDefaultCurrencyRate = ({
    takerType,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    signal,
}: {
    takerType: TakerType
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<FXRate2<Currency, DefaultCurrency> | null> => {
    switch (takerType) {
        case 'usd':
            return fetchDefaultCurrencyRateFromUSD({
                defaultCurrencyConfig,
                signal,
            })

        case 'eur':
            return fetchDefaultCurrencyRateToFiatCurrency({
                fiatCurrency: FIAT_CURRENCIES.EUR,
                defaultCurrency: defaultCurrencyConfig.defaultCurrency,
                signal,
            })

        case 'chf':
            return fetchDefaultCurrencyRateToFiatCurrency({
                fiatCurrency: FIAT_CURRENCIES.CHF,
                defaultCurrency: defaultCurrencyConfig.defaultCurrency,
                signal,
            })

        case 'eth':
            return fetchRate({
                cryptoCurrency: ETHEREUM.nativeCurrency,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                signal,
            })

        default:
            return notReachable(takerType)
    }
}

const RETRY_DELAY_MS = 500
const MAX_RETRIES_COUNT = 10

const PAGE = 600

const fetchLatestEarningEventTimestamp = async (
    takerType: Taker['type'],
    startingFromBlock: number,
    networkRPCMap: NetworkRPCMap
): Promise<number> => {
    switch (takerType) {
        case 'usd':
        case 'chf':
            return fetchEIP4626LatestEarningEventTimestamp({
                takerType,
                startingFromBlock,
                networkRPCMap,
            })
        case 'eur':
        case 'eth':
            return Date.now()
        /* istanbul ignore next */
        default:
            return notReachable(takerType)
    }
}

const fetchEIP4626LatestEarningEventTimestamp = async ({
    takerType,
    startingFromBlock,
    networkRPCMap,
}: {
    takerType: USDTakerType | CHFTakerType
    startingFromBlock: number
    networkRPCMap: NetworkRPCMap
}): Promise<number> => {
    const start = startingFromBlock - PAGE
    const fromBlock = Hexadecimal.unpad(Hexadecimal.fromNumber(start))
    const toBlock = Hexadecimal.unpad(Hexadecimal.fromNumber(startingFromBlock))

    const investmentAsset = EARN_PRIMARY_INVESTMENT_ASSETS_MAP[takerType]

    const responses = await fetchRPCBatch2WithRetry(
        [
            requestLogs({
                address: investmentAsset.address,
                fromBlock: toBigInt(fromBlock),
                toBlock: toBigInt(toBlock),
                topics: [
                    '0xdcbc1c05240f31ff3ad067ef1ee35ce4997762752e3a095284754544f4c709d7', // Deposit
                ],
            }),
            requestLogs({
                address: investmentAsset.address,
                fromBlock: toBigInt(fromBlock),
                toBlock: toBigInt(toBlock),
                topics: [
                    '0xfbde797d201c681b91056529119e0b02407c7bb96a4a2c75c01fc9667232c8db', // Withdraw
                ],
            }),
        ],
        {
            network: EARN_NETWORK,
            networkRPCMap,
        }
    )

    const blocks = responses.flatMap((res) =>
        arrayOf(res, (item) =>
            object(item).andThen((obj) =>
                oneOf(obj.blockNumber, [
                    Hexadecimal.parserHexAsNumber(obj.blockNumber),
                    number(obj.blockNumber),
                ])
            )
        ).getSuccessResultOrThrow(
            'Failed to parse blocks from earnings event logs'
        )
    )

    if (blocks.length === 0) {
        return fetchEIP4626LatestEarningEventTimestamp({
            takerType,
            startingFromBlock: start - 1,
            networkRPCMap,
        })
    }

    const blockNumber = Math.max(...blocks)

    return fetchBlockTimestamp({
        blockNumber: Hexadecimal.fromNumber(blockNumber),
        network: EARN_NETWORK,
        networkRPCMap,
    })
}

const fetchLatestEarningEventTimestampWithRetry = withRetries({
    retries: MAX_RETRIES_COUNT,
    delayMs: RETRY_DELAY_MS,
    fn: fetchLatestEarningEventTimestamp,
})
