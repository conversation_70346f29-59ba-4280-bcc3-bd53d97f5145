import { notReachable } from '@zeal/toolkit'

import { TakerType } from '..'

export const getTakerLabel = (takerType: TakerType): string => {
    switch (takerType) {
        case 'usd':
            return 'Sky'
        case 'eur':
            return 'Aave'
        case 'eth':
            return 'Lido'
        case 'chf':
            return 'Fr.coin'

        /* istanbul ignore next */
        default:
            return notReachable(takerType)
    }
}
