import { CurrencyId, unsafeGetCurrencyInfo } from '@zeal/domains/Currency'
import { GNOSIS_SVZCHF, GNOSIS_ZCHF } from '@zeal/domains/Currency/constants'
import { DEFI_PROTOCOL_TOKENS } from '@zeal/domains/Portfolio/constants'
import { ContractStats, ContractStatsMap } from '@zeal/domains/Token'

const MIN_RECIPIENTS = 35
const MIN_SENDERS = 35
const MAX_RECIPIENTS_TO_SENDERS_RATIO = 10

const WHITE_LISTED_TOKENS: Set<CurrencyId> = new Set([
    GNOSIS_ZCHF.id,
    GNOSIS_SVZCHF.id,
])

export const checkIfScam = ({
    currencyId,
    statsMap,
}: {
    currencyId: CurrencyId
    statsMap: ContractStatsMap
}): boolean => {
    const { address } = unsafeGetCurrencyInfo(currencyId)

    const defiProtocolAddresses = new Set(
        DEFI_PROTOCOL_TOKENS.map((token) => token.address)
    )

    const isDefiToken = defiProtocolAddresses.has(address)
    const hasScamStats = checkHasScamStats(statsMap[address])
    const isWhiteListed = WHITE_LISTED_TOKENS.has(currencyId)

    return !isWhiteListed && (isDefiToken || hasScamStats)
}

const checkHasScamStats = (stats: ContractStats | null) =>
    !stats ||
    stats.recievers < MIN_RECIPIENTS ||
    stats.senders < MIN_SENDERS ||
    stats.recievers / stats.senders > MAX_RECIPIENTS_TO_SENDERS_RATIO
