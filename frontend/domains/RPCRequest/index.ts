import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'
import { Address } from '@zeal/toolkit/Web3/address'

import { CurrencyId } from '@zeal/domains/Currency'
import { Network, NetworkHexId } from '@zeal/domains/Network'

export type RPCRequest =
    | BlacklistedRpcRequest
    | DebugTraceTransaction
    | EthAccounts
    | EthBlockNumber
    | EthGetBlockReceipts
    | EthCall
    | EthChainId
    | EthCoinbase
    | EthEstimateGas
    | EthFeeHistory
    | EthGasPrice
    | EthGetBalance
    | EthGetBlockByNumber
    | EthGetCode
    | EthGetFilterChanges
    | EthGetStorageAt
    | EthGetTransactionByHash
    | EthGetTransactionCount
    | EthGetTransactionReceipt
    | EthLogs
    | EthMaxPriorityFeePerGas
    | EthNewFilter
    | EthRequestAccounts
    | EthSendRawTransaction
    | EthSendTransaction
    | EthSignTypedData
    | EthSignTypedDataV3
    | EthSignTypedDataV4
    | EthUninstallFilter
    | MetamaskGetProviderState
    | NetListening
    | NetVersion
    | PersonalECRecover
    | PersonalSign
    | WalletAddEthereumChain
    | WalletGetPermissions
    | WalletGetSnaps
    | WalletInvokeSnap
    | WalletRequestPermissions
    | WalletRequestSnaps
    | WalletSwitchEthereumChain
    | WalletWatchAsset
    | Web3ClientVersion

type Common = {
    id: number | string
    jsonrpc: '2.0'
}

export type MetamaskGetProviderState = {
    method: 'metamask_getProviderState'
    params: []
} & Common

export type EthUninstallFilter = {
    method: 'eth_uninstallFilter'
    params: [string] // filterId
} & Common

export type EthGetFilterChanges = {
    method: 'eth_getFilterChanges'
    params: [string] // filterID
} & Common

export type EthNewFilter = {
    method: 'eth_newFilter'
    params: object[] // https://www.quicknode.com/docs/ethereum/eth_newFilter filter param in an object all optional
} & Common

export type NetListening = {
    method: 'net_listening'
    params: []
} & Common

// https://docs.metamask.io/snaps/reference/rpc-api/#wallet_getsnaps
export type WalletGetSnaps = {
    method: 'wallet_getSnaps'
    params: []
} & Common

// https://docs.metamask.io/snaps/reference/rpc-api/#wallet_requestsnaps
export type WalletRequestSnaps = {
    method: 'wallet_requestSnaps'
    params: []
} & Common

// https://docs.metamask.io/snaps/reference/rpc-api/#wallet_invokesnap
export type WalletInvokeSnap = {
    method: 'wallet_invokeSnap'
    params: []
} & Common

export type EthGetStorageAt = {
    method: 'eth_getStorageAt'
    params: [string, string, string] // address, position,blockNumber
} & Common

export type Web3ClientVersion = {
    method: 'web3_clientVersion'
    params: []
} & Common

export type WalletWatchAsset = {
    method: 'wallet_watchAsset'
    params: [] // we don't use watch asset call
} & Common

export type EthGetBalance = {
    method: 'eth_getBalance'
    params: [string, string] // [address, block]
} & Common

export type NetVersion = {
    method: 'net_version'
    params: []
} & Common

export type PersonalECRecover = {
    method: 'personal_ecRecover'
    params: [string, string] // [message, signature]
} & Common

export type PersonalSign = {
    method: 'personal_sign'
    params: [string] // [message]
} & Common

export type EthSignTypedDataV4 = {
    method: 'eth_signTypedData_v4'
    params: [string, string] //  [account, message]
} & Common

export type EthSignTypedDataV3 = {
    method: 'eth_signTypedData_v3'
    params: [string, string] //  [account, message]
} & Common

export type EthSignTypedData = {
    method: 'eth_signTypedData'
    params: [string, string] //  [account, message]
} & Common

export type EthGasPrice = {
    method: 'eth_gasPrice'
    params: unknown[]
} & Common

export type EthGetBlockReceipts = {
    method: 'eth_getBlockReceipts'
    params: [string] // QUANTITY | TAG - integer of a block number, or the string "earliest", "latest" or "pending", as in the default block parameter.
} & Common

export type EthGetTransactionCount = {
    method: 'eth_getTransactionCount'
    params: [string, string | undefined] //  [address, block]
} & Common

export type EthGetCode = {
    method: 'eth_getCode'
    params: [string, string | undefined] //  [address, block]
} & Common

export type EthGetBlockByNumber = {
    method: 'eth_getBlockByNumber'
    params: [
        string, // QUANTITY | TAG - integer of a block number, or the string "earliest", "latest" or "pending", as in the default block parameter.
        boolean,
    ]
} & Common

export type EthMaxPriorityFeePerGas = {
    method: 'eth_maxPriorityFeePerGas'
    params: []
} & Common

export type EthFeeHistory = {
    method: 'eth_feeHistory'
    params: [number, string, number[]]
} & Common

export type WalletSwitchEthereumChain = {
    method: 'wallet_switchEthereumChain'
    params: [
        {
            chainId: NetworkHexId
        },
    ]
} & Common

export type EthSendTransaction = {
    method: 'eth_sendTransaction'
    params: [
        {
            from: string
            data: string
            to?: string
            gas?: string // string or number? Should we parse it??
            gasPrice?: string // string or number? Should we parse it?? !!! :: no we should not ... some trx not support it and we got Error: eip-1559 transactions don't support gasPrice
            maxPriorityFeePerGas?: string
            maxFeePerGas?: string
            value?: string // value + fee = :heart:
            nonce?: string
        },
    ]
} & Common

export type DebugTraceTransaction = {
    method: 'debug_traceTransaction'
    params: [
        string,
        {
            tracer: 'callTracer'
            tracerConfig: {
                enableMemory: boolean
                enableReturnData: boolean
            }
            timeout: string
        },
    ]
} & Common

export type EthAccounts = {
    method: 'eth_accounts'
    params: []
} & Common

export type EthBlockNumber = {
    method: 'eth_blockNumber'
    params: []
} & Common

export type EthCall = {
    method: 'eth_call'
    params: [object, Hexadecimal | 'latest'] // TODO @resetko-zeal should we do opaque type for block number, because it should be unpadded hex or latest to work on all RPC providers
} & Common

export type EthEstimateGas = {
    method: 'eth_estimateGas'
    params: unknown[]
} & Common

export type EthLogs = {
    method: 'eth_getLogs'
    params: any[]
} & Common

export type EthChainId = {
    method: 'eth_chainId'
    params: []
} & Common

export type EthCoinbase = {
    method: 'eth_coinbase'
    params: []
} & Common

export type EthRequestAccounts = {
    method: 'eth_requestAccounts'
    params: []
} & Common

export type EthSendRawTransaction = {
    method: 'eth_sendRawTransaction'
    params: [string]
} & Common

export type EthGetTransactionReceipt = {
    method: 'eth_getTransactionReceipt'
    params: [string]
} & Common

export type EthGetTransactionByHash = {
    method: 'eth_getTransactionByHash'
    params: [string]
} & Common

export type WalletAddEthereumChain = {
    method: 'wallet_addEthereumChain'
    params: [
        {
            chainId: NetworkHexId
            iconUrls?: string[]
            blockExplorerUrls: string[]
            chainName?: string
            nativeCurrency?: {
                symbol: string
                decimals: number
            }
            rpcUrls: string[]
        },
    ]
} & Common

export type WalletGetPermissions = {
    method: 'wallet_getPermissions'
    params: []
} & Common

export type WalletRequestPermissions = {
    method: 'wallet_requestPermissions'
    params: [
        {
            eth_accounts: Record<string, never>
        },
    ]
} & Common

export type BlacklistedRpcRequest = {
    method:
        | 'debug_getBadBlocks'
        | 'debug_getRawBlock'
        | 'debug_getRawHeader'
        | 'debug_getRawReceipts'
        | 'debug_getRawTransaction'
        | 'debug_storageRangeAt'
        | 'debug_getTrieFlushInterval'
        | 'debug_traceBlock'
        | 'debug_traceBlockByHash'
        | 'debug_traceBlockByNumber'
        | 'debug_traceCall'
        | 'trace_block'
        | 'trace_call'
        | 'trace_callMany'
        | 'trace_filter'
        | 'trace_rawTransaction'
        | 'trace_replayBlockTransactions'
        | 'trace_replayTransaction'
        | 'trace_transaction'
        | 'txpool_status'
        | 'txpool_content'
        | 'txpool_inspect'
        | 'txpool_contentFrom'
    params: []
} & Common

export type SignMessageRequest =
    | PersonalSign
    | EthSignTypedDataV4
    | EthSignTypedData
    | EthSignTypedDataV3

// Request that requires user interaction
export type InteractionRequest =
    | EthSendTransaction
    | EthRequestAccounts
    | EthSignTypedDataV4
    | EthSignTypedDataV3
    | EthSignTypedData
    | PersonalSign
    | WalletAddEthereumChain
    | WalletRequestPermissions

export * from './RPCErrors'

export type TransactionReceiptDTO = {
    blockHash: Hexadecimal | null
    blockNumber: number
    contractAddress: Address | null
    to: Address | null //The address of the receiver. null when it's a contract creation transaction
    cumulativeGasUsed: Hexadecimal
    effectiveGasPrice: Hexadecimal
    gasUsed: Hexadecimal
    from: Address
    logs: RPCLogDTO[]
    logsBloom: Hexadecimal // not sure
    status: 0 | 1 // It is either 1 (success) or 0 (failure) encoded as a hexadecimal
    transactionHash: Hexadecimal | null // null when pending
    transactionIndex: number | null
    type: Hexadecimal // 1 | 0
}

export type RPCLogDTO = {
    address: Address
    topics: Hexadecimal[] // should be tuple union?
    data: Hexadecimal | null
    blockNumber: number
    transactionHash: Hexadecimal | null
    transactionIndex: number | null
    blockHash: Hexadecimal | null
    logIndex: number
    removed: boolean
}
export type TransactionReceipt = ContractDeployTransaction | SendTransaction

export type SendTransaction = {
    type: 'transaction'
    network: Network
    blockHash: Hexadecimal
    blockNumber: number
    transactionHash: Hexadecimal
    transactionIndex: number
    cumulativeGasUsed: Hexadecimal
    effectiveGasPrice: Hexadecimal
    gasUsed: Hexadecimal
    transactionType: Hexadecimal // 1 | 0
    from: Address
    to: Address
    logs: ParsedLog[]
}

export type ContractDeployTransaction = {
    type: 'contract_deploy'
    network: Network
    blockHash: Hexadecimal
    blockNumber: number
    status: 0 | 1 // It is either 1 (success) or 0 (failure) encoded as a hexadecimal
    transactionHash: Hexadecimal
    transactionIndex: number
    cumulativeGasUsed: Hexadecimal
    effectiveGasPrice: Hexadecimal
    gasUsed: Hexadecimal
    transactionType: Hexadecimal // 1 | 0
    from: Address
    logs: ParsedLog[]
}

export type ParsedLog =
    | ERC20TransferLog
    | AddedOwnerLog
    | ApprovalLog
    | AccountDeployedLog
    | ThresholdUpdatedLog
    | SetAllowanceLog
    | EnableModuleLog
    | DisableModuleLog
    | SafeModuleTransactionLog
    | SafeModuleTransactionForNativeFeePaymentLog
    | SafeReceivedLog
    | UserOperationEventLog
    | UserOperationRevertReasonLog
    | UnknownLog

export type UserOperationRevertReasonLog = {
    type: 'user_operation_revert_reason'
    eventSignature: 'UserOperationRevertReason(bytes32,address,uint256,bytes)'
    userOpHash: Hexadecimal
    sender: Address
    nonce: bigint
    revertReason: string | null
    logIndex: number
}

export type ERC20TransferLog = {
    type: 'erc20_transfer'
    eventSignature: 'Transfer(address,address,amount)'
    from: Address
    to: Address
    amount: bigint
    currencyId: CurrencyId
    logIndex: number
}

export type AddedOwnerLog = {
    type: 'added_owner'
    eventSignature: 'AddedOwner(address)'
    owner: Address
    logIndex: number
}

export type ApprovalLog = {
    type: 'approval'
    eventSignature: 'Approval(address,address,uint256)'
    owner: Address
    spender: Address
    amount: bigint
    currencyId: CurrencyId
    logIndex: number
}

export type AccountDeployedLog = {
    type: 'account_deployed'
    eventSignature: 'AccountDeployed(bytes32,address,address,address)'
    userOperationHash: Hexadecimal
    sender: Address
    factory: Address
    paymaster: Address
    logIndex: number
}

export type ThresholdUpdatedLog = {
    type: 'threshold_updated'
    eventSignature: 'ThresholdUpdated(uint256)'
    threshold: bigint
    logIndex: number
}

export type SetAllowanceLog = {
    type: 'set_allowance'
    eventSignature: 'SetAllowance(bytes32,uint128,uint128,uint128,uint64,uint64)'
    allowanceKey: Hexadecimal
    balance: bigint
    maxRefill: bigint
    refill: bigint
    period: number
    timestamp: number
    logIndex: number
}

export type EnableModuleLog = {
    type: 'enable_module'
    eventSignature: 'EnabledModule(address)'
    module: Address
    logIndex: number
}

export type DisableModuleLog = {
    type: 'disable_module'
    eventSignature: 'DisabledModule(address)'
    module: Address
    logIndex: number
}

export type SafeModuleTransactionLog = {
    type: 'safe_module_transaction'
    eventSignature: 'SafeModuleTransaction(address,address,uint256,bytes,uint8)'
    module: Address
    to: Address
    from: Address
    value: bigint
    data: Hexadecimal
    operation: number
    logIndex: number
}

export type SafeModuleTransactionForNativeFeePaymentLog = {
    type: 'safe_module_transaction_for_native_fee_payment'
    eventSignature: 'SafeModuleTransaction(address,address,uint256,bytes,uint8)'
    to: Address
    from: Address
    value: bigint
    logIndex: number
}

export type SafeReceivedLog = {
    type: 'safe_received'
    eventSignature: 'SafeReceived(address,uint256)'
    from: Address
    to: Address
    value: bigint
    logIndex: number
}

export type UserOperationEventLog = {
    type: 'user_operation_event'
    eventSignature: 'UserOperationEvent(bytes32,address,address,uint256,bool,uint256,uint256)'
    userOpHash: `0x${string}`
    sender: Web3.address.Address
    paymaster: Web3.address.Address
    nonce: bigint
    success: boolean
    actualGasCost: bigint
    actualGasUsed: bigint
    logIndex: number
}

export type UnknownLog = {
    type: 'unknown'
    log: unknown
    logIndex: number
}
