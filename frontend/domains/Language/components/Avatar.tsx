import { AvatarSize } from '@zeal/uikit/Avatar'
import { Language } from '@zeal/uikit/Language'

import { notReachable } from '@zeal/toolkit'

import { Avatar as CountryAvatar } from '@zeal/domains/Country/components/Avatar'

type Props = {
    language: Language
    size: AvatarSize
}

export const Avatar = ({ language, size }: Props) => {
    switch (language) {
        case 'en-GB':
            return <CountryAvatar size={size} countryCode="GB" />
        case 'bg-BG':
            return <CountryAvatar size={size} countryCode="BG" />
        case 'fr-FR':
            return <CountryAvatar size={size} countryCode="FR" />
        case 'fr-BE':
            return <CountryAvatar size={size} countryCode="BE" />
        case 'de-DE':
            return <CountryAvatar size={size} countryCode="DE" />
        case 'es-ES':
            return <CountryAvatar size={size} countryCode="ES" />
        case 'ca-ES':
            return <CountryAvatar size={size} countryCode="ES" />
        case 'pt-BR':
            return <CountryAvatar size={size} countryCode="BR" />
        case 'pt-PT':
            return <CountryAvatar size={size} countryCode="PT" />
        case 'it-IT':
            return <CountryAvatar size={size} countryCode="IT" />
        case 'hr-HR':
            return <CountryAvatar size={size} countryCode="HR" />
        case 'hu-HU':
            return <CountryAvatar size={size} countryCode="HU" />
        case 'is-IS':
            return <CountryAvatar size={size} countryCode="IS" />
        case 'lb-LU':
            return <CountryAvatar size={size} countryCode="LU" />
        case 'lt-LT':
            return <CountryAvatar size={size} countryCode="LT" />
        case 'lv-LV':
            return <CountryAvatar size={size} countryCode="LV" />
        case 'mt-MT':
            return <CountryAvatar size={size} countryCode="MT" />
        case 'nb-NO':
            return <CountryAvatar size={size} countryCode="NO" />
        case 'nl-BE':
            return <CountryAvatar size={size} countryCode="BE" />
        case 'nl-NL':
            return <CountryAvatar size={size} countryCode="NL" />
        case 'pl-PL':
            return <CountryAvatar size={size} countryCode="PL" />
        case 'uk-UA':
            return <CountryAvatar size={size} countryCode="UA" />
        case 'sv-SE':
            return <CountryAvatar size={size} countryCode="SE" />
        case 'af-ZA':
            return <CountryAvatar size={size} countryCode="ZA" />
        case 'cs-CZ':
            return <CountryAvatar size={size} countryCode="CZ" />
        case 'da-DK':
            return <CountryAvatar size={size} countryCode="DK" />
        case 'el-GR':
            return <CountryAvatar size={size} countryCode="GR" />
        case 'et-EE':
            return <CountryAvatar size={size} countryCode="EE" />
        case 'fi-FI':
            return <CountryAvatar size={size} countryCode="FI" />
        case 'ro-RO':
            return <CountryAvatar size={size} countryCode="RO" />
        case 'sk-SK':
            return <CountryAvatar size={size} countryCode="SK" />
        case 'sl-SI':
            return <CountryAvatar size={size} countryCode="SI" />
        case 'sq-AL':
            return <CountryAvatar size={size} countryCode="AL" />
        case 'sr-RS':
            return <CountryAvatar size={size} countryCode="RS" />
        case 'tr-TR':
            return <CountryAvatar size={size} countryCode="TR" />
        case 'ga-IE':
            return <CountryAvatar size={size} countryCode="IE" />
        default:
            return notReachable(language)
    }
}
