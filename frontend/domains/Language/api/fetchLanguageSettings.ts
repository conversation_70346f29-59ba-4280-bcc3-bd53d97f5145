import { fetchStaticData } from '@zeal/api/fetchStaticData'
import { getLocales } from 'expo-localization'

import {
    DEFAULT_LANGUAGE,
    Language,
    LanguageSettings,
    SUPPORTED_LANGUAGES,
} from '@zeal/uikit/Language'

import { notReachable } from '@zeal/toolkit'
import { recordOf, string } from '@zeal/toolkit/Result'
import * as Storage from '@zeal/toolkit/Storage'

import { captureError } from '@zeal/domains/Error/helpers/captureError'

import { parseLanguage } from '../helpers/parseLanguage'
const LOCAL_STORE_KEY = 'language_settings'

export const fetchLanguageSettingsWithCache =
    async (): Promise<LanguageSettings> => {
        const value = await Storage.local.get(LOCAL_STORE_KEY)
        const parsedLanguage =
            parseLanguage(value).getSuccessResult() ||
            getSystemLanguage() ||
            DEFAULT_LANGUAGE

        return {
            currentSelectedLanguage: parsedLanguage,
            formattedMessagesMap: await fetchMessages(parsedLanguage),
        }
    }

export const fetchMessages = async (
    language: Language
): Promise<Record<string, string>> => {
    try {
        switch (language) {
            case 'en-GB':
                return {} as Record<string, string>
            case 'bg-BG':
            case 'fr-FR':
            case 'fr-BE':
            case 'de-DE':
            case 'es-ES':
            case 'ca-ES':
            case 'pt-BR':
            case 'pt-PT':
            case 'it-IT':
            case 'hr-HR':
            case 'hu-HU':
            case 'is-IS':
            case 'lb-LU':
            case 'lt-LT':
            case 'lv-LV':
            case 'mt-MT':
            case 'nb-NO':
            case 'nl-BE':
            case 'nl-NL':
            case 'pl-PL':
            case 'uk-UA':
            case 'sv-SE':
            case 'cs-CZ':
            case 'da-DK':
            case 'el-GR':
            case 'et-EE':
            case 'fi-FI':
            case 'af-ZA':
            case 'ro-RO':
            case 'sk-SK':
            case 'sl-SI':
            case 'sq-AL':
            case 'sr-RS':
            case 'tr-TR':
            case 'ga-IE':
                return fetchStaticData(
                    `@zeal/assets/data/${language}.json`,
                    (input) =>
                        recordOf(input, {
                            keyParser: string,
                            valueParser: string,
                        })
                )

            default:
                return notReachable(language)
        }
    } catch (e) {
        captureError(e)
        return {} as Record<string, string>
    }
}

const getSystemLanguage = (): Language | null => {
    const [locale] = getLocales()
    const supportedLanguageExactMatch = SUPPORTED_LANGUAGES.find(
        (lang) => lang === locale.languageTag
    )
    const [userLanguageLanguagePart] = locale.languageTag.split('-')
    const supportedLanguageNotExactMatch = SUPPORTED_LANGUAGES.find((lang) => {
        const [languagePart] = lang.split('-')
        return languagePart === userLanguageLanguagePart
    })
    return supportedLanguageExactMatch || supportedLanguageNotExactMatch || null
}

export const updateLanguageSettings = async (language: Language) => {
    await Storage.local.set(LOCAL_STORE_KEY, language)
}
