import { Column } from '@zeal/uikit/Column'
import {
    Refresh<PERSON>ontainer,
    RefreshContainerState,
} from '@zeal/uikit/RefreshContainer'
import { Screen } from '@zeal/uikit/Screen'

import { notReachable } from '@zeal/toolkit'
import { LoadedReloadableData } from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    FetchPortfolioRequest,
    FetchPortfolioResponse,
} from '@zeal/domains/Account/api/fetchAccounts'
import { CardConfig } from '@zeal/domains/Card'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SubmittedOfframpTransaction } from '@zeal/domains/Currency/domains/BankTransfer'
import { SubmitedBridgesMap } from '@zeal/domains/Currency/domains/Bridge'
import { SwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { WalletConnectInstanceLoadable } from '@zeal/domains/DApp/domains/WalletConnect/api/fetchWalletConnectInstance'
import {
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
    TotalEarningsInDefaultCurrencyMap,
} from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Mode } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    BankTransferInfo,
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { Submited } from '@zeal/domains/TransactionRequest'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Loaded } from './Loaded'

type Props = {
    keystoreMap: KeyStoreMap
    submitedBridgesMap: SubmitedBridgesMap
    submittedOffRampTransactions: SubmittedOfframpTransaction[]
    transactionRequests: Record<Web3.address.Address, Submited[]>
    account: Account
    accountsMap: AccountsMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    portfolioLoadable: LoadedReloadableData<
        FetchPortfolioResponse,
        FetchPortfolioRequest
    >
    portfolioMap: PortfolioMap
    bankTransferInfo: BankTransferInfo
    currencyHiddenMap: CurrencyHiddenMap
    customCurrencyMap: CustomCurrencyMap
    isEthereumNetworkFeeWarningSeen: boolean
    totalEarningsInDefaultCurrencyMap: TotalEarningsInDefaultCurrencyMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    currencyPinMap: CurrencyPinMap
    installationId: string
    mode: Mode
    earnTakerMetrics: EarnTakerMetrics
    sessionPassword: string
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    walletConnectInstanceLoadable: WalletConnectInstanceLoadable
    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    refreshContainerState: RefreshContainerState
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    installationCampaign: string | null
    encryptedPassword: string
    referralConfig: ReferralConfig
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof Loaded> | { type: 'on_portfolio_refresh_pulled' }

const getScreenPadding = (
    mode: Mode
): React.ComponentProps<typeof RefreshContainer>['padding'] => {
    switch (mode) {
        case 'fullscreen':
            return 'home_screen'
        case 'popup':
            return 'controller_tabs_popup'
        default:
            return notReachable(mode)
    }
}

export const Layout = ({
    account,
    portfolioLoadable,
    portfolioMap,
    earnHistoricalTakerUserCurrencyRateMap,
    swapsIOSwapRequestsMap,
    transactionActivitiesCacheMap,
    totalEarningsInDefaultCurrencyMap,
    keystoreMap,
    celebrationConfig,
    appRating,
    installationCampaign,
    accountsMap,
    submitedBridgesMap,
    submittedOffRampTransactions,
    transactionRequests,
    networkMap,
    networkRPCMap,
    isEthereumNetworkFeeWarningSeen,
    earnTakerMetrics,
    bankTransferInfo,
    currencyHiddenMap,
    customCurrencyMap,
    currencyPinMap,
    installationId,
    walletConnectInstanceLoadable,
    mode,
    sessionPassword,
    gasCurrencyPresetMap,
    feePresetMap,
    cardConfig,
    defaultCurrencyConfig,
    refreshContainerState,
    encryptedPassword,
    referralConfig,
    experimentalMode,
    onMsg,
}: Props) => {
    return (
        <Screen
            padding="home_screen"
            background="default"
            onNavigateBack={null}
        >
            <Column spacing={0} shrink fill>
                <RefreshContainer
                    padding={getScreenPadding(mode)}
                    onRefreshPulled={() =>
                        onMsg({ type: 'on_portfolio_refresh_pulled' })
                    }
                    state={refreshContainerState}
                >
                    <Loaded
                        experimentalMode={experimentalMode}
                        referralConfig={referralConfig}
                        encryptedPassword={encryptedPassword}
                        installationCampaign={installationCampaign}
                        appRating={appRating}
                        celebrationConfig={celebrationConfig}
                        refreshContainerState={refreshContainerState}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        feePresetMap={feePresetMap}
                        totalEarningsInDefaultCurrencyMap={
                            totalEarningsInDefaultCurrencyMap
                        }
                        earnHistoricalTakerUserCurrencyRateMap={
                            earnHistoricalTakerUserCurrencyRateMap
                        }
                        swapsIOSwapRequestsMap={swapsIOSwapRequestsMap}
                        transactionActivitiesCacheMap={
                            transactionActivitiesCacheMap
                        }
                        earnTakerMetrics={earnTakerMetrics}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        sessionPassword={sessionPassword}
                        mode={mode}
                        installationId={installationId}
                        submittedOffRampTransactions={
                            submittedOffRampTransactions
                        }
                        currencyHiddenMap={currencyHiddenMap}
                        customCurrencyMap={customCurrencyMap}
                        currencyPinMap={currencyPinMap}
                        bankTransferInfo={bankTransferInfo}
                        accountsMap={accountsMap}
                        networkMap={networkMap}
                        submitedBridgesMap={submitedBridgesMap}
                        transactionRequests={transactionRequests}
                        keystoreMap={keystoreMap}
                        walletConnectInstanceLoadable={
                            walletConnectInstanceLoadable
                        }
                        portfolioLoadable={portfolioLoadable}
                        portfolioMap={portfolioMap}
                        account={account}
                        networkRPCMap={networkRPCMap}
                        cardConfig={cardConfig}
                        onMsg={onMsg}
                    />
                </RefreshContainer>
            </Column>
        </Screen>
    )
}
