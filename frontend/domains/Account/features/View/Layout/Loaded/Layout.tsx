import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { OutlineDoubleArrows } from '@zeal/uikit/Icon/OutlineDoubleArrows'
import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'
import { Row } from '@zeal/uikit/Row'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { LoadedReloadableData } from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { keys } from '@zeal/toolkit/Object'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    FetchPortfolioRequest,
    FetchPortfolioResponse,
} from '@zeal/domains/Account/api/fetchAccounts'
import { Widget } from '@zeal/domains/Account/components/Widget'
import { CardConfig } from '@zeal/domains/Card'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SubmittedOfframpTransaction } from '@zeal/domains/Currency/domains/BankTransfer'
import { SubmitedBridgesMap } from '@zeal/domains/Currency/domains/Bridge'
import { SwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { BridgeWidget } from '@zeal/domains/Currency/features/BridgeWidget'
import { WalletConnectInstanceLoadable } from '@zeal/domains/DApp/domains/WalletConnect/api/fetchWalletConnectInstance'
import {
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
    TotalEarningsInDefaultCurrencyMap,
} from '@zeal/domains/Earn'
import { KeyStore, KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { Mode } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { View as ViewPortfolio } from '@zeal/domains/Portfolio/features/View'
import {
    BankTransferInfo,
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { Submited } from '@zeal/domains/TransactionRequest'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { ActionBar } from '../ActionBar'

type Props = {
    account: Account
    submitedBridgesMap: SubmitedBridgesMap
    submittedOffRampTransactions: SubmittedOfframpTransaction[]
    transactionRequests: Record<Web3.address.Address, Submited[]>
    portfolioLoadable: LoadedReloadableData<
        FetchPortfolioResponse,
        FetchPortfolioRequest
    >
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    keystore: KeyStore
    bankTransferInfo: BankTransferInfo
    currencyHiddenMap: CurrencyHiddenMap
    sessionPassword: string
    feePresetMap: FeePresetMap
    isEthereumNetworkFeeWarningSeen: boolean
    customCurrencies: CustomCurrencyMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    earnTakerMetrics: EarnTakerMetrics
    currencyPinMap: CurrencyPinMap
    installationId: string
    mode: Mode
    walletConnectInstanceLoadable: WalletConnectInstanceLoadable
    totalEarningsInDefaultCurrencyMap: TotalEarningsInDefaultCurrencyMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    refreshContainerState: RefreshContainerState
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    encryptedPassword: string
    installationCampaign: string | null
    referralConfig: ReferralConfig
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof ActionBar>
    | MsgOf<typeof Widget>
    | MsgOf<typeof ViewPortfolio>
    | MsgOf<typeof BridgeWidget>
    | { type: 'on_account_name_clicked' }

export const Layout = ({
    account,
    portfolioLoadable,
    earnTakerMetrics,
    accountsMap,
    keyStoreMap,
    totalEarningsInDefaultCurrencyMap,
    earnHistoricalTakerUserCurrencyRateMap,
    swapsIOSwapRequestsMap,
    transactionActivitiesCacheMap,
    walletConnectInstanceLoadable,
    keystore,
    installationCampaign,
    submitedBridgesMap,
    transactionRequests,
    submittedOffRampTransactions,
    networkMap,
    networkRPCMap,
    bankTransferInfo,
    currencyHiddenMap,
    isEthereumNetworkFeeWarningSeen,
    currencyPinMap,
    appRating,
    celebrationConfig,
    installationId,
    customCurrencies,
    feePresetMap,
    gasCurrencyPresetMap,
    sessionPassword,
    portfolioMap,
    mode,
    cardConfig,
    defaultCurrencyConfig,
    refreshContainerState,
    encryptedPassword,
    referralConfig,
    experimentalMode,
    onMsg,
}: Props) => {
    return (
        <>
            <ActionBar
                installationId={installationId}
                mode={mode}
                networkMap={networkMap}
                onMsg={onMsg}
            />
            <Column spacing={8}>
                <AccountName
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    currentAccount={account}
                    onClick={() => {
                        onMsg({ type: 'on_account_name_clicked' })
                    }}
                />
                <Widget
                    refreshContainerState={refreshContainerState}
                    keyStore={keystore}
                    portfolioLoadable={portfolioLoadable}
                    networkMap={networkMap}
                    accountsMap={accountsMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    walletConnectInstanceLoadable={
                        walletConnectInstanceLoadable
                    }
                    installationId={installationId}
                    currencyHiddenMap={currencyHiddenMap}
                    keyStoreMap={keyStoreMap}
                    currentAccount={account}
                    cardConfig={cardConfig}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    onMsg={onMsg}
                />

                <ViewPortfolio
                    experimentalMode={experimentalMode}
                    referralConfig={referralConfig}
                    installationCampaign={installationCampaign}
                    encryptedPassword={encryptedPassword}
                    celebrationConfig={celebrationConfig}
                    appRating={appRating}
                    refreshContainerState={refreshContainerState}
                    customCurrencies={customCurrencies}
                    earnTakerMetrics={earnTakerMetrics}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    portfolioMap={portfolioMap}
                    totalEarningsInDefaultCurrencyMap={
                        totalEarningsInDefaultCurrencyMap
                    }
                    swapsIOSwapRequestsMap={swapsIOSwapRequestsMap}
                    transactionActivitiesCacheMap={
                        transactionActivitiesCacheMap
                    }
                    earnHistoricalTakerUserCurrencyRateMap={
                        earnHistoricalTakerUserCurrencyRateMap
                    }
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    sessionPassword={sessionPassword}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    feePresetMap={feePresetMap}
                    installationId={installationId}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    keyStoreMap={keyStoreMap}
                    accountsMap={accountsMap}
                    networkMap={networkMap}
                    submitedBridgesMap={submitedBridgesMap}
                    transactionRequests={transactionRequests}
                    networkRPCMap={networkRPCMap}
                    account={account}
                    portfolio={portfolioLoadable.data.portfolio}
                    bankTransferInfo={bankTransferInfo}
                    submittedOffRampTransactions={submittedOffRampTransactions}
                    cardConfig={cardConfig}
                    onMsg={onMsg}
                />
            </Column>
        </>
    )
}

const COUNT_ACTIVE_ACCOUNTS_FOR_DISPLAY_ACCOUNT_LABEL = 2

const AccountName = ({
    accountsMap,
    currentAccount,
    keyStoreMap,
    onClick,
}: {
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    currentAccount: Account
    onClick: () => void
}) => {
    const activeAccounts = keys(accountsMap).filter((address) => {
        const keyStore = getKeyStore({
            address,
            keyStoreMap,
        })
        switch (keyStore.type) {
            case 'private_key_store':
            case 'ledger':
            case 'secret_phrase_key':
            case 'trezor':
            case 'safe_4337':
                return true
            case 'track_only':
                return false
            default:
                return notReachable(keyStore)
        }
    })

    if (
        activeAccounts.length > COUNT_ACTIVE_ACCOUNTS_FOR_DISPLAY_ACCOUNT_LABEL
    ) {
        return (
            <Clickable onClick={onClick}>
                <Row spacing={2}>
                    <Text color="gray20">{currentAccount.label}</Text>

                    <OutlineDoubleArrows size={24} color="gray20" />
                </Row>
            </Clickable>
        )
    }

    return null
}
