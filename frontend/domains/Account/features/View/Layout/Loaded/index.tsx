import { useState } from 'react'

import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'

import { notReachable } from '@zeal/toolkit'
import { LoadedReloadableData } from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    FetchPortfolioRequest,
    FetchPortfolioResponse,
} from '@zeal/domains/Account/api/fetchAccounts'
import { CardConfig } from '@zeal/domains/Card'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SubmittedOfframpTransaction } from '@zeal/domains/Currency/domains/BankTransfer'
import { SubmitedBridgesMap } from '@zeal/domains/Currency/domains/Bridge'
import { SwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { WalletConnectInstanceLoadable } from '@zeal/domains/DApp/domains/WalletConnect/api/fetchWalletConnectInstance'
import {
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
    TotalEarningsInDefaultCurrencyMap,
} from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { Mode } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    BankTransferInfo,
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { Submited } from '@zeal/domains/TransactionRequest'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    keystoreMap: KeyStoreMap
    submitedBridgesMap: SubmitedBridgesMap
    submittedOffRampTransactions: SubmittedOfframpTransaction[]
    transactionRequests: Record<Web3.address.Address, Submited[]>
    portfolioLoadable: LoadedReloadableData<
        FetchPortfolioResponse,
        FetchPortfolioRequest
    >
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    account: Account
    networkMap: NetworkMap
    isEthereumNetworkFeeWarningSeen: boolean
    networkRPCMap: NetworkRPCMap
    walletConnectInstanceLoadable: WalletConnectInstanceLoadable
    bankTransferInfo: BankTransferInfo
    currencyHiddenMap: CurrencyHiddenMap
    customCurrencyMap: CustomCurrencyMap
    earnTakerMetrics: EarnTakerMetrics
    currencyPinMap: CurrencyPinMap
    installationId: string
    mode: Mode
    sessionPassword: string
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    totalEarningsInDefaultCurrencyMap: TotalEarningsInDefaultCurrencyMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    refreshContainerState: RefreshContainerState
    installationCampaign: string | null
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    encryptedPassword: string
    referralConfig: ReferralConfig
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'reload_button_click' }
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_profile_change_confirm_click'
                  | 'on_custom_currency_delete_request'
                  | 'on_custom_currency_update_request'
                  | 'on_send_nft_click'
                  | 'bank_transfer_click'
                  | 'receive_click'
                  | 'from_any_wallet_click'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_swap_clicked'
                  | 'on_token_hide_click'
                  | 'on_token_un_pin_click'
                  | 'on_token_un_hide_click'
                  | 'on_token_pin_click'
                  | 'on_bridge_clicked'
                  | 'on_send_clicked'
                  | 'on_bank_transfer_selected'
                  | 'on_discover_more_apps_clicked'
                  | 'on_dapp_link_clicked'
                  | 'on_tokens_refresh_pulled'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'account_filter_click'
          }
      >
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_card_b_reward_dissmiss_clicked'
                  | 'card_breward_claimed'
                  | 'card_brewards_updated'
                  | 'on_dismiss_add_to_wallet_banner_clicked'
                  | 'on_cashback_token_click'
                  | 'on_settings_clicked'
                  | 'on_balance_clicked'
                  | 'on_account_name_clicked'
                  | 'bridge_completed'
                  | 'on_refresh_button_clicked'
                  | 'on_bridge_submitted_click'
                  | 'on_dismiss_kyc_button_clicked'
                  | 'on_do_bank_transfer_clicked'
                  | 'on_kyc_try_again_clicked'
                  | 'on_onramp_success'
                  | 'on_recovery_kit_setup'
                  | 'on_tracked_tag_click'
                  | 'on_transaction_request_widget_click'
                  | 'transaction_request_cancelled'
                  | 'transaction_request_completed'
                  | 'transaction_request_failed'
                  | 'on_withdrawal_monitor_fiat_transaction_success'
                  | 'transaction_request_replaced'
                  | 'on_nba_close_click'
                  | 'on_nba_cta_click'
                  | 'on_open_fullscreen_view_click'
                  | 'on_zwidget_expand_request'
                  | 'on_send_clicked'
                  | 'on_discover_more_apps_clicked'
                  | 'on_placeholder_dapp_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_asset_added_to_earn'
                  | 'on_earn_withdrawal_success'
                  | 'on_earn_deposit_success'
                  | 'track_wallet_clicked'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_recharge_configured'
                  | 'on_external_earn_deposit_completed_close_click'
                  | 'on_card_marketing_card_click'
                  | 'on_card_widget_clicked'
                  | 'earn_and_card_widget_view_card_clicked'
                  | 'on_earn_last_recharge_transaction_hash_loaded'
                  | 'on_cashback_loaded'
                  | 'on_earn_taker_address_click'
                  | 'on_earnings_fetched'
                  | 'on_address_scanned'
                  | 'on_add_funds_click'
                  | 'on_bank_transfer_selected'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'cancel_submitted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_submited'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_buy_clicked'
                  | 'on_earn_configured'
                  | 'on_meta_mask_mode_changed_pupup_refresh_page_clicked'
                  | 'on_dismiss_bridge_widget_click'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'import_card_owner_clicked'
                  | 'on_get_cashback_currency_clicked'
                  | 'on_cashback_widget_clicked'
                  | 'on_card_activity_transactions_loaded'
                  | 'on_card_onboarded_account_state_received'
                  | 'on_dissmiss_card_kyc_onboarding_widget_clicked'
                  | 'on_app_rating_submitted'
                  | 'on_cashback_celebration_triggered'
                  | 'on_earn_celebration_triggered'
                  | 'on_top_up_transaction_complete_close'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_swaps_io_transaction_activity_completed'
                  | 'on_swaps_io_transaction_activity_swap_started'
                  | 'on_swaps_io_transaction_activity_failed'
                  | 'on_activate_free_card_clicked'
                  | 'on_transaction_activities_loaded'
                  | 'on_see_all_transactions_clicked'
                  | 'on_swaps_io_pending_transaction_activity_completed'
                  | 'on_pending_send_transaction_activity_completed'
                  | 'on_pending_send_transaction_activity_failed'
                  | 'on_dissmiss_card_kyc_onboarded_widget_clicked'
                  | 'on_pending_breward_claim_transaction_activity_completed'
                  | 'on_pending_breward_claim_transaction_activity_failed'
                  | 'on_pending_areward_claim_transaction_activity_completed'
                  | 'on_pending_areward_claim_transaction_activity_failed'
                  | 'on_pending_card_top_up_state_changed'
                  | 'on_card_top_up_success'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_completed_transaction_close_click'
                  | 'on_swap_cancelled_close_clicked'
                  | 'on_swap_success_clicked'
                  | 'on_pending_card_balance_timer_completed'
          }
      >

export const Loaded = ({
    portfolioLoadable,
    portfolioMap,
    account,
    walletConnectInstanceLoadable,
    accountsMap,
    keystoreMap,
    earnTakerMetrics,
    submitedBridgesMap,
    celebrationConfig,
    appRating,
    submittedOffRampTransactions,
    transactionRequests,
    networkMap,
    installationCampaign,
    networkRPCMap,
    bankTransferInfo,
    currencyHiddenMap,
    currencyPinMap,
    installationId,
    mode,
    customCurrencyMap,
    sessionPassword,
    gasCurrencyPresetMap,
    isEthereumNetworkFeeWarningSeen,
    feePresetMap,
    cardConfig,
    totalEarningsInDefaultCurrencyMap,
    earnHistoricalTakerUserCurrencyRateMap,
    swapsIOSwapRequestsMap,
    transactionActivitiesCacheMap,
    defaultCurrencyConfig,
    refreshContainerState,
    encryptedPassword,
    referralConfig,
    experimentalMode,
    onMsg,
}: Props) => {
    const [state, setState] = useState<ModalState>({ type: 'closed' })
    const keystore = getKeyStore({
        keyStoreMap: keystoreMap,
        address: account.address,
    })

    return (
        <>
            <Layout
                experimentalMode={experimentalMode}
                referralConfig={referralConfig}
                encryptedPassword={encryptedPassword}
                installationCampaign={installationCampaign}
                appRating={appRating}
                celebrationConfig={celebrationConfig}
                refreshContainerState={refreshContainerState}
                customCurrencies={customCurrencyMap}
                earnTakerMetrics={earnTakerMetrics}
                defaultCurrencyConfig={defaultCurrencyConfig}
                portfolioMap={portfolioMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                sessionPassword={sessionPassword}
                mode={mode}
                installationId={installationId}
                currencyHiddenMap={currencyHiddenMap}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                currencyPinMap={currencyPinMap}
                networkMap={networkMap}
                accountsMap={accountsMap}
                keyStoreMap={keystoreMap}
                submitedBridgesMap={submitedBridgesMap}
                transactionRequests={transactionRequests}
                submittedOffRampTransactions={submittedOffRampTransactions}
                keystore={keystore}
                walletConnectInstanceLoadable={walletConnectInstanceLoadable}
                portfolioLoadable={portfolioLoadable}
                account={account}
                networkRPCMap={networkRPCMap}
                bankTransferInfo={bankTransferInfo}
                totalEarningsInDefaultCurrencyMap={
                    totalEarningsInDefaultCurrencyMap
                }
                earnHistoricalTakerUserCurrencyRateMap={
                    earnHistoricalTakerUserCurrencyRateMap
                }
                swapsIOSwapRequestsMap={swapsIOSwapRequestsMap}
                transactionActivitiesCacheMap={transactionActivitiesCacheMap}
                cardConfig={cardConfig}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_earn_withdrawal_success':
                        case 'on_earn_deposit_success':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_zwidget_expand_request':
                        case 'on_transaction_request_widget_click':
                        case 'transaction_request_completed':
                        case 'transaction_request_failed':
                        case 'on_settings_clicked':
                        case 'on_balance_clicked':
                        case 'reload_button_click':
                        case 'bridge_completed':
                        case 'on_bridge_submitted_click':
                        case 'on_refresh_button_clicked':
                        case 'on_dismiss_kyc_button_clicked':
                        case 'on_kyc_try_again_clicked':
                        case 'on_do_bank_transfer_clicked':
                        case 'on_onramp_success':
                        case 'on_withdrawal_monitor_fiat_transaction_success':
                        case 'transaction_request_replaced':
                        case 'on_open_fullscreen_view_click':
                        case 'on_send_clicked':
                        case 'on_buy_clicked':
                        case 'on_discover_more_apps_clicked':
                        case 'on_recharge_configured':
                        case 'on_card_marketing_card_click':
                        case 'on_earn_taker_address_click':
                        case 'on_recovery_kit_setup':
                        case 'on_earnings_fetched':
                        case 'on_account_create_request':
                        case 'add_wallet_clicked':
                        case 'track_wallet_clicked':
                        case 'hardware_wallet_clicked':
                        case 'on_bank_transfer_selected':
                        case 'on_accounts_create_success_animation_finished':
                        case 'on_add_label_to_track_only_account_during_send':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'cancel_submitted':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_submited':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_earn_configured':
                        case 'on_meta_mask_mode_changed_pupup_refresh_page_clicked':
                        case 'on_dismiss_bridge_widget_click':
                        case 'on_address_scanned':
                        case 'on_address_scanned_and_add_label':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_card_widget_clicked':
                        case 'on_cashback_loaded':
                        case 'safe_wallet_clicked':
                        case 'recover_safe_wallet_clicked':
                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                        case 'on_account_name_clicked':
                        case 'import_card_owner_clicked':
                        case 'on_get_cashback_currency_clicked':
                        case 'on_cashback_widget_clicked':
                        case 'on_cashback_token_click':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_chf_taker_metrics_loaded':
                        case 'on_card_onboarded_account_state_received':
                        case 'on_dissmiss_card_kyc_onboarding_widget_clicked':
                        case 'on_dissmiss_card_kyc_onboarded_widget_clicked':
                        case 'on_app_rating_submitted':
                        case 'on_cashback_celebration_triggered':
                        case 'on_earn_celebration_triggered':
                        case 'on_top_up_transaction_complete_close':
                        case 'on_swaps_io_swap_request_created':
                        case 'on_swaps_io_transaction_activity_swap_started':
                        case 'on_swaps_io_transaction_activity_completed':
                        case 'on_swaps_io_transaction_activity_failed':
                        case 'on_activate_free_card_clicked':
                        case 'on_dismiss_add_to_wallet_banner_clicked':
                        case 'on_transaction_activities_loaded':
                        case 'on_see_all_transactions_clicked':
                        case 'on_pending_send_transaction_activity_completed':
                        case 'on_pending_send_transaction_activity_failed':
                        case 'on_pending_breward_claim_transaction_activity_failed':
                        case 'on_pending_breward_claim_transaction_activity_completed':
                        case 'on_pending_areward_claim_transaction_activity_completed':
                        case 'on_pending_areward_claim_transaction_activity_failed':
                        case 'on_pending_card_top_up_state_changed':
                        case 'on_card_b_reward_dissmiss_clicked':
                        case 'card_breward_claimed':
                        case 'card_brewards_updated':
                        case 'on_card_top_up_success':
                        case 'on_add_funds_click':
                        case 'on_completed_safe_transaction_close_click':
                        case 'on_completed_transaction_close_click':
                        case 'on_swap_cancelled_close_clicked':
                        case 'on_swap_success_clicked':
                        case 'on_pending_card_balance_timer_completed':
                            onMsg(msg)
                            break
                        case 'on_token_click':
                            setState({
                                type: 'send_or_receive',
                                token: msg.token,
                            })
                            break
                        case 'show_all_tokens_click':
                            setState({ type: 'show_all_tokens' })
                            break
                        case 'show_all_apps_click':
                            setState({ type: 'show_all_apps' })
                            break
                        case 'show_all_nft_click':
                            setState({ type: 'show_all_nfts' })
                            break
                        case 'on_cash_item_clicked':
                            setState({ type: 'show_cash_tokens' })
                            break
                        case 'on_nft_click':
                            setState({
                                type: 'nft_detailed_view',
                                nft: msg.nft,
                                nftCollection: msg.nftCollection,
                            })
                            break

                        case 'on_app_position_click':
                            setState({
                                type: 'app_position',
                                app: msg.app,
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                refreshContainerState={refreshContainerState}
                defaultCurrencyConfig={defaultCurrencyConfig}
                installationId={installationId}
                currencyHiddenMap={currencyHiddenMap}
                customCurrencyMap={customCurrencyMap}
                currencyPinMap={currencyPinMap}
                networkMap={networkMap}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                accountsMap={accountsMap}
                networkRPCMap={networkRPCMap}
                keystore={keystore}
                state={state}
                portfolioLoadable={portfolioLoadable}
                account={account}
                keystoreMap={keystoreMap}
                portfolioMap={portfolioMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setState({ type: 'closed' })
                            break

                        case 'on_receive_selected':
                            setState({ type: 'receive' })
                            break

                        case 'on_pairing_uri_scanned':
                            break

                        case 'account_filter_click':
                        case 'on_profile_change_confirm_click':
                        case 'on_custom_currency_update_request':
                        case 'on_custom_currency_delete_request':
                        case 'on_send_nft_click':
                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                        case 'on_swap_clicked':
                        case 'on_token_hide_click':
                        case 'on_token_un_pin_click':
                        case 'on_token_un_hide_click':
                        case 'on_token_pin_click':
                        case 'on_bridge_clicked':
                        case 'on_send_clicked':
                        case 'on_bank_transfer_selected':
                        case 'on_discover_more_apps_clicked':
                        case 'on_dapp_link_clicked':
                        case 'on_tokens_refresh_pulled':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_address_scanned':
                        case 'on_address_scanned_and_add_label':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
