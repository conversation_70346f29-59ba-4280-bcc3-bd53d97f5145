import appsFlyer from 'react-native-appsflyer'

import { noop, notReachable } from '@zeal/toolkit'
import { getEnvironment, isProduction } from '@zeal/toolkit/Environment'
import { ImperativeError } from '@zeal/toolkit/Error'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import {
    match,
    nullableOf,
    object,
    oneOf,
    Result,
    shape,
    string,
} from '@zeal/toolkit/Result'
import * as Storage from '@zeal/toolkit/Storage'
import { unsafe_addQueryParams } from '@zeal/toolkit/URL/addQueryParams'

import { REFERRAL_CUSTOM_STORE_PAGE_ID } from '@zeal/domains/Card/domains/Reward/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import {
    AF_APP_ID,
    AF_DEV_KEY,
    BRAND_DOMAIN,
    IN_APP_BROWSER_ESCAPE_WRAPPER,
    ONE_LINK_TEMPLATE_ID,
    ONE_LINK_TEMPLATE_URL,
    PLAY_STORE_REFERRAL_LISTING,
    REFERRAL_LINK_IOS_CPP_ID,
} from './constants'

export type DeepLinkData =
    | {
          type: 'referral'
          referralCode: string
          referrerInstallationId: string | null
          utm_channel: string | null
      }
    | {
          type: 'attribution'
          campaign: string | null
          media_source: string | null
          utm_campaign: string | null
          utm_channel: string | null
          rawEvent: unknown
      }

export type AppsflyerEvent =
    | { type: 'af_activation_reward' }
    | { type: 'af_card_created'; cardType: 'virtual' | 'physical' }
    | { type: 'af_card_created_first_time'; cardType: 'virtual' | 'physical' }
    | { type: 'af_earn_deposit'; asset: 'usd' | 'eur' | 'eth' | 'chf' }
    | {
          type: 'af_earn_deposit_first_time'
          asset: 'usd' | 'eur' | 'eth' | 'chf'
      }
    | { type: 'af_funded_wallet_viewed' }
    | { type: 'af_funded_wallet_viewed_first_time' }
    | { type: 'af_kyc_completed' }
    | { type: 'af_kyc_entered' }
    | { type: 'af_kyc_entered_first_time' }
    | { type: 'af_qualified_50_spend' }
    | { type: 'af_wallet_installed' }

export const isAppsflyerEnabled = (): boolean => {
    switch (ZealPlatform.OS) {
        case 'web':
            return false
        case 'ios':
        case 'android':
            return true
        default:
            return notReachable(ZealPlatform)
    }
}

/*
  Most appsflyer functions are callback based, although they do not return classic (err, res) tuples.
  Looking at implementation it feels like it just passes the call to native SDK, while sources for native SDK are not open.
  In RN wrapper they call native module synchronously, and then call callback with "Success" string as argument.
  So here we try to convert those to Promises, because they still have those callbacks, and adding some try..catch for safety, although
  it feels like most of those calls are synchronous.
*/

export const init = async ({
    installationId,
    onDeepLink,
}: {
    installationId: string
    onDeepLink: (data: DeepLinkData) => void
}): Promise<{
    unsubscribe: () => void
}> => {
    const onAttributionFailureUnsub = appsFlyer.onAttributionFailure(
        isProduction()
            ? (data: unknown) =>
                  captureError(
                      new ImperativeError('[Appsflyer] onAttributionFailure', {
                          data,
                      })
                  )
            : noop
    )

    const onInstallConversionFailureUnsub =
        appsFlyer.onInstallConversionFailure(
            isProduction()
                ? (data: unknown) =>
                      captureError(
                          new ImperativeError(
                              '[Appsflyer] onInstallConversionFailure',
                              {
                                  data,
                              }
                          )
                      )
                : noop
        )

    const listener = (rawData: unknown) => {
        try {
            const parsedData = parseDeepLinkData(
                rawData
            ).getSuccessResultOrThrow(`Failed to parse deep link data`)

            if (!parsedData) {
                postUserEvent({
                    type: 'AppsflyerAttributionDeepLinkMissingEvent',
                    rawData,
                    installationId,
                })
            } else {
                onDeepLink(parsedData)
            }
        } catch (error) {
            captureError(error, { extra: { rawData } })
        }
    }

    const onDeepLinkUnsub = appsFlyer.onDeepLink(listener)
    const ononInstallConversionDataUnsub =
        appsFlyer.onInstallConversionData(listener)
    const onAppOpenAttributionUnsub = appsFlyer.onAppOpenAttribution(listener)

    await new Promise<void>((resolve, reject) => {
        try {
            appsFlyer.setCustomerUserId(installationId, () => resolve())
        } catch (error) {
            reject(error)
        }
    })

    await setOneLinkTemplate(ONE_LINK_TEMPLATE_ID)

    await new Promise<void>((resolve, reject) => {
        try {
            appsFlyer.setOneLinkCustomDomains(
                [BRAND_DOMAIN],
                () => resolve(),
                reject
            )
        } catch (error) {
            reject(error)
        }
    })

    await new Promise<void>((resolve, reject) => {
        appsFlyer.initSdk(
            {
                devKey: AF_DEV_KEY,
                appId: AF_APP_ID,
                isDebug: true,
                onInstallConversionDataListener: true,
                onDeepLinkListener: true,
                manualStart: false,
            },
            () => resolve(),
            (error) => reject(error)
        )
    })

    logAppsflyerEventOncePerInstallation({ type: 'af_wallet_installed' })

    return {
        unsubscribe: () => {
            onAttributionFailureUnsub()
            onInstallConversionFailureUnsub()
            onDeepLinkUnsub()
            ononInstallConversionDataUnsub()
            onAppOpenAttributionUnsub()
        },
    }
}

const setOneLinkTemplate = (templateId: string): Promise<void> =>
    isAppsflyerEnabled()
        ? new Promise((resolve, reject) => {
              try {
                  appsFlyer.setAppInviteOneLinkID(templateId, () => {
                      resolve()
                  })
              } catch (error) {
                  reject(error)
              }
          })
        : Promise.resolve(undefined)

export const generateReferralLink = async (
    referralCode: string
): Promise<string> => {
    const androidUrl = new URL('https://play.google.com/store/apps/details')

    androidUrl.search = new URLSearchParams({
        id: 'app.zeal.wallet',
        listing: PLAY_STORE_REFERRAL_LISTING,
        referrer: `page=${REFERRAL_CUSTOM_STORE_PAGE_ID}`,
    }).toString()

    const link = await new Promise<string>((resolve, reject) => {
        const userParams = {
            deep_link_value: 'referral_page',
            deep_link_sub1: referralCode,
            af_android_url: encodeURIComponent(androidUrl.toString()),
            af_ios_store_cpp: REFERRAL_LINK_IOS_CPP_ID,
        }

        appsFlyer.generateInviteLink(
            {
                channel: 'in_app_share',
                campaign: 'referral_program',
                userParams: userParams,
            },
            (link) => {
                try {
                    resolve(
                        string(link).getSuccessResultOrThrow(
                            'Failed to parse generated link'
                        )
                    )
                } catch (error) {
                    reject(error)
                }
            },
            (err) => {
                reject(err)
            }
        )
    })

    return link
}

const parseDeepLinkData = (
    input: unknown
): Result<unknown, DeepLinkData | null> =>
    object(input).andThen((obj) =>
        oneOf(obj, [
            shape({
                data: match(obj.data, 'deep link not found'),
                deepLinkStatus: match(obj.deepLinkStatus, 'NOT_FOUND'),
            }).map(() => null),
            shape({
                data: object(obj.data).andThen((data) =>
                    oneOf(data, [
                        shape({
                            deep_link_value: match(
                                data.deep_link_value,
                                'referral_page'
                            ),
                            deep_link_sub1: string(data.deep_link_sub1),
                            af_referrer_customer_id: nullableOf(
                                data.af_referrer_customer_id,
                                string
                            ),
                            utm_channel: nullableOf(data.utm_channel, string),
                        }).map((parsed) => ({
                            type: 'referral' as const,
                            referralCode: parsed.deep_link_sub1,
                            referrerInstallationId:
                                parsed.af_referrer_customer_id,
                            utm_channel: parsed.utm_channel,
                        })),

                        shape({
                            campaign: nullableOf(data.campaign, string),
                            media_source: nullableOf(data.media_source, string),
                            utm_campaign: nullableOf(data.utm_campaign, string),
                            utm_channel: nullableOf(data.utm_channel, string),
                        }).map((parsed) => ({
                            type: 'attribution' as const,
                            campaign: parsed.campaign,
                            media_source: parsed.media_source,
                            utm_campaign: parsed.utm_campaign,
                            utm_channel: parsed.utm_channel,
                            rawEvent: input,
                        })),
                    ])
                ),
            }).map((parsed) => parsed.data),
        ])
    )

export const wrapInAppBrowserEscape = ({
    url,
    channel,
}: {
    url: string
    channel: 'copy' | 'system_share'
}) => {
    try {
        const linkToWrap = unsafe_addQueryParams({
            url,
            params: { utm_channel: channel },
        })

        const linkToWrapURL = new URL(linkToWrap)

        return unsafe_addQueryParams({
            url: IN_APP_BROWSER_ESCAPE_WRAPPER,
            params: {
                r: `${linkToWrapURL.pathname.replace(/^\//, '')}${linkToWrapURL.search}`,
            },
        })
    } catch (e) {
        captureError(e)
        return url
    }
}

const unwrapInAppBrowserEscape = (link: string) => {
    if (link.startsWith(IN_APP_BROWSER_ESCAPE_WRAPPER)) {
        try {
            const wrapperRedirect = new URL(link).searchParams.get('r')
            if (wrapperRedirect) {
                const originalLink = new URL(
                    wrapperRedirect,
                    ONE_LINK_TEMPLATE_URL
                )
                return originalLink.toString()
            }
            return link
        } catch (e) {
            captureError(e)
            return link
        }
    } else {
        return link
    }
}

export const reAttributeLink = async (link: string) => {
    const oneLink = unwrapInAppBrowserEscape(link)

    return new Promise<void>((resolve, reject) => {
        try {
            appsFlyer.performOnAppAttribution(
                oneLink,
                () => {
                    resolve()
                },
                reject
            )
        } catch (error) {
            reject(error)
        }
    })
}

export const logAppsflyerEvent = async (
    event: AppsflyerEvent
): Promise<void> => {
    if (isAppsflyerEnabled()) {
        const env = getEnvironment()
        switch (env) {
            case 'production':
                const { type, ...rest } = event
                await appsFlyer.logEvent(type, rest)
                break
            case 'local':
                break
            case 'development':
                logEventToConsole(event)
                break

            default:
                notReachable(env)
        }
    }
}

export const logAppsflyerEventOncePerInstallation = async (
    event: AppsflyerEvent
): Promise<void> => {
    const STORAGE_KEY = `appsflyer_event_${event.type}_logged`
    const alreadyLogged = await Storage.local.get(STORAGE_KEY)

    if (!alreadyLogged) {
        await Storage.local.set(STORAGE_KEY, STORAGE_KEY)
        await logAppsflyerEvent(event)
    }
}

const logEventToConsole = (event: AppsflyerEvent) => {
    switch (ZealPlatform.OS) {
        case 'ios':
            // console.error is the only way for this log to appear in Console app. Prefix for easier filtering in Console tool
            console.error('AppsflyerEvent', event) // eslint-disable-line no-console
            break
        case 'android':
            // just JSON in adb output. use `adb logcat "*:S" ReactNativeJS:V` to see it
            console.log('AppsflyerEvent', event) // eslint-disable-line no-console
            break
        case 'web':
            // Look nice, do not pollute console much
            console.table(event) // eslint-disable-line no-console
            break
        default:
            notReachable(ZealPlatform)
    }
}
