export type UnblockLoginUserDidNotExists = {
    type: 'unblock_login_user_did_not_exists'
}

export type UnblockUserWithAddressAlreadyExists = {
    type: 'unblock_user_with_address_already_exists'
}

export type UnblockInvalidOtpError = { type: 'unblock_invalid_otp' }

export type UnblockMaximumOTPSubmitAttemptsError = {
    type: 'unblock_maximum_otp_attempts_exceeded'
}

export type UnblockOtpExpiredError = {
    type: 'unblock_otp_expired'
}

export type UnblockUserAssociatedWithOtherMerchant = {
    type: 'unblock_user_associated_with_other_merchant'
}
export type UnblockNonceAlreadyInUse = {
    type: 'unblock_nonce_already_in_use'
}

export type UnblockUserWithSuchEmailAlreadyExists = {
    type: 'unblock_user_with_such_email_already_exists'
}

export type UnblockAccountNumberAndSortCodeMismatch = {
    type: 'unblock_account_number_and_sort_code_mismatch'
}

export type UnblockInvalidIBAN = {
    type: 'unblock_invalid_iban'
}

export type UnblockInvalidFasterPaymentConfiguration = {
    type: 'unblock_invalid_faster_payment_configuration'
}

export type UnblockHardKycFailure = {
    type: 'unblock_hard_kyc_failure'
}

export type UnblockSessionExpired = {
    type: 'unblock_session_expired'
}

export type UnblockUnsupportedCountry = {
    type: 'unblock_unsupported_country'
}

export type UnblockCanNotChangeDetailsAfterKYC = {
    type: 'unblock_can_not_change_details_after_kyc'
}

export type UnblockError =
    | UnblockAccountNumberAndSortCodeMismatch
    | UnblockHardKycFailure
    | UnblockInvalidFasterPaymentConfiguration
    | UnblockInvalidIBAN
    | UnblockInvalidOtpError
    | UnblockLoginUserDidNotExists
    | UnblockMaximumOTPSubmitAttemptsError
    | UnblockNonceAlreadyInUse
    | UnblockOtpExpiredError
    | UnblockSessionExpired
    | UnblockCanNotChangeDetailsAfterKYC
    | UnblockUserAssociatedWithOtherMerchant
    | UnblockUserWithAddressAlreadyExists
    | UnblockUserWithSuchEmailAlreadyExists
    | UnblockUnsupportedCountry
