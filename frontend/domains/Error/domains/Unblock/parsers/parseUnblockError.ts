import {
    failure,
    match,
    matchRegExp,
    number,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'

import { HttpError } from '@zeal/domains/Error'
import { parseHttpError } from '@zeal/domains/Error/parsers/parseHttpError'

import {
    UnblockAccountNumberAndSortCodeMismatch,
    UnblockCanNotChangeDetailsAfterKYC,
    UnblockError,
    UnblockHardKycFailure,
    UnblockInvalidFasterPaymentConfiguration,
    UnblockInvalidIBAN,
    UnblockInvalidOtpError,
    UnblockLoginUserDidNotExists,
    UnblockMaximumOTPSubmitAttemptsError,
    UnblockNonceAlreadyInUse,
    UnblockOtpExpiredError,
    UnblockSessionExpired,
    UnblockUnsupportedCountry,
    UnblockUserAssociatedWithOtherMerchant,
    UnblockUserWithAddressAlreadyExists,
    UnblockUserWithSuchEmailAlreadyExists,
} from '..'

const parseUnblockHttpError = (input: unknown): Result<unknown, HttpError> =>
    parseHttpError(input).andThen((httpError) =>
        matchRegExp(httpError.urlPathname, /\/proxy\/unblock.*/).andThen(() =>
            success(httpError)
        )
    )

const parseUnblockSessionExpired = (
    input: unknown
): Result<unknown, UnblockSessionExpired> =>
    parseUnblockHttpError(input)
        .andThen((error) => object(error.responseBody))
        .andThen((err) =>
            object(err.responseBody).andThen((obj) =>
                shape({
                    statusCode: match(err.status, 401),
                    message: string(obj.message).andThen((msg) =>
                        msg.match('Unauthorized')
                            ? success(msg)
                            : failure({
                                  type: 'unauthorized_message_does_not_match_regexp',
                                  msg,
                              })
                    ),
                })
            )
        )
        .map(() => ({ type: 'unblock_session_expired' }) as const)

const parseUnblockUserAlreadyExists = (
    input: unknown
): Result<unknown, UnblockUserWithAddressAlreadyExists> =>
    parseUnblockHttpError(input)
        .andThen((error) =>
            shape({
                body: object(error.responseBody),
                statusCode: number(error.status),
            })
        )
        .andThen(({ body, statusCode }) =>
            oneOf(body, [
                shape({
                    name: match(body.name, 'UserAlreadyExistsError'),
                    message: string(body.message).andThen((msg) =>
                        msg.match(/^user with address/i)
                            ? success(msg)
                            : failure({
                                  type: 'message_does_not_match_regexp',
                                  msg,
                              })
                    ),
                }),
                shape({
                    statusCode: match(statusCode, 409),
                    message: match(
                        body.message,
                        'This target address already exists for a user of this merchant.'
                    ),
                }),
            ])
        )
        .map(
            () =>
                ({ type: 'unblock_user_with_address_already_exists' }) as const
        )

const parseUnblockUserAssociatedWithOtherMerchant = (
    input: unknown
): Result<unknown, UnblockUserAssociatedWithOtherMerchant> =>
    parseUnblockHttpError(input)
        .andThen((error) => object(error.responseBody))
        .andThen((obj) =>
            shape({
                name: match(obj.name, 'UserNotAssociatedWithMerchant'),
                message: string(obj.message).andThen((msg) =>
                    msg.match(
                        /^user with uuid.*not associated with the given merchant/i
                    )
                        ? success(msg)
                        : failure({
                              type: 'message_does_not_match_regexp',
                              msg,
                          })
                ),
            })
        )
        .map(
            () =>
                ({
                    type: 'unblock_user_associated_with_other_merchant',
                }) as const
        )

const parseUnblockOtpExpiredError = (
    input: unknown
): Result<unknown, UnblockOtpExpiredError> =>
    parseUnblockHttpError(input)
        .andThen((error) => object(error.responseBody))
        .andThen((obj) =>
            oneOf(obj, [
                shape({
                    error: match(obj.error, 'TOKEN_EXPIRED'),
                    error_id: string(obj.error_id),
                }),
                shape({
                    error: match(obj.error, 'TOKEN_NOT_FOUND'),
                    error_id: string(obj.error_id),
                }),
            ])
        )
        .map(() => ({ type: 'unblock_otp_expired' }) as const)

const parseUnblockInvalidOtpError = (
    input: unknown
): Result<unknown, UnblockInvalidOtpError> =>
    parseUnblockHttpError(input)
        .andThen((error) => object(error.responseBody))
        .andThen((obj) =>
            shape({
                error: match(obj.error, 'CODE_MISMATCH'),
                error_id: string(obj.error_id),
            })
        )
        .map(() => ({ type: 'unblock_invalid_otp' }) as const)

const parseUnblockMaximumOTPSubmitAttemptsError = (
    input: unknown
): Result<unknown, UnblockMaximumOTPSubmitAttemptsError> =>
    parseUnblockHttpError(input)
        .andThen((error) => object(error.responseBody))
        .andThen((obj) =>
            shape({
                name: match(obj.name, 'ATTEMPTS_EXCEEDED'),
                error_id: string(obj.error_id),
            })
        )
        .map(() => ({ type: 'unblock_maximum_otp_attempts_exceeded' }) as const)

const parseUnblockLoginError = (
    input: unknown
): Result<unknown, UnblockLoginUserDidNotExists> =>
    parseUnblockHttpError(input)
        .andThen((error) => object(error.responseBody))
        .andThen((obj) =>
            shape({
                message: string(obj.message).andThen((msg) =>
                    msg.match(/^user not found/i)
                        ? success(msg)
                        : failure({
                              type: 'message_does_not_match_regexp',
                              msg,
                          })
                ),
                name: match(obj.name, 'AuthBadRequestError'),
            })
        )
        .map(() => ({ type: 'unblock_login_user_did_not_exists' }) as const)

const parseUnblockHardKycFailure = (
    input: unknown
): Result<unknown, UnblockHardKycFailure> =>
    parseUnblockHttpError(input)
        .andThen((error) => object(error.responseBody))
        .andThen((obj) =>
            shape({
                error: string(obj.error).andThen((msg) =>
                    msg.match('KYC status for user is HARD_KYC_FAILED')
                        ? success(msg)
                        : failure({
                              type: 'message_does_not_match_regexp',
                              msg,
                          })
                ),
            })
        )
        .map(
            () =>
                ({
                    type: 'unblock_hard_kyc_failure',
                }) as const
        )

const parseUnblockFasterPaymentsIsInvalid = (
    input: unknown
): Result<unknown, UnblockInvalidFasterPaymentConfiguration> =>
    parseUnblockHttpError(input)
        .andThen((error) => object(error.responseBody))
        .andThen((obj) =>
            shape({
                message: string(obj.error).andThen((msg) =>
                    msg.match('BENEFICIARY_DETAILS_INVALID') &&
                    msg.match('FASTER_PAYMENTS')
                        ? success(msg)
                        : failure({
                              type: 'message_does_not_match_regexp',
                              msg,
                          })
                ),
            })
        )
        .map(() => ({
            type: 'unblock_invalid_faster_payment_configuration' as const,
        }))

const parseUnblockAccountNumberAndSortCodeMismatch = (
    input: unknown
): Result<unknown, UnblockAccountNumberAndSortCodeMismatch> =>
    parseUnblockHttpError(input)
        .andThen((error) => object(error.responseBody))
        .andThen((data) => object(data.error))
        .andThen((obj) =>
            shape({
                statusCode: match(obj.statusCode, 400),
                message: string(obj.message).andThen((msg) =>
                    msg.match('BENEFICIARY_DETAILS_INVALID') &&
                    msg.match('accountNumber_sortCode_mismatch')
                        ? success(msg)
                        : failure({
                              type: 'message_does_not_match_regexp',
                              msg,
                          })
                ),
            })
        )
        .map(
            () =>
                ({
                    type: 'unblock_account_number_and_sort_code_mismatch',
                }) as const
        )

const parseUnblockNonceAlreadyInUse = (
    input: unknown
): Result<unknown, UnblockNonceAlreadyInUse> =>
    parseUnblockHttpError(input)
        .andThen((error) => object(error.responseBody))
        .andThen((obj) =>
            shape({
                name: match(obj.name, 'AuthBadRequestError'),
                message: string(obj.message).andThen((msg) =>
                    msg.match(/^Given nonce is already in use/i)
                        ? success(msg)
                        : failure({
                              type: 'message_does_not_match_regexp',
                              msg,
                          })
                ),
            })
        )
        .map(
            () =>
                ({
                    type: 'unblock_nonce_already_in_use',
                }) as const
        )

const parseUnblockUnsupportedCountry = (
    input: unknown
): Result<unknown, UnblockUnsupportedCountry> =>
    parseUnblockHttpError(input)
        .andThen((error) => object(error.responseBody))
        .andThen((obj) => match(obj.name, 'CountryNotSupportedError'))
        .map(() => ({ type: 'unblock_unsupported_country' }) as const)

const parseUnblockUserWithSuchEmailAlreadyExists = (
    input: unknown
): Result<unknown, UnblockUserWithSuchEmailAlreadyExists> =>
    parseUnblockHttpError(input)
        .andThen((error) => object(error.responseBody))
        .andThen((data) =>
            oneOf(data, [
                match(data.name, 'EmailAlreadyExistsError'),
                string(data.error).andThen((msg) =>
                    match(msg, 'EMAIL_ALREADY_USED_FOR_CURRENT_PARENT_CLIENT')
                ),
                match(
                    data.error,
                    'This user email already exists for this merchant'
                ),
                match(
                    data.message,
                    'The provided email is already registered for this merchant'
                ),
            ])
        )
        .map(
            () =>
                ({
                    type: 'unblock_user_with_such_email_already_exists',
                }) as const
        )

const parseUnblockInvalidIBAN = (
    input: unknown
): Result<unknown, UnblockInvalidIBAN> =>
    parseUnblockHttpError(input)
        .andThen((error) => object(error.responseBody))
        .andThen((data) => object(data.error))
        .andThen((obj) =>
            shape({
                message: string(obj.error).andThen((msg) =>
                    msg.match('BENEFICIARY_DETAILS_INVALID') &&
                    msg.match('iban_invalid')
                        ? success(msg)
                        : failure({
                              type: 'message_does_not_match_regexp',
                              msg,
                          })
                ),
            })
        )
        .map(
            () =>
                ({
                    type: 'unblock_invalid_iban',
                }) as const
        )

const parseUnblockCanNotChangeDetailsAfterKYC = (
    input: unknown
): Result<unknown, UnblockCanNotChangeDetailsAfterKYC> =>
    parseUnblockHttpError(input)
        .andThen((error) => object(error.responseBody))
        .andThen((data) =>
            string(data.error).andThen((str) =>
                matchRegExp(str, /After KYC, only .* can be updated/gi)
            )
        )
        .map(
            () =>
                ({
                    type: 'unblock_can_not_change_details_after_kyc',
                }) as const
        )

const _dontForgetToAddParser: Record<UnblockError['type'], true> = {
    unblock_account_number_and_sort_code_mismatch: true,
    unblock_can_not_change_details_after_kyc: true,
    unblock_hard_kyc_failure: true,
    unblock_invalid_faster_payment_configuration: true,
    unblock_invalid_iban: true,
    unblock_invalid_otp: true,
    unblock_login_user_did_not_exists: true,
    unblock_maximum_otp_attempts_exceeded: true,
    unblock_nonce_already_in_use: true,
    unblock_otp_expired: true,
    unblock_session_expired: true,
    unblock_unsupported_country: true,
    unblock_user_associated_with_other_merchant: true,
    unblock_user_with_address_already_exists: true,
    unblock_user_with_such_email_already_exists: true,
}

export const parseUnblockError = (
    error: unknown
): Result<unknown, UnblockError> =>
    oneOf(error, [
        oneOf(error, [
            parseUnblockAccountNumberAndSortCodeMismatch(error),
            parseUnblockFasterPaymentsIsInvalid(error),
            parseUnblockHardKycFailure(error),
            parseUnblockInvalidIBAN(error),
            parseUnblockInvalidOtpError(error),
            parseUnblockLoginError(error),
            parseUnblockMaximumOTPSubmitAttemptsError(error),
            parseUnblockNonceAlreadyInUse(error),
            parseUnblockOtpExpiredError(error),
        ]),
        oneOf(error, [
            parseUnblockSessionExpired(error),
            parseUnblockUnsupportedCountry(error),
            parseUnblockUserAlreadyExists(error),
            parseUnblockUserAssociatedWithOtherMerchant(error),
            parseUnblockUserWithSuchEmailAlreadyExists(error),
            parseUnblockCanNotChangeDetailsAfterKYC(error),
        ]),
    ])
