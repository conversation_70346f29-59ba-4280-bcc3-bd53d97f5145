import { GNOSIS_PAY_BASE_URL } from '@zeal/api/gnosisApi'
import { BASE_URL as BACKEND_BASE_URL } from '@zeal/api/requestBackend'

import {
    failure,
    match,
    matchRegExp,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'

import { HttpError } from '@zeal/domains/Error'
import { parseHttpError } from '@zeal/domains/Error/parsers/parseHttpError'

import {
    GnosisPayAddressLinkedToAnotherMoneriumProfile,
    GnosisPayAlreadyAcceptedTermsError,
    GnosisPayCardBlockedCantBeActivated,
    GnosisPayCardDelayRelayNotEmpty,
    GnosisPayCardUnsupportedCountries,
    GnosisPayEmailOrAccountAlreadyRegisteredError,
    GnosisPayError,
    GnosisPayFailedToVerifySignature,
    GnosisPayInvalidPhoneOtpError,
    GnosisPayInvalidSignupOtpError,
    GnosisPayIsNotAvailableInThisCountry,
    GnosisPayNameTooLong,
    GnosisPayNoActiveCardsFound,
    GnosisPayPublicKeyNotMatchingError,
    GnosisPayReadonlySignerIsAlredyInUse,
    GnosisPayThereIsAlreadyPendingCardOrder,
    GnosisPayUserAlreadyHasMoneriumAccount,
    GnosisPayUserCannotCreateCardReKYCNeeded,
    GnosisPayUserDoesntHaveSOFAnswered,
    GnosisPayUserDoesntMeetRiskScoreCriteria,
    GnosisPayUserIsNotSignedUp,
} from '..'

const _parsers: Record<GnosisPayError['type'], 'ive_added_parser'> = {
    gnosis_pay_name_too_long: 'ive_added_parser',
    gnosis_pay_address_linked_to_another_monerium_profile: 'ive_added_parser',
    gnosis_pay_already_accepted_terms_error: 'ive_added_parser',
    gnosis_pay_email_or_account_already_registered_error: 'ive_added_parser',
    gnosis_pay_invalid_phone_otp_error: 'ive_added_parser',
    gnosis_pay_invalid_signup_otp_error: 'ive_added_parser',
    gnosis_pay_is_not_available_in_this_country: 'ive_added_parser',
    gnosis_pay_no_active_cards_found: 'ive_added_parser',
    gnosis_pay_public_key_not_matching_error: 'ive_added_parser',
    gnosis_pay_readonly_signer_is_already_in_use: 'ive_added_parser',
    gnosis_pay_there_is_already_pending_card_order: 'ive_added_parser',
    gnosis_pay_user_already_has_monerium_account: 'ive_added_parser',
    gnosis_pay_user_cannot_create_card_re_kyc_needed: 'ive_added_parser',
    gnosis_pay_user_is_not_signed_up: 'ive_added_parser',
    gnosis_pay_card_blocked_cant_be_activated: 'ive_added_parser',
    gnosis_pay_card_delay_relay_not_empty: 'ive_added_parser',
    gnosis_pay_failed_to_verify_signature: 'ive_added_parser',
    gnosis_pay_user_doesnt_meet_risk_score_criteria: 'ive_added_parser',
    gnosis_pay_card_unsupported_countries: 'ive_added_parser',
    gnosis_pay_user_doesnt_have_sof_answered: 'ive_added_parser',
}

const parseGnosisPayHttpError = (input: unknown): Result<unknown, HttpError> =>
    parseHttpError(input).andThen((httpError) =>
        httpError.url.startsWith(GNOSIS_PAY_BASE_URL)
            ? success(httpError)
            : failure({ type: 'not_gnosis_pay_base_url' })
    )

const parseGnosisPayHttpErrorMessage = (
    input: HttpError
): Result<unknown, string> =>
    string(input.responseBody).andThen((response) =>
        oneOf(response, [
            string(response),
            object(response).andThen((body) => string(body.error)),
            object(response).andThen((body) => string(body.message)),
            object(response)
                .andThen((body) => object(body.error))
                .andThen((err) => string(err.message)),
        ])
    )

export const parseGnosisPayFailedToVerifySignature = (
    input: unknown
): Result<unknown, GnosisPayFailedToVerifySignature> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 403),
            payload: object(httpError.responseBody).andThen((data) =>
                shape({
                    error: match(
                        data.error,
                        'Siwe message is either not valid or validation could not be performed.'
                    ),
                })
            ),
        }).map(() => new GnosisPayFailedToVerifySignature(httpError))
    )

const parseGnosisPayIsNotAvailableInThisCountry = (
    input: unknown
): Result<unknown, GnosisPayIsNotAvailableInThisCountry> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 403),
            payload: object(httpError.responseBody).andThen((data) =>
                shape({
                    code: match(data.code, 'WAFForbidden'),
                    message: match(data.message, 'forbidden'),
                })
            ),
        }).map(() => new GnosisPayIsNotAvailableInThisCountry(httpError))
    )

const parseGnosisPayCardUnsupportedCountries = (
    input: unknown
): Result<unknown, GnosisPayCardUnsupportedCountries> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 422),
            message: parseGnosisPayHttpErrorMessage(httpError).andThen(
                (message) =>
                    matchRegExp(
                        message,
                        /User needs to be in one of the supported countries/i
                    )
            ),
        }).map(() => new GnosisPayCardUnsupportedCountries(httpError))
    )

const parseGnosisPayUserDoesntHaveSOFAnswered = (
    input: unknown
): Result<unknown, GnosisPayUserDoesntHaveSOFAnswered> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 422),
            message: parseGnosisPayHttpErrorMessage(httpError).andThen(
                (message) =>
                    matchRegExp(
                        message,
                        /User doesn't have source of funds questionnaire answered/i
                    )
            ),
        }).map(() => new GnosisPayUserDoesntHaveSOFAnswered(httpError))
    )

// TODO @resetko-zeal use parseGnosisPayHttpErrorMessage
const parseGnosisPayNoActiveCardsFound = (
    input: unknown
): Result<unknown, GnosisPayNoActiveCardsFound> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 404),
            payload: object(httpError.responseBody).andThen((data) =>
                shape({
                    error: match(data.error, 'No activated cards found'),
                })
            ),
        }).map(
            () =>
                ({
                    type: 'gnosis_pay_no_active_cards_found',
                }) as const
        )
    )

// TODO @resetko-zeal use parseGnosisPayHttpErrorMessage
const parseGnosisPayThereIsAlreadyPendingCardOrder = (
    input: unknown
): Result<unknown, GnosisPayThereIsAlreadyPendingCardOrder> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 400),
            payload: string(httpError.responseBody).andThen((data) =>
                match(data, 'Error: There is already a pending card order')
            ),
        }).map(
            () =>
                ({
                    type: 'gnosis_pay_there_is_already_pending_card_order',
                }) as const
        )
    )

// TODO @resetko-zeal use parseGnosisPayHttpErrorMessage
const parseGnosisPayReadonlySignerIsAlreadyInUse = (
    input: unknown
): Result<unknown, GnosisPayReadonlySignerIsAlredyInUse> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 422),
            payload: object(httpError.responseBody).andThen((data) =>
                shape({
                    message: match(
                        data.message,
                        'EOA account with this address already exists'
                    ),
                })
            ),
        }).map(
            () =>
                ({
                    type: 'gnosis_pay_readonly_signer_is_already_in_use',
                }) as const
        )
    )

const parseGnosisPayUserDoesntMeetRiskScoreCriteria = (
    input: unknown
): Result<unknown, GnosisPayUserDoesntMeetRiskScoreCriteria> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 422),
            message: parseGnosisPayHttpErrorMessage(httpError).andThen(
                (message) =>
                    matchRegExp(
                        message,
                        /User doesn't meet risk score criteria/i
                    )
            ),
        }).map(
            () =>
                ({
                    type: 'gnosis_pay_user_doesnt_meet_risk_score_criteria',
                }) as const
        )
    )

const parseGnosisPayUserAlreadyHasMoneriumAccount = (
    input: unknown
): Result<unknown, GnosisPayUserAlreadyHasMoneriumAccount> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 409),
            redirectUrl: object(httpError.responseBody).andThen((dataObj) =>
                object(dataObj.data).andThen((data) => string(data.redirectUrl))
            ),
        }).map(
            ({ redirectUrl }) =>
                ({
                    type: 'gnosis_pay_user_already_has_monerium_account',
                    oAuthUrl: redirectUrl,
                }) as const
        )
    )

const parseGnosisPayUserCannotCreateCardReKYCNeeded = (
    input: unknown
): Result<unknown, GnosisPayUserCannotCreateCardReKYCNeeded> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            msg: parseGnosisPayHttpErrorMessage(httpError).andThen((message) =>
                matchRegExp(message, /User is not approved through KYC/i)
            ),
            status: match(httpError.status, 400),
        }).map(
            () =>
                ({
                    type: 'gnosis_pay_user_cannot_create_card_re_kyc_needed',
                }) as const
        )
    )

// TODO @resetko-zeal use parseGnosisPayHttpErrorMessage
const parseGnosisPayAddressLinkedToAnotherMoneriumProfile = (
    input: unknown
): Result<unknown, GnosisPayAddressLinkedToAnotherMoneriumProfile> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 422),
            error: object(httpError.responseBody).andThen((dataObj) =>
                object(dataObj.errors).andThen((errors) =>
                    match(
                        errors.message,
                        'The specified address is already linked to another Monerium profile'
                    )
                )
            ),
        }).map(() => ({
            type: 'gnosis_pay_address_linked_to_another_monerium_profile',
        }))
    )

const parseGnosisPayAlreadyAcceptedTermsError = (
    input: unknown
): Result<unknown, GnosisPayAlreadyAcceptedTermsError> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 422),
            error: object(httpError.responseBody).andThen((dataObj) =>
                string(dataObj.message).andThen((message) =>
                    matchRegExp(
                        message,
                        /You have already accepted these terms/i
                    )
                )
            ),
        }).map(() => ({ type: 'gnosis_pay_already_accepted_terms_error' }))
    )

const parseGnosisPayPublicKeyNotMatchingError = (
    input: unknown
): Result<unknown, GnosisPayPublicKeyNotMatchingError> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 500),
            error: object(httpError.responseBody).andThen((dataObj) =>
                string(dataObj.error).andThen((error) =>
                    matchRegExp(error, /RSA Public key Not Matching/i)
                )
            ),
        }).map(() => ({
            type: 'gnosis_pay_public_key_not_matching_error',
        }))
    )

const parseGnosisPayInvalidSignupOtpError = (
    input: unknown
): Result<unknown, GnosisPayInvalidSignupOtpError> =>
    parseHttpError(input)
        .andThen((httpError) =>
            oneOf(httpError, [
                httpError.url.startsWith(BACKEND_BASE_URL)
                    ? success(httpError)
                    : failure({ type: 'not_gnosis_pay_proxified_url' }),
                parseGnosisPayHttpError(httpError),
            ])
        )
        .andThen((httpError) =>
            shape({
                status: match(httpError.status, 401),
                error: object(httpError.responseBody).andThen((dataObj) =>
                    string(dataObj.error).andThen((error) =>
                        matchRegExp(error, /invalid or expired otp/i)
                    )
                ),
            }).map(() => ({
                type: 'gnosis_pay_invalid_signup_otp_error',
            }))
        )

const parseGnosisPayInvalidPhoneOtpError = (
    input: unknown
): Result<unknown, GnosisPayInvalidPhoneOtpError> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 422),
            error: object(httpError.responseBody).andThen((dataObj) =>
                string(dataObj.error).andThen((error) =>
                    matchRegExp(error, /Verification failed/i)
                )
            ),
        }).map(() => ({
            type: 'gnosis_pay_invalid_phone_otp_error',
        }))
    )

const parseGnosisPayCardBlockedCantBeActivated = (
    // https://zeal-hzn3129.slack.com/archives/C06R39S06J1/p1752675833121729
    input: unknown
): Result<unknown, GnosisPayCardBlockedCantBeActivated> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 422),
            message: parseGnosisPayHttpErrorMessage(httpError).andThen(
                (message) =>
                    matchRegExp(
                        message,
                        /Card is blocked and could not be activated/i
                    )
            ),
        }).map(() => new GnosisPayCardBlockedCantBeActivated(httpError))
    )

const parseGnosisPayCardDelayRelayNotEmpty = (
    input: unknown
): Result<unknown, GnosisPayCardDelayRelayNotEmpty> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 422),
            error: object(httpError.responseBody).andThen((dataObj) =>
                string(dataObj.error).andThen((error) =>
                    matchRegExp(
                        error,
                        /There is already an incomplete transaction/i
                    )
                )
            ),
        }).map(() => new GnosisPayCardDelayRelayNotEmpty(httpError))
    )

const parseGnosisPayEmailOrAccountAlreadyRegisteredError = (
    input: unknown
): Result<unknown, GnosisPayEmailOrAccountAlreadyRegisteredError> =>
    parseHttpError(input)
        .andThen((httpError) =>
            oneOf(httpError, [
                httpError.url.startsWith(BACKEND_BASE_URL)
                    ? success(httpError)
                    : failure({ type: 'not_gnosis_pay_proxified_url' }),
                parseGnosisPayHttpError(httpError),
            ])
        )
        .andThen((httpError) =>
            shape({
                status: match(httpError.status, 409),
                error: object(httpError.responseBody).andThen((dataObj) =>
                    string(dataObj.error).andThen((error) =>
                        matchRegExp(error, /Email address already registered/i)
                    )
                ),
            }).map(() => ({
                type: 'gnosis_pay_email_or_account_already_registered_error',
            }))
        )

const parseGnosisPayUserIsNotSignedUp = (
    input: unknown
): Result<unknown, GnosisPayUserIsNotSignedUp> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 401),
            error: object(httpError.responseBody).andThen((resp) =>
                string(resp.error).andThen(
                    (error) =>
                        matchRegExp(error, /User authentication required/i) // https://zeal-hzn3129.slack.com/archives/C06R39S06J1/p1752486445553529?thread_ts=**********.658349&cid=C06R39S06J1
                )
            ),
        }).map(() => new GnosisPayUserIsNotSignedUp(httpError))
    )

const parseGnosisPayNameTooLong = (
    input: unknown
): Result<unknown, GnosisPayNameTooLong> =>
    parseGnosisPayHttpError(input).andThen((httpError) =>
        shape({
            status: match(httpError.status, 422),
            error: object(httpError.responseBody)
                .andThen(string)
                .andThen((str) =>
                    matchRegExp(
                        str,
                        /First name and last name are too long to shorten/i
                    )
                ),
        }).map(() => new GnosisPayNameTooLong(httpError))
    )

export const parseGnosisPayError = (
    input: unknown
): Result<unknown, GnosisPayError> =>
    oneOf(input, [
        oneOf(input, [
            parseGnosisPayAddressLinkedToAnotherMoneriumProfile(input),
            parseGnosisPayAlreadyAcceptedTermsError(input),
            parseGnosisPayInvalidSignupOtpError(input),
            parseGnosisPayIsNotAvailableInThisCountry(input),
            parseGnosisPayNoActiveCardsFound(input),
            parseGnosisPayPublicKeyNotMatchingError(input),
            parseGnosisPayReadonlySignerIsAlreadyInUse(input),
            parseGnosisPayThereIsAlreadyPendingCardOrder(input),
            parseGnosisPayUserAlreadyHasMoneriumAccount(input),
            parseGnosisPayUserCannotCreateCardReKYCNeeded(input),
            parseGnosisPayFailedToVerifySignature(input),
        ]),
        oneOf(input, [
            parseGnosisPayEmailOrAccountAlreadyRegisteredError(input),
            parseGnosisPayInvalidPhoneOtpError(input),
            parseGnosisPayUserIsNotSignedUp(input),
            parseGnosisPayCardBlockedCantBeActivated(input),
            parseGnosisPayCardDelayRelayNotEmpty(input),
            parseGnosisPayUserDoesntMeetRiskScoreCriteria(input),
            parseGnosisPayCardUnsupportedCountries(input),
            parseGnosisPayUserDoesntHaveSOFAnswered(input),
            parseGnosisPayNameTooLong(input),
        ]),
    ])
