import { HttpError } from '@zeal/domains/Error'
import { getReplacedStack } from '@zeal/domains/Error/helpers/joinStacks'

export type GnosisPayThereIsAlreadyPendingCardOrder = {
    type: 'gnosis_pay_there_is_already_pending_card_order'
}

export type GnosisPayNoActiveCardsFound = {
    type: 'gnosis_pay_no_active_cards_found'
}
export type GnosisPayReadonlySignerIsAlredyInUse = {
    type: 'gnosis_pay_readonly_signer_is_already_in_use'
}
export type GnosisPayPublicKeyNotMatchingError = {
    type: 'gnosis_pay_public_key_not_matching_error'
}
export type GnosisPayUserAlreadyHasMoneriumAccount = {
    type: 'gnosis_pay_user_already_has_monerium_account'
    oAuthUrl: string
}

export type GnosisPayUserDoesntMeetRiskScoreCriteria = {
    type: 'gnosis_pay_user_doesnt_meet_risk_score_criteria'
}

export type GnosisPayAddressLinkedToAnotherMoneriumProfile = {
    type: 'gnosis_pay_address_linked_to_another_monerium_profile'
}

export type GnosisPayUserCannotCreateCardReKYCNeeded = {
    type: 'gnosis_pay_user_cannot_create_card_re_kyc_needed'
}

export type GnosisPayAlreadyAcceptedTermsError = {
    type: 'gnosis_pay_already_accepted_terms_error'
}

export type GnosisPayInvalidSignupOtpError = {
    type: 'gnosis_pay_invalid_signup_otp_error'
}

export type GnosisPayInvalidPhoneOtpError = {
    type: 'gnosis_pay_invalid_phone_otp_error'
}

export type GnosisPayEmailOrAccountAlreadyRegisteredError = {
    type: 'gnosis_pay_email_or_account_already_registered_error'
}

export class GnosisPayFailedToVerifySignature extends Error {
    type: 'gnosis_pay_failed_to_verify_signature' =
        'gnosis_pay_failed_to_verify_signature' as const
    responseHeaders: HttpError['responseHeaders']
    requestBody: HttpError['requestBody']

    constructor(httpError: HttpError) {
        super()
        this.stack = httpError.stack
        this.responseHeaders = httpError.responseHeaders
        this.requestBody = httpError.requestBody
    }
}

// Gnosis pay filters requested based on GEO IP location
export class GnosisPayIsNotAvailableInThisCountry extends Error {
    type: 'gnosis_pay_is_not_available_in_this_country' =
        'gnosis_pay_is_not_available_in_this_country' as const
    responseHeaders: HttpError['responseHeaders']
    requestBody: HttpError['requestBody']

    constructor(httpError: HttpError) {
        super()
        this.stack = httpError.stack
        this.responseHeaders = httpError.responseHeaders
        this.requestBody = httpError.requestBody
    }
}
export class GnosisPayCardUnsupportedCountries extends Error {
    type: 'gnosis_pay_card_unsupported_countries' =
        'gnosis_pay_card_unsupported_countries' as const
    responseHeaders: HttpError['responseHeaders']
    requestBody: HttpError['requestBody']

    constructor(httpError: HttpError) {
        super()
        this.stack = httpError.stack
        this.responseHeaders = httpError.responseHeaders
        this.requestBody = httpError.requestBody
    }
}

export class GnosisPayUserDoesntHaveSOFAnswered extends Error {
    type: 'gnosis_pay_user_doesnt_have_sof_answered' =
        'gnosis_pay_user_doesnt_have_sof_answered' as const
    responseHeaders: HttpError['responseHeaders']
    requestBody: HttpError['requestBody']

    constructor(httpError: HttpError) {
        super()
        this.stack = httpError.stack
        this.responseHeaders = httpError.responseHeaders
        this.requestBody = httpError.requestBody
    }
}

export class GnosisPayUserIsNotSignedUp extends Error {
    type: 'gnosis_pay_user_is_not_signed_up' =
        'gnosis_pay_user_is_not_signed_up' as const
    responseHeaders: HttpError['responseHeaders']
    pathname: string
    method: string

    constructor(httpError: HttpError) {
        super()
        this.stack = getReplacedStack(this, httpError)
        this.method = httpError.method
        this.pathname = httpError.urlPathname
        this.responseHeaders = httpError.responseHeaders
    }
}

export class GnosisPayCardBlockedCantBeActivated extends Error {
    type: 'gnosis_pay_card_blocked_cant_be_activated' =
        'gnosis_pay_card_blocked_cant_be_activated' as const

    constructor(httpError: HttpError) {
        super()
        this.stack = httpError.stack
    }
}

export class GnosisPayCardDelayRelayNotEmpty extends Error {
    type: 'gnosis_pay_card_delay_relay_not_empty' =
        'gnosis_pay_card_delay_relay_not_empty' as const
    responseHeaders: HttpError['responseHeaders']
    requestBody: HttpError['requestBody']

    constructor(httpError: HttpError) {
        super()
        this.stack = httpError.stack
        this.responseHeaders = httpError.responseHeaders
        this.requestBody = httpError.requestBody
    }
}

export class GnosisPayNameTooLong extends Error {
    type: 'gnosis_pay_name_too_long' = 'gnosis_pay_name_too_long' as const
    responseHeaders: HttpError['responseHeaders']
    requestBody: HttpError['requestBody']

    constructor(httpError: HttpError) {
        super()
        this.stack = httpError.stack
        this.responseHeaders = httpError.responseHeaders
        this.requestBody = httpError.requestBody
    }
}

export type GnosisPayError =
    | GnosisPayAddressLinkedToAnotherMoneriumProfile
    | GnosisPayAlreadyAcceptedTermsError
    | GnosisPayEmailOrAccountAlreadyRegisteredError
    | GnosisPayInvalidPhoneOtpError
    | GnosisPayInvalidSignupOtpError
    | GnosisPayIsNotAvailableInThisCountry
    | GnosisPayNoActiveCardsFound
    | GnosisPayPublicKeyNotMatchingError
    | GnosisPayReadonlySignerIsAlredyInUse
    | GnosisPayUserAlreadyHasMoneriumAccount
    | GnosisPayUserCannotCreateCardReKYCNeeded
    | GnosisPayThereIsAlreadyPendingCardOrder
    | GnosisPayUserIsNotSignedUp
    | GnosisPayCardBlockedCantBeActivated
    | GnosisPayCardDelayRelayNotEmpty
    | GnosisPayFailedToVerifySignature
    | GnosisPayUserDoesntMeetRiskScoreCriteria
    | GnosisPayCardUnsupportedCountries
    | GnosisPayNameTooLong
    | GnosisPayUserDoesntHaveSOFAnswered
