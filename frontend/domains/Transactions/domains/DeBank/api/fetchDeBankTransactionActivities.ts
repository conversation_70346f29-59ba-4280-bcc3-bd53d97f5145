import * as Web3 from '@zeal/toolkit/Web3'

import { CurrentNetwork, NetworkRPCMap } from '@zeal/domains/Network'
import { RegularTransactionActivity } from '@zeal/domains/Transactions'

import {
    DeBankContinuationToken,
    fetchDeBankActivityRawFeedWithRetry,
    fetchDebankTransactionActivitiesFromRawFeed,
} from './fetchDeBankActivityRawFeed'

export const fetchDeBankTransactionActivities = async ({
    address,
    currentNetwork,
    startTime,
    endTime,
    withScam,
    deBankContinuationToken,
    networkRPCMap,
}: {
    address: Web3.address.Address
    currentNetwork: CurrentNetwork
    networkRPCMap: NetworkRPCMap
    deBankContinuationToken: DeBankContinuationToken | null
    withScam: boolean
    startTime: Date
    endTime: Date
}): Promise<{
    transactionActivities: RegularTransactionActivity[]
    deBankContinuationToken: DeBankContinuationToken
}> => {
    const {
        deBankRawActivityFeed,
        deBankContinuationToken: nextDeBankContinuationToken,
    } = await fetchDeBankActivityRawFeedWithRetry({
        address,
        startTime,
        endTime,
        withScam,
        deBankContinuationToken,
        currentNetwork,
    })

    const transactionActivities =
        await fetchDebankTransactionActivitiesFromRawFeed({
            deBankActivityRawFeed: deBankRawActivityFeed,
            withScam,
            currentNetwork,
            networkRPCMap,
        })

    return {
        transactionActivities,
        deBankContinuationToken: nextDeBankContinuationToken,
    }
}
