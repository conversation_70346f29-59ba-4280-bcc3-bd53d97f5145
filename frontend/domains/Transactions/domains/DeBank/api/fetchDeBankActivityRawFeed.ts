import { get } from '@zeal/api/requestBackend'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { withRetries } from '@zeal/toolkit/Function'
import { keys } from '@zeal/toolkit/Object'
import { groupByType } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { fetchCryptoCurrency2 } from '@zeal/domains/Currency/api/fetchCryptoCurrency2'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import {
    CurrentNetwork,
    NetworkRPCMap,
    PredefinedNetwork,
} from '@zeal/domains/Network'
import { RegularTransactionActivity } from '@zeal/domains/Transactions'

import { DeBankActivityRawFeed } from '..'
import {
    DEBANK_NETWORK_TO_NETWORK_MAP,
    NETWORK_TO_DEBANK_NETWORK_MAP,
} from '../constants'
import { parseDeBankActitityTransation } from '../helpers/parseDeBankActivityTransaction'
import { parseDeBankActivityRawFeed } from '../helpers/parseDeBankTransactionActivityRaw'

const PAGE_SIZE = 20

export type DeBankContinuationToken =
    | {
          type: 'cached_transactions'
          startTime: Date
          endTime: Date
          deBankActivityRawFeed: DeBankActivityRawFeed
      }
    | {
          type: 'no_more_transactions'
          startTime: Date
          deBankActivityRawFeed: DeBankActivityRawFeed
      }

export const fetchDebankTransactionActivitiesFromRawFeed = async ({
    withScam,
    currentNetwork,
    networkRPCMap,
    deBankActivityRawFeed,
}: {
    withScam: boolean
    currentNetwork: CurrentNetwork
    networkRPCMap: NetworkRPCMap
    deBankActivityRawFeed: DeBankActivityRawFeed
}): Promise<RegularTransactionActivity[]> => {
    const { rawTransactions, currencies, projectsMap } = deBankActivityRawFeed

    const filteredTransactions = rawTransactions.filter(
        ({ is_scam }) => is_scam === withScam
    )

    const knownCurrencies = await fetchCryptoCurrency2({
        currencies,
        networkRPCMap,
    })

    const [unparsedTransactions, transactionActivities] = groupByType(
        filteredTransactions.map((rawTransaction) =>
            parseDeBankActitityTransation({
                input: rawTransaction,
                deBankProjectsInfoMap: projectsMap,
                knownCurrencies,
            })
        )
    )

    const transactionActivitiesFilteredByNetwork = transactionActivities.filter(
        (activity) => {
            switch (currentNetwork.type) {
                case 'all_networks':
                    return true
                case 'specific_network':
                    return (
                        activity.networkHexId ===
                        currentNetwork.network.hexChainId
                    )

                /* istanbul ignore next */
                default:
                    return notReachable(currentNetwork)
            }
        }
    )

    if (unparsedTransactions.length > 0) {
        captureError(
            new ImperativeError('Unable to parse DeBank transaction activity', {
                unparsedTransactions,
            })
        )
    }

    return transactionActivitiesFilteredByNetwork
}

const fetchDeBankActivityRawFeed = async ({
    address,
    startTime,
    endTime,
    withScam,
    deBankContinuationToken,
    signal,
    currentNetwork,
}: {
    address: Web3.address.Address
    startTime: Date
    endTime: Date
    withScam: boolean
    deBankContinuationToken: DeBankContinuationToken | null
    currentNetwork: CurrentNetwork
    signal?: AbortSignal
}): Promise<{
    deBankRawActivityFeed: DeBankActivityRawFeed
    deBankContinuationToken: DeBankContinuationToken
}> => {
    switch (deBankContinuationToken?.type) {
        case undefined:
            break
        case 'cached_transactions':
            if (
                deBankContinuationToken.startTime.getTime() >=
                    startTime.getTime() &&
                deBankContinuationToken.endTime.getTime() <= endTime.getTime()
            ) {
                return {
                    deBankRawActivityFeed:
                        filteredDeBankActivityRawFeedByTimeRange({
                            deBankActivityRawFeed:
                                deBankContinuationToken.deBankActivityRawFeed,
                            startTime,
                            endTime,
                            withScam,
                        }),
                    deBankContinuationToken,
                }
            }
            break

        case 'no_more_transactions':
            if (
                deBankContinuationToken.startTime.getTime() >=
                startTime.getTime()
            ) {
                return {
                    deBankRawActivityFeed:
                        filteredDeBankActivityRawFeedByTimeRange({
                            deBankActivityRawFeed:
                                deBankContinuationToken.deBankActivityRawFeed,
                            startTime,
                            endTime,
                            withScam,
                        }),
                    deBankContinuationToken,
                }
            }
            break

        /* istanbul ignore next */
        default:
            return notReachable(deBankContinuationToken)
    }
    // FIXME :: @max type is wrong we cannot get test and predefined newtork for debank
    // we need to rething and reimplement this

    const chainIds = (() => {
        switch (currentNetwork.type) {
            case 'all_networks':
                return keys(DEBANK_NETWORK_TO_NETWORK_MAP)
            case 'specific_network': {
                const debankNetwork =
                    NETWORK_TO_DEBANK_NETWORK_MAP[
                        currentNetwork.network.name as PredefinedNetwork['name']
                    ]
                if (!debankNetwork) {
                    return keys(DEBANK_NETWORK_TO_NETWORK_MAP)
                }
                return [debankNetwork]
            }
            /* istanbul ignore next */
            default:
                return notReachable(currentNetwork)
        }
    })()

    const response = await get(
        '/proxy/dbk/user/all_history_list',
        {
            query: {
                id: address,
                start_time: Math.floor(startTime.getTime() / 1000),
                page_count: PAGE_SIZE,
                chain_ids: chainIds,
            },
        },
        signal
    )

    const deBankActivityRawFeed = parseDeBankActivityRawFeed(
        response
    ).getSuccessResultOrThrow('Unable to parse DeBank activity feed')

    const { rawTransactions } = deBankActivityRawFeed

    const lastFetchedTransaction =
        rawTransactions[rawTransactions.length - 1]?.time_at

    const filteredDeBankActivityRawFeed =
        filteredDeBankActivityRawFeedByTimeRange({
            deBankActivityRawFeed,
            startTime,
            endTime,
            withScam,
        })

    const nextDeBankContinuationToken: DeBankContinuationToken = (() =>
        rawTransactions.length === PAGE_SIZE
            ? {
                  type: 'cached_transactions',
                  startTime,
                  endTime: new Date(lastFetchedTransaction),
                  deBankActivityRawFeed,
              }
            : {
                  type: 'no_more_transactions',
                  startTime,
                  deBankActivityRawFeed,
              })()

    if (lastFetchedTransaction && lastFetchedTransaction > endTime.getTime()) {
        const {
            deBankContinuationToken,
            deBankRawActivityFeed: nextDeBankActivityRawFeed,
        } = await fetchDeBankActivityRawFeedWithRetry({
            address,
            startTime: new Date(lastFetchedTransaction),
            endTime,
            withScam,
            deBankContinuationToken: nextDeBankContinuationToken,
            signal,
            currentNetwork,
        })

        return {
            deBankRawActivityFeed: {
                rawTransactions: [
                    ...filteredDeBankActivityRawFeed.rawTransactions,
                    ...nextDeBankActivityRawFeed.rawTransactions,
                ],
                currencies: keys(
                    [
                        ...filteredDeBankActivityRawFeed.currencies,
                        ...nextDeBankActivityRawFeed.currencies,
                    ].reduce(
                        (hash, currencyId) => ({
                            ...hash,
                            [currencyId]: true,
                        }),
                        {}
                    )
                ),
                projectsMap: {
                    ...filteredDeBankActivityRawFeed.projectsMap,
                    ...nextDeBankActivityRawFeed.projectsMap,
                },
            },
            deBankContinuationToken,
        }
    }

    return {
        deBankRawActivityFeed: filteredDeBankActivityRawFeed,
        deBankContinuationToken: nextDeBankContinuationToken,
    }
}

export const fetchDeBankActivityRawFeedWithRetry = withRetries({
    retries: 3,
    delayMs: 500,
    fn: fetchDeBankActivityRawFeed,
})

const filteredDeBankActivityRawFeedByTimeRange = ({
    deBankActivityRawFeed,
    withScam,
    startTime,
    endTime,
}: {
    deBankActivityRawFeed: DeBankActivityRawFeed
    withScam: boolean
    startTime: Date
    endTime: Date
}): DeBankActivityRawFeed => {
    const filteredTransactions = deBankActivityRawFeed.rawTransactions.filter(
        ({ time_at, is_scam }) =>
            time_at <= startTime.getTime() &&
            time_at >= endTime.getTime() &&
            is_scam === withScam
    )

    const filteredCurrencies = deBankActivityRawFeed.currencies.filter(
        (currencyId) =>
            filteredTransactions.some(
                (rawTransaction) =>
                    rawTransaction.sends.some(
                        (send) => send.currency_id === currencyId
                    ) ||
                    rawTransaction.receives.some(
                        (receive) => receive.currency_id === currencyId
                    ) ||
                    rawTransaction.token_approve?.currency_id === currencyId
            )
    )

    return {
        ...deBankActivityRawFeed,
        rawTransactions: filteredTransactions,
        currencies: filteredCurrencies,
    }
}
