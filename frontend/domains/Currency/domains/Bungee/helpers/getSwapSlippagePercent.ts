import { fromFixedWithF<PERSON> } from '@zeal/toolkit/BigInt'
import { keys } from '@zeal/toolkit/Object'
import { Percentage } from '@zeal/toolkit/Percentage'

import { CryptoCurrency } from '@zeal/domains/Currency'
import {
    GNOSIS_AAVE_EURE,
    GNOSIS_SDAI,
    GNOSIS_WSTETH,
    STABLE_COIN_TO_FIAT_CURRENCY_MAP,
} from '@zeal/domains/Currency/constants'
import { CryptoMoney } from '@zeal/domains/Money'
import { GNOSIS } from '@zeal/domains/Network/constants'

const SLIPPAGE_FOR_SMALL_AMOUNT: Percentage = 1

const STABLE_COIN_MIN_AMOUNT = 10

const MIN_AMOUNTS_MAP: Partial<Record<CryptoCurrency['id'], number>> = {
    [GNOSIS_SDAI.id]: STABLE_COIN_MIN_AMOUNT,
    [GNOSIS_AAVE_EURE.id]: STABLE_COIN_MIN_AMOUNT,
    [GNOSIS.nativeCurrency.id]: STABLE_COIN_MIN_AMOUNT,
    [GNOSIS_WSTETH.id]: 0.002,
    ...keys(STABLE_COIN_TO_FIAT_CURRENCY_MAP).reduce(
        (map, currencyId) => {
            map[currencyId] = STABLE_COIN_MIN_AMOUNT
            return map
        },
        {} as Record<CryptoCurrency['id'], number>
    ),
}

const PROVIDER_SLIPPAGE_MAP: Record<'bungee' | 'sockets', Percentage> = {
    bungee: 0.5,
    sockets: 0.1,
}

// TODO :: @Nicvaniek move this to some new IntentSwap domain once we migrate away from Sockets
export const getSwapSlippagePercent = ({
    provider,
    fromAmount,
}: {
    fromAmount: CryptoMoney
    provider: 'bungee' | 'sockets'
}): Percentage => {
    const minAmount = MIN_AMOUNTS_MAP[fromAmount.currency.id]

    const providerSlippage = PROVIDER_SLIPPAGE_MAP[provider]

    if (!minAmount) {
        return providerSlippage
    }

    const minAmountScaled = fromFixedWithFraction(
        minAmount.toString(),
        fromAmount.currency.fraction
    )

    return fromAmount.amount < minAmountScaled
        ? SLIPPAGE_FOR_SMALL_AMOUNT
        : providerSlippage
}
