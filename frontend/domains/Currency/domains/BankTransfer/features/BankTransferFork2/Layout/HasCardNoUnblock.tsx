import { useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { Address } from '@zeal/toolkit/Web3/address'

import { AccountsMap } from '@zeal/domains/Account'
import {
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import { Deposit as MoneriumDeposit } from '@zeal/domains/Card/domains/MoneriumBankTransfer/features/Deposit'
import { Withdraw } from '@zeal/domains/Card/domains/MoneriumBankTransfer/features/Withdraw'
import { tryToGetUserTimeZoneCountries } from '@zeal/domains/Country/helpers/tryToGetUserTimeZoneCountries'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { DepositWithdrawVariant } from '@zeal/domains/Currency/domains/BankTransfer'
import { BANK_TRANSFER_LOGIN_NETWORK } from '@zeal/domains/Currency/domains/BankTransfer/constants'
import { DepositWithdraw as UnblockDepositWithdraw } from '@zeal/domains/Currency/domains/BankTransfer/features/DepositWithdraw'
import { MtPelerinBankTransfer } from '@zeal/domains/Currency/domains/BankTransfer/features/MtPelerinBankTransfer'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    BankTransferInfo,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { MtPelerinMoneriumSelectionScreen } from './MtPelerinMoneriumSelectionScreen'

type Props = {
    variant: DepositWithdrawVariant
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    selectedAddress: Address
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    bankTransfer: BankTransferInfo
    keystoreMap: KeyStoreMap
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    installationCampaign: string | null
    portfolio: ServerPortfolio2
    counterparties: Counterparty[]
    keyStore: SigningKeyStore
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof MoneriumDeposit>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_activate_existing_monerium_account_click'
                  | 'monerium_deposit_on_enable_card_clicked'
                  | 'on_monerium_deposit_success_go_to_wallet_clicked'
                  | 'monerium_on_card_disconnected'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_monerium_sign_delay_relay_success_close_clicked'
                  | 'on_card_disconnected'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
          }
      >
    | MsgOf<typeof UnblockDepositWithdraw>
    | Extract<
          MsgOf<typeof Withdraw>,
          {
              type:
                  | 'on_monerium_order_status_changed'
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_sign_in_to_gnosis_pay_error_close_clicked'
                  | 'on_gnosis_pay_not_available_accepted'
                  | 'on_activate_existing_monerium_account_click'
                  | 'monerium_deposit_on_enable_card_clicked'
                  | 'on_save_counterparty_form_submitted'
                  | 'on_delete_counterparty_submitted'
                  | 'on_delete_last_counterparty_submitted'
                  | 'on_contact_support_clicked'
                  | 'monerium_on_card_disconnected'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_monerium_sign_delay_relay_success_close_clicked'
          }
      >

type State =
    | { type: 'monerium_bank_transfer' }
    | { type: 'mt_pelerin_monerium_selection_screen' }
    | { type: 'mt_pelerin_bank_transfer' }
    | { type: 'unblock_bank_transfer' }

const checkShouldShowMtPelerin = (
    defaultCurrencyConfig: DefaultCurrencyConfig
): boolean => {
    const tzCountries = tryToGetUserTimeZoneCountries().getSuccessResult() || []

    return (
        tzCountries.map((c) => c.code).includes('CH') ||
        defaultCurrencyConfig.defaultCurrency.code === 'CHF'
    )
}

export const HasCardNoUnblock = ({
    variant,
    onMsg,
    cardConfig,
    defaultCurrencyConfig,
    gasCurrencyPresetMap,
    feePresetMap,
    installationId,
    portfolioMap,
    accountsMap,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    currencyHiddenMap,
    currencyPinMap,
    customCurrencies,
    keystoreMap,
    bankTransfer,
    installationCampaign,
    portfolio,
    sessionPassword,
    selectedAddress,
    counterparties,
    keyStore,
}: Props) => {
    const shouldShowMtPelerin = checkShouldShowMtPelerin(defaultCurrencyConfig)

    const [state, setState] = useState<State>(
        shouldShowMtPelerin
            ? { type: 'mt_pelerin_monerium_selection_screen' }
            : { type: 'monerium_bank_transfer' }
    )

    switch (state.type) {
        case 'mt_pelerin_monerium_selection_screen':
            return (
                <MtPelerinMoneriumSelectionScreen
                    cardConfig={cardConfig}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_provider_selected':
                                switch (msg.provider) {
                                    case 'mt_pelerin':
                                        setState({
                                            type: 'mt_pelerin_bank_transfer',
                                        })
                                        break
                                    case 'monerium':
                                        setState({
                                            type: 'monerium_bank_transfer',
                                        })
                                        break
                                    default:
                                        return notReachable(msg.provider)
                                }
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        case 'mt_pelerin_bank_transfer':
            return (
                <MtPelerinBankTransfer
                    cardOwnerKeyStore={keyStore}
                    cardReadonlySignerAddress={cardConfig.readonlySignerAddress}
                    accountsMap={accountsMap}
                    keystoreMap={keystoreMap}
                    portfolioMap={portfolioMap}
                    currencyHiddenMap={currencyHiddenMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    installationId={installationId}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    installationCampaign={installationCampaign}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                if (shouldShowMtPelerin) {
                                    setState({
                                        type: 'mt_pelerin_monerium_selection_screen',
                                    })
                                    break
                                }
                                onMsg(msg)
                                break
                            case 'on_card_disconnected':
                            case 'on_create_smart_wallet_clicked':
                            case 'on_card_import_on_import_keys_clicked':
                            case 'on_card_imported_success_animation_complete':
                            case 'on_onboarded_card_imported_success_animation_complete':
                                onMsg(msg)
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        case 'monerium_bank_transfer':
            switch (variant.type) {
                case 'deposit':
                    return (
                        <MoneriumDeposit
                            installationCampaign={installationCampaign}
                            accountsMap={accountsMap}
                            currencyHiddenMap={currencyHiddenMap}
                            cardConfig={cardConfig}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            installationId={installationId}
                            keyStoreMap={keyStoreMap}
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            portfolioMap={portfolioMap}
                            sessionPassword={sessionPassword}
                            location="bank_transfer"
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'on_4337_auto_gas_token_selection_clicked':
                                    case 'on_4337_gas_currency_selected':
                                    case 'on_activate_existing_monerium_account_click':
                                    case 'monerium_deposit_on_enable_card_clicked':
                                    case 'on_monerium_deposit_success_go_to_wallet_clicked':
                                    case 'monerium_on_card_disconnected':
                                    case 'on_create_smart_wallet_clicked':
                                    case 'on_monerium_sign_delay_relay_success_close_clicked':
                                    case 'on_card_disconnected':
                                    case 'on_card_import_on_import_keys_clicked':
                                    case 'on_card_imported_success_animation_complete':
                                    case 'on_onboarded_card_imported_success_animation_complete':
                                        onMsg(msg)
                                        break
                                    case 'close':
                                        if (shouldShowMtPelerin) {
                                            setState({
                                                type: 'mt_pelerin_monerium_selection_screen',
                                            })
                                            break
                                        }
                                        onMsg(msg)
                                        break
                                    case 'on_switch_bank_transfer_provider_clicked':
                                        setState({
                                            type: 'unblock_bank_transfer',
                                        })
                                        break
                                    case 'on_user_not_eligible_for_monerium':
                                        setState({
                                            type: 'unblock_bank_transfer',
                                        })
                                        break
                                    case 'on_monerium_successfully_activated':
                                        noop() // no need to react to this here
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(msg)
                                }
                            }}
                        />
                    )
                case 'withdraw':
                    return (
                        <Withdraw
                            installationCampaign={installationCampaign}
                            selectedAdress={selectedAddress}
                            currencyHiddenMap={currencyHiddenMap}
                            portfolio={portfolio}
                            counterparties={counterparties}
                            keyStore={keyStore}
                            accountsMap={accountsMap}
                            cardConfig={cardConfig}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            installationId={installationId}
                            keyStoreMap={keyStoreMap}
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            portfolioMap={portfolioMap}
                            sessionPassword={sessionPassword}
                            location="bank_transfer"
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'on_4337_auto_gas_token_selection_clicked':
                                    case 'on_4337_gas_currency_selected':
                                    case 'on_activate_existing_monerium_account_click':
                                    case 'monerium_deposit_on_enable_card_clicked':
                                    case 'on_monerium_order_status_changed':
                                    case 'on_save_counterparty_form_submitted':
                                    case 'on_delete_counterparty_submitted':
                                    case 'on_delete_last_counterparty_submitted':
                                    case 'on_contact_support_clicked':
                                    case 'monerium_on_card_disconnected':
                                    case 'on_create_smart_wallet_clicked':
                                    case 'on_monerium_sign_delay_relay_success_close_clicked':
                                    case 'on_card_disconnected':
                                    case 'on_card_import_on_import_keys_clicked':
                                    case 'on_card_imported_success_animation_complete':
                                    case 'on_onboarded_card_imported_success_animation_complete':
                                        onMsg(msg)
                                        break
                                    case 'close':
                                        if (shouldShowMtPelerin) {
                                            setState({
                                                type: 'mt_pelerin_monerium_selection_screen',
                                            })
                                            break
                                        }
                                        onMsg(msg)
                                        break
                                    case 'on_user_not_eligible_for_monerium':
                                        setState({
                                            type: 'unblock_bank_transfer',
                                        })
                                        break
                                    case 'on_monerium_successfully_activated':
                                        noop() // no need to react to this here
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(msg)
                                }
                            }}
                        />
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(variant)
            }
        case 'unblock_bank_transfer':
            return (
                <UnblockDepositWithdraw
                    variant={variant}
                    network={BANK_TRANSFER_LOGIN_NETWORK}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    initialCurrency={null}
                    selectedAddress={selectedAddress}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    accountsMap={accountsMap}
                    bankTransfer={bankTransfer}
                    customCurrencies={customCurrencies}
                    cardConfig={cardConfig}
                    installationId={installationId}
                    keystoreMap={keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
