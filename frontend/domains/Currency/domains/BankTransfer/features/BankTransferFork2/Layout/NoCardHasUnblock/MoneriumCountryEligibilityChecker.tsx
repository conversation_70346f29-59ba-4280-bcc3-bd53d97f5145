import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { Address } from '@zeal/toolkit/Web3/address'

import { AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { EnableCardToUseMonerium } from '@zeal/domains/Card/domains/MoneriumBankTransfer/components/EnableCardToUseMonerium'
import { MONERIUM_SUPPORTED_COUNTRIES } from '@zeal/domains/Card/domains/MoneriumBankTransfer/constants'
import { CountryISOCode } from '@zeal/domains/Country'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { DepositWithdrawVariant } from '@zeal/domains/Currency/domains/BankTransfer'
import { BANK_TRANSFER_LOGIN_NETWORK } from '@zeal/domains/Currency/domains/BankTransfer/constants'
import { ChooseProviderCurrency } from '@zeal/domains/Currency/domains/BankTransfer/features/ChooseProviderCurrency'
import { DepositWithdraw as UnblockDepositWithdraw } from '@zeal/domains/Currency/domains/BankTransfer/features/DepositWithdraw'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    BankTransferUnblockUserCreated,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    userCountryCode: CountryISOCode
    installationId: string
    networkMap: NetworkMap
    selectedAddress: Address | null
    bankTransferInfo: BankTransferUnblockUserCreated
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    feePresetMap: FeePresetMap
    networkRPCMap: NetworkRPCMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: CardConfig
    variant: DepositWithdrawVariant
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof ChooseProviderCurrency>, { type: 'close' }>
    | MsgOf<typeof EnableCardToUseMonerium>
    | MsgOf<typeof UnblockDepositWithdraw>

type State =
    | { type: 'choose_provider_currency' }
    | { type: 'enable_card' }
    | { type: 'unblock_bank_transfers'; initialCurrency: CryptoCurrency | null }

const calculateState = (userCountryCode: CountryISOCode): State => {
    return MONERIUM_SUPPORTED_COUNTRIES.map((c) => c.code).includes(
        userCountryCode
    )
        ? { type: 'choose_provider_currency' }
        : { type: 'unblock_bank_transfers', initialCurrency: null }
}

export const MoneriumCountryEligibilityChecker = ({
    userCountryCode,
    onMsg,
    networkMap,
    installationId,
    variant,
    customCurrencies,
    sessionPassword,
    selectedAddress,
    accountsMap,
    keystoreMap,
    currencyHiddenMap,
    currencyPinMap,
    gasCurrencyPresetMap,
    feePresetMap,
    defaultCurrencyConfig,
    cardConfig,
    portfolioMap,
    networkRPCMap,
    bankTransferInfo,
}: Props) => {
    const [state, setState] = useState<State>(() =>
        calculateState(userCountryCode)
    )

    switch (state.type) {
        case 'choose_provider_currency':
            return (
                <ChooseProviderCurrency
                    installationId={installationId}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    networkMap={networkMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_monerium_currency_selected':
                            case 'on_mt_pelerin_currency_selected':
                                setState({ type: 'enable_card' })
                                break
                            case 'on_unblock_currency_selected':
                                setState({
                                    type: 'unblock_bank_transfers',
                                    initialCurrency: msg.currency,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'enable_card':
            return (
                <EnableCardToUseMonerium
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                const moneriumEligible =
                                    MONERIUM_SUPPORTED_COUNTRIES.map(
                                        (c) => c.code
                                    ).includes(userCountryCode)

                                if (moneriumEligible) {
                                    setState({
                                        type: 'choose_provider_currency',
                                    })
                                } else {
                                    onMsg(msg)
                                }
                                break
                            case 'monerium_deposit_on_enable_card_clicked':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'unblock_bank_transfers':
            return (
                <UnblockDepositWithdraw
                    variant={variant}
                    network={BANK_TRANSFER_LOGIN_NETWORK}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    initialCurrency={state.initialCurrency}
                    selectedAddress={selectedAddress}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    accountsMap={accountsMap}
                    bankTransfer={bankTransferInfo}
                    customCurrencies={customCurrencies}
                    cardConfig={cardConfig}
                    installationId={installationId}
                    keystoreMap={keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                const moneriumEligible =
                                    MONERIUM_SUPPORTED_COUNTRIES.map(
                                        (c) => c.code
                                    ).includes(userCountryCode)

                                if (moneriumEligible) {
                                    setState({
                                        type: 'choose_provider_currency',
                                    })
                                } else {
                                    onMsg(msg)
                                }
                                break
                            case 'on_account_create_request':
                            case 'import_keys_button_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_import_latest_bank_transfer_owner_clicked':
                            case 'on_user_login_to_unblock_success':
                            case 'kyc_applicant_created':
                            case 'bank_transfer_owner_successfully_changed':
                            case 'on_withdrawal_monitor_fiat_transaction_start':
                            case 'on_withdrawal_monitor_fiat_transaction_success':
                            case 'on_contact_support_clicked':
                            case 'on_on_ramp_transfer_success_close_click':
                            case 'on_kyc_data_updated_close_clicked':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
