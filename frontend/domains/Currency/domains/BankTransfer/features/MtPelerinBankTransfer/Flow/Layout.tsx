import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { Header } from '@zeal/uikit/Header'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldDiscount } from '@zeal/uikit/Icon/BoldDiscount'
import { BoldGeneralBank } from '@zeal/uikit/Icon/BoldGeneralBank'
import { ExternalLink } from '@zeal/uikit/Icon/ExternalLink'
import { MtPelerin } from '@zeal/uikit/Icon/Providers/MtPelerin'
import { ShieldDone } from '@zeal/uikit/Icon/ShieldDone'
import { IconButton } from '@zeal/uikit/IconButton'
import { BulletpointsListItem } from '@zeal/uikit/ListItem/BulletpointsListItem'
import { Screen } from '@zeal/uikit/Screen'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { openExternalURL } from '@zeal/toolkit/Window'

import { MT_PELERIN_ABOUT_US_URL } from '@zeal/domains/Currency/domains/BankTransfer/constants'

type Props = {
    url: URL
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const Layout = ({ onMsg, url }: Props) => {
    return (
        <Screen
            padding="form"
            background="default"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {() => <BackIcon size={24} color="iconDefault" />}
                    </IconButton>
                }
            />
            <Column spacing={24} fill>
                <Header
                    icon={({ size }) => (
                        <Avatar size={size} variant="rounded">
                            <MtPelerin size={size} />
                        </Avatar>
                    )}
                    title="Continue with Mt Pelerin"
                />
                <Column spacing={16} fill alignY="stretch">
                    <Group variant="default">
                        <BulletpointsListItem
                            avatar={({ size }) => (
                                <BoldGeneralBank size={size} color="green30" />
                            )}
                            text={
                                <FormattedMessage
                                    id="mtPelerinProviderInfo.registration"
                                    defaultMessage="Mt Pelerin Group Ltd is affiliated with SO-FIT, a self-regulatory body recognized by the Swiss Financial Authority (FINMA) under the Anti-Money Laundering Act. <link>Learn more</link>"
                                    values={{
                                        link: (chunks) => (
                                            <Tertiary
                                                color="on_light"
                                                size="regular"
                                                onClick={() =>
                                                    openExternalURL(
                                                        MT_PELERIN_ABOUT_US_URL
                                                    )
                                                }
                                            >
                                                {({
                                                    color,
                                                    textVariant,
                                                    textWeight,
                                                }) => (
                                                    <Text
                                                        textDecorationLine="underline"
                                                        color={color}
                                                        variant={textVariant}
                                                        weight={textWeight}
                                                    >
                                                        {chunks}
                                                    </Text>
                                                )}
                                            </Tertiary>
                                        ),
                                    }}
                                />
                            }
                            rightIcon={null}
                        />
                        <BulletpointsListItem
                            avatar={({ size }) => (
                                <BoldDiscount size={size} color="teal40" />
                            )}
                            text={
                                <FormattedMessage
                                    id="mtPelerinProviderInfo.fees"
                                    defaultMessage="You pay 0% fees"
                                />
                            }
                            rightIcon={null}
                        />
                        <BulletpointsListItem
                            avatar={({ size }) => (
                                <ShieldDone size={size} color="blue30" />
                            )}
                            text={
                                <FormattedMessage
                                    id="mtPelerinProviderInfo.selfCustody"
                                    defaultMessage="The digital cash that you receive is self custodied and no one else will have control over your assets"
                                />
                            }
                            rightIcon={null}
                        />
                    </Group>
                    <Actions variant="default" direction="row">
                        <Button
                            size="regular"
                            variant="secondary"
                            onClick={() => onMsg({ type: 'close' })}
                        >
                            <FormattedMessage
                                id="action.cancel"
                                defaultMessage="Cancel"
                            />
                        </Button>

                        <Button
                            size="regular"
                            variant="primary"
                            rightIcon={({ size, color }) => (
                                <ExternalLink size={size} color={color} />
                            )}
                            onClick={() => openExternalURL(url.toString())}
                        >
                            <FormattedMessage
                                id="action.continue"
                                defaultMessage="Continue"
                            />
                        </Button>
                    </Actions>
                </Column>
            </Column>
        </Screen>
    )
}
