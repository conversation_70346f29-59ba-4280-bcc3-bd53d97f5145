import { useEffect } from 'react'

import { Language } from '@zeal/uikit/Language'
import { useLanguage } from '@zeal/uikit/Language/LanguageContext'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import {
    generateRandomNumber,
    generateRandomNumberBetween,
} from '@zeal/toolkit/Number'
import { base64Encode, base64UrlEncode } from '@zeal/toolkit/String/base64'
import * as Web3 from '@zeal/toolkit/Web3'

import { CardSlientSignKeyStore } from '@zeal/domains/Card'
import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { signMessage } from '@zeal/domains/RPCRequest/helpers/signMessage'

import { Layout } from './Layout'

type Props = {
    cardReadonlySignerAddress: Web3.address.Address
    cardOwnerKeyStore: CardSlientSignKeyStore
    sessionPassword: string
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

type MtPelerinLanguage = 'en' | 'fr' | 'de' | 'it' | 'es' | 'pt'

const WIDGET_BASE_URL = new URL(
    'https://widget.mtpelerin.com/?_ctkn=21394ded-ade9-427d-889a-244a0ca9e70f&type=direct-link&tabs=buy&bsc=CHF&bsa=100&bdc=ZCHF&ssc=ZCHF&ssa=100&sdc=CHF&wsc=XDAI&wsa=100&wdc=ZCHF&dnet=xdai_mainnet&snet=xdai_mainnet&rfr=zeal'
)

const LANGUAGE_TO_MT_PELERIN_LANGUAGE_MAP: Record<
    Language,
    MtPelerinLanguage | null
> = {
    'en-GB': 'en',
    'bg-BG': null,
    'fr-FR': 'fr',
    'fr-BE': 'fr',
    'de-DE': 'de',
    'es-ES': 'es',
    'ca-ES': 'es',
    'pt-BR': 'pt',
    'pt-PT': 'pt',
    'it-IT': 'it',
    'hr-HR': null,
    'hu-HU': null,
    'is-IS': null,
    'lb-LU': null,
    'lt-LT': null,
    'lv-LV': null,
    'mt-MT': null,
    'nb-NO': null,
    'nl-BE': null,
    'nl-NL': null,
    'pl-PL': null,
    'uk-UA': null,
    'sv-SE': null,
    'cs-CZ': null,
    'da-DK': null,
    'el-GR': null,
    'et-EE': null,
    'fi-FI': null,
    'af-ZA': null,
    'ro-RO': null,
    'sk-SK': null,
    'sl-SI': null,
    'sq-AL': null,
    'sr-RS': null,
    'tr-TR': null,
    'ga-IE': null,
}

const generateUrl = async ({
    cardOwnerKeyStore,
    cardReadonlySignerAddress,
    sessionPassword,
    currentSelectedLanguage,
}: {
    cardReadonlySignerAddress: Web3.address.Address
    cardOwnerKeyStore: CardSlientSignKeyStore
    currentSelectedLanguage: Language
    sessionPassword: string
}): Promise<URL> => {
    const url = new URL(WIDGET_BASE_URL)

    const language =
        LANGUAGE_TO_MT_PELERIN_LANGUAGE_MAP[currentSelectedLanguage] || 'en'

    const code = generateRandomNumberBetween(1000, 9999) // https://developers.mtpelerin.com/integration-guides/parameters-and-customization/automating-the-end-user-address-validation
    const message = `MtPelerin-${code}`

    const signature = await signMessage({
        request: {
            id: generateRandomNumber(),
            jsonrpc: '2.0',
            method: 'personal_sign',
            params: [message],
        },
        keyStore: cardOwnerKeyStore,
        network: CARD_NETWORK,
        sessionPassword,
        dApp: null,
    })

    const signatureBase64 = base64Encode(
        Uint8Array.from(Hexadecimal.toBuffer(signature))
    )

    url.search = (() => {
        switch (cardOwnerKeyStore.type) {
            case 'private_key_store':
            case 'secret_phrase_key':
                return new URLSearchParams({
                    ...Object.fromEntries(WIDGET_BASE_URL.searchParams),
                    lang: language,
                    addr: cardReadonlySignerAddress,
                    code: code.toString(),
                    hash: base64UrlEncode(signatureBase64),
                }).toString()
            case 'safe_4337':
                return new URLSearchParams({
                    ...Object.fromEntries(WIDGET_BASE_URL.searchParams),
                    lang: language,
                    addr: cardReadonlySignerAddress,
                    code: code.toString(),
                    hash: base64UrlEncode(signatureBase64),
                    chain: 'xdai_mainnet',
                }).toString()
            default:
                return notReachable(cardOwnerKeyStore)
        }
    })()

    return url
}

export const Flow = ({
    onMsg,
    cardReadonlySignerAddress,
    sessionPassword,
    cardOwnerKeyStore,
}: Props) => {
    const { currentSelectedLanguage } = useLanguage()

    const [loadable] = useLoadableData(generateUrl, {
        type: 'loading',
        params: {
            cardOwnerKeyStore,
            cardReadonlySignerAddress,
            currentSelectedLanguage,
            sessionPassword,
        },
    })

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
            case 'loaded':
                break
            case 'error':
                captureError(loadable.error)
                break
            default:
                return notReachable(loadable)
        }
    }, [loadable])

    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout
                    actionBar={null}
                    title={null}
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'loaded':
            return <Layout url={loadable.data} onMsg={onMsg} />
        case 'error':
            return <Layout url={WIDGET_BASE_URL} onMsg={onMsg} />
        default:
            return notReachable(loadable)
    }
}
