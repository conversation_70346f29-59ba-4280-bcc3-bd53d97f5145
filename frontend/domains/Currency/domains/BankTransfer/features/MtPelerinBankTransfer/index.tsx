import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import { HardwareWalletSupportDrop } from '@zeal/domains/Card/features/CardHardwareWalletSupportDrop'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Flow } from './Flow'

type Props = {
    cardReadonlySignerAddress: Web3.address.Address
    cardOwnerKeyStore: SigningKeyStore

    installationId: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationCampaign: string | null
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' } | MsgOf<typeof HardwareWalletSupportDrop>

export const MtPelerinBankTransfer = ({
    onMsg,
    cardOwnerKeyStore,
    cardReadonlySignerAddress,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    currencyHiddenMap,
    accountsMap,
    keystoreMap,
    sessionPassword,
    installationId,
    installationCampaign,
    portfolioMap,
}: Props) => {
    switch (cardOwnerKeyStore.type) {
        case 'trezor':
        case 'ledger':
            return (
                <HardwareWalletSupportDrop
                    variant="closable"
                    installationId={installationId}
                    cardReadonlySignerAddress={cardReadonlySignerAddress}
                    accountsMap={accountsMap}
                    keystoreMap={keystoreMap}
                    portfolioMap={portfolioMap}
                    currencyHiddenMap={currencyHiddenMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    installationCampaign={installationCampaign}
                    onMsg={onMsg}
                />
            )
        case 'private_key_store':
        case 'secret_phrase_key':
        case 'safe_4337':
            return (
                <Flow
                    cardOwnerKeyStore={cardOwnerKeyStore}
                    cardReadonlySignerAddress={cardReadonlySignerAddress}
                    sessionPassword={sessionPassword}
                    onMsg={onMsg}
                />
            )
        default:
            return notReachable(cardOwnerKeyStore)
    }
}
