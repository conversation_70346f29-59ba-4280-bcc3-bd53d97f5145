import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { fetchUnblockSupportedCurrencies } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { NetworkMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Layout } from './Layout'

type Props = {
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' } | MsgOf<typeof Layout>

export const ChooseProviderCurrency = ({
    onMsg,
    defaultCurrencyConfig,
    networkMap,
    installationId,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(
        fetchUnblockSupportedCurrencies,
        {
            type: 'loading',
            params: { networkMap },
        }
    )

    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout
                    title={null}
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={
                            <ActionBar
                                left={
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => onMsg({ type: 'close' })}
                                    >
                                        {({ color }) => (
                                            <BackIcon size={24} color={color} />
                                        )}
                                    </IconButton>
                                }
                            />
                        }
                        onClose={() => onMsg({ type: 'close' })}
                    />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        case 'loaded':
            return (
                <Layout
                    networkMap={networkMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    unblockSupportedCurrencies={loadable.data}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
