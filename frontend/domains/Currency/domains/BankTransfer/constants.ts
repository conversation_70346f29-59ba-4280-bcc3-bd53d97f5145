import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { values } from '@zeal/toolkit/Object'

import { Country, CountryISOCode } from '@zeal/domains/Country'
import { COUNTRIES_MAP } from '@zeal/domains/Country/constants'
import { CryptoCurrency } from '@zeal/domains/Currency'
import {
    ARBITRUM_USDC,
    ARBITRUM_USDC_E,
    ARBITRUM_USDT,
    BASE_USDC,
    FIAT_CURRENCIES,
    OPTIMISM_USDC,
    OPTIMISM_USDC_E,
    OPTIMISM_USDT,
    POLYGON_USDC,
    POLYGON_USDC_E,
    POLYGON_USDT_POS,
} from '@zeal/domains/Currency/constants'
import { FiatMoney } from '@zeal/domains/Money'
import { Network, NetworkHexId } from '@zeal/domains/Network'
import {
    ARBITRUM,
    BASE,
    OPTIMISM,
    POLYGON,
} from '@zeal/domains/Network/constants'

import { UnblockChain } from '.'
import { BankTransferFiatCurrencies } from './api/fetchUnblockSupportedCurrencies'

export const UNBLOCK_URL = 'https://www.getunblock.com/about-us'

export const MT_PELERIN_ABOUT_US_URL = 'https://www.mtpelerin.com/about-us'

export const BANK_TRANSFER_LOGIN_NETWORK: Network = OPTIMISM

export const POST_KYC_TRANSFER_LIMIT_IN_USD: FiatMoney = {
    amount: fromFixedWithFraction('100000', FIAT_CURRENCIES.USD.fraction),
    currency: FIAT_CURRENCIES.USD,
}

export const MINIMUM_TRANSFER_AMOUNT_IN_USD: FiatMoney = {
    amount: fromFixedWithFraction('2', FIAT_CURRENCIES.USD.fraction),
    currency: FIAT_CURRENCIES.USD,
}

export const OFF_RAMP_SERVICE_TIME_MS = 120_000

export const ON_RAMP_SERVICE_TIME_MS = 120_000
export const SUPPORT_SOFT_DEADLINE_MS = 60_000 * 60 * 24

export const UNBLOCK_SUPPORTED_NETWORK_MAP: Record<UnblockChain, NetworkHexId> =
    {
        base: BASE.hexChainId,
        polygon: POLYGON.hexChainId,
        arbitrum: ARBITRUM.hexChainId,
        optimism: OPTIMISM.hexChainId,
    }

export const UNBLOCK_FIAT_CURRENCIES_MAP: BankTransferFiatCurrencies = {
    GBP: FIAT_CURRENCIES['GBP'],
    EUR: FIAT_CURRENCIES['EUR'],
}

export const UNBLOCK_SUPPORTED_CRYPTO_CURRENCIES: CryptoCurrency[] = [
    POLYGON_USDC,
    POLYGON_USDC_E,
    POLYGON_USDT_POS,

    OPTIMISM_USDC,
    OPTIMISM_USDC_E,
    OPTIMISM_USDT,

    ARBITRUM_USDC,
    ARBITRUM_USDC_E,
    ARBITRUM_USDT,

    BASE_USDC,
]

/*
Script to update the list (just add API-Key and paste in node)

console.log(
  (
    await fetch("https://sandbox.getunblock.com/supported/countries", {
      headers: {
        accept: "application/json",
        authorization:
          "API-Key *****"
      },
      body: null,
      method: "GET",
    })
      .then((response) => response.json())
      .then((countries) =>
        countries
          .filter((country) => !country.is_individual_supported)
          .map(
            (country) =>
              `${country.country_code}: true, // ${country.country_name}`
          ).toSorted()
      )
  ).join("\n")
);


*/
const countriesUnsupportedByUnblock: Partial<Record<CountryISOCode, true>> = {
    AF: true, // Afghanistan
    AL: true, // Albania
    AM: true, // Armenia
    AZ: true, // Azerbaijan
    BB: true, // Barbados
    BF: true, // Burkina Faso
    BI: true, // Burundi
    BW: true, // Botswana
    BY: true, // Belarus
    CD: true, // Congo (the Democratic Republic of the)
    CF: true, // Central African Republic (the)
    CN: true, // China
    CU: true, // Cuba
    EH: true, // Western Sahara*
    ER: true, // Eritrea
    GH: true, // Ghana
    GN: true, // Guinea
    GW: true, // Guinea-Bissau
    HT: true, // Haiti
    IQ: true, // Iraq
    IR: true, // Iran (Islamic Republic of)
    JM: true, // Jamaica
    JO: true, // Jordan
    JP: true, // Japan
    KG: true, // Kyrgyzstan
    KH: true, // Cambodia
    KP: true, // Korea (the Democratic People's Republic of)
    KZ: true, // Kazakhstan
    LB: true, // Lebanon
    LR: true, // Liberia
    LY: true, // Libya
    MA: true, // Morocco
    MD: true, // Moldova (the Republic of)
    MG: true, // Madagascar
    ML: true, // Mali
    MM: true, // Myanmar
    MZ: true, // Mozambique
    NI: true, // Nicaragua
    PA: true, // Panama
    PH: true, // Philippines (the)
    PS: true, // Palestine, State of
    RU: true, // Russian Federation (the)
    SD: true, // Sudan (the)
    SN: true, // Senegal
    SO: true, // Somalia
    SS: true, // South Sudan
    SY: true, // Syrian Arab Republic (the)
    TJ: true, // Tajikistan
    TM: true, // Turkmenistan
    TN: true, // Tunisia
    TR: true, // Türkiye
    TT: true, // Trinidad and Tobago
    TZ: true, // Tanzania, the United Republic of
    UA: true, // Ukraine
    UG: true, // Uganda
    US: true, // United States of America (the)
    UZ: true, // Uzbekistan
    VE: true, // Venezuela (Bolivarian Republic of)
    VN: true, // Viet Nam
    VU: true, // Vanuatu
    YE: true, // Yemen
    ZW: true, // Zimbabwe
    GB: true,
}

export const UNBLOCK_SUPPORTED_COUNTRIES: Country[] = values(COUNTRIES_MAP)
    .filter((country) => !countriesUnsupportedByUnblock[country.code])
    .sort((a, b) => a.name.localeCompare(b.name))
