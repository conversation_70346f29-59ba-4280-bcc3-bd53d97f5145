import intersection from 'lodash/intersection'

import { notReachable } from '@zeal/toolkit'

import { CardConfig } from '@zeal/domains/Card'
import { countriesSupportedByGnosisPay } from '@zeal/domains/Card/constants'
import { CountryISOCode } from '@zeal/domains/Country'
import { EEA_COUNTRIES } from '@zeal/domains/Country/constants'
import { tryToGetUserTimeZoneCountries } from '@zeal/domains/Country/helpers/tryToGetUserTimeZoneCountries'
import { FIAT_DUST } from '@zeal/domains/Currency/constants'
import { Earn, Taker, TakerApyMap, TakerType } from '@zeal/domains/Earn'
import { sumEarn } from '@zeal/domains/Earn/helpers/sumEarn'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type EarnCarouselItem = { type: 'earn_account_promo'; taker: Taker }

export type MarketingCarouselItem = { type: 'card_promo' } | EarnCarouselItem

export type MarketingCarouselState =
    | { type: 'hidden' }
    | { type: 'populated'; items: MarketingCarouselItem[] }

const sortEarnPromoItemsByAPY = ({
    items,
    takerApyMap,
}: {
    items: EarnCarouselItem[]
    takerApyMap: TakerApyMap
}): EarnCarouselItem[] => {
    return items.toSorted((a, b) => {
        const apyA = takerApyMap[a.taker.type]
        const apyB = takerApyMap[b.taker.type]
        return apyB - apyA
    })
}

const getEarnCarouselItems = ({
    earn,
    tzCountries,
}: {
    earn: Earn
    tzCountries: CountryISOCode[]
}): EarnCarouselItem[] => {
    const earnPromoItems = earn.takers
        .filter((taker) => {
            switch (taker.type) {
                case 'usd':
                case 'eur':
                case 'chf':
                    return true
                case 'eth':
                    return false
                /* istanbul ignore next */
                default:
                    return notReachable(taker.type)
            }
        })
        .map((taker) => ({
            type: 'earn_account_promo' as const,
            taker,
        }))

    const isSwiss = tzCountries.includes('CH')

    const isEuropean = tzCountries.some((country) =>
        EEA_COUNTRIES.includes(country)
    )

    const priorityTakerType: TakerType | null = isSwiss
        ? 'chf'
        : isEuropean
          ? 'eur'
          : null

    const priorityItem = earnPromoItems.find(
        (item) => item.taker.type === priorityTakerType
    )

    if (!priorityItem) {
        return sortEarnPromoItemsByAPY({
            items: earnPromoItems,
            takerApyMap: earn.takerApyMap,
        })
    }

    const rest = earnPromoItems.filter((item) => item !== priorityItem)

    return [
        priorityItem,
        ...sortEarnPromoItemsByAPY({
            items: rest,
            takerApyMap: earn.takerApyMap,
        }),
    ]
}

export const calculateMarketingCarouselState = ({
    earn,
    cardConfig,
    defaultCurrencyConfig,
}: {
    earn: Earn
    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
}): MarketingCarouselState => {
    const userTimeZoneCountries =
        tryToGetUserTimeZoneCountries()
            .getSuccessResult()
            ?.map((country) => country.code) || []

    const earnPromoItems = getEarnCarouselItems({
        earn,
        tzCountries: userTimeZoneCountries,
    })

    const userIsInCardUnsupportedCountry =
        intersection(countriesSupportedByGnosisPay, userTimeZoneCountries)
            .length === 0

    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
            switch (earn.type) {
                case 'not_configured':
                    return {
                        type: 'populated',
                        items: userIsInCardUnsupportedCountry
                            ? earnPromoItems
                            : [{ type: 'card_promo' }, ...earnPromoItems],
                    }
                case 'configured':
                    const earnValueInDefaultCurrency = sumEarn({
                        earn: earn,
                        defaultCurrencyConfig,
                    })

                    return earnValueInDefaultCurrency.amount > FIAT_DUST
                        ? userIsInCardUnsupportedCountry
                            ? {
                                  type: 'hidden',
                              }
                            : {
                                  type: 'populated',
                                  items: [{ type: 'card_promo' }],
                              }
                        : {
                              type: 'populated',
                              items: userIsInCardUnsupportedCountry
                                  ? earnPromoItems
                                  : [{ type: 'card_promo' }, ...earnPromoItems],
                          }
                default:
                    return notReachable(earn)
            }
        case 'card_readonly_signer_address_is_selected_fully_onboarded':
        case 'card_readonly_signer_address_is_selected':
            switch (earn.type) {
                case 'not_configured':
                    return {
                        type: 'populated',
                        items: earnPromoItems,
                    }
                case 'configured':
                    const earnValueInDefaultCurrency = sumEarn({
                        earn: earn,
                        defaultCurrencyConfig,
                    })
                    return earnValueInDefaultCurrency.amount > FIAT_DUST
                        ? {
                              type: 'hidden',
                          }
                        : {
                              type: 'populated',
                              items: earnPromoItems,
                          }

                default:
                    return notReachable(earn)
            }
        default:
            return notReachable(cardConfig)
    }
}
