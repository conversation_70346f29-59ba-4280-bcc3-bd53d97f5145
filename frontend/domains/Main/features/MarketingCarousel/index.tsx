import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { Earn, EarnTakerMetrics } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { MarketingCarouselItem } from '@zeal/domains/Main/helpers/calculateMarketingCarouselState'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    items: MarketingCarouselItem[]
    earn: Earn
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnTakerMetrics: EarnTakerMetrics
    installationId: string
    earnOwner: Account
    cardConfig: CardConfig
    portfolioMap: PortfolioMap
    accounts: AccountsMap
    keystores: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    isEthereumNetworkFeeWarningSeen: boolean
    networkRPCMap: NetworkRPCMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    customCurrencies: CustomCurrencyMap
    installationCampaign: string | null
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          {
              type: 'on_card_marketing_card_click'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_earn_deposit_success'
                  | 'add_wallet_clicked'
                  | 'on_bank_transfer_selected'
                  | 'track_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_accounts_create_success_animation_finished'
                  | 'hardware_wallet_clicked'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'cancel_submitted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_earn_configured'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
                  | 'on_swaps_io_swap_request_created'
          }
      >

export const MarketingCarousel = ({
    items,
    onMsg,
    installationId,
    earn,
    earnOwner,
    isEthereumNetworkFeeWarningSeen,
    cardConfig,
    customCurrencies,
    currencyHiddenMap,
    currencyPinMap,
    portfolioMap,
    gasCurrencyPresetMap,
    feePresetMap,
    networkMap,
    networkRPCMap,
    installationCampaign,
    sessionPassword,
    keystores,
    accounts,
    earnTakerMetrics,
    defaultCurrencyConfig,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })
    return (
        <>
            <Layout
                installationCampaign={installationCampaign}
                marketingItems={items}
                installationId={installationId}
                earn={earn}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_taker_investment_tile_click':
                            switch (msg.taker.type) {
                                case 'usd':
                                case 'eur':
                                case 'chf':
                                    setModal({
                                        type: 'taker_investment_details',
                                        taker: msg.taker,
                                    })
                                    break
                                case 'eth':
                                    throw new ImperativeError(
                                        'Impossible state - taker is ETH, but clicked on investment tile'
                                    )
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg.taker.type)
                            }
                            break
                        case 'on_taker_investment_tile_deposit_click':
                            setModal({ type: 'deposit', taker: msg.taker })
                            break
                        case 'on_card_marketing_card_click':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
            <Modal
                earnTakerMetrics={earnTakerMetrics}
                defaultCurrencyConfig={defaultCurrencyConfig}
                customCurrencies={customCurrencies}
                portfolioMap={portfolioMap}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                earn={earn}
                cardConfig={cardConfig}
                accounts={accounts}
                keystores={keystores}
                networkMap={networkMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                sessionPassword={sessionPassword}
                installationId={installationId}
                networkRPCMap={networkRPCMap}
                currencyPinMap={currencyPinMap}
                currencyHiddenMap={currencyHiddenMap}
                earnOwner={earnOwner}
                state={modal}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_earn_deposit_asset_click':
                            setModal({ type: 'deposit', taker: msg.taker })
                            break
                        case 'on_earn_withdrawal_asset_click':
                            throw new ImperativeError(
                                'Cannot withdraw from earn in Zero state'
                            )
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_earn_deposit_success':
                        case 'add_wallet_clicked':
                        case 'on_bank_transfer_selected':
                        case 'track_wallet_clicked':
                        case 'on_account_create_request':
                        case 'on_accounts_create_success_animation_finished':
                        case 'hardware_wallet_clicked':
                        case 'on_add_label_to_track_only_account_during_send':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'cancel_submitted':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_request_replaced':
                        case 'transaction_submited':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_earn_configured':
                        case 'on_address_scanned':
                        case 'on_address_scanned_and_add_label':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_chf_taker_metrics_loaded':
                        case 'on_swaps_io_swap_request_created':
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
