import { useEffect } from 'react'

import { StepWizard } from '@zeal/uikit/StepWizard'

import { notReachable } from '@zeal/toolkit'
import {
    LoadableData,
    useLoadableData,
} from '@zeal/toolkit/LoadableData/LoadableData'
import { useLoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { keys } from '@zeal/toolkit/Object'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import {
    NotConfiguredEarn,
    NotDeployedTaker,
    TakerApyMap,
    TakerPortfolioMap2,
} from '@zeal/domains/Earn'
import { fetchTakerUserCurrencyRateAndApy } from '@zeal/domains/Earn/api/fetchTakerUserCurrencyRate'
import { EARN_PRIMARY_INVESTMENT_ASSETS_MAP } from '@zeal/domains/Earn/constants'
import { getHolderPredictedAddress } from '@zeal/domains/Earn/helpers/getHolderPredictedAddress'
import { getTakerPredictedAddress } from '@zeal/domains/Earn/helpers/getTakerPredictedAddress'
import { unsafeGetNotConfiguredEarn } from '@zeal/domains/Earn/helpers/unsafeGetNotConfiguredEarn'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { Network, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig, Storage } from '@zeal/domains/Storage'
import { addAccountsWithKeystores } from '@zeal/domains/Storage/helpers/addAccountsWithKeystores'
import { init } from '@zeal/domains/Storage/helpers/init'
import { SubmittedUserOperationPending } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import { fetchSubmittedUserOperation } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation/api/fetchSubmittedUserOperation'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { CreatingAccountSpinner } from './CreatingAccountSpinner'
import { getEmptyPortfolioWithEarn } from './getEmptyPortfolioWithEarn'

import { AddPinWithReferralCodeChecker } from '../../AddPinWithReferralCodeChecker'

type Props = {
    keyStore: Safe4337
    account: Account
    submittedUserOperation: SubmittedUserOperationPending
    defaultCurrencyConfig: DefaultCurrencyConfig
    cardConfig: CardConfig
    network: Network
    networkRPCMap: NetworkRPCMap
    installationId: string
    sessionPassword: string
    referralConfig: ReferralConfig
    referralCodeNeededLoadable: LoadableData<boolean, unknown>
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof CreatingAccountSpinner>
    | {
          type: 'on_onboarding_completed'
          account: Account
          keyStore: Safe4337
          storage: Storage
          sessionPassword: string
      }

type State =
    | { type: 'add_pin' }
    | {
          type: 'creating_account_spinner'
          sessionPassword: string
          encryptedPassword: string
      }

const POLL_INTERVAL_MS = 500

const fetch = async ({
    address,
    networkRPCMap,
    signal,
}: {
    address: Web3.address.Address
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<NotConfiguredEarn> => {
    const takerTypes = keys(EARN_PRIMARY_INVESTMENT_ASSETS_MAP)

    const takerEmpltyPortfolioMap = await Promise.all(
        takerTypes.map((takerType) =>
            fetchTakerUserCurrencyRateAndApy({
                takerType,
                networkRPCMap,
                signal,
            })
        )
    ).then((takersUserCurrencyRateAndApy) =>
        takersUserCurrencyRateAndApy.reduce(
            (acc, { takerType, apy, userCurrencyRate }) => {
                acc[takerType] = {
                    assetBalance: {
                        amount: 0n,
                        currency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP[takerType],
                    },
                    apy,
                    userCurrencyToDefaultCurrencyRate: null,
                    userCurrencyRate,
                    dataTimestampMs: Date.now(),
                }
                return acc
            },
            {} as TakerPortfolioMap2
        )
    )
    const holder = getHolderPredictedAddress({ earnOwnerAddress: address })
    const takers = takerTypes.map<NotDeployedTaker>((takerType) => {
        const takerAddress = getTakerPredictedAddress({ holder, takerType })

        return {
            type: takerType,
            state: 'not_deployed',
            cryptoCurrency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP[takerType],
            address: takerAddress,
        }
    })

    return {
        type: 'not_configured',
        takers,
        holder,
        takerPortfolioMap: takerEmpltyPortfolioMap,
        takerApyMap: takerTypes.reduce((acc, takerType) => {
            acc[takerType] = takerEmpltyPortfolioMap[takerType].apy
            return acc
        }, {} as TakerApyMap),
    }
}

export const MonitorDeployment = ({
    onMsg,
    submittedUserOperation,
    installationId,
    networkRPCMap,
    defaultCurrencyConfig,
    network,
    referralCodeNeededLoadable,
    cardConfig,
    account,
    sessionPassword,
    keyStore,
    referralConfig,
}: Props) => {
    const [pollable] = useLoadedPollableData(
        fetchSubmittedUserOperation,
        {
            type: 'reloading',
            params: { network, submittedUserOperation, networkRPCMap },
            data: submittedUserOperation,
        },
        {
            stopIf: (pollable) => {
                switch (pollable.type) {
                    case 'loaded':
                        switch (pollable.data.state) {
                            case 'pending':
                            case 'bundled':
                                return false
                            case 'completed':
                            case 'rejected':
                            case 'failed':
                                return true
                            /* istanbul ignore next */
                            default:
                                return notReachable(pollable.data)
                        }
                    case 'reloading':
                    case 'subsequent_failed':
                        return false
                    /* istanbul ignore next */
                    default:
                        return notReachable(pollable)
                }
            },
            pollIntervalMilliseconds: POLL_INTERVAL_MS,
        }
    )

    useEffect(() => {
        switch (pollable.type) {
            case 'loaded':
                switch (pollable.data.state) {
                    case 'pending':
                    case 'bundled':
                    case 'rejected':
                    case 'failed':
                        break
                    case 'completed':
                        postUserEvent({
                            type: 'SmartWalletDeployedEvent',
                            installationId,
                        })
                        break
                    /* istanbul ignore next */
                    default:
                        return notReachable(pollable.data)
                }
                break
            case 'reloading':
            case 'subsequent_failed': // reporting is done in spinner component
                break
            /* istanbul ignore next */
            default:
                return notReachable(pollable)
        }
    }, [installationId, pollable])

    const [notConfiguredEarnLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            address: account.address,
            networkRPCMap,
        },
    })

    return (
        <StepWizard<State> initialStep={{ type: 'add_pin' }}>
            {({ step, forwardTo }) => {
                switch (step.type) {
                    case 'add_pin':
                        return (
                            <AddPinWithReferralCodeChecker
                                cardConfig={cardConfig}
                                referralCodeNeededLoadable={
                                    referralCodeNeededLoadable
                                }
                                sessionPassword={sessionPassword}
                                installationId={installationId}
                                referralConfig={referralConfig}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'password_added':
                                            switch (pollable.type) {
                                                case 'loaded':
                                                    switch (
                                                        pollable.data.state
                                                    ) {
                                                        case 'pending':
                                                        case 'bundled':
                                                        case 'rejected':
                                                        case 'failed':
                                                            forwardTo({
                                                                type: 'creating_account_spinner',
                                                                encryptedPassword:
                                                                    msg.encryptedPassword,
                                                                sessionPassword:
                                                                    msg.sessionPassword,
                                                            })
                                                            break
                                                        case 'completed':
                                                            switch (
                                                                notConfiguredEarnLoadable.type
                                                            ) {
                                                                case 'loading':
                                                                    forwardTo({
                                                                        type: 'creating_account_spinner',
                                                                        encryptedPassword:
                                                                            msg.encryptedPassword,
                                                                        sessionPassword:
                                                                            msg.sessionPassword,
                                                                    })
                                                                    break
                                                                case 'loaded': {
                                                                    const storage =
                                                                        init(
                                                                            msg.encryptedPassword
                                                                        )
                                                                    const withPortfolioCache: Storage =
                                                                        {
                                                                            ...storage,
                                                                            portfolios:
                                                                                {
                                                                                    [account.address]:
                                                                                        getEmptyPortfolioWithEarn(
                                                                                            {
                                                                                                earn: notConfiguredEarnLoadable.data,
                                                                                                defaultCurrencyConfig,
                                                                                            }
                                                                                        ),
                                                                                },
                                                                        }

                                                                    onMsg({
                                                                        type: 'on_onboarding_completed',
                                                                        sessionPassword:
                                                                            sessionPassword,
                                                                        account,
                                                                        keyStore,
                                                                        storage:
                                                                            addAccountsWithKeystores(
                                                                                withPortfolioCache,
                                                                                [
                                                                                    {
                                                                                        account,
                                                                                        keystore:
                                                                                            keyStore,
                                                                                    },
                                                                                ],
                                                                                account.address
                                                                            ),
                                                                    })
                                                                    break
                                                                }
                                                                case 'error':
                                                                    const storage =
                                                                        init(
                                                                            msg.encryptedPassword
                                                                        )
                                                                    const withPortfolioCache: Storage =
                                                                        {
                                                                            ...storage,
                                                                            portfolios:
                                                                                {
                                                                                    [account.address]:
                                                                                        getEmptyPortfolioWithEarn(
                                                                                            {
                                                                                                earn: unsafeGetNotConfiguredEarn(
                                                                                                    {
                                                                                                        address:
                                                                                                            account.address,
                                                                                                    }
                                                                                                ),
                                                                                                defaultCurrencyConfig,
                                                                                            }
                                                                                        ),
                                                                                },
                                                                        }

                                                                    onMsg({
                                                                        type: 'on_onboarding_completed',
                                                                        account,
                                                                        keyStore,
                                                                        sessionPassword:
                                                                            sessionPassword,
                                                                        storage:
                                                                            addAccountsWithKeystores(
                                                                                withPortfolioCache,
                                                                                [
                                                                                    {
                                                                                        account,
                                                                                        keystore:
                                                                                            keyStore,
                                                                                    },
                                                                                ],
                                                                                account.address
                                                                            ),
                                                                    })
                                                                    break
                                                                /* istanbul ignore next */
                                                                default:
                                                                    return notReachable(
                                                                        notConfiguredEarnLoadable
                                                                    )
                                                            }
                                                            break
                                                        /* istanbul ignore next */
                                                        default:
                                                            return notReachable(
                                                                pollable.data
                                                            )
                                                    }
                                                    break
                                                case 'reloading':
                                                case 'subsequent_failed':
                                                    forwardTo({
                                                        type: 'creating_account_spinner',
                                                        encryptedPassword:
                                                            msg.encryptedPassword,
                                                        sessionPassword:
                                                            msg.sessionPassword,
                                                    })
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(
                                                        pollable
                                                    )
                                            }
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg.type)
                                    }
                                }}
                            />
                        )
                    case 'creating_account_spinner':
                        return (
                            <CreatingAccountSpinner
                                installationId={installationId}
                                account={account}
                                keyStore={keyStore}
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                sessionPassword={sessionPassword}
                                encryptedPassword={step.encryptedPassword}
                                notConfiguredEarnLoadable={
                                    notConfiguredEarnLoadable
                                }
                                deploymentPollable={pollable}
                                onMsg={onMsg}
                            />
                        )
                    /* istanbul ignore next */
                    default:
                        return notReachable(step)
                }
            }}
        </StepWizard>
    )
}
