import { useEffect } from 'react'

import { StepWizard } from '@zeal/uikit/StepWizard'

import { notReachable } from '@zeal/toolkit'
import { LoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { Passkey } from '@zeal/domains/KeyStore/domains/Passkey'
import { NetworkRPCMap } from '@zeal/domains/Network'
import { SMART_WALLET_REFERENCE_NETWORK } from '@zeal/domains/Network/constants'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { SubmittedUserOperationPending } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { CreatePasskey } from './CreatePasskey'
import { MonitorDeployment } from './MonitorDeployment'
import { SignAndSubmitDeployment } from './SignAndSubmitDeployment'

type Props = {
    installationId: string
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    referralConfig: ReferralConfig
    referralCodeNeededLoadable: LoadableData<boolean, unknown>
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof CreatePasskey>, { type: 'close' }>
    | Extract<
          MsgOf<typeof MonitorDeployment>,
          { type: 'on_onboarding_completed' }
      >

type State =
    | { type: 'create_passkey' }
    | { type: 'sign_and_submit_deployment'; passkey: Passkey; label: string }
    | {
          type: 'monitor_deployment'
          keyStore: Safe4337
          account: Account
          submittedUserOperation: SubmittedUserOperationPending
      }

export const CreateAccount = ({
    onMsg,
    sessionPassword,
    installationId,
    referralCodeNeededLoadable,
    networkRPCMap,
    defaultCurrencyConfig,
    cardConfig,
    referralConfig,
}: Props) => {
    useEffect(() => {
        postUserEvent({
            type: 'SmartWalletCreationFlowEnteredEvent',
            installationId,
        })
    }, [installationId])

    return (
        <StepWizard<State> initialStep={{ type: 'create_passkey' }}>
            {({ step, forwardTo, backTo }) => {
                switch (step.type) {
                    case 'create_passkey':
                        return (
                            <CreatePasskey
                                installationId={installationId}
                                sessionPassword={sessionPassword}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        case 'on_passkey_created':
                                            forwardTo({
                                                type: 'sign_and_submit_deployment',
                                                passkey: msg.passkey,
                                                label: msg.label,
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'sign_and_submit_deployment':
                        return (
                            <SignAndSubmitDeployment
                                installationId={installationId}
                                sessionPassword={sessionPassword}
                                passkey={step.passkey}
                                label={step.label}
                                network={SMART_WALLET_REFERENCE_NETWORK}
                                networkRPCMap={networkRPCMap}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'on_deployment_submitted':
                                            forwardTo({
                                                type: 'monitor_deployment',
                                                account: msg.account,
                                                keyStore: msg.keyStore,
                                                submittedUserOperation:
                                                    msg.submittedUserOperation,
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg.type)
                                    }
                                }}
                            />
                        )
                    case 'monitor_deployment':
                        return (
                            <MonitorDeployment
                                referralCodeNeededLoadable={
                                    referralCodeNeededLoadable
                                }
                                cardConfig={cardConfig}
                                referralConfig={referralConfig}
                                keyStore={step.keyStore}
                                account={step.account}
                                submittedUserOperation={
                                    step.submittedUserOperation
                                }
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                network={SMART_WALLET_REFERENCE_NETWORK}
                                installationId={installationId}
                                networkRPCMap={networkRPCMap}
                                sessionPassword={sessionPassword}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            backTo({
                                                type: 'sign_and_submit_deployment',
                                                passkey:
                                                    step.keyStore
                                                        .safeDeplymentConfig
                                                        .passkeyOwner,
                                                label: step.account.label,
                                            })
                                            break
                                        case 'on_onboarding_completed':
                                            onMsg(msg)
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )
                    /* istanbul ignore next */
                    default:
                        return notReachable(step)
                }
            }}
        </StepWizard>
    )
}
