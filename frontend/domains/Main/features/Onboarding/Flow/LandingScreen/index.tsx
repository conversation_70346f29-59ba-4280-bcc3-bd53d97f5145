import { useEffect } from 'react'
import { useIntl } from 'react-intl'
import { PlayInstallReferrer } from 'react-native-play-install-referrer'

import * as Linking from 'expo-linking'

import { StepWizard } from '@zeal/uikit/StepWizard'

import { noop, notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

import { AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { REFERRAL_CUSTOM_STORE_PAGE_ID } from '@zeal/domains/Card/domains/Reward/constants'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import {
    clearAllReminders,
    setupReminder,
} from '@zeal/domains/Notification/domains/Reminder'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { CreateAccount } from './CreateAccount'
import { Landing } from './Landing'
import { Login } from './Login'

type Props = {
    skyApy: number

    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    installationId: string
    customCurrencies: CustomCurrencyMap
    networkMap: NetworkMap
    currencyHiddenMap: CurrencyHiddenMap
    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    referralConfig: ReferralConfig
    onMsg: (msg: Msg) => void
}

type Msg = Extract<MsgOf<typeof Login>, { type: 'on_onboarding_completed' }>

type State =
    | { type: 'login_or_create_account' }
    | { type: 'login'; currentArtwork: number }
    | {
          type: 'create_account'
      }

const fetchAndroidInstallReferrer = (): Promise<string | null> =>
    new Promise<string | null>((resolve, reject) => {
        PlayInstallReferrer.getInstallReferrerInfo((info, error) => {
            if (error) {
                return reject(error)
            }
            resolve(info?.installReferrer || null)
        })
    })

const isReferralCodeNeeded = async (): Promise<boolean> => {
    switch (ZealPlatform.OS) {
        case 'ios':
            /*
              IOS has no way to get install referrer info. getInitialURL() will return the custom store page URL if
              this is the first launch and user launched from "open" button in app store.
             */
            const initialUrl = await Linking.parseInitialURLAsync()
            return initialUrl.hostname === REFERRAL_CUSTOM_STORE_PAGE_ID // e.g. zeal://referral
        case 'android':
            const installReferrer = await fetchAndroidInstallReferrer()

            if (!installReferrer) {
                return false
            }

            const params = new URLSearchParams(installReferrer)
            return params.get('page') === REFERRAL_CUSTOM_STORE_PAGE_ID // e.g. &referrer=page%3Dreferral appended to custom store Google Play URL
        case 'web':
            return false
        default:
            return notReachable(ZealPlatform)
    }
}

export const LandingScreen = ({
    skyApy,
    onMsg,
    keyStoreMap,
    accountsMap,
    installationId,
    networkRPCMap,
    networkMap,
    customCurrencies,
    currencyHiddenMap,
    cardConfig,
    defaultCurrencyConfig,
    sessionPassword,
    referralConfig,
}: Props) => {
    const { formatMessage } = useIntl()

    const [loadable] = useLoadableData(isReferralCodeNeeded, {
        type: 'loading',
        params: undefined,
    })

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
            case 'loaded':
                break
            case 'error':
                captureError(loadable.error)
                break
            default:
                return notReachable(loadable)
        }
    }, [loadable])

    return (
        <StepWizard<State> initialStep={{ type: 'login_or_create_account' }}>
            {({ step, forwardTo, backTo, moveTo }) => {
                switch (step.type) {
                    case 'login_or_create_account':
                        return (
                            <Landing
                                skyApy={skyApy}
                                slide={0}
                                installationId={installationId}
                                paused={false}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'on_login_clicked':
                                            postUserEvent({
                                                type: 'ExistingWalletFlowEnteredEvent',
                                                name: 'onboarding',
                                                installationId,
                                            })
                                            moveTo({
                                                type: 'login',
                                                currentArtwork:
                                                    msg.currentArtwork,
                                            })
                                            break
                                        case 'on_create_account_clicked':
                                            postUserEvent({
                                                type: 'CreateWalletClickedEvent',
                                                name: 'onboarding',
                                                installationId,
                                            })
                                            forwardTo({
                                                type: 'create_account',
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )

                    case 'login':
                        return (
                            <>
                                <Landing
                                    skyApy={skyApy}
                                    slide={step.currentArtwork}
                                    installationId={installationId}
                                    paused={true}
                                    onMsg={noop}
                                />
                                <Login
                                    referralCodeNeededLoadable={loadable}
                                    accountsMap={accountsMap}
                                    networkRPCMap={networkRPCMap}
                                    sessionPassword={sessionPassword}
                                    installationId={installationId}
                                    keyStoreMap={keyStoreMap}
                                    customCurrencies={customCurrencies}
                                    networkMap={networkMap}
                                    currencyHiddenMap={currencyHiddenMap}
                                    cardConfig={cardConfig}
                                    defaultCurrencyConfig={
                                        defaultCurrencyConfig
                                    }
                                    referralConfig={referralConfig}
                                    onMsg={(msg) => {
                                        switch (msg.type) {
                                            case 'close':
                                                moveTo({
                                                    type: 'login_or_create_account',
                                                })
                                                break
                                            case 'safe_wallet_clicked':
                                                moveTo({
                                                    type: 'create_account',
                                                })
                                                break
                                            case 'on_onboarding_completed':
                                                clearAllReminders().catch(
                                                    captureError
                                                )
                                                onMsg(msg)
                                                break
                                            /* istanbul ignore next */
                                            default:
                                                return notReachable(msg)
                                        }
                                    }}
                                />
                            </>
                        )
                    case 'create_account':
                        return (
                            <CreateAccount
                                referralCodeNeededLoadable={loadable}
                                referralConfig={referralConfig}
                                cardConfig={cardConfig}
                                installationId={installationId}
                                sessionPassword={sessionPassword}
                                networkRPCMap={networkRPCMap}
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                onMsg={async (msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            backTo({
                                                type: 'login_or_create_account',
                                            })
                                            break
                                        case 'on_onboarding_completed': {
                                            setupReminder({
                                                formatMessage,
                                                reminder: 'fund_wallet',
                                            }).catch(captureError)

                                            onMsg(msg)
                                            break
                                        }
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )
                    /* istanbul ignore next */
                    default:
                        return notReachable(step)
                }
            }}
        </StepWizard>
    )
}
