import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'
import { openExternalURL } from '@zeal/toolkit/Window'

import { Add as AddAccount } from '@zeal/domains/Account/features/Add'
import { AddFromHardwareWallet } from '@zeal/domains/Account/features/AddFromHardwareWallet'
import { AddFunds } from '@zeal/domains/Account/features/AddFunds'
import { AddTrackOnlyWallet } from '@zeal/domains/Account/features/AddTrackOnlyWallet'
import { AddWithCardDetection } from '@zeal/domains/Account/features/AddWithCardDetection'
import { CreateNewSafe4337WithStories } from '@zeal/domains/Account/features/CreateNewSafe4337WithStories'
import { RecoverSafeWithCardDetection } from '@zeal/domains/Account/features/RecoverSafeWithCardDetection'
import { BANK_TRANSFER_LOGIN_NETWORK } from '@zeal/domains/Currency/domains/BankTransfer/constants'
import { BankTransfer } from '@zeal/domains/Currency/domains/BankTransfer/features/BankTransfer'
import { Bridge } from '@zeal/domains/Currency/domains/Bridge/features/Bridge'
import { SwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { Buy } from '@zeal/domains/Currency/features/Buy'
import { Swap } from '@zeal/domains/Currency/features/Swap'
import { SetupRecoveryKit } from '@zeal/domains/KeyStore/features/SetupRecoveryKit'
import { OnboardedEntrypoint } from '@zeal/domains/Main'
import { DISCORD_URL } from '@zeal/domains/Main/constants'
import { KycProcess } from '@zeal/domains/Main/features/KycProcess'
import { SendMoney } from '@zeal/domains/Money/features/SendMoney'
import { NetworkMap } from '@zeal/domains/Network'
import { updateNetworkRPC } from '@zeal/domains/Network/helpers/updateNetworkRPC'
import { LockScreen } from '@zeal/domains/Password/features/LockScreen'
import { Send } from '@zeal/domains/RPCRequest/features/Send'
import { Storage } from '@zeal/domains/Storage'
import { addAccountsWithKeystores } from '@zeal/domains/Storage/helpers/addAccountsWithKeystores'
import { calculateStorageState } from '@zeal/domains/Storage/helpers/calculateStorageState'
import { changeBankTransferOwner } from '@zeal/domains/Storage/helpers/changeBankTransferOwner'
import { removeGasCurrencyPreset } from '@zeal/domains/Storage/helpers/removeGasCurrencyPreset'
import { resetCardConfig } from '@zeal/domains/Storage/helpers/resetCardConfig'
import { saveFeePreset } from '@zeal/domains/Storage/helpers/saveFeePreset'
import { saveGasCurrencyPreset } from '@zeal/domains/Storage/helpers/saveGasCurrencyPreset'
import { saveSessionPassword } from '@zeal/domains/Storage/helpers/saveSessionPassword'
import { toLocalStorage } from '@zeal/domains/Storage/helpers/toLocalStorage'
import { removeTransactionRequest } from '@zeal/domains/TransactionRequest/helpers/removeTransactionRequest'
import { TransactionActivitiesCache } from '@zeal/domains/Transactions'
import { getTransactionActivitiesCache } from '@zeal/domains/Transactions/helpers/getTransactionActivitiesCache'
import { keystoreToUserEventType } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    entryPoint: OnboardedEntrypoint

    storage: Storage | null
    sessionPassword: string | null
    installationId: string
    installationCampaign: string | null
    networkMap: NetworkMap

    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof AddFunds>,
          {
              type:
                  | 'add_wallet_clicked'
                  | 'on_bank_transfer_selected'
                  | 'track_wallet_clicked'
                  | 'import_keys_button_clicked'
                  | 'close'
                  | 'on_accounts_create_success_animation_finished'
                  | 'hardware_wallet_clicked'
                  | 'on_top_up_transaction_complete_close'
          }
      >
    | Extract<
          MsgOf<typeof LockScreen>,
          {
              type: 'lock_screen_close_click'
          }
      >
    | Extract<
          MsgOf<typeof Send>,
          {
              type:
                  | 'close'
                  | 'add_wallet_clicked'
                  | 'track_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'import_keys_button_clicked'
                  | 'internal_send_transaction_closed'
          }
      >
    | MsgOf<typeof SetupRecoveryKit>
    | Extract<
          MsgOf<typeof AddTrackOnlyWallet>,
          {
              type:
                  | 'close'
                  | 'on_accounts_create_success_animation_finished'
                  | 'safe_wallet_clicked'
          }
      >
    | Extract<
          MsgOf<typeof AddAccount>,
          {
              type: 'close' | 'on_accounts_create_success_animation_finished'
          }
      >
    | Extract<
          MsgOf<typeof AddWithCardDetection>,
          {
              type:
                  | 'close'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_account_create_with_card_detected_success_animation_finished'
          }
      >
    | Extract<
          MsgOf<typeof AddFromHardwareWallet>,
          {
              type: 'on_accounts_create_success_animation_finished'
          }
      >
    | Extract<
          MsgOf<typeof Swap>,
          {
              type:
                  | 'close'
                  | 'import_keys_button_clicked'
                  | 'on_all_transaction_success'
          }
      >
    | Extract<
          MsgOf<typeof Bridge>,
          {
              type: 'bridge_completed'
          }
      >
    | Extract<
          MsgOf<typeof KycProcess>,
          {
              type: 'on_do_bank_transfer_clicked'
          }
      >
    | Extract<
          MsgOf<typeof BankTransfer>,
          {
              type:
                  | 'on_on_ramp_transfer_success_close_click'
                  | 'on_import_latest_bank_transfer_owner_clicked'
                  | 'on_activate_existing_monerium_account_click'
                  | 'monerium_deposit_on_enable_card_clicked'
                  | 'on_monerium_deposit_success_go_to_wallet_clicked'
                  | 'on_monerium_order_status_changed'
                  | 'monerium_on_card_disconnected'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_monerium_sign_delay_relay_success_close_clicked'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_kyc_data_updated_close_clicked'
                  | 'on_gnosis_pay_kyc_submitted_animation_complete'
                  | 'on_gnosis_pay_onboarding_flow_closed'
                  | 'on_card_disconnected'
          }
      >
    | Extract<
          MsgOf<typeof Buy>,
          {
              type:
                  | 'on_buy_success'
                  | 'buy_closed'
                  | 'add_wallet_clicked'
                  | 'on_bank_transfer_selected'
                  | 'track_wallet_clicked'
                  | 'import_keys_button_clicked'
                  | 'close'
                  | 'on_accounts_create_success_animation_finished'
                  | 'hardware_wallet_clicked'
                  | 'on_top_up_transaction_complete_close'
                  | 'on_address_scanned_and_add_label'
                  | 'on_address_scanned'
          }
      >

export const StorageValidator = ({
    storage,
    sessionPassword,
    installationId,
    installationCampaign,
    networkMap,
    entryPoint,
    onMsg,
}: Props) => {
    const state = calculateStorageState({ storage, sessionPassword })
    switch (state.type) {
        case 'locked':
            return (
                <LockScreen
                    installationId={installationId}
                    variant="default"
                    encryptedPassword={state.storage.encryptedPassword}
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'lock_screen_close_click':
                                onMsg(msg)
                                break

                            case 'session_password_decrypted':
                                await saveSessionPassword(msg.sessionPassword)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'unlocked':
            return (
                <OnboardedEntrypointFork
                    key={state.storage.defaultCurrencyConfig.defaultCurrency.id}
                    entryPoint={entryPoint}
                    installationId={installationId}
                    installationCampaign={installationCampaign}
                    networkMap={networkMap}
                    onMsg={onMsg}
                    sessionPassword={state.sessionPassword}
                    storage={state.storage}
                />
            )

        case 'no_storage':
            throw new ImperativeError(
                `Impossible state. No storage for onboarded entry points`
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}

const OnboardedEntrypointFork = ({
    entryPoint,
    installationId,
    installationCampaign,
    networkMap,
    onMsg,
    sessionPassword,
    storage,
}: {
    entryPoint: OnboardedEntrypoint

    storage: Storage
    sessionPassword: string
    installationId: string
    installationCampaign: string | null
    networkMap: NetworkMap

    onMsg: (msg: Msg) => void
}) => {
    switch (entryPoint.type) {
        case 'kyc_process':
            return (
                <KycProcess
                    network={BANK_TRANSFER_LOGIN_NETWORK}
                    defaultCurrencyConfig={storage.defaultCurrencyConfig}
                    selectedAddress={storage.selectedAddress}
                    customCurrencies={storage.customCurrencies}
                    currencyHiddenMap={storage.currencyHiddenMap}
                    currencyPinMap={storage.currencyPinMap}
                    cardConfig={storage.cardConfig}
                    portfolioMap={storage.portfolios}
                    feePresetMap={storage.feePresetMap}
                    gasCurrencyPresetMap={storage.gasCurrencyPresetMap}
                    installationId={installationId}
                    networkRPCMap={storage.networkRPCMap}
                    bankTransferInfo={storage.bankTransferInfo}
                    keyStoreMap={storage.keystoreMap}
                    sessionPassword={sessionPassword}
                    accountsMap={storage.accounts}
                    networkMap={networkMap}
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_do_bank_transfer_clicked':
                                onMsg(msg)
                                break
                            case 'kyc_applicant_created':
                                await toLocalStorage({
                                    ...storage,
                                    bankTransferInfo: {
                                        ...msg.bankTransferInfo,
                                        sumSubAccessToken:
                                            msg.sumSubAccessToken,
                                    },
                                })
                                break
                            case 'on_4337_auto_gas_token_selection_clicked':
                                await toLocalStorage(
                                    removeGasCurrencyPreset({
                                        networkHexId: msg.network.hexChainId,
                                        storage: storage,
                                    })
                                )
                                break
                            case 'on_4337_gas_currency_selected':
                                await toLocalStorage(
                                    saveGasCurrencyPreset({
                                        currencyId: msg.selectedGasCurrency.id,
                                        networkHexId:
                                            msg.selectedGasCurrency
                                                .networkHexChainId,
                                        storage: storage,
                                    })
                                )
                                break

                            case 'on_import_latest_bank_transfer_owner_clicked':
                                await toLocalStorage({
                                    ...storage,
                                    bankTransferInfo: {
                                        type: 'not_started',
                                    },
                                })
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'bank_transfer':
            return (
                <BankTransfer
                    installationCampaign={installationCampaign}
                    variant={entryPoint.variant}
                    defaultCurrencyConfig={storage.defaultCurrencyConfig}
                    selectedAddress={
                        storage.selectedAddress as Web3.address.Address
                    }
                    gasCurrencyPresetMap={storage.gasCurrencyPresetMap}
                    currencyHiddenMap={storage.currencyHiddenMap}
                    currencyPinMap={storage.currencyPinMap}
                    accountsMap={storage.accounts}
                    bankTransferInfo={storage.bankTransferInfo}
                    customCurrencies={storage.customCurrencies}
                    cardConfig={storage.cardConfig}
                    installationId={installationId}
                    keystoreMap={storage.keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={storage.networkRPCMap}
                    feePresetMap={storage.feePresetMap}
                    portfolioMap={storage.portfolios}
                    sessionPassword={sessionPassword}
                    counterparties={storage.counterparties}
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'on_activate_existing_monerium_account_click':
                            case 'monerium_deposit_on_enable_card_clicked':
                            case 'on_monerium_order_status_changed':
                            case 'monerium_on_card_disconnected':
                            case 'on_create_smart_wallet_clicked':
                            case 'on_monerium_sign_delay_relay_success_close_clicked':
                            case 'on_card_import_on_import_keys_clicked':
                            case 'on_kyc_data_updated_close_clicked':
                            case 'on_gnosis_pay_kyc_submitted_animation_complete':
                            case 'on_gnosis_pay_onboarding_flow_closed':
                                onMsg(msg)
                                break

                            case 'on_card_imported_success_animation_complete':
                                await toLocalStorage({
                                    ...storage,
                                    cardConfig: {
                                        type: 'card_readonly_signer_address_is_selected',
                                        readonlySignerAddress:
                                            msg.cardReadonlySigner.address,
                                        lastDismissedOnboardingBannerState:
                                            null,
                                        userId: msg.userId,
                                    },
                                })
                                break
                            case 'on_gnosis_pay_account_created':
                                await toLocalStorage({
                                    ...storage,
                                    cardConfig: {
                                        type: 'card_readonly_signer_address_is_selected',
                                        readonlySignerAddress:
                                            msg.cardReadonlySigner.address,
                                        lastDismissedOnboardingBannerState:
                                            null,
                                        userId: msg.userId,
                                    },
                                })
                                break

                            case 'on_onboarded_card_imported_success_animation_complete':
                                await toLocalStorage({
                                    ...storage,
                                    cardConfig: {
                                        lastSeenSafeAddress:
                                            msg.lastSeenSafeAddress,
                                        type: 'card_readonly_signer_address_is_selected_fully_onboarded',
                                        readonlySignerAddress:
                                            msg.cardReadonlySigner.address,
                                        selectedCardId: msg.selectedCardId,
                                        cardTransactionsCache: null,
                                        lastRechargeTransactionHash: null,
                                        currency: msg.currency,
                                        dissmissedAddToWalletBanner: false,
                                        lastDismissedKycBannerState: null,
                                        country: msg.country,
                                        cashback: {
                                            type: 'not_eligible_for_cashback',
                                        },
                                        rewards: msg.reward,
                                        isCreatedViaZeal: msg.isCreatedViaZeal,
                                        userId: msg.userId,
                                    },
                                })
                                break

                            case 'on_card_disconnected':
                                await toLocalStorage(
                                    resetCardConfig({
                                        storage,
                                        cardReadonlySigner:
                                            msg.cardReadonlySigner,
                                    })
                                )
                                onMsg(msg)
                                break

                            case 'on_monerium_deposit_success_go_to_wallet_clicked':
                                await toLocalStorage({
                                    ...storage,
                                    selectedAddress: msg.account.address,
                                })
                                onMsg(msg)
                                break

                            case 'kyc_applicant_created':
                                await toLocalStorage({
                                    ...storage,
                                    bankTransferInfo: {
                                        ...msg.bankTransferInfo,
                                        sumSubAccessToken:
                                            msg.sumSubAccessToken,
                                    },
                                })
                                break
                            case 'on_withdrawal_monitor_fiat_transaction_start':
                                const { submittedOffRampTransactions } = storage

                                await toLocalStorage({
                                    ...storage,
                                    submittedOffRampTransactions: [
                                        ...submittedOffRampTransactions,
                                        msg.submittedTransaction,
                                    ],
                                })

                                break
                            case 'on_withdrawal_monitor_fiat_transaction_success':
                                const removed =
                                    storage.submittedOffRampTransactions.filter(
                                        (submitted) =>
                                            submitted.transactionHash !==
                                            msg.event.transactionHash
                                    )

                                await toLocalStorage({
                                    ...storage,
                                    submittedOffRampTransactions: removed,
                                })
                                break
                            case 'close':
                            case 'import_keys_button_clicked':
                            case 'on_on_ramp_transfer_success_close_click':
                                onMsg(msg)
                                break

                            case 'on_account_create_request':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        msg.accountsWithKeystores[0].account
                                            .address
                                    )
                                )
                                break

                            case 'on_user_login_to_unblock_success':
                                await toLocalStorage({
                                    ...storage,
                                    bankTransferInfo: msg.bankTransferInfo,
                                })
                                break

                            case 'on_predefined_fee_preset_selected':
                                await toLocalStorage(
                                    saveFeePreset({
                                        storage: storage,
                                        feePreset: msg.preset,
                                        networkHexId: msg.networkHexId,
                                    })
                                )
                                break

                            case 'bank_transfer_owner_successfully_changed':
                                const newBankTransferInfo =
                                    changeBankTransferOwner({
                                        newOwnerAddress: msg.newOwner.address,
                                        newOwnerSignature:
                                            msg.newOwnerSignature,
                                        oldBankTransferInfo:
                                            storage.bankTransferInfo,
                                    })

                                await toLocalStorage({
                                    ...storage,
                                    bankTransferInfo: newBankTransferInfo,
                                })
                                break

                            case 'on_import_latest_bank_transfer_owner_clicked':
                                await toLocalStorage({
                                    ...storage,
                                    bankTransferInfo: {
                                        type: 'not_started',
                                    },
                                })
                                onMsg(msg)
                                break

                            case 'on_4337_gas_currency_selected':
                                await toLocalStorage(
                                    saveGasCurrencyPreset({
                                        currencyId: msg.selectedGasCurrency.id,
                                        networkHexId:
                                            msg.selectedGasCurrency
                                                .networkHexChainId,
                                        storage: storage,
                                    })
                                )
                                break

                            case 'on_4337_auto_gas_token_selection_clicked':
                                await toLocalStorage(
                                    removeGasCurrencyPreset({
                                        networkHexId: msg.network.hexChainId,
                                        storage: storage,
                                    })
                                )
                                break

                            case 'on_contact_support_clicked':
                                openExternalURL(DISCORD_URL)
                                break

                            case 'on_save_counterparty_form_submitted':
                                const currentCounterparty =
                                    storage.counterparties.find(
                                        (counterparty) =>
                                            counterparty.iban ===
                                            msg.counterparty.iban
                                    )

                                await toLocalStorage({
                                    ...storage,
                                    counterparties: currentCounterparty
                                        ? storage.counterparties.map(
                                              (counterparty) => {
                                                  if (
                                                      counterparty.iban ===
                                                      msg.counterparty.iban
                                                  ) {
                                                      return msg.counterparty
                                                  }
                                                  return counterparty
                                              }
                                          )
                                        : [
                                              ...storage.counterparties,
                                              msg.counterparty,
                                          ],
                                })

                                break
                            case 'on_delete_counterparty_submitted':
                            case 'on_delete_last_counterparty_submitted':
                                await toLocalStorage({
                                    ...storage,
                                    counterparties:
                                        storage.counterparties.filter(
                                            (counterparty) =>
                                                counterparty.iban !==
                                                msg.counterparty.iban
                                        ),
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'bridge':
            return (
                <Bridge
                    defaultCurrencyConfig={storage.defaultCurrencyConfig}
                    gasCurrencyPresetMap={storage.gasCurrencyPresetMap}
                    customCurrencies={storage.customCurrencies}
                    feePresetMap={storage.feePresetMap}
                    currencyHiddenMap={storage.currencyHiddenMap}
                    currencyPinMap={storage.currencyPinMap}
                    networkMap={networkMap}
                    networkRPCMap={storage.networkRPCMap}
                    swapSlippagePercent={storage.swapSlippagePercent}
                    accountMap={storage.accounts}
                    keystoreMap={storage.keystoreMap}
                    sessionPassword={sessionPassword}
                    installationId={installationId}
                    fromCurrencyId={entryPoint.fromCurrencyId}
                    account={storage.accounts[entryPoint.fromAddress]}
                    cardConfig={storage.cardConfig}
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'bridge_completed': {
                                const fromAddress =
                                    msg.bridgeSubmitted.fromAddress

                                const currentSubmitedBridges =
                                    storage.submitedBridges[fromAddress] || []

                                await toLocalStorage({
                                    ...storage,
                                    submitedBridges: {
                                        ...storage.submitedBridges,
                                        [fromAddress]:
                                            currentSubmitedBridges.filter(
                                                (bridge) =>
                                                    bridge.submittedBridge
                                                        .sourceTransactionHash !==
                                                    msg.bridgeSubmitted
                                                        .sourceTransactionHash
                                            ),
                                    },
                                })
                                onMsg(msg)
                                break
                            }

                            case 'source_transaction_submitted': {
                                const fromAddress = msg.request.fromAddress

                                const currentSubmitedBridges =
                                    storage.submitedBridges[fromAddress] || []

                                await toLocalStorage({
                                    ...storage,
                                    submitedBridges: {
                                        ...storage.submitedBridges,
                                        [fromAddress]: [
                                            {
                                                submittedBridge: msg.request,
                                                dismissed: false,
                                            },
                                            ...currentSubmitedBridges,
                                        ],
                                    },
                                })
                                break
                            }

                            case 'on_set_slippage_percent':
                                await toLocalStorage({
                                    ...storage,
                                    swapSlippagePercent: msg.slippagePercent,
                                })
                                break

                            case 'on_predefined_fee_preset_selected':
                                await toLocalStorage(
                                    saveFeePreset({
                                        storage: storage,
                                        feePreset: msg.preset,
                                        networkHexId: msg.networkHexId,
                                    })
                                )
                                break

                            case 'on_rpc_change_confirmed':
                                await toLocalStorage({
                                    ...storage,
                                    networkRPCMap: {
                                        ...storage.networkRPCMap,
                                        [msg.network.hexChainId]:
                                            updateNetworkRPC({
                                                network: msg.network,
                                                initialRPCUrl:
                                                    msg.initialRPCUrl,
                                                networkRPCMap:
                                                    storage.networkRPCMap,
                                                rpcUrl: msg.rpcUrl,
                                            }),
                                    },
                                })
                                break

                            case 'on_select_rpc_click':
                                await toLocalStorage({
                                    ...storage,
                                    networkRPCMap: {
                                        ...storage.networkRPCMap,
                                        [msg.network.hexChainId]:
                                            msg.networkRPC,
                                    },
                                })
                                break

                            case 'import_keys_button_clicked':
                            case 'close':
                                onMsg(msg)
                                break

                            case 'on_4337_auto_gas_token_selection_clicked':
                                await toLocalStorage(
                                    removeGasCurrencyPreset({
                                        networkHexId: msg.network.hexChainId,
                                        storage: storage,
                                    })
                                )
                                break

                            case 'on_4337_gas_currency_selected':
                                await toLocalStorage(
                                    saveGasCurrencyPreset({
                                        currencyId: msg.selectedGasCurrency.id,
                                        networkHexId:
                                            msg.selectedGasCurrency
                                                .networkHexChainId,
                                        storage: storage,
                                    })
                                )
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'add_account':
            return (
                <AddWithCardDetection
                    defaultCurrencyConfig={storage.defaultCurrencyConfig}
                    installationId={installationId}
                    currencyHiddenMap={storage.currencyHiddenMap}
                    cardConfig={storage.cardConfig}
                    networkMap={networkMap}
                    networkRPCMap={storage.networkRPCMap}
                    customCurrencies={storage.customCurrencies}
                    accountsMap={storage.accounts}
                    keystoreMap={storage.keystoreMap}
                    sessionPassword={sessionPassword}
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_accounts_create_success_animation_finished':
                                onMsg(msg)
                                break

                            case 'on_account_create_request':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        msg.accountsWithKeystores[0].account
                                            .address
                                    )
                                )
                                break

                            case 'on_account_create_with_card_detected_success_animation_finished':
                                await toLocalStorage({
                                    ...storage,
                                    cardConfig: msg.cardConfig,
                                })
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'create_contact':
            return (
                <AddTrackOnlyWallet
                    accountMap={storage.accounts}
                    installationId={installationId}
                    networkRPCMap={storage.networkRPCMap}
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_accounts_create_success_animation_finished':
                                onMsg(msg)
                                break

                            case 'on_account_create_request':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        msg.accountsWithKeystores[0].account
                                            .address
                                    )
                                )
                                break

                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'send_money': {
            return (
                <SendMoney
                    currencyId={entryPoint.tokenCurrencyId}
                    fromAddress={entryPoint.fromAddress}
                    bankTransferInfo={storage.bankTransferInfo}
                    counterparties={storage.counterparties}
                    defaultCurrencyConfig={storage.defaultCurrencyConfig}
                    gasCurrencyPresetMap={storage.gasCurrencyPresetMap}
                    cardConfig={storage.cardConfig}
                    currencyHiddenMap={storage.currencyHiddenMap}
                    currencyPinMap={storage.currencyPinMap}
                    customCurrencies={storage.customCurrencies}
                    accountsMap={storage.accounts}
                    keyStoreMap={storage.keystoreMap}
                    portfolioMap={storage.portfolios}
                    sessionPassword={sessionPassword}
                    installationId={installationId}
                    networkMap={networkMap}
                    networkRPCMap={storage.networkRPCMap}
                    feePresetMap={storage.feePresetMap}
                    installationCampaign={installationCampaign}
                    isEthereumNetworkFeeWarningSeen={
                        storage.isEthereumNetworkFeeWarningSeen
                    }
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                // We don't change storage for safe transaction completeion because we don't store transaction requests for safe transactions
                                break

                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_request_replaced':
                            case 'cancel_submitted': {
                                const from =
                                    msg.transactionRequest.account.address

                                const removed = storage.transactionRequests[
                                    from
                                ]
                                    ? removeTransactionRequest(
                                          storage.transactionRequests[from],
                                          msg.transactionRequest
                                      )
                                    : []

                                await toLocalStorage({
                                    ...storage,
                                    transactionRequests: {
                                        ...storage.transactionRequests,
                                        [from]: removed,
                                    },
                                })
                                break
                            }

                            case 'transaction_submited':
                                const fromAddress =
                                    msg.transactionRequest.account.address

                                const existingRequests = storage
                                    .transactionRequests[fromAddress]
                                    ? removeTransactionRequest(
                                          storage.transactionRequests[
                                              fromAddress
                                          ],
                                          msg.transactionRequest
                                      )
                                    : []

                                await toLocalStorage({
                                    ...storage,
                                    transactionRequests: {
                                        ...storage.transactionRequests,
                                        [fromAddress]: [
                                            msg.transactionRequest,
                                            ...existingRequests,
                                        ],
                                    },
                                })
                                break

                            case 'import_keys_button_clicked':
                            case 'close':
                            case 'on_accounts_create_success_animation_finished':
                            case 'track_wallet_clicked':
                            case 'add_wallet_clicked':
                            case 'hardware_wallet_clicked':
                            case 'monerium_deposit_on_enable_card_clicked':
                            case 'on_import_latest_bank_transfer_owner_clicked':
                            case 'on_on_ramp_transfer_success_close_click':
                            case 'on_activate_existing_monerium_account_click':
                            case 'on_monerium_order_status_changed':
                            case 'monerium_on_card_disconnected':
                            case 'on_create_smart_wallet_clicked':
                            case 'on_monerium_sign_delay_relay_success_close_clicked':
                            case 'internal_send_transaction_closed':
                            case 'on_card_import_on_import_keys_clicked':
                            case 'on_kyc_data_updated_close_clicked':
                            case 'on_gnosis_pay_kyc_submitted_animation_complete':
                            case 'on_gnosis_pay_onboarding_flow_closed':
                                onMsg(msg)
                                break
                            case 'on_ethereum_network_fee_warning_understand_clicked':
                                await toLocalStorage({
                                    ...storage,
                                    isEthereumNetworkFeeWarningSeen: true,
                                })
                                break
                            case 'on_add_label_to_track_only_account_during_send':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        storage.selectedAddress
                                    )
                                )
                                break
                            case 'on_address_scanned_and_add_label':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        storage.selectedAddress
                                    )
                                )
                                onMsg(msg)
                                break

                            case 'on_card_disconnected':
                                await toLocalStorage(
                                    resetCardConfig({
                                        storage,
                                        cardReadonlySigner:
                                            msg.cardReadonlySigner,
                                    })
                                )
                                onMsg(msg)
                                break

                            case 'on_account_create_request':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        msg.accountsWithKeystores[0].account
                                            .address
                                    )
                                )
                                break

                            case 'on_predefined_fee_preset_selected':
                                await toLocalStorage(
                                    saveFeePreset({
                                        storage: storage,
                                        feePreset: msg.preset,
                                        networkHexId: msg.networkHexId,
                                    })
                                )
                                break

                            case 'on_4337_auto_gas_token_selection_clicked':
                                await toLocalStorage(
                                    removeGasCurrencyPreset({
                                        networkHexId: msg.network.hexChainId,
                                        storage: storage,
                                    })
                                )
                                break

                            case 'on_4337_gas_currency_selected':
                                await toLocalStorage(
                                    saveGasCurrencyPreset({
                                        storage: storage,
                                        currencyId: msg.selectedGasCurrency.id,
                                        networkHexId:
                                            msg.selectedGasCurrency
                                                .networkHexChainId,
                                    })
                                )
                                break

                            case 'on_user_login_to_unblock_success':
                                await toLocalStorage({
                                    ...storage,
                                    bankTransferInfo: msg.bankTransferInfo,
                                })
                                break
                            case 'kyc_applicant_created':
                                await toLocalStorage({
                                    ...storage,
                                    bankTransferInfo: {
                                        ...msg.bankTransferInfo,
                                        sumSubAccessToken:
                                            msg.sumSubAccessToken,
                                    },
                                })
                                break
                            case 'bank_transfer_owner_successfully_changed':
                                const newBankTransferInfo =
                                    changeBankTransferOwner({
                                        newOwnerAddress: msg.newOwner.address,
                                        newOwnerSignature:
                                            msg.newOwnerSignature,
                                        oldBankTransferInfo:
                                            storage.bankTransferInfo,
                                    })

                                await toLocalStorage({
                                    ...storage,
                                    bankTransferInfo: newBankTransferInfo,
                                })
                                break
                            case 'on_withdrawal_monitor_fiat_transaction_start':
                                const { submittedOffRampTransactions } = storage

                                await toLocalStorage({
                                    ...storage,
                                    submittedOffRampTransactions: [
                                        ...submittedOffRampTransactions,
                                        msg.submittedTransaction,
                                    ],
                                })

                                break
                            case 'on_withdrawal_monitor_fiat_transaction_success':
                                const removed =
                                    storage.submittedOffRampTransactions.filter(
                                        (submitted) =>
                                            submitted.transactionHash !==
                                            msg.event.transactionHash
                                    )

                                await toLocalStorage({
                                    ...storage,
                                    submittedOffRampTransactions: removed,
                                })
                                break
                            case 'on_contact_support_clicked':
                                openExternalURL(DISCORD_URL)
                                break

                            case 'on_monerium_deposit_success_go_to_wallet_clicked':
                                await toLocalStorage({
                                    ...storage,
                                    selectedAddress: msg.account.address,
                                })
                                onMsg(msg)
                                break
                            case 'on_save_counterparty_form_submitted':
                                const currentCounterparty =
                                    storage.counterparties.find(
                                        (counterparty) =>
                                            counterparty.iban ===
                                            msg.counterparty.iban
                                    )

                                await toLocalStorage({
                                    ...storage,
                                    counterparties: currentCounterparty
                                        ? storage.counterparties.map(
                                              (counterparty) => {
                                                  if (
                                                      counterparty.iban ===
                                                      msg.counterparty.iban
                                                  ) {
                                                      return msg.counterparty
                                                  }
                                                  return counterparty
                                              }
                                          )
                                        : [
                                              ...storage.counterparties,
                                              msg.counterparty,
                                          ],
                                })
                                break

                            case 'on_delete_counterparty_submitted':
                            case 'on_delete_last_counterparty_submitted':
                                await toLocalStorage({
                                    ...storage,
                                    counterparties:
                                        storage.counterparties.filter(
                                            (counterparty) =>
                                                counterparty.iban !==
                                                msg.counterparty.iban
                                        ),
                                })
                                break
                            case 'internal_send_transaction_submitted': {
                                const currentTransactionActivitiesCache: TransactionActivitiesCache =
                                    getTransactionActivitiesCache({
                                        address: msg.address,
                                        transactionActivitiesCacheMap:
                                            storage.transactionActivitiesCacheMap,
                                    })

                                await toLocalStorage({
                                    ...storage,
                                    transactionActivitiesCacheMap: {
                                        ...storage.transactionActivitiesCacheMap,
                                        [msg.address]: {
                                            ...currentTransactionActivitiesCache,
                                            pendingSendTransactionActivities: [
                                                msg.pendingSendTransactionActivity,
                                                ...currentTransactionActivitiesCache.pendingSendTransactionActivities,
                                            ],
                                        },
                                    },
                                })
                                onMsg({ type: 'close' })
                                break
                            }

                            case 'on_card_imported_success_animation_complete':
                                await toLocalStorage({
                                    ...storage,
                                    cardConfig: {
                                        type: 'card_readonly_signer_address_is_selected',
                                        readonlySignerAddress:
                                            msg.cardReadonlySigner.address,
                                        lastDismissedOnboardingBannerState:
                                            null,
                                        userId: msg.userId,
                                    },
                                })
                                break
                            case 'on_gnosis_pay_account_created':
                                await toLocalStorage({
                                    ...storage,
                                    cardConfig: {
                                        type: 'card_readonly_signer_address_is_selected',
                                        readonlySignerAddress:
                                            msg.cardReadonlySigner.address,
                                        lastDismissedOnboardingBannerState:
                                            null,
                                        userId: msg.userId,
                                    },
                                })
                                break
                            case 'on_onboarded_card_imported_success_animation_complete':
                                await toLocalStorage({
                                    ...storage,
                                    cardConfig: {
                                        lastSeenSafeAddress:
                                            msg.lastSeenSafeAddress,
                                        type: 'card_readonly_signer_address_is_selected_fully_onboarded',
                                        readonlySignerAddress:
                                            msg.cardReadonlySigner.address,
                                        selectedCardId: msg.selectedCardId,
                                        cardTransactionsCache: null,
                                        lastRechargeTransactionHash: null,
                                        currency: msg.currency,
                                        dissmissedAddToWalletBanner: false,
                                        lastDismissedKycBannerState: null,
                                        country: msg.country,
                                        cashback: {
                                            type: 'not_eligible_for_cashback', // TODO :: @Nicvaniek see if we can load this on import
                                        },
                                        rewards: msg.reward,
                                        isCreatedViaZeal: msg.isCreatedViaZeal,
                                        userId: msg.userId,
                                    },
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        }
        case 'send_nft':
        case 'send_erc20_token':
            return (
                <Send
                    presetMap={storage.feePresetMap}
                    actionSource={{
                        type: 'internal',
                        transactionEventSource: 'send',
                    }}
                    defaultCurrencyConfig={storage.defaultCurrencyConfig}
                    gasCurrencyPresetMap={storage.gasCurrencyPresetMap}
                    cardConfig={storage.cardConfig}
                    currencyHiddenMap={storage.currencyHiddenMap}
                    currencyPinMap={storage.currencyPinMap}
                    entrypoint={entryPoint}
                    customCurrencies={storage.customCurrencies}
                    accountsMap={storage.accounts}
                    keyStoreMap={storage.keystoreMap}
                    portfolioMap={storage.portfolios}
                    sessionPassword={sessionPassword}
                    installationId={installationId}
                    networkMap={networkMap}
                    networkRPCMap={storage.networkRPCMap}
                    feePresetMap={storage.feePresetMap}
                    isEthereumNetworkFeeWarningSeen={
                        storage.isEthereumNetworkFeeWarningSeen
                    }
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                // We don't change storage for safe transaction completeion because we don't store transaction requests for safe transactions
                                break

                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_request_replaced':
                            case 'cancel_submitted': {
                                const from =
                                    msg.transactionRequest.account.address

                                const removed = storage.transactionRequests[
                                    from
                                ]
                                    ? removeTransactionRequest(
                                          storage.transactionRequests[from],
                                          msg.transactionRequest
                                      )
                                    : []

                                await toLocalStorage({
                                    ...storage,
                                    transactionRequests: {
                                        ...storage.transactionRequests,
                                        [from]: removed,
                                    },
                                })
                                break
                            }

                            case 'transaction_submited':
                                const fromAddress =
                                    msg.transactionRequest.account.address

                                const existingRequests = storage
                                    .transactionRequests[fromAddress]
                                    ? removeTransactionRequest(
                                          storage.transactionRequests[
                                              fromAddress
                                          ],
                                          msg.transactionRequest
                                      )
                                    : []

                                await toLocalStorage({
                                    ...storage,
                                    transactionRequests: {
                                        ...storage.transactionRequests,
                                        [fromAddress]: [
                                            msg.transactionRequest,
                                            ...existingRequests,
                                        ],
                                    },
                                })
                                break

                            case 'import_keys_button_clicked':
                            case 'close':
                            case 'internal_send_transaction_closed':
                            case 'on_accounts_create_success_animation_finished':
                            case 'track_wallet_clicked':
                            case 'add_wallet_clicked':
                            case 'hardware_wallet_clicked':
                                onMsg(msg)
                                break
                            case 'on_completed_safe_transaction_close_click':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'on_completed_transaction_close_click':
                                onMsg({ type: 'close' })
                                break
                            case 'internal_send_transaction_submitted': {
                                const currentTransactionActivitiesCache: TransactionActivitiesCache =
                                    getTransactionActivitiesCache({
                                        address: msg.address,
                                        transactionActivitiesCacheMap:
                                            storage.transactionActivitiesCacheMap,
                                    })

                                await toLocalStorage({
                                    ...storage,
                                    transactionActivitiesCacheMap: {
                                        ...storage.transactionActivitiesCacheMap,
                                        [msg.address]: {
                                            ...currentTransactionActivitiesCache,
                                            pendingSendTransactionActivities: [
                                                msg.pendingSendTransactionActivity,
                                                ...currentTransactionActivitiesCache.pendingSendTransactionActivities,
                                            ],
                                        },
                                    },
                                })
                                onMsg({ type: 'close' })
                                break
                            }

                            case 'on_ethereum_network_fee_warning_understand_clicked':
                                await toLocalStorage({
                                    ...storage,
                                    isEthereumNetworkFeeWarningSeen: true,
                                })
                                break
                            case 'on_address_scanned_and_add_label':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        storage.selectedAddress
                                    )
                                )
                                onMsg(msg)
                                break
                            case 'on_add_label_to_track_only_account_during_send':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        storage.selectedAddress
                                    )
                                )
                                break
                            case 'on_account_create_request':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        msg.accountsWithKeystores[0].account
                                            .address
                                    )
                                )
                                break

                            case 'on_predefined_fee_preset_selected':
                                await toLocalStorage(
                                    saveFeePreset({
                                        storage: storage,
                                        feePreset: msg.preset,
                                        networkHexId: msg.networkHexId,
                                    })
                                )
                                break

                            case 'on_4337_auto_gas_token_selection_clicked':
                                await toLocalStorage(
                                    removeGasCurrencyPreset({
                                        networkHexId: msg.network.hexChainId,
                                        storage: storage,
                                    })
                                )
                                break

                            case 'on_4337_gas_currency_selected':
                                await toLocalStorage(
                                    saveGasCurrencyPreset({
                                        storage: storage,
                                        currencyId: msg.selectedGasCurrency.id,
                                        networkHexId:
                                            msg.selectedGasCurrency
                                                .networkHexChainId,
                                    })
                                )
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'setup_recovery_kit':
            return (
                <SetupRecoveryKit
                    installationId={installationId}
                    accountsMap={storage.accounts}
                    encryptedPassword={storage.encryptedPassword}
                    keystoreMap={storage.keystoreMap}
                    address={entryPoint.address}
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break

                            case 'on_secret_phrase_verified_success':
                            case 'on_google_drive_backup_success':
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        msg.accountsWithKeystores[0].account
                                            .address
                                    )
                                )
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'swap':
            return (
                <Swap
                    defaultCurrencyConfig={storage.defaultCurrencyConfig}
                    gasCurrencyPresetMap={storage.gasCurrencyPresetMap}
                    cardConfig={storage.cardConfig}
                    customCurrencies={storage.customCurrencies}
                    feePresetMap={storage.feePresetMap}
                    currencyHiddenMap={storage.currencyHiddenMap}
                    currencyPinMap={storage.currencyPinMap}
                    networkMap={networkMap}
                    networkRPCMap={storage.networkRPCMap}
                    swapSlippagePercent={storage.swapSlippagePercent}
                    accountsMap={storage.accounts}
                    entrypoint={entryPoint}
                    installationId={installationId}
                    keystoreMap={storage.keystoreMap}
                    sessionPassword={sessionPassword}
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                // We don't change storage for safe transaction completeion because we don't store transaction requests for safe transactions
                                break

                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_request_replaced':
                            case 'cancel_submitted': {
                                const from =
                                    msg.transactionRequest.account.address

                                const removed = storage.transactionRequests[
                                    from
                                ]
                                    ? removeTransactionRequest(
                                          storage.transactionRequests[from],
                                          msg.transactionRequest
                                      )
                                    : []

                                await toLocalStorage({
                                    ...storage,
                                    transactionRequests: {
                                        ...storage.transactionRequests,
                                        [from]: removed,
                                    },
                                })
                                break
                            }
                            case 'transaction_submited':
                                const fromAddress =
                                    msg.transactionRequest.account.address

                                const existingRequests = storage
                                    .transactionRequests[fromAddress]
                                    ? removeTransactionRequest(
                                          storage.transactionRequests[
                                              fromAddress
                                          ],
                                          msg.transactionRequest
                                      )
                                    : []

                                await toLocalStorage({
                                    ...storage,
                                    transactionRequests: {
                                        ...storage.transactionRequests,
                                        [fromAddress]: [
                                            msg.transactionRequest,
                                            ...existingRequests,
                                        ],
                                    },
                                })
                                break
                            case 'close':
                            case 'import_keys_button_clicked':
                            case 'on_all_transaction_success':
                                onMsg(msg)
                                break

                            case 'on_set_slippage_percent':
                                await toLocalStorage({
                                    ...storage,
                                    swapSlippagePercent: msg.slippagePercent,
                                })
                                break

                            case 'on_predefined_fee_preset_selected':
                                await toLocalStorage(
                                    saveFeePreset({
                                        storage: storage,
                                        feePreset: msg.preset,
                                        networkHexId: msg.networkHexId,
                                    })
                                )
                                break

                            case 'on_rpc_change_confirmed':
                                await toLocalStorage({
                                    ...storage,
                                    networkRPCMap: {
                                        ...storage.networkRPCMap,
                                        [msg.network.hexChainId]:
                                            updateNetworkRPC({
                                                network: msg.network,
                                                initialRPCUrl:
                                                    msg.initialRPCUrl,
                                                networkRPCMap:
                                                    storage.networkRPCMap,
                                                rpcUrl: msg.rpcUrl,
                                            }),
                                    },
                                })
                                break

                            case 'on_select_rpc_click':
                                await toLocalStorage({
                                    ...storage,
                                    networkRPCMap: {
                                        ...storage.networkRPCMap,
                                        [msg.network.hexChainId]:
                                            msg.networkRPC,
                                    },
                                })
                                break

                            case 'on_4337_auto_gas_token_selection_clicked':
                                await toLocalStorage(
                                    removeGasCurrencyPreset({
                                        networkHexId: msg.network.hexChainId,
                                        storage: storage,
                                    })
                                )
                                break

                            case 'on_4337_gas_currency_selected':
                                await toLocalStorage(
                                    saveGasCurrencyPreset({
                                        currencyId: msg.selectedGasCurrency.id,
                                        networkHexId:
                                            msg.selectedGasCurrency
                                                .networkHexChainId,
                                        storage: storage,
                                    })
                                )
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'add_from_hardware_wallet':
            return (
                <AddFromHardwareWallet
                    defaultCurrencyConfig={storage.defaultCurrencyConfig}
                    installationId={installationId}
                    currencyHiddenMap={storage.currencyHiddenMap}
                    cardConfig={storage.cardConfig}
                    networkMap={networkMap}
                    networkRPCMap={storage.networkRPCMap}
                    accounts={storage.accounts}
                    customCurrencies={storage.customCurrencies}
                    keystoreMap={storage.keystoreMap}
                    sessionPassword={sessionPassword}
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_account_create_request':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        msg.accountsWithKeystores[0].account
                                            .address
                                    )
                                )
                                break

                            case 'on_accounts_create_success_animation_finished':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'create_safe':
            return (
                <CreateNewSafe4337WithStories
                    installationId={installationId}
                    networkRPCMap={storage.networkRPCMap}
                    accountsMap={storage.accounts}
                    sessionPassword={sessionPassword}
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'on_account_create_request':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        msg.accountsWithKeystores[0].account
                                            .address
                                    )
                                )
                                break
                            case 'on_accounts_create_success_animation_finished':
                            case 'close':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        case 'recover_safe':
            return (
                <RecoverSafeWithCardDetection
                    cardConfig={storage.cardConfig}
                    installationId={installationId}
                    networkRPCMap={storage.networkRPCMap}
                    accountsMap={storage.accounts}
                    networkMap={networkMap}
                    defaultCurrencyConfig={storage.defaultCurrencyConfig}
                    sessionPassword={sessionPassword}
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'on_account_create_request':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )

                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        msg.accountsWithKeystores[0].account
                                            .address
                                    )
                                )
                                break
                            case 'on_account_create_with_card_detected_success_animation_finished':
                                await toLocalStorage({
                                    ...storage,
                                    cardConfig: msg.cardConfig,
                                })
                                onMsg(msg)
                                break
                            case 'on_accounts_create_success_animation_finished':
                            case 'close':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'buy':
            return (
                <Buy
                    accountsMap={storage.accounts}
                    installationId={installationId}
                    networkMap={networkMap}
                    keyStoreMap={storage.keystoreMap}
                    portfolioMap={storage.portfolios}
                    networkRPCMap={storage.networkRPCMap}
                    feePresetMap={storage.feePresetMap}
                    customCurrencies={storage.customCurrencies}
                    currencyHiddenMap={storage.currencyHiddenMap}
                    currencyPinMap={storage.currencyHiddenMap}
                    gasCurrencyPresetMap={storage.gasCurrencyPresetMap}
                    cardConfig={storage.cardConfig}
                    defaultCurrencyConfig={storage.defaultCurrencyConfig}
                    isEthereumNetworkFeeWarningSeen={
                        storage.isEthereumNetworkFeeWarningSeen
                    }
                    sessionPassword={sessionPassword}
                    fromAddress={entryPoint.fromAddress}
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                // We don't change storage for safe transaction completeion because we don't store transaction requests for safe transactions
                                break

                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_request_replaced':
                            case 'cancel_submitted': {
                                const from =
                                    msg.transactionRequest.account.address

                                const removed = storage.transactionRequests[
                                    from
                                ]
                                    ? removeTransactionRequest(
                                          storage.transactionRequests[from],
                                          msg.transactionRequest
                                      )
                                    : []

                                await toLocalStorage({
                                    ...storage,
                                    transactionRequests: {
                                        ...storage.transactionRequests,
                                        [from]: removed,
                                    },
                                })
                                break
                            }
                            case 'transaction_submited':
                                const fromAddress =
                                    msg.transactionRequest.account.address

                                const existingRequests = storage
                                    .transactionRequests[fromAddress]
                                    ? removeTransactionRequest(
                                          storage.transactionRequests[
                                              fromAddress
                                          ],
                                          msg.transactionRequest
                                      )
                                    : []

                                await toLocalStorage({
                                    ...storage,
                                    transactionRequests: {
                                        ...storage.transactionRequests,
                                        [fromAddress]: [
                                            msg.transactionRequest,
                                            ...existingRequests,
                                        ],
                                    },
                                })
                                break

                            case 'import_keys_button_clicked':
                                onMsg(msg)
                                break

                            case 'on_predefined_fee_preset_selected':
                                await toLocalStorage(
                                    saveFeePreset({
                                        storage: storage,
                                        feePreset: msg.preset,
                                        networkHexId: msg.networkHexId,
                                    })
                                )
                                break

                            case 'on_4337_auto_gas_token_selection_clicked':
                                await toLocalStorage(
                                    removeGasCurrencyPreset({
                                        networkHexId: msg.network.hexChainId,
                                        storage: storage,
                                    })
                                )
                                break

                            case 'on_4337_gas_currency_selected':
                                await toLocalStorage(
                                    saveGasCurrencyPreset({
                                        currencyId: msg.selectedGasCurrency.id,
                                        networkHexId:
                                            msg.selectedGasCurrency
                                                .networkHexChainId,
                                        storage: storage,
                                    })
                                )
                                break

                            case 'on_ethereum_network_fee_warning_understand_clicked':
                                await toLocalStorage({
                                    ...storage,
                                    isEthereumNetworkFeeWarningSeen: true,
                                })
                                break
                            case 'on_swaps_io_swap_request_created': {
                                const updatedMap: SwapsIOSwapRequestsMap = {
                                    ...storage.swapsIOSwapRequestsMap,
                                    [msg.request.sender]: [
                                        ...(storage.swapsIOSwapRequestsMap[
                                            msg.request.sender
                                        ] || []),
                                        msg.request,
                                    ],
                                }
                                await toLocalStorage({
                                    ...storage,
                                    swapsIOSwapRequestsMap: updatedMap,
                                })
                                break
                            }
                            case 'add_wallet_clicked':
                            case 'on_bank_transfer_selected':
                            case 'track_wallet_clicked':
                            case 'on_accounts_create_success_animation_finished':
                            case 'hardware_wallet_clicked':
                            case 'on_top_up_transaction_complete_close':
                            case 'on_buy_success':
                            case 'buy_closed':
                            case 'on_address_scanned':
                                onMsg(msg)
                                break
                            case 'on_address_scanned_and_add_label':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        storage.selectedAddress
                                    )
                                )
                                onMsg(msg)
                                break

                            case 'on_add_label_to_track_only_account_during_send':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        storage.selectedAddress
                                    )
                                )
                                break
                            case 'on_account_create_request':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        msg.accountsWithKeystores[0].account
                                            .address
                                    )
                                )
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'add_funds':
            return (
                <AddFunds
                    accountsMap={storage.accounts}
                    address={entryPoint.address}
                    installationId={installationId}
                    networkMap={networkMap}
                    keyStoreMap={storage.keystoreMap}
                    portfolioMap={storage.portfolios}
                    networkRPCMap={storage.networkRPCMap}
                    feePresetMap={storage.feePresetMap}
                    customCurrencies={storage.customCurrencies}
                    currencyHiddenMap={storage.currencyHiddenMap}
                    currencyPinMap={storage.currencyHiddenMap}
                    gasCurrencyPresetMap={storage.gasCurrencyPresetMap}
                    cardConfig={storage.cardConfig}
                    defaultCurrencyConfig={storage.defaultCurrencyConfig}
                    isEthereumNetworkFeeWarningSeen={
                        storage.isEthereumNetworkFeeWarningSeen
                    }
                    sessionPassword={sessionPassword}
                    onMsg={async (msg) => {
                        switch (msg.type) {
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                // We don't change storage for safe transaction completeion because we don't store transaction requests for safe transactions
                                break

                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_request_replaced':
                            case 'cancel_submitted': {
                                const from =
                                    msg.transactionRequest.account.address

                                const removed = storage.transactionRequests[
                                    from
                                ]
                                    ? removeTransactionRequest(
                                          storage.transactionRequests[from],
                                          msg.transactionRequest
                                      )
                                    : []

                                await toLocalStorage({
                                    ...storage,
                                    transactionRequests: {
                                        ...storage.transactionRequests,
                                        [from]: removed,
                                    },
                                })
                                break
                            }

                            case 'on_ethereum_network_fee_warning_understand_clicked':
                                await toLocalStorage({
                                    ...storage,
                                    isEthereumNetworkFeeWarningSeen: true,
                                })
                                break

                            case 'transaction_submited':
                                const fromAddress =
                                    msg.transactionRequest.account.address

                                const existingRequests = storage
                                    .transactionRequests[fromAddress]
                                    ? removeTransactionRequest(
                                          storage.transactionRequests[
                                              fromAddress
                                          ],
                                          msg.transactionRequest
                                      )
                                    : []

                                await toLocalStorage({
                                    ...storage,
                                    transactionRequests: {
                                        ...storage.transactionRequests,
                                        [fromAddress]: [
                                            msg.transactionRequest,
                                            ...existingRequests,
                                        ],
                                    },
                                })
                                break
                            case 'add_wallet_clicked':
                            case 'on_bank_transfer_selected':
                            case 'track_wallet_clicked':
                            case 'import_keys_button_clicked':
                            case 'close':
                            case 'on_accounts_create_success_animation_finished':
                            case 'hardware_wallet_clicked':
                            case 'on_top_up_transaction_complete_close':
                            case 'on_address_scanned':
                                onMsg(msg)
                                break
                            case 'on_address_scanned_and_add_label':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        storage.selectedAddress
                                    )
                                )
                                onMsg(msg)
                                break
                            case 'on_add_label_to_track_only_account_during_send':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        storage.selectedAddress
                                    )
                                )
                                break
                            case 'on_account_create_request':
                                msg.accountsWithKeystores.forEach(
                                    ({ keystore }) => {
                                        postUserEvent({
                                            type: 'WalletAddedEvent',
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keystore
                                                ),
                                            keystoreId: keystore.id,
                                            installationId,
                                        })
                                    }
                                )
                                await toLocalStorage(
                                    addAccountsWithKeystores(
                                        storage,
                                        msg.accountsWithKeystores,
                                        msg.accountsWithKeystores[0].account
                                            .address
                                    )
                                )
                                break

                            case 'on_predefined_fee_preset_selected':
                                await toLocalStorage(
                                    saveFeePreset({
                                        storage: storage,
                                        feePreset: msg.preset,
                                        networkHexId: msg.networkHexId,
                                    })
                                )
                                break

                            case 'on_4337_auto_gas_token_selection_clicked':
                                await toLocalStorage(
                                    removeGasCurrencyPreset({
                                        networkHexId: msg.network.hexChainId,
                                        storage: storage,
                                    })
                                )
                                break

                            case 'on_4337_gas_currency_selected':
                                await toLocalStorage(
                                    saveGasCurrencyPreset({
                                        storage: storage,
                                        currencyId: msg.selectedGasCurrency.id,
                                        networkHexId:
                                            msg.selectedGasCurrency
                                                .networkHexChainId,
                                    })
                                )
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(entryPoint)
    }
}
