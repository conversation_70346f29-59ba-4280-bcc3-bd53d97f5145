import { useLayoutEffect, useState } from 'react'

import { ApplicationContainer } from '@zeal/uikit/ApplicationContainer'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import { joinURL } from '@zeal/toolkit/URL/joinURL'
import * as Web3 from '@zeal/toolkit/Web3'
import { openExternalURL } from '@zeal/toolkit/Window'

import { CASHBACK_CURRENCY } from '@zeal/domains/Card/domains/Cashback/constants'
import { CurrencyId } from '@zeal/domains/Currency'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { EntryPoint, TabControllerTab } from '@zeal/domains/Main'
import { sendToActiveTabZWidget } from '@zeal/domains/Main/api/sendToActiveTabZWidget'
import { EXTENSION_URL } from '@zeal/domains/Main/constants'
import { Main } from '@zeal/domains/Main/features/Extension'
import { ZWidget } from '@zeal/domains/Main/features/ZWidget'
import { fireCelebrateAnimation } from '@zeal/domains/Main/helpers/fireCelebrateAnimation'
import {
    openAddAccountPageTab,
    openAddFromHardwareWallet,
    openCreateContactPage,
    openCreateSafePage,
    openOnboardingPageTab,
    openRecoverSafePage,
} from '@zeal/domains/Main/helpers/openEntrypoint'
import { parseEntrypointFromQueryString } from '@zeal/domains/Main/parsers/parseEntrypointFromQueryString'
import {
    PortfolioNFT,
    PortfolioNFTCollection,
} from '@zeal/domains/NFTCollection'

import { StorageLoader } from './StorageLoader'

type Props = {
    appBrowserProviderScript: string | null
}

export const WalletWidgetFork = ({ appBrowserProviderScript }: Props) => {
    const applicationContainerVariant = (() => {
        switch (ZealPlatform.OS) {
            case 'ios':
            case 'android':
                return 'mobile'
            case 'web':
                return 'extension'
            /* istanbul ignore next */
            default:
                return notReachable(ZealPlatform)
        }
    })()

    const [entryPoint, setEntrypoint] = useState<EntryPoint>(() => {
        switch (ZealPlatform.OS) {
            case 'ios':
            case 'android':
                return {
                    type: 'extension',
                    mode: 'fullscreen',
                    initialActiveTab: { type: 'portfolio' },
                }
            case 'web':
                return parseEntrypointFromQueryString(
                    window.location.search
                ).getSuccessResultOrThrow('fail to parse entrypoint params')
            /* istanbul ignore next */
            default:
                return notReachable(ZealPlatform)
        }
    })

    useLayoutEffect(() => {
        switch (entryPoint.type) {
            case 'extension':
                switch (ZealPlatform.OS) {
                    case 'ios':
                    case 'android':
                        break
                    case 'web':
                        document.documentElement.style.minHeight = '600px'
                        break
                    /* istanbul ignore next */
                    default:
                        return notReachable(ZealPlatform)
                }
                break
            case 'bridge':
            case 'zwidget':
            case 'add_account':
            case 'send_nft':
            case 'send_erc20_token':
            case 'send_money':
            case 'setup_recovery_kit':
            case 'onboarding':
            case 'create_contact':
            case 'swap':
            case 'buy':
            case 'add_from_hardware_wallet':
            case 'bank_transfer':
            case 'kyc_process':
            case 'create_safe':
            case 'recover_safe':
            case 'add_funds':
                break
            /* istanbul ignore next */
            default:
                return notReachable(entryPoint)
        }
    }, [entryPoint])

    switch (entryPoint.type) {
        case 'add_account':
        case 'create_contact':
        case 'onboarding':
        case 'send_erc20_token':
        case 'send_money':
        case 'send_nft':
        case 'setup_recovery_kit':
        case 'swap':
        case 'add_from_hardware_wallet':
        case 'bridge':
        case 'bank_transfer':
        case 'kyc_process':
        case 'create_safe':
        case 'recover_safe':
        case 'add_funds':
        case 'buy':
            return (
                <ApplicationContainer variant={applicationContainerVariant}>
                    <StorageLoader
                        entryPoint={entryPoint}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'add_wallet_clicked':
                                case 'import_keys_button_clicked':
                                case 'on_import_latest_bank_transfer_owner_clicked':
                                case 'on_card_import_on_import_keys_clicked':
                                    setEntrypoint({ type: 'add_account' })
                                    break

                                case 'track_wallet_clicked':
                                    setEntrypoint({
                                        type: 'create_contact',
                                    })
                                    break

                                case 'hardware_wallet_clicked':
                                    setEntrypoint({
                                        type: 'add_from_hardware_wallet',
                                    })
                                    break
                                case 'on_address_scanned':
                                    setEntrypoint({
                                        type: 'send_erc20_token',
                                        fromAddress: msg.account.address,
                                        tokenCurrencyId: null,
                                        toAddress: msg.address,
                                    })
                                    break
                                case 'on_address_scanned_and_add_label':
                                    setEntrypoint({
                                        type: 'send_erc20_token',
                                        fromAddress: msg.account.address,
                                        tokenCurrencyId: null,
                                        toAddress:
                                            msg.accountsWithKeystores[0].account
                                                .address,
                                    })
                                    break

                                case 'on_accounts_create_success_animation_finished':
                                case 'on_onboarding_completed':
                                    switch (ZealPlatform.OS) {
                                        case 'ios':
                                        case 'android':
                                            break
                                        case 'web':
                                            // after navigating back to portfolio view we need to reset location to enable "refresh" button in browser
                                            window.parent.location.replace(
                                                joinURL(
                                                    EXTENSION_URL,
                                                    'page_entrypoint.html?type=extension&mode=fullscreen&celebrate=true'
                                                )
                                            )
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(ZealPlatform)
                                    }

                                    setEntrypoint({
                                        type: 'extension',
                                        mode: 'fullscreen',
                                        initialActiveTab: { type: 'portfolio' },
                                    })
                                    break

                                case 'on_secret_phrase_verified_success':
                                case 'on_google_drive_backup_success':
                                case 'lock_screen_close_click':
                                case 'close':
                                case 'on_all_transaction_success':
                                case 'bridge_completed':
                                case 'on_on_ramp_transfer_success_close_click':
                                case 'on_monerium_deposit_success_go_to_wallet_clicked':
                                case 'on_top_up_transaction_complete_close':
                                case 'on_monerium_order_status_changed':
                                case 'on_buy_success':
                                case 'buy_closed':
                                case 'on_monerium_sign_delay_relay_success_close_clicked':
                                case 'internal_send_transaction_closed':
                                case 'on_kyc_data_updated_close_clicked':
                                case 'on_gnosis_pay_kyc_submitted_animation_complete':
                                case 'on_gnosis_pay_onboarding_flow_closed':
                                case 'on_account_create_with_card_detected_success_animation_finished':
                                    switch (ZealPlatform.OS) {
                                        case 'ios':
                                        case 'android':
                                            break
                                        case 'web':
                                            // after navigating back to portfolio view we need to reset location to enable "refresh" button in browser
                                            window.parent.location.replace(
                                                joinURL(
                                                    EXTENSION_URL,
                                                    'page_entrypoint.html?type=extension&mode=fullscreen'
                                                )
                                            )
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(ZealPlatform)
                                    }

                                    setEntrypoint({
                                        type: 'extension',
                                        mode: 'fullscreen',
                                        initialActiveTab: { type: 'portfolio' },
                                    })
                                    break

                                case 'on_do_bank_transfer_clicked':
                                case 'on_bank_transfer_selected':
                                    setEntrypoint({
                                        type: 'bank_transfer',
                                        variant: { type: 'deposit' },
                                    })
                                    break

                                case 'monerium_deposit_on_enable_card_clicked':
                                case 'monerium_on_card_disconnected':
                                case 'on_card_disconnected':
                                    switch (ZealPlatform.OS) {
                                        case 'ios':
                                        case 'android':
                                            break
                                        case 'web':
                                            // after navigating back to card view we need to reset location to enable "refresh" button in browser
                                            const cardTab: TabControllerTab = {
                                                type: 'card',
                                            }

                                            window.parent.location.replace(
                                                joinURL(
                                                    EXTENSION_URL,
                                                    `page_entrypoint.html?type=extension&mode=fullscreen&initialActiveTab=${encodeURIComponent(
                                                        JSON.stringify(cardTab)
                                                    )}`
                                                )
                                            )
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(ZealPlatform)
                                    }

                                    setEntrypoint({
                                        type: 'extension',
                                        mode: 'fullscreen',
                                        initialActiveTab: { type: 'card' },
                                    })
                                    break
                                case 'on_activate_existing_monerium_account_click':
                                    switch (ZealPlatform.OS) {
                                        case 'ios':
                                        case 'android':
                                            setEntrypoint({
                                                type: 'extension',
                                                mode: 'fullscreen',
                                                initialActiveTab: {
                                                    type: 'browse',
                                                    url: new URL(msg.url),
                                                },
                                            })
                                            break
                                        case 'web':
                                            openExternalURL(msg.url)
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(ZealPlatform)
                                    }
                                    break
                                case 'on_create_smart_wallet_clicked':
                                    setEntrypoint({
                                        type: 'create_safe',
                                    })
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </ApplicationContainer>
            )

        case 'extension':
            return (
                <ApplicationContainer variant={applicationContainerVariant}>
                    <Main
                        appBrowserProviderScript={appBrowserProviderScript}
                        mode={entryPoint.mode}
                        initialActiveTab={entryPoint.initialActiveTab}
                        onMsg={async (msg) => {
                            switch (entryPoint.mode) {
                                case 'fullscreen':
                                    switch (msg.type) {
                                        case 'on_open_fullscreen_view_click':
                                            captureError(
                                                new ImperativeError(
                                                    'trying to open fullscreen mode in fullscreen mode'
                                                )
                                            )
                                            break

                                        case 'on_get_started_clicked':
                                            setEntrypoint({
                                                type: 'onboarding',
                                            })
                                            break

                                        case 'on_recovery_kit_setup':
                                            setEntrypoint({
                                                type: 'setup_recovery_kit',
                                                address: msg.address,
                                            })
                                            break

                                        case 'on_send_nft_click':
                                            setEntrypoint({
                                                type: 'send_nft',
                                                fromAddress: msg.fromAddress,
                                                nftId: encodeURIComponent(
                                                    msg.nft.tokenId
                                                ),
                                                mintAddress:
                                                    msg.collection.mintAddress,
                                                networkHexId:
                                                    msg.collection.networkHexId,
                                            })
                                            break

                                        case 'on_swap_clicked':
                                            setEntrypoint({
                                                type: 'swap',
                                                fromAddress: msg.fromAddress,
                                                fromCurrencyId: msg.currencyId,
                                                toCurrencyId: null,
                                            })
                                            break

                                        case 'on_get_cashback_currency_clicked':
                                            setEntrypoint({
                                                type: 'swap',
                                                fromAddress:
                                                    msg.fromAccount.address,
                                                fromCurrencyId: null,
                                                toCurrencyId:
                                                    CASHBACK_CURRENCY.id,
                                            })
                                            break

                                        case 'on_bridge_clicked':
                                            setEntrypoint({
                                                type: 'bridge',
                                                fromAddress: msg.fromAddress,
                                                fromCurrencyId: msg.currencyId,
                                            })
                                            break

                                        case 'on_send_clicked':
                                            setEntrypoint({
                                                type: 'send_money',
                                                fromAddress: msg.fromAddress,
                                                tokenCurrencyId: msg.currencyId,
                                                toAddress: null,
                                            })
                                            break

                                        case 'on_address_scanned_and_add_label':
                                            setEntrypoint({
                                                type: 'send_erc20_token',
                                                fromAddress:
                                                    msg.account.address,
                                                tokenCurrencyId: null,
                                                toAddress:
                                                    msg.accountsWithKeystores[0]
                                                        .account.address,
                                            })
                                            break
                                        case 'on_address_scanned':
                                            setEntrypoint({
                                                type: 'send_erc20_token',
                                                fromAddress:
                                                    msg.account.address,
                                                tokenCurrencyId: null,
                                                toAddress: msg.address,
                                            })
                                            break

                                        case 'track_wallet_clicked':
                                            setEntrypoint({
                                                type: 'create_contact',
                                            })
                                            break

                                        case 'safe_wallet_clicked':
                                        case 'on_create_smart_wallet_clicked':
                                            setEntrypoint({
                                                type: 'create_safe',
                                            })
                                            break

                                        case 'recover_safe_wallet_clicked':
                                            setEntrypoint({
                                                type: 'recover_safe',
                                            })
                                            break

                                        case 'hardware_wallet_clicked':
                                            setEntrypoint({
                                                type: 'add_from_hardware_wallet',
                                            })
                                            break

                                        case 'on_add_private_key_click':
                                        case 'add_wallet_clicked':
                                        case 'import_card_owner_clicked':
                                        case 'on_card_import_on_import_keys_clicked':
                                            setEntrypoint({
                                                type: 'add_account',
                                            })
                                            break

                                        case 'on_bank_transfer_selected':
                                        case 'on_do_bank_transfer_clicked':
                                        case 'on_switch_bank_transfer_provider_clicked':
                                            setEntrypoint({
                                                type: 'bank_transfer',
                                                variant: { type: 'deposit' },
                                            })
                                            break

                                        case 'on_kyc_try_again_clicked':
                                            setEntrypoint({
                                                type: 'kyc_process',
                                            })
                                            break

                                        case 'on_zwidget_expand_request':
                                            sendToActiveTabZWidget({
                                                type: 'extension_to_zwidget_expand_zwidget',
                                            })
                                            window.close()
                                            break

                                        case 'on_earn_deposit_success': {
                                            fireCelebrateAnimation()
                                            break
                                        }

                                        case 'import_keys_button_clicked':
                                            setEntrypoint({
                                                type: 'add_account',
                                            })
                                            break
                                        case 'on_physical_card_activated_info_screen_closed_wallet_not_funded':
                                        case 'on_add_funds_click':
                                            setEntrypoint({
                                                type: 'add_funds',
                                                address: msg.address,
                                            })
                                            break
                                        case 'on_buy_clicked':
                                            setEntrypoint({
                                                type: 'buy',
                                                fromAddress: msg.fromAddress,
                                            })
                                            break
                                        case 'on_accounts_create_success_animation_finished':
                                            switch (ZealPlatform.OS) {
                                                case 'ios':
                                                case 'android':
                                                    break
                                                case 'web':
                                                    // after navigating back to portfolio view we need to reset location to enable "refresh" button in browser
                                                    window.parent.location.replace(
                                                        joinURL(
                                                            EXTENSION_URL,
                                                            'page_entrypoint.html?type=extension&mode=fullscreen&celebrate=true'
                                                        )
                                                    )
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(
                                                        ZealPlatform
                                                    )
                                            }

                                            setEntrypoint({
                                                type: 'extension',
                                                mode: 'fullscreen',
                                                initialActiveTab: {
                                                    type: 'portfolio',
                                                },
                                            })
                                            break

                                        case 'on_meta_mask_mode_changed_pupup_refresh_page_clicked':
                                            const [tab] =
                                                await chrome.tabs.query({
                                                    active: true,
                                                    currentWindow: true,
                                                })

                                            if (tab && tab.id) {
                                                await chrome.tabs.reload(tab.id)
                                            }
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                    break

                                case 'popup':
                                    switch (msg.type) {
                                        case 'on_open_fullscreen_view_click':
                                            openExtensionInFullScreen()
                                            break

                                        case 'on_get_started_clicked':
                                            openOnboardingPageTab()
                                            break

                                        case 'on_recovery_kit_setup':
                                            openRecoveryKitSetup(msg.address)
                                            break

                                        case 'on_send_nft_click':
                                            openSendNFT({
                                                collection: msg.collection,
                                                fromAddress: msg.fromAddress,
                                                nft: msg.nft,
                                            })
                                            break

                                        case 'on_get_cashback_currency_clicked':
                                            openSwap({
                                                fromAddress:
                                                    msg.fromAccount.address,
                                                fromCurrencyId: null,
                                                toCurrencyId:
                                                    CASHBACK_CURRENCY.id,
                                            })
                                            break

                                        case 'on_swap_clicked':
                                            openSwap({
                                                fromAddress: msg.fromAddress,
                                                fromCurrencyId: msg.currencyId,
                                                toCurrencyId: null,
                                            })
                                            break
                                        case 'on_bridge_clicked':
                                            openBridge({
                                                fromAddress: msg.fromAddress,
                                                fromCurrencyId: msg.currencyId,
                                            })
                                            break

                                        // We don't have scanning on web, and also we want to prevent phishing
                                        case 'on_address_scanned_and_add_label':
                                        case 'on_address_scanned':
                                            throw new ImperativeError(
                                                'on_address_scanned event triggered in popup'
                                            )

                                        case 'on_send_clicked':
                                            openSendMoney({
                                                currencyId: msg.currencyId,
                                                fromAddress: msg.fromAddress,
                                            })
                                            break

                                        case 'track_wallet_clicked':
                                            openCreateContactPage()
                                            break

                                        case 'safe_wallet_clicked':
                                        case 'on_create_smart_wallet_clicked':
                                            openCreateSafePage()
                                            break

                                        case 'recover_safe_wallet_clicked':
                                            openRecoverSafePage()
                                            break

                                        case 'hardware_wallet_clicked':
                                            openAddFromHardwareWallet()
                                            break

                                        case 'on_add_private_key_click':
                                        case 'add_wallet_clicked':
                                        case 'import_card_owner_clicked':
                                        case 'on_card_import_on_import_keys_clicked':
                                            openAddAccountPageTab()
                                            break

                                        case 'on_bank_transfer_selected':
                                        case 'on_switch_bank_transfer_provider_clicked':
                                        case 'on_do_bank_transfer_clicked':
                                            openBankTransferPage()
                                            break

                                        case 'on_kyc_try_again_clicked':
                                            openKycProcessPage()
                                            break

                                        case 'on_zwidget_expand_request':
                                            sendToActiveTabZWidget({
                                                type: 'extension_to_zwidget_expand_zwidget',
                                            })
                                            window.close()
                                            break

                                        case 'import_keys_button_clicked':
                                            setEntrypoint({
                                                type: 'add_account',
                                            })
                                            break
                                        case 'on_physical_card_activated_info_screen_closed_wallet_not_funded':
                                        case 'on_add_funds_click': {
                                            openAddFundsPage({
                                                address: msg.address,
                                            })
                                            break
                                        }

                                        case 'on_earn_deposit_success':
                                            noop() // cannot celebrate in popup
                                            break

                                        case 'on_accounts_create_success_animation_finished':
                                            setEntrypoint({
                                                type: 'extension',
                                                mode: 'popup',
                                                initialActiveTab: {
                                                    type: 'portfolio',
                                                },
                                            })
                                            break
                                        case 'on_buy_clicked':
                                            openExternalURL(
                                                chrome.runtime.getURL(
                                                    `page_entrypoint.html?type=buy&fromAddress=${msg.fromAddress}`
                                                )
                                            )
                                            break

                                        case 'on_meta_mask_mode_changed_pupup_refresh_page_clicked':
                                            const [tab] =
                                                await chrome.tabs.query({
                                                    active: true,
                                                    currentWindow: true,
                                                })

                                            if (tab && tab.id) {
                                                await chrome.tabs.reload(tab.id)
                                            }
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                    break

                                default:
                                    notReachable(entryPoint.mode)
                            }
                        }}
                    />
                </ApplicationContainer>
            )

        case 'zwidget':
            return (
                <ApplicationContainer variant="extension_zwidget">
                    <ZWidget dAppUrl={entryPoint.dAppUrl} />
                </ApplicationContainer>
            )

        /* istanbul ignore next */
        default:
            return notReachable(entryPoint)
    }
}

const openRecoveryKitSetup = (address: Web3.address.Address) => {
    const url = chrome.runtime.getURL(
        `page_entrypoint.html?type=setup_recovery_kit&address=${address}`
    )
    openExternalURL(url)
}

const openKycProcessPage = () => {
    const url = chrome.runtime.getURL(`page_entrypoint.html?type=kyc_process`)
    openExternalURL(url)
}

const openAddFundsPage = ({ address }: { address: Web3.address.Address }) => {
    const url = chrome.runtime.getURL(
        `page_entrypoint.html?type=add_funds&address=${address}`
    )
    openExternalURL(url)
}

const openBankTransferPage = () => {
    const url = chrome.runtime.getURL(`page_entrypoint.html?type=bank_transfer`)
    openExternalURL(url)
}

const openExtensionInFullScreen = () => {
    const url = chrome.runtime.getURL(
        `page_entrypoint.html?type=extension&mode=fullscreen`
    )
    openExternalURL(url)
}

const openSwap = ({
    fromAddress,
    fromCurrencyId,
    toCurrencyId,
}: {
    fromAddress: Web3.address.Address
    fromCurrencyId: CurrencyId | null
    toCurrencyId: CurrencyId | null
}) => {
    const fromCurrencyIdEncoded =
        fromCurrencyId && encodeURIComponent(fromCurrencyId)

    const toCurrencyIdEncoded = toCurrencyId && encodeURIComponent(toCurrencyId)

    const fromCurrencyParam =
        (fromCurrencyId && `&fromCurrencyId=${fromCurrencyIdEncoded}`) || ''

    const toCurrencyParam =
        (toCurrencyId && `&toCurrencyId=${toCurrencyIdEncoded}`) || ''

    const url = `page_entrypoint.html?type=swap&fromAddress=${fromAddress}${fromCurrencyParam}${toCurrencyParam}`

    openExternalURL(chrome.runtime.getURL(url))
}

const openBridge = ({
    fromAddress,
    fromCurrencyId,
}: {
    fromAddress: Web3.address.Address
    fromCurrencyId: CurrencyId | null
}) => {
    const fromCurrencyIdEncoded =
        fromCurrencyId && encodeURIComponent(fromCurrencyId)
    const url = fromCurrencyId
        ? `page_entrypoint.html?type=bridge&fromAddress=${fromAddress}&fromCurrencyId=${fromCurrencyIdEncoded}`
        : `page_entrypoint.html?type=bridge&fromAddress=${fromAddress}`

    openExternalURL(chrome.runtime.getURL(url))
}

const openSendMoney = ({
    fromAddress,
    currencyId,
}: {
    fromAddress: Web3.address.Address
    currencyId: CurrencyId | null
}) => {
    const currencyIdEncoded = currencyId && encodeURIComponent(currencyId)
    const url = currencyId
        ? `page_entrypoint.html?type=send_money&fromAddress=${fromAddress}&tokenCurrencyId=${currencyIdEncoded}`
        : `page_entrypoint.html?type=send_money&fromAddress=${fromAddress}`

    openExternalURL(chrome.runtime.getURL(url))
}

const openSendNFT = ({
    fromAddress,
    collection,
    nft,
}: {
    fromAddress: Web3.address.Address
    nft: PortfolioNFT
    collection: PortfolioNFTCollection
}) => {
    const nftId = encodeURIComponent(nft.tokenId)
    const { mintAddress, networkHexId } = collection

    const url = `page_entrypoint.html?type=send_nft&fromAddress=${fromAddress}&nftId=${nftId}&mintAddress=${mintAddress}&networkHexId=${networkHexId}`

    openExternalURL(chrome.runtime.getURL(url))
}
