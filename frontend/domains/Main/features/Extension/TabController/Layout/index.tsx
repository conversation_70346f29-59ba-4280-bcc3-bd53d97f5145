import { useState } from 'react'
import { useIntl } from 'react-intl'

import { NavActivityIconOutline } from '@zeal/uikit/Icon/NavActivityIconOutline'
import { NavActivityIconSolid } from '@zeal/uikit/Icon/NavActivityIconSolid'
import { NavBrowseIconOutline } from '@zeal/uikit/Icon/NavBrowseIconOutline'
import { NavBrowseIconSolid } from '@zeal/uikit/Icon/NavBrowseIconSolid'
import { NavCardIconOutline } from '@zeal/uikit/Icon/NavCardIconOutline'
import { NavCardIconSolid } from '@zeal/uikit/Icon/NavCardIconSolid'
import { NavHomeIconOutline } from '@zeal/uikit/Icon/NavHomeIconOutline'
import { NavHomeIconSolid } from '@zeal/uikit/Icon/NavHomeIconSolid'
import { OutlineGift } from '@zeal/uikit/Icon/OutlineGift'
import { SolidGift } from '@zeal/uikit/Icon/SolidGift'
import { IconButton } from '@zeal/uikit/IconButton'
import { LanguageSettings } from '@zeal/uikit/Language'
import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'
import { TabsLayout } from '@zeal/uikit/TabsLayout'

import { notReachable } from '@zeal/toolkit'
import { LoadedReloadableData } from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { keys } from '@zeal/toolkit/Object'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import * as Web3 from '@zeal/toolkit/Web3'
import { openExternalURL } from '@zeal/toolkit/Window'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    FetchPortfolioRequest,
    FetchPortfolioResponse,
} from '@zeal/domains/Account/api/fetchAccounts'
import { View } from '@zeal/domains/Account/features/View'
import { getPredefinedExplorerLink } from '@zeal/domains/Address/helpers/getExplorerLink'
import { AppBrowser } from '@zeal/domains/App/features/AppBrowser'
import { CardConfig } from '@zeal/domains/Card'
import {
    GNOSIS_PAY_DASHBOARD_CARD_URL,
    GNOSIS_PAY_DASHBOARD_COMPLETE_CARD_ORDER_URL,
} from '@zeal/domains/Card/constants'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { RewardsTab } from '@zeal/domains/Card/domains/Reward/features/RewardsTab'
import { CardTab } from '@zeal/domains/Card/features/CardTab'
import { cardConfigToUserEventCardOnboardedStatus } from '@zeal/domains/Card/helpers/cardConfigToUserEventCardOnboardedStatus'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SubmittedOfframpTransaction } from '@zeal/domains/Currency/domains/BankTransfer'
import { SubmitedBridgesMap } from '@zeal/domains/Currency/domains/Bridge'
import { SwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { ConnectionMap } from '@zeal/domains/DApp/domains/ConnectionState'
import { WalletConnectInstanceLoadable } from '@zeal/domains/DApp/domains/WalletConnect/api/fetchWalletConnectInstance'
import {
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
    TotalEarningsInDefaultCurrencyMap,
} from '@zeal/domains/Earn'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { InitialActiveTab, Mode, TabControllerTab } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    BankTransferInfo,
    BrowserTabState,
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { Submited } from '@zeal/domains/TransactionRequest'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { ViewTransactionActivity } from '@zeal/domains/Transactions/features/ViewTransactionActivity'
import { keystoreToUserEventType } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    mode: Mode
    initialActiveTab: InitialActiveTab
    browserTabState: BrowserTabState
    sessionPassword: string
    account: Account
    accounts: AccountsMap
    portfolioMap: PortfolioMap
    portfolioLoadable: LoadedReloadableData<
        FetchPortfolioResponse,
        FetchPortfolioRequest
    >
    keystoreMap: KeyStoreMap
    submitedBridgesMap: SubmitedBridgesMap
    isEthereumNetworkFeeWarningSeen: boolean
    submittedOffRampTransactions: SubmittedOfframpTransaction[]
    networkMap: NetworkMap
    bankTransferInfo: BankTransferInfo
    earnTakerMetrics: EarnTakerMetrics
    installationCampaign: string | null

    encryptedPassword: string
    transactionRequests: Record<Web3.address.Address, Submited[]>
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    customCurrencyMap: CustomCurrencyMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap

    networkRPCMap: NetworkRPCMap
    connections: ConnectionMap
    installationId: string
    walletConnectInstanceLoadable: WalletConnectInstanceLoadable
    cardConfig: CardConfig
    appBrowserProviderScript: string | null
    notificationsConfig: NotificationsConfig
    totalEarningsInDefaultCurrencyMap: TotalEarningsInDefaultCurrencyMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    refreshContainerState: RefreshContainerState
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    referralConfig: ReferralConfig
    languageSettings: LanguageSettings
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof View>,
          {
              type:
                  | 'on_experimental_change_clicked'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
                  | 'on_add_funds_click'
                  | 'reload_button_click'
                  | 'on_profile_change_confirm_click'
                  | 'on_recovery_kit_setup'
                  | 'on_custom_currency_delete_request'
                  | 'on_custom_currency_update_request'
                  | 'on_token_click'
                  | 'on_send_nft_click'
                  | 'bridge_completed'
                  | 'on_bridge_submitted_click'
                  | 'on_transaction_request_widget_click'
                  | 'on_dismiss_kyc_button_clicked'
                  | 'on_kyc_try_again_clicked'
                  | 'on_do_bank_transfer_clicked'
                  | 'transaction_request_completed'
                  | 'transaction_request_failed'
                  | 'on_onramp_success'
                  | 'on_withdrawal_monitor_fiat_transaction_success'
                  | 'transaction_request_replaced'
                  | 'on_open_fullscreen_view_click'
                  | 'on_refresh_button_clicked'
                  | 'on_zwidget_expand_request'
                  | 'on_send_clicked'
                  | 'on_swap_clicked'
                  | 'on_buy_clicked'
                  | 'on_bridge_clicked'
                  | 'on_token_hide_click'
                  | 'on_token_un_pin_click'
                  | 'on_token_un_hide_click'
                  | 'on_token_pin_click'
                  | 'on_tokens_refresh_pulled'
                  | 'on_bank_transfer_selected'
                  | 'on_portfolio_refresh_pulled'
                  | 'on_account_label_change_submit'
                  | 'account_item_clicked'
                  | 'confirm_account_delete_click'
                  | 'on_rewards_warning_confirm_account_delete_click'
                  | 'on_network_item_click'
                  | 'on_account_create_request'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'track_wallet_clicked'
                  | 'on_rpc_change_confirmed'
                  | 'on_select_rpc_click'
                  | 'on_add_private_key_click'
                  | 'safe_wallet_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_asset_added_to_earn'
                  | 'on_earn_withdrawal_success'
                  | 'on_earn_deposit_success'
                  | 'on_recharge_configured'
                  | 'on_external_earn_deposit_completed_close_click'
                  | 'on_cashback_loaded'
                  | 'on_earnings_fetched'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'cancel_submitted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_submited'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_earn_configured'
                  | 'on_meta_mask_mode_changed_pupup_refresh_page_clicked'
                  | 'on_dismiss_bridge_widget_click'
                  | 'on_disconnect_dapps_click'
                  | 'on_delete_all_dapps_confirm_click'
                  | 'import_card_owner_clicked'
                  | 'on_notifications_config_changed'
                  | 'on_card_disconnected'
                  | 'on_switch_card_new_card_selected'
                  | 'on_default_currency_selected'
                  | 'on_lock_zeal_click'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_get_cashback_currency_clicked'
                  | 'on_dissmiss_card_kyc_onboarding_widget_clicked'
                  | 'on_earn_celebration_triggered'
                  | 'on_top_up_transaction_complete_close'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_transaction_activities_loaded'
                  | 'on_swaps_io_pending_transaction_activity_completed'
                  | 'on_pending_send_transaction_activity_completed'
                  | 'on_pending_send_transaction_activity_failed'
                  | 'on_dissmiss_card_kyc_onboarded_widget_clicked'
                  | 'on_card_b_reward_dissmiss_clicked'
                  | 'card_breward_claimed'
                  | 'card_brewards_updated'
                  | 'on_pending_breward_claim_transaction_activity_completed'
                  | 'on_pending_breward_claim_transaction_activity_failed'
                  | 'on_pending_areward_claim_transaction_activity_completed'
                  | 'on_pending_areward_claim_transaction_activity_failed'
                  | 'on_physical_card_activated_info_screen_closed'
                  | 'on_language_settings_language_selected'
                  | 'on_pending_card_top_up_state_changed'
                  | 'on_card_top_up_success'
                  | 'session_password_decrypted'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_completed_transaction_close_click'
                  | 'on_swap_cancelled_close_clicked'
                  | 'on_swap_success_clicked'
                  | 'on_pending_card_balance_timer_completed'
          }
      >
    | MsgOf<typeof ViewTransactionActivity>
    | Extract<
          MsgOf<typeof CardTab>,
          {
              type:
                  | 'on_virtual_card_order_created_animation_completed'
                  | 'on_card_onboarded_account_state_received'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_account_create_request'
                  | 'cancel_submitted'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'track_wallet_clicked'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'import_keys_button_clicked'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'safe_wallet_clicked'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_card_transactions_fetch_success'
                  | 'on_notifications_config_changed'
                  | 'on_earn_last_recharge_transaction_hash_loaded'
                  | 'on_external_earn_deposit_completed_close_click'
                  | 'on_get_cashback_currency_clicked'
                  | 'on_cashback_loaded'
                  | 'import_card_owner_clicked'
                  | 'on_gnosis_portfolio_loaded'
                  | 'on_card_disconnected'
                  | 'on_switch_card_new_card_selected'
                  | 'recover_safe_wallet_clicked'
                  | 'on_earnings_fetched'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_monerium_deposit_success_go_to_wallet_clicked'
                  | 'on_bank_transfer_selected'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_switch_bank_transfer_provider_clicked'
                  | 'on_earn_updated'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_card_onboarded_state_refresh_pulled'
                  | 'on_gnosis_pay_account_created'
                  | 'on_do_bank_transfer_clicked'
                  | 'on_app_rating_submitted'
                  | 'on_cashback_celebration_triggered'
                  | 'on_top_up_transaction_complete_close'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_new_virtual_card_created_successfully'
                  | 'on_dismiss_add_to_wallet_banner_clicked'
                  | 'on_physical_card_activated_info_screen_closed'
                  | 'on_card_onboarded_account_state_card_owner_signer_or_keystore_not_found'
                  | 'on_card_top_up_banner_dismissed'
                  | 'on_pending_card_top_up_state_changed'
          }
      >
    | MsgOf<typeof AppBrowser>
    | Extract<
          MsgOf<typeof RewardsTab>,
          {
              type:
                  | 'on_create_smart_wallet_clicked'
                  | 'on_a_reward_claimed_successfully'
                  | 'on_a_rewards_configured'
          }
      >

const TABS: Record<TabControllerTab['type'], true> = {
    portfolio: true,
    activity: true,
    card: true,
    rewards: true,
    browse: true,
}

export const Layout = ({
    mode,
    initialActiveTab,
    accounts,
    account,
    browserTabState,
    networkRPCMap,
    portfolioMap,
    isEthereumNetworkFeeWarningSeen,
    portfolioLoadable,
    keystoreMap,
    submittedOffRampTransactions,
    encryptedPassword,
    transactionRequests,
    submitedBridgesMap,
    connections,
    totalEarningsInDefaultCurrencyMap,
    earnHistoricalTakerUserCurrencyRateMap,
    transactionActivitiesCacheMap,
    swapsIOSwapRequestsMap,
    earnTakerMetrics,
    networkMap,
    bankTransferInfo,
    currencyHiddenMap,
    currencyPinMap,
    installationCampaign,
    customCurrencyMap,
    installationId,
    sessionPassword,
    walletConnectInstanceLoadable,
    feePresetMap,
    gasCurrencyPresetMap,
    cardConfig,
    celebrationConfig,
    appRating,
    appBrowserProviderScript,
    notificationsConfig,
    defaultCurrencyConfig,
    refreshContainerState,
    referralConfig,
    languageSettings,
    experimentalMode,
    onMsg,
}: Props) => {
    const [state, setState] = useState<TabControllerTab>(initialActiveTab)

    const tabs = keys(TABS)

    const openUrl = (url: string | null) => {
        switch (ZealPlatform.OS) {
            case 'ios':
            case 'android':
                setState({ type: 'browse', url: url ? new URL(url) : null })
                break
            case 'web':
                url && openExternalURL(url)
                break
            /* istanbul ignore next */
            default:
                return notReachable(ZealPlatform)
        }
    }

    return (
        <TabsLayout
            tabs={tabs.map((tab) => (
                <TabButton
                    key={tab}
                    tab={tab}
                    selected={tab === state.type}
                    onClick={() => {
                        switch (tab) {
                            case 'portfolio':
                            case 'activity':
                            case 'rewards':
                                setState({ type: tab })
                                break
                            case 'card':
                                postUserEvent({
                                    type: 'CardEnteredEvent',
                                    location: 'navbar',
                                    cardOnboardedStatus:
                                        cardConfigToUserEventCardOnboardedStatus(
                                            cardConfig
                                        ),
                                    keystoreType: keystoreToUserEventType(
                                        getKeyStore({
                                            keyStoreMap: keystoreMap,
                                            address: account.address,
                                        })
                                    ),
                                    installationId,
                                })
                                setState({ type: tab })
                                break
                            case 'browse':
                                setState({ type: tab, url: null })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(tab)
                        }
                    }}
                />
            ))}
            content={(() => {
                switch (state.type) {
                    case 'portfolio':
                        return (
                            <View
                                languageSettings={languageSettings}
                                experimentalMode={experimentalMode}
                                referralConfig={referralConfig}
                                appRating={appRating}
                                installationCampaign={installationCampaign}
                                celebrationConfig={celebrationConfig}
                                refreshContainerState={refreshContainerState}
                                connections={connections}
                                notificationsConfig={notificationsConfig}
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                feePresetMap={feePresetMap}
                                totalEarningsInDefaultCurrencyMap={
                                    totalEarningsInDefaultCurrencyMap
                                }
                                earnHistoricalTakerUserCurrencyRateMap={
                                    earnHistoricalTakerUserCurrencyRateMap
                                }
                                transactionActivitiesCacheMap={
                                    transactionActivitiesCacheMap
                                }
                                swapsIOSwapRequestsMap={swapsIOSwapRequestsMap}
                                earnTakerMetrics={earnTakerMetrics}
                                gasCurrencyPresetMap={gasCurrencyPresetMap}
                                walletConnectInstanceLoadable={
                                    walletConnectInstanceLoadable
                                }
                                isEthereumNetworkFeeWarningSeen={
                                    isEthereumNetworkFeeWarningSeen
                                }
                                customCurrencyMap={customCurrencyMap}
                                sessionPassword={sessionPassword}
                                mode={mode}
                                installationId={installationId}
                                currencyHiddenMap={currencyHiddenMap}
                                currencyPinMap={currencyPinMap}
                                networkMap={networkMap}
                                networkRPCMap={networkRPCMap}
                                submitedBridgesMap={submitedBridgesMap}
                                encryptedPassword={encryptedPassword}
                                submittedOffRampTransactions={
                                    submittedOffRampTransactions
                                }
                                keystoreMap={keystoreMap}
                                portfolioLoadable={portfolioLoadable}
                                transactionRequests={transactionRequests}
                                account={account}
                                portfolioMap={portfolioMap}
                                accounts={accounts}
                                bankTransferInfo={bankTransferInfo}
                                cardConfig={cardConfig}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'on_dapp_link_clicked':
                                            openUrl(msg.url.toString())
                                            break
                                        case 'on_discover_more_apps_clicked':
                                            openUrl(null)
                                            break
                                        case 'on_card_marketing_card_click':
                                        case 'on_card_widget_clicked':
                                        case 'on_cashback_widget_clicked':
                                        case 'on_cashback_token_click':
                                        case 'on_activate_free_card_clicked':
                                            postUserEvent({
                                                type: 'CardEnteredEvent',
                                                location: 'portfolio_screen',
                                                cardOnboardedStatus:
                                                    cardConfigToUserEventCardOnboardedStatus(
                                                        cardConfig
                                                    ),
                                                keystoreType:
                                                    keystoreToUserEventType(
                                                        getKeyStore({
                                                            keyStoreMap:
                                                                keystoreMap,
                                                            address:
                                                                account.address,
                                                        })
                                                    ),
                                                installationId,
                                            })
                                            setState({ type: 'card' })
                                            break
                                        case 'on_physical_card_activated_info_screen_closed':
                                        case 'on_switch_card_new_card_selected':
                                        case 'on_new_virtual_card_created_successfully':
                                            onMsg(msg)
                                            setState({ type: 'card' })
                                            break
                                        case 'on_earn_taker_address_click':
                                            const explorerUrl =
                                                getPredefinedExplorerLink(
                                                    msg.taker.address,
                                                    EARN_NETWORK
                                                )

                                            openUrl(explorerUrl)
                                            break
                                        case 'on_see_all_transactions_clicked':
                                            setState({
                                                type: 'activity',
                                            })
                                            break
                                        case 'on_card_order_redirect_to_gnosis_pay_clicked':
                                            openUrl(
                                                GNOSIS_PAY_DASHBOARD_COMPLETE_CARD_ORDER_URL
                                            )
                                            break

                                        case 'on_cashback_loaded':
                                        case 'on_add_funds_click':
                                        case 'reload_button_click':
                                        case 'on_profile_change_confirm_click':
                                        case 'on_recovery_kit_setup':
                                        case 'on_custom_currency_delete_request':
                                        case 'on_custom_currency_update_request':
                                        case 'on_send_nft_click':
                                        case 'bridge_completed':
                                        case 'on_bridge_submitted_click':
                                        case 'on_transaction_request_widget_click':
                                        case 'on_dismiss_kyc_button_clicked':
                                        case 'on_kyc_try_again_clicked':
                                        case 'on_do_bank_transfer_clicked':
                                        case 'transaction_request_completed':
                                        case 'transaction_request_failed':
                                        case 'on_onramp_success':
                                        case 'on_withdrawal_monitor_fiat_transaction_success':
                                        case 'transaction_request_replaced':
                                        case 'on_open_fullscreen_view_click':
                                        case 'on_refresh_button_clicked':
                                        case 'on_zwidget_expand_request':
                                        case 'on_send_clicked':
                                        case 'on_swap_clicked':
                                        case 'on_buy_clicked':
                                        case 'on_bridge_clicked':
                                        case 'on_token_hide_click':
                                        case 'on_token_un_pin_click':
                                        case 'on_token_un_hide_click':
                                        case 'on_token_pin_click':
                                        case 'on_tokens_refresh_pulled':
                                        case 'on_bank_transfer_selected':
                                        case 'on_portfolio_refresh_pulled':
                                        case 'on_account_label_change_submit':
                                        case 'account_item_clicked':
                                        case 'confirm_account_delete_click':
                                        case 'on_rewards_warning_confirm_account_delete_click':
                                        case 'on_account_create_request':
                                        case 'add_wallet_clicked':
                                        case 'hardware_wallet_clicked':
                                        case 'track_wallet_clicked':
                                        case 'on_rpc_change_confirmed':
                                        case 'on_select_rpc_click':
                                        case 'on_add_private_key_click':
                                        case 'safe_wallet_clicked':
                                        case 'recover_safe_wallet_clicked':
                                        case 'on_4337_auto_gas_token_selection_clicked':
                                        case 'on_4337_gas_currency_selected':
                                        case 'import_keys_button_clicked':
                                        case 'on_predefined_fee_preset_selected':
                                        case 'on_earn_withdrawal_success':
                                        case 'on_earn_deposit_success':
                                        case 'on_recharge_configured':
                                        case 'on_earnings_fetched':
                                        case 'on_address_scanned':
                                        case 'on_accounts_create_success_animation_finished':
                                        case 'on_add_label_to_track_only_account_during_send':
                                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                        case 'cancel_submitted':
                                        case 'on_transaction_completed_splash_animation_screen_competed':
                                        case 'transaction_submited':
                                        case 'on_ethereum_network_fee_warning_understand_clicked':
                                        case 'on_earn_configured':
                                        case 'on_meta_mask_mode_changed_pupup_refresh_page_clicked':
                                        case 'on_dismiss_bridge_widget_click':
                                        case 'on_address_scanned_and_add_label':
                                        case 'on_disconnect_dapps_click':
                                        case 'on_delete_all_dapps_confirm_click':
                                        case 'import_card_owner_clicked':
                                        case 'on_notifications_config_changed':
                                        case 'on_card_disconnected':
                                        case 'on_default_currency_selected':
                                        case 'on_lock_zeal_click':
                                        case 'on_usd_taker_metrics_loaded':
                                        case 'on_get_cashback_currency_clicked':
                                        case 'on_eur_taker_metrics_loaded':
                                        case 'on_chf_taker_metrics_loaded':
                                        case 'on_card_onboarded_account_state_received':
                                        case 'on_dissmiss_card_kyc_onboarding_widget_clicked':
                                        case 'on_app_rating_submitted':
                                        case 'on_cashback_celebration_triggered':
                                        case 'on_earn_celebration_triggered':
                                        case 'on_top_up_transaction_complete_close':
                                        case 'on_swaps_io_swap_request_created':
                                        case 'on_swaps_io_transaction_activity_swap_started':
                                        case 'on_swaps_io_transaction_activity_completed':
                                        case 'on_swaps_io_transaction_activity_failed':
                                        case 'on_dismiss_add_to_wallet_banner_clicked':
                                        case 'on_transaction_activities_loaded':
                                        case 'on_pending_send_transaction_activity_completed':
                                        case 'on_pending_send_transaction_activity_failed':
                                        case 'on_dissmiss_card_kyc_onboarded_widget_clicked':
                                        case 'on_card_b_reward_dissmiss_clicked':
                                        case 'card_breward_claimed':
                                        case 'card_brewards_updated':
                                        case 'on_card_import_on_import_keys_clicked':
                                        case 'on_card_imported_success_animation_complete':
                                        case 'on_onboarded_card_imported_success_animation_complete':
                                        case 'on_create_smart_wallet_clicked':
                                        case 'on_pending_breward_claim_transaction_activity_completed':
                                        case 'on_pending_breward_claim_transaction_activity_failed':
                                        case 'on_pending_areward_claim_transaction_activity_completed':
                                        case 'on_pending_areward_claim_transaction_activity_failed':
                                        case 'on_language_settings_language_selected':
                                        case 'on_pending_card_top_up_state_changed':
                                        case 'on_card_top_up_success':
                                        case 'on_experimental_change_clicked':
                                        case 'session_password_decrypted':
                                        case 'on_completed_safe_transaction_close_click':
                                        case 'on_completed_transaction_close_click':
                                        case 'on_swap_cancelled_close_clicked':
                                        case 'on_swap_success_clicked':
                                        case 'on_pending_card_balance_timer_completed':
                                            onMsg(msg)
                                            break

                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'activity':
                        return (
                            <ViewTransactionActivity
                                userAReferralConfig={referralConfig.userA}
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                sessionPassword={sessionPassword}
                                customCurrencyMap={customCurrencyMap}
                                installationId={installationId}
                                isEthereumNetworkFeeWarningSeen={
                                    isEthereumNetworkFeeWarningSeen
                                }
                                portfolio={portfolioLoadable.data.portfolio}
                                currencyHiddenMap={currencyHiddenMap}
                                networkMap={networkMap}
                                submitedBridgesMap={submitedBridgesMap}
                                transactionRequests={transactionRequests}
                                cardConfig={cardConfig}
                                keystoreMap={keystoreMap}
                                portfolioMap={portfolioMap}
                                accounts={accounts}
                                account={account}
                                networkRPCMap={networkRPCMap}
                                earnHistoricalTakerUserCurrencyRateMap={
                                    earnHistoricalTakerUserCurrencyRateMap
                                }
                                swapsIOSwapRequestsMap={swapsIOSwapRequestsMap}
                                encryptedPassword={encryptedPassword}
                                onMsg={onMsg}
                            />
                        )

                    case 'browse':
                        return (
                            <AppBrowser
                                appBrowserProviderScript={
                                    appBrowserProviderScript
                                }
                                dAppUrl={
                                    state.url || browserTabState.lastVisitedURL
                                }
                                onMsg={onMsg}
                            />
                        )

                    case 'card':
                        return (
                            <CardTab
                                experimentalMode={experimentalMode}
                                historicalTakerUserCurrencyRateMap={
                                    earnHistoricalTakerUserCurrencyRateMap
                                }
                                transactionActivitiesCacheMap={
                                    transactionActivitiesCacheMap
                                }
                                installationCampaign={installationCampaign}
                                appRating={appRating}
                                celebrationConfig={celebrationConfig}
                                refreshContainerState={refreshContainerState}
                                earnTakerMetrics={earnTakerMetrics}
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                notificationsConfig={notificationsConfig}
                                isEthereumNetworkFeeWarningSeen={
                                    isEthereumNetworkFeeWarningSeen
                                }
                                customCurrencyMap={customCurrencyMap}
                                currencyPinMap={currencyPinMap}
                                currencyHiddenMap={currencyHiddenMap}
                                accountsMap={accounts}
                                feePresetMap={feePresetMap}
                                gasCurrencyPresetMap={gasCurrencyPresetMap}
                                installationId={installationId}
                                keyStoreMap={keystoreMap}
                                networkMap={networkMap}
                                networkRPCMap={networkRPCMap}
                                portfolioMap={portfolioMap}
                                sessionPassword={sessionPassword}
                                encryptedPassword={encryptedPassword}
                                cardConfig={cardConfig}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'on_gnosis_pay_kyc_submitted_animation_complete':
                                            setState({ type: 'portfolio' })
                                            break
                                        case 'on_monerium_deposit_success_go_to_wallet_clicked':
                                            setState({ type: 'portfolio' })
                                            onMsg(msg)
                                            break
                                        case 'on_card_onboarded_account_state_received':
                                        case 'on_card_imported_success_animation_complete':
                                        case 'on_onboarded_card_imported_success_animation_complete':
                                        case 'on_predefined_fee_preset_selected':
                                        case 'on_account_create_request':
                                        case 'cancel_submitted':
                                        case 'on_4337_auto_gas_token_selection_clicked':
                                        case 'on_4337_gas_currency_selected':
                                        case 'track_wallet_clicked':
                                        case 'add_wallet_clicked':
                                        case 'hardware_wallet_clicked':
                                        case 'import_keys_button_clicked':
                                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                        case 'on_transaction_completed_splash_animation_screen_competed':
                                        case 'transaction_request_replaced':
                                        case 'transaction_submited':
                                        case 'safe_wallet_clicked':
                                        case 'recover_safe_wallet_clicked':
                                        case 'on_select_rpc_click':
                                        case 'on_rpc_change_confirmed':
                                        case 'on_card_transactions_fetch_success':
                                        case 'on_notifications_config_changed':
                                        case 'on_earn_last_recharge_transaction_hash_loaded':
                                        case 'on_get_cashback_currency_clicked':
                                        case 'on_cashback_loaded':
                                        case 'import_card_owner_clicked':
                                        case 'on_earn_updated':
                                        case 'on_card_disconnected':
                                        case 'on_switch_card_new_card_selected':
                                        case 'on_card_import_on_import_keys_clicked':
                                        case 'on_create_smart_wallet_clicked':
                                        case 'on_bank_transfer_selected':
                                        case 'on_accounts_create_success_animation_finished':
                                        case 'on_add_label_to_track_only_account_during_send':
                                        case 'on_ethereum_network_fee_warning_understand_clicked':
                                        case 'on_switch_bank_transfer_provider_clicked':
                                        case 'on_address_scanned':
                                        case 'on_address_scanned_and_add_label':
                                        case 'on_usd_taker_metrics_loaded':
                                        case 'on_card_onboarded_state_refresh_pulled':
                                        case 'on_eur_taker_metrics_loaded':
                                        case 'on_chf_taker_metrics_loaded':
                                        case 'on_gnosis_pay_account_created':
                                        case 'on_do_bank_transfer_clicked':
                                        case 'on_app_rating_submitted':
                                        case 'on_cashback_celebration_triggered':
                                        case 'on_top_up_transaction_complete_close':
                                        case 'on_swaps_io_swap_request_created':
                                        case 'on_new_virtual_card_created_successfully':
                                        case 'on_dismiss_add_to_wallet_banner_clicked':
                                        case 'on_virtual_card_order_created_animation_completed':
                                        case 'on_physical_card_activated_info_screen_closed':
                                        case 'on_card_onboarded_account_state_card_owner_signer_or_keystore_not_found':
                                        case 'on_card_top_up_success':
                                        case 'on_card_top_up_banner_dismissed':
                                        case 'on_pending_card_top_up_state_changed':
                                            onMsg(msg)
                                            break
                                        case 'close':
                                            setState({ type: 'portfolio' })
                                            break

                                        case 'on_activate_existing_monerium_account_click':
                                            openUrl(msg.url)
                                            break
                                        case 'on_fallback_freeze_card_click':
                                        case 'on_card_freeze_toggle_failed':
                                            openUrl(
                                                GNOSIS_PAY_DASHBOARD_CARD_URL
                                            )
                                            break
                                        case 'on_card_order_redirect_to_gnosis_pay_clicked':
                                            openUrl(
                                                GNOSIS_PAY_DASHBOARD_COMPLETE_CARD_ORDER_URL
                                            )
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )

                    case 'rewards':
                        return (
                            <RewardsTab
                                appRating={appRating}
                                networkRPCMap={networkRPCMap}
                                installationId={installationId}
                                userAReferralConfig={referralConfig.userA}
                                transactionActivitiesCacheMap={
                                    transactionActivitiesCacheMap
                                }
                                accountsMap={accounts}
                                keyStoreMap={keystoreMap}
                                selectedAccount={account}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'on_rewards_config_close':
                                            setState({ type: 'portfolio' })
                                            break

                                        case 'on_create_smart_wallet_clicked':
                                        case 'on_a_reward_claimed_successfully':
                                        case 'on_a_rewards_configured':
                                            onMsg(msg)
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )

                    /* istanbul ignore next */
                    default:
                        return notReachable(state)
                }
            })()}
        />
    )
}

type TabButtonProps = {
    tab: TabControllerTab['type']
    selected: boolean
    onClick: () => void
}

const TabButton = ({ tab, selected, onClick }: TabButtonProps) => {
    const { formatMessage } = useIntl()

    switch (tab) {
        case 'portfolio':
            return (
                <IconButton
                    variant="on_light_bold"
                    aria-label={formatMessage({
                        id: 'mainTabs.portfolio.label',
                        defaultMessage: 'Portfolio',
                    })}
                    onClick={onClick}
                >
                    {({ color }) =>
                        selected ? (
                            <NavHomeIconSolid size={28} color="teal40" />
                        ) : (
                            <NavHomeIconOutline size={28} color={color} />
                        )
                    }
                </IconButton>
            )

        case 'activity':
            return (
                <IconButton
                    variant="on_light_bold"
                    aria-pressed={selected}
                    aria-label={formatMessage({
                        id: 'mainTabs.activity.label',
                        defaultMessage: 'Activity',
                    })}
                    onClick={onClick}
                >
                    {({ color }) =>
                        selected ? (
                            <NavActivityIconSolid size={28} color="teal40" />
                        ) : (
                            <NavActivityIconOutline size={28} color={color} />
                        )
                    }
                </IconButton>
            )

        case 'card':
            return (
                <IconButton
                    variant="on_light_bold"
                    aria-pressed={selected}
                    aria-label={formatMessage({
                        id: 'mainTabs.card.label',
                        defaultMessage: 'Card',
                    })}
                    onClick={onClick}
                >
                    {({ color }) =>
                        selected ? (
                            <NavCardIconSolid size={28} color="teal40" />
                        ) : (
                            <NavCardIconOutline size={28} color={color} />
                        )
                    }
                </IconButton>
            )

        case 'rewards':
            switch (ZealPlatform.OS) {
                case 'android':
                case 'ios':
                    return (
                        <IconButton
                            variant="on_light_bold"
                            aria-pressed={selected}
                            aria-label={formatMessage({
                                id: 'mainTabs.rewards.label',
                                defaultMessage: 'Rewards',
                            })}
                            onClick={onClick}
                        >
                            {({ color }) =>
                                selected ? (
                                    <SolidGift size={28} color="teal40" />
                                ) : (
                                    <OutlineGift size={28} color={color} />
                                )
                            }
                        </IconButton>
                    )
                case 'web':
                    return null
                default:
                    return notReachable(ZealPlatform)
            }

        case 'browse':
            switch (ZealPlatform.OS) {
                case 'android':
                case 'ios':
                    return (
                        <IconButton
                            variant="on_light_bold"
                            aria-pressed={selected}
                            aria-label={formatMessage({
                                id: 'mainTabs.browse.title',
                                defaultMessage: 'Browse',
                            })}
                            onClick={onClick}
                        >
                            {({ color }) =>
                                selected ? (
                                    <NavBrowseIconSolid
                                        size={28}
                                        color="teal40"
                                    />
                                ) : (
                                    <NavBrowseIconOutline
                                        size={28}
                                        color={color}
                                    />
                                )
                            }
                        </IconButton>
                    )

                case 'web':
                    return null
                /* istanbul ignore next */
                default:
                    return notReachable(ZealPlatform)
            }

        /* istanbul ignore next */
        default:
            return notReachable(tab)
    }
}
