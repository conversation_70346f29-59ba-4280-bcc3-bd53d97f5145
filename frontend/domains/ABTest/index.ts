import { str as hash } from 'crc-32'
import memoize from 'lodash.memoize'

import { isDevelopment, isLocal, isProduction } from '@zeal/toolkit/Environment'

export type Feature =
    | 'live_activities'
    | 'landing_screen_2'
    | 'card_onboarding'
    | 'card_topup_vanity'

const isEnabled = (installationId: string, feature: Feature): boolean => {
    if (!isProduction()) {
        return true
    }
    const hashed = hash(`${installationId}-${feature}`)
    return hashed % 2 === 0
}

export const isLandingScreen2Enabled = (installationId: string) =>
    isFeatureEnabled(installationId, 'landing_screen_2')

export const isFeatureEnabled = memoize(
    isEnabled,
    (installationId, feature) => `${installationId}-${feature}`
)

export const isCardTopupVanityEnabled = ({
    experimentalMode,
}: {
    experimentalMode: boolean
}): boolean => isDevelopment() || isLocal() || experimentalMode
