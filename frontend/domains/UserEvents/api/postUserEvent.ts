import { post } from '@zeal/api/request'
import { post as postBackend } from '@zeal/api/requestBackend'
import once from 'lodash/once'

import { noop, notReachable } from '@zeal/toolkit'
import { getAppVersion, getEnvironment } from '@zeal/toolkit/Environment'
import * as reporting from '@zeal/toolkit/Error/reporting'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

import { UserEvent } from '..'

export const postUserEvent = async (
    userEvent: UserEvent & { installationId: string }
): Promise<void> => {
    try {
        const { type, installationId: _, ...rest } = userEvent

        const completeUserEvent = {
            ...userEvent,
            version: getAppVersion(),
            platform: ZealPlatform.OS,
        }

        const env = getEnvironment()

        switch (env) {
            case 'production':
                reporting.addBreadCrumb({ message: type, data: rest })

                await post('/wallet/metrics', {
                    body: completeUserEvent,
                } as any) // we want to disconnect from swagger api as BE did not validate or is source of truth for events

                await postBackend('/api/metrics', {
                    body: completeUserEvent,
                })
                break

            case 'local':
                break
            case 'development':
                logEventToConsole(completeUserEvent)
                break

            default:
                notReachable(env)
        }
    } catch {}
}

const logEventToConsole = (userEvent: unknown) => {
    switch (ZealPlatform.OS) {
        case 'ios':
            // console.error is the only way for this log to appear in Console app. Prefix for easier filtering in Console tool
            console.error('AnalyticsEvent', userEvent) // eslint-disable-line no-console
            break
        case 'android':
            // just JSON in adb output. use `adb logcat "*:S" ReactNativeJS:V` to see it
            console.log('AnalyticsEvent', userEvent) // eslint-disable-line no-console
            break
        case 'web':
            // Look nice, do not pollute console much
            console.table(userEvent) // eslint-disable-line no-console
            break
        default:
            notReachable(ZealPlatform)
    }
}

export const postUserEventOnce = once(postUserEvent)
