import { FormattedMessage } from 'react-intl'

import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { KeyPadFloatIntut } from '@zeal/uikit/Input/FloatInput'
import { Screen } from '@zeal/uikit/Screen'

import { noop, notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

import { AccountsMap } from '@zeal/domains/Account'
import { NetworkMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Banner } from './Banner'
import { CTA } from './CTA'
import { validateEditStep } from './validate'

import { Data, Pollable } from '../../../fetch'
import { FormLayout } from '../FormLayout'

type Props = {
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    accountsMap: AccountsMap
    installationId: string
    portfolioMap: PortfolioMap
    pollable: Pollable
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_form_continue_clicked'; data: Data }
    | { type: 'import_keys_button_clicked' }
    | MsgOf<typeof Banner>
    | MsgOf<typeof CTA>
    | Extract<
          MsgOf<typeof FormLayout>,
          {
              type:
                  | 'on_from_token_selector_clicked'
                  | 'on_to_wallet_clicked'
                  | 'on_pollable_error_widget_clicked'
                  | 'import_keys_button_clicked'
                  | 'on_amount_in_crypto_currency_changed'
          }
      >

export const EditStep = ({
    pollable,
    networkMap,
    defaultCurrencyConfig,

    installationId,
    accountsMap,
    portfolioMap,
    onMsg,
}: Props) => {
    const validationResult = validateEditStep(pollable)

    const errors = validationResult.getFailureReason() || {}

    const onContinue = () => {
        validationResult.tap((data) => {
            onMsg({ type: 'on_form_continue_clicked', data })
        })
    }

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={16} shrink alignY="stretch">
                <FormLayout
                    pollable={pollable}
                    errors={errors}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_amount_input_clicked':
                                noop()
                                break
                            case 'on_from_token_selector_clicked':
                            case 'on_to_wallet_clicked':
                            case 'on_pollable_error_widget_clicked':
                            case 'import_keys_button_clicked':
                            case 'on_amount_in_crypto_currency_changed':
                                onMsg(msg)
                                break
                            case 'on_form_continue_clicked':
                                onContinue()
                                break
                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    networkMap={networkMap}
                    accountsMap={accountsMap}
                    portfolioMap={portfolioMap}
                />
                <Column shrink alignY="end" fill spacing={12}>
                    <Banner
                        onMsg={onMsg}
                        errors={errors.banner}
                        installationId={installationId}
                    />

                    {(() => {
                        switch (ZealPlatform.OS) {
                            case 'web':
                                return null
                            case 'android':
                            case 'ios':
                                return (
                                    <KeyPadFloatIntut
                                        onChange={(amount) =>
                                            onMsg({
                                                type: 'on_amount_in_crypto_currency_changed',
                                                amount,
                                            })
                                        }
                                        value={pollable.params.form.amount}
                                        fraction={
                                            pollable.params.form.cryptoCurrency
                                                .fraction
                                        }
                                        disabled={false}
                                    />
                                )
                            /* istanbul ignore next */
                            default:
                                return notReachable(ZealPlatform)
                        }
                    })()}

                    <Actions variant="default">
                        <Button
                            size="regular"
                            variant="secondary"
                            onClick={() => {
                                onMsg({ type: 'close' })
                            }}
                        >
                            <FormattedMessage
                                id="action.cancel"
                                defaultMessage="Cancel"
                            />
                        </Button>
                        <CTA
                            pollable={pollable}
                            validationResult={validationResult}
                            onMsg={onMsg}
                        />
                    </Actions>
                </Column>
            </Column>
        </Screen>
    )
}
