import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import { Result } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { CardCashback } from '@zeal/domains/Card/domains/Cashback'
import { BReward } from '@zeal/domains/Card/domains/Reward'
import { CountryISOCode } from '@zeal/domains/Country'
import {
    CryptoCurrency,
    FiatCurrency,
    PriceChange24H,
} from '@zeal/domains/Currency'
import { SwapRoute } from '@zeal/domains/Currency/domains/SwapQuote'
import { DeployedTaker, TakerApyMap, TakerType } from '@zeal/domains/Earn'
import { FXRate2 } from '@zeal/domains/FXRate'
import {
    EOA,
    PrivateKey,
    Safe4337,
    SecretPhrase,
    SigningKeyStore,
} from '@zeal/domains/KeyStore'
import { InternalTransactionActionSource } from '@zeal/domains/Main'
import { Cry<PERSON>oMoney, FiatMoney, Money2 } from '@zeal/domains/Money'
import { Network } from '@zeal/domains/Network'
import {
    EthSendTransaction,
    EthSignTypedDataV4,
} from '@zeal/domains/RPCRequest'
import {
    SubmitedTransaction,
    SubmitedTransactionFailed,
} from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction'
import {
    SubmittedUserOperation,
    SubmittedUserOperationFailed,
    SubmittedUserOperationRejected,
} from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import { MetaTransactionData } from '@zeal/domains/UserOperation'

export * from './MerchantCategory'

export type CardConfig =
    | ReadonlySignerIsNotSelectedCardConfig
    | ReadonlySignerSelectedCardConfig
    | ReadonlySignerSelectedOnboardedCardConfig

export type ReadonlySignerIsNotSelectedCardConfig = {
    type: 'card_readonly_signer_address_is_not_selected'
}

export type ReadonlySignerSelectedCardConfig = {
    type: 'card_readonly_signer_address_is_selected'
    readonlySignerAddress: Web3.address.Address
    lastDismissedOnboardingBannerState:
        | GnosisPayAccountNotOnboardedState['state']
        | null
    userId: string | null
}

export type RechargePreferences = {
    taker: TakerType
    threshold: bigint
}

export type ReadonlySignerSelectedOnboardedCardConfig = {
    type: 'card_readonly_signer_address_is_selected_fully_onboarded'
    readonlySignerAddress: Web3.address.Address
    currency: CryptoCurrency

    selectedCardId: string | null
    lastSeenSafeAddress: Web3.address.Address
    cardTransactionsCache: CardTransaction[] | null
    lastRechargeTransactionHash: string | null
    cashback: CardCashback
    dissmissedAddToWalletBanner: boolean
    lastDismissedKycBannerState: GnosisPayOnboardedKycStatus | null

    isCreatedViaZeal: boolean
    country: CountryISOCode | null
    rewards: BReward
    userId: string | null
}

export type CardSlientSignKeyStore = PrivateKey | SecretPhrase | Safe4337

export type GnosisPayLoginSignature = {
    type: 'gnosis_pay_login_info'
    address: Web3.address.Address
    message: string
    signature: string
}

export type GnosisPayLoginInfo = {
    type: 'gnosis_pay_login_info'
    token: string // only token so far
    expiresAtMs: number
}

export type MerchantInfo = {
    name: string
    mcc: number
    country: string
    city: string
}

export type CardTransactionState =
    | { type: 'pending' }
    | { type: 'settled'; clearedAt: number }

type CardTransactionCommon = {
    createdAt: number
    transactionAmount: FiatMoney | null // TODO @resetko-zeal make it not nullable once we have fiat currency dictionary in the app
    billingAmount: CryptoMoney
    merchant: MerchantInfo
    state: CardTransactionState
}

export type Reversal = {
    kind: 'Reversal'
    onChainTransactionHash: string
} & CardTransactionCommon

export type CardPayment = {
    kind: 'Payment'
} & (
    | {
          status: 'Approved' | 'Reversal'
          onChainTransactionHash: string
      }
    | {
          status: 'Declined'
          reason: DeclineReason
      }
) &
    CardTransactionCommon

export type DeclineReason =
    | 'InsufficientFunds'
    | 'IncorrectPin'
    | 'InvalidAmount'
    | 'PinEntryTriesExceeded'
    | 'IncorrectSecurityCode'
    | 'ExceedsApprovalAmountLimit'
    | 'Other'

export type Refund = {
    kind: 'Refund'
} & CardTransactionCommon

export type UnknownTransaction = {
    kind: 'Unknown'
    transactionAmount: FiatMoney | null
    billingAmount: CryptoMoney | null
    createdAt: number
    originalTransaction: unknown
}

export type CardTransaction =
    | CardPayment
    | Reversal
    | Refund
    | UnknownTransaction

export type CardSafeNotDeployed = { type: 'not_deployed' }
export type CardSafeDeployed = {
    type: 'deployed'
    address: Web3.address.Address
}

export type CardSafeCurrencyConfigured = {
    type: 'currency_configured'
    address: Web3.address.Address
    fiatCurrency: FiatCurrency
    cryptoCurrency: CryptoCurrency
}

export type CardSafeFullyConfigured = {
    type: 'fully_configured'
    address: Web3.address.Address
    fiatCurrency: FiatCurrency
    cryptoCurrency: CryptoCurrency
}

export type GnosisTermsType =
    | 'general-tos'
    | 'card-monavate-tos'
    | 'cashback-tos'

export type CardTermsType =
    | 'general_terms'
    | 'monavate_terms'
    | 'cashback_terms'

export type CardTermsRequiredForSignupType = Extract<
    CardTermsType,
    'general_terms' | 'monavate_terms'
>

type TermsCommon = { url: string; currentVersion: string; accepted: boolean }

export type CardTerms = {
    type: CardTermsType
} & TermsCommon

export type CardTermsRequiredForSignup = {
    type: CardTermsRequiredForSignupType
} & TermsCommon

export type CardSafeState =
    | CardSafeNotDeployed
    | CardSafeDeployed
    | CardSafeCurrencyConfigured
    | CardSafeFullyConfigured

export type GnosisPayPreKYCApprovedKycStatus =
    | 'terms_accepted_kyc_not_started'
    | 'kyc_started_documents_requested'
    | 'kyc_started_verification_in_progress'
    | 'kyc_started_resubmission_requested'
    | 'kyc_failed'

export type GnosisPayAccountConfigurationState =
    | GnosisPayAccountConfigurationStateFullyConfigured
    | GnosisPayAccountConfigurationStateInProgress

export type GnosisPayAccountConfigurationStateFullyConfigured = {
    type: 'fully_configured'
}

export type GnosisPayAccountConfigurationStateInProgressSOFRequired = {
    type: 'required_sof_verification'
}

export type GnosisPayAccountConfigurationStateInProgressPhoneNumberRequired = {
    type: 'required_verification_phone_number'
}

export type GnosisPayAccountConfigurationStateInProgressSOFPhoneNumberRequired =
    {
        type: 'required_sof_verification_and_verification_phone_number'
    }

export type GnosisPayAccountConfigurationStateInProgress =
    | GnosisPayAccountConfigurationStateInProgressPhoneNumberRequired
    | GnosisPayAccountConfigurationStateInProgressSOFRequired
    | GnosisPayAccountConfigurationStateInProgressSOFPhoneNumberRequired

export type GnosisPayPreKYCApprovedState =
    | {
          type: 'not_onboarded'
          state: 'terms_not_accepted'
          userId: string

          missingSignupTerms: CardTermsRequiredForSignup[]
          cardSafe: CardSafeState // FIXME :: @Nicvaniek Should be CardSafeNotDeployed | CardSafeDeployed but need to support all states until re-kyc is done
      }
    | {
          type: 'not_onboarded'
          state: GnosisPayPreKYCApprovedKycStatus
          userId: string

          cardSafe: CardSafeState // FIXME :: @Nicvaniek Should be CardSafeNotDeployed | CardSafeDeployed but need to support all states until re-kyc is done
      }

export type GnosisPayPostKYCApprovedState =
    | GnosisPayCardOrderPendingPaymentState
    | GnosisPayCardOrderReadyState
    | GnosisPayCardCreatedFromOrderState

export type GnosisPayAccountNotOnboardedState =
    | GnosisPayPreKYCApprovedState
    | GnosisPayKYCApprovedState
    | GnosisPayCardOrderPendingPaymentState
    | GnosisPayCardOrderReadyState
    | GnosisPayCardCreatedFromOrderState

export type GnosisPayCardOrderPendingPaymentState = {
    type: 'not_onboarded'
    state: 'card_order_pending_payment'
    userId: string

    cardOrderId: string
    shippingAddress: ResidentialAddress | null

    accountConfiguration: GnosisPayAccountConfigurationState
    residentialAddress: ResidentialAddress | null
    cardSafe: CardSafeState
}

export type GnosisPayCardOrderReadyState = {
    type: 'not_onboarded'
    state: 'card_order_ready'
    userId: string

    cardOrderId: string
    shippingAddress: ResidentialAddress | null

    accountConfiguration: GnosisPayAccountConfigurationState
    residentialAddress: ResidentialAddress | null
    cardSafe: CardSafeState
}

export type GnosisPayKYCApprovedState = {
    type: 'not_onboarded'
    state: 'kyc_approved'
    userId: string

    cardSafe: CardSafeState
    accountConfiguration: GnosisPayAccountConfigurationState
    residentialAddress: ResidentialAddress | null
}

export type GnosisPayCardCreatedFromOrderState = {
    type: 'not_onboarded'
    state: 'card_created_from_order'
    userId: string
    cardSafe: CardSafeState // CardSafeNotDeployed, CardSafeDeployed, and CardSafeCurrencyConfigured are still valid types for users who come from the Gnosis portal and start card ordering but don't finish it yet.
    country: CountryISOCode | null
    accountConfiguration: GnosisPayAccountConfigurationStateFullyConfigured
    notActivatedPhysicalCards: NonEmptyArray<NotActivatedPhysicalCard>
}

export type PendingCardBalance = {
    amount: FiatMoney
    lastTopUpTimestampMs: number
}

export type CardBalance = {
    total: FiatMoney
    totalInDefaultCurrency: FiatMoney | null

    spendable: FiatMoney
    pending: PendingCardBalance | null

    cashbackTokenBalance: CryptoMoney
    cashbackTokenBalanceInDefaultCurrency: FiatMoney | null
    cashBackRate: FXRate2<CryptoCurrency, FiatCurrency> | null
    cashBackPriceChange24H: PriceChange24H | null
}
type PhysicalCardType = 'physical'
type VirtualCardType = 'virtual'

export type CardType = PhysicalCardType | 'virtual'

export type CardState = NotActivatedCardState | ActivatedCardState

export type ActivatedCardState = { type: 'card_activated'; status: CardStatus }

export type NotActivatedCardState = { type: 'card_not_activated' }

export type Card = ActivatedCard | NotActivatedPhysicalCard

export type ActivatedCard = ActivatedVirtualCard | ActivatedPhysicalCard

type ActivatedVirtualCard = {
    id: string
    type: VirtualCardType
    state: ActivatedCardState
    lastFourDigits: string
}

export type ActivatedPhysicalCard = {
    id: string
    type: PhysicalCardType
    state: ActivatedCardState
    lastFourDigits: string
}

export type NotActivatedPhysicalCard = {
    id: string
    type: PhysicalCardType
    lastFourDigits: string
    state: NotActivatedCardState
}

export type GnosisPayAccountOnboardedStateResult = Result<
    | { type: 'keystore_not_eligible_for_silent_sign' }
    | { type: 'gnosisPayAccountState_not_in_onboarded_state' },
    {
        gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
        keyStore: CardSlientSignKeyStore
    }
>

export type GnosisPayOnboardedKycStatus =
    | 'not_started'
    | 'documents_requested'
    | 'verification_in_progress'
    | 'resubmission_requested'
    | 'failed'
    | 'approved'

export type GnosisPayAccountOnboardedState = {
    type: 'onboarded'
    selectedCard: ActivatedCard
    cardSafe: CardSafeFullyConfigured
    balance: CardBalance
    residentialAddress: ResidentialAddress | null
    // TODO :: @kate - there should be ActiveCard type instead of union, ask gnosis-pay team to provide it also array should be notNullableArray
    // https://zeal-hzn3129.slack.com/archives/C06R39S06J1/p1750952253099369
    activatedCards: {
        id: string
        type: 'virtual' | 'physical'
        activatedAt: string
        lastFourDigits: string
    }[]
    notActivatedPhysicalCards: NotActivatedPhysicalCard[]
    isCreatedViaZeal: boolean
    userId: string
    acceptedCashbackTerms: boolean
    fullName: string | null
    kycStatus: GnosisPayOnboardedKycStatus
}

export type CardDetails = {
    cvv: string
    pan: string
    expiryYear: string
    expiryMonth: string
}

export type CardsMap = Record<string, ActivatedCard | null>

export type CardStatus = Frozen | Active | Cancelled

export type Frozen = 'frozen'
export type Active = 'active'
export type Cancelled = 'cancelled'

export type GnosisPayAccountState2 =
    | GnosisPayAccountNotOnboardedState
    | GnosisPayAccountOnboardedState

export type CardTopUpRequest = {
    sender: Account
    amount: CryptoMoney
    cardSafeAddress: Web3.address.Address
}

export type TopUpCardFromEarnRequest = {
    earnOwner: Account
    taker: DeployedTaker
    takerApyMap: TakerApyMap
    investmentAssetAmount: CryptoMoney
    userCurrencyAmount: Money2
    defaultCurrencyAmount: FiatMoney | null
    earnWithdrawalTransaction: EthSendTransaction
    swapRoute: SwapRoute
}

export type CardDelayRelayTransactionRequest = {
    cardSafe: Web3.address.Address
    network: Network

    transaction: MetaTransactionData
    relayTransaction: MetaTransactionData
    relayMessage: EthSignTypedDataV4
    relaySalt: Hexadecimal.Hexadecimal
    signature: Hexadecimal.Hexadecimal
}

export type ResidentialAddress = {
    address1: string
    address2: string | null
    city: string
    postalCode: string
    country: CountryISOCode
}

// Card Top Up types
export type SubmittedEOASendCardTopUp = {
    type: 'send_eoa'
    amount: CryptoMoney
    actionSource: InternalTransactionActionSource
    keyStore: EOA
    startedAtMs: number
    cardSafeAddress: Web3.address.Address
    state:
        | CardTopUpWaitingForTransaction
        | CardTopUpWaitingForIndexerSingleTx
        | CardTopUpCompletedWithSingleTx
        | CardTopUpFailedTransaction
}

export type SubmittedSafeSendCardTopUp = {
    type: 'send_safe'
    amount: CryptoMoney
    keyStore: Safe4337
    actionSource: InternalTransactionActionSource
    startedAtMs: number
    cardSafeAddress: Web3.address.Address
    state:
        | CardTopUpWaitingForUserOperation
        | CardTopUpWaitingForIndexerSingleTx
        | CardTopUpCompletedWithSingleTx
        | CardTopUpFailedUserOperation
}

export type SubmittedEOAEarnEUReCardTopUp = {
    type: 'earn_eoa_eure_withdrawal'
    actionSource: InternalTransactionActionSource
    amount: CryptoMoney
    keyStore: EOA
    startedAtMs: number
    cardSafeAddress: Web3.address.Address
    takerType: TakerType
    state:
        | CardTopUpWaitingForTransaction
        | CardTopUpWaitingForIndexerMultipleTx
        | CardTopUpCompletedWithMultipleTx
        | CardTopUpFailedTransaction
}

export type SubmittedSafeEarnEUReCardTopUp = {
    type: 'earn_safe_eure_withdrawal'
    actionSource: InternalTransactionActionSource
    amount: CryptoMoney
    keyStore: Safe4337
    startedAtMs: number
    cardSafeAddress: Web3.address.Address
    takerType: TakerType
    state:
        | CardTopUpWaitingForUserOperation
        | CardTopUpWaitingForIndexerSingleTx
        | CardTopUpCompletedWithSingleTx
        | CardTopUpFailedUserOperation
}

export type SubmittedEOASendNativeCardTopUp = {
    type: 'swap_eoa_native_send_transaction'
    fromAmount: CryptoMoney
    toAmount: CryptoMoney
    quoteRequestHash: Hexadecimal.Hexadecimal
    actionSource: InternalTransactionActionSource
    keyStore: EOA
    startedAtMs: number
    cardSafeAddress: Web3.address.Address
    state:
        | CardTopUpWaitingForTransaction
        | CardTopUpWaitingForSwap
        | CardTopUpWaitingForIndexerMultipleTx
        | CardTopUpCompletedWithMultipleTx
        | CardTopUpFailedTransaction
        | CardTopUpFailedBungee
}

export type SubmittedSafeSendNativeCardTopUp = {
    type: 'swap_safe_native_send_transaction'
    fromAmount: CryptoMoney
    toAmount: CryptoMoney
    quoteRequestHash: Hexadecimal.Hexadecimal
    actionSource: InternalTransactionActionSource
    startedAtMs: number
    cardSafeAddress: Web3.address.Address
    keyStore: Safe4337
    state:
        | CardTopUpWaitingForUserOperation
        | CardTopUpWaitingForSwap
        | CardTopUpWaitingForIndexerMultipleTx
        | CardTopUpCompletedWithMultipleTx
        | CardTopUpFailedUserOperation
        | CardTopUpFailedBungee
}

export type SubmittedSwapSignTypedDataCardTopUp = {
    type: 'swap_sign_typed_data'
    fromAmount: CryptoMoney
    toAmount: CryptoMoney
    quoteRequestHash: Hexadecimal.Hexadecimal
    actionSource: InternalTransactionActionSource
    startedAtMs: number
    cardSafeAddress: Web3.address.Address
    keyStore: SigningKeyStore
    state:
        | CardTopUpWaitingForSwap
        | CardTopUpWaitingForIndexerMultipleTx
        | CardTopUpWaitingForIndexerSingleTx
        | CardTopUpCompletedWithMultipleTx
        | CardTopUpCompletedWithSingleTx
        | CardTopUpFailedBungee
}

export type SubmittedEarnSignTypedDataCardTopUp = {
    type: 'earn_sign_typed_data'
    takerType: TakerType
    toAmount: CryptoMoney
    quoteRequestHash: Hexadecimal.Hexadecimal
    actionSource: InternalTransactionActionSource
    startedAtMs: number
    cardSafeAddress: Web3.address.Address
    keyStore: SigningKeyStore
    state:
        | CardTopUpWaitingForSwap
        | CardTopUpWaitingForIndexerMultipleTx
        | CardTopUpCompletedWithMultipleTx
        | CardTopUpFailedBungee
}

export type CardTopUpCompletedWithSingleTx = {
    type: 'completed_with_single_tx'
    completedTxHash: Hexadecimal.Hexadecimal
    completedAtMs: number
}

export type CardTopUpCompletedWithMultipleTx = {
    type: 'completed_with_multiple_tx'
    completedAtMs: number
}

export type CardTopUpFailedTransaction = {
    type: 'failed_tx'
    transaction: SubmitedTransactionFailed
}

export type CardTopUpFailedUserOperation = {
    type: 'failed_user_operation'
    userOperation: SubmittedUserOperationFailed | SubmittedUserOperationRejected
}

export type CardTopUpFailedBungee = {
    type: 'failed_bungee'
    quoteRequestHash: Hexadecimal.Hexadecimal
    state: 'refunded' | 'expired' | 'cancelled'
}

export type CardTopUpWaitingForTransaction = {
    type: 'waiting_for_transaction'
    submittedTransaction: SubmitedTransaction
}

export type CardTopUpWaitingForUserOperation = {
    type: 'waiting_for_user_operation'
    submittedUserOperation: SubmittedUserOperation
}

export type CardTopUpWaitingForSwap = {
    type: 'waiting_for_swap'
}

export type CardTopUpWaitingForIndexerSingleTx = {
    type: 'waiting_for_indexer_single_tx'
    txHash: Hexadecimal.Hexadecimal
}

export type CardTopUpWaitingForIndexerMultipleTx = {
    type: 'waiting_for_indexer_multiple_tx'
    finalTxHash: Hexadecimal.Hexadecimal
}

export type CardTopUpProgressState =
    | CardTopUpCompletedWithSingleTx
    | CardTopUpCompletedWithMultipleTx
    | CardTopUpFailedTransaction
    | CardTopUpFailedUserOperation
    | CardTopUpFailedBungee
    | CardTopUpWaitingForTransaction
    | CardTopUpWaitingForUserOperation
    | CardTopUpWaitingForSwap
    | CardTopUpWaitingForIndexerSingleTx
    | CardTopUpWaitingForIndexerMultipleTx

export type SubmittedCardTopUp =
    | SubmittedEOASendCardTopUp
    | SubmittedEarnSignTypedDataCardTopUp
    | SubmittedSwapSignTypedDataCardTopUp
    | SubmittedEOASendNativeCardTopUp
    | SubmittedSafeSendCardTopUp
    | SubmittedSafeSendNativeCardTopUp
    | SubmittedEOAEarnEUReCardTopUp
    | SubmittedSafeEarnEUReCardTopUp
