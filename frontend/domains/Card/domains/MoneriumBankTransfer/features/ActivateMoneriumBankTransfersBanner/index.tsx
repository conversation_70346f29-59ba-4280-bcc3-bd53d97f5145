import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { fetchDepositAccountDetails } from '@zeal/domains/Card/domains/MoneriumBankTransfer/api/fetchDepositAccountDetails'
import { fetchMoneriumEligibility } from '@zeal/domains/Card/domains/MoneriumBankTransfer/api/fetchMoneriumEligibility'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Layout } from './Layout'
import { Modal, State } from './Modal'

type Props = {
    cardReadOnlySigner: Account
    keyStore: CardSlientSignKeyStore

    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap
    keyStoreMap: KeyStoreMap
    installationCampaign: string | null
    portfolioMap: PortfolioMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    onMsg: (msg: Msg) => void
}

type Msg = Extract<
    MsgOf<typeof Modal>,
    {
        type:
            | 'on_4337_auto_gas_token_selection_clicked'
            | 'on_4337_gas_currency_selected'
            | 'on_activate_existing_monerium_account_click'
            | 'on_monerium_deposit_success_go_to_wallet_clicked'
            | 'on_switch_bank_transfer_provider_clicked'
            | 'on_create_smart_wallet_clicked'
            | 'on_card_disconnected'
            | 'on_card_import_on_import_keys_clicked'
            | 'on_card_imported_success_animation_complete'
            | 'on_onboarded_card_imported_success_animation_complete'
    }
>

const fetch = async ({
    signal,
    keyStore,
    sessionPassword,
    cardReadOnlySigner,
}: {
    cardReadOnlySigner: Account
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    signal?: AbortSignal
}) => {
    const [eligibility, accountDetails] = await Promise.all([
        fetchMoneriumEligibility({
            signal,
            sessionPassword,
            keyStore,
            readonlySignerAddress: cardReadOnlySigner.address,
        }),
        fetchDepositAccountDetails({
            signal,
            sessionPassword,
            keyStore,
            readonlySignerAddress: cardReadOnlySigner.address,
        }),
    ])
    return {
        eligibility,
        accountDetails,
    }
}

export const ActivateMoneriumBankTransfersBanner = ({
    onMsg,
    accountsMap,
    cardReadOnlySigner,
    defaultCurrencyConfig,
    currencyHiddenMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    installationCampaign,
    keyStore,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    sessionPassword,
    cardConfig,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            sessionPassword,
            keyStore,
            cardReadOnlySigner,
        },
    })

    const [state, setState] = useState<State>({ type: 'closed' })

    return (
        <>
            <Layout
                loadable={loadable}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_monerium_enable_banner_clicked':
                            setState({ type: 'monerium_deposit' })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg.type)
                    }
                }}
            />
            <Modal
                state={state}
                currencyHiddenMap={currencyHiddenMap}
                installationCampaign={installationCampaign}
                cardConfig={cardConfig}
                defaultCurrencyConfig={defaultCurrencyConfig}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                installationId={installationId}
                keyStoreMap={keyStoreMap}
                portfolioMap={portfolioMap}
                sessionPassword={sessionPassword}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'monerium_on_card_disconnected':
                        case 'on_monerium_sign_delay_relay_success_close_clicked':
                        case 'monerium_deposit_on_enable_card_clicked':
                            setState({ type: 'closed' })
                            break

                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'on_activate_existing_monerium_account_click':
                        case 'on_monerium_deposit_success_go_to_wallet_clicked':
                        case 'on_switch_bank_transfer_provider_clicked':
                        case 'on_create_smart_wallet_clicked':
                        case 'on_card_disconnected':
                        case 'on_card_import_on_import_keys_clicked':
                        case 'on_card_imported_success_animation_complete':
                        case 'on_onboarded_card_imported_success_animation_complete':
                            setState({ type: 'closed' })
                            onMsg(msg)
                            break

                        case 'on_monerium_successfully_activated':
                            setLoadable({
                                type: 'loading',
                                params: loadable.params,
                            })
                            break

                        case 'on_user_not_eligible_for_monerium':
                            throw new ImperativeError(
                                '[Monerium Banner] Impossible state - user must be eligible for monerium at this point'
                            )
                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
