import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import {
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { MoneriumDepositAccountDetails } from '@zeal/domains/Card/domains/MoneriumBankTransfer'
import { GnosisPayStateValidator } from '@zeal/domains/Card/domains/MoneriumBankTransfer/features/GnosisPayStateValidator'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { MoneriumSetupFlowEnteredEvent } from '@zeal/domains/UserEvents'

import { AwaitMoneriumDeposit } from './AwaitMoneriumDeposit'

type Props = {
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationCampaign: string | null
    location: MoneriumSetupFlowEnteredEvent['location']
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof GnosisPayStateValidator>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_activate_existing_monerium_account_click'
                  | 'monerium_deposit_on_enable_card_clicked'
                  | 'on_user_not_eligible_for_monerium'
                  | 'on_monerium_successfully_activated'
                  | 'monerium_on_card_disconnected'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_monerium_sign_delay_relay_success_close_clicked'
                  | 'on_card_disconnected'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
          }
      >
    | MsgOf<typeof AwaitMoneriumDeposit>

type State =
    | { type: 'gnosis_pay_state_validator' }
    | {
          type: 'show_account_details'
          accountDetails: MoneriumDepositAccountDetails
      }

const getSigningKeyStore = (
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig,
    keyStoreMap: KeyStoreMap
): SigningKeyStore => {
    const keyStore = getKeyStore({
        keyStoreMap,
        address: cardConfig.readonlySignerAddress,
    })

    switch (keyStore.type) {
        case 'track_only':
            throw new ImperativeError(
                'Found track_only key store for card signer in Monerium deposit flow'
            )
        case 'private_key_store':
        case 'secret_phrase_key':
        case 'safe_4337':
        case 'trezor':
        case 'ledger':
            return keyStore
        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}

export const Deposit = ({
    onMsg,
    cardConfig,
    installationId,
    portfolioMap,
    accountsMap,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationCampaign,
    currencyHiddenMap,
    sessionPassword,
    location,
    defaultCurrencyConfig,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'gnosis_pay_state_validator',
    })

    const cardReadonlySigner = accountsMap[cardConfig.readonlySignerAddress]
    const keyStore = getSigningKeyStore(cardConfig, keyStoreMap)

    switch (state.type) {
        case 'gnosis_pay_state_validator':
            return (
                <GnosisPayStateValidator
                    cardReadonlySigner={cardReadonlySigner}
                    keyStore={keyStore}
                    installationCampaign={installationCampaign}
                    accountsMap={accountsMap}
                    cardConfig={cardConfig}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    currencyHiddenMap={currencyHiddenMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    location={location}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_activate_existing_monerium_account_click':
                            case 'monerium_deposit_on_enable_card_clicked':
                            case 'on_user_not_eligible_for_monerium':
                            case 'on_monerium_successfully_activated':
                            case 'monerium_on_card_disconnected':
                            case 'on_create_smart_wallet_clicked':
                            case 'on_monerium_sign_delay_relay_success_close_clicked':
                            case 'on_card_disconnected':
                            case 'on_card_import_on_import_keys_clicked':
                            case 'on_card_imported_success_animation_complete':
                            case 'on_onboarded_card_imported_success_animation_complete':
                                onMsg(msg)
                                break
                            case 'on_monerium_enabled_card_signer_is_receiver':
                                setState({
                                    type: 'show_account_details',
                                    accountDetails: msg.accountDetails,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'show_account_details':
            return (
                <AwaitMoneriumDeposit
                    cardConfig={cardConfig}
                    accountDetails={state.accountDetails}
                    cardReadonlySigner={cardReadonlySigner}
                    installationId={installationId}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
