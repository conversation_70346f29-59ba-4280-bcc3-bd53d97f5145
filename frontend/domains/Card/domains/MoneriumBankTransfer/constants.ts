import { Country } from '@zeal/domains/Country'
import { COUNTRIES_MAP, EEA_COUNTRIES } from '@zeal/domains/Country/constants'
import {
    FIAT_CURRENCIES,
    GNOSIS_EURE_V2,
} from '@zeal/domains/Currency/constants'
import { NetworkHexId } from '@zeal/domains/Network'
import { GNOSIS } from '@zeal/domains/Network/constants'

export const MONERIUM_TNC_URL =
    'https://monerium.com/policies/personal-terms-of-service/'
export const MONERIUM_URL = 'https://monerium.com/financial-information'

export const MONERIUM_SUPPORTED_NETWORKS_MAP: Record<NetworkHexId, 'gnosis'> = {
    [GNOSIS.hexChainId]: 'gnosis',
}

export const DEFAULT_MONERIUM_CRYPTO_CURRENCY = GNOSIS_EURE_V2
export const DEFAULT_MONERIUM_FIAT_CURRENCY = FIAT_CURRENCIES.EUR

export const MONERIUM_SUPPORTED_COUNTRIES: Country[] = [
    ...EEA_COUNTRIES,
    COUNTRIES_MAP['CH'].code,
].map((countryCode) => COUNTRIES_MAP[countryCode])

export const MAX_LENGTH_ONELINE_NAME = 25
