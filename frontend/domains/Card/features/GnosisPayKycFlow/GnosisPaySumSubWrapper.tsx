import SumSubWebSdk from '@sumsub/websdk-react'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { UnexpectedResultFailureError } from '@zeal/toolkit/Result'

import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseSumsubWebSdkMessage } from '@zeal/domains/KYC/parsers/parseSumsubSdkMessage'
import { GnosisPayOnboardingLocation } from '@zeal/domains/UserEvents'

type Props = {
    sumSubAccessToken: string
    installationId: string
    location: GnosisPayOnboardingLocation
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'on_gnosis_pay_sumsub_kyc_completed'
      }

export const GnosisPaySumSubWrapper = ({
    onMsg,
    sumSubAccessToken,
    installationId: _, // needed on Native version
    location: _l, // needed on Native version
}: Props) => {
    return (
        <Screen
            background="default"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
            />

            <ScrollContainer withFloatingActions={false}>
                <SumSubWebSdk
                    accessToken={sumSubAccessToken}
                    onMessage={(msg: unknown) => {
                        // TODO :: @Nicvaniek - we're not getting any idCheck.onStepCompleted events for the GnoPay flow so we cannot add events for Web
                        const parsed = parseSumsubWebSdkMessage(msg)

                        switch (parsed.type) {
                            case 'Failure':
                                captureError(
                                    new UnexpectedResultFailureError(
                                        'Failed to parse SumSub web sdk message',
                                        parsed.reason
                                    )
                                )
                                break
                            case 'Success':
                                switch (parsed.data) {
                                    case 'idCheck.onApplicantSubmitted':
                                    case 'idCheck.onApplicantResubmitted':
                                        onMsg({
                                            type: 'on_gnosis_pay_sumsub_kyc_completed',
                                        })
                                        break
                                    case 'idCheck.actionCompleted':
                                    case 'idCheck.applicantReviewComplete':
                                    case 'idCheck.applicantStatus':
                                    case 'idCheck.livenessCompleted':
                                    case 'idCheck.moduleResultPresented':
                                    case 'idCheck.onActionSubmitted':
                                    case 'idCheck.onAgreementSigned':
                                    case 'idCheck.onApplicantActionCompleted':
                                    case 'idCheck.onApplicantActionLoaded':
                                    case 'idCheck.onApplicantActionSubmitted':
                                    case 'idCheck.onApplicantLoaded':
                                    case 'idCheck.onApplicantStatusChanged':
                                    case 'idCheck.onError':
                                    case 'idCheck.onInitialized':
                                    case 'idCheck.onLanguageChanged':
                                    case 'idCheck.onNavigationUiControlsStateChanged':
                                    case 'idCheck.onReady':
                                    case 'idCheck.onResize':
                                    case 'idCheck.onStepCompleted':
                                    case 'idCheck.onStepInitiated':
                                    case 'idCheck.onUploadError':
                                    case 'idCheck.onUploadWarning':
                                    case 'idCheck.onUserAction':
                                    case 'idCheck.onVideoIdentCallStarted':
                                    case 'idCheck.onVideoIdentCompleted':
                                    case 'idCheck.onVideoIdentModeratorJoined':
                                    case 'idCheck.restoreScrollPosition':
                                    case 'idCheck.stepCompleted':
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(parsed.data)
                                }
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(parsed)
                        }
                    }}
                    expirationHandler={() => {
                        // TODO @Nicvaniek: Refresh token if this becomes a problem
                        captureError(
                            new ImperativeError(
                                'SumSub access token expired [web SDK]'
                            )
                        )
                        return sumSubAccessToken
                    }}
                />
            </ScrollContainer>
        </Screen>
    )
}
