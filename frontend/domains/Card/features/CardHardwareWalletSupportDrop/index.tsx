import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    variant: 'closable' | 'not_closable'

    installationId: string
    cardReadonlySignerAddress: Web3.address.Address
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationCampaign: string | null
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof Layout>, { type: 'on_card_disconnected' | 'close' }>
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_create_smart_wallet_clicked'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
          }
      >

// TODO :: @Nicvaniek kill once we have no more hardware wallets as card owners in Zeal
export const HardwareWalletSupportDrop = ({
    onMsg,
    accountsMap,
    cardReadonlySignerAddress,
    variant,
    currencyHiddenMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    keystoreMap,
    sessionPassword,
    installationCampaign,
    installationId,
    defaultCurrencyConfig,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    return (
        <>
            <Layout
                variant={variant}
                cardReadonlySignerAddress={cardReadonlySignerAddress}
                accountsMap={accountsMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_card_disconnected':
                        case 'close':
                            onMsg(msg)
                            break
                        case 'on_hw_support_drop_add_new_owner_clicked':
                            setModal({ type: 'import_new_card_owner_owner' })
                            break
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                state={modal}
                installationId={installationId}
                installationCampaign={installationCampaign}
                accountsMap={accountsMap}
                keystoreMap={keystoreMap}
                portfolioMap={portfolioMap}
                currencyHiddenMap={currencyHiddenMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                sessionPassword={sessionPassword}
                defaultCurrencyConfig={defaultCurrencyConfig}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_gnosis_pay_not_available_accepted':
                            setModal({ type: 'closed' })
                            break
                        case 'on_gnosis_pay_account_created':
                        case 'on_gnosis_pay_onboarding_flow_closed':
                        case 'on_gnosis_pay_kyc_submitted_animation_complete':
                            throw new ImperativeError(
                                'Got impossible msg in Card Hardware Wallet Support Drop',
                                { msg: msg.type }
                            )
                        case 'on_create_smart_wallet_clicked':
                        case 'on_card_import_on_import_keys_clicked':
                        case 'on_card_imported_success_animation_complete':
                        case 'on_onboarded_card_imported_success_animation_complete':
                            onMsg(msg)
                            setModal({ type: 'closed' })
                            break
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
