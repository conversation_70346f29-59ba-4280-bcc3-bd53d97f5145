import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { uuid } from '@zeal/toolkit/Crypto'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { LoadingLayout } from './LoadingLayout'
import { Modal, State as ModalState } from './Modal'

import { fetchSourceBalancesAndCurrencies } from '../api/fetchSourceBalancesAndCurrencies'
import { Flow } from '../Flow'

type Props = {
    feePresetMap: FeePresetMap
    earnTakerMetrics: EarnTakerMetrics
    initialTopUpAmount: CryptoMoney | null
    initialSenderAddress: Web3.address.Address
    accountsMap: AccountsMap

    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    installationId: string
    sessionPassword: string
    portfolioMap: PortfolioMap
    customCurrencyMap: CustomCurrencyMap
    currencyPinMap: CurrencyPinMap

    keyStoreMap: KeyStoreMap
    gasCurrencyPresetMap: GasCurrencyPresetMap

    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof Flow>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'track_wallet_clicked'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_card_top_up_success'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
                  | 'on_recharge_configured'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'track_wallet_clicked'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_account_create_request'
          }
      >

const calculateInitialAmount = (
    initialTopUpAmount: CryptoMoney | null
): string | null => {
    return initialTopUpAmount
        ? toFixedWithFraction(
              initialTopUpAmount.amount,
              initialTopUpAmount.currency.fraction
          )
        : null
}

export const DataLoader = ({
    feePresetMap,
    earnTakerMetrics,
    cardConfig,
    currencyHiddenMap,
    defaultCurrencyConfig,
    initialTopUpAmount,
    customCurrencyMap,
    currencyPinMap,
    portfolioMap,
    keyStoreMap,
    gasCurrencyPresetMap,
    networkMap,
    networkRPCMap,
    installationId,
    accountsMap,
    sessionPassword,
    initialSenderAddress,
    onMsg,
}: Props) => {
    const [amount, setAmount] = useState<string | null>(() =>
        calculateInitialAmount(initialTopUpAmount)
    )

    useEffect(() => {
        postUserEvent({
            type: 'CardTopupFlowEnteredEvent',
            installationId,
        })
    }, [installationId])

    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    const [loadable, setLoadable] = useLoadableData(
        fetchSourceBalancesAndCurrencies,
        {
            type: 'loading',
            params: {
                senderAddress: initialSenderAddress,
                currencyHiddenMap,
                defaultCurrencyConfig,
                installationId,
                networkMap,
                networkRPCMap,
                cardConfig,
                featureCacheKey: uuid(),
            },
        }
    )

    const initialTopUpCurrency = initialTopUpAmount?.currency || null

    return (
        <>
            {(() => {
                switch (loadable.type) {
                    case 'loading':
                        return (
                            <LoadingLayout
                                amount={amount}
                                initialTopUpCurrency={initialTopUpCurrency}
                                sender={
                                    accountsMap[loadable.params.senderAddress]
                                }
                                accountsMap={accountsMap}
                                networkMap={networkMap}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        case 'on_sender_account_clicked':
                                            setModal({ type: 'select_sender' })
                                            break
                                        case 'on_amount_changed':
                                            setAmount(msg.amount)
                                            break
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'error':
                        return (
                            <>
                                <LoadingLayout
                                    amount={amount}
                                    initialTopUpCurrency={initialTopUpCurrency}
                                    sender={
                                        accountsMap[
                                            loadable.params.senderAddress
                                        ]
                                    }
                                    accountsMap={accountsMap}
                                    networkMap={networkMap}
                                    onMsg={(msg) => {
                                        switch (msg.type) {
                                            case 'close':
                                                onMsg(msg)
                                                break
                                            case 'on_sender_account_clicked':
                                                setModal({
                                                    type: 'select_sender',
                                                })
                                                break
                                            case 'on_amount_changed':
                                                setAmount(msg.amount)
                                                break
                                            default:
                                                return notReachable(msg)
                                        }
                                    }}
                                />
                                <AppErrorPopup
                                    error={parseAppError(loadable.error)}
                                    installationId={installationId}
                                    onMsg={(msg) => {
                                        switch (msg.type) {
                                            case 'close':
                                                onMsg(msg)
                                                break
                                            case 'try_again_clicked':
                                                setLoadable({
                                                    type: 'loading',
                                                    params: loadable.params,
                                                })
                                                break
                                            default:
                                                return notReachable(msg)
                                        }
                                    }}
                                />
                            </>
                        )
                    case 'loaded':
                        return (
                            <Flow
                                portfolioMap={portfolioMap}
                                feePresetMap={feePresetMap}
                                earnTakerMetrics={earnTakerMetrics}
                                supportedTopUpCurrenciesInPortfolio={
                                    loadable.data
                                        .supportedTopUpCurrenciesInPortfolio
                                }
                                senderPortfolio={loadable.data.serverPortfolio}
                                senderEarn={loadable.data.earn}
                                cardOwnerEarn={loadable.data.cardOwnerEarn}
                                cardBalance={loadable.data.cardBalance}
                                initialTopUpCurrency={initialTopUpCurrency}
                                initialTopUpAmount={amount}
                                installationId={installationId}
                                senderAddress={loadable.params.senderAddress}
                                cardConfig={cardConfig}
                                accountsMap={accountsMap}
                                networkMap={networkMap}
                                networkRPCMap={networkRPCMap}
                                keyStoreMap={keyStoreMap}
                                currencyHiddenMap={currencyHiddenMap}
                                sessionPassword={sessionPassword}
                                gasCurrencyPresetMap={gasCurrencyPresetMap}
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                currencyPinMap={currencyPinMap}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'on_sender_account_clicked':
                                            setModal({ type: 'select_sender' })
                                            break
                                        case 'close':
                                        case 'on_4337_auto_gas_token_selection_clicked':
                                        case 'on_4337_gas_currency_selected':
                                        case 'import_keys_button_clicked':
                                        case 'on_select_rpc_click':
                                        case 'on_rpc_change_confirmed':
                                        case 'on_card_top_up_success':
                                        case 'on_predefined_fee_preset_selected':
                                        case 'on_usd_taker_metrics_loaded':
                                        case 'on_eur_taker_metrics_loaded':
                                        case 'on_chf_taker_metrics_loaded':
                                            onMsg(msg)
                                            break
                                        case 'on_recharge_configured':
                                            setLoadable({
                                                type: 'loaded',
                                                params: loadable.params,
                                                data: {
                                                    ...loadable.data,
                                                    cardOwnerEarn: msg.earn,
                                                },
                                            })
                                            onMsg(msg)
                                            break
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )
                    default:
                        return notReachable(loadable)
                }
            })()}
            <Modal
                state={modal}
                selectedAccount={accountsMap[loadable.params.senderAddress]}
                keyStoreMap={keyStoreMap}
                installationId={installationId}
                currencyHiddenMap={currencyHiddenMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                defaultCurrencyConfig={defaultCurrencyConfig}
                sessionPassword={sessionPassword}
                accountsMap={accountsMap}
                portfolioMap={portfolioMap}
                cardConfig={cardConfig}
                customCurrencyMap={customCurrencyMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_sender_selected':
                            setModal({ type: 'closed' })
                            setLoadable({
                                type: 'loading',
                                params: {
                                    ...loadable.params,
                                    senderAddress: msg.account.address,
                                },
                            })
                            break
                        case 'track_wallet_clicked':
                        case 'add_wallet_clicked':
                        case 'hardware_wallet_clicked':
                        case 'safe_wallet_clicked':
                        case 'recover_safe_wallet_clicked':
                        case 'on_account_create_request':
                            onMsg(msg)
                            break
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
