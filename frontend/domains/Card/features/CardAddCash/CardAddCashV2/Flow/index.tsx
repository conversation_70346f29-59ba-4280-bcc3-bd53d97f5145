import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardBalance,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { Earn, EarnTakerMetrics } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Form } from './Form'
import { Submitter } from './Submitter'

import { CardTopUpRequest } from '../types'

type Props = {
    portfolioMap: PortfolioMap
    feePresetMap: FeePresetMap
    earnTakerMetrics: EarnTakerMetrics
    initialTopUpCurrency: CryptoCurrency | null
    initialTopUpAmount: string | null
    senderPortfolio: ServerPortfolio2
    cardBalance: CardBalance
    senderEarn: Earn
    cardOwnerEarn: Earn
    supportedTopUpCurrenciesInPortfolio: CryptoCurrency[]
    senderAddress: Web3.address.Address

    accountsMap: AccountsMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    installationId: string
    sessionPassword: string
    currencyPinMap: CurrencyPinMap
    keyStoreMap: KeyStoreMap

    gasCurrencyPresetMap: GasCurrencyPresetMap

    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Form>,
          {
              type:
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_sender_account_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
                  | 'on_recharge_configured'
          }
      >
    | MsgOf<typeof Submitter>

type State =
    | { type: 'form' }
    | { type: 'submitter'; cardTopupRequest: CardTopUpRequest }

export const Flow = ({
    portfolioMap,
    feePresetMap,
    earnTakerMetrics,
    accountsMap,
    supportedTopUpCurrenciesInPortfolio,
    senderPortfolio,
    senderEarn,
    cardOwnerEarn,
    initialTopUpCurrency,
    initialTopUpAmount,
    cardBalance,
    cardConfig,
    currencyHiddenMap,
    defaultCurrencyConfig,
    gasCurrencyPresetMap,
    sessionPassword,
    senderAddress,
    installationId,
    currencyPinMap,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'form' })

    switch (state.type) {
        case 'form':
            return (
                <Form
                    cardOwnerEarn={cardOwnerEarn}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    feePresetMap={feePresetMap}
                    earnTakerMetrics={earnTakerMetrics}
                    initialTopUpCurrency={initialTopUpCurrency}
                    initialTopUpAmount={initialTopUpAmount}
                    supportedTopUpCurrenciesInPortfolio={
                        supportedTopUpCurrenciesInPortfolio
                    }
                    senderPortfolio={senderPortfolio}
                    senderEarn={senderEarn}
                    cardBalance={cardBalance}
                    currencyPinMap={currencyPinMap}
                    accountsMap={accountsMap}
                    cardConfig={cardConfig}
                    currencyHiddenMap={currencyHiddenMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    senderAddress={senderAddress}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_select_rpc_click':
                            case 'on_rpc_change_confirmed':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'import_keys_button_clicked':
                            case 'on_sender_account_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_usd_taker_metrics_loaded':
                            case 'on_eur_taker_metrics_loaded':
                            case 'on_chf_taker_metrics_loaded':
                            case 'on_recharge_configured':
                                onMsg(msg)
                                break

                            case 'on_topup_clicked':
                                setState({
                                    type: 'submitter',
                                    cardTopupRequest: msg.topUpRequest,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        case 'submitter':
            return (
                <Submitter
                    cardOwnerEarn={cardOwnerEarn}
                    keyStoreMap={keyStoreMap}
                    cardConfig={cardConfig}
                    cardTopUpRequest={state.cardTopupRequest}
                    accountsMap={accountsMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )

        default:
            return notReachable(state)
    }
}
