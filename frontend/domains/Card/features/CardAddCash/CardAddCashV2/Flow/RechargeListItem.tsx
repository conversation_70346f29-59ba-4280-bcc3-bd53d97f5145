import { FormattedMessage } from 'react-intl'

import { Avatar as UIAvatar } from '@zeal/uikit/Avatar'
import { BoldThickRechargeLightning } from '@zeal/uikit/Icon/BoldThickRechargeLightning'
import { ListItemButton } from '@zeal/uikit/ListItem'
import { Toggle } from '@zeal/uikit/Toggle'

import { notReachable } from '@zeal/toolkit'

import {
    CardSlientSignKeyStore,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { CardRecharge, Earn } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'

type Msg = {
    type: 'on_reacharge_list_item_click'
    keyStore: CardSlientSignKeyStore
}

type Props = {
    keyStoreMap: KeyStoreMap
    cardOwnerEarn: Earn
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    disabled: boolean
    onMsg: (msg: Msg) => void
}

export const RechargeListItem = ({
    keyStoreMap,
    cardOwnerEarn,
    cardConfig,
    disabled,
    onMsg,
}: Props) => {
    const keyStore = getKeyStore({
        address: cardConfig.readonlySignerAddress,
        keyStoreMap,
    })

    const recharge = ((): CardRecharge => {
        switch (cardOwnerEarn.type) {
            case 'configured':
                return cardOwnerEarn.cardRecharge
            case 'not_configured':
                return {
                    type: 'recharge_disabled',
                }
            /* istanbul ignore next */
            default:
                return notReachable(cardOwnerEarn)
        }
    })()

    switch (keyStore?.type) {
        case 'track_only':
        case 'ledger':
        case 'trezor':
            return null
        case 'private_key_store':
        case 'secret_phrase_key':
        case 'safe_4337': {
            return (
                <ListItemButton
                    aria-current={false}
                    variant="default"
                    background="surface"
                    onClick={() => {
                        onMsg({
                            type: 'on_reacharge_list_item_click',
                            keyStore,
                        })
                    }}
                    disabled={disabled}
                    avatar={({ size }) => {
                        switch (recharge.type) {
                            case 'recharge_disabled':
                                return (
                                    <UIAvatar
                                        size={size}
                                        variant="round"
                                        backgroundColor="gray100"
                                    >
                                        <BoldThickRechargeLightning
                                            size={size}
                                            color="gray20"
                                        />
                                    </UIAvatar>
                                )
                            case 'recharge_enabled':
                                return (
                                    <UIAvatar
                                        size={size}
                                        variant="round"
                                        backgroundColor="gray100"
                                    >
                                        <BoldThickRechargeLightning
                                            size={size}
                                            color="teal40"
                                        />
                                    </UIAvatar>
                                )
                            /* istanbul ignore next */
                            default:
                                return notReachable(recharge)
                        }
                    }}
                    primaryText={(() => {
                        switch (recharge.type) {
                            case 'recharge_disabled':
                                return (
                                    <FormattedMessage
                                        id="earn.card-recharge.disabled.list-item.title"
                                        defaultMessage="Auto-recharge disabled"
                                    />
                                )
                            case 'recharge_enabled':
                                return (
                                    <FormattedMessage
                                        id="earn.card-recharge.enabled.list-item.title"
                                        defaultMessage="Auto-recharge enabled"
                                    />
                                )
                            /* istanbul ignore next */
                            default:
                                return notReachable(recharge)
                        }
                    })()}
                    side={{
                        rightIcon: () => {
                            switch (recharge.type) {
                                case 'recharge_disabled':
                                    return (
                                        <Toggle
                                            size="regular"
                                            variant="orange"
                                            title={null}
                                            checked={false}
                                            onClick={() => {
                                                onMsg({
                                                    type: 'on_reacharge_list_item_click',
                                                    keyStore,
                                                })
                                            }}
                                        />
                                    )
                                case 'recharge_enabled':
                                    return (
                                        <Toggle
                                            size="regular"
                                            variant="orange"
                                            title={null}
                                            checked
                                            onClick={() => {
                                                onMsg({
                                                    type: 'on_reacharge_list_item_click',
                                                    keyStore,
                                                })
                                            }}
                                        />
                                    )
                                /* istanbul ignore next */
                                default:
                                    return notReachable(recharge)
                            }
                        },
                    }}
                />
            )
        }

        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}
