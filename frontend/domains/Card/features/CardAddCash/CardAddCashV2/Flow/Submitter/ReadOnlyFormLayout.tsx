import { FormattedMessage, useIntl } from 'react-intl'

import { Column } from '@zeal/uikit/Column'
import { Divider } from '@zeal/uikit/Divider'
import { FancyButton } from '@zeal/uikit/FancyButton'
import { CreditCard } from '@zeal/uikit/Icon/CreditCard'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import { values } from '@zeal/toolkit/Object'
import { getFormattedPercentage } from '@zeal/toolkit/Percentage'

import { AccountsMap } from '@zeal/domains/Account'
import { AvatarWithoutBadge as AccountAvatar } from '@zeal/domains/Account/components/Avatar'
import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { Earn } from '@zeal/domains/Earn'
import { TakerAvatar } from '@zeal/domains/Earn/components/TakerAvatar'
import { TakerTitleWithSuffix } from '@zeal/domains/Earn/components/TakerTitleWithSuffix'
import { EARN_PRIMARY_INVESTMENT_ASSET_TO_TAKER_TYPE_MAP } from '@zeal/domains/Earn/constants'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Money2 } from '@zeal/domains/Money'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { convertStableCoinToFiat } from '@zeal/domains/Money/helpers/convertStableCoinToFiat'
import { NetworkMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'

import { getPercentageLoss } from '../../helpers/getPercentageLoss'
import { CardTopUpRequest } from '../../types'
import { RechargeListItem } from '../RechargeListItem'

type Props = {
    cardOwnerEarn: Earn
    keyStoreMap: KeyStoreMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    accountsMap: AccountsMap
    networkMap: NetworkMap
    cardTopUpRequest: CardTopUpRequest
}

const getFromAmount = (cardTopUpRequest: CardTopUpRequest): Money2 => {
    switch (cardTopUpRequest.quote.type) {
        case 'send_eoa':
        case 'swap_eoa_sign_typed_data':
        case 'swap_eoa_sign_typed_data_with_approval':
        case 'swap_eoa_native_send_transaction':
        case 'send_safe':
        case 'swap_safe_sign_typed_data_with_approval_or_safe_deployment':
        case 'swap_safe_sign_typed_data':
        case 'swap_safe_native_send_transaction':
            return cardTopUpRequest.fromAmount
        case 'earn_eoa_eure_withdrawal':
        case 'earn_eoa_sign_typed_data':
        case 'earn_eoa_sign_typed_data_with_approval':
        case 'earn_safe_eure_withdrawal':
        case 'earn_safe_sign_typed_data_with_optional_approval':
            return cardTopUpRequest.quote.fromAmountInUserCurrency
        /* istanbul ignore next */
        default:
            return notReachable(cardTopUpRequest.quote)
    }
}

export const ReadOnlyFormLayout = ({
    cardOwnerEarn,
    keyStoreMap,
    cardConfig,
    accountsMap,
    networkMap,
    cardTopUpRequest,
}: Props) => {
    const { formatMessage } = useIntl()

    const sender = accountsMap[cardTopUpRequest.senderAddress]

    const fromAmount = getFromAmount(cardTopUpRequest)

    const percentageLoss =
        cardTopUpRequest.fromAmountInDefaultCurrency &&
        cardTopUpRequest.quote.toAmountInDefaultCurrency
            ? getPercentageLoss({
                  fromAmount: cardTopUpRequest.fromAmountInDefaultCurrency,
                  toAmount: cardTopUpRequest.quote.toAmountInDefaultCurrency,
              })
            : null

    return (
        <Column spacing={4} fill>
            <AmountInput
                top={
                    values(accountsMap).length > 1 && (
                        <Column spacing={0}>
                            <FancyButton
                                color="secondary"
                                rounded
                                left={() => (
                                    <Row grow shrink spacing={4}>
                                        <AccountAvatar
                                            size={12}
                                            account={sender}
                                        />
                                        <Text
                                            variant="caption1"
                                            color="gray40"
                                            weight="medium"
                                            ellipsis
                                        >
                                            {sender.label}
                                        </Text>
                                    </Row>
                                )}
                                right={null}
                                onClick={noop}
                            />
                            <Divider variant="secondary" />
                        </Column>
                    )
                }
                content={{
                    topLeft: (
                        <FromAsset
                            topupRequest={cardTopUpRequest}
                            networkMap={networkMap}
                        />
                    ),
                    topRight: ({ onBlur, onFocus }) => (
                        <AmountInput.Input
                            onBlur={onBlur}
                            onFocus={onFocus}
                            onChange={noop}
                            label={formatMessage({
                                id: 'card.add-cash.amount-to-withdraw',
                                defaultMessage: 'Top up amount',
                            })}
                            prefix=""
                            fraction={fromAmount.currency.fraction}
                            readOnly={true}
                            amount={toFixedWithFraction(
                                fromAmount.amount,
                                fromAmount.currency.fraction
                            )}
                            onSubmitEditing={noop}
                        />
                    ),
                    bottomLeft: (
                        <Tertiary color="on_light" size="regular">
                            {({ color, textVariant, textWeight }) => (
                                <Row spacing={4}>
                                    <Text
                                        color={color}
                                        variant={textVariant}
                                        weight={textWeight}
                                        textDecorationLine="underline"
                                    >
                                        <FormattedMessage
                                            id="bankTransfer.withdraw.max_loading"
                                            defaultMessage="Max: {amount}"
                                            values={{
                                                amount: (
                                                    <FormattedMoneyPrecise
                                                        withSymbol={false}
                                                        sign={null}
                                                        money={
                                                            cardTopUpRequest.maxBalanceAtSubmit
                                                        }
                                                    />
                                                ),
                                            }}
                                        />
                                    </Text>
                                </Row>
                            )}
                        </Tertiary>
                    ),
                    bottomRight:
                        cardTopUpRequest.fromAmountInDefaultCurrency && (
                            <Text
                                variant="paragraph"
                                color="gray40"
                                weight="medium"
                            >
                                <FormattedMoneyPrecise
                                    withSymbol
                                    sign={null}
                                    money={
                                        cardTopUpRequest.fromAmountInDefaultCurrency
                                    }
                                />
                            </Text>
                        ),
                }}
                state="normal"
            />

            <NextStepSeparator />

            <AmountInput
                content={{
                    topLeft: (
                        <Row spacing={8}>
                            <CreditCard size={32} color="gray20" />
                            <Text
                                variant="title3"
                                color="textPrimary"
                                weight="medium"
                            >
                                <FormattedMessage
                                    id="card"
                                    defaultMessage="Card"
                                />
                            </Text>
                        </Row>
                    ),
                    bottomLeft: (
                        <Text
                            variant="paragraph"
                            color="gray40"
                            weight="medium"
                        >
                            <FormattedMessage
                                id="card-balance"
                                defaultMessage="Balance: {balance}"
                                values={{
                                    balance: (
                                        <FormattedMoneyPrecise
                                            withSymbol
                                            sign={null}
                                            money={
                                                cardTopUpRequest
                                                    .cardBalanceAtSubmit.total
                                            }
                                        />
                                    ),
                                }}
                            />
                        </Text>
                    ),
                    topRight: () => (
                        <Row spacing={0} alignX="end">
                            <Text
                                variant="title3"
                                weight="medium"
                                color="textPrimary"
                            >
                                <FormattedMoneyPrecise
                                    withSymbol
                                    sign={null}
                                    money={convertStableCoinToFiat({
                                        money: cardTopUpRequest.quote.toAmount,
                                    })}
                                />
                            </Text>
                        </Row>
                    ),
                    bottomRight:
                        cardTopUpRequest.quote.toAmountInDefaultCurrency &&
                        (percentageLoss && percentageLoss > 0 ? (
                            <Row spacing={0} alignX="end">
                                <Text
                                    variant="paragraph"
                                    color="gray40"
                                    weight="medium"
                                >
                                    <FormattedMessage
                                        id="card.addCash.to-amount-percentage-loss"
                                        defaultMessage="(-{percentageLoss}) {toAmount}"
                                        values={{
                                            percentageLoss:
                                                getFormattedPercentage(
                                                    percentageLoss
                                                ),
                                            toAmount: (
                                                <FormattedMoneyPrecise
                                                    withSymbol
                                                    sign={null}
                                                    money={
                                                        cardTopUpRequest.quote
                                                            .toAmountInDefaultCurrency
                                                    }
                                                />
                                            ),
                                        }}
                                    />
                                </Text>
                            </Row>
                        ) : (
                            <Text
                                variant="paragraph"
                                color="gray40"
                                weight="medium"
                            >
                                <FormattedMoneyPrecise
                                    withSymbol
                                    sign={null}
                                    money={
                                        cardTopUpRequest.quote
                                            .toAmountInDefaultCurrency
                                    }
                                />
                            </Text>
                        )),
                }}
                state="normal"
            />

            <RechargeListItem
                cardOwnerEarn={cardOwnerEarn}
                keyStoreMap={keyStoreMap}
                cardConfig={cardConfig}
                disabled={true}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_reacharge_list_item_click':
                            // impossibe as it's disabled
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg.type)
                    }
                }}
            />
        </Column>
    )
}

const FromAsset = ({
    topupRequest,
    networkMap,
}: {
    topupRequest: CardTopUpRequest
    networkMap: NetworkMap
}) => {
    switch (topupRequest.quote.type) {
        case 'send_eoa':
        case 'swap_eoa_sign_typed_data':
        case 'swap_eoa_sign_typed_data_with_approval':
        case 'swap_eoa_native_send_transaction':
        case 'send_safe':
        case 'swap_safe_sign_typed_data_with_approval_or_safe_deployment':
        case 'swap_safe_sign_typed_data':
        case 'swap_safe_native_send_transaction':
            return (
                <Row spacing={4}>
                    <CurrencyAvatar
                        key={topupRequest.fromAmount.currency.id}
                        currency={topupRequest.fromAmount.currency}
                        rightBadge={({ size }) => (
                            <Badge
                                size={size}
                                network={findNetworkByHexChainId(
                                    topupRequest.fromAmount.currency
                                        .networkHexChainId,
                                    networkMap
                                )}
                            />
                        )}
                        size={24}
                    />
                    <Text variant="title3" color="textPrimary" weight="medium">
                        {topupRequest.fromAmount.currency.code}
                    </Text>
                </Row>
            )
        case 'earn_safe_sign_typed_data_with_optional_approval':
        case 'earn_eoa_sign_typed_data_with_approval':
        case 'earn_eoa_sign_typed_data':
        case 'earn_safe_eure_withdrawal':
        case 'earn_eoa_eure_withdrawal':
            const takerType =
                EARN_PRIMARY_INVESTMENT_ASSET_TO_TAKER_TYPE_MAP[
                    topupRequest.fromAmount.currency.id
                ]

            if (!takerType) {
                throw new ImperativeError('Taker not found for currency', {
                    currencyId: topupRequest.fromAmount.currency.id,
                })
            }
            return (
                <Row spacing={4}>
                    <TakerAvatar size={24} takerType={takerType} />
                    <Text variant="title3" color="textPrimary" weight="medium">
                        <TakerTitleWithSuffix takerType={takerType} />
                    </Text>
                </Row>
            )
        /* istanbul ignore next */
        default:
            return notReachable(topupRequest.quote)
    }
}
