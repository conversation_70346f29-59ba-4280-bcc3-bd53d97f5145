import { FormattedMessage } from 'react-intl'

import { BannerSolid } from '@zeal/uikit/BannerSolid'
import { Column } from '@zeal/uikit/Column'
import { Screen } from '@zeal/uikit/Screen'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { Earn } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { keystoreToUserEventType } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Footer } from './Footer'
import { ReadOnlyFeeWidget } from './ReadOnlyFeeWidget'
import { ReadOnlyFormLayout } from './ReadOnlyFormLayout'

import { CardTopUpQuote, CardTopUpRequest } from '../../types'

type Props = {
    cardOwnerEarn: Earn
    keyStoreMap: KeyStoreMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    cardTopUpRequest: CardTopUpRequest

    accountsMap: AccountsMap
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof Footer> | { type: 'close' }

export const Submitter = ({
    cardOwnerEarn,
    keyStoreMap,
    cardConfig,
    cardTopUpRequest,
    sessionPassword,
    networkRPCMap,
    networkMap,
    accountsMap,
    defaultCurrencyConfig,
    installationId,
    onMsg,
}: Props) => {
    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} fill>
                <ReadOnlyFormLayout
                    cardOwnerEarn={cardOwnerEarn}
                    keyStoreMap={keyStoreMap}
                    cardConfig={cardConfig}
                    cardTopUpRequest={cardTopUpRequest}
                    networkMap={networkMap}
                    accountsMap={accountsMap}
                />
                <HardWareWalletBanner quote={cardTopUpRequest.quote} />
                <ReadOnlyFeeWidget
                    cardTopUpRequest={cardTopUpRequest}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                />
                <Footer
                    sessionPassword={sessionPassword}
                    cardTopUpRequest={cardTopUpRequest}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_card_top_up_success':
                                postUserEvent({
                                    type: 'TransactionSubmittedEvent',
                                    keystoreId:
                                        cardTopUpRequest.quote.keystore.id,
                                    keystoreType: keystoreToUserEventType(
                                        cardTopUpRequest.quote.keystore
                                    ),
                                    source: msg.submittedCardTopUp.actionSource
                                        .transactionEventSource,
                                    installationId,
                                    gasPaymentMethod: null,
                                    network:
                                        cardTopUpRequest.fromAmount.currency
                                            .networkHexChainId,
                                })
                                onMsg(msg)
                                break
                            case 'close':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            </Column>
        </Screen>
    )
}

const HardWareWalletBanner = ({ quote }: { quote: CardTopUpQuote }) => {
    switch (quote.type) {
        case 'send_eoa':
        case 'swap_eoa_sign_typed_data':
        case 'swap_eoa_sign_typed_data_with_approval':
        case 'swap_eoa_native_send_transaction':
        case 'earn_eoa_sign_typed_data_with_approval':
        case 'earn_eoa_eure_withdrawal':
        case 'earn_eoa_sign_typed_data': {
            switch (quote.keystore.type) {
                case 'private_key_store':
                case 'secret_phrase_key':
                    return null
                case 'ledger':
                case 'trezor':
                    return (
                        <BannerSolid
                            variant="warning"
                            title={
                                <FormattedMessage
                                    id="card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.title"
                                    defaultMessage="Sign hardware wallet"
                                />
                            }
                            subtitle={
                                <FormattedMessage
                                    id="card-add-cash.submitter.hardware-wallet-banner.sign-hardware-wallet.subtitle"
                                    defaultMessage="We sent the transaction request to your hardware wallet. Please continue there."
                                />
                            }
                            rounded
                        />
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(quote.keystore)
            }
        }
        case 'earn_track_only':
        case 'send_track_only':
        case 'swap_track_only':
        case 'earn_safe_eure_withdrawal':
        case 'earn_safe_sign_typed_data_with_optional_approval':
        case 'send_safe':
        case 'swap_safe_native_send_transaction':
        case 'swap_safe_sign_typed_data':
        case 'swap_safe_sign_typed_data_with_approval_or_safe_deployment':
            return null

        /* istanbul ignore next */
        default:
            return notReachable(quote)
    }
}
