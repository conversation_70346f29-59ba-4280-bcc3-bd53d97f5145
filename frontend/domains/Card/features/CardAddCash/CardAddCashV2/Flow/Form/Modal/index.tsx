import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardBalance,
    CardSlientSignKeyStore,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { Earn, EarnTakerMetrics } from '@zeal/domains/Earn'
import { ConfigureRecharge } from '@zeal/domains/Earn/features/ConfigureRecharge'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { SelectFromAsset } from './SelectFromAsset'

type Props = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    portfolioMap: PortfolioMap
    sessionPassword: string
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    earnTakerMetrics: EarnTakerMetrics
    onMsg: (msg: Msg) => void
    state: State
    senderPortfolio: ServerPortfolio2
    cardBalance: CardBalance
    cardOwnerEarn: Earn
    senderEarn: Earn
    supportedTopUpCurrenciesInPortfolio: CryptoCurrency[]
    selectedAccount: Account
    keyStoreMap: KeyStoreMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    currencyPinMap: CurrencyPinMap
}

type Msg = MsgOf<typeof SelectFromAsset> | MsgOf<typeof ConfigureRecharge>

export type State =
    | { type: 'closed' }
    | {
          type: 'select_from_asset'
      }
    | {
          type: 'configure_recharge'
          keyStore: CardSlientSignKeyStore
      }

export const Modal = ({
    cardConfig,
    portfolioMap,
    sessionPassword,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    earnTakerMetrics,
    state,
    onMsg,
    keyStoreMap,
    networkMap,
    supportedTopUpCurrenciesInPortfolio,
    senderPortfolio,
    networkRPCMap,
    currencyHiddenMap,
    installationId,
    defaultCurrencyConfig,
    senderEarn,
    cardOwnerEarn,
    cardBalance,
    selectedAccount,
    currencyPinMap,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'select_from_asset':
            return (
                <UIModal>
                    <SelectFromAsset
                        sender={selectedAccount}
                        supportedTopupCurrencies={
                            supportedTopUpCurrenciesInPortfolio
                        }
                        portfolio={{
                            ...senderPortfolio,
                            earn: senderEarn,
                            cardBalance,
                        }}
                        currencyPinMap={currencyPinMap}
                        keyStoreMap={keyStoreMap}
                        installationId={installationId}
                        currencyHiddenMap={currencyHiddenMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'configure_recharge':
            return (
                <UIModal>
                    <ConfigureRecharge
                        cardOwnerEarn={cardOwnerEarn}
                        cardConfig={cardConfig}
                        keyStore={state.keyStore}
                        portfolioMap={portfolioMap}
                        keyStoreMap={keyStoreMap}
                        cardBalance={cardBalance}
                        networkRPCMap={networkRPCMap}
                        sessionPassword={sessionPassword}
                        accountsMap={accountsMap}
                        networkMap={networkMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        installationId={installationId}
                        location="card_topup"
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        earnTakerMetrics={earnTakerMetrics}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
