import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { uuid } from '@zeal/toolkit/Crypto'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardBalance,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { OPTIMISM_USDC } from '@zeal/domains/Currency/constants'
import {
    ConfiguredEarn,
    DeployedTaker,
    Earn,
    EarnTakerMetrics,
} from '@zeal/domains/Earn'
import { DUST_AMOUNT } from '@zeal/domains/Earn/constants'
import { sumTakerPortfolio } from '@zeal/domains/Earn/helpers/sumEarn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { FiatMoney } from '@zeal/domains/Money'
import { compare2 } from '@zeal/domains/Money/helpers/compare'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import {
    Portfolio2,
    PortfolioMap,
    ServerPortfolio2,
} from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

import { fetchTopUpQuote } from '../../api/fetchTopUpQuote'
import {
    CardTopUpRequestQuote,
    Form as CardTopUpForm,
    QuoteParams,
} from '../../types'

type Props = {
    portfolioMap: PortfolioMap
    sessionPassword: string
    feePresetMap: FeePresetMap
    earnTakerMetrics: EarnTakerMetrics
    initialTopUpCurrency: CryptoCurrency | null
    initialTopUpAmount: string | null

    senderAddress: Web3.address.Address
    accountsMap: AccountsMap
    senderPortfolio: ServerPortfolio2
    cardBalance: CardBalance
    senderEarn: Earn
    cardOwnerEarn: Earn
    supportedTopUpCurrenciesInPortfolio: CryptoCurrency[]
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    installationId: string
    currencyPinMap: CurrencyPinMap

    keyStoreMap: KeyStoreMap
    gasCurrencyPresetMap: GasCurrencyPresetMap

    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_topup_clicked'
                  | 'import_keys_button_clicked'
                  | 'on_sender_account_clicked'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
                  | 'on_recharge_configured'
          }
      >

const calculateInitialForm = ({
    initialTopUpCurrency,
    initialTopUpAmount,
    senderEarn,
    senderPortfolio,
    supportedTopUpCurrenciesInPortfolio,
    cardBalance,
    defaultCurrencyConfig,
    cardConfig,
}: {
    initialTopUpCurrency: CryptoCurrency | null
    initialTopUpAmount: string | null
    senderPortfolio: ServerPortfolio2
    cardBalance: CardBalance
    senderEarn: Earn
    supportedTopUpCurrenciesInPortfolio: CryptoCurrency[]
    defaultCurrencyConfig: DefaultCurrencyConfig
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
}): CardTopUpForm => {
    if (
        initialTopUpCurrency &&
        supportedTopUpCurrenciesInPortfolio.some(
            (c) => c.id === initialTopUpCurrency.id
        )
    ) {
        return calculateTokenTopUpForm({
            currency: initialTopUpCurrency,
            cardConfig,
            amount: initialTopUpAmount,
        })
    }

    const portfolio = {
        tokens: senderPortfolio.tokens,
        nftCollections: senderPortfolio.nftCollections,
        apps: senderPortfolio.apps,
        earn: senderEarn,
        cardBalance,
    }

    const eligibleTokens = getEligiblePortfolioTokens({
        defaultCurrencyConfig,
        supportedTopupCurrencies: supportedTopUpCurrenciesInPortfolio,
        portfolio,
    })

    const eligibleEarnAccounts = getEligibleEarnAccounts({
        defaultCurrencyConfig,
        portfolio,
    })

    const [assetWithMaxBalance] = [
        ...eligibleTokens,
        ...eligibleEarnAccounts,
    ].sort((a, b) =>
        compare2(b.balanceInDefaultCurrency, a.balanceInDefaultCurrency)
    )

    if (
        !assetWithMaxBalance ||
        assetWithMaxBalance.balanceInDefaultCurrency.amount === 0n
    ) {
        return calculateTokenTopUpForm({
            currency: OPTIMISM_USDC,
            cardConfig,
            amount: initialTopUpAmount,
        })
    }

    switch (assetWithMaxBalance.type) {
        case 'earn_account':
            return {
                type: 'earn',
                taker: assetWithMaxBalance.taker,
                amount: initialTopUpAmount,
                stage: 'edit',
            }
        case 'token':
            return calculateTokenTopUpForm({
                currency: assetWithMaxBalance.cryptoCurrency,
                cardConfig,
                amount: initialTopUpAmount,
            })
        /* istanbul ignore next */
        default:
            return notReachable(assetWithMaxBalance)
    }
}

const calculateTokenTopUpForm = ({
    currency,
    amount,
    cardConfig,
}: {
    currency: CryptoCurrency
    amount: string | null
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
}): Extract<CardTopUpForm, { type: 'send' | 'swap' }> => {
    if (currency.id === cardConfig.currency.id) {
        return {
            type: 'send',
            amount,
            stage: 'edit',
        }
    }

    return {
        type: 'swap',
        amount,
        stage: 'edit',
        fromCurrency: currency,
    }
}

const getEligiblePortfolioTokens = ({
    portfolio,
    supportedTopupCurrencies,
    defaultCurrencyConfig,
}: {
    portfolio: Portfolio2
    supportedTopupCurrencies: CryptoCurrency[]
    defaultCurrencyConfig: DefaultCurrencyConfig
}): {
    type: 'token'
    cryptoCurrency: CryptoCurrency
    balanceInDefaultCurrency: FiatMoney
}[] => {
    const supportedCurrencyIds = new Set(
        supportedTopupCurrencies.map(({ id }) => id)
    )

    const portfolioTokens = portfolio.tokens

    return portfolioTokens
        .filter((token) => supportedCurrencyIds.has(token.balance.currency.id))
        .map((token) => ({
            type: 'token' as const,
            cryptoCurrency: token.balance.currency,
            balanceInDefaultCurrency: token.priceInDefaultCurrency || {
                amount: 0n,
                currency: defaultCurrencyConfig.defaultCurrency,
            },
        }))
}

const getEligibleEarnAccounts = ({
    portfolio,
    defaultCurrencyConfig,
}: {
    portfolio: Portfolio2
    defaultCurrencyConfig: DefaultCurrencyConfig
}): {
    type: 'earn_account'
    taker: DeployedTaker
    balanceInDefaultCurrency: FiatMoney
    earn: ConfiguredEarn
}[] => {
    switch (portfolio.earn.type) {
        case 'not_configured':
            return []
        case 'configured':
            const earn = portfolio.earn
            return earn.takers
                .filter((taker): taker is DeployedTaker => {
                    switch (taker.state) {
                        case 'not_deployed':
                            return false
                        case 'deployed':
                            return true
                        /* istanbul ignore next */
                        default:
                            return notReachable(taker)
                    }
                })
                .map((taker) => ({
                    type: 'earn_account' as const,
                    taker,
                    earn,
                    balanceInDefaultCurrency: sumTakerPortfolio({
                        taker,
                        takerPortfolioMap: earn.takerPortfolioMap,
                        defaultCurrencyConfig,
                    }),
                }))
        /* istanbul ignore next */
        default:
            return notReachable(portfolio.earn)
    }
}

const POLL_INTERVAL_MS = 30000

export const Form = ({
    portfolioMap,
    sessionPassword,
    feePresetMap,
    earnTakerMetrics,
    initialTopUpAmount,
    initialTopUpCurrency,
    accountsMap,
    cardConfig,
    cardBalance,
    supportedTopUpCurrenciesInPortfolio,
    senderPortfolio,
    senderEarn,
    cardOwnerEarn,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    senderAddress,
    currencyHiddenMap,
    currencyPinMap,
    installationId,
    keyStoreMap,
    gasCurrencyPresetMap,
    onMsg,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    const [pollable, setPollable] = usePollableData<
        CardTopUpRequestQuote,
        QuoteParams
    >(
        fetchTopUpQuote,
        {
            type: 'loading',
            params: {
                form: calculateInitialForm({
                    initialTopUpAmount,
                    initialTopUpCurrency,
                    senderEarn,
                    senderPortfolio,
                    cardBalance,
                    supportedTopUpCurrenciesInPortfolio,
                    defaultCurrencyConfig,
                    cardConfig,
                }),
                keyStoreMap,
                gasCurrencyPresetMap,
                senderAddress,
                networkMap,
                networkRPCMap,
                cardConfig,
                senderPortfolio,
                senderEarn,
                defaultCurrencyConfig,
                safeRpcDataCacheKey: uuid(),
            },
        },
        {
            pollIntervalMilliseconds: POLL_INTERVAL_MS,
        }
    )

    return (
        <>
            <Layout
                cardOwnerEarn={cardOwnerEarn}
                keyStoreMap={keyStoreMap}
                cardBalance={cardBalance}
                installationId={installationId}
                accountsMap={accountsMap}
                quotePollable={pollable}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_topup_clicked':
                        case 'import_keys_button_clicked':
                            onMsg(msg)
                            break

                        case 'on_sender_account_clicked':
                            postUserEvent({
                                type: 'CardTopupSelectSenderClickedEvent',
                                installationId,
                            })
                            onMsg(msg)
                            break

                        case 'on_from_amount_input_clicked':
                        case 'on_confirm_form_close':
                        case 'on_value_loss_error_revert_clicked':
                            setPollable({
                                ...pollable,
                                params: {
                                    ...pollable.params,
                                    form: {
                                        ...pollable.params.form,
                                        stage: 'edit',
                                    },
                                },
                            })
                            break

                        case 'on_from_amount_change':
                            switch (pollable.type) {
                                case 'loading':
                                case 'error':
                                    setPollable({
                                        type: 'loading',
                                        params: {
                                            ...pollable.params,
                                            form: {
                                                ...pollable.params.form,
                                                amount: msg.amount,
                                                stage: 'edit',
                                            },
                                        },
                                    })
                                    break
                                case 'loaded':
                                case 'reloading':
                                case 'subsequent_failed':
                                    setPollable({
                                        type: 'reloading',
                                        params: {
                                            ...pollable.params,
                                            form: {
                                                ...pollable.params.form,
                                                amount: msg.amount,
                                                stage: 'edit',
                                            },
                                        },
                                        data: pollable.data,
                                    })
                                    break
                                default:
                                    return notReachable(pollable)
                            }
                            break

                        case 'on_continue_clicked':
                            setPollable({
                                ...pollable,
                                params: {
                                    ...pollable.params,
                                    form: {
                                        ...pollable.params.form,
                                        stage: 'confirm',
                                    },
                                },
                            })
                            break

                        case 'on_source_asset_clicked':
                            setModal({
                                type: 'select_from_asset',
                            })
                            break

                        case 'on_4337_auto_gas_token_selection_clicked':
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    gasCurrencyPresetMap: {
                                        ...pollable.params.gasCurrencyPresetMap,
                                        [msg.network.hexChainId]: null,
                                    },
                                },
                            })
                            onMsg(msg)
                            break
                        case 'on_4337_gas_currency_selected':
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    gasCurrencyPresetMap: {
                                        ...pollable.params.gasCurrencyPresetMap,
                                        [msg.selectedGasCurrency
                                            .networkHexChainId]:
                                            msg.selectedGasCurrency.id,
                                    },
                                },
                            })
                            onMsg(msg)
                            break

                        case 'on_error_retry_clicked':
                            setPollable({
                                type: 'loading',
                                params: pollable.params,
                            })
                            break
                        case 'on_reacharge_list_item_click':
                            setModal({
                                type: 'configure_recharge',
                                keyStore: msg.keyStore,
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
            <Modal
                cardOwnerEarn={cardOwnerEarn}
                cardConfig={cardConfig}
                portfolioMap={portfolioMap}
                sessionPassword={sessionPassword}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                earnTakerMetrics={earnTakerMetrics}
                state={modal}
                currencyPinMap={currencyPinMap}
                selectedAccount={accountsMap[pollable.params.senderAddress]}
                senderPortfolio={senderPortfolio}
                cardBalance={cardBalance}
                senderEarn={senderEarn}
                supportedTopUpCurrenciesInPortfolio={
                    supportedTopUpCurrenciesInPortfolio
                }
                keyStoreMap={keyStoreMap}
                installationId={installationId}
                currencyHiddenMap={currencyHiddenMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                defaultCurrencyConfig={defaultCurrencyConfig}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_earn_account_selected':
                            setModal({ type: 'closed' })
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    form: {
                                        type: 'earn',
                                        stage: 'edit',
                                        amount: pollable.params.form.amount,
                                        taker: msg.taker,
                                    },
                                },
                            })
                            break
                        case 'on_crypto_currency_selected':
                            setModal({ type: 'closed' })
                            setPollable({
                                type: 'loading',
                                params: {
                                    ...pollable.params,
                                    form:
                                        msg.currency.id ===
                                        cardConfig.currency.id
                                            ? {
                                                  type: 'send',
                                                  stage: 'edit',
                                                  amount: pollable.params.form
                                                      .amount,
                                              }
                                            : {
                                                  type: 'swap',
                                                  stage: 'edit',
                                                  amount: pollable.params.form
                                                      .amount,
                                                  fromCurrency: msg.currency,
                                              },
                                },
                            })
                            break
                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_chf_taker_metrics_loaded':
                            onMsg(msg)
                            break
                        case 'on_recharge_configured': {
                            onMsg(msg)
                            const rechargingState = getRechargingState({
                                cardBalance,
                                earn: msg.earn,
                            })
                            switch (rechargingState.type) {
                                case 'not_recharging':
                                    setModal({ type: 'closed' })
                                    break
                                case 'recharging_possible':
                                    // close card top up flow to avoid possible race conditions as recharge can be triggerd
                                    onMsg({ type: 'close' })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(rechargingState.type)
                            }
                            break
                        }

                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}

const getRechargingState = ({
    cardBalance,
    earn,
}: {
    cardBalance: CardBalance
    earn: ConfiguredEarn
}): {
    type: 'not_recharging' | 'recharging_possible'
} => {
    const { cardRecharge } = earn

    switch (cardRecharge.type) {
        case 'recharge_disabled':
            return { type: 'not_recharging' }
        case 'recharge_enabled': {
            const notEmptyRebalancers = cardRecharge.rebalancers.filter(
                (rabalencer) => {
                    const takerPortfolio =
                        earn.takerPortfolioMap[rabalencer.type]
                    return (
                        takerPortfolio.assetBalance.amount >
                        DUST_AMOUNT[rabalencer.type]
                    )
                }
            )

            if (
                cardRecharge.threshold >= cardBalance.total.amount &&
                notEmptyRebalancers.length > 0
            ) {
                return { type: 'recharging_possible' }
            }

            return { type: 'not_recharging' }
        }
        /* istanbul ignore next */
        default:
            return notReachable(cardRecharge)
    }
}
