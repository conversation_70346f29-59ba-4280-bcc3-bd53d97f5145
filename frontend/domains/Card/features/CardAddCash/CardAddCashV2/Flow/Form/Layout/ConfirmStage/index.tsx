import { FormattedMessage } from 'react-intl'

import { Actions } from '@zeal/uikit/Actions'
import { BannerSolid } from '@zeal/uikit/BannerSolid'
import { Button, CompressedButton } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Refresh } from '@zeal/uikit/Icon/Refresh'
import { Screen } from '@zeal/uikit/Screen'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { getFormattedPercentage } from '@zeal/toolkit/Percentage'

import { AccountsMap } from '@zeal/domains/Account'
import { CardBalance } from '@zeal/domains/Card'
import { Earn } from '@zeal/domains/Earn'
import { AppErrorBanner } from '@zeal/domains/Error/components/AppErrorBanner'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { InternalTransactionEventSource } from '@zeal/domains/Main'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { keystoreToUserEventType } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { FeeWidget } from './FeeWidget'

import {
    CardTopUpEOAQuote,
    CardTopUpRequest,
    CardTopUpSafeQuote,
    QuotePollable,
} from '../../../../types'
import {
    ConfirmStageError,
    validateConfirmStageAsYouType,
    validateConfirmStageSubmit,
} from '../../../../validation'
import { FormInputsLayout } from '../FormInputsLayout'

type Props = {
    cardOwnerEarn: Earn
    keyStoreMap: KeyStoreMap
    accountsMap: AccountsMap
    quotePollable: QuotePollable
    cardBalance: CardBalance
    installationId: string

    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof ErrorBanner>
    | MsgOf<typeof FeeWidget>
    | Extract<
          MsgOf<typeof CTA>,
          { type: 'on_confirm_form_close' | 'import_keys_button_clicked' }
      >
    | Extract<
          MsgOf<typeof FormInputsLayout>,
          {
              type:
                  | 'on_sender_account_clicked'
                  | 'on_source_asset_clicked'
                  | 'on_from_amount_change'
                  | 'on_from_amount_input_clicked'
                  | 'on_reacharge_list_item_click'
          }
      >
    | { type: 'on_topup_clicked'; topUpRequest: CardTopUpRequest }

const quoteTypeToTransactionActionSource = (
    quote: CardTopUpSafeQuote | CardTopUpEOAQuote
): InternalTransactionEventSource => {
    switch (quote.type) {
        case 'send_eoa':
        case 'send_safe':
            return 'topupCardSend'

        case 'earn_eoa_eure_withdrawal':
        case 'earn_safe_eure_withdrawal':
            return 'topupCardEarnAAVEEURe'

        case 'earn_eoa_sign_typed_data':
        case 'earn_eoa_sign_typed_data_with_approval':
        case 'earn_safe_sign_typed_data_with_optional_approval':
            return 'topupCardEarnBungee'

        case 'swap_eoa_native_send_transaction':
        case 'swap_safe_native_send_transaction':
            return 'topupCardBungeeNativeSend'

        case 'swap_eoa_sign_typed_data':
        case 'swap_eoa_sign_typed_data_with_approval':
        case 'swap_safe_sign_typed_data':
        case 'swap_safe_sign_typed_data_with_approval_or_safe_deployment':
            return 'topupCardBungee'
        default:
            return notReachable(quote)
    }
}

export const ConfirmStage = ({
    cardOwnerEarn,
    keyStoreMap,
    quotePollable,
    installationId,
    accountsMap,
    cardBalance,
    onMsg,
}: Props) => {
    const errors =
        validateConfirmStageAsYouType({
            pollable: quotePollable,
            cardBalance,
        }).getFailureReason() || {}

    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'on_confirm_form_close' })}
        >
            <Column spacing={12} fill alignY="stretch">
                <FormInputsLayout
                    cardOwnerEarn={cardOwnerEarn}
                    keyStoreMap={keyStoreMap}
                    quotePollable={quotePollable}
                    cardBalance={cardBalance}
                    accountsMap={accountsMap}
                    maxBalanceError={null}
                    toAmountInDefaultCurrencyError={
                        errors.toAmountInDefaultCurrencyError || null
                    }
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_sender_account_clicked':
                            case 'on_source_asset_clicked':
                            case 'on_from_amount_change':
                            case 'on_from_amount_input_clicked':
                            case 'on_reacharge_list_item_click':
                                onMsg(msg)
                                break
                            case 'on_continue_clicked':
                                validateConfirmStageSubmit({
                                    pollable: quotePollable,
                                    cardBalance,
                                }).tap((topUpRequest) => {
                                    onMsg({
                                        type: 'on_topup_clicked',
                                        topUpRequest,
                                    })
                                })
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
                <Column spacing={12} alignY="end">
                    <ErrorBanner
                        aboveKeypadError={errors.errorBanner}
                        installationId={installationId}
                        onMsg={onMsg}
                    />

                    <FeeWidget quotePollable={quotePollable} onMsg={onMsg} />

                    <CTA
                        error={errors.submitButton}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'on_deposit_clicked':
                                    validateConfirmStageSubmit({
                                        pollable: quotePollable,
                                        cardBalance,
                                    }).tap((topUpRequest) => {
                                        const keyStore = getKeyStore({
                                            keyStoreMap:
                                                quotePollable.params
                                                    .keyStoreMap,
                                            address:
                                                quotePollable.params
                                                    .senderAddress,
                                        })
                                        postUserEvent({
                                            type: 'TransactionInitiatedEvent',
                                            keystoreId: keyStore.id,
                                            network:
                                                topUpRequest.fromAmount.currency
                                                    .networkHexChainId,
                                            keystoreType:
                                                keystoreToUserEventType(
                                                    keyStore
                                                ),
                                            source: quoteTypeToTransactionActionSource(
                                                topUpRequest.quote
                                            ),
                                            installationId,
                                        })
                                        onMsg({
                                            type: 'on_topup_clicked',
                                            topUpRequest,
                                        })
                                    })
                                    break
                                case 'on_confirm_form_close':
                                case 'import_keys_button_clicked':
                                    onMsg(msg)
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </Column>
            </Column>
        </Screen>
    )
}

const ErrorBanner = ({
    aboveKeypadError,
    installationId,
    onMsg,
}: {
    aboveKeypadError: ConfirmStageError['errorBanner']
    installationId: string
    onMsg: (
        msg:
            | { type: 'on_value_loss_error_revert_clicked' }
            | { type: 'on_error_retry_clicked' }
    ) => void
}) => {
    switch (aboveKeypadError?.type) {
        case undefined:
        case 'pollable_still_loading':
        case 'from_amount_zero':
            return null
        case 'value_loss_error':
            return (
                <BannerSolid
                    rounded
                    variant="warning"
                    title={
                        <FormattedMessage
                            id="card-add-cash.confirm-stage.banner.value-loss"
                            defaultMessage="You’ll lose {loss} of value"
                            values={{
                                loss: getFormattedPercentage(
                                    aboveKeypadError.percentageOfLoss
                                ),
                            }}
                        />
                    }
                    right={
                        <Tertiary
                            size="regular"
                            color="warning"
                            onClick={() =>
                                onMsg({
                                    type: 'on_value_loss_error_revert_clicked',
                                })
                            }
                        >
                            {({ color, textVariant, textWeight }) => (
                                <>
                                    <Refresh size={14} color={color} />
                                    <Text
                                        color={color}
                                        variant={textVariant}
                                        weight={textWeight}
                                    >
                                        <FormattedMessage
                                            id="card-add-cash.confirm-stage.banner.value-loss.revert"
                                            defaultMessage="Revert"
                                        />
                                    </Text>
                                </>
                            )}
                        </Tertiary>
                    }
                />
            )
        case 'not_enought_balance_for_fee':
            return (
                <BannerSolid
                    rounded
                    variant="warning"
                    title={
                        <FormattedMessage
                            id="card-add-cash.confirm-stage.banner.not-enough-balance-for-fee"
                            defaultMessage="You need {amount} more {symbol} to pay fees"
                            values={{
                                amount: (
                                    <FormattedMoneyPrecise
                                        withSymbol={false}
                                        sign={null}
                                        money={aboveKeypadError.neededAmount}
                                    />
                                ),
                                symbol: aboveKeypadError.neededAmount.currency
                                    .symbol,
                            }}
                        />
                    }
                />
            )
        case 'no_routes':
            return (
                <BannerSolid
                    rounded
                    variant="warning"
                    title={
                        <FormattedMessage
                            id="card-add-cash.confirm-stage.banner.no-routes-found"
                            defaultMessage="No routes, try a different token or amount"
                        />
                    }
                />
            )
        case 'pollable_errored':
            return (
                <AppErrorBanner
                    error={parseAppError(aboveKeypadError.error)}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'try_again_clicked':
                                onMsg({ type: 'on_error_retry_clicked' })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg.type)
                        }
                    }}
                    installationId={installationId}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(aboveKeypadError)
    }
}

const CTA = ({
    error,
    onMsg,
}: {
    error: ConfirmStageError['submitButton']
    onMsg: (
        msg:
            | { type: 'on_confirm_form_close' }
            | { type: 'on_deposit_clicked' }
            | { type: 'import_keys_button_clicked' }
    ) => void
}) => {
    return (
        <Actions variant="default" direction="row">
            <CompressedButton
                size="regular"
                variant="secondary"
                onClick={() => {
                    onMsg({ type: 'on_confirm_form_close' })
                }}
            >
                {({ size, color }) => <BackIcon size={size} color={color} />}
            </CompressedButton>
            {(() => {
                switch (error?.type) {
                    case undefined:
                        return (
                            <Button
                                size="regular"
                                variant="primary"
                                onClick={() =>
                                    onMsg({ type: 'on_deposit_clicked' })
                                }
                            >
                                <FormattedMessage
                                    id="action.deposit"
                                    defaultMessage="Deposit"
                                />
                            </Button>
                        )
                    case 'no_routes':
                    case 'pollable_errored':
                    case 'pollable_still_loading':
                    case 'not_enought_balance_for_fee':
                    case 'from_amount_zero':
                        return (
                            <Button size="regular" variant="primary" disabled>
                                <FormattedMessage
                                    id="action.deposit"
                                    defaultMessage="Deposit"
                                />
                            </Button>
                        )
                    case 'keys_not_imported':
                        return (
                            <Button
                                size="regular"
                                variant="primary"
                                onClick={() => {
                                    onMsg({
                                        type: 'import_keys_button_clicked',
                                    })
                                }}
                            >
                                <FormattedMessage
                                    id="action.import-keys"
                                    defaultMessage="Import keys"
                                />
                            </Button>
                        )

                    default:
                        return notReachable(error)
                }
            })()}
        </Actions>
    )
}
