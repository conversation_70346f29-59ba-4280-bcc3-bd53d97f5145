import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { CardBalance } from '@zeal/domains/Card'
import { Earn } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'

import { ConfirmStage } from './ConfirmStage'
import { EditStage } from './EditStage'

import { QuotePollable } from '../../../types'

type Props = {
    cardOwnerEarn: Earn
    keyStoreMap: KeyStoreMap
    accountsMap: AccountsMap
    quotePollable: QuotePollable
    cardBalance: CardBalance
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof EditStage> | MsgOf<typeof ConfirmStage>

export const Layout = ({
    cardOwnerEarn,
    keyStoreMap,
    accountsMap,
    quotePollable,
    cardBalance,
    installationId,
    onMsg,
}: Props) => {
    switch (quotePollable.params.form.stage) {
        case 'edit':
            return (
                <EditStage
                    cardOwnerEarn={cardOwnerEarn}
                    keyStoreMap={keyStoreMap}
                    cardBalance={cardBalance}
                    accountsMap={accountsMap}
                    quotePollable={quotePollable}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )

        case 'confirm':
            return (
                <ConfirmStage
                    cardOwnerEarn={cardOwnerEarn}
                    keyStoreMap={keyStoreMap}
                    accountsMap={accountsMap}
                    quotePollable={quotePollable}
                    cardBalance={cardBalance}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )
        default:
            return notReachable(quotePollable.params.form.stage)
    }
}
