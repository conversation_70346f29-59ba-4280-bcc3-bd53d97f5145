import { values } from '@zeal/toolkit/Object'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    CardBalance,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { fetchBalanceOfCardOnChain } from '@zeal/domains/Card/api/fetchBalance'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyId,
} from '@zeal/domains/Currency'
import { fetchCurrenciesMatrix } from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import { BUNGEE_NOT_SUPPORTED_STABLES } from '@zeal/domains/Currency/domains/Bungee/constants'
import { Earn } from '@zeal/domains/Earn'
import { fetchEarn } from '@zeal/domains/Earn/api/fetchEarn'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { fetchServerPortfolio2 } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type FetchParams = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    senderAddress: Web3.address.Address
    signal?: AbortSignal
}

export const fetchSourceBalancesAndCurrencies = async ({
    cardConfig,
    currencyHiddenMap,
    defaultCurrencyConfig,
    installationId,
    networkMap,
    networkRPCMap,
    senderAddress,
    signal,
}: FetchParams): Promise<{
    serverPortfolio: ServerPortfolio2
    cardBalance: CardBalance
    earn: Earn
    cardOwnerEarn: Earn
    supportedTopUpCurrenciesInPortfolio: CryptoCurrency[]
}> => {
    const senderEarnPromise = fetchEarn({
        defaultCurrencyConfig,
        earnOwnerAddress: senderAddress,
        networkMap,
        networkRPCMap,
        signal,
    })

    const cardOwnerEarnPromise =
        cardConfig.readonlySignerAddress === senderAddress
            ? senderEarnPromise
            : fetchEarn({
                  defaultCurrencyConfig,
                  earnOwnerAddress: cardConfig.readonlySignerAddress,
                  networkMap,
                  networkRPCMap,
                  signal,
              })

    const [serverPortfolio, cardBalance, earn, cardOwnerEarn, currencyMatrix] =
        await Promise.all([
            fetchServerPortfolio2({
                variant: 'only_tokens',
                address: senderAddress,
                currencyHiddenMap,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                installationId,
                signal,
            }),

            fetchBalanceOfCardOnChain({
                cardAddress: cardConfig.lastSeenSafeAddress,
                cardCryptoCurrency: cardConfig.currency,
                networkRPCMap,
                defaultCurrencyConfig,
                networkMap,
                signal,
            }),

            senderEarnPromise,
            cardOwnerEarnPromise,

            fetchCurrenciesMatrix(),
        ])

    const allMatrixCurrenciesMap = values(currencyMatrix.currencies).reduce(
        (acc, curr) => {
            if (!curr) {
                return acc
            }
            return {
                ...acc,
                ...values(curr).reduce(
                    (acc, toMatrix) => {
                        if (!toMatrix) {
                            return acc
                        }

                        toMatrix.from.forEach(
                            (curr) => (acc[curr] = true as const)
                        )
                        toMatrix.to.forEach(
                            (curr) => (acc[curr] = true as const)
                        )

                        return acc
                    },
                    {} as Record<CurrencyId, true>
                ),
            }
        },
        {} as Record<CurrencyId, true>
    )

    const supportedTopUpCurrenciesInPortfolio = serverPortfolio.tokens
        .filter((token) => !token.scam)
        .map((token) => token.balance.currency)
        .filter(
            (currency) =>
                allMatrixCurrenciesMap[currency.id] &&
                !BUNGEE_NOT_SUPPORTED_STABLES.has(currency.id)
        )

    return {
        serverPortfolio,
        cardBalance,
        earn,
        cardOwnerEarn,
        supportedTopUpCurrenciesInPortfolio,
    }
}
