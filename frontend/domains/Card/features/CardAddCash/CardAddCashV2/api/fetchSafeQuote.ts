import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import { memoizeOne } from '@zeal/toolkit/Function/memoizeOne'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { values } from '@zeal/toolkit/Object'
import { failure, success } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { SAFE_4337_MODULE_ENTRYPOINT_ADDRESS } from '@zeal/domains/Address/constants'
import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import {
    CryptoCurrency,
    DefaultCurrency,
    GasCurrencyPresetMap,
    KnownCryptoCurrencies,
} from '@zeal/domains/Currency'
import { requestAllowance } from '@zeal/domains/Currency/api/fetchAllowance'
import { fetchStaticCurrencies } from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import {
    PAYMASTER_ADDRESS,
    PAYMASTER_MAP,
} from '@zeal/domains/Currency/constants'
import { BungeeERC20IntentQuote } from '@zeal/domains/Currency/domains/Bungee'
import { fetchBungeeIntentQuote } from '@zeal/domains/Currency/domains/Bungee/api/fetchBungeeIntentQuote'
import { getSwapSlippagePercent } from '@zeal/domains/Currency/domains/Bungee/helpers/getSwapSlippagePercent'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { createAAVEEUReWithdrawalTransaction } from '@zeal/domains/Earn/helpers/createAAVEEUReWithdrawalTransaction'
import { createWithdrawalTransaction } from '@zeal/domains/Earn/helpers/createWithdrawalTransaction'
import { FXRate2 } from '@zeal/domains/FXRate'
import { fetchCrossRates } from '@zeal/domains/FXRate/api/fetchCrossRates'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { Safe4337 } from '@zeal/domains/KeyStore'
import { requestSafeOwners } from '@zeal/domains/KeyStore/api/fetchSafeOwners'
import {
    fetchSafeInstanceAndNonce,
    SafeInstance,
} from '@zeal/domains/KeyStore/helpers/fetchSafe4337Instance'
import { getSafeDeploymentInitCode } from '@zeal/domains/KeyStore/helpers/getSafeDeploymentInitCode'
import { ActionSource } from '@zeal/domains/Main'
import { CryptoMoney, FiatMoney, Money2 } from '@zeal/domains/Money'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import {
    DEFAULT_NETWORK_HEX_IDS_TO_SPONSOR,
    findNetworkByHexChainId,
} from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { fetchRPCBatch2 } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { createTransferEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/createERC20EthSendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import {
    AAVE_WITHDRAWAL_GAS,
    BUNGEE_NATIVE_TRANSFER_GAS,
    EARN_WITHDRAWAL_GAS,
    ERC_20_TRANSFER_GAS,
    ERC20_APPROVAL_GAS,
} from '@zeal/domains/Transactions/domains/FeeForecast/constants'
import {
    FeeAndGasEstimates,
    MetaTransactionData,
    NonSponsoredUserOperationFee,
    UserOperationFee,
} from '@zeal/domains/UserOperation'
import { requestCurrentEntrypointNonce2 } from '@zeal/domains/UserOperation/api/fetchCurrentEntrypointNonce'
import { fetchBiconomyBundlerFeeAndGasEstimates } from '@zeal/domains/UserOperation/api/fetchFeeAndGasEstimatesFromBundler'
import { fetchPimlicoGasEstimates } from '@zeal/domains/UserOperation/api/fetchGasEstimates'
import { PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA } from '@zeal/domains/UserOperation/constants'
import { calculateFee } from '@zeal/domains/UserOperation/helpers/calculateFee'
import { createAddOwnerMetaTransaction } from '@zeal/domains/UserOperation/helpers/createAddOwnerMetaTransaction'
import { ethSendTransactionToMetaTransactionData } from '@zeal/domains/UserOperation/helpers/ethSendTransactionToMetaTransactionData'
import { getDummySignature } from '@zeal/domains/UserOperation/helpers/getDummySignature'
import { getNoopMetaTransaction } from '@zeal/domains/UserOperation/helpers/getNoopMetaTransaction'
import { getSignatureVGLBuffer } from '@zeal/domains/UserOperation/helpers/getSignatureVGLBuffer'
import { metaTransactionDatasToUserOperationCallData } from '@zeal/domains/UserOperation/helpers/metaTransactionDatasToUserOperationCallData'

import { fetchRateIfNeeded, fetchRatesIfNeeded } from './fetchRatesIfNeeded'

import { getEarnMaxBalanceAndInvestmentAssetAmount } from '../helpers/getEarnMaxBalanceAndInvestmentAssetAmount'
import { CardTopUpRequestQuote, Form, QuoteParams } from '../types'

type FetchSafeQuoteParams = Omit<QuoteParams, 'keyStoreMap'> & {
    keyStore: Safe4337
    signal?: AbortSignal
}

type SafeInstanceAndNonceParams = {
    senderAddress: Web3.address.Address
    network: Network
    keyStore: Safe4337
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}

type NonSponsoredRPCDataParams = {
    senderAddress: Web3.address.Address
    senderPortfolio: ServerPortfolio2
    network: Network
    keystore: Safe4337
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    currencies: KnownCryptoCurrencies
    signal?: AbortSignal
}

const getFeeAndMaxBalance = ({
    estimate,
    keystore,
    network,
    nativeCurrency,
    gasCurrencyPresetMap,
    safeInstance,
    portfolio,
    fromCurrency,
    nativeToDefaultRate,
    currencies,
    approvals,
    crossRates,
}: {
    nativeToDefaultRate: FXRate2<CryptoCurrency, DefaultCurrency> | null
    estimate: FeeAndGasEstimates
    fromCurrency: CryptoCurrency
    nativeCurrency: CryptoCurrency
    network: Network
    keystore: Safe4337
    gasCurrencyPresetMap: GasCurrencyPresetMap
    portfolio: ServerPortfolio2
    safeInstance: SafeInstance
    approvals: CryptoMoney[]
    crossRates: FXRate2<CryptoCurrency, CryptoCurrency>[]
    currencies: KnownCryptoCurrencies
}): {
    fee: NonSponsoredUserOperationFee
    maxBalance: CryptoMoney
} => {
    const userOperationFee = calculateFee({
        network,
        nativeCurrency,
        bundlerGasEstimate: estimate,
        keystore,
        safeInstance,
        gasCurrencyPresetMap,
        portfolio,
        currencies,
        nativeToDefaultRate,
        crossRates,
        approvals,
    })

    const selectedTokenOnForm = portfolio.tokens.find(
        (token) => token.balance.currency.id === fromCurrency.id
    )

    if (!selectedTokenOnForm) {
        // impossible state?
        return {
            fee: userOperationFee,
            maxBalance: {
                amount: 0n,
                currency: fromCurrency,
            },
        }
    }

    if (
        userOperationFee.selectedFee.feeInTokenCurrency.currency.id ===
        selectedTokenOnForm.balance.currency.id
    ) {
        return {
            fee: userOperationFee,
            maxBalance:
                selectedTokenOnForm.balance.amount >
                userOperationFee.selectedFee.feeInTokenCurrency.amount
                    ? {
                          amount:
                              selectedTokenOnForm.balance.amount -
                              userOperationFee.selectedFee.feeInTokenCurrency
                                  .amount,
                          currency: fromCurrency,
                      }
                    : {
                          amount: 0n,
                          currency: fromCurrency,
                      },
        }
    }
    return {
        fee: userOperationFee,
        maxBalance: {
            amount: selectedTokenOnForm.balance.amount,
            currency: fromCurrency,
        },
    }
}

const isSponsored = (network: Network): boolean => {
    switch (network.smartWalletSupport.type) {
        case 'not_supported':
            return false
        case 'supported':
            return (
                network.smartWalletSupport.isSponsorshipSupported &&
                DEFAULT_NETWORK_HEX_IDS_TO_SPONSOR.includes(network.hexChainId)
            )
        default:
            return notReachable(network.smartWalletSupport)
    }
}

const getCallData = ({
    safeInstance,
    keyStore,
    transactions,
}: {
    safeInstance: SafeInstance
    keyStore: Safe4337
    transactions: MetaTransactionData[]
}): Hexadecimal.Hexadecimal => {
    const addOwnerTxData = createAddOwnerMetaTransaction({
        safeAddress: safeInstance.safeAddress,
        owner: keyStore.localSignerKeyStore.address,
        treshold: 1n,
    })

    const metaTransactions = (() => {
        switch (safeInstance.type) {
            case 'deployed':
                return safeInstance.owners.includes(
                    keyStore.localSignerKeyStore.address
                )
                    ? transactions
                    : [addOwnerTxData, ...transactions]
            case 'not_deployed':
                return [addOwnerTxData, ...transactions]
            default:
                return notReachable(safeInstance)
        }
    })()

    return metaTransactionDatasToUserOperationCallData({
        metaTransactionDatas: metaTransactions,
    })
}

const getInitCode = (
    safeInstance: SafeInstance
): Hexadecimal.Hexadecimal | null => {
    switch (safeInstance.type) {
        case 'not_deployed':
            return safeInstance.deploymentInitCode
        case 'deployed':
            return null
        default:
            return notReachable(safeInstance)
    }
}

const fetchSafeInstanceAndNonceWithCache = memoizeOne(
    ({
        cacheKey: _,
        signal: __,
        ...rest
    }: SafeInstanceAndNonceParams & { cacheKey: string }) =>
        fetchSafeInstanceAndNonce(rest),
    ({ cacheKey, senderAddress, network }) =>
        `${cacheKey}${senderAddress}${network.hexChainId}`
)

const fetchNonSponsoredRPCData = async ({
    senderAddress,
    senderPortfolio,
    network,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
    currencies,
    keystore,
    signal,
}: NonSponsoredRPCDataParams): Promise<{
    safeInstance: SafeInstance
    crossRates: FXRate2<CryptoCurrency, CryptoCurrency>[]
    entrypointNonce: bigint
    gasTokenAllowances: CryptoMoney[]
    nativeToDefaultRate: FXRate2<CryptoCurrency, DefaultCurrency> | null
}> => {
    const [
        nativeToDefaultRate,
        crossRates,
        [entrypointNonce, owners, ...gasTokenAllowances],
    ] = await Promise.all([
        fetchRateIfNeeded({
            networkMap,
            networkRPCMap,
            cryptoCurrency: network.nativeCurrency,
            serverPortfolio: senderPortfolio,
            defaultCurrencyConfig,
            signal,
        }),
        fetchCrossRates({
            baseCurrency: network.nativeCurrency,
            quoteCurrencies: PAYMASTER_MAP[network.hexChainId]
                .filter(
                    (currencyId) => currencyId !== network.nativeCurrency.id
                )
                .map((currencyId) => currencies[currencyId]),
            networkMap,
            networkRPCMap,
            signal,
        }).then((ratesMap) => values(ratesMap).filter(excludeNullValues)),
        fetchRPCBatch2(
            [
                requestCurrentEntrypointNonce2({
                    entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
                    address: senderAddress,
                }),
                requestSafeOwners({ safeAddress: senderAddress }),
                ...PAYMASTER_MAP[network.hexChainId]
                    .filter(
                        (currencyId) => currencyId !== network.nativeCurrency.id
                    )
                    .map((currencyId) => {
                        const currency = currencies[currencyId]
                        return requestAllowance({
                            currency,
                            owner: senderAddress,
                            spender: PAYMASTER_ADDRESS,
                        })
                    }),
            ],
            { networkRPCMap, network, signal }
        ),
    ])
    const safeInstance: SafeInstance = owners
        ? {
              type: 'deployed',
              safeAddress: senderAddress,
              entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
              owners: owners,
          }
        : {
              type: 'not_deployed',
              entrypoint: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
              safeAddress: senderAddress,
              deploymentInitCode: getSafeDeploymentInitCode(
                  keystore.safeDeplymentConfig
              ),
          }
    return {
        safeInstance,
        gasTokenAllowances,
        nativeToDefaultRate,
        crossRates,
        entrypointNonce,
    }
}

const fetchNonSponsoredRPCDataWithCache = memoizeOne(
    ({
        cacheKey: _,
        signal: __,
        ...rest
    }: NonSponsoredRPCDataParams & { cacheKey: string }) =>
        fetchNonSponsoredRPCData(rest),
    ({ cacheKey, senderAddress, network }) =>
        `${cacheKey}${senderAddress}${network.hexChainId}`
)

type GasEstimationParams = {
    safeInstance: SafeInstance
    keyStore: Safe4337
    senderAddress: Web3.address.Address
    entrypointNonce: bigint
    transactions: MetaTransactionData[]
    network: Network
    callGasLimitBuffer: bigint
    actionSource: ActionSource
    signal?: AbortSignal
}

const fetchSponsoredGasEstimate = async ({
    safeInstance,
    keyStore,
    senderAddress,
    entrypointNonce,
    transactions,
    network,
    callGasLimitBuffer,
    actionSource,
    signal,
}: GasEstimationParams) =>
    fetchPimlicoGasEstimates({
        entrypoint: safeInstance.entrypoint,
        network,
        initialUserOperation: {
            callData: getCallData({
                safeInstance,
                keyStore,
                transactions,
            }),
            sender: senderAddress,
            nonce: entrypointNonce,
            initCode: getInitCode(safeInstance),
            paymasterAndData: PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA,
            signature: getDummySignature({
                safeInstance,
                keyStore,
            }),
        },
        verificationGasLimitBuffer: getSignatureVGLBuffer({
            safeInstance,
            keystore: keyStore,
        }),
        callGasLimitBuffer,
        actionSource,
        signal,
    })

const fetchNonSponsoredGasEstimate = async ({
    safeInstance,
    keyStore,
    senderAddress,
    entrypointNonce,
    transactions,
    network,
    callGasLimitBuffer,
    actionSource,
    signal,
}: GasEstimationParams) =>
    fetchBiconomyBundlerFeeAndGasEstimates({
        entrypoint: safeInstance.entrypoint,
        network,
        initialUserOperation: {
            callData: getCallData({
                safeInstance,
                keyStore,
                transactions,
            }),
            sender: senderAddress,
            nonce: entrypointNonce,
            initCode: getInitCode(safeInstance),
            paymasterAndData: '0x',
            signature: getDummySignature({
                safeInstance,
                keyStore,
            }),
        },
        callGasLimitBuffer,
        actionSource,
        signal,
    })

const fetchNativeSwapFeeAndMaxBalance = async ({
    senderAddress,
    senderPortfolio,
    signal,
    defaultCurrencyConfig,
    networkRPCMap,
    networkMap,
    fromNetwork,
    fromAmount,
    keyStore,
    gasCurrencyPresetMap,
    safeRpcDataCacheKey,
}: Omit<FetchSafeQuoteParams, 'senderEarn' | 'form' | 'cardConfig'> & {
    fromAmount: CryptoMoney
    fromNetwork: Network
}): Promise<{
    fee: UserOperationFee
    maxBalance: CryptoMoney
    safeInstance: SafeInstance
    entrypointNonce: bigint
}> => {
    const actionSource: ActionSource = {
        type: 'internal',
        transactionEventSource: 'topupCardBungeeNativeSendMaxBalance',
    }

    if (isSponsored(fromNetwork)) {
        const { safeInstance, entrypointNonce } =
            await fetchSafeInstanceAndNonceWithCache({
                network: fromNetwork,
                networkRPCMap,
                keyStore,
                senderAddress,
                cacheKey: safeRpcDataCacheKey,
                signal,
            })

        const gasEstimate = await fetchSponsoredGasEstimate({
            safeInstance,
            keyStore,
            senderAddress,
            entrypointNonce,
            transactions: [getNoopMetaTransaction(senderAddress)],
            network: fromNetwork,
            callGasLimitBuffer: BUNGEE_NATIVE_TRANSFER_GAS,
            actionSource,
            signal,
        })

        return {
            fee: { type: 'sponsored_user_operation_fee', gasEstimate },
            maxBalance: getBalanceByCryptoCurrency2({
                currency: fromAmount.currency,
                serverPortfolio: senderPortfolio,
            }),
            safeInstance,
            entrypointNonce,
        }
    }

    const currencies = await fetchStaticCurrencies()

    const {
        safeInstance,
        entrypointNonce,
        gasTokenAllowances,
        nativeToDefaultRate,
        crossRates,
    } = await fetchNonSponsoredRPCDataWithCache({
        network: fromNetwork,
        networkRPCMap,
        keystore: keyStore,
        networkMap,
        defaultCurrencyConfig,
        senderPortfolio,
        currencies,
        senderAddress,
        cacheKey: safeRpcDataCacheKey,
        signal,
    })

    const gasEstimate = await fetchNonSponsoredGasEstimate({
        safeInstance,
        keyStore,
        senderAddress,
        entrypointNonce,
        transactions: [getNoopMetaTransaction(senderAddress)],
        network: fromNetwork,
        callGasLimitBuffer: BUNGEE_NATIVE_TRANSFER_GAS,
        actionSource,
        signal,
    })

    const { fee, maxBalance } = getFeeAndMaxBalance({
        portfolio: senderPortfolio,
        approvals: gasTokenAllowances,
        crossRates,
        network: fromNetwork,
        safeInstance,
        keystore: keyStore,
        gasCurrencyPresetMap,
        nativeCurrency: fromNetwork.nativeCurrency,
        fromCurrency: fromAmount.currency,
        estimate: gasEstimate,
        nativeToDefaultRate,
        currencies,
    })

    return { fee, maxBalance, safeInstance, entrypointNonce }
}

const fetchNativeTokenSwapQuote = async ({
    fromAmount,
    fromNetwork,
    cardConfig,
    senderPortfolio,
    keyStore,
    networkMap,
    networkRPCMap,
    senderAddress,
    defaultCurrencyConfig,
    safeRpcDataCacheKey,
    gasCurrencyPresetMap,
    signal,
}: Omit<FetchSafeQuoteParams, 'senderEarn' | 'form'> & {
    fromAmount: CryptoMoney
    fromNetwork: Network
}): Promise<CardTopUpRequestQuote> => {
    const [rates, quote, { fee, maxBalance, safeInstance, entrypointNonce }] =
        await Promise.all([
            fetchRatesIfNeeded({
                cryptoCurrencies: [fromAmount.currency, cardConfig.currency],
                serverPortfolio: senderPortfolio,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                signal,
            }),
            fetchBungeeIntentQuote({
                request: {
                    fromAddress: senderAddress,
                    fromAmount,
                    toCurrency: cardConfig.currency,
                    toAddress: cardConfig.lastSeenSafeAddress,
                    slippage: getSwapSlippagePercent({
                        fromAmount,
                        provider: 'bungee',
                    }),
                },
                networkMap,
                networkRPCMap,
                signal,
            }),
            fetchNativeSwapFeeAndMaxBalance({
                fromAmount,
                fromNetwork,
                signal,
                keyStore,
                networkMap,
                networkRPCMap,
                senderAddress,
                senderPortfolio,
                gasCurrencyPresetMap,
                safeRpcDataCacheKey,
                defaultCurrencyConfig,
            }),
        ])

    const fromRate = rates[fromAmount.currency.id] || null

    const fromAmountInDefaultCurrency = fromRate
        ? applyRate2({ baseAmount: fromAmount, rate: fromRate })
        : null

    if (!quote) {
        return {
            fromAmount,
            fromAmountInDefaultCurrency,
            maxBalance,
            quote: failure({ type: 'no_routes' }),
        }
    }

    switch (quote.type) {
        case 'bungee_erc20_intent_quote':
            throw new ImperativeError('Got ERC20 bungee quote for native swap')
        case 'bungee_native_token_intent_quote':
            const toRate = rates[cardConfig.currency.id] || null

            const toAmountInDefaultCurrency = toRate
                ? applyRate2({
                      baseAmount: quote.outputAmount,
                      rate: toRate,
                  })
                : null

            return {
                fromAmount,
                fromAmountInDefaultCurrency,
                maxBalance,
                quote: success({
                    type: 'swap_safe_native_send_transaction',
                    keystore: keyStore,
                    toAmount: quote.outputAmount,
                    toAmountInDefaultCurrency,
                    entrypointNonce,
                    safeInstance,
                    quoteRequestHash: quote.requestHash,
                    transaction: ethSendTransactionToMetaTransactionData(
                        quote.transaction
                    ),
                    fee,
                }),
            }
        default:
            return notReachable(quote)
    }
}

const fetchERC20TokenSwapQuote = async ({
    fromAmount,
    fromNetwork,
    cardConfig,
    senderPortfolio,
    keyStore,
    networkMap,
    networkRPCMap,
    senderAddress,
    defaultCurrencyConfig,
    safeRpcDataCacheKey,
    gasCurrencyPresetMap,
    signal,
}: Omit<FetchSafeQuoteParams, 'senderEarn' | 'form'> & {
    fromAmount: CryptoMoney
    fromNetwork: Network
}): Promise<CardTopUpRequestQuote> => {
    const [rates, quote] = await Promise.all([
        fetchRatesIfNeeded({
            cryptoCurrencies: [fromAmount.currency, cardConfig.currency],
            serverPortfolio: senderPortfolio,
            defaultCurrencyConfig,
            networkMap,
            networkRPCMap,
            signal,
        }),
        fetchBungeeIntentQuote({
            request: {
                fromAddress: senderAddress,
                fromAmount,
                toCurrency: cardConfig.currency,
                toAddress: cardConfig.lastSeenSafeAddress,
                slippage: getSwapSlippagePercent({
                    fromAmount,
                    provider: 'bungee',
                }),
            },
            networkMap,
            networkRPCMap,
            signal,
        }),
    ])

    const fromRate = rates[fromAmount.currency.id] || null

    const fromAmountInDefaultCurrency = fromRate
        ? applyRate2({ baseAmount: fromAmount, rate: fromRate })
        : null

    const maxBalance = getBalanceByCryptoCurrency2({
        currency: fromAmount.currency,
        serverPortfolio: senderPortfolio,
    })

    if (!quote) {
        return {
            fromAmount,
            fromAmountInDefaultCurrency,
            maxBalance,
            quote: failure({ type: 'no_routes' }),
        }
    }

    switch (quote.type) {
        case 'bungee_native_token_intent_quote':
            throw new ImperativeError('Got native bungee quote for ERC20 swap')
        case 'bungee_erc20_intent_quote': {
            const toRate = rates[cardConfig.currency.id] || null

            const toAmountInDefaultCurrency = toRate
                ? applyRate2({
                      baseAmount: quote.outputAmount,
                      rate: toRate,
                  })
                : null

            const { safeInstance, entrypointNonce } =
                await fetchSafeInstanceAndNonceWithCache({
                    network: fromNetwork,
                    networkRPCMap,
                    keyStore,
                    senderAddress,
                    cacheKey: safeRpcDataCacheKey,
                    signal,
                })

            if (!quote.permitApprovalTransaction) {
                switch (safeInstance.type) {
                    case 'deployed':
                        if (
                            safeInstance.owners.includes(
                                keyStore.localSignerKeyStore.address
                            )
                        ) {
                            return {
                                fromAmount,
                                fromAmountInDefaultCurrency,
                                maxBalance,
                                quote: success({
                                    type: 'swap_safe_sign_typed_data',
                                    keystore: keyStore,
                                    toAmount: quote.outputAmount,
                                    toAmountInDefaultCurrency,
                                    quoteRequestHash: quote.requestHash,
                                    quoteId: quote.quoteId,
                                    swapSignatureRequest: quote.signTypedData,
                                    witness: quote.witness,
                                    quoteRequestType: quote.quoteRequestType,
                                }),
                            }
                        }
                        return fetchERC20SwapQuoteWithTransaction({
                            transaction: getNoopMetaTransaction(senderAddress),
                            fromAmount,
                            fromAmountInDefaultCurrency,
                            maxBalance,
                            quote,
                            keyStore,
                            safeInstance,
                            entrypointNonce,
                            senderAddress,
                            fromNetwork,
                            senderPortfolio,
                            networkMap,
                            networkRPCMap,
                            defaultCurrencyConfig,
                            safeRpcDataCacheKey,
                            gasCurrencyPresetMap,
                            toAmountInDefaultCurrency,
                            signal,
                        })
                    case 'not_deployed':
                        return fetchERC20SwapQuoteWithTransaction({
                            transaction: getNoopMetaTransaction(senderAddress),
                            fromAmount,
                            fromAmountInDefaultCurrency,
                            maxBalance,
                            quote,
                            keyStore,
                            safeInstance,
                            entrypointNonce,
                            senderAddress,
                            fromNetwork,
                            senderPortfolio,
                            networkMap,
                            networkRPCMap,
                            defaultCurrencyConfig,
                            safeRpcDataCacheKey,
                            gasCurrencyPresetMap,
                            toAmountInDefaultCurrency,
                            signal,
                        })
                    default:
                        return notReachable(safeInstance)
                }
            }

            return fetchERC20SwapQuoteWithTransaction({
                transaction: ethSendTransactionToMetaTransactionData(
                    quote.permitApprovalTransaction
                ),
                fromAmount,
                fromAmountInDefaultCurrency,
                maxBalance,
                quote,
                keyStore,
                safeInstance,
                entrypointNonce,
                senderAddress,
                fromNetwork,
                senderPortfolio,
                networkMap,
                networkRPCMap,
                defaultCurrencyConfig,
                safeRpcDataCacheKey,
                gasCurrencyPresetMap,
                toAmountInDefaultCurrency,
                signal,
            })
        }
        default:
            return notReachable(quote)
    }
}

const fetchERC20SwapQuoteWithTransaction = async ({
    transaction,
    fromAmount,
    fromAmountInDefaultCurrency,
    maxBalance,
    quote,
    keyStore,
    safeInstance,
    entrypointNonce,
    senderAddress,
    fromNetwork,
    senderPortfolio,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
    safeRpcDataCacheKey,
    gasCurrencyPresetMap,
    toAmountInDefaultCurrency,
    signal,
}: {
    transaction: MetaTransactionData
    fromAmount: CryptoMoney
    fromAmountInDefaultCurrency: FiatMoney | null
    maxBalance: Money2
    quote: BungeeERC20IntentQuote
    keyStore: Safe4337
    safeInstance: SafeInstance
    entrypointNonce: bigint
    senderAddress: Web3.address.Address
    fromNetwork: Network
    senderPortfolio: ServerPortfolio2
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    safeRpcDataCacheKey: string
    gasCurrencyPresetMap: any
    toAmountInDefaultCurrency: FiatMoney | null
    signal?: AbortSignal
}): Promise<CardTopUpRequestQuote> => {
    const actionSource: ActionSource = {
        type: 'internal',
        transactionEventSource: 'topupCardBungeeQuote',
    }
    if (isSponsored(fromNetwork)) {
        const gasEstimate = await fetchSponsoredGasEstimate({
            safeInstance,
            keyStore,
            senderAddress,
            entrypointNonce,
            transactions: [transaction],
            network: fromNetwork,
            callGasLimitBuffer: 0n,
            actionSource,
            signal,
        })

        return {
            fromAmount,
            fromAmountInDefaultCurrency,
            maxBalance,
            quote: success({
                type: 'swap_safe_sign_typed_data_with_approval_or_safe_deployment',
                keystore: keyStore,
                toAmount: quote.outputAmount,
                toAmountInDefaultCurrency,
                quoteRequestHash: quote.requestHash,
                swapSignatureRequest: quote.signTypedData,
                quoteId: quote.quoteId,
                witness: quote.witness,
                quoteRequestType: quote.quoteRequestType,
                preparationTransaction: transaction,
                safeInstance,
                entrypointNonce,
                fee: {
                    type: 'sponsored_user_operation_fee',
                    gasEstimate,
                },
            }),
        }
    }

    const currencies = await fetchStaticCurrencies()

    const { gasTokenAllowances, nativeToDefaultRate, crossRates } =
        await fetchNonSponsoredRPCDataWithCache({
            network: fromNetwork,
            networkRPCMap,
            keystore: keyStore,
            networkMap,
            defaultCurrencyConfig,
            senderPortfolio,
            currencies,
            senderAddress,
            cacheKey: safeRpcDataCacheKey,
            signal,
        })

    const gasEstimate = await fetchNonSponsoredGasEstimate({
        safeInstance,
        keyStore,
        senderAddress,
        entrypointNonce,
        transactions: [transaction],
        network: fromNetwork,
        callGasLimitBuffer: 0n,
        actionSource,
        signal,
    })

    const { fee, maxBalance: adjustedMaxBalance } = getFeeAndMaxBalance({
        portfolio: senderPortfolio,
        approvals: gasTokenAllowances,
        crossRates,
        network: fromNetwork,
        safeInstance,
        keystore: keyStore,
        gasCurrencyPresetMap,
        nativeCurrency: fromNetwork.nativeCurrency,
        fromCurrency: fromAmount.currency,
        estimate: gasEstimate,
        nativeToDefaultRate,
        currencies,
    })

    return {
        fromAmount,
        fromAmountInDefaultCurrency,
        maxBalance: adjustedMaxBalance,
        quote: success({
            type: 'swap_safe_sign_typed_data_with_approval_or_safe_deployment',
            keystore: keyStore,
            toAmount: quote.outputAmount,
            toAmountInDefaultCurrency,
            quoteRequestHash: quote.requestHash,
            swapSignatureRequest: quote.signTypedData,
            quoteId: quote.quoteId,
            witness: quote.witness,
            quoteRequestType: quote.quoteRequestType,
            preparationTransaction: transaction,
            safeInstance,
            entrypointNonce,
            fee,
        }),
    }
}

const fetchSwapQuote = async ({
    form,
    cardConfig,
    senderPortfolio,
    keyStore,
    networkMap,
    networkRPCMap,
    senderAddress,
    defaultCurrencyConfig,
    safeRpcDataCacheKey,
    gasCurrencyPresetMap,
    signal,
}: Omit<FetchSafeQuoteParams, 'senderEarn'> & {
    form: Extract<Form, { type: 'swap' }>
}): Promise<CardTopUpRequestQuote> => {
    const fromNetwork = findNetworkByHexChainId(
        form.fromCurrency.networkHexChainId,
        networkMap
    )

    const fromAmount: CryptoMoney = {
        amount: fromFixedWithFraction(form.amount, form.fromCurrency.fraction),
        currency: form.fromCurrency,
    }

    if (fromAmount.currency.id === fromNetwork.nativeCurrency.id) {
        return fetchNativeTokenSwapQuote({
            fromAmount,
            fromNetwork,
            cardConfig,
            senderPortfolio,
            keyStore,
            networkMap,
            networkRPCMap,
            senderAddress,
            defaultCurrencyConfig,
            safeRpcDataCacheKey,
            gasCurrencyPresetMap,
            signal,
        })
    }

    return fetchERC20TokenSwapQuote({
        fromAmount,
        fromNetwork,
        cardConfig,
        senderPortfolio,
        keyStore,
        networkMap,
        networkRPCMap,
        senderAddress,
        defaultCurrencyConfig,
        safeRpcDataCacheKey,
        gasCurrencyPresetMap,
        signal,
    })
}

const fetchSendQuote = async ({
    form,
    cardConfig,
    senderPortfolio,
    keyStore,
    networkMap,
    networkRPCMap,
    senderAddress,
    defaultCurrencyConfig,
    safeRpcDataCacheKey,
    signal,
}: Omit<FetchSafeQuoteParams, 'gasCurrencyPresetMap' | 'senderEarn'> & {
    form: Extract<Form, { type: 'send' }>
}): Promise<CardTopUpRequestQuote> => {
    const actionSource: ActionSource = {
        type: 'internal',
        transactionEventSource: 'topupCardSendQuote',
    }
    const amount: CryptoMoney = {
        amount: fromFixedWithFraction(
            form.amount,
            cardConfig.currency.fraction
        ),
        currency: cardConfig.currency,
    }

    const sendTransaction = ethSendTransactionToMetaTransactionData(
        createTransferEthSendTransaction({
            network: CARD_NETWORK,
            from: senderAddress,
            amount,
            to: cardConfig.lastSeenSafeAddress,
        })
    )

    const { safeInstance, entrypointNonce } =
        await fetchSafeInstanceAndNonceWithCache({
            network: CARD_NETWORK,
            networkRPCMap,
            keyStore,
            senderAddress,
            cacheKey: safeRpcDataCacheKey,
            signal,
        })

    const [rate, gasEstimate] = await Promise.all([
        fetchRateIfNeeded({
            cryptoCurrency: cardConfig.currency,
            serverPortfolio: senderPortfolio,
            defaultCurrencyConfig,
            networkMap,
            networkRPCMap,
            signal,
        }),
        fetchSponsoredGasEstimate({
            safeInstance,
            keyStore,
            senderAddress,
            entrypointNonce,
            transactions: [getNoopMetaTransaction(senderAddress)],
            network: CARD_NETWORK,
            callGasLimitBuffer: ERC_20_TRANSFER_GAS,
            actionSource,
            signal,
        }),
    ])

    const toAmountInDefaultCurrency = rate
        ? applyRate2({ baseAmount: amount, rate })
        : null

    return {
        fromAmount: amount,
        fromAmountInDefaultCurrency: rate
            ? applyRate2({ baseAmount: amount, rate })
            : null,
        maxBalance: getBalanceByCryptoCurrency2({
            currency: amount.currency,
            serverPortfolio: senderPortfolio,
        }),
        quote: success({
            type: 'send_safe',
            keystore: keyStore,
            toAmount: amount,
            toAmountInDefaultCurrency,
            safeInstance,
            entrypointNonce,
            transaction: sendTransaction,
            fee: { type: 'sponsored_user_operation_fee', gasEstimate },
        }),
    }
}

const fetchEarnQuote = async ({
    senderEarn,
    senderAddress,
    senderPortfolio,
    keyStore,
    networkMap,
    networkRPCMap,
    signal,
    defaultCurrencyConfig,
    safeRpcDataCacheKey,
    cardConfig,
    form,
}: Omit<FetchSafeQuoteParams, 'gasCurrencyPresetMap'> & {
    form: Extract<Form, { type: 'earn' }>
}): Promise<CardTopUpRequestQuote> => {
    const takerPortfolio = senderEarn.takerPortfolioMap[form.taker.type]
    const userCurrency = takerPortfolio.userCurrencyRate.quote

    const fromAmountInUserCurrency = {
        amount: form.amount
            ? fromFixedWithFraction(form.amount, userCurrency.fraction)
            : 0n,
        currency: userCurrency,
    } as Money2

    const fromRate = takerPortfolio.userCurrencyToDefaultCurrencyRate

    const fromAmountInDefaultCurrency = fromRate
        ? applyRate2({
              baseAmount: fromAmountInUserCurrency,
              rate: fromRate,
          })
        : null

    const { maxBalanceInUserCurrency, investmentAssetAmount } =
        getEarnMaxBalanceAndInvestmentAssetAmount({
            takerPortfolio,
            fromAmountInUserCurrency,
        })

    const { safeInstance, entrypointNonce } =
        await fetchSafeInstanceAndNonceWithCache({
            network: EARN_NETWORK,
            networkRPCMap,
            keyStore,
            senderAddress,
            cacheKey: safeRpcDataCacheKey,
            signal,
        })

    switch (form.taker.type) {
        case 'eur': {
            const earnWithdrawalTransaction = createWithdrawalTransaction({
                investmentAssetAmount: investmentAssetAmount.amount,
                ownerAddress: senderAddress,
                holderAddress: senderEarn.holder,
                takerAddress: form.taker.address,
            })
            const aaveWithdrawalTransaction =
                createAAVEEUReWithdrawalTransaction({
                    ownerAddress: senderAddress,
                    receiverAddress: cardConfig.lastSeenSafeAddress,
                    amount: investmentAssetAmount.amount,
                })

            const gasEstimate = await fetchSponsoredGasEstimate({
                transactions: [getNoopMetaTransaction(senderAddress)],
                safeInstance,
                keyStore,
                senderAddress,
                network: EARN_NETWORK,
                callGasLimitBuffer: AAVE_WITHDRAWAL_GAS + EARN_WITHDRAWAL_GAS,
                entrypointNonce,
                actionSource: {
                    type: 'internal',
                    transactionEventSource: 'topupCardEarnAAVEEUReQuote',
                },
                signal,
            })

            return {
                fromAmount: investmentAssetAmount,
                fromAmountInDefaultCurrency,
                maxBalance: maxBalanceInUserCurrency,
                quote: success({
                    type: 'earn_safe_eure_withdrawal',
                    earnWithdrawalTransaction:
                        ethSendTransactionToMetaTransactionData(
                            earnWithdrawalTransaction
                        ),
                    aaveWithdrawalTransaction:
                        ethSendTransactionToMetaTransactionData(
                            aaveWithdrawalTransaction
                        ),
                    keystore: keyStore,
                    fromAmountInUserCurrency,
                    entrypointNonce,
                    safeInstance,
                    toAmount: {
                        amount: investmentAssetAmount.amount,
                        currency: cardConfig.currency,
                    },
                    toAmountInDefaultCurrency: fromAmountInDefaultCurrency,
                    fee: {
                        type: 'sponsored_user_operation_fee',
                        gasEstimate,
                    },
                }),
            }
        }
        case 'usd':
        case 'chf':
        case 'eth': {
            const [toRate, quote, gasEstimate] = await Promise.all([
                fetchRateIfNeeded({
                    cryptoCurrency: cardConfig.currency,
                    serverPortfolio: senderPortfolio,
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    signal,
                }),
                fetchBungeeIntentQuote({
                    request: {
                        fromAddress: senderAddress,
                        fromAmount: investmentAssetAmount,
                        toCurrency: cardConfig.currency,
                        toAddress: cardConfig.lastSeenSafeAddress,
                        slippage: getSwapSlippagePercent({
                            fromAmount: investmentAssetAmount,
                            provider: 'bungee',
                        }),
                    },
                    networkMap,
                    networkRPCMap,
                    signal,
                }),
                fetchSponsoredGasEstimate({
                    transactions: [getNoopMetaTransaction(senderAddress)],
                    safeInstance,
                    keyStore,
                    senderAddress,
                    network: EARN_NETWORK,
                    callGasLimitBuffer:
                        ERC20_APPROVAL_GAS + EARN_WITHDRAWAL_GAS, // we assume approval is needed so we don't need to estimate twice
                    entrypointNonce,
                    actionSource: {
                        type: 'internal',
                        transactionEventSource: 'topupCardEarnBungeeQuote',
                    },
                    signal,
                }),
            ])

            if (!quote) {
                return {
                    fromAmount: investmentAssetAmount,
                    fromAmountInDefaultCurrency,
                    maxBalance: maxBalanceInUserCurrency,
                    quote: failure({ type: 'no_routes' }),
                }
            }

            switch (quote.type) {
                case 'bungee_native_token_intent_quote':
                    throw new ImperativeError(
                        'Got native bungee quote for ERC20 swap'
                    )
                case 'bungee_erc20_intent_quote':
                    const toAmountInDefaultCurrency = toRate
                        ? applyRate2({
                              baseAmount: quote.outputAmount,
                              rate: toRate,
                          })
                        : null

                    const withdrawalTransaction =
                        ethSendTransactionToMetaTransactionData(
                            createWithdrawalTransaction({
                                investmentAssetAmount:
                                    investmentAssetAmount.amount,
                                ownerAddress: senderAddress,
                                holderAddress: senderEarn.holder,
                                takerAddress: form.taker.address,
                            })
                        )

                    const metaTransactionDatas = quote.permitApprovalTransaction
                        ? [
                              withdrawalTransaction,
                              ethSendTransactionToMetaTransactionData(
                                  quote.permitApprovalTransaction
                              ),
                          ]
                        : [withdrawalTransaction]

                    return {
                        fromAmount: investmentAssetAmount,
                        fromAmountInDefaultCurrency,
                        maxBalance: maxBalanceInUserCurrency,
                        quote: success({
                            type: 'earn_safe_sign_typed_data_with_optional_approval',
                            keystore: keyStore,
                            fromAmountInUserCurrency,
                            toAmount: quote.outputAmount,
                            toAmountInDefaultCurrency,
                            quoteId: quote.quoteId,
                            quoteRequestHash: quote.requestHash,
                            signatureRequest: quote.signTypedData,
                            witness: quote.witness,
                            quoteRequestType: quote.quoteRequestType,
                            safeInstance,
                            entrypointNonce,
                            metaTransactionDatas,
                            fee: {
                                type: 'sponsored_user_operation_fee',
                                gasEstimate,
                            },
                        }),
                    }

                default:
                    return notReachable(quote)
            }
        }

        default:
            return notReachable(form.taker.type)
    }
}

export const fetchSafeQuote = async ({
    form,
    defaultCurrencyConfig,
    cardConfig,
    senderPortfolio,
    keyStore,
    senderEarn,
    networkMap,
    networkRPCMap,
    senderAddress,
    gasCurrencyPresetMap,
    safeRpcDataCacheKey,
    signal,
}: FetchSafeQuoteParams): Promise<CardTopUpRequestQuote> => {
    switch (form.type) {
        case 'send': {
            return fetchSendQuote({
                form,
                cardConfig,
                senderPortfolio,
                keyStore,
                networkMap,
                networkRPCMap,
                senderAddress,
                defaultCurrencyConfig,
                safeRpcDataCacheKey,
                signal,
            })
        }
        case 'swap': {
            return fetchSwapQuote({
                form,
                cardConfig,
                senderPortfolio,
                keyStore,
                networkMap,
                networkRPCMap,
                senderAddress,
                defaultCurrencyConfig,
                safeRpcDataCacheKey,
                gasCurrencyPresetMap,
                signal,
            })
        }

        case 'earn':
            return fetchEarnQuote({
                form,
                cardConfig,
                senderEarn,
                senderPortfolio,
                keyStore,
                networkMap,
                networkRPCMap,
                senderAddress,
                defaultCurrencyConfig,
                safeRpcDataCacheKey,
                signal,
            })
        default:
            return notReachable(form)
    }
}
