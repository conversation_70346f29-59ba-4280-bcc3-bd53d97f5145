import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import { failure, success } from '@zeal/toolkit/Result'

import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import { fetchBungeeIntentQuote } from '@zeal/domains/Currency/domains/Bungee/api/fetchBungeeIntentQuote'
import { getSwapSlippagePercent } from '@zeal/domains/Currency/domains/Bungee/helpers/getSwapSlippagePercent'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { createAAVEEUReWithdrawalTransaction } from '@zeal/domains/Earn/helpers/createAAVEEUReWithdrawalTransaction'
import { createWithdrawalTransaction } from '@zeal/domains/Earn/helpers/createWithdrawalTransaction'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { EOA } from '@zeal/domains/KeyStore'
import { CryptoMoney, Money2 } from '@zeal/domains/Money'
import { sub2 } from '@zeal/domains/Money/helpers/sub'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { createTransferEthSendTransaction } from '@zeal/domains/RPCRequest/helpers/createERC20EthSendTransaction'
import { FeeForecastResponse } from '@zeal/domains/Transactions/domains/FeeForecast'
import { fetchFeeForecast } from '@zeal/domains/Transactions/domains/FeeForecast/api/fetchFeeForecast'
import {
    AAVE_WITHDRAWAL_GAS,
    BUNGEE_NATIVE_TRANSFER_GAS,
    EARN_WITHDRAWAL_GAS,
    ERC_20_TRANSFER_GAS,
    ERC20_APPROVAL_GAS,
} from '@zeal/domains/Transactions/domains/FeeForecast/constants'
import { getSuggestedGasLimit } from '@zeal/domains/Transactions/helpers/getSuggestedGasLimit'

import { fetchRateIfNeeded, fetchRatesIfNeeded } from './fetchRatesIfNeeded'

import { getEarnMaxBalanceAndInvestmentAssetAmount } from '../helpers/getEarnMaxBalanceAndInvestmentAssetAmount'
import { CardTopUpRequestQuote, QuoteParams } from '../types'

// Used for L1 data fee calculation
const DUMMY_BUNGEE_NATIVE_TRANSFER: EthSendTransaction = {
    id: generateRandomNumber(),
    jsonrpc: '2.0',
    method: 'eth_sendTransaction',
    params: [
        {
            from: '******************************************',
            to: '******************************************',
            data: '0x69e0a55600000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000064000000000000000000000000000000000000000000000000000000000000000a0000000000000000000000000000000000000000000000000000000068a44f080000000000000000000000000000000000000000000000000000000068a44cb00000000000000000000000004278d3e47774dbf2ed529b0286a6008088104d340000000000000000000000003510d37a89399714d336f7cdbf2be7c74c268be70000000000000000000000003510d37a89399714d336f7cdbf2be7c74c268be70000000000000000000000005e01dbbbe59f8987673fadd1469ddd2be71e00af0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000e91d153e0b41518a2ce8dd3d7944fa863463a97d0000000000000000000000000000000000000000000000004563918244f400000000000000000000000000000b2c639c533813f4aa9d7837caf62653d097ff8500000000000000000000000000000000000000000000000000000000004b81650000000000000000000000000000000000000000000000000000000000000000000000000000000000000000ddafbb505ad214d7b80b1f830fccc89b60fb7a8300000000000000000000000000000000000000000000000000000000004c25fa00000000000000000000000000000000000000000000000000000000000000cd00000000000000000000000000000000000000000000000000000000000002a0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002c0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000',
            value: '5000000000000000000',
        },
    ],
}

export const fetchEOAQuote = async ({
    form,
    defaultCurrencyConfig,
    cardConfig,
    senderPortfolio,
    keyStore,
    senderEarn,
    networkMap,
    networkRPCMap,
    senderAddress,
    signal,
}: Omit<
    QuoteParams,
    'keyStoreMap' | 'safeRpcDataCacheKey' | 'gasCurrencyPresetMap'
> & {
    keyStore: EOA
    signal: AbortSignal
}): Promise<CardTopUpRequestQuote> => {
    switch (form.type) {
        case 'send': {
            const amount: CryptoMoney = {
                amount: fromFixedWithFraction(
                    form.amount,
                    cardConfig.currency.fraction
                ),
                currency: cardConfig.currency,
            }

            const maxBalance = getBalanceByCryptoCurrency2({
                currency: cardConfig.currency,
                serverPortfolio: senderPortfolio,
            })

            const transaction = createTransferEthSendTransaction({
                network: CARD_NETWORK,
                from: senderAddress,
                amount,
                to: cardConfig.lastSeenSafeAddress,
            })

            const gasEstimate = Hexadecimal.fromBigInt(ERC_20_TRANSFER_GAS)

            const [rate, fee] = await Promise.all([
                fetchRateIfNeeded({
                    cryptoCurrency: cardConfig.currency,
                    serverPortfolio: senderPortfolio,
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    signal,
                }),
                fetchFeeForecast({
                    network: CARD_NETWORK,
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    address: senderAddress,
                    sendTransactionRequest: transaction,
                    selectedPreset: { type: 'Fast' },
                    gasEstimate,
                    gasLimit: getSuggestedGasLimit(gasEstimate),
                    signal,
                }),
            ])

            const amountInDefaultCurrency = rate
                ? applyRate2({ baseAmount: amount, rate })
                : null

            return {
                maxBalance,
                fromAmount: amount,
                fromAmountInDefaultCurrency: amountInDefaultCurrency,
                quote: success({
                    type: 'send_eoa',
                    keystore: keyStore,
                    toAmount: amount,
                    toAmountInDefaultCurrency: amountInDefaultCurrency,
                    transaction,
                    fee,
                    gasEstimate: Hexadecimal.toBigInt(gasEstimate),
                }),
            }
        }
        case 'swap': {
            const fromNetwork = findNetworkByHexChainId(
                form.fromCurrency.networkHexChainId,
                networkMap
            )

            const fromAmount: CryptoMoney = {
                amount: fromFixedWithFraction(
                    form.amount,
                    form.fromCurrency.fraction
                ),
                currency: form.fromCurrency,
            }

            if (fromAmount.currency.id === fromNetwork.nativeCurrency.id) {
                const gasEstimate = Hexadecimal.fromBigInt(
                    BUNGEE_NATIVE_TRANSFER_GAS
                )

                const [rates, quote, fee] = await Promise.all([
                    fetchRatesIfNeeded({
                        cryptoCurrencies: [
                            fromAmount.currency,
                            cardConfig.currency,
                        ],
                        serverPortfolio: senderPortfolio,
                        defaultCurrencyConfig,
                        networkMap,
                        networkRPCMap,
                        signal,
                    }),
                    fetchBungeeIntentQuote({
                        request: {
                            fromAddress: senderAddress,
                            fromAmount,
                            toCurrency: cardConfig.currency,
                            toAddress: cardConfig.lastSeenSafeAddress,
                            slippage: getSwapSlippagePercent({
                                fromAmount,
                                provider: 'bungee',
                            }),
                        },
                        networkMap,
                        networkRPCMap,
                        signal,
                    }),
                    fetchFeeForecast({
                        network: fromNetwork,
                        defaultCurrencyConfig,
                        networkMap,
                        networkRPCMap,
                        address: senderAddress,
                        sendTransactionRequest: DUMMY_BUNGEE_NATIVE_TRANSFER,
                        selectedPreset: { type: 'Fast' },
                        gasEstimate,
                        gasLimit: getSuggestedGasLimit(gasEstimate),
                        signal,
                    }),
                ])

                const fromRate = rates[fromAmount.currency.id] || null

                const fromAmountInDefaultCurrency = fromRate
                    ? applyRate2({ baseAmount: fromAmount, rate: fromRate })
                    : null

                const nativeTokenBalance = getBalanceByCryptoCurrency2({
                    currency: fromAmount.currency,
                    serverPortfolio: senderPortfolio,
                })

                const maxBalance =
                    nativeTokenBalance.amount >
                    fee.fast.maxPriceInNativeCurrency.amount
                        ? sub2(
                              nativeTokenBalance,
                              fee.fast.maxPriceInNativeCurrency
                          )
                        : nativeTokenBalance

                if (!quote) {
                    return {
                        fromAmount,
                        fromAmountInDefaultCurrency,
                        maxBalance,
                        quote: failure({ type: 'no_routes' }),
                    }
                }

                switch (quote.type) {
                    case 'bungee_erc20_intent_quote':
                        throw new ImperativeError(
                            'Got ERC20 bungee quote for native swap'
                        )
                    case 'bungee_native_token_intent_quote':
                        const toRate = rates[cardConfig.currency.id] || null

                        const toAmountInDefaultCurrency = toRate
                            ? applyRate2({
                                  baseAmount: quote.outputAmount,
                                  rate: toRate,
                              })
                            : null

                        return {
                            fromAmount,
                            fromAmountInDefaultCurrency,
                            maxBalance,
                            quote: success({
                                type: 'swap_eoa_native_send_transaction',
                                quoteRequestHash: quote.requestHash,
                                keystore: keyStore,
                                toAmount: quote.outputAmount,
                                toAmountInDefaultCurrency,
                                transaction: quote.transaction,
                                fee,
                                gasEstimate: Hexadecimal.toBigInt(gasEstimate),
                            }),
                        }
                    default:
                        return notReachable(quote)
                }
            }

            const [rates, quote] = await Promise.all([
                fetchRatesIfNeeded({
                    cryptoCurrencies: [
                        fromAmount.currency,
                        cardConfig.currency,
                    ],
                    serverPortfolio: senderPortfolio,
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    signal,
                }),
                fetchBungeeIntentQuote({
                    request: {
                        fromAddress: senderAddress,
                        fromAmount,
                        toCurrency: cardConfig.currency,
                        toAddress: cardConfig.lastSeenSafeAddress,
                        slippage: getSwapSlippagePercent({
                            fromAmount,
                            provider: 'bungee',
                        }),
                    },
                    networkMap,
                    networkRPCMap,
                    signal,
                }),
            ])

            const fromRate = rates[fromAmount.currency.id] || null

            const fromAmountInDefaultCurrency = fromRate
                ? applyRate2({ baseAmount: fromAmount, rate: fromRate })
                : null

            const maxBalance = getBalanceByCryptoCurrency2({
                currency: fromAmount.currency,
                serverPortfolio: senderPortfolio,
            })

            if (!quote) {
                return {
                    fromAmount,
                    fromAmountInDefaultCurrency,
                    maxBalance,
                    quote: failure({ type: 'no_routes' }),
                }
            }

            switch (quote.type) {
                case 'bungee_native_token_intent_quote':
                    throw new ImperativeError(
                        'Got native bungee quote for ERC20 swap'
                    )
                case 'bungee_erc20_intent_quote':
                    const toRate = rates[cardConfig.currency.id] || null

                    const toAmountInDefaultCurrency = toRate
                        ? applyRate2({
                              baseAmount: quote.outputAmount,
                              rate: toRate,
                          })
                        : null

                    if (!quote.permitApprovalTransaction) {
                        return {
                            fromAmount,
                            fromAmountInDefaultCurrency,
                            maxBalance,
                            quote: success({
                                type: 'swap_eoa_sign_typed_data',
                                quoteRequestHash: quote.requestHash,
                                quoteId: quote.quoteId,
                                keystore: keyStore,
                                toAmount: quote.outputAmount,
                                toAmountInDefaultCurrency,
                                swapSignatureRequest: quote.signTypedData,
                                witness: quote.witness,
                                quoteRequestType: quote.quoteRequestType,
                            }),
                        }
                    }

                    const gasEstimate =
                        Hexadecimal.fromBigInt(ERC20_APPROVAL_GAS)

                    const fee = await fetchFeeForecast({
                        network: fromNetwork,
                        defaultCurrencyConfig,
                        networkMap,
                        networkRPCMap,
                        address: senderAddress,
                        sendTransactionRequest: quote.permitApprovalTransaction,
                        selectedPreset: { type: 'Fast' },
                        gasEstimate,
                        gasLimit: getSuggestedGasLimit(gasEstimate),
                        signal,
                    })

                    return {
                        fromAmount,
                        fromAmountInDefaultCurrency,
                        maxBalance,
                        quote: success({
                            type: 'swap_eoa_sign_typed_data_with_approval',
                            quoteRequestHash: quote.requestHash,
                            quoteId: quote.quoteId,
                            keystore: keyStore,
                            toAmount: quote.outputAmount,
                            toAmountInDefaultCurrency,
                            swapSignatureRequest: quote.signTypedData,
                            witness: quote.witness,
                            quoteRequestType: quote.quoteRequestType,
                            approvalTransaction:
                                quote.permitApprovalTransaction,
                            fee,
                            gasEstimate: Hexadecimal.toBigInt(gasEstimate),
                        }),
                    }
                default:
                    return notReachable(quote)
            }
        }
        case 'earn': {
            const takerPortfolio = senderEarn.takerPortfolioMap[form.taker.type]
            const userCurrency = takerPortfolio.userCurrencyRate.quote

            const fromAmountInUserCurrency = {
                amount: form.amount
                    ? fromFixedWithFraction(form.amount, userCurrency.fraction)
                    : 0n,
                currency: userCurrency,
            } as Money2

            const fromRate = takerPortfolio.userCurrencyToDefaultCurrencyRate

            const fromAmountInDefaultCurrency = fromRate
                ? applyRate2({
                      baseAmount: fromAmountInUserCurrency,
                      rate: fromRate,
                  })
                : null

            const { maxBalanceInUserCurrency, investmentAssetAmount } =
                getEarnMaxBalanceAndInvestmentAssetAmount({
                    fromAmountInUserCurrency,
                    takerPortfolio,
                })

            const earnWithdrawalTransaction = createWithdrawalTransaction({
                investmentAssetAmount: investmentAssetAmount.amount,
                ownerAddress: senderAddress,
                holderAddress: senderEarn.holder,
                takerAddress: form.taker.address,
            })

            const earnWithdrawalGasEstimate =
                Hexadecimal.fromBigInt(EARN_WITHDRAWAL_GAS)

            switch (form.taker.type) {
                case 'eur': {
                    const aaveWithdrawalTransaction =
                        createAAVEEUReWithdrawalTransaction({
                            ownerAddress: senderAddress,
                            receiverAddress: cardConfig.lastSeenSafeAddress,
                            amount: investmentAssetAmount.amount,
                        })

                    const aaveWithdrawalGasEstimate =
                        Hexadecimal.fromBigInt(AAVE_WITHDRAWAL_GAS)

                    const [earnWithdrawalFee, aaveWithdrawalFee] =
                        await Promise.all([
                            fetchFeeForecast({
                                network: EARN_NETWORK,
                                defaultCurrencyConfig,
                                networkMap,
                                networkRPCMap,
                                address: senderAddress,
                                sendTransactionRequest:
                                    earnWithdrawalTransaction,
                                selectedPreset: { type: 'Fast' },
                                gasEstimate: earnWithdrawalGasEstimate,
                                gasLimit: getSuggestedGasLimit(
                                    earnWithdrawalGasEstimate
                                ),
                                signal,
                            }),
                            fetchFeeForecast({
                                network: EARN_NETWORK,
                                defaultCurrencyConfig,
                                networkMap,
                                networkRPCMap,
                                address: senderAddress,
                                sendTransactionRequest:
                                    aaveWithdrawalTransaction,
                                selectedPreset: { type: 'Fast' },
                                gasEstimate: aaveWithdrawalGasEstimate,
                                gasLimit: getSuggestedGasLimit(
                                    aaveWithdrawalGasEstimate
                                ),
                                signal,
                            }),
                        ])

                    return {
                        fromAmount: investmentAssetAmount,
                        fromAmountInDefaultCurrency,
                        maxBalance: maxBalanceInUserCurrency,
                        quote: success({
                            type: 'earn_eoa_eure_withdrawal',
                            keystore: keyStore,
                            fromAmountInUserCurrency,
                            toAmount: {
                                amount: investmentAssetAmount.amount,
                                currency: cardConfig.currency,
                            },
                            aaveWithdrawalFee: {
                                ...aaveWithdrawalFee,
                                nonce: earnWithdrawalFee.nonce + 1,
                            },
                            aaveWithdrawalGasEstimate: Hexadecimal.toBigInt(
                                aaveWithdrawalGasEstimate
                            ),
                            aaveWithdrawalTransaction,

                            earnWithdrawalFee,
                            earnWithdrawalGasEstimate: Hexadecimal.toBigInt(
                                earnWithdrawalGasEstimate
                            ),
                            earnWithdrawalTransaction,

                            toAmountInDefaultCurrency:
                                fromAmountInDefaultCurrency,
                        }),
                    }
                }

                case 'usd':
                case 'chf':
                case 'eth': {
                    const [toRate, quote, withdrawalFee] = await Promise.all([
                        fetchRateIfNeeded({
                            cryptoCurrency: cardConfig.currency,
                            serverPortfolio: senderPortfolio,
                            defaultCurrencyConfig,
                            networkMap,
                            networkRPCMap,
                            signal,
                        }),
                        fetchBungeeIntentQuote({
                            request: {
                                fromAddress: senderAddress,
                                fromAmount: investmentAssetAmount,
                                toCurrency: cardConfig.currency,
                                toAddress: cardConfig.lastSeenSafeAddress,
                                slippage: getSwapSlippagePercent({
                                    fromAmount: investmentAssetAmount,
                                    provider: 'bungee',
                                }),
                            },
                            networkMap,
                            networkRPCMap,
                            signal,
                        }),
                        fetchFeeForecast({
                            network: EARN_NETWORK,
                            defaultCurrencyConfig,
                            networkMap,
                            networkRPCMap,
                            address: senderAddress,
                            sendTransactionRequest: earnWithdrawalTransaction,
                            selectedPreset: { type: 'Fast' },
                            gasEstimate: earnWithdrawalGasEstimate,
                            gasLimit: getSuggestedGasLimit(
                                earnWithdrawalGasEstimate
                            ),
                            signal,
                        }),
                    ])

                    if (!quote) {
                        return {
                            fromAmount: investmentAssetAmount,
                            fromAmountInDefaultCurrency,
                            maxBalance: maxBalanceInUserCurrency,
                            quote: failure({ type: 'no_routes' }),
                        }
                    }

                    switch (quote.type) {
                        case 'bungee_native_token_intent_quote':
                            throw new ImperativeError(
                                'Got native bungee quote for ERC20 swap'
                            )
                        case 'bungee_erc20_intent_quote':
                            const toAmountInDefaultCurrency = toRate
                                ? applyRate2({
                                      baseAmount: quote.outputAmount,
                                      rate: toRate,
                                  })
                                : null

                            if (!quote.permitApprovalTransaction) {
                                return {
                                    fromAmount: investmentAssetAmount,
                                    fromAmountInDefaultCurrency,
                                    maxBalance: maxBalanceInUserCurrency,
                                    quote: success({
                                        type: 'earn_eoa_sign_typed_data',
                                        quoteRequestHash: quote.requestHash,
                                        quoteId: quote.quoteId,
                                        keystore: keyStore,
                                        fromAmountInUserCurrency,
                                        toAmount: quote.outputAmount,
                                        toAmountInDefaultCurrency,
                                        signatureRequest: quote.signTypedData,
                                        witness: quote.witness,
                                        withdrawalTransaction:
                                            earnWithdrawalTransaction,
                                        withdrawalFee,
                                        withdrawalGasEstimate:
                                            Hexadecimal.toBigInt(
                                                earnWithdrawalGasEstimate
                                            ),
                                        quoteRequestType:
                                            quote.quoteRequestType,
                                    }),
                                }
                            }

                            const approvalGasEstimate =
                                Hexadecimal.fromBigInt(ERC20_APPROVAL_GAS)

                            const approvalFee = await fetchFeeForecast({
                                network: EARN_NETWORK,
                                defaultCurrencyConfig,
                                networkMap,
                                networkRPCMap,
                                address: senderAddress,
                                sendTransactionRequest:
                                    quote.permitApprovalTransaction,
                                selectedPreset: { type: 'Fast' },
                                gasEstimate: approvalGasEstimate,
                                gasLimit:
                                    getSuggestedGasLimit(approvalGasEstimate),
                                signal,
                            })

                            const approvalFeeWithCorrectNonce: FeeForecastResponse =
                                {
                                    ...approvalFee,
                                    nonce: withdrawalFee.nonce + 1,
                                }

                            return {
                                fromAmount: investmentAssetAmount,
                                fromAmountInDefaultCurrency,
                                maxBalance: maxBalanceInUserCurrency,
                                quote: success({
                                    type: 'earn_eoa_sign_typed_data_with_approval',
                                    quoteRequestHash: quote.requestHash,
                                    quoteId: quote.quoteId,
                                    keystore: keyStore,
                                    fromAmountInUserCurrency,
                                    toAmount: quote.outputAmount,
                                    toAmountInDefaultCurrency,
                                    signatureRequest: quote.signTypedData,
                                    witness: quote.witness,
                                    quoteRequestType: quote.quoteRequestType,
                                    withdrawalTransaction:
                                        earnWithdrawalTransaction,
                                    withdrawalFee,
                                    withdrawalGasEstimate: Hexadecimal.toBigInt(
                                        earnWithdrawalGasEstimate
                                    ),
                                    approvalTransaction:
                                        quote.permitApprovalTransaction,
                                    approvalFee: approvalFeeWithCorrectNonce,
                                    approvalGasEstimate:
                                        Hexadecimal.toBigInt(
                                            approvalGasEstimate
                                        ),
                                }),
                            }
                        default:
                            return notReachable(quote)
                    }
                }
                default:
                    return notReachable(form.taker.type)
            }
        }
        default:
            return notReachable(form)
    }
}
