import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { isCardTopupVanityEnabled } from '@zeal/domains/ABTest'
import { Account, AccountsMap } from '@zeal/domains/Account'
import { AddFunds } from '@zeal/domains/Account/features/AddFunds'
import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { FIAT_DUST } from '@zeal/domains/Currency/constants'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { sumEarn } from '@zeal/domains/Earn/helpers/sumEarn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { hasTokens } from '@zeal/domains/Portfolio/helpers/hasTokens'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { CardAddCashV2 } from './CardAddCashV2'
import { DataLoader } from './DataLoader'

type Props = {
    installationId: string
    initialSender: Account
    isEthereumNetworkFeeWarningSeen: boolean
    networkMap: NetworkMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    currencyPinMap: CurrencyPinMap
    customCurrencyMap: CustomCurrencyMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    customCurrencies: CustomCurrencyMap
    earnTakerMetrics: EarnTakerMetrics
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof DataLoader>
    | MsgOf<typeof AddFunds>
    | MsgOf<typeof CardAddCashV2>

type State = { type: 'add_funds_flow' } | { type: 'card_add_cash' }

const calculateState = ({
    portfolioMap,
    currencyHiddenMap,
    initialSender,
    defaultCurrencyConfig,
}: {
    initialSender: Account
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
}): State => {
    const cachedPortfolio = unsafe_GetPortfolioCache2({
        address: initialSender.address,
        portfolioMap,
    })

    const funded =
        cachedPortfolio &&
        (hasTokens({
            currencyHiddenMap,
            portfolio: cachedPortfolio,
        }) ||
            sumEarn({ earn: cachedPortfolio.earn, defaultCurrencyConfig })
                .amount > FIAT_DUST)

    if (!funded) {
        return { type: 'add_funds_flow' }
    }

    return {
        type: 'card_add_cash',
    }
}

export const Fork = ({
    onMsg,
    accountsMap,
    cardConfig,
    currencyHiddenMap,
    currencyPinMap,
    customCurrencyMap,
    defaultCurrencyConfig,
    feePresetMap,
    gasCurrencyPresetMap,
    isEthereumNetworkFeeWarningSeen,
    initialSender,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    sessionPassword,
    customCurrencies,
    earnTakerMetrics,
    experimentalMode,
}: Props) => {
    const [state, setState] = useState<State>(
        calculateState({
            defaultCurrencyConfig,
            currencyHiddenMap,
            initialSender,
            portfolioMap,
        })
    )

    switch (state.type) {
        case 'add_funds_flow':
            return (
                <AddFunds
                    accountsMap={accountsMap}
                    address={initialSender.address}
                    installationId={installationId}
                    networkMap={networkMap}
                    keyStoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    networkRPCMap={networkRPCMap}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    feePresetMap={feePresetMap}
                    customCurrencies={customCurrencies}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    cardConfig={cardConfig}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'add_wallet_clicked':
                            case 'hardware_wallet_clicked':
                            case 'on_bank_transfer_selected':
                            case 'on_address_scanned':
                            case 'on_address_scanned_and_add_label':
                            case 'on_ethereum_network_fee_warning_understand_clicked':
                            case 'track_wallet_clicked':
                            case 'on_account_create_request':
                            case 'on_accounts_create_success_animation_finished':
                            case 'on_add_label_to_track_only_account_during_send':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'cancel_submitted':
                            case 'transaction_request_replaced':
                            case 'transaction_submited':
                                onMsg(msg)
                                break
                            case 'on_top_up_transaction_complete_close':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                                onMsg(msg)
                                setState({ type: 'card_add_cash' })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'card_add_cash':
            return isCardTopupVanityEnabled({ experimentalMode }) ? (
                <CardAddCashV2
                    initialTopUpAmount={null}
                    installationId={installationId}
                    initialSenderAddress={initialSender.address}
                    cardConfig={cardConfig}
                    accountsMap={accountsMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    keyStoreMap={keyStoreMap}
                    currencyHiddenMap={currencyHiddenMap}
                    sessionPassword={sessionPassword}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    currencyPinMap={currencyPinMap}
                    customCurrencyMap={customCurrencyMap}
                    portfolioMap={portfolioMap}
                    onMsg={onMsg}
                    feePresetMap={feePresetMap}
                    earnTakerMetrics={earnTakerMetrics}
                />
            ) : (
                <DataLoader
                    customCurrencies={customCurrencies}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    initialSender={initialSender}
                    cardConfig={cardConfig}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    sessionPassword={sessionPassword}
                    installationId={installationId}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    customCurrencyMap={customCurrencyMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    portfolioMap={portfolioMap}
                    onMsg={onMsg}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
