import { useEffect } from 'react'

import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'

import { notReachable } from '@zeal/toolkit'
import { useReloadableData } from '@zeal/toolkit/LoadableData/ReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { HardwareWalletSupportDrop } from '@zeal/domains/Card/features/CardHardwareWalletSupportDrop'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    Earn,
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
    TakerPortfolioMap2,
} from '@zeal/domains/Earn'
import { fetchEarn } from '@zeal/domains/Earn/api/fetchEarn'
import { DEFAULT_TAKER_APY_MAP } from '@zeal/domains/Earn/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { unsafe_GetPortfolioCache2 } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import {
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Flow } from './Flow'

type Msg =
    | {
          type: 'on_earn_updated'
          ownerAddress: Web3.address.Address
          earn: Earn
      }
    | Extract<
          MsgOf<typeof Flow>,
          {
              type:
                  | 'on_dismiss_add_to_wallet_banner_clicked'
                  | 'add_wallet_clicked'
                  | 'cancel_submitted'
                  | 'hardware_wallet_clicked'
                  | 'import_card_owner_clicked'
                  | 'import_keys_button_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_account_create_request'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_activate_existing_monerium_account_click'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_bank_transfer_selected'
                  | 'on_card_freeze_toggle_failed'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_card_onboarded_account_state_received'
                  | 'on_card_owner_address_selected'
                  | 'on_card_transactions_fetch_success'
                  | 'on_cashback_loaded'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_card_disconnected'
                  | 'on_earn_last_recharge_transaction_hash_loaded'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_external_earn_deposit_completed_close_click'
                  | 'on_fallback_freeze_card_click'
                  | 'on_get_cashback_currency_clicked'
                  | 'on_monerium_deposit_success_go_to_wallet_clicked'
                  | 'on_notifications_config_changed'
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_rpc_change_confirmed'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_select_rpc_click'
                  | 'on_switch_bank_transfer_provider_clicked'
                  | 'on_switch_card_new_card_selected'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'recover_safe_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'track_wallet_clicked'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
                  | 'on_card_onboarded_state_refresh_pulled'
                  | 'on_gnosis_pay_account_created'
                  | 'on_order_import_card_clicked'
                  | 'on_order_new_card_clicked'
                  | 'on_do_bank_transfer_clicked'
                  | 'on_app_rating_submitted'
                  | 'on_cashback_celebration_triggered'
                  | 'on_top_up_transaction_complete_close'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_new_virtual_card_created_successfully'
                  | 'on_virtual_card_order_created_animation_completed'
                  | 'on_physical_card_activated_info_screen_closed'
                  | 'on_card_order_redirect_to_gnosis_pay_clicked'
                  | 'on_card_top_up_success'
                  | 'on_card_top_up_banner_dismissed'
                  | 'on_pending_card_top_up_state_changed'
          }
      >
    | MsgOf<typeof HardwareWalletSupportDrop>

type Props = {
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    cardReadOwnerSigner: Account
    keyStore: SigningKeyStore
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig

    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    isEthereumNetworkFeeWarningSeen: boolean
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    portfolioMap: PortfolioMap
    sessionPassword: string
    encryptedPassword: string
    currencyPinMap: CurrencyPinMap
    customCurrencyMap: CustomCurrencyMap
    notificationsConfig: NotificationsConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnTakerMetrics: EarnTakerMetrics
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    refreshContainerState: RefreshContainerState
    installationCampaign: string | null
    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

export const DataLoader = ({
    cardConfig,
    keyStore,
    cardReadOwnerSigner,
    customCurrencyMap,
    networkMap,
    networkRPCMap,
    accountsMap,
    earnTakerMetrics,
    keyStoreMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    isEthereumNetworkFeeWarningSeen,
    portfolioMap,
    sessionPassword,
    encryptedPassword,
    currencyHiddenMap,
    currencyPinMap,
    notificationsConfig,
    celebrationConfig,
    appRating,
    defaultCurrencyConfig,
    transactionActivitiesCacheMap,
    installationCampaign,
    refreshContainerState,
    historicalTakerUserCurrencyRateMap,
    experimentalMode,
    onMsg,
}: Props) => {
    const cachedEarn =
        unsafe_GetPortfolioCache2({
            address: cardConfig.readonlySignerAddress,
            portfolioMap,
        })?.earn || null

    const [loadable, setLoadable] = useReloadableData(
        fetchEarn,
        cachedEarn
            ? {
                  type: 'reloading',
                  params: {
                      earnOwnerAddress: cardConfig.readonlySignerAddress,
                      networkRPCMap,
                      defaultCurrencyConfig,
                      networkMap,
                  },
                  data: cachedEarn,
              }
            : {
                  type: 'loading',
                  params: {
                      earnOwnerAddress: cardConfig.readonlySignerAddress,
                      networkRPCMap,
                      defaultCurrencyConfig,
                      networkMap,
                  },
              }
    )

    const liveOnMsg = useLiveRef(onMsg)
    const liveCardConfigOwner = useLiveRef(cardConfig.readonlySignerAddress)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_earn_updated',
                    ownerAddress: liveCardConfigOwner.current,
                    earn: loadable.data,
                })
                break

            case 'reloading':
            case 'loading':
                break

            case 'subsequent_failed':
            case 'error':
                captureError(loadable.error)
                break

            /* istanbul ignore next */
            default:
                notReachable(loadable)
        }
    }, [loadable, liveOnMsg, liveCardConfigOwner])

    useEffect(() => {
        switch (refreshContainerState) {
            case 'refreshing':
                setLoadable((old) => {
                    switch (old.type) {
                        case 'loaded':
                        case 'reloading':
                        case 'subsequent_failed':
                            return {
                                type: 'reloading',
                                params: old.params,
                                data: old.data,
                            }
                        case 'loading':
                        case 'error':
                            return old
                        /* istanbul ignore next */
                        default:
                            return notReachable(old)
                    }
                })
                break

            case 'refreshed':
                break
            default:
                notReachable(refreshContainerState)
        }
    }, [refreshContainerState, setLoadable])

    switch (keyStore.type) {
        case 'ledger':
        case 'trezor':
            return (
                <HardwareWalletSupportDrop
                    variant="not_closable"
                    installationId={installationId}
                    installationCampaign={installationCampaign}
                    cardReadonlySignerAddress={cardConfig.readonlySignerAddress}
                    accountsMap={accountsMap}
                    keystoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    currencyHiddenMap={currencyHiddenMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    onMsg={onMsg}
                />
            )
        case 'private_key_store':
        case 'secret_phrase_key':
        case 'safe_4337':
            return (
                <Flow
                    experimentalMode={experimentalMode}
                    transactionActivitiesCacheMap={
                        transactionActivitiesCacheMap
                    }
                    historicalTakerUserCurrencyRateMap={
                        historicalTakerUserCurrencyRateMap
                    }
                    appRating={appRating}
                    installationCampaign={installationCampaign}
                    celebrationConfig={celebrationConfig}
                    refreshContainerState={refreshContainerState}
                    earnTakerMetrics={earnTakerMetrics}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    earn={(() => {
                        switch (loadable.type) {
                            case 'loaded':
                            case 'reloading':
                            case 'subsequent_failed':
                                return loadable.data
                            case 'loading':
                            case 'error':
                                // TODO :: @mike
                                // we need to rework whole usage of earn inside card tab, as this is wrong to fallback to not_configured earn
                                // - probably we should not fetch whole portfolio but only earn here
                                // - and not to open loadble switch here, but pass whole loadable to card tab
                                return {
                                    type: 'not_configured',
                                    takerApyMap: DEFAULT_TAKER_APY_MAP,
                                    holder: '' as Web3.address.Address,
                                    takers: [],
                                    takerPortfolioMap: {} as TakerPortfolioMap2,
                                }

                            default:
                                return notReachable(loadable)
                        }
                    })()}
                    notificationsConfig={notificationsConfig}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    cardConfig={cardConfig}
                    key={cardReadOwnerSigner.address}
                    currencyHiddenMap={currencyHiddenMap}
                    cardReadonlySigner={cardReadOwnerSigner}
                    keyStore={keyStore}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    encryptedPassword={encryptedPassword}
                    currencyPinMap={currencyPinMap}
                    customCurrencyMap={customCurrencyMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_create_smart_wallet_clicked':
                            case 'on_notifications_config_changed':
                            case 'on_4337_gas_currency_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'cancel_submitted':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_request_replaced':
                            case 'transaction_submited':
                            case 'track_wallet_clicked':
                            case 'on_account_create_request':
                            case 'add_wallet_clicked':
                            case 'hardware_wallet_clicked':
                            case 'safe_wallet_clicked':
                            case 'recover_safe_wallet_clicked':
                            case 'on_select_rpc_click':
                            case 'on_rpc_change_confirmed':
                            case 'on_card_freeze_toggle_failed':
                            case 'on_fallback_freeze_card_click':
                            case 'on_get_cashback_currency_clicked':
                            case 'on_card_transactions_fetch_success':
                            case 'on_earn_last_recharge_transaction_hash_loaded':
                            case 'on_cashback_loaded':
                            case 'on_card_onboarded_account_state_received':
                            case 'import_card_owner_clicked':
                            case 'on_card_disconnected':
                            case 'on_switch_card_new_card_selected':
                            case 'on_activate_existing_monerium_account_click':
                            case 'on_monerium_deposit_success_go_to_wallet_clicked':
                            case 'on_bank_transfer_selected':
                            case 'on_accounts_create_success_animation_finished':
                            case 'on_add_label_to_track_only_account_during_send':
                            case 'on_ethereum_network_fee_warning_understand_clicked':
                            case 'on_switch_bank_transfer_provider_clicked':
                            case 'on_address_scanned':
                            case 'on_address_scanned_and_add_label':
                            case 'on_usd_taker_metrics_loaded':
                            case 'on_eur_taker_metrics_loaded':
                            case 'on_chf_taker_metrics_loaded':
                            case 'on_card_onboarded_state_refresh_pulled':
                            case 'on_order_import_card_clicked':
                            case 'on_order_new_card_clicked':
                            case 'on_do_bank_transfer_clicked':
                            case 'on_app_rating_submitted':
                            case 'on_cashback_celebration_triggered':
                            case 'on_top_up_transaction_complete_close':
                            case 'on_swaps_io_swap_request_created':
                            case 'on_new_virtual_card_created_successfully':
                            case 'on_dismiss_add_to_wallet_banner_clicked':
                            case 'on_virtual_card_order_created_animation_completed':
                            case 'on_card_imported_success_animation_complete':
                            case 'on_onboarded_card_imported_success_animation_complete':
                            case 'on_card_import_on_import_keys_clicked':
                            case 'on_physical_card_activated_info_screen_closed':
                            case 'on_card_order_redirect_to_gnosis_pay_clicked':
                            case 'on_card_top_up_success':
                            case 'on_card_top_up_banner_dismissed':
                            case 'on_pending_card_top_up_state_changed':
                                onMsg(msg)
                                break
                            case 'on_recharge_configured':
                                setLoadable({
                                    type: 'loaded',
                                    data: msg.earn,
                                    params: loadable.params,
                                })
                                onMsg({
                                    type: 'on_earn_updated',
                                    ownerAddress: liveCardConfigOwner.current,
                                    earn: msg.earn,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}
