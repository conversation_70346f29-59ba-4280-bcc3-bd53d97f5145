import { useState } from 'react'

import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    Earn,
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
} from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Msg = Extract<
    MsgOf<typeof Layout>,
    {
        type:
            | 'on_dismiss_add_to_wallet_banner_clicked'
            | 'on_create_smart_wallet_clicked'
            | 'on_card_import_on_import_keys_clicked'
            | 'on_recharge_configured'
            | 'on_card_onboarded_account_state_received'
            | 'on_fallback_freeze_card_click'
            | 'on_card_freeze_toggle_failed'
            | 'on_predefined_fee_preset_selected'
            | 'on_account_create_request'
            | 'cancel_submitted'
            | 'on_4337_auto_gas_token_selection_clicked'
            | 'on_4337_gas_currency_selected'
            | 'track_wallet_clicked'
            | 'add_wallet_clicked'
            | 'hardware_wallet_clicked'
            | 'import_keys_button_clicked'
            | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
            | 'on_transaction_completed_splash_animation_screen_competed'
            | 'transaction_request_replaced'
            | 'transaction_submited'
            | 'safe_wallet_clicked'
            | 'recover_safe_wallet_clicked'
            | 'on_select_rpc_click'
            | 'on_rpc_change_confirmed'
            | 'on_card_transactions_fetch_success'
            | 'on_notifications_config_changed'
            | 'on_earn_last_recharge_transaction_hash_loaded'
            | 'on_get_cashback_currency_clicked'
            | 'on_cashback_loaded'
            | 'import_card_owner_clicked'
            | 'on_card_disconnected'
            | 'on_card_onboarded_state_refresh_pulled'
            | 'on_switch_card_new_card_selected'
            | 'on_activate_existing_monerium_account_click'
            | 'on_monerium_deposit_success_go_to_wallet_clicked'
            | 'on_bank_transfer_selected'
            | 'on_accounts_create_success_animation_finished'
            | 'on_add_label_to_track_only_account_during_send'
            | 'on_ethereum_network_fee_warning_understand_clicked'
            | 'on_switch_bank_transfer_provider_clicked'
            | 'on_address_scanned'
            | 'on_address_scanned_and_add_label'
            | 'on_usd_taker_metrics_loaded'
            | 'on_eur_taker_metrics_loaded'
            | 'on_chf_taker_metrics_loaded'
            | 'on_order_import_card_clicked'
            | 'on_order_new_card_clicked'
            | 'on_do_bank_transfer_clicked'
            | 'on_app_rating_submitted'
            | 'on_cashback_celebration_triggered'
            | 'on_top_up_transaction_complete_close'
            | 'on_swaps_io_swap_request_created'
            | 'on_new_virtual_card_created_successfully'
            | 'on_virtual_card_order_created_animation_completed'
            | 'on_card_imported_success_animation_complete'
            | 'on_onboarded_card_imported_success_animation_complete'
            | 'on_physical_card_activated_info_screen_closed'
            | 'on_card_order_redirect_to_gnosis_pay_clicked'
            | 'on_card_top_up_success'
            | 'on_card_top_up_banner_dismissed'
            | 'on_pending_card_top_up_state_changed'
    }
>

type Props = {
    cardReadonlySigner: Account
    keyStore: CardSlientSignKeyStore
    earn: Earn

    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    isEthereumNetworkFeeWarningSeen: boolean
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    keyStoreMap: KeyStoreMap
    earnTakerMetrics: EarnTakerMetrics
    portfolioMap: PortfolioMap
    sessionPassword: string
    currencyHiddenMap: CurrencyHiddenMap
    encryptedPassword: string
    currencyPinMap: CurrencyPinMap
    customCurrencyMap: CustomCurrencyMap
    celebrationConfig: CelebrationConfig
    appRating: AppRating

    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    notificationsConfig: NotificationsConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    refreshContainerState: RefreshContainerState
    installationCampaign: string | null
    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

export const Flow = ({
    onMsg,
    cardReadonlySigner,
    keyStore,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    sessionPassword,
    isEthereumNetworkFeeWarningSeen,
    encryptedPassword,
    currencyHiddenMap,
    customCurrencyMap,
    currencyPinMap,
    cardConfig,
    installationCampaign,
    celebrationConfig,
    appRating,
    notificationsConfig,
    earnTakerMetrics,
    earn,
    transactionActivitiesCacheMap,
    defaultCurrencyConfig,
    refreshContainerState,
    historicalTakerUserCurrencyRateMap,
    experimentalMode,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })
    return (
        <>
            <Layout
                experimentalMode={experimentalMode}
                transactionActivitiesCacheMap={transactionActivitiesCacheMap}
                earnHistoricalTakerUserCurrencyRateMap={
                    historicalTakerUserCurrencyRateMap
                }
                appRating={appRating}
                installationCampaign={installationCampaign}
                celebrationConfig={celebrationConfig}
                refreshContainerState={refreshContainerState}
                earnTakerMetrics={earnTakerMetrics}
                defaultCurrencyConfig={defaultCurrencyConfig}
                earn={earn}
                notificationsConfig={notificationsConfig}
                cardConfig={cardConfig}
                key={cardReadonlySigner.address}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                currencyHiddenMap={currencyHiddenMap}
                cardReadonlySigner={cardReadonlySigner}
                keyStore={keyStore}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                installationId={installationId}
                keyStoreMap={keyStoreMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                portfolioMap={portfolioMap}
                sessionPassword={sessionPassword}
                encryptedPassword={encryptedPassword}
                currencyPinMap={currencyPinMap}
                customCurrencyMap={customCurrencyMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_create_smart_wallet_clicked':
                        case 'on_recharge_configured':
                        case 'on_card_onboarded_account_state_received':
                        case 'on_fallback_freeze_card_click':
                        case 'on_card_freeze_toggle_failed':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_account_create_request':
                        case 'cancel_submitted':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'track_wallet_clicked':
                        case 'add_wallet_clicked':
                        case 'hardware_wallet_clicked':
                        case 'import_keys_button_clicked':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_request_replaced':
                        case 'transaction_submited':
                        case 'safe_wallet_clicked':
                        case 'recover_safe_wallet_clicked':
                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                        case 'on_card_transactions_fetch_success':
                        case 'on_notifications_config_changed':
                        case 'on_earn_last_recharge_transaction_hash_loaded':
                        case 'on_get_cashback_currency_clicked':
                        case 'on_cashback_loaded':
                        case 'import_card_owner_clicked':
                        case 'on_card_disconnected':
                        case 'on_card_onboarded_state_refresh_pulled':
                        case 'on_switch_card_new_card_selected':
                        case 'on_activate_existing_monerium_account_click':
                        case 'on_monerium_deposit_success_go_to_wallet_clicked':
                        case 'on_bank_transfer_selected':
                        case 'on_accounts_create_success_animation_finished':
                        case 'on_add_label_to_track_only_account_during_send':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_switch_bank_transfer_provider_clicked':
                        case 'on_address_scanned':
                        case 'on_address_scanned_and_add_label':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_chf_taker_metrics_loaded':
                        case 'on_order_import_card_clicked':
                        case 'on_order_new_card_clicked':
                        case 'on_do_bank_transfer_clicked':
                        case 'on_app_rating_submitted':
                        case 'on_cashback_celebration_triggered':
                        case 'on_top_up_transaction_complete_close':
                        case 'on_swaps_io_swap_request_created':
                        case 'on_new_virtual_card_created_successfully':
                        case 'on_dismiss_add_to_wallet_banner_clicked':
                        case 'on_card_import_on_import_keys_clicked':
                        case 'on_card_imported_success_animation_complete':
                        case 'on_onboarded_card_imported_success_animation_complete':
                        case 'on_physical_card_activated_info_screen_closed':
                        case 'on_card_order_redirect_to_gnosis_pay_clicked':
                        case 'on_card_top_up_success':
                        case 'on_card_top_up_banner_dismissed':
                        case 'on_pending_card_top_up_state_changed':
                            onMsg(msg)
                            break

                        case 'on_transaction_item_clicked':
                            setModal({
                                type: 'card_transaction_details',
                                transaction: msg.transaction,
                                earn,
                            })
                            break

                        case 'on_virtual_card_order_created_animation_completed':
                            setModal({
                                type: 'wallet_pay_flow',
                                gnosisPayAccountOnboardedState:
                                    msg.gnosisAccountState,
                                platform: msg.platform,
                            })
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />

            <Modal
                historicalTakerUserCurrencyRateMap={
                    historicalTakerUserCurrencyRateMap
                }
                sessionPassword={sessionPassword}
                keyStore={keyStore}
                cardReadonlySigner={cardReadonlySigner}
                state={modal}
                networkRpcMap={networkRPCMap}
                cardConfig={cardConfig}
                networkMap={networkMap}
                defaultCurrencyConfig={defaultCurrencyConfig}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_skip_add_to_wallet_button_clicked':
                            setModal({ type: 'closed' })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
                encryptedPassword={encryptedPassword}
                installationId={installationId}
            />
        </>
    )
}
