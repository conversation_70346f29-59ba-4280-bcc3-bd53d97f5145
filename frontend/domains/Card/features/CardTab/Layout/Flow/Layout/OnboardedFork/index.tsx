import { useEffect } from 'react'

import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'

import { noop, notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { CardLoadingScreen } from '@zeal/domains/Card/components/CardLoadingScreen'
import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import { ConfigureCardSafe } from '@zeal/domains/Card/features/ConfigureCardSafe'
import { getOnboardedCardConfig } from '@zeal/domains/Card/helpers/getOnboardedCardConfig'
import { predictDelayModAddress } from '@zeal/domains/Card/helpers/predictDelayModAddress'
import { predictRolesModAddress } from '@zeal/domains/Card/helpers/predictRolesModAddress'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    Earn,
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
} from '@zeal/domains/Earn'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { requestIsContractDeployed } from '@zeal/domains/RPCRequest/api/fetchIsContractDeployed'
import { fetchRPCBatch2WithRetry } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import {
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Onboarded } from '../Onboarded'

type Props = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    networkRPCMap: NetworkRPCMap
    keyStore: CardSlientSignKeyStore
    encryptedPassword: string
    earn: Earn
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    sessionPassword: string
    currencyPinMap: CurrencyPinMap
    networkMap: NetworkMap
    customCurrencyMap: CustomCurrencyMap
    isEthereumNetworkFeeWarningSeen: boolean
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    notificationsConfig: NotificationsConfig
    cardReadonlySigner: Account
    earnTakerMetrics: EarnTakerMetrics
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    refreshContainerState: RefreshContainerState
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof CardLoadingScreen> | MsgOf<typeof Onboarded>

const fetch = async ({
    cardSafeAddress,
    networkRPCMap,
    signal,
}: {
    cardSafeAddress: Web3.address.Address
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<
    | { type: 'safe_configured_correctly' }
    | {
          type: 'safe_misconfigured'
      }
> => {
    const delayModAddress = predictDelayModAddress(cardSafeAddress)
    const rolesModAddress = predictRolesModAddress(cardSafeAddress)

    const [isDelayModDeployed, isRolesModDeployed] =
        await fetchRPCBatch2WithRetry(
            [
                requestIsContractDeployed({
                    address: delayModAddress,
                }),
                requestIsContractDeployed({
                    address: rolesModAddress,
                }),
            ],
            {
                networkRPCMap,
                network: CARD_NETWORK,
                signal,
            }
        )

    return isDelayModDeployed && isRolesModDeployed
        ? { type: 'safe_configured_correctly' }
        : { type: 'safe_misconfigured' }
}

// FIXME :: @Nicvaniek remove this and fix parsing instead
export const OnboardedFork = ({
    onMsg,
    gnosisPayAccountOnboardedState,
    keyStore,
    transactionActivitiesCacheMap,
    defaultCurrencyConfig,
    installationId,
    cardConfig,
    networkRPCMap,
    portfolioMap,
    currencyHiddenMap,
    customCurrencyMap,
    currencyPinMap,
    gasCurrencyPresetMap,
    feePresetMap,
    networkMap,
    earnHistoricalTakerUserCurrencyRateMap,
    earnTakerMetrics,
    earn,
    accountsMap,
    keyStoreMap,
    appRating,
    celebrationConfig,
    isEthereumNetworkFeeWarningSeen,
    notificationsConfig,
    refreshContainerState,
    sessionPassword,
    encryptedPassword,
    cardReadonlySigner,
    experimentalMode,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            cardSafeAddress: gnosisPayAccountOnboardedState.cardSafe.address,
            networkRPCMap,
        },
    })

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
                break
            case 'loaded':
                switch (loadable.data.type) {
                    case 'safe_misconfigured':
                        postUserEvent({
                            type: 'CardOnboardedModulesNotDeployedEvent',
                            installationId,
                            userId: gnosisPayAccountOnboardedState.userId,
                            cardSafe:
                                gnosisPayAccountOnboardedState.cardSafe.address,
                        })
                        break
                    case 'safe_configured_correctly':
                        break
                    default:
                        notReachable(loadable.data)
                        break
                }
                break
            case 'error':
                captureError(loadable.error)
                break
            default:
                notReachable(loadable)
        }
    }, [
        loadable,
        gnosisPayAccountOnboardedState.cardSafe.address,
        gnosisPayAccountOnboardedState.userId,
        installationId,
    ])

    switch (loadable.type) {
        case 'loading':
            return (
                <CardLoadingScreen
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    cardConfig={cardConfig}
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )
        case 'loaded':
            switch (loadable.data.type) {
                case 'safe_misconfigured':
                    const { cardSafe } = gnosisPayAccountOnboardedState

                    return (
                        <ConfigureCardSafe
                            cardSafe={{
                                type: 'currency_configured',
                                address: cardSafe.address,
                                cryptoCurrency: cardSafe.cryptoCurrency,
                                fiatCurrency: cardSafe.fiatCurrency,
                            }}
                            cardConfig={cardConfig}
                            location="card_tab_temporary"
                            keyStore={keyStore}
                            networkRPCMap={networkRPCMap}
                            sessionPassword={sessionPassword}
                            installationId={installationId}
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'close':
                                        noop() // cannot close on card tab
                                        break
                                    case 'card_safe_configured':
                                        setLoadable({
                                            type: 'loading',
                                            params: loadable.params,
                                        })
                                        break
                                    default:
                                        return notReachable(msg)
                                }
                            }}
                        />
                    )
                case 'safe_configured_correctly':
                    return (
                        <Onboarded
                            experimentalMode={experimentalMode}
                            transactionActivitiesCacheMap={
                                transactionActivitiesCacheMap
                            }
                            earnHistoricalTakerUserCurrencyRateMap={
                                earnHistoricalTakerUserCurrencyRateMap
                            }
                            appRating={appRating}
                            celebrationConfig={celebrationConfig}
                            refreshContainerState={refreshContainerState}
                            earnTakerMetrics={earnTakerMetrics}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            isEthereumNetworkFeeWarningSeen={
                                isEthereumNetworkFeeWarningSeen
                            }
                            cardOwnerEarn={earn}
                            keyStore={keyStore}
                            cardReadonlySigner={cardReadonlySigner}
                            notificationsConfig={notificationsConfig}
                            cardConfig={getOnboardedCardConfig(
                                cardConfig,
                                gnosisPayAccountOnboardedState
                            )}
                            networkMap={networkMap}
                            keyStoreMap={keyStoreMap}
                            portfolioMap={portfolioMap}
                            currencyHiddenMap={currencyHiddenMap}
                            sessionPassword={sessionPassword}
                            networkRPCMap={networkRPCMap}
                            currencyPinMap={currencyPinMap}
                            customCurrencyMap={customCurrencyMap}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            accountsMap={accountsMap}
                            installationId={installationId}
                            encryptedPassword={encryptedPassword}
                            initialGnosisPayAccountOnboardedState={
                                gnosisPayAccountOnboardedState
                            }
                            onMsg={onMsg}
                        />
                    )
                default:
                    return notReachable(loadable.data)
            }
        case 'error':
            return (
                <Onboarded
                    experimentalMode={experimentalMode}
                    transactionActivitiesCacheMap={
                        transactionActivitiesCacheMap
                    }
                    earnHistoricalTakerUserCurrencyRateMap={
                        earnHistoricalTakerUserCurrencyRateMap
                    }
                    appRating={appRating}
                    celebrationConfig={celebrationConfig}
                    refreshContainerState={refreshContainerState}
                    earnTakerMetrics={earnTakerMetrics}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    cardOwnerEarn={earn}
                    keyStore={keyStore}
                    cardReadonlySigner={cardReadonlySigner}
                    notificationsConfig={notificationsConfig}
                    cardConfig={getOnboardedCardConfig(
                        cardConfig,
                        gnosisPayAccountOnboardedState
                    )}
                    networkMap={networkMap}
                    keyStoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    currencyHiddenMap={currencyHiddenMap}
                    sessionPassword={sessionPassword}
                    networkRPCMap={networkRPCMap}
                    currencyPinMap={currencyPinMap}
                    customCurrencyMap={customCurrencyMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    accountsMap={accountsMap}
                    installationId={installationId}
                    encryptedPassword={encryptedPassword}
                    initialGnosisPayAccountOnboardedState={
                        gnosisPayAccountOnboardedState
                    }
                    onMsg={onMsg}
                />
            )
        default:
            return notReachable(loadable)
    }
}
