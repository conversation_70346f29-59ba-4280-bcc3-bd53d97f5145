import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountNotOnboardedState,
    ReadonlySignerSelectedCardConfig,
} from '@zeal/domains/Card'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    gnosisPayAccountState: GnosisPayAccountNotOnboardedState

    installationId: string

    cardConfig: ReadonlySignerSelectedCardConfig
    cardReadOnlySigner: Account
    keyStore: CardSlientSignKeyStore
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    currencyHiddenMap: CurrencyHiddenMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationCampaign: string | null
    earnTakerMetrics: EarnTakerMetrics

    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_card_disconnected'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_activate_existing_monerium_account_click'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_switch_bank_transfer_provider_clicked'
                  | 'on_monerium_deposit_success_go_to_wallet_clicked'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_do_bank_transfer_clicked'
                  | 'on_gnosis_pay_onboarding_flow_closed'
                  | 'on_gnosis_pay_kyc_submitted_animation_complete'
                  | 'on_virtual_card_order_created_animation_completed'
                  | 'on_physical_card_activated_info_screen_closed'
                  | 'on_card_order_redirect_to_gnosis_pay_clicked'
                  | 'on_virtual_card_order_onboarding_closed'
                  | 'on_activate_physical_card_closed'
                  | 'on_physical_card_order_onboarding_closed'
                  | 'on_new_physical_card_created_successfully'
          }
      >

export const Flow = ({
    gnosisPayAccountState,
    installationId,
    portfolioMap,
    accountsMap,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    currencyHiddenMap,
    keyStore,
    gasCurrencyPresetMap,
    feePresetMap,
    cardConfig,
    defaultCurrencyConfig,
    installationCampaign,
    cardReadOnlySigner,
    sessionPassword,
    earnTakerMetrics,
    onMsg,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    return (
        <>
            <Layout
                installationCampaign={installationCampaign}
                gnosisPayAccountState={gnosisPayAccountState}
                cardConfig={cardConfig}
                defaultCurrencyConfig={defaultCurrencyConfig}
                currencyHiddenMap={currencyHiddenMap}
                portfolioMap={portfolioMap}
                keyStoreMap={keyStoreMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                installationId={installationId}
                keyStore={keyStore}
                cardReadOnlySigner={cardReadOnlySigner}
                sessionPassword={sessionPassword}
                earnTakerMetrics={earnTakerMetrics}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_card_disconnected':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'on_activate_existing_monerium_account_click':
                        case 'on_create_smart_wallet_clicked':
                        case 'on_switch_bank_transfer_provider_clicked':
                        case 'on_monerium_deposit_success_go_to_wallet_clicked':
                        case 'on_card_import_on_import_keys_clicked':
                        case 'on_card_imported_success_animation_complete':
                        case 'on_onboarded_card_imported_success_animation_complete':
                            onMsg(msg)
                            break
                        case 'on_virtual_order_card_clicked':
                            setModal({
                                type: 'virtual_card_order_onboarding_flow',
                                gnosisPayAccountState:
                                    msg.gnosisPayAccountState,
                            })
                            break

                        case 'on_continue_kyc_clicked':
                            switch (msg.gnosisPayState.state) {
                                case 'terms_not_accepted':
                                case 'terms_accepted_kyc_not_started':
                                case 'kyc_started_documents_requested':
                                case 'kyc_started_resubmission_requested':
                                    setModal({
                                        type: 'gnosis_pay_onboarding_flow',
                                        gnosisPayState: msg.gnosisPayState,
                                    })
                                    break
                                case 'kyc_started_verification_in_progress':
                                    setModal({ type: 'kyc_in_progress_popup' })
                                    break
                                case 'kyc_failed':
                                    setModal({ type: 'kyc_failed_popup' })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg.gnosisPayState)
                            }
                            break
                        case 'on_activate_card_clicked':
                            setModal({
                                type: 'activate_physical_card_onboarding_flow',
                                gnosisPayState: msg.gnosisPayAccountState,
                            })

                            break
                        case 'on_status_widget_clicked':
                            switch (msg.state.state) {
                                case 'terms_not_accepted':
                                case 'terms_accepted_kyc_not_started':
                                case 'kyc_started_documents_requested':
                                case 'kyc_started_resubmission_requested':
                                    setModal({
                                        type: 'gnosis_pay_onboarding_flow',
                                        gnosisPayState: msg.state,
                                    })
                                    break
                                case 'kyc_started_verification_in_progress':
                                    setModal({ type: 'kyc_in_progress_popup' })
                                    break
                                case 'kyc_failed':
                                    setModal({ type: 'kyc_failed_popup' })
                                    break
                                case 'card_order_pending_payment':
                                case 'card_order_ready':
                                    setModal({
                                        type: 'physical_card_order_flow',
                                        gnosisPayState: msg.state,
                                    })
                                    break

                                case 'card_created_from_order':
                                    postUserEvent({
                                        type: 'ClickActivateCardBannerEvent',
                                        installationId: installationId,
                                    })

                                    setModal({
                                        type: 'activate_physical_card_onboarding_flow',
                                        gnosisPayState: msg.state,
                                    })

                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg.state)
                            }
                            break

                        case 'on_fully_configured_safe_activate_card_clicked':
                            setModal({
                                type: 'activate_physical_card_flow',
                                gnosisPayAccountState:
                                    msg.gnosisPayAccountState,
                            })
                            break

                        case 'on_physical_card_order_clicked':
                        case 'on_continue_physical_card_order_clicked':
                            setModal({
                                type: 'physical_card_order_flow',
                                gnosisPayState: msg.gnosisPayAccountState,
                            })
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                cardConfig={cardConfig}
                cardReadonlySigner={cardReadOnlySigner}
                networkMap={networkMap}
                defaultCurrencyConfig={defaultCurrencyConfig}
                sessionPassword={sessionPassword}
                keyStore={keyStore}
                networkRPCMap={networkRPCMap}
                installationId={installationId}
                location="card_tab"
                state={modal}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_activate_free_card_clicked':
                            setModal({ type: 'closed' })
                            break
                        case 'on_do_bank_transfer_clicked':
                        case 'on_gnosis_pay_onboarding_flow_closed':
                        case 'on_gnosis_pay_kyc_submitted_animation_complete':
                        case 'on_virtual_card_order_created_animation_completed':
                        case 'on_physical_card_activated_info_screen_closed':
                        case 'on_card_order_redirect_to_gnosis_pay_clicked':
                        case 'on_virtual_card_order_onboarding_closed':
                        case 'on_activate_physical_card_closed':
                        case 'on_physical_card_order_onboarding_closed':
                        case 'on_new_physical_card_created_successfully':
                            onMsg(msg)
                            setModal({ type: 'closed' })
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
