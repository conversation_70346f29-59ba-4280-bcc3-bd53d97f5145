import { useCallback, useEffect, useState } from 'react'

import { Side } from '@zeal/uikit/CardWidget'
import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'

import { noop, notReachable } from '@zeal/toolkit'
import { useLoadedReloadableData } from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { useReloadableData } from '@zeal/toolkit/LoadableData/ReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    CardTransaction,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedOnboardedCardConfig,
    SubmittedCardTopUp,
} from '@zeal/domains/Card'
import { fetchDelayQueueState } from '@zeal/domains/Card/api/fetchDelayQueueState'
import { fetchGnosisPayOnboardedState } from '@zeal/domains/Card/api/fetchGnosisPayAccountState'
import { fetchTransactionsWithSilentLogin } from '@zeal/domains/Card/api/fetchTransactions'
import {
    CARD_NETWORK,
    DELAY_QUEUE_POLL_INTERVAL_MS,
    SENSITIVE_SECRET_VIEW_TIMEOUT_SECONDS,
} from '@zeal/domains/Card/constants'
import { CardCashback } from '@zeal/domains/Card/domains/Cashback'
import { fetchCardCashBack } from '@zeal/domains/Card/domains/Cashback/api/fetchCardCashBack'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    Earn,
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
} from '@zeal/domains/Earn'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { getTransactionActivitiesCache } from '@zeal/domains/Transactions/helpers/getTransactionActivitiesCache'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, State } from './Modal'

type Props = {
    encryptedPassword: string
    cardOwnerEarn: Earn
    accountsMap: AccountsMap
    installationId: string
    initialGnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    currencyPinMap: CurrencyPinMap
    networkMap: NetworkMap
    customCurrencyMap: CustomCurrencyMap
    isEthereumNetworkFeeWarningSeen: boolean
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    notificationsConfig: NotificationsConfig
    cardReadonlySigner: Account
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    keyStore: CardSlientSignKeyStore
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnTakerMetrics: EarnTakerMetrics
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    refreshContainerState: RefreshContainerState
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_card_transactions_fetch_success'
          transactions: CardTransaction[]
      }
    | { type: 'on_cashback_loaded'; cardCashBack: CardCashback }
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_predefined_fee_preset_selected'
                  | 'on_account_create_request'
                  | 'cancel_submitted'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'track_wallet_clicked'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'import_keys_button_clicked'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_notifications_config_changed'
                  | 'on_recharge_configured'
                  | 'on_card_disconnected'
                  | 'import_card_owner_clicked'
                  | 'on_switch_card_new_card_selected'
                  | 'on_bank_transfer_selected'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
                  | 'on_get_cashback_currency_clicked'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_top_up_transaction_complete_close'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_new_virtual_card_created_successfully'
                  | 'on_physical_card_activated_info_screen_closed'
                  | 'on_card_order_redirect_to_gnosis_pay_clicked'
                  | 'on_card_top_up_success'
          }
      >
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_account_create_request'
                  | 'on_card_freeze_toggle_failed'
                  | 'on_card_onboarded_state_refresh_pulled'
                  | 'on_cashback_loaded'
                  | 'on_earn_last_recharge_transaction_hash_loaded'
                  | 'on_fallback_freeze_card_click'
                  | 'on_get_cashback_currency_clicked'
                  | 'on_transaction_item_clicked'
                  | 'on_app_rating_submitted'
                  | 'on_cashback_celebration_triggered'
                  | 'on_dismiss_add_to_wallet_banner_clicked'
                  | 'on_card_top_up_banner_dismissed'
                  | 'on_pending_card_top_up_state_changed'
          }
      >

export const Onboarded = ({
    accountsMap,
    initialGnosisPayAccountOnboardedState,
    cardConfig,
    installationId,
    gasCurrencyPresetMap,
    customCurrencyMap,
    currencyHiddenMap,
    feePresetMap,
    currencyPinMap,
    transactionActivitiesCacheMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    keyStoreMap,
    earnTakerMetrics,
    sessionPassword,
    cardOwnerEarn,
    cardReadonlySigner,
    onMsg,
    encryptedPassword,
    appRating,
    celebrationConfig,
    isEthereumNetworkFeeWarningSeen,
    notificationsConfig,
    keyStore,
    defaultCurrencyConfig,
    refreshContainerState,
    earnHistoricalTakerUserCurrencyRateMap,
    experimentalMode,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'closed' })
    const [side, setSide] = useState<Side>('front')

    const [submittedCardTopUps, setSubmittedCardTopUps] = useState<
        SubmittedCardTopUp[]
    >(
        () =>
            getTransactionActivitiesCache({
                address: cardReadonlySigner.address,
                transactionActivitiesCacheMap,
            }).pendingCardTopUpTransactionActivity
    )

    const captureErrorOnce = useCaptureErrorOnce()

    const onMsgLive = useLiveRef(onMsg)

    const [
        gnosisPayOnBoardedStateLoadable,
        setGnosisPayOnBoardedStateLoadable,
    ] = useLoadedReloadableData(fetchGnosisPayOnboardedState, {
        type: 'loaded',
        params: {
            gnosisPayAccountOnboardedState:
                initialGnosisPayAccountOnboardedState,
            selectedCardId: cardConfig.selectedCardId,
            networkRPCMap,
            networkMap,
            defaultCurrencyConfig,
            installationId,
            sessionPassword,
            keyStore,
            readonlySignerAddress: cardReadonlySigner.address,
        },
        data: initialGnosisPayAccountOnboardedState,
    })

    const gnosisPayAccountOnboardedState = gnosisPayOnBoardedStateLoadable.data

    const [transactionsLoadable, setTransactionsLoadable] = useReloadableData(
        fetchTransactionsWithSilentLogin,
        cardConfig.cardTransactionsCache?.length
            ? {
                  type: 'reloading',
                  data: cardConfig.cardTransactionsCache,
                  params: {
                      sessionPassword,
                      keyStore,
                      readonlySignerAddress: cardReadonlySigner.address,
                      afterTimestampMs:
                          cardConfig.cardTransactionsCache[
                              cardConfig.cardTransactionsCache.length - 1
                          ].createdAt - 1,
                      beforeTimestampMs: null,
                  },
              }
            : {
                  type: 'loading',
                  params: {
                      sessionPassword,
                      keyStore,
                      readonlySignerAddress: cardReadonlySigner.address,
                      afterTimestampMs: null,
                      beforeTimestampMs: null,
                  },
              }
    )

    const [cardCashBackLoadable, setCardCashBackLoadable] =
        useLoadedReloadableData(fetchCardCashBack, {
            type: 'reloading',
            params: {
                networkRPCMap,
                networkMap,
                defaultCurrencyConfig,
                gnosisPayAccountOnboardedState:
                    initialGnosisPayAccountOnboardedState,
                sessionPassword,
                keyStore,
                readonlySignerAddress: cardReadonlySigner.address,
            },
            data: cardConfig.cashback,
        })

    useEffect(() => {
        switch (transactionsLoadable.type) {
            case 'loaded':
                onMsgLive.current({
                    type: 'on_card_transactions_fetch_success',
                    transactions: transactionsLoadable.data,
                })
                break
            case 'subsequent_failed':
            case 'reloading':
            case 'loading':
            case 'error':
                break

            default:
                notReachable(transactionsLoadable)
        }
    }, [transactionsLoadable, onMsgLive])

    useEffect(() => {
        switch (side) {
            case 'front':
                return noop
            case 'back':
                const timer = setTimeout(() => {
                    setSide('front')
                }, SENSITIVE_SECRET_VIEW_TIMEOUT_SECONDS * 1000)
                return () => clearTimeout(timer)
            /* istanbul ignore next */
            default:
                return notReachable(side)
        }
    }, [side])

    useEffect(() => {
        switch (cardCashBackLoadable.type) {
            case 'loaded':
                onMsgLive.current({
                    type: 'on_cashback_loaded',
                    cardCashBack: cardCashBackLoadable.data,
                })
                break

            case 'reloading':
            case 'subsequent_failed':
                break

            default:
                notReachable(cardCashBackLoadable)
        }
    }, [cardCashBackLoadable, onMsgLive])

    const [delayQueueStatePollable, setDelayQueueStatePollable] =
        usePollableData(
            fetchDelayQueueState,
            {
                type: 'loading',
                params: {
                    cardSafeAddress:
                        gnosisPayAccountOnboardedState.cardSafe.address,
                    network: CARD_NETWORK,
                    networkRPCMap,
                },
            },
            { pollIntervalMilliseconds: DELAY_QUEUE_POLL_INTERVAL_MS }
        )

    useEffect(() => {
        switch (delayQueueStatePollable.type) {
            case 'loaded':
            case 'reloading':
            case 'loading':
                break
            case 'subsequent_failed':
            case 'error':
                captureErrorOnce(delayQueueStatePollable.error)
                break

            /* istanbul ignore next */
            default:
                return notReachable(delayQueueStatePollable)
        }
    }, [delayQueueStatePollable, captureErrorOnce])

    const refreshAllLoadables = useCallback(() => {
        setGnosisPayOnBoardedStateLoadable((old) => ({
            type: 'reloading',
            params: old.params,
            data: old.data,
        }))

        setCardCashBackLoadable((old) => ({
            type: 'reloading',
            data: old.data,
            params: old.params,
        }))

        setTransactionsLoadable((old) => {
            switch (old.type) {
                case 'loaded':
                case 'reloading':
                case 'subsequent_failed':
                    return {
                        type: 'reloading',
                        params: old.params,
                        data: old.data,
                    }
                case 'loading':
                case 'error':
                    return {
                        type: 'loading',
                        params: old.params,
                    }
                default:
                    return notReachable(old)
            }
        })
    }, [
        setCardCashBackLoadable,
        setGnosisPayOnBoardedStateLoadable,
        setTransactionsLoadable,
    ])

    useEffect(() => {
        switch (refreshContainerState) {
            case 'refreshing':
                refreshAllLoadables()
                break

            case 'refreshed':
                break

            default:
                notReachable(refreshContainerState)
        }
    }, [refreshContainerState, refreshAllLoadables])

    return (
        <>
            <Layout
                submittedCardTopUps={submittedCardTopUps}
                encryptedPassword={encryptedPassword}
                appRating={appRating}
                celebrationConfig={celebrationConfig}
                refreshContainerState={refreshContainerState}
                defaultCurrencyConfig={defaultCurrencyConfig}
                cardCashbackLoadable={cardCashBackLoadable}
                transactionsLoadable={transactionsLoadable}
                gnosisPayAccountOnboardedStateLoadable={
                    gnosisPayOnBoardedStateLoadable
                }
                delayQueueStatePollable={delayQueueStatePollable}
                keyStore={keyStore}
                portfolioMap={portfolioMap}
                networkMap={networkMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                feePresetMap={feePresetMap}
                accountsMap={accountsMap}
                keystores={keyStoreMap}
                sessionPassword={sessionPassword}
                cardOwnerEarn={cardOwnerEarn}
                cardReadonlySigner={cardReadonlySigner}
                networkRPCMap={networkRPCMap}
                side={side}
                installationId={installationId}
                cardConfig={cardConfig}
                currencyPinMap={currencyPinMap}
                currencyHiddenMap={currencyHiddenMap}
                onMsg={async (msg) => {
                    switch (msg.type) {
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_fallback_freeze_card_click':
                        case 'on_card_freeze_toggle_failed':
                        case 'on_transaction_item_clicked':
                        case 'on_earn_last_recharge_transaction_hash_loaded':
                        case 'on_get_cashback_currency_clicked':
                        case 'import_card_owner_clicked':
                        case 'on_card_onboarded_state_refresh_pulled':
                        case 'on_app_rating_submitted':
                        case 'on_cashback_celebration_triggered':
                        case 'on_dismiss_add_to_wallet_banner_clicked':
                        case 'on_new_virtual_card_created_successfully':
                        case 'on_switch_card_new_card_selected':
                            onMsg(msg)
                            break
                        case 'on_card_top_up_banner_dismissed':
                            setSubmittedCardTopUps((prev) =>
                                prev.filter(
                                    (i) =>
                                        i.startedAtMs !==
                                        msg.submittedCardTopUp.startedAtMs
                                )
                            )
                            onMsg(msg)
                            break
                        case 'on_pending_card_balance_timer_completed':
                            setGnosisPayOnBoardedStateLoadable({
                                type: 'reloading',
                                params: gnosisPayOnBoardedStateLoadable.params,
                                data: gnosisPayOnBoardedStateLoadable.data,
                            })
                            break
                        case 'on_pending_card_top_up_state_changed':
                            onMsg(msg)
                            switch (msg.submittedCardTopUp.state.type) {
                                case 'completed_with_multiple_tx':
                                case 'completed_with_single_tx':
                                    setGnosisPayOnBoardedStateLoadable(
                                        (old) => ({
                                            type: 'reloading',
                                            params: old.params,
                                            data: old.data,
                                        })
                                    )

                                    break
                                case 'waiting_for_user_operation':
                                case 'failed_user_operation':
                                case 'waiting_for_transaction':
                                case 'failed_tx':
                                case 'waiting_for_swap':
                                case 'failed_bungee':
                                case 'waiting_for_indexer_single_tx':
                                case 'waiting_for_indexer_multiple_tx':
                                    break
                                default:
                                    return notReachable(
                                        msg.submittedCardTopUp.state
                                    )
                            }
                            break
                        case 'on_card_freeze_toggle_succeeded':
                            setGnosisPayOnBoardedStateLoadable({
                                type: 'loaded',
                                data: {
                                    ...gnosisPayAccountOnboardedState,
                                    selectedCard: {
                                        ...gnosisPayAccountOnboardedState.selectedCard,
                                        state: {
                                            type: 'card_activated',
                                            status: msg.status,
                                        },
                                    },
                                },
                                params: gnosisPayOnBoardedStateLoadable.params,
                            })
                            break

                        case 'on_cashback_deposit_success':
                        case 'on_cashback_fetched_after_onboarding':
                            setCardCashBackLoadable({
                                type: 'loaded',
                                params: cardCashBackLoadable.params,
                                data: msg.cardCashback,
                            })
                            break

                        case 'on_card_settings_clicked':
                            postUserEvent({
                                type: 'ClickCardSettingButtonEvent',
                                installationId,
                            })
                            setState({ type: 'card_settings' })
                            break
                        case 'on_add_cash_to_card_click':
                            setState({ type: 'add_cash' })
                            break
                        case 'on_see_all_transactions_clicked':
                            setState({
                                type: 'card_activity',
                                recentTransactions: msg.recentTransactions,
                            })
                            break
                        case 'on_whole_card_clicked':
                        case 'on_show_card_details_click': {
                            const cardStatus =
                                gnosisPayAccountOnboardedState.selectedCard
                                    .state.status
                            switch (cardStatus) {
                                case 'frozen':
                                case 'active':
                                    switch (side) {
                                        case 'front':
                                            setState({
                                                type: 'lock_screen_popup',
                                            })
                                            break
                                        case 'back':
                                            setSide('front')
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            notReachable(side)
                                    }
                                    break
                                case 'cancelled':
                                    noop() // we cannot show card details for cancelled cards
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(cardStatus)
                            }
                            break
                        }
                        case 'on_enabled_recharge_card_tag_clicked': {
                            setState({
                                type: 'update_recharge',
                                cardRecharge: msg.cardRecharge,
                                earn: msg.earn,
                            })
                            break
                        }

                        case 'on_disabled_recharge_card_tag_clicked':
                            setState({ type: 'setup_recharge' })
                            break

                        case 'on_card_transactions_loading_retry_clicked':
                            setTransactionsLoadable({
                                type: 'loading',
                                params: transactionsLoadable.params,
                            })
                            break

                        case 'on_withdraw_click':
                            setState({ type: 'card_withdraw' })
                            break
                        case 'on_cashback_withdraw_queued_successfully':
                            setDelayQueueStatePollable((old) => {
                                switch (old.type) {
                                    case 'loading':
                                    case 'error':
                                        return {
                                            type: 'loading',
                                            params: old.params,
                                        }
                                    case 'loaded':
                                    case 'reloading':
                                    case 'subsequent_failed':
                                        return {
                                            type: 'loaded',
                                            params: old.params,
                                            data: {
                                                type: 'busy',
                                                lastTxCreatedAtMs: Date.now(),
                                            },
                                        }
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(old)
                                }
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />

            <Modal
                experimentalMode={experimentalMode}
                earnHistoricalTakerUserCurrencyRateMap={
                    earnHistoricalTakerUserCurrencyRateMap
                }
                earnTakerMetrics={earnTakerMetrics}
                defaultCurrencyConfig={defaultCurrencyConfig}
                keyStore={keyStore}
                notificationsConfig={notificationsConfig}
                accountsMap={accountsMap}
                delayQueueStatePollable={delayQueueStatePollable}
                gnosisPayAccountOnboardedState={gnosisPayAccountOnboardedState}
                installationId={installationId}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                state={state}
                cardOwnerEarn={cardOwnerEarn}
                cardBalance={gnosisPayAccountOnboardedState.balance}
                cardReadonlySigner={cardReadonlySigner}
                encryptedPassword={encryptedPassword}
                networkMap={networkMap}
                keyStoreMap={keyStoreMap}
                portfolioMap={portfolioMap}
                currencyHiddenMap={currencyHiddenMap}
                sessionPassword={sessionPassword}
                networkRPCMap={networkRPCMap}
                currencyPinMap={currencyPinMap}
                customCurrencyMap={customCurrencyMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                cardConfig={cardConfig}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'lock_screen_close_click':
                        case 'on_transaction_cancelled_successfully_close_clicked':
                        case 'on_swap_created_close_clicked':
                        case 'on_new_physical_card_created_successfully':
                            setState({ type: 'closed' })
                            break
                        case 'session_password_decrypted':
                            setState({ type: 'closed' })
                            setSide('back')
                            break

                        case 'on_completed_safe_transaction_close_click':
                        case 'on_completed_transaction_close_click':
                        case 'on_swap_cancelled_close_clicked':
                        case 'on_swap_success_clicked':
                            setState({ type: 'closed' })
                            refreshAllLoadables()
                            break

                        case 'on_predefined_fee_preset_selected':
                        case 'on_account_create_request':
                        case 'cancel_submitted':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'track_wallet_clicked':
                        case 'add_wallet_clicked':
                        case 'hardware_wallet_clicked':
                        case 'import_keys_button_clicked':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_request_replaced':
                        case 'transaction_submited':
                        case 'safe_wallet_clicked':
                        case 'recover_safe_wallet_clicked':
                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                        case 'on_notifications_config_changed':
                        case 'on_card_disconnected':
                        case 'import_card_owner_clicked':
                        case 'on_switch_card_new_card_selected':
                        case 'on_bank_transfer_selected':
                        case 'on_accounts_create_success_animation_finished':
                        case 'on_add_label_to_track_only_account_during_send':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_address_scanned':
                        case 'on_address_scanned_and_add_label':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_chf_taker_metrics_loaded':
                        case 'on_top_up_transaction_complete_close':
                        case 'on_card_transactions_fetch_success':
                        case 'on_swaps_io_swap_request_created':
                            onMsg(msg)
                            break

                        case 'on_recharge_configured':
                        case 'on_new_virtual_card_created_successfully':
                        case 'on_physical_card_activated_info_screen_closed':
                        case 'on_card_order_redirect_to_gnosis_pay_clicked':
                            onMsg(msg)
                            setState({ type: 'closed' })
                            break

                        case 'on_card_top_up_success':
                            onMsg(msg)
                            setState({ type: 'closed' })
                            setSubmittedCardTopUps((prev) => [
                                msg.submittedCardTopUp,
                                ...prev,
                            ])
                            break

                        case 'on_cashback_withdraw_queued_successfully':
                            setState({ type: 'closed' })
                            setDelayQueueStatePollable((old) => {
                                switch (old.type) {
                                    case 'loading':
                                    case 'error':
                                        return {
                                            type: 'loading',
                                            params: old.params,
                                        }
                                    case 'loaded':
                                    case 'reloading':
                                    case 'subsequent_failed':
                                        return {
                                            type: 'loaded',
                                            params: old.params,
                                            data: {
                                                type: 'busy',
                                                lastTxCreatedAtMs: Date.now(),
                                            },
                                        }
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(old)
                                }
                            })
                            break

                        case 'on_card_withdraw_successfully_close_clicked':
                            setState({ type: 'closed' })
                            setDelayQueueStatePollable((old) => {
                                switch (old.type) {
                                    case 'loading':
                                    case 'error':
                                        return {
                                            type: 'loading',
                                            params: old.params,
                                        }
                                    case 'loaded':
                                    case 'reloading':
                                    case 'subsequent_failed':
                                        return {
                                            type: 'loaded',
                                            params: old.params,
                                            data: {
                                                type: 'busy',
                                                lastTxCreatedAtMs: Date.now(),
                                            },
                                        }
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(old)
                                }
                            })
                            break

                        case 'on_add_card_owner_queued_successfully':
                        case 'on_remove_card_owner_queued_successfully':
                        case 'on_spend_limit_changed_successfully_close_clicked':
                            setDelayQueueStatePollable((old) => {
                                switch (old.type) {
                                    case 'loading':
                                    case 'error':
                                        return {
                                            type: 'loading',
                                            params: old.params,
                                        }
                                    case 'loaded':
                                    case 'reloading':
                                    case 'subsequent_failed':
                                        return {
                                            type: 'loaded',
                                            params: old.params,
                                            data: {
                                                type: 'busy',
                                                lastTxCreatedAtMs: Date.now(),
                                            },
                                        }
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(old)
                                }
                            })
                            break

                        case 'on_delay_queue_pollable_try_again_clicked':
                            setDelayQueueStatePollable({
                                type: 'loading',
                                params: delayQueueStatePollable.params,
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
