import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { useLoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef, usePrevious } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { logAppsflyerEvent } from '@zeal/domains/Appsflyer'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountNotOnboardedState,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedCardConfig,
} from '@zeal/domains/Card'
import { fetchGnosisPayAccountState2WithSilentLogin } from '@zeal/domains/Card/api/fetchGnosisPayAccountState'
import { CardLoadingScreen } from '@zeal/domains/Card/components/CardLoadingScreen'
import { CreateOrImportCard } from '@zeal/domains/Card/features/CreateOrImportCard'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Flow } from './Flow'

type Props = {
    initialGnosisPayAccountState: GnosisPayAccountNotOnboardedState
    cardConfig: ReadonlySignerSelectedCardConfig

    networkRPCMap: NetworkRPCMap
    installationId: string
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    sessionPassword: string
    cardReadOnlySigner: Account
    keyStore: CardSlientSignKeyStore
    currencyHiddenMap: CurrencyHiddenMap
    accountsMap: AccountsMap
    installationCampaign: string | null
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    earnTakerMetrics: EarnTakerMetrics
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_card_onboarded_account_state_received'
          cardReadonlySignerAddress: Web3.address.Address
          gnosisAccountState: GnosisPayAccountOnboardedState
      }
    | MsgOf<typeof CardLoadingScreen>
    | MsgOf<typeof CreateOrImportCard>
    | Extract<
          MsgOf<typeof Flow>,
          {
              type:
                  | 'on_card_disconnected'
                  | 'on_activate_card_clicked'
                  | 'on_do_bank_transfer_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_activate_existing_monerium_account_click'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_switch_bank_transfer_provider_clicked'
                  | 'on_monerium_deposit_success_go_to_wallet_clicked'
                  | 'on_virtual_card_order_created_animation_completed'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'on_physical_card_activated_info_screen_closed'
                  | 'on_card_order_redirect_to_gnosis_pay_clicked'
          }
      >

const POLL_INTERVAL_MS = 10_000

export const NotOnboarded = ({
    onMsg,
    sessionPassword,
    installationId,
    cardConfig,
    networkRPCMap,
    initialGnosisPayAccountState,
    networkMap,
    portfolioMap,
    keyStoreMap,
    currencyHiddenMap,
    keyStore,
    accountsMap,
    installationCampaign,
    feePresetMap,
    gasCurrencyPresetMap,
    cardReadOnlySigner,
    defaultCurrencyConfig,
    earnTakerMetrics,
}: Props) => {
    const liveMsg = useLiveRef(onMsg)
    const captureErrorOnce = useCaptureErrorOnce()
    const liveReadonlySigner = useLiveRef(cardReadOnlySigner)
    const [pollable, setPollable] = useLoadedPollableData(
        fetchGnosisPayAccountState2WithSilentLogin,
        {
            type: 'reloading',
            params: {
                sessionPassword,
                networkMap,
                keyStore,
                defaultCurrencyConfig,
                networkRPCMap,
                selectedCardId: null,
                readonlySignerAddress: cardConfig.readonlySignerAddress,
            },
            data: initialGnosisPayAccountState,
        },
        {
            pollIntervalMilliseconds: POLL_INTERVAL_MS,
            stopIf: (pollable) => {
                switch (pollable.type) {
                    case 'loaded':
                    case 'reloading':
                    case 'subsequent_failed':
                        switch (pollable.data.type) {
                            case 'not_onboarded':
                                switch (pollable.data.state) {
                                    case 'kyc_failed':
                                    case 'kyc_approved':
                                    case 'card_order_pending_payment':
                                    case 'card_order_ready':
                                    case 'card_created_from_order':
                                        return true
                                    case 'kyc_started_verification_in_progress':
                                    case 'terms_not_accepted':
                                    case 'terms_accepted_kyc_not_started':
                                    case 'kyc_started_documents_requested':
                                    case 'kyc_started_resubmission_requested':
                                        return false
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(pollable.data)
                                }
                            case 'onboarded':
                                return true
                            /* istanbul ignore next */
                            default:
                                return notReachable(pollable.data)
                        }
                    /* istanbul ignore next */
                    default:
                        return notReachable(pollable)
                }
            },
        }
    )

    const prevPollable = usePrevious(pollable)

    useEffect(() => {
        switch (pollable.type) {
            case 'subsequent_failed':
                captureErrorOnce(pollable.error)
                return
            case 'loaded':
            case 'reloading':
                switch (pollable.data.type) {
                    case 'not_onboarded':
                        switch (pollable.data.state) {
                            case 'terms_not_accepted':
                            case 'terms_accepted_kyc_not_started':
                            case 'kyc_started_documents_requested':
                            case 'kyc_started_verification_in_progress':
                            case 'kyc_started_resubmission_requested':
                            case 'card_order_pending_payment':
                            case 'card_order_ready':
                            case 'card_created_from_order':
                                return
                            case 'kyc_approved':
                            case 'kyc_failed':
                                if (prevPollable) {
                                    switch (prevPollable.type) {
                                        case 'loaded':
                                        case 'reloading':
                                            switch (prevPollable.data.type) {
                                                case 'not_onboarded':
                                                    if (
                                                        prevPollable.data
                                                            .state !==
                                                        pollable.data.state
                                                    ) {
                                                        postUserEvent({
                                                            type: 'GnosisPayKYCCompletedEvent',
                                                            flow: 'onboarding',
                                                            result: pollable
                                                                .data.state,
                                                            installationId,
                                                            location:
                                                                'portfolio',
                                                        })

                                                        switch (
                                                            pollable.data.state
                                                        ) {
                                                            case 'kyc_approved':
                                                                logAppsflyerEvent(
                                                                    {
                                                                        type: 'af_kyc_completed',
                                                                    }
                                                                )
                                                                break

                                                            case 'kyc_failed':
                                                                break
                                                            default:
                                                                return notReachable(
                                                                    pollable
                                                                        .data
                                                                        .state
                                                                )
                                                        }
                                                    }
                                                    break

                                                case 'onboarded':
                                                    break

                                                default:
                                                    return notReachable(
                                                        prevPollable.data
                                                    )
                                            }
                                            break
                                        case 'subsequent_failed':
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(prevPollable)
                                    }
                                }
                                return
                            /* istanbul ignore next */
                            default:
                                return notReachable(pollable.data)
                        }
                    case 'onboarded':
                        liveMsg.current({
                            type: 'on_card_onboarded_account_state_received',
                            cardReadonlySignerAddress:
                                liveReadonlySigner.current.address,
                            gnosisAccountState: pollable.data,
                        })
                        return
                    /* istanbul ignore next */
                    default:
                        return notReachable(pollable.data)
                }
            /* istanbul ignore next */
            default:
                return notReachable(pollable)
        }
    }, [
        captureErrorOnce,
        installationId,
        liveMsg,
        liveReadonlySigner,
        pollable,
        prevPollable,
    ])

    switch (pollable.type) {
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed':
            switch (pollable.data.type) {
                case 'onboarded':
                    return (
                        <CardLoadingScreen
                            cardConfig={cardConfig}
                            installationId={installationId}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            onMsg={onMsg}
                        />
                    )
                case 'not_onboarded':
                    return (
                        <Flow
                            installationCampaign={installationCampaign}
                            gnosisPayAccountState={pollable.data}
                            installationId={installationId}
                            currencyHiddenMap={currencyHiddenMap}
                            cardConfig={cardConfig}
                            cardReadOnlySigner={cardReadOnlySigner}
                            keyStore={keyStore}
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            accountsMap={accountsMap}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            keyStoreMap={keyStoreMap}
                            portfolioMap={portfolioMap}
                            sessionPassword={sessionPassword}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            earnTakerMetrics={earnTakerMetrics}
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'on_card_disconnected':
                                    case 'on_do_bank_transfer_clicked':
                                    case 'on_4337_auto_gas_token_selection_clicked':
                                    case 'on_4337_gas_currency_selected':
                                    case 'on_activate_existing_monerium_account_click':
                                    case 'on_create_smart_wallet_clicked':
                                    case 'on_switch_bank_transfer_provider_clicked':
                                    case 'on_monerium_deposit_success_go_to_wallet_clicked':
                                    case 'on_virtual_card_order_created_animation_completed':
                                    case 'on_card_import_on_import_keys_clicked':
                                    case 'on_card_imported_success_animation_complete':
                                    case 'on_onboarded_card_imported_success_animation_complete':
                                    case 'on_card_order_redirect_to_gnosis_pay_clicked':
                                        onMsg(msg)
                                        break
                                    case 'on_physical_card_activated_info_screen_closed':
                                        onMsg(msg)
                                        setPollable({
                                            type: 'reloading',
                                            params: pollable.params,
                                            data: pollable.data,
                                        })
                                        break

                                    case 'on_gnosis_pay_onboarding_flow_closed':
                                    case 'on_gnosis_pay_kyc_submitted_animation_complete':
                                    case 'on_virtual_card_order_onboarding_closed':
                                    case 'on_activate_physical_card_closed':
                                    case 'on_physical_card_order_onboarding_closed':
                                    case 'on_new_physical_card_created_successfully':
                                        setPollable({
                                            type: 'reloading',
                                            params: pollable.params,
                                            data: pollable.data,
                                        })
                                        break

                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(msg)
                                }
                            }}
                        />
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(pollable.data)
            }
        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}
