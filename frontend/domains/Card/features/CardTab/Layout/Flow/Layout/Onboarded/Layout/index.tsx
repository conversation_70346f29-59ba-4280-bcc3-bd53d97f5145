import { Column } from '@zeal/uikit/Column'
import {
    Refresh<PERSON>ontainer,
    RefreshContainerState,
} from '@zeal/uikit/RefreshContainer'
import { Screen } from '@zeal/uikit/Screen'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { LoadedReloadableData } from '@zeal/toolkit/LoadableData/LoadedReloadableData'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { ReloadableData } from '@zeal/toolkit/LoadableData/ReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { success } from '@zeal/toolkit/Result'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    CardTransaction,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedOnboardedCardConfig,
    SubmittedCardTopUp,
} from '@zeal/domains/Card'
import { DelayQueueState } from '@zeal/domains/Card/api/fetchDelayQueueState'
import { CardCashback } from '@zeal/domains/Card/domains/Cashback'
import { Widget as CashbackWidget } from '@zeal/domains/Card/domains/Cashback/features/Widget'
import { AddToWalletBanner } from '@zeal/domains/Card/features/AddToWalletBanner'
import {
    CardDetailsWidget,
    Side,
} from '@zeal/domains/Card/features/CardDetailsWidget'
import { PendingCardTopUpBanner } from '@zeal/domains/Card/features/PendingCardTopUpBanner'
import { ReKycWidget } from '@zeal/domains/Card/features/ReKycWidget'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { Earn } from '@zeal/domains/Earn'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { CelebrationConfig, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Actions } from './Actions'
import { CancelledCardActionButton } from './CancelledCardActionButton'
import { DelayQueueActionWidget } from './DelayQueueActionWidget'
import { RecentTransactions } from './RecentTransactions'

type Props = {
    transactionsLoadable: ReloadableData<CardTransaction[], unknown>
    cardCashbackLoadable: LoadedReloadableData<CardCashback, unknown>
    gnosisPayAccountOnboardedStateLoadable: LoadedReloadableData<
        GnosisPayAccountOnboardedState,
        unknown
    >
    delayQueueStatePollable: PollableData<DelayQueueState, unknown>
    side: Side
    cardOwnerEarn: Earn
    cardReadonlySigner: Account
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keystores: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    installationId: string
    networkRPCMap: NetworkRPCMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    keyStore: CardSlientSignKeyStore
    defaultCurrencyConfig: DefaultCurrencyConfig
    refreshContainerState: RefreshContainerState
    celebrationConfig: CelebrationConfig
    encryptedPassword: string
    appRating: AppRating
    submittedCardTopUps: SubmittedCardTopUp[]
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_card_onboarded_state_refresh_pulled' }
    | MsgOf<typeof CardDetailsWidget>
    | MsgOf<typeof RecentTransactions>
    | MsgOf<typeof Actions>
    | MsgOf<typeof CashbackWidget>
    | MsgOf<typeof AddToWalletBanner>
    | MsgOf<typeof CancelledCardActionButton>
    | MsgOf<typeof PendingCardTopUpBanner>

export const Layout = ({
    installationId,
    gnosisPayAccountOnboardedStateLoadable,
    side,
    cardOwnerEarn,
    delayQueueStatePollable,
    appRating,
    celebrationConfig,
    networkRPCMap,
    cardReadonlySigner,
    sessionPassword,
    gasCurrencyPresetMap,
    feePresetMap,
    networkMap,
    accountsMap,
    keystores,
    portfolioMap,
    currencyPinMap,
    cardConfig,
    currencyHiddenMap,
    submittedCardTopUps,
    keyStore,
    transactionsLoadable,
    cardCashbackLoadable,
    defaultCurrencyConfig,
    refreshContainerState,
    encryptedPassword,
    onMsg,
}: Props) => {
    const gnosisPayAccountOnboardedState =
        gnosisPayAccountOnboardedStateLoadable.data

    return (
        <Screen
            padding="controller_tabs_fullscreen_scroll"
            background="light"
            onNavigateBack={null}
        >
            <RefreshContainer
                padding="default"
                state={refreshContainerState}
                onRefreshPulled={() =>
                    onMsg({ type: 'on_card_onboarded_state_refresh_pulled' })
                }
            >
                <Column spacing={8}>
                    <CardDetailsWidget
                        earn={cardOwnerEarn}
                        side={side}
                        gnosisPayAccountOnboardedState={
                            gnosisPayAccountOnboardedState
                        }
                        delayQueueStatePollable={delayQueueStatePollable}
                        cardConfig={cardConfig}
                        sessionPassword={sessionPassword}
                        cardReadonlySigner={cardReadonlySigner}
                        keyStore={keyStore}
                        onMsg={onMsg}
                    />
                    <Actions
                        gnosisPayAccountOnboardedState={
                            gnosisPayAccountOnboardedState
                        }
                        sessionPassword={sessionPassword}
                        keyStore={keyStore}
                        cardReadonlySigner={cardReadonlySigner}
                        delayQueueStatePollable={delayQueueStatePollable}
                        installationId={installationId}
                        onMsg={onMsg}
                    />

                    {submittedCardTopUps.map((submittedTopUp) => (
                        <PendingCardTopUpBanner
                            key={`card-top-up-${submittedTopUp.startedAtMs}`}
                            submittedCardTopUp={submittedTopUp}
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            installationId={installationId}
                            onMsg={onMsg}
                        />
                    ))}

                    <ReKycWidget
                        gnosisPayAccountOnboardedState={
                            gnosisPayAccountOnboardedState
                        }
                        sessionPassword={sessionPassword}
                        keyStore={keyStore}
                        cardReadOnlySigner={cardReadonlySigner}
                        cardConfig={cardConfig}
                        networkRPCMap={networkRPCMap}
                        networkMap={networkMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        location="card_tab"
                        dismissible={false}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'on_dissmiss_card_kyc_onboarded_widget_clicked':
                                    captureError(
                                        new ImperativeError(
                                            'Got kyc banner dismiss msg in card tab'
                                        )
                                    )
                                    noop()
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg.type)
                            }
                        }}
                    />

                    <CancelledCardActionButton
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        gnosisPayAccountState={gnosisPayAccountOnboardedState}
                        networkRPCMap={networkRPCMap}
                        networkMap={networkMap}
                        sessionPassword={sessionPassword}
                        cardReadOnlySigner={cardReadonlySigner}
                        cardConfig={cardConfig}
                        keyStore={keyStore}
                        onMsg={onMsg}
                    />
                    <DelayQueueActionWidget
                        pollable={delayQueueStatePollable}
                    />
                    <AddToWalletBanner
                        location="card_tab"
                        sessionPassword={sessionPassword}
                        cardReadonlySigner={cardReadonlySigner}
                        cardConfig={cardConfig}
                        gnosisPayAccountOnboardedStateResultLoadable={{
                            type: 'loaded',
                            params: {
                                keyStore,
                                sessionPassword,
                                cardConfig,
                                networkMap,
                                networkRPCMap,
                                defaultCurrencyConfig,
                            },
                            data: success({
                                gnosisPayAccountOnboardedState,
                                keyStore: keyStore,
                            }),
                        }}
                        installationId={installationId}
                        encryptedPassword={encryptedPassword}
                        onMsg={onMsg}
                    />
                    <CashbackWidget
                        appRating={appRating}
                        celebrationConfig={celebrationConfig}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        cardCashbackLoadable={cardCashbackLoadable}
                        keyStore={keyStore}
                        currencyHiddenMap={currencyHiddenMap}
                        delayQueueStatePollable={delayQueueStatePollable}
                        accountsMap={accountsMap}
                        gnosisPayAccountOnboardedState={
                            gnosisPayAccountOnboardedState
                        }
                        cardReadonlySigner={cardReadonlySigner}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        installationId={installationId}
                        keyStoreMap={keystores}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        portfolioMap={portfolioMap}
                        sessionPassword={sessionPassword}
                        currencyPinMap={currencyPinMap}
                        onMsg={onMsg}
                    />
                    <RecentTransactions
                        onMsg={onMsg}
                        transactionsLoadable={transactionsLoadable}
                        installationId={installationId}
                    />
                </Column>
            </RefreshContainer>
        </Screen>
    )
}
