import { Refresh<PERSON>ontainerState } from '@zeal/uikit/RefreshContainer'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { CreateOrImportCard } from '@zeal/domains/Card/features/CreateOrImportCard'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
} from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { CardReadOnlySignerFound } from './CardReadOnlySignerFound'

type Props = {
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    cardConfig: CardConfig

    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    isEthereumNetworkFeeWarningSeen: boolean
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    portfolioMap: PortfolioMap
    sessionPassword: string
    encryptedPassword: string
    earnTakerMetrics: EarnTakerMetrics
    currencyPinMap: CurrencyPinMap
    customCurrencyMap: CustomCurrencyMap
    notificationsConfig: NotificationsConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    refreshContainerState: RefreshContainerState
    celebrationConfig: CelebrationConfig
    installationCampaign: string | null
    appRating: AppRating
    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof CardReadOnlySignerFound>
    | MsgOf<typeof CreateOrImportCard>

export const Layout = ({
    onMsg,
    cardConfig,
    accountsMap,
    keyStoreMap,
    feePresetMap,
    gasCurrencyPresetMap,
    appRating,
    celebrationConfig,
    installationId,
    networkMap,
    networkRPCMap,
    portfolioMap,
    installationCampaign,
    sessionPassword,
    encryptedPassword,
    currencyHiddenMap,
    transactionActivitiesCacheMap,
    currencyPinMap,
    customCurrencyMap,
    isEthereumNetworkFeeWarningSeen,
    notificationsConfig,
    defaultCurrencyConfig,
    earnTakerMetrics,
    refreshContainerState,
    historicalTakerUserCurrencyRateMap,
    experimentalMode,
}: Props) => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
            return (
                <CreateOrImportCard
                    installationId={installationId}
                    onMsg={onMsg}
                />
            )
        case 'card_readonly_signer_address_is_selected':
        case 'card_readonly_signer_address_is_selected_fully_onboarded': {
            return (
                <CardReadOnlySignerFound
                    experimentalMode={experimentalMode}
                    historicalTakerUserCurrencyRateMap={
                        historicalTakerUserCurrencyRateMap
                    }
                    installationCampaign={installationCampaign}
                    appRating={appRating}
                    celebrationConfig={celebrationConfig}
                    refreshContainerState={refreshContainerState}
                    earnTakerMetrics={earnTakerMetrics}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    notificationsConfig={notificationsConfig}
                    transactionActivitiesCacheMap={
                        transactionActivitiesCacheMap
                    }
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    cardConfig={cardConfig}
                    currencyHiddenMap={currencyHiddenMap}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    encryptedPassword={encryptedPassword}
                    currencyPinMap={currencyPinMap}
                    customCurrencyMap={customCurrencyMap}
                    onMsg={onMsg}
                />
            )
        }

        /* istanbul ignore next */
        default:
            return notReachable(cardConfig)
    }
}
