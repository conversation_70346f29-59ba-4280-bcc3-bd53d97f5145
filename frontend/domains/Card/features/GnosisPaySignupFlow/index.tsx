import { StepW<PERSON>rd } from '@zeal/uikit/StepWizard'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayPreKYCApprovedState,
} from '@zeal/domains/Card'
import { GnosisPayOnboardingFlow } from '@zeal/domains/Card/features/GnosisPayOnboardingFlow'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { GnosisPaySignupLocation } from '@zeal/domains/UserEvents'

import { EmailForm } from './EmailForm'
import { FetchAccountState } from './FetchAccountState'

type Props = {
    cardReadonlySigner: Account
    installationId: string
    keyStore: CardSlientSignKeyStore
    sessionPassword: string
    location: GnosisPaySignupLocation
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationCampaign: string | null
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof EmailForm>,
          {
              type:
                  | 'close'
                  | 'on_connect_existing_gnosis_pay_account_clicked'
                  | 'on_gnosis_pay_account_created'
          }
      >
    | MsgOf<typeof GnosisPayOnboardingFlow>

type State =
    | {
          type: 'email_form'
      }
    | {
          type: 'fetch_account_state'
      }
    | {
          type: 'onboarding_flow'
          gnosisPayState: GnosisPayPreKYCApprovedState
      }

export const GnosisPaySignupFlow = ({
    cardReadonlySigner,
    installationId,
    networkMap,
    networkRPCMap,
    sessionPassword,
    installationCampaign,
    keyStore,
    defaultCurrencyConfig,
    location,
    onMsg,
}: Props) => {
    return (
        <StepWizard<State>
            initialStep={{ type: 'email_form' }}
            background="default"
        >
            {({ step, forwardTo, moveTo }) => {
                switch (step.type) {
                    case 'email_form':
                        return (
                            <EmailForm
                                installationCampaign={installationCampaign}
                                location={location}
                                installationId={installationId}
                                cardReadOnlySigner={cardReadonlySigner}
                                sessionPassword={sessionPassword}
                                keyStore={keyStore}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                        case 'on_connect_existing_gnosis_pay_account_clicked':
                                            onMsg(msg)
                                            break
                                        case 'on_gnosis_pay_account_created':
                                            onMsg(msg)
                                            forwardTo({
                                                type: 'fetch_account_state',
                                            })
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'fetch_account_state':
                        return (
                            <FetchAccountState
                                sessionPassword={sessionPassword}
                                keyStore={keyStore}
                                cardReadOnlySigner={cardReadonlySigner}
                                networkRPCMap={networkRPCMap}
                                networkMap={networkMap}
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                installationId={installationId}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        case 'on_account_state_fetched':
                                            moveTo({
                                                type: 'onboarding_flow',
                                                gnosisPayState:
                                                    msg.gnosisPayState,
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'onboarding_flow':
                        return (
                            <GnosisPayOnboardingFlow
                                sessionPassword={sessionPassword}
                                cardReadOnlySigner={cardReadonlySigner}
                                keyStore={keyStore}
                                gnosisPayState={step.gnosisPayState}
                                location={location}
                                installationId={installationId}
                                onMsg={onMsg}
                            />
                        )
                    /* istanbul ignore next */
                    default:
                        return notReachable(step)
                }
            }}
        </StepWizard>
    )
}
