import { useEffect } from 'react'

import { Column } from '@zeal/uikit/Column'
import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'
import { Row } from '@zeal/uikit/Row'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardBalance,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { fetchGnosisPayAccountOnboardedStateResult } from '@zeal/domains/Card/api/fetchGnosisPayAccountOnboardedStateResult'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { BRewardTracker } from '@zeal/domains/Card/domains/Reward/features/BRewardTracker'
import { AddToWalletBanner } from '@zeal/domains/Card/features/AddToWalletBanner'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { EarnTakerMetrics, TakerPortfolioMap2 } from '@zeal/domains/Earn'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { AppRating } from '@zeal/domains/Support/domains/Feedback'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { CardBalanceTile } from './CardBalanceTile'
import { CashbackTile } from './CashbackTile'
import { GnosisPayReKycWidget } from './GnosisPayReKycWidget'

type Props = {
    earnTakerMetrics: EarnTakerMetrics
    takerPortfolioMap: TakerPortfolioMap2
    accountsMap: AccountsMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    cardBalance: CardBalance
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    customCurrencyMap: CustomCurrencyMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    refreshContainerState: RefreshContainerState
    installationCampaign: string | null
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    encryptedPassword: string
    isEthereumNetworkFeeWarningSeen: boolean
    referralConfig: ReferralConfig
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof CardBalanceTile>
    | MsgOf<typeof CashbackTile>
    | MsgOf<typeof AddToWalletBanner>
    | MsgOf<typeof GnosisPayReKycWidget>
    | MsgOf<typeof BRewardTracker>

export const CardWidget = ({
    earnTakerMetrics,
    currencyPinMap,
    defaultCurrencyConfig,
    networkMap,
    cardConfig,
    cardBalance,
    keyStoreMap,
    networkRPCMap,
    takerPortfolioMap,
    onMsg,
    sessionPassword,
    celebrationConfig,
    installationCampaign,
    appRating,
    accountsMap,
    currencyHiddenMap,
    customCurrencyMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    portfolioMap,
    refreshContainerState,
    isEthereumNetworkFeeWarningSeen,
    encryptedPassword,
    referralConfig,
    experimentalMode,
}: Props) => {
    const keyStore = getKeyStore({
        address: cardConfig.readonlySignerAddress,
        keyStoreMap,
    })

    const [loadable, setLoadable] = useLoadableData(
        fetchGnosisPayAccountOnboardedStateResult,
        {
            type: 'loading',
            params: {
                cardConfig,
                keyStore,
                networkRPCMap,
                sessionPassword,
                networkMap,
                defaultCurrencyConfig,
            },
        }
    )

    useEffect(() => {
        switch (refreshContainerState) {
            case 'refreshing':
                setLoadable((old) => ({ type: 'loading', params: old.params }))
                break
            case 'refreshed':
                break

            default:
                notReachable(refreshContainerState)
        }
    }, [refreshContainerState, setLoadable])

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
            case 'loaded':
                break
            case 'error':
                captureError(loadable.error)
                break
            default:
                notReachable(loadable)
        }
    }, [loadable])

    const cardReadonlySigner = accountsMap[cardConfig.readonlySignerAddress]

    return (
        <Column spacing={8}>
            <Row spacing={8} alignY="stretch">
                <CardBalanceTile
                    experimentalMode={experimentalMode}
                    earnTakerMetrics={earnTakerMetrics}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    currencyPinMap={currencyPinMap}
                    currencyHiddenMap={currencyHiddenMap}
                    customCurrencyMap={customCurrencyMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    accountsMap={accountsMap}
                    cardConfig={cardConfig}
                    cardBalance={cardBalance}
                    onMsg={onMsg}
                />

                <CashbackTile
                    appRating={appRating}
                    celebrationConfig={celebrationConfig}
                    accountsMap={accountsMap}
                    cardConfig={cardConfig}
                    currencyPinMap={currencyPinMap}
                    currencyHiddenMap={currencyHiddenMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    networkMap={networkMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    loadable={loadable}
                    onMsg={onMsg}
                />
            </Row>
            <AddToWalletBanner
                location="portfolio"
                cardConfig={cardConfig}
                sessionPassword={sessionPassword}
                cardReadonlySigner={cardReadonlySigner}
                encryptedPassword={encryptedPassword}
                installationId={installationId}
                onMsg={onMsg}
                gnosisPayAccountOnboardedStateResultLoadable={loadable}
            />
            <GnosisPayReKycWidget
                cardReadOnlySigner={cardReadonlySigner}
                loadable={loadable}
                sessionPassword={sessionPassword}
                installationId={installationId}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                defaultCurrencyConfig={defaultCurrencyConfig}
                cardConfig={cardConfig}
                onMsg={onMsg}
            />

            {(() => {
                switch (loadable.type) {
                    case 'loading':
                    case 'error':
                        return null
                    case 'loaded':
                        switch (loadable.data.type) {
                            case 'Failure':
                                return null
                            case 'Success':
                                const gnosisState =
                                    loadable.data.data
                                        .gnosisPayAccountOnboardedState
                                return (
                                    <BRewardTracker
                                        appRating={appRating}
                                        referralConfig={referralConfig}
                                        installationId={installationId}
                                        takerPortfolioMap={takerPortfolioMap}
                                        installationCampaign={
                                            installationCampaign
                                        }
                                        gnosisOnboardedAccountState={
                                            gnosisState
                                        }
                                        cardConfig={cardConfig}
                                        keyStore={loadable.data.data.keyStore}
                                        owner={cardReadonlySigner}
                                        sessionPassword={sessionPassword}
                                        networkRPCMap={networkRPCMap}
                                        onMsg={onMsg}
                                    />
                                )
                            default:
                                return notReachable(loadable.data)
                        }
                    default:
                        return notReachable(loadable)
                }
            })()}
        </Column>
    )
}
