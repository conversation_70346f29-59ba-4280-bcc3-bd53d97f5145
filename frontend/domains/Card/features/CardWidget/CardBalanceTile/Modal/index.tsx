import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { CardAddCash } from '@zeal/domains/Card/features/CardAddCash'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    accountsMap: AccountsMap
    state: State
    installationId: string
    networkMap: NetworkMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    currencyPinMap: CurrencyPinMap
    customCurrencyMap: CustomCurrencyMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    isEthereumNetworkFeeWarningSeen: boolean
    earnTakerMetrics: EarnTakerMetrics
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}
export type State = { type: 'closed' } | { type: 'card_topup' }

type Msg = MsgOf<typeof CardAddCash>

export const Modal = ({
    state,
    accountsMap,
    cardConfig,
    currencyHiddenMap,
    currencyPinMap,
    customCurrencyMap,
    defaultCurrencyConfig,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    sessionPassword,
    isEthereumNetworkFeeWarningSeen,
    earnTakerMetrics,
    experimentalMode,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null

        case 'card_topup':
            return (
                <UIModal>
                    <CardAddCash
                        experimentalMode={experimentalMode}
                        customCurrencies={customCurrencyMap}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        initialSender={
                            accountsMap[cardConfig.readonlySignerAddress]
                        }
                        cardConfig={cardConfig}
                        accountsMap={accountsMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        networkMap={networkMap}
                        keyStoreMap={keyStoreMap}
                        portfolioMap={portfolioMap}
                        currencyHiddenMap={currencyHiddenMap}
                        sessionPassword={sessionPassword}
                        networkRPCMap={networkRPCMap}
                        currencyPinMap={currencyPinMap}
                        customCurrencyMap={customCurrencyMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        earnTakerMetrics={earnTakerMetrics}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
