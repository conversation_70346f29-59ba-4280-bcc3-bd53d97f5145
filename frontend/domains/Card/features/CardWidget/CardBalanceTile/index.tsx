import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardBalance,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    earnTakerMetrics: EarnTakerMetrics
    accountsMap: AccountsMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    cardBalance: CardBalance
    installationId: string
    networkMap: NetworkMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    sessionPassword: string
    networkRPCMap: NetworkRPCMap
    currencyPinMap: CurrencyPinMap
    customCurrencyMap: CustomCurrencyMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    isEthereumNetworkFeeWarningSeen: boolean
    experimentalMode: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'on_card_widget_clicked'
                  | 'on_pending_card_balance_timer_completed'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_predefined_fee_preset_selected'
                  | 'on_account_create_request'
                  | 'cancel_submitted'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'track_wallet_clicked'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'import_keys_button_clicked'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_silent_gnosis_pay_login_failed'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_bank_transfer_selected'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_top_up_transaction_complete_close'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_card_top_up_success'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_completed_transaction_close_click'
                  | 'on_swap_cancelled_close_clicked'
                  | 'on_swap_success_clicked'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
                  | 'on_recharge_configured'
          }
      >

export const CardBalanceTile = ({
    earnTakerMetrics,
    cardBalance,
    cardConfig,
    accountsMap,
    currencyHiddenMap,
    currencyPinMap,
    customCurrencyMap,
    defaultCurrencyConfig,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    sessionPassword,
    isEthereumNetworkFeeWarningSeen,
    experimentalMode,
    onMsg,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    return (
        <>
            <Layout
                cardBalance={cardBalance}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_card_widget_clicked':
                        case 'on_pending_card_balance_timer_completed':
                            onMsg(msg)
                            break
                        case 'on_topup_card_click':
                            setModal({ type: 'card_topup' })
                            break
                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
            <Modal
                experimentalMode={experimentalMode}
                earnTakerMetrics={earnTakerMetrics}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                currencyHiddenMap={currencyHiddenMap}
                currencyPinMap={currencyPinMap}
                customCurrencyMap={customCurrencyMap}
                defaultCurrencyConfig={defaultCurrencyConfig}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                installationId={installationId}
                keyStoreMap={keyStoreMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                portfolioMap={portfolioMap}
                sessionPassword={sessionPassword}
                accountsMap={accountsMap}
                cardConfig={cardConfig}
                state={modal}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_transaction_cancelled_successfully_close_clicked':
                        case 'on_swap_created_close_clicked':
                            setModal({ type: 'closed' })
                            break

                        case 'on_card_top_up_success':
                        case 'on_completed_safe_transaction_close_click':
                        case 'on_completed_transaction_close_click':
                        case 'on_swap_cancelled_close_clicked':
                        case 'on_swap_success_clicked':
                            setModal({ type: 'closed' })
                            onMsg(msg)
                            break

                        case 'on_predefined_fee_preset_selected':
                        case 'on_account_create_request':
                        case 'cancel_submitted':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'track_wallet_clicked':
                        case 'add_wallet_clicked':
                        case 'hardware_wallet_clicked':
                        case 'import_keys_button_clicked':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_request_replaced':
                        case 'transaction_submited':
                        case 'safe_wallet_clicked':
                        case 'recover_safe_wallet_clicked':
                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                        case 'on_accounts_create_success_animation_finished':
                        case 'on_bank_transfer_selected':
                        case 'on_address_scanned':
                        case 'on_address_scanned_and_add_label':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_add_label_to_track_only_account_during_send':
                        case 'on_top_up_transaction_complete_close':
                        case 'on_swaps_io_swap_request_created':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_chf_taker_metrics_loaded':
                        case 'on_recharge_configured':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
