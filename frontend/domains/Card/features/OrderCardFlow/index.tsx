import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardSlientSignKeyStore } from '@zeal/domains/Card'
import { GnosisPaySignupFlow } from '@zeal/domains/Card/features/GnosisPaySignupFlow'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { GnosisPaySignupLocation } from '@zeal/domains/UserEvents'

import { DetectOwnerWallet } from './DetectOwnerWallet'
import { FetchGnosisAccountStatus } from './FetchGnosisAccountStatus'
import { filterAccountsByCardSlientSigningKeyStore } from './filterAccountsByCardSlientSigningKeyStore'

type Props = {
    userSelected: 'create' | 'import'
    installationId: string
    location: GnosisPaySignupLocation
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    installationCampaign: string | null
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof DetectOwnerWallet>,
          {
              type:
                  | 'on_create_smart_wallet_clicked'
                  | 'on_card_import_on_import_keys_clicked'
          }
      >
    | Extract<
          MsgOf<typeof FetchGnosisAccountStatus>,
          {
              type:
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'on_gnosis_pay_not_available_accepted'
          }
      >
    | Extract<
          MsgOf<typeof GnosisPaySignupFlow>,
          {
              type:
                  | 'close'
                  | 'on_gnosis_pay_account_created'
                  | 'on_gnosis_pay_kyc_submitted_animation_complete'
                  | 'on_gnosis_pay_onboarding_flow_closed'
          }
      >

type State =
    | { type: 'choose_wallet'; userSelected: 'create' | 'import' }
    | {
          type: 'fetch_gnosis_account_status'
          cardReadonlySigner: Account
          keyStore: CardSlientSignKeyStore
          userSelected: 'create' | 'import'
      }
    | {
          type: 'gnosis_pay_signup_flow'
          cardReadonlySigner: Account
          keyStore: CardSlientSignKeyStore
          userSelected: 'create' | 'import'
      }

export const OrderCardFlow = ({
    userSelected,
    location,
    accountsMap,
    keystoreMap,
    portfolioMap,
    currencyHiddenMap,
    installationId,
    networkMap,
    networkRPCMap,
    installationCampaign,
    sessionPassword,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'choose_wallet',
        userSelected,
    })

    switch (state.type) {
        case 'choose_wallet':
            return (
                <DetectOwnerWallet
                    userSelected={state.userSelected}
                    accountsMap={accountsMap}
                    keystoreMap={keystoreMap}
                    installationId={installationId}
                    portfolioMap={portfolioMap}
                    currencyHiddenMap={currencyHiddenMap}
                    networkRPCMap={networkRPCMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_card_import_on_import_keys_clicked':
                            case 'on_create_smart_wallet_clicked':
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_zeal_active_owner_detected':
                            case 'on_zeal_active_account_selected':
                            case 'on_add_card_owner_event_detected':
                                setState({
                                    type: 'fetch_gnosis_account_status',
                                    cardReadonlySigner: msg.account,
                                    keyStore: msg.keyStore,
                                    userSelected: state.userSelected,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        case 'fetch_gnosis_account_status':
            return (
                <FetchGnosisAccountStatus
                    sessionPassword={sessionPassword}
                    installationId={installationId}
                    account={state.cardReadonlySigner}
                    keyStore={state.keyStore}
                    userSelected={userSelected}
                    networkMap={networkMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    networkRPCMap={networkRPCMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_no_gnosis_pay_account_exists':
                                setState({
                                    type: 'gnosis_pay_signup_flow',
                                    cardReadonlySigner:
                                        state.cardReadonlySigner,
                                    keyStore: state.keyStore,
                                    userSelected: state.userSelected,
                                })
                                break
                            case 'on_card_imported_success_animation_complete':
                            case 'on_onboarded_card_imported_success_animation_complete':
                            case 'close': // we can't go to choose wallet because it will create a loop
                            case 'on_gnosis_pay_not_available_accepted':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'gnosis_pay_signup_flow':
            return (
                <GnosisPaySignupFlow
                    installationCampaign={installationCampaign}
                    location={location}
                    installationId={installationId}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    networkRPCMap={networkRPCMap}
                    networkMap={networkMap}
                    sessionPassword={sessionPassword}
                    keyStore={state.keyStore}
                    cardReadonlySigner={state.cardReadonlySigner}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_gnosis_pay_onboarding_flow_closed':
                            case 'on_gnosis_pay_account_created':
                            case 'on_gnosis_pay_kyc_submitted_animation_complete':
                                onMsg(msg)
                                break
                            case 'on_connect_existing_gnosis_pay_account_clicked':
                                {
                                    const items =
                                        filterAccountsByCardSlientSigningKeyStore(
                                            accountsMap,
                                            keystoreMap
                                        )
                                    if (items.length === 1) {
                                        onMsg({
                                            type: 'on_card_import_on_import_keys_clicked',
                                        })
                                        break
                                    }
                                    setState({
                                        type: 'choose_wallet',
                                        userSelected: 'import',
                                    })
                                }

                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
