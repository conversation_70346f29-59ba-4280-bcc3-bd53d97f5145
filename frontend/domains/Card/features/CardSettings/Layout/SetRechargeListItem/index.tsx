import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { Earn } from '@zeal/domains/Earn'

import { DisabledRechargeListItem } from './DisabledRechargeListItem'
import { EnabledRechargeListItem } from './EnabledRechargeListItem'

type Msg =
    | MsgOf<typeof EnabledRechargeListItem>
    | MsgOf<typeof DisabledRechargeListItem>

type Props = {
    cardOwnerEarn: Earn
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    onMsg: (msg: Msg) => void
}
export const SetRechargeListItem = ({
    cardOwnerEarn,
    cardConfig,
    onMsg,
}: Props) => {
    switch (cardOwnerEarn.type) {
        case 'not_configured':
            return <DisabledRechargeListItem onMsg={onMsg} />
        case 'configured':
            switch (cardOwnerEarn.cardRecharge.type) {
                case 'recharge_disabled':
                    return <DisabledRechargeListItem onMsg={onMsg} />
                case 'recharge_enabled':
                    return (
                        <EnabledRechargeListItem
                            cardRecharge={cardOwnerEarn.cardRecharge}
                            cardConfig={cardConfig}
                            cardOwnerEarn={cardOwnerEarn}
                            onMsg={onMsg}
                        />
                    )

                /* istanbul ignore next */
                default:
                    return notReachable(cardOwnerEarn.cardRecharge)
            }

        /* istanbul ignore next */
        default:
            return notReachable(cardOwnerEarn)
    }
}
