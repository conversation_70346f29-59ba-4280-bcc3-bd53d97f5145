import { notReachable } from '@zeal/toolkit'
import {
    arrayOf,
    bigint,
    failure,
    match,
    number,
    numberString,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'

import {
    CardPayment,
    CardTransaction,
    CardTransactionState,
    DeclineReason,
    MerchantInfo,
    Refund,
    Reversal,
} from '@zeal/domains/Card'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import { parseFiatCurrencyCode } from '@zeal/domains/Currency/helpers/parse'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'

import { mapGnosisFiatSymbolToCryptoCurrency } from './mapGnosisTokenSymbol'

const _ = (trx: CardTransaction) => {
    switch (trx.kind) {
        case 'Payment':
        case 'Reversal':
        case 'Refund':
        case 'Unknown':
            // Don't forget to add a parser for new kind!
            return trx
        default:
            return notReachable(trx)
    }
}

const parseMerchantInfo = (trx: unknown): Result<unknown, MerchantInfo> =>
    object(trx).andThen((obj) =>
        shape({
            mcc: numberString(obj.mcc),
            merchant: object(obj.merchant).andThen((merchObj) =>
                shape({
                    name: string(merchObj.name).map((str) => str.trim()),
                    city: string(merchObj.city).map((str) => str.trim()),
                    country: object(merchObj.country)
                        .andThen((country) => string(country.name))
                        .map((str) => str.trim()),
                })
            ),
        }).map(
            ({ mcc, merchant: { city, country, name } }): MerchantInfo => ({
                city,
                country,
                name,
                mcc,
            })
        )
    )

const declineReasonsMap: Record<DeclineReason, true> = {
    IncorrectPin: true,
    InsufficientFunds: true,
    InvalidAmount: true,
    PinEntryTriesExceeded: true,
    IncorrectSecurityCode: true,
    ExceedsApprovalAmountLimit: true,
    Other: true,
}

export const parseTransactionDeclineReason = (
    input: unknown
): Result<unknown, DeclineReason> => {
    if (declineReasonsMap[input as DeclineReason]) {
        return success(input as DeclineReason)
    }

    return failure({ type: 'unknown_decline_reason', reason: input })
}

const parseBillingAmount = (trx: unknown): Result<unknown, CryptoMoney> =>
    object(trx).andThen((obj) =>
        shape({
            billingAmount: bigint(obj.billingAmount),
            billingCurrency: object(obj.billingCurrency).andThen((curObj) =>
                shape({
                    currency: mapGnosisFiatSymbolToCryptoCurrency(
                        curObj.symbol
                    ),
                    decimals: number(curObj.decimals),
                })
            ),
        }).andThen(
            ({
                billingAmount,
                billingCurrency,
            }): Result<unknown, CryptoMoney> => {
                const { currency } = billingCurrency

                const fractionCorrection =
                    currency.fraction - billingCurrency.decimals

                return success({
                    amount: billingAmount * 10n ** BigInt(fractionCorrection),
                    currency: currency,
                })
            }
        )
    )

const parseTransactionAmount = (
    trx: unknown
): Result<unknown, FiatMoney | null> =>
    object(trx).andThen((obj) =>
        oneOf(obj.transactionAmount, [
            shape({
                transactionAmount: bigint(obj.transactionAmount),
                transactionCurrency: object(obj.transactionCurrency).andThen(
                    (curObj) =>
                        shape({
                            symbol: string(curObj.symbol).andThen(
                                parseFiatCurrencyCode
                            ),
                            decimals: number(curObj.decimals),
                        })
                ),
            }).andThen(
                ({
                    transactionAmount,
                    transactionCurrency,
                }): Result<unknown, FiatMoney> => {
                    const matchingFiatCurrency =
                        FIAT_CURRENCIES[transactionCurrency.symbol] || null

                    if (!matchingFiatCurrency) {
                        return failure({
                            type: 'unknown_fiat_currency',
                            symbol: transactionCurrency.symbol,
                        })
                    }

                    const fractionCorrection =
                        matchingFiatCurrency.fraction -
                        transactionCurrency.decimals

                    return success({
                        amount:
                            transactionAmount *
                            10n ** BigInt(fractionCorrection),
                        currency: matchingFiatCurrency,
                    })
                }
            ),
            success(null),
        ])
    )

const parseReversal = (input: unknown): Result<unknown, Reversal> =>
    object(input).andThen((obj) =>
        shape({
            kind: match(obj.kind, 'Reversal' as const),
            merchant: parseMerchantInfo(input),
            billingAmount: parseBillingAmount(input),
            state: parseTransactionState(input),
            transactionAmount: parseTransactionAmount(input),
            createdAt: string(obj.createdAt).andThen(parseTransactionCreatedAt),
            onChainTransactionHash: arrayOf(obj.transactions, (item) =>
                object(item).andThen((obj) => string(obj.hash))
            ).andThen((hashes) =>
                hashes[0]
                    ? success(hashes[0])
                    : failure({ type: 'empty_transaction_hashes' })
            ),
        })
    )

const parseRefund = (input: unknown): Result<unknown, Refund> =>
    object(input).andThen((obj) =>
        shape({
            kind: match(obj.kind, 'Refund' as const),
            merchant: parseMerchantInfo(input),
            state: parseTransactionState(input),
            billingAmount: parseBillingAmount(input),
            transactionAmount: parseTransactionAmount(input),
            createdAt: string(obj.createdAt).andThen(parseTransactionCreatedAt),
        })
    )

const parsePayment = (input: unknown): Result<unknown, CardPayment> =>
    object(input).andThen((obj) =>
        oneOf(obj, [
            shape({
                kind: match(obj.kind, 'Payment' as const),
                status: oneOf(obj.status, [
                    match(obj.status, 'Approved' as const),
                    match(obj.status, 'Reversal' as const),
                ]),
                merchant: parseMerchantInfo(input),
                billingAmount: parseBillingAmount(input),
                transactionAmount: parseTransactionAmount(input),
                createdAt: string(obj.createdAt).andThen(
                    parseTransactionCreatedAt
                ),
                state: parseTransactionState(input),
                onChainTransactionHash: arrayOf(obj.transactions, (item) =>
                    object(item).andThen((obj) => string(obj.hash))
                ).andThen((hashes) =>
                    hashes[0]
                        ? success(hashes[0])
                        : failure({ type: 'empty_transaction_hashes' })
                ),
            }),
            shape({
                kind: match(obj.kind, 'Payment' as const),
                status: success('Declined' as const),
                state: parseTransactionState(input),
                reason: parseTransactionDeclineReason(obj.status),
                merchant: parseMerchantInfo(input),
                billingAmount: parseBillingAmount(input),
                transactionAmount: parseTransactionAmount(input),
                createdAt: string(obj.createdAt).andThen(
                    parseTransactionCreatedAt
                ),
            }),
        ])
    )

const parseTransactionState = (
    input: unknown
): Result<unknown, CardTransactionState> =>
    object(input).andThen((obj) =>
        oneOf(obj, [
            shape({
                type: success('pending' as const),
                isPending: match(obj.isPending, true),
            }).map(({ type }) => ({ type })),
            shape({
                type: success('settled' as const),
                isPending: match(obj.isPending, false),
                clearedAt: oneOf(obj, [
                    parseTransactionClearedAt(obj.clearedAt),
                    parseTransactionCreatedAt(obj.createdAt),
                ]),
            }).map(({ type, clearedAt }) => ({ type, clearedAt })),
        ])
    )

const parseTransactionCreatedAt = (input: unknown): Result<unknown, number> =>
    string(input).map((str) => new Date(str).getTime())

const parseTransactionClearedAt = (input: unknown): Result<unknown, number> =>
    string(input).map((str) => new Date(str).getTime())

// Don't forget to add parser to the oneOf in parseCardTransaction
const _cardTransactionParserMap: Record<CardTransaction['kind'], true> = {
    Payment: true,
    Reversal: true,
    Refund: true,
    Unknown: true,
}

export const parseCardTransaction = (
    input: unknown
): Result<unknown, CardTransaction> =>
    oneOf(input, [
        parsePayment(input),
        parseReversal(input),
        parseRefund(input),
        success({
            kind: 'Unknown',
            billingAmount: parseBillingAmount(input).getSuccessResult() || null,
            transactionAmount:
                parseTransactionAmount(input).getSuccessResult() || null,
            createdAt:
                object(input)
                    .andThen((obj) => parseTransactionCreatedAt(obj.createdAt))
                    .getSuccessResult() || Date.now(),
            originalTransaction: input,
        } as const),
    ])
