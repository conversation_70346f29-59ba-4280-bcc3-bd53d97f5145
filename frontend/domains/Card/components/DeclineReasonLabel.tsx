import { FormattedMessage } from 'react-intl'

import { notReachable } from '@zeal/toolkit'

import { DeclineReason } from '..'

type Props = {
    reason: DeclineReason
}

export const DeclineReasonLabel = ({ reason }: Props) => {
    switch (reason) {
        case 'InsufficientFunds':
            return (
                <FormattedMessage
                    defaultMessage="Low balance"
                    id="cards.transactions.decline_reason.low_balance"
                />
            )

        case 'IncorrectPin':
            return (
                <FormattedMessage
                    defaultMessage="Incorrect PIN"
                    id="cards.transactions.decline_reason.incorrect_pin"
                />
            )

        case 'InvalidAmount':
            return (
                <FormattedMessage
                    defaultMessage="Invalid amount"
                    id="cards.transactions.decline_reason.invalid_amount"
                />
            )

        case 'PinEntryTriesExceeded':
            return (
                <FormattedMessage
                    defaultMessage="PIN tries exceeded"
                    id="cards.transactions.decline_reason.pin_tries_exceeded"
                />
            )

        case 'IncorrectSecurityCode':
            return (
                <FormattedMessage
                    defaultMessage="Incorrect security code"
                    id="cards.transactions.decline_reason.incorrect_security_code"
                />
            )
        case 'ExceedsApprovalAmountLimit':
            return (
                <FormattedMessage
                    defaultMessage="Exceeds daily limit"
                    id="cards.transactions.decline_reason.exceeds_approval_amount_limit"
                />
            )

        case 'Other':
            return (
                <FormattedMessage
                    defaultMessage="Declined"
                    id="cards.transactions.decline_reason.other"
                />
            )

        default:
            return notReachable(reason)
    }
}
