import { Auth } from '@zeal/api/Auth'
import { get } from '@zeal/api/gnosisApi'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { parse as parseJSON } from '@zeal/toolkit/JSON'
import { isNonEmptyArray, NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import { pickKeys, scrubSensitiveFields } from '@zeal/toolkit/Object'
import {
    arrayOf,
    boolean,
    combine,
    failure,
    match,
    nullable,
    nullableOf,
    number,
    object,
    oneOf,
    Result,
    safeArrayOf,
    shape,
    string,
    success,
    unknown,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    CardSafeState,
    CardSlientSignKeyStore,
    CardTermsRequiredForSignup,
    GnosisPayAccountConfigurationState,
    GnosisPayAccountOnboardedState,
    GnosisPayAccountState2,
    GnosisPayLoginInfo,
    GnosisPayOnboardedKycStatus,
    NotActivatedPhysicalCard,
    ResidentialAddress,
} from '@zeal/domains/Card'
import { fetchBalance } from '@zeal/domains/Card/api/fetchBalance'
import { fetchActivatedCardState } from '@zeal/domains/Card/api/fetchCardState'
import { fetchCardTerms } from '@zeal/domains/Card/api/fetchCardTerms'
import { loginWithCache } from '@zeal/domains/Card/api/login'
import { GNOSIS_PAY_KEYS_SAFE_TO_REPORT } from '@zeal/domains/Card/constants'
import {
    mapGnosisFiatSymbolFiatCurrency,
    mapGnosisTokenSymbolCryptoCurrency,
} from '@zeal/domains/Card/helpers/mapGnosisTokenSymbol'
import { CountryISOCode } from '@zeal/domains/Country'
import { parseCountryISOCode } from '@zeal/domains/Country/helpers/parseCountryISOCode'
import { CryptoCurrency, FiatCurrency } from '@zeal/domains/Currency'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { fetchCardSafeState } from './fetchCardSafeState'
import { fetchIsCreatedViaZeal } from './fetchIsCreatedViaZeal'

enum CardSafeStatus {
    Ok = 0,
    SafeNotDeployed = 1,
    SafeMisconfigured = 2,
    DelayQueueNotEmpty = 7,
    Unknown = 8,
}

type CardOrderStatus =
    | 'PENDINGTRANSACTION'
    | 'TRANSACTIONCOMPLETE'
    | 'CONFIRMATIONREQUIRED'
    | 'READY'
    | 'CARDCREATED'
    | 'FAILEDTRANSACTION'
    | 'CANCELLED'

type RawKycStatus =
    | 'notStarted'
    | 'documentsRequested'
    | 'pending'
    | 'processing'
    | 'resubmissionRequested'
    | 'rejected'
    | 'approved'
    | 'requiresAction'

type ConfiguredSafeAccount = {
    type: 'configured'
    isDeployed: true
    accountStatus:
        | CardSafeStatus.Ok
        | CardSafeStatus.DelayQueueNotEmpty
        | CardSafeStatus.Unknown
    address: Web3.address.Address
    fiatCurrency: FiatCurrency
    cryptoCurrency: CryptoCurrency
}

type NotDeployedSafeAccount = {
    type: 'not_deployed'
    isDeployed: false | null
    accountStatus:
        | CardSafeStatus.SafeNotDeployed
        | CardSafeStatus.Unknown
        | null
}
type CurrencyNotConfiguredSafeAccount = {
    type: 'deployed_currency_not_configured'
    isDeployed: true
    address: Web3.address.Address
    accountStatus: null
    tokenSymbol: null
}
type ModulesNotConfiguredSafeAccount = {
    type: 'deployed_modules_not_configured'
    isDeployed: true
    accountStatus: CardSafeStatus.SafeMisconfigured | CardSafeStatus.Unknown
    address: Web3.address.Address
    fiatCurrency: FiatCurrency
    cryptoCurrency: CryptoCurrency
}

type SafeAccount =
    | NotDeployedSafeAccount
    | CurrencyNotConfiguredSafeAccount
    | ModulesNotConfiguredSafeAccount
    | ConfiguredSafeAccount

type GnosisPayUserProfile =
    | KYCNotStarted
    | KYCStarted
    | KYCApproved
    | CardOrderCreated
    | CardOrderReady
    | CardOrderCardCreated
    | CardActivated

type KYCNotStarted = {
    type: 'kyc_not_started'
    safeAccount:
        | NotDeployedSafeAccount
        | CurrencyNotConfiguredSafeAccount
        | ModulesNotConfiguredSafeAccount
    userId: string
}

type KYCStarted = {
    type: 'kyc_started'
    userId: string
    kycStatus: Extract<
        RawKycStatus,
        | 'documentsRequested'
        | 'pending'
        | 'processing'
        | 'resubmissionRequested'
        | 'rejected'
        | 'requiresAction'
    >
    safeAccount: SafeAccount // FIXME @Nicvaniek should be NotDeployedSafeAccount | CurrencyNotConfiguredSafeAccount, but need this for now to support Re-KYC
}

type KYCApproved = {
    type: 'kyc_approved'
    safeAccount: SafeAccount
    hasVerifiedPhoneNumber: boolean
    userId: string
    hasAnsweredSourceOfFunds: boolean
    residentialAddress: ResidentialAddress | null
}

type CardOrderCreated = {
    type: 'card_order_created'
    userId: string
    kycStatus: Extract<RawKycStatus, 'approved'>
    safeAccount: SafeAccount // FIXME :: @Nicvaniek change to ConfiguredSafeAccount once we've released new card order flows. Changing now will break for existing users that have card orders created but safe not deployed / configured yet
    hasVerifiedPhoneNumber: boolean
    hasAnsweredSourceOfFunds: boolean
    residentialAddress: ResidentialAddress | null
    cardOrder: {
        id: string
        status:
            | 'PENDINGTRANSACTION'
            | 'TRANSACTIONCOMPLETE'
            | 'CONFIRMATIONREQUIRED'
            | 'FAILEDTRANSACTION'
            | 'CANCELLED'
        shippingAddress: ResidentialAddress | null
    }
}

type CardOrderReady = {
    type: 'card_order_ready'
    kycStatus: Extract<RawKycStatus, 'approved'>
    safeAccount: SafeAccount // FIXME :: @Nicvaniek change to ConfiguredSafeAccount once we've released new card order flows. Changing now will break for existing users that have card orders created but safe not deployed / configured yet
    userId: string
    hasVerifiedPhoneNumber: boolean
    hasAnsweredSourceOfFunds: boolean
    residentialAddress: ResidentialAddress | null
    cardOrder: {
        id: string
        status: 'READY'
        shippingAddress: ResidentialAddress | null
    }
}

type CardOrderCardCreated = {
    type: 'card_order_card_created'
    kycStatus: Extract<RawKycStatus, 'approved'>
    safeAccount: SafeAccount // FIXME :: @Nicvaniek change to ConfiguredSafeAccount once we've released new card order flows. Changing now will break for existing users that have card orders created but safe not deployed / configured yet
    cardOrderStatus: 'CARDCREATED'
    country: CountryISOCode | null
    accountConfiguration: { type: 'fully_configured' }
    notActivatedPhysicalCards: NonEmptyArray<NotActivatedPhysicalCard>
    userId: string
}

type CardActivated = {
    type: 'card_activated'
    safeAccount: ConfiguredSafeAccount
    activatedCards: {
        id: string
        lastFourDigits: string
        activatedAt: string
        virtual: boolean
    }[]
    notActivatedPhysicalCards: NotActivatedPhysicalCard[]
    residentialAddress: ResidentialAddress | null
    userId: string
    fullName: string | null
    kycStatus: RawKycStatus
}

const mapSafeAccountToCardSafeState = (
    safeAccount: SafeAccount
): CardSafeState => {
    switch (safeAccount.type) {
        case 'not_deployed':
            return { type: 'not_deployed' }
        case 'deployed_currency_not_configured':
            return { type: 'deployed', address: safeAccount.address }
        case 'deployed_modules_not_configured':
            return {
                type: 'currency_configured',
                address: safeAccount.address,
                cryptoCurrency: safeAccount.cryptoCurrency,
                fiatCurrency: safeAccount.fiatCurrency,
            }
        case 'configured':
            return {
                type: 'fully_configured',
                address: safeAccount.address,
                cryptoCurrency: safeAccount.cryptoCurrency,
                fiatCurrency: safeAccount.fiatCurrency,
            }
        /* istanbul ignore next */
        default:
            return notReachable(safeAccount)
    }
}

const mapOnboardedKycStatus = (
    rawKycStatus:
        | 'notStarted'
        | 'documentsRequested'
        | 'pending'
        | 'processing'
        | 'resubmissionRequested'
        | 'rejected'
        | 'approved'
        | 'requiresAction'
): GnosisPayOnboardedKycStatus => {
    switch (rawKycStatus) {
        case 'notStarted':
            return 'not_started'
        case 'documentsRequested':
            return 'documents_requested'
        case 'pending':
        case 'processing':
        case 'requiresAction':
            return 'verification_in_progress'
        case 'resubmissionRequested':
            return 'resubmission_requested'
        case 'rejected':
            return 'failed'
        case 'approved':
            return 'approved'
        default:
            return notReachable(rawKycStatus)
    }
}

const parseRawKycStatus = (input: unknown): Result<unknown, RawKycStatus> =>
    string(input).andThen((str) =>
        oneOf(str, [
            match(str, 'notStarted' as const),
            match(str, 'documentsRequested' as const),
            match(str, 'pending' as const),
            match(str, 'processing' as const),
            match(str, 'resubmissionRequested' as const),
            match(str, 'rejected' as const),
            match(str, 'approved' as const),
            match(str, 'requiresAction' as const),
        ])
    )

const parseCardOrder = (
    input: unknown
): Result<
    unknown,
    {
        id: string
        status: CardOrderStatus
        shippingAddress: ResidentialAddress | null
    }
> =>
    object(input).andThen((obj) =>
        shape({
            id: string(obj.id),
            status: oneOf(obj.status, [
                match(obj.status, 'PENDINGTRANSACTION'),
                match(obj.status, 'TRANSACTIONCOMPLETE'),
                match(obj.status, 'CONFIRMATIONREQUIRED'),
                match(obj.status, 'READY'),
                match(obj.status, 'CARDCREATED'),
                match(obj.status, 'FAILEDTRANSACTION'),
                match(obj.status, 'CANCELLED'),
            ]),
            shippingAddress: oneOf(obj, [
                parseResidentialAddress(obj),
                success(null),
            ]),
        })
    )

const parseNotDeployedSafeAccount = (
    input: unknown
): Result<unknown, NotDeployedSafeAccount> =>
    object(input).andThen((obj) =>
        shape({
            type: success('not_deployed' as const),
            isDeployed: nullableOf(obj.isDeployed, (input) =>
                match(input, false)
            ),
            accountStatus: nullableOf(obj.accountStatus, (input) =>
                oneOf(input, [
                    match(input, CardSafeStatus.SafeNotDeployed),
                    match(input, CardSafeStatus.Unknown),
                ])
            ),
        })
    )

const parseCurrencyNotConfiguredSafeAccount = (
    input: unknown
): Result<unknown, CurrencyNotConfiguredSafeAccount> =>
    object(input).andThen((obj) =>
        shape({
            type: success('deployed_currency_not_configured' as const),
            isDeployed: match(obj.isDeployed, true),
            address: Web3.address.parse(obj.address),
            accountStatus: nullable(obj.accountStatus),
            tokenSymbol: nullable(obj.tokenSymbol),
        })
    )

const parseModulesNotConfiguredSafeAccount = (
    input: unknown
): Result<unknown, ModulesNotConfiguredSafeAccount> =>
    object(input).andThen((obj) =>
        shape({
            type: success('deployed_modules_not_configured' as const),
            isDeployed: match(obj.isDeployed, true),
            address: Web3.address.parse(obj.address),
            accountStatus: oneOf(obj.accountStatus, [
                match(obj.accountStatus, CardSafeStatus.SafeMisconfigured),
                match(obj.accountStatus, CardSafeStatus.Unknown),
            ]),
            cryptoCurrency: mapGnosisTokenSymbolCryptoCurrency(obj.tokenSymbol),
            fiatCurrency: mapGnosisFiatSymbolFiatCurrency(obj.fiatSymbol),
        })
    )

const parseConfiguredSafeAccount = (
    input: unknown
): Result<unknown, ConfiguredSafeAccount> =>
    object(input).andThen((obj) =>
        shape({
            type: success('configured' as const),
            address: Web3.address.parse(obj.address),
            isDeployed: match(obj.isDeployed, true),
            accountStatus: oneOf(obj.accountStatus, [
                match(obj.accountStatus, CardSafeStatus.Ok),
                match(obj.accountStatus, CardSafeStatus.DelayQueueNotEmpty),
                match(obj.accountStatus, CardSafeStatus.Unknown),
            ]),
            cryptoCurrency: mapGnosisTokenSymbolCryptoCurrency(obj.tokenSymbol),
            fiatCurrency: mapGnosisFiatSymbolFiatCurrency(obj.fiatSymbol),
        })
    )

const parseSafeAccount = (input: unknown): Result<unknown, SafeAccount> =>
    oneOf(input, [
        parseConfiguredSafeAccount(input),
        parseModulesNotConfiguredSafeAccount(input),
        parseCurrencyNotConfiguredSafeAccount(input),
        parseNotDeployedSafeAccount(input),
    ])

const parseKycNotStartedProfile = (
    userResponse: unknown,
    safeConfigResponse: unknown
): Result<unknown, KYCNotStarted> =>
    object(userResponse).andThen((obj) =>
        shape({
            type: success('kyc_not_started' as const),
            kycStatus: match(obj.kycStatus, 'notStarted'),
            userId: string(obj.id),
            safeAccount: oneOf(safeConfigResponse, [
                parseNotDeployedSafeAccount(safeConfigResponse),
                parseCurrencyNotConfiguredSafeAccount(safeConfigResponse),
                parseModulesNotConfiguredSafeAccount(safeConfigResponse),
            ]),
        })
    )

const parseKycStartedProfile = (
    userResponse: unknown,
    safeConfigResponse: unknown
): Result<unknown, KYCStarted> =>
    object(userResponse).andThen((obj) =>
        shape({
            type: success('kyc_started' as const),
            kycStatus: oneOf(obj.kycStatus, [
                match(obj.kycStatus, 'documentsRequested'),
                match(obj.kycStatus, 'pending'),
                match(obj.kycStatus, 'processing'),
                match(obj.kycStatus, 'resubmissionRequested'),
                match(obj.kycStatus, 'rejected'),
                match(obj.kycStatus, 'requiresAction'),
            ]),
            userId: string(obj.id),
            safeAccount: parseSafeAccount(safeConfigResponse),
        })
    )

const parseKycApprovedProfile = (
    userResponse: unknown,
    safeConfigResponse: unknown
): Result<unknown, KYCApproved> =>
    object(userResponse).andThen((obj) =>
        shape({
            type: success('kyc_approved' as const),
            kycStatus: match(obj.kycStatus, 'approved' as const),
            userId: string(obj.id),
            safeAccount: parseSafeAccount(safeConfigResponse),
            hasVerifiedPhoneNumber: boolean(obj.isPhoneValidated),
            hasAnsweredSourceOfFunds: boolean(obj.isSourceOfFundsAnswered),
            residentialAddress: oneOf(obj, [
                parseResidentialAddress(obj),
                success(null),
            ]),
        })
    )

const parseCardOrderCreatedProfile = (
    userResponse: unknown,
    cardOrdersResponse: unknown,
    safeConfigResponse: unknown
): Result<unknown, CardOrderCreated> =>
    shape({
        type: success('card_order_created' as const),
        user: object(userResponse).andThen((userObj) =>
            shape({
                kycStatus: match(userObj.kycStatus, 'approved' as const),
                userId: string(userObj.id),
                hasVerifiedPhoneNumber: boolean(userObj.isPhoneValidated),
                hasAnsweredSourceOfFunds: boolean(
                    userObj.isSourceOfFundsAnswered
                ),
                residentialAddress: oneOf(userObj, [
                    parseResidentialAddress(userObj),
                    success(null),
                ]),
            })
        ),
        cardOrder: safeArrayOf(cardOrdersResponse, parseCardOrder).andThen(
            (orders) => {
                const order = orders.find(
                    (o): o is CardOrderCreated['cardOrder'] => {
                        switch (o.status) {
                            case 'PENDINGTRANSACTION':
                            case 'TRANSACTIONCOMPLETE':
                            case 'CONFIRMATIONREQUIRED':
                            case 'FAILEDTRANSACTION':
                            case 'CANCELLED':
                                return true
                            case 'CARDCREATED':
                            case 'READY':
                                return false
                            /* istanbul ignore next */
                            default:
                                return notReachable(o.status)
                        }
                    }
                )

                return order
                    ? success(order)
                    : failure({ type: 'no created card order found' })
            }
        ),
        safeAccount: parseSafeAccount(safeConfigResponse),
    }).map(({ type, user, cardOrder, safeAccount }) => ({
        type,
        cardOrder,
        safeAccount,
        ...user,
    }))

const parseCardOrderReadyProfile = (
    userResponse: unknown,
    cardOrdersResponse: unknown,
    safeConfigResponse: unknown
): Result<unknown, CardOrderReady> =>
    shape({
        type: success('card_order_ready' as const),
        user: object(userResponse).andThen((userObj) =>
            shape({
                kycStatus: match(userObj.kycStatus, 'approved' as const),
                userId: string(userObj.id),
                hasVerifiedPhoneNumber: boolean(userObj.isPhoneValidated),
                hasAnsweredSourceOfFunds: boolean(
                    userObj.isSourceOfFundsAnswered
                ),
                residentialAddress: oneOf(userObj, [
                    parseResidentialAddress(userObj),
                    success(null),
                ]),
            })
        ),
        cardOrder: safeArrayOf(cardOrdersResponse, parseCardOrder).andThen(
            (orders) => {
                const order = orders.find(
                    (o): o is CardOrderReady['cardOrder'] => {
                        switch (o.status) {
                            case 'READY':
                                return true
                            case 'PENDINGTRANSACTION':
                            case 'TRANSACTIONCOMPLETE':
                            case 'CONFIRMATIONREQUIRED':
                            case 'FAILEDTRANSACTION':
                            case 'CANCELLED':
                            case 'CARDCREATED':
                                return false
                            /* istanbul ignore next */
                            default:
                                return notReachable(o.status)
                        }
                    }
                )

                return order
                    ? success(order)
                    : failure({ type: 'no ready card order found' })
            }
        ),
        safeAccount: parseSafeAccount(safeConfigResponse),
    }).map(({ type, user, cardOrder, safeAccount }) => ({
        type,
        cardOrder,
        safeAccount,
        ...user,
    }))

const parseCardOrderCardCreatedProfile = (
    userResponse: unknown,
    cardOrdersResponse: unknown,
    safeConfigResponse: unknown
): Result<unknown, CardOrderCardCreated> =>
    shape({
        type: success('card_order_card_created' as const),
        user: object(userResponse).andThen((userObj) =>
            shape({
                country: nullableOf(userObj.country, parseCountryISOCode),
                kycStatus: match(userObj.kycStatus, 'approved' as const),
                userId: string(userObj.id),
                hasVerifiedPhoneNumber: match(userObj.isPhoneValidated, true),
                hasAnsweredSourceOfFunds: match(
                    userObj.isSourceOfFundsAnswered,
                    true
                ),
                notActivatedPhysicalCards: arrayOf(
                    userObj.cards,
                    unknown
                ).andThen((cards) =>
                    combine(
                        cards.map((card) =>
                            object(card).andThen((cardObj) =>
                                shape({
                                    id: string(cardObj.id),
                                    lastFourDigits: string(
                                        cardObj.lastFourDigits
                                    ),
                                    activatedAt: nullable(cardObj.activatedAt),
                                    virtual: match(cardObj.virtual, false),
                                }).map((card) => ({
                                    ...card,
                                    type: 'physical' as const,
                                    state: {
                                        type: 'card_not_activated' as const,
                                    },
                                }))
                            )
                        )
                    ).andThen((cards) =>
                        isNonEmptyArray(cards)
                            ? success(cards)
                            : failure({
                                  type: 'no not activated physical cards found',
                              })
                    )
                ),
            })
        ),
        cardOrderStatus: safeArrayOf(
            cardOrdersResponse,
            parseCardOrder
        ).andThen((orders) => {
            const orderStatus = orders
                .map((o) => o.status)
                .find(
                    (
                        status
                    ): status is Extract<CardOrderStatus, 'CARDCREATED'> => {
                        switch (status) {
                            case 'CARDCREATED':
                                return true
                            case 'PENDINGTRANSACTION':
                            case 'TRANSACTIONCOMPLETE':
                            case 'CONFIRMATIONREQUIRED':
                            case 'FAILEDTRANSACTION':
                            case 'CANCELLED':
                            case 'READY':
                                return false
                            /* istanbul ignore next */
                            default:
                                return notReachable(status)
                        }
                    }
                )

            return orderStatus
                ? success(orderStatus)
                : failure({ type: 'no ready card order found' })
        }),
        safeAccount: parseSafeAccount(safeConfigResponse),
    }).map(({ user, ...rest }) => ({
        ...rest,
        kycStatus: user.kycStatus,
        userId: user.userId,
        country: user.country,
        notActivatedPhysicalCards: user.notActivatedPhysicalCards,
        accountConfiguration: { type: 'fully_configured' },
    }))

const parseResidentialAddress = (
    response: unknown
): Result<unknown, ResidentialAddress> =>
    object(response).andThen((obj) =>
        shape({
            address1: string(obj.address1),
            address2: nullableOf(obj.address2, string),
            city: string(obj.city),
            country: parseCountryISOCode(obj.country),
            postalCode: string(obj.postalCode),
        })
    )

const parseCardActivatedProfile = (
    userResponse: unknown,
    safeConfigResponse: unknown
): Result<unknown, CardActivated> =>
    shape({
        type: success('card_activated' as const),
        safeAccount: parseConfiguredSafeAccount(safeConfigResponse),
        user: object(userResponse).andThen((obj) =>
            shape({
                residentialAddress: oneOf(obj, [
                    parseResidentialAddress(obj),
                    success(null),
                ]),
                fullName: oneOf({ id: obj.id }, [
                    shape({
                        firstName: string(obj.firstName),
                        lastName: string(obj.lastName),
                    }).map(
                        ({ firstName, lastName }) => `${firstName} ${lastName}`
                    ),
                    success(null),
                ]),
                userId: string(obj.id),
                kycStatus: parseRawKycStatus(obj.kycStatus),
                cards: arrayOf(obj.cards, unknown).andThen((cards) =>
                    combine(
                        cards.map((card) =>
                            object(card).andThen((cardObj) =>
                                shape({
                                    id: string(cardObj.id),
                                    lastFourDigits: string(
                                        cardObj.lastFourDigits
                                    ),
                                    activatedAt: nullableOf(
                                        cardObj.activatedAt,
                                        () => string(cardObj.activatedAt)
                                    ),
                                    virtual: boolean(cardObj.virtual),
                                })
                            )
                        )
                    ).andThen((cards) => {
                        const { activatedCards, notActivatedPhysicalCards } =
                            cards.reduce<{
                                activatedCards: CardActivated['activatedCards']
                                notActivatedPhysicalCards: CardActivated['notActivatedPhysicalCards']
                            }>(
                                (acc, card) => {
                                    if (card.activatedAt !== null) {
                                        return {
                                            ...acc,
                                            activatedCards: [
                                                ...acc.activatedCards,
                                                {
                                                    id: card.id,
                                                    lastFourDigits:
                                                        card.lastFourDigits,
                                                    activatedAt:
                                                        card.activatedAt,
                                                    virtual: card.virtual,
                                                },
                                            ],
                                        }
                                    }
                                    return {
                                        ...acc,
                                        notActivatedPhysicalCards: [
                                            ...acc.notActivatedPhysicalCards,
                                            {
                                                type: 'physical' as const,
                                                id: card.id,
                                                lastFourDigits:
                                                    card.lastFourDigits,
                                                state: {
                                                    type: 'card_not_activated' as const,
                                                },
                                            },
                                        ],
                                    }
                                },
                                {
                                    activatedCards: [],
                                    notActivatedPhysicalCards: [],
                                }
                            )

                        if (activatedCards.length === 0) {
                            return failure({
                                type: 'no activated cards found',
                            })
                        }

                        return success({
                            activatedCards,
                            notActivatedPhysicalCards,
                        })
                    })
                ),
            })
        ),
    }).map(({ type, user, safeAccount }) => ({
        type,
        safeAccount,
        activatedCards: user.cards.activatedCards,
        notActivatedPhysicalCards: user.cards.notActivatedPhysicalCards,
        residentialAddress: user.residentialAddress,
        fullName: user.fullName,
        kycStatus: user.kycStatus,
        userId: user.userId,
    }))

const parseGnosisPayUserProfile = (
    userResponse: unknown,
    cardOrdersResponse: unknown,
    safeConfigResponse: unknown
): Result<unknown, GnosisPayUserProfile> => {
    const rawResponseToReport = scrubSensitiveFields(
        pickKeys(
            {
                user: userResponse,
                safe: safeConfigResponse,
                orders: cardOrdersResponse,
            },
            GNOSIS_PAY_KEYS_SAFE_TO_REPORT
        )
    )

    return oneOf(rawResponseToReport, [
        parseCardActivatedProfile(userResponse, safeConfigResponse),
        parseCardOrderCardCreatedProfile(
            userResponse,
            cardOrdersResponse,
            safeConfigResponse
        ),
        parseCardOrderReadyProfile(
            userResponse,
            cardOrdersResponse,
            safeConfigResponse
        ),
        parseCardOrderCreatedProfile(
            userResponse,
            cardOrdersResponse,
            safeConfigResponse
        ),
        parseKycApprovedProfile(userResponse, safeConfigResponse),
        parseKycStartedProfile(userResponse, safeConfigResponse),
        parseKycNotStartedProfile(userResponse, safeConfigResponse),
    ])
}

const fetchGnosisPayUserProfile = async ({
    gnosisPayLoginInfo,
    networkRPCMap,
    signal,
}: {
    gnosisPayLoginInfo: GnosisPayLoginInfo
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<GnosisPayUserProfile> => {
    const auth: Auth = { type: 'bearer_token', token: gnosisPayLoginInfo.token }

    const [userResponse, cardOrdersResponse, safeConfigResponse] =
        await Promise.all([
            get('/user', { auth }, signal).then((res) =>
                string(res)
                    .andThen(parseJSON)
                    .getSuccessResultOrThrow(
                        'Failed to parse /user response from gnosisPay'
                    )
            ),
            get(
                '/order',
                {
                    auth,
                },
                signal
            ).then((res) =>
                string(res)
                    .andThen(parseJSON)
                    .getSuccessResultOrThrow(
                        'Failed to parse /order response from gnosisPay'
                    )
            ),
            get(
                '/safe-config',
                {
                    auth,
                },
                signal
            ).then((res) =>
                string(res)
                    .andThen(parseJSON)
                    .getSuccessResultOrThrow(
                        'Failed to parse /safe-config response from gnosisPay'
                    )
            ),
        ])

    // TODO @resetko-zeal ZEAL-4522 migrate to that one if works well in prod
    ;(async () => {
        try {
            const theirConfig = object(safeConfigResponse)
                .andThen((obj) =>
                    shape({
                        accountStatus: nullableOf(obj.accountStatus, number),
                        address: Web3.address.parse(obj.address),
                    })
                )
                .getSuccessResultOrThrow(
                    '[fetchCardSafeState] Failed to parse their status'
                )

            const THEIR_TO_OUR_STATE_EXPECTED_MAP: Record<
                number,
                Awaited<ReturnType<typeof fetchCardSafeState>>
            > = {
                0: 'ok',
                1: 'safe_not_deployed',
                2: 'safe_misconfigured',
                3: 'roles_not_deployed',
                4: 'roles_misconfigured',
                5: 'delay_not_deployed',
                6: 'delay_misconfigured',
                7: 'delay_queue_not_empty',
            }

            const ourState = await fetchCardSafeState({
                cardSafeAddress: theirConfig.address,
                networkRPCMap,
            })

            if (
                !theirConfig.accountStatus ||
                THEIR_TO_OUR_STATE_EXPECTED_MAP[theirConfig.accountStatus] !==
                    ourState
            ) {
                captureError(
                    new ImperativeError(
                        '[fetchCardSafeState] Different states',
                        {
                            safeConfigResponse,
                            ourState,
                        }
                    )
                )
            }
        } catch (error) {
            captureError(
                new ImperativeError('[fetchCardSafeState] exploded', {
                    error: parseAppError(error),
                    safeConfigResponse,
                })
            )
        }
    })()

    const parsedProfile = parseGnosisPayUserProfile(
        userResponse,
        cardOrdersResponse,
        safeConfigResponse
    ).getSuccessResultOrThrow('Failed to parse gnosis pay user profile')

    const unknownAccountStatus = object(safeConfigResponse)
        .andThen((obj) => match(obj.accountStatus, CardSafeStatus.Unknown))
        .getSuccessResult()

    if (unknownAccountStatus) {
        const rawProfileScrubbed = scrubSensitiveFields(
            pickKeys(
                {
                    user: userResponse,
                    safe: safeConfigResponse,
                    orders: cardOrdersResponse,
                },
                GNOSIS_PAY_KEYS_SAFE_TO_REPORT
            )
        )

        const parsedProfileScrubbed = scrubSensitiveFields(
            pickKeys(parsedProfile, GNOSIS_PAY_KEYS_SAFE_TO_REPORT)
        )

        captureError(
            new ImperativeError('Got unknown gnosis pay account status', {
                rawProfileScrubbed,
                parsedProfileScrubbed,
            })
        )
    }

    return parsedProfile
}

const calculateAccountConfiguration = ({
    hasVerifiedPhoneNumber,
    hasAnsweredSourceOfFunds,
}: {
    hasVerifiedPhoneNumber: boolean
    hasAnsweredSourceOfFunds: boolean
}): GnosisPayAccountConfigurationState => {
    if (!hasAnsweredSourceOfFunds && !hasVerifiedPhoneNumber) {
        return {
            type: 'required_sof_verification_and_verification_phone_number',
        }
    }
    if (!hasAnsweredSourceOfFunds) {
        return { type: 'required_sof_verification' }
    }
    if (!hasVerifiedPhoneNumber) {
        return { type: 'required_verification_phone_number' }
    }

    return { type: 'fully_configured' }
}

export const fetchGnosisPayAccountState = async ({
    gnosisPayLoginInfo,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    selectedCardId,
    signal,
}: {
    gnosisPayLoginInfo: GnosisPayLoginInfo
    selectedCardId: string | null
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<GnosisPayAccountState2> => {
    const [profile, terms] = await Promise.all([
        fetchGnosisPayUserProfile({
            networkRPCMap,
            gnosisPayLoginInfo,
            signal,
        }),
        fetchCardTerms({ gnosisPayLoginInfo, signal }),
    ])
    const cardSafe = mapSafeAccountToCardSafeState(profile.safeAccount)

    switch (profile.type) {
        case 'kyc_not_started':
            const missingTerms: CardTermsRequiredForSignup[] = terms.filter(
                (term): term is CardTermsRequiredForSignup => {
                    switch (term.type) {
                        case 'general_terms':
                        case 'monavate_terms':
                            return !term.accepted
                        case 'cashback_terms':
                            return false
                        /* istanbul ignore next */
                        default:
                            return notReachable(term.type)
                    }
                }
            )

            if (missingTerms.length) {
                return {
                    type: 'not_onboarded',
                    userId: profile.userId,
                    state: 'terms_not_accepted',
                    missingSignupTerms: missingTerms,
                    cardSafe,
                }
            }

            return {
                type: 'not_onboarded',
                userId: profile.userId,
                state: 'terms_accepted_kyc_not_started',
                cardSafe,
            }
        case 'kyc_started':
            switch (profile.kycStatus) {
                case 'documentsRequested':
                    return {
                        type: 'not_onboarded',
                        userId: profile.userId,
                        state: 'kyc_started_documents_requested',
                        cardSafe,
                    }
                case 'pending':
                case 'processing':
                case 'requiresAction':
                    return {
                        type: 'not_onboarded',
                        userId: profile.userId,
                        state: 'kyc_started_verification_in_progress',
                        cardSafe,
                    }
                case 'resubmissionRequested':
                    return {
                        type: 'not_onboarded',
                        userId: profile.userId,
                        state: 'kyc_started_resubmission_requested',
                        cardSafe,
                    }
                case 'rejected':
                    return {
                        type: 'not_onboarded',
                        userId: profile.userId,
                        state: 'kyc_failed',
                        cardSafe,
                    }
                /* istanbul ignore next */
                default:
                    return notReachable(profile)
            }
        case 'kyc_approved':
            return {
                type: 'not_onboarded',
                userId: profile.userId,
                state: 'kyc_approved',
                cardSafe,
                accountConfiguration: calculateAccountConfiguration({
                    hasAnsweredSourceOfFunds: profile.hasAnsweredSourceOfFunds,
                    hasVerifiedPhoneNumber: profile.hasVerifiedPhoneNumber,
                }),
                residentialAddress: profile.residentialAddress,
            }

        case 'card_order_created':
            return {
                type: 'not_onboarded',
                userId: profile.userId,
                state: 'card_order_pending_payment',
                cardSafe,
                accountConfiguration: calculateAccountConfiguration({
                    hasAnsweredSourceOfFunds: profile.hasAnsweredSourceOfFunds,
                    hasVerifiedPhoneNumber: profile.hasVerifiedPhoneNumber,
                }),
                cardOrderId: profile.cardOrder.id,
                shippingAddress: profile.cardOrder.shippingAddress,
                residentialAddress: profile.residentialAddress,
            }
        case 'card_order_ready':
            return {
                type: 'not_onboarded',
                userId: profile.userId,
                state: 'card_order_ready',
                cardSafe,
                accountConfiguration: calculateAccountConfiguration({
                    hasAnsweredSourceOfFunds: profile.hasAnsweredSourceOfFunds,
                    hasVerifiedPhoneNumber: profile.hasVerifiedPhoneNumber,
                }),
                cardOrderId: profile.cardOrder.id,
                shippingAddress: profile.cardOrder.shippingAddress,
                residentialAddress: profile.residentialAddress,
            }

        case 'card_order_card_created': {
            return {
                type: 'not_onboarded',
                userId: profile.userId,
                state: 'card_created_from_order',
                cardSafe,
                country: profile.country,
                notActivatedPhysicalCards: profile.notActivatedPhysicalCards,
                accountConfiguration: profile.accountConfiguration,
            }
        }

        case 'card_activated': {
            const card = (() => {
                if (!selectedCardId) {
                    return profile.activatedCards[
                        profile.activatedCards.length - 1
                    ]
                }

                const card = profile.activatedCards.find(
                    ({ id }) => selectedCardId === id
                )

                if (!card) {
                    captureError(
                        new ImperativeError('Selected card not found', {
                            cardId: selectedCardId,
                        })
                    )
                    return profile.activatedCards[
                        profile.activatedCards.length - 1
                    ]
                }

                return card
            })()

            const [balance, cardState, isCreatedViaZeal] = await Promise.all([
                fetchBalance({
                    gnosisPayLoginInfo,
                    cardCryptoCurrency: profile.safeAccount.cryptoCurrency,
                    cardAddress: profile.safeAccount.address,
                    networkRPCMap,
                    networkMap,
                    defaultCurrencyConfig,
                    signal,
                }),
                fetchActivatedCardState({
                    gnosisPayLoginInfo,
                    cardId: card.id,
                }),
                fetchIsCreatedViaZeal({
                    id: profile.userId,
                    signal,
                }),
            ])

            const acceptedCashbackTerms = terms.some((term) => {
                switch (term.type) {
                    case 'cashback_terms':
                        return term.accepted
                    case 'general_terms':
                    case 'monavate_terms':
                        return false
                    /* istanbul ignore next */
                    default:
                        return notReachable(term.type)
                }
            })

            return {
                type: 'onboarded',
                residentialAddress: profile.residentialAddress,
                selectedCard: {
                    id: card.id,
                    lastFourDigits: card.lastFourDigits,
                    type: card.virtual ? 'virtual' : 'physical',
                    state: cardState,
                },
                cardSafe: {
                    type: 'fully_configured',
                    address: profile.safeAccount.address,
                    cryptoCurrency: profile.safeAccount.cryptoCurrency,
                    fiatCurrency: profile.safeAccount.fiatCurrency,
                },
                balance,
                userId: profile.userId,
                isCreatedViaZeal,
                activatedCards: profile.activatedCards.map((card) => ({
                    ...card,
                    type: card.virtual ? 'virtual' : 'physical',
                })),
                notActivatedPhysicalCards: profile.notActivatedPhysicalCards,
                acceptedCashbackTerms,
                fullName: profile.fullName,
                kycStatus: mapOnboardedKycStatus(profile.kycStatus),
            }
        }
        /* istanbul ignore next */
        default:
            return notReachable(profile)
    }
}

export const fetchGnosisPayAccountState2WithSilentLogin = async ({
    signal,
    networkMap,
    defaultCurrencyConfig,
    networkRPCMap,
    keyStore,
    sessionPassword,
    readonlySignerAddress,
    selectedCardId,
}: {
    selectedCardId: string | null
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    keyStore: CardSlientSignKeyStore
    sessionPassword: string
    readonlySignerAddress: Web3.address.Address
    signal?: AbortSignal
}): Promise<GnosisPayAccountState2> => {
    const gnosisPayLoginInfo = await loginWithCache({
        keyStore,
        sessionPassword,
        readonlySignerAddress,
        signal,
    })

    return await fetchGnosisPayAccountState({
        gnosisPayLoginInfo,
        signal,
        networkRPCMap,
        networkMap,
        defaultCurrencyConfig,
        selectedCardId,
    })
}

export const fetchGnosisPayOnboardedState = async ({
    gnosisPayAccountOnboardedState: _,
    networkRPCMap,
    keyStore,
    sessionPassword,
    readonlySignerAddress,
    selectedCardId,
    networkMap,
    defaultCurrencyConfig,
    signal,
}: {
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    readonlySignerAddress: Web3.address.Address
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    selectedCardId: string | null
    signal?: AbortSignal
}): Promise<GnosisPayAccountOnboardedState> => {
    const state = await fetchGnosisPayAccountState2WithSilentLogin({
        signal,
        networkMap,
        defaultCurrencyConfig,
        selectedCardId,
        readonlySignerAddress,
        sessionPassword,
        networkRPCMap,
        keyStore,
    })

    switch (state.type) {
        case 'onboarded':
            return state
        case 'not_onboarded':
            throw new ImperativeError(
                'Got not_onboarded state while refreshing onboarded'
            )

        default:
            return notReachable(state)
    }
}
