import memoize from 'lodash/memoize'

import { notReachable } from '@zeal/toolkit'
import { Result } from '@zeal/toolkit/Result'

type Path =
    | '@zeal/assets/data/currencies2.json'
    | '@zeal/assets/data/matrix2.json'
    | '@zeal/assets/data/coingeck-verified-currency-ids.json'
    | '@zeal/assets/data/bg-BG.json'
    | '@zeal/assets/data/ca-ES.json'
    | '@zeal/assets/data/cs-CZ.json'
    | '@zeal/assets/data/da-DK.json'
    | '@zeal/assets/data/de-DE.json'
    | '@zeal/assets/data/el-GR.json'
    | '@zeal/assets/data/fr-FR.json'
    | '@zeal/assets/data/fr-BE.json'
    | '@zeal/assets/data/es-ES.json'
    | '@zeal/assets/data/et-EE.json'
    | '@zeal/assets/data/fi-FI.json'
    | '@zeal/assets/data/af-ZA.json'
    | '@zeal/assets/data/hr-HR.json'
    | '@zeal/assets/data/hu-HU.json'
    | '@zeal/assets/data/is-IS.json'
    | '@zeal/assets/data/uk-UA.json'
    | '@zeal/assets/data/sv-SE.json'
    | '@zeal/assets/data/pt-BR.json'
    | '@zeal/assets/data/pt-PT.json'
    | '@zeal/assets/data/it-IT.json'
    | '@zeal/assets/data/lb-LU.json'
    | '@zeal/assets/data/lt-LT.json'
    | '@zeal/assets/data/lv-LV.json'
    | '@zeal/assets/data/mt-MT.json'
    | '@zeal/assets/data/nb-NO.json'
    | '@zeal/assets/data/nl-BE.json'
    | '@zeal/assets/data/nl-NL.json'
    | '@zeal/assets/data/pl-PL.json'
    | '@zeal/assets/data/ro-RO.json'
    | '@zeal/assets/data/sk-SK.json'
    | '@zeal/assets/data/sl-SI.json'
    | '@zeal/assets/data/sq-AL.json'
    | '@zeal/assets/data/sr-RS.json'
    | '@zeal/assets/data/tr-TR.json'
    | '@zeal/assets/data/ga-IE.json'

const getData = async (path: Path): Promise<unknown> => {
    switch (path) {
        case '@zeal/assets/data/currencies2.json':
            return (await import('@zeal/assets/data/currencies2.json' as any))
                .default
        case '@zeal/assets/data/matrix2.json':
            return (await import('@zeal/assets/data/matrix2.json' as any))
                .default
        case '@zeal/assets/data/coingeck-verified-currency-ids.json':
            return (
                await import(
                    '@zeal/assets/data/coingeck-verified-currency-ids.json' as any
                )
            ).default
        case '@zeal/assets/data/bg-BG.json':
            return (await import('@zeal/assets/data/bg-BG.json' as any)).default
        case '@zeal/assets/data/ca-ES.json':
            return (await import('@zeal/assets/data/ca-ES.json' as any)).default
        case '@zeal/assets/data/cs-CZ.json':
            return (await import('@zeal/assets/data/cs-CZ.json' as any)).default
        case '@zeal/assets/data/da-DK.json':
            return (await import('@zeal/assets/data/da-DK.json' as any)).default
        case '@zeal/assets/data/de-DE.json':
            return (await import('@zeal/assets/data/de-DE.json' as any)).default
        case '@zeal/assets/data/el-GR.json':
            return (await import('@zeal/assets/data/el-GR.json' as any)).default
        case '@zeal/assets/data/fr-FR.json':
            return (await import('@zeal/assets/data/fr-FR.json' as any)).default
        case '@zeal/assets/data/fr-BE.json':
            return (await import('@zeal/assets/data/fr-BE.json' as any)).default
        case '@zeal/assets/data/es-ES.json':
            return (await import('@zeal/assets/data/es-ES.json' as any)).default
        case '@zeal/assets/data/et-EE.json':
            return (await import('@zeal/assets/data/et-EE.json' as any)).default
        case '@zeal/assets/data/fi-FI.json':
            return (await import('@zeal/assets/data/fi-FI.json' as any)).default
        case '@zeal/assets/data/af-ZA.json':
            return (await import('@zeal/assets/data/af-ZA.json' as any)).default
        case '@zeal/assets/data/hr-HR.json':
            return (await import('@zeal/assets/data/hr-HR.json' as any)).default
        case '@zeal/assets/data/hu-HU.json':
            return (await import('@zeal/assets/data/hu-HU.json' as any)).default
        case '@zeal/assets/data/is-IS.json':
            return (await import('@zeal/assets/data/is-IS.json' as any)).default
        case '@zeal/assets/data/uk-UA.json':
            return (await import('@zeal/assets/data/uk-UA.json' as any)).default
        case '@zeal/assets/data/sv-SE.json':
            return (await import('@zeal/assets/data/sv-SE.json' as any)).default
        case '@zeal/assets/data/pt-BR.json':
            return (await import('@zeal/assets/data/pt-BR.json' as any)).default
        case '@zeal/assets/data/pt-PT.json':
            return (await import('@zeal/assets/data/pt-PT.json' as any)).default
        case '@zeal/assets/data/it-IT.json':
            return (await import('@zeal/assets/data/it-IT.json' as any)).default
        case '@zeal/assets/data/lb-LU.json':
            return (await import('@zeal/assets/data/lb-LU.json' as any)).default
        case '@zeal/assets/data/lt-LT.json':
            return (await import('@zeal/assets/data/lt-LT.json' as any)).default
        case '@zeal/assets/data/lv-LV.json':
            return (await import('@zeal/assets/data/lv-LV.json' as any)).default
        case '@zeal/assets/data/mt-MT.json':
            return (await import('@zeal/assets/data/mt-MT.json' as any)).default
        case '@zeal/assets/data/nb-NO.json':
            return (await import('@zeal/assets/data/nb-NO.json' as any)).default
        case '@zeal/assets/data/nl-BE.json':
            return (await import('@zeal/assets/data/nl-BE.json' as any)).default
        case '@zeal/assets/data/nl-NL.json':
            return (await import('@zeal/assets/data/nl-NL.json' as any)).default
        case '@zeal/assets/data/pl-PL.json':
            return (await import('@zeal/assets/data/pl-PL.json' as any)).default
        case '@zeal/assets/data/ro-RO.json':
            return (await import('@zeal/assets/data/ro-RO.json' as any)).default
        case '@zeal/assets/data/sk-SK.json':
            return (await import('@zeal/assets/data/sk-SK.json' as any)).default
        case '@zeal/assets/data/sl-SI.json':
            return (await import('@zeal/assets/data/sl-SI.json' as any)).default
        case '@zeal/assets/data/sq-AL.json':
            return (await import('@zeal/assets/data/sq-AL.json' as any)).default
        case '@zeal/assets/data/sr-RS.json':
            return (await import('@zeal/assets/data/sr-RS.json' as any)).default
        case '@zeal/assets/data/tr-TR.json':
            return (await import('@zeal/assets/data/tr-TR.json' as any)).default
        case '@zeal/assets/data/ga-IE.json':
            return (await import('@zeal/assets/data/ga-IE.json' as any)).default
        default:
            return notReachable(path)
    }
}

export const fetchStaticData = memoize(
    async <T>(
        path: Path,
        parser: (input: unknown) => Result<unknown, T>
    ): Promise<T> => {
        const data = parser(await getData(path)).getSuccessResultOrThrow(
            `failed to parse static asset ${path}`
        )
        return data
    }
)
