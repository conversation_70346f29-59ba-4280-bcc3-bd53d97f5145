import Router from 'koa-router'

import { noop, notReachable } from '@zeal/toolkit'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { hmacSHA1 } from '@zeal/toolkit/Crypto'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import {
    nonEmptyArray,
    number,
    object,
    shape,
    string,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { GnosisPayLoginInfo } from '@zeal/domains/Card'
import {
    createGnosisPayAccount,
    parseGnosisPaySignupRequest,
} from '@zeal/domains/Card/api/createGnosisPayAccount'
import { fetchGnosisPayAccountState } from '@zeal/domains/Card/api/fetchGnosisPayAccountState'
import { fetchTransactions } from '@zeal/domains/Card/api/fetchTransactions'
import { bRewardPayToTaker } from '@zeal/domains/Card/domains/Reward/api/bRewardPayToTaker'
import { fetchAreBRewardsClaimed } from '@zeal/domains/Card/domains/Reward/api/fetchAreRewardsClaimed'
import {
    BREWARD_EXPITY_AFTER_ACTIVATION_TIMESTEMP_MS,
    DEFAULT_AREWARD_AMOUNT_IN_FIAT,
    REWARDS_CONTRACT_ADDRESS,
} from '@zeal/domains/Card/domains/Reward/constants'
import { calculateBRewardState } from '@zeal/domains/Card/domains/Reward/helpers/calculateBRewardState'
import { parseBRewardClaimParams } from '@zeal/domains/Card/domains/Reward/helpers/parseBRewardParams'
import { parseCampaign } from '@zeal/domains/Card/domains/Reward/helpers/parseCampaign'
import { getActivationTimeOfFirstCard } from '@zeal/domains/Card/helpers/getActivationTimeOfFirstCard'
import { DefaultCurrency } from '@zeal/domains/Currency'
import {
    FIAT_CURRENCIES,
    INITIAL_DEFAULT_CURRENCY,
} from '@zeal/domains/Currency/constants'
import { DeployedTaker, Earn, NotDeployedTaker } from '@zeal/domains/Earn'
import { fetchEarn } from '@zeal/domains/Earn/api/fetchEarn'
import {
    EARN_NETWORK,
    EARN_PRIMARY_INVESTMENT_ASSETS_MAP,
} from '@zeal/domains/Earn/constants'
import { createCoordinatorDeployTransaction } from '@zeal/domains/Earn/helpers/createCoordinatorDeployTransaction'
import { getHolderPredictedAddress } from '@zeal/domains/Earn/helpers/getHolderPredictedAddress'
import { getTakerPredictedAddress } from '@zeal/domains/Earn/helpers/getTakerPredictedAddress'
import { ZEAL_ERROR_CODES } from '@zeal/domains/Error'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseHttpError } from '@zeal/domains/Error/parsers/parseHttpError'
import { mulByNumber } from '@zeal/domains/Money/helpers/mul'
import { serialize } from '@zeal/domains/Money/helpers/serialize'
import { getPredefinedNetworkMap } from '@zeal/domains/Network/helpers/getPredefinedNetworkMap'
import { requestCurrentNonce } from '@zeal/domains/RPCRequest/api/fetchCurrentNonce'
import {
    fetchRPCBatch2,
    fetchRPCResponse,
} from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { signEthSendTransactionWithPrivateKey } from '@zeal/domains/RPCRequest/helpers/signEthSendTransaction'
import { SubmitedTransactionQueued } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction'
import { fetchGasEstimate } from '@zeal/domains/Transactions/api/fetchGasEstimate'
import { fetchFeeForecast } from '@zeal/domains/Transactions/domains/FeeForecast/api/fetchFeeForecast'
import { getSuggestedGasLimit } from '@zeal/domains/Transactions/helpers/getSuggestedGasLimit'

import { fetchOrGenerateReferralCode } from 'src/domains/Card/domains/Reward/api/fetchOrGenerateReferralCode'
import { prepareARewardClaimTransactions } from 'src/domains/Card/domains/Reward/api/prepareARewardClaimTransactions'
import * as Notifications from 'src/domains/Notifications'
import { parseSubscribeToNotificationsBody } from 'src/domains/Notifications/helpers/parseSubscribeToNotificationsBody'
import * as sqlite from 'src/toolkit/sqlite'

import { SQLITE_DB_PATH } from './constants'
import {
    createRefereeForCode,
    fetchAUnclaimedBUserIds,
    fetchCampaignByUserId,
    markActivationRewardAsClaimed,
    sendUserAClaimablePushNotification,
} from './domains/Card/domains/Reward'
import { calculateBRewardAmount } from './domains/Card/domains/Reward/api/calculateBRewardAmount'
import { isCampaignActive } from './domains/Card/domains/Reward/api/isCampaignActive'
import { parseIntercomWebhookEvent } from './domains/Intercom'
import {
    fetchAuthCode,
    parseMoneriumAuthInfo,
} from './domains/Monerium/api/fetchAuthCode'
import { fetchAuthToken } from './domains/Monerium/api/fetchAuthToken'
import { parsePushNotificationsForIntercomWebhookEvent } from './domains/Notifications/helpers/parsePushNotification'
import { renderIntercomReplyPushNotification } from './domains/Notifications/helpers/renderPushNotifications'
import { getUnblockWebhookEvents } from './domains/Unblock/features/getUnblockWebhookEvents'
import { storeUnblockWebhookEvent } from './domains/Unblock/features/storeUnblockWebhookEvent'
import { log } from './toolkit/log'
import { SECRETS } from './toolkit/secrets'
// TODO :: @max-tern export route construction function
export const router = new Router()

// TODO :: @max-tern how to connect paths here to backend2 Paths

router.get('/unblock/webhook/:user_id_hash', async (ctx) => {
    await using connection = await sqlite.connection(SQLITE_DB_PATH)
    const { db } = connection

    const userIdHash = Hexadecimal.parse(
        ctx.params.user_id_hash
    ).getSuccessResultOrThrow('Failed to parse userIdHash from query')

    const events = await getUnblockWebhookEvents({ db, userIdHash })

    ctx.status = 200
    ctx.body = events
})

router.post('/unblock/webhook', async (ctx) => {
    const rawAuth =
        ctx.headers['authorization'] || ctx.headers['x-authorization']

    const auth =
        typeof rawAuth === 'string' ? rawAuth.replace('API-Key ', '') : ''

    if (auth === SECRETS.UNBLOCK_WEBHOOK_SECRET) {
        await using connection = await sqlite.connection(SQLITE_DB_PATH)
        const { db } = connection

        await storeUnblockWebhookEvent({
            db,
            eventData: ctx.request.body,
        })

        ctx.status = 204
    } else {
        ctx.status = 401
    }
})

router.post('/intercom/webhook', async (ctx) => {
    const incomingSignature = string(ctx.headers['x-hub-signature'])
        .map((header) => header.split('sha1=')[1])
        .andThen(Hexadecimal.parseFromString)
        .getSuccessResult()

    const expectedSignature = hmacSHA1({
        key: SECRETS.INTERCOM_WEBHOOK_APP_CLIENT_SECRET,
        message: ctx.request.rawBody,
    })

    if (incomingSignature === expectedSignature) {
        const event = parseIntercomWebhookEvent(
            ctx.request.body
        ).getSuccessResultOrThrow('Failed to parse intercom webhook event')

        await using connection = await sqlite.connection(SQLITE_DB_PATH)
        const { db } = connection

        const pushNotifications =
            await parsePushNotificationsForIntercomWebhookEvent({
                db,
                event,
            })

        const msgs = renderIntercomReplyPushNotification({
            pushNotifications,
        })

        const { errors } = await Notifications.send({
            db,
            messages: msgs,
            dryRun: false,
        })

        log('notifications sent for intercom webhook event', {
            messageCount: msgs.length,
            errorCount: errors.length,
        })
    } else {
        captureError(
            new ImperativeError('Invalid signature for intercom webhook')
        )
    }

    ctx.status = 204
})

router.post('/notifications/subscribe', async (ctx) => {
    try {
        await using connection = await sqlite.connection(SQLITE_DB_PATH)
        const { db } = connection

        const subscribeToNotificationsBody = parseSubscribeToNotificationsBody(
            ctx.request.body
        )
            .mapError((e) => {
                // TODO :: @max-tern user proper error
                return e
            })
            .getSuccessResultOrThrow('invalid request body')

        await Notifications.features.subscribeToNotifications({
            db,
            subscribeToNotificationsBody,
        })

        ctx.status = 200
        ctx.body = { ok: true }
    } catch (e: any) {
        // console.log('e', e?.message, JSON.stringify(e, null, 4))
        ctx.status = 500
    }
})

router.post('/monerium/auth', async (ctx) => {
    const result = parseMoneriumAuthInfo(ctx.request.body)
    switch (result.type) {
        case 'Failure':
            ctx.body = result.reason
            ctx.status = 400
            return
        case 'Success': {
            const authCode = await fetchAuthCode({
                authInfo: result.data,
            })
            const authToken = await fetchAuthToken({ authCode })

            ctx.body = authToken
            ctx.status = 200
            return
        }
        /* istanbul ignore next */
        default:
            return notReachable(result)
    }
})

// TODO @resetko-zeal try to reuse prepareEarnDeploymentTransaction from A rewards preparation https://linear.app/zeal/issue/ZEAL-4076
const deployEarnTransction = async ({
    owner,
    taker,
    privateKey,
    nonce,
}: {
    taker: NotDeployedTaker
    owner: Web3.address.Address
    privateKey: Web3.privateKey.PrivateKey
    nonce: number
}): Promise<{ taker: DeployedTaker; txHash: Hexadecimal.Hexadecimal }> => {
    const sendTransactionRequest = createCoordinatorDeployTransaction({
        owner,
        fromAddress: privateKey.address,
        takerType: taker.type,
    })

    const network = EARN_NETWORK
    const networkMap = getPredefinedNetworkMap()

    const networkRPCMap = {}

    const fee = await fetchGasEstimate({
        network,
        networkRPCMap,
        rpcRequest: sendTransactionRequest,
    }).then(async (gasEstimate) => {
        const gasLimit = getSuggestedGasLimit(gasEstimate)
        const fee = await fetchFeeForecast({
            gasEstimate,
            defaultCurrencyConfig: {
                defaultCurrency: INITIAL_DEFAULT_CURRENCY as DefaultCurrency,
            },
            networkMap,
            network,
            networkRPCMap,
            gasLimit,
            address: privateKey.address,
            selectedPreset: { type: 'Fast' },
            sendTransactionRequest: sendTransactionRequest,
            signal: new AbortController().signal,
        })
        return { fee, gasLimit }
    })

    const rawTransaction = await signEthSendTransactionWithPrivateKey({
        fee: fee.fee.fast,
        gas: fee.gasLimit,
        sendTransactionRequest,
        network,
        privateKey,
        nonce,
    })

    const txHash = (await fetchRPCResponse({
        network,
        networkRPCMap,
        request: rawTransaction,
    })) as Hexadecimal.Hexadecimal

    return { taker: { ...taker, state: 'deployed' }, txHash }
}

const earnTakerPreparation = async ({
    earn,
    earnOwnerAddress,
    privateKey,
    nonce,
}: {
    earn: Earn
    privateKey: Web3.privateKey.PrivateKey
    nonce: number
    earnOwnerAddress: Web3.address.Address
}): Promise<
    | { type: 'existing_taker'; taker: DeployedTaker; nextActiveNonce: number }
    | {
          type: 'new_tacker'
          taker: DeployedTaker
          txHash: Hexadecimal.Hexadecimal
          nextActiveNonce: number
      }
> => {
    switch (earn.type) {
        case 'not_configured': {
            const holder = getHolderPredictedAddress({
                earnOwnerAddress,
            })
            const takerAddress = getTakerPredictedAddress({
                holder,
                takerType: 'eur',
            })
            const taker: NotDeployedTaker = {
                type: 'eur',
                state: 'not_deployed',
                cryptoCurrency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eur'],
                address: takerAddress,
            }
            const deployedTaker = await deployEarnTransction({
                taker,
                owner: earnOwnerAddress,
                privateKey,
                nonce,
            })
            return {
                type: 'new_tacker',
                taker: deployedTaker.taker,
                txHash: deployedTaker.txHash,
                nextActiveNonce: nonce + 1,
            }
        }

        case 'configured': {
            const deployedTakers = earn.takers.filter(
                (taker): taker is DeployedTaker => {
                    switch (taker.state) {
                        case 'not_deployed':
                            return false
                        case 'deployed':
                            return true
                        default:
                            return notReachable(taker)
                    }
                }
            )

            if (!deployedTakers.length) {
                throw new ImperativeError(
                    `Cannot find deployed taker for configured earn`
                )
            }

            const eurTaker = deployedTakers.find((taker) => {
                switch (taker.type) {
                    case 'eth':
                    case 'usd':
                    case 'chf':
                        return false
                    case 'eur':
                        return true

                    default:
                        return notReachable(taker.type)
                }
            })
            return {
                type: 'existing_taker',
                taker: eurTaker || deployedTakers[0],
                nextActiveNonce: nonce,
            }
        }

        default:
            return notReachable(earn)
    }
}

router.post('/brewards/claim', async (ctx) => {
    const result = parseBRewardClaimParams(ctx.request.body)
    const networkMap = getPredefinedNetworkMap()
    switch (result.type) {
        case 'Failure':
            ctx.body = { code: 4000, reason: result.reason }
            ctx.status = 400
            return
        case 'Success': {
            const gnosisPayLoginInfo: GnosisPayLoginInfo = {
                type: 'gnosis_pay_login_info',
                token: result.data.gnosisToken,
                expiresAtMs: Number.MAX_SAFE_INTEGER,
            }

            const privateKey = Web3.privateKey
                .fromString(SECRETS.REWARDS_SENDER_PRIVATE_KEY_V2)
                .getSuccessResultOrThrow(
                    '/brewards/claim private key not Hexadecimal'
                )

            const [gnosisAccountState, cardTransactions, earn, [nonce]] =
                await Promise.all([
                    fetchGnosisPayAccountState({
                        gnosisPayLoginInfo,
                        defaultCurrencyConfig: {
                            defaultCurrency:
                                FIAT_CURRENCIES.USD as DefaultCurrency,
                        },
                        networkMap,
                        networkRPCMap: {},
                        selectedCardId: null,
                    }),
                    fetchTransactions({
                        gnosisPayLoginInfo,
                        beforeTimestampMs:
                            result.data.firstCardActivatedTimestampMS +
                            BREWARD_EXPITY_AFTER_ACTIVATION_TIMESTEMP_MS,
                        afterTimestampMs:
                            result.data.firstCardActivatedTimestampMS,
                    }),
                    fetchEarn({
                        defaultCurrencyConfig: {
                            defaultCurrency:
                                FIAT_CURRENCIES.USD as DefaultCurrency,
                        },
                        earnOwnerAddress: result.data.ownerAddress,
                        networkMap,
                        networkRPCMap: {},
                    }),
                    fetchRPCBatch2(
                        [requestCurrentNonce({ address: privateKey.address })],
                        {
                            network: EARN_NETWORK,
                            networkRPCMap: {},
                        }
                    ),
                ])

            switch (gnosisAccountState.type) {
                case 'not_onboarded':
                    ctx.body = {
                        code: ZEAL_ERROR_CODES.B_REWARD_CANNOT_BE_CLAIMED_NOT_ONBOARDED_USER,
                        msg: "BReward isn't ready to be claimed because your Gnosis account hasn't been onboarded yet",
                        gnosisAccountState: gnosisAccountState.state,
                    }
                    ctx.status = 400
                    return
                case 'onboarded':
                    {
                        const [isBRewardClaimed] =
                            await fetchAreBRewardsClaimed({
                                userIds: [gnosisAccountState.userId],
                                networkRPCMap: {},
                            })
                        const breward = calculateBRewardState({
                            gnosisAccountState,
                            cardTransactions,
                            isBRewardClaimed,
                        })
                        if (
                            gnosisAccountState.userId ===
                            'cmevbqnhd0k9c3jkqf66ipmnh'
                        ) {
                            log('user try to claim b reward', {
                                cardTransactions,
                                userId: gnosisAccountState.userId,
                                isBRewardClaimed,
                                gnosisAccountStateType: gnosisAccountState.type,
                                breward,
                                firstCardActivatedTimestampMS:
                                    result.data.firstCardActivatedTimestampMS,
                                isZeal: gnosisAccountState.isCreatedViaZeal,
                                activationTimeOfFirstCard:
                                    getActivationTimeOfFirstCard({
                                        gnosisPayAccountState:
                                            gnosisAccountState,
                                    }),
                            })
                        }

                        await using connection =
                            await sqlite.connection(SQLITE_DB_PATH)
                        const { db } = connection

                        switch (breward.type) {
                            case 'not_eligible':
                            case 'in_progress':
                            case 'expired':
                                ctx.body = {
                                    code: ZEAL_ERROR_CODES.B_REWARD_CANNOT_BE_CLAIMED,
                                    msg: 'BReward cannot be claimed now',
                                    breward,
                                }
                                ctx.status = 400
                                return
                            case 'claimed':
                                await markActivationRewardAsClaimed(
                                    db,
                                    gnosisAccountState.userId
                                )
                                ctx.body = {
                                    code: ZEAL_ERROR_CODES.B_REWARDS_ALREADY_CLAIMED,
                                    msg: 'BReward is already claimed',
                                    breward,
                                }
                                ctx.status = 400
                                return

                            case 'ready_to_claim': {
                                if (
                                    breward.firstCardActivatedTimestampMS !==
                                    result.data.firstCardActivatedTimestampMS
                                ) {
                                    ctx.body = {
                                        code: 4003,
                                        msg: 'firstCardActivatedTimestampMS is invalid',
                                        breward:
                                            breward.firstCardActivatedTimestampMS,
                                        result: result.data
                                            .firstCardActivatedTimestampMS,
                                    }
                                    ctx.status = 400
                                    return
                                }

                                const [prepearedTaker, installationCampaign] =
                                    await Promise.all([
                                        earnTakerPreparation({
                                            earn,
                                            earnOwnerAddress:
                                                result.data.ownerAddress,
                                            privateKey,
                                            nonce,
                                        }),
                                        fetchCampaignByUserId({
                                            db,
                                            userId: gnosisAccountState.userId,
                                        }),
                                    ])

                                if (
                                    !installationCampaign &&
                                    result.data.installationCampaign
                                ) {
                                    captureError(
                                        new ImperativeError(
                                            'Installation campaign is not set in db but is set in claim request',
                                            {
                                                userId: gnosisAccountState.userId,
                                                installationCampaign:
                                                    result.data
                                                        .installationCampaign,
                                            }
                                        )
                                    )

                                    await sqlite.run(
                                        db,
                                        `update gnosis_pay_user set campaignKey = ? where id = ?;`,
                                        [
                                            sqlite.text(
                                                result.data.installationCampaign
                                            ),
                                            sqlite.text(
                                                gnosisAccountState.userId
                                            ),
                                        ]
                                    )
                                }

                                const campaign =
                                    parseCampaign(
                                        installationCampaign ||
                                            result.data.installationCampaign
                                    ).getSuccessResult() || null

                                const {
                                    txHash,
                                    amountInTakerCurrency,
                                    senderAddress,
                                } = await bRewardPayToTaker({
                                    signal: new AbortController().signal,
                                    taker: prepearedTaker.taker,
                                    privateKey,
                                    nonce: prepearedTaker.nextActiveNonce,
                                    contractAddress: REWARDS_CONTRACT_ADDRESS,
                                    networkMap,
                                    networkRPCMap: {},
                                    gnosisAccountState,
                                    amountInRewardCurrency:
                                        await calculateBRewardAmount({
                                            db,
                                            campaign,
                                            referralCode:
                                                result.data.referralCode,
                                        }),
                                })

                                log('Referral userB claiming', {
                                    userId: gnosisAccountState.userId,
                                    referralCode: result.data.referralCode,
                                    installationCampaign:
                                        result.data.installationCampaign,
                                })

                                // TODO @resetko-zeal refactor campaign vs referral logic https://linear.app/zeal/issue/ZEAL-4074
                                if (result.data.referralCode && !campaign) {
                                    await createRefereeForCode({
                                        userBId: gnosisAccountState.userId,
                                        referralCode: result.data.referralCode,
                                        db,
                                    })

                                    await sendUserAClaimablePushNotification({
                                        db,
                                        referralCode: result.data.referralCode,
                                    })
                                }

                                ctx.body = {
                                    txHash,
                                    nonce: prepearedTaker.nextActiveNonce,
                                    senderAddress,
                                    prepearedTaker,
                                    amountInTakerCurrency: {
                                        amount: toFixedWithFraction(
                                            amountInTakerCurrency.amount,
                                            amountInTakerCurrency.currency
                                                .fraction
                                        ),
                                        currency:
                                            amountInTakerCurrency.currency,
                                    },
                                }
                                ctx.status = 200

                                break
                            }

                            default:
                                return notReachable(breward)
                        }
                    }
                    break

                default:
                    notReachable(gnosisAccountState)
                    break
            }

            return
        }
        /* istanbul ignore next */
        default:
            return notReachable(result)
    }
})

router.post('/metrics', async (ctx) => {
    // TODO ZEAL-2723 @resetko-zeal implement write of event into s3
    console.log('event', ctx.request.body)
    ctx.status = 200
})

router.post('/gnosispay/signup', async (ctx) => {
    const params = parseGnosisPaySignupRequest(
        ctx.request.body
    ).getSuccessResultOrThrow('cannot parse params')
    try {
        const body = await createGnosisPayAccount(params)

        await using connection = await sqlite.connection(SQLITE_DB_PATH)
        const { db } = connection

        await sqlite.run(
            db,
            `
        insert or replace
        into gnosis_pay_user (id, campaignKey)
        values (?, ?);
`,
            [
                sqlite.text(body.id),
                sqlite.nullableText(params.installationCampaign),
            ]
        )
        ctx.status = 200
        ctx.body = body
    } catch (e) {
        const error = parseHttpError(e).getSuccessResultOrThrow('unknown error')
        Object.entries(error.responseHeaders).forEach(([key, value]) => {
            if (value) {
                ctx.set(key, value)
            }
        })
        ctx.body = error.responseBody
        ctx.status = error.status || 500
    }
})

router.get('/gnosispay/is-zeal/:id', async (ctx) => {
    const id = string(ctx.params.id).getSuccessResultOrThrow(
        'expecting :id as path param'
    )
    await using connection = await sqlite.connection(SQLITE_DB_PATH)
    const { db } = connection

    const stm = `
      SELECT EXISTS(
        SELECT 1 FROM gnosis_pay_user WHERE id = ?
      ) as 'isZeal';
    `
    const [{ isZeal }] = await sqlite.query(db, stm, [sqlite.text(id)], (i) =>
        object(i).andThen((o) => shape({ isZeal: number(o.isZeal) }))
    )

    ctx.body = !!isZeal

    ctx.status = 200
})

router.get('/campaigns/:id/active', async (ctx) => {
    const campaignId = string(ctx.params.id)
        .andThen(parseCampaign)
        .getSuccessResultOrThrow('expecting :id as path param')

    await using connection = await sqlite.connection(SQLITE_DB_PATH)
    const { db } = connection

    const campaign = parseCampaign(campaignId).getSuccessResult() || null
    const isActive = campaign ? await isCampaignActive({ db, campaign }) : false

    ctx.body = { isActive }
    ctx.status = 200
})

router.get('/referrals/:address', async (ctx) => {
    const userAAddress = Web3.address
        .parse(ctx.params.address)
        .getSuccessResult()

    if (!userAAddress) {
        ctx.status = 400
        ctx.body = { message: 'Invalid address' }
        return
    }

    await using connection = await sqlite.connection(SQLITE_DB_PATH)
    const { db } = connection

    const unclaimedBIds = await fetchAUnclaimedBUserIds({ db, userAAddress })
    const count = unclaimedBIds.length

    const totalClaimableAmount = mulByNumber(
        DEFAULT_AREWARD_AMOUNT_IN_FIAT,
        count
    )

    ctx.body = {
        count,
        totalAmountInFiat: serialize(totalClaimableAmount),
    }
    ctx.status = 200
})

router.post('/referrals/:address/code', async (ctx) => {
    const address = Web3.address.parse(ctx.params.address).getSuccessResult()

    if (!address) {
        ctx.status = 400
        ctx.body = { message: 'Invalid address' }
        return
    }

    await using connection = await sqlite.connection(SQLITE_DB_PATH)
    const { db } = connection

    const code = await fetchOrGenerateReferralCode({ address, db })

    ctx.body = {
        code,
    }
    ctx.status = 200
})

router.post('/referrals/:address/claim', async (ctx) => {
    const userAAddress = Web3.address
        .parse(ctx.params.address)
        .getSuccessResult()

    if (!userAAddress) {
        ctx.status = 400
        ctx.body = { message: 'Invalid address' }
        return
    }

    await using connection = await sqlite.connection(SQLITE_DB_PATH)
    const { db } = connection

    const unclaimedBIds = nonEmptyArray(
        await fetchAUnclaimedBUserIds({ db, userAAddress })
    ).getSuccessResult()

    if (!unclaimedBIds) {
        ctx.body = {
            code: ZEAL_ERROR_CODES.A_REWARDS_ALREADY_CLAIMED,
            message: 'No rewards available to claim',
        }
        ctx.status = 404
        return
    }

    const preparedTransactions = await prepareARewardClaimTransactions({
        ownerAddress: userAAddress,
        userBIds: unclaimedBIds,
        signal: new AbortController().signal,
    })

    const {
        earnDeploymentTransaction,
        rewardPaymentTransaction,
        rewardPaymentTransactionNonce,
        rewardAmountInInvestmentCurrency,
        senderAddress,
    } = preparedTransactions

    if (earnDeploymentTransaction) {
        const [_, rewardPaymentTxHash] = await fetchRPCBatch2(
            [
                {
                    request: earnDeploymentTransaction,
                    parser: noop,
                },
                {
                    request: rewardPaymentTransaction,
                    parser: (result) =>
                        Hexadecimal.parse(result).getSuccessResultOrThrow(
                            'Failed to parse rewardPaymentTx hash'
                        ),
                },
            ],
            { network: EARN_NETWORK, networkRPCMap: {} }
        )

        const rewardTransaction: SubmitedTransactionQueued = {
            state: 'queued',
            queuedAt: Date.now(),
            submittedNonce: rewardPaymentTransactionNonce,
            hash: rewardPaymentTxHash,
            senderAddress,
        }

        ctx.body = {
            rewardTransaction,
            amountInTakerInvestmentCurrency: serialize(
                rewardAmountInInvestmentCurrency
            ),
        }
        ctx.status = 200
        return
    }

    const rewardPaymentResponse = await fetchRPCResponse({
        request: rewardPaymentTransaction,
        networkRPCMap: {},
        network: EARN_NETWORK,
    })

    const rewardPaymentTxHash = Hexadecimal.parse(
        rewardPaymentResponse
    ).getSuccessResultOrThrow('Failed to parse rewardPaymentTx hash')

    const rewardTransaction: SubmitedTransactionQueued = {
        state: 'queued',
        queuedAt: Date.now(),
        submittedNonce: rewardPaymentTransactionNonce,
        hash: rewardPaymentTxHash,
        senderAddress,
    }

    ctx.body = {
        rewardTransaction,
        amountInTakerInvestmentCurrency: serialize(
            rewardAmountInInvestmentCurrency
        ),
    }
    ctx.status = 200
})

// TODO :: @max-tern logging based on request id

// TODO :: @max-tern error handling middle ware (parsing error should 400 how to distinguish) + sentry

router.get('/health-check', async (ctx) => {
    ctx.body = ``
})
