import { notReachable } from '@zeal/toolkit'

import { unsafeGetCurrencyInfo } from '@zeal/domains/Currency'
import { fetchBlockInfo } from '@zeal/domains/RPCRequest/api/fetchBlockInfo'
import { fetchBlockNumber } from '@zeal/domains/RPCRequest/api/fetchBlockNumber'
import { fetchContractStats } from '@zeal/domains/Token/api/fetchContractStats'
import { checkIfScam } from '@zeal/domains/Token/helpers/checkIfScam'

import { send, SupportedNotificationsNetworks } from 'src/domains/Notifications'
import { parsePushNotificationsForBlock } from 'src/domains/Notifications/helpers/parsePushNotification'
import { renderBlockPushNotification } from 'src/domains/Notifications/helpers/renderPushNotifications'
import { log } from 'src/toolkit/log'
import { DB } from 'src/toolkit/sqlite'

export const sendPushNotificationsForBlock = async ({
    signal,
    db,
    network,
    nextBlockToProcess,
}: {
    db: DB
    network: SupportedNotificationsNetworks
    nextBlockToProcess: number | null
    signal: AbortSignal
}): Promise<number | null> => {
    if (signal.aborted) {
        return nextBlockToProcess
    }

    const blockToProcess =
        nextBlockToProcess ||
        (await fetchBlockNumber({
            networkRPCMap: {},
            network,
        }))

    const block = await fetchBlockInfo({
        network,
        blockNumber: blockToProcess,
    })

    switch (block.type) {
        case 'pending':
            return blockToProcess
        case 'included':
            const pushNotifications = await parsePushNotificationsForBlock(
                db,
                block
            )

            const tokens = pushNotifications
                .map((push) => {
                    switch (push.type) {
                        case 'address_receive_token_notification':
                            const { address } = unsafeGetCurrencyInfo(
                                push.currencyId
                            )
                            return address
                        case 'card_spend':
                        case 'card_topup':
                        case 'card_refund':
                        case 'card_cashback_reward':
                        case 'address_send_token_notification':
                        case 'monerium_bank_transfer_receive_push_notification':
                            return null
                        default:
                            return notReachable(push)
                    }
                })
                .filter((token) => !!token)

            const tokenStats = tokens.length
                ? await fetchContractStats({
                      networkHexId: network.hexChainId,
                      addresses: tokens,
                  })
                : {}
            const filteredNotifications = pushNotifications.filter((push) => {
                switch (push.type) {
                    case 'address_receive_token_notification':
                        return !checkIfScam({
                            currencyId: push.currencyId,
                            statsMap: tokenStats,
                        })
                    case 'card_spend':
                    case 'card_topup':
                    case 'card_refund':
                    case 'card_cashback_reward':
                    case 'address_send_token_notification':
                    case 'monerium_bank_transfer_receive_push_notification':
                        return true
                    default:
                        return notReachable(push)
                }
            })

            const msgs = await renderBlockPushNotification({
                pushNotifications: filteredNotifications,
            })

            const { errors } = await send({
                db,
                messages: msgs,
                dryRun: false,
            })

            log('notifications sent', {
                networkHexId: network.hexChainId,
                blockNumber: block.blockNumber,
                messageCount: msgs.length,
                errorCount: errors.length,
            })

            // restart
            return await sendPushNotificationsForBlock({
                db,
                network,
                signal,
                nextBlockToProcess: blockToProcess + 1,
            })
        /* istanbul ignore next */
        default:
            return notReachable(block)
    }
}
