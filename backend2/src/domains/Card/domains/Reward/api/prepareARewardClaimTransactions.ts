import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    DEFAULT_AREWARD_AMOUNT,
    REWARD_SENDER_ABI,
    REWARDS_CONTRACT_ADDRESS,
} from '@zeal/domains/Card/domains/Reward/constants'
import { calculateARewardNonce } from '@zeal/domains/Card/domains/Reward/helpers/getRewardNonce'
import { DefaultCurrency } from '@zeal/domains/Currency'
import { INITIAL_DEFAULT_CURRENCY } from '@zeal/domains/Currency/constants'
import {
    ConfiguredEarn,
    DeployedTaker,
    Taker,
    TakerType,
} from '@zeal/domains/Earn'
import { fetchEarn } from '@zeal/domains/Earn/api/fetchEarn'
import {
    EARN_NETWORK,
    EARN_PRIMARY_INVESTMENT_ASSETS_MAP,
} from '@zeal/domains/Earn/constants'
import { createCoordinatorDeployTransaction } from '@zeal/domains/Earn/helpers/createCoordinatorDeployTransaction'
import { getHolderPredictedAddress } from '@zeal/domains/Earn/helpers/getHolderPredictedAddress'
import { getTakerPredictedAddress } from '@zeal/domains/Earn/helpers/getTakerPredictedAddress'
import { fetchCrossRate } from '@zeal/domains/FXRate/api/fetchCrossRates'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney } from '@zeal/domains/Money'
import { mulByNumber } from '@zeal/domains/Money/helpers/mul'
import { getPredefinedNetworkMap } from '@zeal/domains/Network/helpers/getPredefinedNetworkMap'
import {
    EthSendRawTransaction,
    EthSendTransaction,
} from '@zeal/domains/RPCRequest'
import { requestCurrentNonce } from '@zeal/domains/RPCRequest/api/fetchCurrentNonce'
import { fetchRPCResponseWithRetry2 } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { signEthSendTransactionWithPrivateKey } from '@zeal/domains/RPCRequest/helpers/signEthSendTransaction'
import { fetchGasEstimate } from '@zeal/domains/Transactions/api/fetchGasEstimate'
import { fetchFeeForecast } from '@zeal/domains/Transactions/domains/FeeForecast/api/fetchFeeForecast'
import { getSuggestedGasLimit } from '@zeal/domains/Transactions/helpers/getSuggestedGasLimit'

import { SECRETS } from 'src/toolkit/secrets'

type Response = {
    rewardAmountInInvestmentCurrency: CryptoMoney
    earnDeploymentTransaction: EthSendRawTransaction | null
    rewardPaymentTransaction: EthSendRawTransaction
    rewardPaymentTransactionNonce: number
    senderAddress: Web3.address.Address
}

const network = EARN_NETWORK
const networkRPCMap = {}
const networkMap = getPredefinedNetworkMap()
const defaultCurrencyConfig = {
    defaultCurrency: INITIAL_DEFAULT_CURRENCY as DefaultCurrency,
}

const prepareTransaction = async ({
    sendTransactionRequest,
    adminNonce,
    adminPrivateKey,
    signal,
}: {
    sendTransactionRequest: EthSendTransaction
    adminNonce: number
    adminPrivateKey: Web3.privateKey.PrivateKey
    signal: AbortSignal
}): Promise<EthSendRawTransaction> => {
    const gasEstimate = await fetchGasEstimate({
        network,
        networkRPCMap,
        rpcRequest: sendTransactionRequest,
    })

    const gasLimit = getSuggestedGasLimit(gasEstimate)

    const feeForecast = await fetchFeeForecast({
        gasEstimate,
        defaultCurrencyConfig,
        networkMap,
        network,
        networkRPCMap,
        gasLimit,
        address: adminPrivateKey.address,
        selectedPreset: { type: 'Fast' },
        sendTransactionRequest,
        signal,
    })

    return await signEthSendTransactionWithPrivateKey({
        fee: feeForecast.fast,
        gas: gasLimit,
        sendTransactionRequest,
        network,
        privateKey: adminPrivateKey,
        nonce: adminNonce,
    })
}

const prepareEarnDeploymentTransaction = async ({
    adminNonce,
    adminPrivateKey,
    earnOwnerAddress,
    signal,
}: {
    earnOwnerAddress: Web3.address.Address
    adminNonce: number
    adminPrivateKey: Web3.privateKey.PrivateKey
    signal: AbortSignal
}): Promise<{
    transaction: EthSendRawTransaction
    taker: DeployedTaker
}> => {
    const takerType: TakerType = 'eur'

    const sendTransactionRequest = createCoordinatorDeployTransaction({
        owner: earnOwnerAddress,
        fromAddress: adminPrivateKey.address,
        takerType,
    })

    return {
        transaction: await prepareTransaction({
            sendTransactionRequest,
            adminPrivateKey,
            adminNonce,
            signal,
        }),
        taker: {
            state: 'deployed',
            type: takerType,
            cryptoCurrency: EARN_PRIMARY_INVESTMENT_ASSETS_MAP['eur'],
            address: getTakerPredictedAddress({
                takerType,
                holder: getHolderPredictedAddress({ earnOwnerAddress }),
            }),
        },
    }
}

const prepareRewardPaymentTransaction = async ({
    userBIds,
    taker,
    adminNonce,
    adminPrivateKey,
    signal,
}: {
    userBIds: NonEmptyArray<string>
    taker: Taker
    adminPrivateKey: Web3.privateKey.PrivateKey
    adminNonce: number
    signal: AbortSignal
}): Promise<{
    transaction: EthSendRawTransaction
    amountInTakerCurrency: CryptoMoney
}> => {
    const crossRate = await fetchCrossRate({
        base: DEFAULT_AREWARD_AMOUNT.currency,
        quote: taker.cryptoCurrency,
        networkMap,
        networkRPCMap,
        signal,
    })

    if (!crossRate) {
        throw new ImperativeError(
            `Missing cross rate for a_reward payment. ${DEFAULT_AREWARD_AMOUNT.currency.id} to ${taker.cryptoCurrency.id} `
        )
    }

    const amountInTakerCurrency = applyRate2({
        baseAmount: DEFAULT_AREWARD_AMOUNT,
        rate: crossRate,
    })

    const sendTransactionRequest: EthSendTransaction = {
        id: generateRandomNumber(),
        jsonrpc: '2.0',
        method: 'eth_sendTransaction',
        params: [
            {
                from: adminPrivateKey.address,
                to: REWARDS_CONTRACT_ADDRESS,
                data: Web3.abi.encodeFunctionData({
                    abi: REWARD_SENDER_ABI,
                    functionName: 'sendTokens',
                    args: [
                        userBIds.map((userBId) => ({
                            token: taker.cryptoCurrency.address,
                            to: taker.address,
                            amount: amountInTakerCurrency.amount,
                            nonce: calculateARewardNonce(userBId),
                        })),
                    ],
                }),
            },
        ],
    }

    return {
        transaction: await prepareTransaction({
            sendTransactionRequest,
            adminNonce,
            adminPrivateKey,
            signal,
        }),
        amountInTakerCurrency: mulByNumber(
            amountInTakerCurrency,
            userBIds.length
        ),
    }
}

const getDeployedTakerToReward = (earn: ConfiguredEarn): DeployedTaker => {
    const deployedTakers = earn.takers.filter(
        (taker): taker is DeployedTaker => {
            switch (taker.state) {
                case 'not_deployed':
                    return false
                case 'deployed':
                    return true
                default:
                    return notReachable(taker)
            }
        }
    )

    if (!deployedTakers.length) {
        throw new ImperativeError(
            `Cannot find deployed taker for configured earn when paying a_reward`
        )
    }

    const eurTaker = deployedTakers.find((taker) => {
        switch (taker.type) {
            case 'eth':
            case 'usd':
            case 'chf':
                return false
            case 'eur':
                return true

            default:
                return notReachable(taker.type)
        }
    })

    return eurTaker || deployedTakers[0]
}

export const prepareARewardClaimTransactions = async ({
    userBIds,
    ownerAddress,
    signal,
}: {
    userBIds: NonEmptyArray<string>
    ownerAddress: Web3.address.Address
    signal: AbortSignal
}): Promise<Response> => {
    const adminPrivateKey = Web3.privateKey
        .fromString(SECRETS.REWARDS_SENDER_PRIVATE_KEY_V2)
        .getSuccessResultOrThrow('Failed to parse rewards sender PK secret')

    const [earn, adminCurrentNonce] = await Promise.all([
        fetchEarn({
            networkMap,
            networkRPCMap,
            defaultCurrencyConfig,
            earnOwnerAddress: ownerAddress,
            signal,
        }),
        fetchRPCResponseWithRetry2(
            requestCurrentNonce({ address: adminPrivateKey.address }),
            {
                network,
                networkRPCMap,
                signal,
            }
        ),
    ])

    switch (earn.type) {
        case 'not_configured': {
            const earnDeploymentTransactionNonce = adminCurrentNonce
            const rewardPaymentTransactionNonce = adminCurrentNonce + 1

            const earnTxResponse = await prepareEarnDeploymentTransaction({
                earnOwnerAddress: ownerAddress,
                adminPrivateKey,
                adminNonce: earnDeploymentTransactionNonce,
                signal,
            })

            const rewardTxResponse = await prepareRewardPaymentTransaction({
                taker: earnTxResponse.taker,
                adminNonce: rewardPaymentTransactionNonce,
                adminPrivateKey,
                userBIds,
                signal,
            })

            return {
                earnDeploymentTransaction: earnTxResponse.transaction,
                rewardPaymentTransaction: rewardTxResponse.transaction,
                rewardAmountInInvestmentCurrency:
                    rewardTxResponse.amountInTakerCurrency,
                rewardPaymentTransactionNonce,
                senderAddress: adminPrivateKey.address,
            }
        }
        case 'configured': {
            const rewardTxResponse = await prepareRewardPaymentTransaction({
                taker: getDeployedTakerToReward(earn),
                adminNonce: adminCurrentNonce,
                adminPrivateKey,
                userBIds,
                signal,
            })

            return {
                earnDeploymentTransaction: null,
                rewardPaymentTransaction: rewardTxResponse.transaction,
                rewardAmountInInvestmentCurrency:
                    rewardTxResponse.amountInTakerCurrency,
                rewardPaymentTransactionNonce: adminCurrentNonce,
                senderAddress: adminPrivateKey.address,
            }
        }
        default:
            return notReachable(earn)
    }
}
